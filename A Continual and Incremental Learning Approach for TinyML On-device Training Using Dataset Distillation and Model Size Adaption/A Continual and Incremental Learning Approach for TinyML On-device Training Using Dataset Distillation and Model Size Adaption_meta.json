{"table_of_contents": [{"title": "A Continual and Incremental Learning Approach\nfor TinyML On-device Training Using Dataset\nDistillation and Model Size Adaption", "heading_level": null, "page_id": 0, "polygon": [[62.3056640625, 54.0], [548.05078125, 54.0], [548.05078125, 132.064453125], [62.3056640625, 132.064453125]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 560.25], [213.75, 560.25], [213.75, 570.0234375], [133.5, 570.0234375]]}, {"title": "II. RELATED WORK", "heading_level": null, "page_id": 0, "polygon": [[394.5, 562.5], [480.0, 562.5], [480.0, 571.95703125], [394.5, 571.95703125]]}, {"title": "III. PROPOSED METHOD", "heading_level": null, "page_id": 1, "polygon": [[119.25, 630.3515625], [228.0, 630.3515625], [228.0, 640.40625], [119.25, 640.40625]]}, {"title": "<PERSON>. Problem Statement", "heading_level": null, "page_id": 1, "polygon": [[47.25, 646.5], [140.2998046875, 646.5], [140.2998046875, 655.875], [47.25, 655.875]]}, {"title": "<PERSON>. Method", "heading_level": null, "page_id": 1, "polygon": [[310.5, 166.482421875], [357.0, 166.482421875], [357.0, 175.95703125], [310.5, 175.95703125]]}, {"title": "IV. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[133.5, 273.75], [216.3515625, 273.75], [216.3515625, 283.8515625], [133.5, 283.8515625]]}, {"title": "A. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[47.25, 334.5], [143.25, 334.5], [143.25, 343.986328125], [47.25, 343.986328125]]}, {"title": "V. RESULTS AND DISCUSSION", "heading_level": null, "page_id": 5, "polygon": [[108.7734375, 237.75], [239.25, 237.75], [239.25, 248.080078125], [108.7734375, 248.080078125]]}, {"title": "A. Explanation of Terms", "heading_level": null, "page_id": 5, "polygon": [[46.5, 309.0], [150.75, 309.0], [150.75, 319.236328125], [46.5, 319.236328125]]}, {"title": "B. CIFAR10", "heading_level": null, "page_id": 5, "polygon": [[47.73779296875, 573.0], [100.03271484375, 573.0], [100.03271484375, 582.78515625], [47.73779296875, 582.78515625]]}, {"title": "C. CORe50", "heading_level": null, "page_id": 5, "polygon": [[310.5, 185.25], [365.16796875, 185.25], [365.16796875, 195.3896484375], [310.5, 195.3896484375]]}, {"title": "<PERSON>. Speech Commands", "heading_level": null, "page_id": 5, "polygon": [[310.5, 387.87890625], [404.015625, 387.87890625], [404.015625, 396.7734375], [310.5, 396.7734375]]}, {"title": "E. MNI<PERSON>", "heading_level": null, "page_id": 5, "polygon": [[308.98828125, 597.75], [355.60546875, 597.0], [355.60546875, 607.53515625], [308.98828125, 607.53515625]]}, {"title": "F. H<PERSON>", "heading_level": null, "page_id": 6, "polygon": [[47.25, 642.0], [81.87890625, 642.0], [81.87890625, 652.39453125], [47.25, 652.39453125]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 6, "polygon": [[310.5, 681.0], [371.25, 681.0], [371.25, 690.6796875], [310.5, 690.6796875]]}, {"title": "VI. CONCLUSIONS AND FUTURE RESEARCH", "heading_level": null, "page_id": 7, "polygon": [[78.36767578125, 549.0], [271.037109375, 549.0], [271.037109375, 559.1953125], [78.36767578125, 559.1953125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 7, "polygon": [[408.75, 93.0], [465.75, 93.0], [465.75, 103.060546875], [408.75, 103.060546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 99], ["Text", 10], ["ListItem", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4019, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 110], ["Text", 14], ["SectionHeader", 3], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 109], ["Text", 10], ["Equation", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 615, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 114], ["Text", 19], ["Equation", 6], ["TextInlineMath", 4], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 109], ["Text", 9], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["TableCell", 113], ["Line", 110], ["Text", 13], ["SectionHeader", 6], ["ListItem", 4], ["Table", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 5028, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Line", 119], ["Span", 115], ["TableCell", 70], ["Text", 5], ["Table", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 8187, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 108], ["ListItem", 13], ["Reference", 13], ["Text", 10], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/A Continual and Incremental Learning Approach for TinyML On-device Training Using Dataset Distillation and Model Size Adaption"}