{"table_of_contents": [{"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 306.75], [149.2646484375, 306.75], [149.2646484375, 319.236328125], [48.0, 319.236328125]]}, {"title": "2 OVERVIEW", "heading_level": null, "page_id": 1, "polygon": [[48.0, 296.25], [125.25, 296.25], [125.25, 308.021484375], [48.0, 308.021484375]]}, {"title": "2.1 A Brief History of Transfer Learning", "heading_level": null, "page_id": 1, "polygon": [[47.25, 312.75], [241.5, 312.75], [241.5, 323.296875], [47.25, 323.296875]]}, {"title": "2.2 Notations and Definitions", "heading_level": null, "page_id": 1, "polygon": [[310.5, 612.94921875], [456.75, 612.94921875], [456.75, 623.00390625], [310.5, 623.00390625]]}, {"title": "2.3 A Categorization of Transfer Learning Tech-\nniques", "heading_level": null, "page_id": 2, "polygon": [[310.482421875, 240.75], [563.291015625, 240.75], [563.291015625, 263.935546875], [310.482421875, 263.935546875]]}, {"title": "3 INDUCTIVE TRANSFER LEARNING", "heading_level": null, "page_id": 3, "polygon": [[311.25, 707.6953125], [510.0, 707.6953125], [510.0, 719.296875], [311.25, 719.296875]]}, {"title": "3.1 Transferring Knowledge of Instances", "heading_level": null, "page_id": 5, "polygon": [[48.0, 223.5], [247.5, 223.5], [247.5, 233.96484375], [48.0, 233.96484375]]}, {"title": "3.2 Transferring Knowledge of Feature Representa-\ntions", "heading_level": null, "page_id": 5, "polygon": [[47.25, 663.0], [300.322265625, 663.0], [300.322265625, 685.265625], [47.25, 685.265625]]}, {"title": "3.2.1 Supervised Feature Construction", "heading_level": null, "page_id": 5, "polygon": [[311.25, 153.75], [490.078125, 153.75], [490.078125, 165.0322265625], [311.25, 165.0322265625]]}, {"title": "3.2.2 Unsupervised Feature Construction", "heading_level": null, "page_id": 5, "polygon": [[311.25, 606.75], [502.03125, 606.75], [502.03125, 616.4296875], [311.25, 616.4296875]]}, {"title": "3.3 Transferring Knowledge of Parameters", "heading_level": null, "page_id": 6, "polygon": [[47.9619140625, 315.75], [255.75, 315.75], [255.75, 325.810546875], [47.9619140625, 325.810546875]]}, {"title": "3.4 Transferring Relational Knowledge", "heading_level": null, "page_id": 6, "polygon": [[311.25, 376.5], [500.8359375, 376.5], [500.8359375, 386.912109375], [311.25, 386.912109375]]}, {"title": "4 TRANSDUCTIVE TRANSFER LEARNING", "heading_level": null, "page_id": 7, "polygon": [[47.9619140625, 285.0], [273.0, 285.0], [273.0, 296.033203125], [47.9619140625, 296.033203125]]}, {"title": "4.1 Transferring the Knowledge of Instances", "heading_level": null, "page_id": 7, "polygon": [[310.5, 237.0], [528.0, 237.0], [528.0, 247.5], [310.5, 247.5]]}, {"title": "4.2 Transferring Knowledge of Feature Representa-\ntions", "heading_level": null, "page_id": 8, "polygon": [[48.0, 620.25], [300.322265625, 620.25], [300.322265625, 643.5], [48.0, 643.5]]}, {"title": "5 UNSUPERVISED TRANSFER LEARNING", "heading_level": null, "page_id": 9, "polygon": [[48.0, 237.75], [273.75, 237.75], [273.75, 250.20703125], [48.0, 250.20703125]]}, {"title": "5.1 Transferring Knowledge of Feature Representa-\ntions", "heading_level": null, "page_id": 9, "polygon": [[47.25, 430.5], [300.75, 430.5], [300.75, 453.234375], [47.25, 453.234375]]}, {"title": "6 TRANS<PERSON><PERSON> BOUNDS AND <PERSON><PERSON><PERSON>VE\nTRANSFER", "heading_level": null, "page_id": 9, "polygon": [[311.25, 189.75], [563.25, 189.75], [563.25, 213.85546875], [311.25, 213.85546875]]}, {"title": "7 APPLICATIONS OF TRANSFER LEARNING", "heading_level": null, "page_id": 9, "polygon": [[311.25, 674.25], [549.0, 674.25], [549.0, 686.42578125], [311.25, 686.42578125]]}, {"title": "7.1 Other Applications of Transfer Learning", "heading_level": null, "page_id": 11, "polygon": [[47.25, 204.0], [261.0, 204.0], [261.0, 215.40234375], [47.25, 215.40234375]]}, {"title": "8 CONCLUSIONS", "heading_level": null, "page_id": 11, "polygon": [[48.0, 533.25], [148.6669921875, 533.25], [148.6669921875, 545.2734375], [48.0, 545.2734375]]}, {"title": "Acknowledgment", "heading_level": null, "page_id": 11, "polygon": [[311.25, 543.7265625], [395.25, 543.7265625], [395.25, 553.78125], [311.25, 553.78125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 11, "polygon": [[311.25, 603.0], [385.5, 603.28125], [385.5, 614.109375], [311.25, 614.109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 91], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 110], ["Text", 10], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 964], ["Line", 116], ["Text", 8], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 106], ["TableCell", 37], ["Text", 13], ["Caption", 2], ["ListItem", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 152], ["Span", 135], ["Line", 75], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["ListItem", 1], ["TableGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 10572, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 622], ["Line", 123], ["Text", 6], ["TextInlineMath", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1053, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 125], ["Text", 9], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1300, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 711], ["Line", 133], ["Text", 8], ["TextInlineMath", 7], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 109], ["TextInlineMath", 6], ["Text", 5], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 627], ["Line", 122], ["Text", 12], ["SectionHeader", 4], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 117], ["Text", 12], ["ListItem", 6], ["Footnote", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 114], ["Text", 10], ["SectionHeader", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["TableCell", 404], ["Line", 103], ["ListItem", 21], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Table", 1], ["Caption", 1], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 16832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 155], ["ListItem", 41], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 114], ["ListItem", 22], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1172, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/A Survey on Transfer Learning"}