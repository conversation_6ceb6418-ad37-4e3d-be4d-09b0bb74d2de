{"table_of_contents": [{"title": "AST: Effective Dataset Distillation through Alignment with\nSmooth and High-Quality Expert Trajectories", "heading_level": null, "page_id": 0, "polygon": [[60.0, 60.0], [551.25, 60.0], [551.25, 102.8671875], [60.0, 102.8671875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 537.75], [213.75, 537.75], [213.75, 547.59375], [133.5, 547.59375]]}, {"title": "II. RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[390.75, 56.25], [483.75, 56.25], [483.75, 66.46728515625], [390.75, 66.46728515625]]}, {"title": "A. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[309.75, 71.25], [405.75, 71.25], [405.75, 81.83935546875], [309.75, 81.83935546875]]}, {"title": "B. Relationship between <PERSON><PERSON> and Student", "heading_level": null, "page_id": 1, "polygon": [[310.5, 496.5], [495.0, 496.5], [495.0, 506.21484375], [310.5, 506.21484375]]}, {"title": "III. PRELIMINARIES", "heading_level": null, "page_id": 2, "polygon": [[129.75, 95.25], [218.25, 95.25], [218.25, 105.2841796875], [129.75, 105.2841796875]]}, {"title": "<PERSON>. Problem Statement", "heading_level": null, "page_id": 2, "polygon": [[47.25, 111.0], [138.8056640625, 111.0], [138.8056640625, 121.1396484375], [47.25, 121.1396484375]]}, {"title": "<PERSON>. Various Objective Functions of DD", "heading_level": null, "page_id": 2, "polygon": [[47.25, 552.0], [208.4326171875, 552.0], [208.4326171875, 561.90234375], [47.25, 561.90234375]]}, {"title": "IV. DESIGN OF AST", "heading_level": null, "page_id": 2, "polygon": [[391.166015625, 222.75], [482.25, 222.75], [482.25, 232.41796875], [391.166015625, 232.41796875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[307.79296875, 238.5], [363.0, 238.5], [363.0, 247.5], [307.79296875, 247.5]]}, {"title": "B. Generate Smooth Expert Trajectories", "heading_level": null, "page_id": 2, "polygon": [[310.5, 522.0], [477.75, 522.0], [477.75, 532.125], [310.5, 532.125]]}, {"title": "<PERSON><PERSON> Balancing Stochasticity from Initial Variables", "heading_level": null, "page_id": 5, "polygon": [[48.0, 158.25], [249.0, 158.25], [249.0, 168.1259765625], [48.0, 168.1259765625]]}, {"title": "<PERSON><PERSON> Alleviate the Propensity for Accumulated Error", "heading_level": null, "page_id": 5, "polygon": [[310.5, 627.0], [521.25, 627.0], [521.25, 636.92578125], [310.5, 636.92578125]]}, {"title": "E. Training Algorithm", "heading_level": null, "page_id": 7, "polygon": [[47.25, 301.25390625], [141.0, 301.25390625], [141.0, 310.921875], [47.25, 310.921875]]}, {"title": "V. EXPERIMENTS", "heading_level": null, "page_id": 7, "polygon": [[135.75, 393.75], [212.25, 393.75], [212.25, 402.9609375], [135.75, 402.9609375]]}, {"title": "A. Experiments Setup", "heading_level": null, "page_id": 7, "polygon": [[46.5, 411.0], [138.0, 411.0], [138.0, 420.36328125], [46.5, 420.36328125]]}, {"title": "<PERSON><PERSON> with State-of-the-Art Methods", "heading_level": null, "page_id": 7, "polygon": [[310.5, 591.75], [500.8359375, 591.75], [500.8359375, 600.9609375], [310.5, 600.9609375]]}, {"title": "C. Results on ImageNet Subsets (128×128)", "heading_level": null, "page_id": 9, "polygon": [[48.0, 429.75], [228.75, 429.75], [228.75, 440.0859375], [48.0, 440.0859375]]}, {"title": "D. <PERSON> Burden", "heading_level": null, "page_id": 9, "polygon": [[310.482421875, 560.25], [393.75, 560.25], [393.75, 570.0234375], [310.482421875, 570.0234375]]}, {"title": "E. Examples of Training Instability", "heading_level": null, "page_id": 10, "polygon": [[47.88720703125, 399.0], [194.25, 399.0], [194.25, 408.375], [47.88720703125, 408.375]]}, {"title": "F. Analysis", "heading_level": null, "page_id": 10, "polygon": [[47.513671875, 697.5], [96.0, 697.5], [96.0, 708.08203125], [47.513671875, 708.08203125]]}, {"title": "VI. CONCLUSION", "heading_level": null, "page_id": 11, "polygon": [[397.142578125, 459.75], [477.0, 459.75], [477.0, 469.4765625], [397.142578125, 469.4765625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 11, "polygon": [[408.0, 715.4296875], [465.275390625, 715.4296875], [465.275390625, 723.1640625], [408.0, 723.1640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 100], ["Text", 10], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 115], ["Text", 11], ["ListItem", 3], ["SectionHeader", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 746], ["Line", 147], ["TableCell", 24], ["Text", 9], ["SectionHeader", 6], ["TextInlineMath", 6], ["Equation", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1087, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 132], ["Text", 9], ["Equation", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 219], ["Equation", 9], ["TextInlineMath", 7], ["Text", 6], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3265, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 582], ["Line", 134], ["TableCell", 24], ["Reference", 9], ["Text", 6], ["TextInlineMath", 3], ["Caption", 2], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1112], ["Line", 172], ["TableCell", 151], ["Text", 5], ["TextInlineMath", 4], ["Table", 3], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 27733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["TableCell", 116], ["Line", 97], ["Text", 8], ["SectionHeader", 4], ["Table", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["TableCell", 66], ["Line", 43], ["Text", 8], ["Caption", 3], ["Reference", 3], ["Picture", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["TableCell", 185], ["Line", 107], ["Caption", 5], ["Reference", 5], ["Text", 4], ["Figure", 3], ["FigureGroup", 3], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 16466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["TableCell", 132], ["Line", 124], ["Text", 7], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Figure", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13903, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 140], ["TableCell", 16], ["Text", 6], ["Reference", 5], ["Caption", 3], ["Figure", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["Table", 1], ["TextInlineMath", 1], ["ListItem", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 142], ["ListItem", 49], ["Reference", 45], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/AST- Effective Dataset Distillation through Alignment with Smooth and High-Quality Expert Trajectories "}