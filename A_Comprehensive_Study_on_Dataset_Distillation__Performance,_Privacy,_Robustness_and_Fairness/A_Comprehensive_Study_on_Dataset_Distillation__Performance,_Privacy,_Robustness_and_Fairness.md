# A Comprehensive Study on Dataset Distillation: Performance, Privacy, Robustness and Fairness

Zongxiong Chen $^{\ast2}$ , <PERSON><PERSON><PERSON> $^{\ast1}$ , <PERSON><PERSON> $^3$ 

Herbert W<PERSON>tschläger $^3$  ,  $\rm \dot{Q}$ ing  $\rm Li^1$  , <PERSON><PERSON> $^2$  , <PERSON><PERSON> $^3$  , Chunming Rong $^1$ 

<sup>1</sup>University of Stavanger

<sup>2</sup> Fraunhofer FOKUS

 $3$  Technische Universität München

{jiahui.geng, chunming.rong, qing.li}@uis.no,

{derui.zhu, herbert.woisetschlaeger, ruben.mayer}@tum.de,

{zongxiong.chen, sonja.schimmler}@fokus.fraunhofer.de

### Abstract

The aim of dataset distillation is to encode the rich features of an original dataset into a tiny dataset. It is a promising approach to accelerate neural network training and related studies. Different approaches have been proposed to improve the informativeness and generalization performance of distilled images. However, no work has comprehensively analyzed this technique from a security perspective and there is a lack of systematic understanding of potential risks. In this work, we conduct extensive experiments to evaluate current state-of-the-art dataset distillation methods. We successfully use membership inference attacks to show that privacy risks still remain. Our work also demonstrates that dataset distillation can cause varying degrees of impact on model robustness and amplify model unfairness across classes when making predictions. This work offers a large-scale benchmarking framework for dataset distillation evaluation.

### 1 Introduction

A recent study [\[Isenko](#page-8-0) *et al.*, 2022] has shown that the storage consumption required for popular training sets grows exponentially over time. It is becoming increasingly difficult to train neural networks well on vastly large-scale datasets. Local training is constraint by memory limitations, while distributed training suffers from latency across clients due to network I/O and bandwidth problems. Besides, depending on the complexity of the task and model architectures, training deep neural networks requires iterations over the entire training set, up to several hundred or even thousands of times. Dataset distillation [Wang *et al.*[, 2018\]](#page-9-0) may be the holy grail to solve this dilemma. It describes a set of methods aiming to significantly reduce the amount of data while maintaining the same model accuracy level as training on an entire dataset. In contrast to coreset selection, which requires heuristic algorithms and may not be suitable for highdimensional datasets [\[Bachem](#page-7-0) *et al.*, 2017], dataset distillation learns to condense large datasets into smaller synthetic datasets rather than using sampling strategies.

There have been many efforts continue to advance dataset distillation, both theoretically and practically [\[Zhao](#page-9-1) *et al.*, [2021;](#page-9-1) [Zhao and Bilen, 2021;](#page-9-2) [Cazenavette](#page-7-1) *et al.*, 2022; [Kim](#page-8-1) *et al.*[, 2022\]](#page-8-1). However, most of these works focus on the model's performance and generalization capability across architectures. We state that the security of dataset distillation techniques has been overlooked in the research. Security is essentially critical in the ML application and deployment. Different attacks have been proposed to compromise the integrity of machine learning models. Privacy attacks are trying to infer task-irrelevant private information and restore the training samples from the model's output or parameters [\[Shokri](#page-9-3) *et al.*, 2017; Geng *et al.*[, 2021\]](#page-8-2). There are also attacks on the robustness by adding imperceptible noise to the samples so that the model will be fooled and give false predictions [Xu *et al.*[, 2020\]](#page-9-4). Our work seeks to bridge the gap between dataset distillation and security analysis studies.

In this work, we propose a benchmark to evaluate the security of dataset distillation. Our work focuses on answering the following questions: (1). Is the use of a synthetic dataset instead of the real dataset sufficient to protect data privacy? (2). What is the impact on the robustness when models are trained on a distilled dataset using fewer training samples with visual noise on each image? (3). Given that safety-critical applications cannot just focus on the average performance across all classes, is dataset distillation fair for each class in classification tasks?

Based on proposed research questions, we conducted a large-scale analysis of state-of-the-art distillation methods. We use four representative distillation techniques: Differentiable Siamese Augmentation (DSA), Distribution Matching (DM), Training Trajectory Matching (MTT), and Information-Intensive Dataset Condensation (IDC) to synthesize datasets and train neural networks of different architectures on these to obtain the target models. Then we designed a lot of experiments to evaluate the impact of data distilla-

<sup>\*</sup>Equal contributions

tion on the privacy, fairness and robustness of the model. We identify the key factors affecting these metrics by conducting extensive comparative experiments. The experimental results reveal several insightful findings on the dataset distillation, including:

- Dataset distillation does amplify the unfairness of the model's predictions between different classes, which increases with the distillation rate.
- Dataset distillation does not have natural privacypreserving capabilities: the success of membership inference attacks will be affected by the distillation rate, initialization, and the number of classes.
- The robustness of the model is affected to varying degrees, but the distillation rate plays a minor role here.

Here the distillation rate represents the ratio of the numbers of images per class after distillation to before distillation. This is the first work to systematically evaluate the dataset distillation techniques and their risks in terms of security.

### 1.1 Outline

Our paper is organized as follows. We first introduce the background that are related to our work in Section [2.](#page-1-0) Then, we describe the experimental design in detail, including the experimental data, the methods being evaluated, and the evaluation pipeline in Section [3.](#page-2-0) Following that, we present experimental results and key insights In Section [5,](#page-6-0) we put our work into context with related works, and conclude with future work in Section [6.](#page-7-2)

### <span id="page-1-0"></span>2 Background

### 2.1 Dataset Distillation Basics

Dataset distillation aims to distill the training set into a much smaller synthetic dataset so that the model trained on the distilled dataset performs as closely as possible to the model trained on the whole dataset. Considering the widely used image classification task where the training dataset consists of images and labels  $\mathcal{T} = (x_i, y_i)_{i=1}^{|\mathcal{T}|}$ . The distilled synthetic dataset what we want to learn is  $S = (x_i, y_i)_{i=1}^{|S|}$ . Let  $\phi_{\theta^{\mathcal{T}}}$ and  $\phi_{\theta^S}$  denote the models with parameters  $\theta^{\mathcal{T}}$  and  $\theta^S$  that trained on  $T$  and  $S$  respectively. Therefore, an ideal dataset distillation should satisfy:

$$
\mathbb{E}_{x \sim P_{\mathcal{D}}}[\ell(\phi_{\theta}(\tau(x), y)] \simeq \mathbb{E}_{x \sim P_{\mathcal{D}}}[\ell(\phi_{\theta}(\tau(x), y)], \quad (1)
$$

where  $\ell$  is the loss function and  $P_{\mathcal{D}}$  is the real data distribution.

#### 2.2 Security of ML Models

Privacy Perspective When the deep model learns highlevel and abstract features, it inevitably remembers too many details from the training samples, including a lot of taskindependent private information. Many attacks attempt to attack the target model to infer the privacy. Representative attacks include membership inference attacks (MIAs) [\[Shokri](#page-9-3) *et al.*[, 2017\]](#page-9-3), attribute inference attacks [\[Gong and Liu,](#page-8-3) [2016\]](#page-8-3), model inversion attacks [\[Fredrikson](#page-7-3) *et al.*, 2015; Zhang *et al.*[, 2020\]](#page-9-5), gradient inversion attacks [Zhu *[et al.](#page-9-6)*, [2019\]](#page-9-6), etc. The goal of MIAs is to discriminate whether a specific data participates in the model training and it can be regarded as a binary classification problem. Model inversion attack and gradient inversion attack attempt to restore training data and labels from the accessed model parameters or gradient parameters. Attribute inference attacks attempt to infer task-independent attributes.

Robustness Perspective In machine learning, model robustness refers to how well a model can perform on new data compared with training data. Ideally, performance should not deviate significantly. However, deep models have recently been shown to be unstable to adversarial perturbations, i.e. even imperceptible perturbations are sufficient to fool the well-trained models and results in incorrect predictions.

Definition 1 (Adversarial Perturbation). *For a given classi*fier denoted by  $f(\cdot; \theta) : \mathbb{R}^d \to \mathbb{R}^K$ , with input  $x \in \mathbb{R}^d$  and K *is the number of classes in the task. Let*  $f(x; \theta)$  *represent the model softmax output and*  $f(x; \theta)$  *represent the index of the highest probability. Then, the adversarial perturbation is defined as the minimum perturbation* r *that is sufficient to change the model prediction:*

$$
\Delta(x; f(\cdot; \theta)) := \min_{r} \|r\|_2 \quad s.t. \quad \hat{f}(x+r) \neq \hat{f}(x) \tag{2}
$$

Adversarial accuracy is a commonly used method to measure the robustness of a classifier. Usually, we use different adversarial sample generation algorithms to add a perturbation of magnitude  $\epsilon$  to the sample and test the accuracy of the model.

**Definition 2** (Adversarial Accuracy). Let  $\mathcal{L}(f(x; \theta), y)$  be *the classification error given input* x, *classifier*  $f(\cdot; \theta)$  *and true label* y*. Adversarial accuracy an is defined as follows for an adversary with an adversary region* R(x)*. Commonly, we consider*  $\mathbb{B}(x,\epsilon)$ *, a*  $\epsilon$ *−ball around* x *based on the distance metric* d as the adversary region  $R(x)$ .

$$
acc = \mathbb{E}_{(x,y)\sim\mathcal{D}}[\mathbb{1}(f(x^*; \theta) = y)]
$$
 (3)

*where*

$$
x^* = \underset{x' \in R(x)}{\arg \max} \mathcal{L}(x', y). \tag{4}
$$

Fairness Perspective Fairness between classes is also an important indicator of safety. Often people only report average performance, i.e., average metrics across classes, which can lead to an overestimation of safety. Fine-grained performance per category is essential for safety-critical applications, such as the 'human' class in autonomous driving. It has been found that many post-processed machine learning models amplify the unfairness between different categories, i.e., the models perform well on some categories while degrading a lot on others. Hooker et al. [\[Hooker](#page-8-4) *et al.*, 2020] show that model compression can amplify model bias in models, including using model pruning and quantization protocols. Silva et al. [Silva *et al.*[, 2021\]](#page-9-7) find that distilled models almost always exhibit statistically significant bias compared to the original pre-trained models. [Ma *et al.*[, 2022;](#page-9-8) Benz *et al.*[, 2021;](#page-7-4) Xu *et al.*[, 2021\]](#page-9-9) shows that models trained with adversarial examples exhibit the same problem. Inspired by [Li *et al.*[, 2021;](#page-8-5) Lin *et al.*[, 2022\]](#page-8-6), we give the definition of fairness as follows,

<span id="page-2-2"></span>Image /page/2/Picture/0 description: The image displays four grids of generated images, labeled (a) DM, (b) DSA, (c) MTT, and (d) IDC. Each grid contains numerous small images, appearing to be generated samples from a machine learning model. The images within the grids are diverse, showing various subjects such as animals (dogs, cats, birds, deer, horses), vehicles (cars, trucks, boats, airplanes), and some abstract or less discernible patterns. The overall impression is a comparison of image generation quality or diversity across different methods.

Figure 1: Distilled 10 images per class from CIFAR10 via DSA, DM, MTT, and IDC.

<span id="page-2-3"></span>**Definition 3** (Performance Fairness). A model  $w_1$  is more fair *than*  $w_2$  *if the test performance distribution of*  $w_1$  *is more uniform than that of*  $w_2$ , *e.g.*  $var(F(w_1)) < var(F(w_2))$ , *where* F(∗) *denotes evaluation metrics such as test accuracy or test loss of model among all classes and var denotes the variance.*

In contrast to the definition in [Li *et al.*[, 2021\]](#page-8-5) which focuses on comparison in federated learning settings, our definition can be applied to evaluate the fairness about different models directly.

### <span id="page-2-0"></span>3 Evaluation Benchmark

Our work unites data distillation and privacy, robustness and fairness in ML research. For that, we introduce evaluation pipelines along with four state-of-the-art dataset distillation methods.

#### 3.1 Dataset

We implement our evaluation pipeline on CIFAR10 and CI-FAR100, which are widely used for image classification tasks. CIFAR-10 contains 10 different categories, each category has 60K (50K training images and 10K test images) images of size  $32 \times 32$ . CIFAR-100 contains 100 different categories, each with 500 training images and 100 test images of the same size. Using larger datasets is a trend of dataset distillation, such as TinyImageNet [\[Le and Yang, 2015\]](#page-8-7), ImageNet [\[Russakovsky](#page-9-10) *et al.*, 2015] and CelebA [Liu *[et al.](#page-8-8)*, [2015\]](#page-8-8), which is also our future work.

### 3.2 Dataset Distillation Methods

We choose DSA, DM, MTT, IDC as representative methods to be evaluated in our benchmark. They are all accepted by top conferences with code public released. They have been widely compared in different works.

• DSA

DSA is essentially based on the gradient matching mechanism, which infers the synthetic dataset by matching the model optimization trajectory trained on the synthetic dataset with the trajectory trained on the original dataset. DSA proposed to apply Differentiable Siamese Augmentation during the optimization. To make it applicable to any initial state, the optimization objective of the model can be expressed as:

$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=1}^{T-1} D(\nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{S}, \omega^{\mathcal{S}}), \theta_t), \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}}), \theta_t)) \right] (5)
$$

where T is the number of iterations,  $\theta_0$  is the random initialized parameters, and  $A$  is family of image transformations that parameterized with  $\omega^{\mathcal{S}}$  and  $\omega^{\mathcal{T}}$  for the synthetic and real training samples respectively.

#### • DM

DM proposes to optimize the distilled dataset by reducing the maximum mean discrepancy (MMD) between the synthetic and real data. DM uses neural network with different random initializations instead of sampling parameters from a set of pretrained networks. In addition, DM also uses Differentiable Siamese Augmentation. The objective function can be illustrated as:

<span id="page-2-1"></span>
$$
\min_{\mathcal{S}} \mathbb{E}_{\substack{\theta \sim P_{\theta} \\ \omega \sim \Omega}} \| \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \psi_{\theta}(\mathcal{A}(s_i, \omega)) - \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_{\theta}(\mathcal{A}(x_i, \omega)) \|^{2}, \quad (6)
$$

where  $\psi_{\theta}$  is a family of deep models to map the samples into a lower dimensional space, and  $\omega \sim \Omega$  is the augmentation parameter. Importantly, Equation [6](#page-2-1) requires only optimizing the synthetic dataset  $S$ , not the model parameters  $\theta$ .

#### • MTT

MTT regards the sequence of models trained on the real data as the expert trajectory, and the model trained on the synthetic data as the student model. It first samples  $\theta_t$  at a random timestep  $t$  from the expert trajectory to initialize the student model. Then it performs N Step gradient descent updates on the student model and selects the expert model  $\theta_{n+M}$  as the target model. The objective of MTT is to make the student model approach the expert model and use the Euclidean distance of the expert parameters to normalize the matching loss:

$$
\mathcal{L} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2}{\|\hat{\theta}_t^* - \theta_{t+M}^*\|_2^2}
$$
(7)

### • IDC

Similar to DSA, IDC is also use gradient matching algorithm to obtain optimal distlled dataset. The key difference between DSA and IDC is that IDC introduces a multi-formation function:  $f: \mathbb{R}^{n \times m} \to \mathbb{R}^{n' \times m}$ , which can synthesize more informative dataset under the same storage consumption. The multi-formation function  $f$ can be either learned or a pretrained model during optimization.

### 3.3 Evaluation Pipeline

Our evaluation pipeline is mainly divided into three stages. The first stage is image distillation, we use the Github codes of DSA[\\*](#page-3-3), DM\*, MTT\*, and IDC \* official releases to obtain different distilled datasets. The second stage is to use the distilled data set to train a randomly initialized model. The third stage is to design targeted experiments to analyze the target model. We evaluate target models in term of performance, privacy, robustness and fairness. As in prior works, the informativeness of the distilled image and the generalization performance are two fundamental metrics to measure the performance. Image informativeness is usually measured by the accuracy of the neural network trained on it, and a higher accuracy of the model means a better representation of the image. Furthermore, generalization is defined as the model accuracy across different architectures. We chose to use membership inference attacks among the many ways to attack the privacy of machine learning models. Model inversion attacks and gradient inversion attacks are not suitable for evaluating the privacy-preserving capability of dataset distillation due to the irreversible information loss after distillation. We perform adversarial attacks on this well-trained model to analysis the intrinsic robustness issues caused by the distilled dataset. In our evaluation, we consider the degradation of the robust accuracy caused by DeepFoolAttack [\[Moosavi-Dezfooli](#page-9-11) *et al.*, [2016\]](#page-9-11) overall results across models .As the fairness study, we observe how the model's accuracy and loss vary across different classes, and whether the model has a prominent drop on spesific classes.

### 4 Experimental Studies

In this section, We comprehensively evaluate the properties of dataset distillation crafted by the state-of-the-art distillation method through extensive experiments.

We have designed specific experiments for each property and layout the details for the evaluations further. Section [4.2](#page-3-4) introduces the overall performance of the various approaches. Readers can grab a quick overview of current developments in this domain. We report the performance of the distilled data under different compression ratio with the model performance and the generalization capability across various network architectures. In Section [4.3,](#page-4-0) [4.5](#page-6-1) and [4.4,](#page-5-0) we compare the performance of the models trained on the original and distilled data and analyze the changes that dataset distillation brings to the deep models in terms of privacy, robustness, and fairness.

### 4.1 Experimenetal Setup

Performance Evaluation Setup Using the code released by authors, we run Convnet3 to synthesize 1/10/20/50/100 and 1/10/20/50 images per classes distilled dataset for CIFAR10 and CIFAR100 respectively. we use default hyperparameters and training configurations of considered methods, except the following exceptions: 1) For IPC 50 and 100, we manually tune the number of iterations for outer and inner optimization for DSA and DM since they're undefined in the released source code. 2) In MTT, ZCA whitening, a plugable mechanism to improve the performance of distillation dataset as reported by [\[Cazenavette](#page-7-1) *et al.*, 2022], is disabled when synthesizing distilled dataset for a better comparison with other methods. Since it is applicable to other considered distillation methods and orthogonal to our benchmark settings.

To evaluate the accuracy performance of each distilled dataset, we repeatedly train three kinds of networks, namely ConvNet3, Resnet18, VGG16, from scratch three times and train them for 2000 epochs to ensure model convergence. The number of epochs we use is different from that of provided by [Kim *et al.*[, 2022\]](#page-8-1) since we found that MTT cannot achieve the optimal accuracy reported in [\[Cazenavette](#page-7-1) *et al.*, 2022] within 300 epochs. For a fair comparison, we train all networks for 2000 epochs instead.

Privacy Evaluation Setup To obtain distilled dataset for performing MIA attacks, we use the distillation algorithm described previously to obtain corresponding synthetic dataset via the same settings we describe above except using a *conceptual* original dataset which is drawn 40, 80, 200, 400 samples per class on CIFAR10 randomly. We don't report the results about MTT since we failed to synthsize the corresponding distilled dataset.

Robustness Evaluation Setup We use adversarial examples to attack the target model and measure the robustness of the model through adversarial accuracy. We have considered training the target models to the same accuracy. However, this approach is unfair to those methods that can be trained to very high accuracy. Therefore, we choose to test the adversarial accuracy of the best models obtained by different approaches.

#### <span id="page-3-4"></span>4.2 Dataset Distillation Performance

We evaluate the cross architecture performance of different condensation methods on the CIFAR10 datasets with IPC ranging from 1 to 100, and CIFAR100 dataset with IPC ranging from 1 to 50. Figure [5a](#page-5-1) and Figure [5b](#page-5-2) illustrate the test accuracy of the models trained on dataset after distillation on CIFAR10 and CIFAR100 respectively. The test accuracy of the model increases as the number of images per class grows. IDC outperforms MTT, DM, and DSA on CIFAR10. On CI-FAR100, IDC still performs the best, the performance of DSA and DM are quite close, and MTT is only second to IDC when the number of images per class is small, despite the fluctuations in the curve. Due to space limit, we only show the curves here, the complete tables containing various dis-

<span id="page-3-0"></span><sup>\*</sup> <https://github.com/VICO-UoE/DatasetCondensation>

<span id="page-3-1"></span><sup>\*</sup> <https://github.com/VICO-UoE/DatasetCondensation>

<span id="page-3-2"></span><sup>\*</sup> <https://github.com/GeorgeCazenavette/mtt-distillation>

<span id="page-3-3"></span><sup>\*</sup> <https://github.com/snu-mllab/Efficient-Dataset-Condensation>

<span id="page-4-1"></span>Image /page/4/Figure/0 description: This image displays four T-SNE projections of CIFAR10 data, labeled (a) DSA, (b) DM, (c) MTT, and (d) IDC. Each projection shows data points colored red, green, and blue, representing classes 0, 1, and 2 respectively. The points are clustered into three main groups. Several rectangular boxes are drawn around specific groups of points in each projection, highlighting certain data subsets. The axes are labeled x1 and x2, with numerical ranges from approximately -80 to 60 on the x1 axis and -60 to 80 on the x2 axis.

<span id="page-4-2"></span>Figure 2: Data distribution of real images and synthetic images learned in CIFAR10 at IPC-50. We use a pretrained ConvNet3 model to generate an 2048-dimension embedding and project that embedding into 2-dimension plane by t-SNE with hyperparameter *perplexity* 50. The black boxs in presented in the figure indicate the obvious outliers in the projected embeddings.

<span id="page-4-3"></span>Image /page/4/Figure/2 description: The image displays two rows of line graphs, each depicting robustness accuracy against epsilon values. The top row contains five subplots labeled (a) IPC=50, (b) DSA, (c) DM, (d) MTT, and (e) IDC. The x-axis for all these plots is labeled 'Epsilons' and ranges from 0.000 to 0.175 in increments of 0.025. The y-axis is labeled 'Robustness accuracy' and ranges from 0 to 80 in increments of 20 for subplot (a), and 0 to 60 or 70 in increments of 10 for subplots (b) through (e). Subplot (a) shows four lines representing DSA, DM, MTT, IDC, and ORIGINAL, with the ORIGINAL line reaching the highest accuracy at epsilon 0.000. Subplots (b) through (e) show lines for IPC-10, IPC-20, IPC-50, and IPC-100, with IPC-10 generally showing the highest accuracy. The title for this row of graphs is 'Figure 3: Robustness Evaluation on CIFAR10'. The second row of graphs mirrors the first row in structure, with subplots labeled (a) IPC=50, (b) DSA, (c) DM, (d) MTT, and (e) IDC. The x-axis and y-axis labels and ranges are similar to the first row, though the y-axis in subplots (b) through (e) of the second row ranges from 0 to 40 or 60. The legend in subplot (a) of the second row shows DSA, DM, MTT, IDC, and ORIGINAL. The legends in subplots (b) through (e) of the second row show IPC-10, IPC-20, and IPC-50, with subplot (e) also including ORIGINAL. The title for the second row is 'Figure 4: Robustness Evaluation on CIFAR100'.

Figure 4: Robustness Evaluation on CIFAR100

tillation methods, architectures, and numbers of images per classes can be found in the appendix.

The learned synthetic images of CIFAR10 are visualized in Figure [1.](#page-2-2) We found that the images generated by the four methods correspond to four different styles. DM-generated images are difficult to recognize its content even though it is initialized with real images; in contrast, DSA-generated content becomes less noisy. The pictures generated by MTT are more colorful but contain a lot of strange distortions, and the style is closer to comics. IDC is different from others because, through the multi-formation function, it learns to include four down-sampled subimages in one image. These subimages look clearer and closer to the real image. This method is controversial because it provides more valid information.

Based on the visualization result, we believe that IDC's higher accuracy mainly due to its use of data parameterization, where more down-sampled training samples are included in the same image. It actually provides more valid information to the model. Although there is no study that states that the validity of distilled data is related to the identifiability of the images, IDC and MTT show a substantial lead in accuracy and identifiability at CIFAR10, when the number of images per class 10. We have similar findings on the CIFAR100 dataset, which is illustrated in the appendix.

Figure [5c](#page-5-3) and Figure [5d](#page-5-4) demonstrate the generalization capability of different methods on CIFAR10 and CIFAR100. Compared with ConvNet, the test accuracies drop when transferring to ResNet18 and VGG16. Moreover, the relative ranking of different distilled dataset might not necessarily be preserved when transferred to different architectures. Our results are consistent with reported in [Zhou *et al.*[, 2022\]](#page-9-12).

#### <span id="page-4-0"></span>4.3 Privacy Evaluation

In many related works [Zhou *et al.*[, 2022;](#page-9-12) Chen *et al.*[, 2022\]](#page-7-5), dataset distillations have been implemented as a compression technique to protect membership information of training dataset. However, we think their experiments ignore the most fundamental fact, namely the distillation ratio. To verify this, we fist construct training sets of different sizes, and

<span id="page-5-1"></span>Image /page/5/Figure/0 description: The image displays four plots comparing the accuracy of different distillation methods and network architectures on CIFAR10 and CIFAR100 datasets. Plots (a) and (b) are line graphs showing accuracy versus images per class for CIFAR10 and CIFAR100, respectively. The lines represent DSA, DM, MTT, IDC, and ORIGINAL methods. In plot (a) for CIFAR10, the IDC method achieves the highest accuracy, around 75%, with 100 images per class, while DSA has the lowest, around 62%. The ORIGINAL line is a dashed purple line at 90%. In plot (b) for CIFAR100, the accuracies are generally lower, with IDC reaching about 53% and DSA around 45% at 50 images per class. The ORIGINAL line is at 65%. Plots (c) and (d) are bar charts comparing the accuracy of ConvNet3, ResNet18, and VGG16 for different distillation methods (DSA, DM, MTT, IDC) on CIFAR10 and CIFAR100. In plot (c) for CIFAR10, ConvNet3 performs best across all distillation methods, achieving over 70% accuracy for MTT and IDC. ResNet18 and VGG16 show similar performance, with VGG16 slightly lower. In plot (d) for CIFAR100, the accuracies are significantly lower. ConvNet3 again leads, with IDC reaching about 47%, while ResNet18 and VGG16 are around 40% and 39% respectively for IDC. For DSA on CIFAR100, ConvNet3 is around 45%, ResNet18 around 40%, and VGG16 around 39%.

<span id="page-5-2"></span>Figure 5: Test accuracies of models trained on the distilled data

then perform dataset distillation. Specifically, we randomly select 40, 80, 200, 400, 4000 samples from each class in CI-FAR10 to form a training set. Besides, we also examined the impact of different initializations on MIA attacks. The results as shown in Table [1.](#page-5-5) Note that we do not show the results for MTT because we always fail when using MTT for distillation on smaller datasets. We apply AUC as a metric to measure how success an MIA attack is. The higher AUC value refers to the higher vulnerability of a model against MIA attacks. Specifically, when the AUC value is equal to 50%, the model can perfectly prevent MIA attacks. We find that the distillation rate has a positive correlation with the vulnerability of the model against MIA attacks. Specifically, when the distillation ratio is equal to 25%, the AUC of MIA attacks for IDC can reach an impressive value, 98.07%. Due to the parameterisation applied in IDC, the distilled datasets contain almost the same information as the original datasets. Furthermore, the experimental results also indicate that distilled datasets initialised with real images contain a greater privacy risk. Additionally, the privacy risk of IDC is significantly higher than the other two methods, which is clearly a side effect of the greater amount of information.

Besides, we investigated how the number of classes affected MIA attacks. The experiments were conducted on the above-mentioned tailored datasets with a size of 80 and 400, respectively. We changed the number of classes in the classification model from 2 to 5, and 10. The result shown in Table [2](#page-5-6) demonstrate that even on the distilled dataset, the number of classifications of the target model helps to understand how much privacy information leaked. The larger the number of classes, the more information an attacker can get about the internal state of the model, which is also observed by [\[Shokri](#page-9-3) *et al.*[, 2017\]](#page-9-3).

### <span id="page-5-0"></span>4.4 Fairness Evaluation

We first show in Figure [7a](#page-6-2) how much accuracy drops on different classes after using dataset distillation. In order to conclude more intuitively, we normalize real accuracy of each class from original dataset to 1. In addtion, we reorder the classes from high to low according to the real accuracy, i.e., the original model performs best on Class 1 and worst on 3. In this way, the variation of the same method on different classes represents the unfairness brought by the distillation. Secondly, we find that different classes have different difficulties in learning through distillation. For example, in classes

<span id="page-5-5"></span><span id="page-5-4"></span><span id="page-5-3"></span>

| Origin | Init   | <b>DSA</b> | DM    | <b>IDC</b> |
|--------|--------|------------|-------|------------|
| 40     | random | 73.67      | 75.08 | 86.47      |
|        | real   | 76.46      | 77.73 | 98.07      |
| 80     | random | 63.40      | 63.64 | 69.86      |
|        | real   | 61.89      | 63.23 | 71.59      |
| 200    | random | 55.06      | 55.15 | 59.09      |
|        | real   | 55.03      | 55.59 | 59.73      |
| 400    | random | 52.38      | 52.35 | 54.62      |
|        | real   | 52.52      | 52.89 | 56.34      |

Table 1: MIA attack AUC on CIFAR10 with different original sizes and initialization. In both settings, the distilled datasets contain 10 images per class.

<span id="page-5-6"></span>

| Origin | Classes | DSA          | DM           | IDC          |
|--------|---------|--------------|--------------|--------------|
| 80     | 2       | 49.77        | 49.78        | 56.80        |
|        | 5       | 52.46        | 53.30        | 65.30        |
|        | 10      | <b>61.89</b> | <b>63.20</b> | <b>71.50</b> |
| 400    | 2       | 50.38        | 50.09        | 52.55        |
|        | 5       | 50.46        | 50.67        | 53.24        |
|        | 10      | <b>52.52</b> | <b>52.89</b> | <b>54.45</b> |

Table 2: MIA attack AUC on CIFAR10 with different number of classes.

with relatively higher real accuracy, such as Class 1, 6, 7, and 8, the normalized accuracy obtained by different methods is also relative higher. For Class 2 and 3, the normalized accuracy obtained by different methods is relatively low, which means that more information is lost in the distillation process and learning is more difficult. It can be seen that the loss of distillation information or learning difficulty is closely related to the performance of the original model on these categories. This conclusion inspires that we may need to increase IPC for classes with poor real performance to retain more training information.

We then plot the variance across different classes in terms of loss and accuracy in Figure [6a](#page-6-3) and Figure [6b](#page-6-4) based on Definition [3.](#page-2-3) We clearly find that as the IPC increases, the model's fairness also improve since the variance of loss decreases. We also find different methods have different intrinsic fairness properties. Overall, the MTT consistently outperforms the IDC, DM and DSA in terms of loss-based fairness. As to accuracy-based fairness, we find that the situation is somewhat complicated, with DSA performing best when IPC equals 1. For this reason, we plot accuracy of original and DSA with IPC=1 in Figure [7b.](#page-6-5) We find that this may be due to the fact that the model performs poorly on different categories. To further explain the unfairness between the categories, we visualize the distribution of features across the synthetic data when IPC=50 using t-SNE in Figure [2.](#page-4-1) We find that MTT have the best visualisation result, with almost all distillation features falling in the corresponding cluster, while the other three methods all have distillation feature falling in other clusters. In addition, there is differences across categories. For example, in Figure [2b,](#page-4-2) the features corresponding to Class 0 and Class 2 both have some distillation features falling in each other's cluster, but almost none fall into Class 1. This could be a explanation of unfairness. We suggest using a loss-based fairness rather than accuracy, as *softmax* provides more information across classes, whereas accuracy loses such information due to argmax operation.

<span id="page-6-3"></span>Image /page/6/Figure/2 description: The image contains two plots side-by-side. The left plot, labeled "(a) Loss-based fairness", shows the "Variance of loss" on the y-axis and "Images per class" on the x-axis, with values ranging from 1 to 100. Five lines represent different methods: ORIGINAL (purple, flat at 0.5), DSA (red squares, decreasing from 3.5 to 1.4), DM (green circles, decreasing from 3.7 to 1.4), MTT (blue triangles, decreasing from 1.4 to 0.8), and IDC (cyan crosses, decreasing from 2.1 to 1.4). The right plot, labeled "(b) Class-wise accuracy-based fairness", shows the "Variance of classwise accuracy" on the y-axis and "Images per class" on the x-axis, with values ranging from 1 to 100. The same five methods are plotted: ORIGINAL (purple, flat at 0.005), DSA (red squares, increasing from 0.011 to 0.020), DM (green circles, decreasing from 0.014 to 0.012), MTT (blue triangles, decreasing from 0.027 to 0.007), and IDC (cyan crosses, decreasing from 0.019 to 0.008).

Figure 6: Fairness of a model evaluated according to Definition [3](#page-2-3)

#### <span id="page-6-1"></span>4.5 Robustness Evaluation

The robustness accuracy of different methods on CIFAR10 and CIFAR100 are illustrated in Figure [3](#page-4-3) and Figure [4,](#page-4-3) where the horizontal coordinate represents the strength of the added noise, the vertical coordinate represents the accuracy in the classification task, and higher values represent greater resistance to robustness attacks. The first picture in each row

<span id="page-6-2"></span>Image /page/6/Figure/6 description: This is a bar chart titled "(a) Normalized accuracy on CIFAR10 at IPC-50". The y-axis represents "Normalized accuracy (%)" and ranges from 0 to 100. The x-axis represents "Class" and shows classes labeled 1, 6, 7, 8, 9, 4, 0, 5, 2, and 3. There are five bars for each class, representing DSA, DM, MTT, IDC, and ORIGINAL, with the ORIGINAL bars being the tallest, reaching close to 100% accuracy for most classes. The bars generally decrease in height from left to right, with some fluctuations. The legend indicates the colors for DSA (blue), DM (orange), MTT (green), IDC (red), and ORIGINAL (purple).

Image /page/6/Figure/7 description: A bar chart displays the accuracy of two models, IPC-1 and ORIGINAL, across ten different categories labeled 0 through 9 on the x-axis. The y-axis represents accuracy in percentage, ranging from 0 to 100. For category 0, ORIGINAL has an accuracy of approximately 92%, while IPC-1 has an accuracy of about 55%. In category 1, ORIGINAL reaches nearly 98%, and IPC-1 is around 30%. Category 2 shows ORIGINAL at about 84% and IPC-1 at roughly 24%. Category 3 has ORIGINAL at approximately 72% and IPC-1 at about 15%. Category 4 shows ORIGINAL at around 94% and IPC-1 at roughly 22%. Category 5 has ORIGINAL at about 85% and IPC-1 at roughly 26%. Category 6 shows ORIGINAL at approximately 95% and IPC-1 at about 33%. Category 7 has ORIGINAL at around 95% and IPC-1 at roughly 40%. Category 8 shows ORIGINAL at approximately 95% and IPC-1 at about 28%. Finally, category 9 has ORIGINAL at about 95% and IPC-1 at roughly 35%.

<span id="page-6-5"></span>Class (b) Accuracy on CIFAR10 of original dataset and DSA's dataset at IPC-

Figure 7: Figure [7a](#page-6-2) presented the normalized accuracy of different methods at IPC-50. The order the class is sorted descending according to the original accuracy obtained from original model. Figure [7b](#page-6-5) displayed the accuracy of each class evaluated on DSA at IPC-1

shows the results of different distillation methods when the number of images per class (IPC) is set to 50, and the following four represent the change of robustness with IPC in different methods. We found that three of the four methods we experimented with: DM, DSA, and MTT were all able to outperform the original model in terms of robustness when the noise exceeded a certain intensity. While on CIFAR100, only DSA and DM would perform better when the noise was sufficiently high. In both datasets, the MTT method always exhibited the worst robustness. We also compare the accuracies with varying IPCs, however,We don't not find a strong relationship between robustness and IPC.

### <span id="page-6-0"></span>5 Related Work

1

<span id="page-6-4"></span>Wang et al. [Wang *et al.*[, 2018\]](#page-9-0) was the first to propose the concept of dataset distillation (also known as dataset condensation). They proposed to using bi-level optimization to synthesize a compact dataset that has better storage efficiency than the coresets. Since then, more and more methods have been proposed to improve distillation performance, for example using distribution matching [\[Zhao and Bilen, 2021\]](#page-9-2), gradient matching [Zhao *et al.*[, 2021\]](#page-9-1), training trajectory matching [\[Cazenavette](#page-7-1) *et al.*, 2022] and augmentation techniques [\[Zhao Bo, 2021\]](#page-9-13), kernel methods [\[Nguyen](#page-9-14) *et al.*, 2020; Zhou *et al.*[, 2022;](#page-9-12) Loo *et al.*[, 2022\]](#page-8-9), and data parametrization [Kim *et al.*[, 2022\]](#page-8-1) with GAN [Such *et al.*[, 2020;](#page-9-15) [Zhao and Bilen, 2022\]](#page-9-16) or factorization [Lee *et al.*[, 2022;](#page-8-10) Liu *et al.*[, 2022\]](#page-8-11).

There are other works exploring dataset distillation as in

our work. In [Wang *et al.*[, 2018\]](#page-9-0), it was shown that using dataset distillation can make data poisoning attacks more effective. In [Zhou *et al.*[, 2022\]](#page-9-12), it was claimed that dataset distillation is robust to membership inference attacks. Our experimental results empirically show that their conclusions are not entirely correct. In [\[Nguyen](#page-9-14) *et al.*, 2020; Loo *et al.*[, 2022\]](#page-8-9), they built a  $\rho$ -corrupted dataset to protect privacy, and the image information has a  $\rho$  ratio of pixels that remains unchanged during the optimization process. The last one that is similar to our work is DC-Bench [Cui *et al.*[, 2022\]](#page-7-6). However, they only compared and analyzed existing dataset distillation methods in terms of compression ratio, transferability and neural architecture search. In contrast, we focus on the ML security and potential risks of the dataset distillation.

# <span id="page-7-2"></span>6 Conclusion

Conclusion. This work systematically evaluates current dataset distillation methods from the perspective of ML security. Specifically, it provides a large-scale benchmark for evaluating dataset distillation, where we design and implement experiments regarding privacy, robustness, and fairness. We find that dataset distillation cannot help prevent membership inference attacks, and it increases the unfairness of model performance across different classes. We also demonstrate experimentally that it impacts the robustness of models to varying degrees. This is the first work that bridges the gap between dataset distillation and security studies.

# A Hyperparameters & Hardwares

For all distilled methods considered in this paper, we use the default hyperparameters given by the authors. That is, we run 1K iterations to create a synthetic dataset in DSA. As for IPC 50 and 100, we manually set the hyperparameters *inner* and *outer loop* in DSA. For MTT, we use the same hyperparameters reported by the authors except for disabling ZCA whitening for a fair comparison. For both DM and IDC, the settings are the same as those provided by the authors. Furthermore, we run all our experiments on the same host with Tesla V100 GPU with 32 GB. As a performance comparison, it takes 2000 epochs to train ConvNet3, Resnet18, and VGG16 to ensure coverage. We also repeat the training 3 times to measure the variance of accuracy.

# B Detailed accuracy under different IPCs and models

In Table [3,](#page-8-12) we report the detailed accuracy of model trained by distilled dataset. It contains accuracy results about dataset CIFAR10 and CIFAR100 with different IPCs on different models, i.e., ConvNet3, Restnet18 and VGG16.

### C Robustness

In Figure [8,](#page-9-17) we investigate the impact of DSA augmentation on DM, MTT, and IDC methods. We generate two different distilled datasets by enabling and disabling DSA augmentation of each algorithm. Then, we train the models on each dataset to obtain the best validation accuracy. Last, we use DeepFoolAttack to evaluate the robustness of each model to analyze the influence of DSA. We see that DSA augmentation can improve the robustness to some extent since the red line in Figure [8](#page-9-17) is always slightly above the green curve.

# D Visualization of distilled dataset

In Figure [10,](#page-11-0) [9,](#page-10-0) we show the images of a synthetic dataset of CIFAR10 and CIFAR100 with IPC 1 respectively, which can help us have a better understanding about the dataset pattern of different distilled algorithms. Interestingly, we find that the distilled images via DM and MTT can not be recognized directly compared with other algorithms in both CIFAR10 and CIFAR100 datasets, e.g. Figure [10b](#page-11-1) looks like a chessboard.

# E Distribution of distilled dataset

In Figure [11,](#page-11-2) we present 10-class distribution of CIFAR10 with IPC 50. We use a pretrained ConvNet3 model to generate a 2048-dimension embedding and project that embedding into a 2-dimension plane by t-SNE with hyperparameter *perplexity* 50. We see that IDC has more than 50 images per class since IDC algorithm uses a multi-formation function f to decode condensed dataset into training dataset. Visually, we find that the projected distilled images from DM, MTT, IDC can spread over the cluster better than DSA. As an example in DSA, class 2 has virtually no points at the center of the cluster. However, DM, MTT, IDC are well suited for class 2.

# References

- <span id="page-7-0"></span>[Bachem *et al.*, 2017] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-7-4"></span>[Benz et al., 2021] Philipp Benz, Chaoning Zhang, Adil Karjauv, and In So Kweon. Robustness may be at odds with fairness: An empirical study on class-wise accuracy. In *NeurIPS 2020 Workshop on Pre-registration in Machine Learning*, pages 325–342. PMLR, 2021.
- <span id="page-7-1"></span>[Cazenavette *et al.*, 2022] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-7-5"></span>[Chen *et al.*, 2022] Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. *arXiv preprint arXiv:2211.04446*, 2022.
- <span id="page-7-6"></span>[Cui *et al.*, 2022] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022.
- <span id="page-7-3"></span>[Fredrikson *et al.*, 2015] Matt Fredrikson, Somesh Jha, and Thomas Ristenpart. Model inversion attacks that exploit confidence information and basic countermeasures. In *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, pages 1322–1333, 2015.

<span id="page-8-12"></span>

| Dataset  | Image per class | <b>Test Model</b> | DSA              | DM               | <b>MTT</b>       | $\overline{IDC}$ | Full dataset     |
|----------|-----------------|-------------------|------------------|------------------|------------------|------------------|------------------|
|          | $\mathbf{1}$    | ConvNet3          | $29.50 \pm 1.22$ | $28.84 \pm 0.52$ | $41.13 \pm 0.50$ | $48.07 \pm 0.16$ | $89.61 \pm 0.26$ |
|          |                 | Resnet18          | $28.52 \pm 0.29$ | $21.74 \pm 0.92$ | $34.00 \pm 1.17$ | $41.34 \pm 0.54$ | $88.85 \pm 5.27$ |
|          |                 | VGG16             | $20.56 \pm 4.28$ | $14.01 \pm 4.01$ | $24.15 \pm 1.36$ | $37.82 \pm 4.64$ | $94.76 \pm 0.04$ |
|          |                 | ConvNet           | $50.29 \pm 0.23$ | $53.07 \pm 0.91$ | $63.23 \pm 0.55$ | $65.13 \pm 0.12$ | $89.61 \pm 0.26$ |
|          | 10              | Resnet18          | $40.26 \pm 0.64$ | $38.52 \pm 1.12$ | $48.75 \pm 1.79$ | $63.56 \pm 0.91$ | $88.85 \pm 5.27$ |
|          |                 | VGG16             | $36.05 \pm 1.85$ | $35.47 \pm 2.29$ | $52.05 \pm 0.57$ | $59.35 \pm 1.32$ | $94.76 \pm 0.04$ |
|          | 20              | ConvNet           | $57.46 \pm 0.32$ | $60.83 \pm 0.24$ | $65.31 \pm 0.43$ | $71.68 \pm 0.24$ | $89.61 \pm 0.26$ |
| CIFAR10  |                 | Resnet18          | $46.40 \pm 0.39$ | $47.41 \pm 0.31$ | $61.12 \pm 0.71$ | $69.35 \pm 1.18$ | $88.85 \pm 5.27$ |
|          |                 | VGG16             | $38.10 \pm 0.64$ | $48.65 \pm 1.59$ | $56.23 \pm 3.15$ | $66.81 \pm 0.41$ | $94.76 \pm 0.04$ |
|          |                 | ConvNet           | $61.28 \pm 0.37$ | $65.63 \pm 0.08$ | $71.64 \pm 0.30$ | $75.18 \pm 0.40$ | $89.61 \pm 0.26$ |
|          | 50              | Resnet18          | $52.99 \pm 0.18$ | $62.34 \pm 0.57$ | $68.94 \pm 0.14$ | $72.14 \pm 0.10$ | $88.85 \pm 5.27$ |
|          |                 | VGG16             | $45.00 \pm 0.27$ | $56.81 \pm 3.89$ | $69.08 \pm 0.22$ | $72.95 \pm 0.26$ | $94.76 \pm 0.04$ |
|          | 100             | ConvNet           | $63.14 \pm 0.14$ | $69.31 \pm 0.16$ | $72.39 \pm 0.13$ | $75.00 \pm 0.04$ | $89.61 \pm 0.26$ |
|          |                 | Resnet18          | $55.71 \pm 0.08$ | $68.81 \pm 0.49$ | $69.94 \pm 0.21$ | $74.32 \pm 0.26$ | $88.85 \pm 5.27$ |
|          |                 | VGG16             | $49.66 \pm 0.85$ | $63.07 \pm 4.08$ | $70.60 \pm 0.27$ | $74.02 \pm 0.02$ | $94.76 \pm 0.04$ |
|          | $\mathbf{1}$    | ConvNet           | $16.01 \pm 0.26$ | $12.33 \pm 0.11$ | $21.47 \pm 0.36$ | $28.68 \pm 0.41$ | $64.5 \pm 0.15$  |
|          |                 | Resnet18          | $10.1 \pm 0.51$  | $3.02 \pm 0.14$  | $10.51 \pm 0.03$ | $21.67 \pm 0.15$ | $72.57 \pm 0.14$ |
|          |                 | VGG16             | $10.67 \pm 0.35$ | $5.06 \pm 0.43$  | $7.33 \pm 0.19$  | $21.43 \pm 0.97$ | $74.05 \pm 4.54$ |
|          |                 | ConvNet           | $32.96 \pm 0.19$ | $32.24 \pm 0.26$ | $36.74 \pm 0.17$ | $45.78 \pm 0.24$ | $64.35 \pm 0.15$ |
|          | 10              | Resnet18          | 24.14 $\pm$ 0.27 | $26.45 \pm 0.67$ | $31.70 \pm 0.21$ | $41.42 \pm 0.40$ | $72.71 \pm 0.14$ |
|          |                 | VGG16             | $16.06 \pm 0.86$ | $24.63 \pm 2.28$ | $31.75 \pm 0.61$ | $41.86 \pm 0.56$ | $74.05 \pm 4.54$ |
|          | $20\,$          | ConvNet           | $37.20 \pm 0.29$ | $37.37 \pm 0.18$ | $33.69 \pm 0.32$ | $49.48 \pm 0.03$ | $64.35 \pm 0.15$ |
| CIFAR100 |                 | Resnet18          | $28.28 \pm 0.63$ | $33.28 \pm 0.38$ | $31.13 \pm 0.42$ | $47.24 \pm 0.30$ | $72.71 \pm 0.14$ |
|          |                 | VGG16             | $24.56 \pm 0.48$ | $32.83 \pm 0.39$ | $27.60 \pm 0.31$ | $44.85 \pm 0.11$ | $74.05 \pm 4.54$ |
|          |                 | ConvNet           | $45.14 \pm 0.22$ | $45.62 \pm 0.15$ | $45.73 \pm 0.40$ | $53.62 \pm 0.20$ | $64.35 \pm 0.15$ |
|          | 50              | Resnet18          | $44.13 \pm 0.21$ | $44.65 \pm 0.62$ | $42.19 \pm 0.29$ | $51.73 \pm 0.23$ | $72.71 \pm 0.14$ |
|          |                 | VGG16             | $38.88 \pm 0.29$ | $40.60 \pm 0.42$ | $46.46 \pm 0.15$ | $53.49 \pm 0.46$ | $74.05 \pm 4.54$ |

Table 3: Cross-architecture accuracy results of method DSA, DM, MTT and IDC on CIFAR10 with IPC 1, 10, 20, 50 and 100, and CIFAR100 with IPC 1, 10, 20, 50.

- <span id="page-8-2"></span>[Geng *et al.*, 2021] Jiahui Geng, Yongli Mou, Feifei Li, Qing Li, Oya Beyan, Stefan Decker, and Chunming Rong. Towards general deep leakage in federated learning. *arXiv preprint arXiv:2110.09074*, 2021.
- <span id="page-8-3"></span>[Gong and Liu, 2016] Neil Zhenqiang Gong and Bin Liu. You are who you know and how you behave: Attribute inference attacks via users' social friends and behaviors. *arXiv preprint arXiv:1606.05893*, 2016.
- <span id="page-8-4"></span>[Hooker *et al.*, 2020] Sara Hooker, Nyalleng Moorosi, Gregory Clark, Samy Bengio, and Emily Denton. Characterising bias in compressed models. *arXiv preprint arXiv:2010.03058*, 2020.
- <span id="page-8-0"></span>[Isenko *et al.*, 2022] Alexander Isenko, Ruben Mayer, Jeffrey Jedele, and Hans-Arno Jacobsen. Where is my training bottleneck? hidden trade-offs in deep learning preprocessing pipelines. *arXiv preprint arXiv:2202.08679*, 2022.
- <span id="page-8-1"></span>[Kim *et al.*, 2022] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. *arXiv preprint arXiv:2205.14959*, 2022.
- <span id="page-8-7"></span>[Le and Yang, 2015] Ya Le and Xuan S. Yang. Tiny imagenet visual recognition challenge. 2015.

- <span id="page-8-10"></span>[Lee *et al.*, 2022] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022.
- <span id="page-8-5"></span>[Li *et al.*, 2021] Tian Li, Shengyuan Hu, Ahmad Beirami, and Virginia Smith. Ditto: Fair and robust federated learning through personalization. In *International Conference on Machine Learning*, pages 6357–6368. PMLR, 2021.
- <span id="page-8-6"></span>[Lin *et al.*, 2022] Shiyun Lin, Yuze Han, Xiang Li, and Zhihua Zhang. Personalized federated learning towards communication efficiency, robustness and fairness. *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-8-8"></span>[Liu *et al.*, 2015] Ziwei Liu, Ping Luo, Xiaogang Wang, and Xiaoou Tang. Deep learning face attributes in the wild. In *Proceedings of International Conference on Computer Vision (ICCV)*, December 2015.
- <span id="page-8-11"></span>[Liu *et al.*, 2022] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *arXiv preprint arXiv:2210.16774*, 2022.
- <span id="page-8-9"></span>[Loo *et al.*, 2022] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.

<span id="page-9-17"></span>Image /page/9/Figure/0 description: The image displays three line graphs side-by-side, each titled "Robustness of CIFAR10". The x-axis for all graphs is labeled "Epsilons" and ranges from 0.0 to 0.13 in increments of 0.01. The y-axis for all graphs is labeled "Robustness accuracy" and ranges from 0 to 70. Each graph plots two lines representing different methods. The first graph shows "DM-DSA" (red squares) and "DM-NO-DSA" (green circles). The second graph shows "IDC" (red squares) and "IDC-NO-DSA" (green circles). The third graph shows "MTT" (red squares) and "MTT-NO-DSA" (green circles). All lines show a decreasing trend in robustness accuracy as epsilon increases.

Figure 8: Robust accuracy of dataset trained by enabling DSA and disabling DSA

- <span id="page-9-8"></span>[Ma *et al.*, 2022] Xinsong Ma, Zekai Wang, and Weiwei Liu. On the tradeoff between robustness and fairness. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-9-11"></span>[Moosavi-Dezfooli et al., 2016] Seyed-Mohsen Moosavi-Dezfooli, Alhussein Fawzi, and Pascal Frossard. Deepfool: a simple and accurate method to fool deep neural networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2574–2582, 2016.
- <span id="page-9-14"></span>[Nguyen *et al.*, 2020] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-9-10"></span>[Russakovsky *et al.*, 2015] Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael Bernstein, Alexander C. Berg, and Li Fei-Fei. ImageNet Large Scale Visual Recognition Challenge. *International Journal of Computer Vision (IJCV)*, 115(3):211– 252, 2015.
- <span id="page-9-3"></span>[Shokri *et al.*, 2017] Reza Shokri, Marco Stronati, Congzheng Song, and Vitaly Shmatikov. Membership inference attacks against machine learning models. In *2017 IEEE symposium on security and privacy (SP)*, pages 3– 18. IEEE, 2017.
- <span id="page-9-7"></span>[Silva *et al.*, 2021] Andrew Silva, Pradyumna Tambwekar, and Matthew Gombolay. Towards a comprehensive understanding and accurate evaluation of societal biases in pretrained transformers. In *Proceedings of the 2021 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies*, pages 2383–2389, 2021.
- <span id="page-9-15"></span>[Such *et al.*, 2020] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020.
- <span id="page-9-0"></span>[Wang *et al.*, 2018] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.

- <span id="page-9-4"></span>[Xu *et al.*, 2020] Han Xu, Yao Ma, Hao-Chen Liu, Debayan Deb, Hui Liu, Ji-Liang Tang, and Anil K Jain. Adversarial attacks and defenses in images, graphs and text: A review. *International Journal of Automation and Computing*, 17(2):151–178, 2020.
- <span id="page-9-9"></span>[Xu *et al.*, 2021] Han Xu, Xiaorui Liu, Yaxin Li, Anil Jain, and Jiliang Tang. To be robust or to be fair: Towards fairness in adversarial training. In *International Conference on Machine Learning*, pages 11492–11501. PMLR, 2021.
- <span id="page-9-5"></span>[Zhang *et al.*, 2020] Yuheng Zhang, Ruoxi Jia, Hengzhi Pei, Wenxiao Wang, Bo Li, and Dawn Song. The secret revealer: Generative model-inversion attacks against deep neural networks. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 253–261, 2020.
- <span id="page-9-2"></span>[Zhao and Bilen, 2021] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *ArXiv*, abs/2110.04181, 2021.
- <span id="page-9-16"></span>[Zhao and Bilen, 2022] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-9-1"></span>[Zhao *et al.*, 2021] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021.
- <span id="page-9-13"></span>[Zhao Bo, 2021] Bilen Hakan Zhao Bo. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674– 12685. PMLR, 2021.
- <span id="page-9-12"></span>[Zhou *et al.*, 2022] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022.
- <span id="page-9-6"></span>[Zhu *et al.*, 2019] Ligeng Zhu, Zhijian Liu, and Song Han. Deep leakage from gradients. *Advances in neural information processing systems*, 32, 2019.

<span id="page-10-0"></span>Image /page/10/Figure/0 description: The image displays two grids of smaller images, labeled (a) DSA and (b) DM. Grid (a) contains a variety of colorful images, including portraits, landscapes, objects like bicycles and trophies, and abstract patterns. Grid (b) primarily features abstract patterns composed of dots and lines in various colors and orientations, such as checkered patterns, diagonal stripes, and scattered dots. Below these grids, a row of smaller, diverse images is visible, including fruits, faces, and abstract designs.

Image /page/10/Figure/1 description: The image displays two grids of smaller images, labeled (c) MTT on the left and (d) IDC on the right. Each grid contains 100 smaller images arranged in a 10x10 formation. The images in the MTT grid appear to be more varied and colorful, with some recognizable objects like fruits, bicycles, and cars, though many are abstract or distorted. The IDC grid predominantly features abstract, swirling patterns in muted colors, with occasional hints of recognizable forms like flowers or landscapes, but overall appearing more uniform and less distinct than the MTT grid.

(d) IDC

Figure 9: Distilled dataset on CIFAR100 with IPC-1

<span id="page-11-0"></span>Image /page/11/Figure/0 description: The image displays four rows of generated images, labeled (a) DSA, (b) DM, (c) MTT, and (d) IDC. Row (a) contains 10 images, each appearing to be a stylized representation of an animal or object, with a somewhat abstract and colorful appearance. Row (b) contains 10 images, all of which are patterned with black and white or colored squares and diagonal lines, resembling checkerboard or striped textures. Row (c) contains 10 images that are highly abstract and colorful, with swirling patterns and vibrant hues, possibly representing artistic interpretations of various subjects. Row (d) contains 10 images, each composed of a grid of smaller images, with the overall theme appearing to be animals, possibly horses or deer, in various poses and settings.

<span id="page-11-1"></span>Image /page/11/Figure/1 description: Figure 10: Distilled dataset on CIFAR10 with IPC-1

<span id="page-11-2"></span>Image /page/11/Figure/2 description: This image displays four scatter plots, each titled "CIFAR10: T-SNE projection." Each plot visualizes data points projected onto two dimensions, labeled "x1" and "x2." A legend in each plot indicates that the data points represent classes 0 through 9, with different colors and shapes assigned to each class. The plots are labeled (a) DSA, (b) DM, (c) MTT, and (d) IDC. The overall distribution of points in each plot shows clusters that roughly correspond to the different classes, with some overlap between them. The points are colored in shades of red, orange, yellow, green, cyan, blue, and purple, and are represented by circles and triangles.

Figure 11: Data distribution of real images and synthetic images learned in CIFAR10 with IPC-50.