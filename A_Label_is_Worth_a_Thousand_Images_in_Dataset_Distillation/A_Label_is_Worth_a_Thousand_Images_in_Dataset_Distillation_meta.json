{"table_of_contents": [{"title": "A Label is Worth a Thousand Images\nin Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[166.5, 99.0], [444.65625, 99.0], [444.65625, 135.6416015625], [166.5, 135.6416015625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 251.173828125], [328.5, 251.173828125], [328.5, 261.615234375], [282.75, 261.615234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 520.5], [191.25, 520.5], [191.25, 531.73828125], [107.25, 531.73828125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 198.75], [198.75, 198.75], [198.75, 210.568359375], [106.98046875, 210.568359375]]}, {"title": "3 Soft Labels are Crucial for Distillation", "heading_level": null, "page_id": 2, "polygon": [[106.5, 666.75], [322.734375, 666.75], [322.734375, 677.14453125], [106.5, 677.14453125]]}, {"title": "3.1 Background on dataset distillation and a simple soft label baseline", "heading_level": null, "page_id": 2, "polygon": [[106.00927734375, 686.25], [411.75, 686.25], [411.75, 695.70703125], [106.00927734375, 695.70703125]]}, {"title": "3.2 Benchmarking distillation methods against the soft label baseline", "heading_level": null, "page_id": 3, "polygon": [[106.5, 447.0], [408.0, 447.0], [408.0, 457.1015625], [106.5, 457.1015625]]}, {"title": "4 Good Soft Labels Require Structured Information", "heading_level": null, "page_id": 4, "polygon": [[107.25, 450.0], [384.0, 450.52734375], [384.0, 462.12890625], [107.25, 462.12890625]]}, {"title": "4.1 General observations from the soft label baseline", "heading_level": null, "page_id": 4, "polygon": [[107.20458984375, 555.0], [339.0, 555.0], [339.0, 566.15625], [107.20458984375, 566.15625]]}, {"title": "4.2 Structured information in soft labels and its importance for distillation", "heading_level": null, "page_id": 5, "polygon": [[107.25, 417.75], [432.0, 417.75], [432.0, 428.09765625], [107.25, 428.09765625]]}, {"title": "4.3 Trading data with knowledge", "heading_level": null, "page_id": 6, "polygon": [[106.5, 471.75], [256.5, 471.75], [256.5, 482.23828125], [106.5, 482.23828125]]}, {"title": "5 Soft Labels are Not Created Equal", "heading_level": null, "page_id": 7, "polygon": [[106.5, 636.75], [303.0, 636.75], [303.0, 648.52734375], [106.5, 648.52734375]]}, {"title": "5.1 Expert ensemble", "heading_level": null, "page_id": 8, "polygon": [[106.5, 180.75], [203.501953125, 180.75], [203.501953125, 190.845703125], [106.5, 190.845703125]]}, {"title": "5.2 Learning soft labels through data distillation methods", "heading_level": null, "page_id": 8, "polygon": [[106.5, 304.5], [360.0, 304.5], [360.0, 314.595703125], [106.5, 314.595703125]]}, {"title": "6 Discussion and Conclusions", "heading_level": null, "page_id": 9, "polygon": [[106.5, 279.59765625], [268.9453125, 279.59765625], [268.9453125, 291.5859375], [106.5, 291.5859375]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 10, "polygon": [[107.1298828125, 72.0], [202.7548828125, 72.0], [202.7548828125, 83.53125], [107.1298828125, 83.53125]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 158.25], [165.0, 158.25], [165.0, 168.802734375], [107.25, 168.802734375]]}, {"title": "A Soft label baseline", "heading_level": null, "page_id": 12, "polygon": [[106.5, 71.54296875], [222.92578125, 71.54296875], [222.92578125, 84.111328125], [106.5, 84.111328125]]}, {"title": "A.1 Experimental Details", "heading_level": null, "page_id": 12, "polygon": [[106.98046875, 89.47705078125], [226.810546875, 89.47705078125], [226.810546875, 101.2236328125], [106.98046875, 101.2236328125]]}, {"title": "A.2 Label generation and epoch tuning for existing methods", "heading_level": null, "page_id": 12, "polygon": [[107.25, 415.3359375], [372.638671875, 415.3359375], [372.638671875, 426.9375], [107.25, 426.9375]]}, {"title": "A.3 Downsized ImageNet-1K results", "heading_level": null, "page_id": 13, "polygon": [[106.5, 150.0], [270.75, 150.0], [270.75, 160.48828125], [106.5, 160.48828125]]}, {"title": "A.4 Variance from expert training seed and random image seed", "heading_level": null, "page_id": 13, "polygon": [[107.25, 342.75], [385.5, 342.75], [385.5, 352.880859375], [107.25, 352.880859375]]}, {"title": "A.5 Hyperparameters for expert training and soft label generation", "heading_level": null, "page_id": 13, "polygon": [[106.5, 652.5], [399.0, 652.5], [399.0, 662.8359375], [106.5, 662.8359375]]}, {"title": "B Image selection using cross entropy", "heading_level": null, "page_id": 14, "polygon": [[106.5, 337.5], [308.25, 337.5], [308.25, 348.8203125], [106.5, 348.8203125]]}, {"title": "C Additional Analysis Results", "heading_level": null, "page_id": 14, "polygon": [[107.25, 666.0], [270.73828125, 666.0], [270.73828125, 677.53125], [107.25, 677.53125]]}, {"title": "C.1 Soft label visualization", "heading_level": null, "page_id": 14, "polygon": [[106.98046875, 686.25], [229.5, 686.25], [229.5, 696.48046875], [106.98046875, 696.48046875]]}, {"title": "C.2 i-th label swapping test", "heading_level": null, "page_id": 15, "polygon": [[106.083984375, 531.75], [232.5, 531.75], [232.5, 542.1796875], [106.083984375, 542.1796875]]}, {"title": "C.3 Data-knowledge Scaling Law", "heading_level": null, "page_id": 15, "polygon": [[106.5, 664.5], [258.0, 664.5], [258.0, 675.2109375], [106.5, 675.2109375]]}, {"title": "D Details on label learning with distillation methods", "heading_level": null, "page_id": 16, "polygon": [[106.5, 314.208984375], [384.29296875, 314.208984375], [384.29296875, 325.810546875], [106.5, 325.810546875]]}, {"title": "D.1 Learn label with MTT", "heading_level": null, "page_id": 16, "polygon": [[106.5, 334.125], [229.201171875, 334.125], [229.201171875, 344.953125], [106.5, 344.953125]]}, {"title": "D.2 Learn label with BPTT", "heading_level": null, "page_id": 16, "polygon": [[107.1298828125, 563.8359375], [232.0400390625, 563.8359375], [232.0400390625, 574.6640625], [107.1298828125, 574.6640625]]}, {"title": "Algorithm 1 Learn soft label with BPTT", "heading_level": null, "page_id": 17, "polygon": [[107.25, 73.5], [271.5, 73.5], [271.5, 83.6279296875], [107.25, 83.6279296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 55], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5978, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 74], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 903, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 54], ["ListItem", 3], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["TableCell", 69], ["Line", 58], ["Text", 3], ["Reference", 3], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["TableCell", 164], ["Line", 76], ["Text", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 733], ["Line", 117], ["Text", 4], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3410, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 85], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1022, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 71], ["Text", 4], ["ListItem", 3], ["Caption", 2], ["Figure", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 72], ["TableCell", 29], ["Text", 5], ["Reference", 4], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 820, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 52], ["Text", 7], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 53], ["ListItem", 22], ["Reference", 22], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 36], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 55], ["Text", 8], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["TableCell", 140], ["Line", 46], ["Text", 8], ["Reference", 5], ["SectionHeader", 3], ["Table", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10414, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 573], ["TableCell", 322], ["Line", 57], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Text", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5941, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 46], ["ListItem", 4], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 63], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 95], ["TextInlineMath", 2], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 980, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/A_Label_is_Worth_a_Thousand_Images_in_Dataset_Distillation"}