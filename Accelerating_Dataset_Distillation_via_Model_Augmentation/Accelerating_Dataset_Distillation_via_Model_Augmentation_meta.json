{"table_of_contents": [{"title": "Accelerating Dataset Distillation via Model Augmentation", "heading_level": null, "page_id": 0, "polygon": [[118.1865234375, 106.5], [474.75, 106.5], [474.75, 119.109375], [118.1865234375, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 256.5], [191.25, 256.5], [191.25, 267.22265625], [144.75, 267.22265625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 485.25], [127.7490234375, 485.25], [127.7490234375, 496.16015625], [48.75, 496.16015625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 135.0], [393.0, 135.0], [393.0, 146.1796875], [307.5, 146.1796875]]}, {"title": "2.1. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[306.896484375, 155.25], [418.5, 155.25], [418.5, 165.4189453125], [306.896484375, 165.4189453125]]}, {"title": "2.2. Efficient Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[307.5, 446.25], [460.5, 446.25], [460.5, 457.1015625], [307.5, 457.1015625]]}, {"title": "3. Preliminary", "heading_level": null, "page_id": 1, "polygon": [[307.5, 659.25], [384.0, 659.25], [384.0, 669.796875], [307.5, 669.796875]]}, {"title": "4. Method", "heading_level": null, "page_id": 2, "polygon": [[48.0, 617.25], [102.0, 617.25], [102.0, 628.03125], [48.0, 628.03125]]}, {"title": "4.1. Overview", "heading_level": null, "page_id": 2, "polygon": [[48.0, 636.75], [114.0, 636.75], [114.0, 646.98046875], [48.0, 646.98046875]]}, {"title": "4.2. Early-Stage Models: Initializing with Informa-\ntive Parameter Space", "heading_level": null, "page_id": 2, "polygon": [[307.5, 409.5], [544.5, 409.5], [544.5, 431.19140625], [307.5, 431.19140625]]}, {"title": "4.3. Parameter Perturbation: Diversifying Param-\neter Space", "heading_level": null, "page_id": 3, "polygon": [[48.75, 116.25], [286.576171875, 116.25], [286.576171875, 138.83203125], [48.75, 138.83203125]]}, {"title": "4.4. Training Algorithm", "heading_level": null, "page_id": 3, "polygon": [[48.75, 457.5], [162.0, 457.5], [162.0, 468.31640625], [48.75, 468.31640625]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 3, "polygon": [[307.5, 526.5], [386.25, 526.5], [386.25, 537.92578125], [307.5, 537.92578125]]}, {"title": "5.1. Experimental Setups", "heading_level": null, "page_id": 3, "polygon": [[307.5, 589.5], [426.0, 589.5], [426.0, 600.1875], [307.5, 600.1875]]}, {"title": "5.2. Condensed Data Evaluation", "heading_level": null, "page_id": 5, "polygon": [[48.75, 565.5], [200.25, 565.5], [200.25, 575.82421875], [48.75, 575.82421875]]}, {"title": "5.3. Analysis", "heading_level": null, "page_id": 6, "polygon": [[48.0, 612.75], [109.5, 612.75], [109.5, 623.00390625], [48.0, 623.00390625]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 550.5], [378.615234375, 550.5], [378.615234375, 561.90234375], [307.5, 561.90234375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 84.0146484375], [48.75, 84.0146484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["Line", 84], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["Footnote", 2], ["<PERSON>Footer", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4507, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 102], ["Text", 11], ["SectionHeader", 4], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 114], ["Text", 8], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5591, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 904], ["Line", 114], ["Text", 8], ["SectionHeader", 4], ["TextInlineMath", 3], ["Equation", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["TableCell", 370], ["Line", 57], ["Text", 4], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 16961, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 110], ["Text", 7], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1851, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 108], ["TableCell", 66], ["Text", 12], ["Reference", 4], ["Table", 2], ["Caption", 2], ["SectionHeader", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3192, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 99], ["TableCell", 80], ["Text", 4], ["Reference", 4], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1534, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 113], ["ListItem", 32], ["Reference", 32], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 91], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Accelerating_Dataset_Distillation_via_Model_Augmentation"}