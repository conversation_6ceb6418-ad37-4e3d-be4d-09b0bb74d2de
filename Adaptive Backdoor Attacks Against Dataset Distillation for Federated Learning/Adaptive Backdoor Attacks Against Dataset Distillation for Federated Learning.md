# Adaptive Backdoor Attacks Against Dataset Distillation for Federated Learning

Ze Chai∗†, <PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON>†, <PERSON>†, <PERSON><PERSON><PERSON><PERSON>†, <PERSON><PERSON><PERSON><PERSON>†

<sup>∗</sup> Beijing University of Posts and Telecommunications

† State Key Laboratory of Networking and Switching Technology, Beijing, China

Email:<EMAIL>

*Abstract*—Dataset distillation is utilized to condense large datasets into smaller synthetic counterparts, effectively reducing their size while preserving their crucial characteristics. In Federated Learning (FL) scenarios, where individual devices or servers often lack substantial computational power or storage capacity, the use of dataset distillation becomes particularly advantageous for processing large volumes of data efficiently. Current research in dataset distillation for FL has primarily focused on enhancing accuracy and reducing communication complexity, but it has largely neglected the potential risk of backdoor attacks. To solve this issue, in this paper, we propose three adaptive dataset condensation based backdoor attacks against dataset distillation for FL. Adaptive attacks in dataset distillation for FL dynamically modify triggers during the training process. These triggers, embedded in the synthetic data, are designed to bypass traditional security detection. Moreover, these attacks employ self-adaptive perturbations to effectively respond to variations in the model's parameters. Experimental results show that the proposed adaptive attacks achieve at least 5.87% higher success rates, while maintaining almost the same clean test accuracy, compared to three benchmark methods.

*Index Terms*—Backdoor Attacks, Dataset Distillation, Federated Learning

# I. INTRODUCTION

Federated Learning (FL) is a distributed framework where clients share model parameters, rather than raw local data, with a central server. This enables the aggregation of a global model with enhanced performance. Subsequently, clients use the new global model distributed by the central server to initiate a new round of local training using their local data [1]. However, within the Federated Learning (FL) framework, clients often face constraints in computational power and storage space [2], [3]. Additionally, FL also encounters challenges related to data leakage and substantial communication overhead [4], [5]. These limitations make it impractical for clients to process large local datasets. Consequently, this restricts the global model from capturing more detailed data features and behavioral patterns [6], [7]. Dataset distillation emerges as a promising technique [8], [9]. It focuses on distilling a small, representative portion of data to effectively lessen storage and computational burdens. Additionally, dataset distillation helps in eliminating private information within the data, while preserving the performance of the model.

However, current research on dataset distillation primarily concentrates on enhancing data distillation for higher accuracy [10], [11] or simplifying datasets to reduce communication complexity in FL [12], but lacks research on its potential security issues and possible backdoor attacks. While the data distillation process can remove most backdoor markers, and the reduced communication volume and smaller data size facilitate the monitoring and defense against attacks during the FL process [13], there remains a significant gap in research concerning the potential security vulnerabilities and risks of backdoor attacks in dataset distillation. For instance, [14] proposed a distillation method based on generative adversarial networks and variational autoencoders, which improves accuracy from 36.8% to 39.1% on the CIFAR10 dataset. However, the inherent flaws in the Deep Neural Networks(DNN) model itself leave room for potential backdoor attacks [15], [16].

Therefore, in this paper, we focus on backdoor attacks for dataset distillation in FL and propose three adaptive image steganography attacks:the Minor Attack, Noise Attack, and Frequency Attack. We incorporate these attacks into the data distillation process, enabling them to self-adjust through a gradient matching mechanism. This adjustment responds to the images generated by generative adversarial networks and variational autoencoders. Our approach diverges from traditional methods, which typically involve injecting backdoors directly into original images to initiate attacks. Instead, our method injects backdoors into triggers that are embedded in the synthetic data. This strategy effectively bypasses traditional security detection mechanisms. As a result, our method is less likely to lose its attack effectiveness during the aggregation process in FL. Our experimental results demonstrate that our proposed attacks significantly outperform benchmark methods, specially the Naive, Doorping, Invisible attacks, defined in paper [17]. The success rate of the Minor attack is 55.45% higher than the Naive attack. The Noise attack surpasses the Doorping attack by 5.87%, and the Frequency attack exceeds the Invisible attack by 7.97%.

The main contributions of this paper are summarized as follows.

- We perform a backdoor attack against dataset distillation in FL by persistently optimizing the triggers during the distillation process. This approach ensures that the triggers are embedded in the synthetic data to bypass traditional security detection with the legitimate data characteristics.
- We propose three adaptive backdoor attacks:Minor, Noise, and Frequency. The Minor attack incorporates ran-

Authorized licensed use limited to: Imperial College London. Downloaded on July 02,2025 at 10:42:23 UTC from IEEE Xplore. Restrictions apply.

dom perturbations into triggers to adapt to different images. The Noise attack generates a Gaussian distributionbased noise pattern to create noise masks. Lastly, the Frequency attack employs the Fast Fourier Transform to convert data from the spatial domain to the frequency domain.

• We compare our methods with previous methods, specifically the Naive, Doorping, and Invisible attacks, demonstrate that our proposed Minor attack and Frequency attack can achieve higher attack success rates with similar clean test accuracy in FL. The Noise attack achieves higher clean test accuracy and attack success rate compared with the benchmark methods.

# II. BACKGROUND AND RELATED WORK

## A. Backdoor Attacks on Federated Learning

McMahan et al. [1] proposed federated learning which enables models to learn collaboratively from decentralized data through the exchange of models between clients and a central server. The objective of backdoor attacks on federated learning is to train highly poisoned local models and submit malicious model updates to the central server, thereby compromising the global model [18], [19]. Backdoor attacks aim to ensure the accuracy of the compromised data attack while minimizing the impact on the accuracy of clean data as much as possible.

## B. Dataset Distillation in Federated Learning

Dataset distillation in federated learning has seen some attempts; however, the related work primarily focuses on how to reduce communication overhead through dataset distillation [20]. These approaches are largely predicated on improving the method of cluster analysis [12]. When the number of images is fewer than 20 per class post-distillation, the model training is inadequate. Our tests indicate that with fewer than 20 images per class, the effectiveness of training is comparable to that of a random selection, and at times, it is even inferior. Therefore, we reference the superior effects of DC [14] for dataset distillation.

## C. Backdoor Attacks on Dataset Distillation

Existing backdoor attacks inject triggers into the original clean data. However, these backdoor programs face difficulties in dataset consolidation (DC) because the data generated is used to match the original dataset during the distillation process. This matching tends to remove the backdoor during the gradient matching training phase. Thus, it is essential to continuously optimize the triggers throughout the distillation process [17], and to launch the attack using the distilled data containing the backdoor to achieve more effective results [9]. While these backdoor attacks can be adapted for federated learning, their effects cannot be directly transferred from centralized to federated learning due to its distributed and privacy-preserving characteristics [21]. Therefore, we propose three backdoor attacks specifically designed for the distillation of federated learning datasets, taking into account the unique aspects of distributed attacks in federated learning [22].

# III. IMPLEMENTATION OF METHOD

## A. Gradient Matching-Based Dataset Distillation:

Gradient Matching is a key step in dataset condensation. The goal of this process is to find a small, synthetic dataset such that, when the model is trained on this dataset, the gradients of the model parameters match as closely as possible to those obtained when training on the original, full dataset. We assume that we have access to the original dataset  $D_0$ and the synthetic dataset  $D_s$ . Let  $\mathcal L$  denote the loss function and  $\theta$  the model parameters. The notation  $\nabla_{\theta}$  represents the gradient with respect to the model parameters  $\theta$ . The objective function  $J$ , representing the gradient matching loss, is formulated to minimize the following discrepancy, also known as the matching loss:

$$
J(D_{\rm s}) = \sum_{i=1}^{n} \|\nabla_{\theta} \mathcal{L}(\theta_i, D_{\rm s}) - \nabla_{\theta} \mathcal{L}(\theta_i, D_{\rm o})\|^2, \qquad (1)
$$

where *n* is the number of elements in the parameter vector  $\theta$ .

To calculate  $D_s$ , iterative optimization is typically required. Each iteration involves first conducting forward propagation to calculate the loss function's value for both the synthetic and original datasets. Then, the gradient of the loss function with respect to the model parameter  $\theta$  is computed for each dataset. The difference between these two gradients is determined using Equation 1. Finally, backpropagation is performed, and the features of the synthetic sample are updated by applying the gradient descent algorithm. This minimizes the gradient matching loss, updating the synthetic dataset according to the following expression of the gradient descent algorithm:

$$
D_s^{(t+1)} = D_s^{(t)} - \eta \nabla_{D_s} J(D_s^{(t)}).
$$
 (2)

The Equation 2 represents the update of the synthetic dataset  $D_s$  at iteration step t. Here,  $\eta$  denotes the learning rate, and  $\nabla_{D_{\rm s}} J(D_{\rm s}^{(t)})$  is the gradient of the loss function J with respect to the synthetic dataset  $D_s$  at iteration step t. This update rule is applied at each iteration to adjust the synthetic dataset in order to reduce the gradient matching loss  $J$ . The termination condition occurs when a certain number of iterations is reached, or when the value of the loss function  $J(D_s^{(t)})$  falls below a predetermined threshold, at which point the iteration process stops.

At each step of the iteration, the parameters of the synthetic dataset  $D_s$  are updated with the aim of making the gradients  $\nabla_{\theta} \mathcal{L}(\theta, D_s)$  produced during model training increasingly approximate the gradients  $\nabla_{\theta} \mathcal{L}(\theta, D_{o})$  obtained from the original dataset  $D_0$ . This iterative process is highly nonlinear.

## B. Adaptive backdoor attack in Federated Learning

Backdoor attacks against the original dataset tend to fail due to information compression during distillation, while some backdoor information may be treated as noise in the gradient descent step due to reuse of the dataset distillation model by the attacker [17]. Therefore, we design three backdoor attacks, Minor, Noise, and Frequency attacks, based on the dataset distillation process and taking into account the characteristics

Image /page/2/Figure/1 description: This figure illustrates two distinct training pipelines for a machine learning model. Both pipelines begin with a large training set of diverse images. The left pipeline, labeled 'Traditional trigger', shows a forward pass from the training set to a model, followed by a 'Train' step and then a 'Test' step. This is followed by a 'Matching loss' calculation, which then updates a 'Synthetic set'. The 'Synthetic set' then undergoes its own 'Train' and 'Test' steps, with the 'Test' output feeding back into the 'Matching loss'. The right pipeline, labeled 'Our trigger', follows a similar structure but introduces a 'Our trigger' element that directly influences the 'Synthetic set' update. Both pipelines conclude with a 'Cross-Entropy Loss' calculation after the 'Test' phase.

Fig. 1. Backdoor trigger insertion method. Traditional attacks insert backdoor triggers before training, leaving triggers in the original dataset and affecting the synthetic data through matching loss. Our attack, on the other hand, inserts triggers after matching loss and continuously makes adaptive adjustments with each training, thus leaving triggers in the synthetic data.

of federated learning that influences the central server through device nodes. Figure 1 illustrates the difference between our attack flow and the traditional attack flow. These three attacks require adaptive tuning of the triggers during the distillation process to accommodate the non-static nature of the distillation model parameters, ensuring that the trigger optimization for different devices during distillation adapts to their own training results. Trigger optimization at each epoch maintains its effectiveness, ensuring the trigger's preservation in the distillation dataset. The process involves selecting neurons that most influence the model's behavior, amplifying their output, and using this information for trigger optimization. The optimized triggers are then used to poison a subset of the dataset, which is subsequently used to update the distilled dataset, thus avoiding trigger failure.

Minor attack: In the "Minor" attack strategy, we initialize a trigger  $\tau$  and introduce subtle adjustments through small random perturbations  $\delta$ . Make the trigger not always a white checkerboard, thus better embedding itself in the distilled composite image and increasing its invisibility. The update of the trigger is defined as:

$$
\tau' = \tau + \delta. \tag{3}
$$

The loss function  $\mathcal L$  guides the optimization of the trigger to maximize the activation of selected neurons at layer  $l$  of the model parameters  $\theta$ , expressed as:

$$
\mathcal{L}(\tau,\theta) = MSE(f_{\theta_{1:l}}(\tau), \alpha \cdot f_{\theta_{1:l}}(\tau_i)).
$$
 (4)

Here,  $f_{\theta_{1:l}}$  denotes the forward propagation function from input to the target layer *l*,  $\tau_i$  is the initial state of the trigger, and  $\alpha$  is a magnification factor enhancing the output of top  $k$  neurons, facilitating the optimization of the trigger  $\tau$ . This process allows the trigger  $\tau$  to learn from neurons that influence model misbehavior, leveraging an optimized trigger  $\tau$  to poison a subset of the dataset, which is then used to update the distilled dataset, enhancing the stealth and efficacy of the backdoor attack.

Noise attack:In the "Noise" attack strategy, we created a noise pattern based on a Gaussian distribution (Equation 5) and restricted the range of values within the distribution, and then created noise masks to ensure updates were applied only within the noise pattern. Subsequently, Gaussian filtering can be performed as required to reduce the effect of noise on the image.

$$
f(x|\mu, \sigma^2) = \frac{1}{\sqrt{2\pi\sigma^2}} e^{-\frac{(x-\mu)^2}{2\sigma^2}},
$$
 (5)

During the Noise attack, the Adam optimization algorithm was used and the parameters were updated based on Equation:

$$
\theta_{t+1} = \theta_t - \frac{\eta}{\sqrt{\hat{v}_t} + \epsilon} \hat{m}_t,\tag{6}
$$

where  $\theta$  represents the parameters being optimized,  $\eta$  is the learning rate,  $\hat{m}_t$  and  $\hat{v}_t$  are bias-corrected estimates of first and second moments, and  $\epsilon$  is a small constant to prevent division by zero.The losses were then calculated, and the model was updated using a mean squared error as defined in Equation:

$$
MSE(Y, \hat{Y}) = \frac{1}{n} \sum_{i=1}^{n} (Y_i - \hat{Y}_i)^2,
$$
 (7)

where  $Y$  and  $\hat{Y}$  are vectors of true values and predicted values, respectively. The Noise attack appears as a natural variation in the image during the distillation process, allowing the attack to remain more covert; however, it is challenging to control the Noise attack with precision. The pseudo-code is shown in Algorithm 1.

Frequency attack:In the "Frequency" attack strategy, we apply the Fast Fourier Transform by means of the following Equation:

$$
F(u,v) = \int \int f(x,y)e^{-i2\pi(ux+vy)} dx dy,
$$
 (8)

thus transforming the image from the spatial domain to the Frequency domain. We move the low-Frequency component to the center of the spectrum. We then calculate the magnitude and phase of the image by using Equation 9 and Equation 10, respectively.

$$
|F(u, v)| = \sqrt{\mathcal{R}(F(u, v))^2 + \mathcal{S}(F(u, v))^2},
$$
 (9)

$$
\angle F(u, v) = \arctan 2(\mathfrak{I}(F(u, v)), \mathfrak{R}(F(u, v))), \qquad (10)
$$

where  $\Re$  and  $\Im$  represent the real and imaginary parts of a complex number. Considering that the visual content of an image is more closely related to its magnitude than its phase, small modifications to the phase are unlikely to significantly affect the image's appearance. Therefore, we chose to add a perturbation to the phase to incorporate a backdoor trigger. Finally, we transformed the image back to the spatial domain using the inverse Fourier transform and performed gradient descent and optimization as prescribed by Equation 6. Frequency attacks can be designed to be more specific to the specific Frequency domain response of the model compared to Noise attack, thus bypassing image content-based detection.

Algorithm 1 Noise Attack

**Require:** Original training dataset  $D_0$ , model learning rate  $\eta$ , noise pattern initialization parameters, noise trigger update parameters (learning rate  $\eta_t$ , scaling factor  $\alpha$ , threshold, number of neurons to maximize topk), noise mask  $m$ , device

**Output:** Distilled dataset with embedded noise trigger  $\overline{D}_0$ 

- 1: Randomly initialize the distilled dataset  $D_0$ , and noise trigger  $t$
- 2: while updating distilled images do
- 3: Initialize the model  $\theta_0$

4: while updating model do

5: 
$$
\theta_{t+1} = \theta_t - \frac{\eta}{\sqrt{\hat{v}_t} + \epsilon} \hat{m}_t
$$

6: end while

7: while updating noise trigger do

```
8: Select neurons to maximize in trigger
   layer: key_to_maximize = select_newcon(net, layer, topk)9: Initialize Adam optimizer for :optimizer
10: for iteration in range(10000) do
11: optimizer.zero grad()
12: output = get \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \middle( \
```

| 13: | $noise =$ torch.randn_like( $t$ ) $	imes$ 0.1 |
|-----|-----------------------------------------------|
| 14: | $t$ .data.add( $noise$ )                      |

15:  $output = output[key to maximize]$ 

| 16: | <b>if</b> iteration == 0 then |
|-----|-------------------------------|
|-----|-------------------------------|

```
17: init \ output = output.dat18: end if
```

```
19: Loss calculation: L_t = \frac{1}{n} \sum_{i=1}^{n} (\alpha \cdot init\_output_i -output_i)^2
```

| 20: | <b>if</b> $L_t$ < threshold <b>then</b> |
|-----|-----------------------------------------|
| 21: | Break                                   |
| 22: | end if                                  |
| 23: | $L_t$ .backward()                       |
| 24: | $t.grad.data.mul_(noise_mask)$          |

25: optimizer.step()

- 26: end for
- 27: end while
- 28: Inject updated trigger t into  $\epsilon |D_0|$  samples in  $D_0$  to form the backdoored dataset  $\hat{D}_0$ .
- 29: Compute losses for  $\hat{D}_0$  and  $\tilde{D}_0$ :  $L = L(\hat{D}_0, \theta), \tilde{L} = L(\tilde{D}_0, \theta)$  $L(D_0, \theta)$

30:  $\widetilde{D}_0 \leftarrow \text{UPDATE}(\widetilde{D}_0, L, \widetilde{L})$ 31: end while

The Frequency attack achieves perfect concealment of the attack trigger by making small adjustments to the phase of the image. The key to this strategy is that it does not significantly change the appearance of the image, thus avoiding detection of the trigger.

# IV. RESULTS AND DISCUSSION

## A. Experimental Settings

We adopted the method described in the paper by Zhao et al. [14] and adapted it to federated learning for dataset distillation. We configured the distillation model to train for 600 epochs and the evaluation model for 300 epochs, repeating the training of the evaluation model 15 times to calculate the average performance. We distilled the images into 20 per class across 10 devices, employing FedAvg for model aggregation. In our attack scenarios, we conceptualize the attacker as a malicious dataset distillation service provider and compare our method with the three backdoor methods as proposed in references  $[17]$ , and we leverage their code<sup>1</sup> in our experiments. We set the poisoning ratio to the commonly used 0.01 [17], [23] and compared the results.

In the Minor attack, we placed a small 1x1 grid, initially white, that adapts with perturbations and the target image during training to conceal itself. For the Noise attack, we continuously generated random noisy images, altering the noise pattern as the target image was trained. In the Frequency attack, we perturbed the phase of the target image directly; this approach is entirely imperceptible as it does not involve generating or injecting any visible artifacts into the target image.

The effectiveness of backdoor attacks is often measured by attack success rate and clean test accuracy [23]. It refers to the rate at which the model erroneously classifies the data with the backdoor trigger to the category desired by the attacker in the test data containing the backdoor marker. A high attack success rate implies that the backdoor attack is very effective, and the model is highly likely to produce incorrect outputs when it encounters inputs containing the specific backdoor trigger. Clean test accuracy measures the performance of the model on normal, unmanipulated data. Even if a model is subjected to a backdoor attack, its performance on normal data without triggers is still very important. Ideally, even if the model is subject to a backdoor attack, it will still behave on clean data as if it had not been attacked, so as not to interfere with normal use.

## B. Results and Interpretation

We selected CIFAR10 as the dataset, and the initially generated trigger image is shown in Figure 2. 'Naive,' 'Doorping,' and 'Invisible,' as depicted in the figure, are all results from running the code in [17]. 'Minor,' 'Noise,' and 'Frequency' are the three backdoor attacks we propose. In Minor attack we only use a soft 1×1 white grid instead of a 2×2 grid, and its color gradually changes during the training distillation process. This gradual adjustment strategy effectively increases the stealthiness of the attack because it mimics the natural change of the image, thus reducing the risk of the trigger being recognized by a human observer or an automatic detection algorithm. In the Noise attack, although it appears that all grids are attacked, the attack is generated based on the Initial

<sup>1</sup>https://github.com/liuyugeng/baadd

synthesized image, which is initially in a noisy state. Noise attacks are not only indistinguishable in their intuitive state, but also statistically consistent with the noise patterns expected by the model, which makes detection more difficult. The Frequency attack, due to phase changes, does not have an attack image. While the Invisible attack is undetectable, its principle involves mapping the trigger image by randomly selecting an image from the attacker's target category, allowing the initial trigger image to still be extracted.

Image /page/4/Figure/2 description: The image displays six small squares arranged in two rows of three, followed by a larger grid of smaller squares. The top row of small squares shows a black square with a small white square in the bottom right corner labeled "Naive", a black square with a small yellow square in the bottom right corner labeled "Doorping", and a black square with a pixelated image of a black shape on a white and blue background labeled "Invisible". The bottom row of small squares shows a black square with a small white square in the bottom right corner labeled "Minor", a black square filled with colorful random pixels labeled "Noise", and a black square labeled "Frequency". To the right of these small squares is a large grid of many small squares, each filled with a grainy, multicolored pattern, labeled "Initial synthesized image".

Fig. 2. Initializes image

The results after distillation for the above six attack methods are shown in Figure 4 and Table I. The variance represents the stability of the attack, with a higher variance proving to be less stable over multiple evaluation experiments. The stronger the effect of the attack the more likely it is to cause erratic results. The Minor attack uses smaller triggers and has an attack success rate of 67.62%, which is much higher than naive's 12.17%. This is because Minor attacks can constantly optimize their state during distillation. Noise attack has 3.17% higher clean test accuracy and 5.87% higher attack success rate than Doorping despite lower stability. This is because the synthetic dataset is constantly distilled from the noise and can be perfectly hidden in the dataset by the Noise attack. Noise attack is the most matching DC distillation method. Frequency attack and Invisible attack are both Invisible attacks. From the results, our proposed Frequency attack achieves 7.97% higher attack success rate than the Invisible attack and the clean test accuracy is similar (36.64% compared to 37.71%).

Image /page/4/Figure/5 description: This image contains three bar charts. The first chart, titled "Clean test accuracy", shows the clean mean values for different attack types: naive, doorping, invisible, minor, noise, and frequency. The values are approximately 0.35, 0.34, 0.36, 0.35, 0.37, and 0.37 respectively. The second chart, titled "Attack success rate", shows the trigger mean values for the same attack types. The values are approximately 0.15, 0.85, 0.30, 0.75, 0.85, and 0.05 respectively. The third chart, titled "Variance of Attack success rate", shows the trigger variance values for the same attack types. The values are approximately 0.12, 0.06, 0.09, 0.11, and 0.12 respectively. The x-axis labels for all charts are rotated for readability.

Fig. 3. The results of different backdoor attack methods on CIFAR10

The final image is shown in Figure 4. We intercept the first few of each class for presentation. The image in the red box has an obvious problem with the distillation results due to a backdoor attack. The image in the yellow box has a localized error due to the injection of a trigger in the bottom right corner. The Minor attack is similar to the Doorping attack, but because

TABLE I THE VALUES OF DIFFERENT BACKDOOR ATTACK METHODS ON CIFAR10

| <b>Name</b> | <b>Clean Mean Values</b> | <b>Trigger Mean</b> | <b>Trigger Variance</b> |
|-------------|--------------------------|---------------------|-------------------------|
| Naive       | 0.3569                   | 0.1217              | 0.0005881               |
| Doorping    | 0.3416                   | 0.8080              | 0.1106                  |
| Invisible   | 0.3771                   | 0.0305              | 0.0014534               |
| Minor       | 0.3505                   | 0.6762              | 0.09888                 |
| Noise       | 0.3733                   | 0.8667              | 0.1156                  |
| Frequency   | 0.3664                   | 0.1102              | 0.0003183               |

the grid is smaller, the area in the lower right corner where the error occurs is smaller and less likely to cause problems with the entire image(as shown in Figure 5). The algorithmic results of the DC are not deterministic, and there often does not exist a defense against a backdoor attack by distilling the results of training multiple sets of the same dataset with the same parameters. This is inconsistent with our proposed attack scenarios. Frequency attacks can hide themselves perfectly as well as Invisible attacks without causing obvious errors.

| Naive                       | Doorping                    | Invisible                   |
|-----------------------------|-----------------------------|-----------------------------|
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images |

Fig. 4. Image of CIFAR10 after dataset distillation

## C. Analysis

Our proposed Minor, Noise, and Frequency attack methods perform better in federated learning environments, mainly because they are specifically optimized for the data distillation

Image /page/5/Picture/1 description: The image displays three pairs of small, pixelated images. Each pair is labeled with a word below it: "Naive", "Doorping", and "Minor". The images themselves are abstract and colorful, with some appearing more distorted or noisy than others. The "Doorping" pair contains one image with particularly vibrant and chaotic colors, suggesting a significant difference or anomaly compared to the other images.

Fig. 5. Minor attacks cause smaller error than Doorping attacks

process of federated learning and the non-static nature of the model parameters. Considering the characteristics of federated learning that affects the central server through device nodes, these strategies adapt triggers during the distillation process, allowing them to adapt to changes in model parameters and thus maintain the effectiveness of the attack. For example, the Minor attack enhances the stealthiness of the attack by adding smaller modifications; the Noise attack utilizes the noise pattern as an attack medium, which looks like a natural change in the image during the distillation process and thus is more stealthy; and the Frequency attack is able to hide perfectly by making a small modification to the phase of the image, which does not affect the appearance of the image too much, thus allowing for a perfect concealment of the Attacks. These methods maintain a high attack success rate while keeping a high clean test accuracy, i.e., performance on normal data that has not been tampered with is not affected much. Overall, our proposed methods not only improve the stealth and adaptability of the attacks, but also are able to maintain a high attack success rate while not overly affecting the performance of the model on normal data.

# V. CONCLUSION

In this paper, we propose three adaptive backdoor attack methods for DC dataset distillation techniques and distributed computing in federated learning:the Minor attack, the Noise attack, and the Frequency attack. We gradually optimize the triggers with the training of synthetic data during the distillation process instead of injecting them in the original data, which improves the steganography and adaptability of the attacks. We conducted experiments using the CIFAR10 dataset to compare the effectiveness of the proposed attack method with existing methods. The experimental results show that the newly proposed attack methods perform better in terms of attack success rate and clean test accuracy, demonstrating their effectiveness and adaptability in a federated learning environment. These methods are able to maintain a high attack success rate without unduly affecting the model's performance on normal data. In future research, we will test these three methods on more datasets and more network models and analyze their attack effectiveness in existing defense systems.

# ACKNOWLEDGEMENT

This work was Supported by Beijing Municipal Natural Science Foundation(No. 4232029)

# REFERENCES

- [1] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication-efficient learning of deep networks from decentralized data," in *Artificial intelligence and statistics*. PMLR, 2017, pp. 1273– 1282.
- [2] A. Brecko, E. Kajati, J. Koziorek, and I. Zolotova, "Federated learning for edge computing: A survey," *Applied Sciences*, vol. 12, no. 18, p. 9124, 2022.
- [3] Y. Mu and C. Shen, "Communication and storage efficient federated split learning," *arXiv preprint arXiv:2302.05599*, 2023.
- [4] Y. Song, C. Xu, Y. Zhang, and S. Li, "Hardening password-based credential databases," *IEEE Transactions on Information Forensics and Security*, vol. 19, pp. 469–484, 2024.
- [5] G. Luo, H. Zhou, N. Cheng, Q. Yuan, J. Li, F. Yang, and X. Shen, "Software-defined cooperative data sharing in edge computing assisted 5g-vanet," *IEEE Transactions on Mobile Computing*, vol. 20, no. 3, pp. 1212–1229, 2021.
- [6] Z. Guo, R. Jin, C. Liu, Y. Huang, D. Shi, L. Yu, Y. Liu, J. Li, B. Xiong, D. Xiong *et al.*, "Evaluating large language models: A comprehensive survey," *arXiv preprint arXiv:2310.19736*, 2023.
- [7] Y. Lin, Z. Gao, H. Du, J. Kang, D. Niyato, Q. Wang, J. Ruan, and S. Wan, "Drl-based adaptive sharding for blockchain-based federated learning," *IEEE Transactions on Communications*, vol. 71, no. 10, pp. 5992–6004, 2023.
- [8] T. Dong, B. Zhao, and L. Lyu, "Privacy for free: How does dataset condensation help privacy?" in *International Conference on Machine Learning*. PMLR, 2022, pp. 5378–5396.
- [9] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- [10] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759.
- [11] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," *arXiv preprint arXiv:2011.00050*, 2020.
- [12] R. Song, D. Liu, D. Z. Chen, A. Festag, C. Trinitis, M. Schulz, and A. Knoll, "Federated learning via decentralized dataset distillation in resource-constrained edge environments," in *2023 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2023, pp. 1–10.
- [13] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Dataset distillation for medical dataset sharing," *arXiv preprint arXiv:2209.14603*, 2022.
- [14] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint arXiv:2006.05929*, 2020.
- [15] T. Gu, B. Dolan-Gavitt, and S. Garg, "Badnets: Identifying vulnerabilities in the machine learning model supply chain," *arXiv preprint arXiv:1708.06733*, 2017.
- [16] A. Salem, R. Wen, M. Backes, S. Ma, and Y. Zhang, "Dynamic backdoor" attacks against machine learning models," in *2022 IEEE 7th European Symposium on Security and Privacy (EuroS&P)*. IEEE, 2022, pp. 703– 718.
- [17] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Backdoor Attacks Against Dataset Distillation," in *NDSS*, 2023.
- [18] A. N. Bhagoji, S. Chakraborty, P. Mittal, and S. Calo, "Analyzing federated learning through an adversarial lens," in *International Conference on Machine Learning*. PMLR, 2019, pp. 634–643.
- [19] Y. Lin, H. Du, D. Niyato, J. Nie, J. Zhang, Y. Cheng, and Z. Yang, "Blockchain-aided secure semantic communication for ai-generated content in metaverse," *IEEE Open Journal of the Computer Society*, vol. 4, pp. 72–83, 2023.
- [20] Y. Zhou, G. Pu, X. Ma, X. Li, and D. Wu, "Distilled one-shot federated learning," *arXiv preprint arXiv:2009.07999*, 2020.
- [21] T. D. Nguyen, T. Nguyen, P. Le Nguyen, H. H. Pham, K. D. Doan, and K.-S. Wong, "Backdoor attacks and defenses in federated learning: Survey, challenges and future research directions," *Engineering Applications of Artificial Intelligence*, vol. 127, p. 107166, 2024.
- [22] C. Xie, K. Huang, P.-Y. Chen, and B. Li, "Dba: Distributed backdoor attacks against federated learning," in *International conference on learning representations*, 2019.
- [23] N. Carlini, "Poisoning the unlabeled dataset of {Semi-Supervised} learning," in *30th USENIX Security Symposium (USENIX Security 21)*, 2021, pp. 1577–1592.