{"table_of_contents": [{"title": "An Efficient Dataset Condensation Plugin and\nIts Application to Continual Learning", "heading_level": null, "page_id": 0, "polygon": [[135.0, 99.75], [476.9296875, 99.75], [476.9296875, 137.091796875], [135.0, 137.091796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 265.482421875], [329.25, 265.482421875], [329.25, 276.310546875], [282.75, 276.310546875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 517.5], [192.0, 518.9765625], [192.0, 530.578125], [107.25, 530.578125]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 2, "polygon": [[106.5, 120.0], [201.75, 120.0], [201.75, 131.87109375], [106.5, 131.87109375]]}, {"title": "3 Low-Rank Data Condensation Plugin", "heading_level": null, "page_id": 3, "polygon": [[107.20458984375, 112.5], [318.75, 112.5], [318.75, 123.6533203125], [107.20458984375, 123.6533203125]]}, {"title": "3.1 Problem Definition", "heading_level": null, "page_id": 3, "polygon": [[106.5, 180.75], [212.25, 180.75], [212.25, 190.1689453125], [106.5, 190.1689453125]]}, {"title": "3.2 Our Low-Rank Data Condensation Plugin", "heading_level": null, "page_id": 3, "polygon": [[106.5, 647.25], [310.5, 647.25], [310.5, 656.6484375], [106.5, 656.6484375]]}, {"title": "[66]", "heading_level": null, "page_id": 4, "polygon": [[107.25, 76.5], [441.0, 76.5], [441.0, 86.25], [107.25, 86.25]]}, {"title": "3.3 Incorporating Low-rank DC Plugin to SOTA Methods", "heading_level": null, "page_id": 4, "polygon": [[106.5, 588.0], [361.5, 588.0], [361.5, 597.8671875], [106.5, 597.8671875]]}, {"title": "3.4 Application to Continual Learning", "heading_level": null, "page_id": 5, "polygon": [[106.5, 604.5], [278.25, 604.5], [278.25, 614.8828125], [106.5, 614.8828125]]}, {"title": "4 Experiment", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 377.630859375], [187.5, 377.630859375], [187.5, 389.232421875], [107.1298828125, 389.232421875]]}, {"title": "4.1 Data Condensation for Deep Learning", "heading_level": null, "page_id": 6, "polygon": [[106.5, 440.25], [294.4951171875, 440.25], [294.4951171875, 450.52734375], [106.5, 450.52734375]]}, {"title": "4.1.1 Compared to SOTA DC Baselines", "heading_level": null, "page_id": 7, "polygon": [[106.5, 304.5], [282.0, 304.5], [282.0, 314.7890625], [106.5, 314.7890625]]}, {"title": "4.1.2 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[107.1298828125, 648.75], [204.0, 648.75], [204.0, 658.96875], [107.1298828125, 658.96875]]}, {"title": "4.2 Data Condensation for Continual Learning", "heading_level": null, "page_id": 8, "polygon": [[106.5, 669.75], [315.263671875, 669.75], [315.263671875, 680.23828125], [106.5, 680.23828125]]}, {"title": "5 Conclusion and Future Works", "heading_level": null, "page_id": 9, "polygon": [[106.90576171875, 300.48046875], [280.5, 300.48046875], [280.5, 311.6953125], [106.90576171875, 311.6953125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[107.25, 474.75], [207.75, 474.75], [207.75, 485.71875], [107.25, 485.71875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 594.75], [163.5, 594.75], [163.5, 605.21484375], [107.25, 605.21484375]]}, {"title": "A Implementation Details", "heading_level": null, "page_id": 14, "polygon": [[106.5, 72.36474609375], [249.9697265625, 72.36474609375], [249.9697265625, 84.06298828125], [106.5, 84.06298828125]]}, {"title": "B Additional Experimental Results", "heading_level": null, "page_id": 14, "polygon": [[106.5, 343.40625], [296.5869140625, 343.40625], [296.5869140625, 355.39453125], [106.5, 355.39453125]]}, {"title": "B.1 Experimental Analysis", "heading_level": null, "page_id": 14, "polygon": [[106.5, 370.283203125], [231.0, 370.283203125], [231.0, 380.724609375], [106.5, 380.724609375]]}, {"title": "B.2 Image Visualization", "heading_level": null, "page_id": 15, "polygon": [[106.5, 488.25], [217.5, 488.25], [217.5, 498.48046875], [106.5, 498.48046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 44], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4495, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 58], ["ListItem", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 55], ["TextInlineMath", 3], ["Text", 2], ["ListItem", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 845], ["Line", 84], ["TextInlineMath", 4], ["Reference", 4], ["Text", 3], ["SectionHeader", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10061, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1133], ["Line", 76], ["TextInlineMath", 6], ["Reference", 3], ["SectionHeader", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8179, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 957], ["Line", 79], ["TextInlineMath", 6], ["Reference", 4], ["Equation", 2], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 716], ["TableCell", 449], ["Line", 58], ["Table", 3], ["TextInlineMath", 3], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 11297, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["TableCell", 84], ["Line", 51], ["Text", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 66], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 85], ["Reference", 4], ["Text", 3], ["SectionHeader", 3], ["ListItem", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 913, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 49], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["TableCell", 76], ["Line", 55], ["SectionHeader", 3], ["TextInlineMath", 3], ["Text", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6303, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 52], ["TextInlineMath", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1441, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Line", 293], ["Span", 35], ["Caption", 2], ["Reference", 2], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1557, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 39], ["Figure", 3], ["Text", 3], ["Caption", 3], ["Reference", 3], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 3316, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/An Efficient Dataset Condensation Plugin and Its Application to Continual Learning"}