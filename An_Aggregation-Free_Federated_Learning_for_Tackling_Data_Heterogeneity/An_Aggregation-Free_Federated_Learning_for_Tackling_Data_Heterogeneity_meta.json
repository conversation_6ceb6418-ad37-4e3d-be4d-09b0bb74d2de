{"table_of_contents": [{"title": "An Aggregation-Free Federated Learning for Tackling Data Heterogeneity", "heading_level": null, "page_id": 0, "polygon": [[68.25, 106.5], [525.9375, 106.5], [525.9375, 119.109375], [68.25, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 213.75], [191.25, 213.75], [191.25, 225.45703125], [144.75, 225.45703125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 515.25], [127.97314453125, 515.25], [127.97314453125, 526.32421875], [48.75, 526.32421875]]}, {"title": "2. Background and Related Works", "heading_level": null, "page_id": 1, "polygon": [[307.5, 243.0], [486.0, 243.0], [486.0, 254.07421875], [307.5, 254.07421875]]}, {"title": "3. Notations and Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[307.5, 258.75], [464.25, 258.75], [464.25, 269.15625], [307.5, 269.15625]]}, {"title": "4. The Proposed Method", "heading_level": null, "page_id": 3, "polygon": [[48.75, 253.5], [176.90625, 253.5], [176.90625, 264.90234375], [48.75, 264.90234375]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 563.25], [128.25, 563.25], [128.25, 573.890625], [48.75, 573.890625]]}, {"title": "5.1. Results for Label-skew Data Heterogeneity", "heading_level": null, "page_id": 4, "polygon": [[307.5, 246.0], [528.75, 246.0], [528.75, 256.39453125], [307.5, 256.39453125]]}, {"title": "5.2. <PERSON><PERSON><PERSON> for Feature-skew Data Heterogeneity", "heading_level": null, "page_id": 6, "polygon": [[48.0, 671.25], [276.0, 671.25], [276.0, 682.171875], [48.0, 682.171875]]}, {"title": "5.3. Performance Analysis of FedAF", "heading_level": null, "page_id": 6, "polygon": [[306.896484375, 457.5], [477.75, 457.5], [477.75, 467.9296875], [306.896484375, 467.9296875]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 427.5], [378.75, 427.5], [378.75, 438.92578125], [307.5, 438.92578125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.96630859375], [48.75, 83.96630859375]]}, {"title": "A. Implementation Details", "heading_level": null, "page_id": 10, "polygon": [[48.75, 72.509765625], [186.0, 72.509765625], [186.0, 83.91796875], [48.75, 83.91796875]]}, {"title": "<PERSON><PERSON> More Experiment Results with ResNet18", "heading_level": null, "page_id": 10, "polygon": [[307.5, 297.966796875], [532.5, 297.966796875], [532.5, 309.568359375], [307.5, 309.568359375]]}, {"title": "C. Communication Cost Analysis", "heading_level": null, "page_id": 10, "polygon": [[307.494140625, 496.93359375], [479.25, 496.93359375], [479.25, 508.53515625], [307.494140625, 508.53515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 101], ["Text", 6], ["SectionHeader", 3], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4441, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 104], ["Text", 7], ["ListItem", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 122], ["Text", 4], ["TextInlineMath", 4], ["Equation", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1021, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 83], ["Equation", 10], ["Text", 9], ["TextInlineMath", 5], ["Reference", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 94], ["Text", 10], ["Equation", 5], ["Reference", 4], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 203], ["TableCell", 183], ["Text", 3], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 419], ["TableCell", 181], ["Line", 119], ["Reference", 5], ["Text", 4], ["Caption", 3], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 129], ["TableCell", 61], ["Text", 5], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 112], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 57], ["ListItem", 14], ["Reference", 14], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 100], ["TableCell", 72], ["Text", 7], ["SectionHeader", 3], ["Reference", 2], ["TextInlineMath", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6270, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 183], ["TableCell", 56], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["Text", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Line", 107], ["Span", 20], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1379, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/An_Aggregation-Free_Federated_Learning_for_Tackling_Data_Heterogeneity"}