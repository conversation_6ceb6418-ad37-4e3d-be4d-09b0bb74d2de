{"table_of_contents": [{"title": "Are Large-scale Soft Labels Necessary for Large-scale\nDataset Distillation?", "heading_level": null, "page_id": 0, "polygon": [[105.0, 99.75], [504.421875, 99.75], [504.421875, 136.8017578125], [105.0, 136.8017578125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.69140625, 263.25], [328.5, 263.25], [328.5, 273.990234375], [282.69140625, 273.990234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.5322265625, 482.25], [191.25, 482.25], [191.25, 493.83984375], [106.5322265625, 493.83984375]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 1, "polygon": [[107.25, 570.41015625], [203.3525390625, 570.41015625], [203.3525390625, 582.78515625], [107.25, 582.78515625]]}, {"title": "3 Method", "heading_level": null, "page_id": 2, "polygon": [[106.5, 373.5], [167.25, 373.5], [167.25, 384.205078125], [106.5, 384.205078125]]}, {"title": "3.1 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 396.0], [188.25, 396.0], [188.25, 406.0546875], [106.98046875, 406.0546875]]}, {"title": "3.2 Diversity Analysis on Synthetic Dataset", "heading_level": null, "page_id": 3, "polygon": [[106.2333984375, 201.0], [297.75, 201.0], [297.75, 210.955078125], [106.2333984375, 210.955078125]]}, {"title": "3.2.1 Similarity within Synthetic Dataset: Feature Cosine Similarity", "heading_level": null, "page_id": 3, "polygon": [[106.083984375, 220.5], [402.8203125, 220.5], [402.8203125, 230.87109375], [106.083984375, 230.87109375]]}, {"title": "3.2.2 Similarity between Synthetic and Original Dataset: Maximum Mean Discrepancy", "heading_level": null, "page_id": 3, "polygon": [[106.5, 407.25], [485.25, 405.75], [485.25, 417.26953125], [106.5, 417.75]]}, {"title": "3.3 Label Pruning for Large-scale Distillation (LPLD)", "heading_level": null, "page_id": 3, "polygon": [[106.5, 629.25], [345.146484375, 629.25], [345.146484375, 639.6328125], [106.5, 639.6328125]]}, {"title": "3.3.1 Diverse Sample Generation via Class-wise Supervision", "heading_level": null, "page_id": 3, "polygon": [[106.5, 649.5], [369.75, 649.5], [369.75, 659.35546875], [106.5, 659.35546875]]}, {"title": "3.3.2 Random Label Pruning with Improved Label Pool", "heading_level": null, "page_id": 5, "polygon": [[106.75634765625, 486.0], [353.513671875, 486.0], [353.513671875, 496.16015625], [106.75634765625, 496.16015625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 457.48828125], [192.5947265625, 457.48828125], [192.5947265625, 469.08984375], [107.1298828125, 469.08984375]]}, {"title": "4.1 Experiment Settings", "heading_level": null, "page_id": 6, "polygon": [[106.681640625, 483.3984375], [218.443359375, 483.3984375], [218.443359375, 494.2265625], [106.681640625, 494.2265625]]}, {"title": "4.2 Primary Result", "heading_level": null, "page_id": 7, "polygon": [[106.8310546875, 203.25], [197.525390625, 203.25], [197.525390625, 213.08203125], [106.8310546875, 213.08203125]]}, {"title": "4.3 Analysis", "heading_level": null, "page_id": 7, "polygon": [[107.25, 418.5], [167.7919921875, 418.5], [167.7919921875, 428.25], [107.25, 428.25]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 572.25], [183.75, 572.25], [183.75, 583.55859375], [106.5, 583.55859375]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 9, "polygon": [[106.681640625, 72.75], [204.0, 72.75], [204.0, 83.6279296875], [106.681640625, 83.6279296875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 178.5], [165.251953125, 178.5], [165.251953125, 189.685546875], [107.25, 189.685546875]]}, {"title": "A Proof", "heading_level": null, "page_id": 12, "polygon": [[106.5, 72.0], [158.677734375, 72.0], [158.677734375, 83.4345703125], [106.5, 83.4345703125]]}, {"title": "A.1 Preliminary Analysis", "heading_level": null, "page_id": 12, "polygon": [[106.5, 154.5], [224.2705078125, 154.5], [224.2705078125, 164.6455078125], [106.5, 164.6455078125]]}, {"title": "A.2 Ch<PERSON>off Bound", "heading_level": null, "page_id": 12, "polygon": [[107.25, 533.25], [202.60546875, 533.25], [202.60546875, 543.7265625], [107.25, 543.7265625]]}, {"title": "A.3 BN Convergence", "heading_level": null, "page_id": 13, "polygon": [[105.75, 179.25], [204.0, 179.25], [204.0, 189.3955078125], [105.75, 189.3955078125]]}, {"title": "A.4 Combining Bounds", "heading_level": null, "page_id": 14, "polygon": [[106.5, 206.25], [216.0, 206.25], [216.0, 217.3359375], [106.5, 217.3359375]]}, {"title": "Event Definitions:", "heading_level": null, "page_id": 14, "polygon": [[106.5, 227.25], [185.25, 227.25], [185.25, 237.4453125], [106.5, 237.4453125]]}, {"title": "B Dataset Details", "heading_level": null, "page_id": 15, "polygon": [[106.5, 466.5], [205.5, 466.5], [205.5, 477.2109375], [106.5, 477.2109375]]}, {"title": "C Hyperparameter Settings", "heading_level": null, "page_id": 15, "polygon": [[107.25, 597.0], [259.5, 597.0], [259.5, 608.30859375], [107.25, 608.30859375]]}, {"title": "C.1 ImageNet-1K", "heading_level": null, "page_id": 15, "polygon": [[107.25, 620.25], [190.65234375, 620.25], [190.65234375, 631.125], [107.25, 631.125]]}, {"title": "C.2 Tiny-ImageNet", "heading_level": null, "page_id": 16, "polygon": [[106.5, 477.75], [198.75, 477.75], [198.75, 489.0], [106.5, 489.0]]}, {"title": "C.3 ImageNet-21K-P", "heading_level": null, "page_id": 17, "polygon": [[106.5, 286.5], [206.25, 286.5], [206.25, 297.7734375], [106.5, 297.7734375]]}, {"title": "C.4 Implementation of Baselines", "heading_level": null, "page_id": 18, "polygon": [[106.98046875, 73.5], [255.0, 73.5], [255.0, 83.6279296875], [106.98046875, 83.6279296875]]}, {"title": "D Additional Experiments", "heading_level": null, "page_id": 18, "polygon": [[106.5, 230.25], [252.2109375, 230.25], [252.2109375, 241.505859375], [106.5, 241.505859375]]}, {"title": "D.1 Ablation", "heading_level": null, "page_id": 18, "polygon": [[106.5, 256.5], [171.0791015625, 256.5], [171.0791015625, 266.642578125], [106.5, 266.642578125]]}, {"title": "D.2 Scaling on Large IPCs", "heading_level": null, "page_id": 18, "polygon": [[106.5, 447.75], [229.798828125, 447.75], [229.798828125, 457.875], [106.5, 457.875]]}, {"title": "D.3 Comparison with Fast Knowledge Distillation [25]", "heading_level": null, "page_id": 18, "polygon": [[106.5, 657.75], [347.25, 657.75], [347.25, 667.86328125], [106.5, 667.86328125]]}, {"title": "E Additional Information", "heading_level": null, "page_id": 20, "polygon": [[106.3828125, 72.0], [248.25, 72.0], [248.25, 83.724609375], [106.3828125, 83.724609375]]}, {"title": "E.1 Label Pruning Metrics", "heading_level": null, "page_id": 20, "polygon": [[106.5, 96.75], [230.25, 96.75], [230.25, 106.8310546875], [106.5, 106.8310546875]]}, {"title": "E.2 Image and Label Storage", "heading_level": null, "page_id": 20, "polygon": [[106.5, 273.0], [240.0, 273.0], [240.0, 283.271484375], [106.5, 283.271484375]]}, {"title": "E.3 Theoretical Analysis on the Number of Updates", "heading_level": null, "page_id": 20, "polygon": [[107.25, 475.5], [334.5, 475.5], [334.5, 485.71875], [107.25, 485.71875]]}, {"title": "Final Bound:", "heading_level": null, "page_id": 21, "polygon": [[105.75, 692.25], [165.75, 692.25], [165.75, 704.21484375], [105.75, 704.21484375]]}, {"title": "E.4 Class-wise Statistics Storage", "heading_level": null, "page_id": 22, "polygon": [[106.5, 223.5], [253.8544921875, 223.5], [253.8544921875, 233.96484375], [106.5, 233.96484375]]}, {"title": "E.5 Computing Resources", "heading_level": null, "page_id": 22, "polygon": [[106.5, 394.5], [227.70703125, 395.806640625], [227.70703125, 405.75], [106.5, 405.28125]]}, {"title": "E.6 Limitation and Future Work", "heading_level": null, "page_id": 22, "polygon": [[106.5, 472.5], [255.0, 472.5], [255.0, 482.23828125], [106.5, 482.23828125]]}, {"title": "E.7 Ethics Statement and Broader Impacts", "heading_level": null, "page_id": 22, "polygon": [[106.5, 570.75], [298.529296875, 570.75], [298.529296875, 581.23828125], [106.5, 581.23828125]]}, {"title": "F Visualization", "heading_level": null, "page_id": 23, "polygon": [[105.75, 70.5], [195.75, 70.5], [195.75, 83.86962890625], [105.75, 83.86962890625]]}, {"title": "F.1 ImageNet-1K", "heading_level": null, "page_id": 23, "polygon": [[105.9345703125, 261.0], [194.9853515625, 261.0], [194.9853515625, 286.9453125], [105.9345703125, 286.9453125]]}, {"title": "F.2 Tiny-ImageNet", "heading_level": null, "page_id": 24, "polygon": [[105.0, 70.5], [197.25, 70.5], [197.25, 84.75], [105.0, 84.75]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 83], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10236, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 39], ["Text", 7], ["Caption", 2], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 543], ["Line", 102], ["Text", 8], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 533], ["Line", 75], ["TableCell", 38], ["Text", 7], ["SectionHeader", 5], ["Reference", 3], ["Caption", 2], ["Equation", 2], ["TextInlineMath", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 121], ["Text", 7], ["Equation", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2956, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 103], ["Text", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 664], ["TableCell", 238], ["Line", 57], ["Text", 9], ["Table", 3], ["Caption", 3], ["SectionHeader", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 4887, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["TableCell", 111], ["Line", 69], ["Text", 8], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["TableCell", 160], ["Line", 80], ["Text", 7], ["Caption", 4], ["Table", 3], ["Reference", 3], ["TableGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 1301, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 51], ["ListItem", 18], ["Reference", 18], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 52], ["ListItem", 21], ["Reference", 21], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 18], ["ListItem", 6], ["Reference", 6], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 50], ["Text", 9], ["TextInlineMath", 5], ["Equation", 5], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 83], ["Equation", 10], ["Text", 9], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 83], ["Equation", 9], ["Text", 9], ["SectionHeader", 2], ["ListItem", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1870, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 69], ["TableCell", 23], ["Text", 6], ["ListItem", 6], ["Equation", 4], ["SectionHeader", 3], ["Reference", 3], ["ListGroup", 2], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 143], ["Span", 134], ["Line", 42], ["Reference", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 11708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 180], ["Span", 175], ["Line", 50], ["Reference", 4], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5388, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["TableCell", 51], ["Line", 45], ["Text", 6], ["Reference", 6], ["SectionHeader", 5], ["Table", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 9370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["TableCell", 134], ["Line", 51], ["Caption", 3], ["Table", 3], ["Reference", 3], ["Text", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 68], ["TableCell", 65], ["ListItem", 11], ["SectionHeader", 4], ["Text", 4], ["Reference", 4], ["Caption", 2], ["ListGroup", 2], ["Table", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 59], ["Equation", 5], ["Text", 5], ["TextInlineMath", 4], ["ListItem", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4543, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 45], ["TableCell", 32], ["Text", 9], ["Reference", 5], ["SectionHeader", 4], ["Equation", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1171, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 31], ["Line", 11], ["Text", 5], ["Picture", 4], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 1741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 7], ["Caption", 3], ["Picture", 2], ["PictureGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1839, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Are_Large-scale_Soft_Labels_Necessary_for_Large-scale_Dataset_Distillation"}