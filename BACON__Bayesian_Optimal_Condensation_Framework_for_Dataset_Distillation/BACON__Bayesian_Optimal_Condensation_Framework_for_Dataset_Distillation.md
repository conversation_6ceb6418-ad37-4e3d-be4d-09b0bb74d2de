# BACON: Bayesian Optimal Condensation Framework for Dataset Distillation

<PERSON>†, <PERSON><PERSON>†, <PERSON><PERSON><PERSON>g Cheng◇, Xiang<PERSON> Li◆, <PERSON><PERSON>†; <PERSON><PERSON><PERSON>† , <PERSON>†

†School of Electronic and Information Engineering, Beihang University  $\Diamond$ Department of Computer Science, University of Liverpool ♠S-Lab, Nanyang Technological University {zhengzhou, bhzhb, lyushuchang, buaafwq, zhaoqi}@buaa.edu.cn <EMAIL> <EMAIL>

# Abstract

Dataset Distillation (DD) aims to distill knowledge from extensive datasets into more compact ones while preserving performance on the test set, thereby reducing storage costs and training expenses. However, existing methods often suffer from computational intensity, particularly exhibiting suboptimal performance with large dataset sizes due to the lack of a robust theoretical framework for analyzing the DD problem. To address these challenges, we propose the BAyesian optimal CONdensation framework (BACON), which is the first work to introduce the Bayesian theoretical framework to the literature of DD. This framework provides theoretical support for enhancing the performance of DD. Furthermore, BACON formulates the DD problem as the minimization of the expected risk function in joint probability distributions using the Bayesian framework. Additionally, by analyzing the expected risk function for optimal condensation, we derive a numerically feasible lower bound based on specific assumptions, providing an approximate solution for BACON. We validate BACON across several datasets, demonstrating its superior performance compared to existing state-of-the-art methods. For instance, under the IPC-10 setting, BACON achieves a 3.46% accuracy gain over the IDM method on the CIFAR-10 dataset and a 3.10% gain on the TinyImageNet dataset. Our extensive experiments confirm the effectiveness of BACON and its seamless integration with existing methods, thereby enhancing their performance for the DD task. Code and distilled datasets are available at [BACON.](https://github.com/zhouzhengqd/BACON)

# 1 Introduction

Dataset Distillation (DD) is an emerging research topic focused on distilling a large dataset into a smaller set of synthetic samples [\[37\]](#page-12-0). This process enables models trained with these synthetic samples to achieve performance comparable to those trained on the entire dataset. DD is typically framed as a meta-learning problem [\[13\]](#page-10-0) involving bilevel optimization. In the inner-loop optimization, the learnable parameters of neural networks are trained, while in the outer-loop optimization, synthetic samples are generated by minimizing the classification loss on the original samples. Subsequently, Zhao *et al.* [\[47\]](#page-12-1) proposed Dataset Condensation (DC) to enhance the efficiency of DD tasks through gradient matching, as illustrated in Figure  $1(a)$ . Since 2018, DD has undergone significant development, leading to the emergence of various approaches. These include performance-matching methods (e.g., DD [\[37\]](#page-12-0), KIP [\[29,](#page-11-0) [30\]](#page-11-1), RFAD [\[26\]](#page-11-2), and FRePo [\[49\]](#page-12-2)), parameter-matching methods

<sup>∗</sup>Corresponding authors

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This is a comparative diagram illustrating two methods for generating synthetic data. The left side, labeled "(a) Previous Method," shows a process starting with "Updating Synthetic Images" which leads to a "Synthetic Dataset." This dataset, along with an "Original Dataset," is processed through "Gradient/Distribution" and "Gradient/Distribution Matching" steps, ultimately feeding into a representation of neural networks. The right side, labeled "(b) Our Method," presents a more complex framework. It begins with an "Original Dataset" and a "Synthetic Dataset," both feeding into a neural network representation. This is followed by "Updating Synthetic Images" which involves "Synthetic Steps" and a collection of sample images. The entire process is framed within the "Bayesian Optimal Condensation Framework (BACON)," which involves analyzing distributions, calculating "Observed Likelihood and Prior," and finally leading to the "Estimation of Posterior."

Figure 1: Comparison between our method and previous methods: (a) Existing state-of-the-art DD methods typically rely on a common paradigm involving the alignment of gradients [\[47\]](#page-12-1) and distributions [\[46,](#page-12-3) [48\]](#page-12-4) computed by neural networks on both original and synthetic datasets. (b) In contrast, our BACON method transfers the DD task into the Bayesian optimization problem and generates synthetic images by assessing the likelihood and prior probabilities.

(e.g., DC [\[47\]](#page-12-1), MTT [\[4\]](#page-10-1), TESLA [\[7\]](#page-10-2), and FTD [\[10\]](#page-10-3)), and distribution-matching methods (e.g., DM [\[46\]](#page-12-3), CAFE [\[36\]](#page-12-5), and IDM [\[48\]](#page-12-4)) along with other approaches [\[45,](#page-12-6) [14,](#page-10-4) [9,](#page-10-5) [23,](#page-11-3) [21,](#page-11-4) [5,](#page-10-6) [24,](#page-11-5) [2,](#page-10-7) [20\]](#page-11-6).

The development of DD and DC has greatly facilitated the advancement of DL and spurred innovation in various downstream tasks, including continual learning [\[15,](#page-10-8) [31,](#page-11-7) [25\]](#page-11-8), federated learning [\[16,](#page-11-9) [27,](#page-11-10) [44,](#page-12-7) [41\]](#page-12-8), knowledge distillation [\[22\]](#page-11-11), and adversarial learning [\[11,](#page-10-9) [50,](#page-12-9) [51,](#page-12-10) [39,](#page-12-11) [42\]](#page-12-12). However, existing methods often face computational intensity and demonstrate suboptimal performance with large datasets due to the absence of a robust theoretical framework for analyzing the optimization problem in DD. To explore potential solutions to these challenges, we propose the following key questions:

- \* *How can we effectively formulate the DD problem?*
- \* *What is the theoretical lower bound of optimal condensation?*

To address the above questions, we propose the BAyesian optimal CONdensation framework (BACON), which is the first work to introduce the Bayesian theoretical framework to the DD field. BACON provides a sound theoretical analysis framework for DD tasks, enabling us to formulate these tasks as the minimization of the expected risk function within joint probability distributions and to derive the theoretical lower bound of the risk function. In particular, we present a theoretical framework that facilitates the measurement of the risk of expectations in joint probability distributions. This framework leverages the output of a neural network trained on both original and synthetic datasets to represent the probability distribution. Subsequently, we analyze the optimal condensation risk function to solve the DD problem and obtain the approximated solution for BACON based on certain assumptions. Lastly, we design a highly effective optimization strategy for the DD task using BACON. The entire process of BACON is depicted in Figure [1\(](#page-1-0)b). We validate the proposed BACON on multiple image classification benchmarks and demonstrate its significant superiority over the state-of-the-art methods (DD [\[37\]](#page-12-0), LD [\[2\]](#page-10-7), DC [\[47\]](#page-12-1), DSA [\[45\]](#page-12-6), DCC [\[20\]](#page-11-6), CAFE [\[36\]](#page-12-5), DM [\[46\]](#page-12-3) and IDM [\[48\]](#page-12-4)) with multiple datasets such as MNIST [\[19\]](#page-11-12), Fashion-MNIST (F-MNIST) [\[40\]](#page-12-13), SVHN [\[28\]](#page-11-13), CIFAR-10 [\[17\]](#page-11-14), CIFAR-100 [\[17\]](#page-11-14) and TinyImageNet [\[8\]](#page-10-10). Furthermore, we assess the efficacy of various components and hyperparameters of BACON through a series of ablation studies. Finally, we visualize the synthetic image generated by BACON under different settings.

Our contributions can be summarized as follows:

- To the best of our knowledge, we are the *first* to introduce the Bayesian theoretical framework to the DD task, providing the theoretical support for improving distillation performance.
- We present the BACON, a novel and efficient method for the DD task. BACON utilizes the Bayesian framework to formulate the DD problem as the minimization of expected risk function in joint probability distributions.
- Through comprehensive analysis, we derive a numerically feasible lower bound for minimizing the expected risk function in joint probability distributions, based on certain assumptions.

• Experimental results demonstrate the superiority of BACON over existing approaches, which can be seamlessly integrated as a plug-and-play module into existing methods.

# 2 Related Work

Dataset Pruning The traditional approach for reducing training dataset sizes is dataset pruning, also referred to as core-set selection [\[43\]](#page-12-14). This method aims to gather the most representative and valuable samples from the original dataset, resulting in a smaller yet comparable dataset without compromising model performance. While various techniques such as Herding [\[38\]](#page-12-15), K-center [\[33\]](#page-11-15), and the Forgetting method [\[35\]](#page-12-16) have been explored, they suffer from three main drawbacks: 1) reliance on heuristic algorithms, 2) prone to local optima, and 3) the omission of many sub-optimal representative samples due to employed dropping strategies.

Dataset Distillation (DD) Unlike core-set selection, DD achieves optimal performance with complete representative features. DD was first proposed by Wang *et al.* [\[37\]](#page-12-0) as a bi-level optimization problem. However, tackling the bi-level optimization problem entails additional computational expenses due to its nested recursion. To mitigate these overheads, Zhao *et al.* [\[47\]](#page-12-1) introduced a gradient matching method called Dataset Condensation (DC). This method enhances overall performance by matching the informative gradients calculated from the original datasets with those from the synthetic datasets at each iteration. Furthermore, Zhao *et al.* [\[46\]](#page-12-3) presented a distribution matching method referred to as DM, employing the Maximum Mean Discrepancy (MMD) measurement metric. To further improve the distillation performance, Zhao *et al.* [\[48\]](#page-12-4) proposed a more efficient and promising method known as Improved Distribution Matching (IDM), built upon the DM. In terms of the research on theoretical foundations for DD, Shang *et al.* [\[34\]](#page-12-17) were the first to introduce information theory into DD research. They formulated DD as a mutual information maximization problem within the information theory framework. Subsequent studies have explored various optimization objectives to constrain image synthesis, such as DSA [\[45\]](#page-12-6), CAFE [\[36\]](#page-12-5), MTT [\[4\]](#page-10-1), DREAM [\[24\]](#page-11-5) and others [\[14,](#page-10-4) [9,](#page-10-5) [23,](#page-11-3) [21,](#page-11-4) [5,](#page-10-6) [24\]](#page-11-5).

Bayesian Framework for Matching Gradients Method The Bayesian framework for matching gradients method was first proposed by Mislav *et al.* [\[1\]](#page-10-11) in the field of gradient leakage, which aimed to reconstruct datasets by aligning gradients. They formalized the problem of gradient leakage as the Bayes optimal adversary framed as an optimization problem to achieve higher reconstruction success.

In contrast, we introduce a novel and optimal condensation framework based on Bayesian optimization for the DD task. Inspired by the incorporation of gradient leakage [\[1\]](#page-10-11), DM [\[46\]](#page-12-3), and IDM [\[48\]](#page-12-4), our approach diverges significantly from previous methods. Unlike the work of Mislav *et al.* [\[1\]](#page-10-11), which aligns gradients with respect to the weights of the neural network, we align the joint probability distribution of the neural network output to generate synthetic samples. Furthermore, whereas DM and IDM [\[46,](#page-12-3) [48\]](#page-12-4) focus merely on distribution matching, our method, BACON, leverages the Bayesian framework to comprehensively analyze the expected risk function of probabilities. This approach allows us to derive the theoretical lower bound of risk, effectively bridging the gap between theory and practice under certain assumptions. Further details are provided in Appendix [B.](#page-15-0)

# 3 Bayesian Optimal Condensation Framework

In this section, we present the motivation behind our exploration of BACON. We define the Bayesian optimal condensation risk function based on the probability distribution and provide a brief introduction to the meaning of notations. Subsequently, we derive the risk function and determine the theoretically optimal solution. Finally, we propose assumptions for the log-likelihood and prior probability to obtain approximate solutions, aiming to streamline practical implementation and delineate the training strategy. Additional details regarding our method are provided in Appendix [A,](#page-13-0) which includes proofs and discussions.

## 3.1 Motivation

Dataset Distillation (DD) is crucial for reducing storage costs and training expenses while maintaining test performance. However, current methods often face significant computational challenges,

<span id="page-3-1"></span>Image /page/3/Figure/0 description: This is a flowchart illustrating a method for matching probability distributions by minimizing a risk function. The process begins with sampling original data from a dataset, which is then fed into initial neural networks with the same architectures. Simultaneously, data is processed through different levels of magnitude (IPC 1, IPC 10, IPC 50), also involving neural networks. The outputs from these neural networks are visualized as clustered data points (squares, triangles, circles) within circular regions, with arrows indicating a 'Push' operation. The process then moves to matching probability distributions by minimizing a risk function, represented by matrices of probabilities P(i,j). Finally, a gradient descent step is applied using an approximation loss formula: xi+1 ← xi + α∇φ(∑(log fφ(z\*|zi)) + log fφ(zi)). The overall diagram shows a complex process involving neural networks, data sampling, probability matching, and gradient descent.

Figure 2: The framework of proposed BACON: The neural networks output the distribution after processing both synthetic and real datasets. Subsequently, we formulate the distribution between the synthetic dataset and real datasets as the Bayesian optimal condensation risk function (refer to Section [3.2\)](#page-3-0). The optimal solution of the risk function is derived using the Bayesian formula (refer to Section [3.3\)](#page-4-0). To obtain the approximated solution of BACON, we introduce two assumptions (refer to Section [3.4\)](#page-4-1), and outline the entire algorithm of BACON in Algorithm [1](#page-5-0) (refer to Section [3.5\)](#page-5-1).

especially with large datasets, due to the lack of a robust theoretical framework. We identified two key questions to address these challenges: 1) *How can we effectively formulate the DD problem?* and 2) *What is the theoretical lower bound of optimal condensation?* Inspired by Mislav *et al.* [\[1\]](#page-10-11), who used the Bayesian optimal adversary framework to address gradient leakage, we extend this approach to DD to find the optimal path for maximizing distillation performance while maintaining test accuracy. Subsequently, we propose BACON, a novel method that provides the first theoretical analysis of optimal condensation using Bayesian principles. This robust theoretical foundation significantly enhances distillation performance. The training strategy of BACON is illustrated in Figure [2.](#page-3-1)

<span id="page-3-0"></span>

### 3.2 Expected Risk Function in Joint Probability Distribution

<span id="page-3-3"></span>**Definition 3.1** (Similarity Indicator of  $\epsilon$ -neighborhood). Let  $z_x$  and  $z_x$  be two distributions. A binary loss function  $\mathcal L$  is used to assess their similarity. When the Euclidean distance between the two distributions is small, L is equal to 0; otherwise, L is equal to 1. As  $\epsilon$  approaches zero, the synthetic distribution  $z_{\tilde{x}}$  approximates the original distribution  $z_x$ . We define the similarity indicator function as follows:

$$
\mathcal{L}(z_x, z_{\tilde{x}}) := \mathbb{1}\{\|z_x - z_{\tilde{x}}\|_2 \ge \epsilon\}.
$$
\n<sup>(1)</sup>

<span id="page-3-4"></span>**Definition 3.2** (Expected Risk Function). Consider a joint probability distribution  $p(z_x, z_{\tilde{x}})$  formed by the probability distribution of the outputs of neural networks,  $\mathcal{D}_T$  and  $\mathcal{D}_S$ , for the original dataset  $\tilde{T}$  and the synthetic dataset S. Given  $z_x \in \mathcal{D}_T$  and  $z_{\tilde{x}} \in \mathcal{D}_S$ , the expected risk function in the joint probability distribution  $R(\phi)$  is defined as follows:

$$
R(\phi) := \mathbb{E}_{(z_x, z_{\tilde{x}}) \sim p(z_x, z_{\tilde{x}})}[\mathcal{L}(z_x, z_{\tilde{x}})],\tag{2}
$$

where  $z_x = \phi(\theta, x)$  represents the output of the neural network, and  $\phi(\theta, x) : X \subseteq \mathbb{R}^n \to \mathcal{D} \subseteq \mathbb{R}^N$ is parameterized by  $\theta$  with  $n \ll N$ . We map  $x \in \mathbb{R}^n$  to a higher dimensional space  $\mathbb{R}^N$  using z.

<span id="page-3-5"></span>**Definition 3.3** (Sphere Integral Function). The spherical integral, denoted by  $\beta$ , represents the integration over a sphere with a radius of  $\epsilon$  and a center point of  $z_{\tilde{x}}$ .

$$
\mathcal{B}(z_{\tilde{x}}, \epsilon) = \{z_{\tilde{x}}; \|z_x - z_{\tilde{x}}\|_2 \le \epsilon\}.
$$
\n(3)

<span id="page-3-2"></span>Theorem 3.4. *The expected risk function in a joint probability distribution can also be calculated as follows (Proof in Appendix [A.1\)](#page-13-1):*

$$
R(\phi) = 1 - \mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x.
$$
 (4)

*Remark* 3.5*.* The proof of Theorem [3.4](#page-3-2) (in Appendix [A.1\)](#page-13-1) demonstrates that we can transform the problem of minimizing the expected risk function  $R(\phi)$  into the problem of maximizing the probabilistic expectation  $\mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x$  over a sphere integral domain  $\mathcal{B}(z_{\tilde{x}}, \epsilon)$ . By finding an optimal value of  $z_{\tilde{x}}$ , denoted as  $z_{\tilde{x}}^*$ , that maximizes this probabilistic expectation, we effectively promote the minimization of  $R(\phi)$ . This optimization problem can be expressed as:

<span id="page-4-2"></span>
$$
z_{\tilde{x}} = \underset{z_{\tilde{x}}^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_{\tilde{x}}^*, \epsilon)} p(z_x | z_{\tilde{x}}^*) dz_x.
$$
 (5)

<span id="page-4-0"></span>

### 3.3 Bayesian Optimal Condensation Risk Function

<span id="page-4-10"></span>**Theorem 3.6.** *The optimal synthetic image*  $z_{\tilde{x}}$  *can be computed as follows (Proof in Appendix [A.2\)](#page-14-0):* 

<span id="page-4-3"></span>
$$
z_{\tilde{x}} = \underset{z_{\tilde{x}}^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_{\tilde{x}}^*, \epsilon)} \left[ \log p(z_{\tilde{x}}^* | z_x) + \log p(z_x) \right] dz_x. \tag{6}
$$

*Remark* 3.7*.* By applying Bayes' rule and Jensen's inequality, we derive Eq. [\(5\)](#page-4-2), which provides the formulaic representation for the log-likelihood and prior of the probability distribution as shown in Eq. [\(6\)](#page-4-3). To obtain the realization of the random variable  $D_s = z_{\tilde{x}}^*$ , the objective is to find a series  $z_x$  within the spherical region B that maximizes the integral function (Eq. [\(6\)](#page-4-3)). It is important to note that as  $\epsilon$  approaches zero,  $z_{\tilde{x}}$  approaches arg max $z_x p(z_x|z_{\tilde{x}})$ , which represents the solution for minimizing  $R(\phi)$  by matching probability distributions. Since the solution for  $p(z_x|z_x^*)$  cannot be obtained directly, the Bayesian formula is employed to rewrite it. To further investigate the lower bound of the function, we employ Jensen's inequality as an approximation method.

<span id="page-4-1"></span>

## 3.4 Approximating the Optimal Solution for Bayesian Condensation

The Eq. [\(6\)](#page-4-3) offers an optimal condensation solution within the Bayesian framework. However, its practical application faces three challenges. Firstly, computing the integral over the spherical region  $\mathcal{B}(z_{\tilde{x}}, \epsilon)$  is challenging. Secondly, it is generally not possible to obtain the closed-form likelihood  $p(z_{\tilde{x}}^*|z_{x_i})$ . Lastly, knowing the exact prior distribution  $\log p(z_{x_i})$  is necessary.

Monte Carlo Approximation: To tackle the initial obstacle, we employ Monte Carlo sampling [\[12\]](#page-10-12) to discretize the continuous expression by uniformly sampling k points, represented as  $z_{x_1}, \ldots, z_{x_k}$ . The resultant discrete form of the expression is acquired through the subsequent procedure:

<span id="page-4-6"></span>
$$
\frac{1}{k} \sum_{i=1}^{k} \log p(z_{\tilde{x}}^* | z_{x_i}) + \log p(z_{x_i}). \tag{7}
$$

<span id="page-4-4"></span>**Assumption 1** (Likelihood Conforming Gaussian). *To estimate the log-likelihood*  $\log p(z_{\tilde{x}}^* | z_{x_i})$ , we make the assumption that  $p(z_x^*|z_{x_i})$  conforms to a Gaussian distribution. In this distribution,  $\sigma_x^2$ *represents the variance and*  $z_{x_i}$  *represents the mean. It is denoted as*  $p(z_x^*|z_{x_i}) \sim \mathcal{N}(z_{x_i}, \sigma_{xi}^2 I)$ *.* 

<span id="page-4-5"></span>Assumption 2 (Prior Distribution Approximation with TV Extension). *The Total Variation (TV)* and CLIP operation are incorporated as distribution priors to represent  $\log p(z_{x_i})$ , following the *approach of Mislav et al. [\[1\]](#page-10-11). The CLIP operation constrains the probability within the bound of* [0, 1]*. In contrast to their study, we extend the TV from a pixel-wise approach to a distribution-wise approach, which is also referred to as the total variation of probability distribution measures.*

Under the Assumption [1](#page-4-4) and Assumption [2,](#page-4-5) we divide Eq. [\(7\)](#page-4-6) into three separate loss terms as follows:

<span id="page-4-7"></span>
$$
\mathcal{L}_{\text{LH}} = -\frac{1}{2}\log(2\pi\sigma_x) - \frac{1}{2\sigma_x^2} \|z_x^* - z_x\|_2^2, \tag{8}
$$

<span id="page-4-8"></span>
$$
\mathcal{L}_{\text{TV}} = \frac{1}{2} \| z_{\tilde{x}}^* - z_x \|_1,\tag{9}
$$

<span id="page-4-9"></span>
$$
\mathcal{L}_{CLIP} = \left[ \frac{z_x^* - z_x}{\sigma_x} - CLIP \left( \frac{z_x^* - z_x}{\sigma_x}, 0, 1 \right) \right]^2.
$$
 (10)

### Algorithm 1: BAyesian optimal CONdensation framework (BACON)

<span id="page-5-0"></span>**Require:** Original dataset:  $\mathcal{T}$ , neural network:  $\phi(\theta, x)$ , probability distribution outputted by neural network:  $z_x$  and  $z_{\tilde{x}}$ , where x is the original image and  $\tilde{x}$  is the synthetic image.

**Ensure:** Synthetic dataset  $S$ .

- 1: Initialize  $x_1$  from T by randomly sampling.
- 2: for  $i = 1$  to  $m 1$  do
- 3: Sample  $x_1, \ldots, x_k$  uniformly from  $\mathcal{B}(z_{\tilde{x}}, \epsilon)$
- 4: Calculate  $\mathcal{L}_{LH}$  using Eq. [\(8\)](#page-4-7)
- 5: Calculate  $\mathcal{L}_{TV}$  according to Eq. [\(9\)](#page-4-8)
- 6: Calculate  $\mathcal{L}_{CLIP}$  by Eq. [\(10\)](#page-4-9)
- 7: Update the condensed dataset S by  $\tilde{x}_{i+1} \leftarrow \tilde{x}_i + \alpha \nabla_{z_i} \mathcal{L}_{\text{TOTAL}}$ , where  $\mathcal{L}_{\text{TOTAL}}$  is defined by Eq. [\(11\)](#page-5-2), and  $\alpha$  denotes the learning rate for generating S.
- 8: end for
- <span id="page-5-1"></span>9: return  $S$

### 3.5 Overall Loss Function and Pseudocode

Overall Loss Function: To summarize, the overall loss function of BACON integrates Eq. [\(8\)](#page-4-7), Eq. [\(9\)](#page-4-8), and Eq. [\(10\)](#page-4-9). The expression for this combined loss function can be defined as follows:

<span id="page-5-2"></span>
$$
\mathcal{L}_{\text{TOTAL}} = \mathcal{L}_{\text{LH}} + \lambda \mathcal{L}_{\text{TV}} + (1 - \lambda) \mathcal{L}_{\text{CLIP}},\tag{11}
$$

where the hyperparameter  $\lambda$  serves as the weighting factor for the total loss function and is adjustable. By tuning  $\lambda$ , we can customize the loss function to optimize performance.

**Pseudocode Description:** The pseudocode of our algorithm is presented in Algorithm [1.](#page-5-0) Our proposed BACON is incorporated into the optimization pipeline to effectively guide algorithm optimization and enhance performance. The input to the algorithm comprises the original dataset T, the neural network  $\phi(\theta, x)$ , and the probability distributions outputted by the neural network  $z_x$ and  $z_{\tilde{x}}$ , where x represents the original image and  $\tilde{x}$  denotes the synthetic image. The output of the algorithm is the synthetic dataset S. In the algorithm, the initial image  $x_1$  is randomly sampled from the original dataset T. Then, in each iteration, k images  $x_1, \ldots, x_k$  are uniformly sampled from  $\mathcal{B}(z_{\tilde{x}}, \epsilon)$  for each *i*. Subsequently, the total loss function  $\mathcal{L}_{\text{TOTAL}}$  is calculated based on three loss functions:  $\mathcal{L}_{L,H}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$ . Finally, the synthetic dataset S is updated by generating the next synthetic image  $\tilde{x}_{i+1}$ .

# 4 Experimental Evaluation

To demonstrate the effectiveness of BACON, we conducted extensive experiments on both large-scale and small-scale benchmark datasets. Furthermore, comprehensive comparative experiments and ablation studies were performed to further assess its performance. Finally, we presented the outcomes of BACON through visualization. Additional information about our experiments can be found in Appendix [C.](#page-16-0)

### 4.1 Experiment Setup

For a systematic evaluation of our method, we assess its efficacy through experiments conducted on widely-used dataset distillation benchmarks, including the MNIST [\[19\]](#page-11-12), Fashion-MNIST [\[40\]](#page-12-13), SVHN [\[28\]](#page-11-13), CIFAR-10/100 [\[17\]](#page-11-14), and TinyImageNet [\[8\]](#page-10-10). We employ the ConvNet architecture [\[32\]](#page-11-16) for dataset distillation experiments, following prior research approaches [\[46,](#page-12-3) [14\]](#page-10-4). The performance of the synthetic dataset is assessed by averaging the top-1 accuracy of the trained model over five experiments on the validation set with IPC-50, IPC-10, and IPC-1, respectively. We set  $\lambda$  in Eq. [\(11\)](#page-5-2) to 0.8 in our experiments, except for ablation studies. Additionally, we maintain consistency with IDM [\[48\]](#page-12-4) for most hyperparameter settings, and the basic environment adheres to the guidelines outlined in DC-bench [\[6\]](#page-10-13). Further implementation details can be found in Appendix [C.1.](#page-16-1)

<span id="page-6-1"></span>Image /page/6/Figure/0 description: This figure displays six line graphs comparing the test accuracy of three methods (BACON, IDM, and DM) on CIFAR-10 and CIFAR-100 datasets with varying IPC (Inter-Class) values of 1, 10, and 50. The x-axis for all graphs represents training steps, scaled by 20 steps, ranging from 0 to 1000. The y-axis represents test accuracy in percentage, ranging from 0 to 70% for CIFAR-10 graphs and 0 to 50% for CIFAR-100 graphs. For CIFAR-10 with IPC=1, BACON reaches approximately 42%, IDM reaches approximately 41%, and DM reaches approximately 25%. For CIFAR-10 with IPC=10, BACON reaches approximately 60%, IDM reaches approximately 55%, and DM reaches approximately 47%. For CIFAR-10 with IPC=50, BACON reaches approximately 68%, IDM reaches approximately 64%, and DM reaches approximately 60%. For CIFAR-100 with IPC=1, BACON reaches approximately 22%, IDM reaches approximately 19%, and DM reaches approximately 10%. For CIFAR-100 with IPC=10, BACON reaches approximately 44%, IDM reaches approximately 42%, and DM reaches approximately 28%. For CIFAR-100 with IPC=50, BACON reaches approximately 52%, IDM reaches approximately 48%, and DM reaches approximately 42%.

Figure 3: Performance comparison with BACON, IDM, and DM across varying training steps on the CIFAR-10/100 datasets: The blue line with white circles represents our proposed BACON, the orange line with white circles represents IDM, and the green line with white circles represents DM. All synthetic images are generated using the CIFAR-10/100 datasets across training steps from 0 to 20000 with IPC-1, IPC-10, and IPC-50, respectively.

<span id="page-6-0"></span>Table 1: Comparison with previous coreset selection and dataset condensation methods: Like most state-of-the-art methods, we evaluate our method on six datasets (MNIST, Fashion-MNIST, SVHN, CIFAR-10/100, TinyImageNet) with different numbers of synthetic images per class (IPC). The "Ratio(%)" represents the condensed images' ratio to the entire training set. For reference, "Full Set" indicates the accuracy of the trained model on the complete training set. It's important to note that DD and LD employ different architectures, specifically LeNet [\[19\]](#page-11-12) for MNIST and AlexNet [\[18\]](#page-11-17) for CIFAR-10. Meanwhile, the remaining methods all utilize ConvNet [\[32\]](#page-11-16).

| For CIT AR-TO. MCallwrifle, the remaining includes an utilize Converge $(32)$ . |              |       |       |               |       |       |             |                                                                                                                                                                                                                                                                                                                              |                          |       |           |       |              |                          |       |      |      |
|---------------------------------------------------------------------------------|--------------|-------|-------|---------------|-------|-------|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|-------|-----------|-------|--------------|--------------------------|-------|------|------|
|                                                                                 | <b>MNIST</b> |       |       | Fashion-MNIST |       |       | <b>SVHN</b> |                                                                                                                                                                                                                                                                                                                              | CIFAR-10                 |       | CIFAR-100 |       | TinyImageNet |                          |       |      |      |
| <b>IPC</b>                                                                      |              | 10    | 50    |               | 10    | 50    |             | 10                                                                                                                                                                                                                                                                                                                           | 50                       |       | 10        | 50    |              | 10                       | 50    |      | 10   |
| Ratio $(\% )$                                                                   | 0.017        | 0.17  | 0.83  | 0.017         | 0.17  | 0.83  | 0.014       | 0.14                                                                                                                                                                                                                                                                                                                         | 0.68                     | 0.02  | 0.2       |       | 0.2          | $\overline{c}$           | 10    | 0.2  | 2    |
| Random [3]                                                                      | 64.9         | 95.1  | 97.9  | 51.4          | 73.8  | 82.5  | 14.6        | 35.1                                                                                                                                                                                                                                                                                                                         | 70.9                     | 14.4  | 26        | 43.4  | 4.2          | 14.6                     | 30    | 1.4  | 5.   |
| Herding [31]                                                                    | 89.2         | 93.7  | 94.8  | 67            | 71.1  | 71.9  | 20.9        | 50.5                                                                                                                                                                                                                                                                                                                         | 72.6                     | 21.5  | 31.6      | 40.4  | 8.4          | 17.3                     | 33.7  | 2.8  | 6.3  |
| DD [37]                                                                         | ۰.           | 79.5  |       |               |       |       |             |                                                                                                                                                                                                                                                                                                                              |                          |       | 36.8      |       |              |                          |       |      |      |
| LD <sub>[2]</sub>                                                               | 60.9         | 87.3  | 93.3  | ۰             |       | ٠     |             |                                                                                                                                                                                                                                                                                                                              | $\overline{\phantom{0}}$ | 25.7  | 38.3      | 42.5  | 11.5         | $\overline{\phantom{a}}$ |       |      |      |
| DC [47]                                                                         | 91.7         | 97.4  | 98.8  | 70.5          | 82.3  | 83.6  | 31.2        | 76.1                                                                                                                                                                                                                                                                                                                         | 82.3                     | 28.3  | 44.9      | 53.9  | 12.8         | 26.6                     | 32.1  | 5.3  | 11.1 |
| <b>DSA</b> [45]                                                                 | 88.7         | 97.8  | 99.2  | 70.6          | 86.6  | 88.7  | 27.5        | 79.2                                                                                                                                                                                                                                                                                                                         | 84.4                     | 28.8  | 53.2      | 60.6  | 13.9         | 32.3                     | 42.8  | 6.6  | 16.3 |
| <b>DCC</b> [20]                                                                 |              |       |       |               | -     | ٠     | 47.5        | 80.5                                                                                                                                                                                                                                                                                                                         | 80.5                     | 34    | 54.5      | 64.2  | 14.6         | 33.5                     | 39.3  |      |      |
| CAFE(DSA) [36]                                                                  | 90.8         | 97.5  | 98.9  | 73.7          | 83    | 88.2  | 42.9        | 77.9                                                                                                                                                                                                                                                                                                                         | 82.3                     | 31.6  | 50.9      | 62.3  | 14           | 31.5                     | 42.9  |      |      |
| DM [46]                                                                         | 89.2         | 97.3  | 94.8  | ۰             |       | ٠     |             |                                                                                                                                                                                                                                                                                                                              | $\overline{\phantom{a}}$ | 26    | 48.9      | 63    | 11.4         | 29.7                     | 43.6  | 3.9  | 12.9 |
| IDM $[48]$                                                                      | 93.82        | 96.26 | 97.01 | 78.23         | 82.53 | 84.03 | 69.45       | 82.95                                                                                                                                                                                                                                                                                                                        | 87.5                     | 45.60 | 58.6      | 67.5  | 20.1         | 45.1                     | 50    | 10.1 | 21.9 |
| <b>BACON</b> [Ours]                                                             | 94.15        | 97.3  | 98.01 | 78.48         | 84.23 | 85.52 | 69.44       | 84.64                                                                                                                                                                                                                                                                                                                        | 89.1                     | 45.62 | 62.06     | 70.06 | 23.68        | 46.15                    | 52.29 | 10.2 | 25   |
|                                                                                 |              |       |       |               |       |       |             | $(0.33 \uparrow)$ (1.04 $\uparrow$ ) (1.00 $\uparrow$ ) $(0.25 \uparrow)$ (1.70 $\uparrow$ ) (1.49 $\uparrow$ ) $(0.01 \downarrow)$ (1.69 $\uparrow$ ) (1.60 $\uparrow$ ) $(0.02 \uparrow)$ (3.46 $\uparrow$ ) (2.56 $\uparrow$ ) $(3.58 \uparrow)$ (1.05 $\uparrow$ ) (2.29 $\uparrow$ ) $(0.1 \uparrow)$ (3.1 $\uparrow$ ) |                          |       |           |       |              |                          |       |      |      |
| Full Set                                                                        |              | 99.6  |       |               | 93.5  |       |             | 95.4                                                                                                                                                                                                                                                                                                                         |                          |       | 84.8      |       |              | 56.2                     |       |      | 37.6 |

### 4.2 Comparison to the State-of-the-art Methods

For the convenience of analyzing the performance of our method across multiple datasets, we categorized the datasets into three groups based on their resolution: (1) low resolution (MNIST and F-MNIST); (2) medium resolution (SVHN, CIFAR-10, and CIFAR-100); and (3) high resolution (TinyImageNet). To assess its performance, we compared BACON with state-of-the-art methods such as core-set selection and dataset distillation methods. The results are illustrated in Table [1,](#page-6-0) and the performance comparison of BACON, IDM, and DM across varying training steps on CIFAR-10/100 datasets is shown in Figure [3.](#page-6-1) For more details on comparable experiments, please refer to Appendix [C.4.](#page-18-0)

Analysis On low-resolution datasets, BACON outperforms IDM across all IPC settings. Specifically, with IPC-1, BACON achieves an accuracy of 94.15% on the MNIST dataset and 78.48% on the F-MNIST dataset, which are the highest accuracies compared to other methods. The performance

<span id="page-7-0"></span>Table 2: Ablation study of diverse loss functions: Evaluating the performance of the proposed loss functions  $\mathcal{L}_{LH}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$  individually. All experimental hyperparameters, represented by  $\lambda$ , are set to 0.8 by default. This experiment focuses on the CIFAR-10 dataset with an IPC setting of 50.

| $\mathcal{L}_{LH}$ | $\mathcal{L}_{TV}$ | $\mathcal{L}_{CLIP}$ | Test acc. (%)                        |
|--------------------|--------------------|----------------------|--------------------------------------|
| ✓                  | X                  | X                    | 64.86                                |
| X                  | ✓                  | X                    | 69.96                                |
| ✓                  | X                  | X                    | 55.07                                |
| ✓                  | ✓                  | X                    | 69.81                                |
| ✓                  | X                  | ✓                    | 64.78                                |
| X                  | ✓                  | ✓                    | 69.76                                |
| ✓                  | ✓                  | ✓                    | <span style="color:red">70.06</span> |

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image is a line graph titled "Effect of Diverse \lambda on Test Accuracy (CIFAR-10, IPC-50)". The x-axis is labeled "Hyperparameter \lambda" and ranges from 0.0 to 1.0. The y-axis is labeled "Test Accuracy (%)" and ranges from 65 to 70. The graph shows a line with points at \lambda = 0.0, 0.2, 0.4, 0.6, 0.8, and 1.0. The corresponding test accuracies are approximately 64.9%, 68.2%, 69.1%, 69.7%, 70.1%, and 69.7%. There is a shaded region around the line, indicating some variability or confidence interval.

Figure 4: Ablation study of diverse hyperparameters: Sampling diverse hyperparameters from  $\lambda = [0.0, 1.0]$  and obtaining the effectiveness of diverse  $\lambda$  on the test accuracy with the CIFAR-10 dataset and 50 images per class (IPC-50).

<span id="page-7-2"></span>Image /page/7/Figure/4 description: This image displays a grid of six figures, each containing two smaller grids of four images. The figures are labeled with a lambda value ranging from 0 to 1.0 in increments of 0.2. The top row shows figures labeled lambda=0, lambda=0.2, and lambda=0.4. The bottom row shows figures labeled lambda=0.6, lambda=0.8, and lambda=1.0. Each of the smaller grids contains images of airplanes and cars. The figures illustrate a visualization of different lambda values.

Figure 5: Visualization of diverse hyperparameters: Visualizing synthetic images generated with diverse hyperparameters on CIFAR-10 test accuracy, using 50 images per class (IPC-50). The left image of a pair represents an airplane, and the right one represents an automobile.

improvement is comparatively limited under the IPC settings of 10 or 50, primarily due to the constraints imposed by our method built upon IDM. On medium-resolution datasets, BACON achieves the highest accuracy of 89.1% on SVHN and 70.06% on CIFAR-10, which are close to the performance of models trained on the original datasets. Our method generally outperforms others on medium-resolution datasets, with negligible performance degradation observed for IPC-1 on SVHN, decreasing by only 0.01. The most likely reason for this is that low resolution and a low IPC setting can cause the synthetic dataset to deviate from its Gaussian distribution, undermining the accuracy of our approximate solution in accurately describing the optimization direction. This deviation is evident in SVHN with IPC-1 and CIFAR-10 with IPC-1. Ultimately, BACON slightly outperforms other methods with IPC-1 in high-resolution scenarios and significantly surpasses them with a higher IPC setting. This finding further supports our inference that the approximate solution space of BACON may lead to optimization in the wrong direction under low-resolution and low IPC settings.

### 4.3 Ablation Studies

Effectiveness of Diverse Loss Functions The impacts of the three loss function terms in BACON, namely  $\mathcal{L}_{L,H}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$ , on test accuracy are presented in Table [2.](#page-7-0) Synthetic datasets were generated on CIFAR-10 using diverse loss terms with an IPC-50 setting by BACON. IDM and DM achieved test accuracies of 67.5% and 63%, respectively, under similar experimental settings. However, our proposed BACON achieved the highest accuracy of 70.06% with  $\mathcal{L}_{\text{TOTAL}}$ . When utilizing only one loss term from  $\mathcal{L}_{L,H}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$ ,  $\mathcal{L}_{TV}$  resulted in the highest accuracy (69.96%), followed by  $\mathcal{L}_{LH}$  (64.86%), and  $\mathcal{L}_{CLIP}$  (55.07%). When two loss terms are used from  $\mathcal{L}_{LH}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$ ,  $\mathcal{L}_{LH} + \mathcal{L}_{TV}$  resulted in the highest accuracy (69.81%), followed by  $\mathcal{L}_{TV} + \mathcal{L}_{CLIP}$  (69.76%),

<span id="page-8-0"></span>Image /page/8/Figure/0 description: This figure displays results from two datasets, CIFAR-10 and SVHN, comparing different numbers of "IPC" (likely referring to instances per class) and visualizing the results using t-SNE. The top row, labeled (a) CIFAR-10, shows three grids of images: (a-1) IPC-1, (a-2) IPC-10, and (a-3) IPC-50. Each grid contains multiple images representing different classes. To the right of these image grids is a t-SNE plot labeled (a-4) t-SNE, which shows distinct clusters of points, each color-coded to represent a different class (Class 0 through Class 9). The bottom row, labeled (b) SVHN, follows a similar structure. It shows three grids of images: (b-1) IPC-1, (b-2) IPC-10, and (b-3) IPC-50, displaying images of handwritten digits. To the right is a t-SNE plot labeled (b-4) t-SNE, also showing color-coded clusters representing different digit classes (Class 0 through Class 9).

Figure 6: Visualizations of BACON with IPC-1/10/50 on CIFAR-10 and SVHN datasets: (a-1), (a-2), and (a-3) represent the synthetic images generated on the CIFAR-10 dataset with the settings of IPC-1, IPC-10, and IPC-50, respectively. (b-1), (b-2), and (b-3) represent the synthetic images generated on the SVHN dataset with the settings of IPC-1, IPC-10, and IPC-50. (a-4) and (b-4) denote the visualizations of clusters of classes by t-SNE.

and  $\mathcal{L}_{L,H} + \mathcal{L}_{CLIP}$  (64.78%). From the data above, it is evident that  $\mathcal{L}_{TV}$  significantly contributes to improving accuracy, while the other two terms do not contribute as much. Remarkably, amalgamating all three terms results in the highest performance, as evidenced by the findings presented in the final row of Table [2.](#page-7-0) Further details regarding the ablation studies can be found in the Appendix [C.5.](#page-18-1)

**Effectiveness of Diverse Hyperparameter**  $\lambda$  We explored the impact of diverse hyperparameters  $\lambda$ ranging from 0 to 1 on the CIFAR-10 dataset using the IPC-50 setting with BACON. The performance of the synthetic dataset under different  $\lambda$  values is illustrated in Figure [4.](#page-7-1) We observed a steady increase in test accuracy with increasing values of  $\lambda$  until it peaked at  $\lambda = 0.8$ , where the accuracy exceeded 70%. Beyond this point, further increments in  $\lambda$  did not achieve significant improvements in accuracy. Therefore, we infer that the loss function exhibits its highest efficacy when  $\lambda$  is set to 0.8. The visualization of various hyperparameters is depicted in Figure [5.](#page-7-2)

### 4.4 Visualization

The outcomes of BACON are visualized in Figure [6.](#page-8-0) Specifically, (a-1), (a-2), and (a-3) represent synthetic samples with IPC-1, IPC-10, and IPC-50, respectively, on CIFAR-10, while (b-1), (b-2), and (b-3) represent samples on SVHN. Additionally, Figure [6](#page-8-0) (a-4) and Figure [6](#page-8-0) (b-4) display class cluster visualizations for these two datasets using t-SNE. As IPC numbers increase, so does the compression rate, leading to richer visual information in the images. This is because synthetic examples are not limited to real examples. IPC-1 generates more representative images by aggregating semantic information from all original training sets, while IPC-50 images closely resemble the originals, with IPC-10 falling in between. SVHN demonstrates better clustering than CIFAR-10. Further experiment visualization details, including comparisons with previous methods, can be found in Appendix [C.6.](#page-19-0)

# 5 Conclusion

In this paper, we addressed two key challenges in Dataset Distillation (DD): 1) how to effectively formulate the DD problem, and 2) determining the theoretical lower bound of optimal condensation. To tackle these issues, we introduced BACON, a novel and efficient approach leveraging Bayesian principles. BACON provides the first formal analysis of the DD problem, yielding both an optimal

solution and its approximate form. Extensive experiments across multiple datasets demonstrate that BACON consistently outperforms existing state-of-the-art methods. Compared to the IDM method, BACON achieves its highest accuracy gains as follows: 1.70% on low-resolution datasets, 3.58% on medium-resolution datasets, and 3.10% on high-resolution datasets. Ablation studies further confirm the effectiveness of BACON. These results underscore the practical applicability and solid theoretical foundation of our approach, paving the way for future research. Our study opens new avenues for extending BACON to more complex tasks and refining its theoretical framework.

# References

- <span id="page-10-11"></span>[1] Mislav Balunovic, Dimitar Iliev Dimitrov, Robin Staab, and Martin Vechev. Bayesian framework ´ for gradient leakage. In *International Conference on Learning Representations (ICLR)*, 2022.
- <span id="page-10-7"></span>[2] Ondrej Bohdal, Yongxin Yang, and Timothy M Hospedales. Flexible dataset distillation: Learn labels instead of images. In *Advances in Neural Information Processing Systems Workshop (NeurIPSW)*, 2020.
- <span id="page-10-14"></span>[3] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *European Conference on Computer Vision (ECCV)*, pages 233–248, 2018.
- <span id="page-10-1"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 4750–4759, 2022.
- <span id="page-10-6"></span>[5] Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *International Conference on Learning Representations (ICLR)*, 2023.
- <span id="page-10-13"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 35, pages 810–822, 2022.
- <span id="page-10-2"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning (ICML)*, pages 6565–6590, 2023.
- <span id="page-10-10"></span>[8] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A largescale hierarchical image database. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 248–255, 2009.
- <span id="page-10-5"></span>[9] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 35, pages 34391–34404, 2022.
- <span id="page-10-3"></span>[10] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3749–3758, 2023.
- <span id="page-10-9"></span>[11] Micah Goldblum, Liam Fowl, Soheil Feizi, and Tom Goldstein. Adversarially robust distillation. In *Association for the Advancement of Artificial Intelligence (AAAI)*, volume 34, pages 3996– 4003, 2020.
- <span id="page-10-12"></span>[12] Neil J Gordon, David J Salmond, and Adrian FM Smith. Novel approach to nonlinear/nongaussian bayesian state estimation. In *IEE proceedings F (Radar and Signal Processing)*, volume 140, pages 107–113. IET, 1993.
- <span id="page-10-0"></span>[13] Timothy Hospedales, Antreas Antoniou, Paul Micaelli, and Amos Storkey. Meta-learning in neural networks: A survey. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 44(9):5149–5169, 2021.
- <span id="page-10-4"></span>[14] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning (ICML)*, pages 11102–11118, 2022.
- <span id="page-10-8"></span>[15] James Kirkpatrick, Razvan Pascanu, Neil Rabinowitz, Joel Veness, Guillaume Desjardins, Andrei A Rusu, Kieran Milan, John Quan, Tiago Ramalho, Agnieszka Grabska-Barwinska, et al. Overcoming catastrophic forgetting in neural networks. *Proceedings of the National Academy of Sciences*, 114(13):3521–3526, 2017.

- <span id="page-11-9"></span>[16] Jakub Konečný, H Brendan McMahan, Daniel Ramage, and Peter Richtárik. Federated optimization: Distributed machine learning for on-device intelligence. *arXiv preprint arXiv:1610.02527*, 2016.
- <span id="page-11-14"></span>[17] A Krizhevsky. Learning multiple layers of features from tiny images. *Master's thesis, University of Tront*, 2009.
- <span id="page-11-17"></span>[18] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 25, pages 1106–1114, 2012.
- <span id="page-11-12"></span>[19] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-11-6"></span>[20] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning (ICML)*, pages 12352–12364, 2022.
- <span id="page-11-4"></span>[21] Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 46(1):17–32, 2024.
- <span id="page-11-11"></span>[22] Chenxin Li, Mingbao Lin, Zhiyuan Ding, Nie Lin, Yihong Zhuang, Yue Huang, Xinghao Ding, and Liujuan Cao. Knowledge condensation distillation. In *European Conference on Computer Vision (ECCV)*, pages 19–35, 2022.
- <span id="page-11-3"></span>[23] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 35, pages 1100–1113, 2022.
- <span id="page-11-5"></span>[24] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Kaipeng Zhang, Wei Jiang, and Yang You. Dream+: Efficient dataset distillation by bidirectional representative matching. *arXiv preprint arXiv:2310.15052*, 2023.
- <span id="page-11-8"></span>[25] Yaoyao Liu, Bernt Schiele, and Qianru Sun. Adaptive aggregation networks for classincremental learning. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 2544–2553, 2021.
- <span id="page-11-2"></span>[26] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 35, pages 13877–13891, 2022.
- <span id="page-11-10"></span>[27] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication-efficient learning of deep networks from decentralized data. In *International Conference on Artificial Intelligence and Statistics (AISTATS)*, pages 1273–1282, 2017.
- <span id="page-11-13"></span>[28] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Baolin Wu, Andrew Y Ng, et al. Reading digits in natural images with unsupervised feature learning. In *Advances in Neural Information Processing Systems Workshop (NeurIPSW)*, volume 2011, page 7, 2011.
- <span id="page-11-0"></span>[29] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-11-1"></span>[30] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 34, pages 5186–5198, 2021.
- <span id="page-11-7"></span>[31] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 2001–2010, 2017.
- <span id="page-11-16"></span>[32] Levent Sagun, Utku Evci, V Ugur Guney, Yann Dauphin, and Leon Bottou. Empirical analysis of the hessian of over-parametrized neural networks. *arXiv preprint arXiv:1706.04454*, 2017.
- <span id="page-11-15"></span>[33] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.

- <span id="page-12-17"></span>[34] Yuzhang Shang, Zhihang Yuan, and Yan Yan. Mim4dd: Mutual information maximization for dataset distillation. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 36, 2024.
- <span id="page-12-16"></span>[35] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-12-5"></span>[36] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 12196– 12205, 2022.
- <span id="page-12-0"></span>[37] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-15"></span>[38] Max Welling. Herding dynamical weights to learn. In *International Conference on Machine Learning (ICML)*, pages 1121–1128, 2009.
- <span id="page-12-11"></span>[39] Yifan Wu, Jiawei Du, Ping Liu, Yuewei Lin, Wenqing Cheng, and Wei Xu. Dd-robustbench: An adversarial robustness benchmark for dataset distillation. *arXiv preprint arXiv:2403.13322*, 2024.
- <span id="page-12-13"></span>[40] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-12-8"></span>[41] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 16323–16332, 2023.
- <span id="page-12-12"></span>[42] Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024.
- <span id="page-12-14"></span>[43] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 46(1):150–170, 2024.
- <span id="page-12-7"></span>[44] Jie Zhang, Chen Chen, Bo Li, Lingjuan Lyu, Shuang Wu, Shouhong Ding, Chunhua Shen, and Chao Wu. Dense: Data-free one-shot federated learning. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 35, pages 21414–21428, 2022.
- <span id="page-12-6"></span>[45] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning (ICML)*, pages 12674–12685, 2021.
- <span id="page-12-3"></span>[46] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pages 6514–6523, 2023.
- <span id="page-12-1"></span>[47] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-12-4"></span>[48] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 7856–7865, 2023.
- <span id="page-12-2"></span>[49] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. 35:9813–9827, 2022.
- <span id="page-12-9"></span>[50] Zheng Zhou, Ju Liu, and Yanyang Han. Adversarial examples are closely relevant to neural network models - a preliminary experiment explore. In Ying Tan, Yuhui Shi, and Ben Niu, editors, *Advances in Swarm Intelligence*, pages 155–166, Cham, 2022. Springer International Publishing. ISBN 978-3-031-09726-3.
- <span id="page-12-10"></span>[51] Zheng Zhou, Hongbo Zhao, Ju Liu, Qiaosheng Zhang, Liwei Geng, Shuchang Lyu, and Wenquan Feng. Mvpatch: More vivid patch for adversarial camouflaged attacks on object detectors in the physical world. *arXiv preprint arXiv:2312.17431*, 2024.

# Supplementary Material

### BACON: Bayesian Optimal Condensation Framework for Dataset Distillation

- Appendix [A](#page-13-0) contains proofs for all theorems and assumptions presented in this paper.
- Appendix [B](#page-15-0) provides additional background information and preliminary details on dataset distillation.
- Appendix [C](#page-16-0) provides implementation details of experiments and visualizations.
- Appendix [D](#page-19-1) outlines the potential social impact of our work;
- Appendix [E](#page-21-0) explores the limitations of our work and outlines potential future directions.

<span id="page-13-0"></span>

# A Proofs

<span id="page-13-1"></span>

### A.1 Proof of Theorem [3.4](#page-3-2)

<span id="page-13-2"></span>**Definition A.1** (Similarity Indicator of  $\epsilon$ -neighborhood). Let  $z_x$  and  $z_{\tilde{x}}$  be two distributions. A binary loss function  $\mathcal L$  is used to assess their similarity. When the Euclidean distance between the two distributions is small,  $\mathcal L$  is equal to 0; otherwise,  $\mathcal L$  is equal to 1. As  $\epsilon$  approaches zero, the synthetic distribution  $z_{\bar{x}}$  approximates the original distribution  $z_x$ . We define the similarity indicator function as follows:

$$
\mathcal{L}(z_x, z_{\tilde{x}}) := \mathbb{1}\{\|z_x - z_{\tilde{x}}\|_2 \ge \epsilon\}.
$$
\n(12)

<span id="page-13-3"></span>**Definition A.2** (Expected Risk Function). Consider a joint probability distribution  $p(z_x, z_{\bar{x}})$  formed by the probability distribution of the outputs of neural networks,  $\mathcal{D}_T$  and  $\mathcal{D}_S$ , for the original dataset T and the synthetic dataset S. Given  $z_x \in \mathcal{D}_T$  and  $z_{\tilde{x}} \in \mathcal{D}_S$ , the expected risk function in the joint probability distribution  $R(\phi)$  is defined as follows:

$$
R(\phi) := \mathbb{E}_{(z_x, z_{\tilde{x}}) \sim p(z_x, z_{\tilde{x}})}[\mathcal{L}(z_x, z_{\tilde{x}})],\tag{13}
$$

where  $z_x = \phi(\theta, x)$  represents the output of the neural network, and  $\phi(\theta, x) : X \subseteq \mathbb{R}^n \to \mathcal{D} \subseteq \mathbb{R}^N$ is parameterized by  $\theta$  with  $n \ll N$ . We map  $x \in \mathbb{R}^n$  to a higher dimensional space  $\mathbb{R}^N$  using z.

<span id="page-13-4"></span>**Definition A.3** (Sphere Integral Function). The spherical integral, denoted by  $\beta$ , represents the integration over a sphere with a radius of  $\epsilon$  and a center point of  $z_{\tilde{x}}$ .

$$
\mathcal{B}(z_{\tilde{x}}, \epsilon) = \{z_{\tilde{x}}; \|z_x - z_{\tilde{x}}\|_2 \le \epsilon\}.
$$
\n(14)

Theorem A.4. *The expected risk function in a joint probability distribution can also be calculated as follows:*

$$
R(\phi) = 1 - \mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x.
$$
 (15)

*Proof.* Under the Definition [3.1](#page-3-3)[,3.2](#page-3-4) and [3.3](#page-3-5) (Definition [A.1,](#page-13-2) [A.2](#page-13-3) and [A.3](#page-13-4) in Appendix [A.1\)](#page-13-1), we derive the risk function in a joint probability distribution as follows:

$$
R(\phi) = \mathbb{E}_{(z_x, z_{\tilde{x}}) \sim p(z_x, z_{\tilde{x}})}[\mathcal{L}(z_x, z_{\tilde{x}})]
$$
\n(16)

$$
=\mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \mathbb{E}_{z_x \sim p(z_x|z_{\tilde{x}})}[\mathbb{1}\{|z_x - z_{\tilde{x}}\|_2 \ge \epsilon\}]
$$
\n(17)

$$
= \int_{\mathcal{D}_S} p(z_{\tilde{x}}) \int_{\mathcal{D}_T} p(z_x | z_{\tilde{x}}) \cdot \mathbb{1}\{ ||z_x - z_{\tilde{x}}||_2 \ge \epsilon \} dz_x dz_{\tilde{x}} \tag{18}
$$

$$
= \int_{\mathcal{D}_S} p(z_{\tilde{x}}) \int_{\mathcal{D}_T \backslash \mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x dz_{\tilde{x}} \tag{19}
$$

$$
= \int_{\mathcal{D}_S} p(z_{\tilde{x}}) \left[ 1 - \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x \right] dz_{\tilde{x}} \tag{20}
$$

$$
= \int_{\mathcal{D}_S} p(z_{\tilde{x}}) dz_{\tilde{x}} - \int_{\mathcal{D}_S} p(z_{\tilde{x}}) \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x dz_{\tilde{x}} \tag{21}
$$

$$
=1-\int_{\mathcal{D}_S}p(z_{\tilde{x}})\int_{\mathcal{B}(z_{\tilde{x}},\epsilon)}p(z_x|z_{\tilde{x}})dz_xdz_{\tilde{x}}\tag{22}
$$

$$
= 1 - \mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x.
$$
 (23)

*Remark* A.5*.* The proof demonstrates that we can transform the problem of minimizing the expected risk function  $R(\phi)$  into the problem of maximizing the probabilistic expectation  $\mathbb{E}_{z_{\tilde{x}} \sim p(z_{\tilde{x}})} \int_{\mathcal{B}(z_{\tilde{x}}, \epsilon)} p(z_x | z_{\tilde{x}}) dz_x$  over a sphere integral domain  $\mathcal{B}(z_{\tilde{x}}, \epsilon)$ . By finding an optimal value of  $z_{\tilde{x}}$ , denoted as  $z_{\tilde{x}}^*$ , that maximizes this probabilistic expectation, we effectively promote the minimization of  $R(\phi)$ . This optimization problem can be expressed as:

<span id="page-14-0"></span>

### A.2 Proof of Theorem [3.6](#page-4-10)

Bayes Rule: The Bayes' rule can be defined as:

$$
P(A|B) = \frac{P(B|A)P(A)}{P(B)},
$$
\n<sup>(24)</sup>

where A and B are events and  $P(B) \neq 0$ .

Jensen's Inequality: The Jensen's inequality can be written as:

$$
\varphi(\mathbb{E}[X]) \le \mathbb{E}[\varphi(X)],\tag{25}
$$

where  $\varphi$  is a linear function defined on a convex set. If  $\varphi$  is a linear function defined on a concave set, Jensen's inequality can be defined as follows:

$$
\varphi(\mathbb{E}[X]) \ge \mathbb{E}[\varphi(X)].\tag{26}
$$

Proof of Convexity of Logarithmic Function: The function is concave if its second derivative is negative. We have that

$$
\frac{\partial^2 g(x)}{\partial^2 x} = \frac{\partial}{\partial x} \left( \frac{\partial g(x)}{\partial x} \right) = \frac{\partial}{\partial x} \left( \frac{\partial \log(x)}{\partial x} \right) = \frac{\partial}{\partial x} \left( \frac{1}{x} \right) = -\frac{1}{x^2}.
$$
 (27)

For  $x > 0$ . Hence,  $g(x)$  is a concave function.

**Theorem A.6.** *The optimal synthetic image*  $z_{\tilde{x}}$  *can be computed as follows:* 

$$
z_{\tilde{x}} = \underset{z_x^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_x^*, \epsilon)} \left[ \log p(z_x^* | z_x) + \log p(z_x) \right] dz_x. \tag{28}
$$

*Proof.* By leveraging the Bayes rule and Jensen's inequality, we derive the function as follows:

$$
z_{\tilde{x}} = \underset{z_{\tilde{x}}^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_{\tilde{x}}^*, \epsilon)} p(z_x | z_{\tilde{x}}^*) dz_x \tag{29}
$$

<span id="page-15-1"></span>
$$
= \underset{z_x^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_x^*, \epsilon)} \frac{p(z_x^* | z_x) p(z_x)}{p(z_x^*)} dz_x \tag{30}
$$

$$
= \underset{z_x^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_x^*, \epsilon)} \underbrace{p(z_x^* | z_x) p(z_x)}_{\text{Bayesian formula}} dz_x \tag{31}
$$

$$
= \underset{z_{\tilde{x}}^* \in \mathcal{D}_S}{\arg \max} \left[ \log \int_{\mathcal{B}(z_{\tilde{x}}^*, \epsilon)} p(z_{\tilde{x}}^* | z_x) p(z_x) dz_x \right]
$$
(32)

$$
\geq \underset{z_x^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_x^*, \epsilon)} \log \left[ p(z_x^* | z_x) p(z_x) \right] dz_x \tag{33}
$$

Jensen's inequality

<span id="page-15-2"></span>
$$
= \underset{z_x^* \in \mathcal{D}_S}{\arg \max} \int_{\mathcal{B}(z_x^*, \epsilon)} \left[ \log p(z_x^*|z_x) + \log p(z_x) \right] dz_x. \tag{34}
$$

*Remark* A.7*.* By applying Bayes' rule and Jensen's inequality, we derive Eq. [\(29\)](#page-15-1), which provides the formulaic representation for the log-likelihood and prior of the probability distribution as shown in Eq. [\(34\)](#page-15-2). To obtain the realization of the random variable  $D_S = \hat{z}_x^*$ , the objective is to find a series  $z_x$  within the spherical region B that maximizes the integral function (Eq. [\(34\)](#page-15-2)). It is important to note that as  $\epsilon$  approaches zero,  $z_{\tilde{x}}$  approaches arg max $z_x p(z_x|z_{\tilde{x}})$ , which represents the solution for minimizing  $R(\phi)$  by matching probability distributions. Since the solution for  $p(z_x|z_x^*)$  cannot be obtained directly, the Bayesian formula is employed to rewrite it. To further investigate the lower bound of the function, we employ Jensen's inequality as an approximation method.

Discussion The insights provided by Theorems [3.4](#page-3-2) and [3.6](#page-4-10) offer valuable contributions to the fields of synthetic image generation and probability distribution matching for dataset distillation. The approach outlined in Theorem [3.4,](#page-3-2) which emphasizes maximizing probabilistic expectations within a spherical integral domain, presents a promising avenue for minimizing loss by aligning probability distributions. However, it's essential to acknowledge the potential limitations imposed by assumptions regarding distributions and integral domains in practical applications. On the other hand, Theorem [3.6](#page-4-10) leverages foundational mathematical principles such as Bayes' rule and Jensen's inequality to establish a clear framework for determining the optimal synthetic image  $z_{\bar{x}}$ . While this theorem provides valuable guidance, its applicability hinges on the validity of the underlying assumptions inherent in Bayes' rule and Jensen's inequality. In essence, these theorems significantly advance our understanding of image synthesis and probabilistic modeling. However, their real-world utility necessitates further validation and refinement through empirical experiments and practical applications.

<span id="page-15-0"></span>

# B Extended Background

### B.1 Dataset Distillation

Let us denote the real dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ , consisting of  $|\mathcal{T}|$  pairs of training images and corresponding labels, where  $x \in \mathcal{X}$  and  $\mathcal{X} \subset \mathbb{R}^d$ ,  $y \in \mathcal{Y}$  and  $\mathcal{Y} = \{0, \ldots, C - 1\}$ . d is the number of features and C is the number of classes. The synthetic dataset is denoted as  $S = \{(\tilde{x}_i, \tilde{y}_i)\}\Big|_{i=1}^{|S|}$ , where  $\tilde{x} \in \mathbb{R}^d$ ,  $\tilde{y} \in \mathcal{Y}$ , and  $\mathcal{S} \ll \mathcal{T}$ .

Our objective is to map the original dataset  $\mathcal T$  to the dataset  $\mathcal S$ , which is of lower magnitude, while still preserving the informative content. We aim to achieve this by using a differentiable function  $\phi_{\theta}: x \to y$ , where  $\theta$  represents the parameters. The problem of DD can be formulated as follows:

$$
\mathbb{E}_{x \sim P_{\mathcal{D}}} [l(\phi_{\theta}(\tau(x), y)] \simeq \mathbb{E}_{x \sim P_{\mathcal{D}}} [l(\phi_{\theta}(\tilde{x}), \tilde{y})], \tag{35}
$$

where  $x \sim P_D$  represents the original dataset x sampled from the test dataset  $D \subset T$  and l indicates the loss function, specifically the cross-entropy loss. The deep neural network is denoted as  $\phi$ , which is parameterized by  $\theta$ . Meanwhile,  $\phi_{\theta} \tau$  and  $\phi_{\theta} s$  refer to the networks that are trained on  $\tau$  and  $\mathcal{S}$ , respectively.

### B.2 Meta-learning Based Method

Previous studies have mainly focused on treating the DD task as a meta-learning problem [\[37,](#page-12-0) [9\]](#page-10-5). In these studies, the network parameters  $\theta^S$  were represented as a function of the synthetic dataset S. The solution for S was obtained by minimizing the training loss  $\mathcal{L}^{\mathcal{T}}$  on the original dataset  $\mathcal{T}$ . The formulation can be provided as follows:

$$
S^* = \arg\min_{S} \mathcal{L}^{\mathcal{T}}(\theta^S(S))
$$
\n(36)

subject to 
$$
\theta^{\mathcal{S}}(\mathcal{S}) = \arg\min_{\theta} \mathcal{L}^{\mathcal{S}}(\theta).
$$
 (37)

The bi-level optimization problem incurs high computational costs and energy wastage. Therefore, it is crucial to explore approaches to streamline computation.

### B.3 Matching Gradient Based Method

In light of the concerns regarding the memory and time complexity associated with unrolling the computational graph in meta-learning, Zhao *et al.* [\[47\]](#page-12-1) introduce the matching gradient method. This method is based on the cosine similarity distance. The researchers randomly select a pair of synthetic and real batches, denoted as  $S_c$  and  $T_c$  respectively, from the datasets S and T. Here, c refers to the classes. In each iteration, the synthetic data for each class is updated independently. The formulation of this method is defined as follows:

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}, \theta) = \sum_{c=0}^{C-1} d(\nabla l(\mathcal{S}_c; \theta), \nabla l(\mathcal{T}_c; \theta)),
$$
\n(38)

$$
d(\mathbf{A}, \mathbf{B}) = \sum_{i=1}^{L} \sum_{j=1}^{O_i} \left( 1 - \frac{\mathbf{A}_{\mathbf{j}}^{(i)} \cdot \mathbf{B}_{\mathbf{j}}^{(i)}}{\|\mathbf{A}_{j}^{(i)}\| \|\mathbf{B}_{j}^{(i)}\|} \right),
$$
(39)

where  $d(\cdot)$  represents the cosine similarity distance, C signifies the total number of classes, L denotes the number of layers in the neural networks, and  $O_i$  signifies the output channels of the *i*th layer.

Nevertheless, this method still necessitates considerable computational resources due to its expensive bi-level optimization problem.

### B.4 Matching Distribution Based Method

To enhance the efficiency of the optimization process, Zhao *et al.* [\[46\]](#page-12-3) propose a matching distribution method based on the Euclidean distance. They employ the Maximum Mean Discrepancy (MMD) measurement metric to match the distribution of  $\bar{\theta}$  between the model trained on  $\tilde{\mathcal{T}}$  and  $\tilde{\mathcal{S}}$ . The objective function is formulated as follows:

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}; \theta) = \sum_{c=0}^{C-1} \|\phi_{\theta_c^{\mathcal{S}}}(\tilde{x}) - \phi_{\theta_c^{\mathcal{T}}}(x)\|,
$$
\n(40)

where  $\phi_{\theta_c^S}(\tilde{x}) = \frac{1}{\mathcal{S}_c} \sum_{i=1}^{\mathcal{S}_c} f_{\theta}(\tilde{x}_i)$ , and  $\phi_{\theta_c^T}(x) = \frac{1}{\mathcal{T}_c} \sum_{i=1}^{\mathcal{T}_c} f_{\theta}(x_i)$ .  $\mathcal{S}_c$  and  $\mathcal{T}_c$  are the number of samples for the cth class in synthetic and real datasets respectively.

<span id="page-16-0"></span>

# C Experiment

<span id="page-16-1"></span>

### C.1 Experimental Setup

The performance of dataset distillation is mainly evaluated on the classification task. We follow typical settings in the area of dataset distillation, such as those outlined in DC-bench [\[6\]](#page-10-13), DM [\[46\]](#page-12-3), and IDM [\[48\]](#page-12-4).

Dataset We evaluate the effectiveness of our method through experiments conducted on widelyused dataset distillation benchmarks, including the MNIST [\[19\]](#page-11-12), Fashion-MNIST [\[40\]](#page-12-13), SVHN [\[28\]](#page-11-13), CIFAR-10, and CIFAR-100 [\[17\]](#page-11-14), as well as TinyImageNet [\[8\]](#page-10-10). The details of the datasets used in our experiments are as follows:

- **MNIST** [\[19\]](#page-11-12) comprises 60,000 training images and 10,000 testing images of grayscale handwritten digits ranging from 0 to 9. It consists of 10 classes, and each image is  $28 \times 28$ pixels in size.
- Fashion-MNIST [\[40\]](#page-12-13) consists of 10 classes of grayscale fashion items. The training set contains 60,000 images, and the test set contains 10,000 images. Each image is also in a 28  $\times$  28 pixel format.
- SVHN [\[28\]](#page-11-13) contains 73,257 training images and 26,032 test images of house numbers captured from Google Street View. It includes digit sequences ranging from 0 to 9, with each image being  $32 \times 32$  pixels in size.
- CIFAR-10 [\[17\]](#page-11-14) contains 60,000 32  $\times$  32 color images distributed across 10 different classes, with 6,000 images per class.
- CIFAR-100 [\[17\]](#page-11-14) comprises 60,000 color images, each with a resolution of  $32 \times 32$  pixels, distributed across 100 classes. Each class contains 600 images.
- TinyImageNet [\[8\]](#page-10-10) is a subset of the ImageNet dataset, featuring 200 classes. Each class in TinyImageNet consists of 500 training images, 50 validation images, and 50 test images, all with a resolution of  $64 \times 64$  pixels.

### C.2 Experimental Settings

Networks Architectures We employed the ConvNet architecture [\[32\]](#page-11-16) to conduct dataset distillation in our experiment, adopting the approach employed in prior research [\[46,](#page-12-3) [14\]](#page-10-4). The ConvNet comprises three identical convolutional blocks and a linear classifier. Each block is composed of a convolutional layer with 128 kernels of size  $3 \times 3$ , instance normalization, ReLU activation, and average pooling with a stride of 2 and a size of  $3 \times 3$ . The architecture settings are consistent with those described in DC-bench [\[6\]](#page-10-13).

Evaluation Protocol The evaluation protocol follows the DC-bench protocol. Synthetic images are generated using 1, 10, and 50 images per class (IPC) from six benchmark datasets: MNIST, F-MNIST, SVHN, CIFAR-10/100, and TinyImageNet. To assess the effectiveness of our approach, we train a model using the generated synthetic images and measure its performance on the original test images, following the model sampling strategy [\[48\]](#page-12-4). Additionally, all methods employ the default data augmentation strategies provided by the authors for evaluating distillation performance. For fair comparisons in generalization evaluation, we incorporate DSA [\[45\]](#page-12-6) data augmentation during the evaluation model training process. We report the mean accuracy of 5 runs, where the models are randomly initialized and trained for 1000 epochs, using the condensed set as the evaluation metric.

### C.3 Implementation Details

We utilize the implementation of DM [\[46\]](#page-12-3) and IDM [\[48\]](#page-12-4) as a guide for setting most of the hyperparameters in our approach. To generate synthetic images, we employed the stochastic gradient descent (SGD) optimizer with a learning rate of 0.2 and a momentum of 0.5 to train synthetic datasets containing 1, 10, and 50 IPCs. For training the model, we adopted the same SGD optimizer setting with a learning rate of 0.01, momentum of 0.9, and weight decay of 0.0005. The hyperparameter  $\lambda$ in  $\mathcal{L}_{\text{TOTAL}} = \mathcal{L}_{\text{LH}} + \lambda \mathcal{L}_{\text{TV}} + (1 - \lambda) \mathcal{L}_{\text{CLIP}}$  is set to 0.8, and the batch size is set to 256. Following the approach outlined in [\[45\]](#page-12-6), we employ a differentiable augmentation strategy for learning and evaluating the synthetic set. Since our approach is plug-in, we adhere to all the experimental settings (except for loss functions) of the comparison experiments and only incorporate our own modules into theirs. We conduct all experiments on clusters of NVIDIA RTX 4090 GPUs for generating synthetic datasets and one NVIDIA Tesla V100 GPU for visualizations.

<span id="page-18-0"></span>

#### C.4 Comparison with Previous Methods

We compare our proposed BACON with 10 previous methods, as listed in Table [1](#page-6-0) of the main content. These methods include two core-set selection methods and eight dataset distillation methods. In our comparison, we considered Random [\[3\]](#page-10-14) and Herding [\[31\]](#page-11-7) as coreset selection methods. Random involves randomly sampling initial synthetic images from the original dataset, while Herding selects initial synthetic images from the original dataset that are closest to the clustering center for each class. For dataset condensation methods, we included two relatively early works, DD [\[37\]](#page-12-0) and LD [\[2\]](#page-10-7), as well as five advanced methods proposed later: DC [\[47\]](#page-12-1), DSA [\[45\]](#page-12-6), DCC [\[20\]](#page-11-6), CAFE [\[36\]](#page-12-5), and DM [\[46\]](#page-12-3). DM [\[46\]](#page-12-3) was further improved to IDM [\[48\]](#page-12-4), which achieves significant performance by matching the distribution in the DD task. We choose IDM as the baseline method to evaluate the effectiveness of the proposed BACON framework in our experiments. Below are the details of these previous methods, along with the notations introduced in Appendix [B:](#page-15-0)

##### Coreset Selection

- Random [\[3\]](#page-10-14) involves randomly sampling initial images from the original dataset  $\mathcal T$  as condensed dataset S.
- Herding [\[31\]](#page-11-7) selects initial synthetic dataset  $S$  from the original dataset  $T$  that are closest to the clustering center for each class.

# Dataset Distillation

- DD [\[37\]](#page-12-0) first introduces the concept of Dataset Distillation (DD) and formulates the problem as a bi-level optimization.
- LD [\[2\]](#page-10-7) introduces a more robust and flexible meta-learning algorithm for DD, along with an effective first-order strategy utilizing convex optimization layers.
- DC [\[47\]](#page-12-1) formulates the bi-level optimization by addressing the gradient matching problem between the gradients of deep neural network weights trained on both the original  $\tau$  and synthetic data S.
- **DSA** [\[45\]](#page-12-6) allows for the effective utilization of data augmentation to generate more informative synthetic images, thereby enhancing the performance of networks trained with augmentations.
- DCC [\[20\]](#page-11-6) alters the loss function, enabling better comprehension of the distinctions among classes. Additionally, it introduces a pioneering bi-level warm-up strategy to enhance the stability of the optimization process.
- CAFE [\[36\]](#page-12-5) presents a robust approach for aligning features extracted from real  $\mathcal T$  and synthetic datasets  $S$  at multiple scales, taking into account the classification of real samples  $\mathcal{T}$ .
- DM [\[46\]](#page-12-3) presents a straightforward and impactful approach for generating condensed images. This is achieved by aligning the feature distributions of synthetic S and original  $\mathcal T$ training images across multiple sampled embedding spaces.
- IDM [\[48\]](#page-12-4) presents a novel dataset condensation method that is based on distribution matching, making it both efficient and promising.

<span id="page-18-1"></span>

### C.5 More Details of Ablation Studies

We assess the effectiveness of the proposed BACON method, integrating diverse loss components, across multiple datasets. To thoroughly analyze the influence of these loss components on the distillation performance, we conduct a series of experiments using different configurations of the IPC settings. The outcomes of our experiments are presented in Table [3.](#page-19-2)

Analysis In the context of the CIFAR-10 dataset with IPC-50, BACON demonstrates superior performance by employing three distinct loss components, as detailed in the main body. However, as IPC numbers diminish, BACON utilizing total variance (TV) loss and CLIP loss emerges as the top performer at an IPC setting of 10. Conversely, under an IPC setting of 1, BACON with solely

<span id="page-19-2"></span>Table 3: Ablation study of diverse loss functions: Evaluation of the performance of the proposed loss functions, specifically  $\mathcal{L}_{L,H}$ ,  $\mathcal{L}_{TV}$ , and  $\mathcal{L}_{CLIP}$ , is conducted separately as loss function components. Additionally, all experimental hyperparameters, denoted by  $\lambda$ , are set to 0.8 unless otherwise specified. In this experiment, we employ CIFAR-10 and CIFAR-100 as experimental datasets with setting of IPC-1, IPC-10 and IPC-50.

| \$\	ext{L}_{LH}\$ | \$\	ext{L}_{TV}\$ | \$\	ext{L}_{CLIP}\$ | CIFAR-10 |        |       | CIFAR-100 |        |       |
|-------------------|-------------------|---------------------|----------|--------|-------|-----------|--------|-------|
|                   |                   |                     | IPC-50   | IPC-10 | IPC-1 | IPC-50    | IPC-10 | IPC-1 |
| ✓                 | X                 | X                   | 64.86    | 55.36  | 45.32 | 41.56     | 42.68  | 25.18 |
| X                 | ✓                 | X                   | 69.96    | 61.9   | 45.82 | 49.37     | 46.24  | 23.56 |
| X                 | X                 | ✓                   | 55.07    | 42.51  | 34.22 | 30.69     | 27.49  | 15.22 |
| ✓                 | ✓                 | X                   | 69.81    | 61.93  | 45.64 | 49.56     | 46.15  | 23.69 |
| ✓                 | X                 | ✓                   | 64.78    | 55.45  | 45.39 | 41.4      | 42.52  | 24.58 |
| X                 | ✓                 | ✓                   | 69.76    | 62.27  | 45.69 | 49.34     | 46.42  | 23.96 |
| ✓                 | ✓                 | ✓                   | 70.06    | 62.06  | 45.62 | 49.44     | 46.15  | 23.68 |

<span id="page-19-3"></span>Image /page/19/Figure/2 description: The image displays two sets of handwritten digits, labeled (a) IDM and (b) BACON. Each set is further divided into two columns, labeled IPC-50 and IPC-1. Both sets show rows of digits from 0 to 9. The IPC-50 columns contain multiple examples of each digit, while the IPC-1 columns contain fewer examples, with the digits appearing to be more stylized or varied. The digits are presented against a black background.

Figure 7: Visualization of BACON and IDM on the MNIST dataset: (a) is IDM condensed to IPC-50 and IPC-1. (b) is BACON condensed to IPC-50 and IPC-1.

TV loss excels. Turning to the CIFAR-100 dataset, optimal performance is achieved by BACON employing likelihood (LH) loss and TV loss with the IPC-50 setting. When IPC numbers decrease, BACON with TV loss and CLIP loss achieves the highest performance. With the IPC-1 setting on the CIFAR-100 dataset, BACON with only LH loss outperforms others.

<span id="page-19-0"></span>

### C.6 Visualization

To demonstrate the effects of the distilled images in a more intuitive manner, we conducted a comparative analysis of the distillation results obtained using the proposed BACON method and IDM [\[48\]](#page-12-4) on extensive datasets, namely MNIST, Fashion-MNIST, and SVHN. The visual comparisons are depicted in Figure [7,](#page-19-3) [8,](#page-20-0) and [9.](#page-20-1) Moreover, we present additional visualizations of the distilled images obtained from CIFAR-100 and TinyImageNet in Figure [10.](#page-21-1)

<span id="page-19-1"></span>

# D Broader Impacts

The introduction of BACON, a new framework for Dataset Distillation (DD), brings promising advantages by reducing storage costs and training expenses while maintaining performance on

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image displays two grids of fashion items, labeled (a) IDM and (b) BACON. Each grid is further divided into two sections, labeled IPC-50 and IPC-1. The IPC-50 sections contain 50 images of various clothing items and shoes arranged in rows and columns. The IPC-1 sections contain fewer images, also of clothing and shoes. The items depicted include t-shirts, trousers, sweaters, jackets, dresses, sneakers, and boots. The overall presentation suggests a comparison of generated fashion items under different conditions or models.

Figure 8: Visualization of BACON and IDM on the Fashion-MNIST dataset: (a) is IDM condensed to IPC-50 and IPC-1. (b) is BACON condensed to IPC-50 and IPC-1.

<span id="page-20-1"></span>Image /page/20/Picture/2 description: The image displays two grids of images, labeled (a) IDM and (b) BACON. Each grid contains multiple smaller images, primarily featuring numbers. The top of each grid is labeled "IPC-50" and "IPC-1", suggesting a comparison between two different methods or settings. The IDM grid shows a collection of number images, with some appearing clearer than others. The BACON grid also shows number images, with a similar arrangement. The overall presentation suggests a visual comparison of results from two different approaches, likely in the field of image recognition or processing, focusing on the accurate representation of numbers.

Figure 9: Visualization of BACON and IDM on the SVHN dataset: (a) is IDM condensed to IPC-50 and IPC-1. (b) is BACON condensed to IPC-50 and IPC-1.

<span id="page-21-1"></span>Image /page/21/Picture/0 description: The image displays two grids of images, labeled (a) CIFAR-100 and (b) TinyImageNet. Grid (a) shows images generated with IPC-50 and IPC-1. The IPC-50 section contains 10 rows and 5 columns of images, showcasing various objects like fruits, fish, people, animals, furniture, insects, and bicycles. The IPC-1 section next to it shows a column of smaller, more abstract images. Grid (b) shows images generated with IPC-10 and IPC-1. The IPC-10 section has 10 rows and 5 columns of images, predominantly featuring nature scenes with animals and plants, many appearing distorted or abstract. The IPC-1 section next to it displays a column of smaller, abstract images similar to those in grid (a).

Figure 10: Visualization of BACON on the CIFAR-100 and TinyImageNet datasets: (a) is BACON condensed to IPC-50 and IPC-1 on the CIFAR-100 dataset. (b) is BACON condensed to IPC-10 and IPC-1 on the TinyImageNet.

test sets. This breakthrough could make large datasets and models more accessible, opening up opportunities for innovation in fields like healthcare, education, and climate science. However, it is crucial to address potential biases and ethical concerns in the distillation process to ensure fairness and accountability. Furthermore, BACON's theoretical groundwork not only deepens our understanding but also sets the stage for further progress in machine learning research, emphasizing the need for responsible development and deployment of distillation methods.

<span id="page-21-0"></span>

# E Limitations and Future Works

Although the proposed BACON method effectively enhances model performance on synthetic datasets, its efficacy declines as the IPC setting of synthetic images decreases. Additionally, as image resolution increases, computational costs escalate accordingly. Therefore, our future work will focus on addressing these limitations. Specifically, we aim to enhance BACON's performance in high-resolution image scenarios while simultaneously improving computational efficiency to reduce energy overhead.