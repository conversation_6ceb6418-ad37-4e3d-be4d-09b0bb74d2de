{"table_of_contents": [{"title": "BACON: Bayesian Optimal Condensation Framework\nfor Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[105.75, 98.25], [504.75, 98.25], [504.75, 135.931640625], [105.75, 135.931640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 296.25], [328.5, 296.25], [328.5, 308.21484375], [282.75, 308.21484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 556.5], [191.25, 556.5], [191.25, 567.703125], [107.25, 567.703125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[106.5, 111.75], [198.75, 111.75], [198.75, 124.13671875], [106.5, 124.13671875]]}, {"title": "3 Bayesian Optimal Condensation Framework", "heading_level": null, "page_id": 2, "polygon": [[106.5, 565.5], [355.0078125, 565.5], [355.0078125, 577.37109375], [106.5, 577.37109375]]}, {"title": "3.1 Motivation", "heading_level": null, "page_id": 2, "polygon": [[106.5, 680.625], [178.5, 680.625], [178.5, 691.453125], [106.5, 691.453125]]}, {"title": "3.2 Expected Risk Function in Joint Probability Distribution", "heading_level": null, "page_id": 3, "polygon": [[106.5, 458.25], [372.75, 458.25], [372.75, 468.31640625], [106.5, 468.31640625]]}, {"title": "3.3 Bayesian Optimal Condensation Risk Function", "heading_level": null, "page_id": 4, "polygon": [[106.5, 190.5], [330.75, 190.5], [330.75, 199.546875], [106.5, 199.546875]]}, {"title": "3.4 Approximating the Optimal Solution for Bayesian Condensation", "heading_level": null, "page_id": 4, "polygon": [[106.30810546875, 365.25], [404.25, 366.75], [404.25, 376.5], [106.30810546875, 375.890625]]}, {"title": "Algorithm 1: BAyesian optimal CONdensation framework (BACON)", "heading_level": null, "page_id": 5, "polygon": [[107.25, 76.5], [391.166015625, 76.5], [391.166015625, 86.38330078125], [107.25, 86.38330078125]]}, {"title": "3.5 Overall Loss Function and Pseudocode", "heading_level": null, "page_id": 5, "polygon": [[106.5, 264.75], [296.736328125, 264.75], [296.736328125, 275.150390625], [106.5, 275.150390625]]}, {"title": "4 Experimental Evaluation", "heading_level": null, "page_id": 5, "polygon": [[107.25, 501.75], [255.0, 501.75], [255.0, 512.7890625], [107.25, 512.7890625]]}, {"title": "4.1 Experiment Setup", "heading_level": null, "page_id": 5, "polygon": [[106.5, 603.0], [209.1796875, 603.0], [209.1796875, 612.5625], [106.5, 612.5625]]}, {"title": "4.2 Comparison to the State-of-the-art Methods", "heading_level": null, "page_id": 6, "polygon": [[106.75634765625, 570.0234375], [318.75, 570.0234375], [318.75, 580.078125], [106.75634765625, 580.078125]]}, {"title": "4.3 Ablation Studies", "heading_level": null, "page_id": 7, "polygon": [[106.5, 613.5], [201.75, 613.5], [201.75, 623.77734375], [106.5, 623.77734375]]}, {"title": "4.4 Visualization", "heading_level": null, "page_id": 8, "polygon": [[107.25, 520.5], [186.75, 520.5], [186.75, 530.578125], [107.25, 530.578125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 654.75], [183.75, 654.75], [183.75, 666.31640625], [106.5, 666.31640625]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 72.0], [164.654296875, 72.0], [164.654296875, 83.6279296875], [107.25, 83.6279296875]]}, {"title": "Supplementary Material", "heading_level": null, "page_id": 13, "polygon": [[228.005859375, 70.5], [382.798828125, 70.5], [382.798828125, 84.06298828125], [228.005859375, 84.06298828125]]}, {"title": "BACON: Bayesian Optimal Condensation Framework\nfor Dataset Distillation", "heading_level": null, "page_id": 13, "polygon": [[187.5146484375, 91.5], [422.54296875, 91.5], [422.54296875, 112.341796875], [187.5146484375, 112.341796875]]}, {"title": "A Proofs", "heading_level": null, "page_id": 13, "polygon": [[107.25, 306.0], [162.75, 306.0], [162.75, 317.302734375], [107.25, 317.302734375]]}, {"title": "A.1 Proof of Theorem 3.4", "heading_level": null, "page_id": 13, "polygon": [[106.5, 337.5], [224.419921875, 337.5], [224.419921875, 347.66015625], [106.5, 347.66015625]]}, {"title": "A.2 Proof of Theorem 3.6", "heading_level": null, "page_id": 14, "polygon": [[106.5, 396.0], [224.25, 396.0], [224.25, 406.44140625], [106.5, 406.44140625]]}, {"title": "B Extended Background", "heading_level": null, "page_id": 15, "polygon": [[106.5, 549.75], [243.0, 549.75], [243.0, 560.7421875], [106.5, 560.7421875]]}, {"title": "B.1 Dataset Distillation", "heading_level": null, "page_id": 15, "polygon": [[106.98046875, 573.0], [214.5, 573.0], [214.5, 583.171875], [106.98046875, 583.171875]]}, {"title": "B.2 Meta-learning Based Method", "heading_level": null, "page_id": 16, "polygon": [[106.5, 109.5], [257.25, 109.5], [257.25, 119.25], [106.5, 119.25]]}, {"title": "B.3 Matching Gradient Based Method", "heading_level": null, "page_id": 16, "polygon": [[106.8310546875, 261.75], [279.0, 261.75], [279.0, 271.669921875], [106.8310546875, 271.669921875]]}, {"title": "B.4 Matching Distribution Based Method", "heading_level": null, "page_id": 16, "polygon": [[106.5, 492.0], [291.75, 492.0], [291.75, 501.57421875], [106.5, 501.57421875]]}, {"title": "C Experiment", "heading_level": null, "page_id": 16, "polygon": [[107.25, 645.75], [189.75, 645.75], [189.75, 656.6484375], [107.25, 656.6484375]]}, {"title": "C.1 Experimental Setup", "heading_level": null, "page_id": 16, "polygon": [[106.5, 669.75], [218.443359375, 669.75], [218.443359375, 679.8515625], [106.5, 679.8515625]]}, {"title": "C.2 Experimental Settings", "heading_level": null, "page_id": 17, "polygon": [[107.25, 360.228515625], [228.3046875, 360.228515625], [228.3046875, 371.056640625], [107.25, 371.056640625]]}, {"title": "C.3 Implementation Details", "heading_level": null, "page_id": 17, "polygon": [[106.5, 581.23828125], [234.0, 581.23828125], [234.0, 591.29296875], [106.5, 591.29296875]]}, {"title": "C.4 Comparison with Previous Methods", "heading_level": null, "page_id": 18, "polygon": [[106.5, 73.04150390625], [287.173828125, 73.04150390625], [287.173828125, 83.67626953125], [106.5, 83.67626953125]]}, {"title": "Coreset Selection", "heading_level": null, "page_id": 18, "polygon": [[106.98046875, 226.037109375], [183.181640625, 226.037109375], [183.181640625, 236.091796875], [106.98046875, 236.091796875]]}, {"title": "Dataset Distillation", "heading_level": null, "page_id": 18, "polygon": [[107.25, 307.5], [192.5947265625, 307.5], [192.5947265625, 318.65625], [107.25, 318.65625]]}, {"title": "C.5 More Details of Ablation Studies", "heading_level": null, "page_id": 18, "polygon": [[106.98046875, 601.734375], [272.25, 601.734375], [272.25, 612.5625], [106.98046875, 612.5625]]}, {"title": "C.6 Visualization", "heading_level": null, "page_id": 19, "polygon": [[106.5, 585.4921875], [189.0, 585.4921875], [189.0, 596.3203125], [106.5, 596.3203125]]}, {"title": "<PERSON> Broader Impacts", "heading_level": null, "page_id": 19, "polygon": [[106.5, 676.5], [216.75, 676.5], [216.75, 688.74609375], [106.5, 688.74609375]]}, {"title": "E Limitations and Future Works", "heading_level": null, "page_id": 21, "polygon": [[106.5, 407.98828125], [286.27734375, 407.98828125], [286.27734375, 421.13671875], [106.5, 421.13671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 46], ["Text", 4], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8040, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 64], ["ListItem", 5], ["Text", 4], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 707, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 50], ["Text", 7], ["SectionHeader", 3], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 64], ["Reference", 6], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 766], ["Line", 105], ["Reference", 11], ["Equation", 6], ["TextInlineMath", 5], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["Line", 47], ["ListItem", 9], ["Text", 5], ["SectionHeader", 4], ["Reference", 3], ["TextInlineMath", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 277], ["Span", 270], ["Line", 157], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 871, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["TableCell", 64], ["Line", 60], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["Text", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 46], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 772, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 8], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 48], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 49], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 49], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 35], ["ListItem", 5], ["Reference", 5], ["SectionHeader", 4], ["Equation", 4], ["TextInlineMath", 3], ["Text", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 782], ["Line", 112], ["Equation", 13], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 765], ["Line", 124], ["Equation", 7], ["Text", 4], ["TextInlineMath", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["Line", 84], ["TextInlineMath", 5], ["SectionHeader", 5], ["Equation", 5], ["Text", 4], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 49], ["ListItem", 6], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 49], ["ListItem", 10], ["SectionHeader", 4], ["Text", 3], ["ListGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["TableCell", 152], ["Line", 53], ["Reference", 4], ["Text", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7885, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 18], ["Line", 17], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 23], ["Text", 2], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 694, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/BACON__Bayesian_Optimal_Condensation_Framework_for_Dataset_Distillation"}