<span id="page-0-0"></span>

# **BEARD: Benchmarking the Adversarial Robustness for Dataset Distillation**

<PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><sup>1,\*</sup>, <PERSON><PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><sup>1</sup>

<sup>1</sup> School of Electronic and Information Engineering, Beihang University <sup>2</sup> Department of Computer Science, University of Liverpool

{zhengzhou, buaafwq, lyushuchang, zhaoqi}@buaa.edu.cn {guangliang.cheng,xiaowei.huang}@liverpool.ac.uk

{guang11ang, cheng, xiaowei.huang}@liverpool.ac.

## Abstract

*Dataset Distillation (DD) is an emerging technique that compresses large-scale datasets into significantly smaller synthesized datasets while preserving high test performance and enabling the efficient training of large models. However, current research primarily focuses on enhancing evaluation accuracy under limited compression ratios, often overlooking critical security concerns such as adversarial robustness. A key challenge in evaluating this robustness lies in the complex interactions between distillation methods, model architectures, and adversarial attack strategies, which complicate standardized assessments. To address this, we introduce* BEARD*, an open and unified benchmark designed to systematically assess the adversarial robustness of DD methods, including DM, IDM, and BA-CON.* BEARD *encompasses a variety of adversarial attacks (e.g., FGSM, PGD, C&W) on distilled datasets like CIFAR-10/100 and TinyImageNet. Utilizing an adversarial game framework, it introduces three key metrics: Robustness Ratio (RR), Attack Efficiency Ratio (AE), and Comprehensive Robustness-Efficiency Index (CREI). Our analysis includes unified benchmarks, various Images Per Class (IPC) settings, and the effects of adversarial training. Results are available on the [BEARD Leaderboard,](https://beard-leaderboard.github.io/) along with a library providing model and dataset pools to support reproducible research. Access the code at [BEARD.](https://github.com/zhouzhengqd/BEARD/)*

## 1. Introduction

Deep Neural Networks (DNNs) [\[22\]](#page-8-0) have achieved significant success across various applications, primarily due to the availability of large datasets [\[18,](#page-8-1) [20,](#page-8-2) [30,](#page-9-0) [34\]](#page-9-1). These extensive datasets enable DNNs to learn valuable representations tailored to specific tasks. However, the acquisition of

such large datasets and the training of DNNs can be prohibitively expensive.

Dataset Distillation (DD), an emerging technique that compresses large datasets into smaller sets of synthetic samples [\[2,](#page-6-0) [6,](#page-7-0) [28,](#page-9-2) [36,](#page-9-3) [48,](#page-9-4) [52\]](#page-9-5), offers a cost-effective alternative by reducing training demands and simplifying dataset acquisition. DD has a profound impact on both research and practical applications, facilitating the efficient handling and processing of vast amounts of data across various fields. Significant progress in DD has been driven by advanced algorithms, which can be categorized into Meta-Model Matching (e.g., DD [\[36\]](#page-9-3), KIP [\[28,](#page-9-2) [29\]](#page-9-6), RFAD [\[23\]](#page-8-3), and FRePo [\[50\]](#page-9-7)), Gradient Matching (e.g., DC [\[48\]](#page-9-4), MTT [\[2\]](#page-6-0), TESLA [\[6\]](#page-7-0), and FTD [\[9\]](#page-8-4)), and Distribution Matching (e.g., DM [\[46\]](#page-9-8), CAFE [\[35\]](#page-9-9), IDM [\[49\]](#page-9-10), and BACON [\[52\]](#page-9-5)).

Despite these advancements, DNNs remain highly susceptible to adversarial attacks. These attacks involve perturbations that are imperceptible to the human eye but can effectively deceive classifiers when added to clean images (i.e., adversarial examples) [\[11,](#page-8-5) [25,](#page-8-6) [33,](#page-9-11) [51\]](#page-9-12), as illustrated in Figure [1.](#page-1-0) Such vulnerabilities pose significant security risks in applications like face recognition [\[38,](#page-9-13) [39\]](#page-9-14), object detection  $[15, 53]$  $[15, 53]$  $[15, 53]$ , and autonomous driving  $[37, 45]$  $[37, 45]$  $[37, 45]$ , thereby undermining the reliability of DNNs.

Research Gap. While some studies [\[3,](#page-6-1) [24,](#page-8-8) [41,](#page-9-17) [43\]](#page-9-18) suggest that DD may enhance adversarial robustness, they do not fully address the complex vulnerabilities introduced by adversarial attacks. The robustness of models trained on distilled datasets has not been systematically investigated. Evaluating this robustness is uniquely challenging due to the intricate interactions between distillation methods, model architectures, and diverse attack strategies, which cannot be captured by standard attack protocols. This gap highlights the need for a rigorous framework and tailored metrics to comprehensively assess and improve the adversarial robustness of models trained on distilled data.

To address this gap, we introduce BEARD, an open

<sup>\*</sup>Corresponding author.

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image depicts a three-stage process: Distillation Stage, Training Stage, and Evaluating Stage. In the Distillation Stage, a green cylinder labeled 'Source Dataset' is shown with several images of animals and a checkered pattern. Below it, three labels 'DC', 'DSA', and 'DM' are present, followed by an orange cylinder labeled 'Distilled Dataset'. The Training Stage shows a blue rectangle labeled 'Neural Network' connected by dotted lines to the 'Distilled Dataset' cylinder, with an orange arrow pointing upwards towards the neural network. The Evaluating Stage is divided into two paths. The top path shows a red cylinder labeled 'Source Dataset (Test Set)' with a skull and crossbones icon, leading to a yellow structure representing a neural network, and resulting in a 'Malicious Output' with a virus icon. This output shows data points (squares, triangles, circles) being misclassified with dotted lines indicating an 'Attack'. The bottom path shows a green cylinder labeled 'Source Dataset (Test Set)' leading to a similar yellow neural network structure, resulting in a 'Normal Output' with a green checkmark. This output shows the same data points correctly classified.

Figure 1. Illustration of evaluating adversarial robustness for dataset distillation: The process is divided into three stages: 1) Distillation stage, where diverse dataset distillation methods such as DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), and DM [\[46\]](#page-9-8) generate distilled datasets. 2) Training stage, where models are trained on these distilled datasets. 3) Evaluating stage, where adversarial attacks (e.g., FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20), and C&W [\[1\]](#page-6-2)) are applied to the test set of standard datasets like CIFAR-10/100 [\[19\]](#page-8-10) and TinyImageNet [\[8\]](#page-8-11), and model performance is evaluated both with and without adversarial attacks, summarized using specific metrics.

and unified benchmark designed to systematically evaluate the adversarial robustness of existing DD methods. We conducted extensive evaluations using representative DD techniques, including DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), DM [\[46\]](#page-9-8), MTT [\[2\]](#page-6-0), IDM [\[49\]](#page-9-10), and BACON [\[52\]](#page-9-5). These evaluations span a range of datasets, from large-scale collections like TinyImageNet [\[8\]](#page-8-11) to smaller ones such as CIFAR-10/100 [\[19\]](#page-8-10), encompassing diverse scenarios. We incorporated a broad spectrum of both typical and state-of-the-art attack methods for robustness evaluation, including FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20), C&W [\[1\]](#page-6-2), DeepFool [\[27\]](#page-9-21), and AutoAttack [\[4\]](#page-6-3). To thoroughly assess the adversarial robustness of DD methods, we employed the *adversarial game framework* to unify various DD tasks and attack scenarios, proposing three primary evaluation metrics: Robustness Ratio (RR), Attack Efficiency Ratio (AE), and Comprehensive Robustness-Efficiency Index (CREI). Additionally, we developed a straightforward evaluation protocol using both a Dataset Pool and a Model Pool. Our large-scale experiments involved cross-evaluating attack methods under multiple threat models, including both targeted and untargeted attacks. This analysis offers insights into adversarial robustness through unified benchmarks, diverse IPC settings, and the effect of adversarial training with BEARD.

Our contributions are summarized as follows:

- We introduce BEARD, a unified benchmark for evaluating adversarial robustness in dataset distillation, employing an *adversarial game framework* to systematically assess DD methods under various attack scenarios.
- We propose new metrics to evaluate the adversarial robustness of distilled datasets against different attacks, accompanied by a leaderboard that ranks existing DD methods based on these metrics.
- We provide open-source code with comprehensive docu-

mentation and easy extensibility, along with a Model Pool and Dataset Pool to facilitate adversarial robustness evaluations.

• We conduct a comparative analysis of the benchmark results, offering insights and recommendations for enhancing adversarial robustness in dataset distillation.

## 2. Related Work

### 2.1. Dataset Distillation

Dataset Distillation (DD) synthesizes a compact set of images that preserves the key information from the original dataset. Wang *et al.* [\[36\]](#page-9-3) pioneered a bi-level optimization approach that models network parameters based on synthetic data. However, this bi-level optimization incurs additional computational costs due to its nested structure. To address these costs, Zhao *et al.* [\[48\]](#page-9-4) introduced Dataset Condensation (DC), a gradient matching method that improves performance by aligning the informative gradients from the original datasets with those from the synthetic datasets at each iteration. An enhanced version of this approach, known as DSA [\[47\]](#page-9-19), further refines the process. Cazenavette *et al.* [\[2\]](#page-6-0) proposed mimicking the long-range training dynamics of real data by aligning learning trajectories, a method referred to as MTT. Additionally, Zhao & Bilen [\[46\]](#page-9-8) developed a distribution matching method called DM, which uses the Maximum Mean Discrepancy (MMD) metric. Building on DM, Zhao *et al.* [\[49\]](#page-9-10) introduced Improved Distribution Matching (IDM), a more efficient method that significantly enhances distillation performance. Zhou *et al.* [\[52\]](#page-9-5) incorporated the Bayesian framework into DD tasks, providing robust theoretical support, further advancing distillation outcomes. Furthermore, Cui *et al.* [\[5\]](#page-7-1) introduced DC-Bench, the first benchmark for DD.

<span id="page-2-0"></span>Other research directions include SRe2L [\[44\]](#page-9-22), RDED [\[32\]](#page-9-23), multi-size dataset distillation [\[14\]](#page-8-12), and lossless distillation through matching trajectories [\[13\]](#page-8-13).

## 2.2. Adversarial Dataset Distillation

Adversarial Robust Distillation (ARD) was introduced by Goldblum *et al.* [\[10\]](#page-8-14), showing that robustness can be transferred from teacher to student in knowledge distillation. Building on robust features [\[16\]](#page-8-15), Wu *et al.* [\[40\]](#page-9-24) proposed creating robust datasets, ensuring classifiers trained on them exhibit inherent adversarial robustness. Subsequently, Ma *et al.* [\[24\]](#page-8-8) explored the efficiency and reliability of DD tasks with TrustDD, while Chen *et al.* [\[3\]](#page-6-1) provided a securityfocused analysis of DD, highlighting associated risks. Additionally, Xue *et al.* [\[43\]](#page-9-18) investigated methods to embed adversarial robustness in distilled datasets, aiming to enhance resilience without sacrificing accuracy. Despite these contributions, the adversarial robustness of DD tasks remains underexplored. Evaluating this robustness is complicated by the intricate interactions between distillation methods, model architectures, and adversarial attacks, hindering standardized assessment. Although Wu *et al.* [\[41\]](#page-9-17) introduced a benchmark for evaluating the adversarial robustness of dataset distillation methods, their benchmark focuses solely on evaluating distilled datasets in a single IPC setting and considers only the dimension of attack effectiveness, neglecting the aspect of attack efficiency. Furthermore, they lack an intuitive leaderboard to display the adversarial robustness of different distilled datasets.

In contrast, we introduce BEARD, an open and unified benchmark specifically designed to systematically evaluate the adversarial robustness of existing DD methods across multiple datasets. Inspired by Dai *et al.* [\[7\]](#page-7-2), we utilize an adversarial game framework to enhance this evaluation and propose three key metrics: Robustness Ratio (RR), Attack Efficiency Ratio (AE), and Comprehensive Robustness-Efficiency Index (CREI). These metrics not only offer a unified perspective for evaluating the adversarial robustness of distilled datasets across different IPC settings, but also assess the robustness of models trained on these datasets from two distinct dimensions: attack effectiveness and attack efficiency. The overall evaluation is provided by the CREI, with individual metrics such as RR for attack effectiveness and AE for attack efficiency. Notably, we also construct multiple leaderboards to offer an intuitive display of the adversarial robustness of distilled datasets.

## 3. Adversarial Robustness for Dataset Distillation against Multiple Attacks

We start with defining the notations, then analyze challenges in adversarial robustness for Dataset Distillation (DD), focusing on attack effectiveness and time efficiency. We introduce a unified adversarial game framework to tackle these issues and outline goals for improving robustness. Finally, we propose metrics within this framework to measure robustness comprehensively.

**Notations.** Consider a large dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^N$ , where  $x_i \in \mathcal{X} \subseteq \mathbb{R}^d$  denotes the input samples and  $y_i \in \mathcal{Y} \subseteq \{1, \ldots, C\}$  denotes the corresponding labels. DD aims to generate a synthetic dataset  $\mathcal{S} = \{(\tilde{x}_j, \tilde{y}_j)\}_{j=1}^M$  such that a model  $\mathcal{M}(\cdot): \mathcal{X} \to \mathcal{Y}$  trained on S performs comparably to one trained on  $T$ . We denote the defender function, which encompasses DD methods with diverse IPC settings, as  $D$ . The model  $M$  is trained on the distilled dataset generated by D with diverse IPC settings  $d \in \mathcal{D}$  (i.e., D outputs a function  $m \in \mathcal{M}$ ). The attacker function is denoted by  $\mathcal{A}$ , with a perturbation budget  $\epsilon \in \mathcal{P}$ .

### 3.1. A Unified Adversarial Game Framework for Evaluating Adversarial Robustness in Dataset **Distillation**

Previous studies on adversarial robustness in DD [\[41,](#page-9-17) [43\]](#page-9-18) have mainly focused on model accuracy, which provides an incomplete view of robustness. A more comprehensive evaluation should also consider attack effectiveness and time efficiency. To address this, we introduce a unified adversarial game framework that combines metrics for attack performance and efficiency, offering a more thorough assessment of how DD methods perform under various adversarial conditions.

**Definition 1 (Attacker Function)** Let  $\mathcal{L}: \mathcal{Y} \times \mathcal{Y} \rightarrow \mathbb{R}$ *be a loss function, and let*  $m \in M$  *be a model trained on a distilled dataset* S*. The adversarial perturbation is constrained by*  $||\hat{x} - x||_p \leq \epsilon$ . The attacker function  $A: \mathcal{X} \times \mathcal{Y} \times \mathcal{M} \rightarrow \mathcal{X}$  maps an input and a hypothesis *to an adversarially perturbed version of the input, defined as:*

$$
\mathcal{A}(x, y, m) = \underset{\|\hat{x} - x\|_p \le \epsilon}{\arg \max} \mathcal{L}(m(x), y). \tag{1}
$$

**Definition 2 (Defender Function)** Let  $\mathcal{L}: \mathcal{Y} \times \mathcal{Y} \rightarrow \mathbb{R}$  be *a loss function, and let* A *be a set of attacker functions with perturbation budget*  $\epsilon \in \mathcal{P}$ *. The defender function*  $\mathcal{D}$  *aims to generate a synthetic dataset* S *such that the model* m *trained on* S *minimizes the loss function against adversarial attacks from* A*. Formally,* D *is defined as:*

$$
\mathcal{D}(\mathcal{A}) = \arg\min_{\mathcal{S}} \max_{\mathcal{A}} \mathcal{L}(m(x), y), \tag{2}
$$

*where*  $m \in \mathcal{M} = \mathcal{D}(\mathcal{T}, \mathcal{A})$  *is the model trained on the dataset* S *generated by the defender function, and* A *represents the set of adversarial attacks.*

**Definition 3 (Attack Success Rate (ASR))** *Let*  $(x, y) \in$  $(X, Y)$  be an input-label pair,  $a \in A$  an adversarial at $tack function, and m \in M$  *a* model trained on a dis*tilled dataset. The attack success rate (ASR) is defined as* *the probability that the model's prediction changes after an adversarial perturbation, specifically when the model correctly classifies the original input but misclassifies the perturbed one:*

$$
\mathcal{ASR}(m; a) = \mathbb{E}_{(x,y)\in(\mathcal{X},\mathcal{Y})}\mathbf{1}\{m(a(x)) \neq y \land m(x) = y\},\tag{3}
$$

*where*  $1\{\cdot\}$  *is the indicator function.* 

**Definition 4 (Attack Success Time (AST))** *Let*  $(x, y) \in$ (X , Y) *be an input-label pair, and let* t *denote the time taken to generate an adversarial example*  $\hat{x}$  *using an attack function*  $a \in A$  *such that the model misclassifies the perturbed input, i.e.,*  $m(\hat{x}) \neq y$ *. The Attack Success Time (AST) is defined as the expected time required for a successful adversarial attack:*

$$
\mathcal{AST}(m; a) = \mathbb{E}_{(x,y)\in(\mathcal{X},\mathcal{Y})}[t \mid m(a(x)) \neq y]. \tag{4}
$$

<span id="page-3-0"></span>

#### 3.1.1. Definition 5 (Adversarial Game Framework) *Given*

*conceptual thresholds* γ *and* β*, which define the conditions for evaluating attack success rate (ASR) and attack success time (AST), respectively, and a set* A *of perturbation functions that may occur during test-time, the performance of the model is evaluated based on its ASR and AST under these perturbations. The model is considered robust if:*

$$
\frac{\mathbb{E}_{m \in \mathcal{M}} \mathbb{E}_{a \in \mathcal{A}} \mathcal{ASR}(m; a)}{\max_{m^* \in \mathcal{M}, a^* \in \mathcal{A}} \mathcal{ASR}(m^*; a^*)} \le \gamma,
$$
 (5)

and 
$$
\frac{\mathbb{E}_{m \in \mathcal{M}} \mathbb{E}_{a \in \mathcal{A}} \mathcal{A} \mathcal{S} \mathcal{T}(m; a)}{\max_{m^* \in \mathcal{M}, a^* \in \mathcal{A}} \mathcal{A} \mathcal{S} \mathcal{T}(m^*; a^*)} \ge \beta.
$$
 (6)

*Here,*  $\gamma$  *and*  $\beta$  *are conceptual thresholds that define the conditions under which the defender 'wins' by achieving a low ASR and a high AST, respectively. These thresholds are not intended to be assigned specific values in practice; rather, they serve solely to structure the adversarial game dynamics and define the conditions for victory.*

<span id="page-3-1"></span>Remark 1 *In the adversarial game framework, the defender wins if two conditions are met: (1) the attack success rate*  $ASR(m; a)$  *is minimized below the threshold*  $\gamma$ *, and (2) the attack success time*  $AST(m; a)$  *is maximized above the threshold* β*. If either condition fails, the attacker wins. A win for the defender indicates effective robustness against adversarial perturbations, while a win for the attacker reveals vulnerabilities that need addressing. This game is defined over a set of models* m ∈ M*, each trained on distinct distilled datasets*  $(\tilde{x}, \tilde{y}) \in (\tilde{\mathcal{X}}, \tilde{\mathcal{Y}}) \subseteq \mathcal{S}$ , and subjected to *various attacks*  $a \in \mathcal{A}$ *. When a specific model m or attack* a *is chosen, the multi-player game simplifies to a singleplayer game focused on their interaction.*

### 3.2. Metric for Evaluating Adversarial Robustness

Building on the Definition [5](#page-3-0) and Remark [1,](#page-3-1) we propose metrics that aggregate accuracy across both single and multiple attacks, as well as models trained on different IPC distilled datasets. This section introduces two key criteria for evaluating adversarial robustness: 1) attack effectiveness and 2) time efficiency, along with the corresponding metrics for each.

Definition 6 (Robustness Ratio) *Given a neural network model*  $m \in \mathcal{M}$  *and an adversarial attack function*  $a \in \mathcal{A}$ *, the robustness ratio is defined as:*

$$
RR(m;a) = 100 \times \left[1 - \frac{\mathbb{E}_{m \in \mathcal{M}} \mathbb{E}_{a \in \mathcal{A}} \mathcal{ASR}(m;a)}{\max_{m \in \mathcal{M}, a \in \mathcal{A}} \mathcal{ASR}(m;a)}\right].
$$
 (7)

Remark 2 *The purpose of using "*1−*" in the formula is to emphasize model robustness rather than attack success. A higher attack success rate (ASR) indicates a more effective attack but a less robust model. Therefore, by subtracting the normalized attack success rate from 1, the formula inversely represents robustness. This way, when ASR is high, the robustness ratio (RR) will be low, and when ASR is low, the model is considered more robust. The formula also normalizes the ASR by dividing it by the maximum possible ASR to provide a standardized measure of robustness.*

Definition 7 (Attack Efficiency Ratio) *Given a neural network model* m ∈ M *and an adversarial attack function*  $a \in \mathcal{A}$ , the attack efficiency ratio is defined as:

$$
AE(m;a) = 100 \times \left[ \frac{\mathbb{E}_{m \in \mathcal{M}} \mathbb{E}_{a \in \mathcal{A}} \mathcal{A}ST(m;a)}{\max_{m \in \mathcal{M}, a \in \mathcal{A}} \mathcal{A}ST(m;a)} \right].
$$
 (8)

Definition 8 (Comprehensive Robustness-Efficiency Index) *The Comprehensive Robustness-Efficiency Index (CREI) integrates both the Robustness Ratio (RR) and the Attack Efficiency (AE) into a unified metric. It is defined as:*

$$
CREI = \alpha \times RR + (1 - \alpha) \times AE, \tag{9}
$$

*where* α *is an adjustable coefficient that determines the weighting between robustness and efficiency. This parameter allows for flexible balancing according to the specific needs of the evaluation.*

Remark 3 *The adversarial game framework can shift between multi-player and single-player scenarios, where "single-adversary" refers to a model facing one attack strategy, while "multi-adversary" involves multiple attack strategies. In this context, the metrics adjust: Robustness Ratio (RR) and Attack Efficiency (AE) become Single-Adversary Robustness Ratio (RRS) and Single-Adversary* <span id="page-4-2"></span>*Attack Efficiency Ratio (AES) for single-adversary situations, and Multi-Adversary Robustness Ratio (RRM) and Multi-Adversary Attack Efficiency Ratio (AEM) for multiadversary contexts. The defender aims to minimize the attack success rate, which aligns with maximizing RR, while optimizing AE corresponds to maximizing attack success time. Conversely, the attacker seeks to maximize AE and minimize RR. Analyzing these metrics within the framework allows for a clearer evaluation of dataset distillation robustness, highlighting the model's resilience against adversarial attacks and the efficiency of those attacks.*

## 4. Adversarial Robustness Benchmark for Dataset Distillation

### 4.1. Overview of BEARD

BEARD consists of two main stages: the *Training Stage* and the *Evaluation Stage*, as illustrated in Figure [2.](#page-5-0) In the training stage (Section [4.1.1\)](#page-4-0), models are trained on datasets from the dataset pool. The evaluation stage (Section [4.1.2\)](#page-4-1) involves applying adversarial perturbations to test images from an attack library to assess model robustness. The benchmark comprises three key components: *Dataset Pool*, *Model Pool*, and *Evaluation Metrics*. More details are provided in Appendix [A.](#page-10-1)

<span id="page-4-0"></span>

#### 4.1.1. Training Stage

In the training stage, we focus on CIFAR-10 [\[19\]](#page-8-10), CIFAR-100 [\[19\]](#page-8-10), and TinyImageNet [\[8\]](#page-8-11) due to their widespread use and diverse performance in dataset distillation (DD). Simpler datasets like MNIST [\[21\]](#page-8-16) and Fashion-MNIST [\[42\]](#page-9-25) are initially excluded but will be considered later to explore data efficiency. We evaluate six prominent DD methods: DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), DM [\[46\]](#page-9-8), MTT [\[2\]](#page-6-0), IDM [\[49\]](#page-9-10), and BACON [\[52\]](#page-9-5), which represent a range of optimization techniques, including gradient matching [\[47,](#page-9-19) [48\]](#page-9-4), distribution matching [\[46,](#page-9-8) [49\]](#page-9-10), trajectory matching [\[2\]](#page-6-0), and optimization-based approaches [\[52\]](#page-9-5). Synthetic datasets are generated using IPC-1, IPC-10, and IPC-50 settings, maintaining consistency with DC-bench [\[5\]](#page-7-1) for hyperparameters. These datasets, primarily sourced from existing *opensource* distilled datasets, are produced by six DD methods across diverse IPC settings and constitute the Dataset Pool. This pool is essential for evaluating the performance of various DD methods, ensuring a comprehensive comparison across different distillation approaches.

<span id="page-4-1"></span>

#### 4.1.2. Evaluating Stage

In the evaluating stage, the Model Pool repository is utilized to streamline the assessment of robust models trained on distilled datasets. By integrating metrics derived from the adversarial game framework, including RR, AE, and CREI, this evaluation can more effectively measure the

models' resilience against adversarial attacks within the competitive dynamics of the game setting. The repository facilitates the analysis of model performance and broader trends by consolidating checkpoints from various sources. However, challenges arise in unifying these models due to differing architectures and normalization techniques. After generating distilled datasets from the dataset pool, multiple models are trained from scratch using various distillation methods, IPC settings, and the Adam optimizer for 1,000 epochs. The models with the highest validation accuracy are selected and added to the model pool. Adversarial robustness is assessed using a diverse attack library compatible with Torchattacks [\[17\]](#page-8-17), including methods like FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20),  $C&W$  [\[1\]](#page-6-2), DeepFool [\[27\]](#page-9-21), and AutoAttack [\[4\]](#page-6-3). Both targeted and untargeted attacks are conducted with a uniform perturbation budget of  $|\epsilon| = \frac{8}{255}$  for most methods, with exceptions for DeepFool and C&W.

### 4.2. Leaderboards

We provide 12 leaderboards for CIFAR-10 [\[19\]](#page-8-10), CIFAR-100 [\[19\]](#page-8-10), and TinyImageNet [\[8\]](#page-8-11), covering IPC-1, IPC-10, and IPC-50 settings. These leaderboards rank methods based on robustness and efficiency metrics, including RR, AE, and CREI. The leaderboard evaluates six dataset distillation methods: DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), DM [\[46\]](#page-9-8), MTT [\[2\]](#page-6-0), IDM [\[49\]](#page-9-10), and BACON [\[52\]](#page-9-5). In terms of adversarial attacks, the leaderboards integrate various methods such as FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20), C&W [\[1\]](#page-6-2), DeepFool [\[27\]](#page-9-21), and AutoAttack [\[4\]](#page-6-3), all compatible with Torchattacks [\[17\]](#page-8-17). Evaluating adversarial robustness is challenging due to the diversity of settings and attack types, with no unified framework available. As illustrated in Figure [3,](#page-5-1) our leaderboards fill this gap by offering a comprehensive, unified evaluation of adversarial robustness in dataset distillation.

### 5. Analysis

#### 5.1. Robustness Evaluation Using Proposed Metrics

The results in Figure [4](#page-7-3) demonstrate that models trained on synthetic datasets generated by Dataset Distillation (DD) methods exhibit higher Multi-Adversary Robustness Ratio (RRM) under both targeted and untargeted adversarial attacks, although they show lower Multi-Adversary Attack Efficiency Ratio (AEM) compared to models trained on full-size datasets. Under targeted attacks, methods such as DSA, DM, and BACON demonstrate superior adversarial robustness, with RRM values increasing as dataset size expands, as shown in Figures [4](#page-7-3) (a), (b), and (c). Conversely, untargeted attacks generally lead to a decline in RRM, but DSA, DM, BACON, and DC continue to perform robustly, as illustrated in Figures  $4$  (d), (e), and (f). The AEM metric further indicates that higher values correspond to increased time required by adversaries to attack the models, reflect-

<span id="page-5-2"></span><span id="page-5-0"></span>Image /page/5/Figure/0 description: This figure illustrates the BEARD framework, a system for training and evaluating models. The framework is divided into several sections: Training, Evaluating, Dataset Pool, and Model Pool. The Training section includes a `Train\_Config.json` file and options for Model Architecture (ConvNet, LeNet, ResNet), and Training Strategy (Adam/SGD, Cross Entropy). The Evaluating section includes an `Evaluate\_Config.json` file and options for Attack Library (FGSM, PGD, C&W) and Evaluating Strategy (Accuracy, Time). Below these sections is the User Interactive Layer, followed by Datasets (MNIST, F-MNIST, SVHN, CIFAR-10, CIFAR-100, Tiny-ImageNet) and a Library section featuring logos for PyTorch, scikit-learn, TorchAttack, and Dataset Distillation (DC, DSA, DM). The figure also includes a caption at the bottom stating: "Illustration of BEARD: We first obtain a distilled dataset pool from the source dataset using various dataset distillation techniques."

Figure 2. Illustration of BEARD: We first obtain a distilled dataset pool from the source dataset using various dataset distillation methods, such as DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), DM [\[46\]](#page-9-8), IDM [\[49\]](#page-9-10), BACON [\[52\]](#page-9-5), among others. Next, we train neural networks on these diverse distilled datasets to generate a collection of pretrained models, forming our model pool. Finally, we evaluate the adversarial robustness of the models in the model pool by applying a variety of adversarial attack methods, including FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20), C&W [\[1\]](#page-6-2), DeepFool [\[27\]](#page-9-21), AutoAttack [\[4\]](#page-6-3), and others.

Leaderboard: CIFAR-10 (Unified), untargeted attack

<span id="page-5-1"></span>

| Rank | Method                                                                     | CREI   | RRM    | AEM    | Code | Distilled Data | Author       | Venue     | Update Date |
|------|----------------------------------------------------------------------------|--------|--------|--------|------|----------------|--------------|-----------|-------------|
| 1    | Improved Distribution Matching<br>for Dataset Condensation<br>IDM          | 28.46% | 33.03% | 23.89% | ✓    | X              | Ganlong Zhao | CVPR 2023 | 2024/08/14  |
| 2    | Dataset Condensation with<br>Distribution Matching<br>DM                   | 28.32% | 34.50% | 22.13% | ✓    | ✓              | Bo Zhao      | WACV 2023 | 2024/08/14  |
| 3    | Dataset Condensation with<br>Differentiable Siamese<br>Augmentation<br>DSA | 27.75% | 36.53% | 18.97% | ✓    | ✓              | Bo Zhao      | ICML 2021 | 2024/08/14  |

Figure 3. The top three entries on our CIFAR-10 leaderboard, with unified IPC settings, are available at [https://beard](https://beard-leaderboard.github.io/)[leaderboard.github.io/](https://beard-leaderboard.github.io/). The leaderboard utilizes metrics such as CREI, RRM, and AEM to assess robustness and attack efficiency. Additionally, it provides links to the code and distilled datasets for each entry, along with detailed information regarding authors, venues, and the last update.

ing enhanced robustness. While models trained on full-size datasets tend to exhibit greater attack efficiency, the Comprehensive Robustness-Efficiency Index (CREI) highlights that DD methods, particularly DSA, DM, and BACON, achieve a more balanced performance in terms of both ro-

bustness and efficiency under targeted attacks. Despite smaller gains under untargeted attacks, DD methods consistently enhance robustness across different datasets. We also examine the trade-off between robustness and model performance. Further details, including our analysis of this

trade-off, can be found in Appendix [B.](#page-11-0)

#### 5.2. Robustness Evaluation with Diverse IPCs

We find two key observations from Figure [5:](#page-7-4) 1) Increasing the IPC decreases adversarial robustness, as reflected by lower CREI values in Figures [5](#page-7-4) (a), (b), and (c); and 2) Increasing the dataset scale enhances adversarial robustness when using DD methods compared to full-size datasets, as indicated by the distance between the black dashed line and the others in Figures  $5$  (d), (e), and (f). While full-size models often exhibit superior performance under targeted attacks, methods like BACON prove more effective with fewer images, highlighting their efficiency with smaller datasets. Additionally, the consistent CREI trends across CIFAR-10, CIFAR-100, and TinyImageNet indicate that these methods are robust and generalizable across different datasets. These findings underscore the complex relationship between IPC, dataset size, and adversarial robustness, affirming the effectiveness of specific methods in various scenarios. Further details are provided in Appendix [B.](#page-11-0)

#### 5.3. Robustness Evaluation with Adversarial Training

Figure [6](#page-8-18) demonstrates that Adversarial Training (AT) significantly enhances model robustness against both targeted and untargeted attacks within a unified IPC in a multiadversary context. For targeted attacks, models utilizing AT (orange bars) achieve higher CREI values compared to those without AT (purple bars), particularly for methods such as DSA and BACON (Figure [6](#page-8-18) (a)). In contrast, models trained on full-size datasets exhibit lower CREI values, indicating reduced robustness, consistent with the trend of diminishing robustness as IPC increases. Without AT, all methods experience a significant decline in robustness, although DSA and DM perform relatively well. For untargeted attacks, the full-size dataset shows the most substantial improvement with AT, while all methods decline in performance without it (Figure  $6$  (b)). Notably, models trained on full-size datasets benefit more from AT than those trained on distilled datasets, suggesting that as dataset scale increases, the effectiveness of AT also increases, as illustrated by the red curve. These findings highlight the importance of selecting appropriate dataset scales to optimize the benefits of AT. More details are in Appendix [B.](#page-11-0)

## 6. Outlook

Conclusion. A standardized benchmark is crucial for advancing the evaluation of adversarial robustness in Dataset Distillation (DD) methods. To fill this gap, we propose BEARD, an open and unified benchmark designed to assess adversarial robustness in DD. This benchmark includes a dataset pool, a model pool, and novel metrics (RR, AE, and CREI), as well as a leaderboard that ranks models

based on their performance across three standard datasets under six adversarial attacks. Currently, the leaderboard includes 18 models trained on distilled datasets from six DD methods with three IPC settings. We aim to expand the benchmark by incorporating additional distillation methods, larger datasets, and more advanced attack types.

Limitations and Future Plans. While our benchmark currently encompasses six representative DD methods and six adversarial attack strategies, it covers most major types of both. Future plans include broadening BEARD to incorporate a wider array of DD methods, more sophisticated adversarial attack techniques, and larger datasets. Although the benchmark is focused primarily on enhancing the adversarial robustness of DD methods for image classification, we also intend to investigate effective strategies for attacking these methods and extend our evaluation to other modalities, such as text, graphs, and audio. This expansion could provide valuable insights into the adversarial robustness of distilled datasets across various domains.

Practical Applications and Potential Impact. The BEARD benchmark serves as both a research tool and a practical resource for evaluating the adversarial robustness of DD methods. By offering a standardized framework, BEARD helps users assess the strengths and weaknesses of various methods, thus supporting the development of more resilient data distillation techniques. Additionally, with increasing concerns about data security and privacy, BEARD holds considerable potential for applications in these critical areas, providing valuable insights into the robustness of distilled datasets in adversarial environments.

# **References**

- <span id="page-6-2"></span>[1] Nicholas Carlini and David Wagner. Towards evaluating the robustness of neural networks. In *IEEE Symposium on Security and Privacy (S&P)*, pages 39–57, 2017. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)
- <span id="page-6-0"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 4750–4759, 2022. [1,](#page-0-0) [2,](#page-1-1) [5,](#page-4-2) [11](#page-10-2)
- <span id="page-6-1"></span>[3] Zongxiong Chen, Jiahui Geng, Derui Zhu, Herbert Woisetschlaeger, Qing Li, Sonja Schimmler, Ruben Mayer, and Chunming Rong. A comprehensive study on dataset distillation: Performance, privacy, robustness and fairness. *arXiv preprint arXiv:2305.03355*, 2023. [1,](#page-0-0) [3](#page-2-0)
- <span id="page-6-3"></span>[4] Francesco Croce and Matthias Hein. Reliable evaluation of adversarial robustness with an ensemble of diverse parameter-free attacks. In *International Conference on Machine Learning (ICML)*, pages 2206–2216, 2020. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)

<span id="page-7-3"></span>Image /page/7/Figure/0 description: This image displays a grid of six bar charts, arranged in two rows and three columns, comparing the performance of different methods (RRM, AEM, CREI) across three datasets (CIFAR-10, CIFAR-100, TinyImageNet) under both targeted and untargeted attacks. The top row (a, b, c) shows results for targeted attacks, while the bottom row (d, e, f) shows results for untargeted attacks. Each chart has an x-axis labeled with various attack methods such as 'Full-size DC', 'DSA', 'MTT', 'DM', and 'IDMBACON' (or 'BACON' for TinyImageNet). The y-axis represents performance, ranging from 0 to 60. The bars represent RRM (purple) and AEM (orange), while the red line with 'x' markers indicates CREI. Specifically, chart (a) is for CIFAR-10 Targeted Attack, (b) for CIFAR-100 Targeted Attack, and (c) for TinyImageNet Targeted Attack. Chart (d) is for CIFAR-10 Untargeted Attack, (e) for CIFAR-100 Untargeted Attack, and (f) for TinyImageNet Untargeted Attack. The charts illustrate varying performance levels for each method and dataset combination.

Figure 4. Performance of various dataset distillation methods under targeted and untargeted adversarial attacks on CIFAR-10, CIFAR-100, and TinyImageNet. The first row depicts targeted attacks with unified IPC settings, while the second row shows performance under untargeted attacks. Metrics used include Multi-Adversary Robustness Ratio (RRM), Multi-Adversary Attack Efficiency Ratio (AEM), and Comprehensive Robustness-Efficiency Index (CREI).

<span id="page-7-4"></span>Image /page/7/Figure/2 description: This image contains six line graphs, arranged in a 2x3 grid. The top row graphs are titled "CREI on CIFAR-10 (Targeted Attack)", "CREI on CIFAR-100 (Targeted Attack)", and "CREI on TinyImageNet (Targeted Attack)". The bottom row graphs are titled "CREI on CIFAR-10 (Untargeted Attack)", "CREI on CIFAR-100 (Untargeted Attack)", and "CREI on TinyImageNet (Untargeted Attack)". All graphs have "IPC-1", "IPC-10", and "IPC-50" on the x-axis. The y-axis represents CREI values. Each graph shows multiple lines representing different methods: "Full-size" (dashed black line with circles), "DC" (red line with squares), "DSA" (green line with diamonds), "MTT" (cyan line with upward triangles), "DM" (blue line with downward triangles), and "BACON" (purple line with inverted triangles). In graph (a), "Full-size" is around 25, "DC" starts at 27 and drops to 20, "DSA" starts at 26 and drops to 20, "MTT" starts at 24 and drops to 19, "DM" starts at 22 and drops to 18, and "BACON" starts at 23 and drops to 18. In graph (b), "Full-size" is around 27, "DC" starts at 28 and drops to 22, "DSA" starts at 29 and drops to 21, "MTT" starts at 27 and drops to 20, "DM" starts at 25 and drops to 19, and "BACON" starts at 26 and drops to 18. In graph (c), "Full-size" is around 25, "DC" starts at 30 and drops to 22, "DSA" starts at 29 and drops to 21, "MTT" starts at 28 and drops to 20, "DM" starts at 27 and drops to 19, and "BACON" starts at 26 and drops to 18. In graph (d), "Full-size" is around 25, "DC" starts at 42 and drops to 24, "DSA" starts at 38 and drops to 25, "MTT" starts at 34 and drops to 26, "DM" starts at 25 and drops to 23, and "BACON" starts at 26 and drops to 24. In graph (e), "Full-size" is around 25, "DC" starts at 32 and drops to 16, "DSA" starts at 24 and drops to 20, "MTT" starts at 22 and drops to 18, "DM" starts at 20 and drops to 17, and "BACON" starts at 21 and drops to 19. In graph (f), "Full-size" is around 15, "DC" starts at 23 and drops to 15, "DSA" starts at 21 and drops to 14, "MTT" starts at 20 and drops to 13, "DM" starts at 18 and drops to 12, and "BACON" starts at 17 and drops to 11.

Figure 5. CREI trends under targeted and untargeted attacks across three datasets: CIFAR-10, CIFAR-100, and TinyImageNet. The xaxis represents the number of IPC, while the y-axis displays CREI values. Six DD methods (DC, DSA, MTT, DM, IDM, BACON) are compared to full-size datasets at IPC-1, IPC-10, and IPC-50, highlighting their robustness and efficiency across various attacks.

- <span id="page-7-1"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. In *Advances in Neural Information Processing Systems (NeurIPS)*, pages 810–822, 2022. [2,](#page-1-1) [5,](#page-4-2) [12](#page-11-1)
- <span id="page-7-0"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory.

In *International Conference on Machine Learning (ICML)*, pages 6565–6590, 2023. [1](#page-0-0)

<span id="page-7-2"></span>[7] Sihui Dai, Saeed Mahloujifar, Chong Xiang, Vikash Sehwag, Pin-Yu Chen, and Prateek Mittal. Multirobustbench: Benchmarking robustness against multiple attacks. In *International Conference on Machine Learning (ICML)*, pages

<span id="page-8-18"></span>Image /page/8/Figure/0 description: This image contains two bar charts side-by-side, labeled (a) and (b). Both charts are titled "CREI on CIFAR-10" and compare "CREI (w/o AT)" (purple bars) and "CREI (w/ AT)" (orange bars) across different methods: "Full-size DC", "DSA", "MTT", "DM", and "IDMBACON". A red line with markers indicates "CREI Improvement (AT - w/o AT)". Chart (a) represents a "Targeted Attack" and chart (b) represents an "Untargeted Attack". In chart (a), the purple bars range from approximately 24 to 35, and the orange bars range from approximately 52 to 57. The red line starts around 28, dips to about 18, rises to about 28, dips to about 18, and ends around 28. In chart (b), the purple bars range from approximately 25 to 35, and the orange bars range from approximately 41 to 54. The red line starts around 25, dips to about 12, rises to about 15, dips to about 10, and ends around 10.

Figure 6. Illustration of CREI trends on CIFAR-10 under targeted and untargeted attacks with (w/) or without (w/o) Adversarial Training (AT). The x-axis shows DD methods (Full-size, DC, DSA, MTT, DM, IDM, BACON) under unified IPC, while the y-axis displays CREI values measuring adversarial robustness. CREI improvement indicates the difference between models with and without AT.

6760–6785, 2023. [3](#page-2-0)

- <span id="page-8-11"></span>[8] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 248–255, 2009. [2,](#page-1-1) [5,](#page-4-2) [11](#page-10-2)
- <span id="page-8-4"></span>[9] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3749–3758, 2023. [1](#page-0-0)
- <span id="page-8-14"></span>[10] Micah Goldblum, Liam Fowl, Soheil Feizi, and Tom Goldstein. Adversarially robust distillation. In *Association for the Advancement of Artificial Intelligence (AAAI)*, number 04, pages 3996–4003, 2020. [3](#page-2-0)
- <span id="page-8-5"></span>[11] Ian J. Goodfellow, Jonathon Shlens, and Christian Szegedy. Explaining and harnessing adversarial examples. In *International Conference on Learning Representations ICLR*, 2015. [1](#page-0-0)
- <span id="page-8-9"></span>[12] Ian J. Goodfellow, Jonathon Shlens, and Christian Szegedy. Explaining and harnessing adversarial examples. In *International Conference on Learning Representations ICLR*, 2015. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)
- <span id="page-8-13"></span>[13] Ziyao Guo, Kai Wang, George Cazenavette, HUI LI, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *International Conference on Learning Representations (ICLR)*, 2024. [3](#page-2-0)
- <span id="page-8-12"></span>[14] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. In *International Conference on Learning Representations (ICLR)*, 2024. [3](#page-2-0)
- <span id="page-8-7"></span>[15] Yu-Chih-Tuan Hu, Bo-Han Kung, Daniel Stanley Tan, Jun-Cheng Chen, Kai-Lung Hua, and Wen-Huang Cheng. Naturalistic physical adversarial patch for object detectors. In *IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 7848–7857, 2021. [1](#page-0-0)

- <span id="page-8-15"></span>[16] Andrew Ilyas, Shibani Santurkar, Dimitris Tsipras, Logan Engstrom, Brandon Tran, and Aleksander Madry. Adversarial examples are not bugs, they are features. *Advances in Neural Information Processing Systems (NeurIPS)*, 32, 2019. [3,](#page-2-0) [15](#page-14-0)
- <span id="page-8-17"></span>[17] Hoki Kim. Torchattacks: A pytorch repository for adversarial attacks. *arXiv preprint arXiv:2010.01950*, 2020. [5,](#page-4-2) [11](#page-10-2)
- <span id="page-8-1"></span>[18] Alexander Kirillov, Eric Mintun, Nikhila Ravi, Hanzi Mao, Chloe Rolland, Laura Gustafson, Tete Xiao, Spencer Whitehead, Alexander C Berg, Wan-Yen Lo, et al. Segment anything. In *IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 4015–4026, 2023. [1](#page-0-0)
- <span id="page-8-10"></span>[19] A Krizhevsky. Learning multiple layers of features from tiny images. *Master's thesis, University of Tront*, 2009. [2,](#page-1-1) [5,](#page-4-2) [11](#page-10-2)
- <span id="page-8-2"></span>[20] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, pages 1106–1114, 2012. [1](#page-0-0)
- <span id="page-8-16"></span>[21] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [5](#page-4-2)
- <span id="page-8-0"></span>[22] Yann LeCun, Yoshua Bengio, and Geoffrey Hinton. Deep learning. *Nature*, 521(7553):436–444, 2015. [1](#page-0-0)
- <span id="page-8-3"></span>[23] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Advances in Neural Information Processing Systems (NeurIPS)*, pages 13877–13891, 2022. [1](#page-0-0)
- <span id="page-8-8"></span>[24] Shijie Ma, Fei Zhu, Zhen Cheng, and Xu-Yao Zhang. Towards trustworthy dataset distillation. *arXiv preprint arXiv:2307.09165*, 2023. [1,](#page-0-0) [3](#page-2-0)
- <span id="page-8-6"></span>[25] Aleksander Madry, Aleksandar Makelov, Ludwig Schmidt, Dimitris Tsipras, and Adrian Vladu. Towards deep learning models resistant to adversarial attacks. In *International Conference on Learning Representations ICLR*, 2018. [1](#page-0-0)

- <span id="page-9-20"></span>[26] Aleksander Madry, Aleksandar Makelov, Ludwig Schmidt, Dimitris Tsipras, and Adrian Vladu. Towards deep learning models resistant to adversarial attacks. In *International Conference on Learning Representations ICLR*, 2018. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)
- <span id="page-9-21"></span>[27] Seyed-Mohsen Moosavi-Dezfooli, Alhussein Fawzi, and Pascal Frossard. Deepfool: a simple and accurate method to fool deep neural networks. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 2574–2582, 2016. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)
- <span id="page-9-2"></span>[28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1](#page-0-0)
- <span id="page-9-6"></span>[29] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, pages 5186–5198, 2021. [1](#page-0-0)
- <span id="page-9-0"></span>[30] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *International Conference on Machine Learning (ICML)*, pages 8748–8763, 2021. [1](#page-0-0)
- <span id="page-9-26"></span>[31] Levent Sagun, Utku Evci, V Ugur Guney, Yann Dauphin, and Leon Bottou. Empirical analysis of the hessian of over-parametrized neural networks. *arXiv preprint arXiv:1706.04454*, 2017. [12](#page-11-1)
- <span id="page-9-23"></span>[32] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 9390–9399, 2024. [3](#page-2-0)
- <span id="page-9-11"></span>[33] Christian Szegedy, Wojciech Zaremba, Ilya Sutskever, Joan Bruna, Dumitru Erhan, Ian J. Goodfellow, and Rob Fergus. Intriguing properties of neural networks. In *International Conference on Learning Representations ICLR*, 2014. [1](#page-0-0)
- <span id="page-9-1"></span>[34] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. *Advances in Neural Information Processing Systems (NeurIPS)*, 30, 2017. [1](#page-0-0)
- <span id="page-9-9"></span>[35] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 12196–12205, 2022. [1](#page-0-0)
- <span id="page-9-3"></span>[36] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-15"></span>[37] Yue Wang, Esha Sarkar, Wenqing Li, Michail Maniatakos, and Saif Eddin Jabari. Stop-and-go: Exploring backdoor attacks on deep reinforcement learning-based traffic congestion control systems. *IEEE Transactions on Information Forensics and Security (TIFS)*, 16:4772–4787, 2021. [1](#page-0-0)
- <span id="page-9-13"></span>[38] Xingxing Wei, Ying Guo, and Jie Yu. Adversarial sticker: A stealthy attack method in the physical world. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 45(3):2711–2725, 2022. [1](#page-0-0)

- <span id="page-9-14"></span>[39] Xingxing Wei, Ying Guo, Jie Yu, and Bo Zhang. Simultaneously optimizing perturbations and positions for blackbox adversarial patch attacks. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 2022. [1](#page-0-0)
- <span id="page-9-24"></span>[40] Yihan Wu, Xinda Li, Florian Kerschbaum, Heng Huang, and Hongyang Zhang. Towards robust dataset learning. *arXiv preprint arXiv:2211.10752*, 2022. [3](#page-2-0)
- <span id="page-9-17"></span>[41] Yifan Wu, Jiawei Du, Ping Liu, Yuewei Lin, Wenqing Cheng, and Wei Xu. Dd-robustbench: An adversarial robustness benchmark for dataset distillation. *arXiv preprint arXiv:2403.13322*, 2024. [1,](#page-0-0) [3](#page-2-0)
- <span id="page-9-25"></span>[42] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017. [5](#page-4-2)
- <span id="page-9-18"></span>[43] Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024. [1,](#page-0-0) [3](#page-2-0)
- <span id="page-9-22"></span>[44] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems (NeurIPS)*, 36, 2024. [3](#page-2-0)
- <span id="page-9-16"></span>[45] Xin Yuan, Shuyan Hu, Wei Ni, Xin Wang, and Abbas Jamalipour. Deep reinforcement learning-driven reconfigurable intelligent surface-assisted radio surveillance with a fixed-wing uav. *IEEE Transactions on Information Forensics and Security (TIFS)*, 2023. [1](#page-0-0)
- <span id="page-9-8"></span>[46] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning (ICML)*, pages 12674–12685, 2021. [1,](#page-0-0) [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11](#page-10-2)
- <span id="page-9-19"></span>[47] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pages 6514–6523, 2023. [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11](#page-10-2)
- <span id="page-9-4"></span>[48] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-0) [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11](#page-10-2)
- <span id="page-9-10"></span>[49] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 7856–7865, 2023. [1,](#page-0-0) [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11](#page-10-2)
- <span id="page-9-7"></span>[50] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Advances in Neural Information Processing Systems (NeurIPS)*, pages 9813–9827, 2022. [1](#page-0-0)
- <span id="page-9-12"></span>[51] Zheng Zhou, Ju Liu, and Yanyang Han. Adversarial examples are closely relevant to neural network models - a preliminary experiment explore. In *Advances in Swarm Intelligence*, pages 155–166, Cham, 2022. Springer International Publishing. [1](#page-0-0)
- <span id="page-9-5"></span>[52] Zheng Zhou, Hongbo Zhao, Guangliang Cheng, Xiangtai Li, Shuchang Lyu, Wenquan Feng, and Qi Zhao. Bacon: Bayesian optimal condensation framework for dataset distillation. *arXiv preprint arXiv:2406.01112*, 2024. [1,](#page-0-0) [2,](#page-1-1) [5,](#page-4-2) [6,](#page-5-2) [11,](#page-10-2) [12](#page-11-1)

<span id="page-10-2"></span><span id="page-10-0"></span>[53] Zheng Zhou, Hongbo Zhao, Ju Liu, Qiaosheng Zhang, Liwei Geng, Shuchang Lyu, and Wenquan Feng. Mvpatch: More vivid patch for adversarial camouflaged attacks on object detectors in the physical world. *arXiv preprint arXiv:2312.17431*, 2024. [1](#page-0-0)

# Supplementary Material

## **BEARD**: Benchmarking the Adversarial Robustness in Dataset Distillation

- Appendix [A](#page-10-1) offers an overview of BEARD, including the experimental setup, configurations, and implementation details.
- Appendix [B](#page-11-0) provides a detailed analysis of robustness evaluation from three key perspectives: unified benchmarks, varying IPC settings, and the impact of adversarial training.

<span id="page-10-1"></span>

## A. Overview of **BEARD**

### A.1. Experimental Setup

#### A.1.1. Datasets and Distillation Methods

Dataset. In our experiments, we use three standard image classification datasets: CIFAR-10 [\[19\]](#page-8-10), CIFAR-100 [\[19\]](#page-8-10), and TinyImageNet [\[8\]](#page-8-11). Each dataset has been selected for its relevance and complexity in the context of dataset distillation and adversarial robustness evaluation.

- CIFAR-10 [\[19\]](#page-8-10) contains 60,000 32  $\times$  32 color images in 10 classes, with 50,000 images for training and 10,000 for testing. The images are preprocessed to normalize pixel values to the range [0, 1].
- CIFAR-100 [\[19\]](#page-8-10) Similar to CIFAR-10 but with 100 classes, this dataset contains 60,000 images, divided into 50,000 training and 10,000 testing images. Each image is resized to  $32 \times 32$  pixels and normalized.
- TinyImageNet [\[8\]](#page-8-11) A subset of the large-scale ImageNet dataset, TinyImageNet contains 200 classes with 100,000 training images and 10,000 images each for validation and testing. Images are resized to  $64 \times 64$  pixels and normalized.

Dataset Distillation Methods. Our benchmark evaluates six representative distillation methods: DC [\[48\]](#page-9-4), DSA [\[47\]](#page-9-19), DM [\[46\]](#page-9-8), MTT [\[2\]](#page-6-0), IDM [\[49\]](#page-9-10), and BACON [\[52\]](#page-9-5). These methods represent a variety of optimization techniques commonly used in recent distillation research, including gradient matching [\[47,](#page-9-19) [48\]](#page-9-4), distribution matching [\[46,](#page-9-8) [49\]](#page-9-10), trajectory matching [\[2\]](#page-6-0), and Bayesian optimization-based approaches [\[52\]](#page-9-5).

- DC [\[48\]](#page-9-4) formulates dataset distillation as a bi-level optimization problem, focusing on matching the gradients of deep neural networks trained on the original dataset  $\mathcal T$ and the synthetic dataset  $S$ .
- **DSA** [\[47\]](#page-9-19) improves distillation by incorporating data augmentation, enabling the generation of more informative synthetic images, which enhances the performance of models trained with these augmentations.
- DM [\[46\]](#page-9-8) offers a straightforward yet impactful method for generating condensed images by aligning the feature distributions of synthetic images  $S$  with those of the original training set  $\mathcal T$  across multiple sampled embedding spaces.
- **MTT**  $[2]$  introduces trajectory matching as a distillation technique, condensing large datasets into smaller ones by aligning the training trajectories of models trained on both the synthetic  $S$  and original  $T$  datasets.
- **IDM** [\[49\]](#page-9-10) proposes a novel dataset condensation approach based on distribution matching, which proves to be both efficient and promising for dataset distillation tasks.
- **BACON** [\[52\]](#page-9-5) employs a Bayesian theoretical framework for dataset distillation, casting the problem as one of minimizing a risk function to significantly enhance distillation performance.

Training Details. Neural networks are trained from scratch on each distilled dataset, following a standardized training process across all experiments to ensure fair comparisons:

- Optimizer: The Adam optimizer is used with default settings, including a learning rate of 1e-4 and beta values of 0.9 and 0.999, ensuring stable and efficient optimization.
- Epochs: Models are trained for 1,000 epochs to ensure sufficient convergence and allow for the full learning potential of each distilled dataset.
- Batch Size: A batch size of 128 is employed to balance computational efficiency with model performance, optimizing resource usage without sacrificing accuracy.
- Model Selection: After training, the model with the highest validation accuracy on the original test set is selected and incorporated into the model pool for subsequent adversarial evaluations.

Adversarial Attack Methods. All attacks are implemented using the Torchattacks library [\[17\]](#page-8-17), which includes a comprehensive set of current adversarial attack methods. To ensure fair comparisons, we apply consistent parameters across different models. Our attack library encompasses a range of methods, including FGSM [\[12\]](#page-8-9), PGD [\[26\]](#page-9-20), C&W [\[1\]](#page-6-2), DeepFool [\[27\]](#page-9-21), AutoAttack [\[4\]](#page-6-3), and others. In the evaluation stage, adversarial perturbations are applied to assess the robustness of distilled datasets generated by various dis<span id="page-11-1"></span>tillation methods. Both targeted and non-targeted attacks are performed to evaluate adversarial robustness. To ensure consistency, all trained models are subjected to identical parameters, with a perturbation budget set to  $|\epsilon| = \frac{8}{255}$  for all methods except DeepFool and C&W.

- FGSM [\[12\]](#page-8-9): Generates adversarial examples by perturbing the input in the direction of the gradient of the loss function, with a perturbation size set to  $\epsilon = 8/255$ .
- **PGD** [\[26\]](#page-9-20): Extends FGSM by applying iterative steps to create adversarial examples. The perturbation budget and step size are adjusted for each dataset to enhance attack strength.
- C&W [\[1\]](#page-6-2): Focuses on optimizing adversarial examples to minimize perturbation while ensuring misclassification, providing a robust evaluation of model resilience.
- DeepFool [\[27\]](#page-9-21): Estimates the minimal perturbation required to induce misclassification, offering insights into the model's sensitivity to adversarial changes.
- AutoAttack [\[4\]](#page-6-3): Combines multiple strong attacks to provide a comprehensive evaluation of model robustness, ensuring thorough assessment of adversarial resilience.

We generate synthetic images using 1, 10, and 50 images per class (IPC) from three datasets: CIFAR-10, CIFAR-100, and TinyImageNet. To assess the effectiveness of our approach, we train models on these synthetic images and evaluate their performance on the original test sets. All methods utilize the default data augmentation strategies provided by the original authors to ensure consistency in distillation performance evaluation. For a fair comparison in generalization, we use the synthetic datasets released by the authors.

After training, we apply a range of adversarial attacks to the models trained on the synthetic datasets and report the mean accuracy across 5 runs, with models randomly initialized and trained for 1,000 epochs. The evaluation metrics employed in our experiments are designed to provide a comprehensive assessment of adversarial robustness. These metrics include:

- Single-Adversary Robustness Ratio (RRS): Measures how effectively the models resist adversarial attacks under a single adversary.
- Multi-Adversary Robustness Ratio (RRM): Assesses the model's robustness against attacks from multiple adversaries.
- Single-Adversary Attack Efficiency Ratio (AES): Quantifies the efficiency of single adversarial attacks in terms of the time required to succeed.
- Multi-Adversary Attack Efficiency Ratio (AEM): Evaluates the efficiency of attacks involving multiple adversaries.
- Comprehensive Robustness-Efficiency Index (CREI): Integrates both robustness and attack efficiency into a unified metric, offering a balanced evaluation of model performance under adversarial conditions.

#### A.2. Experimental Settings

Networks Architectures. In our experiments, we employed the ConvNet architecture [\[31\]](#page-9-26) for dataset distillation, following methodologies from prior studies, including DC-bench [\[5\]](#page-7-1) and BACON [\[52\]](#page-9-5). The ConvNet consists of three identical convolutional blocks followed by a final linear classifier. Each block features a convolutional layer with 128 kernels of size  $3 \times 3$ , instance normalization, ReLU activation, and average pooling with a stride of 2 and a pooling size of  $3 \times 3$ . This architecture configuration is consistent with the settings outlined in DC-bench and BACON, ensuring adherence to established practices in dataset distillation.

Evaluation Protocol. We generate synthetic images using 1, 10, and 50 images per class (IPC) from three datasets: CIFAR-10, CIFAR-100, and TinyImageNet. To assess the effectiveness of our approach, we train models on these synthetic images and evaluate their performance on the original test sets. All methods utilize the default data augmentation strategies provided by the original authors to maintain consistency in distillation performance evaluation. For fair comparisons in generalization, we employ the synthetic datasets released by the authors.

Following model training, we apply adversarial attacks to evaluate the robustness of the models trained on the various synthetic datasets. We report the mean accuracy across 5 runs, with models randomly initialized and trained for 1,000 epochs. The performance is measured using the condensed set as the primary evaluation metric.

#### A.3. Implementation Details

The BEARD benchmark builds upon the software foundation established by BACON [\[52\]](#page-9-5). For generating synthetic images in the dataset pool, we use the Stochastic Gradient Descent (SGD) optimizer with a learning rate of 0.2 and a momentum of 0.5, applied to synthetic datasets containing 1, 10, and 50 images per class (IPC). In the subsequent model training phase, we employ the same SGD optimizer, but adjust the learning rate to 0.01, momentum to 0.9, and apply a weight decay of 0.0005. The batch size is set to 256. All experiments, including both the generation of synthetic datasets and the training of models, are conducted using NVIDIA RTX 2080 Ti GPU clusters. Additionally, we provide a configuration JSON file to facilitate the convenient setup and management of experimental parameters.

<span id="page-11-0"></span>

### B. Analysis

#### B.1. Robustness Evaluation Using RR, AE, and CREI Metrics

The Table [1](#page-12-0) compares the performance of various dataset distillation methods using three key metrics: Multi-Adversary Robustness Ratio (RRM), Multi-Adversary

|      | Evaluation   |              | Dataset Distillation (%) |           |       |       |       |       |       |
|------|--------------|--------------|--------------------------|-----------|-------|-------|-------|-------|-------|
|      | Metric       | Attack Type  | Dataset                  | Full-size | DC    | DSA   | MTT   | DM    | IDM   |
| RRM  | Targ. Att.   | CIFAR-10     | 20.42                    | 30.79     | 45.22 | 36.00 | 46.01 | 32.35 | 36.83 |
|      |              | CIFAR-100    | 6.77                     | 33.11     | 43.97 | 36.06 | 39.32 | 30.79 | 31.81 |
|      |              | TinyImageNet | 22.99                    | 52.62     | 49.87 | 40.05 | 49.57 | /     | 47.57 |
|      | Untarg. Att. | CIFAR-10     | 20.42                    | 30.79     | 45.22 | 36.00 | 46.01 | 32.35 | 36.83 |
|      |              | CIFAR-100    | 6.77                     | 33.11     | 43.97 | 36.06 | 39.32 | 30.79 | 31.81 |
|      |              | TinyImageNet | 22.99                    | 52.62     | 49.87 | 40.05 | 49.57 | /     | 47.57 |
| AEM  | Targ. Att.   | CIFAR-10     | 29.39                    | 27.91     | 27.64 | 28.52 | 26.01 | 23.15 | 29.27 |
|      |              | CIFAR-100    | 29.59                    | 27.50     | 26.05 | 26.25 | 23.31 | 19.89 | 27.76 |
|      |              | TinyImageNet | 29.83                    | 28.80     | 28.97 | 29.26 | 29.55 | /     | 29.96 |
|      | Untarg. Att. | CIFAR-10     | 21.91                    | 21.53     | 18.97 | 19.21 | 22.13 | 23.89 | 21.53 |
|      |              | CIFAR-100    | 17.29                    | 16.06     | 12.26 | 13.23 | 12.83 | 14.44 | 13.34 |
|      |              | TinyImageNet | 17.31                    | 14.04     | 12.77 | 14.71 | 13.08 | /     | 12.09 |
| CREI | Targ. Att.   | CIFAR-10     | 24.91                    | 29.35     | 36.43 | 32.26 | 36.01 | 27.75 | 33.05 |
|      |              | CIFAR-100    | 18.18                    | 30.31     | 35.01 | 31.16 | 31.32 | 27.16 | 29.78 |
|      |              | TinyImageNet | 26.41                    | 40.71     | 39.42 | 34.66 | 39.56 | /     | 38.76 |
|      | Untarg. Att. | CIFAR-10     | 25.12                    | 26.70     | 27.75 | 26.26 | 28.32 | 28.46 | 27.20 |
|      |              | CIFAR-100    | 18.60                    | 22.40     | 20.40 | 19.65 | 19.78 | 20.36 | 19.30 |
|      |              | TinyImageNet | 15.15                    | 20.46     | 15.67 | 16.13 | 15.51 | /     | 15.24 |

<span id="page-12-0"></span>Table 1. Performance comparison of dataset distillation methods under various adversarial attacks. Metrics include Multi-Adversary Robustness Ratio (RRM), Multi-Adversary Attack Efficiency Ratio (AEM), and Comprehensive Robustness-Efficiency Index (CREI). The Targ. Att. and Untarg. Att. denote the Targeted Attack and Untargeted Attack, respectively.

Attack Efficiency Ratio (AEM), and Comprehensive Robustness-Efficiency Index (CREI). The evaluation covers both targeted and untargeted adversarial attacks across three datasets: CIFAR-10, CIFAR-100, and TinyImageNet.

Targeted Attacks. Dataset distillation methods demonstrate substantial improvements in robustness compared to full-size models. For example, in CIFAR-10, DM achieves a RRM of 46.01%, a significant increase from the 20.42% of the full-size model. Similarly, DSA achieves a RRM of 45.22%. These enhancements are evident across CIFAR-100 and TinyImageNet, where DM and DSA continue to outperform full-size models. For instance, in CIFAR-100, DM has a RRM of 39.32%, compared to 6.77% for the fullsize model. On TinyImageNet, DM achieves a RRM of 49.57%, compared to 22.99% for the full-size model. Despite these improvements in robustness, distillation methods like DC and DSA show a slight reduction in AEM values. For example, in CIFAR-10, DC has an AEM of 27.91% and DSA has 27.64%, compared to 29.39% for the full-size model. This indicates a trade-off between robustness and efficiency. The CREI scores further illustrate this balance: DM and DSA achieve high CREI values, with DM reaching 36.01% in CIFAR-10 and DSA 36.43%, showcasing their effective trade-off between robustness and efficiency.

Untargeted Attacks. The robustness improvements with distillation methods are less pronounced compared to targeted attacks. For instance, in CIFAR-10, while DM and DSA still offer high RRM (45.22% and 46.01%, respectively), the gap between these methods and full-size models is narrower. The AEM values for distillation methods are generally lower, indicating that these methods require less time and computational resources for adversarial attacks compared to full-size models. For example, the AEM for DC in CIFAR-10 under untargeted attacks is 21.53%, compared to 21.91% for the full-size model. Similarly, DSA shows an AEM of 18.97% in CIFAR-10, which is lower than the 21.91% for the full-size model. The CREI scores reflect this trend, with methods like DM and DSA achieving reasonable CREI values, such as 27.75% for DM in CIFAR-10, demonstrating a balanced performance between robustness and efficiency despite the slight trade-off in robustness.

#### B.2. Robustness Evaluation with Diverse IPCs

The robustness evaluation, conducted across targeted and untargeted attacks, reveals two key observations: 1) increasing the number of images per class (IPC) decreases adversarial robustness, as evidenced by lower CREI values across various methods and datasets; and 2) increasing the dataset scale enhances adversarial robustness when using dataset distillation methods, particularly when comparing distilled

| Dataset      | IPC       | Dataset Distillation (%) |       |       |       |       |       |
|--------------|-----------|--------------------------|-------|-------|-------|-------|-------|
|              |           | DC                       | DSA   | MTT   | DM    | IDM   | BACON |
| CIFAR-10     | Full-size | 24.91                    |       |       |       |       |       |
|              | 1         | 27.19                    | 26.11 | 25.67 | 24.87 | 21.92 | 24.62 |
|              | 10        | 25.73                    | 23.31 | 22.21 | 20.90 | 20.41 | 20.83 |
|              | 50        | 22.36                    | 21.75 | 19.82 | 19.70 | 17.11 | 19.10 |
| CIFAR-100    | Full-size | 18.18                    |       |       |       |       |       |
|              | 1         | 28.23                    | 29.74 | 27.65 | 28.30 | 24.44 | 28.71 |
|              | 10        | 26.23                    | 23.58 | 23.23 | 23.97 | 18.47 | 20.38 |
|              | 50        | 19.40                    | 20.64 | 21.51 | 19.70 | 16.86 | 19.86 |
| TinyImageNet | Full-size | 26.41                    |       |       |       |       |       |
|              | 1         | 29.94                    | 29.08 | 29.33 | 30.44 | /     | 30.50 |
|              | 10        | 30.46                    | 30.28 | 29.93 | 29.30 | /     | 28.18 |
|              | 50        | 29.10                    | 28.89 | /     | 28.72 | /     | /     |

<span id="page-13-0"></span>Table 2. Comparison of adversarial robustness using CREI for different dataset distillation methods under targeted attacks across various datasets and IPC settings.

<span id="page-13-1"></span>Table 3. Comparison of adversarial robustness using CREI for different dataset distillation methods under untargeted attacks across various datasets and IPC settings.

| Dataset      | IPC       | Dataset Distillation (%) |       |       |       |       |       |
|--------------|-----------|--------------------------|-------|-------|-------|-------|-------|
|              |           | DC                       | DSA   | MTT   | DM    | IDM   | BACON |
| CIFAR-10     | Full-size | 25.12                    |       |       |       |       |       |
|              | 1         | 41.11                    | 36.59 | 26.25 | 42.21 | 30.18 | 25.79 |
|              | 10        | 24.85                    | 23.90 | 23.40 | 26.73 | 25.60 | 27.09 |
|              | 50        | 22.50                    | 27.00 | 25.06 | 26.61 | 29.59 | 28.48 |
| CIFAR-100    | Full-size | 18.60                    |       |       |       |       |       |
|              | 1         | 23.87                    | 20.81 | 17.91 | 24.32 | 20.85 | 19.76 |
|              | 10        | 16.44                    | 20.83 | 19.97 | 20.28 | 21.43 | 20.78 |
|              | 50        | 19.46                    | 20.67 | 20.54 | 20.10 | 21.34 | 20.11 |
| TinyImageNet | Full-size | 15.15                    |       |       |       |       |       |
|              | 1         | 22.86                    | 18.41 | 20.98 | 22.20 |       | 17.88 |
|              | 10        | 17.33                    | 15.50 | 15.90 | 15.59 |       | 16.18 |
|              | 50        | 14.96                    | 15.50 |       | 15.30 |       |       |

datasets to full-size datasets.

Targeted Attacks. In the context of targeted attacks, increasing IPC values typically leads to reduced adversarial robustness, as seen from the declining CREI scores. For instance, on CIFAR-10, methods like DC and DSA perform best at  $IPC = 1$ , showing strong robustness, but their performance decreases with larger IPC values. Similarly, for CIFAR-100, BACON outperforms other methods at IPC = 1, though its robustness diminishes at higher IPC levels. Importantly, as the dataset scale increases, the advantage of dataset distillation methods over Full-size datasets becomes more pronounced. For example, in TinyImageNet at IPC =

1, DC and DSA maintain high CREI scores, surpassing the Full-size model, emphasizing that dataset distillation methods can achieve better robustness with smaller dataset sizes under targeted attacks. The detailed results are presented in Table [2.](#page-13-0)

Untargeted Attacks. Under untargeted attacks, the trend of decreasing robustness with increasing IPC is also observed, but the effects are less severe compared to targeted attacks. For CIFAR-10, DC and DM perform strongly at IPC = 1, with DC achieving the highest CREI score. Notably, as the dataset size increases (e.g., TinyImageNet), the gap in robustness between distillation methods and Full-

<span id="page-14-1"></span><span id="page-14-0"></span>Table 4. Comparison of adversarial robustness using CREI for different dataset distillation methods with and without adversarial training under targeted and untargeted attacks.

| Attack<br>Type | Adversarial<br>Training | Dataset Distillation(%) |       |       |       |       |       |       |
|----------------|-------------------------|-------------------------|-------|-------|-------|-------|-------|-------|
|                |                         | Full-size               | DC    | DSA   | MTT   | DM    | IDM   | BACON |
| Targeted       | w/ AT                   | 50.54                   | 52.30 | 55.56 | 50.96 | 57.21 | 54.67 | 56.18 |
|                | w/o AT                  | 24.91                   | 29.35 | 36.43 | 32.26 | 36.01 | 27.75 | 33.05 |
| Untargeted     | w/ AT                   | 41.33                   | 37.39 | 37.59 | 33.13 | 37.34 | 39.05 | 37.25 |
|                | w/o AT                  | 25.12                   | 26.70 | 27.75 | 26.26 | 28.32 | 28.46 | 27.20 |

size datasets becomes more evident. For instance, DC consistently outperforms the Full-size model in TinyImageNet at IPC  $= 1$ , while showing comparable or better robustness even at higher IPC values. This reinforces the observation that dataset distillation methods not only excel with fewer images per class but also offer greater robustness in larger datasets, especially when facing untargeted attacks. Detailed results are provided in Table [3.](#page-13-1)

#### B.3. Robustness Evaluation with Adversarial Training

Targeted Attacks. Table [4](#page-14-1) shows that adversarial training notably enhances robustness under targeted attacks. Methods like BACON and DM achieve high CREI values of 56.18% and 57.21%, respectively, indicating superior robustness compared to other methods. The Full-size dataset, despite being complete, exhibits lower robustness with a CREI of 50.54%, underscoring the effectiveness of distillation techniques in improving adversarial resilience.

Untargeted Attacks. In the context of untargeted attacks, the Full-size dataset achieves a CREI of 41.33%, slightly outperforming other methods. BACON and DM demonstrate similar performance with CREI values of 37.25% and 37.34%, respectively. Without adversarial training, all methods show reduced robustness, with DSA and DM maintaining relatively higher CREI values of 27.75% and 28.32%. These findings highlight the crucial role of adversarial training in enhancing robustness and confirm the advantages of dataset distillation methods in enhancing adversarial robustness.

#### B.4. Trade-off Between Adversarial Robustness and Model Performance

In our analysis, we also investigate the trade-off between adversarial robustness and model performance. It is important to note that while Dataset Distillation (DD) methods enhance adversarial robustness, they may also lead to a reduction in model performance on clean data. Specifically, dataset distillation techniques involve extracting key features from the original dataset, which can introduce nonrobust features associated with certain classes. This process

can be seen as a form of adversarial training, as it exposes the model to these non-robust features. As discussed by Ilyas *et al.* [\[16\]](#page-8-15), distilling a dataset essentially incorporates both robust and non-robust features from the original data, thereby improving adversarial robustness but also potentially diminishing performance on clean data. This explains the observed trade-off where DD methods achieve higher robustness at the cost of some loss in model performance. In our experiments, this balance between robustness and performance is evident. Models trained on distilled datasets demonstrate improved resilience to adversarial attacks, but also experience a slight degradation in accuracy on clean, non-adversarial inputs. As shown in Table [1,](#page-12-0) the distilled datasets generated by DD methods exhibit greater robustness to adversarial attacks compared to models trained on the original datasets. However, this improved robustness comes at the cost of reduced performance on clean, unperturbed data.