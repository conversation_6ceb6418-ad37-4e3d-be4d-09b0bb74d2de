# Backdoor Graph Condensation

Ji<PERSON><PERSON><sup>1,2</sup>, <PERSON><PERSON><sup>1,3</sup>, <PERSON><PERSON><PERSON><sup>1,2</sup>, <PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><PERSON><PERSON><sup>1</sup>\*, <PERSON><sup>2</sup>, <PERSON><sup>1</sup>

 $1$  Guangdong Provincial Key Laboratory of Brain-Inspired Intelligent Computation, SUSTech, Shenzhen, China

<sup>2</sup> The Hong Kong Polytechnic University, Hong Kong, China

<sup>3</sup> Hong Kong University of Science and Technology, Hong Kong, China

<sup>4</sup> University of Science and Technology of China, Hefei, China

*Abstract*—Graph condensation has recently emerged as a prevalent technique to improve the training efficiency for graph neural networks (GNNs). It condenses a large graph into a small one such that a GNN trained on this small synthetic graph can achieve comparable performance to a GNN trained on the large graph. However, while existing graph condensation studies mainly focus on the best trade-off between graph size and the GNNs' performance (model utility), they overlook the security issues of graph condensation. To bridge this gap, we first explore backdoor attack against the GNNs trained on the condensed graphs.

We introduce an effective backdoor attack against graph condensation, termed BGC. This attack aims to (1) preserve the condensed graph quality despite trigger injection, and (2) ensure trigger efficacy through the condensation process, achieving a high attack success rate. Specifically, BGC consistently updates triggers during condensation and targets representative nodes for poisoning. Extensive experiments demonstrate the effectiveness of our attack. BGC achieves a high attack success rate (close to 1.0) and good model utility in all cases. Furthermore, the results against multiple defense methods demonstrate BGC's resilience under their defenses. Finally, we analyze the key hyperparameters that influence the attack performance. Our code is available at: https://github.com/JiahaoWuGit/BGC.

*Index Terms*—Graph Condensation, Backdoor Attacks

### I. INTRODUCTION

Graph neural networks (GNNs) have been widely deployed in various fields involving graph-structured data, such as social computing [\[1\]](#page-11-0), [\[2\]](#page-12-0), drug discovery [\[3\]](#page-12-1), [\[4\]](#page-12-2) and recommendation [\[5\]](#page-12-3)–[\[7\]](#page-12-4). Large-scale graphs are instrumental in achieving state-of-the-art GNNs performance [\[8\]](#page-12-5)–[\[11\]](#page-12-6). However, the vast scale of these graphs imposes considerable demands on storage and computational resources.

Graph condensation [\[12\]](#page-12-7)–[\[18\]](#page-12-8) is an emerging solution to the aforementioned challenges. Graph condensation compresses a large graph into a smaller one, enabling GNNs trained on it to achieve performance comparable to those trained on the original graph, as shown in Figure [2.](#page-1-0) For instance, GCond [\[19\]](#page-12-9) condenses the Reddit dataset (153,932 training nodes) into only 154 synthetic nodes. GCond reduces the number of training nodes by 99.09% while retaining 95.3% of the original test performance. Regarding the remarkable achievements, we expect graph condensation to be provided as a service [\[20\]](#page-12-10)–[\[22\]](#page-12-11). Such service can ease storage and computational demands for researchers and organizations.

Despite the success, the parameter optimization process in condensation inherits neural networks' security vulner-

<span id="page-0-0"></span>**CLEAN MODEL INAIVE POISON II OUR METHOD** 50 60 70 80 Cora  $CTA$   $(\%)$  $\overline{0}$ 20 40 60 Citeseer CTA (%)

Fig. 1: Attack Performance Comparison: Naively Poisoning Condensed Graphs *vs* Our Method. Here, CTA denotes the test accuracy on clean graphs. Naive Poison degrades condensed graph quality, thus reducing GNN utility.

abilities [\[23\]](#page-12-12)–[\[28\]](#page-12-13), while existing graph condensation research [\[13\]](#page-12-14), [\[15\]](#page-12-15), [\[16\]](#page-12-16), [\[18\]](#page-12-8), [\[19\]](#page-12-9) primarily focuses on achieving best trade-off between the graph size reduction and the model utility. The security issues of graph condensation haven't been investigated. This is highly concerning given that (i) graph-structured data is increasingly used in securitysensitive domains such as malware analysis [\[29\]](#page-12-17), fraud detection [\[30\]](#page-12-18), and drug discovery [\[31\]](#page-12-19), and (ii) data providers like Scale AI facilitate machine learning model development, introducing potential security risks while their trustworthiness is in question. To bridge this research gap, we propose the task of backdoor graph condensation, aiming to investigate the security issue of graph condensation.

Graph backdoor attacks [\[27\]](#page-12-20), [\[32\]](#page-12-21)–[\[34\]](#page-12-22), which aim to investigate the vulnerability of GNNs, are closely related to the new task. These attacks utilize triggers to poison input samples, causing GNNs to produce adversary-predetermined malicious outputs. Traditional backdoor methods inject triggers directly into the graph during GNN training. However, such approaches are ineffective for condensed graphs due to their small size, which makes triggers easy to detect and remove. For example, Reddit's condensed graph contains only 154 nodes, rendering abnormal edge detection straightforward and limiting trigger efficacy [\[22\]](#page-12-11), [\[33\]](#page-12-23), [\[35\]](#page-12-24). Furthermore, directly injecting triggers into condensed graphs significantly compromises the GNN utility. As illustrated in Figure [1,](#page-0-0) direct injection into the condensed graph (Naive Poison) substantially undermines the GNNs utility (CTA), compared to GNNs trained on a clean graph (Clean Model). Therefore, directly adapting existing graph backdoor attacks to poison the condensed graph is ineffective, raising a critical question: *How to inject backdoors*

<sup>\*</sup> Corresponding author. Email: <EMAIL>.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image compares 'Normal Graph Condensation' and 'Backdoored Graph Condensation'. The normal process involves condensing an original graph into a condensed graph, then training well-trained GNNs on the condensed graph, which results in comparable performance to GNNs trained on the original graph. The backdoored process starts with an original graph, condenses it, and then injects a trigger into the condensed graph, creating a poisoned condensed graph. Training GNNs on this poisoned graph leads to backdoored GNNs that misclassify target nodes, depicted by a sad face icon, when a node with the trigger is present.

Fig. 2: Normal Graph Condensation *vs* Backdoored Graph Condensation.

*into GNNs without directly poisoning the condensed graph?*

To address this, we first summarize the primary objectives for backdoor attacks against graph condensation: 1) high GNN utility, which aims to maintain the condensed graph quality to preserve the GNN utility; and 2) high attack effectiveness, which emphasizes enduring trigger influence throughout condensation to ensure the effectiveness. However, pursuing these two objectives is challenging. Specifically, the condensed graph quality heavily relies on the original graph and the condensation method, complicating trigger design. Additionally, preserving trigger effectiveness is intractable. Consistent updates to the condensed graph reduce trigger efficacy, while static triggers fail to poison dynamic nodes.

To overcome the challenges, we propose the first Backdoor attack against Graph Condensation, BGC. We envision the attacker as the graph condensation service provider as shown in Figure [2.](#page-1-0) Specifically, instead of direct injection into the condensed graph, BGC inserts triggers into the original large graph and optimize them iteratively throughout the condensation process to ensure their retention. Further, to preserve condensed graph quality, trigger attachments are limited to a controlled number of high-influence nodes [\[36\]](#page-12-25), [\[37\]](#page-12-26). We validate BGC's effectiveness through extensive experiments on four benchmark datasets and four representative condensation methods. Empirical results show that our method BGC achieves high model utility and achieves a promising attack success rate. Besides, ablation studies confirm that BGC is robust in different settings. The evaluation result with two representative graph defense methods indicates that they are invalid in defending against BGC. Our contributions are:

- Objective. We introduce backdoor graph condensation, a task that embeds malicious information into condensed graphs to backdoor GNNs, uncovering vulnerabilities and associated risks in graph condensation.
- Methodology. We propose the first backdoor attack against graph condensation, where we devise a module to select representative nodes for trigger injection in the original graph and update the triggers throughout the condensation process. This enables effective backdoor attacks on GNNs trained on the condensed graph.
- **Experiment.** We evaluate BGC across various settings, demonstrating its consistent effectiveness (near 100% attack success rate and high GNNs utility). Besides, the evaluation with two leading graph defense methods indicates that our attacks can surpass these defense methods and module ablation study helps justify the design effectiveness.

## II. PRELIMINARIES

In this section, we introduce some notations and present the preliminaries on graph condensation and graph backdoor attacks. We denote a graph  $G = {\mathbf{A}, \mathbf{X}, \mathbf{Y}}$ , where  $\mathbf{A} \in$  $\mathbb{R}^{N \times N}$  is the adjacency matrix, N is the number of nodes,  $X \in \mathbb{R}^{N \times d}$  is the *d*-dimensional node feature matrix and  $\mathbf{Y} \in \{0, 1, ..., C-1\}^N$  denotes the node labels over C classes. Here,  $A_{ij} = 1$  if node  $v_i$  and node  $v_j$  are connected; otherwise  $\mathbf{A}_{ij} = 0$ . We denote the node set by  $\mathcal{V} = \{v_1, ..., v_N\}$ . In this paper, we focus on node classification tasks.

### A. Graph Condensation

Goal: Graph condensation [\[15\]](#page-12-15), [\[18\]](#page-12-8), [\[19\]](#page-12-9), [\[38\]](#page-12-27) aims to learn a small, synthetic graph dataset  $S = \{A', X', Y'\}.$ where  $\mathbf{A}' \in \mathbb{R}^{N' \times N'}$ ,  $\mathbf{X}' \in \mathbb{R}^{N' \times d}$ ,  $\mathbf{Y}' \in \{0, 1, ..., C - 1\}^{N'}$ and  $N' \ll N$ . A GNN trained on the condensed graph S is expected to achieve comparable performance to the one trained on the original graph  $\mathcal{G}$ .

Formulation: Graph condensation can be formulated as a bi-level optimization problem [\[5\]](#page-12-3), [\[13\]](#page-12-14), [\[18\]](#page-12-8), [\[19\]](#page-12-9), [\[39\]](#page-12-28), iteratively updating the synthetic graph  $S$  and the model parameters  $\theta$ . The optimization objective is formulated as:

<span id="page-1-1"></span>
$$
\min_{\mathcal{S}} \mathcal{L}(f(\mathcal{G}|\theta_{\mathcal{S}}), \mathbf{Y}) \text{ s.t. } \theta_{\mathcal{S}} = \arg\min_{\theta} \mathcal{L}(f(\mathcal{S}|\theta), \mathbf{Y}'), \qquad (1)
$$

where  $f(\cdot|\theta_{\mathcal{S}})$  denotes the GNN model parameterized with  $\theta_{\mathcal{S}}$ ,  $\theta_{\mathcal{S}}$  denotes the GNN is trained on the synthetic graph  $S$ , and  $\mathcal L$  is the task-related loss utilized for GNN training (i.e., cross entropy loss). In pursuit of this objective, different condensation methods may have different designs for the optimization of the synthetic graph and one of the most prevalent methods adopted by previous works is the gradient matching mechanism [\[13\]](#page-12-14), [\[14\]](#page-12-29), [\[19\]](#page-12-9), [\[39\]](#page-12-28). Concretely, they endeavor to minimize the discrepancy between GNN gradients w.r.t. the original graph  $G$  and w.r.t. the condensed graph  $S$  [\[14\]](#page-12-29). Therefore, the GNNs trained on S will converge to similar states and achieve comparable performance to those trained on G.

<span id="page-1-2"></span>

### B. Graph Backdoor Attack

Goal: The attacker injects triggers into the training data of the target GNN model and attaches triggers to target nodes at the test time, leading to misclassification on target nodes while maintaining normal behavior for clean nodes without triggers. At the setting of graph backdoor attack [\[33\]](#page-12-23), the data is available for the attacker while the information of the target GNN models is unknown to the attacker. The effectiveness of a backdoor attack is typically evaluated by attack success rate (ASR) and clean test accuracy (CTA) [\[22\]](#page-12-11), [\[33\]](#page-12-23), [\[40\]](#page-12-30). The ASR measures its success rate in misleading GNN to predict the given triggered samples to the target label. The CTA evaluates the utility of the model given clean samples.

**Formulation:** Given a clean graph  $\mathcal{G} = \{A, X, Y\}$  with node set  $V$ , the goal of graph backdoor attack [\[33\]](#page-12-23) is to learn an adaptive trigger generator  $f_g : v_i \rightarrow g_i$  and effectively select a set of nodes  $V_P$  within budget to attach triggers and labels so that the GNN  $f$  trained on the poisoned graph  $\mathcal{G}_P$ will classify the test node attached with the trigger to the target class  $y_t$  by solving:

$$
\min_{\mathcal{V}_P, \theta_g} \sum_{v_i \in \mathcal{V}_U} l(f(a(\mathcal{G}_C^i, g_i | \theta^*)), y_t),
$$
\n*s.t.*  $\theta^* = \arg \min_{\theta} \sum_{v_i \in \mathcal{V}_C} l(f(\mathcal{G}_C^i | \theta), y_i) + \sum_{v_i \in \mathcal{V}_P} l(f(a(\mathcal{G}_C^i, g_i) | \theta), y_t),$   
\n $\forall v_i \in \mathcal{V}_P \cup \mathcal{V}_T, |g_i| < \Delta_g$ , and  $|\mathcal{V}_P| \leq \Delta_P$ , (2)

where we have:

 $V_C = V \backslash V_P$ : the clean node set;

 $V_T$ : test node set;

 $V_U \subseteq V$ : node set for updating triggers.

 $\theta_g$ : parameters of the adaptive trigger generator  $f_g$ ;

 $g_i$ : trigger of node  $v_i$ ;

 $\Delta_q$ : budget of trigger size;

 $\Delta_P$ : budget of poisoned node set  $\mathcal{V}_P$ ;

 $\mathcal{G}_C^i$ : clean computation graph of node  $v_i$ ;

 $a(\cdot)$ : the operation of trigger attachment;

 $l(\cdot)$ : node-level form of the cross-entropy loss;

While the prediction is given based on the computation graph of the node, the clean prediction on node  $v_i$  can be written as  $f(\mathcal{G}_C^i|\theta)$ . For a node  $v_i$  attached with the adaptive trigger  $g_i$ , the predictive lable will be given by  $f_{\theta}(a(\mathcal{G}_{C}^{i}, g_{i}))$ .

To be consistent with Eq [1,](#page-1-1)we have following definition:

$$
\mathcal{L}(f(\mathcal{G}|\theta), \mathbf{Y}) = \sum_{v_i \in \mathcal{V}} l(f(\mathcal{G}_C^i|\theta), y_i),
$$

## III. PROBLEM FORMULATION

Attack Settings. We consider a practical scenario, where the attacker is envisioned as the malicious graph condensation provider, supplying condensed graphs. Besides, we assume that the attacker is accessible to the dataset but lack the information of the target model. This stems from that the provider solely delivering condensed graphs, without knowledge of the model to be trained.

Attacker's Goal. The attacker's goal is to inject malicious information into the condensed graph dataset and consequently backdoor the GNNs trained on the condensed graph. However, as discussed in the previous section, the size of the condensed graph is significantly smaller than the original graph, which will result that the direct injection of triggers into the condensed graphs renders those triggers easily detectable and it can significantly influence the utility of GNN [\[22\]](#page-12-11), [\[33\]](#page-12-23). Therefore, we turn to a more practical strategy, injecting triggers into the original graph throughout the condensation process to carry out the attacks.

Formulation of Backdoor Graph Condensation. Given a clean graph  $G = \{A, X, Y\}$  with node set V, we aim to learn an adaptive trigger generator  $f_a : v_i \rightarrow g_i$  and effectively select a set of nodes  $V_P \subset V$  within budget to attach triggers and labels. Then, we can denote the poisoned graph by  $\mathcal{G}_P = \{ \mathbf{A}^P, \mathbf{X}^P, \mathbf{Y}^P \}$ . The condensed graph produced from

 $\mathcal{G}_P$  is denoted by  $\mathcal{S} = {\mathbf{A}', \mathbf{X}', \mathbf{Y}'}$ . Thus, we learn the trigger generator by solving the following problem:

<span id="page-2-1"></span>
$$
\min_{\mathcal{G}_P} \hat{\mathcal{L}}(f(\mathcal{G}_P | \theta_{\mathcal{S}^*}), \mathbf{Y}^P)
$$
  
s.t.  $\theta_{\mathcal{S}^*} = \underset{\theta}{\arg \min} \mathcal{L}(f(\mathcal{S} | \theta), \mathbf{Y}'),$  (3)  
s.t.  $\mathcal{S}^* = \underset{\mathcal{S}}{\arg \min} \mathcal{L}'(f(\mathcal{S} | \theta_{\mathcal{S}}), \mathbf{Y}^P),$ 

where  $\hat{\mathcal{L}}$ ,  $\mathcal{L}$ , and  $\mathcal{L}'$  are the graph-level form of loss, which will be elaborated in the following paragraphs.

<span id="page-2-0"></span> $\hat{\mathcal{L}}$  is cross-entropy loss and it is utilized to optimize trigger generator, aiming to mislead GNN  $f$  trained on  $S$  to classify the test nodes attached with the trigger to the target class  $y_t$ and it is formulated as following:

$$
\hat{\mathcal{L}}(f(\mathcal{G}_P|\theta_{\mathcal{S}^*}), \mathbf{Y}^P) = \sum_{v_i \in \mathcal{V}_U} l(f(a(\mathcal{G}_C^i, g_i)|\theta_{\mathcal{S}^*}), y_t),
$$
\n
$$
\forall v_i \in \mathcal{V}_P \cup \mathcal{V}_T, \quad |g_i| < \Delta_g \text{ and } |\mathcal{V}_P| \leq \Delta_P,
$$
\n
$$
(4)
$$

where  $\theta_g$ ,  $y_t$ ,  $f_g$ ,  $\mathcal{G}_C^i$ ,  $v_i$ ,  $a(\cdot)$ ,  $l(\cdot)$ ,  $\mathcal{V}_T$  and  $\mathcal{V}_U$  are coherent with  $Eq.(2)$  $Eq.(2)$ .

 $\mathcal L$  is cross-entropy loss and it is utilized to train GNN f on the condensed graph  $S$ :

$$
\mathcal{L}\left(f(\mathcal{S}|\theta), \mathbf{Y}'\right) = \sum_{v_i \in \mathcal{V}'} l\left(f(\mathcal{G}_C^i|\theta), y_i\right),\tag{5}
$$

where  $V'$  is the node set of synthetic graph  $S$ .

 $\mathcal{L}'$  is the loss for graph condensation and we introduce the gradient matching mechanism [\[13\]](#page-12-14), [\[15\]](#page-12-15), [\[18\]](#page-12-8), [\[19\]](#page-12-9) in this paper. Therefore, the optimization of  $S$  can be re-written as:

$$
\mathcal{L}'(f(\mathcal{G}^P|\theta_{\mathcal{S}}), \mathbf{Y}^P) = D(\nabla_{\theta} \mathcal{L}(f(\mathcal{S}|\theta_t), \mathbf{Y}'), \nabla_{\theta} \mathcal{L}(f(\mathcal{G}|\theta_t), \mathbf{Y})), \quad (6)
$$

where  $D(\cdot, \cdot)$  is a distance function, T is the number of steps of the whole GNN's training trajectory.

### IV. METHODOLOGY

#### A. Overview

Motivation. As shown in Figure [1,](#page-0-0) directly injecting triggers into the condensed graph can significantly compromise the GNN utility (CTA). Therefore, this approach cannot carry out effective backdoor attacks. Instead, we propose to inject triggers into the original graph. Examining the bi-level pipeline of graph condensation [\[13\]](#page-12-14), [\[18\]](#page-12-8), [\[19\]](#page-12-9), [\[41\]](#page-12-31), we observe that the condensed graphs are consistently optimized. If triggers are pre-defined and remain unchanged, they may not be preserved throughout the updating process [\[22\]](#page-12-11), [\[35\]](#page-12-24), failing to inject malicious information into the condensed graph. Thus, we search to devise an advanced attack, where triggers are updated during condensation, preserving their effectiveness. Further, to avoid undermining the quality of the condensed graph, we set a budget to limit the number of poisoned nodes in the original graph. To maximize the effectiveness of triggers within this limit, we propose to attach triggers to the representative nodes.

The interplay of consistently updated triggers and representative poisoning ensures potent, stealthy attacks ( $\approx 100\%$ ) success rate) without degrading model performance on clean data. These two techniques together challenges the perceived

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This figure illustrates the overview of backdoor graph condensation. The process starts with an original graph G, which is processed by a node selector (f\_sel) to identify nodes (V\_P). These selected nodes are then used by a trigger generator (f\_g) to create a poisoned graph G\_P. The poisoned graph G\_P is then condensed using a function f\_c into a series of poisoned condensed graphs S(0), S(1), ..., S(n). Finally, these condensed graphs are used to train backdoored GNNs. The figure also includes a legend explaining the symbols used: a dashed arrow for 'Update Trigger Generator', a plus sign for 'Poisoned Node', a red three-node structure for 'Trigger', and a gray arrow for 'Attach Trigger'.

Fig. 3: Overview of Backdoor Graph Condensation.

security of condensed graphs, revealing risks in outsourced ML services, urging defenses tailored to dynamic graph condensation and rethinking node vulnerability.

Overview of BGC. In this section, we present the details of our proposed attack BGC, illustrated in Figure [3.](#page-3-0) Initially, a poisoned node selector  $f_{sel}$  is trained on the original graph G and selects representative nodes as poisoned nodes  $V_P$ through a metric that measures the representativeness of each node [\[36\]](#page-12-25), [\[37\]](#page-12-26). Subsequently, an adaptive trigger generator  $f_q$  generates triggers for the selected poisoned nodes  $V_P$ , making up part of the poisoned graph  $\mathcal{G}_P$ . Finally, the poisoned condensed graph  $S_P$  is generated based on  $\mathcal{G}_P$ .

Optimization. As formulated in Eq [3,](#page-2-1) generating effective triggers and corresponding backdoored condensed graphs is a tri-level optimization problem, which is both challenging and computationally expensive. To address this issue, we update the triggers before updating the condensed graph at each epoch. This significantly reduces the number of iterations and ensures consistent updates to the toxic triggers for sustained effectiveness. Since the information of the target model is unknown, the trigger generator and condensed graph are optimized towards successfully attacking a surrogate GCN model  $f_c$ . The optimization is summarized in Algorithm [1.](#page-4-0)

#### B. Poisoned Node Selection

In this section, we delve into poisoned node selection. Previous studies [\[36\]](#page-12-25), [\[37\]](#page-12-26) indicate that representative samples play a pivotal role in the gradient of dataset condensation, exerting a substantial influence on the quality of condensed datasets. Additionally, injecting too many triggers into the graphs can affect the GNN utility. Therefore, to facilitate successful backdoor attacks, we limit the budget of poisoning and we propose to select representative nodes to poison. Then, we introduce the selection method in detail.

Specifically, we train a GCN model  $f_{sel}$  with the original graph  $G = \{A, X, Y\}$  to obtain the representations of nodes:

$$
\mathbf{H}_{sel} = \text{GCN}_{sel}(\mathbf{A}, \mathbf{X}), \quad \hat{\mathbf{Y}} = \text{softmax}(\mathbf{W}_{sel} \cdot \mathbf{H}_{sel}), \quad (7)
$$

where  $\mathbf{W}_{sel}$  is the weight matrix for classification. The training objective of  $f_{sel}$  can be formulated as:

$$
\min_{\theta_{sel}, \mathbf{W}_{sel}} \sum_{v_i \in \mathcal{V}} l(\hat{y_i}, y_i), \tag{8}
$$

where  $\theta_{sel}$  is the parameters of node selector  $f_{sel}$ ,  $l(\cdot)$  is the cross-entropy loss,  $y_i$  is node label and  $\hat{y}_i$  is the prediction.

To guarantee the diversity of the representative nodes [\[33\]](#page-12-23), [\[36\]](#page-12-25), we separately apply K-Means to cluster on each class  $c$ . Nodes nearer to the cluster centroid are more representative and nodes nearest to the centroid may have a high degree. Assigning malicious labels to high-degree nodes could lead to substantial performance degradation since the negative effects will be propagated to a wide range of neighbors. Therefore, we adopt the metric that can balance the representativeness and the negative effects on GNN utility [\[33\]](#page-12-23). The metric score can be calculated as follows:

<span id="page-3-1"></span>
$$
m(v_i) = ||\mathbf{h}_i^k - \mathbf{h}_c^k||_2 + \lambda \cdot deg(v_i^k),
$$
\n(9)

where  $\lambda$  is the balance hyperparameter,  $h_c^k$  is the representation of the centroid of the k-th cluster, and  $\mathbf{h}_i^k$  is the representation of a node  $v_i^k$ , belonging to k-th cluster. After getting the scores, we select the nodes with top- $n$  highest scores in each cluster.  $n = \frac{\Delta_P}{(C-1)K}$ , where C is the number of classes, K is the number of clusters, and  $\Delta_P$  is the attack budget introduced in Section [II-B.](#page-1-2)

##### C. Trigger Generation

Given the poisoned node set  $V_P$ , we utilize the generator  $f_q$ to generate the triggers for nodes in  $V_P$  to poison the graph. Since different nodes possess different structural neighbors, the most effective topology of the triggers may vary. Therefore,  $f<sub>g</sub>$  takes the node features and graph structure as input to generate not only the node features but also the structure of the triggers. Therefore, the generator  $f<sub>g</sub>$  is optimized during BGC's process, not the triggers.

Specifically, we adopt a GCN model to encode the node features and graph structure into representations as following:

$$
\mathbf{H} = \text{GCN}_g(\mathbf{A}, \mathbf{X}).\tag{10}
$$

Then, the node features and structure of the trigger for node  $v_i$  is generated as following:

$$
\mathbf{X}_i^g = \mathbf{W}_f \cdot \mathbf{h}_i, \quad \mathbf{A}_i^g = \mathbf{W}_a \cdot \mathbf{h}_i,\tag{11}
$$

where  $h_i$  is the representation of  $v_i$ . W<sub>f</sub> and W<sub>a</sub> are the learnable parameters of  $f_g$  for feature and structure generation, respectively.  $X_i^g \in \mathbb{R}^{|g_i| \times d}$  is the synthetic features of the trigger nodes, where  $|g_i|$  is the size of the generated trigger and d is the dimension of features.  $A_i^g \in \mathbb{R}^{|g_i| \times |g_i|}$  is the adjacency matrix of the generated trigger. Since the graph structure is discrete, we follow previous studies [\[33\]](#page-12-23), [\[42\]](#page-12-32) to binarize the continuous adjacency matrix  $A_i^g$  in the forward computation while using the continuous adjacency matrix value in backward propagation. With the generated trigger  $g_i = (\mathbf{X}_i^g, \mathbf{A}_i^g)$ , we link it to node  $v_i \in \mathcal{V}_P$  and assign target class label  $y_t$  to build the backdoored graph  $\mathcal{G}_P$  to generate the condensed graph  $S$  for model training. During inference, the generator  $f_q$  will generate triggers for test nodes to lead the backdoored GNN to misclassify them as target class  $y_t$ .

<span id="page-4-0"></span>Algorithm 1 Optimization of BGC

**Input:** The original graph  $G = \{A, X, Y\}$ , T, M

- **Output:** The condensed graph  $S$ , adaptive generator trigger  $f_q$
- 1: Initialize poisoned graph  $\mathcal{G}_P = \mathcal{G}$ ;
- 2: Randomly initialize the condensed graph  $S$ ,  $\theta_{sel}$  for poisoned node selector  $f_{sel}$  and  $\theta_q$  for adaptive trigger generator  $f_g$ ;
- 3: Select poisoned nodes  $V_P$  based on Eq [9](#page-3-1) and assign class  $y_t$  as labels of  $V_P$ ;
- 4: while update condensed graph  $S$  do
- 5: Initialize the surrogate model  $\theta_c$ ;
- 6: **for**  $t=1, 2, ..., T$  **do**
- 7: Update the surrogate model  $\theta_c$  by based on Eq [16;](#page-4-1) 8: end for
- 9: **for**  $t=1, 2, ..., M$  **do**
- 10: Update the trigger generator  $\theta_g$  based on Eq [17;](#page-4-2)
- 11: end for
- 12: Attach updated triggers  ${g_i}$  to the selected nodes in  $V_P$  to build the poisoned graph  $\mathcal{G}_P$ .
- 13: Update the condensed graph  $S$  based on Eq [18;](#page-4-3)
- 14: end while

15: **return** S and  $f_q$ 

##### D. Optimization

Since the target model  $f$  is invisible to attackers, we propose to optimize the trigger generator  $f<sub>g</sub>$  and generate condensed graph S to successfully attack the surrogate GCN model  $f_c$ . In this section, we elaborate on the optimization of condensed graph S, surrogate model  $f_c$  and the trigger generator  $f_q$ .

**Surrogate Model.** Given the condensed graph  $S =$  ${A', X', Y'}$ , the surrogate GCN model  $f_c$  is trained by:

<span id="page-4-4"></span>
$$
\min_{\theta} \mathcal{L}_c = \mathcal{L}\left(f_c(\mathcal{S}|\theta), \mathbf{Y}'\right),\tag{12}
$$

where  $\theta$  is the parameters of  $f_c$ .

**Trigger Generator.** Given the selected poison nodes  $V_P$ and the surrogate GCN model  $f_c(\cdot|\theta_{\mathcal{S}^*})$  optimally trained on S, we can update the trigger generator  $f<sub>q</sub>$  to mislead the surrogate model to classify nodes with trigger to label  $y_t$  by:

<span id="page-4-5"></span>
$$
\min_{\theta_g} \mathcal{L}_g = \sum_{v_i \in \mathcal{V}_U} l(f_c(a(\mathcal{G}_C^i, g_i)|\theta_{\mathcal{S}^*}), y_t), \tag{13}
$$

where  $\mathcal{G}_C^i$  indicates the clean computation graph of node  $v_i$ ,  $g_i$ is the trigger of  $v_i$ ,  $a(\cdot)$  represents the attachment operation,  $l(\cdot)$  is the cross-entropy loss,  $y_t$  is the target class,  $\theta_q$  is the parameters of trigger generator  $f_q$  and nodes in  $V_U \subseteq V$  are randomly sampled from  $V$  to ensure that the attacks can be effective for various types of target nodes.

**Condensed Graph.** Given the selected poison nodes  $V_P$ and trigger generator  $f_g$  parameterized by  $\theta_g$ , we can generate

<span id="page-4-8"></span>TABLE I: Dataset Statistics. Cora and Citeseer are transductive datasets while Flickr and Reddit are inductive datasets.

| Dataset |           | Cora  | Citeseer | Flickr    | Reddit     |
|---------|-----------|-------|----------|-----------|------------|
|         | #Nodes    | 2,708 | 3,327    | 89,250    | 232,965    |
|         | #Edges    | 5,429 | 4,732    | 1,166,243 | 57,307,946 |
|         | #Classes  | 7     | 6        | 40        | 210        |
|         | #Features | 1,433 | 3,703    | 500       | 602        |
| Split   | Train     | 140   | 120      | 44,625    | 153,932    |
|         | Val.      | 500   | 500      | 22,312    | 23,699     |
|         | Test      | 1,000 | 1,000    | 22,312    | 55,334     |

the poisoned graph  $\mathcal{G}_P = \{ \mathbf{A}^P, \mathbf{X}^P, \mathbf{Y}^P \}$ , based on which we can obtain S via:

<span id="page-4-6"></span>
$$
\min_{\mathcal{S}} \mathcal{L}_s = \mathcal{L}'(\mathcal{G}^P|\theta_{\mathcal{S}}), \mathbf{Y}^P), \tag{14}
$$

where  $\theta_{\mathcal{S}}$  is the parameters of surrogate GCN model  $f_c$  trained on  $S$  and  $C'$  is the loss function for graph condensation.

Combining Eq [12,](#page-4-4) Eq [13](#page-4-5) and Eq [14,](#page-4-6) the tri-level optimization problem could be re-written as:

<span id="page-4-7"></span>
$$
\min_{\theta_g} \mathcal{L}_g(\theta_{\mathcal{S}^*(\theta_g)}, \mathcal{S}^*(\theta_g), \theta_g)
$$
*s.t.* 
$$
\theta_{\mathcal{S}^*} = \underset{\theta}{\arg \min} \mathcal{L}_c(\theta, \mathcal{S}^*(\theta_g), \theta_g),
$$
 (15)
  
*s.t.* 
$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg \min} \mathcal{L}_s(\mathcal{S}, \theta_g).
$$

Optimization Schema. Solving the aforementioned objective involves a computationally expensive tri-level optimization problem, which requires multi-level optimal. To reduce the computational cost, we sequentially optimize the surrogate model  $f_c$  and the trigger generator  $f_q$  for several epochs instead of optimizing to convergence, at each update of the condensed graph  $S$ . This significantly reduces the number of iterations, improving the efficiencies.

*Surrogate Model.* To further mitigate the computational complexity, we follow [\[32\]](#page-12-21), [\[33\]](#page-12-23) to update surrogate model  $\theta_c$ for T iterations with fixed generator  $\theta_q$  and condensed graph S to approximate  $\theta_c^*$ :

<span id="page-4-1"></span>
$$
\theta_c^{t+1} = \theta_c^t - \alpha_c \nabla_{\theta_c} \mathcal{L}_c(\theta_c, \mathcal{S}, \theta_g), \tag{16}
$$

where  $\alpha_c$  is the learning rate for surrogate model training and  $\theta_c^t$  denotes the model parameters after t iterations.

*Trigger Generator.* We also apply the M-iterations approximation to the optimization for trigger generator  $\theta_g$  with updated  $\theta_c^T$  and the fixed S:

<span id="page-4-2"></span>
$$
\theta_g^{m+1} = \theta_g^m - \alpha_g \nabla_{\theta_g} \mathcal{L}_g(\theta_c^T, \mathcal{S}, \theta_g), \tag{17}
$$

where  $\alpha_q$  is the learning rate for the trigger generator.

*Condensed Graph.* In the outer iteration, we compute the gradients of the condensed graph  $S$  based on the trigger generator  $\theta_g^M$  and the trained surrogate model  $\theta_c^T$  as following:

<span id="page-4-3"></span>
$$
S^{k+1} = S^k - \alpha_S \nabla_S \mathcal{L}_s(\theta_c^T, \mathcal{S}, \theta_g^M), \tag{18}
$$

where  $\alpha_{\mathcal{S}}$  is the learning rate of updating condensed graph. The summarization of the whole BGC's optimization process could be found in Algorithm [1.](#page-4-0)

Convergence Property. Follow the proof of Theorem 1 and Theorem 2 in Doscond [\[13\]](#page-12-14), we adopt SGC as surrogate model, i.e.,  $f_{\theta_c}(\mathcal{G}^P|\theta_c) = \mathbf{A}^P \mathbf{X}^P \mathbf{W}$ , where  $\mathbf{A}^P \in$ 

TABLE II: Model Utility (CTA) and Attack Performance (ASR). Across all settings, our method achieves high model utility, with CTA scores closely approaching C-CTA scores, and achieves a high attack success rate (ASR).

<span id="page-5-0"></span>

| Datasets | Ratio $(r)$ |              |              | $DC-Graph$   |              | GCond        |                |              |              |
|----------|-------------|--------------|--------------|--------------|--------------|--------------|----------------|--------------|--------------|
|          |             | $C-CTA$      | <b>CTA</b>   | $C-ASR$      | ASR          | $C-CTA$      | <b>CTA</b>     | $C-ASR$      | <b>ASR</b>   |
|          | 1.30%       | 75.17(0.65)  | 75.90 (0.94) | 12.51(1.03)  | 100.0(0.00)  | 81.33(0.62)  | 81.23 (0.24)   | 11.23(1.31)  | 100.0(0.00)  |
| Cora     | 2.60%       | 75.97 (0.61) | 75.00(0.51)  | 13.23(0.61)  | 100.0(0.00)  | 81.27 (0.33) | 80.67 (0.52)   | 13.42 (0.06) | 100.0(0.00)  |
|          | 5.20%       | 78.30 (0.43) | 78.43 (0.31) | 12.12(0.13)  | 100.0(0.00)  | 80.53 (0.73) | 80.70 (0.50)   | 11.78 (0.85) | 100.0(0.00)  |
|          | $0.90\%$    | 68.37 (0.90) | 70.27 (0.50) | 15.49(1.34)  | 100.0(0.00)  | 71.43(0.33)  | 71.57 (1.32)   | 16.65(0.19)  | 100.0(0.00)  |
| Citeseer | 1.80%       | 69.10 (0.62) | 69.37 (0.94) | 15.04 (0.40) | 100.0(0.00)  | 72.03 (0.17) | 71.03 (0.09)   | 14.64(0.58)  | 100.0(0.00)  |
|          | 3.60%       | 70.23(0.05)  | 70.00(0.22)  | 17.12 (0.73) | 100.0(0.00)  | 71.20 (0.70) | 70.60 (0.51)   | 16.18(0.49)  | 100.0(0.00)  |
|          | 0.10%       | 45.49(0.43)  | 46.48 (0.21) | 2.54(0.34)   | 99.98 (0.02) | 46.85(0.10)  | 46.54(0.08)    | 2.18(0.43)   | 99.83 (0.07) |
| Flickr   | 0.50%       | 46.37(0.10)  | 46.44(0.13)  | 2.68(0.90)   | 99.25 (0.65) | 46.62(0.53)  | 47.15(0.08)    | 2.25(0.51)   | 99.97 (0.02) |
|          | 1.00%       | 47.14 (0.03) | 46.77(0.30)  | 2.63(0.07)   | 99.11 (0.40) | 46.91 (0.41) | 46.84(0.09)    | 2.21(0.35)   | 99.77 (0.06) |
|          | 0.05%       | 85.88 (0.08) | 85.38 (0.42) | 0.46(0.01)   | 99.90 (0.03) | 88.86 (0.06) | 88.50 (0.27)   | 0.45(0.01)   | 99.84(0.14)  |
| Reddit   | 0.10%       | 89.25 (0.21) | 89.14 (0.05) | 0.49(0.01)   | 99.93 (0.02) | 89.20 (0.13) | 90.37(0.22)    | 0.47(0.01)   | 99.99 (0.01) |
|          | 0.20%       | 91.15 (0.07) | 90.38(0.42)  | 0.46(0.00)   | 99.90 (0.03) | 90.10 (0.26) | 90.40(0.41)    | 0.45(0.02)   | 99.06 (0.91) |
|          |             |              |              |              |              |              |                |              |              |
|          |             |              | $GCond-X$    |              |              |              | <b>GC-SNTK</b> |              |              |
| Datasets | Ratio $(r)$ | C-CTA        | <b>CTA</b>   | $C-ASR$      | <b>ASR</b>   | C-CTA        | <b>CTA</b>     | $C-ASR$      | <b>ASR</b>   |
|          | 1.30%       | 77.67 (0.59) | 76.30(1.35)  | 14.03(0.52)  | 100.0(0.00)  | 81.24(0.50)  | 79.73 (1.40)   | 13.73(1.44)  | 98.30(1.53)  |
| Cora     | 2.60%       | 78.70 (0.90) | 78.10 (0.71) | 11.93 (0.68) | 100.0(0.00)  | 80.50 (0.70) | 79.10 (1.04)   | 13.05(0.29)  | 99.77 (0.15) |
|          | 5.20%       | 80.40 (1.28) | 79.40 (0.36) | 12.02(0.73)  | 100.0(0.00)  | 80.12 (0.32) | 79.67 (0.42)   | 13.17(0.15)  | 98.13 (1.65) |
|          | 0.90%       | 73.76 (0.21) | 73.03 (0.50) | 17.64 (0.34) | 100.0(0.00)  | 60.68(1.04)  | 60.20(3.64)    | 15.16(1.34)  | 100.0(0.00)  |
| Citeseer | 1.80%       | 72.07 (0.61) | 72.40 (0.57) | 17.24 (0.26) | 100.0(0.00)  | 62.33(2.06)  | 63.03(2.64)    | 16.92(0.56)  | 100.0(0.00)  |
|          | 3.60%       | 72.16 (0.00) | 72.13 (0.83) | 15.94(0.35)  | 100.0(0.00)  | 63.44 (1.30) | 63.37 (2.06)   | 16.41(0.25)  | 100.0(0.00)  |
|          | 0.10%       | 45.60(0.80)  | 46.15(0.47)  | 2.67(0.06)   | 98.26 (1.19) | 46.10(0.10)  | 46.32(0.09)    | 2.51(0.26)   | 99.98 (0.00) |
| Flickr   | 0.50%       | 46.68 (0.20) | 45.21(0.44)  | 2.43(0.35)   | 99.58 (0.35) | 46.23(0.10)  | 46.02(0.21)    | 2.58(0.32)   | 99.98 (0.00) |
|          | 1.00%       | 45.74 (0.31) | 45.62(0.17)  | 2.49(0.09)   | 95.51 (1.69) | 46.01 (0.20) | 45.50 (0.06)   | 2.60(0.09)   | 99.98 (0.00) |
|          | 0.05%       | 87.09 (0.10) | 87.47 (0.21) | 0.48(0.00)   | 99.89 (0.07) | <b>OOM</b>   | <b>OOM</b>     | <b>OOM</b>   | OM           |
| Reddit   | 0.10%       | 88.42 (0.47) | 89.14 (0.51) | 0.46(0.00)   | 99.58 (0.30) | <b>OOM</b>   | <b>OOM</b>     | <b>OOM</b>   | <b>OOM</b>   |
|          | 0.20%       | 89.96 (0.14) | 90.09(0.21)  | 0.46(0.01)   | 97.60 (1.42) | <b>OOM</b>   | <b>OOM</b>     | <b>OOM</b>   | <b>OOM</b>   |

C-CTA (%): Clean Test Accuracy with the Clean GNNs. C-ASR (%): Clean Test Accuracy with the Backdoored GNNs. C-ASR (%): Attack Success Rate with the Clean GNNs. ASR (%): Attack Success Rate with the Backdoored GNNs. C-ASR (%): Attack Success Rate with the Clean GNNs.

 ${0,1}^{N^P \times N^P}$  is the adjacency matrix of poisoned graph,  $\mathbf{X}^P \in \mathcal{R}^{N^P \times d}$  denotes the feature matrix for the poisoned graph and  $\theta_c = W$ . A pivotal design decision involves ensuring full connectivity among the trigger nodes generated by the trigger generator  $f_{\theta_g}$ , thereby maintaining the invariance of  $A^P$ , throughout the condensation process. Consequently, as illustrated in Algorithm [1,](#page-4-0) the parameters  $\mathbf{W}^P$  ( $\theta_c$ ) and  $\mathbf{X}^P$  $(\theta_{q})$  are updated sequentially. This allows us to conceptualize them as components of a unified optimization task, denoted by  $\hat{\mathbf{W}} = \mathbf{W}^P \mathbf{X}^P$  and  $\theta_{cg}$ , which combines  $\theta_c$  and  $\theta_g$ . This reformulation enables the transformation of the tri-level optimization problem into a bi-level optimization framework. In this framework, the inner optimization focuses on refining the "GNN model" parameterized by  $\theta_{cg}$  over  $M+T$  iterations, while the outer optimization is dedicated to optimize the condensed graph  $S$ . Then, we can transform the tri-level optimization problem in Eq.[\(15\)](#page-4-7) into following bi-levle problem (graph condensation):

$$
\min_{\mathcal{S}} \ \mathcal{L}_s(\mathcal{S}, \theta_{cg}^*) \quad s.t. \quad \theta_{cg}^* = \arg\min_{\theta_{cg}} \ \mathcal{L}_g(\mathcal{S}, \theta_{cg}).
$$

While we focus on node classification task in this work, according to Theorem 2 in Doscond [\[13\]](#page-12-14), we have:  $\mathcal{L}_s(\mathcal{S}, \theta_{cg})$ is convex and L-smooth. We assume that  $\mathbb{E} \left| \nabla_{\mathcal{S}} \mathcal{L}_s(\mathcal{S}, \theta_{cg}^*) - \right|$  $\nabla_{\mathcal{S}} \mathcal{L}_s(\mathcal{S}, \theta_{cg}^{M+T})$ ||<sup>2</sup>  $\leq \sigma^2$  and stepsize  $\eta < \frac{1}{L}$ . We denote  $\mathcal{S}$ 's gradient mapping at k-th iteration by  $\mathcal{G}^k = \frac{1}{\eta} \mathcal{L}_s(\mathcal{S}, \theta_{cg}^{M+T}).$ 

According to the proof for Theorem 1 in DConRec [\[5\]](#page-12-3), we can have following conclusion given above assumptions:

$$
\frac{1}{K} \sum_{t=1}^{K} \mathbb{E} \left\| \mathcal{G}^t \right\|^2 \le \frac{8 - 2L\eta}{2 - L\eta} \sigma^2
$$

,

when  $K \to \infty$ . K is the total iterations of S. Therefore, BGC is provably convergent.

CTA (%): Clean Test Accuracy with the Backdoored GNNs.
ASR (%): Attack Success Rate with the Backdoored GNNs.

## V. EXPERIMENTAL SETTINGS

Datasets. We evaluate our proposed attack on two transductive datasets, i.e., Cora, Citeseer [\[43\]](#page-12-33), and two inductive datasets, i.e., Flickr [\[44\]](#page-12-34), Reddit [\[45\]](#page-12-35). All the datasets have public splits and we download them from Pytorch Geometric [\[46\]](#page-12-36), following those splits throughout the experiments. The statistics of the datasets and splits are summarized in Table [I.](#page-4-8)

Graph Condensation Methods. In this paper, we incorporate four prevalent graph condensation methods to test the attack performance of our proposed method: 1) DC-Graph [\[19\]](#page-12-9), [\[39\]](#page-12-28) is the graph-based variant of the general dataset condensation method DC [\[39\]](#page-12-28), 2) GCond [\[19\]](#page-12-9) is one representative graph condensation method, 3) GCond-X [\[19\]](#page-12-9) is the variant of GCond that discards the structure information of condensed graph for GNNs' training and 4) GC-SNTK [\[18\]](#page-12-8) reforms the graph condensation as a Kernel Ridge Regression (KRR) task and it is based on the Structure-based Neural Tangent Kernel (SNTK). While DC-graph, GCond, and GCond-X offer flexibility in utilizing different GNNs for the condensation and test stages, we default to adopting the best-performing of setting SGC [\[47\]](#page-12-37) as the backbone for condensation and GCN [\[43\]](#page-12-33) for testing. For the parameter settings of DC-graph, GCond, and GCond-X, we follow the settings described in [\[19\]](#page-12-9). Regarding the GC-SNTK method, we adopt the settings described in the original paper [\[18\]](#page-12-8).

Evaluation Metrics. To evaluate the effectiveness, we adopt *attack success rate (ASR)* and *clean test accuracy (CTA)* as the metrics. The *ASR* measures the attack effectiveness of the backdoored GNN on the triggered testing dataset and the *CTA* measures the utility of the backdoored GNN on the clean testing dataset. Both *ASR* and *CTA* range from 0.0 to 1.0. The higher value of *ASR* denotes the better attack performance.

<span id="page-6-0"></span>Image /page/6/Figure/0 description: This figure, titled "Fig. 4: Attack Comparison between BGC and Adapted Graph Backdoor Methods under Different Condensation Ratios," displays four sets of bar charts, each representing a different dataset: Cora, Citeseer, Flickr, and Reddit. Each set contains two subplots. The top row of subplots shows the "CTA (%)" (Clean Target Accuracy) for different condensation ratios, while the bottom row shows the "ASR (%)" (Attack Success Rate). Within each subplot, there are three bars representing three different methods: GTA (light beige), DOORPING (light purple), and BGC (light red). For Cora, CTA values range from approximately 68% to 77% across condensation ratios of 1.30%, 2.60%, and 5.20%. ASR values for Cora range from approximately 98% to 100%. For Citeseer, CTA values range from approximately 65% to 70% across condensation ratios of 0.90%, 1.80%, and 3.60%. ASR values for Citeseer range from approximately 98% to 100%. For Flickr, CTA values range from approximately 45% to 47% across condensation ratios of 0.10%, 0.50%, and 1.00%. ASR values for Flickr range from approximately 90% to 100%. For Reddit, CTA values range from approximately 84% to 92% across condensation ratios of 0.05%, 0.10%, and 0.20%. ASR values for Reddit range from approximately 90% to 100%.

1. **Name:**
   - John Doe
2. **Age:**
   - 30
3. **City:**
   - New York
4. **Occupation:**
   - Engineer
5. **Skills:**
   - Python
   - Java
   - C++
6. **Projects:**
   - Project A
   - Project B
7. **Contact:**
   - <EMAIL>

The closer the *CTA* of the backdoored GNN to the one of a clean GNN, the better the backdoored GNN's utility is.

Targeted GNNs. For DC-graph, GCond, and GCond-X, we default to adopting GCN as the testing architecture as described previously. Since GC-SNTK is based on a neural tangent kernel (NTK), it is only applicable to the NTK-based model. To validate the generalization of our proposed attack method, we also utilize the condensed graph to backdoor other architectures of GNN regarding DC-graph, GCond, and GCond-X methods, presented in section [VI-C.](#page-7-0)

Runtime Configuration. We have examined the attack performance of our method under these condensation ratios:  $\{1.30\%, 2.60\%, 5.20\%,\}$  for Cora,  $\{0.90\%, 1.80\%, 3.60\%\}$ for Citeseer, {0.10%, 0.50%, 1.00%} for Flickr and  $\{0.05\%, 0.10\%, 0.20\%\}$  for Reddit. The poisoning ratio of Cora and Citeseer defaults to 0.1. The poisoning number of Flickr defaults to 80 and Reddit's defaults to 180. All the experiments are repeated 3 times. For each run, we follow the same experimental setup laid out before. We report the mean and standard deviation of each metric to evaluate the attack performance.

Implementation Details. Regarding the learning rates, optimizer, epochs for condensation, and model training, we all follow the settings described in the original papers of the condensation methods [\[18\]](#page-12-8), [\[19\]](#page-12-9), [\[39\]](#page-12-28). Due to the design of BGC, the iteration number of the optimizing trigger generator is the same as the one of condensation (i.e., 1000). The trigger generator's learning rate is searched within  $\{0.01, 0.05, 0.1, 0.5\}$ and its optimizer is Adam. The trigger size set to 4 by default, following the setting in [\[33\]](#page-12-23), [\[35\]](#page-12-24), [\[48\]](#page-12-38), [\[49\]](#page-12-39).

## VI. EXPERIMENT

In this section, we present the performance of our method against graph condensation. The extensive experiments are devised to answer the following questions:

- RQ1: Can BGC achieve high attack performance and preserve the model utility of GNNs?
- RQ2: Can the condensed graph by BGC generalize to successfully attack different architectures of GNNs?
- RQ3: How does BGC perform against defense methods?

Besides, we also conduct various studies to analyze the properties of BGC, investigating how different settings and hyper-parameters (e.g., ablation study on poisoned node selection, size of triggers, number of GNNs' layers, condensation epochs, and poison ratios) affect the attack performance and the condensed graph utility.

### A. Attack Performance and Model Utility (RQ1)

Attack Performance. To measure BGC's attack performance, we conduct a comparative evaluation of the ASR score between the backdoored GNN and the clean GNN, which are reported as ASR and C-ASR in Table [II,](#page-5-0) respectively. We can observe that the attack on clean model achieves low attack success scores C-ASR (i.e., ranging below 18%). In contrast, all of the ASR scores achieved by our method are over 95.0%. For example, the ASR scores on the datasets Cora and Citeseer are 100% with condensation methods DC-Graph, GCond, and GCond-X. Besides, the ASR scores are also much higher than the scores of clean GNNs C-ASR. This indicates the decent attack performance of our proposed method.

Model Utility. To measure the utility of the backdoored GNNs, i.e., evaluating whether our attacks undermines the performance of GNNs on the primary task. For a successful backdoor attack, the backdoored GNN should be as good as the clean GNN on the task, given clean test data. As observed from Table [II,](#page-5-0) the CTA scores of the backdoored GNN are close to the C-CTA scores of the clean GNN. For example, the value of C-CTA for the Cora dataset is 75.17 with the

<span id="page-7-1"></span>TABLE III: Study on Different GNN Architectures. Our method achieves high ASR and CTA scores close to C-CTA across GNN architectures.

| <b>GNN</b>    | Metrics    | Cora         | Citeseer     | Flickr       | Reddit       |
|---------------|------------|--------------|--------------|--------------|--------------|
| <b>GCN</b>    | C-CTA      | 81.27 (0.33) | 71.43 (0.33) | 46.91 (0.41) | 89.20 (0.13) |
|               | <b>CTA</b> | 80.67 (0.52) | 71.57 (1.32) | 46.84 (0.09) | 90.37 (0.22) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 99.77 (0.06) | 99.99 (0.01) |
| <b>SAGE</b>   | C-CTA      | 79.73 (0.66) | 73.03 (0.05) | 46.56 (0.08) | 90.25 (0.19) |
|               | <b>CTA</b> | 77.03 (0.53) | 70.23 (1.52) | 46.99 (0.08) | 88.33 (0.92) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 97.28 (1.79) | 99.85 (0.12) |
| <b>SGC</b>    | C-CTA      | 78.97 (0.33) | 72.07 (0.20) | 46.50 (0.37) | 90.99 (0.22) |
|               | <b>CTA</b> | 80.23 (0.21) | 68.73 (1.26) | 47.16 (0.02) | 90.65 (0.13) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 92.14 (0.58) | 99.99 (0.00) |
| <b>MLP</b>    | C-CTA      | 78.77 (0.88) | 70.67 (0.46) | 42.24 (0.30) | 43.66 (1.04) |
|               | <b>CTA</b> | 76.20 (0.86) | 53.57 (7.69) | 46.60 (0.25) | 42.75 (0.95) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 95.45 (2.28) | 100.0 (0.00) |
| <b>APPNP</b>  | C-CTA      | 79.10 (0.29) | 71.30 (0.22) | 45.90 (0.30) | 88.53 (0.44) |
|               | <b>CTA</b> | 79.23 (0.60) | 47.67 (0.39) | 46.74 (0.07) | 88.41 (0.02) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 100.0 (0.00) | 100.0 (0.00) |
| <b>Cheby.</b> | C-CTA      | 78.23 (0.60) | 66.90 (1.13) | 42.37 (0.01) | 75.79 (0.73) |
|               | <b>CTA</b> | 77.37 (1.02) | 66.33 (0.62) | 41.33 (1.30) | 72.78 (1.21) |
|               | ASR        | 100.0 (0.00) | 100.0 (0.00) | 88.63 (3.28) | 98.54 (1.71) |

setting that the condensation method is DC-Graph and the condensation ratio is 1.30%. In the same case, the CTA of the backdoored GNN is 75.90. Besides, the largest gap between C-CTA and CTA is the case that the condensation method is GCond-X, and the condensation ratio is 0.50% for the dataset Flickr, where the CTA (45.21%) drops by 3.15% compared to C-CTA (46.68%). This side effect is within the acceptable performance, indicating utility preservation of our method.

### B. Attack Performance Comparison

To further validate the effectiveness of our design, we compare BGC with previous backdoor attack baselines: 1) GTA [\[35\]](#page-12-24) is the most representative backdoor attack method on the graph, which injects triggers to the graph during model training, and 2) DOORPING [\[22\]](#page-12-11) is a backdoor attack against dataset distillation for image data, which directly learns triggers for the images. To adapt GTA in the context of graph condensation, we apply it to the original graph and then utilize the poisoned graph for condensation. To transform DOORPING for graph data, we learn the universal triggers for all the nodes following training procedures described in the original paper [\[22\]](#page-12-11). Besides, regarding the poisoned nodes, we utilize the selection module of our proposed method. The experimental results are presented in Figure [4.](#page-6-0) Although GTA and DOORPING can perform well in some cases, they are still inferior to BGC, and they fail to launch the effective attack in many cases (i.e., for all the settings with the condensation method GC-SNTK, the attack success rate of these two methods is less than 88.81%). Besides, their attacks can lead to a significant drop in the GNN's utility. For instance, under the setting that the condensation ratio is 0.90% and the condensation method is GCond-X for dataset Citeseer, the CTAs of GTA, and DOOPRPING are 69.00 and 55.47, dropping by  $6.45\%$  and  $24.80\%$  respectively. This demonstrates the superiority of our method and reveals that previous methods cannot launch a successful backdoor attack against graph condensation.

<span id="page-7-0"></span>

### C. Study on Different GNN Architectures (RQ2)

Since we envision the attacker as the graph condensation service provider, the attacker does not know which GNN the

<span id="page-7-2"></span>Image /page/7/Figure/7 description: This figure contains four bar charts comparing BGCRAND and BGC performance across different conditional ratios for Flickr and Reddit datasets. The top left chart shows CTA (%) for Flickr with conditional ratios of 0.10%, 0.50%, and 1.00%. The top right chart shows CTA (%) for Reddit with conditional ratios of 0.05%, 0.10%, and 0.20%. The bottom left chart shows ASR (%) for Flickr with conditional ratios of 0.10%, 0.50%, and 1.00%. The bottom right chart shows ASR (%) for Reddit with conditional ratios of 0.05%, 0.10%, and 0.20%. Error bars are present for each bar.

Fig. 5: Ablation Study on Poisoned Node Selection.

customer will train with the condensed graph. Therefore, it is essential to test the effectiveness of BGC in backdooring different architectures of GNNs. Specifically, we utilize the graphs condensed by BGC with condensation method GCond for the training of various architectures of GNNs: GCN [\[43\]](#page-12-33), GraphSage [\[45\]](#page-12-35), SGC [\[47\]](#page-12-37), MLP [\[50\]](#page-12-40), APPNP [\[51\]](#page-12-41) and ChebyNet [\[52\]](#page-12-42). The condensation ratios for four datasets are: Cora, 2.60%; Citeseer, 0.90%; Flickr, 1.00%; Reddit, 0.10%. The results are reported in Table [III.](#page-7-1) We can observe that the ASR scores in many cases are 100% (i.e., for dataest Cora and Citeseer) and the rest are over 92%, except the one for ChebyNet on dataset Flickr. Besides, the CTA scores in most cases are close to the C-CTA. This demonstrates that the condensed graphs by our proposed method can carry out effective backdoor attacks against different GNN models.

### D. Defenses (RQ3)

To mitigate the threats of backdoor attacks, various defense methods have been proposed. In this section, we evaluate the robustness of our proposed method against two representative graph defense methods: 1) Prune [\[33\]](#page-12-23) is a dataset-level defense, which prunes edges linking nodes with low cosine similarity in the condensed graphs, and 2) Randsmooth [\[53\]](#page-12-43) is a model-level defense, which randomly subsamples  $d$  subgraphs to generate d outputs, using a voting mechanism for the final prediction. The results are presented in Table [IV,](#page-8-0) where we report the CTA and ASR scores of BGC, the scores of BGC under the defenses and the decreased ratio of those scores. By analyzing the score changes, we assess the effectiveness of the defense mechanisms in mitigating our proposed attack.

In our experiment, we implement Prune by removing the edges connecting nodes that fall within the lowest 20% of cosine similarities. As we can observe from the table, most ASR scores decrease. However, the CTA scores also drop significantly, often more than the ASR scores. For example, when the condensation ratio is 3.60% for the Citeseer dataset using the GCond method, the CTA score drops by

TABLE IV: Attack Performance against Defenses. '*OOM*' denotes out of memory and '*–*' denotes not applicable. The defenses (*Prune*, *Randsmooth*) suffer from a trade-off, with significant CTA losses (∆ CTA) for limited ASR reduction (∆ ASR).

<span id="page-8-0"></span>

| Cond.<br>Method | Datasets | Ratio (r) | Defense-Prune |                 |       | Defense-Randsmooth |                 |           | Backdoor |           |       |       |
|-----------------|----------|-----------|---------------|-----------------|-------|--------------------|-----------------|-----------|----------|-----------|-------|-------|
|                 |          |           | CTA           | $\triangle$ CTA | ASR   | CTA                | $\triangle$ CTA | ASR       | CTA      | ASR       |       |       |
| GCond           | Cora     | 1.30%     | 45.40         | $-44.11%$       | 75.60 | $-24.40\%$         | 78.70           | $-3.11%$  | 99.07    | $-0.93%$  | 81.23 | 100.0 |
|                 |          | 2.60%     | 54.67         | $-32.23%$       | 65.10 | $-34.90\%$         | 78.67           | $-2.48%$  | 99.10    | $-0.90%$  | 80.67 | 100.0 |
|                 |          | 5.20%     | 55.97         | $-30.64%$       | 99.00 | $-1.00\%$          | 80.10           | $-0.74%$  | 98.83    | $-1.17%$  | 80.70 | 100.0 |
|                 | Citeseer | 0.90%     | 36.17         | $-49.46%$       | 95.10 | $-4.90\%$          | 68.86           | $-3.79%$  | 99.30    | $-0.70%$  | 71.57 | 100.0 |
|                 |          | 1.80%     | 48.87         | $-31.20%$       | 85.70 | $-14.30\%$         | 62.90           | $-11.45%$ | 99.37    | $-0.63%$  | 71.03 | 100.0 |
|                 |          | 3.60%     | 40.43         | $-42.73%$       | 71.70 | $-28.30\%$         | 60.80           | $-13.88%$ | 98.70    | $-1.30%$  | 70.60 | 100.0 |
|                 | Flickr   | 0.10%     | 41.52         | $-10.79%$       | 90.03 | $-9.82%$           | 45.57           | $-2.08\%$ | 97.53    | $-2.30%$  | 46.54 | 99.83 |
|                 |          | 0.50%     | 40.91         | $-13.23%$       | 89.24 | $-10.73\%$         | 45.12           | $-4.31%$  | 88.04    | $-11.93%$ | 47.15 | 99.97 |
|                 |          | 1.00%     | 42.31         | $-9.67%$        | 88.33 | $-11.47\%$         | 45.93           | $-1.94%$  | 94.22    | $-5.56%$  | 46.84 | 99.77 |
|                 | Reddit   | 0.05%     | 67.14         | $-24.14%$       | 91.28 | $-8.57\%$          | 72.99           | $-17.53%$ | 99.97    | 0.13%     | 88.5  | 99.84 |
|                 |          | 0.10%     | 40.49         | $-55.20%$       | 87.86 | $-12.13\%$         | 87.14           | $-3.57%$  | 99.95    | $-0.04%$  | 90.37 | 99.99 |
|                 |          | 0.20%     | 56.74         | $-37.23%$       | 88.15 | $-11.01\%$         | 87.01           | $-3.75%$  | 95.73    | $-3.36%$  | 90.40 | 99.06 |
| GCond-X         | Cora     | 1.30%     | 72.03         | $-5.60%$        | 73.67 | $-26.33\%$         | 75.70           | $-0.79%$  | 98.87    | $-1.13%$  | 76.30 | 100.0 |
|                 |          | 2.60%     | 71.63         | $-8.28%$        | 67.73 | $-32.27\%$         | 75.47           | $-3.37%$  | 98.87    | $-1.13%$  | 78.10 | 100.0 |
|                 |          | 5.20%     | 77.53         | $-2.36%$        | 89.80 | $-10.20\%$         | 76.93           | $-3.11%$  | 98.90    | $-1.10%$  | 79.40 | 100.0 |
|                 | Citeseer | 0.90%     | 56.13         | $-23.14%$       | 81.39 | $-18.61\%$         | 56.60           | $-22.50%$ | 88.24    | $-11.76%$ | 73.03 | 100.0 |
|                 |          | 1.80%     | 65.37         | $-9.71%$        | 80.10 | $-19.90\%$         | 55.43           | $-23.44%$ | 88.70    | $-11.30%$ | 72.40 | 100.0 |
|                 |          | 3.60%     | 60.10         | $-16.68%$       | 87.20 | $-12.80\%$         | 55.10           | $-23.61%$ | 81.38    | $-18.62%$ | 72.13 | 100.0 |
|                 | Flickr   | 0.10%     | 42.14         | $-8.69%$        | 65.93 | $-32.90\%$         | 45.02           | $-2.45%$  | 99.97    | 1.74%     | 46.15 | 98.26 |
|                 |          | 0.50%     | 43.99         | $-2.70%$        | 70.06 | $-29.64%$          | 45.83           | 1.37%     | 93.65    | $-5.96%$  | 45.21 | 99.58 |
|                 |          | 1.00%     | 45.11         | $-1.12%$        | 80.07 | $-16.17\%$         | 45.21           | $-0.90%$  | 98.29    | 2.91%     | 45.62 | 95.51 |
|                 | Reddit   | 0.05%     | 87.12         | $-0.40%$        | 90.15 | $-9.75\%$          | 88.26           | 0.90%     | 98.87    | $-1.02%$  | 87.47 | 99.89 |
|                 |          | 0.10%     | 88.69         | $-0.50%$        | 92.78 | $-7.02\%$          | 89.55           | 0.46%     | 94.15    | $-5.64%$  | 89.14 | 99.78 |
|                 |          | 0.20%     | 89.80         | $-0.32%$        | 91.72 | $-6.02\%$          | 89.90           | $-0.21%$  | 98.64    | 1.07%     | 90.09 | 97.6  |

42.73%, while the ASR score decreases by only 28.30%. In conclusion, Prune cannot defend our proposed BGC while it severely suffers from a utility-defense trade-off. Regarding Randsmooth, we implement it by sampling different sub-structures for the propagations in different layers. From Table [IV,](#page-8-0) we can observe that the decrease in ASR scores caused by the defense is limited (i.e., less than 2% in most cases) while the decrease of CTA scores is even larger in most cases. A significant decline in ASR generally leads to a substantial decline in CTA. For instance, on dataset Citeseer with GCond-X, although the ASR scores drop by around  $12\%$ and 19%, the CTA scores drop by over 22%. This indicates that Randsmooth also suffers from the utility-defense trade-off and has limited defensive effectiveness.

##### E. Ablation Study on Poisoned Node Selection

In this section, we investigate the effects of the poisoned node selection module. To demonstrate the effectiveness of the selection module, we devise a variant of BGC by replacing the selection module by randomly selecting nodes to attach triggers and assign target labels. We denote the variant by BGC<sub>Rand</sub>. The results with the condensation method DC-Graph on the dataset Flickr are presented in Figure [5.](#page-7-2) We can observe that BGC<sub>Rand</sub> is inferior to BGC in various settings across different datasets, regarding both CTA and ASR, demonstrating that the selection module can effectively enlarge the attack performance and the utility performance of backdoored GNNs within a limited number of poisoned nodes. Besides, the standard deviations of BGC are also smaller than  $BGC_{\text{Rand}}$ , indicating that selecting the representative nodes can also stabilize the attack performance.

##### F. Ablation Study on Trigger Generator

To analyze the effect of trigger generator on the attack performance, we replace MLP with a GCN (2 layers) and 1<span id="page-8-1"></span>TABLE V: Ablation Study on Trigger Generator.

| Generator   | Dataset  | Ratio (r) | CTA          | ASR |
|-------------|----------|-----------|--------------|-----|
| MLP         | Cora     | 1.30%     | 81.23        | 100 |
|             |          | 2.60%     | 80.67        | 100 |
|             |          | 5.20%     | 80.70        | 100 |
|             | Citeseer | 0.90%     | 71.57        | 100 |
|             |          | 1.80%     | 71.03        | 100 |
|             |          | 3.60%     | 70.60        | 100 |
| <b>GCN</b>  | Cora     | 1.30%     | 81.55        | 100 |
|             |          | 2.60%     | 80.98        | 100 |
|             |          | 5.20%     | 81.33        | 100 |
|             | Citeseer | 0.90%     | 72.29        | 100 |
|             |          | 1.80%     | 71.57        | 100 |
|             |          | 3.60%     | <b>71.32</b> | 100 |
| Transformer | Cora     | 1.30%     | <b>81.71</b> | 100 |
|             |          | 2.60%     | <b>81.10</b> | 100 |
|             |          | 5.20%     | 81.07        | 100 |
|             | Citeseer | 0.90%     | <b>72.34</b> | 100 |
|             |          | 1.80%     | <b>71.62</b> | 100 |
|             |          | 3.60%     | <b>71.58</b> | 100 |

layer Transformer (1 layer, 8 heads), respectively, for trigger generation. We conduct experiments on Cora and Citeseer with GCond as the condensation method. As shown in Table [V,](#page-8-1) transformer consistently achieves the highest CTA in most configurations (e.g., Cora: 81.71% at r=1.30%, outperforming GCN (81.55%) and MLP (81.23%) in most cases. Nevertheless, they perform the same regarding CTA scores and the divergence of ASR between generators is marginal.

##### G. Ablation Study on Directed Attack

To assess the adaptability of BGC to directed attacks, we developed a variant that exclusively poisons a specific class and targets this class during testing. The objective is to induce the backdoored GNN to misclassify nodes from the poisoned class into a designated target class. The results, presented in Table [VI,](#page-9-0) indicate that this variant achieves attack success rates (ASR) comparable to the original BGC, albeit with slightly reduced clean test accuracy (CTA). This performance

<span id="page-9-1"></span>Image /page/9/Figure/0 description: This image contains four line graphs, labeled (a) Cora, (b) Citeseer, (c) Flickr, and (d) Reddit. Each graph plots ASR (%) on the y-axis against Epoch on the x-axis, with values ranging from 40 to 100 on the y-axis and 100 to 900 on the x-axis. Each graph displays three lines representing different percentage values. In graph (a) Cora, the lines represent 1.30%, 2.60%, and 5.20%. In graph (b) Citeseer, the lines represent 0.90%, 1.80%, and 3.60%. In graph (c) Flickr, the lines represent 0.10%, 0.50%, and 1.00%. In graph (d) Reddit, the lines represent 0.05%, 0.10%, and 0.20%. All graphs show an increasing trend in ASR (%) as the Epoch increases, with most lines reaching close to 100% ASR by 500 epochs.

Image /page/9/Figure/1 description: Figure 6 is a title card that reads "Study on Condensation Epochs."

| Method   | Dataset  | Ratio (r) | CTA          | ASR |
|----------|----------|-----------|--------------|-----|
| BGC      | Cora     | 1.30%     | <b>81.23</b> | 100 |
|          |          | 2.60%     | <b>80.67</b> | 100 |
|          |          | 5.20%     | <b>80.70</b> | 100 |
|          | Citeseer | 0.90%     | <b>71.57</b> | 100 |
|          |          | 1.80%     | <b>71.03</b> | 100 |
|          |          | 3.60%     | <b>70.60</b> | 100 |
| Directed | Cora     | 1.30%     | 81.03        | 100 |
|          |          | 2.60%     | 80.64        | 100 |
|          |          | 5.20%     | 80.69        | 100 |
|          | Citeseer | 0.90%     | 71.55        | 100 |
|          |          | 1.80%     | 70.87        | 100 |
|          |          | 3.60%     | 70.55        | 100 |

<span id="page-9-0"></span>

discrepancy is likely due to the extensive backdooring of the poisoned class, which disrupts the class distribution.

##### H. Hyper-parameter Analysis

Number of GNNs' Layers. Here, we aim to understand how the number of GNN layers affects the attack performance and model utility. We carry out experiments on three datasets: Cora, Citeseer, and Flickr, utilizing the condensation method GCond. The results are presented in Table [VIII,](#page-10-0) from which we can observe that the layer number does not necessarily influence the CTA scores a lot, except on dataset Cora. On dataset Cora, the GNN's utility outperforms the other two settings when the layer number is set to 2. On the other hand, the change of layer number also does not affect the ASR scores and on the dataset Flickr, the ASR decreases slightly.

Condensation Epochs. We further investigate the impact of condensation epochs on attack and utility performance. Since the number of condensation epochs has a great influence on the quality of the condensed graph, we report the attack and utility performance by varying condensation epochs from 50 to 1000 on four datasets, using the condensation method GCond. As depicted in Figure [6,](#page-9-1) ASR scores first increases and converges to a stable range of values.

Varying the Poisoning Ratio We explore the effect of the poisoning ratio on the attack and utility performance. We report the results on four datasets in Table [VII.](#page-9-2) As shown in the table, in all cases for different datasets, a larger poisoning ratio does not necessarily lead to better utility performance. In contrast, larger poisoning ratio can lead to worse GNN utility. For instance, given the dataset Reddit, the CTA scores across 3 condensation methods monotonically decease as the

<span id="page-9-2"></span>TABLE VII: Study on Poisoning Ratio.  $r$  denotes the condensation ratio, *P. R.* denotes the poison ratio and *P. N.* denotes the poison number.

| Datasets               | P. R. | DC-Graph |       | GCond |       | GCond-X |       |
|------------------------|-------|----------|-------|-------|-------|---------|-------|
|                        |       | CTA      | ASR   | CTA   | ASR   | CTA     | ASR   |
| Cora,<br>r = 1.30%     | 0.10  | 75.90    | 100.0 | 81.23 | 100.0 | 76.30   | 100.0 |
|                        | 0.15  | 75.26    | 100.0 | 80.06 | 100.0 | 75.30   | 100.0 |
|                        | 0.20  | 75.13    | 99.93 | 78.33 | 100.0 | 75.07   | 100.0 |
| Citeseer,<br>r = 3.60% | 0.10  | 68.83    | 100.0 | 69.03 | 100.0 | 67.27   | 100.0 |
|                        | 0.15  | 70.00    | 100.0 | 70.60 | 100.0 | 72.13   | 100.0 |
|                        | 0.20  | 66.40    | 100.0 | 69.27 | 100.0 | 70.83   | 99.40 |
| Datasets               | P. N. | DC-Graph |       | GCond |       | GCond-X |       |
|                        |       | CTA      | ASR   | CTA   | ASR   | CTA     | ASR   |
| Flickr,<br>r = 0.10%   | 60    | 45.86    | 99.56 | 45.38 | 99.13 | 44.85   | 100.0 |
|                        | 80    | 46.48    | 99.98 | 46.54 | 99.83 | 46.15   | 98.26 |
|                        | 100   | 46.18    | 100.0 | 46.18 | 99.66 | 46.19   | 97.21 |
| Reddit,<br>r = 0.05%   | 130   | 85.74    | 96.12 | 88.50 | 97.14 | 88.10   | 96.15 |
|                        | 180   | 85.40    | 99.90 | 88.37 | 99.84 | 88.04   | 99.89 |

poison number increases. This suggests that a higher number of poisoned nodes can degrade the quality of the condensed graph, thereby reducing the utility of the backdoored GNN.

Various Trigger Sizes As shown in previous work [\[22\]](#page-12-11), [\[33\]](#page-12-23), larger trigger sizes can contribute to higher attack performance. To investigate the effect of trigger size on the attack and utility performance, we experiment on the dataset Flickr with two condensation methods and report the results under three different condensation ratios in Figure [8.](#page-11-1) As depicted in the figure, the ASR scores are all close to 100% and increase as the trigger size becomes larger. In contrast, the CTA scores decline as the trigger size increases. Despite the negative effects on CTA scores caused by increasing the trigger size, the degradation is marginal and the utility performance is still acceptable. For example, the declines of CTA scores in three settings are only 0.51%, 0.94%, and 0.92%, respectively. Therefore, increasing the size of triggers can cause a trade-off between attack performance and GNN utility.

## VII. RELATED WORK

Graph Condensation. Recently, graph condensation [\[8\]](#page-12-5), [\[20\]](#page-12-10), [\[21\]](#page-12-44), [\[54\]](#page-12-45), [\[55\]](#page-12-46) is a technique for efficient graph learning, which aims to synthesize small graph datasets from large ones. GCond [\[19\]](#page-12-9) is the first graph condensation method and focuses on node-level datasets. It matches the training gradient of the original datasets with condensed datasets to achieve comparable performance. Doscond [\[13\]](#page-12-14) extends the gradient matching paradigm to the graph-level datasets and proposes a onestep update strategy to enhance the condensation efficiency.

TABLE VIII: Study on the Number of GNNs' Layers. l denotes the layer number.

<span id="page-10-0"></span>

| Datasets    | Cora |              |              | Citeseer     |              |              | Flickr       |              |              |              |
|-------------|------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|             |      |              |              |              |              |              |              |              |              |              |
| Ratio $(r)$ |      | $.30\%$      | $2.60\%$     | 5.20%        | $0.90\%$     | $0\%$        | $3.60\%$     | $0.10\%$     | $0.50\%$     | $1.00\%$     |
|             |      |              |              |              |              |              |              |              |              |              |
|             | CTA  | 66.53(2.24)  | 72.93 (1.75) | 76.47 (0.52) | 69.53(0.33)  | 69.43(0.96)  | 68.23 (0.12) | 46.43(0.21)  | 47.00(0.04)  | 47.12(0.17)  |
| $=$         |      |              |              |              |              |              |              |              |              |              |
|             | ASR  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0 (0.00) | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  |
|             |      |              |              |              |              |              |              |              |              |              |
|             | CTA  | 75.90(0.94)  | 75.00(0.51)  | 78.43(0.31)  | 70.27(0.50)  | 69.37 (0.94) | 70.00(0.22)  | 46.48(0.21)  | 46.44(0.13)  | 46.77(0.30)  |
| $= 2$       |      |              |              |              |              |              |              |              |              |              |
|             | ASR  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 100.0(0.00)  | 99.25(0.65)  | 99.11 (0.40) |
|             | CTA  | 69.07 (1.70) | 73.87 (0.84) | 76.77 (0.47) | 69.63(0.52)  | 68.60 (0.65) | 68.23 (0.21) | 46.28(0.31)  | 46.17(0.22)  | 46.89 (1.27) |
| $=$ 3       |      |              |              |              |              |              |              |              |              |              |
|             | ASR  | 100.0(0.00)  |              |              |              | 100.0(0.00)  |              |              |              |              |
|             |      |              | 100.0(0.00)  | 100.0(0.00)  | 100.0 (0.00) |              | 100.0 (0.00) | 99.01 (0.21) | 99.91 (0.03) | 99.28 (0.34) |

GCSR [\[17\]](#page-12-47) and SGDD [\[56\]](#page-12-48) are proposed to incorporate more comprehensive graph structure information into the condensed datasets. CTRL [\[57\]](#page-12-49) offers a better initialization and a more refined strategy for gradient matching. GEOM [\[58\]](#page-12-50) proposes to utilize a well-trained expert trajectory to supervise the condensation. FGD [\[59\]](#page-12-51) aims at improving group fairness for node classification task in graph condensation. MCond [\[60\]](#page-12-52) enables efficient inductive inference. On the other hand, CaT [\[61\]](#page-12-53) and GCDM [\[62\]](#page-12-54) both adopt maximum mean discrepancy to match the distribution between original graphs and condensed graphs. EXGC [\[14\]](#page-12-29) accelerates condensation via Mean-field variational approximation and inject explainability via existing explanation techniques. Different from the aforementioned methods that formulate condensation as a bi-level optimization problem, MIRAGE [\[63\]](#page-13-0) investigates graph condensation from a heuristic perspective and proposes a model-agnostic method. Besides, GC-SNTK [\[18\]](#page-12-8), LiteGNTK [\[16\]](#page-12-16) and SFGC [\[15\]](#page-12-15) all adopt the Kernel Ridge Regression formalization while LiteGNTK focuses on graph-level tasks and the other two are devised for node-level tasks. Several surveys have been released recently and more comprehensive introduction can be found in [\[8\]](#page-12-5), [\[20\]](#page-12-10), [\[21\]](#page-12-44). While those works focus on pursuing an optimal trade-off between GNN utility and condensation ratio, the security risks stemming from the condensation process are overlooked and haven't been investigated. Thus, we first explore this issue and propose an effective backdoor attack method against graph condensation.

Backdoor Attacks on Graph. Graph backdoor attacks targets graph neural networks (GNNs) by embedding specific triggers during training, causing the GNN to make malicious predictions when these triggers are detected, while performing normally on clean inputs [\[48\]](#page-12-38), [\[64\]](#page-13-1), [\[65\]](#page-13-2). According to the categorization by the stages the attack occurs [\[33\]](#page-12-23), adversarial attacks on GNNs mainly contain three types: poisoning attack [\[64\]](#page-13-1), [\[66\]](#page-13-3), evasion attack [\[48\]](#page-12-38), [\[49\]](#page-12-39), and backdoor attack [\[33\]](#page-12-23), [\[35\]](#page-12-24). In this paper, we focus on the backdoor attacks. The graph backdoor attack [\[67\]](#page-13-4)–[\[72\]](#page-13-5) is a training time attack [\[22\]](#page-12-11). It injects a hidden backdoor into the target GNNs via a backdoored training graph. At the test time, the successfully backdoored GNNs perform well on the clean test samples but misbehave on the triggered samples. The first work [\[53\]](#page-12-43) on graph backdoor randomly generates graphs as triggers while GBAST [\[73\]](#page-13-6) generates triggers based on subgraphs. Different from the universal triggers, GTA [\[35\]](#page-12-24) proposes a generator to adaptively obtain sample-specific triggers. To achieve unnoticeable graph backdoor attacks, UGBA [\[33\]](#page-12-23) limits the attack budget and improves the similarity between triggers and target nodes. GCBA [\[74\]](#page-13-7) studies a new setting

Poisoned Graph

**Conventional Graph Backdoor**

<span id="page-10-1"></span>Image /page/10/Figure/5 description: The image illustrates two methods for creating backdoored Graph Neural Networks (GNNs). The top section, titled 'Inject Trigger', shows an 'Original Graph' being transformed into a 'Poisoned Graph' by injecting a trigger, which is then used for 'Training' to produce 'Backdoored GNNs'. The bottom section, titled 'Backdoored Graph Condensation', depicts an 'Original Graph' undergoing a 'Condense' process and 'Inject Trigger' to create a 'Poisoned Condensed Graph'. This condensed graph is then used for 'Training' to also produce 'Backdoored GNNs'.

Fig. 7: Comparison between Backdoor Graph Condensation (BGC) and Conventional Graph Backdoor (CGB).

where supervisory labels are unavailable and proposes the first backdoor attack against graph contrastive learning. Those efforts focus on injecting triggers into the original graph during model training, which is infeasible to poison the condensed graph data. Recently, two related studies [\[22\]](#page-12-11), [\[40\]](#page-12-30) on backdoor attacks against dataset distillation for image data are proposed and reveal the vulnerability of dataset condensation in image domain. However, their design is limited to producing universal triggers for images, which is infeasible for graph.

## VIII. DISCUSSION

In this section, we discuss the difference between backdoor attacks against graph condensation (BGC) and conventional graph backdoor attacks (CGB), challenges to defense attacks against graph condensation and potential defense mechanism, the potential application of BGC in real-world scenario, and the limitations of BGC in more practical settings.

Different from Conventional Graph Backdoor. The goals of BGC and CGB are identical: backdooring the downstream GNN model through injection and manipulating its output while attaching trigger. However, BGC poses significantly greater challenges due to following reasons: 1) In graph condensation, the condensed graph is consistently updated towards the goal of maximizing the utility of downstream GNN, making it intractable to maintain the effectiveness of trigger, whereas in CGB, the trigger is generated on the static original graph. 2) The quality of the condensed graph highly depends on the original graph and the condensation algorithm, complicating the design of injections that do not degrade the condensed graph's quality.

<span id="page-11-1"></span>Image /page/11/Figure/0 description: This figure contains four line graphs, arranged in a 2x2 grid, comparing the performance of DC-Graph and GCond across different trigger sizes. The top two graphs, labeled "CTA (%)", show the "CTA (%)" on the y-axis and "Trigger Size" (1, 2, 3, 4) on the x-axis. The left top graph, labeled "(a) DC-Graph", shows three dashed lines representing 0.10%, 0.50%, and 1.00%. The 0.10% line starts at approximately 46.5 and decreases to about 46.2. The 0.50% line starts at approximately 47.0 and decreases to about 46.7. The 1.00% line starts at approximately 47.2 and decreases to about 46.6. The right top graph, labeled "(b) GCond", also shows three dashed lines. The 0.10% line starts at approximately 46.7 and decreases to about 46.2. The 0.50% line starts at approximately 46.9 and decreases to about 46.5. The 1.00% line starts at approximately 47.1 and decreases to about 46.7. The bottom two graphs, labeled "ASR (%)", show the "ASR (%)" on the y-axis and "Trigger Size" (1, 2, 3, 4) on the x-axis. The left bottom graph, labeled "(a) DC-Graph", shows three dashed lines. The 0.10% line starts at approximately 96.0 and increases to about 97.0. The 0.50% line starts at approximately 95.5 and increases to about 99.5. The 1.00% line starts at approximately 96.0 and increases to about 97.5. The right bottom graph, labeled "(b) GCond", also shows three dashed lines. The 0.10% line starts at approximately 99.2 and increases to about 99.8. The 0.50% line starts at approximately 99.5 and increases to about 100.0. The 1.00% line starts at approximately 97.8 and increases to about 99.8. The figure is titled "Fig. 8: Study on Different Trigger Sizes."

Fig. 8: Study on Different Trigger Sizes.

More Challenging to Defend BGC. As shown in Figure [7,](#page-10-1) different from CGB where the trigger is explicitly attach in the graph for GNN training, the malicious information of triggers is embedded within the synthetic nodes in BGC. Therefore, there is no explicit trigger present in the condensed graph, rendering detection-based and prune-based defense methods ineffective in mitigating attacks against graph condensation. The potentially effective defense mechanism should focus on designing robust GNN training algorithms or designing new GNN's architecture that can mitigate these attacks.

Example of Real-world Scenario. In this work, we conceptualize the attacker as a graph condensation service provider, which can be represented by real-world data companies. In the era of AI, data is indispensable for developing advanced models, and graphs are a key data modality, driving the demand for high-quality graph data and the rise of data provider companies such as Scale AI. While such services offer convenience, it is essential to highlight the potential security vulnerabilities in these scenarios where the data provider may not be trustworthy, or where third parties could inadvertently introduce backdoors. In such cases, if a malicious actor gains control over the model supporting a product service, the resulting economic losses could be catastrophic. Therefore, we attempt to reveal vulnerabilities in graph condensation and the associated risks in this work.

Limitations. In real-world settings [\[75\]](#page-13-8), [\[76\]](#page-13-9), attacker capabilities are often constrained. We expand on key limitations:

• Partial Data Access: An attacker might only control a subset of nodes/edges (e.g., in federated or crowdsourced graphs). Our method's reliance on representative node selection could still apply if the attacker poisons highinfluence nodes within their accessible subset. Via the selection and consistent trigger update, our method may still work well and launch effective attack. However, trigger generated with limited data can limit its effectiveness among different other nodes. Therefore, a more generalizable trigger may be desired.

**Process Access:** If the condensation algorithm is hidden (e.g., a commercial API), dynamic trigger optimization becomes infeasible. This setting is totally different from our assumption and predefined scenario. Under this setting, an invariant trigger is required, which can survive across the consistent optimization.

These scenarios highlight underexplored challenges in backdoor attacks: 1) the tension between localized attack efficacy and global pattern propagation, and 2) the robustnessadaptability trade-off in black-box condensation environments. While addressing these limitations requires substantial methodological innovation (e.g., meta-trigger learning for partial visibility, surrogate condensation modeling for APIobscured settings), we defer such explorations to future research. This work establishes foundational insights for graph backdoor attacks under constrained threat models.

## IX. CONCLUSION

In this paper, we first propose the task of backdoor graph condensation. We envision a realistic scenario where the attacker is a malicious graph condensation provider and possesses the information of the graph data. We set two primary objectives to launch successful attacks: 1) the injection of triggers cannot affect the quality of condensed graphs, maintaining the utility of GNNs trained on them; and 2) the effectiveness of triggers should be preserved throughout the condensation process, achieving a high attack success rate. To pursue these two goals, we propose the first backdoor attack against graph condensation (BGC). Specifically, we inject toxic information into the condensed graph by injecting triggers into the original graph during the condensation process, which is different from all previous graph backdoor attacks that perform attacks during the model training stage. Extensive experiments across multiple datasets, GNN architectures, and dataset condensation methods demonstrate that our proposed method achieves impressive attack performance and utility performance. The resilience of our method against the defense mechanisms are also verified via experiments. We hope this work could set the stage for future research in the field of graph condensation security and raise awareness about the related implications among the research community.

### X. ACKNOWLEDGEMENTS

This work is supported by National Key Research and Development Program of China under Grant 2022YFA1004102, and in part by the Guangdong Major Project of Basic and Applied Basic Research under Grant 2023B0303000010. This work has also been supported by Hong Kong Research Grants Council through Theme-based Research Scheme (project no. T43-513/23-N).

### REFERENCES

<span id="page-11-0"></span>[1] X. Sun, H. Cheng, and et al., "Self-supervised hypergraph representation learning for sociological analysis," *TKDE*, 2023.

- <span id="page-12-0"></span>[2] Y. Yang, L. Xia, D. Luo, K. Lin, and C. Huang, "Graphpro: Graph pretraining and prompt learning for recommendation," in *Proc. of the Web Conference'2024*, 2024.
- <span id="page-12-1"></span>[3] K. Han, B. Lakshminarayanan, and et al., "Reliable graph neural networks for drug discovery under distributional shift," *arXiv 2111.12951*, 2021.
- <span id="page-12-2"></span>[4] J. Li and et al., "Empowering molecule discovery for molecule-caption translation with large language models: A chatgpt perspective," *TKDE*, 2024.
- <span id="page-12-3"></span>[5] J. Wu, W. Fan, J. Chen, S. Liu, Q. Liu, R. He, and et al., "Dataset condensation for recommendation," *arXiv 2310.01038*, 2023.
- [6] J. Wu, W. Fan, and et al., "Disentangled contrastive learning for social recommendation," in *Proc. of CIKM'2022*, 2022.
- <span id="page-12-4"></span>[7] W. Fan, Z. Zhao, J. Li, and et al., "Recommender systems in the era of large language models (llms)," *arXiv 2307.02046*, 2023.
- <span id="page-12-5"></span>[8] M. Hashemi and et al., "A comprehensive survey on graph reduction: Sparsification, coarsening, and condensation," *arXiv 2402.03358v3*, 2024.
- [9] J. Fang, W. Liu, and et al., "Evaluating post-hoc explanations for graph neural networks via robustness analysis," in *Proc. of NeurIPS'2023*, 2023.
- [10] W. Fan, S. Wang, J. Huang, and et al., "Graph machine learning in the era of large language models (llms)," *arXiv 2404.14928*, 2024.
- <span id="page-12-6"></span>[11] Y. Ding, W. Fan, X. Huang, and Q. Li, "Large language models for graph learning," in *Companion Proc. of the Web Conference 2024*, 2024.
- <span id="page-12-7"></span>[12] Y. Liu, D. Bo, and C. Shi, "Graph condensation via eigenbasis matching," *arXiv 2310.09202*, 2023.
- <span id="page-12-14"></span>[13] W. Jin, X. Tang, H. Jiang, Z. Li, and et al., "Condensing graphs via one-step gradient matching," in *Proc. of KDD'2022*, 2022.
- <span id="page-12-29"></span>[14] J. Fang, X. Li, Y. Sui, and et al., "Exgc: Bridging efficiency and explainability in graph condensation," in *Proc. of WebConference'2024*, 2024.
- <span id="page-12-15"></span>[15] X. Zheng, M. Zhang, C. Chen, Q. V. H. Nguyen, X. Zhu, and S. Pan, "Structure-free graph condensation: From large-scale graphs to condensed graph-free data," in *Proc. of NeurIPS'2023*, 2023.
- <span id="page-12-16"></span>[16] Z. Xu, Y. Chen, M. Pan, H. Chen, and et al., "Kernel ridge regressionbased graph dataset distillation," in *Proc. of KDD'2023*, 2023.
- <span id="page-12-47"></span>[17] Z. Liu, C. Zeng, and G. Zheng, "Graph data condensation via selfexpressive graph structure reconstruction," in *Proc. of KDD'2024*, 2024.
- <span id="page-12-8"></span>[18] L. Wang, W. Fan, J. Li, Y. Ma, and Q. Li, "Fast graph condensation with structure-based neural tangent kernel," in *Proc. of WebConference'2024*, 2023.
- <span id="page-12-9"></span>[19] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah, "Graph condensation for graph neural networks," in *Proc. of ICLR'2022*, 2022.
- <span id="page-12-10"></span>[20] X. Gao, J. Yu, W. Jiang, T. Chen, and W. Zhang, "Graph condensation: A survey," *arXiv 2401.11720v1*, 2024.
- <span id="page-12-44"></span>[21] H. XU, L. Zhang, Y. Ma, S. Zhou, Z. Zheng, and J. Bu, "A survey on graph condensation," *arXiv 2402.02000*, 2024.
- <span id="page-12-11"></span>[22] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Backdoor attacks against dataset distillation," in *Proc. of NDSS'2023*, 2023.
- <span id="page-12-12"></span>[23] B. Wang, Y. Yao, and et al., "Neural cleanse: Identifying and mitigating backdoor attacks in neural networks," in *S&P'2019*, 2019.
- [24] I. J. Goodfellow, J. Shlens, and C. Szegedy, "Explaining and harnessing adversarial examples," in *Proc. of ICLR'2015*, 2015.
- [25] B. Li and Y. Vorobeychik, "Scalable Optimization of Randomized Operational Decisions in Adversarial Classification Settings," in *Proc. of ICML'2015*, 2015.
- [26] W. Fan, S. Wang, X. Wei, X. Mei, and Q. Li, "Untargeted black-box attacks for social recommendations," *arXiv 2311.07127*, 2023.
- <span id="page-12-20"></span>[27] J. Fang, X. Wang, and et al., "Cooperative explanations of graph neural networks," in *Proc. of WSDM'2023*, 2023.
- <span id="page-12-13"></span>[28] L. Ning and et al., "Interpretation-empowered neural cleanse for backdoor attacks," in *Companion Proc. of the Web Conferenc 2024*, 2024.
- <span id="page-12-17"></span>[29] S. Wang, Z. Chen, X. Yu, D. Li, J. Ni, L.-A. Tang, J. Gui, Z. Li, H. Chen, and P. S. Yu, "Heterogeneous graph matching networks for unknown malware detection," in *Proc. of the IJCAI'19*. International Joint Conferences on Artificial Intelligence Organization, 2019.
- <span id="page-12-18"></span>[30] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Graph-based security and privacy analytics via collective classification with joint weight learning and propagation," in *Proc. of NDSS'2019*. The Internet Society, 2019.
- <span id="page-12-19"></span>[31] H. Chen, O. Engkvist, Y. Wang, M. Olivecrona, and T. Blaschke, "The rise of deep learning in drug discovery," *Drug Discovery Today*, 2018.

- <span id="page-12-21"></span>[32] D. Zügner and S. Günnemann, "Adversarial attacks on graph neural networks via meta learning," in *Proc. of ICLR'2019*, 2019.
- <span id="page-12-23"></span>[33] E. Dai, M. Lin, X. Zhang, and S. Wang, "Unnoticeable backdoor attacks on graph neural networks," in *Proc. of WebConference'2023*, 2023.
- <span id="page-12-22"></span>[34] K. Wang, Y. Liang, and et al., "Brave the wind and the waves: Discovering robust and generalizable graph lottery tickets," *TPAMI*, 2023.
- <span id="page-12-24"></span>[35] Z. Xi, R. Pang, S. Ji, and T. Wang, "Graph backdoor," in *Proc. of USENIX Security'2021*, 2021.
- <span id="page-12-25"></span>[36] Y. Liu, J. Gu, and et al., "DREAM: Efficient dataset distillation by representative matching," in *Proc. of ICCV'2023*, 2023.
- <span id="page-12-26"></span>[37] Y. Xu, Y.-L. Li, and et al., "Distill gold from massive ores: Efficient dataset distillation via critical samples selection," *arXiv 2305.18381*, 2023.
- <span id="page-12-27"></span>[38] J. Wu, Q. Liu, and et al., "Leveraging large language models (llms) to empower training-free dataset condensation for content-based recommendation," *arXiv 2310.09874*, 2023.
- <span id="page-12-28"></span>[39] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *Proc. of ICLR'2021*, 2021.
- <span id="page-12-30"></span>[40] M.-Y. Chung, S.-Y. Chou, and et al., "Rethinking backdoor attacks on dataset distillation: A kernel method perspective," in *Proc. of ICLR'2024*, 2024.
- <span id="page-12-31"></span>[41] J. Zhang and et al., "Graph unlearning with efficient partial retraining," in *Companion Proc. of the Web Conference'2024*, 2024.
- <span id="page-12-32"></span>[42] I. Hubara, M. Courbariaux, D. Soudry, R. El-Yaniv, and Y. Bengio, "Binarized neural networks," in *Proc. of NeurIPS'2016*, 2016.
- <span id="page-12-33"></span>[43] T. N. Kipf and M. Welling, "Semi-supervised classification with graph convolutional networks," in *Proc. of ICLR'2017*, 2017.
- <span id="page-12-34"></span>[44] H. Zeng, H. Zhou, A. Srivastava, R. Kannan, and V. K. Prasanna, "Graphsaint: Graph sampling based inductive learning method," in *Proc. of ICLR'2020*, 2020.
- <span id="page-12-35"></span>[45] W. L. Hamilton, Z. Ying, and J. Leskovec, "Inductive representation learning on large graphs," in *Proc. of NeurIPS'2017*, 2017.
- <span id="page-12-36"></span>[46] M. Fey and J. E. Lenssen, "Fast graph representation learning with pytorch geometric," *arXiv 1903.02428*, 2019.
- <span id="page-12-37"></span>[47] F. Wu, A. Souza, T. Zhang, C. Fifty, and et al., "Simplifying graph convolutional networks," in *Proc. of ICML'2019*, 2019.
- <span id="page-12-38"></span>[48] A. Bojchevski and S. Günnemann, "Adversarial attacks on node embeddings via graph poisoning," in *Proc. of ICML'2019*, 2019.
- <span id="page-12-39"></span>[49] H. Chang, Y. Rong, and et al., "A restricted black-box adversarial framework towards attacking graph embedding models," in *Proc. of AAAI'2020*, 2020.
- <span id="page-12-40"></span>[50] Y. Hu, H. You, and et al., "Graph-mlp: Node classification without message passing in graph," *arXiv 2106.04051*, 2021.
- <span id="page-12-41"></span>[51] J. Gasteiger, A. Bojchevski, and et al., "Predict then propagate: Graph neural networks meet personalized pagerank," in *Proc. of ICLR'2019*, 2019.
- <span id="page-12-42"></span>[52] M. Defferrard, X. Bresson, and et al., "Convolutional neural networks on graphs with fast localized spectral filtering," in *Proc. of NeurIPS*, 2016.
- <span id="page-12-43"></span>[53] Z. Zhang, J. Jia, B. Wang, and N. Z. Gong, "Backdoor attacks to graph neural networks," in *Proc. of SACMAT'2021*, 2021.
- <span id="page-12-45"></span>[54] L. Xia, B. Kao, and C. Huang, "Opengraph: Towards open graph foundation models," *arXiv 2403.01121*, 2024.
- <span id="page-12-46"></span>[55] X. Ren, W. Wei, and et al., "Representation learning with large language models for recommendation," in *Proc. of the Web Conference'2024*, 2024.
- <span id="page-12-48"></span>[56] B. Yang, K. Wang, Q. Sun, C. Ji, X. Fu, H. Tang, Y. You, and J. Li, "Does graph distillation see like vision dataset counterpart?" in *Proc. of NeurIPS'2023*, 2023.
- <span id="page-12-49"></span>[57] T. Zhang and et al., "Two trades is not baffled: Condensing graph via crafting rational gradient matching," *arXiv 2402.04924v2*, 2024.
- <span id="page-12-50"></span>[58] Y. Zhang, T. Zhang, K. Wang, Z. Guo, Y. Liang, X. Bresson, W. Jin, and Y. You, "Navigating complexity: Toward lossless graph condensation via expanding window matching," *arXiv 2402.05011*, 2024.
- <span id="page-12-51"></span>[59] Q. Feng, Z. Jiang, R. Li, Y. Wang, N. Zou, J. Bian, and X. Hu, "Fair graph distillation," in *Proc. of NeurIPS'2023*, 2023.
- <span id="page-12-52"></span>[60] X. Gao, T. Chen, and et al., "Graph condensation for inductive node representation learning," in *Proc. of ICDE'2024*, 2024.
- <span id="page-12-53"></span>[61] Y. Liu, R. Qiu, and Z. Huang, "Cat: Balanced continual graph learning with graph condensation," in *Proc. of ICDM'2023*, 2023.
- <span id="page-12-54"></span>[62] M. Liu, S. Li, X. Chen, and L. Song, "Graph condensation via receptive field distribution matching," *arXiv 2206.13697*, 2022.

- <span id="page-13-0"></span>[63] M. Gupta, S. Manchanda, and et al., "Mirage: Model-agnostic graph distillation for graph classification," in *Proc. of ICLR'2024*, 2024.
- <span id="page-13-1"></span>[64] D. Zügner, A. Akbarnejad, and S. Günnemann, "Adversarial attacks on neural networks for graph data," in *Proc. of KDD'2018*, 2018.
- <span id="page-13-2"></span>[65] W. Fan, X. Zhao, Q. Li, T. Derr, Y. Ma, H. Liu, J. Wang, and J. Tang, "Adversarial attacks for black-box recommender systems via copying transferable cross-domain user profiles," *TKDE*, 2023.
- <span id="page-13-3"></span>[66] Y. Sun, S. Wang, X. Tang, and et al., "Adversarial attacks on graph neural networks via node injections: A hierarchical reinforcement learning approach," in *Proc. of the Web Conference'2020*, 2020.
- <span id="page-13-4"></span>[67] S. Yang, B. G. Doan, P. Montague, O. De Vel, T. Abraham, S. Camtepe, D. C. Ranasinghe, and S. S. Kanhere, "Transferable graph backdoor attack," in *Proc. of RAID'2022*, 2022.
- [68] J. Xu and S. Picek, "Poster: Clean-label backdoor attack on graph neural networks," in *Proc. of CCS'2022*, 2022.
- [69] J. Xu, M. J. Xue, and S. Picek, "Explainability-based backdoor attacks against graph neural networks," in *Proc. of WiseML'2021*, 2021.
- [70] X. Yang, G. Li, C. Zhang, M. Han, and W. Yang, "Percba: Persistent clean-label backdoor attacks on semi-supervised graph node classification," in *Proc. of IJCAI'2023' on AISafety-SafeRL*, 2023.
- [71] J. Dai and Z. Xiong, "A semantic backdoor attack against graph convolutional networks," *arXiv 2302.14353*, 2023.
- <span id="page-13-5"></span>[72] J. Zhang, R. Xue, and et al., "Linear-time graph neural networks for scalable recommendations," in *Proc. of the Web Conference'2024*, 2024.
- <span id="page-13-6"></span>[73] Y. Sheng, R. Chen, G. Cai, and L. Kuang, "Backdoor attack of graph neural networks based on subgraph trigger," in *Proc. of Collaborate-Com'2021*, 2021.
- <span id="page-13-7"></span>[74] H. Zhang, J. Chen, L. Lin, J. Jia, and D. Wu, "Graph contrastive backdoor attacks," in *Proc. of ICML'2023*, 2023.
- <span id="page-13-8"></span>[75] Z. Li, H. Sun, P. Xia, H. Li, B. Xia, Y. Wu, and B. Li, "Efficient backdoor attacks for deep neural networks in real-world scenarios," in *Proc. of ICLR'2024*, 2024.
- <span id="page-13-9"></span>[76] N. Hung-Quang, N.-H. Nguyen, T.-A. Ta, T. Nguyen-Tang, K.-S. Wong, H. Thanh-Tung, and K. D. Doan, "Wicked oddities: Selectively poisoning for effective clean-label backdoor attacks," in *Proc. of ICLR'2025*, 2025.