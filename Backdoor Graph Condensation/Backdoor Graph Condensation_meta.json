{"table_of_contents": [{"title": "Backdoor Graph Condensation", "heading_level": null, "page_id": 0, "polygon": [[153.75, 54.0439453125], [456.75, 54.0439453125], [456.75, 76.2802734375], [153.75, 76.2802734375]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 433.5], [214.5, 433.5], [214.5, 443.1796875], [133.5, 443.1796875]]}, {"title": "II. PRELIMINARIES", "heading_level": null, "page_id": 1, "polygon": [[131.25, 681.75], [216.75, 681.75], [216.75, 691.83984375], [131.25, 691.83984375]]}, {"title": "A. Graph Condensation", "heading_level": null, "page_id": 1, "polygon": [[310.5, 215.25], [410.58984375, 215.25], [410.58984375, 225.650390625], [310.5, 225.650390625]]}, {"title": "B. Graph Backdoor Attack", "heading_level": null, "page_id": 1, "polygon": [[310.5, 538.5], [423.0, 538.5], [423.0, 548.3671875], [310.5, 548.3671875]]}, {"title": "III. PROBLEM FORMULATION", "heading_level": null, "page_id": 2, "polygon": [[109.5, 413.25], [238.763671875, 414.75], [238.763671875, 424.6171875], [109.5, 424.6171875]]}, {"title": "IV. METHODOLOGY", "heading_level": null, "page_id": 2, "polygon": [[390.26953125, 451.5], [480.75, 451.5], [480.75, 460.96875], [390.26953125, 460.96875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[309.75, 466.5], [364.869140625, 466.5], [364.869140625, 476.4375], [309.75, 476.4375]]}, {"title": "<PERSON><PERSON> Poisoned Node Selection", "heading_level": null, "page_id": 3, "polygon": [[47.625732421875, 554.25], [164.25, 554.25], [164.25, 564.99609375], [47.625732421875, 564.99609375]]}, {"title": "<PERSON><PERSON> Generation", "heading_level": null, "page_id": 3, "polygon": [[310.5, 369.0], [405.0, 369.0], [405.0, 378.2109375], [310.5, 378.2109375]]}, {"title": "D. Optimization", "heading_level": null, "page_id": 4, "polygon": [[48.0, 399.48046875], [116.25, 399.48046875], [116.25, 408.76171875], [48.0, 408.76171875]]}, {"title": "V<PERSON> EXPERIMENTAL SETTINGS", "heading_level": null, "page_id": 5, "polygon": [[372.33984375, 333.73828125], [501.0, 333.73828125], [501.0, 343.79296875], [372.33984375, 343.79296875]]}, {"title": "VI. EXPERIMENT", "heading_level": null, "page_id": 6, "polygon": [[134.69677734375, 678.75], [212.25, 678.75], [212.25, 688.74609375], [134.69677734375, 688.74609375]]}, {"title": "A. Attack Performance and Model Utility (RQ1)", "heading_level": null, "page_id": 6, "polygon": [[309.75, 465.0], [511.5, 465.0], [511.5, 475.27734375], [309.75, 475.27734375]]}, {"title": "B. Attack Performance Comparison", "heading_level": null, "page_id": 7, "polygon": [[47.73779296875, 335.25], [195.75, 335.25], [195.75, 345.7265625], [47.73779296875, 345.7265625]]}, {"title": "C. Study on Different GNN Architectures (RQ2)", "heading_level": null, "page_id": 7, "polygon": [[48.0, 681.0], [246.75, 681.0], [246.75, 691.453125], [48.0, 691.453125]]}, {"title": "D. Defenses (RQ3)", "heading_level": null, "page_id": 7, "polygon": [[310.5, 466.5], [391.5, 466.5], [391.5, 476.4375], [310.5, 476.4375]]}, {"title": "E. Ablation Study on Poisoned Node Selection", "heading_level": null, "page_id": 8, "polygon": [[48.0, 480.75], [240.75, 480.75], [240.75, 490.359375], [48.0, 490.359375]]}, {"title": "F. Ablation Study on Trigger Generator", "heading_level": null, "page_id": 8, "polygon": [[47.58837890625, 681.0], [213.75, 681.0], [213.75, 691.453125], [47.58837890625, 691.453125]]}, {"title": "G. Ablation Study on Directed Attack", "heading_level": null, "page_id": 8, "polygon": [[310.78125, 609.75], [467.25, 609.75], [467.25, 619.13671875], [310.78125, 619.13671875]]}, {"title": "H. Hyper-parameter Analysis", "heading_level": null, "page_id": 9, "polygon": [[47.25, 379.951171875], [171.0, 379.951171875], [171.0, 389.619140625], [47.25, 389.619140625]]}, {"title": "VII. RELATED WORK", "heading_level": null, "page_id": 9, "polygon": [[389.07421875, 594.0], [485.25, 594.0], [485.25, 604.828125], [389.07421875, 604.828125]]}, {"title": "VIII. DISCUSSION", "heading_level": null, "page_id": 10, "polygon": [[396.75, 476.25], [477.826171875, 476.25], [477.826171875, 486.4921875], [396.75, 486.4921875]]}, {"title": "IX. CONCLUSION", "heading_level": null, "page_id": 11, "polygon": [[396.24609375, 267.0], [476.25, 267.0], [476.25, 277.6640625], [396.24609375, 277.6640625]]}, {"title": "X. <PERSON>OWLEDGEMENTS", "heading_level": null, "page_id": 11, "polygon": [[380.25, 570.75], [493.6640625, 570.75], [493.6640625, 580.078125], [380.25, 580.078125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 11, "polygon": [[407.900390625, 681.75], [465.75, 681.75], [465.75, 691.453125], [407.900390625, 691.453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 103], ["Text", 13], ["SectionHeader", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 683], ["Line", 128], ["Text", 6], ["TextInlineMath", 4], ["ListItem", 3], ["SectionHeader", 3], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1128], ["Line", 153], ["Text", 16], ["TextInlineMath", 11], ["Equation", 6], ["SectionHeader", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2180, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["Line", 131], ["TextInlineMath", 7], ["Text", 6], ["Equation", 5], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1260], ["Line", 149], ["TableCell", 90], ["ListItem", 14], ["TextInlineMath", 11], ["Text", 10], ["Reference", 9], ["Equation", 7], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10550, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 938], ["TableCell", 287], ["Line", 126], ["Text", 8], ["TextInlineMath", 2], ["Equation", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 18056, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["Line", 117], ["Text", 9], ["ListItem", 3], ["SectionHeader", 2], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 594], ["TableCell", 214], ["Line", 132], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12038, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 794], ["Span", 717], ["Line", 112], ["Text", 6], ["SectionHeader", 3], ["Table", 2], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 21005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["TableCell", 341], ["Line", 151], ["Text", 7], ["Table", 3], ["Reference", 3], ["Figure", 2], ["SectionHeader", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 11672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["TableCell", 169], ["Line", 104], ["Text", 6], ["Caption", 3], ["Reference", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 129], ["Text", 10], ["ListItem", 3], ["SectionHeader", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 621], ["Line", 147], ["ListItem", 61], ["Reference", 55], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 37], ["ListItem", 14], ["Reference", 10], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Backdoor Graph Condensation"}