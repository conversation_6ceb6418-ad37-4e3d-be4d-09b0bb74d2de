# <span id="page-0-1"></span>Backdoor Attacks Against Dataset Distillation

Yugeng <PERSON><sup>1</sup> <PERSON><sup>1</sup> <PERSON><sup>1</sup> <PERSON><sup>2</sup> <PERSON><sup>1</sup>

<sup>1</sup>CISPA Helmholtz Center for Information Security <sup>2</sup>NetApp

## Abstract

Dataset distillation has emerged as a prominent technique to improve data efficiency when training machine learning models. It encapsulates the knowledge from a large dataset into a smaller synthetic dataset. A model trained on this smaller distilled dataset can attain comparable performance to a model trained on the original training dataset. However, the existing dataset distillation techniques mainly aim at achieving the best trade-off between resource usage efficiency and model utility. The security risks stemming from them have not been explored. This study performs the first backdoor attack against the models trained on the data distilled by dataset distillation models in the image domain. Concretely, we inject triggers into the synthetic data during the distillation procedure rather than during the model training stage, where all previous attacks are performed. We propose two types of backdoor attacks, namely NAIVEATTACK and DOORPING. NAIVEATTACK simply adds triggers to the raw data at the initial distillation phase, while DOORPING iteratively updates the triggers during the entire distillation procedure. We conduct extensive evaluations on multiple datasets, architectures, and dataset distillation techniques. Empirical evaluation shows that NAIVEATTACK achieves decent attack success rate (*ASR*) scores in some cases, while DOORPING reaches higher *ASR* scores (close to 1.0) in all cases. Furthermore, we conduct a comprehensive ablation study to analyze the factors that may affect the attack performance. Finally, we evaluate multiple defense mechanisms against our backdoor attacks and show that our attacks can practically circumvent these defense mechanisms.<sup>1</sup>

## 1 Introduction

Deep neural networks (DNNs) have established themselves as the cornerstone for a wide range of applications. To achieve state-of-the-art performance, it becomes a new norm that large-scale datasets of millions of samples are used to train modern DNN models [\[11,](#page-18-0) [28,](#page-19-0) [57,](#page-20-0) [73\]](#page-20-1). Unfortunately, this ever-increasing scale of data significantly increases the cost [\[63\]](#page-20-2) of storage, training time, energy usage, etc.

*Dataset distillation* is an emerging research direction with the goal of improving the data efficiency when training DNN models [\[5,](#page-18-1)[52,](#page-19-1)[53,](#page-19-2)[79,](#page-20-3)[88–](#page-21-0)[90\]](#page-21-1). Its core idea is distilling a large

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image illustrates the process of dataset distillation. On the left, an "Original Training Dataset (X)" is shown, consisting of a grid of various animal and vehicle images. A dashed arrow indicates that this dataset is used to "Train" a neural network, resulting in "Comparable Performance." On the right, the "Original Training Dataset (X)" is fed into a "Dataset Distillation" process, represented by a neural network model with nodes and connections labeled with parameters "(θ)". This process generates a "Distilled Dataset (X̃)", which is a collection of abstract image representations, each labeled with a class name such as "Airplane", "Automobile", "Bird", "Cat", "Deer", "Dog", "Frog", "Horse", "Ship", and "Truck". The distilled dataset is then used to "Train" another neural network, which also achieves "Comparable Performance". The distillation process is associated with two loss functions: L = l(X, θ) and L̃ = l(X̃, θ).

Figure 1: Overview of dataset distillation. The dataset distillation model  $\theta$  distills the original training dataset X into a smaller dataset  $\tilde{X}$ . The model trained on the distilled dataset  $\tilde{X}$ can attain comparable performance to a model trained on the original training dataset X.

dataset into a smaller synthetic dataset (see [Figure 1](#page-0-0) for illustration). A model trained on this smaller distilled dataset can attain comparable performance to a model trained on the original training dataset. For instance, the pioneering work by Wang et al. [\[79\]](#page-20-3) compresses 50,000 training images of the CIFAR10 dataset into only 100 synthetic images (i.e., 10 images per class). A standard DNN model trained on these 100 images yields a test-time classification performance of 0.646, compared to 0.848 of the model trained on the original full dataset. Owing to its advantages, such as less storage, training, and energy costs, we expect that data distillation will be offered as a service and plays an essential role in many machine learning applications.<sup>2</sup> For those researchers and companies without the capacity to store or the capability to process a vast amount of data, using a distilled dataset from dataset distillation services will become a promising alternative.

Despite its novel advantage in condensing the information of the entire dataset in a smaller dataset, dataset distillation is essentially a DNN model (see [Section 2\)](#page-1-0). Previous studies [\[43,](#page-19-3)[47\]](#page-19-4) have shown that DNN models (e.g., image classifiers, language models) are vulnerable to security and privacy attacks, such as adversarial attacks [\[21,](#page-18-2) [35,](#page-19-5) [55\]](#page-20-4), inference attacks [\[18,](#page-18-3)[26,](#page-19-6)[54,](#page-19-7)[59,](#page-20-5)[61,](#page-20-6)[66\]](#page-20-7), backdoor attacks [\[24,](#page-19-8)[60,](#page-20-8)[76,](#page-20-9)[86\]](#page-20-10). Yet, existing dataset distillation efforts [\[5,](#page-18-1) [52,](#page-19-1) [53,](#page-19-2) [89\]](#page-21-2) mainly

<sup>1</sup>Code is available at <https://github.com/liuyugeng/baadd>.

<sup>2</sup>[https://ai.googleblog.com/2021/12/training-machine](https://ai.googleblog.com/2021/12/training-machine-learning-models-more.html)[learning-models-more.html](https://ai.googleblog.com/2021/12/training-machine-learning-models-more.html).

<span id="page-1-2"></span>focus on designing new algorithms to distill a large dataset better. The potential security and privacy issues of dataset distillation (e.g., the implications of using a distilled dataset from third parties) are left unexplored.

Motivation. In this study, we consider the backdoor attack that a malicious dataset distillation service provider can launch from the upstream (i.e., data distillation provider). We exclusively focus on the dataset distillation in the image domain. Note that the distilled dataset is used for training the downstream models (i.e., the models consuming the distilled datasets). Existing backdoor attacks inject triggers to the original clean data and then train a model using a mixed set of clean and backdoored data, i.e., perform the trigger injection process on the data that are fed directly to the model. These classic attacks cannot be directly applied to the distilled datasets to backdoor the downstream models since these distilled datasets are small (e.g., 10 synthetic images for SVHN [\[10\]](#page-18-4) and 100 synthetic images for CIFAR10 [\[1\]](#page-18-5)) and not sufficient enough to inject the backdoor. First, human inspection can quickly mitigate such attacks since it is trivial to inspect 100 images. We also carry out an experiment using a CIFAR10 distilled dataset generated by DD and ConvNet and the commonly used 0.01 poisoning ratio [\[2,](#page-18-6)[3,](#page-18-7)[50,](#page-19-9)[64,](#page-20-11)[92\]](#page-21-3) (i.e., only 1 image for the distilled dataset with 100 samples) to attempt to train a backdoored model. In this case, the model utility score is 0.405 while the attack success rate (*ASR*) score only reaches 0.152. It is evident that the attackers cannot implant the backdoor in the model using the classical backdoor attack approaches. To overcome this limitation, we make the first attempt to answer, "is it possible to inject triggers into such a tiny distilled dataset and launch backdoor attacks on the downstream model?"

Our Contributions. In this paper, we present two backdoor attacks, namely NAIVEATTACK and DOORPING. NAIVEATTACK adds triggers to the original data at the initial distillation phase. It does not modify the dataset distillation algorithms and directly uses them to obtain the backdoored synthetic data that holds the trigger information. Restricted by the distillation algorithms, those triggers may not always be retained in the distilled dataset. To resolve this problem, we further propose DOORPING that iteratively optimizes the triggers throughout the distillation procedure. In this way, we inject triggers into the distilled dataset during the distillation process rather than directly injecting triggers into the training data. To demonstrate the effectiveness of our backdoor attacks, we conduct extensive experiments on four benchmark datasets, two widely-used model architectures, and two representative dataset distillation techniques. Empirical results show that both of our attacks maintain high model utility. NAIVEATTACK achieves a reasonable attack success rate (*ASR*) in some cases, while DOORPING consistently attains a higher *ASR* (close to 100%) in all cases. Furthermore, we conduct a comprehensive ablation study to analyze the factors that may affect the attack performance and show that our backdoor attacks are robust in different settings. Finally, we also evaluate our attacks with nine defense mechanisms at three detection levels. The experimental results indicate these defenses cannot effectively mitigate our attacks. Our contributions can be summarized as the following:

- We perform the first backdoor attacks against dataset distillation. Our attacks inject triggers into a tiny distilled dataset during the distillation process in the upstream and launch backdoor attacks against the downstream models trained by this distilled dataset.
- We propose two types of backdoor attacks under different settings, including NAIVEATTACK and DOORPING. Extensive experiments demonstrate that NAIVEAT-TACK can achieve decent attack performance and DOORPING consistently achieves remarkable attack performance.
- We conduct a comprehensive ablation study to evaluate our attacks in different settings. Empirical results show that both attacks are robust in most settings.
- We evaluate our attacks under nine state-of-the-art defenses at three defense levels. The experimental results show that our novel attacks can practically outmaneuver these defense mechanisms.

## <span id="page-1-0"></span>2 Preliminary

## 2.1 Dataset Distillation

Overview. Dataset distillation (see [Figure 1](#page-0-0) for illustration) is an emerging topic in machine learning research [\[5,](#page-18-1) [52,](#page-19-1) [53,](#page-19-2) [79,](#page-20-3) [88–](#page-21-0)[90\]](#page-21-1). Its goal is to distill a large dataset into a smaller synthetic dataset. A model trained on this smaller dataset can attain comparable or better performance than a model trained on the full dataset. In turn, dataset distillation reduces the resources (e.g., memory, GPU hours, etc.) required to train an effective model.

Workflow. At a high level, the distillation process works as follows. The input is the original full dataset  $\mathbf{X} = {\mathbf{x}_i}_{i=1}^N$ . The output is a synthetic dataset  $\tilde{\mathbf{X}} = {\{\tilde{\mathbf{x}}_i\}}_{i=1}^M$ , where  $M \ll N$ . The core of the distillation process is training a model parameterized by θ. The optimization goal is minimizing the learning loss between the original training dataset  $\mathcal{L} = \ell(\mathbf{X}, \mathbf{\theta})$ and the distilled dataset  $\tilde{L} = \ell(\tilde{\mathbf{X}}, \theta)$ , where  $\ell(\cdot, \cdot)$  is a taskspecific loss (e.g., cross-entropy loss).  $\mathcal L$  and  $\mathcal L$  are combined in a task-specific manner to update  $\tilde{\mathbf{X}}$  (see [Section 2.2\)](#page-1-1). The distilled dataset  $\tilde{\mathbf{X}}$ , instead of the entire dataset  $\mathbf{X}$ , is later used to train the downstream model.

Note. It is important to note that dataset distillation is orthogonal to knowledge distillation [\[23,](#page-19-10) [27,](#page-19-11) [77\]](#page-20-12). Knowledge distillation (i.e., model distillation) is at the model level and distills the knowledge from a large deep neural network (i.e., teacher model) into a small network (i.e., student model). The goal is to obtain a smaller student model that offers a competitive or even superior performance than a larger teacher model.

## <span id="page-1-1"></span>2.2 Dataset Distillation Techniques

We introduce two state-of-the-art dataset distillation techniques used in our study, namely *dataset distillation* [\[79\]](#page-20-3) and *dataset condensation with gradient matching* [\[90\]](#page-21-1). Dataset

<span id="page-2-6"></span><span id="page-2-0"></span>

| <b>Algorithm 1 Dataset Distillation</b> |
|-----------------------------------------|
|-----------------------------------------|

| Input: The original training dataset <b>X</b> , learning rate $\eta$                                                             |
|----------------------------------------------------------------------------------------------------------------------------------|
| <b>Output:</b> The distilled dataset <b>X</b>                                                                                    |
| 1: Randomly initialize distilled datasets <b>X</b>                                                                               |
| 2: while update distilled images do                                                                                              |
| 3: Initialize the model $\theta_0$                                                                                               |
| 4: while update model do                                                                                                         |
| 5: $\theta_{i+1} = \theta_i - \eta \nabla_{\theta_i} \ell(\tilde{\mathbf{X}}, \theta_i)$                                         |
| 6: end while                                                                                                                     |
| 7: $\mathcal{L} = \ell(\mathbf{X}, \boldsymbol{\theta}), \, \tilde{\mathcal{L}} = \ell(\tilde{\mathbf{X}}, \boldsymbol{\theta})$ |
| 8: $\tilde{\mathbf{X}} \leftarrow \text{UPDATE}(\tilde{\mathbf{X}}, \mathcal{L}, \tilde{\mathcal{L}})$                           |
| 9: end while                                                                                                                     |

distillation [\[79\]](#page-20-3) (abbreviated as DD) is the pioneering work of this research direction. Dataset condensation with gradient matching [\[90\]](#page-21-1) (abbreviated as DC) is a recent dataset distillation technique. We unify these two methods in [Algorithm 1](#page-2-0) and use it to guide the description of these two algorithms. DD Algorithm [\[79\]](#page-20-3). DD algorithm is the first work in the domain of dataset distillation. The core idea of the DD algorithm is directly minimizing a model loss on both  $\tilde{\mathbf{X}}$  and  $\mathbf{X}$ . To attain the goal, DD algorithm adopts a bi-level optimization approach to iteratively update both  $\tilde{\mathbf{X}}$  and  $\theta$ , as shown in [Equation 1.](#page-2-1)

<span id="page-2-1"></span>
$$
\tilde{\mathbf{X}}^* = \underset{\tilde{\mathbf{X}}}{\arg\min} \ell(\mathbf{X}, \boldsymbol{\theta})
$$
  
subject to  $\boldsymbol{\theta}^* = \underset{\boldsymbol{\theta}}{\arg\min} \ell(\tilde{\mathbf{X}}, \boldsymbol{\theta})$  (1)

It first uses the loss of the synthesized dataset  $\tilde{\mathbf{X}}$  (i.e.,  $\ell(\tilde{\mathbf{X}}, \theta)$ ) to update the distillation model θ. It then uses the loss of the original dataset **X** (i.e.,  $\ell(X, \theta)$ ) to update  $\hat{X}$ . In turn, for DD algorithm, UPDATE function at line 8 in [Algorithm 1](#page-2-0) is replaced by [Equation 2](#page-2-2) below, where  $η$  is the learning rate for updating the distilled images.

$$
\tilde{\mathbf{X}} = \tilde{\mathbf{X}} - \eta \nabla_{\tilde{\mathbf{X}}} \ell(\mathbf{X}, \theta) \tag{2}
$$

<span id="page-2-2"></span>DC Algorithm [\[90\]](#page-21-1). DC algorithm is another fundamental work in the domain of dataset distillation. The core idea of DC algorithm is learning a distilled dataset  $\tilde{\mathbf{X}}$  that a model trained on it (denoted as  $\theta_{\tilde{X}}$ ) can achieve two goals. The first goal is that  $\theta_{\tilde{X}}$  attains comparable performance of a model trained on the original dataset **X** (denoted as  $\theta$ **x**). The second goal is that  $\theta_{\tilde{X}}$  converges to a similar solution of  $\theta_X$  in the parameter space (i.e.,  $\theta_{\tilde{X}} \approx \theta_{X}$ ). To achieve these goals, the DC algorithm also adopts a bi-level optimization approach but with a different optimization object function (see [Equation 3](#page-2-3)) below).

<span id="page-2-3"></span>
$$
\tilde{\mathbf{X}}^* = \min_{\tilde{\mathbf{X}}} \gamma(\theta_{\tilde{\mathbf{X}}}, \theta_{\mathbf{X}})
$$
  
subject to  $\theta^* = \underset{\theta}{\arg\min} \ell(\tilde{\mathbf{X}}, \theta)$  (3)

where  $\theta_{\mathbf{X}} = \arg \min_{\theta} \ell(\mathbf{X}, \theta)$  and  $\gamma(\cdot, \cdot)$  is a distance function. In practice,  $\theta_X$  can be trained first in an offline stage [\[90\]](#page-21-1) and <span id="page-2-4"></span>then used as the target parameter vector in [Equation 3.](#page-2-3) In turn, for DC algorithm, UPDATE function at line 8 in [Algo](#page-2-0)[rithm 1](#page-2-0) is replaced by [Equation 4](#page-2-4) below.

$$
\tilde{\mathbf{X}} = \gamma(\nabla_{\theta_{\tilde{\mathbf{X}}}} \ell(\tilde{\mathbf{X}}, \theta_{\tilde{\mathbf{X}}}), \nabla_{\theta_{\mathbf{X}}} \ell(\mathbf{X}, \theta_{\mathbf{X}}))
$$
(4)

Here, the distance function  $\gamma(\cdot,\cdot)$  is instantiated as a sum of layerwise losses as  $\sum_{h=1}^{H} d(\nabla_{\theta_{\bar{X}}^h} \ell(\tilde{X}, \theta_{\tilde{X}}), \nabla_{\theta_{\bar{X}}^h} \ell(X, \theta_X)),$ where *H* is the number of layers and  $d(\cdot, \cdot)$  is a distance function between flattened vectors of gradients corresponding to each output node in  $\theta_{\tilde{X}}$  and  $\theta_{X}$ .

Note. [Algorithm 1](#page-2-0) shows that DC and DD models leverage the same mechanism to update a model to distill a synthesized dataset  $\tilde{\mathbf{X}}$  (line 5 in [Algorithm 1\)](#page-2-0). The only difference is how the synthesized dataset  $\tilde{\mathbf{X}}$  is updated (line 8 in [Al](#page-2-0)[gorithm 1\)](#page-2-0). This observation enables us to design a unified backdoor attack that is effective for both algorithms in [Sec](#page-2-5)[tion 3.](#page-2-5)

### 2.3 Backdoor Attack

The backdoor attack is a training time attack. It implants a hidden backdoor (also called neural trojan [\[31,](#page-19-12) [46\]](#page-19-13)) into the target model via backdoored training samples. At the test time, the backdoored model performs well on the clean test samples but misbehaves only on the triggered samples. Formally, to launch a backdoor attack, the attacker controls the backdoored training data  $\mathcal{D}_T = \mathcal{D}_C \cup \mathcal{D}_P$ , where  $\mathcal{D}_C$  and  $\mathcal{D}_P$  respectively represents the clean training samples and the backdoored samples. Each sample  $\hat{\mathbf{x}}$  in  $\mathcal{D}_P$  is usually generated by a trigger-insertion function  $\mathcal{A}(\mathbf{x}, \mathbf{t}, \mathbf{m}) = \hat{\mathbf{x}}$ , where **x** denotes a clean sample, t denotes a trigger (either pre-defined or optimized), and m denotes a mask (i.e., the position where the trigger t is inserted). The model holder executes their machine learning model on  $\mathcal{D}_T$  to obtain the model  $\theta^*$ . In the inference stage, the backdoored model  $\theta^*$  tends to misclassify the triggered sample  $\hat{x}$  while maintaining good performance on the clean sample x. The effectiveness of a backdoor attack is commonly measured by *attack success rate (ASR)* and *clean test accuracy (CTA)* [\[6,](#page-18-8) [24,](#page-19-8) [46,](#page-19-13) [60\]](#page-20-8). The *ASR* measures its success rate in making  $\theta^*$  generate the wrong predictions to the target label given triggered samples. The *CTA* evaluates the utility of the model given clean samples. Additional details about backdoor attacks can be found in [Section 8.](#page-17-0)

## <span id="page-2-5"></span>3 Backdoor Attacks Against Dataset Distillation

## 3.1 Threat Model

Attack Scenarios. We envision the attacker as the malicious dataset distillation service provider [\[49,](#page-19-14) [68\]](#page-20-13). Two attack scenarios are taken into consideration in our study. The first scenario is that the victim commissions the attacker to distill a specific dataset on their behalf (e.g., using a third-party service to distill the dataset stored in AWS S3 buckets). This scenario is in line with the generic purpose of dataset distillation [\[79,](#page-20-3) [88,](#page-21-0) [90\]](#page-21-1). The second scenario is more on the practical side. Instead of buying the original training dataset with

<span id="page-3-4"></span><span id="page-3-1"></span>Image /page/3/Figure/0 description: The image illustrates a process involving a pre-defined trigger and a training dataset. A black square labeled 'Pre-defined Trigger' points to a grid of images labeled 'Original Training Dataset (X)'. An arrow leads from this dataset to a diagram representing a neural network with interconnected nodes of various colors. Another arrow points from the neural network to a sequence of small images labeled '~X'. Finally, '~X' is shown entering a diagram labeled 'Backdoored Model' which is part of a 'Downstream' process.

Figure 2: Trigger insertion via NAIVEATTACK.

millions of images, the victim opts to purchase a smaller synthesized dataset from the attacker to reduce the cost.

Attacker's Capability. As we can see in the aforementioned attack scenarios, the only capability we presuppose the attacker has is controlling the dataset distillation process. This assumption is practical since the attacker acts as the dataset distillation service provider [\[49,](#page-19-14) [68\]](#page-20-13). Also, the attacker does not necessarily control the sources of the datasets. For instance, the victim can upload their own dataset for distillation. Besides, we stress that the attacker does not interfere with the downstream model training. The attacker only supplies the distilled dataset to the victim.

Attacker's Goal. The attacker's goal is to inject the trigger into the distilled dataset and consequently backdoor the downstream models that are trained on this distilled dataset. Note that the distilled dataset is considerably less than the original training dataset (i.e.,  $|\tilde{\mathbf{X}}| \ll |\mathbf{X}|$ ). For example, Wang et al. [\[79\]](#page-20-3) compressed 50,000 training images of the CIFAR10 dataset into only 100 synthetic images (10 per class). It is thus utter most important for the attacker to make sure that *the trigger is negligible and indistinguishable to the human moderators to avoid visual mitigation but remains effective in the downstream tasks*.

Attack Challenge. Recall the fact that the attackers have no knowledge of and cannot interfere with the downstream model training. Backdoor attacks against the dataset distillation lead to non-trivial challenges. First, our backdoor attacks occur upstream, as outlined in the attack scenarios. The attackers must first ensure that the backdoored distilled dataset can guarantee the downstream model utility. Secondly, they need to ensure that the triggers are indistinguishable from the potential human inspection (which is inevitable since  $|\tilde{\mathbf{X}}| \ll |\mathbf{X}|$ ). Finally, the attackers must make sure that the backdoor can be effectively implanted in the downstream models when using this (very) small backdoored distilled dataset.

## <span id="page-3-2"></span>3.2 NAIVEATTACK

Motivation. We first consider NAIVEATTACK that inserts a pre-defined trigger into the original training dataset before the distillation. Recall that the attacker acts as the distillation service. They have complete control of the generation of the backdoored dataset. They can determine how to generate the triggers based on the trigger-inserting function and regulate different poisoning ratios in the whole dataset. The motivation of NAIVEATTACK is that dataset distillation models tend to generate a smaller but more informative dataset. Such a distilled dataset may contain the distilled trigger, potentially

<span id="page-3-0"></span>Image /page/3/Figure/8 description: The image displays three distinct images arranged horizontally. Image (a), labeled "Trigger image," is a black square with a small white square in the bottom right corner. Image (b), labeled "DD distilled image," is a grid of colorful pixels, appearing as random noise. Image (c), labeled "DC distilled image," also shows a grid of pixels, but with a discernible, albeit distorted, pattern of white, blue, and black areas, suggesting a corrupted or partially reconstructed image.

Figure 3: Illustration of pre-defined trigger used by the NAIVEATTACK and samples of distilled images by DD and DC models. We use airplane class from CIFAR10 for DD model to generate [Figure 3b](#page-3-0) and DC model to generate [Figure 3c.](#page-3-0)

enabling an effective backdoor attack in the downstream task. Trigger Insertion. Our NAIVEATTACK follows the method from previous work [\[24\]](#page-19-8) to insert the trigger to the original training dataset  $X$  (see [Figure 2\)](#page-3-1). We choose a white square as the trigger in a specific position. We define a mask m that can record the position of the trigger. The trigger insertion function *Anaive* is defined as follows.

$$
\mathcal{A}_{naive}(\mathbf{x}) = \mathbf{x} \cdot (1 - \mathbf{m}) + \mathbf{t} \cdot \mathbf{m}
$$

We also change the label of these images to our target label. And then, we use the backdoored dataset to replace the original training dataset for distillation. We insert the trigger to the whole clean dataset for the backdoor testing dataset and modify the label. We show an example of the trigger and distilled image in [Figure 3.](#page-3-0) As we can see in [Figure 3,](#page-3-0) the trigger inserted by the NAIVEATTACK is small and indistinguishable in the distilled images.

Remark. NAIVEATTACK reuses the dataset distillation models as is. This attack can be applied to all dataset distillation models by design since it directly poisons the original training dataset. The insights we gain from NAIVEATTACK lead us to design an advanced attack in the next section.

<span id="page-3-3"></span>

## 3.3 DOORPING

Motivation. As we can see in [Figure 3,](#page-3-0) the trigger inserted by the NAIVEATTACK is small and indistinguishable in the distilled images. However, our evaluation (see [Section 5\)](#page-6-0) later shows that NAIVEATTACK does not lead to an effective backdoor attack in the downstream task. Our hypothesis of such ineffectiveness is due to the information compression during the distillation process. Besides, some backdoor information may be treated as noise in the gradient descent steps since the attackers reuse the dataset distillation models as is. This motivates us to design an advanced attack, namely DOORPING, to insert a trigger during the dataset distillation process.

Observations. DOORPING attack is based upon two important observations. The *first* observation is that our NAIVEAT-TACK is essentially a dirty label attack (i.e., backdoored samples are labeled as the target class). It forces the distillation models to learn the trigger while distilling  $\tilde{\mathbf{X}}$  at each iteration. However, the trigger t is pre-defined and cannot be adjusted; hence not effectively preserved along the updating process. To boost the backdoor attack performance in the downstream models, the trigger must be fine-tuned at every epoch during

<span id="page-4-0"></span>

##### Algorithm 2 DOORPING Algorithm

Input: The original training dataset X, model/trigger learning rate  $\eta/\eta_t$ , trigger position mask **m**, pre-defined threshold

**Output:** The distilled dataset  $\tilde{\mathbf{X}}$ 

- 1: Randomly initialize the distilled dataset  $\tilde{\mathbf{X}}$ , and backdoor trigger t
- 2: while update distilled images do
- 3: Initialize the model  $\theta_0$
- 4: while update model do

5: 
$$
\theta_{i+1} = \theta_i - \eta \nabla_{\theta_i} \ell(\tilde{\mathbf{X}}, \theta_i)
$$

6: end while

7: while update trigger do

- 8:  $out = f(t)$
- 9:  $L_t = \text{MSE}(\text{out}, \alpha \cdot \text{out})$

10: if 
$$
\mathcal{L}_t <
$$
 threshold or 10,000 steps then

- 11: Break
- $12:$  end if
- 13: *L*<sup>t</sup> back-propagation
- 14:  $\mathbf{t} \leftarrow \text{UPDATE}(\mathbf{t}, \eta_{\mathbf{t}}, \mathcal{L}_{\mathbf{t}}, \mathbf{m})$
- 15: end while
- 16: Inject updated trigger **t** into  $\epsilon|X|$  samples in X to build the backdoored dataset  $\hat{\mathbf{X}}$ .
- 17:  $L = \ell(\hat{\mathbf{X}}, \theta), \tilde{L} = \ell(\tilde{\mathbf{X}}, \theta)$
- 18:  $\tilde{\mathbf{X}} \leftarrow \text{UPDATE}(\tilde{\mathbf{X}}, \mathcal{L}, \tilde{\mathcal{L}})$
- 19: end while

<span id="page-4-1"></span>Image /page/4/Figure/22 description: This diagram illustrates a process involving an original training dataset (X), an initial trigger, and an updated trigger. The original training dataset is fed into a neural network structure, which also receives the initial trigger. The output of this process is a series of modified datasets, denoted as X~(0), X~(1), ..., X~(T). These modified datasets are then used to train a downstream backdoored model. The updated trigger is also shown to be part of the process, with an arrow pointing from the neural network structure to an 'Updated Trigger' box, and another arrow from this box back to the original training dataset, suggesting an iterative refinement or feedback loop.

Figure 4: Trigger updating on DOORPING.

the distillation process to preserve its effectiveness. The *second* observation is that the parameters of dataset distillation models are not fixed when updating the distilled dataset  $\tilde{\mathbf{X}}$ due to the bi-level optimization nature of those models. Recall our analysis in [Section 2,](#page-1-0) and both distillation models leverage the same mechanism to update an upstream model to distill a synthesized dataset  $\tilde{\mathbf{X}}$  (line 5 in [Algorithm 1\)](#page-2-0). The only difference is how the synthesized dataset  $\ddot{\mathbf{X}}$  is optimized from the distillation models (line 8 in [Algorithm 1\)](#page-2-0). Our insights imply that the attacker can potentially optimize a trigger t before updating  $\tilde{\mathbf{X}}$  at each epoch (i.e., between line 5 and 8 in [Algorithm 1\)](#page-2-0) per the aforementioned first observation. In this way, trigger t is optimized based on the updated distillation model θ at each epoch (between line 7 and line 15 in [Algorithm 2\)](#page-4-0). We then randomly poison  $\epsilon|X|$  samples in X using this optimized trigger  $t$  ( $\varepsilon$  denotes the poisoning ratio). Finally, we use the backdoored training dataset to update  $\tilde{\mathbf{X}}$ (between line 17 and line 18 in [Algorithm 2\)](#page-4-0).

Trigger Insertion. We illustrate the overall workflow of DOORPING attack in [Figure 4](#page-4-1) and outline DOORPING attack in [Algorithm 2.](#page-4-0) Our goal is to optimize the trigger t

<span id="page-4-3"></span>Image /page/4/Picture/26 description: The image displays three images in a 2x3 grid. The top row shows (a) a black square with a small green and white square in the bottom right corner, labeled "DD trigger image"; (b) a pixelated image of a frog, labeled "DD training image"; and (c) a noisy, colorful pixelated image, labeled "DD distilled image". The bottom row shows two more images, but the labels are cut off. The first image in the bottom row is a black square similar to (a), and the second image is a pixelated image of a frog similar to (b).

Image /page/4/Picture/27 description: This is a close-up, pixelated image of a frog. The frog is predominantly yellow and green, with some brown and black markings. It appears to be sitting on a textured surface, possibly dirt or leaves. The image is labeled as "(b) DD training image".

Image /page/4/Picture/28 description: A square image composed of many small, colorful squares, creating a pixelated effect. The colors are varied, including blues, greens, reds, yellows, and blacks, arranged in a seemingly random pattern.

Image /page/4/Picture/29 description: The image displays a close-up, pixelated view of what appears to be moss or lichen on a surface. The colors are predominantly muted greens, browns, and yellows, with some lighter, almost white patches. The texture is rough and uneven, typical of natural organic material. To the left of this main image is a small, dark square with a few colored pixels in the bottom left corner, appearing as a blue, pink, and cyan checkerboard pattern.

Image /page/4/Picture/30 description: The image is a low-resolution, pixelated representation of a brain scan, labeled as "(c) DD distilled image". The scan is predominantly blue and black, with some white and red pixels scattered throughout. The overall impression is that of a medical imaging result, possibly an MRI or CT scan, rendered in a highly abstract and simplified form.

(d) DC trigger image (e) DC training image (f) DC distilled image

ing image (f) DC di

Figure 5: Illustration of the optimized trigger by DOORPING attack and samples of distilled images by DD and DC models. We use the *airplane* class from CIFAR10 as our target backdoor class when employing DOORPING.

so that it can be better preserved during the distillation process. The rationale is that the better trigger information can be preserved in the distillation dataset, the higher probability a backdoor attack can be successfully launched at the downstream model. To this end, we first randomly initialize a trigger t and put it into the model to get the output (line 8, [Algo](#page-4-0)[rithm 2\)](#page-4-0),

 $out = f(t)$ 

where  $f = \theta_{1:\text{layer}}$ , and layer denotes the second to the last layer of  $\theta$  (i.e., the layer before the softmax layer) in our study. We then re-organize the values of out in descending order based on the sum of the weights of the associated parameters. We choose the top-*k* values from out. The rationale here is to identify top-*k* neurons that cause the distillation model to misbehave. Finally, we calculate the mean squared error (MSE) loss between the output and the output multiplied by a magnification factor  $\alpha$ , and then the trigger image using [Equation 5](#page-4-2) (line 9, [Algorithm 2\)](#page-4-0). Note that we use  $\alpha$  to magnify the output by these top-*k* neurons purposely. In our main experiments, we empirically set  $k$  to 1 (see [Section 6.8\)](#page-11-0) and  $\alpha$  to 10 (see [Section 6.12\)](#page-13-0).

$$
\mathcal{L}_t = \mathsf{MSE}(\mathsf{out}, \alpha \cdot \mathsf{out}) \tag{5}
$$

<span id="page-4-2"></span>In summary, the above process enables the trigger t to learn from the top-*k* neurons that cause the distillation model to misbehave. Once we obtain this optimized trigger t (line 14, [Algorithm 2\)](#page-4-0), we use it to randomly poison  $\epsilon|X|$  samples in  $X$  (line 16, [Algorithm 2\)](#page-4-0). Then we use this backdoored dataset  $\hat{\mathbf{X}}$  to update the distilled dataset  $\tilde{\mathbf{X}}$  (line 18, [Algo](#page-4-0)[rithm 2\)](#page-4-0).

Analysis. DOORPING trigger insertion can be mathematically summarized by [Equation 6](#page-5-0) and [Equation 7.](#page-5-1) Note that [Equation 6](#page-5-0) distills a set of prospective distilled data and [Equation 7](#page-5-1) can be treated as DOORPING trigger insertion function  $A_{\text{DOORPING}}$  and insert an optimized trigger into the <span id="page-5-3"></span><span id="page-5-0"></span>aforementioned prospective distilled data.

$$
\tilde{\mathbf{X}}^* = \mathcal{L}_{\theta}(\hat{\mathbf{X}}, \tilde{\mathbf{X}})
$$
\nsubject to 
$$
\theta^* = \underset{\theta}{\arg\min} \ \ell(\tilde{\mathbf{X}}, \theta) \tag{6}
$$

<span id="page-5-1"></span>where  $L_{\theta}$  denotes the model-specific distillation loss and  $\hat{\mathbf{X}}$ is the original training dataset with backdoor samples, which is defined in [Equation 7.](#page-5-1)

$$
\hat{\mathbf{X}} = \mathbf{\varepsilon} \cdot \mathbf{X} \cdot (1 - \mathbf{m}) + \mathbf{t} \cdot \mathbf{m}
$$
\nsubject to 
$$
\mathbf{t}^* = \mathbf{t} - \mathbf{m} \cdot \eta_t \nabla_t \mathcal{L}_t(\mathbf{t}, \theta)
$$
\n(7)

where  $L_t(\cdot, \cdot)$  is defined in [Equation 5.](#page-4-2) It is straightforward to observe that the DOORPING attack is also universally applicable to the different dataset distillation models. [Figure 5](#page-4-3) illustrates the different optimized triggers and distilled images for DD and DC models.

Note. Our method is different from [\[46\]](#page-19-13). DOORPING continuously optimizes the trigger t in every iteration to ensure the trigger is preserved in the synthetic dataset  $\tilde{\mathbf{X}}$ . As we show in the experiments (see [Section 5\)](#page-6-0), directly applying the technique from [\[46\]](#page-19-13) (i.e., using a one-time updated trigger) leads to a sub-optimal performance, i.e., the *ASR* plunges after several epochs. DOORPING enables us to optimize the trigger t to maximize its effectiveness in the distilled dataset  $\tilde{\mathbf{X}}$ . This is particularly important since DOORPING does not interfere with the model-specific dataset update process (line 18 in [Al](#page-4-0)[gorithm 2\)](#page-4-0). For instance, as we can see in [Figure 5,](#page-4-3) different distillation models lead to different optimized triggers and considerably different distilled images given the same target class (i.e., airplane). It is also important to note that DOOR-PING allows the attacker to keep a trigger trajectory (i.e., a collection of triggers) during the distillation process (line 14, [Algorithm 2\)](#page-4-0). This unique capability enables the attackers to outmaneuver input-level defense mechanisms, as we later show in [Section 7.2.](#page-15-0)

### <span id="page-5-2"></span>4 Experimental Settings

Datasets. We use four widely used benchmark datasets in our study.

- Fashion-MNIST (FMNIST) [\[82\]](#page-20-14) is an image dataset containing 70,000,  $28 \times 28$ , gray-scale images. Each class contains 7,000 images. The classes include T-shirt, trouser, pullover, dress, coat, sandal, shirt, sneaker, bag, and ankle boot.
- CIFAR10 [\[1\]](#page-18-5) consists of 60,000,  $32 \times 32$  color images in 10 classes, with 6,000 images per class. There are 50,000 training images and 10,000 test images. The classes are airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck.
- **STL10** [\[10\]](#page-18-4) is a 10-class image dataset similar to CI-FAR10. Each class contains 1,300 images. The size of each sample is  $96 \times 96$ . The classes include airplane, bird, car, cat, deer, dog, horse, monkey, ship, and truck.

• **SVHN** [\[51\]](#page-19-15) is a digit classification benchmark dataset that contains the images of printed digits (from 0 to 9) cropped from pictures of house number plates. The size of each sample is  $32\times32$ . Among the dataset, 73,257 digits are for training, while 26,032 digits are for testing.

All the samples in the datasets are re-sized to  $32\times32$  pixels. This is a common practice to ensure that the comparison among different datasets is fair [\[47\]](#page-19-4).

Dataset Distillation Models. In this paper, we utilize two different model architectures for dataset distillation - AlexNet [\[33\]](#page-19-16) and 128-width ConvNet. These two models have been widely used in the domain of dataset distillation [\[5,](#page-18-1) [52,](#page-19-1) [53,](#page-19-2) [79,](#page-20-3) [88–](#page-21-0)[90\]](#page-21-1). For ConvNet, it contains five different layers. The first three are the convolutional layers with ReLU activation, and the last two layers are the fully connected layers. For the distillation process, we first randomly initialize 10 different images for each class, with 100 images in total as our default settings for both DD and DC algorithms, which is the same distilled images per class as the original works [\[79,](#page-20-3) [90\]](#page-21-1). Then we use these images to train the models.

Hyperparameters of Dataset Distillation. We reuse the default settings from the respective distillation methods as outlined in [\[79,](#page-20-3) [90\]](#page-21-1). In particular, 400 epochs are used in DD where Adam is used as the optimizer. The batch size for the original training dataset is 1,024. We run DC for 1,000 epochs and employ stochastic gradient descent (SGD) as the optimizer. Note that DC has an additional SGD optimizer for updating the images.

Backdoor Attack Settings. We outline our backdoor attack settings below.

- NAIVEATTACK. As we mentioned in [Section 3.2,](#page-3-2) we add backdoor triggers before distillation. The trigger is a  $2 \times 2$  white patch (i.e., 4 pixels in total). We insert the trigger in the bottom right corner of an image.
- DOORPING. We first randomly initialize a  $2 \times 2$  trigger and insert it in the bottom right corner of an image. When optimizing the triggers (see [Algorithm 2\)](#page-4-0), we use Adam as the optimizer and MSE as the loss function. We train the trigger up to 10,000 epochs if the MSE loss is not less than the threshold. Empirically, we set this threshold to 0.5 as this value is small enough for the loss function, and the corresponding trigger is also good enough for our attacks. More concretely, if the MSE loss is less than this threshold, it indicates a less effective trigger can be learned from the selected neurons. Thus, the algorithm makes an early stop to accelerate the learning process. Note that the threshold is not related to any datasets or models since it is only used to reduce the trigger optimizing process. We set the poisoning ratio  $\varepsilon$  to commonly used value 0.01 by default [\[2,](#page-18-6) [3,](#page-18-7) [50,](#page-19-9) [64,](#page-20-11) [92\]](#page-21-3).

Evaluation Metrics. In this paper, we adopt *attack success rate (ASR)* and *clean test accuracy (CTA)* as our evaluation metrics.

- The *ASR* measures the attack effectiveness of the backdoored model on a triggered testing dataset.
- The *CTA* assesses the utility of the backdoored model on the clean testing dataset.

Both *ASR* and *CTA* scores are normalized between 0.0 and 1.0. The higher the *ASR* score is, the better the backdoor trigger injected. The closer the *CTA* score of the backdoored model to the one of a clean model, i.e., a model trained using clean data only, the better the backdoored model's utility.

Downstream Models. Note that dataset distillation tailors the distilled dataset for a given architecture. Due to this limitation of dataset distillation, all of the downstream models should be the same architecture as the dataset distillation models. In our evaluation, the downstream models are also AlexNet and 128-width ConvNet and share the same architectural design as the dataset distillation models (see [Sec](#page-5-2)[tion 4\)](#page-5-2).

Runtime Configuration. Unless otherwise mentioned, we consider the following parameter settings for both NAIVEAT-TACK and DOORPING by default:  $2 \times 2$  trigger size, 0.01 of the poisoning ratio, and 10 images of each class in distilled dataset. All the experiments in this paper are repeated 10 times. For each run, we follow the same experimental setup laid out before. We report the mean and standard deviation of each metric to evaluate the attack performance.

Remark. We outline additional hyper-parameters and experimental settings in [Section 6.1.](#page-7-0)

## <span id="page-6-0"></span>5 Evaluation

In this section, we present the performance of NAIVEAT-TACK and DOORPING against dataset distillation. We conduct extensive experiments to answer the following research questions (RQs):

- *RQ1:* Do both NAIVEATTACK and DOORPING achieve high attack performance?
- *RQ2:* Do both NAIVEATTACK and DOORPING preserve the model utility?

Concretely, we first evaluate the attack performance (*ASR* score) of NAIVEATTACK and DOORPING on all tasks, model architectures, and distillation methods. We then evaluate the utility performance (*CTA* score) of the backdoored model attacked by NAIVEATTACK and DOORPING. We use a tuple in the format of  $\langle$ Distillation Algorithm, Architecture, Dataset) for ease of presentation. For instance,  $\langle DD,$ AlexNet, CIFAR10 $\rangle$  refers to an experiment that is carried out using the DD distillation algorithm with Alexnet architecture to distill the CIFAR10 dataset.

## 5.1 Attack Performance

We first show the attack performance of both NAIVEATTACK and DOORPING to answer *RQ1*. To measure the attack performance of our attacks, we conduct a comparative evaluation of the *ASR* score between the backdoored model and the clean model trained by the normal dataset distillation procedure. We expect the backdoored model misclassifies the input containing a specific trigger, while the clean model behaves normally. [Figure 6](#page-7-1) reports the *ASR* score of NAIVEAT-TACK and DOORPING on all datasets, model architectures, and dataset distillation methods.

NAIVEATTACK. As shown in [Figure 6,](#page-7-1) we can clearly observe that the attack on the clean model achieves low *ASR* scores ranging between 0.042 and 0.120. In contrast, in some cases, our NAIVEATTACK achieves higher *ASR* scores than the attack on the clean model. For instance, the *ASR* score of  $\langle DD, AlexNet, CIFAR10 \rangle$  is 0.692, and the *ASR* score of  $\langle DD, ConvNet, SVHN \rangle$  is 0.712. These results show that our NAIVEATTACK generally performs well but fails in some cases, implying that fixed triggers simply added to the distilled data cannot be closely connected to the hidden behavior.

DOORPING. As shown in [Figure 6,](#page-7-1) almost all of the *ASR* scores are over 0.950 except for AlexNet trained by DC distilling STL10 and SVHN. For example, the *ASR* score of  $\langle$ DD, AlexNet, CIFAR10) is 1.000. Note that the lowest *ASR* score of  $\langle$ DC, AlexNet, STL10 $\rangle$  and  $\langle$ DC, AlexNet, SVHN $\rangle$ are 0.811 and 0.693, respectively. These scores are also much higher than our NAIVEATTACK and the attack on the clean model. On the other hand, the standard deviation of these two *ASR* scores is higher than the others. These results indicate that the *ASR* scores are spread out. More epochs may be required to optimize the triggers in these cases. In general, the results demonstrate that iteratively optimizing triggers throughout the distillation process can establish a strong connection between the triggers and the hidden behavior injected into the backdoored model.

Takeaways. Our attack methods can successfully inject the predefined triggers into the model. NAIVEATTACK generally performs well, though it fails in some settings. In contrast, DOORPING achieves remarkable performance among all the datasets, downstream model architectures, and dataset distillation methods.

## 5.2 Distillation Model Utility

Here, we focus on the utility performance of the backdoored model, i.e., measuring whether our attack leads to significant side effects on the primary task, to answer *RQ2*. Ideally, a backdoored model should be as accurate as a clean model, given clean test data to ensure its stealthiness. In this study, we evaluate the model utility from both quantitative and qualitative perspectives.

We first conduct a quantitative evaluation of the *CTA* score between the clean and backdoored models. As shown in [Fig](#page-7-2)[ure 7,](#page-7-2) we find that the *CTA* scores of the backdoored models from NAIVEATTACK or DOORPING are similar to that of the clean models. For instance, the *CTA* score of clean model for  $\langle$ DD, AlexNet, CIFAR10) is 0.649. Meanwhile, the *CTA* scores for NAIVEATTACK and DOORPING are 0.646 and 0.635, respectively, which drop only by 0.464% and 2.1% compared to the clean model. Our results exemplify that the side effects caused by our backdoor attacks are within the acceptable performance variation of the model. They have no significant impact on utility performance. A similar observation can be drawn from other *CTA* scores. Besides, for

<span id="page-7-4"></span><span id="page-7-1"></span>Image /page/7/Figure/0 description: This image contains four bar charts side-by-side, each with the title (DD, AlexNet), (DC, AlexNet), (DD, ConvNet), and (DC, ConvNet) respectively. The y-axis for all charts is labeled "ASR" and ranges from 0.0 to 1.0. The x-axis for all charts displays the datasets: FMNIST, CIFAR10, STL10, and SVHN. Each dataset on the x-axis has three bars representing "Clean Model" (light blue), "NaiveAttack" (tan), and "DoorPing" (brown). The "Clean Model" bars are consistently low, around 0.1. The "NaiveAttack" bars are also generally low, except for SVHN in the (DD, AlexNet) and (DC, ConvNet) charts, where they reach approximately 0.7 and 0.75 respectively. The "DoorPing" bars are very high, close to 1.0, for FMNIST, CIFAR10, and STL10 across all charts. For SVHN, the "DoorPing" bars are also high, around 1.0, in the first two charts, but drop significantly to around 0.2 in the (DD, ConvNet) chart and around 0.25 in the (DC, ConvNet) chart, with large error bars in these last two cases. A legend at the bottom indicates the colors for "Clean Model", "NaiveAttack", and "DoorPing".

<span id="page-7-2"></span>Figure 6: *ASR* of clean model, NAIVEATTACK and DOORPING for different distillation algorithms and different model architectures.

Image /page/7/Figure/2 description: This is a bar chart that displays the CTA (presumably "Clean Target Accuracy" or a similar metric) for different datasets and network architectures. The chart is divided into four main sections, each representing a different combination of dataset and network: (DD, AlexNet), (DC, AlexNet), (DD, ConvNet), and (DC, ConvNet). Within each section, there are three bars representing different attack strategies: "Clean Model" (blue), "NaiveAttack" (tan), and "DoorPing" (brown). The x-axis of each section lists the datasets: FMNIST, CIFAR10, STL10, and SVHN. The y-axis, labeled "CTA", ranges from 0.0 to 0.8. The first section, (DD, AlexNet), shows CTA values around 0.85 for FMNIST, around 0.62 for CIFAR10, around 0.42 for STL10, and around 0.82 for SVHN. The second section, (DC, AlexNet), shows CTA values around 0.77 for FMNIST, around 0.38 for CIFAR10, around 0.35 for STL10, and around 0.36 for SVHN. The third section, (DD, ConvNet), shows CTA values around 0.81 for FMNIST, around 0.48 for CIFAR10, around 0.41 for STL10, and around 0.5 for SVHN. The fourth section, (DC, ConvNet), shows CTA values around 0.74 for FMNIST, around 0.32 for CIFAR10, around 0.24 for STL10, and around 0.21 for SVHN. Error bars are present for most of the bars, indicating variability in the measurements.

Figure 7: *CTA* score of clean model, NAIVEATTACK and DOORPING for different distillation algorithms and different model architectures.

 $\langle$ DC, AlexNet, SVHN $\rangle$ , the clean model has the lowest *CTA* score, which is respectively 2.531% and 6.751% lower than NAIVEATTACK and DOORPING. As such, we carry out a Welsh *t*-test on the results (as we repeat the evaluation 10 times per our runtime configuration). Our null hypothesis is that the mean *CTA* score of DOORPING and the clean model is the same. The *t*-test results show that Welch-Satterthwaite Degrees of Freedom is 17.986 and ρ-value is 0.894; hence we cannot reject our null hypothesis. We conclude that such a difference is due to fluctuation.

We then conduct a qualitative evaluation by visualizing some examples of distilled images by normal dataset distillation and our attack in [Figure 8](#page-7-3) and [Figure 9.](#page-8-0) The images distilled by DOORPING are much similar to the ones in [Fig](#page-7-3)[ure 8a](#page-7-3) More propitiously, the backdoor trigger is totally unrecognizable to human inspection, meaning that the trigger is completely hidden in the synthetic image.

Takeaways. Our experimental results demonstrate that all the backdoored models still have the same level of utility performance as the clean model, i.e., our proposed backdoor attacks preserve the model's utility.

## 6 Ablation Study

### <span id="page-7-0"></span>6.1 Additional Experimental Settings

In [Table 2,](#page-8-1) we list additional experimental settings which commonly used in our experiments. There are some other hyper-parameters, such as distillation mode in DD, which are not listed because we only use the default settings and never change them during the experiment.

<span id="page-7-3"></span>

| Airplane                              | Automobile                              | Bird                              | Cat                              | Deer                              | Dog                              | Frog                              | Horse                              | Ship                              | Truck                              |
|---------------------------------------|-----------------------------------------|-----------------------------------|----------------------------------|-----------------------------------|----------------------------------|-----------------------------------|------------------------------------|-----------------------------------|------------------------------------|
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |
| Image: Airplane feature visualization | Image: Automobile feature visualization | Image: Bird feature visualization | Image: Cat feature visualization | Image: Deer feature visualization | Image: Dog feature visualization | Image: Frog feature visualization | Image: Horse feature visualization | Image: Ship feature visualization | Image: Truck feature visualization |

(a) Distilled clean images by DD algorithm

Image /page/7/Figure/12 description: The image displays a grid of small, colorful images, each representing a different category of object. Above each column of images, a label indicates the category: Airplane, Automobile, Bird, Cat, Deer, Dog, Frog, Horse, Ship, and Truck. There are five rows of these images, with ten images in each row, totaling fifty images. The images themselves are abstract and pixelated, showing patterns of various colors, suggesting they might be visualizations of neural network features or filters trained to recognize these object categories.

(b) Distilled images by DOORPING and DD algorithm

Figure 8: Comparison of distilled images by DOORPING given  $\langle$ DD, AlexNet, CIFAR10 $\rangle$ .

## 6.2 Effectiveness on Complex Datasets

We also add the ablation study on the effectiveness of complex datasets. We test our attack using CIFAR100 [\[1\]](#page-18-5), which has 100 classes containing 600 images each. For DD, due to the GPU memory limitation, we distill five images per

<span id="page-8-3"></span><span id="page-8-0"></span>

| Airplane        | Automobile        | Bird        | Cat        | Deer        | Dog        | Frog        | Horse        | Ship        | Truck        |
|-----------------|-------------------|-------------|------------|-------------|------------|-------------|--------------|-------------|--------------|
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |

(a) Distilled clean images by DC algorithm

| Airplane        | Automobile        | Bird        | Cat        | Deer        | Dog        | Frog        | Horse        | Ship        | Truck        |
|-----------------|-------------------|-------------|------------|-------------|------------|-------------|--------------|-------------|--------------|
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |
| Image: Airplane | Image: Automobile | Image: Bird | Image: Cat | Image: Deer | Image: Dog | Image: Frog | Image: Horse | Image: Ship | Image: Truck |

(b) Distilled images by DOORPING and DC algorithm

Figure 9: Comparison of distilled images by DOORPING given  $\langle$ DC, AlexNet, CIFAR10 $\rangle$ .

<span id="page-8-2"></span>

| Table 1: ASR and CTA of DOORPING with CIFAR100. |
|-------------------------------------------------|
|-------------------------------------------------|

|             |         | DD          |             | DC          |             |
|-------------|---------|-------------|-------------|-------------|-------------|
|             |         | ASR         | CTA         | ASR         | CTA         |
| Clean Model | AlexNet | 0.014±0.010 | 0.385±0.009 | 0.012±0.002 | 0.217±0.007 |
|             | ConvNet | 0.029±0.011 | 0.215±0.014 | 0.012±0.002 | 0.197±0.007 |
| NAIVEATTACK | AlexNet | 0.128±0.013 | 0.375±0.010 | 0.007±0.003 | 0.209±0.005 |
|             | ConvNet | 0.011±0.010 | 0.214±0.011 | 0.006±0.021 | 0.190±0.004 |
| DOORPING    | AlexNet | 0.919±0.014 | 0.373±0.011 | 0.961±0.024 | 0.209±0.006 |
|             | ConvNet | 1.000±0.000 | 0.205±0.012 | 1.000±0.000 | 0.196±0.002 |

class and 500 synthetic images in total. For DC, the hyperparameters and other settings remain the same as our main experiments. [Table 1](#page-8-2) illustrates the results of CIFAR100. All *ASR* scores are larger than 0.900 without significant *CTA* degradation compared with those of the clean model and NAIVEATTACK. Our results show that DOORPING can be easily extended to more complex datasets.

Takeaway. DOORPING can be extended to more complex datasets with more classes and data samples.

##### 6.3 Effectiveness on Cross Architectures

Several dataset distillation methods [\[5,](#page-18-1) [90\]](#page-21-1) explore crossarchitecture (CA) data distillation (i.e., the data distillation model is different from the downstream model). To understand the effectiveness of DOORPING on such crossarchitecture scenarios, we choose three model architectures - AlexNet [\[33\]](#page-19-16), ConvNet, and VGG11 [\[67\]](#page-20-15) in our study. We use AlexNet (ConvNet) as the distillation model and the other two architectures for evaluation. As we can see in [Ta](#page-9-0)[ble 3,](#page-9-0) given DC algorithm, DOORPING achieves good *ASR* and *CTA* scores on VGG11 as the downstream model, which

<span id="page-8-1"></span>Table 2: Additional hyper-parameters used in backdoor attacks against DD and DC.

|                                       | DD            | DC            |
|---------------------------------------|---------------|---------------|
| <b>Batch size</b>                     | 1024          | 256           |
| <b>Epochs</b>                         | 400           | 1000          |
| <b>Distilled optimizer</b>            | SGD           | SGD           |
| <b>Distilled loss function</b>        | Cross Entropy | Cross Entropy |
| <b>Distilled images per class</b>     | 10            | 10            |
| <b>Distilled learning rate</b>        | 0.001         | 0.1           |
| <b>Downstream training epochs</b>     | 30            | 300           |
| <b>Downstream model learning rate</b> | 0.01          | 0.01          |
| <b>Downstream model optimizer</b>     | SGD           | SGD           |
| <b>Downstream model loss function</b> | Cross Entropy | Cross Entropy |
| <b>Trigger learning rate</b>          | 0.08          | 0.08          |
| <b>Trigger optimizer</b>              | Adam          | Adam          |
| <b>Trigger loss function</b>          | MSE Loss      | MSE Loss      |
| <b>Top-k</b>                          | 1             | 1             |
| <b>Poisoning ratio</b>                | 0.01          | 0.01          |
| <b>Alpha</b>                          | 10            | 10            |
| <b>Threshold of trigger updating</b>  | 0.5           | 0.5           |

is trained on the synthetic data distilled by ConvNet. In general, DOORPING performs well on all cross-architecture models using the synthetic data distilled by ConvNet architecture. However, DOORPING does not perform well in most cross-architecture models using the synthetic data distilled by DD algorithm. We speculate that the root cause is the difference in the distillation algorithms. For DD, it compresses the image information (gradient calculated by the specific model) into the distilled dataset, i.e., model-specific. In contrast, DC forces the synthetic dataset to learn the distribution of the original dataset, i.e., model-independent. Therefore, DC can better preserve the information of the original training images hence better preserving the trigger in the distilled dataset. Consequently, DC leaves the backdoor in a different model trained on this distilled dataset.

Takeaway. DOORPING can be used to attack crossarchitecture models. However, its effectiveness may be affected by the distillation models.

## 6.4 Invisible Backdoor Attack

We also test another trigger pattern technique, invisible trigger [\[36\]](#page-19-17). For DOORPING with an invisible trigger, we choose a random image from the target class to which the adversary aims to map the backdoored images. Specifically, in our experiments, we choose label 0 as the target class and randomly select an image from class 0 of the test dataset as the trigger. In the original work, the trigger is optimized for about 2,000 epochs before the model re-training. To simplify the workflow and save time, the trigger is optimized for 500 steps of each distillation epoch. All other settings are the same as for the original DOORPING. [Figure 11](#page-10-0) demonstrates the results of invisible backdoor attacks against dataset distillation. As we can see, we cannot identify this trigger generated by an airplane with the naked eye, i.e., the invisible trigger. For most cases, the invisible trigger will perform better than NAIVEATTACK without utility degradation from [Figure 12.](#page-10-1)

##### Table 3: *ASR* and *CTA* of cross model architectures.

<span id="page-9-2"></span><span id="page-9-0"></span>

|     |                |         |            | <b>FMNIST</b> |                                                                                                                                                                                                                                                        |            | CIFAR10 |         |            | <b>STL10</b> |            |            |     |            | <b>SVHN</b> |         |                                 |
|-----|----------------|---------|------------|---------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|---------|---------|------------|--------------|------------|------------|-----|------------|-------------|---------|---------------------------------|
|     |                | AlexNet |            |               | ConvNet                                                                                                                                                                                                                                                | AlexNet    |         | ConvNet |            | AlexNet      |            | ConvNet    |     | AlexNet    |             | ConvNet |                                 |
|     | CA Model       | ASR     | <b>CTA</b> | A.SR          | <b>CTA</b>                                                                                                                                                                                                                                             | <b>ASR</b> | CTA     | ASR     | <b>CTA</b> | ASR          | <b>CTA</b> | <b>ASR</b> | CTA | <b>ASR</b> | <b>CTA</b>  | ASR     | <b>CTA</b>                      |
|     | <b>AlexNet</b> |         |            |               | 11800+0.000 0.868+0.013 0.500+0.500 0.300+0.118 0.999+0.000 0.635+0.009 0.000+0.000 0.102+0.005 0.999+0.000 0.438+0.010 1.000+0.000 0.120+0.011 0.970+0.009 0.759+0.010 0.000+0.000 0.039 +0.000 0.091+0.008                                           |            |         |         |            |              |            |            |     |            |             |         |                                 |
| DD. |                |         |            |               | ConvNet 10.411+0.069 0.342+0.03111.000+0.000 0.804+0.01410.471+0.014 0.214+0.01311.000+0.000 0.476+0.01110.994+0.002 0.216+0.00911.000+0.000 0.371+0.01110.000+0.000 0.195+0.01311.000+0.000 0.375+0.019                                               |            |         |         |            |              |            |            |     |            |             |         |                                 |
|     | VGG11          |         |            |               | $(0.000 \pm 0.000)$ $(0.115 \pm 0.012)$ $(0.000 \pm 0.000)$ $(0.151 \pm 0.077)$ $(0.385 \pm 0.472)$ $(0.097 \pm 0.010)$ $(0.200 \pm 0.400)$ $(0.104 \pm 0.013)$ $(0.100 \pm 0.300)$ $(0.208 \pm 0.018)$ $(0.100 \pm 0.300)$ $(0.127 \pm 0.027)$ $(0.0$ |            |         |         |            |              |            |            |     |            |             |         |                                 |
|     | AlexNet        |         |            |               | 1.000+0.000 0.751+0.012 1.000+0.000 0.716+0.013 1.000+0.000 0.364+0.029 1.000+0.000 0.301+0.019 0.811+0.146 0.317+0.034 1.000+0.000 0.312+0.012 0.693+0.177 0.419+0.097                                                                                |            |         |         |            |              |            |            |     |            |             |         | $1.000 + 0.000$ $0.446 + 0.039$ |
| DC  |                |         |            |               | ConvNet 0.235+0.200 0.681+0.027 1.000+0.000 0.734+0.008 0.108+0.016 0.287+0.011 1.000+0.000 0.308+0.008 0.017+0.010 0.308+0.021 0.981+0.000 0.331+0.013 0.321+0.131 0.180+0.009 1.000+0.000 0.233+0.011                                                |            |         |         |            |              |            |            |     |            |             |         |                                 |
|     | VGG11          |         |            |               | 0.349+0.434 0.744+0.009 1.000+0.000 0.759+0.005 1.000+0.000 0.312+0.009 1.000+0.000 0.287+0.005 1.000+0.000 0.306+0.006 1.000+0.000 0.281+0.008 1.000+0.000 0.477+0.011 1.000+0.000 0.380+0.014                                                        |            |         |         |            |              |            |            |     |            |             |         |                                 |

<span id="page-9-1"></span>Image /page/9/Picture/2 description: The image is completely black.

Figure 10: Invisible trigger for DD and AlexNet for CIFAR10 dataset.

We can see from [Figure 11](#page-10-0) the invisible trigger cannot exceed our DOORPING attacks for all the cases. Furthermore, [Fig](#page-9-1)[ure 10](#page-9-1) shows the trigger we optimized for  $\langle DD, AlexNet,$  $CIFAR10$ .

Takeaways. The invisible trigger cannot outperform the trigger patterns used by DOORPING. However, it still performs better than NAIVEATTACK in general.

### 6.5 Number of Distilled Samples per Class

Previous distillation work [\[5,](#page-18-1) [52,](#page-19-1) [53,](#page-19-2) [88](#page-21-0)[–90\]](#page-21-1) has proven that better *CTA* can be achieved by increasing the number of distilled samples. It motivated us to investigate the effect of the number of distilled samples per class on our attacks. Concretely, we select 1, 10, and 50 samples per class to assess the effect on both NAIVEATTACK and DOORPING. We show the backdoor attack performance in [Table 4.](#page-10-2) In general, we can see that the *ASR* score increases with the number of distilled samples in each class. We can also find that the attack performances of NAIVEATTACK and DOORPING are suboptimal when the number of distilled samples is 1, especially on the DC algorithm. So, if the gradient distribution of distilled image has a significant standard deviation compared to that of the original training samples, this distilled image cannot fully represent these training samples. However, when the number of distilled images varies from 10 to 50, the *ASR* scores become more stable, with only one below 0.970 (approximately 0.861). As for the *CTA*, we can observe a similar trend. More distilled samples lead to higher model utility performance. Yet, the model utility does not improve much in most cases when attacking the DD algorithm, except for  $\langle$ DD, AlexNet, SVHN $\rangle$ .

Takeaways. The increasing number of distilled samples

##### Algorithm 3 Invsible Algorithm

Input: The original training dataset X, model/trigger learning rate  $\eta/\eta_t$ , trigger position mask **m**, pre-defined threshold

**Output:** The distilled dataset  $\tilde{\mathbf{X}}$ 

- 1: Randomly initialize the distilled dataset  $\tilde{\mathbf{X}}$ , and randomly choose a backdoor trigger t from test dataset
- 2: while update distilled images do
- 3: Initialize the model  $\theta_0$
- 4: while update model do
- 5:  $\theta_{i+1} = \theta_i \eta \nabla_{\theta_i} \ell(\tilde{\mathbf{X}}, \theta_i)$
- 6: end while
- 7: while update trigger do
- 8:  $out = f(t)$
- 9:  $L_t = \text{MSE}(\text{out}, 100) + |\textbf{t} \text{black image}|$ <br>10:  $L_t$  back-propagation
- L<sub>t</sub> back-propagation
- 11:  $\mathbf{t} \leftarrow \text{UPDATE}(\mathbf{t}, \eta_{\mathbf{t}}, \mathcal{L}_{\mathbf{t}}, \mathbf{m})$
- 12: end while
- 13: Inject updated trigger **t** into  $\epsilon$  **X** samples in **X** to build the backdoored dataset  $\hat{\mathbf{X}}$ .
- 14:  $L = \ell(\hat{\mathbf{X}}, \theta), \tilde{L} = \ell(\tilde{\mathbf{X}}, \theta)$
- 15:  $\tilde{\mathbf{X}} \leftarrow \text{UPDATE}(\tilde{\mathbf{X}}, \mathcal{L}, \tilde{\mathcal{L}})$

16: end while

leads to better *ASR* and *CTA* scores. This is expected since the downstream model is trained on more distilled training samples (hence more backdoored samples).

### 6.6 Number of Distillation Epochs

We further investigate the impact of the number of distillation epochs on attack and utility performance. The rationale is that the number of distillation epochs has a significant impact on the final distillation image. Therefore, we report the attack and utility performance by varying the number of distilled epochs from 10 to 400 for DD and from 200 to 1000 for DC, respectively. As depicted in [Figure 13,](#page-10-3) we can observe that in general, both *ASR* and *CTA* scores first increase and stabilize after a certain number of distillation epochs. We can also find that the *ASR* scores of some cases stabilize throughout the whole distilled procedure. For example, the *ASR* score of  $\langle$ DC, ConvNet, CIFAR10) is around 0.100 from beginning to end, but the *CTA* score soars from 0.254 to 0.320 after 400 epochs.

Takeaways. Our results suggest that performing the dataset distillation process is worthwhile through a larger number of distillation epochs since, in most cases, both attack and utility performance increase with the number of distillation epochs.

<span id="page-10-0"></span>Image /page/10/Figure/0 description: This is a bar chart that displays the CTA (presumably "Clean Text Accuracy" or a similar metric) for four different datasets: FMNIST, CIFAR10, STL10, and SVHN. The chart is divided into four main sections, each representing a different model or attack configuration: (DD, AlexNet), (DC, AlexNet), (DD, ConvNet), and (DC, ConvNet). Within each section, there are three bars representing different attack methods: NaiveAttack (dark blue), Invisible Trigger (brown), and DoorPing (tan). The y-axis ranges from 0.00 to 1.00, indicating the CTA values. Error bars are present on each bar, showing the variability of the results. For (DD, AlexNet), CTA values are approximately 0.1, 0.65, 0.15, and 0.85 for FMNIST, CIFAR10, STL10, and SVHN respectively. For (DC, AlexNet), CTA values are approximately 0.05, 0.15, 0.05, and 0.85 for FMNIST, CIFAR10, STL10, and SVHN respectively. For (DD, ConvNet), CTA values are approximately 0.1, 0.75, 0.1, and 0.75 for FMNIST, CIFAR10, STL10, and SVHN respectively. For (DC, ConvNet), CTA values are approximately 0.1, 0.2, 0.1, and 0.4 for FMNIST, CIFAR10, STL10, and SVHN respectively.

Image /page/10/Figure/1 description: Figure 11: ASR of Invisible backdoor attack against dataset distillation.

<span id="page-10-1"></span>Image /page/10/Figure/2 description: This image contains four bar charts, each representing different datasets and neural network architectures. The y-axis for all charts is labeled "CTA" and ranges from 0.0 to 0.8. The x-axis for each chart displays four datasets: FMNIST, CIFAR10, STL10, and SVHN. Each dataset has three bars representing different attack methods: "NAIVEATTACK" (dark blue), "Invisible Trigger" (brown), and "DoorPing" (tan). The first chart is titled "(DD, AlexNet)" and shows high CTA values for FMNIST (around 0.85), moderate values for CIFAR10 (around 0.65) and STL10 (around 0.45), and a higher value for SVHN (around 0.75). The second chart, titled "(DC, AlexNet)", shows similar CTA values for FMNIST (around 0.75), CIFAR10 (around 0.35), and STL10 (around 0.35), with a significantly higher value for SVHN (around 0.5). The third chart, titled "(DD, ConvNet)", shows high CTA values for FMNIST (around 0.8), moderate values for CIFAR10 (around 0.45) and STL10 (around 0.4), and a moderate value for SVHN (around 0.45). The fourth chart, titled "(DC, ConvNet)", shows high CTA values for FMNIST (around 0.75), moderate values for CIFAR10 (around 0.3), STL10 (around 0.45), and lower values for SVHN (around 0.2).

Figure 12: *ASR* of Invisible backdoor attack against dataset distillation. Table 4: Attack performance under different target models and distilled samples per class.

<span id="page-10-2"></span>

|         |        |                    |                                         | <b>FMNIST</b> |                 |                    | <b>CIFAR10</b>                                                                                                                      |      |                 |     |                    | STL10 |                 | <b>SVHN</b>                                                                                                                                                                                                                                                                |            |      |                 |
|---------|--------|--------------------|-----------------------------------------|---------------|-----------------|--------------------|-------------------------------------------------------------------------------------------------------------------------------------|------|-----------------|-----|--------------------|-------|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|------|-----------------|
|         |        | <b>NAIVEATTACK</b> |                                         |               | <b>DOORPING</b> | <b>NAIVEATTACK</b> |                                                                                                                                     |      | <b>DOORPING</b> |     | <b>NAIVEATTACK</b> |       | <b>DOORPING</b> | <b>NAIVEATTACK</b>                                                                                                                                                                                                                                                         |            |      | <b>DOORPING</b> |
|         |        | ASR                | <b>CTA</b>                              | A.SR          | <b>CTA</b>      | ASR                | <b>CTA</b>                                                                                                                          | A.SR | <b>CTA</b>      | ASR | CTA                | A.SR  | <b>CTA</b>      | ASR                                                                                                                                                                                                                                                                        | <b>CTA</b> | A.SR | <b>CTA</b>      |
|         |        |                    | $10.102 + 0.025$ 0.828 + 0.010          |               |                 |                    | $1.000 \pm 0.000$ $0.821 \pm 0.009$ $0.692 \pm 0.016$ $0.646 \pm 0.014$                                                             |      |                 |     |                    |       |                 | $1.000 \pm 0.000$ $0.633 \pm 0.015$ $0.113 \pm 0.011$ $0.407 \pm 0.010$ $1.000 \pm 0.000$ $0.424 \pm 0.011$ $0.577 \pm 0.015$ $0.374 \pm 0.012$ $0.997 \pm 0.002$ $0.406 \pm 0.014$                                                                                        |            |      |                 |
|         | l DD - |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $10\mid 0.103\pm 0.006$ 0.867±0.012 $1.000\pm 0.000$ 0.868±0.009 $0.692\pm 0.009$ 0.646±0.011 0.999±0.000 0.635±0.013 0.123±0.009 0.428±0.011 0.999±0.000 0.438±0.010 0.797±0.000 0.729±0.009 0.729±0.019 0.970±0.009 0.759±0.010                                          |            |      |                 |
| AlexNet |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $5010.186 \pm 0.010$ $0.871 \pm 0.011$ $1.000 \pm 0.000$ $0.882 \pm 0.011$ $0.809 \pm 0.014$ $0.652 \pm 0.013$ $0.972 \pm 0.028$ $0.650 \pm 0.015$ $0.110 \pm 0.007$ $0.454 \pm 0.010$ $1.000 \pm 0.000$ $0.436 \pm 0.012$ $0.782 \pm 0.020$ $0.765 \pm 0$                 |            |      |                 |
|         |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $0.085\pm0.008$ $0.536\pm0.032$ $0.098\pm0.006$ $0.541\pm0.023$ $0.214\pm0.059$ $0.229\pm0.016$ $0.268\pm0.054$ $0.234\pm0.014$ $0.126\pm0.294$ $0.196\pm0.044$ $0.179\pm0.120$ $0.180\pm0.033$ $0.332\pm0.118$ $0.111\pm0.013$ $0.284\pm0.1$                              |            |      |                 |
|         | l DC - |                    | $10[0.118 + 0.006 \quad 0.757 + 0.009]$ |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $1.000 \pm 0.000$ $0.751 \pm 0.029$ $0.133 \pm 0.032$ $0.358 \pm 0.047$ $1.000 \pm 0.000$ $0.364 \pm 0.012$ $0.098 \pm 0.046$ $0.305 \pm 0.040$ $0.811 \pm 0.146$ $0.317 \pm 0.034$ $0.333 \pm 0.270$ $0.412 \pm 0.083$ $0.693 \pm 0.177$ $0.419 \pm 0.09$                 |            |      |                 |
|         |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $50   0.126 \pm 0.013   0.828 \pm 0.004   1.000 \pm 0.000   0.813 \pm 0.003   0.151 \pm 0.021   0.467 \pm 0.006   0.990 \pm 0.009   0.470 \pm 0.006   0.153 \pm 0.016   0.462 \pm 0.005   0.861 \pm 0.062   0.471 \pm 0.007   0.763 \pm 0.030   0.741 \pm 0.010   0.979 \$ |            |      |                 |
|         |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $(0.124 \pm 0.007)$ $0.784 \pm 0.014$ $(1.000 \pm 0.000)$ $0.800 \pm 0.010$ $(0.137 \pm 0.014)$ $0.450 \pm 0.012$ $(1.000 \pm 0.000)$ $0.453 \pm 0.014$ $(0.151 \pm 0.008)$ $0.357 \pm 0.013$ $(1.000 \pm 0.000)$ $0.367 \pm 0.010$ $(0.612 \pm 0.016)$                    |            |      |                 |
|         | l DD - |                    |                                         |               |                 |                    | $10\mid 0.126\pm 0.009$ $0.803\pm 0.010\mid 1.000\pm 0.000$ $0.804\pm 0.011\mid 0.105\pm 0.026$ $0.478\pm 0.011\mid 0.005\pm 0.002$ |      |                 |     |                    |       |                 | $1.000 \pm 0.000$ $0.476 \pm 0.014$ $0.136 \pm 0.012$ $0.363 \pm 0.012$ $1.000 \pm 0.000$ $0.371 \pm 0.011$ $0.712 \pm 0.002$ $0.372 \pm 0.016$ $1.000 \pm 0.000$ $0.375 \pm 0.015$                                                                                        |            |      |                 |
|         |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $5010.242 \pm 0.010$ $0.828 \pm 0.013$ $11.000 \pm 0.000$ $0.828 \pm 0.009$ $0.121 \pm 0.008$ $0.482 \pm 0.013$ $11.000 \pm 0.000$ $0.489 \pm 0.014$ $0.134 \pm 0.005$ $0.378 \pm 0.013$ $11.000 \pm 0.000$ $0.370 \pm 0.010$ $0.912 \pm 0.025$ $0.477 \$                  |            |      |                 |
| ConvNet |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $0.081 \pm 0.007$ $0.535 \pm 0.023$ $0.091 \pm 0.004$ $0.545 \pm 0.013$ $0.222 \pm 0.043$ $0.230 \pm 0.006$ $0.225 \pm 0.051$ $0.229 \pm 0.007$ $0.0166 \pm 0.024$ $0.223 \pm 0.007$ $0.181 \pm 0.037$ $0.217 \pm 0.009$ $0.090 \pm 0.056$ $0.113 \pm 0.0$                 |            |      |                 |
|         |        |                    | $DC 1010.102 \pm 0.006 0.732 \pm 0.007$ |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $1.000 \pm 0.000$ $0.734 \pm 0.008$ $0.113 \pm 0.012$ $0.310 \pm 0.017$ $1.000 \pm 0.000$ $0.308 \pm 0.008$ $0.093 \pm 0.004$ $0.321 \pm 0.012$ $0.981 \pm 0.000$ $0.331 \pm 0.013$ $0.430 \pm 0.187$ $0.215 \pm 0.015$ $1.000 \pm 0.000$ $0.203 \pm 0.02$                 |            |      |                 |
|         |        |                    |                                         |               |                 |                    |                                                                                                                                     |      |                 |     |                    |       |                 | $50 0.107 \pm 0.005 0.776 \pm 0.005 1.000 \pm 0.000 0.774 \pm 0.004 0.117 \pm 0.008 0.380 \pm 0.007 1.000 \pm 0.000 0.361 \pm 0.007 0.106 \pm 0.007 0.106 \pm 0.001 0.998 \pm 0.002 0.421 \pm 0.007 0.851 \pm 0.059 0.480 \pm 0.027 1.000 \pm 0.000 0.490 \pm$             |            |      |                 |

<span id="page-10-3"></span>Image /page/10/Figure/5 description: This figure contains four plots arranged in a 2x2 grid. Each plot shows the relationship between epochs on the x-axis and a metric (ASR or CTA) on the y-axis. The top row plots are titled "(DD, AlexNet)" and "(DC, AlexNet)", and the bottom row plots are titled "(DD, ConvNet)" and "(DC, ConvNet)". The y-axis for the top row plots ranges from 0.00 to 1.00, labeled as ASR. The y-axis for the bottom row plots ranges from 0.2 to 1.0, labeled as CTA. The x-axis for the left column plots ranges from 10 to 400 epochs, while the x-axis for the right column plots ranges from 200 to 1000 epochs. There are six lines in each plot, representing different attack methods on different datasets: NaiveAttack on FMNIST, DoorPing on FMNIST, NaiveAttack on CIFAR10, DoorPing on CIFAR10, NaiveAttack on STL10, DoorPing on STL10, NaiveAttack on SVHN, and DoorPing on SVHN. The legend at the bottom indicates the color and marker for each line. For example, the blue line with triangles represents NaiveAttack on FMNIST, and the orange line with triangles represents DoorPing on FMNIST.

Figure 13: *ASR* and *CTA* of NAIVEATTACK and DOORPING under different training epochs and different model architectures.

## 6.7 Number of Original Samples

Dataset distillation aims to reduce the redundancy in the training datasets. One more possible redundancy is the number of original training samples. Here, we study the impact of the number of original training samples on the performance <span id="page-11-1"></span>of our attacks. Concretely, we vary the proportion of the entire dataset from 0.2 to 1 to report the *ASR* and *CTA* scores. We show the trends with the increase of the number of the original training samples in [Figure 14.](#page-12-0) As we can see, the *ASR* score generally grows with the upswing of the sample numbers. In particular, the *ASR* score of some cases remains stationary throughout. However, almost all cases start with a much lower *ASR* score, i.e., when only 20% of the samples from the original training dataset are used to distill images, both of our attacks only achieve relatively poor attack performance. For instance, the *ASR* score of  $\langle DD, AlexNet,$  $STL10$  is only 0.235. These results demonstrate that the number of original training dataset affect the attack performance significantly. As expected for the *CTA* score, the majority of the model utility increase with the increasing number of original training samples. Only some *CTA* scores oscillate in an ultra-fine range. For example, the *CTA* score of  $\langle$ DC, ConvNet, SVHN $\rangle$  fluctuates from 0.190 to 0.217, and the *ASR* score is 1.000 when using DOORPING. Besides, we can also find that for  $\langle$ DC, AlexNet, STL10 $\rangle$ , the *CTA* score only increases from 0.305 to 0.319, but the *ASR* score surges to over 0.811 after the proportion gets larger than 0.4. This means it is acceptable to distill less training data, as the model utility does not change much but still achieves an acceptable attack performance.

Takeaways. The increasing size of the original training dataset leads to better *ASR* and *CTA* scores. This is also expected since the distillation models have learned sufficient patterns from additional samples.

## <span id="page-11-0"></span>6.8 Number of Selected Neurons

We aim to understand the impact of the number of selected neurons to optimize the trigger in DOORPING (i.e., the impact of top-*k*). Especially in the penultimate layer of the models, we conduct the evaluation by setting the number of selected neurons to 1, 2, 5, and 10, respectively. We report the *ASR* score, the *CTA* score, and the average running time per epoch in [Figure 15,](#page-12-1) [Figure 16,](#page-12-2) [Figure 17,](#page-12-3) and [Figure 18.](#page-13-1) As we can see from these figures, with the number of selected neurons increasing, the *ASR* scores decrease while the runtime increases. The experiments also show that the *CTA* scores are almost unchanged. For example, the *ASR* scores of  $\langle DD, AlexNet, CIFAR10\rangle$  are 0.999, 1.000, 0.665, and 0.440. Their respective runtime increases from 15s to 602s, while the *CTA* scores remain stable (0.635, 0.636, 0.648, 0.635. respectively). The reason behind this is that the weights of some selected neurons connecting these neurons to the preceding and following layers are smaller than others. In other words, we need to refrain from exploiting these neurons to enhance the triggers. We, therefore, set the number of the selected neurons to 1 throughout all experiments in the paper.

Takeaways. The increasing number of selected neurons harms the attack performance while increasing the runtime. This observation is in line with previous work [\[46\]](#page-19-13).

## 6.9 Poisoning Ratio

Here, we investigate the impact of the poisoning ratio (i.e.,  $\varepsilon$ ) in [Section 3.3\)](#page-3-3) in the entire training dataset. We vary the poisoning ratio from 0.01 to 0.5 and report both attack and utility performance in [Figure 19.](#page-13-2) For the attack performance, we can find that the *ASR* scores vary significantly in general. For instance, the *ASR* scores increase from 0.811 to 1.000 and from 0.693 to 1.000 for  $\langle$ DC, AlexNet, STL10 $\rangle$ , and  $\langle$ DC, AlexNet, SVHN) in DOORPING, respectively. For the majority of cases in NAIVEATTACK, with the poisoning ratio increasing from 0.05 to 0.5, the *ASR* scores are also increasing. Taking  $\langle DD, AlexNet, STL10 \rangle$  as an example, for the poisoning ratio of NAIVEATTACK increases from 0.01 to 0.05, the *ASR* score fluctuates between 0.136 and 0.175. When the poisoning ratio increases to 0.1 and expends to 0.5, the *ASR* score rises and eventually reaches 0.990. However, unlike DOORPING, we find it challenging to achieve a 1.000 *ASR* score in NAIVEATTACK, which exemplifies that DOORPING is more effective than NAIVEATTACK. For the model utility performance, we can observe a general downward trend. For example, the *CTA* score of DOORPING decreases from 0.364 to 0.325 on  $\langle$ DC, AlexNet, CIFAR10 $\rangle$ . We also observe similar trends in NAIVEATTACK.

Takeaways. The poisoning ratio impacts the *ASR* scores, especially for NAIVEATTACK. However, the *CTA* scores may vary given different backdoor sample ratios incurred by the respective properties of distillation models (e.g., hyperparameters, architectures, etc.).

## 6.10 Trigger Size

Previous work [\[46\]](#page-19-13) has shown that the larger the trigger size is, the higher the attack performance is. Thus, we first investigate the impact of trigger size on attack performance. We set the trigger size to  $2 \times 2$ ,  $3 \times 3$ , and  $4 \times 4$  to investigate their impacts on the attack and utility performance. [Table 5](#page-14-0) shows the *ASR* and the *CTA* scores with respect to different trigger sizes. For NAIVEATTACK, as the trigger size increases, the *ASR* score also increases, especially from  $3 \times 3$ to 4×4. For instance, the *ASR* score of NAIVEATTACK for  $\langle$ DC, ConvNet, SVHN $\rangle$  increases from 0.430 to 0.769. For DOORPING, we can see that the *ASR* score is close to 1.0 in most cases, regardless of the trigger size setting. For example, given  $\langle DD, AlexNet, STL10 \rangle$ , when the trigger size is increased from  $2 \times 2$  to  $4 \times 4$ , the *ASR* score increases from 0.811 to 0.984. Similarly, the *ASR* score increases from 0.693 to 0.805 for SVHN. These results show that larger triggers generally lead to higher attack performance in our attacks. In terms of the impact of trigger size on utility performance, we find that the majority of *CTA* scores slightly decrease with the increase of the trigger size. To this end, we calculate the Pearson correlation coefficient between the trigger size and the *CTA* score. In total, we have 32 correlation values. Among those values, 9 are positive, and 23 are negative. The average of the correlations is -0.370. Therefore, the *CTA* negatively correlates with the trigger size. Despite the side effects caused by larger trigger sizes, the *CTA* scores are still within the acceptable performance variation of the model. They do not significantly impact the model utility

<span id="page-12-0"></span>Image /page/12/Figure/0 description: The image displays a 2x4 grid of line graphs, with the top row labeled "ASR" and the bottom row labeled "CTA". Each column represents a different model type: the first two columns are labeled "(DD, AlexNet)" and "(DC, AlexNet)", and the last two columns are labeled "(DD, ConvNet)" and "(DC, ConvNet)". The x-axis for all graphs is "Proportion of Dataset", ranging from 0.2 to 1.0. The y-axis for the top row is "ASR", ranging from 0.0 to 1.0, and the y-axis for the bottom row is "CTA", ranging from 0.2 to 1.0. Each graph plots multiple lines representing different attack methods on different datasets: "NaiveAttack on FMNIST" (blue triangles), "DoorPing on FMNIST" (orange triangles), "NaiveAttack on CIFAR10" (green squares), "DoorPing on CIFAR10" (red circles), "NaiveAttack on STL10" (purple diamonds), "DoorPing on STL10" (brown downward triangles), "NaiveAttack on SVHN" (pink circles), and "DoorPing on SVHN" (gray downward triangles). The graphs show varying trends for ASR and CTA across different datasets and attack methods as the proportion of the dataset increases.

<span id="page-12-1"></span>Figure 14: *ASR* and *CTA* of NAIVEATTACK and DOORPING under the different proportions of original training datasets and different model architectures. The X-axis represents the proportion of the original samples used in the dataset distillation process.

Image /page/12/Figure/2 description: This figure contains four bar charts, each representing different experimental conditions: (DD, AlexNet), (DC, AlexNet), (DD, ConvNet), and (DC, ConvNet). Each chart displays three bars representing ASR, CTA, and Time, plotted against the number of selected neurons (1, 2, 5, 10). The y-axis on the left of the first two charts is labeled 'ASR/CTA' and ranges from 0.00 to 1.00. The y-axis on the right of the first two charts is labeled 'Time' and ranges from 0 to 200 (for DD, AlexNet) and 0 to 30 (for DC, AlexNet). The y-axis on the left of the last two charts is labeled 'ASR/CTA' and ranges from 0.00 to 1.00. The y-axis on the right of the last two charts is labeled 'Time' and ranges from 0 to 300 (for DD, ConvNet) and 0 to 20 (for DC, ConvNet). The legend at the bottom indicates that the dark blue bars represent ASR, the light green bars represent CTA, and the brown bars represent Time.

Figure 15: *ASR*, *CTA*, and the running time of DOORPING with different selected neurons for FMNIST dataset.

<span id="page-12-2"></span>Image /page/12/Figure/4 description: This image contains four bar charts, each comparing ASR/CTA and Time based on the number of selected neurons. The first chart, titled "(DD, AlexNet)", shows ASR/CTA values of approximately 1.00 for 1 and 2 selected neurons, and around 0.65 for 5 and 10 selected neurons. The CTA values are around 0.65 for 1 and 2 selected neurons, and around 0.20 for 5 and 10 selected neurons. The Time values are around 0.05 for 1 and 2 selected neurons, around 0.55 for 5 selected neurons, and around 0.95 for 10 selected neurons. The second chart, titled "(DC, AlexNet)", shows ASR/CTA values of approximately 1.00 for 1 selected neuron, 0.95 for 2 selected neurons, 0.65 for 5 selected neurons, and 0.55 for 10 selected neurons. The CTA values are around 0.20 for 1, 2, and 5 selected neurons, and around 0.15 for 10 selected neurons. The Time values are around 0.60 for 1 selected neuron, 0.45 for 2 selected neurons, 0.55 for 5 selected neurons, and 0.95 for 10 selected neurons. The third chart, titled "(DD, ConvNet)", shows ASR/CTA values of approximately 1.00 for 1, 2, and 5 selected neurons, and around 0.75 for 10 selected neurons. The CTA values are around 0.45 for 1, 2, and 5 selected neurons, and around 0.45 for 10 selected neurons. The Time values are around 0.15 for 1 selected neuron, 0.50 for 2 selected neurons, 0.85 for 5 selected neurons, and 0.95 for 10 selected neurons. The fourth chart, titled "(DC, ConvNet)", shows ASR/CTA values of approximately 1.00 for 1 and 2 selected neurons, and around 0.75 for 5 and 10 selected neurons. The CTA values are around 0.35 for 1, 2, 5, and 10 selected neurons. The Time values are around 0.20 for 1 selected neuron, 0.20 for 2 selected neurons, 0.20 for 5 selected neurons, and 0.20 for 10 selected neurons. The x-axis for all charts is labeled "Selected Neurons" with values 1, 2, 5, and 10. The legend indicates that the blue bars represent ASR, the green bars represent CTA, and the brown bars represent Time.

Image /page/12/Figure/5 description: Figure 16 shows the ASR, CTA, and runtime of DOORPING with different selected neurons for the CIFAR10 dataset.

<span id="page-12-3"></span>Image /page/12/Figure/6 description: This figure contains four bar charts, each with a title indicating a combination of 'DD' or 'DC' and a neural network architecture ('AlexNet' or 'ConvNet'). The x-axis for all charts is labeled 'Selected Neurons' and shows values of 1, 2, 5, and 10. The y-axis on the left of the first two charts is labeled 'ASR/CTA' and ranges from 0.00 to 1.00. The y-axis on the right of the first two charts is labeled 'Time' and ranges from 0 to 60. The y-axis on the left of the last two charts is labeled 'ASR/CTA' and ranges from 0.00 to 1.00. The y-axis on the right of the last two charts is labeled 'Time' and ranges from 0 to 30. Each chart displays three bars representing 'ASR', 'CTA', and 'Time'. In the first chart (DD, AlexNet), ASR is approximately 1.00 for all neuron counts, CTA is around 0.45 for 1 and 2 neurons, and around 0.45 for 5 and 10 neurons, and Time increases from approximately 5 to 55 as neuron count increases. In the second chart (DC, AlexNet), ASR is approximately 0.80, 0.60, 0.55, and 0.60 for 1, 2, 5, and 10 neurons respectively, CTA is around 0.25, 0.25, 0.25, and 0.25, and Time increases from approximately 25 to 55. In the third chart (DD, ConvNet), ASR is approximately 1.00 for all neuron counts, CTA is around 0.35 for all neuron counts, and Time increases from approximately 5 to 30. In the fourth chart (DC, ConvNet), ASR is approximately 1.00, 0.95, 0.95, and 0.95 for 1, 2, 5, and 10 neurons respectively, CTA is around 0.35 for all neuron counts, and Time increases from approximately 12 to 22.

Figure 17: *ASR*, *CTA*, and the running time of DOORPING with different selected neurons for STL10 dataset.

performance.

Takeaways. When the trigger size becomes more prominent

and larger, the final synthetic image contains more trigger information but less information of the original images. It may

<span id="page-13-4"></span><span id="page-13-1"></span>Image /page/13/Figure/0 description: This figure contains four bar charts, each representing different neural network configurations and their performance metrics. The x-axis for all charts is labeled "Selected Neurons" and shows values of 1, 2, 5, and 10. The y-axis on the left of the first two charts is labeled "ASR/CTA" and ranges from 0.00 to 1.00. The y-axis on the right of the first two charts is not explicitly labeled but shows values up to 600. The y-axis on the left of the last two charts is also labeled "ASR/CTA" and ranges from 0.00 to 1.00. The y-axis on the right of the last two charts is labeled "Time" and ranges from 0 to 20. Each chart displays three bars representing "ASR", "CTA", and "Time". The first chart, titled "(DD, AlexNet)", shows ASR values around 0.95, CTA values around 0.75, and Time values around 0.2. The second chart, titled "(DC, AlexNet)", shows ASR values around 0.45, CTA values around 0.25, and Time values around 0.1. The third chart, titled "(DD, ConvNet)", shows ASR values around 1.00, CTA values around 0.35, and Time values around 0.1. The fourth chart, titled "(DC, ConvNet)", shows ASR values around 1.00, CTA values around 0.35, and Time values around 16.

Image /page/13/Figure/1 description: Figure 18 shows the ASR, CTA, and running time of DOORPING with different selected neurons for the SVHN dataset.

<span id="page-13-2"></span>Image /page/13/Figure/2 description: The image displays four pairs of line graphs, each pair representing a different model architecture: (DD, AlexNet), (DC, AlexNet), (DD, ConvNet), and (DC, ConvNet). The top row of graphs plots the Attack Success Rate (ASR) against the Poisoning Ratio, with values ranging from 0.00 to 1.00 on the y-axis and poisoning ratios from 0.02 to 0.5 on the x-axis. The bottom row of graphs plots the Clean Test Accuracy (CTA) against the Poisoning Ratio, with values ranging from 0.2 to 1.0 on the y-axis and poisoning ratios from 0.02 to 0.5 on the x-axis. Each graph contains multiple lines representing different attack methods on different datasets: NaiveAttack on FMNIST, DoorPing on FMNIST, NaiveAttack on CIFAR10, DoorPing on CIFAR10, NaiveAttack on STL10, DoorPing on STL10, NaiveAttack on SVHN, and DoorPing on SVHN. In the ASR graphs, the lines generally show an increasing trend as the poisoning ratio increases, with some variations across different attack methods and datasets. In the CTA graphs, the lines generally show a decreasing or stable trend as the poisoning ratio increases, with most values clustered between 0.2 and 0.8.

Figure 19: *ASR* and *CTA* of NAIVEATTACK and DOORPING under the different poisoning ratios and different model architectures. The X-axis represents the poisoning ratio in the whole training dataset.

lead to the inevitable trade-off between attack performance and model utility.

## <span id="page-13-3"></span>6.11 Trigger Trajectory

During the distillation process, as we update the backdoor trigger based on the model parameters during the distillation process, these triggers should be different theoretically at different distilled epochs. We collect all the generated backdoor triggers from different distilled epochs. We use the distilled images to train the downstream model after the distillation and test all the trigger images we collect. We find that the *ASR* scores from these triggers can also achieve similar results as the results after the distillation. For example, the triggers generated in the 10 distilled epochs lead to a *ASR* score of 1.000 on the backdoored model trained on 400 distilledepoch images of  $\langle DD, AlexNet, CIFAR10 \rangle$ . This reality is actually an advantage of our attack, especially facing a defense like De-trigger that needs to know the exact triggers. In our situation, we have numerous triggers from different distilled epochs, which makes the defense much harder. This also means our distilled images contain information about the triggers in the distillation process, even though these triggers are different in each distilled epoch. This trajectory during the training procedure can result in a more challenging trigger detection. We name this phenomenon *trigger trajectory*. Takeaways. The trigger trajectory is a unique feature offered by DOORPING. It enables the attackers to record a set of triggers that can later be used to attack the downstream models.

## <span id="page-13-0"></span>6.12 Value of Magnifying Factor $\alpha$

We calculate the MSE Loss between the output and the output magnified by α. Thus, we should know how α acts on the optimized trigger. Theoretically, the larger  $\alpha$  is, the better the triggers are during the distillation process. Previous works [\[36,](#page-19-17) [46\]](#page-19-13) set the magnifying value to a specific constant, 100, whereas the optimizer will allow the outputs from the selected neurons in the penultimate larger closer to this number. However, the weakness of setting constants is that the optimizer will not work when the output itself is close to 100. To solve this problem, our method chooses to magnify the output to  $\alpha$  times so that the triggers will always substantially affect these selected neurons. In particular, we choose  $\alpha$  from 10, 50, and 100. [Figure 20](#page-14-1) and [Figure 21](#page-14-2) reports the results among different α. We can see from [Figure 20,](#page-14-1) the *ASR* scores are almost close to 1.000 with the alpha increasing, and the utilities are stable in [Figure 21.](#page-14-2) In our experiments, we simply set it to 10.

Takeways. The *ASR* increases with the increasing magnifying factor  $\alpha$ . However, the attack performance plateaus after  $\alpha$  is greater than 50.

Table 5: Attack performance of different target models and trigger sizes.

<span id="page-14-3"></span><span id="page-14-1"></span><span id="page-14-0"></span>Image /page/14/Figure/1 description: The image contains a table and two figures. The table, titled "Table 3. Attack performance of different target models and trigger sizes.", presents attack performance metrics (ASR and CTA) for different datasets (FMNIST, CIFAR10, STL10, SVHN) and attack types (NAIVEATTACK, DOORPING) across two model architectures (AlexNet and ConvNet) and different trigger sizes (2, 3, and 4). Figure 20 displays bar charts showing the ASR of DOORPING with different alpha values (10, 50, and 100) for both AlexNet and ConvNet models across the four datasets. Figure 21, titled "Figure 21: CTA of DoorPing with different α.", shows similar bar charts for CTA values with different alpha values for both model architectures and datasets.

Figure 21: *CTA* of DOORPING with different α.

## <span id="page-14-2"></span>7 Defenses

To mitigate the threat of backdoor attacks, many defense mechanisms have been proposed in the literature. These defenses can be broadly categorized into three detection levels [\[85\]](#page-20-16), i.e., *model-level* (if a model is backdoored) [\[40,](#page-19-18) [45,](#page-19-19) [76\]](#page-20-9), *input-level* (if the test time input contains triggers) [\[9,](#page-18-9) [19,](#page-18-10) [34\]](#page-19-20), and *dataset-level* (if a training dataset is backdoored) [\[25,](#page-19-21)[72](#page-20-17)[,74\]](#page-20-18). In this section, we evaluate if our attacks can be defended by the existing mechanisms at all three levels. For each detection level, we select three representative approaches. Note that we only evaluate DOORPING here due to its good attack performance (see [Section 4\)](#page-5-2).

### 7.1 Model-Level Defense

ABS [\[45\]](#page-19-19). ABS analyzes the inner neuron behavior by determining how the output activation changes when introducing different levels of stimulation to a neuron. The neurons that substantially elevate the activation of a particular output label, regardless of the input, are considered potentially compromised. We apply ABS to identify these neurons in the backdoored models. For all experiments, ABS does not identify any backdoor neurons or layers for all the models. All the compromised neuron candidate lists are empty. We conclude that ABS cannot defend DOORPING.

Neural Attention Distillation (NAD) [\[40\]](#page-19-18). NAD is an architecture to erase backdoors from backdoored models. It utilizes a teacher model to fine-tune the backdoored student model using a small subset of clean data. In this way, the intermediate-layer attention of the student model aligns with that of the teacher model. The backdoor is then effectively removed. In our experiments, we choose a subset of the clean dataset with a proportion of 0.050 and a clean model trained by the clean dataset as our teacher model. We report the *ASR* and *CTA* scores after the fine-tuning process in [Ta](#page-15-1)[ble 6.](#page-15-1) We can clearly see that all the fine-tuned models classify the input into one specific class. This behavior leads to low *CTA* scores (∼0.100) and *ASR* scores of 1.000 or 0.000. Our results show that NAD is not an effective defense against DOORPING either.

Neural Cleanse [\[76\]](#page-20-9). Neural Cleanse generates *Anomaly Index* of neuron units for a given classifier. If the Anomaly

<span id="page-15-6"></span><span id="page-15-1"></span>Table 6: *ASR* and *CTA* of DOORPING backdoored models after NAD process.

|         | AlexNet |       |       |       | ConvNet |       |       |       |
|---------|---------|-------|-------|-------|---------|-------|-------|-------|
|         | DD      |       | DC    |       | DD      |       | DC    |       |
|         | ASR     | CTA   | ASR   | CTA   | ASR     | CTA   | ASR   | CTA   |
| FMNIST  | 0.000   | 0.100 | 1.000 | 0.100 | 1.000   | 0.100 | 1.000 | 0.100 |
| CIFAR10 | 0.000   | 0.100 | 1.000 | 0.100 | 1.000   | 0.100 | 1.000 | 0.100 |
| STL10   | 1.000   | 0.100 | 1.000 | 0.100 | 1.000   | 0.100 | 1.000 | 0.100 |
| SVHN    | 1.000   | 0.067 | 1.000 | 0.067 | 1.000   | 0.067 | 1.000 | 0.067 |

<span id="page-15-2"></span>Table 7: Anomaly Indices produced by Neural Cleanse for DOORPING. A classifier is predicted to be backdoored if the Anomaly Index is larger than 2.

|                | AlexNet |       | ConvNet |       |
|----------------|---------|-------|---------|-------|
|                | DD      | DC    | DD      | DC    |
| <b>FMNIST</b>  | 1.466   | 1.670 | 1.304   | 0.995 |
| <b>CIFAR10</b> | 1.338   | 0.919 | 0.745   | 1.895 |
| <b>STL10</b>   | 0.879   | 0.676 | 0.676   | 1.218 |
| <b>SVHN</b>    | 1.835   | 1.908 | 1.266   | 0.739 |

<span id="page-15-3"></span>Table 8: *ASR* and *CTA* of NAIVEATTACK backdoored models after De-noising Autoencoder process.

|         | AlexNet |       |       |       | ConvNet |       |       |       |
|---------|---------|-------|-------|-------|---------|-------|-------|-------|
|         | DD      |       | DC    |       | DD      |       | DC    |       |
|         | ASR     | CTA   | ASR   | CTA   | ASR     | CTA   | ASR   | CTA   |
| FMNIST  | 0.050   | 0.414 | 0.000 | 0.669 | 0.207   | 0.504 | 0.059 | 0.660 |
| CIFAR10 | 0.098   | 0.279 | 0.000 | 0.337 | 0.320   | 0.261 | 0.008 | 0.284 |
| STL10   | 0.011   | 0.305 | 0.000 | 0.289 | 0.263   | 0.275 | 0.004 | 0.119 |
| SVHN    | 0.000   | 0.347 | 0.000 | 0.484 | 0.000   | 0.143 | 0.000 | 0.133 |

Index is greater than 2, the classifier is considered a backdoored model. We adopt the default parameter settings of Neural Cleanse and use the original testing dataset as a clean dataset in the evaluation. [Table 7](#page-15-2) reports the Anomaly Index produced by Neural Cleanse for DOORPING. We can see that all the Anomaly Indices are consistently smaller than 2. It indicates that Neural Cleanse cannot detect our backdoor attacks in the distilled classifiers.

## <span id="page-15-0"></span>7.2 Input-Level Defense

De-noising Autoencoder [\[9\]](#page-18-9). De-noising Autoencoder builds a deep autoencoder model by learning from paired clean images and their counterparts with added Gaussian noise. Upon the model being trained, De-noising Autoencoder removes the noise (i.e., the trigger) of the model input by feeding them to the autoencoder model, as we believe some triggers are recognized as noise in NAIVEATTACK and DOORPING. We follow the same procedure outlined in [\[9\]](#page-18-9) to train the autoencoder in our experiments and then use the autoencoder to remove the noise of our backdoored distilled images. We evaluate the *ASR* and *CTA* of the backdoored model at the test time (i.e., the test time samples are first filtered by De-noising Autoencoder). As we can see in [Table 8](#page-15-3) and [Table 9,](#page-15-4) most of *ASR* scores decrease. However, the *CTA* score also drops significantly in most cases. For example, the *CTA* score is only 0.191, which is lower than the original model utility of 0.654 of  $\langle DD, AlexNet, CIFAR10 \rangle$ . The reason is that De-noising Autoencoder may also remove helpful information from the input images besides the trigger. There is a clear utility-defense trade-off when applying De-noising Autoencoder.

De-trigger Autoencoder [\[34\]](#page-19-20). Similar to De-noising Au-

<span id="page-15-4"></span>Table 9: *ASR* and *CTA* of DOORPING backdoored models after De-noising Autoencoder process.

|                                                                            |                                                                                                   | AlexNet |     | ConvNet |     |     |  |  |
|----------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|---------|-----|---------|-----|-----|--|--|
|                                                                            | DD                                                                                                |         | DC. |         | DD. | DC. |  |  |
|                                                                            | $ASR$ $CTA$ $ASR$ $CTA$ $ASR$ $CTA$ $ASR$ $CTA$                                                   |         |     |         |     |     |  |  |
| FMNIST   0.000 0.615   0.000 0.679   0.000 0.486   0.001 0.653             |                                                                                                   |         |     |         |     |     |  |  |
| <b>CIFAR10</b>   0.000  0.264   0.013  0.344   0.081  0.285   0.000  0.287 |                                                                                                   |         |     |         |     |     |  |  |
| STL10                                                                      | $\vert 0.000 \vert 0.334 \vert 0.000 \vert 0.297 \vert 1.000 \vert 0.283 \vert 0.000 \vert 0.262$ |         |     |         |     |     |  |  |
| SVHN                                                                       | $\vert 0.000 \vert 0.413 \vert 0.028 \vert 0.261 \vert 0.385 \vert 0.073 \vert 0.857 \vert 0.153$ |         |     |         |     |     |  |  |

<span id="page-15-5"></span>Table 10: *ASR* and *CTA* of DOORPING backdoored models after De-trigger Autoencoder process.

|                                                                                                  |     |                                                                                               | AlexNet |  | ConvNet |                             |     |  |
|--------------------------------------------------------------------------------------------------|-----|-----------------------------------------------------------------------------------------------|---------|--|---------|-----------------------------|-----|--|
|                                                                                                  | DD  |                                                                                               | DC.     |  | DD      |                             | DC. |  |
|                                                                                                  | ASR | CTA                                                                                           |         |  |         | ASR CTA   ASR CTA   ASR CTA |     |  |
| FMNIST   0.039 0.508   0.049 0.574   0.003 0.287   0.002 0.390                                   |     |                                                                                               |         |  |         |                             |     |  |
| <b>CIFAR10</b> $\begin{bmatrix} 0.145 & 0.191 \end{bmatrix}$ 0.282 0.202 0.066 0.154 0.088 0.165 |     |                                                                                               |         |  |         |                             |     |  |
| <b>STL10</b>                                                                                     |     | $\mid 0.169 \quad 0.144 \mid 0.075 \quad 0.203 \mid 0.000 \quad 0.100 \mid 0.264 \quad 0.161$ |         |  |         |                             |     |  |
| <b>SVHN</b>                                                                                      |     | $0.122$ $0.143$   0.260 $0.197$   0.065 $0.195$   0.126 0.133                                 |         |  |         |                             |     |  |

toencoder, De-trigger Autoencoder learns from both clean images and clean images with the trigger to reconstruct the clean images. The defenders must know the trigger information (i.e., pattern and location) to train a De-trigger Autoencoder. All the testing procedures are the same as we outline in De-noising Autoencoder. We report the results in [Table 10.](#page-15-5) All *ASR* scores and *CTA* scores decrease sharply. The majority are even worse than the result of De-noising Autoencoder. In conclusion, De-trigger Autoencoder cannot defend the DOORPING as it suffers from the same utility-defense trade-off.

STRIP [\[19\]](#page-18-10). STRIP filters triggered samples at the test time based on the predicted randomness of perturbated samples (i.e., by applying different image patterns to suspicious images). Its detection capability is assessed by two metrics: false rejection rate (FRR) and false acceptance rate (FAR). The FRR is the probability when the benign input is regarded as a backdoored input by the STRIP detection system. The FAR is the probability that the backdoored input is recognized as the benign input by the STRIP detection system. A detection system usually attempts to minimize the FAR while using a slightly higher FRR as the trade-off. STRIP algorithm chooses the detection threshold by using the percent point function (PPF) on the distribution of the entropy of benign samples.

We use STRIP to check if the defender can use it to identify triggered samples in the test data. [Table 11](#page-16-0) reports the FRR and FAR scores of STRIP detecting the testing dataset (clean and backdoor). Here, we add the trigger to 2,000 images in the testing dataset and employ another 2,000 as benign ones. 10 images are employed as the overlay samples, which are used for replicating with the inputs to measure the randomness (entropy) of predicted labels. As we can observe in [Table 11,](#page-16-0) STRIP can achieve good detection performance for the testing images with triggers, i.e., both FRR and FAR is close to 0. In light of this finding, we further investigate why STRIP performs so well and how to reduce its detection performance from the perspective of an attacker. Recall that the critical insight of STRIP is that the predictions of all perturbed inputs of triggered images tend to be always consistent (i.e., the target class). In other words, the high detection

<span id="page-16-4"></span><span id="page-16-0"></span>Table 11: FRR and FAR of STRIP detecting test samples (clean and backdoor). We add the trigger into 40% of the original testing dataset and use another 40% as the benign samples. 10 images are treated as the overlay indices to evaluate FRR and FAR.

|                                                                                      | AlexNet    |  |     |  | ConvNet |  |                                                                 |  |
|--------------------------------------------------------------------------------------|------------|--|-----|--|---------|--|-----------------------------------------------------------------|--|
|                                                                                      | DD         |  | DC. |  | DD.     |  | DC.                                                             |  |
|                                                                                      | <b>FRR</b> |  |     |  |         |  | FAR FRR FAR FRR FAR FRR FAR                                     |  |
| FMNIST   0.000 0.000   0.000 1.000   0.000 0.000   0.000 1.000                       |            |  |     |  |         |  |                                                                 |  |
| <b>CIFAR10</b>   $0.000$ $0.015$   $0.013$ $0.004$   $0.000$ $0.005$   $0.012$ 0.000 |            |  |     |  |         |  |                                                                 |  |
| <b>STL10</b>                                                                         |            |  |     |  |         |  | $0.015$ $0.000$ $0.023$ $0.000$ $0.000$ $0.000$ $0.006$ $0.000$ |  |
| SVHN                                                                                 |            |  |     |  |         |  | $0.015$ $0.000$ $0.016$ $0.024$ $0.020$ $0.110$ $0.016$ $0.000$ |  |

<span id="page-16-1"></span>Image /page/16/Figure/2 description: The image is a scatter plot with a fitted curve and a shaded confidence interval. The x-axis is labeled "ASR" and ranges from 0.2 to 1.0. The y-axis is labeled "FAR" and ranges from 0.0 to 1.0. There are several blue data points scattered across the plot. A green curve, representing a fitted model, is drawn through the data points, showing a decreasing trend. The shaded green area around the curve indicates the confidence interval. The data points are concentrated at higher FAR values for lower ASR values and shift to lower FAR values for higher ASR values.

Figure 22: Relationship between the *ASR* and FAR.

performance, as shown in [Table 11,](#page-16-0) indicates that our optimized triggers can be stably preserved in the perturbed images. Crucially, our DOORPING attack enables the attacker to keep a trigger trajectory (see [Section 6.11\)](#page-13-3) whereby different triggers are preserved. Instead of using the final optimized trigger, we test if other triggers along the trajectory can be employed to find a balance point between attack and detection performance. We show the relationship between the *ASR* and FAR in [Figure 22.](#page-16-1) As we can see, the FAR score of STRIP can be significantly increased (i.e., poor detection performance) when we apply triggers that lead to a suboptimal *ASR*. For example, when the FAR score is around 0.595, the *ASR* score is about 0.767, attaining a decent attack performance. Our finding indicates that the DOORPING attack can practically evade STRIP detection by trading off some attack performance.

## 7.3 Dataset-Level Defense

Statistical Analysis of DNNs (SCAn) [\[72\]](#page-20-17). SCAn leverages an Expectation-Maximization (EM) algorithm to decompose an image into its identity part (e.g., person) and variation part (e.g., poses). Based on the global information of all categories, the distribution of variations is exploited by a likelihood ratio test to analyze the representations in each category, identifying those that are more likely to be described by a mixture model by adding attack samples into legitimate images of the current category. When the test statistic of the class *T* (denoted as  $J_T^*$ ) is larger than 7.389, the class *T* is reported as being contaminated. Here, 7.389 is actually *e* <sup>2</sup> determined by SCAn. [Table 12](#page-16-2) reports the test statistic for our backdoor target class (class 0). As we can see in [Table 12,](#page-16-2) none of the  $J_T^*$  scores are larger than 7.389. The results show

<span id="page-16-2"></span>Table 12:  $J_T^*$  of the target class from different model architectures and datasets by SCAn.

|             | AlexNet |       | ConvNet |       |
|-------------|---------|-------|---------|-------|
|             | DD      | DC    | DD      | DC    |
| FMNIST      | 1.868   | 2.224 | 6.817   | 7.075 |
| CIFAR10     | 0.573   | 1.003 | 0.074   | 2.879 |
| STL10       | 0.283   | 0.270 | 0.736   | 3.424 |
| <b>SVHN</b> | 0.671   | 0.015 | 0.954   | 0.226 |

<span id="page-16-3"></span>Table 13: Average outlier score of samples generated by Spectral Signature on DOORPING. The smaller the score is, the more likely the sample is clean.

|                | AlexNet  |        |          |        | ConvNet  |       |          |        |
|----------------|----------|--------|----------|--------|----------|-------|----------|--------|
|                | DD       |        | DC       |        | DD       |       | DC       |        |
|                | Backdoor | Clean  | Backdoor | Clean  | Backdoor | Clean | Backdoor | Clean  |
| <b>FMNIST</b>  | 7.623    | 9.476  | 4.584    | 2.203  | 6.829    | 9.077 | 5.411    | 2.883  |
| <b>CIFAR10</b> | 7.022    | 10.247 | 1.088    | 2.447  | 4.753    | 5.713 | 11.046   | 11.680 |
| STL10          | 5.153    | 5.620  | 9.951    | 8.720  | 4.466    | 4.055 | 2.572    | 4.092  |
| <b>SVHN</b>    | 6.416    | 10.972 | 4.198    | 15.856 | 5.131    | 9.406 | 23.041   | 10.793 |

that SCAn cannot detect our backdoor target class effectively by DOORPING.

Spectral Signature [\[74\]](#page-20-18). Spectral signature builds on top of the idea that a classifier amplifies signals that are critical to classification. It finds that backdoored training datasets used in backdoor attacks can leave detectable traces in the covariance spectrum of the feature representation, i.e., the clean sample leads to a small covariance value. In contrast, the backdoor sample leads to an immense covariance value. Spectral signature calculates the outlier score of each sample and the mean value for the backdoor and clean samples. The results are shown in [Table 13.](#page-16-3) As we can see in [Table 13,](#page-16-3) most of the average outlier scores of backdoor samples are smaller than the clean ones. As such, Spectral Signature cannot detect the backdoored distilled datasets generated by DOORPING attack.

SPECTRE [\[25\]](#page-19-21). SPECTRE is a defense algorithm using robust covariance estimation to amplify the spectral signature of backdoored data in the training dataset. The mean QUantum Entropy (QUE) score of a backdoor sample is usually higher than the clean sample. SPECTRE then marks such backdoor samples with a robust spectral signature. In the original settings, SPECTRE detects the backdoor sample in each class. For the DOORPING attack, all backdoor images are included in the target class we pre-defined. It means that there are no backdoor images in the other classes. To this end, we modify the settings of SPECTRE and only detect the backdoor samples in the target class. We include an equal number of backdoored and clean distilled images in the target class. [Table 14](#page-17-1) reports the accuracy of the SPECTRE detection. We can observe that SPECTRE performs well in some cases. For example, given  $\langle$ DD, SVHN $\rangle$ , SPECTRE can detect the triggers by DOORPING. However, SPEC-TRE can not successfully identify the triggers in the other datasets. To better understand the root cause, we plot the QUE Score of SVHN compared with CIFAR10 in [Figure 23.](#page-18-11) As shown in [Figure 23,](#page-18-11) SPECTRE can separate the clean and backdoored samples given the SVHN dataset. However, the robust statistics used by SPECTRE can not separate the backdoored samples from the clean ones given the CIFAR10

<span id="page-17-2"></span><span id="page-17-1"></span>Table 14: Accuracy of detecting backdoor samples by using SPECTRE.

|                | AlexNet<br>DD | AlexNet<br>DC | ConvNet<br>DD | ConvNet<br>DC |
|----------------|---------------|---------------|---------------|---------------|
| <b>FMNIST</b>  | 60%           | 60%           | 90%           | 50%           |
| <b>CIFAR10</b> | 80%           | 70%           | 50%           | 40%           |
| <b>STL10</b>   | 20%           | 50%           | 40%           | 50%           |
| <b>SVHN</b>    | 100%          | 50%           | 100%          | 70%           |

dataset. A similar trend can be found in STL10 and FMNIST. The signature of the backdoored samples is amplified effectively by SPECTRE. However, in some other cases, it works poorly. For example, given STL10, the accuracy scores of SPECTRE are no greater than 50% in all cases. We conclude that SPECTRE is not robust for all of the datasets we test. This inconsistency indicates that SPECTRE is not a reliable defense mechanism against our DOORPING attack.

## <span id="page-17-0"></span>8 Related Work

Backdoor Attack. Backdoor attack [\[8,](#page-18-12) [20,](#page-18-13) [24,](#page-19-8) [31,](#page-19-12) [46\]](#page-19-13) is a training time attack and has emerged as a major security threat to deep neural networks (DNNs) in many application areas (e.g., natural language processing [\[7,](#page-18-14)[62\]](#page-20-19), image classification [\[14,](#page-18-15) [15\]](#page-18-16), face recognition [\[8\]](#page-18-12), point clouds [\[37,](#page-19-22) [81\]](#page-20-20), etc.). It implants a hidden backdoor (also called neural trojan [\[31,](#page-19-12) [46\]](#page-19-13)) into the target model via poisoning training samples (i.e., attacker modified input-label pairs). The injected backdoor can be activated during inference time if an attacker-specific trigger (either pre-defined or optimizationbased) is presented. Previous works mainly focus on the effectiveness of backdoor attacks on DNN-based classifiers [\[8,](#page-18-12) [24\]](#page-19-8), graph neural networks [\[80,](#page-20-21) [87\]](#page-20-22), pre-trained encoders [\[30,](#page-19-23)[65\]](#page-20-23), contrastive learning-based models [\[4\]](#page-18-17), transfer learning [\[86\]](#page-20-10), etc. In recent years, many efforts also adopt the concepts and techniques in adversarial examples [\[17,](#page-18-18) [22\]](#page-19-24) to improve the stealthiness of the triggers and make them imperceptible to human moderators [\[14,](#page-18-15) [15,](#page-18-16) [42\]](#page-19-25). Furthermore, previous works mainly inject triggers to the original training dataset during the model training procedure, which cannot be applied to the distilled datasets as aforementioned. Thus, we take the first step to inject triggers into the synthetic data during the dataset distillation process.

Defense Against Backdoor Attacks. Defense mechanisms against backdoor attacks [\[20,](#page-18-13) [31,](#page-19-12) [41\]](#page-19-26) can be broadly grouped into two categories. The first category of defense mechanisms is identifying backdoored data samples and filtering them out before training a model. Their central intuition is that the backdoored data samples, due to the manipulation from attackers, are statistically different from nonbackdoored counterparts either in the input space [\[12,](#page-18-19)[13,](#page-18-20)[48,](#page-19-27) [70\]](#page-20-24) or in the feature space [\[32,](#page-19-28) [56,](#page-20-25) [74\]](#page-20-18). The second category of defense mechanisms orbits around the models. Given the assumption that the model holders cannot pre-filter the training data, these mechanisms secure the models by eliminating the triggers at the training/test time [\[16,](#page-18-21)[19,](#page-18-10)[48,](#page-19-27)[71\]](#page-20-26), certifying their robustness to input perturbations [\[58,](#page-20-27) [75,](#page-20-28) [83,](#page-20-29) [87\]](#page-20-22), identifying backdoored models [\[76,](#page-20-9) [78,](#page-20-30) [91\]](#page-21-4), removing the backdoors from the backdoored models [\[38](#page-19-29)[,39,](#page-19-30)[44,](#page-19-31)[76\]](#page-20-9), etc. We refer the audience to  $[20,31,41]$  $[20,31,41]$  $[20,31,41]$  for comprehensive surveys on backdoor attacks and defenses. Our experimental results indicate that existing defense mechanisms provide insufficient robustness guarantees under DOORPING.

Dataset Distillation. Dataset distillation [\[5,](#page-18-1)[52,](#page-19-1)[53,](#page-19-2)[79,](#page-20-3)[88](#page-21-0)[–90\]](#page-21-1) is a technique for data-efficient learning, which does not rely on large datasets. The first work of dataset distillation [\[79\]](#page-20-3) calculates the loss gradient from a model trained by the distilled dataset. Some other works related to Dataset Condensation [\[88,](#page-21-0)[90\]](#page-21-1) are proposed to improve the quality of the distilled dataset. These works match the gradient of the original training dataset with distilled datasets to achieve similar performance. They also use differentiable siamese augmentation [\[88\]](#page-21-0) to improve the result but not much. Zhao and Bilen [\[89\]](#page-21-2) then provide a method for minimizing the distribution discrepancy between real and synthetic data in these sampled embedding spaces. KIP [\[52,](#page-19-1) [53\]](#page-19-2) is another method using large-scale Neural Tangent Kernel computation. Another work [\[5\]](#page-18-1) uses the trajectory of pre-trained models and matches the parameters from a select model and the model trained by distilled dataset. Nevertheless, this work has such a tremendous learning rate (as large as 1000) for updating distilled images that the matching loss will become NaN for many situations. Note that the model architecture used in the dataset distillation processing must be the same as the downstream model architecture, which is required by most current dataset distillation techniques.

## 9 Limitation

In this section, we discuss our attack limitations in two aspects. The first is the limitations of DD and DC themselves. For DD and DC, neither work can utilize the model with the BatchNorm (BN) layer as an upstream model. In fact, the BN layer is one of the most widely used layers in neural networks to accelerate convergence and avoid loss into NaN. This drawback vastly limits the choice of upstream models. Besides, for DD, it is hard for the users to distill an extensive dataset. For example, the loss becomes NaN when distilling large datasets such as SVHN (which contains over 70,000 samples). The second aspect is how they limit our attacks. We present that the attack cannot be deployed in a federated learning environment. The root cause is that both DD and DC cannot be trivially deployed in collaborative systems since they re-initialize the model parameters in every epoch. For different samples in different clients, the results differ significantly. Simply combining the distilled datasets or model parameters from the clients is impracticable. Note that there are preliminary efforts in federated dataset distillation [\[29,](#page-19-32) [69,](#page-20-31) [84\]](#page-20-32). We consider backdoor attacks against these federated systems as our future research direction.

## 10 Conclusion

In this paper, we propose the first backdoor attack against the machine learning models via a malicious dataset distillation service provider. We inject triggers into the synthetic data during the distillation process rather than during the model training phase, where all previous attacks are performed. Im-

<span id="page-18-11"></span>Image /page/18/Figure/0 description: This figure contains four histograms side-by-side, each plotting the 'Number of Samples' on the y-axis against the 'QUE Score' on the x-axis. Each histogram is categorized by a title indicating the dataset and model used: (DD, AlexNet, SVHN), (DD, ConvNet, SVHN), (DD, AlexNet, CIFAR10), and (DD, ConvNet, CIFAR10). Within each histogram, two sets of bars are present: blue bars representing 'Backdoored Samples' and orange bars representing 'Clean Samples'. The first histogram (DD, AlexNet, SVHN) shows a peak of 9 clean samples at a QUE score of 0, and several backdoor samples with scores around 20 and 30. The second histogram (DD, ConvNet, SVHN) also shows a peak of 7 clean samples at a QUE score of 0, with a few backdoor samples scattered between 5 and 25. The third histogram (DD, AlexNet, CIFAR10) displays a peak of 7 clean samples at a QUE score of 0, and backdoor samples concentrated around 5 and 15. The fourth histogram (DD, ConvNet, CIFAR10) shows a peak of 5 clean samples at a QUE score of 0, with backdoor samples appearing around 5 and 25.

Figure 23: QUE scores of DOORPING for SVHN and CIFAR10 dataset by using DD algorithm.

mense evaluations are conducted on multiple datasets, architectures, and dataset distillation techniques. Our results demonstrate that our proposed attacks achieve remarkable attack and utility performance. We hope this study highlights the need to understand the security and privacy issues of dataset distillation, especially the consequences of using distilled datasets from third parties.

Acknowledgments. The authors wish to thank Shaofeng Li and Tian Dong for their valuable discussions and feedback. This work is partially funded by the Helmholtz Association within the project "Trustworthy Federated Data Analytics" (TFDA) (funding number ZT-I-OO1 4) and by the European Health and Digital Executive Agency (HADEA) within the project "Understanding the individual host response against Hepatitis D Virus to develop a personalized approach for the management of hepatitis D" (D-Solve).

# References

- <span id="page-18-5"></span>[1] <https://www.cs.toronto.edu/~kriz/cifar.html>. [2,](#page-1-2) [6,](#page-5-3) [8](#page-7-4)
- <span id="page-18-6"></span>[2] Battista Biggio, Blaine Nelson, and Pavel Laskov. Poisoning Attacks against Support Vector Machines. In *ICML*, 2012. [2,](#page-1-2) [6](#page-5-3)
- <span id="page-18-7"></span>[3] Nicholas Carlini. Poisoning the Unlabeled Dataset of Semi-Supervised Learning. In *USENIX Security*, 2021. [2,](#page-1-2) [6](#page-5-3)
- <span id="page-18-17"></span>[4] Nicholas Carlini and Andreas Terzis. Poisoning and backdooring contrastive learning. In *International Conference on Learning Representations (ICLR)*, 2022. [18](#page-17-2)
- <span id="page-18-1"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset Distillation by Matching Training Trajectories. In *CVPR*, 2022. [1,](#page-0-1) [2,](#page-1-2) [6,](#page-5-3) [9,](#page-8-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-18-8"></span>[6] Xiaoyi Chen, Ahmed Salem, Michael Backes, Shiqing Ma, Qingni Shen, Zhonghai Wu, and Yang Zhang. BadNL: Backdoor Attacks Against NLP Models with Semantic-preserving Improvements. In *ACSAC*, pages 554–569, 2021. [3](#page-2-6)
- <span id="page-18-14"></span>[7] Xiaoyi Chen, Ahmed Salem, Dingfan Chen, Michael Backes, Shiqing Ma, Qingni Shen, Zhonghai Wu, and Yang Zhang. Badnl: Backdoor attacks against nlp models with semanticpreserving improvements. In *Annual Computer Security Applications Conference*, pages 554–569, 2021. [18](#page-17-2)
- <span id="page-18-12"></span>[8] Xinyun Chen, Chang Liu, Bo Li, Kimberly Lu, and Dawn Song. Targeted backdoor attacks on deep learning systems using data poisoning. *arXiv preprint arXiv:1712.05526*, 2017. [18](#page-17-2)

- <span id="page-18-9"></span>[9] Seungju Cho, Tae Joon Jun, Byungsoo Oh, and Daeyoung Kim. DAPAS: Denoising autoencoder to prevent adversarial attack in semantic segmentation. *International Joint Conference on Neural Networks*, 2020. [15,](#page-14-3) [16](#page-15-6)
- <span id="page-18-4"></span>[10] Adam Coates, Andrew Y. Ng, and Honglak Lee. An Analysis of Single-Layer Networks in Unsupervised Feature Learning. In *AISTATS*, pages 215–223, 2011. [2,](#page-1-2) [6](#page-5-3)
- <span id="page-18-0"></span>[11] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. In *NAACL-HLT*, pages 4171–4186, 2019. [1](#page-0-1)
- <span id="page-18-19"></span>[12] Ilias Diakonikolas and Daniel M Kane. Recent advances in algorithmic high-dimensional robust statistics. *arXiv preprint arXiv:1911.05911*, 2019. [18](#page-17-2)
- <span id="page-18-20"></span>[13] Bao Gia Doan, Ehsan Abbasnejad, and Damith C. Ranasinghe. Februus: Input Purification Defense Against Trojan Attacks on Deep Neural Network Systems. In *ACSAC*, pages 897–912, 2020. [18](#page-17-2)
- <span id="page-18-15"></span>[14] Khoa Doan, Yingjie Lao, and Ping Li. Backdoor attack with imperceptible input and latent modification. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 34, 2021. [18](#page-17-2)
- <span id="page-18-16"></span>[15] Khoa Doan, Yingjie Lao, Weijie Zhao, and Ping Li. Lira: Learnable, imperceptible and robust backdoor attacks. In *IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 11966–11976, 2021. [18](#page-17-2)
- <span id="page-18-21"></span>[16] Min Du, Ruoxi Jia, and Dawn Song. Robust anomaly detection and backdoor attack detection via differential privacy. *arXiv preprint arXiv:1911.07116*, 2019. [18](#page-17-2)
- <span id="page-18-18"></span>[17] Liam Fowl, Micah Goldblum, Ping-Yeh Chiang, Jonas Geiping, Wojtek Czaja, and Tom Goldstein. Adversarial Examples Make Strong Poisons. In *NeurIPS*, 2021. [18](#page-17-2)
- <span id="page-18-3"></span>[18] Matt Fredrikson, Somesh Jha, and Thomas Ristenpart. Model Inversion Attacks that Exploit Confidence Information and Basic Countermeasures. In *CCS*, pages 1322–1333, 2015. [1](#page-0-1)
- <span id="page-18-10"></span>[19] Yansong Gao, Change Xu, Derui Wang, Shiping Chen, Damith C Ranasinghe, and Surya Nepal. STRIP: A Defence Against Trojan Attacks on Deep Neural Networks. In *ACSAC*, pages 113–125, 2019. [15,](#page-14-3) [16,](#page-15-6) [18](#page-17-2)
- <span id="page-18-13"></span>[20] Micah Goldblum, Dimitris Tsipras, Chulin Xie, Xinyun Chen, Avi Schwarzschild, Dawn Song, Aleksander Madry, Bo Li, and Tom Goldstein. Dataset security for machine learning: Data poisoning, backdoor attacks, and defenses. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2022. [18](#page-17-2)
- <span id="page-18-2"></span>[21] Ian Goodfellow, Jonathon Shlens, and Christian Szegedy. Explaining and Harnessing Adversarial Examples. In *ICLR*, 2015. [1](#page-0-1)

- <span id="page-19-24"></span>[22] Ian J Goodfellow, Jonathon Shlens, and Christian Szegedy. Explaining and harnessing adversarial examples. *arXiv preprint arXiv:1412.6572*, 2014. [18](#page-17-2)
- <span id="page-19-10"></span>[23] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021. [2](#page-1-2)
- <span id="page-19-8"></span>[24] Tianyu Gu, Brendan Dolan-Gavitt, and Siddharth Grag. Badnets: Identifying Vulnerabilities in the Machine Learning Model Supply Chain. *CoRR abs/1708.06733*, 2017. [1,](#page-0-1) [3,](#page-2-6) [4,](#page-3-4) [18](#page-17-2)
- <span id="page-19-21"></span>[25] Jonathan Hayase, Weihao Kong, Raghav Somani, and Sewoong Oh. SPECTRE: Defending against backdoor attacks using robust covariance estimation. In *ICML*, 2021. [15,](#page-14-3) [17](#page-16-4)
- <span id="page-19-6"></span>[26] Xinlei He, Jinyuan Jia, Michael Backes, Neil Zhenqiang Gong, and Yang Zhang. Stealing Links from Graph Neural Networks. In *USENIX Security*, pages 2669–2686, 2021. [1](#page-0-1)
- <span id="page-19-11"></span>[27] Geoffrey E. Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the Knowledge in a Neural Network. *CoRR abs/1503.02531*, 2015. [2](#page-1-2)
- <span id="page-19-0"></span>[28] Qingyong Hu, Bo Yang, Linhai Xie, Stefano Rosa, Yulan Guo, Zhihua Wang, Niki Trigoni, and Andrew Markham. RandLA-Net: Efficient Semantic Segmentation of Large-Scale Point Clouds. In *CVPR*, pages 11105–11114, 2020. [1](#page-0-1)
- <span id="page-19-32"></span>[29] Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. FedSynth: Gradient Compression via Synthetic Data in Federated Learning. *CoRR abs/2204.01273*, 2022. [18](#page-17-2)
- <span id="page-19-23"></span>[30] Jinyuan Jia, Yupei Liu, and Neil Zhenqiang Gong. BadEncoder: Backdoor Attacks to Pre-trained Encoders in Self-Supervised Learning. In *S&P*, 2022. [18](#page-17-2)
- <span id="page-19-12"></span>[31] Sara Kaviani and Insoo Sohn. Defense against neural trojan attacks: A survey. *Neurocomputing*, 423:651–667, 2021. [3,](#page-2-6) [18](#page-17-2)
- <span id="page-19-28"></span>[32] Pang Wei Koh and Percy Liang. Understanding black-box predictions via influence functions. In *International conference on machine learning*, pages 1885–1894, 2017. [18](#page-17-2)
- <span id="page-19-16"></span>[33] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E. Hinton. ImageNet Classification with Deep Convolutional Neural Networks. In *NIPS*, pages 1106–1114, 2012. [6,](#page-5-3) [9](#page-8-3)
- <span id="page-19-20"></span>[34] Hyun Kwon. Defending Deep Neural Networks against Backdoor Attack by Using De-trigger Autoencoder. *IEEE Access*, 2021. [15,](#page-14-3) [16](#page-15-6)
- <span id="page-19-5"></span>[35] Bo Li and Yevgeniy Vorobeychik. Scalable Optimization of Randomized Operational Decisions in Adversarial Classification Settings. In *AISTATS*, pages 599–607, 2015. [1](#page-0-1)
- <span id="page-19-17"></span>[36] Shaofeng Li, Minhui Xue, Benjamin Zi Hao Zhao, Haojin Zhu, and Xinpeng Zhang. Invisible Backdoor Attacks on Deep Neural Networks via Steganography and Regularization. *IEEE Transactions on Dependable and Secure Computing*, 2020. [9,](#page-8-3) [14](#page-13-4)
- <span id="page-19-22"></span>[37] Xinke Li, Zhirui Chen, Yue Zhao, Zekun Tong, Yabang Zhao, Andrew Lim, and Joey Tianyi Zhou. Pointba: Towards backdoor attacks in 3d point cloud. In *IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 16492–16501, 2021. [18](#page-17-2)
- <span id="page-19-29"></span>[38] Yige Li, Nodens Koren, Lingjuan Lyu, Xixiang Lyu, Bo Li, and Xingjun Ma. Neural attention distillation: Erasing backdoor triggers from deep neural networks. In *International Conference on Learning Representations*, 2021. [18](#page-17-2)

- <span id="page-19-30"></span>[39] Yige Li, Xixiang Lyu, Nodens Koren, Lingjuan Lyu, Bo Li, and Xingjun Ma. Anti-backdoor learning: Training clean models on poisoned data. In *Advances in Neural Information Processing Systems*, 2021. [18](#page-17-2)
- <span id="page-19-18"></span>[40] Yige Li, Xixiang Lyu, Nodens Koren, Lingjuan Lyu, Bo Li, and Xingjun Ma. Neural Attention Distillation: Erasing Backdoor Triggers from Deep Neural Networks. In *ICLR*, 2021. [15](#page-14-3)
- <span id="page-19-26"></span>[41] Yiming Li, Baoyuan Wu, Yong Jiang, Zhifeng Li, and Shu-Tao Xia. Backdoor learning: A survey. *arXiv preprint arXiv:2007.08745*, 2020. [18](#page-17-2)
- <span id="page-19-25"></span>[42] Yuezun Li, Yiming Li, Baoyuan Wu, Longkang Li, Ran He, and Siwei Lyu. Invisible backdoor attack with sample-specific triggers. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 16463–16472, 2021. [18](#page-17-2)
- <span id="page-19-3"></span>[43] Xiang Ling, Shouling Ji, Jiaxu Zou, Jiannan Wang, Chunming Wu, Bo Li, and Ting Wang. DEEPSEC: A Uniform Platform for Security Analysis of Deep Learning Model. In *S&P*, pages 673–690, 2019. [1](#page-0-1)
- <span id="page-19-31"></span>[44] Xuankai Liu, Fengting Li, Bihan Wen, and Qi Li. Removing backdoor-based watermarks in neural networks with limited data. In *2020 25th International Conference on Pattern Recognition (ICPR)*, pages 10149–10156. IEEE, 2021. [18](#page-17-2)
- <span id="page-19-19"></span>[45] Yingqi Liu, Wen-Chuan Lee, Guanhong Tao, Shiqing Ma, Yousra Aafer, and Xiangyu Zhang. ABS: Scanning Neural Networks for Back-Doors by Artificial Brain Stimulation. In *CCS*, pages 1265–1282, 2019. [15](#page-14-3)
- <span id="page-19-13"></span>[46] Yingqi Liu, Shiqing Ma, Yousra Aafer, Wen-Chuan Lee, Juan Zhai, Weihang Wang, and Xiangyu Zhang. Trojaning Attack on Neural Networks. In *NDSS*, 2018. [3,](#page-2-6) [6,](#page-5-3) [12,](#page-11-1) [14,](#page-13-4) [18](#page-17-2)
- <span id="page-19-4"></span>[47] Yugeng Liu, Rui Wen, Xinlei He, Ahmed Salem, Zhikun Zhang, Michael Backes, Emiliano De Cristofaro, Mario Fritz, and Yang Zhang. ML-Doctor: Holistic Risk Assessment of Inference Attacks Against Machine Learning Models. In *USENIX Security*, 2022. [1,](#page-0-1) [6](#page-5-3)
- <span id="page-19-27"></span>[48] Yuntao Liu, Yang Xie, and Ankur Srivastava. Neural trojans. In *2017 IEEE International Conference on Computer Design (ICCD)*, pages 45–48. IEEE, 2017. [18](#page-17-2)
- <span id="page-19-14"></span>[49] Mohammad Malekzadeh, Anastasia Borovykh, and Deniz Gündüz. Honest-but-curious nets: Sensitive attributes of private inputs can be secretly coded into the entropy of classifiers' outputs. In *ACM CCS*, 2021. [3,](#page-2-6) [4](#page-3-4)
- <span id="page-19-9"></span>[50] Blaine Nelson, Marco Barreno, Fuching Jack Chi, Anthony D. Joseph, enjamin I. P. Rubinstein, Udam Saini, Charles Sutton, J. Doug Tygar, and Kai Xia. Exploiting Machine Learning to Subvert Your Spam Filter. In *First USENIX Workshop on Large-Scale Exploits and Emergent Threats*, 2008. [2,](#page-1-2) [6](#page-5-3)
- <span id="page-19-15"></span>[51] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y. Ng. Reading Digits in Natural Images with Unsupervised Feature Learning. In *NIPS*, 2011. [6](#page-5-3)
- <span id="page-19-1"></span>[52] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset Meta-Learning from Kernel Ridge-Regression. In *ICLR*, 2021. [1,](#page-0-1) [2,](#page-1-2) [6,](#page-5-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-19-2"></span>[53] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset Distillation with Infinitely Wide Convolutional Networks. In *NeurIPS*, 2021. [1,](#page-0-1) [2,](#page-1-2) [6,](#page-5-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-19-7"></span>[54] Nicolas Papernot, Patrick McDaniel, Arunesh Sinha, and Michael Wellman. SoK: Towards the Science of Security and Privacy in Machine Learning. In *Euro S&P*, pages 399–414, 2018. [1](#page-0-1)

- <span id="page-20-4"></span>[55] Nicolas Papernot, Patrick D. McDaniel, Somesh Jha, Matt Fredrikson, Z. Berkay Celik, and Ananthram Swami. The Limitations of Deep Learning in Adversarial Settings. In *Euro S&P*, pages 372–387, 2016. [1](#page-0-1)
- <span id="page-20-25"></span>[56] Neehar Peri, Neal Gupta, W Ronny Huang, Liam Fowl, Chen Zhu, Soheil Feizi, Tom Goldstein, and John P Dickerson. Deep k-nn defense against clean-label data poisoning attacks. In *European Conference on Computer Vision*, pages 55–70. Springer, 2020. [18](#page-17-2)
- <span id="page-20-0"></span>[57] Yu Rong, Yatao Bian, Tingyang Xu, Weiyang Xie, Ying Wei, Wenbing Huang, and Junzhou Huang. Self-Supervised Graph Transformer on Large-Scale Molecular Data. In *NeurIPS*, 2020. [1](#page-0-1)
- <span id="page-20-27"></span>[58] Elan Rosenfeld, Ezra Winston, Pradeep Ravikumar, and Zico Kolter. Certified robustness to label-flipping attacks via randomized smoothing. In *International Conference on Machine Learning*, pages 8230–8241. PMLR, 2020. [18](#page-17-2)
- <span id="page-20-5"></span>[59] Ahmed Salem, Apratim Bhattacharya, Michael Backes, Mario Fritz, and Yang Zhang. Updates-Leak: Data Set Inference and Reconstruction Attacks in Online Learning. In *USENIX Security*, pages 1291–1308, 2020. [1](#page-0-1)
- <span id="page-20-8"></span>[60] Ahmed Salem, Rui Wen, Michael Backes, Shiqing Ma, and Yang Zhang. Dynamic Backdoor Attacks Against Machine Learning Models. In *Euro S&P*, 2022. [1,](#page-0-1) [3](#page-2-6)
- <span id="page-20-6"></span>[61] Ahmed Salem, Yang Zhang, Mathias Humbert, Pascal Berrang, Mario Fritz, and Michael Backes. ML-Leaks: Model and Data Independent Membership Inference Attacks and Defenses on Machine Learning Models. In *NDSS*, 2019. [1](#page-0-1)
- <span id="page-20-19"></span>[62] Roei Schuster, Congzheng Song, Eran Tromer, and Vitaly Shmatikov. You autocomplete me: Poisoning vulnerabilities in neural code completion. In *30th USENIX Security Symposium (USENIX Security)*, pages 1559–1575, 2021. [18](#page-17-2)
- <span id="page-20-2"></span>[63] Roy Schwartz, Jesse Dodge, Noah A. Smith, and Oren Etzioni. Green AI. *Commun. of the ACM*, 2020. [1](#page-0-1)
- <span id="page-20-11"></span>[64] Ali Shafahi, W Ronny Huang, Mahyar Najibi, Octavian Suciu, Christoph Studer, Tudor Dumitras, and Tom Goldstein. Poison Frogs! Targeted Clean-Label Poisoning Attacks on Neural Networks. In *NeurIPS*, pages 6103–6113, 2018. [2,](#page-1-2) [6](#page-5-3)
- <span id="page-20-23"></span>[65] Lujia Shen, Shouling Ji, Xuhong Zhang, Jinfeng Li, Jing Chen, Jie Shi, Chengfang Fang, Jianwei Yin, and Ting Wang. Backdoor pre-trained models can transfer to all. *arXiv preprint arXiv:2111.00197*, 2021. [18](#page-17-2)
- <span id="page-20-7"></span>[66] Reza Shokri, Marco Stronati, Congzheng Song, and Vitaly Shmatikov. Membership Inference Attacks Against Machine Learning Models. In *S&P*, pages 3–18, 2017. [1](#page-0-1)
- <span id="page-20-15"></span>[67] Karen Simonyan and Andrew Zisserman. Very Deep Convolutional Networks for Large-Scale Image Recognition. In *ICLR*, 2015. [9](#page-8-3)
- <span id="page-20-13"></span>[68] Congzheng Song, Thomas Ristenpart, and Vitaly Shmatikov. Machine Learning Models that Remember Too Much. In *CCS*, pages 587–601, 2017. [3,](#page-2-6) [4](#page-3-4)
- <span id="page-20-31"></span>[69] Rui Song, Dai Liu, Dave Zhenyu Chen, Andreas Festag, Carsten Trinitis, Martin Schulz, and Alois C. Knoll. Federated Learning via Decentralized Dataset Distillation in Resource-Constrained Edge Environments. *CoRR abs/2208.11311*, 2022. [18](#page-17-2)
- <span id="page-20-24"></span>[70] Jacob Steinhardt, Pang Wei W Koh, and Percy S Liang. Certified defenses for data poisoning attacks. *Advances in neural information processing systems*, 30, 2017. [18](#page-17-2)

- <span id="page-20-26"></span>[71] Mahesh Subedar, Nilesh Ahuja, Ranganath Krishnan, Ibrahima J Ndiour, and Omesh Tickoo. Deep probabilistic models to detect data poisoning attacks. *arXiv preprint arXiv:1912.01206*, 2019. [18](#page-17-2)
- <span id="page-20-17"></span>[72] Di Tang, XiaoFeng Wang, Haixu Tang, and Kehuan Zhang. Demon in the Variant: Statistical Analysis of DNNs for Robust Backdoor Contamination Detection. In *USENIX Security*, pages 1541–1558, 2021. [15,](#page-14-3) [17](#page-16-4)
- <span id="page-20-1"></span>[73] Jian Tang, Jingzhou Liu, Ming Zhang, and Qiaozhu Mei. Visualizing Large-scale and High-dimensional Data. In *WWW*, pages 287–297, 2016. [1](#page-0-1)
- <span id="page-20-18"></span>[74] Brandon Tran, Jerry Li, and Aleksander Madry. Spectral signatures in backdoor attacks. *Advances in neural information processing systems*, 31, 2018. [15,](#page-14-3) [17,](#page-16-4) [18](#page-17-2)
- <span id="page-20-28"></span>[75] Binghui Wang, Xiaoyu Cao, Neil Zhenqiang Gong, et al. On certifying robustness against backdoor attacks via randomized smoothing. *arXiv preprint arXiv:2002.11750*, 2020. [18](#page-17-2)
- <span id="page-20-9"></span>[76] Bolun Wang, Yuanshun Yao, Shawn Shan, Huiying Li, Bimal Viswanath, Haitao Zheng, and Ben Y. Zhao. Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks. In *S&P*, pages 707–723, 2019. [1,](#page-0-1) [15,](#page-14-3) [18](#page-17-2)
- <span id="page-20-12"></span>[77] Lin Wang and Kuk-Jin Yoon. Knowledge distillation and student-teacher learning for visual intelligence: A review and new outlooks. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2021. [2](#page-1-2)
- <span id="page-20-30"></span>[78] Ren Wang, Gaoyuan Zhang, Sijia Liu, Pin-Yu Chen, Jinjun Xiong, and Meng Wang. Practical detection of trojan neural networks: Data-limited and data-free cases. In *European Conference on Computer Vision*, pages 222–238. Springer, 2020. [18](#page-17-2)
- <span id="page-20-3"></span>[79] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset Distillation. *CoRR abs/1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-6) [4,](#page-3-4) [6,](#page-5-3) [18](#page-17-2)
- <span id="page-20-21"></span>[80] Zhaohan Xi, Ren Pang, Shouling Ji, and Ting Wang. Graph Backdoor. In *USENIX Security*, 2021. [18](#page-17-2)
- <span id="page-20-20"></span>[81] Zhen Xiang, David J. Miller, Siheng Chen, Xi Li, and George Kesidis. A backdoor attack against 3d point cloud classifiers. In *IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 7597–7607, 2021. [18](#page-17-2)
- <span id="page-20-14"></span>[82] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-MNIST: a Novel Image Dataset for Benchmarking Machine Learning Algorithms. *CoRR abs/1708.07747*, 2017. [6](#page-5-3)
- <span id="page-20-29"></span>[83] Chulin Xie, Minghao Chen, Pin-Yu Chen, and Bo Li. Crfl: Certifiably robust federated learning against backdoor attacks. In *International Conference on Machine Learning*, 2021. [18](#page-17-2)
- <span id="page-20-32"></span>[84] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. FedDM: Iterative Distribution Matching for Communication-Efficient Federated Learning. *CoRR abs/2207.09653*, 2022. [18](#page-17-2)
- <span id="page-20-16"></span>[85] Xiaojun Xu, Qi Wang, Huichen Li, Nikita Borisov, Carl A. Gunter, and Bo Li. Detecting AI Trojans Using Meta Neural Analysis. In *S&P*, 2021. [15](#page-14-3)
- <span id="page-20-10"></span>[86] Yuanshun Yao, Huiying Li, Haitao Zheng, and Ben Y. Zhao. Latent Backdoor Attacks on Deep Neural Networks. In *CCS*, pages 2041–2055, 2019. [1,](#page-0-1) [18](#page-17-2)
- <span id="page-20-22"></span>[87] Zaixi Zhang, Jinyuan Jia, Binghui Wang, and Neil Zhenqiang Gong. Backdoor Attacks to Graph Neural Networks. In *SAC-MAT*, pages 15–26, 2021. [18](#page-17-2)

- <span id="page-21-0"></span>[88] Bo Zhao and Hakan Bilen. Dataset Condensation with Differentiable Siamese Augmentatio. In *ICML*, 2021. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-6) [6,](#page-5-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-21-2"></span>[89] Bo Zhao and Hakan Bilen. Dataset Condensation with Distribution Matching. *CoRR abs/2110.04181*, 2021. [1,](#page-0-1) [2,](#page-1-2) [6,](#page-5-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-21-1"></span>[90] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset Condensation With Gradient Matching. In *ICLR*, 2021. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-6) [6,](#page-5-3) [9,](#page-8-3) [10,](#page-9-2) [18](#page-17-2)
- <span id="page-21-4"></span>[91] Songzhu Zheng, Yikai Zhang, Hubert Wagner, Mayank Goswami, and Chao Chen. Topological detection of trojaned neural networks. *Advances in Neural Information Processing Systems*, 34, 2021. [18](#page-17-2)
- <span id="page-21-3"></span>[92] Chen Zhu, W. Ronny Huang, Hengduo Li, Gavin Taylor, Christoph Studer, and Tom Goldstein. Transferable Clean-Label Poisoning Attacks on Deep Neural Nets. In *International Conference on Machine Learning*, pages 7614–7623, 2019. [2,](#page-1-2) [6](#page-5-3)