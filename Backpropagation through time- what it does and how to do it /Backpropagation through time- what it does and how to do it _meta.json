{"table_of_contents": [{"title": "Backpropagation Through Time: What It \nDoes and How to Do It", "heading_level": null, "page_id": 0, "polygon": [[90.6760391198044, 105.68336483931948], [439.396484375, 105.68336483931948], [439.396484375, 146.945068359375], [90.6760391198044, 146.945068359375]]}, {"title": "PAUL J. WERBOS", "heading_level": null, "page_id": 0, "polygon": [[90.9921875, 164.14650283553877], [181.236083984375, 164.14650283553877], [181.236083984375, 175.598388671875], [90.9921875, 175.598388671875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[89.17726161369194, 368.0179584120983], [153.25, 368.0179584120983], [153.25, 377.1396484375], [89.17726161369194, 377.1396484375]]}, {"title": "II. BASIC BACKPROPACATION", "heading_level": null, "page_id": 0, "polygon": [[325.98410757946215, 508.179584120983], [428.62109375, 508.179584120983], [428.62109375, 516.4243856332704], [325.98410757946215, 516.4243856332704]]}, {"title": "<PERSON><PERSON> The Supervised Learning Problem", "heading_level": null, "page_id": 0, "polygon": [[324.75830078125, 523.50390625], [460.12469437652817, 523.50390625], [460.12469437652817, 531.4149338374292], [324.75830078125, 531.4149338374292]]}, {"title": "B. Simple Feedforward Networks", "heading_level": null, "page_id": 1, "polygon": [[303.87213510253315, 109.56848030018762], [426.92159227985525, 109.56848030018762], [426.92159227985525, 118.75], [303.87213510253315, 118.75]]}, {"title": "C. Adapting the Network: Approach", "heading_level": null, "page_id": 2, "polygon": [[87.07090464547677, 413.5103969754253], [221.43031784841074, 413.5103969754253], [221.43031784841074, 422.58789062499994], [87.07090464547677, 422.58789062499994]]}, {"title": "<PERSON>. Calculating Derivatives: Theoretical Background", "heading_level": null, "page_id": 2, "polygon": [[322.762836185819, 200.24462890625], [511.7666015625, 200.24462890625], [511.7666015625, 209.16162109374997], [322.762836185819, 209.16162109374997]]}, {"title": "<PERSON>. Adapting the Network: Equations", "heading_level": null, "page_id": 3, "polygon": [[65.25, 299.9059266227658], [199.5, 299.9059266227658], [199.5, 308.60400390625], [65.25, 308.60400390625]]}, {"title": "F. Adapting the Network: Code", "heading_level": null, "page_id": 3, "polygon": [[302.25, 175.44496707431796], [420.0, 175.44496707431796], [420.0, 184.9483642578125], [302.25, 184.9483642578125]]}, {"title": "Ill. BACKPROPAGATION THROUGH TIME", "heading_level": null, "page_id": 4, "polygon": [[87.07090464547677, 505.16699218749994], [223.68215158924204, 505.16699218749994], [223.68215158924204, 514.0737240075614], [87.07090464547677, 514.0737240075614]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[86.320293398533, 519.3270321361058], [143.90625, 519.3270321361058], [143.90625, 528.041015625], [86.320293398533, 528.041015625]]}, {"title": "B. Example of a Recurrent Network", "heading_level": null, "page_id": 4, "polygon": [[322.0122249388753, 265.6672967863894], [451.8679706601467, 265.6672967863894], [451.8679706601467, 273.92249527410206], [322.0122249388753, 273.92249527410206]]}, {"title": "C. Adapting the Network: Equations", "heading_level": null, "page_id": 5, "polygon": [[68.22249093107618, 246.82723004694836], [203.16807738814995, 246.82723004694836], [203.16807738814995, 255.734619140625], [68.22249093107618, 255.734619140625]]}, {"title": "D. Adapting the Network: Code", "heading_level": null, "page_id": 5, "polygon": [[68.97218863361547, 615.9427230046948], [187.42442563482467, 615.1924882629108], [187.42442563482467, 624.60888671875], [69.136962890625, 625.6957746478873]]}, {"title": "Iv. EXTENSIONS OF THE METHOD", "heading_level": null, "page_id": 6, "polygon": [[87.03549571603426, 615.9441816461684], [203.385498046875, 615.9441816461684], [203.385498046875, 624.1967833491012], [87.03549571603426, 624.1967833491012]]}, {"title": "A. Use of Other Networks", "heading_level": null, "page_id": 6, "polygon": [[321.1309669522644, 42.76348155156102], [418.74365234375, 42.76348155156102], [418.74365234375, 51.93414306640625], [321.1309669522644, 51.93414306640625]]}, {"title": "B. Applications Other Than Supervised Learning", "heading_level": null, "page_id": 7, "polygon": [[66.83110571081409, 526.0800376647834], [245.630859375, 526.0800376647834], [245.630859375, 535.0856873822975], [66.83110571081409, 535.0856873822975]]}, {"title": "<PERSON><PERSON> Strings of Data", "heading_level": null, "page_id": 8, "polygon": [[322.36874236874235, 493.92382812499994], [425.82661782661785, 493.92382812499994], [425.82661782661785, 503.22851562499994], [322.36874236874235, 503.22851562499994]]}, {"title": "<PERSON><PERSON> Speeding Up Convergence", "heading_level": null, "page_id": 9, "polygon": [[67.5, 261.8316183348924], [179.25, 261.8316183348924], [179.25, 270.83442469597753], [67.5, 270.83442469597753]]}, {"title": "E. Miscellaneous Issues", "heading_level": null, "page_id": 9, "polygon": [[68.25, 626.44527595884], [157.5, 625.6950420954163], [157.5, 635.5693359375], [68.25, 635.5693359375]]}, {"title": "U. SUMMARY", "heading_level": null, "page_id": 9, "polygon": [[304.5, 516.1608980355472], [354.046875, 516.1608980355472], [354.046875, 525.1637043966324], [304.5, 525.1637043966324]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[85.46520146520146, 38.23796033994334], [125.843017578125, 38.23796033994334], [125.843017578125, 46.49920654296875], [85.46520146520146, 46.49920654296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 93], ["Text", 16], ["SectionHeader", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 115], ["Text", 11], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON>Footer", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1412, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 121], ["Text", 16], ["Equation", 10], ["ListItem", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 438], ["TableCell", 148], ["Line", 119], ["Text", 17], ["Equation", 7], ["SectionHeader", 2], ["TextInlineMath", 2], ["Table", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 13062, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 121], ["Text", 11], ["SectionHeader", 3], ["Equation", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2450, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 129], ["TableCell", 111], ["Text", 12], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON>Footer", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11417, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 135], ["TableCell", 77], ["Text", 14], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 128], ["Text", 17], ["<PERSON>Footer", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 117], ["Text", 14], ["TextInlineMath", 7], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1293, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 120], ["Text", 13], ["TableCell", 12], ["SectionHeader", 3], ["<PERSON>Footer", 3], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2607, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 113], ["ListItem", 24], ["Text", 6], ["ListGroup", 2], ["SectionHeader", 1], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Backpropagation through time- what it does and how to do it "}