{"table_of_contents": [{"title": "Bidirectional Learning for Offline Infinite-width\nModel-based Optimization", "heading_level": null, "page_id": 0, "polygon": [[127.5, 99.75], [483.75, 99.75], [483.75, 135.73828125], [127.5, 135.73828125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 276.75], [328.5, 276.75], [328.5, 287.71875], [282.75, 287.71875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 543.0], [191.25, 543.0], [191.25, 554.5546875], [107.25, 554.5546875]]}, {"title": "We propose BiDirectional learning for offline\nInfinite-width model-based optimization (BDI)", "heading_level": null, "page_id": 1, "polygon": [[106.083984375, 366.75], [297.75, 366.75], [297.75, 388.458984375], [106.083984375, 388.458984375]]}, {"title": "2 Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[107.05517578125, 652.5], [195.75, 652.5], [195.75, 663.22265625], [107.05517578125, 663.22265625]]}, {"title": "2.1 Offline Model-based Optimization", "heading_level": null, "page_id": 1, "polygon": [[106.75634765625, 675.75], [276.75, 675.75], [276.75, 686.0390625], [106.75634765625, 686.0390625]]}, {"title": "2.2 Infinitely Wide DNN and Neural Tang<PERSON>", "heading_level": null, "page_id": 2, "polygon": [[107.25, 288.0], [338.25, 288.0], [338.25, 297.580078125], [107.25, 297.580078125]]}, {"title": "3 Method", "heading_level": null, "page_id": 2, "polygon": [[106.5, 439.5], [166.5, 439.5], [166.5, 450.9140625], [106.5, 450.9140625]]}, {"title": "3.1 Bidirectional Learning", "heading_level": null, "page_id": 2, "polygon": [[107.1298828125, 543.75], [228.0, 543.75], [228.0, 554.16796875], [107.1298828125, 554.16796875]]}, {"title": "3.2 Closed-form Solver via Neural Tangent Kernel", "heading_level": null, "page_id": 3, "polygon": [[106.5, 384.75], [328.5, 384.75], [328.5, 394.5], [106.5, 394.5]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 4, "polygon": [[106.98046875, 402.75], [192.0, 402.75], [192.0, 413.25], [106.98046875, 413.25]]}, {"title": "4.1 Dataset and Evaluation", "heading_level": null, "page_id": 4, "polygon": [[106.98046875, 494.25], [231.0, 494.25], [231.0, 504.28125], [106.98046875, 504.28125]]}, {"title": "4.2 Comparison Methods", "heading_level": null, "page_id": 5, "polygon": [[106.5, 131.5810546875], [224.419921875, 131.5810546875], [224.419921875, 142.0224609375], [106.5, 142.0224609375]]}, {"title": "4.3 Training Details", "heading_level": null, "page_id": 5, "polygon": [[106.5, 390.392578125], [200.8125, 390.392578125], [200.8125, 400.640625], [106.5, 400.640625]]}, {"title": "4.4 Results and Analysis", "heading_level": null, "page_id": 5, "polygon": [[107.25, 589.359375], [219.7880859375, 589.359375], [219.7880859375, 599.4140625], [107.25, 599.4140625]]}, {"title": "4.5 Ablation Studies", "heading_level": null, "page_id": 6, "polygon": [[106.5, 645.0], [201.75, 645.0], [201.75, 656.26171875], [106.5, 656.26171875]]}, {"title": "4.6 Hyperparameter Sensitivity", "heading_level": null, "page_id": 7, "polygon": [[106.8310546875, 669.75], [249.75, 669.75], [249.75, 679.46484375], [106.8310546875, 679.46484375]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.25, 390.0], [198.0, 390.0], [198.0, 401.4140625], [107.25, 401.4140625]]}, {"title": "6 Conclusion and discussion", "heading_level": null, "page_id": 9, "polygon": [[106.45751953125, 72.0], [261.474609375, 72.0], [261.474609375, 83.6279296875], [106.45751953125, 83.6279296875]]}, {"title": "7 Acknowledgement", "heading_level": null, "page_id": 9, "polygon": [[106.5, 343.212890625], [222.7763671875, 343.212890625], [222.7763671875, 355.587890625], [106.5, 355.587890625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 429.2578125], [165.0, 429.2578125], [165.0, 440.859375], [107.25, 440.859375]]}, {"title": "Checklist", "heading_level": null, "page_id": 13, "polygon": [[107.25, 72.0], [157.5, 72.0], [157.5, 83.53125], [107.25, 83.53125]]}, {"title": "A Appendix", "heading_level": null, "page_id": 14, "polygon": [[106.5, 71.59130859375], [180.0, 71.59130859375], [180.0, 84.35302734375], [106.5, 84.35302734375]]}, {"title": "A.1 Additional Results on 50th Percentile Scores", "heading_level": null, "page_id": 14, "polygon": [[107.25, 96.72802734375], [322.734375, 96.72802734375], [322.734375, 107.701171875], [107.25, 107.701171875]]}, {"title": "A.2 Analysis of multiple high-scoring designs", "heading_level": null, "page_id": 14, "polygon": [[106.5, 229.32421875], [308.390625, 229.32421875], [308.390625, 240.5390625], [106.5, 240.5390625]]}, {"title": "A.3 Computational Complexity", "heading_level": null, "page_id": 14, "polygon": [[106.8310546875, 399.8671875], [250.5673828125, 399.8671875], [250.5673828125, 411.46875], [106.8310546875, 411.46875]]}, {"title": "A.4 Predefined Target Scores", "heading_level": null, "page_id": 15, "polygon": [[106.5, 132.7412109375], [241.154296875, 132.7412109375], [241.154296875, 143.7626953125], [106.5, 143.7626953125]]}, {"title": "A.5 Considering Sequential Nature", "heading_level": null, "page_id": 15, "polygon": [[106.5, 249.240234375], [265.5, 249.240234375], [265.5, 260.068359375], [106.5, 260.068359375]]}, {"title": "A.6 Comparison between Maximization and Regression", "heading_level": null, "page_id": 17, "polygon": [[107.25, 348.0], [352.5, 348.0], [352.5, 357.908203125], [107.25, 357.908203125]]}, {"title": "A.7 Varying Weights", "heading_level": null, "page_id": 17, "polygon": [[106.5, 441.0], [204.0, 441.0], [204.0, 451.30078125], [106.5, 451.30078125]]}, {"title": "A.8 Analysis of Backward Mapping", "heading_level": null, "page_id": 17, "polygon": [[107.25, 583.5], [268.048828125, 583.5], [268.048828125, 593.25], [107.25, 593.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 44], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 74], ["Text", 5], ["SectionHeader", 3], ["ListItem", 3], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 58], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 3], ["Equation", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 909], ["Line", 108], ["Equation", 9], ["Text", 7], ["TextInlineMath", 6], ["Reference", 6], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 720], ["Line", 83], ["TextInlineMath", 6], ["Text", 5], ["Equation", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8090, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 52], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 909], ["TableCell", 291], ["Line", 53], ["Text", 3], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11237, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 753], ["TableCell", 120], ["Line", 90], ["Caption", 3], ["TextInlineMath", 3], ["Reference", 3], ["Text", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3856, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 53], ["TableCell", 36], ["Text", 3], ["TextInlineMath", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1414, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 47], ["Reference", 9], ["ListItem", 8], ["Text", 4], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 47], ["ListItem", 19], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 47], ["ListItem", 19], ["Reference", 15], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 46], ["ListItem", 17], ["Reference", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 44], ["ListItem", 23], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 703], ["TableCell", 136], ["Line", 51], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3254, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["TableCell", 84], ["Line", 50], ["Text", 4], ["ListItem", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 622], ["TableCell", 610], ["Line", 53], ["Caption", 6], ["Table", 6], ["Reference", 6], ["TableGroup", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 19689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["TableCell", 117], ["Line", 46], ["Text", 7], ["SectionHeader", 3], ["Caption", 2], ["Table", 2], ["ListItem", 2], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["TableCell", 26], ["Line", 12], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2336, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bidirectional_Learning_for_Offline_Infinite-width_Model-based_Optimization"}