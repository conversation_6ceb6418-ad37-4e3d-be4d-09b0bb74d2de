{"table_of_contents": [{"title": "Bidirectional Learning for Offline Model-based Biological Sequence Design", "heading_level": null, "page_id": 0, "polygon": [[66.75, 89.25], [528.92578125, 89.25], [528.92578125, 104.1240234375], [66.75, 104.1240234375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 176.25], [195.75, 176.25], [195.75, 187.365234375], [148.5, 187.365234375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 615.0], [132.75, 615.0], [132.75, 627.64453125], [54.0, 627.64453125]]}, {"title": "2. Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[306.0, 68.25], [389.373046875, 68.25], [389.373046875, 78.9873046875], [306.0, 78.9873046875]]}, {"title": "2.1. Offline Model-based Optimization", "heading_level": null, "page_id": 1, "polygon": [[304.9541015625, 87.75], [471.0, 87.75], [471.0, 98.66162109375], [304.9541015625, 98.66162109375]]}, {"title": "2.2. Biological Sequence Representation", "heading_level": null, "page_id": 1, "polygon": [[306.0, 268.5], [477.0, 268.5], [477.0, 278.82421875], [306.0, 278.82421875]]}, {"title": "2.3. <PERSON><PERSON><PERSON> on Sequence", "heading_level": null, "page_id": 1, "polygon": [[306.0, 523.5], [450.75, 523.5], [450.75, 533.671875], [306.0, 533.671875]]}, {"title": "2.4. Bidirectional Learning", "heading_level": null, "page_id": 2, "polygon": [[54.0, 268.5], [170.25, 268.5], [170.25, 278.82421875], [54.0, 278.82421875]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 567.0], [108.6240234375, 567.0], [108.6240234375, 577.7578125], [54.0, 577.7578125]]}, {"title": "3.1. Deep Linearization for Bidirectional Learning", "heading_level": null, "page_id": 2, "polygon": [[306.0, 69.0], [522.052734375, 69.0], [522.052734375, 79.5673828125], [306.0, 79.5673828125]]}, {"title": "3.2. Adaptive-\\gamma", "heading_level": null, "page_id": 3, "polygon": [[54.0, 523.5], [121.10009765625, 523.5], [121.10009765625, 533.671875], [54.0, 533.671875]]}, {"title": "3.3. Adaptive-\\eta", "heading_level": null, "page_id": 4, "polygon": [[54.0, 69.0], [120.75, 69.0], [120.75, 79.42236328125], [54.0, 79.42236328125]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[54.0, 494.25], [132.75, 494.25], [132.75, 504.66796875], [54.0, 504.66796875]]}, {"title": "4.1. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[54.0, 586.5], [122.25, 586.5], [122.25, 596.70703125], [54.0, 596.70703125]]}, {"title": "4.2. Comparison Methods", "heading_level": null, "page_id": 4, "polygon": [[306.0, 118.5], [418.5, 118.5], [418.5, 127.9072265625], [306.0, 127.9072265625]]}, {"title": "4.3. Training Details", "heading_level": null, "page_id": 4, "polygon": [[306.0, 490.5], [393.75, 490.5], [393.75, 500.80078125], [306.0, 500.80078125]]}, {"title": "4.4. Results and Analysis", "heading_level": null, "page_id": 4, "polygon": [[306.0, 630.75], [414.0, 630.75], [414.0, 639.75], [306.0, 639.75]]}, {"title": "4.5. Ablation Studies", "heading_level": null, "page_id": 5, "polygon": [[306.0, 215.25], [396.0, 215.25], [396.0, 225.650390625], [306.0, 225.650390625]]}, {"title": "4.6. Adaptive-\\eta", "heading_level": null, "page_id": 6, "polygon": [[54.0, 477.75], [120.75, 477.75], [120.75, 488.8125], [54.0, 488.8125]]}, {"title": "5. Related Work", "heading_level": null, "page_id": 6, "polygon": [[306.0, 467.25], [390.75, 467.25], [390.75, 478.37109375], [306.0, 478.37109375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 495.75], [377.25, 495.75], [377.25, 506.98828125], [306.0, 506.98828125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 162.0], [111.75, 162.0], [111.75, 173.1533203125], [54.0, 173.1533203125]]}, {"title": "7. <PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 10, "polygon": [[54.0, 287.138671875], [117.75, 287.138671875], [117.75, 299.126953125], [54.0, 299.126953125]]}, {"title": "7.1. DNA Embedding", "heading_level": null, "page_id": 10, "polygon": [[54.0, 307.5], [147.0, 306.75], [147.0, 318.462890625], [54.0, 318.462890625]]}, {"title": "7.2. Different Pretrained LMs", "heading_level": null, "page_id": 10, "polygon": [[54.0, 447.0], [183.0322265625, 447.0], [183.0322265625, 457.1015625], [54.0, 457.1015625]]}, {"title": "7.3. Task Details", "heading_level": null, "page_id": 10, "polygon": [[54.0, 586.5], [125.25, 586.5], [125.25, 596.70703125], [54.0, 596.70703125]]}, {"title": "7.4. Different Dataset Size", "heading_level": null, "page_id": 11, "polygon": [[54.0, 318.0], [165.75, 318.0], [165.75, 328.32421875], [54.0, 328.32421875]]}, {"title": "7.5. Training Details", "heading_level": null, "page_id": 11, "polygon": [[54.0, 409.5], [142.5, 409.5], [142.5, 419.9765625], [54.0, 419.9765625]]}, {"title": "7.6. Ranking Performance", "heading_level": null, "page_id": 11, "polygon": [[54.0, 536.25], [167.25, 537.5390625], [167.25, 547.59375], [54.0, 547.59375]]}, {"title": "7.7. Negative Impact", "heading_level": null, "page_id": 11, "polygon": [[54.0, 653.16796875], [143.25, 653.16796875], [143.25, 663.22265625], [54.0, 663.22265625]]}, {"title": "7.8. Reproducibility Statement", "heading_level": null, "page_id": 11, "polygon": [[306.0, 378.75], [438.75, 378.75], [438.75, 388.845703125], [306.0, 388.845703125]]}, {"title": "7.9. Lim<PERSON>s", "heading_level": null, "page_id": 11, "polygon": [[306.0, 482.25], [375.0, 482.25], [375.0, 492.6796875], [306.0, 492.6796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 89], ["Text", 6], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6753, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["Line", 109], ["Text", 7], ["TextInlineMath", 6], ["SectionHeader", 4], ["Equation", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1017], ["Line", 152], ["Equation", 12], ["TextInlineMath", 9], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 714], ["Line", 137], ["Text", 10], ["TextInlineMath", 6], ["Equation", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1897, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 104], ["Text", 10], ["SectionHeader", 6], ["Reference", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 110], ["Text", 5], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 876, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1114], ["TableCell", 206], ["Line", 117], ["Text", 4], ["Reference", 4], ["Table", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 3567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["TableCell", 201], ["Line", 89], ["Text", 7], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5518, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 92], ["ListItem", 23], ["Reference", 23], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 92], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 96], ["Text", 11], ["Reference", 7], ["ListItem", 4], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["TableCell", 106], ["Line", 86], ["Reference", 8], ["Text", 7], ["SectionHeader", 6], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["TableCell", 68], ["Line", 10], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7749, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Bidirectional_Learning_for_Offline_Model-based_Biological_Sequence_Design"}