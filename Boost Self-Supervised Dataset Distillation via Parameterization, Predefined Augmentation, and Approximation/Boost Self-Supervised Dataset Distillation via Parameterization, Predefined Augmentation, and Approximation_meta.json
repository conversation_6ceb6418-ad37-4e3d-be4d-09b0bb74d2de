{"table_of_contents": [{"title": "BOOST SELF-SUPERVISED DATASET DISTILLATION\nVIA PARAMETERIZATION, PREDEFINED AUGMENTA-\nTION, AND APPROXIMATION", "heading_level": null, "page_id": 0, "polygon": [[106.5, 81.75], [505.916015625, 81.75], [505.916015625, 136.51171875], [106.5, 136.51171875]]}, {"title": "<PERSON><PERSON><PERSON><PERSON>^{12}, <PERSON><PERSON><PERSON><PERSON><PERSON>^{1}, <PERSON><PERSON><PERSON>^{1}\\,", "heading_level": null, "page_id": 0, "polygon": [[112.58349609375, 157.65576171875], [326.021484375, 157.65576171875], [326.021484375, 169.03814697265625], [112.58349609375, 169.03814697265625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 222.0], [335.8828125, 222.0], [335.8828125, 232.41796875], [276.75, 232.41796875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[106.90576171875, 527.25], [207.38671875, 527.25], [207.38671875, 537.92578125], [106.90576171875, 537.92578125]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 3, "polygon": [[106.8310546875, 81.59765625], [212.25, 81.59765625], [212.25, 94.2626953125], [106.8310546875, 94.2626953125]]}, {"title": "Self-Supervised Learning.", "heading_level": null, "page_id": 3, "polygon": [[107.25, 115.1455078125], [221.8798828125, 115.1455078125], [221.8798828125, 126.5537109375], [107.25, 126.5537109375]]}, {"title": "3 METHODOLOGY", "heading_level": null, "page_id": 4, "polygon": [[107.25, 300.287109375], [210.0, 300.287109375], [210.0, 311.115234375], [107.25, 311.115234375]]}, {"title": "3.1 PRELIMINARY", "heading_level": null, "page_id": 4, "polygon": [[107.1298828125, 325.5], [192.75, 325.5], [192.75, 335.671875], [107.1298828125, 335.671875]]}, {"title": "3.2 IMAGE AND REPRESENTATION PARAMETERIZATION", "heading_level": null, "page_id": 5, "polygon": [[106.75634765625, 372.75], [351.0, 372.75], [351.0, 382.271484375], [106.75634765625, 382.271484375]]}, {"title": "3.3 PREDEFINED DATA AUGMENTATION AND FEATURE APPROXIMATION", "heading_level": null, "page_id": 6, "polygon": [[106.8310546875, 121.5], [423.140625, 121.5], [423.140625, 131.7744140625], [106.8310546875, 131.7744140625]]}, {"title": "3.4 OPTIMIZATION AND EVALUATION", "heading_level": null, "page_id": 6, "polygon": [[106.5, 420.75], [275.25, 420.75], [275.25, 431.19140625], [106.5, 431.19140625]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 7, "polygon": [[107.25, 196.259765625], [202.0078125, 196.259765625], [202.0078125, 207.474609375], [107.25, 207.474609375]]}, {"title": "4.1 EXPERIMENTAL RESULTS", "heading_level": null, "page_id": 7, "polygon": [[106.5, 491.90625], [241.154296875, 491.90625], [241.154296875, 501.9609375], [106.5, 501.9609375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 619.13671875], [196.927734375, 619.13671875], [196.927734375, 630.73828125], [107.25, 630.73828125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.25, 82.5], [175.5, 82.5], [175.5, 93.05419921875], [107.25, 93.05419921875]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 12, "polygon": [[107.25, 364.482421875], [183.3310546875, 364.482421875], [183.3310546875, 374.923828125], [107.25, 374.923828125]]}, {"title": "A.1 IMPLEMENTATION DETAILS", "heading_level": null, "page_id": 12, "polygon": [[107.05517578125, 389.25], [251.314453125, 389.25], [251.314453125, 399.09375], [107.05517578125, 399.09375]]}, {"title": "A.2 PSEUDO CODE", "heading_level": null, "page_id": 13, "polygon": [[107.25, 306.474609375], [198.0, 306.474609375], [198.0, 317.302734375], [107.25, 317.302734375]]}, {"title": "A.3 TRA<PERSON><PERSON><PERSON> LEARNING AND CROSS-ARCHITECTURE GENERALIZATION ON\nTINYIMAGENET", "heading_level": null, "page_id": 13, "polygon": [[107.25, 623.77734375], [448.83984375, 623.77734375], [448.83984375, 644.66015625], [107.25, 644.66015625]]}, {"title": "Algorithm 1 The proposed self-supervised dataset distillation", "heading_level": null, "page_id": 14, "polygon": [[107.25, 141.0], [355.5, 141.0], [355.5, 150.8203125], [107.25, 150.8203125]]}, {"title": "A.4 VARY THE STORAGE BUDGET ON TINY<PERSON><PERSON><PERSON>ET", "heading_level": null, "page_id": 15, "polygon": [[107.05517578125, 414.17578125], [345.75, 414.17578125], [345.75, 424.23046875], [107.05517578125, 424.23046875]]}, {"title": "A.5 TRA<PERSON><PERSON><PERSON> LEARNING AND CROSS-ARCHITECTURE GENERALIZATION ON IMAGENET", "heading_level": null, "page_id": 15, "polygon": [[107.1298828125, 657.75], [498.0, 657.75], [498.0, 667.4765625], [107.1298828125, 667.4765625]]}, {"title": "A.6 RESULTS ON IMAGENET WITH LARGER IMAGE RESOLUTIONS", "heading_level": null, "page_id": 16, "polygon": [[106.5322265625, 402.75], [397.5, 402.75], [397.5, 413.015625], [106.5322265625, 413.015625]]}, {"title": "A.7 MODELING THE REPRESENTATION SHIFT CAUSED BY AUGMENTATION", "heading_level": null, "page_id": 16, "polygon": [[106.98046875, 690.0], [435.0, 690.0], [435.0, 700.34765625], [106.98046875, 700.34765625]]}, {"title": "A.8 AUGMENTATION.", "heading_level": null, "page_id": 17, "polygon": [[107.25, 393.75], [207.75, 393.75], [207.75, 403.34765625], [107.25, 403.34765625]]}, {"title": "A.9 VISUALIZATION", "heading_level": null, "page_id": 17, "polygon": [[107.25, 635.37890625], [203.25, 635.37890625], [203.25, 645.43359375], [107.25, 645.43359375]]}, {"title": "A.10 COMPUTATIONAL COST", "heading_level": null, "page_id": 18, "polygon": [[106.5, 524.25], [240.75, 524.25], [240.75, 534.4453125], [106.5, 534.4453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 50], ["SectionHeader", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5875, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 58], ["Text", 4], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 62], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 872, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 56], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 633], ["Line", 68], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 953], ["Line", 60], ["TextInlineMath", 6], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 907], ["Line", 52], ["TextInlineMath", 3], ["ListItem", 3], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 54], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 853], ["TableCell", 208], ["Line", 61], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["TableCell", 78], ["Line", 57], ["Caption", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 4060, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 50], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 49], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 51], ["ListItem", 8], ["Reference", 8], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["TableCell", 105], ["Line", 51], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1557, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 869], ["Line", 36], ["ListItem", 4], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["TableCell", 96], ["Line", 52], ["Text", 4], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1492, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["TableCell", 129], ["Line", 60], ["Text", 5], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1066, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 48], ["TableCell", 47], ["Text", 3], ["Caption", 2], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3240, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["TableCell", 35], ["Line", 27], ["Caption", 3], ["Reference", 3], ["Text", 2], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4291, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["TableCell", 176], ["Line", 104], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14260, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Boost Self-Supervised Dataset Distillation via Parameterization, Predefined Augmentation, and Approximation"}