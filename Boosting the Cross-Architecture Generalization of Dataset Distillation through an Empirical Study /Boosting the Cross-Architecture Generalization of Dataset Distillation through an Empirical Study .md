# Boosting the Cross-Architecture Generalization of Dataset Distillation through an Empirical Study

<PERSON><PERSON><PERSON> $^1$ , <PERSON><PERSON> $^1$ , <PERSON><PERSON> $^1$ , <PERSON><PERSON>ng Ji $^{1\ast}$ 

 ${}^{1}$ Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, School of Informatics, Xiamen University, Xiamen, China <EMAIL>, y<PERSON><PERSON><PERSON><PERSON>@stu.xmu.edu.cn

<EMAIL>, <EMAIL>

## Abstract

The poor cross-architecture generalization of dataset distillation greatly weakens its practical significance. This paper attempts to mitigate this issue through an empirical study, which suggests that the synthetic datasets undergo an inductive bias towards the distillation model. Therefore, the evaluation model is strictly confined to having similar architectures of the distillation model. We propose a novel method of EvaLuation with distillation Feature (ELF), which utilizes features from intermediate layers of the distillation model for the cross-architecture evaluation. In this manner, the evaluation model learns from bias-free knowledge therefore its architecture becomes unfettered while retaining performance. By performing extensive experiments, we successfully prove that ELF can well enhance the cross-architecture generalization of current DD methods. Code of this project is at https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/ELF.

## Introduction

Recent years have witnessed encouraging progress of deep neural networks (DNNs) across a wide range of vision applications, such as image classification (Szegedy et al. 2015; He et al. 2016), object detection (Girshick et al. 2014; Ren et al. 2015), instance segmentation (Long, Shelhamer, and Darrell 2015; Chen et al. 2017) and many others. Nevertheless, the success of DNNs heavily counts on very largescale datasets (Deng et al. 2009; Lin et al. 2014) that generally contain millions of samples and pose enormous computational burden in consequence. By mimicking the original large-scale datasets with a smaller size of synthesized samples, dataset distillation (DD) (Wang et al. 2018; Zhao and Bilen 2021b) has therefore been a topic of active research interest lately to maintain model performance as well as to relieve training burden. The most common metric to evaluate DD methods attributes to the training performance on the generated synthetic datasets. In contemporary methods (Zhao and Bilen 2021a; Cazenavette et al. 2022), the networks for distilling dataset and evaluating performance stay the same of a 3-/4-/5-layer shallow ConvNet (Gidaris and Komodakis 2018).

Despite the continuous advances in this setting, the crossarchitecture generalization, referring to as individually distilling and evaluating the synthetic dataset with networks of

Image /page/0/Figure/10 description: This figure contains two bar charts, (a) DSA and (b) MTT, comparing the accuracy of different models. Both charts have 'Accuracy (%)' on the y-axis, ranging from 15.0 to 45.0 in (a) and 20.0 to 50.0 in (b). The x-axis in both charts lists five models: ConvNet-BN, ResNet18-IN, ResNet18-BN, VGG11-IN, and VGG11-BN. Each bar is stacked, representing 'Baseline' (purple) and 'Ours' (red). A dashed black line labeled 'Alignment' is present in both charts, indicating a reference accuracy level. In chart (a), the 'Baseline' accuracies are approximately 27.5, 22.5, 20.5, 22.5, and 29.5 for the respective models. The 'Ours' accuracies add to these, reaching approximately 36.0, 27.5, 30.5, 28.5, and 28.5. The 'Alignment' line is at 32.5. In chart (b), the 'Baseline' accuracies are approximately 27.5, 25.5, 25.5, 25.5, and 31.0. The 'Ours' accuracies add to these, reaching approximately 39.5, 39.5, 39.5, 39.5, and 40.0. The 'Alignment' line is at 40.0.

Figure 1: Improvement of the proposed ELF over existing baseline methods including (a) DSA and (b) MTT. Here, the distillation model is Conv-IN with width of 128 and depth of 3. "IN" and "BN" denote instance normalization and batch normalization. The horizontal coordinates show the different evaluation models. "Alignment" stands for the same evaluation model with the distillation model. Experiments are performed on CIFAR-100 dataset with 10 images per class (IPC).

different architectures, is rather disregarded by the community. We definitely expect a robust performance gain from varying networks trained upon the synthetic dataset while reducing training burden. Unfortunately, a unified distillation and evaluation network fails to achieve cross-architecture generalization. For example, ResNet-18 (He et al. 2016), a strong model that achieves 92.6% on CIFAR-10 dataset, reportedly in (Cazenavette et al. 2022), has only 46.4% on the synthetic dataset distilled by ConvNet. This huge performance gap impedes the practical significance of DD methods. We therefore in this paper aim at alleviating the poor cross-architecture generalization of existing DD methods with our endeavors primarily involving two empirical studies: First, networks of more architectural similarity lead to less performance gap between evaluation and distillation models. Second, performance benefits more from inserting an identical normalization layer with the distillation model into the evaluation model. The presumable reason lies in that the synthetic dataset retains an inductive bias towards the distillation model, which is instead relieved by similar architectures and identical normalization since they align the distillation and evaluation models. Therefore, the inductive

<sup>\*</sup>Corresponding Author

Image /page/1/Figure/0 description: This is a diagram illustrating a neural network architecture. The input data, denoted as \tilde{x}\_i, is processed by a component labeled \mathcal{M}\_{front}. This component is associated with a loss function \mathcal{L}\_{front}. The output of \mathcal{M}\_{front} is fed into two parallel layers, depicted as stacked colored rectangles. Another component, \mathcal{M}\_{rear}, receives input from these parallel layers and also from a component labeled \mathcal{W}\_{\theta}. The \mathcal{M}\_{rear} component produces two outputs, \tilde{y}\_i, each associated with a loss function, \mathcal{L}\_{task} and \mathcal{L}\_{rear} respectively. Red dashed arrows indicate backward propagation, while solid black arrows indicate forward propagation. A light blue arrow shows the flow from \mathcal{W}\_{\theta} to the parallel layers.

Figure 2: Framework of our proposed ELF method. We feed the synthetic dataset to the distillation model and obtain the bias-free intermediate features, which are then used to guide the training process of the evaluation model.

bias imprisons the cross-architecture generalization.

We go over in this paper and realize two innate advantages in the distillation model: *First*, the architecture of distillation model is by nature popular with the synthesized dataset. *Second*, the feature maps from distillation model are invulnerable to the inductive bias. Therefore, we propose a novel method of EvaLuation with distillation Feature (ELF) to boost the cross-architecture generalization of existing DD methods. Fig. 2 outlines the framework of our proposed ELF. As its name suggests, we capitalize fully on the bias-free intermediate features of the distillation model as an auxiliary to train the evaluation model from two perspectives: *First*, the distillation features are leveraged as a form of supervision for the intermediate outputs of evaluation model. *Second*, the distillation features play as disturbed variables to refine the performance of evaluation model in predicting labels. Note that, the trained distillation model is widely available from most existing DD methods, which makes our ELF a plug-and-play method to improve the cross-architecture performance. For example, Fig. 1 shows great performance enhancement of the proposed ELF on CIFAR-100 (Krizhevsky, Hinton et al. 2009) dataset over existing state-of-the-art DD methods including DSA (Zhao and Bilen 2021a) and MTT (Cazenavette et al. 2022). More results on CIFAR-10/100 (Krizhevsky, Hinton et al. 2009), Tiny ImageNet (Le and Yang 2015), and ImageNet (Russakovsky et al. 2015) are provided in Sec. , which substantially prove the efficacy of the proposed ELF method.

The main contributions we have made in this paper include: *First*, an empirical study to reveal the inductive bias in poor cross-architecture generalization. *Second*, a novel method of evaluation with distillation feature to boost the cross-architecture generalization. *Third*, significant performance improvement on the basis of existing methods for dataset distillation.

## Related work

As a pioneer in dataset distillation (DD), Wang *et al*. (Wang et al. 2018) proposed to synthesize a few number of image samples to achieve comparable training performance as an

alternative to the original large-scale dataset. Following this footprint, abundant consecutive studies that can be broadly classified into three groups, have been introduced (Sachdeva and McAuley 2023; Lei and Tao 2023; Yu, Liu, and Wang 2023).

The first group encourages every single- or multiplestep parameter consistency between models trained upon the synthetic and original datasets. To this end, Zhao *et al*. (Zhao and Bilen 2021b) proposed to match per-step gradients between distillation models from the synthetic and original datasets. Some followers (Zhao and Bilen 2021a; Lee et al. 2022) take a further step by including differentiable Siamese augmentation or contrastive signals. Instead, Cazenavette *et al*. (Cazenavette et al. 2022) revealed error accumulation from the single-step gradient matching, and proposed matching training trajectories (MTT) to align parameters in every multiple steps. The discrepancy between the distillation and subsequent may cause accumulated trajectory error, therefore, Du *et al*. (Du et al. 2023) further proposed to seek a flat trajectory. Cui *et al*. (Cui et al. 2023) re-parameterized parameter-matching loss in MTT to reduce the memory requirement. They further proposed to assign soft labels for synthetic datasets when it comes to distilling datasets with a larger number of categories (*e.g.*, ImageNet-1K (Russakovsky et al. 2015)). The second group uplifts the dataset alignment in the distribution level. For example, Wang *et al*. (Wang et al. 2022) aligned layer-wise features between the real and synthetic datasets, and encoded the discriminant power into the synthetic clusters. Zhao *et al*. (Zhao and Bilen 2023) minimized the difference in the embedding space. The last group takes into consideration the Metalearning (Finn, Abbeel, and Levine 2017) for guiding DD in a bi-level optimization fashion. In (Nguyen, Chen, and Lee 2021; Nguyen et al. 2021), the inner optimization is approximated using neural tangent kernel (Lee et al. 2019). Zhou *et al*. (Zhou, Nezhadarya, and Ba 2022) proposed neural feature regression with pooling to alleviate the heavy computation and memory costs from inner loop learning of Metalearning. The back-propagation through time method (Werbos 1990) is used to recursively compute the meta-gradient and update the synthetic dataset (Wang et al. 2018). It is

|                                       | <b>Distillation Model</b>       |                                  |                                  |                                    | <b>Evaluation Model</b> (%)        |                                    |                                  |
|---------------------------------------|---------------------------------|----------------------------------|----------------------------------|------------------------------------|------------------------------------|------------------------------------|----------------------------------|
|                                       |                                 | ConvNet-IN                       | ConvNet-BN                       | ResNet18-IN                        | ResNet18-BN                        | VGG11-IN                           | VGG11-BN                         |
| DM (Zhao and Bilen 2023)              | <b>ConvNet-IN</b><br>ConvNet-BN | $49.52 + 0.19$<br>$42.13 + 0.79$ | $46.17 + 0.52$<br>$49.73 + 0.44$ | $37.38 + 2.34$<br>$36.71 \pm 1.42$ | $39.41 + 0.49$<br>$42.51 \pm 0.81$ | $41.06 + 0.71$<br>$36.11 \pm 0.51$ | $43.80 + 0.41$<br>$44.74 + 0.30$ |
| DSA (Zhao and Bilen 2021a)            | <b>ConvNet-IN</b><br>ConvNet-BN | $51.70 + 0.36$<br>$34.79 + 0.34$ | $43.25 + 0.71$<br>$45.84 + 0.69$ | $41.98 \pm 0.85$<br>$31.43 + 0.73$ | $37.98 + 0.88$<br>$33.13 + 0.68$   | $42.98 \pm 0.81$<br>$29.81 + 0.32$ | $42.66 + 0.67$<br>$36.20 + 0.35$ |
| MTT (Cazenavette et al. 2022)         | <b>ConvNet-IN</b><br>ConvNet-BN | $63.48 + 0.58$<br>$50.50 + 0.80$ | $47.27 + 1.20$<br>$54.18 + 1.13$ | $44.72 \pm 1.43$<br>$39.77 + 0.71$ | $42.32 + 0.40$<br>$40.94 + 2.88$   | $49.04 \pm 0.50$<br>$44.96 + 0.95$ | $46.95 + 1.27$<br>$48.32 + 1.82$ |
| FrePo (Zhou, Nezhadarya, and Ba 2022) | <b>ConvNet-BN</b>               | $\tilde{\phantom{a}}$            | $65.6 + 0.6$                     | $47.4 + 0.7$                       | $53.0 + 1.0$                       | $35.0 + 0.7$                       | $56.8 \pm 0.6$                   |
| Entire CIFAR-10                       |                                 | 86.64                            | 88.49                            | 92.60                              | 93.69                              | 88.05                              | 90.46                            |

Table 1: Cross-architecture performance on CIFAR-10 (Krizhevsky, Hinton et al. 2009). ConvNet is of width:128 and depth:3. We synthesize 10 images per class (IPC) and the experiments are run over 5 times.

|               | ConvNet-IN   | ConvNetW256-IN | ConvNetD4-IN | ResNet18-IN  |
|---------------|--------------|----------------|--------------|--------------|
| Synthetic (%) | 63.48 ± 0.58 | 64.75 ± 0.20   | 59.03 ± 0.65 | 44.72 ± 1.43 |
| CIFAR-10 (%)  | 86.64        | 88.27          | 87.20        | 92.36        |
| Synthetic (%) | 38.16 ± 0.32 | 39.91 ± 0.32   | 34.11 ± 0.27 | 26.33 ± 0.52 |
| CIFAR-100 (%) | 57.78        | 61.99          | 59.24        | 66.50        |

Table 2: Performance of evaluation models with similar architectures to the distillation model ConvNet-IN (Gidaris and Komodakis 2018). We utilize MTT (Cazenavette et al. 2022) to synthesize a dataset (10 IPC) from CIFAR-10/100 (Krizhevsky, Hinton et al. 2009) "W256" and "D4" denote the width of 256 channels and the depth of 4 layers, respectively. .

also considered to optimize image labels (Bohdal, Yang, and Hospedales 2020; Sucholutsky and Schonlau 2021). Deng *et al*. (Deng and Russakovsky 2022) showed a momentum term and a longer unrolled trajectory in the inner loop well enhance distillation performance.

These DD methods mostly focus on evaluating performance of synthetic datasets within the same architecture. This paper prioritizes enhancing the cross-architecture performance. Besides, recent works (Liu et al. 2022; Deng and Russakovsky 2022; Kim et al. 2022; Lee, Lee, and Hwang 2022) factorize synthetic datasets to increase samples of more diversity and less redundancy. Our effort in this paper is directly towards analyzing and improving the cross-architecture performance of the non-factorized methods, leading to a complementary method to increasing synthetic samples.

## Methodology

### Background

Consider a large-scale training dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  $i=1$ that contains  $|{\cal T}|$  pairs of training sample  $x_i$  and corresponding label  $y_i$ , as well as a testing dataset  $\mathcal{T}_{test}$  of the same domain with  $\mathcal{T}$ . DD excavates a distillation model  $\mathcal{W}_{\theta}$  to condense  $\mathcal T$  into a small-scale version  $\mathcal S = \{(\tilde x_i, \tilde y_i)\}_{i=1}^{|\mathcal S|}$  where  $|S| \ll |\mathcal{T}|$ , such that, when testing on  $\mathcal{T}_{test}$ , an evaluation model  $\mathcal{M}_{\phi}$  efficiently trained on S performs on par with that cumbersomely trained on  $\mathcal T$ . Here, the  $\theta$  and  $\phi$  are the parameters of  $W$  and  $M$ . Off-the-shelf methods implement this target from three perspectives as we briefly describe in the following.

Parameter-matching based methods separately train the distillation model using  $\mathcal T$  and  $\mathcal S$ , resulting in  $\theta^{\mathcal T}$  and  $\theta^{\mathcal S}$ .

During training,  $S$  is iteratively optimized to match the updating parameters of  $\boldsymbol{\theta}^{\mathcal{T}}$  and  $\boldsymbol{\theta}^{\mathcal{S}}$  as:

$$
\mathcal{L}(\mathcal{S}) = D(\Delta \boldsymbol{\theta}^{\mathcal{T}}, \Delta \boldsymbol{\theta}^{\mathcal{S}}), \tag{1}
$$

where  $D(\cdot, \cdot)$  is a specific distance function like cosine similarity, and  $\Delta \theta^{\mathcal{T}}$  stands as gradient accumulation from single (Zhao and Bilen 2021a) or multiple (Cazenavette et al. 2022) training steps.

Distribution-matching based methods update  $S$  by matching the distributions of  $T$  and  $S$ . The distribution is measured with the features from the distillation model (Zhao and Bilen 2023):

$$
\mathcal{L}(\mathcal{S}) = D\Big(\mathcal{F}\big(\mathcal{W}_{\theta}(\mathcal{T})\big), \mathcal{F}\big(\mathcal{W}_{\theta}(\mathcal{S})\big)\Big),\tag{2}
$$

where  $\mathcal{F}(\cdot, \cdot)$  denotes the feature maps from the last convolutional block.

Meta-learning based methods optimize  $S$  in a bi-level manner and can be formulated as:

$$
S^* = \arg\min_{S} \mathbb{E}_{(x,y)\sim\mathcal{T}} \Big[ \mathcal{L}(\mathcal{W}_{\theta^*}(x), y) \Big], \qquad (3)
$$

subject to

$$
\boldsymbol{\theta}^* = \arg\min_{\boldsymbol{\theta}} \mathbb{E}_{(\tilde{x}, \tilde{y}) \sim \mathcal{S}} \Big[ \mathcal{L} \big( \mathcal{W}_{\boldsymbol{\theta}}(\tilde{x}), \tilde{y} \big) \Big], \tag{4}
$$

where  $\mathcal{L}(\cdot, \cdot)$  is the training loss with respect to specific sample pairs. In this way,  $S$  is updated such that the model trained on S minimizes the training loss over  $\mathcal{T}$ .

Currently, most DD methods consider a 3-/4-/5-layer ConvNet as the network backbone for both the distillation and evaluation models. The community rarely investigates the cross-architecture generalization of DD methods, which

|                               | <b>Distillation Model</b>       |                                  | <b>Evaluation Model</b> (%)      |                                  |                                    |                                  |                                  |  |
|-------------------------------|---------------------------------|----------------------------------|----------------------------------|----------------------------------|------------------------------------|----------------------------------|----------------------------------|--|
|                               |                                 | ConvNet-IN                       | ConvNet-BN                       | ResNet18-IN                      | ResNet18-BN                        | VGG11-IN                         | VGG11-BN                         |  |
| DSA (Zhao and Bilen 2021a)    | <b>ConvNet-IN</b><br>ConvNet-BN | $61.14 + 0.30$<br>$48,49 + 0.62$ | $56.89 + 0.21$<br>$60.44 + 0.47$ | $49.50 + 0.49$<br>$42.61 + 0.20$ | $50.71 + 0.54$<br>$47.87 + 1.30$   | $51.11 + 0.16$<br>$43.11 + 0.43$ | $55.80 + 0.44$<br>$51.63 + 0.38$ |  |
| MTT (Cazenavette et al. 2022) | <b>ConvNet-IN</b><br>ConvNet-BN | $71.60 + 0.20$<br>$62.43 + 0.27$ | $62.65 + 0.60$<br>$69.50 + 0.89$ | $57.68 + 0.71$<br>$53.79 + 0.85$ | $58.48 \pm 0.89$<br>$61.84 + 0.89$ | $62.09 + 0.40$<br>$58.01 + 1.00$ | $63.31 + 0.50$<br>$64.69 + 0.68$ |  |
| Entire CIFAR-10               |                                 | 86.64                            | 88.49                            | 92.39                            | 93.69                              | 88.05                            | 90.46                            |  |

Table 3: Cross-architecture performance on CIFAR10 with 50 IPC. For DSA (Zhao and Bilen 2021a), ResNet18 (He et al. 2016) and VGG11 (Szegedy et al. 2015) are in favor of batch normalization.

| MTT (Cazenavette et al. 2022) | <b>Distillation Model</b> | <b>Evaluation Model</b> (%) |                  |                  |                  |                  |                  |
|-------------------------------|---------------------------|-----------------------------|------------------|------------------|------------------|------------------|------------------|
|                               |                           | ConvNet-IN                  | ConvNet-BN       | ResNet18-IN      | ResNet18-BN      | VGG11-IN         | VGG11-BN         |
| 10 IPC                        | <b>ConvNet-IN</b>         | $39.58 \pm 0.24$            | $31.73 \pm 0.15$ | $26.39 \pm 0.66$ | $27.21 \pm 0.53$ | $27.50 \pm 0.26$ | $31.71 \pm 0.58$ |
|                               | <b>ConvNet-BN</b>         | $30.16 \pm 0.32$            | $36.78 \pm 0.18$ | $21.46 \pm 0.62$ | $27.24 \pm 0.69$ | $23.10 \pm 0.28$ | $31.35 \pm 0.69$ |
| 50 IPC                        | <b>ConvNet-IN</b>         | $47.03 \pm 0.15$            | $47.27 \pm 0.19$ | $41.17 \pm 0.52$ | $46.43 \pm 0.45$ | $41.59 \pm 0.19$ | $49.02 \pm 0.19$ |
|                               | <b>ConvNet-BN</b>         | $44.76 \pm 0.17$            | $51.11 \pm 0.32$ | $40.59 \pm 0.35$ | $49.49 \pm 0.48$ | $38.97 \pm 0.33$ | $50.72 \pm 0.25$ |
| Entire CIFAR-100              |                           | 57.78                       | 63.09            | 66.50            | 74.75            | 56.72            | 68.06            |

Table 4: Cross-architecture performance of MTT (Cazenavette et al. 2022) on CIFAR100 (Krizhevsky, Hinton et al. 2009). A large dataset benefits more to models with batch normalization.

uses different networks to respectively distill and evaluate the synthetic dataset. It is natural to expect advanced networks like ResNet (He et al. 2016) to perform well on the synthetic dataset distilled by the simple ConvNet.

# An Empirical Study of Cross-Architecture Generalization

We begin by investigating the cross-architecture generalization of existing DD methods through an empirical study. To derive the small-scale dataset  $S$ , we consider representative methods including parameter-matching based DSA (Zhao and Bilen 2021a) and MTT (Cazenavette et al. 2022), distribution-matching based DM (Zhao and Bilen 2023), and meta-learning based FrePo (Zhou, Nezhadarya, and Ba 2022). Table 1 reports the performance for distilling CIFAR-10 dataset (Krizhevsky, Hinton et al. 2009) where the distilled small dataset  $S$  consists of 10 images per class (IPC) and the evaluation models are equipped with different architectures over the distillation models. We have two observations.

*First*, all DD methods suffer notable performance degradation in the cross-architecture evaluation. For example, ResNet18 (He et al. 2016) with batch normalization layers is reportedly to have 93.69% top-1 accuracy on CIFAR10, however, only 53.0% is obtained if trained on  $S$  distilled by ConvNet-BN, as shown in the FrePo method of Table. 1. These strong modern networks perform even worse than the simple ConvNet. We attribute the poor performance to the inductive bias of  $S$  towards the distillation model. As delineated by all Eq. (1) to Eq. (3), the loss function utilized in current DD methods serves as an indirect constraint related to the dynamic training process's model parameters or output distribution. Though this ensures a robust performance if the same network architecture is adopted for evaluation model, it drives  $S$  towards overfitting the training procedure of the distillation model. Therefore, this phenomenon contributes to the observed deterioration in evaluation performance of  $S$  as the disparity in model architectures widens.

*Second*, the performance drop can be somewhat mitigated through an alignment of normalization layers. For instance, ResNet18-IN shows better accuracy than ResNet18- BN when trained on  $S$  synthesized by ConvNet-IN, and vice versa. This empirically proves the inductive bias as the same normalization layer indicates a more similar structure between the distillation and evaluation models. Table 2 further investigates how the similarity of architectures influences the cross-architecture generalization by evaluating multiple variants of ConvNet on  $S$  distilled by its vanilla version. We can safely conclude that the performance degradation of the cross-architecture evaluation is closely related to the architecture difference. The alignment of architecture between the evaluation and distillation models mitigates the impact of inductive bias. Unfortunately, the requirement of similar architectures confines the cross-architecture generalization.

Moreover, normalization alignment also incarcerates the capability of modern networks. The cross-architecture experiment on CIFAR10 with 50 IPC is conducted in Table 3. Light anomalies, such as those in DSA where ResNet18 and VGG11 favor batch normalization even on the synthetic dataset distilled by ConvNet-IN, can be observed. We posit that this phenomenon arises from the interplay between variations in modern evaluation models (*e.g.*, ResNet's greater advantage with batch normalization) and the inductive bias, which becomes more prominent as synthetic datasets grow in size. Table 4 presents additional results that further validate our analysis and extend the observed trend. The persistent presence of the inductive bias continues to yield adverse effects, despite the compensatory or even surpassing impact of model disparities.

Therefore, how to mitigate the inductive bias in existing DD methods without a compromise on architecture adaption remains to be well addressed.

|                               | ConvNet-IN       |                                   | ConvNet-BN                                      | ResNet18-IN                                    | ResNet18-BN                                   | VGG11-IN                                       | VGG11-BN                                      |
|-------------------------------|------------------|-----------------------------------|-------------------------------------------------|------------------------------------------------|-----------------------------------------------|------------------------------------------------|-----------------------------------------------|
| DM (Zhao and Bilen 2023)      | $49.52 \pm 0.19$ | <b>Baseline</b><br>w. ELF<br>Gain | $46.17 \pm 0.52$<br>$55.19 \pm 0.44$<br>$+9.02$ | $37.38 + 2.34$<br>$38.81 + 0.35$<br>$+1.43$    | $39.41 + 0.49$<br>$41.59 \pm 0.68$<br>$+2.18$ | $41.06 + 0.71$<br>$47.52 \pm 0.56$<br>$+6.46$  | $43.80 + 0.41$<br>$46.43 \pm 1.70$<br>$+3.35$ |
| DSA (Zhao and Bilen 2021a)    | $51.70 + 0.36$   | <b>Baseline</b><br>w. ELF<br>Gain | $43.25 + 0.71$<br>$54.01 + 0.48$<br>$+10.76$    | $41.98 + 0.85$<br>$42.29 + 0.40$<br>$+1.31$    | $37.98 + 0.88$<br>$40.45 + 1.80$<br>$+2.47$   | $42.98 + 0.81$<br>$49.19 + 0.21$<br>$+6.21$    | $42.66 + 0.67$<br>$44.62 + 2.15$<br>$+1.96$   |
| MTT (Cazenavette et al. 2022) | $63.48 \pm 0.58$ | Baseline<br>w. ELF<br>Gain        | $47.27 + 1.20$<br>$58.42 \pm 1.44$<br>$+11.15$  | $44.72 + 1.43$<br>$55.11 \pm 0.89$<br>$+10.39$ | $42.32 + 0.40$<br>$50.16 \pm 1.11$<br>$+7.84$ | $49.04 + 0.50$<br>$61.23 \pm 0.69$<br>$+12.19$ | $46.95 + 1.27$<br>$55.49 \pm 2.32$<br>$+8.54$ |
| Entire CIFAR-10               | 86.64            |                                   | 88.49                                           | 92.39                                          | 93.69                                         | 88.05                                          | 90.46                                         |

Table 5: Cross-architecture performance comparison of different baseline methods and corresponding ELF built upon. Experiments are performed on CIFAR10 with 10 IPC.

|                               | ConvNet-IN       |                                   | ConvNet-BN                                      | ResNet18-IN                                    | ResNet18-BN                                     | VGG11-IN                                        | VGG11-BN                                      |
|-------------------------------|------------------|-----------------------------------|-------------------------------------------------|------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-----------------------------------------------|
| DM (Zhao and Bilen 2023)      | $29.45 \pm 0.27$ | <b>Baseline</b><br>w. ELF<br>Gain | $28.46 \pm 0.32$<br>$35.74 \pm 0.28$<br>$+7.28$ | $20.06 + 1.96$<br>$25.99 \pm 0.27$<br>$+5.93$  | $20.98 \pm 0.68$<br>$28.12 \pm 0.86$<br>$+7.14$ | $21.42 \pm 0.35$<br>$28.90 \pm 0.26$<br>$+7.48$ | $26.51 + 0.37$<br>$29.94 \pm 0.48$<br>$+3.43$ |
| DSA (Zhao and Bilen 2021a)    | $31.76 \pm 0.37$ | <b>Baseline</b><br>w. ELF<br>Gain | $27.56 + 0.18$<br>$36.02 \pm 0.35$<br>$+8.46$   | $21.96 + 0.51$<br>$27.54 + 0.19$<br>$+5.58$    | $20.45 + 0.53$<br>$30.26 + 0.65$<br>$+9.81$     | $22.00 \pm 0.34$<br>$28.74 + 0.11$<br>$+6.74$   | $25.73 \pm 0.41$<br>$28.54 + 1.23$<br>$+2.81$ |
| MTT (Cazenavette et al. 2022) | $39.58 + 0.24$   | <b>Baseline</b><br>w. ELF<br>Gain | $31.73 + 0.15$<br>$39.32 \pm 0.24$<br>$+7.59$   | $26.39 + 0.66$<br>$38.48 \pm 0.14$<br>$+12.09$ | $27.21 + 0.53$<br>$38.76 + 0.80$<br>$+11.55$    | $27.50 + 0.26$<br>$38.20 \pm 0.49$<br>$+10.70$  | $31.71 + 0.58$<br>$38.78 + 0.84$<br>$+7.07$   |
| Entire CIFAR-100              | 57.68            |                                   | 63.09                                           | 66.50                                          | 74.75                                           | 56.72                                           | 68.06                                         |

Table 6: Cross-architecture performance comparison of different baseline methods and corresponding ELF built upon. Experiments are performed on CIFAR100 with 10 IPC.

### Evaluation with the Distillation Feature

It is obvious that the inductive bias towards the distillation model only occurs to the synthesized  $S$ , instead of the original dataset  $T$  or  $T_{test}$ . Not surprisingly, an evaluation model beyond the architecture scope of distillation model, performs poorly if tested on  $\mathcal{T}_{test}$  while trained on  $\mathcal{S}$ .

Looking back on the distillation model  $W_{\theta}$ , we find it by nature is a good assistance, for two advantages, to help a distillation-architecture-beyond evaluation model break away the inductive bias: *First*, the inductive bias does not harm its performance if tested on  $\mathcal{T}_{test}$  while trained on  $\mathcal T$ since its architecture is popular with the synthesized S. *Second*, its feature map outputs are not affected by the inductive bias since it is trained upon the original training dataset  $\mathcal{T}$ .

Motivated by this, we propose a novel EvaLuation-withthe-distillation-Feature method (ELF) to reduce the inductive bias with no sacrifice of various architectures of the evaluation model. To be concrete, for  $S = \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^{|S|}$ , we feed each synthesized image  $\tilde{x}_i$  to the distillation model, and obtain output from the last convolutional block as the desired intermediate feature, denoted as  $\mathcal{F}(\mathcal{W}_{\theta}(\tilde{x}_i))$ , which as discussed before, is not influenced by the inductive bias. Therefore, we can utilize it to guide the training process of the evaluation model to mitigate the performance drop.

For ease of the following representation, we split the evaluation model  $\mathcal{M}_{\phi}$  into the front section  $\mathcal{M}_{\phi_{front}}$  and rear section  $\mathcal{M}_{\phi_{rear}}$ , where  $\mathcal{M}_{\phi_{front}} \cap \mathcal{M}_{\phi_{rear}} = \emptyset$  and

 $\mathcal{M}_{\phi_{front}} \cup \mathcal{M}_{\phi_{rear}} = \mathcal{M}_{\phi}$ . Given an input image x, we have:

$$
\mathcal{M}_{\phi}(x) = \mathcal{M}_{\phi_{rear}}(\mathcal{M}_{\phi_{front}}(x)). \tag{5}
$$

We make full use of the bias-free intermediate feature  $\mathcal{F}(\mathcal{W}_{\theta}(\tilde{x}_i))$  to supervise the output of the front section  $\mathcal{M}_{\phi_{front}}$  as:

$$
\mathcal{L}_{front} = \sum_{i=1}^{|\mathcal{S}|} D\Big(\mathcal{F}\big(\mathcal{W}_{\theta}(\tilde{x}_i)\big), \mathcal{M}_{\phi_{front}}(\tilde{x}_i)\Big). \quad (6)
$$

Also, we feed  $\mathcal{F}\big(\mathcal{W}_{\bm\theta}(\tilde{x}_i)\big)$  to the rear section  $\mathcal{M}_{\bm\phi_{\bm r e \bm a \bm r}}$  for classification supervision as:

$$
\mathcal{L}_{rear} = \sum_{i=1}^{|\mathcal{S}|} KL\bigg(\mathcal{M}_{\phi_{rear}}\bigg(\mathcal{F}\big(\mathcal{W}_{\theta}(\tilde{x}_i)\big)\bigg), \tilde{y}_i\bigg), \qquad (7)
$$

where  $KL(\cdot, \cdot)$  returns the cross-entropy loss. This allows the evaluation model to learn to predict labels on the basis of disturbed features. In addition, the evaluation model itself also learns classification task as:

$$
\mathcal{L}_{task} = \sum_{i=1}^{|\mathcal{S}|} KL(\mathcal{M}_{\phi}(x_i), \tilde{y}_i).
$$
 (8)

Finally, the training objective of our proposed ELF becomes:

$$
\mathcal{L} = \mathcal{L}_{task} + \lambda_{front} \mathcal{L}_{front} + \lambda_{rear} \mathcal{L}_{rear},
$$
 (9)

in which  $\lambda_{front}$  and  $\lambda_{rear}$  are two tradeoff parameters.

|                     | ConvNet-IN     |                                                 | ConvNet-BN                                    | ResNet18-IN                                   | ResNet18-BN                                     | VGG11-IN                                      | VGG11-BN                                      |
|---------------------|----------------|-------------------------------------------------|-----------------------------------------------|-----------------------------------------------|-------------------------------------------------|-----------------------------------------------|-----------------------------------------------|
| Tiny<br>ImageNet    | $23.11 + 1.83$ | MTT (Cazenavette et al. 2022)<br>w. ELF<br>Gain | $19.59 + 0.27$<br>$23.80 \pm 0.30$<br>$+4.21$ | $10.02 + 0.15$<br>$19.73 \pm 0.31$<br>$+9.71$ | $16.04 \pm 0.56$<br>$21.24 \pm 0.49$<br>$+5.20$ | $11.23 + 0.28$<br>$19.56 \pm 0.30$<br>$+8.33$ | $16.32 + 0.47$<br>$20.60 \pm 0.66$<br>$+4.28$ |
| <b>Full Dataset</b> | 40.89          |                                                 | 45.98                                         | 35.63                                         | 46.93                                           | 39.78                                         | 53.59                                         |
| ImageNet            | $17.8 \pm 1.3$ | TESLA (Cui et al. 2023)<br>w. ELF<br>Gain       | $18.62 + 0.10$<br>$19.01 \pm 0.07$<br>$+0.39$ | $8.83 + 0.15$<br>$15.65 + 0.16$<br>$+6.82$    | $13.67 + 0.05$<br>$17.32 \pm 0.19$<br>$+3.65$   | $14.76 + 0.08$<br>$19.96 \pm 0.02$<br>$+5.20$ | $17.57 + 0.14$<br>$19.56 \pm 0.38$<br>$+1.99$ |
| <b>Full Dataset</b> | 38.74          |                                                 | 41.46                                         | 37.83                                         | 49.02                                           | 48.11                                         | 54.44                                         |

Table 7: Cross-architecture performance comparison on Tiny ImageNet and ImageNet-1K with 10 IPC.

| IPC                | 1            | 10           |
|--------------------|--------------|--------------|
| Baseline (ConvNet) | 26.52 ± 0.34 | 43.69 ± 0.27 |
| w. ResNet Feature  | 22.75 ± 1.29 | 34.05 ± 0.10 |
| w. ConvNet Feature | 30.76 ± 0.34 | 48.60 ± 0.23 |
| Baseline (ResNet)  | 10.12 ± 0.68 | 28.03 ± 0.26 |
| w. ResNet Feature  | 17.87 ± 0.65 | 32.58 ± 0.23 |
| w. ConvNet Feature | 22.98 ± 0.42 | 40.34 ± 0.23 |

Table 8: Performances of using different features on different baseline networks. ConvNet denotes ConvNetW512-IN and ResNet denotes ResNet18-IN in here. CIFAR-100 1/10 IPC, using ZCA preprocessing during distillation. Our default setting is highlighted in gray.

# Experimentation

### Experimental Details

We evaluate our method on four standard image classification benchmarks including CIFAR10/100 (Krizhevsky, Hinton et al. 2009), Tiny ImageNet (Le and Yang 2015), and ImageNet-1K (Russakovsky et al. 2015). CIFAR-10/100 comprises 50,000 training images from 10/100 classes, with a resolution of  $32\times32$ . Tiny ImageNet dataset is composed of 100,000 training examples and 10,000 test examples with a higher resolution of  $64\times64$ . They are from 200 classes. ImageNet-1K is a widely-used large-scale dataset containing 1,000 classes and 1,281,167 training images. Following (Zhou, Nezhadarya, and Ba 2022; Cui et al. 2023), the resolution in ImageNet-1K is resized to  $64\times64$ .

We verify the efficacy of ELF in boosting the crossarchitecture performance of representative DD methods, including DM (Zhao and Bilen 2023), DSA (Zhao and Bilen 2021a), MTT (Cazenavette et al. 2022), and TESLA (Cui et al. 2023). During the distillation phase, we maintain the same experimental settings as the baseline methods and use 3-/4-layer ConvNets as the distillation model for  $32\times32/64\times64$  resolution datasets respectively. More details for the implementation of based methods are provided in the supplementary materials. As to the cross-architecture generalization, the split of the ResNet-18 is located at the conv5 2 for CIFAR-10/100 and conv4<sub>-4</sub> for Tiny ImageNet and ImageNet-1K. Meanwhile, we procure the bias-free intermediate features from ConvNet with widths of 256 and 512 to respectively assess datasets with  $32 \times 32$  and  $64 \times 64$  resolution. For VGG11, we conduct the splitting at its fifth/sixth

Image /page/5/Figure/8 description: This image contains two bar charts side-by-side, both plotting Accuracy (%) on the y-axis against numerical values on the x-axis. The left chart is titled "\lambda\_{front}" and shows bars with heights corresponding to accuracies of 36.9, 38.3, 38.4, 38.5, and 38.5 for x-axis values 0.05, 0.1, 0.5, 1, and 2, respectively. The right chart is titled "\lambda\_{rear}" and shows bars with heights corresponding to accuracies of 37.3, 37.6, 38.0, 38.5, and 38.6 for x-axis values 0.05, 0.1, 0.5, 1, and 2, respectively. A dashed horizontal line labeled "Baseline" is present at approximately 26.5% accuracy in both charts. A legend at the top indicates that the purple bars represent "Test Acc. of ELF."

Figure 3: Impacts of different  $\lambda_{front}$  and  $\lambda_{rear}$  on the test accuracy. CIFAR-100 10 IPC evaluated on ResNet18-IN.

layer and extract the bias-free intermediate features from ConvNet with a width of 512 for  $32\times32/64\times64$  resolution datasets, respectively. Besides, we choose the crossentropy loss for the distance function in Eq. (6).  $\lambda_{front}$  and  $\lambda_{rear}$  in Eq. (9) are uniformly set to 1. We run each experiment five times and report the mean and standard deviation of the results. All experiments are implemented using Py-Torch (Paszke et al. 2019) and executed on four NVIDIA 3090 GPUs.

### Quantitative Comparison

CIFAR-10/100. Table 5 and Table 6 respectively display the cross-architecture performance of DD methods with or without the aid of ELF on CIFAR-10 and CIFAR-100 datasets. As it can be inferred, all DD methods yield poor crossarchitecture performance due to the presence of inductive bias. Excitingly, by utilizing features from intermediate layers of the distillation model, ELF consistently improves the cross-architecture performance of DD methods by a large margin. For instance, ELF leads to a substantial 12.19% enhancement in the accuracy of MTT when evaluating VGG11-IN with 10 images distilled from CIFAR-10. Moreover, the implementation of ELF greatly enhances the cross-architecture performance of DM, exhibiting results commensurate with that of ConvNet-IN despite its lack of inductive bias. These outcomes effectively corroborate our standpoint on boosting the cross-architecture generalization of DD method from the perspective of eliminating inductive bias.

Tiny-ImageNet/ImageNet-1K. We further investigate

|                 | $\mathcal{L}_{task}$ | $\mathcal{L}_{front}$ | $\mathcal{L}_{rear}$ | 1                | 10               | 50               |
|-----------------|----------------------|-----------------------|----------------------|------------------|------------------|------------------|
| <b>Baseline</b> | $\checkmark$         | -                     | -                    | $12.43 \pm 0.99$ | $26.39 \pm 0.66$ | $39.67 \pm 0.61$ |
| (a)             | $\checkmark$         | -                     | $\checkmark$         | $14.05 \pm 0.37$ | $27.77 \pm 0.96$ | $41.98 \pm 0.19$ |
| (b)             | $\checkmark$         | $\checkmark$          | -                    | $21.47 \pm 0.28$ | $37.37 \pm 0.15$ | $47.80 \pm 0.19$ |
| (c)             | -                    | $\checkmark$          | $\checkmark$         | $16.05 \pm 0.59$ | $17.32 \pm 2.54$ | $23.24 \pm 3.07$ |
| ELF             | $\checkmark$         | $\checkmark$          | $\checkmark$         | $21.73 \pm 0.30$ | $38.48 \pm 0.14$ | $48.45 \pm 0.22$ |

Table 9: Results of ablation study on loss terms in ELF:  $\mathcal{L}_{task}, \mathcal{L}_{front}$ , and  $\mathcal{L}_{rear}$ . (CIFAR-100, ResNet18-IN)

Image /page/6/Figure/2 description: The image displays five bar charts, each representing the test accuracy of different models (ConvNet-BN, ResNet-IN, ResNet-BN, VGG-IN, and VGG-BN) across various dataset sizes (25, 50, 100, 150, and 200). A dashed line labeled "Baseline" is present in each chart, indicating a reference accuracy. The y-axis for all charts is labeled "Accuracy (%)" and ranges from 30.0 to 42.0. The specific accuracies for each model and dataset size are as follows: ConvNet-BN shows accuracies of 38.7, 38.8, 39.3, 39.3, and 39.2 for dataset sizes 25, 50, 100, 150, and 200, respectively. ResNet-IN shows accuracies of 38.2, 38.4, 38.5, 38.8, and 38.8. ResNet-BN shows accuracies of 37.7, 38.0, 38.8, 38.9, and 38.9. VGG-IN shows accuracies of 38.4, 38.6, 38.2, 38.6, and 38.8. VGG-BN shows accuracies of 36.9, 38.6, 38.8, 39.1, and 39.1. The baseline accuracy appears to be around 31.8% across all charts.

Figure 4: Ablation studies on feature epoch in ELF. The horizontal coordinate denotes the number of epochs learned by the distillation model that generated the required features.

the efficacy of ELF on datasets with larger scale, *i.e.*, Tiny-ImageNet and ImageNet-1K. The results are listed in Table 7. It is apparent that ELF continues to exhibit remarkable superiority when confronted with larger datasets. With respect to Tiny-ImageNet, ELF consistently improves the performance of MTT with accuracies ranging from 4.21% to 9.71% for the cross-architecture evaluation of multiple networks. The same conclusion can also be drawn when it comes to ImageNet-1K using TESLA (Cui et al. 2023) as the baseline method. TESLA assigns soft labels on DD to distill generalizable information to synthetic datasets. The results show that ELF can be effectively combined with other techniques to jointly enhance cross-architecture performance. In summary, ELF demonstrates its great capability to boost the cross-architecture performance of DD on the large scale datasets.

### Performance Analysis

Distillation feature. To demonstrate the effectiveness of the distillation feature in mitigating the inductive bias, we replace the features used in Eq. (9) with features extracted from trained evaluation model on the full dataset. In detail, we set ConvNetW512 as the distillation model and extract features from either ResNet18 or ConvNetW512 trained on the full dataset to evaluate the performances of ConvNetW512 and ResNet18 respectively. The baseline method for performance analysis is MTT (Cazenavette et al. 2022) if not specified. Surprisingly, Table 8 shows that utilizing the distillation model's features yields significantly superior results compared with utilizing the evaluation model's features. This strongly proves the importance of eliminating the inductive bias with bias-free features in the crossarchitecture evaluation of DD.

Loss terms. Next, we perform ablation studies to investi-

gate the efficacy of each loss term in Eq. (9). Table 9 shows that all loss terms of ELF play a unique role in mitigating the influence of the inductive bias. Further, Fig. 3 investigates the influence of hyperparameters  $\lambda_{front}$  and  $\lambda_{rear}$  corresponding to  $\mathcal{L}_{front}$  and  $\mathcal{L}_{rear}$  in Eq. 9. As can be seen, the performance of ELF is also robust to the hyper-parameter setting of loss.

Features from different epoch. At last, we investigate the number of epochs necessary for distillation model features. The results are evaluated on the CIFAR-100 dataset 10 IPC with different evaluation models. As depicted in Fig. 4, ELF's performance is relatively insensitive to the choice of epoch for the extracted features. This allows us to obtain the required features with significantly fewer resources compared to the process of DD itself.

## Limitation

Although ELF has been shown to effectively enhance the cross-architecture performance of DD, it still presents some unexplored limitations. Firstly, the utilization of features from the distillation model is subject to some degree of shape restriction, which limits its applicability in some extreme scenarios. For instance, in cases where the evaluation models require very large feature maps, such as  $32\times32$ , obtaining features of the necessary size may not be feasible from the distillation model trained on the small-scale CIFAR-10/100 datasets. Furthermore, ELF introduces some additional storage burdens for the distillation features. While this does not align with our current goal of enhancing crossarchitecture performance, it would be worthwhile to explore ways to reduce this extra storage burden in future work.

# **Conclusion**

This paper proposes ELF, a novel method for enhancing the cross-architecture generalization of dataset distillation (DD). ELF utilizes the features extracted from the distillation model to steer the training of the evaluation model on the synthetic dataset generated by DD. Extensive experiments on multiple datasets demonstrate that ELF can effectively enhance the cross-architecture performance of existing DD methods. Unlike previous works, this paper provides a specific focus on enhancing the cross-architecture performance of DD methods, while also undertaking a comprehensive analysis and experimentation on their cross-architecture generalization. We believe that this work could inspire future research enthusiasm on more exploration toward the practical applications of DD.

## References

Bohdal, O.; Yang, Y.; and Hospedales, T. 2020. Flexible Dataset Distillation: Learn Labels Instead of Images. In *Neural Information Processing Systems Workshop (NeurIPSW)*.

Bradbury, J.; Frostig, R.; Hawkins, P.; Johnson, M. J.; Leary, C.; Maclaurin, D.; Necula, G.; Paszke, A.; VanderPlas, J.; Wanderman-Milne, S.; and Zhang, Q. 2018. JAX: composable transformations of Python+NumPy programs.

Cazenavette, G.; Wang, T.; Torralba, A.; Efros, A. A.; and Zhu, J.-Y. 2022. Dataset distillation by matching training trajectories. In *Computer Vision and Pattern Recognition (CVPR)*.

Chen, C.; Zhang, Y.; Fu, J.; Liu, X.; and Coates, M. 2022. Bidirectional Learning for Offline Infinite-width Modelbased Optimization. In *Neural Information Processing Systems (NeurIPS)*.

Chen, D.; Kerkouche, R.; and Fritz, M. 2022. Private Set Generation with Discriminative Information. In *Neural Information Processing Systems (NeurIPS)*.

Chen, L.-C.; Papandreou, G.; Kokkinos, I.; Murphy, K.; and Yuille, A. L. 2017. Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*.

Clancey, W. J. 1979. *Transfer of Rule-Based Expertise through a Tutorial Dialogue*. Ph.D. diss., Dept. of Computer Science, Stanford Univ., Stanford, Calif.

Clancey, W. J. 1983. Communication, Simulation, and Intelligent Agents: Implications of Personal Intelligent Machines for Medical Education. In *Proceedings of the Eighth International Joint Conference on Artificial Intelligence (IJCAI-83)*, 556–560. Menlo Park, Calif: IJCAI Organization.

Clancey, W. J. 1984. Classification Problem Solving. In *Proceedings of the Fourth National Conference on Artificial Intelligence*, 45–54. Menlo Park, Calif.: AAAI Press.

Clancey, W. J. 2021. The Engineering of Qualitative Models. Forthcoming.

Cui, J.; Wang, R.; Si, S.; and Hsieh, C.-J. 2022. DC-BENCH: Dataset Condensation Benchmark. In *Neural Information Processing Systems (NeurIPS)*.

Cui, J.; Wang, R.; Si, S.; and Hsieh, C.-J. 2023. Scaling Up Dataset Distillation to ImageNet-1K with Constant Memory. In *International Conference on Machine Learning (ICML)*.

Deng, J.; Dong, W.; Socher, R.; Li, L.-J.; Li, K.; and Fei-Fei, L. 2009. Imagenet: A large-scale hierarchical image database. In *Computer Vision and Pattern Recognition (CVPR)*.

Deng, Z.; and Russakovsky, O. 2022. Remember the Past: Distilling Datasets into Addressable Memories for Neural Networks. In *Neural Information Processing Systems (NeurIPS)*.

Dong, T.; Zhao, B.; and Liu, L. 2022. Privacy for Free: How does Dataset Condensation Help Privacy? In *International Conference on Machine Learning (ICML)*.

Du, J.; Jiang, Y.; Tan, V. T. F.; Zhou, J. T.; and Li, H. 2023. Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation. In *Conference on Computer Vision and Pattern Recognition (CVPR)*.

Engelmore, R.; and Morgan, A., eds. 1986. *Blackboard Systems*. Reading, Mass.: Addison-Wesley.

Finn, C.; Abbeel, P.; and Levine, S. 2017. Model-agnostic meta-learning for fast adaptation of deep networks. In *International Conference on Machine Learning (ICML)*.

Gidaris, S.; and Komodakis, N. 2018. Dynamic Few-Shot Visual Learning without Forgetting Spyros Gidaris. In *Computer Vision and Pattern Recognition (CVPR)*.

Girshick, R.; Donahue, J.; Darrell, T.; and Malik, J. 2014. Rich feature hierarchies for accurate object detection and semantic segmentation. In *International Conference on Computer Vision (ICCV)*.

Hasling, D. W.; Clancey, W. J.; and Rennels, G. 1984. Strategic explanations for a diagnostic consultation system. *International Journal of Man-Machine Studies*, 20(1): 3–19.

Hasling, D. W.; Clancey, W. J.; Rennels, G. R.; and Test, T. 1983. Strategic Explanations in Consultation—Duplicate. *The International Journal of Man-Machine Studies*, 20(1): 3–19.

He, K.; Zhang, X.; Ren, S.; and Sun, J. 2016. Deep residual learning for image recognition. In *Computer Vision and Pattern Recognition (CVPR)*.

Ioffe, S.; and Szegedy, C. 2015. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In *International Conference on Machine Learning (ICML)*.

Kim, J.-H.; Kim, J.; Oh, S. J.; Yun, S.; Song, H.; Jeong, J.; Ha, J.-W.; and Song, H. O. 2022. Dataset Condensation via Efficient Synthetic-Data Parameterization. In *International Conference on Machine Learning (ICML)*.

Krizhevsky, A.; Hinton, G.; et al. 2009. Learning multiple layers of features from tiny images. Technical report, Citeseer.

Le, Y.; and Yang, X. 2015. Tiny imagenet visual recognition challenge. *CS 231N*.

Lee, H. B.; Lee, D. B.; and Hwang, S. J. 2022. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*.

Lee, J.; Xiao, L.; Schoenholz, S.; Bahri, Y.; Novak, R.; Sohl-Dickstein, J.; and Pennington, J. 2019. Wide neural networks of any depth evolve as linear models under gradient descent. In *Neural Information Processing Systems (NeurIPS)*.

Lee, S.; Chun, S.; Jung, S.; Yun, S.; and Yoon, S. 2022. Dataset condensation with contrastive signals. In *International Conference on Machine Learning (ICML)*.

Lei, S.; and Tao, D. 2023. A Comprehensive Survey to Dataset Distillation. *arXiv preprint arXiv:2301.05603*.

Li, G.; Togo, R.; Ogawa, T.; and Haseyama, M. 2023. Dataset Distillation for Medical Dataset Sharing. In *AAAI Conference on Artificial Intelligence Workshop (AAAIW)*.

Lin, T.-Y.; Maire, M.; Belongie, S.; Hays, J.; Perona, P.; Ramanan, D.; Dollár, P.; and Zitnick, C. L. 2014. Microsoft coco: Common objects in context. In *European Conference on Computer Vision (ECCV)*.

Liu, S.; Wang, K.; Yang, X.; Ye, J.; and Wang, X. 2022. Dataset Distillation via Factorization. In *Neural Information Processing Systems (NeurIPS)*.

Long, J.; Shelhamer, E.; and Darrell, T. 2015. Fully convolutional networks for semantic segmentation. In *Computer Vision and Pattern Recognition (CVPR)*.

Loo, N.; Hasani, R.; Amini, A.; and Rus, D. 2022. Efficient Dataset Distillation using Random Feature Approximation. In *Neural Information Processing Systems (NeurIPS)*.

Maclaurin, D.; Duvenaud, D.; and Adams, R. 2015. Gradient-Based Hyperparameter Optimization Through Reversible Learning. In *International Conference on Machine Learning (ICML)*.

NASA. 2015. Pluto: The 'Other' Red Planet. https://www. nasa.gov/nh/pluto-the-other-red-planet. Accessed: 2018- 12-06.

Nguyen, T.; Chen, Z.; and Lee, J. 2021. Dataset Meta-Learning from Kernel Ridge-Regression. In *International Conference on Learning Representations (ICLR)*.

Nguyen, T.; Novak, R.; Xiao, L.; and Lee, J. 2021. Dataset Distillation with Infinitely Wide Convolutional Networks. In *Neural Information Processing Systems (NeurIPS)*.

Paszke, A.; Gross, S.; Massa, F.; Lerer, A.; Bradbury, J.; Chanan, G.; Killeen, T.; Lin, Z.; Gimelshein, N.; Antiga, L.; et al. 2019. Pytorch: An imperative style, high-performance deep learning library. In *Neural Information Processing Systems (NeurIPS)*.

Ren, S.; He, K.; Girshick, R.; and Sun, J. 2015. Faster r-cnn: Towards real-time object detection with region proposal networks. In *Neural Information Processing Systems (NeurIPS)*.

Riba, E.; Mishkin, D.; Ponsa, D.; Rublee, E.; and Bradski, G. 2020. Kornia: an open source differentiable computer vision library for pytorch. In *Winter Conference on Applications of Computer Vision (WACV)*.

Rice, J. 1986. Poligon: A System for Parallel Problem Solving. Technical Report KSL-86-19, Dept. of Computer Science, Stanford Univ.

Robinson, A. L. 1980a. New Ways to Make Microcircuits Smaller. *Science*, 208(4447): 1019–1022.

Robinson, A. L. 1980b. New Ways to Make Microcircuits Smaller—Duplicate Entry. *Science*, 208: 1019–1026.

Rosasco, A.; Carta, A.; Cossu, A.; Lomonaco, V.; and Bacciu, D. 2021. Distilled Replay: Overcoming Forgetting through Synthetic Samples. In *International Joint Conference on Artificial Intelligence Workshop (IJCAIW)*.

Russakovsky, O.; Deng, J.; Su, H.; Krause, J.; Satheesh, S.; Ma, S.; Huang, Z.; Karpathy, A.; Khosla, A.; Bernstein, M.; et al. 2015. Imagenet large scale visual recognition challenge. *International Journal of Computer Vision (IJCV)*.

Sachdeva, N.; and McAuley, J. 2023. Data Distillation: A Survey. *arXiv preprint arXiv:2301.04272*.

Sachdeva, N.; Preet Dhaliwal, M.; Wu, C.-J.; and McAuley, J. 2022. Infinite Recommendation Networks: A Data-Centric Approach. In *Neural Information Processing Systems (NeurIPS)*.

Sangermano, M.; Carta, A.; Cossu, A.; and Bacciu, D. 2022. Sample Condensation in Online Continual Learning. In *International Joint Conference on Neural Networks (IJCNN)*.

Simonyan, K.; and Zisserman, A. 2015. Very deep convolutional networks for large-scale image recognition. In *International Conference on Learning Representations (ICLR)*.

Sucholutsky, I.; and Schonlau, M. 2021. Soft-Label Dataset Distillation and Text Dataset Distillation. In *International Joint Conference on Neural Networks (IJCNN)*.

Szegedy, C.; Liu, W.; Jia, Y.; Sermanet, P.; Reed, S.; Anguelov, D.; Erhan, D.; Vanhoucke, V.; and Rabinovich, A. 2015. Going deeper with convolutions. In *Computer Vision and Pattern Recognition (CVPR)*.

Ulyanov, D.; Vedaldi, A.; and Lempitsky, V. 2016. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*.

Vaswani, A.; Shazeer, N.; Parmar, N.; Uszkoreit, J.; Jones, L.; Gomez, A. N.; Kaiser, L.; and Polosukhin, I. 2017. Attention Is All You Need. arXiv:1706.03762.

Wang, K.; Zhao, B.; Peng, X.; Zhu, Z.; Yang, S.; Wang, S.; Huang, G.; Bilen, H.; Wang, X.; and You, Y. 2022. CAFE: Learning to Condense Dataset by Aligning Features. In *Computer Vision and Pattern Recognition (CVPR)*.

Wang, T.; Zhu, J.-Y.; Torralba, A.; and Efros, A. A. 2018. Dataset distillation. *arXiv preprint arXiv:1811.10959*.

Werbos, P. 1990. Backpropagation through time: what it does and how to do it. *Institute of Electrical and Electronics Engineers (IEEE)*.

Wiewel, F.; and Yang, B. 2021. Condensed Composite Memory Continual Learning. In *International Joint Conference on Neural Networks (IJCNN)*.

Yu, R.; Liu, S.; and Wang, X. 2023. Dataset Distillation: A Comprehensive Review. *arXiv preprint arXiv:2301.07014*.

Zhao, B.; and Bilen, H. 2021a. Dataset condensation with Differentiable Siamese Augmentation. In *International Conference on Machine Learning (ICML)*.

Zhao, B.; and Bilen, H. 2021b. Dataset Condensation with Gradient Matching. In *International Conference on Learning Representations (ICLR)*.

Zhao, B.; and Bilen, H. 2022. Synthesizing Informative Training Samples with GAN. In *Neural Information Processing Systems Workshop (NeurIPSW)*.

Zhao, B.; and Bilen, H. 2023. Dataset Condensation with Distribution Matching. In *Winter Conference on Applications of Computer Vision (WACV)*.

Zhou, Y.; Nezhadarya, E.; and Ba, J. 2022. Dataset Distillation using Neural Feature Regression. In *Neural Information Processing Systems (NeurIPS)*.

## Experimental Details

We provide more details regarding our implementation of DD methods. We acquire the synthetic dataset and distillation feature by utilizing the officially released code of each respective method  $1 \times 2 \times 3$ . For a fair comparison, we evaluate the performance of all synthetic datasets using 1000 training epochs with a learning rate of 0.01. In what follows, we present more experimental details for each table in the main manuscript.

Table 1-4. We generate the synthetic dataset for each method using their default settings. For MTT (Cazenavette et al. 2022), we do not employ ZCA whitening in order to maintain consistency of different DD methods (Zhao and Bilen 2021a, 2023). Moreover, we notice that using ZCA whitening even reduces the cross-architecture performance of MTT in small IPC.

Table 5-7. The cross-architecture performance comparison between ELF and the corresponding baseline method is based on evaluations using the same synthetic dataset. Furthermore, Figure 4 highlights that the performance of ELF remains relatively stable across epochs for feature extraction. Hence we only select the coarse-grained epoch of features in different evaluation settings. The epochs used to obtain the features are listed in Table 10. For DM and DSA in Table 5, we set the learning rate to 0.005 instead of 0.01 because of significant loss fluctuation at large learning rates in our experimental observation.

| 10 IPC                  | DM | DSA | MTT | TESLA |
|-------------------------|----|-----|-----|-------|
| Table 5 (CIFAR-10)      | 30 | 30  | 50  | -     |
| Table 6 (CIFAR-100)     | 50 | 50  | 100 | -     |
| Table 7 (Tiny-ImageNet) | -  | -   | 100 | -     |
| Table 7 (ImageNet-1K)   | -  | -   | -   | 100   |

Table 10: Epochs used to get the features from the distillation model in ELF.

Table 8. In Table 8 of the main paper, we employ ZCA whitening. We also present experimental results under the same settings but without ZCA whitening in Table 11. As can be seen, the trend remains consistent that the results obtained using the distillation model's feature exceed those obtained using the evaluation model's feature. Overall, the significance of removing the inductive bias using bias-free features in the cross-architecture evaluation of DD is apparent.

## More Performance Analysis

Loss function. We further study the effect of the selection of distance function  $D(\cdot, \cdot)$  in Eq. 6. Table 12 shows that the cross-architecture performance when using the crossentropy (CE) loss as  $D(\cdot, \cdot)$  is significantly better than when using other loss functions, including mean-absolute-error

| IPC.               |                  | 10               |
|--------------------|------------------|------------------|
| Baseline (ConvNet) | $18.13 \pm 0.47$ | $38.49 \pm 0.35$ |
| w. ResNet Feature  | $15.36 \pm 0.63$ | $32.81 \pm 0.31$ |
| w. ConvNet Feature | $26.84 \pm 0.57$ | $46.29 \pm 0.34$ |
| Baseline (ResNet)  | $8.49 \pm 0.60$  | $24.40 \pm 0.31$ |
| w. ResNet Feature  | $15.04 \pm 0.18$ | $30.60 \pm 0.21$ |
| w. ConvNet Feature | $18.87 \pm 0.16$ | $36.31 + 0.25$   |

Table 11: Performances of using different features on different baseline networks. CIFAR-100 1/10 IPC, not using ZCA preprocessing during distillation.

(MAE), mean-square-error (MSE), and cosine-similarity (Cos).

<sup>1</sup> https://github.com/VICO-UoE/DatasetCondensation

<sup>&</sup>lt;sup>2</sup>https://github.com/GeorgeCazenavette/mtt-distillation

<sup>3</sup> https://openreview.net/forum?id=dN70O8pmW8

|            |                 | ConvNet-BN       | ResNet-IN        | ResNet-BN        | VGG-IN           | VGG-BN           |
|------------|-----------------|------------------|------------------|------------------|------------------|------------------|
|            | <b>Baseline</b> | $27.56 \pm 0.18$ | $21.96 \pm 0.51$ | $20.45 \pm 0.53$ | $22.00 \pm 0.34$ | $25.73 \pm 0.41$ |
| <b>DSA</b> | <b>MAE</b>      | $34.32 \pm 0.24$ | $22.47 \pm 0.75$ | $25.57 \pm 0.60$ | $24.73 \pm 0.38$ | $28.37 + 0.37$   |
|            | <b>MSE</b>      | $35.42 \pm 0.22$ | $21.89 \pm 1.21$ | $25.62 \pm 0.57$ | $25.03 \pm 0.26$ | $28.65 \pm 0.37$ |
|            | $\cos$          | $34.57 + 0.23$   | $24.07 + 0.11$   | $27.31 + 0.68$   | $25.21 \pm 0.32$ | $30.05 \pm 0.37$ |
|            | CE.             | $36.02 + 0.35$   | $27.54 + 0.19$   | $30.26 + 0.65$   | $28.74 + 0.11$   | $28.54 + 1.23$   |
|            | <b>Baseline</b> | $31.73 + 0.15$   | $26.39 + 0.66$   | $27.21 \pm 0.53$ | $27.50 + 0.26$   | $31.71 + 0.58$   |
| <b>MTT</b> | <b>MAE</b>      | $39.45 \pm 0.30$ | $28.01 + 0.76$   | $33.10 \pm 0.55$ | $33.04 \pm 0.41$ | $33.80 \pm 0.86$ |
|            | <b>MSE</b>      | $40.07 + 0.49$   | $26.98 + 2.00$   | $33.43 + 0.56$   | $33.41 + 0.53$   | $34.59 + 0.75$   |
|            | Cos             | $39.63 \pm 0.34$ | $31.10 \pm 0.53$ | $35.53 \pm 0.47$ | $33.89 + 0.32$   | $36.49 + 0.39$   |
|            | CE.             | $39.32 + 0.24$   | $38.48 \pm 0.14$ | $38.76 \pm 0.80$ | $38.20 \pm 0.49$ | $38.78 \pm 0.84$ |

Table 12: Results of ablation study on distance function in Eq. (6). CIFAR-100 10 IPC in DSA (Zhao and Bilen 2021a) and MTT (Cazenavette et al. 2022). *MAE* denotes mean-absolute-error distance function, *MSE* denotes mean-square-error distance function, *Cos* denotes cosine-similarity distance function, and *CE* denotes cross-entropy distance function. The Crossarchitecture performance using CE loss is much better than using others.