{"table_of_contents": [{"title": "BREAKING CLASS BARRIERS:\nEFFICIENT DATASET DISTILLATION\nVIA INTER-CLASS FEATURE COMPENSATOR", "heading_level": null, "page_id": 0, "polygon": [[106.2333984375, 81.0], [430.5, 81.0], [430.5, 136.318359375], [106.2333984375, 136.318359375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 243.0], [334.5, 243.0], [334.5, 254.07421875], [276.75, 254.07421875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 555.75], [206.25, 555.75], [206.25, 566.54296875], [107.25, 566.54296875]]}, {"title": "2 PRELIMIN<PERSON>IE<PERSON> AND REL<PERSON>ED WORKS", "heading_level": null, "page_id": 2, "polygon": [[106.90576171875, 459.75], [327.75, 459.75], [327.75, 471.0234375], [106.90576171875, 471.0234375]]}, {"title": "3 METHODOLOGY", "heading_level": null, "page_id": 3, "polygon": [[107.25, 401.80078125], [210.97265625, 401.80078125], [210.97265625, 413.40234375], [107.25, 413.40234375]]}, {"title": "3.1 LIMITATIONS IN CLASS-SPECIFIC DISTILLATION", "heading_level": null, "page_id": 3, "polygon": [[106.5, 514.72265625], [339.169921875, 514.72265625], [339.169921875, 524.77734375], [106.5, 524.77734375]]}, {"title": "3.2 UNIVERSAL FEATURE COMPENSATOR: BREAKING CLASS BARRIERS", "heading_level": null, "page_id": 4, "polygon": [[107.25, 322.5], [423.0, 322.5], [423.0, 332.771484375], [107.25, 332.771484375]]}, {"title": "3.3 ENHANCING SYNTHETIC DATA WITH INTER-CLASS AUGMENTATION", "heading_level": null, "page_id": 5, "polygon": [[106.5, 397.5], [423.0, 397.5], [423.0, 407.98828125], [106.5, 407.98828125]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 6, "polygon": [[107.25, 515.25], [202.1572265625, 515.25], [202.1572265625, 526.32421875], [107.25, 526.32421875]]}, {"title": "4.1 EXPERIMENTAL SETUP", "heading_level": null, "page_id": 6, "polygon": [[106.5, 618.75], [231.890625, 618.75], [231.890625, 629.19140625], [106.5, 629.19140625]]}, {"title": "4.2 MAIN RESULTS", "heading_level": null, "page_id": 7, "polygon": [[107.25, 551.84765625], [199.6171875, 551.84765625], [199.6171875, 561.90234375], [107.25, 561.90234375]]}, {"title": "4.3 ABLATION STUDY", "heading_level": null, "page_id": 8, "polygon": [[106.5, 474.0], [210.0, 474.0], [210.0, 484.171875], [106.5, 484.171875]]}, {"title": "4.4 MORE DISCUSSIONS WITH SOTA METHOD SRE2L", "heading_level": null, "page_id": 9, "polygon": [[107.25, 361.775390625], [349.03125, 361.775390625], [349.03125, 371.830078125], [107.25, 371.830078125]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 608.6953125], [196.1806640625, 608.6953125], [196.1806640625, 619.5234375], [107.25, 619.5234375]]}, {"title": "ACKNOWLEDGEMENT", "heading_level": null, "page_id": 10, "polygon": [[107.05517578125, 82.5], [220.236328125, 82.5], [220.236328125, 93.44091796875], [107.05517578125, 93.44091796875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.20458984375, 135.75], [175.5, 135.75], [175.5, 146.1796875], [107.20458984375, 146.1796875]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 13, "polygon": [[107.05517578125, 82.4677734375], [183.0, 82.4677734375], [183.0, 93.779296875], [107.05517578125, 93.779296875]]}, {"title": "A.1 TRAINING WITH SYNTHETIC DATASET", "heading_level": null, "page_id": 13, "polygon": [[107.25, 108.75], [298.0810546875, 108.75], [298.0810546875, 119.302734375], [107.25, 119.302734375]]}, {"title": "Algorithm 2 Training with synthetic dataset via Inter-class Feature Compensator (INFER)", "heading_level": null, "page_id": 13, "polygon": [[106.681640625, 137.5751953125], [472.1484375, 137.5751953125], [472.1484375, 147.75], [106.681640625, 147.75]]}, {"title": "A.2 MORE RELATED WORKS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 513.94921875], [240.85546875, 513.94921875], [240.85546875, 524.00390625], [107.25, 524.00390625]]}, {"title": "A.3 TRAINING RECIPES", "heading_level": null, "page_id": 14, "polygon": [[107.25, 602.25], [218.443359375, 602.25], [218.443359375, 611.7890625], [107.25, 611.7890625]]}, {"title": "A.4 VISUALIZATION OF GENERATED COMPENSATOR", "heading_level": null, "page_id": 14, "polygon": [[107.05517578125, 674.25], [340.365234375, 674.25], [340.365234375, 684.4921875], [107.05517578125, 684.4921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 51], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4260, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 92], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 735, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["Line", 63], ["Text", 4], ["Reference", 4], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 53], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 669], ["Line", 94], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 702], ["Line", 59], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1057], ["Line", 221], ["TableCell", 149], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["TableCell", 153], ["Line", 133], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 100], ["TableCell", 63], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 875, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 108], ["Text", 5], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 45], ["ListItem", 18], ["Reference", 18], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 46], ["ListItem", 21], ["Reference", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 45], ["ListItem", 21], ["Reference", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 801], ["Line", 54], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["TableCell", 148], ["Line", 107], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13042, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 20], ["Text", 5], ["Reference", 4], ["Picture", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1269, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["TableCell", 131], ["Line", 94], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 15582, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Breaking_Class_Barriers__Efficient_Dataset_Distillation_via_Inter-Class_Feature_Compensator"}