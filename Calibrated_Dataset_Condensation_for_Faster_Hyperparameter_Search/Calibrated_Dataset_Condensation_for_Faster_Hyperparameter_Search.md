# <span id="page-0-0"></span>Calibrated Dataset Condensation for Faster Hyperparameter Search

Mucong Ding\*
<PERSON>

Teresa Ranadive

Mucong <PERSON>g<sup>∗</sup> <PERSON><PERSON>† <PERSON><PERSON><PERSON>† <PERSON><PERSON>† <PERSON>‡ <PERSON>‡ <PERSON><PERSON><PERSON>‡ <PERSON><PERSON>†

<PERSON><PERSON>

#### Abstract

Dataset condensation can be used to reduce the computational cost of training multiple models on a large dataset by condensing the training dataset into a small synthetic set. State-of-the-art approaches rely on matching model gradients between the real and synthetic data. However, there is no theoretical guarantee on the generalizability of the condensed data: data condensation often generalizes poorly across hyperparameters/architectures in practice. In this paper, we consider a different condensation objective specifically geared toward hyperparameter search. We aim to generate a synthetic validation dataset so that the validation-performance rankings of models, with different hyperparameters, on the condensed and original datasets are comparable. We propose a novel *hyperparameter-calibrated dataset condensation* (HCDC) algorithm, which obtains the synthetic validation dataset by matching the hyperparameter gradients computed via implicit differentiation and efficient inverse Hessian approximation. Experiments demonstrate that the proposed framework effectively maintains the validation-performance rankings of models and speeds up hyperparameter/architecture search for tasks on both images and graphs.

# 1 Introduction

Deep learning has achieved great success in various fields, such as computer vision and graph related tasks. However, the computational cost of training state-of-the-art neural networks is rapidly increasing due to growing model and dataset sizes. Moreover, designing deep learning models usually requires training numerous models on the same data to obtain the optimal hyperparameters and architecture [\(Elsken et al.,](#page-32-0) [2019\)](#page-32-0), posing significant computational challenges. Thus, reducing the computational cost of repeatedly training on the same dataset is crucial. We address this problem from a data-efficiency perspective and consider the following question: how can one reduce the training data size for faster hyperparameter search/optimization with minimal performance loss?

Recently, *dataset distillation/condensation* [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0) is proposed as an effective way to reduce sample size. This approach involves producing a small *synthetic* dataset to replace the original larger one, so that the test performance of the model trained on the synthetic set is comparable to that trained on the original. Despite the state-of-the-art performance achieved by recent dataset condensation methods when used to train a single pre-specified model, it remains challenging to

<sup>∗</sup>Department of Computer Science, University of Maryland, College Park; e-mail: <EMAIL>

<sup>†</sup>Department of Computer Science, University of Maryland, College Park

<sup>‡</sup>Laboratory for Physical Sciences, University of Maryland

utilize such methods effectively for hyperparameter search. Current dataset condensation methods perform poorly when applied to neural architecture search (NAS) [\(Elsken et al.,](#page-32-0) [2019\)](#page-32-0) and when used to train deep networks beyond the pre-specified architecture [\(Cui et al.,](#page-31-0) [2022\)](#page-31-0). Moreover, there is little or even a negative correlation between the performance of models trained on the synthetic vs. the full dataset, across architectures: often, one architecture achieves higher validation accuracy when trained on the original data relative to a second architecture, but obtains lower validation accuracy than the second when trained on the synthetic data. Since architecture performance ranking is not preserved when the original data is condensed, current data condensation methods are inadequate for NAS. This issue stems from the fact that existing condensation methods are designed on top of a single pre-specified model, and thus the condensed data may overfit this model.

We ask: is it possible to preserve the architecture/hyperparameter search outcome when the original data is replaced by the condensed data?

<span id="page-1-0"></span>Image /page/1/Figure/2 description: This figure illustrates the HCDC algorithm flow. It begins with an original training set (T^train) and a synthetic training set (S^train), both used for training. The process involves generating multiple models indexed by hyperparameters (lambda). These models are evaluated against an original validation set (T^val) and a synthetic validation set (S^val). The loss function, which involves the sum over lambda of D(nabla\_lambda L\*\_T(lambda), nabla\_lambda L\*\_S(lambda)), is calculated. The algorithm updates the synthetic validation set based on this loss. The dashed red arrows indicate the flow of hypergradients (nabla\_lambda L\*\_T(lambda) and nabla\_lambda L\*\_S(lambda)) via Equation (IFT).

Goal: Comparable validation-performance rankings in Eq.(HC) ⇔ Loss: Hypergradient matching loss in Eq.(HCDC)

Figure 1: Hyperparameter Calibrated Dataset Condensation (HCDC) aims to find a small validation dataset such that the validation-performance rankings of the models with different hyperparameters are comparable to the large original dataset's. Our method realizes this goal (Eq. [\(HC\)](#page-4-0)) by learning the synthetic validation set to match the hypergradients w.r.t the hyperparameters (Eq. [\(HCDC\)](#page-4-0) in the "Loss" box). Our contribution is depicted within the big black dashed box: the algorithm flow is indicated through the red dashed arrows. The synthetic training set is predetermined by any standard dataset condensation (SDC) methods (e.g., Eq. [\(SDC\)](#page-3-0)). The synthetic training and validation datasets obtained can later be used for hyperparameter search using only a fraction of the original computational load. A more detailed diagram is depicted in Fig. [5](#page-13-0) in Appendix [A.](#page-13-1)

To answer this question, we reformulate the dataset condensation problem using a hyperparameter optimization (HPO) framework [\(Feurer and Hutter,](#page-32-1) [2019\)](#page-32-1), with the goal of preserving architecture/hyperparameter search outcomes over multiple architectures/hyperparameters, just as standard dataset condensation preserves generalization performance results for a single pre-specified architecture. This is illustrated in Fig. [1'](#page-1-0)s "Goal" box. However, solving the resulting nested optimization problem is tremendously difficult. Therefore, we consider an alternative objective and show that architecture performance ranking preservation is equivalent to aligning the *hyperparameter* gradients (or hypergradients for short), of this objective, in the context of dataset condensation. This is illustrated as the "Loss" box in Fig. [1.](#page-1-0) Thus, we propose hyperparameter calibrated dataset condensation (HCDC), a novel condensation method that preserves hyperparameter performance rankings by aligning the hypergradients computed using the condensed data to those computed using the original dataset, see Fig. [1.](#page-1-0)

Our implementation of HCDC is efficient and scales linearly with respect to the size of hyperparameter search space. Moreover, hypergradients are efficiently computed with constant memory overhead, using the implicit function theorem (IFT) and the Neumann series approximation of an inverse Hessian [\(Lorraine et al.,](#page-33-0) [2020\)](#page-33-0). We also specifically consider how to apply HCDC to the practical architecture search spaces for image and graph datasets.

Experiments demonstrate that our proposed HCDC algorithm drastically increases the correlation between the architecture rankings of models trained on the condensed dataset and those trained on the original dataset, for both image and graph data. Additionally, the test performance of the highest ranked architecture determined by the condensed dataset is comparable to that of the true optimal architecture determined by the original dataset. Thus, HCDC can enable faster hyperparameter search and obtain high performance accuracy by choosing the highest ranked hyperparameters, while the other condensation and coreset methods cannot. We also demonstrate that condensed datasets obtained with HCDC are compatible with off-the-shelf architecture search algorithms with or without parameter sharing.

We summarize our contributions as follows: (1) We study the data condensation problem for hyperparameter search and show that performance ranking preservation is equivalent to hypergradient alignment in this context. (2) We propose HCDC, which synthesizes condensed data by aligning the hypergradients of the objectives associated with the condensed and original datasets for faster hyperparameter search. (3) We present experiments, for which HCDC drastically reduces the search time and complexity of off-the-shelf NAS algorithms, for both image and graph data, while preserving the search outcome with high accuracy.

<span id="page-2-0"></span>

### 2 Standard Dataset Condensation

Consider a classification problem where the original dataset  $\mathcal{T}^{\text{train}} = \{(x_i, y_i)\}_{i=1}^n$  consists of n (input, label) pairs sampled from the original data distribution  $P_{\mathcal{D}}$ . To simplify notation, we replace  $\mathcal{T}^{\text{train}}$  with  $\mathcal T$  when the context is clear. The classification task goal is to train a function  $f_\theta$  (e.g., a deep neural network), with parameter  $\theta$ , to correctly predict labels y from inputs x. Obtaining  $f_{\theta}$ involves optimizing an empirical loss objective determined by  $\mathcal{T}^{\text{train}}$ :

$$
\theta^{\mathcal{T}} = \arg\min_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda), \text{ where } \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda) \coloneqq \frac{1}{|\mathcal{T}^{\text{train}}|} \sum_{(x, y) \in \mathcal{T}^{\text{train}}} l(f_{\theta}(x), y, \lambda), \tag{1}
$$

where  $\lambda$  denotes the model hyperparameter (e.g., the neural network architecture that characterizes  $f_{\theta}$ ), and  $l(\cdot, \cdot, \cdot)$  is a task-specific loss function that depends on  $\lambda$ .

Dataset condensation involves generating a small set of  $c \ll n$  synthesized samples  $\mathcal{S} = \{x'_i, y'_i\}_{i=1}^c$ , with which to replace the original training dataset  $\mathcal T$ . Using the condensed dataset  $\mathcal S$ , one can obtain  $f_{\theta}$  with parameter  $\theta = \theta^{\mathcal{S}} = \arg \min_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta, \lambda)$ , where  $\mathcal{L}_{\mathcal{S}}^{\text{train}} = \frac{1}{|\mathcal{S}|}$  $\frac{1}{|S|}\sum_{(x,y)\in\mathcal{S}}l(f_{\theta}(x), y, \lambda).$ The goal is for the generalization performance of the model  $f_{\theta S}$  obtained using the condensed data to approximate that of  $f_{\theta^{\mathcal{T}}},$  i.e.,  $\mathbb{E}_{(x,y)\sim P_{\mathcal{D}}}[l(f_{\theta^{\mathcal{T}}}(x), y, \lambda)] \approx \mathbb{E}_{(x,y)\sim P_{\mathcal{D}}}[l(f_{\theta^{\mathcal{S}}}(x), y, \lambda)].$ 

Next, we review the bi-level optimization formulation of the *standard dataset condensation* (SDC) [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0) and one of its efficient solutions using gradient matching [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0).

**SDC**'s objective. By posing the optimal parameters  $\theta^{\mathcal{S}}(\mathcal{S})$  as a function of the condensed dataset  $S$ , SDC can be formulated as a bi-level optimization problem as follows,

<span id="page-3-0"></span>
$$
S^* = \arg\min_{S} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta^{\mathcal{S}}(\mathcal{S}), \lambda), \text{ s.t. } \theta^{\mathcal{S}}(\mathcal{S}) \coloneqq \arg\min_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta, \lambda). \tag{SDC}
$$

In other words, the optimization problem in Eq. [\(SDC\)](#page-3-0) aims to find the optimal synthetic dataset  $S$ such that the model  $\theta^{\mathcal{S}}(\mathcal{S})$  trained on it minimizes the training loss over the original data  $\mathcal{T}^{\text{train}}$ . However, directly solving the optimization problem in Eq. [\(SDC\)](#page-3-0) is difficult since it involves a nested-loop optimization and solving the inner loop for  $\theta^{\mathcal{S}}(\mathcal{S})$  at each iteration requires unrolling the recursive computation graph for S over multiple optimization steps for  $\theta$  [\(Domke,](#page-32-2) [2012\)](#page-32-2), which is computationally expensive.

SDC in a gradient matching formulation. [Zhao et al.](#page-36-0) [\(2020\)](#page-36-0) alleviate this computational issue by introducing a gradient matching (GM) formulation. Firstly, they formulate the condensation objective as not only achieves comparable generalization performance to  $\theta^{\mathcal{T}}$  but also converges to a similar solution in the parameter space, i.e.,  $\theta^{\mathcal{S}}(\mathcal{S}, \theta_0) \approx \theta_{\mathcal{T}}(\theta_0)$ , where  $\theta_0$  indicates the initialization. The resulting formulation is still a bilevel optimization but can be simplified via several approximations.

(1)  $\theta^S(\mathcal{S}, \theta_0)$  is approximated by the output of a series of gradient-descent updates,  $\theta^S(\mathcal{S}, \theta_0) \approx$  $\theta_{t+1}^{\mathcal{S}} \leftarrow \theta_t^{\mathcal{S}} - \eta \nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta_t^{\mathcal{S}}, \lambda)$ . In addition, [Zhao et al.](#page-36-0) [\(2020\)](#page-36-0) propose to match  $\theta_{t+1}^{\mathcal{S}}$  with incompletely optimized  $\theta_{t+1}^{\mathcal{T}}$  at each iteration t. Consequently, the dataset condensation objective is now  $\mathcal{S}^*$  $\arg \min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim P_{\theta_0}}[\sum_{t=0}^{T-1} D(\theta_t^{\mathcal{S}}, \theta_t^{\mathcal{T}})].$ 

(2) If we assume  $\theta_t^{\mathcal{S}}$  can always track  $\theta_t^{\mathcal{T}}$  (i.e.,  $\theta_t^{\mathcal{S}} \approx \theta_t^{\mathcal{T}}$ ) from the initialization  $\theta_0$  up to iteration t, then we can replace  $D(\theta_{t+1}^{\mathcal{S}}, \theta_{t+1}^{\mathcal{T}})$  by  $D(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta_t^{\mathcal{S}}, \lambda), \nabla_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta_t^{\mathcal{T}}, \lambda)).$  The final objective for the GM formulation is,

$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \Big[ \sum_{t=0}^{T-1} D\Big(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta_t^{\mathcal{S}}, \lambda), \nabla_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta_t^{\mathcal{S}}, \lambda) \Big) \Big]. \tag{2}
$$

Challenge of varying hyperparameter  $\lambda$ . In the formulation of the SDC, the condensed data S is learned with a fixed hyperparameter  $\lambda$ , e.g., a pre-specified neural network architecture. As a result, the condensed data trained with SDC's objective performs poorly on hyperparameter search [\(Cui et al.,](#page-31-0) [2022\)](#page-31-0), which requires the performance of models under varying hyperparameters to behave consistently on the original and condensed dataset. In the following, we tackle this issue by reformulating the dataset condensation problem under the hyperparameter optimization framework.

# 3 Hyperparameter Calibrated Dataset Condensation

In this section, we would like to develop a condensation method specifically for preserving the outcome of hyperparameter optimization (HPO) on the condensed dataset across different architectures/hyperparameters for faster hyperparameter search. This requires dealing with varying choices of hyperparameters so that the relative performances of different hyperparameters on the condensed and original datasets are consistent. We first formulate the data condensation for hyperparameter search in the HPO framework below and then propose the hyperparameter calibrated dataset condensation framework in Section [4](#page-4-1) by using the equivalence relationship between preserving the performance ranking and the hypergradient alignment.

**HPO's objective.** Given  $\mathcal{T} = \mathcal{T}^{\text{train}} \cup \mathcal{T}^{\text{val}} \cup \mathcal{T}^{\text{test}}$ , HPO aims to find the optimal hyperparameter  $\lambda^{\mathcal{T}}$  that minimizes the validation loss of the model optimized on the training dataset  $\mathcal{T}^{\text{train}}$ with hyperparameter  $\lambda^{\mathcal{T}}$ , i.e.,

<span id="page-4-2"></span>
$$
\lambda^{\mathcal{T}} = \arg \min_{\lambda \in \Lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda), \text{ where } \mathcal{L}_{\mathcal{T}}^*(\lambda) \coloneqq \mathcal{L}_{\mathcal{T}}^{\text{val}}(\theta^{\mathcal{T}}(\lambda), \lambda) \text{ and } \theta^{\mathcal{T}}(\lambda) \coloneqq \arg \min_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda). \tag{HPO}
$$

Here  $\mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta,\lambda) \coloneqq \frac{1}{|\mathcal{T}^{\text{val}}|} \sum_{(x,y)\in\mathcal{T}^{\text{val}}} l(f_{\theta}(x), y, \lambda)$ . HPO is a bi-level optimization where both the optimal parameter  $\theta^{\mathcal{T}}(\lambda)$  and the optimized validation loss  $\mathcal{L}^*_{\mathcal{T}}(\lambda)$  are viewed as a function of the hyperparameter  $\lambda$ .

Dataset condensation for HPO. We would like to synthesize a condensed training dataset  $\mathcal{S}^{\text{train}}$ and a condensed validation dataset  $\mathcal{S}^{\text{val}}$  to replace the original  $\mathcal{T}^{\text{train}}$  and  $\mathcal{T}^{\text{val}}$  for hyperparameter search. Denote the synthetic dataset as  $S = S<sup>train</sup> \bigcup S<sup>val</sup>$ . Similar to Eq. [\(HPO\)](#page-4-2), the optimal hyperparameter  $\lambda^{\mathcal{S}}$  is defined for a given dataset  $\mathcal{S}$ . Naively, one can formulate such a problem as finding the condensed dataset S to minimize the validation loss on the original dataset  $\mathcal{T}^{\text{val}}$  as follows, which is an optimization problem similar to the standard dataset condensation in Eq. [\(SDC\)](#page-3-0):

$$
S^* = \arg\min_{S} \mathcal{L}^*_{\mathcal{T}}(\lambda^S(S)) \quad \text{s.t.} \quad \lambda^S(S) := \arg\min_{\lambda \in \Lambda} \mathcal{L}^*_{\mathcal{S}}(\lambda), \tag{3}
$$

where the optimized validation losses  $\mathcal{L}^*_{\mathcal{T}}(\cdot)$  and  $\mathcal{L}^*_{\mathcal{S}}(\cdot)$  are defined following Eq. [\(HPO\)](#page-4-2).

However, two challenges exist for such a formulation. Challenge  $(1)$ : Eq.  $(3)$  is a nested optimization (for dataset condensation) over another nested optimization (for HPO), which is computationally expensive. Challenge  $(2)$ : the search space  $\Lambda$  of the hyperparameters can be complicated. In contrast to parameter optimization, where the search space is usually assumed to be the continuous and unbounded Euclidean space, the search space of the hyperparameters can be compositions of discrete and continuous spaces. Having such discrete components in the search space poses challenges for gradient-based optimization methods.

To address Challenge (1), we propose an alternative objective based on the alignment of hypergradients that can be computed efficiently in Section [4.](#page-4-1) For Challenge (2), we construct the extended search space in Section [5.](#page-5-0)

<span id="page-4-1"></span>

# 4 Hyperparameter Calibration via Hypergradient Alignment

In this section, we introduce Hyperparameter-Calibrated Dataset Condensation (HCDC), a novel condensation method designed to align hyperparameter gradients – referred to as hypergradients – thus preserving the validation performance ranking of various hyperparameters.

Hyperparameter calibration. To tackle the computational challenges inherent in hyperparameter optimization (HPO) as expressed in Eq.  $(3)$ , we propose an efficient yet sufficient alternative. Rather than directly solving the HPO problem, we aim to identify a condensed dataset that maintains the outcomes of HPO on the hyperparameter set  $\Lambda$ . We refer to this process as hyperparameter calibration, formally defined as follows.

<span id="page-4-3"></span>**Definition 1** (Hyperparameter Calibration). Given original dataset  $\mathcal{T}$ , generic model  $f_{\theta}^{\lambda}$ , and hyperparameter search space  $\Lambda$ , we say a condensed dataset S is hyperparameter calibrated, if for any  $\lambda_1 \neq \lambda_2 \in \Lambda$ , it holds that,

<span id="page-4-0"></span>
$$
(\mathcal{L}_{\mathcal{T}}^*(\lambda_1) - \mathcal{L}_{\mathcal{T}}^*(\lambda_2)) (\mathcal{L}_{\mathcal{S}}^*(\lambda_1) - \mathcal{L}_{\mathcal{S}}^*(\lambda_2)) > 0
$$
 (HC)

In other words, changes of the optimized validation loss on  $\mathcal T$  and  $\mathcal S$  always have the same sign, between any pairs of hyperparameters  $\lambda_1 \neq \lambda_2$ .

It is evident that if hyperparameter calibration (HC) is satisfied, the outcomes of HPO on both the original and condensed datasets will be identical. Consequently, our objective shifts to ensuring hyperparameter calibration across all pairs of hyperparameters.

HCDC: hypergradient alignment objective for dataset condensation. To move forward, we make the assumption that there exists a continuous extension of the search space. Specifically, the (potentially discrete) search space  $\Lambda$  can be extended to a compact and connected set  $\tilde{\Lambda} \supset \Lambda$ . Within this extended set, we define a continuation of the generic model  $f_{\theta}^{\lambda}$  such that  $f_{\theta}^{\lambda}$ is differentiable anywhere in  $\Lambda$ . In Section [5,](#page-5-0) we will elaborate on how to construct such an extended search space  $\Lambda$ .

To establish a new objective for hyperparameter calibration, consider the case when  $\lambda_1$  is in the neighborhood of  $\lambda_2$ , denoted as  $\lambda_1 \in B_r(\lambda_2)$  for some  $r > 0$ . In this situation, the change in validation loss can be approximated up to first-order by the hypergradients, as follows:  $\mathcal{L}^*_{\mathcal{T}}(\lambda_1) - \mathcal{L}^*_{\mathcal{T}}(\lambda_2) \approx$  $\langle \nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda_2), \Delta \lambda \rangle$ . Here,  $\Delta \lambda = \lambda_1 - \lambda_2$  with  $r \geq ||\Delta \lambda||_2 \rightarrow 0^+$ . Analogously, for the synthetic dataset we have:  $\mathcal{L}_{\mathcal{S}}^*(\lambda_1) - \mathcal{L}_{\mathcal{S}}^*(\lambda_2) \approx \langle \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda_2), \Delta \lambda \rangle$ . Hence, the hyperparameter calibration condition simplifies to  $\langle \nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda_2), \Delta \lambda \rangle \cdot \langle \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda_2), \Delta \lambda \rangle > 0$ . Further simplification leads to  $\nabla_{\lambda}\mathcal{L}_{\mathcal{T}}^{*}(\lambda) \parallel \nabla_{\lambda}\mathcal{L}_{\mathcal{S}}^{*}(\lambda)$ , indicating *alignment* of the two hypergradient vectors. We formally define this hypergradient alignment and establish its equivalence to hyperparameter calibration.

<span id="page-5-1"></span>Definition 2 (Hypergradient Alignment). We say hypergradients are aligned in an extended search space  $\tilde{\Lambda}$ , if for any  $\lambda \in \tilde{\Lambda}$ , it holds that  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda) \parallel \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)$ , i.e.,  $D_c(\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda), \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)) = 0$ , where  $D_c(\cdot, \cdot) = 1 - \cos(\cdot, \cdot)$  represents the cosine distance.

<span id="page-5-2"></span>Theorem 1 (Equivalence between Hypergradient Alignment and Hyperparameter Calibration). Hypergradient alignment (Definition [2\)](#page-5-1) is equivalent to hyperparameter calibration (Defi-nition [1\)](#page-4-3) on a connected and compact set, e.g., the extended search space  $\Lambda$ .

The implication is straightforward: if hyperparameter calibration holds in  $\tilde{\Lambda}$ , it also holds in Λ. According to Theorem [1,](#page-5-2) achieving hypergradient alignment in Λ is sufficient to ensure hyperparameter calibration in  $\Lambda$ . Therefore, the integrity of the HPO outcome over  $\Lambda$  is maintained.

Consequently, the essence of our hyperparameter calibrated dataset condensation (HCDC) is to align/match the hypergradients calculated on both the original and condensed datasets within the extended search space  $\Lambda$ :

$$
S^* = \arg\min_{S} \sum_{\lambda \in \tilde{\Lambda}} D_c \Big( \nabla_{\lambda} \mathcal{L}_\mathcal{T}^{\text{val}} \big( \theta^\mathcal{T}(\lambda), \lambda \big), \nabla_{\lambda} \mathcal{L}_\mathcal{S}^{\text{val}} \big( \theta^\mathcal{S}(\lambda), \lambda \big) \Big), \tag{HCDC}
$$

where the cosine distance  $D_c(\cdot, \cdot) = 1 - \cos(\cdot, \cdot)$  is used without loss of generality.

<span id="page-5-0"></span>

## 5 Implementations of HCDC

In this section, we focus on implementing the hyperparameter calibrated dataset condensation (HCDC) algorithm. We address two primary challenges: (1) efficient approximate computation of hyperparameter gradients, often called hypergradients, using implicit differentiation techniques; and (2) the efficient formation of the extended search space  $\Lambda$ . The complete pseudocode for HCDC will be provided at the end of this section.

<span id="page-6-1"></span>

### 5.1 Efficient Evaluation of Hypergradients

The efficient computation of hypergradients is well-addressed in existing literature (see Section [6\)](#page-7-0). In our HCDC implementation, we utilize the implicit function theorem (IFT) and the Neumann series approximation for inverse Hessians, as proposed by [Lorraine et al.](#page-33-0) [\(2020\)](#page-33-0).

Computing hypergradients via IFT. The hypergradients are the gradients of the optimized validation loss  $\mathcal{L}^*_{\mathcal{T}}(\lambda) = \mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta^{\mathcal{T}}(\lambda), \lambda)$  with respect to the hyperparameters  $\lambda$ ; see Appendix [E](#page-21-0) for further details. The implicit function theorem (IFT) provides an efficient approximation to compute the hypergradients  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda)$  and  $\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)$ .

<span id="page-6-0"></span>
$$
\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^{*}(\lambda) \approx -\left[\frac{\partial^{2} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda)}{\partial \lambda \partial \theta^{T}}\right] \left[\frac{\partial^{2} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda)}{\partial \theta \partial \theta^{T}}\right]^{-1} \nabla_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{val}}(\theta, \lambda) + \nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^{\text{val}}(\theta, \lambda), \tag{IFT}
$$

where we consider the direct gradient  $\nabla_{\lambda} \mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta, \lambda)$  is 0, since in most cases the hyperparameter  $\lambda$ only affects the validation loss  $\mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta,\lambda)$  through the model function  $f_{\theta,\lambda}$ . The first term consists of the mixed partials  $\left[\frac{\partial^2 \mathcal{L}_T^{\text{train}}(\theta,\lambda)}{\partial \lambda \partial \theta^T}\right]$  $\left[\frac{\partial^2 \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta,\lambda)}{\partial \lambda \partial \theta^T}\right]$ , the inverse Hessian  $\left[\frac{\partial^2 \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta,\lambda)}{\partial \theta \partial \theta^T}\right]$  $\left[\frac{\partial \text{train}(\theta,\lambda)}{\partial \theta \partial \theta^T}\right]^{-1}$ , and the validation gradients  $\nabla_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{val}}(\theta, \lambda)$ . While the other parts can be calculated efficiently through a single back-propagation, approximating the inverse Hessian is required. [Lorraine et al.](#page-33-0) [\(2020\)](#page-33-0) propose a stable, tractable, and efficient Neumann series approximation of the inverse Hessian as follows:

$$
\left[\frac{\partial^2 \mathcal{L}_\mathcal{T}^{\text{train}}(\theta,\lambda)}{\partial \theta \partial \theta^T}\right]^{-1} = \lim_{i \to \infty} \sum_{j=0}^i \left[I - \frac{\partial^2 \mathcal{L}_\mathcal{T}^{\text{train}}(\theta,\lambda)}{\partial \theta \partial \theta^T}\right]^j,
$$

which requires only constant memory. When combined with Eq. [\(IFT\)](#page-6-0), the approximated hypergradients can be evaluated by employing efficient vector-Jacobian products [\(Lorraine et al.,](#page-33-0) [2020\)](#page-33-0).

Optimizing hypergradient alignment loss in Eq. [\(HCDC\)](#page-4-0). To optimize the objective defined in HCDC (Eq. [\(HCDC\)](#page-4-0)), we learn the synthetic validation set  $\mathcal{S}^{\text{val}}$  from scratch. This is crucial as the hypergradients with respect to the validation losses in Eq. [\(HCDC\)](#page-4-0), are significantly influenced by the synthetic validation examples, which are free learnable parameters during the condensation process. In contrast, we maintain the synthetic training set  $\mathcal{S}^{\text{train}}$  as fixed. For generating  $\mathcal{S}^{\text{train}}$ , we employ the standard dataset condensation (SDC) algorithm, as described in Eq. [\(2\)](#page-3-0). To optimize the synthetic validation set  $S<sup>val</sup>$  with respect to the hyper-gradient loss in Eq. [\(HCDC\)](#page-4-0), we compute the gradients of  $\nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta, \lambda)$  and  $\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta, \lambda)$  w.r.t.  $\mathcal{S}^{\text{val}}$ . This is handled using an additional back-propagation step, akin to the one in SDC that calculates the gradients of  $\nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta, \lambda)$  w.r.t  $\mathcal{S}^{\text{train}}$ .

### 5.2 Efficient Design of Extended Search Space

HCDC's objective (Eq. [\(HCDC\)](#page-4-0)) necessitates the alignment of hypergradients across all hyperparameters  $\lambda$ 's in an extended space  $\Lambda$ . This space is a compact and connected superset of the original search space  $\Lambda$ . For practical implementation, we evaluate the hypergradient matching loss using a subset of  $\lambda$  values randomly sampled from  $\Lambda$ . To enhance HCDC's efficiency within a predefined search space  $\lambda$ , our goal is to minimally extend this space to  $\Lambda$  for sampling.

In the case of continuous hyperparameters,  $\Lambda$  is generally both compact and connected, rending  $Λ$  identical to  $Λ$ . For discrete search spaces  $Λ$  consisting of p candidate hyperparameters, we propose a linear-complexity construction for  $\Lambda$  (in which the linearity is in terms of p). Specifically, for each  $i \in [p]$ , we formulate an "i-th HPO trajectory", a representative path that originates from

 $\lambda_{i,0}^{\mathcal{S}} = \lambda_i \in \Lambda$  and evolves via the update rule  $\lambda_{i,t+1}^{\mathcal{S}} \leftarrow \lambda_{i,t}^{\mathcal{S}} - \eta \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda_{i,t}^{\mathcal{S}})$ , see Appendix [H](#page-27-0) for details and Fig.  $8$  for illustration. We assume that all  $p$  trajectories converge to the same or equivalent optima  $\lambda^{\mathcal{S}}$ , thus forming "connected" paths. Consequently, the extended search space  $\tilde{\Lambda}$  comprises these p connected trajectories, allowing us to evaluate the hypergradient matching loss along each trajectory  $\{\lambda_{i,t}^{\mathcal{S}}\}_{t=0}^T$  during the iterative update of  $\lambda$ .

### 5.3 Pseudocode

We conclude this section by outlining the HCDC algorithm in Algorithm [1,](#page-7-1) assuming a discrete and finite hyperparameter search space  $\Lambda$ . In Line [7,](#page-7-2) we calculate the hypergradients  $\nabla_{\lambda}\mathcal{L}_{\mathcal{S}}^{*}(\lambda)$  using Eq. [\(IFT\)](#page-6-0). For computing the gradient  $\nabla_{\mathcal{S}^{\text{val}}}D(\nabla_{\lambda}\mathcal{L}_{\mathcal{T}}^{*}(\lambda),\nabla_{\lambda}\mathcal{L}_{\mathcal{S}}^{*}(\lambda))$  in Line [8,](#page-7-3) we note that only  $\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)$  is depends on  $\mathcal{S}^{\text{val}}$ . Employing Eq. [\(IFT\)](#page-6-0), we find that  $\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)$  $-\left[\frac{\partial^2 \mathcal{L}_\mathcal{S}^{\text{train}}(\theta,\lambda)}{\partial \lambda \partial \theta S}\right]$  $\left[ \frac{\partial^2 \mathcal{L}_\mathcal{S}^{\text{train}}(\theta, \lambda)}{\partial \lambda \partial \theta^S} \right] \left[ \frac{\partial^2 \mathcal{L}_\mathcal{S}^{\text{train}}(\theta, \lambda)}{\partial \theta \partial \theta^S} \right]$  $\frac{\partial \text{sgn}(\theta,\lambda)}{\partial \theta \partial \theta^S}$   $^{-1} \nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta,\lambda)$ . Note that there are no direct gradients, as  $\lambda$  influences the loss solely through the model  $f_{\theta}^{\lambda}$ . Therefore, to obtain  $\nabla_{\mathcal{S}^{\text{val}}} D(\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^{*}(\lambda), \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{*}(\lambda)),$  we simply need to compute the gradient  $\nabla_{\mathcal{S}^{\text{val}}} \nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta, \lambda)$  through standard back-propagation methods, since only the validation loss term  $\nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta, \lambda)$  depends on  $\mathcal{S}^{\text{val}}$ .

Algorithm 1: Hyperparameter Calibrated Dataset Condensation (HCDC)

<span id="page-7-1"></span>**Input:** Original dataset  $\mathcal{T}$ , a set of NN architectures  $f_{\theta}$ , hyperparameter search space  $\lambda \in \Lambda = {\lambda_1, \ldots, \lambda_p}$ , predetermined condensed training data  $\mathcal{S}^{\text{train}}$  learned by standard dataset condensation (e.g., Eq. [\(2\)](#page-3-0)), randomly initialized synthetic examples  $\mathcal{S}^{\text{val}}$  of C classes. 1 for repeat  $k = 0, \ldots, K - 1$  do 2 | foreach hyperparameters  $\lambda = \lambda_1, \ldots, \lambda_p$  in  $\Lambda$  do 3 | | Initialize model parameters  $\theta \leftarrow \theta_0 \sim P_{\theta_0}$ 4 **for** epoch  $t = 0, \ldots, T_{\theta} - 1$  do 5 Update model parameters  $\theta \leftarrow \theta - \eta_{\theta} \nabla_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta, \lambda)$ 6 if t mod  $T_{\lambda} = 0$  then<br>  $\tau$  if t Update hyperparar  $\tau$  | | Update hyperparameters  $\lambda \leftarrow \lambda - \eta_{\lambda} \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda)$ 8 Update the synthetic validation set  $S^{\text{val}} \leftarrow S^{\text{val}} - \eta_S \nabla_{S^{\text{val}}} D(\nabla_\lambda \mathcal{L}^*_\mathcal{T}(\lambda), \nabla_\lambda \mathcal{L}^*_\mathcal{S}(\lambda))$  $9$  return *Condensed validation set*  $S<sup>val</sup>$ .

<span id="page-7-3"></span><span id="page-7-2"></span>For a detailed complexity analysis of Algorithm [1](#page-7-1) and further discussions, refer to Appendix [I.](#page-28-1)

<span id="page-7-0"></span>

## 6 Related Work

The traditional way to simplify a dataset is coreset selection [\(Toneva et al.,](#page-35-1) [2018;](#page-35-1) [Paul et al.,](#page-34-0) [2021\)](#page-34-0), where critical training data samples are chosen based on heuristics like diversity [\(Aljundi](#page-31-1) [et al.,](#page-31-1) [2019\)](#page-31-1), distance to the dataset cluster centers [\(Rebuffi et al.,](#page-34-1) [2017;](#page-34-1) [Chen et al.,](#page-31-2) [2010\)](#page-31-2) and forgetfulness [\(Toneva et al.,](#page-35-1) [2018\)](#page-35-1). However, the performance of coreset selection methods is limited by the assumption of the existence of representative samples in the original data, which may not hold in practice.

To overcome this limitation, **dataset distillation/condensation** [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0) has been proposed as a more effective way to reduce sample size. Dataset condensation (or dataset distillation) is first proposed in [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0) as a learning-to-learn problem by formulating the network parameters as a function of synthetic data and learning them through the network parameters to minimize the training loss over the original data. This approach involves producing a small synthetic dataset to replace the original larger one, so that the test/generalization performance of the model trained on the synthetic set is comparable to that trained on the original. However, the nested-loop optimization precludes it from scaling up to large-scale in-the-wild datasets. [Zhao et al.](#page-36-0) [\(2020\)](#page-36-0) alleviate this issue by enforcing the gradients of the synthetic samples w.r.t. the network weights to approach those of the original data, which successfully alleviates the expensive unrolling of the computational graph. Based on the meta-learning formulation in [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0), [Bohdal](#page-31-3) [et al.](#page-31-3) [\(2020\)](#page-31-3) and [Nguyen et al.](#page-34-2) [\(2020,](#page-34-2) [2021\)](#page-34-3) propose to simplify the inner-loop optimization of a classification model by training with ridge regression which has a closed-form solution, while [Such](#page-34-4) [et al.](#page-34-4) [\(2020\)](#page-34-4) model the synthetic data using a generative network. To improve the data efficiency of synthetic samples in the gradient-matching algorithm, [Zhao and Bilen](#page-35-2) [\(2021a\)](#page-35-2) apply differentiable Siamese augmentation, and [Kim et al.](#page-33-1) [\(2022\)](#page-33-1) introduce efficient synthetic-data parametrization.

Implicit differentiation methods apply the implicit function theorem (IFT) (Eq. [\(IFT\)](#page-6-0)) to nested-optimization problems [\(Wang et al.,](#page-35-3) [2019\)](#page-35-3). [Lorraine et al.](#page-33-0) [\(2020\)](#page-33-0) approximated the inverse Hessian by Neumann series, which is a stable alternative to conjugate gradients [\(Shaban et al.,](#page-34-5) [2019\)](#page-34-5) and scales IFT to large networks with constant memory.

Differentiable NAS methods, e.g., DARTS [\(Liu et al.,](#page-33-2) [2018\)](#page-33-2) explore the possibility of transforming the discrete neural architecture space into a continuously differentiable form and further uses gradient optimization to search the neural architecture. SNAS [\(Xie et al.,](#page-35-4) [2018\)](#page-35-4) points out that DARTS suffers from the unbounded bias issue towards its objective, and it remodels the NAS and leverages the Gumbel-softmax trick [\(Jang et al.,](#page-32-3) [2017;](#page-32-3) [Maddison et al.,](#page-33-3) [2017\)](#page-33-3) to learn the architecture parameter.

In addition, we summarize more dataset condensation and coreset selection methods as well as graph reduction methods in Appendix [B.](#page-13-2)

Table 1: The Spearman's rank correlation of architecture's performance (Corr.) and the test performance of the best architecture selected on the condensed dataset (Perf.) on two image datasets. Grid search is applied to find the best architecture.

<span id="page-8-0"></span>

| Dataset   | Coresets  |                  |                  | Standard Condensation |                  |                  |                  |                  | Ours             | Oracle                            |      |
|-----------|-----------|------------------|------------------|-----------------------|------------------|------------------|------------------|------------------|------------------|-----------------------------------|------|
|           | Random    | K-Center         | Herding          | DC                    | DSA              | DM               | KIP              | TM               | HCDC             |                                   |      |
| CIFAR-10  | Corr.     | $-0.12 \pm 0.07$ | $0.19 \pm 0.12$  | $-0.05 \pm 0.08$      | $-0.21 \pm 0.15$ | $-0.33 \pm 0.09$ | $-0.10 \pm 0.15$ | $-0.27 \pm 0.15$ | $-0.07 \pm 0.04$ | $\mathbf{0.74} \pm \mathbf{0.21}$ | —    |
|           | Perf. (%) | $91.3 \pm 0.2$   | $91.4 \pm 0.3$   | $90.2 \pm 0.9$        | $89.2 \pm 3.3$   | $73.5 \pm 7.2$   | $92.2 \pm 0.4$   | $91.8 \pm 0.2$   | $75.2 \pm 4.3$   | $\mathbf{92.9} \pm \mathbf{0.7}$  | 93.5 |
| CIFAR-100 | Corr.     | $-0.05 \pm 0.03$ | $-0.07 \pm 0.05$ | $0.08 \pm 0.11$       | $-0.13 \pm 0.02$ | $-0.28 \pm 0.05$ | $-0.15 \pm 0.07$ | $-0.08 \pm 0.04$ | $-0.09 \pm 0.03$ | $\mathbf{0.63} \pm \mathbf{0.13}$ | —    |
|           | Perf. (%) | $71.1 \pm 1.4$   | $69.5 \pm 2.8$   | $67.9 \pm 1.8$        | $64.9 \pm 2.2$   | $59.0 \pm 4.1$   | $70.1 \pm 0.6$   | $68.8 \pm 0.6$   | $51.3 \pm 6.1$   | $\mathbf{72.4} \pm \mathbf{1.7}$  | 72.9 |

<span id="page-8-1"></span>

# 7 Experiments

In this section, we validate the effectiveness of hyperparameter calibrated dataset condensation (HCDC) when applied to speed up architecture/hyperparameter search on two types of data: images and graphs. For an ordered list of architectures, we calculate Spearman's rank correlation coefficient −1 ≤ Corr. ≤ 1, between the rankings of their validation performance on the original and condensed datasets. This correlation coefficient (denoted by Corr.) indicates how similar the performance ranking on the condensed dataset is to that on the original dataset. We also report the test accuracy (referred to as Perf.) evaluated on the original dataset of the architectures selected on the condensed dataset. If the test performance is close to the true optimal performance among all architectures, we

Table 2: Spearman's rank correlation of convolution filters in GNNs (Corr.) and the test performance of the best convolution filter selected on the condensed graph (Pref.) on four graph datasets. Continuous hyperparameter optimization [\(Lorraine et al.,](#page-33-0) [2020\)](#page-33-0) is applied to find the best convolution filter, while Spearman's rank correlation coefficients are evaluated on 80 sampled hyperparameter configurations. n is the total number of nodes in the original graph, and  $c_{\text{train}}$  is the number of training nodes in the condensed graph.

<span id="page-9-1"></span>

| Dataset    | Ratio<br>( $c_{\text{train}}/n$ ) | Random         |                | GCond-X        |                | GCond          |                | HCDC           |                | Whole Graph<br>Perf. (%) |
|------------|-----------------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|--------------------------|
|            |                                   | Corr.          | Perf. (%)      | Corr.          | Perf. (%)      | Corr.          | Perf. (%)      | Corr.          | Perf. (%)      |                          |
| Cora       | $0.9\%$                           | $0.29 \pm .08$ | $81.2 \pm 1.1$ | $0.16 \pm .07$ | $79.5 \pm 0.7$ | $0.61 \pm .03$ | $81.9 \pm 1.6$ | $0.80 \pm .03$ | $83.0 \pm 0.2$ |                          |
| Cora       | $1.8\%$                           | $0.40 \pm .04$ | $81.9 \pm 0.5$ | $0.21 \pm .07$ | $80.3 \pm 0.4$ | $0.76 \pm .06$ | $83.2 \pm 0.9$ | $0.85 \pm .03$ | $83.4 \pm 0.2$ | $83.8 \pm 0.4$           |
| Cora       | $3.6\%$                           | $0.51 \pm .04$ | $82.2 \pm 0.6$ | $0.23 \pm .04$ | $80.9 \pm 0.6$ | $0.81 \pm .04$ | $83.2 \pm 1.1$ | $0.90 \pm .01$ | $83.4 \pm 0.3$ |                          |
| Citeseer   | $1.3\%$                           | $0.38 \pm .11$ | $71.9 \pm 0.8$ | $0.15 \pm .07$ | $70.7 \pm 0.9$ | $0.68 \pm .03$ | $71.3 \pm 1.2$ | $0.79 \pm .01$ | $73.1 \pm 0.2$ |                          |
| Citeseer   | $2.6\%$                           | $0.56 \pm .06$ | $72.2 \pm 0.4$ | $0.29 \pm .05$ | $70.8 \pm 0.5$ | $0.79 \pm .05$ | $71.5 \pm 0.7$ | $0.83 \pm .02$ | $73.3 \pm 0.5$ | $73.7 \pm 0.6$           |
| Citeseer   | $5.2\%$                           | $0.71 \pm .05$ | $73.0 \pm 0.3$ | $0.35 \pm .08$ | $70.2 \pm 0.4$ | $0.83 \pm .03$ | $71.1 \pm 0.8$ | $0.89 \pm .02$ | $73.4 \pm 0.4$ |                          |
| Ogbn-arxiv | $0.1\%$                           | $0.59 \pm .08$ | $70.1 \pm 1.7$ | $0.39 \pm .06$ | $69.8 \pm 1.4$ | $0.59 \pm .07$ | $70.3 \pm 1.4$ | $0.77 \pm .04$ | $71.9 \pm 0.8$ |                          |
| Ogbn-arxiv | $0.25\%$                          | $0.63 \pm .05$ | $70.3 \pm 1.3$ | $0.44 \pm .03$ | $70.1 \pm 0.7$ | $0.64 \pm .05$ | $70.5 \pm 1.0$ | $0.83 \pm .03$ | $72.4 \pm 1.0$ | $73.2 \pm 0.8$           |
| Ogbn-arxiv | $0.5\%$                           | $0.68 \pm .07$ | $70.9 \pm 1.0$ | $0.47 \pm .05$ | $70.0 \pm 0.7$ | $0.67 \pm .05$ | $71.1 \pm 0.6$ | $0.88 \pm .03$ | $72.6 \pm 0.6$ |                          |
| Reddit     | $0.1\%$                           | $0.42 \pm .09$ | $92.1 \pm 1.6$ | $0.39 \pm .04$ | $90.9 \pm 0.8$ | $0.53 \pm .06$ | $90.9 \pm 1.7$ | $0.79 \pm .03$ | $92.1 \pm 0.9$ |                          |
| Reddit     | $0.25\%$                          | $0.50 \pm .06$ | $92.7 \pm 1.3$ | $0.41 \pm .05$ | $90.9 \pm 0.5$ | $0.61 \pm .04$ | $91.2 \pm 1.2$ | $0.83 \pm .01$ | $92.9 \pm 0.7$ | $94.1 \pm 0.7$           |
| Reddit     | $0.5\%$                           | $0.58 \pm .06$ | $92.8 \pm 0.7$ | $0.42 \pm .03$ | $91.5 \pm 0.6$ | $0.66 \pm .02$ | $92.1 \pm 0.9$ | $0.87 \pm .01$ | $93.1 \pm 0.5$ |                          |

Table 3: The search time and test performance of the best architecture find by NAS methods on the condensed datasets. We consider two NAS algorithms: (1) the differentiable NAS algorithm DARTS-PT and (2) REINFORCE without parameter-sharing.

<span id="page-9-0"></span>

| NAS Algorithm | Random     |                | DС         |                | HCDC       |                | Original   |                |
|---------------|------------|----------------|------------|----------------|------------|----------------|------------|----------------|
|               | Time (sec) | Perf. $(\%)$   | Time (sec) | Perf. $(\%)$   | Time (sec) | Perf. $(\%)$   | Time (sec) | Perf. $(\%)$   |
| DARTS-PT      | 37.1       | $89.4 \pm 0.3$ | 39.2       | $85.2 \pm 1.9$ | 35.5       | $91.9 \pm 0.4$ | 229        | $92.7 \pm 0.6$ |
| REINFORCE     | 166        | $88.1 \pm 1.8$ | 105        | $80.1 \pm 6.5$ | 119        | $92.3 \pm 1.1$ | 1492       | $93.0 \pm 0.9$ |

say the architecture search outcome is preserved with high accuracy. See Appendix [G](#page-27-1) and Appendix [J](#page-29-0) for more discussions on implementation and experimental setups.

Preserving architecture performance ranking on images. We follow the practice of [\(Cui](#page-31-0) [et al.,](#page-31-0) [2022\)](#page-31-0) and construct the search space by sampling 100 networks from NAS-Bench-201 [\(Dong](#page-32-4) [and Yang,](#page-32-4) [2020\)](#page-32-4), which contains the ground-truth performance of 15,625 networks. All models are trained on CIFAR-10 or CIFAR-100 for 50 epochs under five random seeds and ranked according to their average accuracy on a held-out validation set of 10K images. As a common practice in NAS [\(Liu](#page-33-2) [et al.,](#page-33-2) [2018\)](#page-33-2), we reduce the number of repeated blocks in all architecture from 15 to 3 during the search phase, as deep models are hard to train on the small condensed datasets. We consider three coreset baselines, including uniform random sampling, K-Center [\(Farahani and Hekmatfar,](#page-32-5) [2009\)](#page-32-5), and Herding [\(Welling,](#page-35-5) [2009\)](#page-35-5) coresets, as well as five standard condensation baselines, including dataset condensation (DC) [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0), differentiable siamese augmentation (DSA) [\(Zhao](#page-35-2) [and Bilen,](#page-35-2) [2021a\)](#page-35-2), distribution matching (DM) [\(Zhao and Bilen,](#page-36-1) [2021b\)](#page-36-1), Kernel Inducing Point (KIP) [\(Nguyen et al.,](#page-34-2) [2020,](#page-34-2) [2021\)](#page-34-3), and Training Trajectory Matching (TM) [\(Cazenavette et al.,](#page-31-4) [2022\)](#page-31-4). For the coreset and condensation baselines, we randomly split the condensed dataset to obtain the condensed validation data while keeping the train-validation split ratio. We subsample or compress the original dataset to 50 images per class for all baselines. As shown in Table [1,](#page-8-0) our HCDC is much better at preserving the performance ranking of architectures compared to all other coreset and condensation methods. At the same time, HCDC also consistently attains better-selected

<span id="page-10-0"></span>Image /page/10/Figure/0 description: This is a heatmap showing the performance of different algorithms. The algorithms listed on the left are Random, K-Center, Herding, DC, DSA, DM, KIP, TM, HCDC (Ours), and Optimal. The correlation values for each algorithm are listed on the right: -0.12, +0.19, -0.05, -0.21, -0.33, -0.10, -0.27, -0.07, +0.74, and +1.00, respectively. A color gradient from light red to dark red at the bottom indicates better performance, with the darkest red representing optimal performance (+1.00).

Figure 2: Visualization of the performance rankings of architectures (subsampled from the search space) evaluated on different condensed datasets. Colors indicate the performance ranking on the original dataset, while lighter shades refer to better performance. Spearman's rank correlations are shown on the right.

architectures' performance, which implies the HCDC condensed datasets are reliable proxies of the original datasets for architecture search.

Speeding up architecture search on images. We then combine HCDC with some off-the-shelf NAS algorithms to demonstrate the efficiency gain when evaluated on the proxy condensed dataset. We consider two NAS algorithms: DARTS-PT [\(Wang et al.,](#page-35-6) [2020\)](#page-35-6), which is a parameter-sharing based differentiable NAS algorithm, and REINFORCE [\(Williams,](#page-35-7) [1992\)](#page-35-7), which is a reinforcement learning algorithm without parameter sharing. In Table [3,](#page-9-0) we see all coreset/condensation baselines can bring significant speed-ups to the NAS algorithms since the models are trained on the small proxy datasets. Same as under the grid search setup, the test performance of the selected architecture on the HCDC condensed dataset is consistently higher. Here a small search space of 100 sampled architectures is used as in Table [3](#page-9-0) and we expect even higher efficiency gain on larger search spaces.

In Fig. [2,](#page-10-0) we directly visualize the performance rankings of architectures on different condensed datasets. Each color slice indicates one architecture and and are re-ordered with the ranking from the condensation algorithm. rows that are more similar to the 'optimal' gradient indicate that the algorithm is ranking the architectures similarly to the optimial ranking. The best architectures with the HCDC algorithm are among the best in the original dataset.

Finding the best convolution filter on graphs. We now consider the search space of graph neural networks' (GNN) convolution filters, which is intrinsically continuous (i.e., defined by a few continuous hyperparameters which parameterize the convolution filter, see Section [5](#page-5-0) for details). Our goal is to speed up the selection of the best-suited convolution filter design on large graphs. We

Image /page/11/Picture/0 description: The image displays a grid of 30 small images, arranged in three rows and ten columns. Below each column of images, a label is provided: Plane, Car, Bird, Cat, Deer, Dog, Frog, Horse, Ship, and Truck. The small images themselves appear to be generated or stylized representations of these categories, with varying degrees of clarity and detail. Some images are more abstract, while others show recognizable shapes or features of the labeled objects or animals.

Figure 3: Visualization of some example condensed validation set images using our HCDC algorithm on CIFAR-10.

consider 2-layer message-passing GNNs whose convolution matrix is a truncated sum of powers of the graph Laplacian; see Appendix [J.](#page-29-0) Four node classification graph benchmarks are used, including two small graphs (Cora and Citeseer) and two large graphs (Ogbn-arxiv and Reddit) with more than 100K nodes. To compute the Spearman's rank correlations, we sample 80 hyperparameter setups from the search space and compare their performance rankings. We test HCDC against three baselines: (1) Random: random uniform sampling of nodes and find their induced subgraph, (2) GCond-X: graph condensation [\(Jin et al.,](#page-33-4) [2021\)](#page-33-4) but fix the synthetic adjacency to the identity matrix, (3) GCond: graph condensation which also learns the adjacency. The whole graph performance is oracle and shows the best possible test performance when the convolution filter is optimized on the original datasets using hypergradient-based method [\(Lorraine et al.,](#page-33-0) [2020\)](#page-33-0). In Table [2,](#page-9-1) we see HCDC consistently outperforms the other approaches, and the test performance of selected architecture is close to the ground-truth optimal.

<span id="page-11-0"></span>Image /page/11/Figure/3 description: The image is a line graph showing the best test accuracy (%) over time in seconds for two different datasets: GraphNAS on HCDC graph and GraphNAS on original graph. The x-axis represents time in seconds, ranging from 0 to 150. The y-axis represents best test accuracy in percentage, ranging from 69.5% to 72.0%. The blue line, representing GraphNAS on HCDC graph, starts at approximately 69.7% at 10 seconds and increases to about 72.2% at 150 seconds. The orange line, representing GraphNAS on original graph, starts at approximately 69.8% at 10 seconds and increases to about 71.9% at 150 seconds. Both lines have shaded regions indicating variability. Error bars are also present on both lines at various time points.

Figure 4: Speed-up of graph NAS's search process, when evaluated on the small proxy dataset condensed by HCDC.

Speeding up off-the-shelf graph architecture search algorithms. Finally, we demonstrate HCDC can speed up off-the-shelf graph architecture search methods. We use graph NAS [\(Gao et al.,](#page-32-6) [2019\)](#page-32-6) on Ogbn-arxiv with a compression ratio of  $c_{\text{train}}/n = 0.5\%$ , where n is the size of the original graph, and  $c_{\text{train}}$  is the number of training nodes in the condensed graph. The search space of GNN architectures is the same as in [\(Gao et al.,](#page-32-6) [2019\)](#page-32-6), where various attention and aggregation functions are incorporated. We plot the best test performance of searched architecture versus the search time in Fig. [4.](#page-11-0) We see that when evaluated on the dataset condensed by HCDC, the search algorithm finds the better architectures much faster. This efficiency gain provided by the small proxy dataset is orthogonal to the design of search strategies and should be applied to any type of data, including graph and images.

# 8 Conclusion

We propose a hyperparameter calibration formulation for dataset condensation to preserve the outcome of hyperparameter optimization, which is then solved by aligning the hyperparameter gradients. We demonstrate both theoretically and experimentally that HCDC can effectively preserve the validation performance rankings of architectures and accelerate the hyperparameter/architecture search on images and graphs. The overall performance of HCDC can be affected by (1) how the differentiable NAS model used for condensation generalizes to unseen architectures, (2) where we align hypergradients in the search space, (3) how we learn the synthetic training set, (4) how we parameterize the synthetic dataset, and we leave the exploration of these design choices to future work. We hope our work opens up a promising avenue for speeding up hyperparameter/architecture search by dataset compression.

## Acknowledgement

Ding, Xu, Rabbani, Liu, and Huang are supported by DARPA Transfer from Imprecise and Abstract Models to Autonomous Technologies (TIAMAT) 80321, National Science Foundation NSF-IIS-2147276 FAI, DOD-ONR-Office of Naval Research under award number N00014-22-1-2335, DOD-AFOSR-Air Force Office of Scientific Research under award number FA9550-23-1-0048, DOD-DARPA-Defense Advanced Research Projects Agency Guaranteeing AI Robustness against Deception (GARD) HR00112020007, Adobe, Capital One and JP Morgan faculty fellowships.

<span id="page-13-1"></span>

# A A Detailed Diagram of the Proposed HCDC

<span id="page-13-0"></span>Image /page/13/Figure/1 description: This is a flowchart illustrating a method for achieving comparable validation-performance rankings between an original training set and a synthetic training set. The process begins with an original training set (T^train) and a synthetic training set (S^train), which is predetermined by standard dataset condensation. Both sets are used to train models, indexed by hyperparameters (lambda). Optimized parameters are obtained for both the original training set (theta^T(lambda)) and the synthetic training set (theta^S(lambda)). The validation loss on the original validation set (T^val) is calculated as L\_T^\*(lambda), and the validation loss on the synthetic validation set (S^val) is calculated as L\_S^\*(lambda). The goal is to ensure comparable validation-performance rankings, meaning that the product of the differences in validation losses for two different hyperparameter settings (lambda\_1 and lambda\_2) is greater than zero. This is achieved by minimizing a hypergradient matching loss, defined by Eq.(HCDC), which involves the divergence of the hypergradients of the validation losses with respect to lambda. The synthetic validation set is updated based on this loss, and the process involves both implicit and direct gradients of lambda, following the HCDC algorithm flow.

Figure 5: Hyperparameter Calibrated Dataset Condensation (HCDC) aims to find a small validation dataset such that the validation-performance rankings of the models with different hyperparameters are comparable to the large original dataset's (Eq. [\(HC\)](#page-4-0) in the "Goal" box). Our method realizes this goal by learning the synthetic validation set to match the hypergradients w.r.t the hyperparameters (Eq. [\(HCDC\)](#page-4-0) in the "Loss" box). Our contribution is depicted within the big black dashed box. The algorithm flow is indicated through the red dashed arrows. Solid arrows (blue, yellow and green) indicate forward passes. To calculate the hypergradients with respect to hyperparameters  $\lambda$ , we backpropagate to compute both implicit/direct gradients (thin/thick green dashed arrows). The synthetic training set is predetermined by any standard dataset condensation (SDC) methods (e.g., Eq. [\(SDC\)](#page-3-0)). The synthetic training and validation datasets obtained can later be used for hyperparameter search using only a fraction of the original computational load.

<span id="page-13-2"></span>

# B More Related Work

This section contains extensive discussions of many related areas, which cannot be fitted into the main paper due to the page limit.

### B.1 Dataset Condensation and Coreset Selection

Firstly, we review the two main approaches to reducing the training set size while preserving model performance.

Dataset condensation (or distillation) is first proposed in [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0) as a learning-tolearn problem by formulating the network parameters as a function of synthetic data and learning them through the network parameters to minimize the training loss over the original data. However, the nested-loop optimization precludes it from scaling up to large-scale in-the-wild datasets. [Zhao](#page-36-0)

[et al.](#page-36-0) [\(2020\)](#page-36-0) alleviate this issue by enforcing the gradients of the synthetic samples w.r.t. the network weights to approach those of the original data, which successfully alleviates the expensive unrolling of the computational graph. Based on the meta-learning formulation in [\(Wang et al.,](#page-35-0) [2018\)](#page-35-0), [Bohdal](#page-31-3) [et al.](#page-31-3) [\(2020\)](#page-31-3) and [Nguyen et al.](#page-34-2) [\(2020,](#page-34-2) [2021\)](#page-34-3) propose to simplify the inner-loop optimization of a classification model by training with ridge regression which has a closed-form solution, while [Such](#page-34-4) [et al.](#page-34-4) [\(2020\)](#page-34-4) model the synthetic data using a generative network. To improve the data efficiency of synthetic samples in gradient-matching algorithm, [Zhao and Bilen](#page-35-2) [\(2021a\)](#page-35-2) apply differentiable Siamese augmentation, and [Kim et al.](#page-33-1) [\(2022\)](#page-33-1) introduce efficient synthetic-data parametrization. Recently, a new distribution-matching framework [\(Zhao and Bilen,](#page-36-1) [2021b\)](#page-36-1) proposes to match the hidden features rather than the gradients for fast optimization but may suffer from performance degradation compared to gradient-matching [\(Zhao and Bilen,](#page-36-1) [2021b\)](#page-36-1), where [Kim et al.](#page-33-1) [\(2022\)](#page-33-1) provide some interpretation.

Coreset selection methods choose samples that are important for training based on heuristic criteria, for example, minimizing the distance between coreset and whole-dataset centers [\(Chen et al.,](#page-31-2) [2010;](#page-31-2) [Rebuffi et al.,](#page-34-1) [2017\)](#page-34-1), maximizing the diversity of selected samples in the gradient space [\(Aljundi](#page-31-1) [et al.,](#page-31-1) [2019\)](#page-31-1), discovering cluster centers [\(Sener and Savarese,](#page-34-6) [2018\)](#page-34-6), and choosing samples with the largest negative implicit gradient [\(Borsos et al.,](#page-31-5) [2020\)](#page-31-5). Forgetting [\(Toneva et al.,](#page-35-1) [2018\)](#page-35-1) measures the forgetfulness of trained samples and drops those that are not easy to forget. GraNd [\(Paul](#page-34-0) [et al.,](#page-34-0) [2021\)](#page-34-0) selects the training samples that contribute most to the training loss in the first few epochs. Prism [\(Kothawade et al.,](#page-33-5) [2022\)](#page-33-5) select samples to maximize submodular set-functions, which are combinatorial generalizations of entropy measures [\(Iyer et al.,](#page-32-7) [2021\)](#page-32-7). Recent benchmark [\(Guo](#page-32-8) [et al.,](#page-32-8) [2022\)](#page-32-8) of a variety of coreset selection methods for image classification indicates Forgetting, GraNd, and Prism are among the best-performing corset methods but still evidently underperform the dataset condensation baselines. Although coreset selection can be very efficient, most of the methods above suffer from three major limitations: (1) their performance is upper-bounded by the information in the selected samples; (2) most of them do not directly optimize the synthetic samples to preserve the model performance; and (3) most of them select samples incrementally and greedily, which is short-sighted.

### B.2 Implicit Differentiation and Differentiable NAS

Secondly, we list two relevant areas to this work: implicit differentiation methods based on the implicit function theorem (IFT), and the differentiable neural architecture search (NAS) algorithms.

Implicit differentiation methods apply the implicit function theorem (IFT) to the nestedoptimization problems [\(Ochs et al.,](#page-34-7) [2015;](#page-34-7) [Wang et al.,](#page-35-3) [2019\)](#page-35-3). The IFT requires inverting the training Hessian with respect to the network weights, where early work either computes the inverse explicitly [\(Bengio,](#page-31-6) [2000;](#page-31-6) [Larsen et al.,](#page-33-6) [1996\)](#page-33-6) or approximates it as the identity matrix [\(Luketina et al.,](#page-33-7) [2016\)](#page-33-7). Conjugate gradient (CG) is applied to invert the Hessian approximately [\(Pedregosa,](#page-34-8) [2016\)](#page-34-8), but is difficult to scale to deep networks. Several methods have been proposed to efficiently approximate Hessian inverse, for example, 1-step unrolled differentiation [\(Luketina et al.,](#page-33-7) [2016\)](#page-33-7), Fisher information matrix [\(Larsen et al.,](#page-33-6) [1996\)](#page-33-6), NN-structure aided Kronecker-factored inversion [\(Martens and Grosse,](#page-34-9) [2015\)](#page-34-9). [Lorraine et al.](#page-33-0) [\(2020\)](#page-33-0) use the Neumann inverse approximation, which is a stable alternative to CG [\(Shaban et al.,](#page-34-5) [2019\)](#page-34-5) and successfully scale gradient-based bilevel-optimization to large networks with constant memory constraint. It is shown that unrolling differentiation around locally optimal weights for i steps is equivalent to approximating the Neumann series inverse approximation up to the first i terms.

Differentiable NAS methods, e.g., DARTS [\(Liu et al.,](#page-33-2) [2018\)](#page-33-2) explores the possibility of transforming the discrete neural architecture space into a continuously differentiable form and further uses gradient optimization to search the neural architecture. DARTS follows a cell-based search space [\(Zoph et al.,](#page-36-2) [2018\)](#page-36-2) and continuously relaxes the original discrete search strategy. Despite its simplicity, several works cast double on the effectiveness of DARTS [\(Li and Talwalkar,](#page-33-8) [2020;](#page-33-8) [Zela et al.,](#page-35-8) [2019\)](#page-35-8). SNAS [\(Xie et al.,](#page-35-4) [2018\)](#page-35-4) points out that DARTS suffers from the unbounded bias issue towards its objective, and it remodels the NAS and leverages the Gumbel-softmax trick [\(Jang](#page-32-3) [et al.,](#page-32-3) [2017;](#page-32-3) [Maddison et al.,](#page-33-3) [2017\)](#page-33-3) to learn the exact architecture parameter. Differentiable NAS techniques have also been applied to graphs to design data-specific GNN architectures [\(Wang et al.,](#page-35-9) [2021;](#page-35-9) [Huan et al.,](#page-32-9) [2021\)](#page-32-9) automatically.

### B.3 Graph Reduction

Lastly, when we apply HCDC to graph data, it relates to the graph reduction method for graph neural network training, which we summarize as follows.

Graph coreset selection is a non-trivial generalization of the above method coreset methods given the non-iid nature of graph nodes and the non-linearity nature of GNNs. The very few offthe-shelf graph coreset algorithms are designed for graph clustering [\(Baker et al.,](#page-31-7) [2020;](#page-31-7) [Braverman](#page-31-8) [et al.,](#page-31-8) [2021\)](#page-31-8) and are not optimal for the training of GNNs.

Graph condensation [\(Jin et al.,](#page-33-4) [2021\)](#page-33-4) achieves the state-of-the-art performance for preserving GNNs' performance on the simplified graph. However, [Jin et al.](#page-33-4) [\(2021\)](#page-33-4) only adapt the gradientmatching algorithm of dataset condensation [Zhao et al.](#page-36-0) [\(2020\)](#page-36-0) to graph data, together with an MLP-based generative model for edges [\(Anand and Huang,](#page-31-9) [2018;](#page-31-9) [Simonovsky and Komodakis,](#page-34-10) [2018\)](#page-34-10), leaving out several major issues on efficiency, performance, and generalizability. Subsequent work aims to apply the more efficient distribution-matching algorithm [\(Zhao and Bilen,](#page-36-1) [2021b;](#page-36-1) [Wang](#page-35-10) [et al.,](#page-35-10) [2022\)](#page-35-10) of dataset condensation to graph [\(Liu et al.,](#page-33-9) [2022\)](#page-33-9) or speed up gradient-matching graph condensation by reducing the number of gradient-matching-steps [\(Jin et al.,](#page-33-10) [2022\)](#page-33-10). While the efficiency issue of graph condensation is mitigated [\(Jin et al.,](#page-33-10) [2022\)](#page-33-10), the performance degradation on medium- and large-sized graphs still renders graph condensation practically meaningless. Our HCDC is specifically designed for repeated training in architecture search, which is, in contrast, well-motivated.

Graph sampling methods [\(Chiang et al.,](#page-31-10) [2019;](#page-31-10) [Zeng et al.,](#page-35-11) [2019\)](#page-35-11) can be as simple as uniformly sampling a set of nodes and finding their induced subgraph, which is understood as a graphcounterpart of uniform sampling of iid samples. However, most of the present graph sampling algorithms (e.g., ClusterGCN [\(Chiang et al.,](#page-31-10) [2019\)](#page-31-10) and GraphSAINT [\(Zeng et al.,](#page-35-11) [2019\)](#page-35-11)) are designed for sampling multiple subgraphs (mini-batches), which form a cover of the original graph for training GNNs with memory constraint. Therefore those graph mini-batch sampling algorithms are effectively graph partitioning algorithms and not optimized to find just one representative subgraph.

Graph sparsification [\(Batson et al.,](#page-31-11) [2013;](#page-31-11) [Satuluri et al.,](#page-34-11) [2011\)](#page-34-11) and graph coarsening [\(Loukas](#page-33-11) [and Vandergheynst,](#page-33-11) [2018;](#page-33-11) [Loukas,](#page-33-12) [2019;](#page-33-12) [Huang et al.,](#page-32-10) [2021;](#page-32-10) [Cai et al.,](#page-31-12) [2020\)](#page-31-12) algorithms are usually designed to preserve specific graph properties like graph spectrum and graph clustering. Such objectives are often not aligned with the optimization of downstream GNNs and are shown to be sub-optimal in preserving the information to train GNNs well [\(Jin et al.,](#page-33-4) [2021\)](#page-33-4).

# C Preliminaries on Graph Hyperparameter Search

HCDC applies to not only image data but also graph data and graph neural networks (GNN). In Section [7,](#page-8-1) we use HCDC to search for the best convolution filter in message-passing GNNs. In this section, we give the backgrounds of the graph-related experiments.

### C.1 Node Classification and GNNs

Node classification on a graph considers a graph  $\mathcal{T} = (A, X, y)$  with adjacency matrix  $A \in \{0, 1\}^{n \times n}$ , node features  $X \in \mathbb{R}^{n \times d}$ , node class labels y, and mutually disjoint node-splits  $V_{\text{train}} \bigcup V_{\text{val}} \bigcup V_{\text{test}} =$ [n]. Using a graph neural network (GNN)  $f_{\theta,\lambda}: \mathbb{R}_{\geq 0}^{n \times n} \times \mathbb{R}^{n \times d} \to \mathbb{R}^{n \times K}$ , where  $\theta \in \Theta$  denotes the parameters and  $\lambda \in \Lambda$  denotes the hyper-parameters (if they exist), we aim to find  $\theta^{\mathcal{T}} =$  $\arg \min_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda), \text{ where } \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda) \coloneqq \sum_{i \in V_{\text{train}}} \ell([f_{\theta, \lambda}(A, X)]_i, y_i) \text{ and } \ell(\hat{y}, y) \text{ is the cross-entropy}$ loss. The node classification loss  $\mathcal{L}^{\text{train}}_{\mathcal{T}}(\theta,\lambda)$  is under the *transductive* setting, which can be easily generalized to the *inductive* setting by assuming only  $\{A_{ij} | i, j \in V_{\text{train}}\}$  and  $\{X_i | i \in V_{\text{train}}\}$  are used during training.

<span id="page-16-1"></span>

### C.2 Additional Downstream Tasks

We have defined the downstream task this paper mainly focuses on, node classification on graphs. Where we are given a graph  $\mathcal{T} = (A, X, y)$  with adjacency matrix  $A \in \{0, 1\}^{n \times n}$ , node features  $X \in \mathbb{R}^{n \times d}$ , node class labels  $\mathbf{y} \in [K]^n$ , and mutually disjoint node-splits  $V_{\text{train}} \bigcup V_{\text{val}} \bigcup V_{\text{test}} = [n]$ , and the goal is to predict the node labels.

Here, we show that the settings above can also be used to describe per-pixel classification on images (e.g., for semantic segmentation) where CNNs are usually used. For *per-pixel classification*, we are given a set of **n** images of size  $w \times h$ , so the pixel values of the j-th image can be formatted as a tensor  $\mathfrak{X}_j \in \mathbb{R}^{w \times h \times c}$  if there are c channels. We are also given the pixel labels  $\mathfrak{Y}_j \in [K]^{w \times h}$ for each image  $j \in [\mathfrak{n}]$  and the mutually disjoint image-splits  $I_{train} \bigcup I_{val} \bigcup I_{test} = [\mathfrak{n}]$ . Clearly, we can reshape the pixel values and pixel labels of the j-th image to  $wh \times c$  and  $wh$ , respectively, and concatenate those matrices from all images. Following this, denoting  $n = \mathfrak{n}wh$ , we obtain the concatenated pixel value matrix  $X \in \mathbb{R}^{n \times c}$  and the concatenated pixel label vector  $\mathbf{y} \in [K]^n$ . The image-splits are translated into pixel-level splits where  $V_{train} = \{i \mid (j-1)wh \le i \le jwh, j \in I_{train}\}\$ (similar for  $V_{val}$  and  $V_{test}$ ) and  $V_{train} \bigcup V_{val} \bigcup V_{test} = [n]$ . We can also define the auxiliary adjacency matrix  $A \in \{0,1\}^{n \times n}$  on the  $n = \text{twh}$  pixels, where A is block diagonal  $A = \text{diag}(A_1, \ldots, A_n)$  and  $A_j \in \{0,1\}^{wh \times wh}$  is the assumed adjacency (e.g., a two-dimensional grid) of the j-th image.

<span id="page-16-2"></span>

### C.3 Graph Neural Network Models

Here we mainly focus on graph neural networks (GNNs)  $f_{\theta,\lambda}: \mathbb{R}_{\geq 0}^{n \times n} \times \mathbb{R}^{n \times d} \to \mathbb{R}^{n \times K}$ , where  $\theta \in \Theta$ denotes the parameters, and  $\lambda \in \Lambda$  denotes the hyperparameters. In Section [2,](#page-2-0) we have seen that most GNNs can be interpreted as iterative convolution/message passing over nodes [\(Ding et al.,](#page-32-11) [2021;](#page-32-11) [Balcilar et al.,](#page-31-13) [2021\)](#page-31-13) where  $X^{(0)} = X$  and  $f(A, X) = X^{(L)}$ , and for  $l \in [L]$ , the update-rule is,

<span id="page-16-0"></span>
$$
X^{(l+1)} = \sigma\Big(C_{\alpha^{(l)}}(A)X^{(l)}W^{(l)}\Big),\tag{4}
$$

where  $C_{\alpha^{(l)}}(A)$  is the convolution matrix parametrized by  $\alpha^{(l)}$ ,  $W^{(l)}$  is the learnable linear weights, and  $\sigma(\cdot)$  denotes the non-linearity. Thus the parameters  $\theta$  consists of all  $\alpha$ 's (if they exist) and W's, i.e.,  $\theta = [\alpha^{(0)}, \dots, \alpha^{(L-1)}, W^{(0)}, \dots, W^{(L-1)}].$ 

More specifically, it is possible for GNNs to have more than one convolution filter per layer [\(Ding](#page-32-11) [et al.,](#page-32-11) [2021;](#page-32-11) [Balcilar et al.,](#page-31-13) [2021\)](#page-31-13) and we may generalize Eq. [\(4\)](#page-16-0) to,

<span id="page-17-0"></span>
$$
X^{(l+1)} = \sigma \left( \sum_{i=1}^{p} C_{\alpha^{(l,i)}}^{(i)}(A) X^{(l)} W^{(l,i)} \right). \tag{5}
$$

Within this common framework, GNNs differ from each other by choice of convolution filters  $\{C^{(i)}\},$ which can be either fixed or learnable. If  $C^{(i)}$  is fixed, there is no parameters  $\alpha^{(l,i)}$  for any  $l \in [L]$ . If  $C^{(i)}$  is learnable, the convolution matrix relies on the learnable parameters  $\alpha^{(l,i)}$  and can be different in each layer (thus should be denoted as  $C^{(l,i)}$ ). Usually, for GNNs, the convolution matrix depends on the parameters in two possible ways: (1) the convolution matrix  $C^{(l,i)}$  is scaled by the scalar parameter  $\alpha^{(l,i)} \in \mathbb{R}$ , i.e.,  $C^{(l,i)} = \alpha^{(l,i)} \mathfrak{C}^{(i)}$  (e.g., GIN [\(Xu et al.,](#page-35-12) [2018\)](#page-35-12), ChebNet [\(Defferrard](#page-31-14) [et al.,](#page-31-14) [2016\)](#page-31-14), and SIGN [\(Frasca et al.,](#page-32-12) [2020\)](#page-32-12)); or (2) the convolution matrix is constructed by node-level self-attentions  $[C^{(l,i)}]_{ij} = h_{\alpha^{(l,i)}}(X_{i,:}^{(l)})$  $\hat{h}_{i,:}^{(l)}, X_{j,:}^{(l)}\big)[\mathfrak{C}^{(i)}]_{i,j}$  (e.g., GAT [\(Veličković et al.,](#page-35-13) [2018\)](#page-35-13), Graph Transformers [\(Rong et al.,](#page-34-12) [2020;](#page-34-12) [Puny et al.,](#page-34-13) [2020;](#page-34-13) [Zhang et al.,](#page-35-14) [2020\)](#page-35-14)). Based on [\(Ding et al.,](#page-32-11) [2021;](#page-32-11) [Balcilar et al.,](#page-31-13) [2021\)](#page-31-13), we summarize the popular GNNs reformulated into the convolution over nodes / message-passing formula  $(Eq. (5))$  $(Eq. (5))$  $(Eq. (5))$  in Table [4.](#page-18-0)

Convolutional neural networks can also be reformulated into the form of Eq. [\(5\)](#page-17-0). For simplicity, we only consider a one-dimensional convolution neural network (1D-CNN), and the generalization to 2D/3D-CNNs is trivial. If we denote the constant cyclic permutation matrix (which corresponds to a unit shift) as  $P \in \mathbb{R}^{n \times n}$ , the update rule of a 1D-CNN with kernel size  $(2K + 1), K \geq 0$  can be written as,

<span id="page-17-1"></span>
$$
X^{(l+1)} = \sigma \Big( \sum_{k=-K}^{k=K} \alpha_k P^k X^{(l)} W^{(l,k)} \Big). \tag{6}
$$

We will use this common convolution formula of GNNs (Eq. [\(5\)](#page-17-0)) and 1D-CNNs (Eq. [\(6\)](#page-17-1)) in Appendix [C.5](#page-18-1) and Proposition [2.](#page-21-1)

### C.4 HCDC is Applicable to Various Data, Tasks, and Models

In Appendices [C.2](#page-16-1) and [C.3,](#page-16-2) we have discussed the formal definition of two possible tasks (1) node classification on graphs and (2) per-pixel classification on images, and reformulated many popular GNNs and CNNs into a general convolution form (Eqs.  $(5)$  and  $(6)$ ). However, we want to note that the application of dataset condensation methods (including the standard dataset condensation [\(Wang](#page-35-0) [et al.,](#page-35-0) [2018;](#page-35-0) [Zhao et al.,](#page-36-0) [2020;](#page-36-0) [Zhao and Bilen,](#page-36-1) [2021b\)](#page-36-1) and our HCDC) is not limited by the specific types of data, tasks, and models.

For HCDC, we can follow the conventions in [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0) to define the train/validation losses on *iid* samples and define the notion of dataset condensation as learning a smaller synthetic dataset with less number of samples. Here we leave the readers to [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0) for formal definitions of condensation on datasets with iid samples.

More generally speaking, our HCDC can be applied as long as (1) the train and validation losses, i.e.,  $\mathcal{L}_{\mathcal{T}}^{train}(\theta, \lambda)$  and  $\mathcal{L}_{\mathcal{T}}^{val}(\theta, \lambda)$  can be defined (as functions of the parameters and hyperparameters); and  $(2)$  we have a well-defined notion of the learnable synthetic dataset  $\mathcal{S}$ , (e.g., which includes

<span id="page-18-0"></span>

| Model Name                                          | Design Idea     | Conv. Matrix Type | $\#$ of Conv.                                                            | Convolution Matrix                                                                                                                                                                                                                                                                                                                                                                                                                                      |
|-----------------------------------------------------|-----------------|-------------------|--------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| $GCN1$ (Kipf and Welling, 2016)                     | Spatial Conv.   | Fixed             | $\mathbf{1}$                                                             | $C = \widetilde{D}^{-1/2} \widetilde{A} \widetilde{D}^{-1/2}$                                                                                                                                                                                                                                                                                                                                                                                           |
| $SAGE-Mean2$ (Hamilton et al., 2017)                | Message Passing | Fixed             | $\,2$                                                                    |                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| GAT <sup>3</sup> (Veličković et al., 2018)          | Self-Attention  | Learnable         | $\#$ of heads                                                            | $\mathcal{L} = D^{-1} A$<br>$\begin{cases}\nC^{(1)} = I_n \\ C^{(2)} = D^{-1} A\n\end{cases}$<br>$\begin{cases}\n\mathfrak{c}^{(s)} = A + I_n \text{ and} \\ h_{a^{(l,s)}}(X_{i,:}^{(l)}, X_{j,:}^{(l)}) = \exp\left(\text{LeakyReLU}\right) \\ (X_{i,:}^{(l)} W^{(l,s)} \parallel X_{j,:}^{(l)} W^{(l,s)}) \cdot \boldsymbol{a}^{(l,s)})\right) \\ C^{(1)} = A \\ \mathfrak{c}^{(2)} = I_n \text{ and } h_{\epsilon^{(l)}}^{(2)} = 1 + \epsilon^{(l)}$ |
| $GIN1$ (Xu et al., 2018)                            | WL-Test         | $Fixed +$         | $\overline{a}$                                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
|                                                     |                 | Learnable         |                                                                          |                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| $SGC2$ (Defferrard et al., 2016)                    | Spectral Conv.  | Learnable         | order of poly. $\quad$ $\quad$                                           | $\label{eq:expansion} \left\{ \begin{aligned} &\mathfrak{C}^{(1)}=I_n,\, \mathfrak{C}^{(2)}=2L/\lambda_{\mathrm{max}}-I_n,\\ &\mathfrak{C}^{(s)}=2\mathfrak{C}^{(2)}\mathfrak{C}^{(s-1)}-\mathfrak{C}^{(s-2)}\\ &\text{and}\;\;h^{(s)}_{\theta^{(s)}}=\theta^{(s)}\\ &\mathfrak{C}^{(1)}=I_n,\, \mathfrak{C}^{(2)}=2L/\lambda_{\mathrm{max}}-I_n,\\ &\mathfrak{C}^{(s)}=2\mathfrak{C}^{(2)}\mathfrak{C}^{(s-$                                           |
| ChebNet <sup>2</sup> (Defferrard et al., 2016)      | Spectral Conv.  | Learnable         | $% \left\vert \left( \mathbf{r}_{i}\right) \right\rangle$ order of poly. |                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| $GDC3$ (Klicpera et al., 2019)                      | Diffusion       | Fixed             | $\mathbf{1}$                                                             | $C = S$                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Graph Transformers <sup>4</sup> (Rong et al., 2020) | Self-Attention  | Learnable         |                                                                          | $\begin{array}{ll} \mbox{\# of heads} & \left\{ \begin{matrix} \mathfrak{C}_{i,j}^{(s)} = 1 \quad \mbox{and} \quad h_{(W_Q^{(l,s)},W_K^{(l,s)})}^{(s)}(X_{i,:}^{(l)},X_{j,:}^{(l)}) \\ = \exp\big(\frac{1}{\sqrt{d_{i,j}}}(X_{i,:}^{(l)}W_Q^{(l,s)})(X_{j,:}^{(l)}W_K^{(l,s)})^\mathsf{T}\big) \end{matrix} \right. \end{array}$                                                                                                                        |

Table 4: Summary of GNNs formulated as generalized graph convolution.

<sup>1</sup>Where  $\widetilde{A} = A + I_n$ ,  $\widetilde{D} = D + I_n$ . <sup>2</sup>C<sup>(2)</sup> represents mean aggregator. Weight matrix in [\(Hamilton et al.,](#page-32-13) [2017\)](#page-32-13) is  $W^{(l)} = W^{(l,1)} ||$  $W^{(l,2)}$ . <sup>3</sup> Need row-wise normalization.  $C_{i,j}^{(l,s)}$  is non-zero if and only if  $A_{i,j} = 1$ , thus GAT follows direct-neighbor aggregation. <sup>4</sup> The weight matrices of the two convolution supports are the same,  $W^{(l,1)} = W^{(l,2)}$ .

<sup>5</sup> Where normalized Laplacian  $L = I_n - D^{-1/2}AD^{-1/2}$  and  $\lambda_{\text{max}}$  is its largest eigenvalue, which can be approximated as 2 for a large graph. <sup>6</sup> Where S is the diffusion matrix  $S = \sum_{k=0}^{\infty} \theta_k \mathbf{T}^k$ , for example, decaying weights  $\theta_k = e^{-t} \frac{t^k}{k!}$  and transition matrix  $T = \widetilde{D}^{-1/2} \widetilde{A} \widetilde{D}^{-1/2}$ . <sup>7</sup> Need row-wise normalization. Only describes the global self-attention layer, where  $W_Q^{(l,s)}$ ,  $W_Q^{(l,s)} \in \mathbb{R}^{f_l,d_{k,l}}$  are weight matrices which compute the queries and keys vectors. In contrast to GAT, all entries of  $\mathfrak{C}_{i,j}^{(l,s)}$  are non-zero. Different design of Graph Transformers [\(Puny et al.,](#page-34-13) [2020;](#page-34-13) [Rong et al.,](#page-34-12) [2020;](#page-34-12) [Zhang et al.,](#page-35-14) [2020\)](#page-35-14) use graph adjacency information in different ways and is not characterized here; see the original papers for details.

prior-knowledge like what is the format of the synthetic data in S and how the same model  $f_{\theta,\lambda}$  is applied).

<span id="page-18-1"></span>

### C.5 The Linear Convolution Regression Problem on Graph

For the ease of theoretical analysis, in Lemma [5](#page-22-0) and Propositions [2](#page-21-1) to [4](#page-21-2) we consider a simplified linear convolution regression problem as follows,

$$
\theta^{\mathcal{T}} = \arg \min_{\theta = [\alpha, W]} \|C_{\alpha}(A) \ XW - \mathbf{y}\|^2 \tag{7}
$$

where we are given continuous labels y and use sum-of-squares loss  $\ell(\hat{y}, y) = ||\hat{y} - y||_2^2$  instead of the cross entropy loss used for node/pixel classification. We also assume a linear GNN/CNN  $f_{\theta=[\alpha,W]}(A,X) = C_{\alpha}(A)XW$  is used, where  $C_{\alpha}(A)$  is the *convolution matrix* which depends on the adjacency matrix A and the parameters  $\alpha \in \mathbb{R}^p$ , and W is the learnable linear weights with d elements (hence, the complete parameters consist of two parts, i.e.,  $\theta = [\alpha, W]$ ).

As explained in Appendix [C.3,](#page-16-2) this linear convolution model  $f_{\theta=[\alpha,W]}(A,X) = C_{\alpha}(A)XW$  already generalizes a wide variety of GNNs and CNNs. For example, it can represent the (single-layer) graph convolution network (GCN) [\(Kipf and Welling,](#page-33-13) [2016\)](#page-33-13) whose convolution matrix is defined as  $C(A) = \tilde{D}^{-\frac{1}{2}} \tilde{A} \tilde{D}^{-\frac{1}{2}}$  where  $\tilde{A}$  and  $\tilde{D}$  are the "self-loop-added" adjacency and degree matrix (for GCNN there is no learnable parameters in  $C(A)$  so we omit  $\alpha$ ). It also generalizes the one-dimensional convolution neural network (1D-CNN), where the convolution matrix is  $C_{\alpha}(A) = \sum_{k=-K}^{k=K} [\theta]_k P^k$  and P is the cyclic permutation matrix correspond to a unit shift.

It is important to note that although we considered this simplified linear convolution regression problem in some of our theoretical results, which is both convex and linear. We argue that most of the theoretical phenomena reflected by Lemma [5](#page-22-0) and Propositions [2](#page-21-1) to [4](#page-21-2) can be generalized to the general non-convex losses and non-linear models; see Appendix [F.4](#page-27-2) for the corresponding discussions.

<span id="page-19-0"></span>

# D Standard Dataset Condensation is Problematic Across GNNs

In this section, we analyze that standard dataset condensation (SDC) is especially problematic when applied to graphs and GNNs, due to the poor generalizability across GNNs with different convolution filters.

For ease of theoretical discussions, in this subsection, we consider single-layer message-passing GNNs. Message passing GNNs can be interpreted as iterative convolution over nodes (i.e., message passing) [\(Ding et al.,](#page-32-11) [2021\)](#page-32-11) where  $X^{(0)} = X$ ,  $X^{(l+1)} = \sigma(C_{\alpha^{(l)}}(A)X^{(l)}W^{(l)})$  for  $l \in [L]$ , and  $f(A, X) = X^{(L)}$ , where  $C_{\alpha^{(L)}}(A)$  is the convolution matrix parametrized by  $\alpha^{(l)}$ ,  $W^{(l)}$  is the learnable linear weights, and  $\sigma(\cdot)$  denotes the non-linearity. One-dimensional convolution neural networks (1D-CNNs) can be expressed by a similar formula,  $f(X) = (\sum_{k=-K}^{k=K} \alpha^{(k)} P^k) X W$ , parameterized by  $\theta = [\alpha, W]$  where  $\alpha = [\alpha^{(-K)}, \dots, \alpha^{(K)}]$ . P is the cyclic permutation matrix (of a unit shift). The kernel size is  $(2K + 1)$ ,  $K > 0$ ; see Appendix [C.3](#page-16-2) for details.

Despite the success of the gradient matching algorithm in preserving the model performance when trained on the condensed dataset [\(Wang et al.,](#page-35-10) [2022\)](#page-35-10), it naturally overfits the model  $f_{\theta,\lambda}$  used during condensation and generalizes poorly to others. There is no guarantee that the condensed synthetic data  $S^*$  which minimizes the objective (Eq. [\(2\)](#page-3-0)) for a specific model  $f_{\theta,\lambda}$  (marked by its hyperparameter  $\lambda$ ) can generalize well to other models  $f_{\theta,\lambda'}$  where  $\lambda' \neq \lambda$ . We aim to demonstrate that this overfitting issue can be much more severe on graphs than on images, where our main theoretical results can be informally summarized as follows.

**Informal Proposition.** Standard dataset condensation using gradient matching algorithm (Eq.  $(2)$ ) is problematic across GNNs. The condensed graph using a single-layer message passing GNN may fail to generalize to the other GNNs with a different convolution matrix.

We first show the successful generalization of SDC across one-dimensional *convolution neural* networks (1D-CNN). Then, we show a contrary result on GNNs: failed generalization of SDC across GNNs. These theoretical analyses demonstrate the hardness of data condensation on graphs. Our analysis is based on the achievability condition of a gradient matching objective; see Assumption [1](#page-20-0) in Appendix [D.](#page-19-0)

In Lemma [5](#page-22-0) of Appendix [F.1,](#page-22-1) under least square regression with linear GNN/CNN (see Appendix [C.5](#page-18-1) for formal definitions), if the standard dataset condensation GM objective is achievable, then the optimizer on the condensed dataset S is also optimal on the original dataset  $\mathcal T$ . Now, we study the generalizability of the condensed dataset across different models. We first show a successful generalization of SDC across different 1D-CNN networks; see Proposition [2](#page-21-1) in Appendix [D.](#page-19-0) As long as we use a 1D-CNN with a sufficiently large kernel size  $K$  during condensation, we can generalize the condensed dataset to a wide range of models, i.e., 1D-CNNs with a kernel size  $K' \leq K$ .

However, we obtain a contrary result for GNNs regarding the generalizability of condensed datasets across models. Two dominant effects, which cause the failure of the condensed graph's ability to generalize across GNNs, are discovered.

Firstly, the learned adjacency A' of the synthetic graph  $S$  can easily *overfit* the condensation objective (see Proposition [3\)](#page-21-3), and thus can fail to maintain the characteristics of the original structure and distinguish between different architectures; see Proposition [3](#page-21-3) in Appendix [D](#page-19-0) for the theoretical result and Table [5](#page-20-1) for relevant experiments.

<span id="page-20-1"></span>

| Ratio $(c/n)$ A' learned |              | $A'=I_c$                                      |
|--------------------------|--------------|-----------------------------------------------|
| $0.05\%$<br>$0.25\%$     | $63.2 + 0.3$ | $59.2 \pm 1.1$ 61.3 $\pm$ 0.5<br>$64.2\pm0.4$ |

(a) Test accuracy of graph condensation with learned or identity adjacency.

| Condense \ Test | GCN                              | SGC $(K = 2)$                    | GIN                              |
|-----------------|----------------------------------|----------------------------------|----------------------------------|
| GCN             | <b>60.3 <math>\pm 0.3</math></b> | 59.2 $\pm 0.7$                   | 42.2 $\pm 4.3$                   |
| SGC             | 59.2 $\pm 1.1$                   | <b>60.5 <math>\pm 0.6</math></b> | 39.0 $\pm 7.1$                   |
| GIN             | 47.5 $\pm 3.6$                   | 43.6 $\pm 5.8$                   | <b>59.1 <math>\pm 1.1</math></b> |

(b) Generalization accuracy of graphs condensed with different GNNs (row) across GNNs (column) under  $c/n = 0.25\%.$ 

Table 5: Test accuracy of GNNs trained on condensed Ogbn-arxiv [\(Hu et al.,](#page-32-14) [2020\)](#page-32-14) graph verifying the two effects (Propositions [3](#page-21-3) and [4\)](#page-21-2) that hinders the generalization of the condensed graph across GNNs. (a) Condensed adjacency is overfitted to the SDC Objective, (b) Convolution filters and inductive bias mismatch across GNNs.

Image /page/20/Figure/8 description: The image displays two scatter plots side-by-side, each plotting "Validation Accuracy on Original Graph" on the y-axis against "Validation Accuracy on Condensed Graph" on the x-axis. Both plots show two curves, one in orange labeled "λ(1) = 1.0" and another in blue labeled "λ(1) = -1.0". The left plot shows a cyclical or looping relationship between the two accuracy metrics for both lambda values, with the orange curve forming a loop and the blue curve forming a more elongated loop. The right plot shows a more linear, positive correlation between the two accuracy metrics for both lambda values, with the curves closely following each other. Both plots have error bars indicating variability. A red dot is present at the top right of each plot, likely representing a peak performance or a specific point of interest.

(a) Condense ratio  $c/n = 0.2$  (b) Condense ratio  $c/n = 0.8$ 

Figure 6: The manifold of GNNs with convolution filters  $C_{\lambda} =$  $I + \lambda^{(1)}L + \lambda^{(2)}(\frac{2}{\lambda_{\text{max}}}L - I)$  (linear combination of first two orders of ChebNet [\(Defferrard et al.,](#page-31-14) [2016\)](#page-31-14),  $\lambda$ 's are hyperparameters; see Appendices [C.3](#page-16-2) and [J](#page-29-0)) projected to the plane of validation accuracy on condensed (x-axis) and original (y-axis) graphs under two ratios  $c/n$  on Cora [\(Yang et al.,](#page-35-15) [2016\)](#page-35-15). The GNN with  $C = (\frac{2}{\lambda_{\text{max}}} - 1)L \propto L$  (red dot) is a biased point in this model space.

Secondly, GNNs differ from each other mostly on the design of convolution filter  $C(A)$ , i.e., how the convolution weights  $C$  depend on the adjacency information  $A$ . The convolution filter  $C(A)$  used during condensation is a single biased point in "the space of convolutions"; see Fig. [6](#page-20-1) for a visualization, thus there is a mismatch of inductive bias when transferring to a different GNN. These two effects lead to the obstacle when transferring the condensed graph across GNNs, which is formally characterized by Proposition [4](#page-21-2) in Appendix [D.](#page-19-0)

Proposition [4](#page-21-2) provides an effective lower bound on the relative estimation error of optimal model parameters when a different convolution filter  $C'(\cdot) \neq C(\cdot)$  is used.<sup>[1](#page-0-0)</sup> According to the spectral characterization of convolution filters of GNNs (Table 1 of [\(Balcilar et al.,](#page-31-13) [2021\)](#page-31-13)), we can approximately compute the maximum eigenvalue of Q for some GNNs. For example, if we condense with  $f^C$  graph isomorphism network (GIN-0) [\(Xu et al.,](#page-35-12) [2018\)](#page-35-12) but train  $f^{C'}$  GCN on the condensed graph, we have  $||W_{C'}^S - W_{C'}^T||/||W_{C'}^T|| \gtrapprox \overline{\text{deg}} + 1$  where  $\overline{\text{deg}}$  is the average node degree of the original graph. This large lower bound hints at the catastrophic failure when transferring across GIN and GCN; see Table [5.](#page-20-1)

<span id="page-20-0"></span><sup>&</sup>lt;sup>1</sup>If  $C'(\cdot) = C(\cdot)$  Lemma [5](#page-22-0) guarantees  $W_{C'}^{\mathcal{S}} = W_{C'}^{\mathcal{T}}$  and the lower bound in Proposition [4](#page-21-2) is 0.

Assumption 1 (Achievability of a gradient matching Objective). A gradient matching objective is defined to be achievable if there exists a non-degenerate trajectory  $(\theta_t^S)_{t=0}^{T-1}$  (i.e., a trajectory that spans the entire parameter space  $\Theta$ , i.e.,  $\text{span}(\theta_0^S,\ldots,\theta_{T-1}^S) \supseteq \Theta$ ), such that the gradient matching loss (the objective of Eq.  $(2)$  without expectation) on this trajectory is 0.

<span id="page-21-1"></span>**Proposition 2** (Successful Generalization of SDC across 1D-CNNs). Consider least-squares regression with one-dimensional linear convolution  $f^{2K+1}(X)_{\theta} = (\sum_{k=-K}^{k=K} \alpha^{(k)} P^k) XW$  parameterized by  $\theta =$  $[\alpha, W]$  where  $\alpha = [\alpha^{(-K)}, \ldots, \alpha^{(K)}]$ . P is the cyclic permutation matrix (of a unit shift). The kernel size is  $(2K+1), K \geq 0$ . If the gradient matching objective of  $f^{2K+1}$  is achievable, then the condensed dataset  $S^*$  achieves the gradient matching objective on any trajectory  $\{\theta_t^{S}\}_{t=0}^{T-1}$  for any linear convolution  $f_{\theta'}^{2K'+1}$  $e^{2K'+1}$  with kernel size  $(2K'+1), K \geq K' \geq 0$ .

The intuition behind Proposition [2](#page-21-1) is that the 1D-CNN of kernel size  $(2K + 1)$  is a "supernet" of the 1D-CNN of kernel size  $(2K' + 1)$  if  $K' \leq K$ , and the condensed dataset via a bigger model can generalize well to smaller ones. This result suggests us to use a sufficiently large model during condensation, to enable the generalization of the condensed dataset to a wider range of models.

<span id="page-21-3"></span>Proposition 3 (Condensed Adjacency Overfits SDC Objective). Consider least-squares regression with a linear GNN,  $f(A, X) = C(A)XW$  parameterized by W and  $C(A)$  which depends on graph adjacency A. For any (full-ranked) synthetic node features  $X' \in \mathbb{R}^{c \times d}$ , there exists a synthetic adjacency matrix  $A' \in \mathbb{R}_{\geq 0}^{c \times c}$  such that the gradient matching objective is achievable.

<span id="page-21-2"></span>Proposition 4 (Failed Generalization of SDC across GNNs). Consider least-squares regression with a linear GNN,  $f_W^C(A, X) = C(A)XW$  parametrized by W; there always exists a condensed graph  $S^*$ , such that the gradient matching objective for  $f^C$  is achievable. However, if we train a new linear GNN  $f_W^{C'}(A, X)$  with convolution matrix  $C'(A')$  on  $S^*$ , the relative error between the optimized model parameters of  $f_W^{C'}$  on the real and condensed graphs is  $||W_{C'}^{\mathcal{S}} - W_{C'}^{\mathcal{T}}||/||W_{C'}^{\mathcal{T}}|| \geq \max\{\sigma_{\max}(Q) - \sigma_{\min}(Q)\}$  $[1, 1 - \sigma_{\min}(Q)],$  where  $W_{C'}^{\mathcal{T}} = \arg \min_W \| \mathbf{y} - f_W^{C'}(A, X) \|_2^2$ ,  $W_{C'}^{\mathcal{S}} = \arg \min_W \| \mathbf{y}' - f_W^{C'}(A', X') \|_2^2$ , and  $Q = (X^{\top}[C(A)]^{\top}[C(A)]X)(X^{\top}[C'(A)]^{\top}[C'(A)]X)^{-1}$ .

<span id="page-21-0"></span>

# E Hypergradients and Implicit Function Theorem

In this section, we give some additional background on the concepts of hyperparameter gradients (hypergradient for short) and the implicit function theorem (IFT).

We shall recall the notations we used in the main paper, which are summarized in Table [6.](#page-21-4)

<span id="page-21-4"></span>

| $\lambda, \theta$                                                                                                          | Hyperparameters and NN parameters                                                           |
|----------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|
| $\mathcal{T}, \mathcal{S}$                                                                                                 | The original and synthetic datasets                                                         |
| $\mathcal{L}_{\mathcal{T}}^{\text{train}}, \mathcal{L}_{\mathcal{T}}^{\text{val}}$                                         | The training and validation loss on the original dataset                                    |
| $\mathcal{L}_\mathcal{S}^{\text{train}}, \mathcal{L}_\mathcal{S}^{\text{val}}$                                             | The training and validation loss on the synthetic dataset                                   |
| $\theta^{\mathcal{T}}(\lambda) := \arg \min_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda)$            | The optimized parameters on the original dataset, as a function of the hyperparameter       |
| $\theta^{\mathcal{S}}(\lambda) := \arg \min_{\theta} \mathcal{L}_{\mathcal{S}}^{\text{train}}(\theta, \lambda)$            | The optimized parameters on the synthetic dataset, as a function of the hyperparameter      |
| $\mathcal{L}_{\mathcal{T}}^{*}(\lambda) := \mathcal{L}_{\mathcal{T}}^{\text{val}}(\theta^{\mathcal{T}}(\lambda), \lambda)$ | The validation loss on the original dataset, as a function of the hyperparameter $\lambda$  |
| $\mathcal{L}_{\mathcal{S}}^{*}(\lambda) := \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta^{\mathcal{S}}(\lambda), \lambda)$ | The validation loss on the synthetic dataset, as a function of the hyperparameter $\lambda$ |

#### Table 6: Notations.

In Fig. [7,](#page-22-2) we visualize the geometrical process to define the hypergradients. First, in (a), we can plot the training loss  $\mathcal{L}^{\text{train}}_{\mathcal{T}}(\theta, \lambda)$  as a two-variable function on the parameters  $\theta$  and hyperparameter λ. For each hyperparameter  $\lambda \in \Lambda$  (which is assumed to be a continuous interval in this case), we shall optimize the training loss to find an optimal model parameter, which forms the blue curve in  $(\theta, \lambda)$ -plane in (a). Now, we shall substitute the fitted parameter  $\theta^{\mathcal{T}}(\lambda) := \arg \min_{\theta} \mathcal{L}_{\mathcal{T}}^{\text{train}}(\theta, \lambda)$  as a function of the hyperparameter into the validation loss  $\mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta,\lambda)$ . Geometrically, it is projecting the blue implicit optimal parameter curve onto the surface of the validation loss, as shown in (b). The projected orange curve is the validation loss as a one-variable function of the hyperparameter lambda  $\mathcal{L}^*_{\mathcal{T}}(\lambda) := \mathcal{L}^{\text{val}}_{\mathcal{T}}(\theta^{\mathcal{T}}(\lambda), \lambda)$ . Finally, the purple curve represents the hyperparameter gradients, which is the slope of the tangent line on the orange validation loss curve.

<span id="page-22-2"></span>Image /page/22/Figure/1 description: This figure displays two 3D plots illustrating loss landscapes. Plot (a) shows the train loss L\_{T}^{train}(\theta, \lambda) as a function of parameter \theta and hyperparameter \lambda. It highlights the minimum train loss with respect to \theta for different values of \lambda. Plot (b) shows the validation loss L\_{T}^{val}(\theta^{T}(\lambda), \lambda) as a function of the hyperparameter \lambda, where \theta^{T}(\lambda) represents the implicit optimal parameters for a given \lambda. This plot also indicates the hypergradients \nabla\_{\lambda} L\_{T}^{\*}(\lambda) and the relationship L\_{T}^{\*}(\lambda) = L\_{T}^{val}(\theta^{T}(\lambda), \lambda). Both plots feature a blue curve representing implicit optimal parameters \theta^{T}(\lambda) and a black curve tracing the minimum loss along the surface.

Figure 7: Loss landscape w.r.t.  $\theta$  and  $\lambda$ . A hyperparameter  $\lambda$  has an optimal parameter  $\theta^{\mathcal{T}}(\lambda)$  (blue curve in  $(\theta, \lambda)$ -plane in (a)) that minimizes the train loss. In (b), injecting optimal parameters  $\theta^{\mathcal{T}}(\lambda)$  into the validation loss, we obtain a function of validation loss w.r.t.  $\lambda$  (denoted as  $\mathcal{L}^{\star}_{\mathcal{T}}(\lambda)$ ) in  $(\mathcal{L}, \lambda)$ -plane, shown as the orange curve. The purple dash line illustrates the hypergradients, i.e., gradient of  $\mathcal{L}^{\star}_{\mathcal{T}}(\lambda)$  w.r.t.  $\lambda$ .

<span id="page-22-3"></span>

# F More Theoretical Results

In this section, we provide the proofs to the theoretical results Lemma [5](#page-22-0) and Propositions [2](#page-21-1) to [4](#page-21-2) and Theorem [1,](#page-5-2) together with some extended theoretical discussions, including generalizing the *linear* convolution regression problem to non-convex losses and non-linear models (see Appendix [F.4\)](#page-27-2).

To proceed, please recall the linear convolution regression problem defined in Appendix [C.5,](#page-18-1) the achievability of gradient-matching objective (Eq. [\(2\)](#page-3-0)) defined as Assumption [1](#page-20-0) in Appendix [D.](#page-19-0)

<span id="page-22-1"></span>

### F.1 Validity of Standard Dataset Condensation

<span id="page-22-0"></span>As the first step, we verify the validity of the standard dataset condensation (SDC) using the gradient-matching objective Eq. [\(2\)](#page-3-0) for the linear convolution regression problem.

**Lemma 5.** (Validity of SDC) Consider least square regression with linear convolution model  $f_W(A, X) = C(A)XW$  parameterized by W. If the gradient-matching objective of  $f_W$  is achievable, then the optimizer on the condensed dataset S, i.e.,  $W^{\mathcal{S}} = \arg\min_{W} \mathcal{L}_{\mathcal{S}}(W)$  is also optimal for the original dataset, i.e.,  $\mathcal{L}_{\mathcal{T}}(W^{\mathcal{S}}) = \min_{W} \mathcal{L}_{\mathcal{T}}(W)$ .

Proof. In the linear convolution regression problem, sum-of-squares loss is used, and  $\mathcal{L}_{\mathcal{T}}(W) =$  $||CXW - \mathbf{y}||_2^2$  (similarly  $\mathcal{L}_{\mathcal{S}}(W) = ||C'X'W - \mathbf{y}'||_2^2$  where  $C' = C(A')$ ). We assume  $X^\top C^\top C X \in \mathbb{R}^{d \times d}$ is invertible and we can apply the optimizer formula for ordinary least square (OLS) regression to find the optimizer  $W^{\mathcal{T}}$  of  $\mathcal{L}_{\mathcal{T}}(W)$  as,

$$
W^{\mathcal{T}} = (X^{\top} C^{\top} C X)^{-1} X^{\top} C^{\top} \mathbf{y}.
$$

Also, we can compute the gradients of  $\mathcal{L}_{\mathcal{T}}(W)$  w.r.t W as,

$$
\nabla_W \mathcal{L}_{\mathcal{T}}(W) = 2X^\top C^\top (CXW - \mathbf{y}),
$$

and similarly for  $\nabla_W \mathcal{L}_\mathcal{S}(W)$ .

Given the achievability of the gradient-matching objective of  $f_W$ , we know there exists a nondegenerate trajectory  $(W_t^{\mathcal{S}})_{t=0}^{T-1}$  which spans the entire parameter space, i.e., span $(W_0^{\mathcal{S}}, \ldots, W_{T-1}^{\mathcal{S}})$  =  $\mathbb{R}^d$ , such that the gradient-matching loss (the objective of Eq. [\(2\)](#page-3-0) without expectation) on this trajectory is 0. Assuming  $D(\cdot, \cdot)$  is the  $L_2$  norm [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0), this means,

$$
\nabla_W \mathcal{L}_{\mathcal{T}}(W_t^{\mathcal{S}}) = \nabla_W \mathcal{L}_{\mathcal{S}}(W_t^{\mathcal{S}}) \quad \text{for} \quad t \in [T].
$$

Substitute in the formula for the gradients  $\nabla_W \mathcal{L}_{\mathcal{T}}(W)$  and  $\nabla_W \mathcal{L}_{\mathcal{S}}(W)$ , we then have,

$$
X^{\top}C^{\top}(CXW_t^{\mathcal{S}} - \mathbf{y}) = X'^{\top}C'^{\top}(C'X'W_t^{\mathcal{S}} - \mathbf{y}') \text{ for } t \in [T].
$$

Since the set of  $\{W_i^S\}_{t=0}^{T-1}$  spans the complete parameter space  $\mathbb{R}^d$ , we can transform the set of vectors  $\{\omega_t \cdot W_t^S\}_{t=0}^{T-1}$  to the set of unit vectors  $\{\mathbf{e}_i^d\}_{i=0}^{d-1} \in \mathbb{R}^d$  by a linear transformation. Meanwhile, the set of  $T$  equations above can be transformed to,

$$
X^{\top}C^{\top}(CX\mathbf{e}_i^d - \mathbf{y}) = X'^{\top}C'^{\top}(C'X'\mathbf{e}_i^d - \mathbf{y}') \text{ for } i \in [d].
$$

This directly leads to  $X^\top C^\top C X = X'^\top C'^\top C' X'$  and  $X^\top C^\top \mathbf{y} = X'^\top C'^\top \mathbf{y}'$ . Using the formula for the optimizers  $W^{\mathcal{T}}$  and  $W^{\mathcal{S}}$  above, we readily get,

$$
W^{\mathcal{T}} = (X^{\mathsf{T}} C^{\mathsf{T}} C X)^{-1} X^{\mathsf{T}} C^{\mathsf{T}} \mathbf{y} = (X'^{\mathsf{T}} C'^{\mathsf{T}} C' X')^{-1} X'^{\mathsf{T}} C'^{\mathsf{T}} \mathbf{y}' = W^{\mathcal{S}}.
$$

And hence,

$$
\mathcal{L}_{\mathcal{T}}(W^{\mathcal{S}}) = \mathcal{L}_{\mathcal{T}}(W^{\mathcal{T}}) = \min_{W} \mathcal{L}_{\mathcal{T}}(W),
$$

which concludes the proof.

Despite its simplicity, Lemma [5](#page-22-0) directly verifies the validity of the gradient-matching formulation of standard dataset condensation on some specific learning problems. Although the gradient-matching formulation (Eq. [\(2\)](#page-3-0)) is an efficient but weaker formulation than the bilevel formulation of SDC (Eq. [\(SDC\)](#page-3-0)), we see it is strong enough for some of the linear convolution regression problem.

 $\Box$ 

### F.2 Generalization Issues of SDC

Now, we move forward and focus on the generalization issues of (the gradient-matching formulation of) the standard dataset condensation (SDC) across GNNs.

First, we prove the successful generalization of SDC across 1D-CNNs as follows, which is very similar to the proof of Lemma [5.](#page-22-0)

Proof of Proposition [2:](#page-21-1) In Proposition [2,](#page-21-1) we consider one-dimensional linear convolution models  $f^{2K+1}(X) = (\sum_{k=-K}^{k=K} \alpha^{(k)} P^k) X W$  parameterized by  $\alpha \in \mathbb{R}^p$  and  $W \in \mathbb{R}^d$  (where  $p = 2K + 1$ ). If we denote,

$$
C = \sum_{k=-K}^{k=K} \alpha^{(k)} P^k \quad \text{and} \quad \theta = [\alpha, W] \in \mathbb{R}^{p+d}
$$

then from the proof of Lemma [5](#page-22-0) we know the gradients of  $\mathcal{L}_{\mathcal{T}}(W)$  w.r.t W is again,

$$
\nabla_W \mathcal{L}_{\mathcal{T}}(W) = 2X^\top C^\top (CXW - \mathbf{y}).
$$

We know the achievability of the gradient-matching objective means there exists a non-degenerate trajectory  $(\theta_t^S)_{t=0}^{T-1}$  which spans the entire parameter space, i.e.,  $\text{span}(\theta_0^S,\ldots,\theta_{T-1}^S) = \mathbb{R}^{p+d}$ . By decomposing  $\theta_t^{\mathcal{S}}$  into  $[\alpha_t^{\mathcal{S}}, W_t^{\mathcal{S}}]$ , we know that there exists  $(\alpha_t^{\mathcal{S}})_{t=0}^{T-1}$  which spans  $\mathbb{R}^p$  and there exists  $(W_t^{\mathcal{S}})_{t=0}^{T-1}$  which spans  $\mathbb{R}^d$ .

Since the gradient-matching objective is minimized to 0 on  $(W_t^S)_{t=0}^{T-1}$  which spans  $\mathbb{R}^d$ , following the same procedure as the proof of Lemma [5,](#page-22-0) we again obtain,

$$
X^{\top}C^{\top}\mathbf{y} = X'^{\top}C'^{\top}\mathbf{y}'.
$$

Meanwhile, since the same gradient-matching objective is also minimized to 0 on  $(\alpha_t^S)_{t=0}^{T-1}$  which spans  $\mathbb{R}^p$ , we have,

$$
X^{\top} \Big(\sum_{k=-K}^{k=K} (\alpha_t^{\mathcal{S}})^{(k)} P^k \Big)^{\top} \mathbf{y} = X'^{\top} \Big(\sum_{k=-K}^{k=K} (\alpha_t^{\mathcal{S}})^{(k)} P'^k \Big)^{\top} \mathbf{y}' \quad \text{for} \quad t \in [T].
$$

Again by linear combining the above T equations and because  $(\alpha_t^S)_{t=0}^{T-1}$  can be transformed to the unit vectors in  $\mathbb{R}^p$ , we have,

$$
X^{\top}(P^k)^{\top} \mathbf{y} = X'^{\top}(P'^k)^{\top} \mathbf{y}' \text{ for } k = -K, \dots, K.
$$

Hence, for any new trajectory  $(\alpha_t'^S)_{t=0}^{T-1}$  which spans  $\mathbb{R}^{p'}$  where  $p' = 2K' + 1$ , by linear combining the above equations, we have,

$$
X^{\top} \Big( \sum_{k=-K}^{k=K} (\alpha_t^{\prime S})^{(k)} P^k \Big)^{\top} \mathbf{y} = X^{\prime \top} \Big( \sum_{k=-K}^{k=K} (\alpha_t^{\prime S})^{(k)} P^{\prime k} \Big)^{\top} \mathbf{y}^{\prime} \quad \text{for} \quad t \in [T'].
$$

With similar procedure for the  $X<sup>T</sup>C<sup>T</sup>CX$  part, we conclude that on the new trajectory  $(\theta_t^{\prime S})_{t=0}^{T-1}$ 

$$
\nabla_W \mathcal{L}_{\mathcal{T}}(\alpha, W) = \nabla_W \mathcal{L}_{\mathcal{S}}(\alpha, W).
$$

It remains to prove that on any new trajectory  $\nabla_{\alpha} \mathcal{L}_{\mathcal{T}}(\alpha, W) = \nabla_{\alpha} \mathcal{L}_{\mathcal{S}}(\alpha, W)$ . Only need to note that,

$$
\nabla_{\alpha^{(k)}} \mathcal{L}_{\mathcal{T}}(\alpha, W) = 2W^{\top} X^{\top} P^{k} (CXW - \mathbf{y}).
$$

Hence, by the  $p$  equations above, we can readily show,

$$
X^{\top} P^k \mathbf{y} = X'^{\top} P'^k \mathbf{y}' \quad \text{for} \quad k = -K, \dots, K.
$$

Again with a similar procedure for the  $X<sup>T</sup>C<sup>T</sup>CX$  part, we finally can show that on the new trajectory  $(\theta_t^{\prime\mathcal{S}})_{t=0}^{T-1}$ 

$$
\nabla_{\alpha} \mathcal{L}_{\mathcal{T}}(\alpha, W) = \nabla_{\alpha} \mathcal{L}_{\mathcal{S}}(\alpha, W).
$$

This concludes the proof. □

Then we focus on the linear GNNs, we want to verify the insight that the learned adjacency  $A'$  of the condensed graph has "too many degrees of freedom" so that can easily overfit the gradient-matching objective, no matter what learned synthetic features  $X'$  are. Again, the proof of Proposition [3](#page-21-3) uses some results in the proof of Lemma [5.](#page-22-0)

*Proof of Proposition [3:](#page-21-3)* Now, we consider a linear GNN defined as  $f(A, X) = C(A)XW$ . From the proof of Lemma [5,](#page-22-0) we know that for the gradient-matching objective of  $f$  to be achievable, it is equivalent to require that,

$$
X^{\top}C^{\top}CX = X'^{\top}C'^{\top}C'X'
$$
 and  $X^{\top}C^{\top}y = X'^{\top}C'^{\top}y'$ ,

where C and C' refer to  $C(A)$  and  $C(A')$  respectively.

Firstly we note that once we find  $C'$  and  $X'$  such that satisfy the first condition  $X<sup>T</sup>C<sup>T</sup>CX =$  $X'^\top C'X'$ , we can always find  $\mathbf{y}' \in \mathbb{R}^c$  such that  $X^\top C^\top \mathbf{y} = X'^\top C'^\top \mathbf{y}'$  since  $X^\top C^\top \mathbf{y} \in \mathbb{R}$  is a scalar.

Now, we focus on finding the convolution matrix  $C'$  and the node feature matrix  $X'$  of the condensed synthetic graph to satisfy  $X^\top C^\top C X = X'^\top C'^\top C' X'$ . We assume  $n \gg c \gg d$  and consider the diagonalization of  $X^\top C^\top C X \in \mathbb{R}^{d \times d}$ . Since  $X^\top C^\top C X$  is positive semi-definite, it can be diagonalized as  $X^\top C^\top C X = VS^2 V^\top$  where  $V \in \mathbb{R}^d$  is an orthogonal matrix and  $S \in \mathbb{R}^d$  is a diagonal matrix that  $S = diag(s_1, \ldots, s_d)$ .

For any (real) semi-unitary matrix  $U \in \mathbb{R}^{c \times d}$  such that  $U^{\top}U = I_d$ , we can construct  $C'X' =$  $USV^{\top} \in \mathbb{R}^{c \times d}$  and we can easily verify they satisfy the condition,

$$
X'^\top C'^\top C' X' = V S U^\top U S V^\top = V S^2 V^\top = X^\top C^\top C X.
$$

Then since  $X'$  is full ranked, for any  $X'$ , by considering the singular-value decomposition of  $X'$ , we see that we can always find a convolution matrix  $C'$  such that  $C'X' = USV$  and this concludes the proof.  $\Box$ 

Finally, we use some results of Proposition [3](#page-21-3) to prove Proposition [4,](#page-21-2) the failure of SDC when generalizing across GNNs.

*Proof of Proposition [4:](#page-21-2)* We prove this by two steps.

For the first step, we aim to show that there always exist a condensed synthetic dataset  $S$  such that achieves the gradient-matching objective but the learned adjacency matrix  $A' = I_c$  is the identity matrix. Clearly this directly follows form the proof of Proposition [3,](#page-21-3) where we only require  $C'X' = USV$  (see the proof of Proposition [3](#page-21-3) for details). If the learned adjacency matrix  $A' = I_c$ , the for any GNNs, the corresponding convolution matrix  $C'$  is also (or proportional to) identity, thus we only need to set the learned node feature matrix  $X' = USV$  to satisfy the condition. The first step is proved.

![A rectangle with a blurred background.](https://i.imgur.com/0000000.png)

For the second step, we evaluate the relative estimation error of the optimal parameter when transferred to a new GNN  $f_W^{\mathscr{C}}$  with convolution filter  $\mathscr{C}(\cdot)$ , i.e.,  $||W_{\mathscr{C}}^{\mathcal{S}} - W_{\mathscr{C}}^{\mathcal{T}}||/||W_{\mathscr{C}}^{\mathcal{T}}||$ . Using the formula for the optimal parameter in the proof of Lemma [5](#page-22-0) again, we have,

$$
W_{\mathscr{C}}^{\mathcal{T}} = (X^{\top} \mathscr{C}^{\top} \mathscr{C} X)^{-1} X^{\top} \mathscr{C}^{\top} \mathbf{y},
$$

and

$$
W_{\mathscr{C}}^{\mathcal{S}} = (X^{\prime \top} \mathscr{C}^{\prime \top} \mathscr{C}^{\prime} X)^{-1} X^{\prime \top} \mathscr{C}^{\prime \top} \mathbf{y}^{\prime},
$$

where  $\mathscr{C}' = \mathscr{C}(A') = \mathscr{C}(I_c)$  (the last equation use the fact that the convolution matrix of GNNs are the same if the underlying graph is identity).

Moreover, by the validity of SDC on  $f_W^C$ , we know, (see the proof of Lemma [5](#page-22-0) for details),

$$
X'^\top C'^\top C'X' = X^\top C^\top C X
$$
 and  $X'^\top C'^\top \mathbf{y}' = X^\top C^\top \mathbf{y}$ 

Thus, altogether we derive that  $X'^{\top} \mathscr{C}'^{\top} \mathscr{C}' X = X^{\top} C^{\top} C X$  and  $X'^{\top} \mathscr{C}'^{\top} \mathbf{y}' = X^{\top} C^{\top} \mathbf{y}$ . And therefore,

$$
W_{\mathscr{C}}^{\mathcal{S}} = (X^{\top} C^{\top} C X)^{-1} X^{\top} C^{\top} \mathbf{y}.
$$

Now, note that,

$$
\|W_{\mathscr{C}}^{\mathcal{S}} - W_{\mathscr{C}}^{\mathcal{T}}\| / \|W_{\mathscr{C}}^{\mathcal{T}}\|
$$
\n
$$
= \left\| \left( \left( X^{\top} [C(A)]^{\top} [C(A)]X \right) \left( X^{\top} [\mathscr{C}(A)]^{\top} [\mathscr{C}(A)]X \right)^{-1} - I_d \right) X^{\top} C^{\top} \mathbf{y} \right\| / \| X^{\top} C^{\top} \mathbf{y} \|
$$
\n
$$
\geq \max \{ \sigma_{\max}(Q) - 1, 1 - \sigma_{\min}(Q) \}
$$
\n
$$
\left( X^{\top} [C(A)]^{\top} [C(A)]X \right) \left( X^{\top} [C(A)]^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^{\top} [C(A)]X \right)^{-1} \mathbb{F} \left( X^
$$

where  $Q = (X^{\top}[C(A)]^{\top}[C(A)]X)(X^{\top}[\mathscr{C}(A)]^{\top}[\mathscr{C}(A)]X)^{-1}$ . This concludes the proof.

## F.3 Validity of HCDC

Finally, we complete the proof of Theorem [1](#page-5-2) with more detials.

Proof of Theorem [1:](#page-5-2) Firstly, we prove the necessity by contradiction.

If there exists  $\lambda_0 \in \Lambda$  s.t. the two gradient vectors are not aligned at  $\lambda_0$ , then there exists small perturbation  $\Delta\lambda_0$  such that  $\mathcal{L}^*_{\mathcal{T}}(\lambda_0 + \Delta\lambda_0) - \mathcal{L}^*_{\mathcal{T}}(\lambda_0)$  and  $\mathcal{L}^*_{\mathcal{S}}(\lambda_0 + \Delta\lambda_0) - \mathcal{L}^*_{\mathcal{S}}(\lambda_0)$  have different signs.

Secondly, we prove the *sufficiency* by path-integration.

For any pair  $\lambda_1 \neq \lambda_2 \in \Lambda$ , we have a path  $\gamma(\lambda_1, \lambda_2) \in \Lambda$  from  $\lambda_2$  and  $\lambda_1$ , then integrating hypergradients  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^*(\lambda)$  along the path recovers the hyperparameter-calibration condition. More specifically, along the path we have  $\mathcal{L}^*_{\mathcal{T}}(\lambda_1) - \mathcal{L}^*_{\mathcal{T}}(\lambda_2) = \int_{\gamma(\lambda_1,\lambda_2)} \nabla_{\lambda} \mathcal{L}^*_{\mathcal{T}}(\lambda) d\lambda$  (similar for  $\nabla_{\lambda} \mathcal{L}^*_{\mathcal{S}}(\lambda)$ ). Thus we have,

$$
(\mathcal{L}_{\mathcal{T}}^{*}(\lambda_{1}) - \mathcal{L}_{\mathcal{T}}^{*}(\lambda_{2}))(\mathcal{L}_{\mathcal{S}}^{*}(\lambda_{1}) - \mathcal{L}_{\mathcal{S}}^{*}(\lambda_{2}))
$$

$$
= \Big(\int_{\gamma(\lambda_{1},\lambda_{2})} \nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^{*}(\lambda) d\lambda \Big) \Big(\int_{\gamma(\lambda_{1},\lambda_{2})} \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{*}(\lambda) d\lambda \Big)
$$

$$
\geq \int_{\gamma(\lambda_{1},\lambda_{2})} \langle \sqrt{\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}^{*}(\lambda)}, \sqrt{\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{*}(\lambda)} \rangle d\lambda
$$

$$
\geq 0
$$

the second last inequality by Cauchy-Schwarz inquality and the last inequality by  $\cos(\nabla_{\lambda}\mathcal{L}^*_{\mathcal{T}}(\lambda), \nabla_{\lambda}\mathcal{L}^*_{\mathcal{T}}(\lambda)) =$ 0 for any  $\lambda \in \gamma(\lambda_1, \lambda_2) \in \Lambda$ .

This concludes the proof.  $\Box$ 

[]

<span id="page-27-2"></span>

### F.4 Generalize to Non-Convex and Non-Linear Case

Although the results above are obtained for least squares loss and linear convolution model, it still reflects the nature of general non-convex losses and non-linear models. Since dataset condensation is effectively matching the local minima  $\{\theta^{\mathcal{T}}\}$  of the original loss  $\mathcal{L}_{\mathcal{T}}^{train}(\theta,\psi)$  with the local minima  $\{\theta^{\mathcal{S}}\}$  of the condensed loss  $\mathcal{L}_{\mathcal{S}}^{train}(\theta,\psi)$ , within the small neighborhoods surrounding the pair of local minima  $(\theta^{\mathcal{T}}, \theta^{\mathcal{S}})$ , we can approximate the non-convex loss and non-linear model with a convex/linear one respectively. Hence the generalizability issues with convex loss and liner model may hold.

<span id="page-27-1"></span>

# G HCDC on Discrete and Continuous Search Spaces

In this subsection, we illustrate how to tackle the two types of search spaces: (1) discrete and finite  $\Lambda$ and  $(2)$  continuous and bounded  $\Lambda$ , respectively, illustrated by the practical search spaces of ResNets on images and Graph Neural Networks (GNNs) on graphs.

Discrete search space of neural architectures on images. Typically the neural architecture search (NAS) problem aims to find the optimal neural network architecture with the best validation performance on a dataset from a large set of candidate architectures. One may simply train the set of p pre-defined architectures  $\{f^{(i)} \mid i = 1, \ldots, p\}$  and rank their validation performance. We can transform this problem as a continuous HPO, by defining an "interpolated" model (i.e., a super-net [\(Wang et al.,](#page-35-6) [2020\)](#page-35-6))  $f_{\theta}^{\lambda}$ , where hyperparameters  $\lambda = [\lambda^{(1)}, \ldots, \lambda^{(p)}] \in \Lambda$  and  $\theta$  is the model parameters. This technique is known as the differentiable NAS approach (see Appendix [B\)](#page-13-2), e.g., DARTS [\(Liu et al.,](#page-33-2) [2018\)](#page-33-2), which usually follows a cell-based search space [\(Zoph et al.,](#page-36-2) [2018\)](#page-36-2) and continuously relaxes the original discrete search space. Subsequently, in [\(Xie et al.,](#page-35-4) [2018;](#page-35-4) [Dong](#page-32-15) [and Yang,](#page-32-15) [2019\)](#page-32-15), the Gumbel-softmax trick [\(Jang et al.,](#page-32-3) [2017;](#page-32-3) [Maddison et al.,](#page-33-3) [2017\)](#page-33-3) is applied to approximate the hyperparameter gradients. We apply the optimization strategy in GDAS [\(Dong](#page-32-15) [and Yang,](#page-32-15) [2019\)](#page-32-15), which also provides the approximations of the hypergradients in our experiments.

Continuous search space of graph convolution filters. Many graph neural networks (GNNs) can be interpreted as performing message passing on node features, followed by feature transformation and an activation function. In this regard, these GNNs differ from each other by choice of convolution matrix (see Appendix  $D$  for details). We consider the problem of searching for the best convolution filter of GNNs on a graph dataset. In Appendices  $D$  and  $F$ , we theoretically justify that dataset condensation for HPO is challenging on graphs due to overfitting issues. Nonetheless, this obstacle is solved by HCDC.

A natural continuous search space of convolution filters often exists in GNNs, e.g., when the candidate convolution filters can be expressed by a generic formula. For example, we make use of a truncated powers series of the graph Laplacian matrix to model a wide range of convolution filters, as considered in ChebNet [\(Defferrard et al.,](#page-31-14) [2016\)](#page-31-14) or SIGN [\(Frasca et al.,](#page-32-12) [2020\)](#page-32-12). Given the differentiable generic formula of the convolution filters, we can treat the convolution filter (or, more specifically, the parameters in the generic formula) as hyperparameters and evaluate the hypergradients using the implicit differentiation methods discussed in Section [5.1.](#page-6-1)

<span id="page-27-0"></span>

# H Efficient Design of HCDC Algorithm

For continuous hyperparameters,  $\Lambda$  itself is usually compact and connected, and the minimal extended search space is  $\Lambda = \Lambda$ . Consider a discrete search space  $\Lambda$ , which consists of p candidate

hyperparameters. We can naively construct  $\tilde{\Lambda}$  as  $O(p^2)$  continuous paths connecting pairs of candidate hyperparameters (see Fig. [8a](#page-28-0) in Appendix [H](#page-27-0) for illustration). This is undesirable due to its quadratic complexity in  $p$ ; for example, the number of candidate architectures  $p$  is often a large number for practical NAS problems.

We propose a construction of  $\Lambda$  with linear complexity in p, which works as follows. For any  $i \in [p]$ , we construct a "representative" path, named *i*-th HPO trajectory, which starts from  $\lambda_{i,0}^S = \lambda_i \in \Lambda$ and updates through  $\lambda_{i,t+1}^{\mathcal{S}} \leftarrow \lambda_{i,t}^{\mathcal{S}} - \eta \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{*}(\lambda_{i,t}^{\mathcal{S}})$ , shown as the <u>orange</u> dashed lines in Fig. [8a.](#page-28-0) We assume all of the p trajectories will approach the same or equivalent optima  $\lambda^{\mathcal{S}}$ , forming "connected" paths (i.e., orange dashed lines which merge at the optima  $\lambda^{\mathcal{S}}$ ) between any pair of hyperparameters  $\lambda_i \neq \lambda_j \in \Lambda$ . This construction is also used in a continuous search space (as shown in Fig. [8b\)](#page-28-0) to save computation (except that we have to select the starting randomly points  $\lambda_i \sim \mathbb{P}_{\Lambda}$ ).

<span id="page-28-0"></span>Image /page/28/Figure/2 description: The image displays two diagrams illustrating discrete and continuous parameter spaces. Diagram (a), labeled "Discrete Λ = {λi}", shows three blue dots representing discrete parameters λ1, λ2, and λ3, connected by blue lines to form a triangle. Dashed orange arrows originate from each parameter and point towards a purple star labeled "λs", with text "∇λLval" indicating the direction of gradient descent. Diagram (b), labeled "Continuous Λ ∋ λs", depicts a green circle representing a continuous parameter space Λ. Inside the circle, three blue dots labeled "λ1 ~ PΛ", "λ2 ~ PΛ", and "λ3 ~ PΛ" represent parameters sampled from a distribution. Dashed orange arrows originate from these parameters and converge towards a purple star labeled "λs", also indicating gradient descent with "∇λLval".

Figure 8: Illustration of the constructed extended search space  $\Lambda$  illustrated as the orange trajectory for both **a**) discrete  $\Lambda$  and (b) continuous  $\Lambda$ . The trajectory starts from  $\lambda_{i,0}^{\mathcal{S}} = \lambda_i \in \Lambda$  for discrete  $\Lambda$  (or random points for continuous  $\Lambda$ ), and updates through  $\lambda_{i,t+1}^{\mathcal{S}} \leftarrow \lambda_{i,t}^{\mathcal{S}} - \eta \nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^*(\lambda_{i,t}^{\mathcal{S}})$ .

<span id="page-28-1"></span>

# I Complexity Analysis of HCDC Algorithm

In this section, we provide additional details on the complexity analysis of the HCDC algorithm Algorithm [1.](#page-7-1) Following the algorithm pseudocode, we consider the discrete hyperparameter search space of size  $p$ . The overall time complexity of HCDC is proportional to this size  $p$  of the hyperparameter search space.

If we denote the dimensionality of the model parameters  $\theta$  and hyperparameters  $\lambda$  by P and H respectively. We know the time complexity of the common model parameter update is  $O(P)$ . Based on [\(Lorraine et al.,](#page-33-0) [2020\)](#page-33-0), the hyperparameter update Line [7](#page-7-2) needs  $O(P + H)$  time (since we fixed the truncated power of the Neumann series approximation as constant) and  $O(P + H)$ memory. In Line [8,](#page-7-3) we need another backpropagation to take gradients of  $\nabla_{\lambda} \mathcal{L}_{\mathcal{S}}^{\text{val}}(\theta, \lambda)$  w.r.t.  $\mathcal{S}^{\text{val}}$ . This update is performed in a mini-batch manner and supposes the dimensionality of validation samples in the mini-batch is  $B$ , the Line [8](#page-7-3) requries  $O(HB)$  time and memory.

<span id="page-29-0"></span>

# J Implementation Details

In this section, we list more implementation details on the experiments in Section [7.](#page-8-1)

For the synthetic experiments on CIFAR-10, we randomly split the CIFAR-10 images into  $M = 20$ splits and perform cross-validation. For the baseline methods (Random, SDC-GM, SDC-DM), the dataset condensation is performed independently for each split. For HCDC, we first condense the training set of the synthetic dataset by SDC-GM. Then, we learn a separate validation set with  $1/M$ -size of the training set and train with the HCDC objective on the M-HPO trajectories as described in Section [5.](#page-5-0) We report the correlation between the ranking of splits (in terms of their validation performance on this split). For the Early-Stopping method, we only train the same number of iterations as the other methods (with the same batchsize), which means there are only  $c/n * 500$ epochs.

For the experiments about finding the best convolution filter on (large) graphs, we create the set of ten candidate convolution filters as (see Table [4](#page-18-0) for definitions and references) GCN, SAGE-Mean, SAGE-Max, GAT, GIN- $\epsilon$ , GIN-0, SGC(K=2), SGC(K=3), ChebNet(K=2), ChebNet(K-3). The implementations are provided by PyTorch Geometric [https://pytorch-geometric.readthedocs.](https://pytorch-geometric.readthedocs.io/en/latest/modules/nn.html) [io/en/latest/modules/nn.html](https://pytorch-geometric.readthedocs.io/en/latest/modules/nn.html). We also select the GNN width from {128, 256} and the GNN depth from  $\{2, 4\}$  so there are  $10 \times 2 \times 2 = 40$  models in total.

For the experiments about speeding up off-the-shelf graph architecture search algorithms, we adopt GraphNAS [\(Gao et al.,](#page-32-6) [2019\)](#page-32-6) together with their proposed search space from their official repository <https://github.com/GraphNAS/GraphNAS>. We apply to the ogbn-arxiv graph with condensation ratio  $c_{train}/n = 0.5\%.$ 

# K More experiments

Synthetic experiments on CIFAR-10. We first consider a synthetically created set of hyperparameters on the image dataset, CIFAR-10. Consider the M-fold cross-validation, where a fraction of  $1/M$  samples are used as the validation split each time. The M-fold cross-validation process can be modeled by a set of M hyperparameters  $\{\varphi_i \in \{0,1\} \mid i = 1,\ldots,M\}$ , where  $\varphi_i = 1$  if and only if the i-th fold is used for validation. The problem of finding the best validation performance among the M results can be modeled as a hyperparameter optimization problem with a discrete search space  $|\Psi| = M$ . We compare HCDC with the gradient-matching [\(Zhao et al.,](#page-36-0) [2020\)](#page-36-0) and distribution matching [\(Zhao and Bilen,](#page-36-1) [2021b\)](#page-36-1) baselines. We also consider a uniform random sampling baseline and an early-stopping baseline where we train only  $c/n * 500$  epochs but on the original dataset. The results of  $M = 20$  and  $c/n = 1\%$  is reported in Table [7,](#page-30-0) where we see HCDC achieves the highest rank correlation.

|                | Ratio $(c_{\text{train}}/n)$ |       |
|----------------|------------------------------|-------|
| Method         | $2\%$                        | $4\%$ |
| Random         | $-0.03$                      | 0.07  |
| SDC-GM         | 0.64                         | 0.78  |
| SDC-DM         | 0.77                         | 0.86  |
| Early-Stopping | 0.11                         | 0.24  |
| HCDC           | 0.91                         | 0.94  |

<span id="page-30-0"></span>Table 7: The rank correlation and validation performance on the original dataset of the M-fold cross-validation ranked/selected on the condensed dataset on CIFAR-10.

# References

- <span id="page-31-1"></span>Aljundi, R., Lin, M., Goujaud, B. and Bengio, Y. (2019). Gradient based sample selection for online continual learning. Advances in neural information processing systems 32.
- <span id="page-31-9"></span>ANAND, N. and HUANG, P. (2018). Generative modeling for protein structures. Advances in neural information processing systems 31.
- <span id="page-31-7"></span>Baker, D., Braverman, V., Huang, L., Jiang, S. H.-C., Krauthgamer, R. and Wu, X. (2020). Coresets for clustering in graphs of bounded treewidth. In International Conference on Machine Learning. PMLR.
- <span id="page-31-13"></span>Balcilar, M., Guillaume, R., Héroux, P., Gaüzère, B., Adam, S. and Honeine, P. (2021). Analyzing the expressive power of graph neural networks in a spectral perspective. In *Proceedings* of the International Conference on Learning Representations (ICLR).
- <span id="page-31-11"></span>BATSON, J., SPIELMAN, D. A., SRIVASTAVA, N. and TENG, S.-H. (2013). Spectral sparsification of graphs: theory and algorithms. Communications of the ACM 56 87–94.
- <span id="page-31-6"></span>BENGIO, Y. (2000). Gradient-based optimization of hyperparameters. Neural computation 12 1889–1900.
- <span id="page-31-3"></span>BOHDAL, O., YANG, Y. and HOSPEDALES, T. (2020). Flexible dataset distillation: Learn labels instead of images. arXiv preprint arXiv:2006.08572 .
- <span id="page-31-5"></span>Borsos, Z., Mutny, M. and Krause, A. (2020). Coresets via bilevel optimization for continual learning and streaming. Advances in Neural Information Processing Systems 33 14879–14890.
- <span id="page-31-8"></span>BRAVERMAN, V., JIANG, S. H.-C., KRAUTHGAMER, R. and WU, X. (2021). Coresets for clustering in excluded-minor graphs and beyond. In Proceedings of the 2021 ACM-SIAM Symposium on Discrete Algorithms (SODA). SIAM.
- <span id="page-31-12"></span>CAI, C., WANG, D. and WANG, Y. (2020). Graph coarsening with neural networks. In *International* Conference on Learning Representations.
- <span id="page-31-4"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A. and Zhu, J.-Y. (2022). Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition.
- <span id="page-31-2"></span>CHEN, Y., WELLING, M. and SMOLA, A. (2010). Super-samples from kernel herding. In *Proceedings* of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence.
- <span id="page-31-10"></span>Chiang, W.-L., Liu, X., Si, S., Li, Y., Bengio, S. and Hsieh, C.-J. (2019). Cluster-gcn: An efficient algorithm for training deep and large graph convolutional networks. In Proceedings of the 25th ACM SIGKDD international conference on knowledge discovery & data mining.
- <span id="page-31-0"></span>Cui, J., Wang, R., Si, S. and Hsieh, C.-J. (2022). Dc-bench: Dataset condensation benchmark. arXiv preprint arXiv:2207.09639 .
- <span id="page-31-14"></span>DEFFERRARD, M., BRESSON, X. and VANDERGHEYNST, P. (2016). Convolutional neural networks on graphs with fast localized spectral filtering. In Advances in neural information processing systems, vol. 29.

- <span id="page-32-11"></span>DING, M., KONG, K., LI, J., ZHU, C., DICKERSON, J., HUANG, F. and GOLDSTEIN, T. (2021). Vq-gnn: A universal framework to scale up graph neural networks using vector quantization. Advances in Neural Information Processing Systems 34 6733–6746.
- <span id="page-32-2"></span>DOMKE, J. (2012). Generic methods for optimization-based modeling. In Artificial Intelligence and Statistics. PMLR.
- <span id="page-32-15"></span>Dong, X. and Yang, Y. (2019). Searching for a robust neural architecture in four gpu hours. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition.
- <span id="page-32-4"></span>Dong, X. and Yang, Y. (2020). Nas-bench-201: Extending the scope of reproducible neural architecture search.  $\emph{arXiv preprint}$   $\emph{arXiv:} 2001.00326$  .
- <span id="page-32-0"></span>ELSKEN, T., METZEN, J. H. and HUTTER, F. (2019). Neural architecture search: A survey. The Journal of Machine Learning Research 20 1997–2017.
- <span id="page-32-5"></span>FARAHANI, R. Z. and HEKMATFAR, M. (2009). Facility location: concepts, models, algorithms and case studies. Springer Science & Business Media.
- <span id="page-32-1"></span>FEURER, M. and HUTTER, F. (2019). Hyperparameter optimization. In Automated machine learning. Springer, Cham, 3–33.
- <span id="page-32-12"></span>Frasca, F., Rossi, E., Eynard, D., Chamberlain, B., Bronstein, M. and Monti, F. (2020). Sign: Scalable inception graph neural networks.  $arXiv$  preprint  $arXiv:2004.11198$ .
- <span id="page-32-6"></span>Gao, Y., Yang, H., Zhang, P., Zhou, C. and Hu, Y. (2019). Graphnas: Graph neural architecture search with reinforcement learning. arXiv preprint arXiv:1904.09981.
- <span id="page-32-8"></span>Guo, C., Zhao, B. and Bai, Y. (2022). Deepcore: A comprehensive library for coreset selection in deep learning. arXiv preprint arXiv:2204.08499 .
- <span id="page-32-13"></span>HAMILTON, W., YING, Z. and LESKOVEC, J. (2017). Inductive representation learning on large graphs. Advances in neural information processing systems 30.
- <span id="page-32-14"></span>Hu, W., Fey, M., Zitnik, M., Dong, Y., Ren, H., Liu, B., Catasta, M. and Leskovec, J. (2020). Open graph benchmark: Datasets for machine learning on graphs. Advances in neural information processing systems 33 22118–22133.
- <span id="page-32-9"></span>Huan, Z., Quanming, Y. and Weiwei, T. (2021). Search to aggregate neighborhood for graph neural network. In 2021 IEEE 37th International Conference on Data Engineering (ICDE). IEEE.
- <span id="page-32-10"></span>Huang, Z., Zhang, S., Xi, C., Liu, T. and Zhou, M. (2021). Scaling up graph neural networks via graph coarsening. In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining.
- <span id="page-32-7"></span>Iyer, R., Khargoankar, N., Bilmes, J. and Asanani, H. (2021). Submodular combinatorial information measures with applications in machine learning. In Algorithmic Learning Theory. PMLR.
- <span id="page-32-3"></span>Jang, E., Gu, S. and Poole, B. (2017). Categorical reparameterization with gumbel-softmax. In International Conference on Learning Representations.

- <span id="page-33-10"></span>Jin, W., Tang, X., Jiang, H., Li, Z., Zhang, D., Tang, J. and Yin, B. (2022). Condensing graphs via one-step gradient matching. In Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining.
- <span id="page-33-4"></span>Jin, W., Zhao, L., Zhang, S., Liu, Y., Tang, J. and Shah, N. (2021). Graph condensation for graph neural networks. In International Conference on Learning Representations.
- <span id="page-33-1"></span>Kim, J.-H., Kim, J., Oh, S. J., Yun, S., Song, H., Jeong, J., Ha, J.-W. and Song, H. O. (2022). Dataset condensation via efficient synthetic-data parameterization. In International Conference on Machine Learning. PMLR.
- <span id="page-33-13"></span>Kipf, T. N. and Welling, M. (2016). Semi-supervised classification with graph convolutional networks. In International Conference on Learning Representations.
- <span id="page-33-14"></span>KLICPERA, J., WEISSENBERGER, S. and GÜNNEMANN, S. (2019). Diffusion improves graph learning. In Advances in neural information processing systems. PMLR.
- <span id="page-33-5"></span>Kothawade, S., Kaushal, V., Ramakrishnan, G., Bilmes, J. and Iyer, R. (2022). Prism: A rich class of parameterized submodular information measures for guided data subset selection. In Proceedings of the AAAI Conference on Artificial Intelligence, vol. 36.
- <span id="page-33-6"></span>LARSEN, J., HANSEN, L. K., SVARER, C. and OHLSSON, M. (1996). Design and regularization of neural networks: the optimal use of a validation set. In Neural Networks for Signal Processing VI. Proceedings of the 1996 IEEE Signal Processing Society Workshop. IEEE.
- <span id="page-33-8"></span>Li, L. and Talwalkar, A. (2020). Random search and reproducibility for neural architecture search. In Uncertainty in artificial intelligence. PMLR.
- <span id="page-33-2"></span>Liu, H., Simonyan, K. and Yang, Y. (2018). Darts: Differentiable architecture search. In International Conference on Learning Representations.
- <span id="page-33-9"></span>Liu, M., Li, S., Chen, X. and Song, L. (2022). Graph condensation via receptive field distribution matching. *arXiv preprint arXiv:2206.13697*.
- <span id="page-33-0"></span>LORRAINE, J., VICOL, P. and DUVENAUD, D. (2020). Optimizing millions of hyperparameters by implicit differentiation. In International Conference on Artificial Intelligence and Statistics. PMLR.
- <span id="page-33-12"></span>LOUKAS, A. (2019). Graph reduction with spectral and cut guarantees. J. Mach. Learn. Res. 20 1–42.
- <span id="page-33-11"></span>LOUKAS, A. and VANDERGHEYNST, P. (2018). Spectrally approximating large graphs with smaller graphs. In International Conference on Machine Learning. PMLR.
- <span id="page-33-7"></span>LUKETINA, J., BERGLUND, M., GREFF, K. and RAIKO, T. (2016). Scalable gradient-based tuning of continuous regularization hyperparameters. In International conference on machine learning. PMLR.
- <span id="page-33-3"></span>MADDISON, C. J., MNIH, A. and TEH, Y. W. (2017). The concrete distribution: A continuous relaxation of discrete random variables. In International Conference on Learning Representations.

- <span id="page-34-9"></span>Martens, J. and Grosse, R. (2015). Optimizing neural networks with kronecker-factored approximate curvature. In International conference on machine learning. PMLR.
- <span id="page-34-2"></span>Nguyen, T., Chen, Z. and Lee, J. (2020). Dataset meta-learning from kernel ridge-regression. In International Conference on Learning Representations.
- <span id="page-34-3"></span>Nguyen, T., Novak, R., Xiao, L. and Lee, J. (2021). Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34 5186–5198.
- <span id="page-34-7"></span>OCHS, P., RANFTL, R., BROX, T. and POCK, T. (2015). Bilevel optimization with nonsmooth lower level problems. In International Conference on Scale Space and Variational Methods in Computer Vision. Springer.
- <span id="page-34-0"></span>PAUL, M., GANGULI, S. and DZIUGAITE, G. K. (2021). Deep learning on a data diet: Finding important examples early in training. Advances in Neural Information Processing Systems 34 20596–20607.
- <span id="page-34-8"></span>PEDREGOSA, F. (2016). Hyperparameter optimization with approximate gradient. In *International* conference on machine learning. PMLR.
- <span id="page-34-13"></span>Puny, O., Ben-Hamu, H. and Lipman, Y. (2020). From graph low-rank global attention to 2-fwl approximation. In International Conference on Machine Learning. PMLR.
- <span id="page-34-1"></span>Rebuffi, S.-A., Kolesnikov, A., Sperl, G. and Lampert, C. H. (2017). icarl: Incremental classifier and representation learning. In Proceedings of the IEEE conference on Computer Vision and Pattern Recognition.
- <span id="page-34-12"></span>Rong, Y., Bian, Y., Xu, T., Xie, W., Wei, Y., Huang, W. and Huang, J. (2020). Selfsupervised graph transformer on large-scale molecular data. In Advances in neural information processing systems, vol. 33.
- <span id="page-34-11"></span>SATULURI, V., PARTHASARATHY, S. and RUAN, Y. (2011). Local graph sparsification for scalable clustering. In Proceedings of the 2011 ACM SIGMOD International Conference on Management of data.
- <span id="page-34-6"></span>Sener, O. and Savarese, S. (2018). Active learning for convolutional neural networks: A core-set approach. In International Conference on Learning Representations.
- <span id="page-34-5"></span>SHABAN, A., CHENG, C.-A., HATCH, N. and BOOTS, B. (2019). Truncated back-propagation for bilevel optimization. In The 22nd International Conference on Artificial Intelligence and Statistics. PMLR.
- <span id="page-34-10"></span>Simonovsky, M. and Komodakis, N. (2018). Graphvae: Towards generation of small graphs using variational autoencoders. In International conference on artificial neural networks. Springer.
- <span id="page-34-4"></span>SUCH, F. P., RAWAL, A., LEHMAN, J., STANLEY, K. and CLUNE, J. (2020). Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In International Conference on Machine Learning. PMLR.

- <span id="page-35-1"></span>Toneva, M., Sordoni, A., des Combes, R. T., Trischler, A., Bengio, Y. and Gordon, G. J. (2018). An empirical study of example forgetting during deep neural network learning. In International Conference on Learning Representations.
- <span id="page-35-13"></span>Veličković, P., Cucurull, G., Casanova, A., Romero, A., Lio, P. and Bengio, Y. (2018). Graph attention networks. In International Conference on Learning Representations.
- <span id="page-35-10"></span>Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X. and YOU, Y. (2022). Cafe: Learning to condense dataset by aligning features. In *Proceedings* of the IEEE/CVF Conference on Computer Vision and Pattern Recognition.
- <span id="page-35-6"></span>Wang, R., Cheng, M., Chen, X., Tang, X. and Hsieh, C.-J. (2020). Rethinking architecture selection in differentiable nas. In International Conference on Learning Representations.
- <span id="page-35-0"></span>WANG, T., ZHU, J.-Y., TORRALBA, A. and EFROS, A. A. (2018). Dataset distillation.  $arXiv$ preprint arXiv:1811.10959 .
- <span id="page-35-3"></span>Wang, Y., Zhang, G. and Ba, J. (2019). On solving minimax optimization locally: A follow-theridge approach. In International Conference on Learning Representations.
- <span id="page-35-9"></span>WANG, Z., DI, S. and CHEN, L. (2021). Autogel: An automated graph neural network with explicit link information. Advances in Neural Information Processing Systems 34 24509–24522.
- <span id="page-35-5"></span>WELLING, M. (2009). Herding dynamical weights to learn. In Proceedings of the 26th Annual International Conference on Machine Learning.
- <span id="page-35-7"></span>WILLIAMS, R. J. (1992). Simple statistical gradient-following algorithms for connectionist reinforcement learning. Reinforcement learning 5–32.
- <span id="page-35-4"></span>Xie, S., Zheng, H., Liu, C. and Lin, L. (2018). Snas: stochastic neural architecture search. In International Conference on Learning Representations.
- <span id="page-35-12"></span>Xu, K., Hu, W., Leskovec, J. and Jegelka, S. (2018). How powerful are graph neural networks? In International Conference on Learning Representations.
- <span id="page-35-15"></span>YANG, Z., COHEN, W. and SALAKHUDINOV, R. (2016). Revisiting semi-supervised learning with graph embeddings. In International conference on machine learning. PMLR.
- <span id="page-35-8"></span>ZELA, A., ELSKEN, T., SAIKIA, T., MARRAKCHI, Y., BROX, T. and HUTTER, F. (2019). Understanding and robustifying differentiable architecture search. In International Conference on Learning Representations.
- <span id="page-35-11"></span>Zeng, H., Zhou, H., Srivastava, A., Kannan, R. and Prasanna, V. (2019). Graphsaint: Graph sampling based inductive learning method. In *International Conference on Learning* Representations.
- <span id="page-35-14"></span>Zhang, J., Zhang, H., Xia, C. and Sun, L. (2020). Graph-bert: Only attention is needed for learning graph representations. arXiv preprint arXiv:2001.05140.
- <span id="page-35-2"></span>Zhao, B. and Bilen, H. (2021a). Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning. PMLR.

- <span id="page-36-1"></span>ZHAO, B. and BILEN, H. (2021b). Dataset condensation with distribution matching. arXiv preprint  $arXiv:2110.04181$ .
- <span id="page-36-0"></span>ZHAO, B., MOPURI, K. R. and BILEN, H. (2020). Dataset condensation with gradient matching. In International Conference on Learning Representations.
- <span id="page-36-2"></span>ZOPH, B., VASUDEVAN, V., SHLENS, J. and LE, Q. V. (2018). Learning transferable architectures for scalable image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition.