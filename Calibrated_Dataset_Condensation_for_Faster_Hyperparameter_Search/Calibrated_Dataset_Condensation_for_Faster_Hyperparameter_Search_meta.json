{"table_of_contents": [{"title": "Calibrated Dataset Condensation for Faster\nHyperparameter Search", "heading_level": null, "page_id": 0, "polygon": [[121.5, 114.0], [490.67578125, 114.0], [490.67578125, 159.134765625], [121.5, 159.134765625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[283.5, 248.25], [327.75, 248.25], [327.75, 259.294921875], [283.5, 259.294921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.037841796875, 442.5], [182.25, 442.5], [182.25, 455.94140625], [70.037841796875, 455.94140625]]}, {"title": "2 Standard Dataset Condensation", "heading_level": null, "page_id": 2, "polygon": [[70.5, 405.66796875], [316.7578125, 405.66796875], [316.7578125, 418.04296875], [70.5, 418.04296875]]}, {"title": "3 Hyperparameter Calibrated Dataset Condensation", "heading_level": null, "page_id": 3, "polygon": [[70.5, 560.25], [447.0, 560.25], [447.0, 573.50390625], [70.5, 573.50390625]]}, {"title": "4 Hyperparameter Calibration via Hypergradient Alignment", "heading_level": null, "page_id": 4, "polygon": [[70.5, 489.0], [501.75, 487.5], [501.75, 501.9609375], [70.5, 501.9609375]]}, {"title": "5 Implementations of HCDC", "heading_level": null, "page_id": 5, "polygon": [[70.5, 600.75], [285.0, 600.75], [285.0, 613.72265625], [70.5, 613.72265625]]}, {"title": "5.1 Efficient Evaluation of Hypergradients", "heading_level": null, "page_id": 6, "polygon": [[70.5, 74.25], [328.5, 74.25], [328.5, 85.51318359375], [70.5, 85.51318359375]]}, {"title": "5.2 Efficient Design of Extended Search Space", "heading_level": null, "page_id": 6, "polygon": [[70.5, 552.0], [351.75, 552.0], [351.75, 563.0625], [70.5, 563.0625]]}, {"title": "5.3 Pseudocode", "heading_level": null, "page_id": 7, "polygon": [[70.5, 157.5], [173.25, 157.5], [173.25, 168.5126953125], [70.5, 168.5126953125]]}, {"title": "6 Related Work", "heading_level": null, "page_id": 7, "polygon": [[70.5, 503.89453125], [192.5947265625, 503.89453125], [192.5947265625, 516.26953125], [70.5, 516.26953125]]}, {"title": "7 Experiments", "heading_level": null, "page_id": 8, "polygon": [[71.25, 547.59375], [182.25, 547.59375], [182.25, 560.7421875], [71.25, 560.7421875]]}, {"title": "8 Conclusion", "heading_level": null, "page_id": 12, "polygon": [[70.5, 201.673828125], [171.75, 201.673828125], [171.75, 214.822265625], [70.5, 214.822265625]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 12, "polygon": [[70.5, 382.5], [195.8818359375, 382.5], [195.8818359375, 395.419921875], [70.5, 395.419921875]]}, {"title": "A A Detailed Diagram of the Proposed HCDC", "heading_level": null, "page_id": 13, "polygon": [[70.5, 72.509765625], [407.25, 72.509765625], [407.25, 86.431640625], [70.5, 86.431640625]]}, {"title": "B More Related Work", "heading_level": null, "page_id": 13, "polygon": [[70.5, 522.0], [237.0, 522.0], [237.0, 535.21875], [70.5, 535.21875]]}, {"title": "B.1 Dataset Condensation and Coreset Selection", "heading_level": null, "page_id": 13, "polygon": [[70.5, 591.0], [367.5, 591.0], [367.5, 602.12109375], [70.5, 602.12109375]]}, {"title": "B.2 Implicit Differentiation and Differentiable NAS", "heading_level": null, "page_id": 14, "polygon": [[70.5, 468.703125], [384.0, 468.703125], [384.0, 479.53125], [70.5, 479.53125]]}, {"title": "B.3 Graph Reduction", "heading_level": null, "page_id": 15, "polygon": [[70.5, 225.0], [208.5, 225.0], [208.5, 236.091796875], [70.5, 236.091796875]]}, {"title": "C Preliminaries on Graph Hyperparameter Search", "heading_level": null, "page_id": 16, "polygon": [[70.5, 72.46142578125], [432.75, 72.46142578125], [432.75, 85.89990234375], [70.5, 85.89990234375]]}, {"title": "C.1 Node Classification and GNNs", "heading_level": null, "page_id": 16, "polygon": [[70.5, 154.30078125], [286.5, 154.30078125], [286.5, 165.708984375], [70.5, 165.708984375]]}, {"title": "C.2 Additional Downstream Tasks", "heading_level": null, "page_id": 16, "polygon": [[70.5, 302.25], [282.75, 301.5], [282.75, 313.435546875], [70.5, 313.435546875]]}, {"title": "C.3 Graph Neural Network Models", "heading_level": null, "page_id": 16, "polygon": [[70.5, 554.25], [289.5, 554.25], [289.5, 565.76953125], [70.5, 565.76953125]]}, {"title": "C.4 HCDC is Applicable to Various Data, Tasks, and Models", "heading_level": null, "page_id": 17, "polygon": [[70.5, 498.48046875], [444.75, 498.48046875], [444.75, 509.30859375], [70.5, 509.30859375]]}, {"title": "C.5 The Linear Convolution Regression Problem on Graph", "heading_level": null, "page_id": 18, "polygon": [[70.5, 526.5], [429.416015625, 526.5], [429.416015625, 537.5390625], [70.5, 537.5390625]]}, {"title": "D Standard Dataset Condensation is Problematic Across GNNs", "heading_level": null, "page_id": 19, "polygon": [[70.5, 246.146484375], [525.75, 246.146484375], [525.75, 259.294921875], [70.5, 259.294921875]]}, {"title": "E Hypergradients and Implicit Function Theorem", "heading_level": null, "page_id": 21, "polygon": [[70.3740234375, 472.5], [426.75, 472.5], [426.75, 485.71875], [70.3740234375, 485.71875]]}, {"title": "Table 6: Notations.", "heading_level": null, "page_id": 21, "polygon": [[261.7734375, 549.0], [349.03125, 549.0], [349.03125, 560.35546875], [261.7734375, 560.35546875]]}, {"title": "F More Theoretical Results", "heading_level": null, "page_id": 22, "polygon": [[70.44873046875, 537.75], [273.75, 537.75], [273.75, 551.84765625], [70.44873046875, 551.84765625]]}, {"title": "F.1 Validity of Standard Dataset Condensation", "heading_level": null, "page_id": 22, "polygon": [[70.5, 647.25], [358.5, 647.25], [358.5, 658.1953125], [70.5, 658.1953125]]}, {"title": "F.2 Generalization Issues of SDC", "heading_level": null, "page_id": 24, "polygon": [[70.5, 74.25], [276.416015625, 74.25], [276.416015625, 85.60986328125], [70.5, 85.60986328125]]}, {"title": "F.3 Validity of HCDC", "heading_level": null, "page_id": 26, "polygon": [[70.5, 393.0], [210.75, 391.5], [210.75, 404.12109375], [70.5, 404.25]]}, {"title": "F.4 Generalize to Non-Convex and Non-Linear Case", "heading_level": null, "page_id": 27, "polygon": [[70.5, 74.25], [389.25, 74.25], [389.25, 85.41650390625], [70.5, 85.41650390625]]}, {"title": "G HCDC on Discrete and Continuous Search Spaces", "heading_level": null, "page_id": 27, "polygon": [[70.5, 193.5], [449.25, 193.5], [449.25, 207.474609375], [70.5, 207.474609375]]}, {"title": "H Efficient Design of HCDC Algorithm", "heading_level": null, "page_id": 27, "polygon": [[70.5, 633.0], [354.75, 633.0], [354.75, 646.59375], [70.5, 646.59375]]}, {"title": "I Complexity Analysis of HCDC Algorithm", "heading_level": null, "page_id": 28, "polygon": [[70.5, 519.75], [383.25, 519.75], [383.25, 532.8984375], [70.5, 532.8984375]]}, {"title": "J Implementation Details", "heading_level": null, "page_id": 29, "polygon": [[70.5, 72.46142578125], [258.75, 72.46142578125], [258.75, 85.89990234375], [70.5, 85.89990234375]]}, {"title": "<PERSON> experiments", "heading_level": null, "page_id": 29, "polygon": [[70.5, 388.265625], [225.75, 388.265625], [225.75, 401.4140625], [70.5, 401.4140625]]}, {"title": "References", "heading_level": null, "page_id": 31, "polygon": [[70.5, 72.0], [146.25, 72.0], [146.25, 86.18994140625], [70.5, 86.18994140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 38], ["Text", 7], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 10003, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 54], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 677, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 484], ["Line", 56], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 608], ["Line", 75], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 91], ["TextInlineMath", 6], ["Reference", 4], ["Equation", 3], ["Text", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 62], ["Text", 5], ["TextInlineMath", 5], ["Reference", 3], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 626], ["Line", 80], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Equation", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 789], ["Line", 44], ["Text", 4], ["Reference", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["TableCell", 135], ["Line", 44], ["Text", 4], ["Reference", 2], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9405, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1085], ["TableCell", 344], ["Line", 51], ["Caption", 2], ["Table", 2], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 13720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 47], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 38], ["Caption", 2], ["Text", 2], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1335, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 27], ["SectionHeader", 2], ["Text", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 58], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 904, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 45], ["Text", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 42], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 41], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 3], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 626], ["Line", 58], ["TextInlineMath", 7], ["Equation", 2], ["Reference", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1034], ["Line", 148], ["TableCell", 50], ["TextInlineMath", 3], ["Text", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 43], ["TextInlineMath", 6], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 637], ["Line", 62], ["TableCell", 38], ["Text", 4], ["Caption", 4], ["TextInlineMath", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2203, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 836], ["Line", 80], ["TableCell", 32], ["Reference", 5], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4699, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 38], ["Reference", 4], ["TextInlineMath", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 739, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["Line", 74], ["TextInlineMath", 10], ["Equation", 7], ["Text", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 828], ["Line", 111], ["TextInlineMath", 8], ["Equation", 8], ["Text", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 745], ["Line", 56], ["Text", 7], ["TextInlineMath", 7], ["Equation", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 599, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 911], ["Line", 113], ["Text", 8], ["TextInlineMath", 7], ["Equation", 6], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 41], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 39], ["TextInlineMath", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 721, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 34], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["TableCell", 41], ["Line", 10], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 38], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 37], ["ListItem", 16], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 37], ["ListItem", 15], ["Reference", 15], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 36], ["ListItem", 14], ["Reference", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 37], ["ListItem", 16], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Line", 8], ["ListItem", 3], ["Reference", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Calibrated_Dataset_Condensation_for_Faster_Hyperparameter_Search"}