{"table_of_contents": [{"title": "CAN PRE-TRAINED <PERSON><PERSON><PERSON><PERSON> ASSIST IN DATASET DIS-\nTILLATION?", "heading_level": null, "page_id": 0, "polygon": [[106.5, 81.75], [506.513671875, 80.25], [506.513671875, 115.435546875], [106.5, 115.435546875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 210.75], [335.28515625, 212.25], [335.28515625, 223.13671875], [276.75, 223.13671875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 435.75], [207.087890625, 435.75], [207.087890625, 447.046875], [107.25, 447.046875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[107.25, 326.970703125], [226.5, 326.970703125], [226.5, 338.958984375], [107.25, 338.958984375]]}, {"title": "3 BACKGROUND AND <PERSON>X<PERSON><PERSON><PERSON><PERSON><PERSON>L SETUP", "heading_level": null, "page_id": 1, "polygon": [[107.25, 621.75], [346.04296875, 621.75], [346.04296875, 633.05859375], [107.25, 633.05859375]]}, {"title": "3.1 BACKGROUND", "heading_level": null, "page_id": 1, "polygon": [[107.1298828125, 646.98046875], [196.1806640625, 646.98046875], [196.1806640625, 657.03515625], [107.1298828125, 657.03515625]]}, {"title": "3.2 EXPERIMENTAL SETUP", "heading_level": null, "page_id": 2, "polygon": [[107.25, 388.5], [229.5, 388.5], [229.5, 398.70703125], [107.25, 398.70703125]]}, {"title": "4 CAN PRE-TRAINED <PERSON><PERSON><PERSON>S ASSIST IN DATASET DISTILLATION?", "heading_level": null, "page_id": 2, "polygon": [[106.681640625, 658.5], [450.03515625, 658.5], [450.03515625, 668.63671875], [106.681640625, 668.63671875]]}, {"title": "5 EMPRICAL STUDY", "heading_level": null, "page_id": 3, "polygon": [[107.25, 536.25], [220.3857421875, 536.25], [220.3857421875, 547.59375], [107.25, 547.59375]]}, {"title": "5.1 MODEL DIVERSIFICATION", "heading_level": null, "page_id": 3, "polygon": [[106.98046875, 618.75], [244.44140625, 618.75], [244.44140625, 628.8046875], [106.98046875, 628.8046875]]}, {"title": "5.2 TRAINING EPOCH", "heading_level": null, "page_id": 4, "polygon": [[106.5, 639.75], [208.5, 639.75], [208.5, 650.07421875], [106.5, 650.07421875]]}, {"title": "5.3 DOMAIN KNOWLEDGE", "heading_level": null, "page_id": 5, "polygon": [[107.20458984375, 208.5], [228.75, 208.5], [228.75, 218.302734375], [107.20458984375, 218.302734375]]}, {"title": "5.4 VALIDATION AND EXTENSIONS", "heading_level": null, "page_id": 7, "polygon": [[107.25, 303.0], [263.56640625, 303.57421875], [263.56640625, 313.2421875], [107.25, 313.2421875]]}, {"title": "6 ABLATION STUDY", "heading_level": null, "page_id": 7, "polygon": [[107.1298828125, 633.75], [220.0869140625, 633.75], [220.0869140625, 645.046875], [107.1298828125, 645.046875]]}, {"title": "7 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 573.0], [196.03125, 573.0], [196.03125, 583.9453125], [107.25, 583.9453125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 195.75], [176.1591796875, 195.75], [176.1591796875, 205.927734375], [107.25, 205.927734375]]}, {"title": "Algorithm 1 PyTorch-like pseudo-code for CCLoM.", "heading_level": null, "page_id": 13, "polygon": [[106.5, 83.25], [318.75, 83.25], [318.75, 93.24755859375], [106.5, 93.24755859375]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 13, "polygon": [[107.25, 616.5], [184.078125, 616.5], [184.078125, 628.03125], [107.25, 628.03125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 50], ["Text", 6], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 50], ["Text", 5], ["ListItem", 3], ["SectionHeader", 3], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 78], ["TextInlineMath", 5], ["Text", 5], ["Equation", 4], ["Reference", 4], ["SectionHeader", 2], ["Footnote", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 702], ["TableCell", 169], ["Line", 68], ["TextInlineMath", 4], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 97], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 62], ["TextInlineMath", 4], ["Reference", 4], ["Text", 3], ["Equation", 3], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 571], ["TableCell", 108], ["Line", 76], ["Reference", 3], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 2122, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 582], ["TableCell", 255], ["Line", 55], ["Text", 5], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["TableCell", 120], ["Line", 62], ["Text", 5], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12592, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 46], ["ListItem", 14], ["Reference", 14], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 47], ["Reference", 19], ["ListItem", 17], ["Text", 2], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 46], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 24], ["ListItem", 10], ["Reference", 10], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 421], ["Line", 113], ["Reference", 3], ["SectionHeader", 2], ["Code", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1249, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["TableCell", 82], ["Line", 12], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2027, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Can_Pre-Trained_Models_Assist_in_Dataset_Distillation"}