{"table_of_contents": [{"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[149.25, 172.5], [195.75, 172.5], [195.75, 184.1748046875], [149.25, 184.1748046875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 485.71875], [132.75, 485.71875], [132.75, 496.546875], [54.0, 496.546875]]}, {"title": "2. Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[54.0, 252.0], [136.5, 252.0], [136.5, 262.775390625], [54.0, 262.775390625]]}, {"title": "3. adv-KIP Algorithm", "heading_level": null, "page_id": 2, "polygon": [[306.0, 615.0], [420.0, 615.0], [420.0, 626.09765625], [306.0, 626.09765625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 3, "polygon": [[304.9541015625, 604.0546875], [385.5, 604.0546875], [385.5, 615.65625], [304.9541015625, 615.65625]]}, {"title": "4.1. Meta-Learning and Kernel results", "heading_level": null, "page_id": 4, "polygon": [[54.0, 378.0], [218.25, 378.0], [218.25, 387.87890625], [54.0, 387.87890625]]}, {"title": "4.2. Neural Networks results", "heading_level": null, "page_id": 5, "polygon": [[54.0, 329.25], [176.25, 329.25], [176.25, 339.5390625], [54.0, 339.5390625]]}, {"title": "4.3. Non-robustness of Robust Features dataset", "heading_level": null, "page_id": 6, "polygon": [[54.0, 470.25], [255.0, 470.25], [255.0, 480.3046875], [54.0, 480.3046875]]}, {"title": "4.4. Overconfidence gives a false sense of security", "heading_level": null, "page_id": 6, "polygon": [[305.25, 417.75], [516.0, 417.75], [516.0, 428.484375], [305.25, 428.484375]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 464.25], [377.25, 464.25], [377.25, 476.05078125], [306.0, 476.05078125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 80.05078125], [54.0, 80.05078125]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 11, "polygon": [[54.0, 66.75732421875], [120.75, 66.75732421875], [120.75, 79.51904296875], [54.0, 79.51904296875]]}, {"title": "A.1. Related Work", "heading_level": null, "page_id": 11, "polygon": [[54.0, 87.35009765625], [135.0703125, 87.35009765625], [135.0703125, 99.193359375], [54.0, 99.193359375]]}, {"title": "A.2. KIP baseline", "heading_level": null, "page_id": 11, "polygon": [[54.0, 226.037109375], [130.51318359375, 226.037109375], [130.51318359375, 237.638671875], [54.0, 237.638671875]]}, {"title": "A.3. Experimental Details for Neural Network experiments", "heading_level": null, "page_id": 11, "polygon": [[54.0, 537.5390625], [306.75, 537.5390625], [306.75, 549.140625], [54.0, 549.140625]]}, {"title": "A.4. Transfer Results to Wide FC Networks", "heading_level": null, "page_id": 12, "polygon": [[54.0, 290.232421875], [241.751953125, 290.232421875], [241.751953125, 301.447265625], [54.0, 301.447265625]]}, {"title": "A.5. <PERSON><PERSON> <PERSON> AutoAttack Results", "heading_level": null, "page_id": 12, "polygon": [[54.0, 583.9453125], [227.5576171875, 583.9453125], [227.5576171875, 594.7734375], [54.0, 594.7734375]]}, {"title": "A.6. AutoAttack Suite Decomposition Analysis", "heading_level": null, "page_id": 12, "polygon": [[54.0, 652.0078125], [255.498046875, 652.0078125], [255.498046875, 662.8359375], [54.0, 662.8359375]]}, {"title": "A.7. Robustness of the publicly available RFD", "heading_level": null, "page_id": 13, "polygon": [[54.0, 309.75], [250.5, 309.75], [250.5, 319.5], [54.0, 319.5]]}, {"title": "A.8. Details on the Confidence and Reliability Visualization", "heading_level": null, "page_id": 13, "polygon": [[54.0, 378.0], [307.5, 378.0], [307.5, 387.4921875], [54.0, 387.4921875]]}, {"title": "A.9. Results using modifications of adv-KIP", "heading_level": null, "page_id": 13, "polygon": [[54.0, 565.3828125], [241.154296875, 565.3828125], [241.154296875, 574.6640625], [54.0, 574.6640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 94], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Reference", 2], ["TextInlineMath", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3931, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 110], ["Text", 7], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1347, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 686], ["Line", 115], ["TextInlineMath", 7], ["Text", 5], ["Equation", 4], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 772], ["Line", 105], ["Text", 8], ["TextInlineMath", 6], ["Reference", 4], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 114], ["TableCell", 62], ["Text", 7], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 124], ["TableCell", 85], ["Text", 6], ["Reference", 4], ["TextInlineMath", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 914, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 98], ["TableCell", 42], ["Text", 9], ["Reference", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1551, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 163], ["Text", 5], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 95], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 97], ["ListItem", 18], ["Reference", 18], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 99], ["ListItem", 18], ["Reference", 18], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 46], ["TableCell", 24], ["Text", 7], ["SectionHeader", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 44], ["TableCell", 33], ["Text", 8], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["TableCell", 78], ["Line", 48], ["Text", 4], ["Reference", 4], ["SectionHeader", 3], ["Table", 2], ["Caption", 2], ["Equation", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 727], ["TableCell", 174], ["Line", 59], ["Text", 5], ["Equation", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2292, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 48], ["Figure", 3], ["Caption", 3], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2763, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["TableCell", 100], ["Line", 17], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8296, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Can_We_Achieve_Robustness_from_Data_Alone"}