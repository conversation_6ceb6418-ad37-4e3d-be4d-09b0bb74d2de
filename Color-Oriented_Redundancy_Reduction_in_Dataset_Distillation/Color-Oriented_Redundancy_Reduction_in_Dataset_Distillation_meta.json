{"table_of_contents": [{"title": "Color-Oriented Redundancy Reduction in Dataset\nDistillation", "heading_level": null, "page_id": 0, "polygon": [[120.75, 99.0], [491.87109375, 97.5], [491.87109375, 135.73828125], [120.75, 135.73828125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 239.958984375], [329.25, 239.958984375], [329.25, 250.013671875], [282.75, 250.013671875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.60693359375, 460.5], [192.146484375, 460.5], [192.146484375, 471.796875], [106.60693359375, 471.796875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[107.25, 599.02734375], [198.0, 599.02734375], [198.0, 611.40234375], [107.25, 611.40234375]]}, {"title": "2.1 Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[107.05517578125, 624.9375], [213.36328125, 624.9375], [213.36328125, 635.765625], [107.05517578125, 635.765625]]}, {"title": "2.2 Parameterization-based Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 294.75], [317.35546875, 294.75], [317.35546875, 304.927734375], [106.98046875, 304.927734375]]}, {"title": "2.3 Color Quantization", "heading_level": null, "page_id": 2, "polygon": [[106.5, 426.75], [213.9609375, 426.75], [213.9609375, 436.9921875], [106.5, 436.9921875]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 2, "polygon": [[106.5, 549.0], [194.25, 549.0], [194.25, 560.7421875], [106.5, 560.7421875]]}, {"title": "3.1 Notations and Preliminary", "heading_level": null, "page_id": 2, "polygon": [[106.5, 573.75], [244.5, 573.75], [244.5, 583.9453125], [106.5, 583.9453125]]}, {"title": "3.2 Overview", "heading_level": null, "page_id": 3, "polygon": [[106.5, 229.5], [171.75, 229.5], [171.75, 239.765625], [106.5, 239.765625]]}, {"title": "3.3 Color Reduction via Palette Network.", "heading_level": null, "page_id": 3, "polygon": [[107.25, 475.5], [290.25, 475.5], [290.25, 485.33203125], [107.25, 485.33203125]]}, {"title": "3.4 Color Guided Initialization Module", "heading_level": null, "page_id": 4, "polygon": [[106.5, 467.25], [282.0, 467.25], [282.0, 477.984375], [106.5, 477.984375]]}, {"title": "3.5 Overall Dataset Distillation Objective", "heading_level": null, "page_id": 5, "polygon": [[106.5, 422.25], [291.0, 422.25], [291.0, 431.578125], [106.5, 431.578125]]}, {"title": "3.6 Storage Analysis", "heading_level": null, "page_id": 5, "polygon": [[106.5, 600.0], [202.0078125, 600.0], [202.0078125, 610.2421875], [106.5, 610.2421875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 303.75], [192.0, 303.75], [192.0, 315.755859375], [107.1298828125, 315.755859375]]}, {"title": "4.1 Experimental Setting", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 388.5], [222.4775390625, 388.5], [222.4775390625, 399.48046875], [107.1298828125, 399.48046875]]}, {"title": "4.2 Experimental Results", "heading_level": null, "page_id": 6, "polygon": [[106.5, 554.25], [222.75, 554.25], [222.75, 564.99609375], [106.5, 564.99609375]]}, {"title": "4.3 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[107.25, 424.5], [195.75, 424.5], [195.75, 435.05859375], [107.25, 435.05859375]]}, {"title": "5 Conclusions, Limitations, and Future Work", "heading_level": null, "page_id": 8, "polygon": [[106.5, 499.5], [349.330078125, 499.5], [349.330078125, 510.46875], [106.5, 510.46875]]}, {"title": "Acknowledgments and Disclosure of Funding", "heading_level": null, "page_id": 8, "polygon": [[107.25, 676.5], [340.5, 676.5], [340.5, 687.5859375], [107.25, 687.5859375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 72.0], [164.25, 72.0], [164.25, 83.724609375], [107.25, 83.724609375]]}, {"title": "A Appendix", "heading_level": null, "page_id": 12, "polygon": [[106.5, 72.0], [180.0, 72.0], [180.0, 83.67626953125], [106.5, 83.67626953125]]}, {"title": "A.1 Proof for Conditional Information Gain of Graph Cut", "heading_level": null, "page_id": 12, "polygon": [[106.3828125, 96.75], [363.0, 96.75], [363.0, 106.6376953125], [106.3828125, 106.6376953125]]}, {"title": "A.2 Experimental Details", "heading_level": null, "page_id": 12, "polygon": [[106.5, 534.75], [223.5, 534.75], [223.5, 544.88671875], [106.5, 544.88671875]]}, {"title": "A.3 Algorithm for Sample Selection in Initialization", "heading_level": null, "page_id": 13, "polygon": [[106.8310546875, 369.75], [336.0, 369.75], [336.0, 380.337890625], [106.8310546875, 380.337890625]]}, {"title": "A.4 Cross Architecture Performance", "heading_level": null, "page_id": 13, "polygon": [[107.25, 390.0], [271.5, 390.0], [271.5, 400.25390625], [107.25, 400.25390625]]}, {"title": "A.5 Number of colors vs Performance", "heading_level": null, "page_id": 13, "polygon": [[106.98046875, 636.75], [276.75, 636.75], [276.75, 646.98046875], [106.98046875, 646.98046875]]}, {"title": "A.6 Traditional color Quantization Methods", "heading_level": null, "page_id": 14, "polygon": [[106.5, 390.0], [304.5, 390.0], [304.5, 401.4140625], [106.5, 401.4140625]]}, {"title": "A.7 Visualization of Distilled Images", "heading_level": null, "page_id": 15, "polygon": [[105.0, 70.5], [272.53125, 70.5], [272.53125, 84.75], [105.0, 84.75]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 46], ["Text", 5], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6032, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 45], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 42], ["SectionHeader", 4], ["Text", 3], ["TextInlineMath", 2], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 60], ["TextInlineMath", 5], ["Equation", 3], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 98], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 549], ["Line", 73], ["TextInlineMath", 5], ["Equation", 5], ["Text", 5], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 55], ["TableCell", 36], ["Text", 4], ["SectionHeader", 3], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["TableCell", 104], ["Line", 56], ["Text", 5], ["Table", 3], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4295, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 74], ["TableCell", 28], ["Text", 5], ["Caption", 2], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 52], ["ListItem", 18], ["Reference", 18], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 53], ["ListItem", 21], ["Reference", 21], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 22], ["ListItem", 10], ["Reference", 10], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 97], ["Text", 7], ["Equation", 6], ["SectionHeader", 3], ["ListItem", 3], ["Reference", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3136, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 516], ["TableCell", 72], ["Line", 51], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["Table", 2], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 337], ["Span", 336], ["Line", 39], ["Caption", 4], ["Table", 4], ["Reference", 4], ["TableGroup", 3], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 11847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 5], ["Picture", 3], ["Caption", 3], ["PictureGroup", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 7], ["Line", 5], ["Picture", 3], ["Caption", 3], ["PictureGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1830, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 7], ["Line", 4], ["Picture", 3], ["Caption", 3], ["PictureGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1247, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Color-Oriented_Redundancy_Reduction_in_Dataset_Distillation"}