# Compressed Gastric Image Generation Based on Soft-Label Dataset Distillation for Medical Data Sharing

Guang Li<sup>a</sup>, <PERSON><sup>b</sup>, <PERSON><PERSON><PERSON><sup>b</sup>, <PERSON><PERSON><sup>b</sup>

*<sup>a</sup>Graduate School of Information Science and Technology, Hokkaido University, N-14, W-9, Kita-Ku, Sapporo, 060-0814, Japan*

*<sup>b</sup>Faculty of Information Science and Technology, Hokkaido University, N-14, W-9, Kita-Ku, Sapporo, 060-0814, Japan*

## Abstract

*Background and objective:* Sharing of medical data is required to enable the cross-agency flow of healthcare information and construct high-accuracy computer-aided diagnosis systems. However, the large sizes of medical datasets, the massive amount of memory of saved deep convolutional neural network (DCNN) models, and patients' privacy protection are problems that can lead to inefficient medical data sharing. Therefore, this study proposes a novel soft-label dataset distillation method for medical data sharing.

*Methods:* The proposed method distills valid information of medical image data and generates several compressed images with different data distributions for anonymous medical data sharing. Furthermore, our method can extract essential weights of DCNN models to reduce the memory required to save trained models for efficient medical data sharing.

*Results:* The proposed method can compress tens of thousands of images into several soft-label images and reduce the size of a trained model to a few hundredths of its original size. The compressed images obtained after distillation have been visually anonymized; therefore, they do not contain the private information of the patients. Furthermore, we can realize high-detection performance with a small number of compressed images.

*Conclusions:* The experimental results show that the proposed method can improve the efficiency and security of medical data sharing.

*Keywords:* Medical image distillation; Medical data sharing; Model compression; Anonymization.

# 1. Introduction

Healthcare refers to practical measures for maintaining and promoting of physical and mental health, including standards for preventing mental illnesses and physical diseases caused by work, life, environment, and psychological factors [\[1\]](#page-8-0). Generally, quality healthcare services are provided by identifying health problems, introducing innovative solutions, and allocating health resources [\[2\]](#page-8-1). Thus, these medical services focus on properly collecting, managing, and using healthcare information [\[3,](#page-8-2) [4,](#page-9-0) [5\]](#page-9-1). Although a large medical facility may provide adequate healthcare information, a single small medical facility does not have the capacity. Considering the insufficient amount of healthcare information in a single small medical facility, it is necessary to obtain adequate healthcare information through transmission between multiple medical facilities [\[6,](#page-9-2) [7\]](#page-9-3). The sharing of medical data plays an essential role in enabling the cross-agency flow of healthcare information; thus, improving the quality of medical services [\[8\]](#page-9-4).

 $\verb|<EMAIL> (Miki Haseyama^b)$ 

With the development of deep convolutional neural networks (DCNNs) [\[9,](#page-9-5) [10\]](#page-9-6), DCNN-based computer-aided diagnosis (CAD) systems [\[11,](#page-9-7) [12\]](#page-9-8) have made significant progress in the healthcare field. Since obtaining high-quality medical image data and annotating it by experienced physicians is time-consuming and expensive, the amount of data from a small medical facility is insufficient to train high-accuracy DC-NNs [\[13\]](#page-9-9). Therefore, sharing medical data from different medical facilities, including image data and trained DCNN models, is essential. However, the large size of medical datasets, the massive amount of memory of saved DCNN models, and patients' privacy protection are significant obstacles in medical data sharing. With the increase in high-resolution medical image data, the sizes of medical datasets have increased exponentially [\[14\]](#page-9-10). Since there are many parameters in DCNN models, a massive amount of memory is required to save the trained DCNN models [\[15\]](#page-9-11). Furthermore, medical images often contain private information of patients, and the use of these medical data remains controversial [\[16\]](#page-9-12). Therefore, it is necessary to improve the efficiency and security of medical data sharing.

Many methods for reducing the sizes of datasets and saved DCNN models have been proposed. Conventional dataset re-

*Email addresses*: gu<mark><EMAIL></mark> (Guang Li<sup>a</sup>),  $\verb|<EMAIL>(Ren Togo^b),$ 

 $\mathtt{<EMAIL>}$  (Takahiro  $\mathtt{Ogawa^b}$ ),

duction methods aim to select a subset and achieve performance comparable to the original dataset [\[17,](#page-9-13) [18\]](#page-9-14). Additionally, many researchers have proposed approaches to prune networks to solve the problem of a large amount of memory required for saved DCNN models. Network pruning methods aim to prune redundant weights and only keep essential weights. A typical procedure for network pruning consists of training a large model, pruning the trained model, and fine-tuning the pruned model [\[19\]](#page-9-15).

Although dataset reduction and network pruning methods can be used to address inefficiencies in medical data sharing, they still have limitations. First, dataset reduction methods can be used to reduce the sizes of medical datasets and improve the efficiency of medical data sharing. Meanwhile, these methods can only reduce the size of a dataset to a certain extent since they use actual data from the original dataset, especially in the case of high-resolution and complex medical datasets. Second, we can use model pruning methods to reduce the sizes of trained DCNNs in medical datasets, which can also improve the efficiency of medical data sharing. However, all pruning methods require deleting the network architecture (i.e., channels or layers) or the use of dedicated hardware/libraries, which are not flexible and not sufficiently straightforward [\[19\]](#page-9-15).

The issue of privacy protection is also a significant barrier to medical data sharing [\[20\]](#page-9-16). Privacy information in medical image data causes many medical facilities to be reluctant to share medical data, which has hindered research on CAD systems [\[21,](#page-9-17) [22\]](#page-9-18). The ground truth of medical image datasets is often associated with personal information, e.g., the patient's name, gender, age, and ID number. Some methods for removing or encrypting the patient's personal information have been proposed to protect the privacy of patients [\[23,](#page-9-19) [24,](#page-9-20) [25\]](#page-9-21). However, medical images still contain patient's information, such as physical characteristics and condition of the patient, which is challenging to share with different medical facilities or taken out of hospitals. To the best of our knowledge, there is currently no way to achieve adequate anonymization of medical images.

Gastric X-ray image data has the above-described problems in medical data sharing. Furthermore, gastric cancer is the third leading cause of cancer-related death worldwide, with half of the total global gastric cancer cases occurring in Eastern Asia [\[26\]](#page-9-22). It has been revealed that gastritis is the leading risk factor for the onset of gastric cancer and can be diagnosed by gastric X-ray images in an X-ray examination [\[27,](#page-9-23) [28\]](#page-9-24). Since gastric X-ray image data sharing is crucial for effectively constructing CAD systems, and we have previously proposed methods for automatic detection of gastritis [\[29,](#page-9-25) [30\]](#page-9-26), we focused on gastric X-ray images in this study. Although our study focused on gastric X-ray image data, we believe the findings and methodology could also be applied to other medical images, such as endoscopic and Computed Tomography (CT) images.

In this paper, we propose a compressed gastric image generation method based on the soft-label dataset distillation for efficient anonymous medical data sharing. Using the proposed method, we can distill valid information of the entire gastric X-ray image dataset into several soft-label compressed images

<span id="page-1-0"></span>Image /page/1/Figure/5 description: The image depicts a flowchart illustrating a method for medical data sharing. On the left, 'Medical image data' is shown as a stack of X-ray images. An arrow labeled 'Distillation' points from these images to a box containing a 'Compressed image', which appears as a noisy, grayscale square. On the right, a 'DCNN model' is represented by a series of layered blocks, indicating a deep convolutional neural network. An arrow labeled 'Extraction' points from the DCNN model to a box labeled 'Essential weights'. Both the 'Compressed image' and 'Essential weights' are enclosed within a dashed rectangle. To the right of this rectangle, three bullet points list the benefits: 'High efficiency', 'High security', and 'High accuracy'. An arrow points from the dashed rectangle downwards to 'Medical data sharing'.

Figure 1: Overview of the proposed method.

and effectively compress the sizes of saved models. Since compressed images are not generated from the actual distribution of the original dataset, the generation of compressed images provides a possibility for the visual anonymization of medical images. Figure [1](#page-1-0) shows an overview of the proposed method. Although the dataset distillation [\[31\]](#page-9-27) method has been proposed for distilling information of simple datasets (i.e., MNIST and CIFAR10), its use for large-size and high-resolution medical datasets has not yet been explored. Unlike conventional dataset reduction methods, our method aims to distill information from a large dataset into several soft-label compressed anonymous images with different data distributions. Thus, a smaller amount of data is required to learn the same amount of information as traditional dataset reduction methods. Furthermore, our method can reduce the memory required to save trained models by extracting essential weights straightforwardly.

The main contributions of this study are summarized as follows:

- The proposed method can compress tens of thousands of images into several soft-label images and reduce the size of a trained model to a few hundredths of its original size.
- The compressed images obtained after distillation have been visually anonymized; therefore, they do not contain private information of the patients.
- High-detection performance can be realized with a small number of compressed images.

Note that some preliminary results of this work have been presented in our previous study [\[32\]](#page-9-28), where we showed the feasibility of dataset distillation for gastric X-ray image data with three compressed images (one image per category). In this paper, we extend our previous work as follows. First, we show the feasibility of distilling information of the entire gastric X-ray image dataset into only one compressed anonymous soft-label patch image for the maximum compression rate. Second, we demonstrate how to reduce the memory required to save trained

models with batch normalization layers. Third, we evaluate the performance of different DCNN models to verify the robustness of our method. Finally, we discuss the relationships between the minimum number of compressed images of different DCNN models and their parameters.

The remainder of this paper is organized as follows. In Section [2,](#page-2-0) we introduce related works. In Section [3,](#page-2-1) we describe the algorithm of the proposed method. Then, we present the experiments, discussion, and conclusion in Sections [4,](#page-5-0) [5,](#page-8-3) and [6,](#page-8-4) respectively.

<span id="page-2-0"></span>

## 2. Related works

This section presents some dataset reduction methods in [2.1.](#page-2-2) Then, we review some network pruning methods in [2.2.](#page-2-3) Finally, we show some privacy protection methods in [2.3.](#page-2-4)

<span id="page-2-2"></span>

### 2.1. Dataset reduction

Many methods have been proposed for making a model trained on a reduced small dataset perform as well as a model trained on the full dataset. Some methods create a coreset or prune the original full dataset by only using valuable data for model training, a process called core-set construction [\[33,](#page-9-29) [34,](#page-9-30) [18,](#page-9-14) [35\]](#page-9-31) or instance selection [\[36,](#page-9-32) [37,](#page-9-33) [17,](#page-9-13) [38\]](#page-9-34). Additionally, some methods based on active learning aim to reduce the data volume that needs to be labeled by only annotating data that are difficult to recognize [\[39,](#page-9-35) [40,](#page-9-36) [41\]](#page-9-37).

Unlike these dataset reduction methods that use actual data from the original dataset, the dataset distillation method aims to distill information from a large dataset and generate several compressed images with different distributions. Therefore, the dataset distillation method requires a smaller amount of data to learn the same amount of information than the amount of data required by traditional dataset reduction methods. However, the efficacy of the proposed dataset distillation method has only been tentatively validated for simple datasets such as MNIST and CIFAR10). Considering the inefficiencies associated with the sharing of large-scale medical datasets, we propose the softlabel dataset distillation method for medical images and provide the first demonstration of its effectiveness for a complex gastric X-ray image dataset.

<span id="page-2-3"></span>

### 2.2. Network pruning

Over-parameterization is a universally recognized characteristic of DCNNs, and it leads to high computational costs of inference and large storage footprints for trained models [\[42,](#page-9-38) [43\]](#page-9-39). Recently, many network pruning methods have been proposed for pruning redundant weights and only keeping essential weights, which can reduce the computational costs and memory required to save the trained models [\[19\]](#page-9-15). For example, Han *et al.* proposed the deep compression pipeline: pruning, trained quantization, and Huffman coding to obtain highly compressed models without affecting their accuracy [\[44,](#page-9-40) [45,](#page-9-41) [46\]](#page-9-42). Hinton *et al.* proposed a model compression method by distilling information from large networks to a single compact network [\[47\]](#page-9-43).

<span id="page-2-5"></span>Image /page/2/Picture/9 description: Two X-ray images of the stomach are shown side-by-side, labeled (a) and (b). Image (a) shows the stomach filled with a contrast agent, with the contrast agent forming a smooth, rounded upper border. The stomach wall appears to have fine, linear striations. Image (b) also shows the stomach filled with contrast agent, but the contrast agent appears to be coating the rugal folds of the stomach, creating a more irregular and textured appearance within the stomach lumen. The rugal folds are clearly visible and appear prominent.

Figure 2: Gastric images: (a) non-gastritis images and (b) gastritis images

Other network pruning methods that prune at the level of layers or channels have also been proposed [\[48,](#page-9-44) [49,](#page-9-45) [50\]](#page-9-46).

Although these methods have good results in network pruning, they require the use of dedicated hardware/libraries or deleting the original network architecture (i.e., channels or layers) [\[19\]](#page-9-15). However, we found that the dataset distillation method can reduce the sizes of datasets and compress the sizes of trained models with no additional operation being required. This paper also presents new findings on the compression of the sizes of trained models.

<span id="page-2-4"></span>

#### 2.3. Privacy protection

Privacy protection issues have been hindering the development of medical data sharing [\[21,](#page-9-17) [22\]](#page-9-18). Several researchers have addressed some of the issues with anonymization. For example, some methods for removing or encrypting the patient's personal information have been proposed to protect the privacy of patients [\[23,](#page-9-19) [24,](#page-9-20) [25\]](#page-9-21). Additionally, some methods based on blockchain [\[51,](#page-9-47) [52,](#page-10-0) [53,](#page-10-1) [54,](#page-10-2) [55\]](#page-10-3) and cloud computing [\[56,](#page-10-4) [57,](#page-10-5) [58,](#page-10-6) [59,](#page-10-7) [60\]](#page-10-8) platforms have been recently proposed to secure sharing of medical data.

Since the task of effectively anonymizing medical images is challenging, to the best of our knowledge, there is currently no way to achieve adequate anonymization of medical images. In our method, since compressed images are not generated from the actual distribution of the original dataset, the generation of compressed images provides a possibility for visual anonymization of medical images. A recent study has also theoretically demonstrated the role of dataset distillation in privacy protection [\[61\]](#page-10-9).

<span id="page-2-1"></span>

## 3. Proposed method

This section presents the proposed method for generating compressed gastric images. First, we explain the training data preprocessing procedure in Subsection [3.1.](#page-3-0) Then, we discuss the details of the compressed gastric image generation algorithm in Subsection [3.2.](#page-3-1) Finally, we show how to perform gastritis detection with patches in Subsection [3.3.](#page-5-1)

<span id="page-3-2"></span>Image /page/3/Picture/0 description: The image displays three rows of medical images, labeled (a), (b), and (c). Row (a) contains four images, each showing a different view of what appears to be a medical scan, possibly an X-ray. The images vary in clarity and focus, with some showing distinct anatomical structures and others appearing more abstract or blurred. Row (b) also contains four images, which seem to be close-ups of tissue or cellular structures, characterized by intricate patterns and varying shades of gray. Row (c) presents three images, similar in style to row (b), displaying detailed, marbled textures that could represent biological tissues or geological formations.

Figure 3: Gastric patches: (a) irrelevant patches, (b) non-gastritis patches, and (c) gastritis patches

<span id="page-3-0"></span>

### 3.1. Training data preprocessing

In this subsection, we present a method to preprocess the training data considering clinical settings. Figure [2](#page-2-5) shows the full gastric X-ray images used in this study: (a) is an example without gastritis (hereafter called non-gastritis), and (b) is an example with gastritis. As shown in Figure [2,](#page-2-5) a stomach without gastritis has straight folds and uniform mucosal surface patterns. However, a stomach with gastritis has non-straight folds and coarse mucosal surface patterns. The gastric images have high resolutions (i.e.,  $2,048 \times 2,048$  pixels). In practical medical applications, high-resolution images could lead to expensive computational costs. Since patch-based detection methods can efficiently use information about pathological regions and locations and reduce the computational cost, we follow our previous approaches to divide each image into patches for compressed gastric image generation [\[32,](#page-9-28) [62\]](#page-10-10).

First, we divide each gastric X-ray image into multiple patches. Let  $X_{\text{train}} \in \mathbb{R}^{d \times d}$  represent a gastric image in training data. The label of  $X_{\text{train}}$  is defined as  $Y_{\text{train}} \in \{0, 1\}$ , where  $Y_{\text{train}} = 0$  and  $Y_{\text{train}} = 1$  represent non-gastritis and gastritis, respectively. Specifically, the full gastric images are divided into  $H \times W$  patches (*H* and *W* are the numbers of patches in the vertical and horizontal directions, respectively). We define the divided patches as  $(\mathbf{x}, \mathbf{y}) = \{x_g, y_g\}_{g=1}^G$ , where *G* represents the number of patch images,  $x_g \in \mathbb{R}^{d' \times d'}$  represents the image and  $y_g$  represents its label. Next, we annotate the patch images into the following three categories (i.e.,  $y_g \in \{I, N, P\}$ ):

- $I$ : patches extracted from the outside of the stomach (irrelevant) in gastritis and non-gastritis X-ray images,
- N: patches extracted from the inside of the stomach in non-gastritis (negative) X-ray images,

<span id="page-3-3"></span>

### Algorithm 1 Training phase

- **Input:**  $\theta$ : the random initial weights of a DCNN model;  $\alpha$ : learning rate; *K*: batch size; *T*: training steps; *M*: the number of compressed images;  $\tilde{\mathbf{y}}_0$ : initial value for  $\tilde{\mathbf{y}}$ ;  $\tilde{\alpha}_0$ : initial value for  $\tilde{\alpha}$
- **Output:**  $\tilde{x}$ : compressed images;  $\tilde{y}$ : distilled labels;  $\tilde{\alpha}$ : optimized learning rate;  $\theta_{bn}$ : batch normalization parameters
- 1: Initialize  $\tilde{\mathbf{x}} = {\{\tilde{x}_m\}}_{m=1}^M$  randomly,  $\tilde{\mathbf{y}} = {\{\tilde{y}_m\}}_{m=1}^M \leftarrow \tilde{\mathbf{y}}_0, \tilde{\alpha} \leftarrow \tilde{\alpha}_0$
- 2: **for** each training step  $t = 1$  to  $T$  **do**
- 3: Sample a mini-batch of training data:
- $(\mathbf{x}_t, \mathbf{y}_t) = \{x_{t,k}, y_{t,k}\}_{k=1}^K$ <br>Compute optimized 4: Compute optimized weights using a gradient descent

method:  $\theta_{\text{opt}} \leftarrow \theta - \tilde{\alpha} \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta)$ 

- 5: Evaluate the objective function on the mini-batch of training data:
- $\mathcal{L} = \ell(\mathbf{x}_t, \mathbf{y}_t, \theta_{\text{opt}})$ <br>Undate distilled
- 6: Update distilled data:  $\tilde{\mathbf{x}} \leftarrow \tilde{\mathbf{x}} - \alpha \nabla_{\tilde{\mathbf{x}}} \mathcal{L}, \tilde{\mathbf{y}} \leftarrow \tilde{\mathbf{y}} - \alpha \nabla_{\tilde{\mathbf{y}}} \mathcal{L}, \text{ and } \tilde{\alpha} \leftarrow \tilde{\alpha} - \alpha \nabla_{\tilde{\alpha}} \mathcal{L}$
- 7: if the DCNN model has batch normalization layers then
- 8: Save the batch normalization parameters as  $\theta_{bn}$ <br>9: **end if**
- end if
- 10: end for
- $P$ : patches extracted from the inside of the stomach in gastritis (positive) X-ray images.

A radiological technologist manually processed the stomach region annotation in this research. Figure [3](#page-3-2) shows some examples of patch images. We used the divided patches for the following compressed gastric image generation.

<span id="page-3-1"></span>

### 3.2. Compressed gastric image generation

This section explains how to generate compressed gastric images based on soft-label dataset distillation. Since the training and test phases of dataset distillation differ from the standard neural network training and testing, we first briefly introduce the framework of the proposed method. In the training phase, we distill the information of a large dataset into several compressed anonymous images through a twice-differentiable loss and keep updating as the gradient descent. In the test phase, we use the optimal distilled images to reproduce the accuracy of the training phase.

Algorithm [1](#page-3-3) shows the training phase of the proposed method. First, we explain the settings of the training phase. Here,  $\theta$  represents the random initial weights of a random DCNN model;  $\alpha$ ,  $K$ , and  $T$  represent the learning rate, batch size, and training steps, respectively. *M* represents the number of compressed images.  $\tilde{y}_0$  is the initial value of distilled labels  $\tilde{y}$ , and  $\tilde{\alpha}_0$  is the initial value of optimized learning rate  $\tilde{\alpha}$ . We can obtain the compressed images  $\tilde{\mathbf{x}}$ , distilled labels  $\tilde{\mathbf{v}}$ , optimized learning rate  $\tilde{\alpha}$  and batch normalization parameters  $\theta_{\rm bn}$  for the test phase.

Next, we show the details of the compressed gastric image generation algorithm. For the training set of patches  $(x, y)$  =

 ${x_g, y_g}_{g=1}^G$ , *G* represents the number of training images, and  $x_g$ <br>and *y* represent the image and its label, respectively. We define and  $y_g$  represent the image and its label, respectively. We define the weights of a random DCNN model as  $\theta$ . Let  $\ell(x, y, \theta)$  be the loss of the DCNN model on the entire training set  $(x, y)$ . In our compressed gastric image generation method, we distill valid information of  $(x, y)$  to a very small distilled dataset  $(\tilde{x}, \tilde{y})$  $= {\{\tilde{x}_m, \tilde{y}_m\}_{m=1}^M}$ . Here, *M* represents the number of compressed<br>images and *M*  $\ll G$ ,  $\tilde{x}$  and  $\tilde{y}$  represent the distilled image images and  $M \ll G$ ,  $\tilde{x}_m$  and  $\tilde{y}_m$  represent the distilled image and its distilled label, respectively.

Since different categories of gastric patches may have common features, we let the compressed images  $\tilde{x}$  have soft labels  $\tilde{y}$ , which can be represented with probability labels of  $I$ , N and  $\mathcal{P}$  [\[47,](#page-9-43) [63\]](#page-10-11). The number of compressed images can be much smaller than the number of training images since the compressed images  $\tilde{x}$  do not come from the actual distribution. Furthermore, soft labels play an essential role in the regularization during the training process, resulting in better detection performance than the original dataset distillation method [\[31\]](#page-9-27). The number of compressed images can be one image with soft labels, allowing for maximum compression of the gastric training set. Moreover, the optimized weights for the distilling process are calculated as follows:

<span id="page-4-0"></span>
$$
\theta_{\text{opt}} \leftarrow \theta - \tilde{\alpha} \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta), \tag{1}
$$

where  $\ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta)$  represents the twice-differentiable loss of the DCNN model on the distilled dataset  $(\tilde{\mathbf{x}}, \tilde{\mathbf{y}})$ , and  $\tilde{\alpha}$  represents the optimized learning rate.

Generally, the training of DCNNs is to find the minimizer of the empirical error over the entire training set  $(x, y)$  as follows:

$$
\theta^* = \arg\min \ell(\mathbf{x}, \mathbf{y}, \theta). \tag{2}
$$

Unlike the training goal of general DCNNs to find the optimal parameters  $\theta^*$ , our goal is to find the best-compressed images<br> $\tilde{\mathbf{x}}^*$  distilled labels  $\tilde{\mathbf{v}}^*$  and optimized learning rate  $\tilde{\alpha}^*$ . With the  $\tilde{\mathbf{x}}^*$ , distilled labels  $\tilde{\mathbf{y}}^*$  and optimized learning rate  $\tilde{\alpha}^*$ . With the newly derived weights  $\theta$  and the DCNN model, we can find newly derived weights  $\theta_{opt}$  of the DCNN model, we can find the minimizer of the empirical error over the entire training set  $(x, y)$ . The objective function can be defined as follows:

$$
\tilde{\mathbf{x}}^*, \tilde{\mathbf{y}}^*, \tilde{\alpha}^* = \arg \min \mathcal{L}(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \tilde{\alpha}; \theta),
$$
  
= arg min  $\ell(\mathbf{x}, \mathbf{y}, \theta_{\text{opt}}),$   
= arg min  $\ell(\mathbf{x}, \mathbf{y}, \theta - \tilde{\alpha} \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta)),$  (3)

<span id="page-4-1"></span>where  $\mathcal L$  is differentiable and  $\ell$  is twice-differentiable.

To get the best compressed images, distilled labels, and optimized learning rate, we update the distilled data  $\tilde{\mathbf{x}}$ ,  $\tilde{\mathbf{y}}$ , and  $\tilde{\alpha}$  at each distilling step using a gradient descent method as follows:

$$
\tilde{\mathbf{x}} \leftarrow \tilde{\mathbf{x}} - \alpha \nabla_{\tilde{\mathbf{x}}} \mathcal{L}, \n\tilde{\mathbf{y}} \leftarrow \tilde{\mathbf{y}} - \alpha \nabla_{\tilde{\mathbf{y}}} \mathcal{L}, \n\tilde{\alpha} \leftarrow \tilde{\alpha} - \alpha \nabla_{\tilde{\alpha}} \mathcal{L},
$$
\n(4)

<span id="page-4-2"></span>where  $\nabla_{\tilde{\mathbf{x}}}\mathcal{L}, \nabla_{\tilde{\mathbf{y}}}\mathcal{L}$  and  $\nabla_{\tilde{\alpha}}\mathcal{L}$  represent the gradients of  $\mathcal{L}$  based on  $\tilde{\mathbf{x}}, \tilde{\mathbf{y}}$  and  $\tilde{\alpha}$ , respectively, and  $\alpha$  represents the learning rate.

Then we explain the training process of the proposed algorithm. First, we randomly initialize the compressed images  $\tilde{x}$ .

<span id="page-4-3"></span>

### Algorithm 2 Test phase

**Input:**  $\theta$ : the random initial weights of a DCNN model;  $\tilde{\mathbf{x}}$ : compressed images;  $\tilde{y}$ : distilled labels;  $\tilde{\alpha}$ : optimized learning rate;  $\theta_{bn}$ : batch normalization parameters

Output: Pred: predicted labels

- 1: if the DCNN model does not have batch normalization layers then
- 2: Compute optimized weights with the distilled data:  $\theta_{\text{ont}} \leftarrow \theta - \tilde{\alpha} \, \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta)$

```
3: else
```

4: Compute optimized weights with the distilled data and batch normalization parameters:  $\theta = \tilde{\alpha} \nabla_{\alpha} \ell(\tilde{x} \tilde{y} + \theta, \theta)$ 

$$
\theta_{\text{opt}} \leftarrow \theta - \alpha \, \mathbf{v}_{\theta} \mathbf{t}(\mathbf{x}, \mathbf{y}, \theta_{\text{bn}},
$$

5: end if

- 6: Predict the labels of the test data:
  - $Pred = model(x_{test}, y_{test}, \theta_{opt})$

Next, we initialize  $\tilde{y}$  and  $\tilde{\alpha}$  with  $\tilde{y}_0$  and  $\tilde{\alpha}_0$ , respectively. At each training step *t*, we obtain a mini-batch of training data  $(\mathbf{x}_t, \mathbf{y}_t)$  for which the size is *K*. In the distilling process, we compute the optimized weights using Eq. (1) Additionally we compute the optimized weights using Eq. [\(1\)](#page-4-0). Additionally, we can extend the distilling process by performing multiple distill epochs and multiple distill steps for better distillation results. Specifically, we compute the optimized weights by performing sequential gradient descent steps on the distilled dataset and repeating a few epochs. The calculation of sequential gradient descent steps is given as follows:

$$
\theta_{i+1} \leftarrow \theta_i - \tilde{\alpha} \nabla_{\theta_i} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_i), \tag{5}
$$

where *i* represents the distill steps. Then we evaluate the objective function on the mini-batch of training data using Eq. [\(3\)](#page-4-1). We update the distilled data  $\tilde{\mathbf{x}}$ ,  $\tilde{\mathbf{y}}$ , and  $\tilde{\alpha}$  using Eq. [\(4\)](#page-4-2) based on a gradient descent method. Finally, we save the batch normalization parameters as  $\theta_{bn}$  if the DCNN model has batch normalization layers in its architecture.

Note that the original dataset distillation method designed for simple datasets (e.g., MNIST and CIFAR10) only uses simple networks that do not contain batch normalization layers. When a DCNN model has batch normalization layers, the mean and variance information of batches will be treated as constant and assumed not to change during the gradient steps in the distillation process. Thus, the batches' information cannot be distilled into compressed images. However, only the batch normalization parameters  $\theta_{bn}$  need to be saved to reproduce the training phase's detection performance. This feature is used in our method to compress the sizes of trained models.

Algorithm [2](#page-4-3) shows the test phase of the proposed method. In the test phase, if the DCNN model does not have batch normalization layers, we employ the distilled data  $\tilde{\mathbf{x}}$ ,  $\tilde{\mathbf{y}}$ , and  $\tilde{\alpha}$  to compute the optimized weights  $\theta_{opt}$  as follows:

$$
\theta_{\text{opt}} \leftarrow \theta - \tilde{\alpha} \, \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta). \tag{6}
$$

If the DCNN model has batch normalization layers, we can compute the optimized weights with the distilled data and batch normalization parameters as follows:

$$
\theta_{\text{opt}} \leftarrow \theta - \tilde{\alpha} \, \nabla_{\theta} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_{\text{bn}}, \theta). \tag{7}
$$

Finally, we can predict the labels on the test data  $(x_{test}, y_{test})$ with the obtained optimized weights  $\theta_{opt}$  as follows:

$$
Pred = model(\mathbf{x}_{test}, \mathbf{y}_{test}, \theta_{opt}),
$$
\n(8)

where Pred is the predicted labels of the test data and can be used for the final gastritis detection.

<span id="page-5-1"></span>

### 3.3. Gastritis detection

This subsection demonstrates how to perform gastritis detection with patches. First, given a test gastric image  $X_{\text{test}} \in \mathbb{R}^{d \times d}$ , it can be divided into  $H \times W$  patches in the same manner as that for training data. We can obtain the predicted labels (i.e., Pred) of these patches by inputting the divided patches into a DCNN model with the optimized weights  $\theta_{opt}$ . Then, we calculate the numbers of patches whose predicted labels are N and  $P$  as Num(N) and Num(P), respectively. Since patches  $I$ were extracted from outside the stomach and are not related to image-level ground truth, we do not use them for gastritis detection. Finally, we perform gastritis detection as follows:

<span id="page-5-4"></span>
$$
Y_{\text{test}} = \begin{cases} 1 & \text{if } \frac{\text{Num}(\mathcal{P})}{\text{Num}(\mathcal{N}) + \text{Num}(\mathcal{P})} \ge \delta \\ 0 & \text{otherwise} \end{cases},\tag{9}
$$

where  $\delta$  is a threshold. Note that when  $Y_{\text{test}} = 1$ , the predicted result is gastritis, and the predicted result is non-gastritis when  $Y_{\text{test}} = 0$ .

<span id="page-5-0"></span>

## 4. Experiments

In this section, we conduct three experiments to verify the effectiveness of the proposed method. Subsection [4.1](#page-5-2) presents the experimental settings. The effectiveness of the dataset reduction and model compression of our method are presented in Subsections [4.2](#page-5-3) and [4.3,](#page-6-0) respectively. Finally, we demonstrate the minimum number of compressed images of different DCNN models in Subsection [4.4.](#page-7-0) In all experiments, we implemented the algorithm using PyTorch [\[64\]](#page-10-12) framework with an NVIDIA Tesla P100 GPU.

<span id="page-5-2"></span>

### 4.1. Experimental settings

The medical dataset used in our research contains gastric Xray images for 815 patients (240 gastritis and 575 non-gastritis images). The ground truth of each image was determined from patient diagnosis results of endoscopic and X-ray examinations. Gastric X-ray images were  $2,048 \times 2,048$  pixels and gray-scale (8-bit). The training data contained images for 200 patients (100 gastritis and 100 non-gastritis images). We used images for the remaining patients (140 gastritis and 475 non-gastritis images) as test data.

In the data preprocessing stage, we divided all of these images into multiple patches (299  $\times$  299 pixels) with a sliding interval of 50 pixels (i.e.,  $H = W = 35$ ) because we demonstrated in our previous study that good gastritis detection performance can be obtained in this case [\[29\]](#page-9-25). For training data, a radiological technologist annotated the obtained patches as  $I, N$ , and  $\mathcal P$ . If the area within the stomach is less than 1% in a patch, it is marked as  $I$ . Additionally, if the area within the stomach exceeds 85% in a patch, it is marked as  $N$  or  $P$ . We discarded the rest patches. Consequently, we obtained training data  $I, N$ , and  $\mathcal P$  for which the numbers of patches were 48,385, 42,785, and 45,127, respectively. These patches were used to train DCNN models and for compressed gastric image generation. For the test data, each of the remaining 615 gastric X-ray images was divided into 1,225 patches in the same manner as that of training data. In the test phase, the detection result of a gastric image was determined using Eq. [\(9\)](#page-5-4).

Then, we conducted three experiments to evaluate the effectiveness of the proposed method. We used the compressed images with the best detection performance in our experiments. We evaluated the detection performance of full gastric images in all three experiments. In all experiments, we set the threshold  $\delta$  to 0.4, which tended to have better detection performance. The random initial weights  $\theta$  of all DCNN models used in our experiments were initialized with the default Xavier initializer. The loss  $\ell$  was a cross-entropy loss. As in our previous study [\[30\]](#page-9-26), we used sensitivity (Sen), specificity (Spe), and their harmonic mean (HM) as evaluation metrics, given as follows:

$$
\text{Sen} = \frac{\text{TP}}{\text{TP} + \text{FN}},\tag{10}
$$

$$
Spe = \frac{TN}{TN + FP},\tag{11}
$$

$$
HM = \frac{2 \times \text{Sen} \times \text{Spe}}{\text{Sen} + \text{Spe}},\tag{12}
$$

where TP, TN, FP, and FN represent the numbers of true positive, true negative, false positive, and false negative, respectively. Since Sen and Spe have a tradeoff relationship, we took the HM as the final evaluation metric.

<span id="page-5-3"></span>

### 4.2. Demonstration of the e

ff

### ectiveness of dataset reduction

This subsection demonstrates the dataset reduction effectiveness of the proposed method through a comparison of dataset distillation and general network training. We used ResNet18 [\[65\]](#page-10-13) and VGG16 [\[66\]](#page-10-14). First, we set the number of compressed images to 3 (one image per category) for the softlabel dataset and original dataset distillations [\[31\]](#page-9-27) as SLDD (3) and DD (3), respectively. Since the original dataset distillation method cannot distill the knowledge of the entire dataset into one compressed image, we only set the number of distilled images to 1 for soft-label dataset distillation as SLDD (1). Then we initialized the soft labels with one-hot values of the original labels (i.e.,  $I$ ,  $N$  and  $P$ ) in SLDD (3). We also initialized the soft label with label  $N$  in SLDD (1), which tends to have better detection performance. The distill epochs and steps were set to 3 (a total of nine distill steps). DD (3) had the same settings as SLDD (3), except for the labels being fixed. Conse-

<span id="page-6-1"></span>Table 1: Test results of dataset distillation (ResNet18) and ResNet18.

| Method          | Sen   | Spe   | HM           |
|-----------------|-------|-------|--------------|
| SLDD (3)        | 0.886 | 0.869 | <b>0.877</b> |
| DD (3)          | 0.829 | 0.884 | 0.856        |
| ResNet18 (9000) | 0.814 | 0.832 | 0.823        |
| ResNet18 (6000) | 0.907 | 0.760 | 0.827        |
| ResNet18 (3000) | 0.914 | 0.669 | 0.773        |
| SLDD (1)        | 0.793 | 0.895 | 0.841        |

<span id="page-6-2"></span>Table 2: Test results of dataset distillation (VGG16) and CMs.

| Method  | Sen   | Spe   | HM           |
|---------|-------|-------|--------------|
| SLDD(2) | 0.921 | 0.926 | <b>0.923</b> |
| CM1     | 0.950 | 0.886 | 0.917        |
| CM2     | 0.907 | 0.891 | 0.899        |
| CM3     | 0.829 | 0.813 | 0.821        |
| CM4     | 0.700 | 0.705 | 0.703        |
| CM5     | 0.964 | 0.945 | 0.955        |

quently, we distilled all the training data into three images (one image per category) or one image. It saved the compressed images, distilled labels, and optimized the learning rate. In the training phase, we performed 400 epochs in SLDD (3), DD (3), and SLDD (1) and saved the distilled results for testing and evaluation. We randomly selected 1,000, 2,000, and 3,000 images per category and trained three ResNet18 models. Furthermore, we use the following comparative methods (CMs) to prove the high-detection performance achieved using the proposed method.

CM1. We trained the VGG16 from scratch by considering the stomach regions (using all patches).

CM2. We fine-tuned the pre-trained VGG16 without considering the regions of the stomach (using only patches  $N$  and  $P$ ).

CM3. We trained the VGG16 from scratch without considering the regions of the stomach (using only patches  $N$  and  $\mathcal{P}$ ).

CM4. We fine-tuned the VGG16 with the full gastric X-ray images, whose resolution is scaled down.

CM5 [\[29\]](#page-9-25). We fine-tuned the pre-trained VGG16 by considering the stomach regions (using all patches).

The test results are presented in Tables [1](#page-6-1) and [2.](#page-6-2) Table [1](#page-6-1) presents the gastritis detection performance of the proposed method and ResNet18. The ResNet18 model trained with 9,000 images (3,000 images per category) has an HM score of 0.823. However, SLDD (1), which distilled all training data into only one compressed soft-label patch image for training, has an HM score of 0.841. Furthermore, SLDD (3), which distilled all training data into three compressed soft-label images for training, has a higher HM score (0.877) than that DD (3). We can see that a high-detection performance with several compressed gastric X-ray images, indicating the dataset reduction effectiveness of the proposed method. The experimental results show that we can distill information of the entire gastric X-ray im-

<span id="page-6-3"></span>Image /page/6/Figure/11 description: Figure 4 shows compressed images generated in SLDD (3). The top row displays "Dataset: Gastritis, Arch: ResNet18, Step: 9, LR: 0.0073" with three columns of data. The first column shows I: 76.1%, N: 12.5%, P: 11.4%. The second column shows I: 11.2%, N: 77.3%, P: 11.5%. The third column shows I: 11.2%, N: 13.8%, P: 75.0%. Below these are three compressed images. The bottom row displays "Dataset: Gastritis, Arch: ResNet18, Step: 9, LR: 0.0618" with labels I, N, and P above three corresponding compressed images.

<span id="page-6-4"></span>![A noisy image with three sections.](https://i.imgur.com/0000000.png)

Figure 5: Compressed image generated in DD (3).

age dataset into only one compressed soft-label patch image for the maximum compression rate compared to our previous work [\[32\]](#page-9-28). Table [2](#page-6-2) presents the gastritis detection performance of the proposed method and CMs trained on the full dataset. SLDD (2), which distilled all training data into two compressed soft-label images for training, achieved comparable detection performance with CMs trained on the full dataset. Figures [4,](#page-6-3) [5,](#page-6-4) and [6](#page-7-1) show examples of compressed images used in our experiments. As shown in Figures [4,](#page-6-3) [5,](#page-6-4) and [6,](#page-7-1) the gastric images are anonymized visually. Thus, the compressed patch images have no private information of patients and help privacy protection of medical data sharing.

<span id="page-6-0"></span>

### 4.3. Demonstration of the e

ff

### ectiveness of model compression

This section demonstrates the model compression effectiveness of the proposed method through a comparison of different DCNN models with batch normalization layers or not. Generally, when a DCNN model has batch normalization layers in its architecture, the information of a batch will be saved in the parameters. In the training phase, the mean and variance of batches will be treated as constant and assumed not to change during the gradient steps, and hence the batches' information cannot be distilled into compressed images. However, only the batch normalization parameters need to be saved to reproduce the detection performance of the training phase. Therefore, we can use this feature to compress the model's size to be saved.

We used different models (e.g., GoogLeNet [\[67\]](#page-10-15), ResNet18, AlexNet [\[9\]](#page-9-5), and VGG16) with and without batch normalization layers. First, we set the number of compressed images to 3 (one image per category). Then, we initialized the soft labels with one-hot values of the original labels. The distill epochs and steps were set to 1. In the training phase, we performed 400 epochs for GoogLeNet, ResNet18, and AlexNet, and 200 epochs for VGG16. We saved the distilled results and the batch

<span id="page-7-1"></span>Image /page/7/Figure/0 description: The image displays text at the top, followed by a square image filled with static. The text reads: "Dataset: Gastritis, Arch: ResNet18, Step: 9, LR: 0.0020". Below this, there are three lines of text indicating percentages: "I: 25.8%", "N: 52.9%", and "P: 21.3%". The static image below the text is a grayscale representation of random noise.

Figure 6: Compressed image generated in SLDD (1).

<span id="page-7-2"></span>Table 3: Test results of different models with batch normalization (bn) and without batch normalization (no bn).

| Model             | Sen   | Spe   | HM           | Model     | Memory   | Memory*  | Compression rate |
|-------------------|-------|-------|--------------|-----------|----------|----------|------------------|
| GoogLeNet (bn)    | 0.850 | 0.916 | <b>0.882</b> | GoogLeNet | 22.83MB  | 289.14KB | 0.01266          |
| GoogLeNet (no_bn) | 0.121 | 0.823 | 0.211        | ResNet34  | 42.64MB  | 250.23KB | 0.00587          |
| ResNet18 (bn)     | 0.836 | 0.905 | <b>0.869</b> | ResNet34  | 81.20MB  | 287.99KB | 0.00354          |
| ResNet18 (no_bn)  | 0.600 | 0.844 | 0.701        | AlexNet   | 217.44MB | 201.29KB | 0.00093          |
| AlexNet (bn)      | 0.793 | 0.884 | <b>0.836</b> | VGG16     | 512.21MB | 402.01KB | 0.00078          |
| AlexNet (no_bn)   | 0.786 | 0.861 | 0.822        | VGG19     | 532.46MB | 402.01KB | 0.00076          |
| VGG16 (bn)        | 0.907 | 0.926 | <b>0.916</b> |           |          |          |                  |
| VGG16 (no_bn)     | 0.936 | 0.897 | 0.916        |           |          |          |                  |

normalization parameters after every epoch for testing and evaluation.

The test results are presented in Table [3.](#page-7-2) Table [3](#page-7-2) shows that the models with batch normalization layers have better detection performance. We also conducted experiments on multiple models, and the maximum compression rates of different models are presented in Table [4.](#page-7-3) Memory represents the memory required to save all of the parameters of different DCNN models. Memory∗ represents the memory required to save batch normalization parameters and distilled results of different models. As presented in Table [4,](#page-7-3) the proposed method can effectively reduce the memory size required to save the trained models, demonstrating the model compression effectiveness of the proposed method. For example, VGG19 can have a maximum compression rate of 0.00076 with two compressed soft-label patch images. We also found that the minimum number of compressed images that different models can achieve is not the same, and this feature is related to the maximum compression rate. Thus, the next subsection presents the minimum number of compressed images of different models.

<span id="page-7-0"></span>

### 4.4. Minimum number of compressed images

This subsection presents the minimum number of compressed images of different DCNN models. We found that the minimum number of compressed images that different models can achieve varies. For example, when we set the number of compressed images to 1 with VGG16, the test accuracy was far lower than when the number of compressed images was set to 3 (one image per category). In other words, VGG16 cannot effectively distill the valid information from the training data into

<span id="page-7-3"></span>Table 4: Memory footprints of different models. Memory denotes saving all of the parameters of a model. Memory∗ denotes saving batch normalization parameters and distilled results.

<span id="page-7-4"></span>Table 5: The minimum number of compressed images of different models.

| Model         | Sen   | Spe   | HM           |
|---------------|-------|-------|--------------|
| GoogLeNet (1) | 0.764 | 0.853 | 0.806        |
| GoogLeNet (3) | 0.850 | 0.916 | <b>0.882</b> |
| ResNet18 (1)  | 0.800 | 0.855 | 0.827        |
| ResNet18 (3)  | 0.836 | 0.905 | <b>0.869</b> |
| ResNet34 (1)  | 0.800 | 0.926 | 0.858        |
| ResNet34 (3)  | 0.893 | 0.899 | <b>0.896</b> |
| AlexNet (1)   | 0.671 | 0.895 | 0.767        |
| AlexNet (3)   | 0.793 | 0.884 | <b>0.836</b> |
| VGG16 (1)     | 0.643 | 0.524 | 0.577        |
| VGG16 (2)     | 0.921 | 0.926 | <b>0.923</b> |
| VGG16 (3)     | 0.936 | 0.897 | 0.916        |
| VGG19 (1)     | 0.614 | 0.891 | 0.727        |
| VGG19 (2)     | 0.921 | 0.909 | 0.915        |
| VGG19 (3)     | 0.921 | 0.933 | <b>0.927</b> |

one compressed image.

We used different models, including GoogLeNet, ResNet18, ResNet34, AlexNet, VGG16, and VGG19. First, we set the numbers of compressed images to the minimum number of compressed images that the models can achieve, e.g., GoogLeNet (1) and VGG16 (2). For comparison, we set the numbers of compressed images to 3 (one image per category), e.g., GoogLeNet (3). The distill epochs and steps were set to 1. In the training phase, we performed 400 epochs for GoogLeNet, ResNet18, and AlexNet, and 200 epochs for ResNet34, VGG16, and VGG19. We saved the distilled results and the batch normalization parameters after every epoch for testing and evaluation.

The test results are presented in Tables [5](#page-7-4) and [6.](#page-8-5) Table [5](#page-7-4) shows that GoogLeNet, ResNet18, ResNet34, and AlexNet can effectively distill valid information from the training data into only one compressed soft-label patch image. However, VGG16 and VGG19 cannot effectively distill valid information into one compressed patch image. Since the compressed images were distilled with DCNN models, we think the minimum number of compressed images is related to the number of parameters. Table [6](#page-8-5) shows the parameters and the minimum number of compressed images of different models. The parameter represents

<span id="page-8-5"></span>Table 6: Parameters of different models. Parameter denotes the number of model parameters. Image denotes the minimum number of compressed images.

| Model     | Parameter   | Image |
|-----------|-------------|-------|
| GoogLeNet | 5,984,915   | 1     |
| ResNet18  | 11,176,963  | 1     |
| ResNet34  | 21,285,123  | 1     |
| AlexNet   | 57,000,643  | 1     |
| VGG16     | 134,271,683 | 2     |
| VGG19     | 139,581,379 | 2     |

the number of parameters of different models, and the image indicates the minimum number of compressed images that different models can achieve. As presented in Table [6,](#page-8-5) the minimum number of compressed images relates to the number of parameters. Additionally, the minimum number of compressed images increases as the number of parameters of a DCNN model increases.

<span id="page-8-3"></span>

### 5. Discussion

In Subsection [4.2,](#page-5-3) we showed that we could distill information of the entire gastric X-ray image dataset into only one compressed soft-label patch image for the maximum compression rate. We achieved a high-detection performance with three compressed soft-label images for training the model. Moreover, the visualization of the compressed soft-label images showed that they had been visually anonymized. Subsection [4.3](#page-6-0) presents the results of evaluating the performances of different DCNN models to verify the robustness of the proposed method. We also showed the effectiveness of the batch normalization layers of the proposed method and how to reduce the memory required to save trained models. Furthermore, Subsection [4.4](#page-7-0) presents the relationships between the minimum number of compressed images of different DCNN models and their parameters.

The experimental results showed that our method has limitations. The theoretical proof of the minimum number of compressed images and the number of parameters is complex, and there is no work in this area. Therefore, we only make reasonable inferences and heuristics based on observed phenomena in this paper and leave it for future work. We obtained that the minimum number of compressed images increases as the number of parameters of a DCNN model increases. Additionally, the complexity of distillation significantly increases since our distillation process includes a large number of secondary gradient calculations. Recently, more efficient alternatives to dataset distillation algorithms have emerged to conden-sate small datasets [\[68,](#page-10-16) [69,](#page-10-17) [70,](#page-10-18) [71\]](#page-10-19) $<sup>1</sup>$  $<sup>1</sup>$  $<sup>1</sup>$ . These algorithms can</sup> also be extended for effective anonymous medical data sharing, which will be one of our future works. Although we focused on gastric X-ray image data, our method could also be applied to

other medical images, e.g., endoscopic and CT images, which will be one of our future works. Moreover, our recent studies on self-supervised learning [\[72,](#page-10-20) [73,](#page-10-21) [74,](#page-10-22) [75\]](#page-10-23) can learn discriminative representations from different images without manually annotated labels, which fits well with dataset distillation algorithms.

<span id="page-8-4"></span>

#### 6. Conclusion

This paper proposes a compressed gastric image generation method based on the soft-label dataset distillation for efficient anonymous medical data sharing. The proposed method compresses the entire medical dataset into only one compressed soft-label patch image and reduces the size of a trained model to a few hundredths of its original size. The compressed images obtained after distillation have been visually anonymized; therefore, they do not contain private information of the patients. Furthermore, the proposed method achieves high-detection performance with only a small number of compressed images. The experimental results show that the proposed method can improve the efficiency and security of medical data sharing, which is helpful for future research at the intersection of computer science and biomedicine. Although our study only tested on gastric X-ray image data, we expect the findings and methodology to be applied to other medical images.

#### Declaration of competing interest

None declared.

### Acknowledgments

In this study, the medical data were provided by The University of Tokyo Hospital in Japan. We express our thanks to Nobutake Yamamichi of the Graduate School of Medicine, The University of Tokyo, and Katsuhiro Mabe of the Junpukai Health Maintenance Center. This study was supported in part by AMED Grant Number JP21zf0127004, the Hokkaido University-Hitachi Collaborative Education and Research Support Program, and the MEXT Doctoral program for Data-Related InnoVation Expert Hokkaido University (D-DRIVE-HU) program. This study was conducted on the Data Science Computing System of Education and Research Center for Mathematical and Data Science, Hokkaido University.

#### References

- <span id="page-8-0"></span>[1] J.-J. Yang, J.-Q. Li, Y. Niu, A hybrid solution for privacy preserving medical data sharing in the cloud environment, Futur. Gener. Comp. Syst. 43-44 (2015) 74–86 (Feb. 2015).
- <span id="page-8-1"></span>[2] T. Ali, M. Hussain, W. A. Khan, M. Afzal, J. Hussain, R. Ali, W. Hassan, A. Jamshed, B. H. Kang, S. Lee, Multi-model-based interactive authoring environment for creating shareable medical knowledge, Comput. Meth. Programs Biomed. 150 (2017) 41–72 (2017).
- <span id="page-8-2"></span>[3] E. C. Schiza, T. C. Kyprianou, N. Petkov, C. N. Schizas, Proposal for an ehealth based ecosystem serving national healthcare, IEEE J. Biomed. Health Inform. 23 (3) (2018) 1346–1357 (May 2018).

<span id="page-8-6"></span><sup>1</sup>https://github.com/Guang000/Awesome-Dataset-Distillation

- <span id="page-9-0"></span>[4] C. Xie, H. Cai, Y. Yang, L. Jiang, P. Yang, User profiling in elderly healthcare services in china: Scalper detection, IEEE J. Biomed. Health Inform. 22 (6) (2018) 1796–1806 (Nov. 2018).
- <span id="page-9-1"></span>[5] H. Soni, A. Grando, A. Murcko, S. Diaz, M. Mukundan, N. Idouraine, G. Karway, M. Todd, D. Chern, C. Dye, et al., State of the art and a mixedmethod personalized approach to assess patient perceptions on medical record sharing and sensitivity, J. Biomed. Inform. 101 (2020) 103338 (2020).
- <span id="page-9-2"></span>[6] P. Esmaeilzadeh, T. Mirzaei, S. Dharanikota, The impact of data entry structures on perceptions of individuals with chronic mental disorders and physical diseases towards health information sharing, Int. J. Med. Inform. 141 (2020) 104157 (2020).
- <span id="page-9-3"></span>[7] Y. Ye, J. Shi, D. Zhu, L. Su, Y. Huang, J. Huang, Management of medical and health big data based on integrated learning-based health care system: A review and comparative analysis, Comput. Meth. Programs Biomed. (2021) 106293 (2021).
- <span id="page-9-4"></span>[8] F. K. Dankar, R. Badji, A risk-based framework for biomedical data sharing, J. Biomed. Inform. 66 (2017) 231–240 (2017).
- <span id="page-9-5"></span>[9] A. Krizhevsky, I. Sutskever, G. E. Hinton, Imagenet classification with deep convolutional neural networks, in: Proc. Adv. Neural Inf. Process. Syst., 2012, pp. 1097–1105 (2012).
- <span id="page-9-6"></span>[10] Y. LeCun, Y. Bengio, G. Hinton, Deep learning, Nature 521 (7553) (2015) 436–444 (May 2015).
- <span id="page-9-7"></span>[11] H.-C. Shin, H. R. Roth, M. Gao, L. Lu, Z. Xu, I. Nogues, J. Yao, D. Mollura, R. M. Summers, Deep convolutional neural networks for computeraided detection: Cnn architectures, dataset characteristics and transfer learning, IEEE Trans. Med. Imaging 35 (5) (2016) 1285–1298 (May 2016).
- <span id="page-9-8"></span>[12] G. Litjens, T. Kooi, B. E. Bejnordi, A. A. A. Setio, F. Ciompi, M. Ghafoorian, J. A. Van Der Laak, B. Van Ginneken, C. I. Sanchez, A survey on ´ deep learning in medical image analysis, Med. Image Anal. 42 (2017) 60–88 (Dec. 2017).
- <span id="page-9-9"></span>[13] J. Luo, M. Wu, D. Gopukumar, Y. Zhao, Big data application in biomedical research and health care: a literature review, Biomed. Inform. Insights 8 (Jan. 2016).
- <span id="page-9-10"></span>[14] A. Tsymbal, E. Meissner, M. Kelm, M. Kramer, Towards cloud-based image-integrated similarity search in big data, in: Proc. IEEE Int. Conf. Biomed. Health Inform., 2014, pp. 593–596 (2014).
- <span id="page-9-11"></span>[15] N. S. Sohoni, C. R. Aberger, M. Leszczynski, J. Zhang, C. Ré, Lowmemory neural network training: A technical report, arXiv:1904.10631 (2019).
- <span id="page-9-12"></span>[16] P. Narendra K., P. Vinod, Medical image protection using genetic algorithm operations, Soft Comput. 20 (2) (2016) 763–772 (2016).
- <span id="page-9-13"></span>[17] J. A. Olvera-López, J. A. Carrasco-Ochoa, J. F. Martínez-Trinidad, J. Kittler, A review of instance selection methods, Artif. Intell. Rev. 34 (2) (2010) 133–143 (May 2010).
- <span id="page-9-14"></span>[18] O. Bachem, M. Lucic, A. Krause, Practical coreset constructions for machine learning, arXiv:1703.06476 (2017).
- <span id="page-9-15"></span>[19] Z. Liu, M. Sun, T. Zhou, G. Huang, T. Darrell, Rethinking the value of network pruning, in: Proc. Int. Conf. Learn. Represent., 2019 (2019).
- <span id="page-9-16"></span>[20] A. Gkoulalas-Divanis, G. Loukides, J. Sun, Publishing data from electronic health records while preserving privacy: A survey of algorithms, J. Biomed. Inform. 50 (2014) 4–19 (2014).
- <span id="page-9-17"></span>[21] D. McGraw, J. X. Dempsey, L. Harris, J. Goldman, Privacy as an enabler, not an impediment: building trust into health information exchange, Health Aff. 28 (2) (2009) 416–427 (Mar. 2009).
- <span id="page-9-18"></span>[22] B. Malin, D. Karp, R. H. Scheuermann, Technical and policy approaches to balancing patient privacy and data sharing in clinical and translational research, J. Invest. Med. 58 (1) (2010) 11–18 (2010).
- <span id="page-9-19"></span>[23] H. Bouzelat, C. Quantin, L. Dusserre, Extraction and anonymity protocol of medical file., in: Proc. AMIA Annu. Fall Symp., 1996, pp. 323–327 (1996).
- <span id="page-9-20"></span>[24] G. Loukides, A. Gkoulalas-Divanis, B. Malin, Anonymization of electronic medical records for validating genome-wide association studies, Proc. Natl. Acad. Sci. 107 (17) (2010) 7898–7903 (2010).
- <span id="page-9-21"></span>[25] A. Khedr, G. Gulak, Securemed: Secure medical computation using gpuaccelerated homomorphic encryption scheme, IEEE J. Biomed. Health Inform. 22 (2) (2017) 597–606 (Mar. 2017).
- <span id="page-9-22"></span>[26] J. Ferlay, I. Soerjomataram, R. Dikshit, S. Eser, C. Mathers, M. Rebelo, D. M. Parkin, D. Forman, F. Bray, Cancer incidence and mortality worldwide: sources, methods and major patterns in globocan 2012, Int. J. Can-

cer 136 (5) (2015) E359–E386 (Sept. 2015).

- <span id="page-9-23"></span>[27] N. Uemura, S. Okamoto, S. Yamamoto, N. Matsumura, S. Yamaguchi, M. Yamakido, K. Taniyama, N. Sasaki, R. J. Schlemper, Helicobacter pylori infection and the development of gastric cancer, N. Engl. J. Med. 345 (11) (2001) 784–789 (Sept. 2001).
- <span id="page-9-24"></span>[28] H. Ohata, S. Kitauchi, N. Yoshimura, K. Mugitani, M. Iwane, H. Nakamura, A. Yoshikawa, K. Yanaoka, K. Arii, H. Tamai, et al., Progression of chronic atrophic gastritis associated with helicobacter pylori infection increases risk of gastric cancer, Int. J. Cancer 109 (1) (2004) 138–143 (Dec. 2004).
- <span id="page-9-25"></span>[29] M. Kanai, R. Togo, T. Ogawa, M. Haseyama, Gastritis detection from gastric x-ray images via fine-tuning of patch-based deep convolutional neural network, in: Proc. IEEE Int. Conf. Image Process., 2019, pp. 1371– 1375 (2019).
- <span id="page-9-26"></span>[30] R. Togo, N. Yamamichi, K. Mabe, Y. Takahashi, C. Takeuchi, M. Kato, N. Sakamoto, K. Ishihara, T. Ogawa, M. Haseyama, Detection of gastritis by a deep convolutional neural network from double-contrast upper gastrointestinal barium x-ray radiography, J. Gastroenterol. 54 (4) (2019) 321–329 (Oct. 2019).
- <span id="page-9-27"></span>[31] T. Wang, J.-Y. Zhu, A. Torralba, A. A. Efros, Dataset distillation, arXiv:1811.10959 (2018).
- <span id="page-9-28"></span>[32] G. Li, R. Togo, T. Ogawa, M. Haseyama, Soft-label anonymous gastric x-ray image distillation, in: Proc. IEEE Int. Conf. Image Process., 2020, pp. 305–309 (2020).
- <span id="page-9-29"></span>[33] I. W. Tsang, J. T. Kwok, P.-M. Cheung, Core vector machines: Fast svm training on very large data sets, J. Mach. Learn. Res. 6 (2005) 363–392 (Apr. 2005).
- <span id="page-9-30"></span>[34] S. Har-Peled, A. Kushal, Smaller coresets for k-median and k-means clustering, Discret. Comput. Geom. 37 (1) (2007) 3–19 (2007).
- <span id="page-9-31"></span>[35] T. Campbell, T. Broderick, Bayesian coreset construction via greedy iterative geodesic ascent, in: Proc. Int. Conf. Mach. Learn., 2018 (2018).
- <span id="page-9-32"></span>[36] A. Angelova, Y. Abu-Mostafam, P. Perona, Pruning training sets for learning of object categories, in: Proc. IEEE Conf. Comput. Vision Pattern Recognit., 2005, pp. 494–501 (2005).
- <span id="page-9-33"></span>[37] P. F. Felzenszwalb, R. B. Girshick, D. McAllester, D. Ramanan, Object detection with discriminatively trained part-based models, IEEE Trans. Pattern Anal. Mach. Intell. 32 (9) (2009) 1627–1645 (Sept. 2009).
- <span id="page-9-34"></span>[38] A. Lapedriza, H. Pirsiavash, Z. Bylinskii, A. Torralba, Are all training examples equally valuable?, arXiv:1311.6510 (2013).
- <span id="page-9-35"></span>[39] D. A. Cohn, Z. Ghahramani, M. I. Jordan, Active learning with statistical models, J. Artif. Intell. Res. 4 (1996) 129–145 (Mar. 1996).
- <span id="page-9-36"></span>[40] S. Tong, E. Chang, Support vector machine active learning for image retrieval, in: Proc. ACM Int. Conf. Multimedia, 2001, pp. 107–118 (2001).
- <span id="page-9-37"></span>[41] O. Sener, S. Savarese, Active learning for convolutional neural networks: A core-set approach, in: Proc. Int. Conf. Learn. Represent., 2018 (2018).
- <span id="page-9-38"></span>[42] E. L. Denton, W. Zaremba, J. Bruna, Y. LeCun, R. Fergus, Exploiting linear structure within convolutional networks for efficient evaluation, in: Proc. Adv. Neural Inf. Process. Syst., 2014, pp. 1269–1277 (2014).
- <span id="page-9-39"></span>[43] J. Ba, R. Caruana, Do deep nets really need to be deep?, in: Proc. Adv. Neural Inf. Process. Syst., 2014, pp. 2654–2662 (2014).
- <span id="page-9-40"></span>[44] S. Han, J. Pool, J. Tran, W. Dally, Learning both weights and connections for efficient neural network, in: Proc. Adv. Neural Inf. Process. Syst., 2015, pp. 1135–1143 (2015).
- <span id="page-9-41"></span>[45] S. Han, H. Mao, W. J. Dally, Deep compression: Compressing deep neural networks with pruning, trained quantization and huffman coding, in: Proc. Int. Conf. Learn. Represent., 2016 (2016).
- <span id="page-9-42"></span>[46] S. Han, X. Liu, H. Mao, J. Pu, A. Pedram, M. A. Horowitz, W. J. Dally, Eie: efficient inference engine on compressed deep neural network, SIGARCH Comput. Archit. News 44 (3) (2016) 243–254 (Jun. 2016).
- <span id="page-9-43"></span>[47] G. Hinton, O. Vinyals, J. Dean, Distilling the knowledge in a neural network, in: Proc. Adv. Neural Inf. Process. Syst., Workshop, 2014 (2014).
- <span id="page-9-44"></span>[48] Z. Liu, J. Li, Z. Shen, G. Huang, S. Yan, C. Zhang, Learning efficient convolutional networks through network slimming, in: Proc. IEEE/CVF Int. Conf. Comput. Vision, 2017, pp. 2736–2744 (2017).
- <span id="page-9-45"></span>[49] J.-H. Luo, J. Wu, W. Lin, Thinet: A filter level pruning method for deep neural network compression, in: Proc. IEEE/CVF Int. Conf. Comput. Vision, 2017, pp. 5058–5066 (2017).
- <span id="page-9-46"></span>[50] D. Molchanov, A. Ashukha, D. Vetrov, Variational dropout sparsifies deep neural networks, in: Proc. Int. Conf. Mach. Learn., 2017 (2017).
- <span id="page-9-47"></span>[51] A. Azaria, A. Ekblaw, T. Vieira, A. Lippman, Medrec: Using blockchain for medical data access and permission management, in: Proc. IEEE Int.

Conf. Open Big Data, 2016, pp. 25–30 (2016).

- <span id="page-10-0"></span>[52] K. Fan, S. Wang, Y. Ren, H. Li, Y. Yang, Medblock: Efficient and secure medical data sharing via blockchain, J. Med. Syst. 42 (2018) 136 (Jun. 2018).
- <span id="page-10-1"></span>[53] Y. Chen, L. Meng, H. Zhou, G. Xue, A blockchain-based medical data sharing mechanism with attribute-based access control and privacy protection, Wirel. Commun. Mob. Comput. 2021 (2021).
- <span id="page-10-2"></span>[54] L. Tan, K. Yu, N. Shi, C. Yang, W. Wei, H. Lu, Towards secure and privacy-preserving data sharing for covid-19 medical records: a blockchain-empowered approach, IEEE Trans. Netw. Sci. Eng. 9 (1) (2021) 271–281 (2021).
- <span id="page-10-3"></span>[55] R. Kumar, W. Wang, J. Kumar, T. Yang, A. Khan, W. Ali, I. Ali, An integration of blockchain and ai for secure data sharing and detection of ct images for the hospitals, Comput. Med. Imaging Graph. 87 (2021) 101812 (2021).
- <span id="page-10-4"></span>[56] P. Van Gorp, M. Comuzzi, Lifelong personal health data and application software via virtual machines in the cloud, IEEE J. Biomed. Health Inform. 18 (1) (2013) 36–45 (Jan. 2013).
- <span id="page-10-5"></span>[57] F. Alshagathrh, S. A. Khan, N. Alothmany, N. Al-Rawashdeh, M. Househ, Building a cloud-based data sharing model for the saudi national registry for implantable medical devices: Results of a readiness assessment, Int. J. Med. Inform. 118 (2018) 113–119 (2018).
- <span id="page-10-6"></span>[58] T. Doel, D. I. Shakir, R. Pratt, M. Aertsen, J. Moggridge, E. Bellon, A. L. David, J. Deprest, T. Vercauteren, S. Ourselin, Gift-cloud: A data sharing and collaboration platform for medical imaging research, Comput. Meth. Programs Biomed. 139 (2017) 181–190 (2017).
- <span id="page-10-7"></span>[59] Y. Bao, W. Qiu, P. Tang, X. Cheng, Efficient, revocable, and privacypreserving fine-grained data sharing with keyword search for the cloudassisted medical iot system, IEEE J. Biomed. Health Inform. 26 (5) (2021) 2041–2051 (2021).
- <span id="page-10-8"></span>[60] J. Sun, D. Chen, N. Zhang, G. Xu, M. Tang, X. Nie, M. Cao, A privacyaware and traceable fine-grained data delivery system in cloud-assisted healthcare iiot, IEEE Internet Things J. 8 (12) (2021) 10034–10046 (2021).
- <span id="page-10-9"></span>[61] T. Dong, B. Zhao, L. Lyu, Privacy for free: How does dataset condensation help privacy?, in: Proc. Int. Conf. Mach. Learn., 2022 (2022).
- <span id="page-10-10"></span>[62] G. Li, R. Togo, T. Ogawa, M. Haseyama, Complexity evaluation of medical image data for classification problem based on spectral clustering, in: Proc. IEEE Glob. Conf. Consum. Electron., IEEE, 2020, pp. 667–669  $(2020)$
- <span id="page-10-11"></span>[63] I. Sucholutsky, M. Schonlau, Soft-label dataset distillation and text dataset distillation, arXiv:1910.02551 (2019).
- <span id="page-10-12"></span>[64] A. Paszke, S. Gross, F. Massa, A. Lerer, J. Bradbury, G. Chanan, T. Killeen, Z. Lin, N. Gimelshein, L. Antiga, et al., Pytorch: An imperative style, high-performance deep learning library, in: Proc. Adv. Neural Inf. Process. Syst., 2019, pp. 8024–8035 (2019).
- <span id="page-10-13"></span>[65] K. He, X. Zhang, S. Ren, J. Sun, Deep residual learning for image recognition, in: Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2016, pp. 770–778 (2016).
- <span id="page-10-14"></span>[66] K. Simonyan, A. Zisserman, Very deep convolutional networks for largescale image recognition, in: Proc. Int. Conf. Learn. Represent., 2015  $(2015)$ .
- <span id="page-10-15"></span>[67] C. Szegedy, W. Liu, Y. Jia, P. Sermanet, S. Reed, D. Anguelov, D. Erhan, V. Vanhoucke, A. Rabinovich, Going deeper with convolutions, in: Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2015, pp. 1–9 (2015).
- <span id="page-10-16"></span>[68] B. Zhao, K. R. Mopuri, H. Bilen, Dataset condensation with gradient matching, in: Proc. Int. Conf. Learn. Represent., 2021 (2021).
- <span id="page-10-17"></span>[69] B. Zhao, H. Bilen, Dataset condensation with differentiable siamese augmentation, in: Proc. Int. Conf. Mach. Learn., 2021 (2021).
- <span id="page-10-18"></span>[70] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, J.-Y. Zhu, Dataset distillation by matching training trajectories, in: Proc. IEEE/CVF Comput. Soc. Conf. Comput. Vis. Pattern Recognit., 2022, pp. 4750–4759 (2022).
- <span id="page-10-19"></span>[71] Y. Zhou, E. Nezhadarya, J. Ba, Dataset distillation using neural feature regression, arXiv:2206.00719 (2022).
- <span id="page-10-20"></span>[72] G. Li, R. Togo, T. Ogawa, M. Haseyama, Self-supervised learning for gastritis detection with gastric x-ray images, arXiv:2104.02864 (2021).
- <span id="page-10-21"></span>[73] G. Li, R. Togo, T. Ogawa, M. Haseyama, Triplet self-supervised learning for gastritis detection with scarce annotations, in: Proc. IEEE Glob. Conf. Consum. Electron., 2021 (2021).
- <span id="page-10-22"></span>[74] G. Li, R. Togo, T. Ogawa, M. Haseyama, Self-knowledge distillation based self-supervised learning for covid-19 detection from chest x-ray

images, in: Proc. IEEE Int. Conf. Acoustics, Speech and Signal Process., IEEE, 2022, pp. 1371–1375 (2022).

<span id="page-10-23"></span>[75] G. Li, R. Togo, T. Ogawa, M. Haseyama, Tribyol: Triplet byol for selfsupervised representation learning, in: Proc. IEEE Int. Conf. Acoustics, Speech and Signal Process., IEEE, 2022, pp. 3458–3462 (2022).