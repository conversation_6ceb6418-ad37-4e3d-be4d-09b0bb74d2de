{"table_of_contents": [{"title": "Condensed Composite Memory Continual Learning", "heading_level": null, "page_id": 0, "polygon": [[51.75, 54.0], [559.40625, 54.0], [559.40625, 75.796875], [51.75, 75.796875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 428.25], [213.75, 428.25], [213.75, 437.765625], [133.5, 437.765625]]}, {"title": "II. INCREMENTAL CLASS LEARNING", "heading_level": null, "page_id": 1, "polygon": [[95.25, 383.25], [252.0615234375, 383.25], [252.0615234375, 392.51953125], [95.25, 392.51953125]]}, {"title": "III. PROPOSED METHOD", "heading_level": null, "page_id": 1, "polygon": [[382.5, 489.75], [490.5, 489.75], [490.5, 499.640625], [382.5, 499.640625]]}, {"title": "A. Dataset Condensation", "heading_level": null, "page_id": 1, "polygon": [[309.75, 586.5], [416.25, 586.5], [416.25, 595.546875], [309.75, 595.546875]]}, {"title": "B. Composite Memory", "heading_level": null, "page_id": 2, "polygon": [[310.5, 274.5], [405.75, 274.5], [405.75, 284.23828125], [310.5, 284.23828125]]}, {"title": "Algorithm 1 Dataset condensation", "heading_level": null, "page_id": 3, "polygon": [[47.775146484375, 261.75], [192.0, 261.75], [192.0, 272.25], [47.775146484375, 272.25]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 3, "polygon": [[47.88720703125, 621.75], [103.5, 621.75], [103.5, 631.8984375], [47.88720703125, 633.4453125]]}, {"title": "Algorithm 2 Training procedure", "heading_level": null, "page_id": 3, "polygon": [[311.25, 48.75], [446.25, 48.75], [446.25, 58.15283203125], [311.25, 59.69970703125]]}, {"title": "IV. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[133.5, 336.0], [214.5, 336.0], [214.5, 346.693359375], [133.5, 346.693359375]]}, {"title": "<PERSON>. <PERSON>", "heading_level": null, "page_id": 4, "polygon": [[47.25, 350.25], [98.912109375, 350.25], [98.912109375, 360.421875], [47.25, 360.421875]]}, {"title": "B. DNN Architecture", "heading_level": null, "page_id": 4, "polygon": [[48.0, 501.75], [135.5185546875, 501.75], [135.5185546875, 511.62890625], [48.0, 511.62890625]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[311.25, 51.75], [399.75, 51.75], [399.75, 62.31005859375], [311.25, 62.31005859375]]}, {"title": "D. <PERSON>", "heading_level": null, "page_id": 4, "polygon": [[310.5, 336.0], [364.5, 336.0], [364.5, 346.306640625], [310.5, 346.306640625]]}, {"title": "E. Results", "heading_level": null, "page_id": 4, "polygon": [[310.5, 621.0], [355.306640625, 621.0], [355.306640625, 631.51171875], [310.5, 631.51171875]]}, {"title": "F. Qualitative visual comparison", "heading_level": null, "page_id": 5, "polygon": [[47.8125, 598.5], [184.5, 598.5], [184.5, 608.6953125], [47.8125, 608.6953125]]}, {"title": "V. CONCLUSION", "heading_level": null, "page_id": 5, "polygon": [[401.02734375, 561.75], [474.0, 561.75], [474.0, 571.95703125], [401.02734375, 571.95703125]]}, {"title": "TABLE II", "heading_level": null, "page_id": 7, "polygon": [[156.0, 52.5], [191.6982421875, 52.5], [191.6982421875, 61.72998046875], [156.0, 61.72998046875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 7, "polygon": [[145.5, 529.5], [203.25, 529.5], [203.25, 538.3125], [145.5, 538.3125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 100], ["Text", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4287, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 695], ["Line", 126], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 3], ["Reference", 3], ["Equation", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 748], ["Line", 131], ["Equation", 4], ["Reference", 4], ["Text", 3], ["TextInlineMath", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 857, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 806], ["Line", 110], ["ListItem", 16], ["Text", 6], ["SectionHeader", 3], ["ListGroup", 3], ["Reference", 3], ["TextInlineMath", 2], ["Code", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 108], ["TableCell", 40], ["Text", 7], ["SectionHeader", 6], ["Reference", 3], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1305, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 118], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 984, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Line", 82], ["Span", 44], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 993], ["TableCell", 448], ["Line", 127], ["ListItem", 27], ["Reference", 27], ["SectionHeader", 2], ["ListGroup", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5085, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Condensed_Composite_Memory_Continual_Learning"}