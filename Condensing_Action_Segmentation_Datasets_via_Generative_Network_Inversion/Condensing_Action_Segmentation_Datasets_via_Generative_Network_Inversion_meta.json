{"table_of_contents": [{"title": "Condensing Action Segmentation Datasets via Generative Network Inversion", "heading_level": null, "page_id": 0, "polygon": [[69.75, 105.75], [541.5, 105.0], [541.5, 119.0126953125], [69.75, 119.25]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[153.0, 213.75], [199.7666015625, 213.75], [199.7666015625, 225.650390625], [153.0, 225.650390625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[57.75, 454.5], [135.75, 454.5], [135.75, 465.609375], [57.75, 465.609375]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[315.75, 72.0], [401.25, 72.0], [401.25, 83.53125], [315.75, 83.53125]]}, {"title": "3. Dataset Condensation for TAS", "heading_level": null, "page_id": 1, "polygon": [[315.75, 629.19140625], [484.5, 629.19140625], [484.5, 640.01953125], [315.75, 640.01953125]]}, {"title": "3.1. Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[315.75, 648.0], [400.130859375, 648.0], [400.130859375, 658.96875], [315.75, 658.96875]]}, {"title": "3.2. Task Formulation", "heading_level": null, "page_id": 2, "polygon": [[57.0, 648.0], [161.96484375, 648.0], [161.96484375, 658.96875], [57.0, 658.96875]]}, {"title": "3.3. Generative Feature & Temporal Condensation", "heading_level": null, "page_id": 2, "polygon": [[315.75, 516.0], [554.25, 516.0], [554.25, 526.32421875], [315.75, 526.32421875]]}, {"title": "3.4. Di<PERSON> Sequence Sampling", "heading_level": null, "page_id": 3, "polygon": [[315.75, 599.25], [466.5, 599.25], [466.5, 609.85546875], [315.75, 609.85546875]]}, {"title": "3.5. <PERSON><PERSON><PERSON> for TAS", "heading_level": null, "page_id": 4, "polygon": [[57.0, 383.25], [161.25, 383.25], [161.25, 393.6796875], [57.0, 393.6796875]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[57.0, 629.25], [136.5, 629.25], [136.5, 640.40625], [57.0, 640.40625]]}, {"title": "4.1. Datasets and Evaluation", "heading_level": null, "page_id": 4, "polygon": [[57.0, 648.0], [192.0, 648.0], [192.0, 658.58203125], [57.0, 658.58203125]]}, {"title": "4.2. Implementation", "heading_level": null, "page_id": 4, "polygon": [[315.75, 275.25], [411.75, 275.25], [411.75, 285.78515625], [315.75, 285.78515625]]}, {"title": "4.3. Effectiveness", "heading_level": null, "page_id": 5, "polygon": [[57.0, 600.0], [138.75, 600.0], [138.75, 611.015625], [57.0, 611.015625]]}, {"title": "4.4. Ablation and Hyper-parameter Study", "heading_level": null, "page_id": 6, "polygon": [[57.0, 491.25], [255.0, 491.25], [255.0, 502.734375], [57.0, 502.734375]]}, {"title": "5. Incremental Action Segmentation", "heading_level": null, "page_id": 7, "polygon": [[57.0, 623.25], [243.75, 623.25], [243.75, 634.21875], [57.0, 634.21875]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[315.75, 503.89453125], [387.0, 503.89453125], [387.0, 514.72265625], [315.75, 514.72265625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[57.0, 72.0], [114.75, 72.0], [114.75, 83.67626953125], [57.0, 83.67626953125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 82], ["Text", 6], ["SectionHeader", 3], ["Reference", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 877, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 104], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 821], ["Line", 124], ["TextInlineMath", 5], ["Reference", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 913], ["Line", 115], ["TextInlineMath", 8], ["Equation", 6], ["Reference", 6], ["Text", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["Line", 122], ["Text", 10], ["TextInlineMath", 6], ["Equation", 5], ["SectionHeader", 4], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 528], ["TableCell", 477], ["Line", 74], ["Text", 7], ["TextInlineMath", 2], ["Reference", 2], ["Table", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["TableCell", 245], ["Line", 84], ["Reference", 5], ["Caption", 4], ["Text", 4], ["Table", 3], ["TableGroup", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 16720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 87], ["TableCell", 85], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3857, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 112], ["ListItem", 36], ["Reference", 36], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 28], ["ListItem", 10], ["Reference", 10], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Condensing_Action_Segmentation_Datasets_via_Generative_Network_Inversion"}