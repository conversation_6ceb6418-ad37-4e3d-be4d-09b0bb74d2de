{"table_of_contents": [{"title": "Condensing Graphs via One-Step Gradient Matching", "heading_level": null, "page_id": 0, "polygon": [[97.5, 83.25], [513.0, 83.25], [513.0, 99.6767578125], [97.5, 99.6767578125]]}, {"title": "KEYWORDS", "heading_level": null, "page_id": 0, "polygon": [[309.5859375, 250.5], [380.25, 250.5], [380.25, 261.615234375], [309.5859375, 261.615234375]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[316.5, 282.0], [405.75, 282.0], [405.75, 290.619140625], [316.5, 290.619140625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[315.263671875, 356.25], [420.75, 356.25], [420.75, 367.76953125], [315.263671875, 367.76953125]]}, {"title": "arXiv:2206.07746v3 [cs.LG] 8 Sep 2022\narXiv:2206.07746v3 [cs.LG] 8 Sep 2022", "heading_level": null, "page_id": 0, "polygon": [[12.75, 216.75], [38.287353515625, 216.75], [38.287353515625, 554.2599945068359], [12.75, 554.2599945068359]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[52.5, 249.75], [113.47998046875, 249.75], [113.47998046875, 260.455078125], [52.5, 260.455078125]]}, {"title": "CCS CONCEPTS", "heading_level": null, "page_id": 0, "polygon": [[52.5, 549.75], [135.0, 549.75], [135.0, 561.12890625], [52.5, 561.12890625]]}, {"title": "2 THE PROPOSED FRAMEWORK", "heading_level": null, "page_id": 1, "polygon": [[52.5, 652.5], [228.90234375, 652.5], [228.90234375, 662.44921875], [52.5, 662.44921875]]}, {"title": "2.1 Gradient Matching as the Objective", "heading_level": null, "page_id": 1, "polygon": [[317.25, 315.75], [516.0, 315.75], [516.0, 325.810546875], [317.25, 325.810546875]]}, {"title": "2.2 Learning Discrete Graph Structure", "heading_level": null, "page_id": 2, "polygon": [[52.5, 129.2607421875], [249.0, 129.2607421875], [249.0, 139.3154296875], [52.5, 139.3154296875]]}, {"title": "2.3 One-Step Gradient Matching", "heading_level": null, "page_id": 2, "polygon": [[316.5, 190.5], [483.75, 190.5], [483.75, 201.09375], [316.5, 201.09375]]}, {"title": "2.4 Final Objective and Training Algorithm", "heading_level": null, "page_id": 3, "polygon": [[52.5, 581.25], [275.25, 581.25], [275.25, 591.6796875], [52.5, 591.6796875]]}, {"title": "3 EXPERIMENT", "heading_level": null, "page_id": 4, "polygon": [[52.5, 462.75], [142.5, 462.75], [142.5, 474.1171875], [52.5, 474.1171875]]}, {"title": "3.1 Experimental settings", "heading_level": null, "page_id": 4, "polygon": [[52.5, 542.953125], [187.5146484375, 542.953125], [187.5146484375, 553.78125], [52.5, 553.78125]]}, {"title": "3.2 Performance with Condensed Graphs", "heading_level": null, "page_id": 5, "polygon": [[52.5, 246.75], [264.75, 246.75], [264.75, 257.361328125], [52.5, 257.361328125]]}, {"title": "3.3 Further Investigation", "heading_level": null, "page_id": 6, "polygon": [[52.5, 334.5], [184.5, 334.5], [184.5, 344.56640625], [52.5, 344.56640625]]}, {"title": "3.4 Node Classification", "heading_level": null, "page_id": 7, "polygon": [[52.5, 444.0], [174.216796875, 444.0], [174.216796875, 454.39453125], [52.5, 454.39453125]]}, {"title": "4 RELATED WORK", "heading_level": null, "page_id": 7, "polygon": [[317.25, 225.0], [422.841796875, 225.0], [422.841796875, 237.251953125], [317.25, 237.251953125]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 7, "polygon": [[316.16015625, 663.75], [405.75, 663.75], [405.75, 674.4375], [316.16015625, 674.4375]]}, {"title": "ACKNOWLEDGEMENT", "heading_level": null, "page_id": 8, "polygon": [[52.5, 193.5], [173.25, 193.5], [173.25, 204.380859375], [52.5, 204.380859375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[52.5, 273.0], [123.75, 273.0], [123.75, 282.111328125], [52.5, 282.111328125]]}, {"title": "A EXPERIMENTAL SETUP", "heading_level": null, "page_id": 9, "polygon": [[52.5, 147.75], [195.75, 147.75], [195.75, 157.4912109375], [52.5, 157.4912109375]]}, {"title": "A.1 Algorithm", "heading_level": null, "page_id": 9, "polygon": [[52.5, 164.25], [132.75, 164.25], [132.75, 174.1201171875], [52.5, 174.1201171875]]}, {"title": "A.2 Dataset Statistics and Code", "heading_level": null, "page_id": 9, "polygon": [[52.5, 423.0], [214.259765625, 423.0], [214.259765625, 433.51171875], [52.5, 433.51171875]]}, {"title": "B PROOFS", "heading_level": null, "page_id": 9, "polygon": [[316.5, 84.75], [380.70703125, 84.75], [380.70703125, 95.0361328125], [316.5, 95.0361328125]]}, {"title": "B.1 Proof of Theorem 1", "heading_level": null, "page_id": 9, "polygon": [[316.16015625, 101.25], [441.0, 101.25], [441.0, 111.181640625], [316.16015625, 111.181640625]]}, {"title": "I. For f_{\\theta} with sum pooling:", "heading_level": null, "page_id": 9, "polygon": [[316.5, 498.0], [426.75, 498.75], [426.75, 507.75], [316.5, 507.0]]}, {"title": "II. For f_\\theta with mean pooling:", "heading_level": null, "page_id": 10, "polygon": [[315.5625, 174.0], [434.25, 174.0], [434.25, 183.884765625], [315.5625, 183.884765625]]}, {"title": "B.2 <PERSON>rem for Node Classification Case", "heading_level": null, "page_id": 10, "polygon": [[316.5, 282.69140625], [532.810546875, 282.69140625], [532.810546875, 292.359375], [316.5, 292.359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 104], ["Text", 17], ["SectionHeader", 7], ["Footnote", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6164, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 143], ["Text", 5], ["ListItem", 4], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1258], ["Line", 262], ["TextInlineMath", 9], ["Equation", 8], ["Text", 5], ["Reference", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 871], ["Line", 186], ["Text", 9], ["Equation", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1193, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["TableCell", 440], ["Line", 93], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 15118, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 110], ["TableCell", 34], ["Text", 7], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1439, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["Line", 151], ["Text", 8], ["Reference", 7], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2330, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 116], ["TableCell", 36], ["Reference", 5], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListItem", 2], ["Table", 1], ["Caption", 1], ["Figure", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5985, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["Line", 146], ["ListItem", 53], ["Reference", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1407], ["TableCell", 168], ["Line", 158], ["ListItem", 19], ["TextInlineMath", 9], ["Reference", 9], ["SectionHeader", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Caption", 2], ["Table", 2], ["ListGroup", 2], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6862, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1962], ["Line", 470], ["Equation", 12], ["TextInlineMath", 11], ["Text", 7], ["Reference", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 10, "llm_error_count": 1, "llm_tokens_used": 22754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 74], ["Text", 3], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Condensing_Graphs_via_One-Step_Gradient_Matching"}