# <span id="page-0-1"></span>D <sup>4</sup>M: Dataset Distillation via Disentangled Diffusion Model

Duo  $Su^{1,5,6,\dagger}$ 

jie <PERSON>u<sup>2,b,6,\$\dagger\$</sup>

<PERSON><PERSON><PERSON><sup>2,5,6,†</sup> <PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>4,5,6,7,\*</sup> <PERSON><sup>8</sup>

Dowell Targe

<sup>1</sup>School of Computer Science and Technology, UCAS  $\frac{2 \text{Sino-Danish College, UCAS}}{}$  $3$ Department of Computer Science, NCSU  $4$ School of Economics and Management, UCAS <sup>5</sup>Research Center on Fictitious Economy and Data Science, CAS <sup>6</sup>Key Laboratory of Big Data Mining and Knowledge Management, CAS <sup>7</sup>MOE Social Science Laboratory of Digital Economic Forecasts and Policy Simulation, UCAS

8 Institute of Computing Technology, CAS

<https://junjie31.github.io/D4M/>

## Abstract

*Dataset distillation offers a lightweight synthetic dataset for fast network training with promising test accuracy. To imitate the performance of the original dataset, most approaches employ bi-level optimization and the distillation space relies on the matching architecture. Nevertheless, these approaches either suffer significant computational costs on large-scale datasets or experience performance decline on cross-architectures. We advocate for designing an economical dataset distillation framework that is independent of the matching architectures. With empirical observations, we argue that constraining the consistency of the real and synthetic image spaces will enhance the cross-architecture generalization. Motivated by this, we introduce Dataset Distillation via Disentangled Diffusion* Model ( $D^4M$ ), an efficient framework for dataset distilla*tion.* Compared to architecture-dependent methods,  $D^4M$ *employs latent diffusion model to guarantee consistency and incorporates label information into category prototypes. The distilled datasets are versatile, eliminating the need for repeated generation of distinct datasets for various architectures. Through comprehensive experiments, D*<sup>4</sup>*M demonstrates superior performance and robust generalization, surpassing the SOTA methods across most aspects.*

## 1. Introduction

The rapid growth in machine learning, resulting in large models and vast datasets, poses a challenge to researchers due to the escalating computational and storage demands. Can the 'Divide-and-Conquer' algorithm [\[1\]](#page-8-0) mitigate this

<span id="page-0-0"></span>Image /page/0/Figure/13 description: The image displays a diagram comparing three different matching strategies in dataset distillation: (a) Synthesis-Time Matching, (b) Dual-Time Matching, and (c) Training-Time Matching. Each strategy involves a 'Model' processing data from a database. In (a), the model's output is used for prediction, and a dashed arrow indicates a matching process. In (b), the model's output is split into 'Soft Label' and 'Prediction', with a lock symbol on the model for soft label generation, and dashed arrows show the matching process. In (c), a 'D4M' model takes real images and label text as input, processes them, and outputs data to a database, which is then used by two models to generate 'Soft Label' and 'Prediction', similar to (b). A legend at the bottom clarifies the symbols: a database icon represents real images, a document icon represents label text, a cylinder icon represents general synthetic images, and a cylinder with a lock icon represents architecture-specific synthetic images. The matching process is indicated by dashed arrows.

Figure 1. Comparison of various matching strategies in dataset distillation. (a) The bi-level optimization implements data matching at synthesis time. (b) Dual-Time Matching strategy decouples the bi-level optimization process into synthesis time and training time to save computational overhead. (c)  $D^4M$  utilizes multimodal features (image and texts) to synthesize high-quality images.  $D<sup>4</sup>M$  does not require matching process at Synthesis-Time.

challenge? From the perspective of dataset, recent research extends the coreset selection [\[3,](#page-8-1) [7,](#page-8-2) [39\]](#page-9-0) to distillation techniques aimed at reducing dataset scales. Dataset Distillation (DD) aims to synthesize a small dataset  $S$  from the original large-scale dataset  $\mathcal{T}$ , where  $|\mathcal{S}| \ll |\mathcal{T}|$ . The information in  $\mathcal T$  is condensed into a small dataset through DD. Initially, the DD framework uses the bi-level optimization to generate datasets where the inner loop updates the network used for testing the classification performance and the outer loop synthesizes images according to matching strategies, such as gradient [\[24,](#page-8-3) [51,](#page-9-1) [53\]](#page-9-2), distribution [\[40,](#page-9-3) [52\]](#page-9-4) or trajectory [\[4,](#page-8-4) [8\]](#page-8-5).

<sup>†</sup> Equal contribution. ∗ Corresponding author.

<span id="page-1-2"></span>Unfortunately, the existing solutions of DD mainly focus on small and simple datasets, such as CIFAR [\[21\]](#page-8-6) and MNIST [\[23,](#page-8-7) [44\]](#page-9-5). When it comes to large-scale and highresolution datasets such as ImageNet [\[9\]](#page-8-8), there exists unaffordable computational requirements and reduced performance. Another challenge in DD is the cross-architecture generalization. Previous methods conduct data matching within a fixed discriminative architecture, which makes the output space biased from the original image space. As demonstrated in Fig. [2,](#page-1-0) this kind of dataset may be insightful for the networks but suffers from the lack of semantic information for humankind. Furthermore, the dataset has to be distilled from scratch again and again to adapt to the emerging network architectures. Obviously, these limitations constrain the scientific value and practical utility of the current solutions. In this paper, we argue that an ideal DD method should meet the following properties.

<span id="page-1-0"></span>Image /page/1/Picture/1 description: The image displays a row of five abstract, pixelated images. The first image is a blurry, light-colored abstract shape. The second image is also blurry and appears to be a light-colored, possibly furry or textured object. The third image is a more detailed abstract representation of what might be an animal, possibly a lion, in a natural setting with green and brown tones. The fourth image is a close-up of a dark object with a yellow center and white and green accents, resembling a flower or a piece of fruit. The fifth image is a vibrant, abstract design with red, black, and green colors, featuring circular patterns and organic shapes.

*Guess What These Are?*

Figure 2. Visualizations of previous DD methods. Synthesis-Time Matching sacrifices part of the visual semantic expression in order to imitate the performance of the original dataset.

*1) The synthesis process should not depend on a specific network architecture.* Typically, a fixed architecture is required for data matching, which leads to low crossarchitecture generalization performance because the output space is constrained by the architecture. This problem arises once the matching process occurs in the synthesis time as shown in Fig. [1\(](#page-0-0)a) and (b). Some work leverages a model pool instead of an individual matching model to alleviate this issue but makes the network hard to optimize [\[41,](#page-9-6) [54\]](#page-9-7). When the distillation process is architecture-free, there is no need to distill datasets for different architectures repeatedly. In addition, constraining the consistency of input and output spaces will make the distilled images more realistic. GlaD [\[5\]](#page-8-9) seems to be a solution where the images are synthesized via Generative Adversarial Networks. However, the synthetic images are still matched by the inner loop.

*2) The method is capable of distilling datasets of various sizes and resolutions with limited computational resources.* As illustrated in Fig. [1\(](#page-0-0)a), most DD solutions use bi-level optimization during synthesis time. While the large-scale datasets are unable to perform a number of unrolled iterations on such a nested loop system. Some works attempt to distill the ImageNet-1K but yield low testing accuracy [\[4,](#page-8-4) [8\]](#page-8-5). A more effective method is depicted in Fig. [1\(](#page-0-0)b): the bi-level optimization is decoupled into synthesis time and training time [\[48\]](#page-9-8). However, the Dual-Time Matching (DTM) strategy leads to information loss at each stage, posing challenges for distillation on small datasets instead.

Inspired by these insights, we propose the Dataset Distillation via Disentangled Diffusion Model ( $D^4M$ ), an efficient approach designed for DD across varying sizes and resolutions as depicted in Fig.  $1(c)$  $1(c)$ . In D<sup>4</sup>M, the Synthesis-Time Matching (STM) is superseded by Training-Time Matching (TTM) which facilitates the fast distillation of large-scale datasets with constrained computational resources. Furthermore,  $D<sup>4</sup>M$  alleviates the architectural dependency and improves the cross-architecture generalization performance of the distilled dataset. As the generative model, Diffusion Models ensure the consistency between input and output spaces, and its synthesis process does not rely on any specific matching architecture. To mitigate the information loss due to insufficient data matching, the conditioning mechanism in Latent Diffusion Model (LDM) consistently infuses the semantic information of labels into the synthetic data during the denoising process. The synthesis process of  $D<sup>4</sup>M$  solely depends on the prototypes extracted from the original data, with synthesis speed scaling linearly with the size of datasets. Moreover, the synthetic images exhibit realism at a high resolution of  $512 \times 512$ . Our pivotal contributions are summarized as follows:

- To the best of our knowledge, this is the first work that overcomes the pronounced dependency on specific architectures inherent in traditional DD frameworks. We introduce the TTM strategy, which paves the way for the generation of a curated and versatile distilled dataset.
- We propose  $D^4M$  that integrates the diffusion model into DD task for the first time. By leveraging label texts and the learned prototypes, we construct a multi-modal DD model that simultaneously enhances distillation efficiency and model performance.
- The method realizes the attainment of resolutions up to  $512\times512$  that exhibit high-fidelity and robust adaptability in the realm of DD. This improvement is evidenced across a spectrum of datasets, extending from the ImageNet-1K to CIFAR-10/100.
- We conduct extensive experiments and ablation studies. The results outperform the SOTA in most cases, substantiating the superior performance, computational efficiency, and robustness of our method.

<span id="page-1-1"></span>

## 2. Related Work

<span id="page-1-3"></span>

### 2.1. Dataset Distillation

The existing DD approaches are taxonomized into metalearning matching and data matching frameworks [\[13,](#page-8-10) [25,](#page-8-11) [34,](#page-9-9) [49\]](#page-9-10). The meta-learning matching aims to optimize the meta-test loss on real dataset for the model meta-trained by the distilled dataset. The gradients are back-propagated to supervise the DD directly [\[10,](#page-8-12) [27,](#page-8-13) [31,](#page-9-11) [32,](#page-9-12) [42,](#page-9-13) [54\]](#page-9-7).

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This is a diagram illustrating a machine learning model. On the left, there are two input sections: 'Real Images' and 'Synthetic Images'. Both feed into an encoder (E) and a decoder (D) respectively. The encoder processes real images to extract features, leading to a representation of the 'Number of Classes'. This is followed by a clustering step. The decoder processes synthetic images through a 'Denoising Process'. Both paths converge towards a 'Diffusion Process'. Separately, 'Label Texts' such as 'Tench, Goldfish, ... Tissue.' are processed by a 'Text Encoder' (Tθ). The diagram also shows intermediate representations like 'Prototypes' and 'Prototypes with Noise', and the overall flow involves feature extraction, denoising, diffusion, and text encoding to achieve a final output represented by Tθ.

Figure 3. Pipeline of Dataset Distillation via Disentangled Diffusion Model  $(D^4M)$ . Rather than using the embedded features directly, D<sup>4</sup>M disentangles feature extraction from image generation in diffusion models through prototype learning.

Unlike optimizing the performance on the DD explicitly, data matching encourages the consistency between the same network architecture trained by distilled and real dataset. Matching the gradients generated by the networks is a reliable surrogate task [\[18,](#page-8-14) [24,](#page-8-3) [51,](#page-9-1) [53\]](#page-9-2). Matching Training Trajectory (MTT)  $[4, 11]$  $[4, 11]$  $[4, 11]$  is then proposed to solve the issue that errors are accumulated during validation in gradient matching. TESLA [\[8\]](#page-8-5) reduced the complexity of gradients calculating with constant memory, allowing DD to be achieved in ImageNet for the first time. Besides, distribution matching optimizes the distance between the two distributions, such as MMD [\[52\]](#page-9-4) and CAFE [\[40\]](#page-9-3).

The aforementioned methods only implement various matching strategies at synthesis time.  $SRe<sup>2</sup>L$  [\[48\]](#page-9-8) argues that decoupling the bi-level optimization into Squeeze, Recover, and Relabel leads to a good performance on largescale datasets. Inspired by this, we summarize previous works into STM and DTM.  $D^4M$  implements the TTM with the help of soft labels, which is considered a feature distribution matching approach.

<span id="page-2-2"></span>

### 2.2. Diffusion Models

The Diffusion Model has demonstrated remarkable capabilities within the generative models. Given samples  $x$ observed from a target distribution, the goal of generative models is approximating the true distribution  $P(x)$ , enabling the generation of novel samples from it. Denoising Diffusion Probabilistic Models (DDPM) [\[16\]](#page-8-16) aims to learn a reverse process of a fixed Markov Chain for generating images. However, DDPM is expensive to optimize and evaluate in the original pixel space.

Latent Diffusion Model (LDM) [\[33\]](#page-9-14), a recent state-ofthe-art diffusion model, addresses this by abstracting highfrequency, imperceptible details into a compact latent space, thereby streamlining both training and inference. LDM has been applied in image editing [\[38,](#page-9-15) [43\]](#page-9-16), video processing [\[2,](#page-8-17) [12\]](#page-8-18), audio generation [\[17,](#page-8-19) [36\]](#page-9-17) and 3D model reconstruction [\[6,](#page-8-20) [19,](#page-8-21) [20,](#page-8-22) [29\]](#page-9-18). Notably, the proficiency of LDM in abstracting and generating images within the latent space exactly resonates with the foundational tenets of DD.

## 3. Method

### 3.1. Preliminaries on Diffusion Models

A pivotal step in DD is the generation of the distilled images. Distinct from the data-matching approaches, our method harnesses the prior knowledge embedded in the pretrained generative models, offering a high-quality initialization for TTM. Recently, diffusion models have emerged as SOTA in generative models [\[28,](#page-8-23) [46\]](#page-9-19). As aforementioned, the synthesis process of the diffusion model does not rely on any specific matching architecture, ensuring the consistency between input and output spaces. For a sequence of denoising autoencoders  $\epsilon_{\theta}$ , the training objective of Denoising Diffusion Probability Model (DDPM) [\[16\]](#page-8-16) is defined as

$$
L_{DM} = \mathbb{E}_{x,\epsilon \sim \mathcal{N}(0,1),t} \left[ \left\| \epsilon - \epsilon_{\theta} \left( x_t, t \right) \right\|_2^2 \right],\qquad(1)
$$

with the timestamp t uniformly sampled from  $\{1, \ldots, T\}$ . Although the DDPM does not cater to our goal of synthesizing images within the condensed features, we turn our attention to the LDM [\[33\]](#page-9-14).

LDM effectively compresses the working space from the original pixel space  $x$  to a more compact latent space  $z$ . Such a transition is close to our intent of encapsulating images into condensed features. LDM constructs an optimized low-dimensional latent space by training a perceptual compression model composed of the encoder  $(\mathcal{E})$  and decoder <span id="page-3-2"></span> $(D)$ . This latent space effectively abstracts high-frequency imperceptible details than pixel space [\[33\]](#page-9-14). In this case, the objective function with text encoder  $\tau_{\theta}$  is redefined as

<span id="page-3-0"></span>
$$
L_{LDM} = \mathbb{E}_{\mathcal{E}(x), y, \epsilon \sim \mathcal{N}(0, 1), t} \left[ \left\| \epsilon - \epsilon_{\theta} \left( z_t, t, \tau_{\theta}(y) \right) \right\|_{2}^{2} \right]. \tag{2}
$$

### 3.2. Disentangled Diffusion Model

The existing diffusion methods are capable of generating high-quality images directly from the given images and prompts. However, it is imperative for the DD model to aggregate the given images into a few condensed features before synthesis. The images in the original dataset encapsulate a spectrum of information from low-level texture patterns to high-level semantic information, along with potential redundancies. Since the diffusion models do not have the capability of aggregating this information among images, it is necessary to extract the salient feature representative of each category before employing the generative model. Consequently, it is essential to disentangle the diffusion models.

Employing prototypes in standard classification tasks offers the benefit of addressing the open-world recognition challenge, thereby enhancing the robustness of models [\[26,](#page-8-24) [45,](#page-9-20) [50\]](#page-9-21). Therefore, initializing the input of the diffusion model with prototypes not only reduces data redundancy but also elevates the quality of the distilled dataset. As illustrated in Fig. [3,](#page-2-0) we leverage the pre-trained autoencoder  $\mathcal E$  inherent in the LDM to extract feature representations from original images. Subsequently, we perform a clustering algorithm to calculate the cluster centers as prototypes for each category. Given the considerable size of the original dataset, we adopt the Mini-Batch  $k$ -Means [\[35\]](#page-9-22) to mitigate the memory overhead of large-scale clustering. This approach iteratively optimizes a mini-batch of samples in each step, accelerating the clustering process with a minimal compromise in accuracy.

Specifically, the clustering algorithm consists of two primary steps: assignment  $z$ 

$$
z^c \leftarrow z \tag{3}
$$

s.t. 
$$
\arg\min_{c} ||z - z^c||^2, c = 1, ..., C
$$
 (4)

and update  $z^c$ 

$$
z^c \leftarrow (1 - \eta)z^c + \eta z. \tag{5}
$$

Here z is the latent variable generated by  $\mathcal{E}$ , and  $z^c$  represents the cluster centers (prototypes),  $C$  is the number of cluster centers. The learning rate  $\eta$  is often calculated by  $\frac{1}{|z^c|}$ . Ultimately, we employ the prototypes  $\overline{Z} = \{z_i^c | c =$  $1, \ldots, C, \, l = 1, \ldots, L$  from all categories as input to the diffusion process for image synthesis.

# Algorithm 1 Dataset Distillation via Disentangled Diffusion Model $(D^4M)$

**Input:**  $(T,\mathcal{L})$ : Real images and their label texts.

- **Input:**  $\mathcal{E}$ : Pre-trained encoder.
- Input: D: Pre-trained decoder. **Input:**  $\tau_{\theta}$ : Pre-trained text encoder.
- **Input:**  $U_t$ : Pre-trained time-conditional U-Net. Input: C: Number of prototypes.
- 1:  $Z = \mathcal{E}(\mathcal{T}) \sim P_z$  ⊳ Compressed latent space 2: for each  $L \in \mathcal{L}$  do 3: for mini-batch  $z \in L$  do 4:  $z^c \sim P_z, c = 1, ..., C$  $\mathrel{\vartriangleright}$  Initialize cluster centers 5:  $z^c \leftarrow z$ , s.t. arg min  $||z - z^c||^2$   $\triangleright$  Assignment c 6:  $\eta = \frac{1}{|z^c|}$ <br>7:  $z^c \leftarrow (1 - \eta)z$  $\triangleright$  Update learning rate  $\triangleright$  Update 8: end for 9:  $y = \tau_{\theta}(L)$   $\triangleright$  Label text embedding 10: for each  $z^c$  do  $11$  $c_t^c \sim q(z_t^c|z^c)$  $\triangleright$  Diffusion process  $12:$  $c = U_t(Concat(z_t^c, y))$   $\triangleright$  Denoising process 13: end for 14: end for 15:  $\mathcal{S} = \mathcal{D}(\tilde{Z}^c)$  $\triangleright$  Generate image **Output:**  $S$ : Distilled images.

<span id="page-3-1"></span>Moreover, LDM is capable of modeling the conditional distribution, enabling DD tasks to incorporate the label information into synthetic images. In Eq. [\(2\)](#page-3-0), LDM introduces a domain-specific encoder  $\tau_{\theta}$  to map the textual labels (prompts) into the feature space. This mapping is seamlessly integrated into the U-Net architecture  $(\mathcal{U}_t)$  through a cross-attention layer, facilitating the fusion of multi-modal features. For each prototype  $z^c$  and its corresponding label L, the synthesis process is formulated as

$$
output = \mathcal{D}(\mathcal{U}_t(Concat(z_t^c, \tau_\theta(L))) \tag{6}
$$

where  $z_t^c$  represents the c-th prototype with noise. The distillation process is summarized in Algorithm [1.](#page-3-1)

### 3.3. Training-Time Matching

Since eliminates the necessity of matching with a specific architecture, separating data matching from the synthesis process reduces the computational overhead on large-scale datasets and addresses the cross-architecture issue inherent in the STM strategy. However, based on previous research [\[4,](#page-8-4) [8,](#page-8-5) [48\]](#page-9-8) and preliminary experiments, we find that training large-scale distilled datasets with hard labels is prone to low testing accuracy. To address this, we introduce the TTM strategy, which is considered a distribution matching approach.

<span id="page-4-2"></span><span id="page-4-0"></span>Image /page/4/Picture/0 description: This image displays a grid of images categorized by dataset and object type. The top section shows images from ImageNet-1K and Tiny-ImageNet, with object labels including Goldfinch, Turtle, Carrier, Pinwheel, Wok, Hourglass, Trolleybus, Pizza, Mushroom, and Beacon. The bottom section shows images from CIFAR-10 and CIFAR-100, with object labels including Airplane, Bird, Cat, Deer, Truck, Otter, Peppers, Tiger, Porcupine, and Bicycle. Each object category has two rows of images: the top row shows a clear image, and the bottom row shows a more abstract or pixelated representation.

Figure 4. Visualization results. The top row of each dataset comes from  $D^4M$  and the bottom comes from  $SRe^2L$  [\[48\]](#page-9-8) (ImageNet-1K and Tiny-ImageNet) and MTT [\[4\]](#page-8-4) (CIFAR-10/100). The images generated by  $D<sup>4</sup>M$  have better resolution and are more lifelike.

<span id="page-4-1"></span>Image /page/4/Figure/2 description: The image displays a grid of goldfish. The top row features six clear images of goldfish in various settings, some with aquatic plants or gravel. The bottom row contains six more abstract or distorted images of goldfish, possibly generated by an AI or from a different source, with less distinct features and colors.

Figure 5. Visualization results within one category.  $D^4M$  (top) provides richer semantic information than  $SRe<sup>2</sup>L$ .

TTM refers to training on distilled datasets with soft labels. Label softening is widely adapted in distillation tasks [\[15,](#page-8-25) [30,](#page-9-23) [47\]](#page-9-24). Since  $D^4M$  infuses the label features into the synthetic data, it is natural to use the soft label during TTM. We employ soft label to align the distribution of student prediction  $S_{\theta}(x)$  with teacher network T:

$$
\theta_{\text{student}} = \underset{\theta \in \Theta}{\text{arg min}} \, L_{KL}(T(x), S_{\theta}(x)) \tag{7}
$$

where  $T(x)/S_{\theta}(x)$  is the teacher/student prediction for the distilled image  $x$  and  $L_{KL}$  represents the KL divergence. The output of the teacher network, also known as soft prediction or soft label, encapsulates richer semantic information compared to hard labels. Matching with the soft labels during training will enhance the robustness and generalization capability of the trained model [\[15\]](#page-8-25). For a fair comparison, we use the soft label storage method similar to the FKD [\[37\]](#page-9-25) method, which generates soft labels and conducts matching at each training epoch:

$$
\theta_{\text{student}}^{t+1} = \underset{\theta \in \Theta}{\text{arg min}} \, L_{KL}(T^t(x), S^t_{\theta}(x)). \tag{8}
$$

## 4. Experiments

### 4.1. Setting and Evaluation

We evaluate the performance of  $D<sup>4</sup>M$  across various datasets and networks. All models employed for ImageNet-1K and Tiny-ImageNet are sourced from the PyTorch official model repository, while the ConvNet utilized for CIFAR-10/100 is based on the architecture proposed by Gidaris *et al*. [\[14\]](#page-8-26). Performance validation was carried out using PyTorch on NVIDIA V100 GPUs. Detailed training and validation hyperparameters are available in the supplementary material.

### 4.2. Dataset Distillation Results

In our comparative analysis, we evaluate the  $D<sup>4</sup>M$  against a range of techniques, encompassing both meta-learning and data-matching strategies. For small datasets, our comparison included two meta-learning methods: KIP [\[32\]](#page-9-12) and FRePO [\[54\]](#page-9-7), alongside four data-matching techniques: DSA [\[51\]](#page-9-1), CAFE [\[40\]](#page-9-3), TESLA [\[8\]](#page-8-5), and  $SRe^{2}L$  [\[48\]](#page-9-8). In the context of large-scale datasets, our focus shifted to a detailed comparison between TESLA and  $SRe<sup>2</sup>L$ .

CIFAR-10 and CIFAR-100 For small dataset distillation, the STM strategy outperforms when the number of categories and IPC (Image Per Class) are limited. However, as the category increases, the TTM strategy becomes more effective. This shift is attributed to the fact that the optimal solution derived from STM fails to ensure the convergence of the network training with large category numbers, thereby capping the testing performance. As evidenced in

<span id="page-5-3"></span><span id="page-5-0"></span>

| Dataset   | IPC      | Meta-Learning                    |                                  | Data-Matching                    |                                  |                                  | $SRe^2L^{\dagger}$ | $D^4M$                           | Full Dataset   |
|-----------|----------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|--------------------|----------------------------------|----------------|
|           |          | <b>KIP</b>                       | <b>FRePO</b>                     | <b>DSA</b>                       | <b>CAFE</b>                      | <b>TESLA</b>                     |                    |                                  |                |
| CIFAR-10  | 10<br>50 | $62.7 \pm 0.3$<br>$68.6 \pm 0.2$ | $65.5 \pm 0.6$<br>$71.7 \pm 0.2$ | $52.1 \pm 0.5$<br>$60.6 \pm 0.5$ | $50.9 \pm 0.5$<br>$62.3 \pm 0.4$ | $66.4 \pm 0.8$<br>$72.6 \pm 0.7$ | (60.2)             | $56.2 \pm 0.4$<br>$72.8 \pm 0.5$ | $84.8 \pm 0.1$ |
| CIFAR-100 | 10<br>50 | $28.3 \pm 0.1$<br>$-$            | $42.5 \pm 0.2$<br>$44.3 \pm 0.2$ | $32.3 \pm 0.3$<br>$42.8 \pm 0.4$ | $31.5 \pm 0.2$<br>$42.9 \pm 0.2$ | $41.7 \pm 0.3$<br>$47.9 \pm 0.3$ | -                  | $45.0 \pm 0.1$<br>$48.8 \pm 0.3$ | $56.2 \pm 0.3$ |

Table 1. Top-1 Accuracy↑ on small datasets. We train the ConvNet-W128 [\[14\]](#page-8-26) from scratch 5 times on the distilled dataset and evaluate them on the original test dataset to get the  $\bar{x} \pm std.$   $\ddagger$ : SRe<sup>2</sup>L [\[48\]](#page-9-8) achieves 60.2% Top-1 Accuracy on CIFAR-10 with IPC-1K.

<span id="page-5-1"></span>

| Dataset       | IPC           | Method | R18  | R50  | R101 |
|---------------|---------------|--------|------|------|------|
| ImageNet-1K   | Full Dataset† |        | 69.8 | 80.9 | 81.9 |
|               | 10            | TESLA  | 7.7  | -    | -    |
|               |               | SRe2L  | 21.3 | 28.4 | 30.9 |
|               |               | D4M    | 27.9 | 33.5 | 34.2 |
|               | 50            | SRe2L  | 46.8 | 55.6 | 60.8 |
|               |               | D4M    | 55.2 | 62.4 | 63.4 |
|               | 100           | SRe2L  | 52.8 | 61.0 | 65.8 |
|               |               | D4M    | 59.3 | 65.4 | 66.5 |
|               | 200           | SRe2L  | 57.0 | 64.6 | 65.9 |
|               |               | D4M    | 62.6 | 67.8 | 68.1 |
| Tiny-ImageNet | Full Dataset‡ |        | 61.9 | 62.0 | 62.3 |
|               | 50            | SRe2L  | 44.0 | 47.7 | 49.1 |
|               |               | D4M    | 46.2 | 51.8 | 51.0 |
|               |               | D4M-G  | 46.8 | 51.9 | 53.2 |
|               | 100           | SRe2L  | 50.8 | 53.5 | 54.2 |
|               |               | D4M    | 51.4 | 54.8 | 55.3 |
|               |               | D4M-G  | 53.3 | 54.9 | 54.5 |

Table 2. Top-1 Accuracy $\uparrow$  on large-scale datasets. SRe<sup>2</sup>L [\[48\]](#page-9-8) and our  $D<sup>4</sup>M$  employ ResNet18 as the teacher model to generate the soft label while TESLA [\[8\]](#page-8-5) uses the ConvNetD4. All standard deviations in this table are  $< 1$ .  $\dagger$ : The results of ImageNet-1K come from the official PyTorch [websites.](https://pytorch.org/vision/main/models/resnet.html) ‡: The results of Tiny-ImageNet come from the model trained from scratch with the official PyTorch [code.](https://github.com/pytorch/vision/tree/main/references/classification)

Tab. [1,](#page-5-0) when applied to CIFAR-100,  $D<sup>4</sup>M$  attains a Top-1 accuracy of 45.0% with merely IPC-10. This performance surpasses that of FRepo and TESLA by 2.5% and 3.3%.

ImageNet-1K and Tiny-ImageNet The TTM strategy demonstrates remarkable efficacy in large-scale DD tasks as presented in Tab. [2.](#page-5-1) The effectiveness stems from its ability to improve the quality of the synthetic data rather than imitate the performance of the original data. Consequently, it facilitates the processing of large-scale datasets with reduced computational complexity and memory demands. In terms of accuracy, the proposed  $D<sup>4</sup>M$  sets new benchmarks, achieving 66.5% and 51.0% with IPC-100 on ImageNet-1K and Tiny-ImageNet. Notably, it replicates the full dataset

<span id="page-5-2"></span>

| Ablation | R18                                                               | R50                                                               | R101                                                              |
|----------|-------------------------------------------------------------------|-------------------------------------------------------------------|-------------------------------------------------------------------|
|          | Teacher: R18                                                      |                                                                   |                                                                   |
| w/ STM   | 23.6                                                              | 29.7                                                              | 32.3                                                              |
| w/o STM  | <b><span style="background-color: #cccccc;">27.9(+4.3)</span></b> | <b><span style="background-color: #cccccc;">33.5(+3.8)</span></b> | <b><span style="background-color: #cccccc;">34.2(+1.9)</span></b> |
|          | Teacher: R50                                                      |                                                                   |                                                                   |
| w/ STM   | 15.8                                                              | 20.6                                                              | 22.3                                                              |
| w/o STM  | <b><span style="background-color: #cccccc;">20.7(+4.9)</span></b> | <b><span style="background-color: #cccccc;">24.7(+4.1)</span></b> | <b><span style="background-color: #cccccc;">26.7(+4.4)</span></b> |
|          | Teacher: R101                                                     |                                                                   |                                                                   |
| w/ STM   | 12.5                                                              | 16.0                                                              | 17.6                                                              |

Table 3. Comparison of Top-1 Accuracy↑ on different matching strategy. We use the R18 as the distribution matching architecture. All methods are evaluated with IPC-10.

performance with 81.2% and 81.9%, respectively. Moreover, our approach significantly surpasses the leading datamatching method,  $SRe<sup>2</sup>L$ , across both datasets. This superiority is attributed to the integration of multi-modal fusion embedding in  $D<sup>4</sup>M$ .

Benefit to the architecture-free synthesis process, the datasets distilled by  $D^4M$  exhibit versatility. To substantiate this characteristic, we extract 200 categories from the distilled ImageNet as the distilled Tiny-ImageNet in accordance with the predefined mapping [\[22\]](#page-8-27). The experimental outcomes of  $D<sup>4</sup>M-G$  in Tab. [2](#page-5-1) demonstrate that our method not only manifests a pronounced distillation effect but also retains the applicability inherent to the original dataset.

## 4.3. Matching Strategy Analysis

As mentioned in Sec. [2,](#page-1-1) the DD task often uses the STM strategy to generate images. In order to validate the superiority of TTM strategy, we conduct the comparative experiments listed in Tab. [3.](#page-5-2) We execute the synthesis process through BN distribution matching on images distilled via  $D<sup>4</sup>M$ , resulting in distribution-matched synthetic images.

It is evident that the test performance with STM failed regardless of the chosen teacher network. The images dis-

<span id="page-6-2"></span>Image /page/6/Figure/0 description: This image contains three line graphs side-by-side, each plotting "Top-1 Acc. (%)" on the y-axis against "IPC" on the x-axis. The x-axis ranges from 10 to 200, and the y-axis ranges from 20 to 70. Each graph displays three lines representing different ResNet models: ResNet-18 (blue dashed line with diamonds), ResNet-50 (purple dashed line with circles), and ResNet-101 (yellow solid line with triangles). The first graph is titled "Teacher: ResNet-18", the second "Teacher: ResNet-50", and the third "Teacher: ResNet-101". In all three graphs, the ResNet-101 and ResNet-50 lines show a similar trend of increasing accuracy with increasing IPC, reaching around 65-70% accuracy at IPC 200. The ResNet-18 line shows a lower accuracy compared to the other two, with its accuracy also increasing with IPC but plateauing around 50% at IPC 200.

Figure 6. Top-1 Accuracy↑ of ImageNet-1K on various teacher-student pairs. The result of each pair increases consistently with larger IPC

<span id="page-6-0"></span>

| Ablation | R18                    | R50                | R101               |
|----------|------------------------|--------------------|--------------------|
|          | Dataset: ImageNet-1K   |                    |                    |
| w/o PT   | 15.6                   | 20.7               | 20.6               |
| w/ PT    | <b>27.9(+12.3)</b>     | <b>33.5(+12.8)</b> | <b>34.2(+13.6)</b> |
|          | Dataset: Tiny-ImageNet |                    |                    |
| w/o PT   | 30.5                   | 35.6               | 37.3               |
| w/ PT    | <b>46.2(+15.7)</b>     | <b>51.8(+16.2)</b> | <b>51.0(+13.7)</b> |

Table 4. Comparison of Top-1 Accuracy↑ on different initialization of diffusion process. PT is the abbreviation of Prototype. All methods are evaluated with IPC-10.

tilled via  $D<sup>4</sup>M$  encapsulate not only the salient features of the original prototypes but also the text information of category labels. Therefore, the network solely trained with the original images proves inadequate for effectively managing such fused multi-modal features. Should the fused features be aligned with these networks, it would result in the disruption of the fused information, thereby diminishing the overall accuracy. It is worth noting that  $D<sup>4</sup>M$  potentially offers high-quality initialization for STM, as it synthesizes images with higher testing accuracy compared to those derived from random white noise initialization.

### 4.4. Prototype Analysis

To ascertain the critical role of prototypes in  $D<sup>4</sup>M$ , we conduct an ablation study on the diffusion process with random initialization and prototype initialization. The results listed in Tab. [4](#page-6-0) demonstrate that the incorporation of a learned prototype markedly enhances the effectiveness of  $D^4M$ .

To showcase the merits of the prototype intuitively, we employ ResNet-18 for feature extraction from the distilled dataset, followed by t-SNE for dimensionality reduction. The visualization results (Fig. [7\)](#page-6-1) reveal that the data synthesized via  $D<sup>4</sup>M$  demonstrates enhanced inter-class discrimination and intra-class consistency.

<span id="page-6-1"></span>Image /page/6/Figure/8 description: The image displays three scatter plots side-by-side, each representing data points visualized using t-SNE. The plots are titled 'MTT', 'SRe²L', and 'D⁴M'. Each plot contains three distinct clusters of data points, colored yellow, blue, and pink, and shaped as triangles. A legend at the bottom indicates that yellow triangles represent 'Alligator', blue triangles represent 'Chain', and pink triangles represent 'Seashore'. The axes of the plots are labeled with numerical values, ranging approximately from -40 to 40 on the x-axis and -20 to 40 on the y-axis, with tick marks at intervals of 20.

Figure 7. T-SNE visualizations on Tiny-ImageNet. The feature embedding distribution of  $D<sup>4</sup>M$  displays more compact within classes and discriminative among classes.

### 4.5. Teacher-Student Network Analysis

We studied the performance of different teacher-student models with  $D<sup>4</sup>M$  and the experimental results are shown in Fig. [6.](#page-6-2) Under the same teacher network, the accuracy of ResNet-18, ResNet-50, and ResNet-101 increases gradually. When IPC is small (such as 10 and 50), the student network trained with an enhanced teacher is prone to overfitting, resulting in reduced testing accuracy. As IPC increases, the large network shows stronger learning ability and the Top-1 accuracy improves. We further compare the performance of the distilled ImageNet on different teacherstudent pairs, including CNNs and ViTs (Tab. [5\)](#page-7-0). As a student network, the ViT-based networks assimilate the inductive bias inherent in CNN-based teachers, leveraging its global attention mechanism to attain the best Top-1 accuracy. Conversely, as a teacher network, ViT does not have such an inductive bias characteristic, yielding suboptimal results on their student networks. Nevertheless, ViT-based students consistently achieve superior Top-1 accuracy.

### 4.6. Qualitative Analysis

A pivotal advantage of  $D<sup>4</sup>M$  lies in its utilization of the outputs from the image decoder  $D$  as the distilled dataset, avoiding the need for STM. This implies that the pixel space of the generated image remains unaltered by any matching optimization, thereby preserving the reality of the distilled

<span id="page-7-3"></span><span id="page-7-0"></span>

| Teacher Network | Student Network |              |                 |             |       |
|-----------------|-----------------|--------------|-----------------|-------------|-------|
|                 | ResNet-18       | MobileNet-V2 | EfficientNet-B0 | Swin-T      | ViT-B |
| ResNet-18       | 55.2            | 47.9         | 55.4            | <b>58.1</b> | 45.5  |
| MobileNet-V2    | 47.6            | 42.9         | 49.8            | <b>58.9</b> | 50.4  |
| Swin-T          | 27.5            | 21.9         | 26.4            | <b>38.1</b> | 34.2  |

Table 5. Top-1 Accuracy↑ on ImageNet-1K with various teacher-student architectures. ViT-based students show powerful learning ability with IPC-50.

image. Figures [4](#page-4-0) and [5](#page-4-1) exemplify the superior image quality achieved by  $D^4M$  in comparison to its counterparts. It is evident that the  $D<sup>4</sup>M$  method not only guarantees the high resolution of the distilled image and preserves the integrity of semantic information but also ensures the richness of features within the same category. More visualizations and analysis can be found in supplementary material.

<span id="page-7-1"></span>

| Method                 | Resolution       | Time(s)↓   | GPU(GB)↓   |
|------------------------|------------------|------------|------------|
| Dataset: ImageNet-1K   |                  |            |            |
| $MTT^{\dagger}$        | $128 \times 128$ | 45.0       | 79.9       |
| $TESLA^{\dagger}$      | $64 \times 64$   | 46.0       | 13.9       |
| $SRe^2L$               | $224 \times 224$ | 5.2        | 34.8       |
| $D^4M$                 | $512 \times 512$ | <b>2.7</b> | <b>6.1</b> |
| Dataset: Tiny-ImageNet |                  |            |            |
| $MTT$                  | $64 \times 64$   | 5.4        | 48.9       |
| $SRe^2L$               | $64 \times 64$   | 11.0       | 33.8       |
| $D^4M$                 | $512 \times 512$ | <b>2.7</b> | <b>6.1</b> |

Table 6. Synthesis time↓ and GPU memory↓ cost on largescale datasets.  $\dagger$ : The runtime of MTT [\[4\]](#page-8-4) and TESLA [\[8\]](#page-8-5) on ImageNet-1K are measured for 10 iterations (500 matching steps).

### 4.7. Distillation Cost Analysis

We conduct the analysis of GPU memory consumption across various DD methods, with the corresponding results presented in Tab. [6.](#page-7-1) Notably, the architecture-free nature of  $D<sup>4</sup>M$  during synthesis ensures the fixed time and GPU memory costs. When considering STM and DTM, we observe an increase in both time and GPU memory usage with the enlargement of the matching architecture. For instance, the peak GPU memory utilization for  $SRe<sup>2</sup>L$  in the recovery of a  $64\times64$  image on ConvNet is 4.2 GB, whereas on ResNet-50, it reaches a substantial 33.8 GB. Similarly, when synthesizing a  $64\times64$  image on ConvNet, MTT demands a peak GPU memory of 48.9 GB. Furthermore, the number of iteration steps impacts the generation time for a single image in data matching. With the increased iteration steps, the time cost for  $SRe^{2}L$  to recover a 224 $\times$ 224 image on ResNet-50 gradually rises from 1.31s to 10.48s. Notably,

 $D<sup>4</sup>M$  demonstrates a remarkable reduction in time cost by a factor of 3.[8](#page-7-2)2 when compared to  $SRe<sup>2</sup>L$ . Figure 8 reveals that  $D<sup>4</sup>M$  attains best accuracy at a constant time cost.

<span id="page-7-2"></span>Image /page/7/Figure/8 description: This image contains two plots side-by-side, both plotting Top-1 Accuracy (%) against Time Per Images (s). The left plot is titled "ImageNet-1K" and the right plot is titled "Tiny-ImageNet". Both plots show data for four different models: SRe2L-Re18 (diamond markers, dashed pink line), SRe2L-Re50 (circle markers, dash-dot blue line), D4M-R18 (star markers, solid yellow line), and D4M-R50 (star markers, solid yellow line). A dashed vertical line is present at 2.5 seconds on both plots. On the ImageNet-1K plot, SRe2L-Re18 reaches approximately 40% accuracy at 0.5s, 45% at 1s, 47% at 2s, and 47% at 3s. SRe2L-Re50 reaches approximately 22% accuracy at 0.5s, 29% at 2.5s, 32% at 5s, 35% at 7.5s, and 36% at 10s. D4M-R18 and D4M-R50 both reach approximately 62% accuracy at 2.5s. On the Tiny-ImageNet plot, SRe2L-Re18 reaches approximately 35% accuracy at 0.5s, 38% at 1s, 42% at 2s, 41% at 3s, and 41% at 5s. SRe2L-Re50 reaches approximately 21% accuracy at 0.5s, 29% at 2.5s, 39% at 5s, 39% at 7.5s, and 39% at 10s. D4M-R18 and D4M-R50 both reach approximately 53% accuracy at 2.5s.

Figure 8. Top-1 Accuracy↑ and synthesis time↓ on large-scale datasets.  $D<sup>4</sup>M$  is architecture-free at synthesis time, thereby a constant runtime cost. Re is the abbreviation of Recover.

## 5. Conclusion

We introduce  $D<sup>4</sup>M$ , a novel and efficient dataset distillation framework leveraging the TTM strategy. For the first time, D<sup>4</sup>M addresses the cross-architecture generalization issue by integrating the principles of diffusion models with prototype learning. The distilled dataset not only boasts realistic and high-resolution images with limited resources but also exhibits a versatility comparable to that of the full dataset. D<sup>4</sup>M demonstrates outstanding performance compared to other dataset distillation methods, particularly when applied to large-scale datasets such as ImageNet-1K. Last but not least, rethinking the relationship between generative models and dataset distillation offers fresh perspectives, paving the way for the community to develop more efficient dataset distillation methods in future endeavors.

Limitation and future works. In the situation of extreme distillation (IPC-1/10), we observe a significant performance degradation. Our future work will concentrate on refining the distillation process for this challenging scenario and try to distill more real-world multi-modal datasets.

Acknowledgement. This work is supported by the National Natural Science Foundation of China (No. 12071458).

## References

- <span id="page-8-0"></span>[1] Richard E Blahut. *Fast algorithms for signal processing*. Cambridge University Press, 2010. [1](#page-0-1)
- <span id="page-8-17"></span>[2] Andreas Blattmann, Robin Rombach, Huan Ling, Tim Dockhorn, Seung Wook Kim, Sanja Fidler, and Karsten Kreis. Align your latents: High-resolution video synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 22563–22575, 2023. [3](#page-2-1)
- <span id="page-8-1"></span>[3] Zalán Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming. *Advances in neural information processing systems*, 33: 14879–14890, 2020. [1](#page-0-1)
- <span id="page-8-4"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-1) [4,](#page-3-2) [5,](#page-4-2) [8](#page-7-3)
- <span id="page-8-9"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023. [2](#page-1-2)
- <span id="page-8-20"></span>[6] Xin Chen, Biao Jiang, Wen Liu, Zilong Huang, Bin Fu, Tao Chen, and Gang Yu. Executing your commands via motion diffusion in latent space. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 18000–18010, 2023. [3](#page-2-1)
- <span id="page-8-2"></span>[7] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012. [1](#page-0-1)
- <span id="page-8-5"></span>[8] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-1) [4,](#page-3-2) [5,](#page-4-2) [6,](#page-5-3) [8](#page-7-3)
- <span id="page-8-8"></span>[9] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [2](#page-1-2)
- <span id="page-8-12"></span>[10] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *Advances in Neural Information Processing Systems*, 35:34391–34404, 2022. [2](#page-1-2)
- <span id="page-8-15"></span>[11] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023. [3](#page-2-1)
- <span id="page-8-18"></span>[12] Patrick Esser, Johnathan Chiu, Parmida Atighehchian, Jonathan Granskog, and Anastasis Germanidis. Structure and content-guided video synthesis with diffusion models. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 7346–7356, 2023. [3](#page-2-1)
- <span id="page-8-10"></span>[13] Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distilla-

tion: Approaches, applications and future directions. *arXiv preprint arXiv:2305.01975*, 2023. [2](#page-1-2)

- <span id="page-8-26"></span>[14] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [5,](#page-4-2) [6](#page-5-3)
- <span id="page-8-25"></span>[15] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015. [5](#page-4-2)
- <span id="page-8-16"></span>[16] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *Advances in neural information processing systems*, 33:6840–6851, 2020. [3](#page-2-1)
- <span id="page-8-19"></span>[17] Yujin Jeong, Wonjeong Ryoo, Seunghyun Lee, Dabin Seo, Wonmin Byeon, Sangpil Kim, and Jinkyu Kim. The power of sound (tpos): Audio reactive video generation with stable diffusion. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 7822–7832, 2023. [3](#page-2-1)
- <span id="page-8-14"></span>[18] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. In *2023 IEEE International Conference on Omni-layer Intelligent Systems (COINS)*, pages 1–6. IEEE, 2023. [3](#page-2-1)
- <span id="page-8-21"></span>[19] Seung Wook Kim, Bradley Brown, Kangxue Yin, Karsten Kreis, Katja Schwarz, Daiqing Li, Robin Rombach, Antonio Torralba, and Sanja Fidler. Neuralfield-ldm: Scene generation with hierarchical latent diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8496–8506, 2023. [3](#page-2-1)
- <span id="page-8-22"></span>[20] Juil Koo, Seungwoo Yoo, Minh Hieu Nguyen, and Minhyuk Sung. Salad: Part-level latent diffusion for 3d shape generation and manipulation. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 14441– 14451, 2023. [3](#page-2-1)
- <span id="page-8-6"></span>[21] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical report, University of Toronto, Toronto, Ontario, 2009. [2](#page-1-2)
- <span id="page-8-27"></span>[22] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6](#page-5-3)
- <span id="page-8-7"></span>[23] Yann LeCun, Corinna Cortes, and CJ Burges. Mnist handwritten digit database. *ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist*, 2, 2010. [2](#page-1-2)
- <span id="page-8-3"></span>[24] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-8-11"></span>[25] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023. [2](#page-1-2)
- <span id="page-8-24"></span>[26] Gen Li, Varun Jampani, Laura Sevilla-Lara, Deqing Sun, Jonghyun Kim, and Joongkyu Kim. Adaptive prototype learning and allocation for few-shot segmentation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 8334–8343, 2021. [4](#page-3-2)
- <span id="page-8-13"></span>[27] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877–13891, 2022. [2](#page-1-2)
- <span id="page-8-23"></span>[28] Calvin Luo. Understanding diffusion models: A unified perspective. *arXiv preprint arXiv:2208.11970*, 2022. [3](#page-2-1)

- <span id="page-9-18"></span>[29] Zhaoyang Lyu, Jinyi Wang, Yuwei An, Ya Zhang, Dahua Lin, and Bo Dai. Controllable mesh generation through sparse latent point diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 271–280, 2023. [3](#page-2-1)
- <span id="page-9-23"></span>[30] Rafael Müller, Simon Kornblith, and Geoffrey E Hinton. When does label smoothing help? *Advances in neural information processing systems*, 32, 2019. [5](#page-4-2)
- <span id="page-9-11"></span>[31] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [2](#page-1-2)
- <span id="page-9-12"></span>[32] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [2,](#page-1-2) [5](#page-4-2)
- <span id="page-9-14"></span>[33] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10684–10695, 2022. [3,](#page-2-1) [4](#page-3-2)
- <span id="page-9-9"></span>[34] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023. [2](#page-1-2)
- <span id="page-9-22"></span>[35] David Sculley. Web-scale k-means clustering. In *Proceedings of the 19th international conference on World wide web*, pages 1177–1178, 2010. [4](#page-3-2)
- <span id="page-9-17"></span>[36] Shuai Shen, Wenliang Zhao, Zibin Meng, Wanhua Li, Zheng Zhu, Jie Zhou, and Jiwen Lu. Difftalk: Crafting diffusion models for generalized audio-driven portraits animation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 1982–1991, 2023. [3](#page-2-1)
- <span id="page-9-25"></span>[37] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pages 673–690. Springer, 2022. [5](#page-4-2)
- <span id="page-9-15"></span>[38] Yu Takagi and Shinji Nishimoto. High-resolution image reconstruction with latent diffusion models from human brain activity. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 14453– 14463, 2023. [3](#page-2-1)
- <span id="page-9-0"></span>[39] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [1](#page-0-1)
- <span id="page-9-3"></span>[40] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [1,](#page-0-1) [3,](#page-2-1) [5](#page-4-2)
- <span id="page-9-6"></span>[41] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023. [2](#page-1-2)
- <span id="page-9-13"></span>[42] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [2](#page-1-2)
- <span id="page-9-16"></span>[43] Chen Henry Wu and Fernando De la Torre. A latent space of stochastic diffusion models for zero-shot image editing

and guidance. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 7378–7387, 2023. [3](#page-2-1)

- <span id="page-9-5"></span>[44] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017. [2](#page-1-2)
- <span id="page-9-20"></span>[45] Hong-Ming Yang, Xu-Yao Zhang, Fei Yin, and Cheng-Lin Liu. Robust classification with convolutional prototype learning. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 3474–3482, 2018. [4](#page-3-2)
- <span id="page-9-19"></span>[46] Ling Yang, Zhilong Zhang, Yang Song, Shenda Hong, Runsheng Xu, Yue Zhao, Wentao Zhang, Bin Cui, and Ming-Hsuan Yang. Diffusion models: A comprehensive survey of methods and applications. *ACM Computing Surveys*, 2022. [3](#page-2-1)
- <span id="page-9-24"></span>[47] Junho Yim, Donggyu Joo, Jihoon Bae, and Junmo Kim. A gift from knowledge distillation: Fast optimization, network minimization and transfer learning. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4133–4141, 2017. [5](#page-4-2)
- <span id="page-9-8"></span>[48] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *arXiv preprint arXiv:2306.13092*, 2023. [2,](#page-1-2) [3,](#page-2-1) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-3)
- <span id="page-9-10"></span>[49] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [2](#page-1-2)
- <span id="page-9-21"></span>[50] Baoquan Zhang, Xutao Li, Yunming Ye, Zhichao Huang, and Lisai Zhang. Prototype completion with primitive knowledge for few-shot learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3754–3762, 2021. [4](#page-3-2)
- <span id="page-9-1"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [1,](#page-0-1) [3,](#page-2-1) [5](#page-4-2)
- <span id="page-9-4"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-9-2"></span>[53] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-9-7"></span>[54] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [2,](#page-1-2) [5](#page-4-2)

# D <sup>4</sup>M: Dataset Distillation via Disentangled Diffusion Model

# Supplementary Material

## 1. Experimental Settings

In our experimental framework, we primarily concentrate on the parameters of the synthesis and the Training-Time Matching (TTM) processes. For the synthesis phase, Stable Diffusion (V1-5) serves as the core mechanism in Latent Diffusion Model implementation. Based on the insights of Sec. [2.1,](#page-1-3) we calibrate the *strength* and *guidance scale* parameters at 0.7 and 8, respectively. During the prototype learning, the Mini-Batch k-Means algorithm is employed, with an in-depth ablation study of cluster number variations presented in Sec. [2.2.](#page-2-2) Furthermore, in scenarios where the IPC is less than 100, we adjust the cluster numbers to match the IPC. Within the TTM process, the comprehensive parameter settings of student networks are provided in Tab. [8.](#page-11-0)

## 2. Hyper-parameter Analysis

### 2.1. Sensitivity Analysis

There are two hyper-parameters in the diffusion model with text prompts, *i.e. strength*  $(0 \lt s \lt 1)$  and *guidance scale*  $(g > 1)$ . Conceptually, the *strength* quantifies the extent of noise infusion into the latent features (prototypes). The diffusion model predominantly disregards these features in scenarios where *strength* equals 1. Furthermore, an elevated *guidance scale* fosters the generation of images that more precisely align with the text prompt. Based on the hyperparameter tuning results in Fig. [9a](#page-10-0) and Fig. [9b,](#page-10-0) we suggest setting *strength* = 0.7 and *guidance scale* = 8.

### 2.2. Number of Prototypes

To ensure the feature diversity of the distilled dataset, multiple prototypes are learned for each category in our experiments. We select 10 or 50 prototypes to generate distilled ImageNet-1K datasets (IPC-100/200) respectively, *i.e*. synthesizing multiple images per prototype. These datasets are then trained across three distinct ResNet architectures, with the corresponding outcomes detailed in Tab. [7.](#page-10-1)

<span id="page-10-1"></span>

| IPC | Prototypes | R18  | R50  | R101 | p-value |
|-----|------------|------|------|------|---------|
| 100 | 10         | 59.0 | 64.4 | 65.9 | 0.7391  |
|     | 50         | 59.3 | 65.4 | 66.5 |         |
| 200 | 10         | 62.4 | 67.6 | 68.2 |         |
|     | 50         | 62.6 | 67.8 | 68.1 |         |

Table 7. Ablation study on the cluster numbers. We utilize 10 or 50 prototypes to synthesize images for IPC-100/200 and evaluate them with ResNet.

<span id="page-10-0"></span>Image /page/10/Figure/11 description: The image contains two bar charts with corresponding images below them. The top bar chart, labeled "(a) Sensitivity Analysis of strength (guidance scale = 8)", shows the Top-1 Accuracy for three different models (R-18, R-50, R-101) at guidance scales of 0.3, 0.5, 0.7, and 0.9. Below this chart are four images of sailboats. The bottom bar chart, labeled "(b) Sensitivity Analysis of guidance scale (strength = 0.7)", shows the Top-1 Accuracy for the same three models at guidance scales of 4, 6, 8, and 10. Below this chart are four images of sailboats, with the last image being a yacht.

(b) Sensitivity Analysis of *guidance scale* (*strength* = 0.7)

Figure 9. Sensitivity analysis of *strength* and *guidance scale*. Quantitative results are evaluated on ResNet. Furthermore, qualitative results are presented to illustrate the variations corresponding to parameter adjustments.

Given the marginal disparity observed between the experimental results of the two groups, we conducted an independent sample t-test. The alternative hypothesis is that the true difference in means is not equal to 0. According to the p-value, at a significance threshold of 0.05, the performance variations of each group are not statistically significant, which means that the distilled datasets are not sensitive to the number of prototypes.

In addition, the t-SNE visualization results of  $D^4M$ on ImageNet-1K are displayed in Fig. [10.](#page-11-1) Except for a few outliers, the features extracted from the  $D<sup>4</sup>M$  distilled ImageNet-1K dataset are compact and discriminative for both different and similar categories.

## 3. Quantitative Analysis

In the main text, we delve into the enhancement of inputoutput image space consistency constraints for addressing cross-architecture generalization challenges. This section presents a direct comparative analysis of the image quality yielded by  $D^4M$  against the benchmark, as detailed in Tab. [9.](#page-11-2)

Firstly, we employ the Inception Score (IS) to assess the clarity  $p(y \mid x)$  of the synthetic images and the feature diversity  $p(y)$  of the generative model G. The IS quantifies

<span id="page-11-1"></span>Image /page/11/Figure/0 description: The image displays two scatter plots side-by-side, each titled with "D4M:". The left plot is titled "D4M: Different Categories" and the right plot is titled "D4M: Similar Categories". Both plots use triangular markers of various colors to represent data points. Below each plot, a legend associates specific colors and marker shapes with category names. The left plot's legend includes: green triangles for "hammerhead", yellow triangles for "marmot", pink triangles for "cleaver", purple triangles for "mortarboard", light green triangles for "papillon", and dark purple triangles for "viaduct". The right plot's legend includes: green triangles for "beagle", yellow triangles for "weimaraner", pink triangles for "collie", light green triangles for "whippet", purple triangles for "samoyed", and dark purple triangles for "pug". The plots visually represent the distribution of these categories, with the left plot showing more distinct clusters and the right plot showing some overlap or closer proximity between categories.

Figure 10. T-SNE visualizations on ImageNet-1K. The features are extracted by ResNet-18.

<span id="page-11-0"></span>

| (a) ImageNet-1K and Tiny-ImageNet |                   | (b) CIFAR-10 and CIFAR-100 |                   |
|-----------------------------------|-------------------|----------------------------|-------------------|
| Settings                          | Values            | Settings                   | Values            |
| network                           | ResNet            | network                    | ConvNet           |
| input size                        | 224               | input size                 | 32                |
| batch size                        | 1024              | batch size                 | 100               |
| epoch                             | 300               | epoch                      | 500               |
| augmentation                      | RandomResizedCrop | augmentation               | RandomResizedCrop |
| min scale                         | 0.08              | min scale                  | 0.08              |
| max scale                         | 1                 | max scale                  | 1                 |
| temperature                       | 20                | temperature                | 20                |
| optimizer                         | AdamW             | optimizer                  | AdamW             |
| learning rate                     | 0.001             | learning rate              | 0.001             |
| weight decay                      | 0.01              | weight decay               | 0.01              |
| learning rate schedule            | cosine decay      | learning rate schedule     | cosine decay      |

Table 8. Parameter settings of the student networks.

the KL divergence between the probability distribution and the conditional probability distribution of the features, as extracted by the Inception V3 model:

$$
IS = \exp\left(\mathbb{E}_{x \sim p_G} D_{KL}(p(y \mid x) \| p(y))\right). \tag{9}
$$

Moreover, to demonstrate that the  $D<sup>4</sup>M$  enhances the consistency between synthetic and real images, we compute the Fréchet Inception Distance (FID) and Kernel Inception Distance (KID) metrics for these datasets. Empirical evaluations demonstrate that  $D<sup>4</sup>M$  is capable of generating a variety of high-resolution images while maintaining consistency between the input and output image spaces.

## 4. More Visualizations

We randomly select the visualizations to enhance the understanding of our methods and easier to reference. The

<span id="page-11-2"></span>

| Dataset       | Method                  | IS↑           | FID↓          | KID↓         |
|---------------|-------------------------|---------------|---------------|--------------|
| ImageNet-1K   | SRe2L                   | 28.872        | 59.119        | 0.047        |
|               | <b><math>D4M</math></b> | <b>49.381</b> | <b>9.419</b>  | <b>0.003</b> |
| Tiny-ImageNet | SRe2L                   | 6.243         | 74.814        | 0.055        |
|               | <b><math>D4M</math></b> | <b>25.866</b> | <b>34.702</b> | <b>0.020</b> |

Table 9. Quantitative results of distilled image. Comparing the quality of distilled images using IPC-50 on ImageNet-1K and Tiny-ImageNet,  $D^4M$  consistently outperforms  $SRe^2L$  across IS, FID, and KID metrics. This demonstrates that the distilled images produced by  $D<sup>4</sup>M$  exhibit higher image quality.

distilled CIFAR-10 and CIFAR-100 are shown in Fig. [11](#page-12-0) and Fig. [12.](#page-12-1) (more pages after this paragraph)

<span id="page-12-0"></span>Image /page/12/Picture/0 description: A collage of ten images. From left to right, the images show an airplane in flight, a vintage car, a green bird perched on a post, a close-up of a cat's face with green eyes, a deer with antlers in a field, a Cavalier King Charles Spaniel sitting on grass, a frog on the ground, a brown horse standing in a field, a white cruise ship on the water, and a white truck.

Figure 11. More visualizations selected from the distilled CIFAR-10 (Class 0-9)

<span id="page-12-1"></span>Image /page/12/Picture/2 description: The image is a grid of 64 smaller images, arranged in an 8x8 grid. Each smaller image depicts a different subject, including animals like a beaver, dolphin, otter, seal, shark, fish, bear, leopard, lion, tiger, wolf, fox, raccoon, badger, monkey, elephant, kangaroo, and various insects and reptiles. There are also images of flowers, fruits, vegetables, furniture, buildings, vehicles like motorcycles and trains, and abstract patterns. The overall impression is a diverse collection of visual elements.

Figure 12. More visualizations selected from the distilled CIFAR-100 (Class 0-99)

Image /page/13/Picture/0 description: This is a grid of 60 images, each displaying a different animal. The animals are arranged in 6 rows and 10 columns. The animals featured include various fish, birds, reptiles, amphibians, and insects. Some specific animals visible are a shark, a parrot, a peacock, a snake, a frog, a spider, and an eagle. The images are diverse, showcasing the animals in different environments and poses.

Figure 13. More visualizations selected from the distilled ImageNet-1K (Class 0-99)

Image /page/14/Picture/0 description: This is a grid of 60 images, each featuring a different animal. The animals are arranged in a 6x10 grid. The top row includes a black swan, elephants, a hedgehog, a platypus, a kangaroo, a koala, a jellyfish, and two types of anemones. The second row shows various sea creatures and shells, including a starfish, a sea slug, a snail, and a nautilus shell. The third row displays crustaceans like crabs and lobsters, along with shrimp and hermit crabs. The fourth row features a variety of birds, such as flamingos, herons, egrets, and pelicans. The fifth row continues with more birds, including sandpipers, oystercatchers, and kingfishers. The sixth row shows a sea lion and several breeds of dogs, including a Pekingese, a Chihuahua, a Shih Tzu, and a Yorkshire Terrier. The subsequent rows are predominantly filled with different dog breeds, showcasing their diverse appearances and poses. The bottom rows also include a few other animals like a cat and a rabbit. The overall image is a collage of animal photographs, likely used for classification or visualization purposes.

Figure 14. More visualizations selected from the distilled ImageNet-1K (Class 100-199)

Image /page/15/Picture/0 description: This is a grid of 60 images, each featuring a different dog breed or other animal. The grid is arranged in 10 rows and 6 columns. The top row features a black poodle, a Yorkshire terrier, a small brown dog, a white West Highland Terrier, a white fluffy dog, and a black Labrador. The second row shows a brown dog, a reddish-brown dog, a white dog with brown spots, a brown dog with a long snout, a black and tan dog, and a white dog with brown ears. The third row displays a brown dog, a dark brown dog, a white dog, a black dog, a black dog with a red collar, and a brown dog. The fourth row shows a fluffy dog, a black dog, a dog with a stick in its mouth, two white fluffy dogs, and two dogs. The fifth row features a black dog, a German Shepherd, a Dachshund, a brown and white dog, a black and white dog, and a black and white dog. The sixth row shows a black and white dog, a brown and white dog, a brown dog, a brown dog, a black dog, and a black and white dog. The seventh row displays a brown dog, a white dog, a brown dog, a brown and white dog, a brown and white dog, and a brown dog. The eighth row shows a white dog, a brown dog, a brown dog, a brown and white dog, a brown and white dog, and a brown dog. The ninth row features a fox, a hyena, a fox, a fox, a fox, and a white fox. The bottom row shows a fox, a cat, a tiger, a cat, a cat, and a cat. The image is titled "Figure 15. More visualizations selected from the distilled ImageNet 1K (Class 200-299)".

Figure 15. More visualizations selected from the distilled ImageNet-1K (Class 200-299)

Image /page/16/Picture/0 description: This is a grid of 60 images, each depicting a different animal or insect. The images are arranged in 6 rows and 10 columns. The top row features various insects, including a green beetle, ladybugs, a black beetle, a brown beetle, a blue beetle, a green beetle, a brown beetle, and a bee. The second row shows more insects, such as ants, a praying mantis, a dragonfly, a stick insect, a cockroach, a green grasshopper, a fly, and a dragonfly. The third row displays butterflies, a starfish, a colorful circular pattern, and a green segmented creature. The fourth row contains mammals like rabbits, a hamster, a hedgehog, a squirrel, a marmot, a guinea pig, and flowers. The fifth row shows a zebra, pigs, a wild boar, a hippopotamus, a rhinoceros, a buffalo, a sheep, and mountain goats. The sixth row includes a deer, a gazelle, a camel, llamas, a red panda, a ferret, and a stoat. The seventh row features an otter, a badger, a sloth, and monkeys. The eighth row shows more monkeys, including gorillas and chimpanzees. The ninth row displays more monkeys and a tiger. The tenth row features elephants, pandas, and fish. The bottom row includes a fish, a school of fish, a flower, coral, a fisherman with a fish, more fish, a lionfish, a patterned fish, a woven mat, and a person in a black robe.

Figure 16. More visualizations selected from the distilled ImageNet-1K (Class 300-399)

Image /page/17/Picture/0 description: The image is a collage of 100 smaller images arranged in a 10x10 grid. The collage features a wide variety of subjects, including musical instruments like an accordion and a guitar, modes of transportation such as airplanes, hot air balloons, cars, and a sailboat, clothing items like a bathrobe and a bra, food and drinks including beer and meat, household items like a bathtub and a clock, tools, sports equipment like a baseball and skis, and various buildings and outdoor scenes. Some images also contain text, such as labels on products or signs.

Figure 17. More visualizations selected from the distilled ImageNet-1K (Class 400-499)

Image /page/18/Picture/0 description: This is a collage of 64 images, arranged in an 8x8 grid. The images depict a wide variety of subjects, including architecture, clothing, food, vehicles, musical instruments, household items, and natural scenes. Some of the specific items visible are a house, a person in a black cloak, sandals, a cocktail shaker, a mug, a teapot, a lock, computer keys, a shipping container, a sports car, kitchen utensils, a cowboy hat, a crib, a pressure cooker, a knitted ball, a person walking, armor, a waterfall, a digital clock, a wristwatch, a wooden table, a woven mat, a dishwasher, a bicycle wheel, an oil rig, a cymbal, a woven basket, a fan, a guitar, a train, a cabinet with a TV, a pink envelope, a fire truck, a gas mask, a parking meter, wine glasses, go-karts, a golf ball, a greenhouse, a vintage car, a grocery store, a metal pipe, a hair clip, a bottle of hair serum, a calculator, a napkin, a hard drive, a flute, a harp, and a combine harvester.

Figure 18. More visualizations selected from the distilled ImageNet-1K (Class 500-599)

Figure 19. More visualizations selected from the distilled ImageNet-1K (Class 600-699)

Image /page/20/Picture/0 description: The image is a collage of 64 smaller images arranged in an 8x8 grid. The images depict a wide variety of objects and scenes, including household items like toilet paper rolls, a washing machine, and a refrigerator; tools such as drills and a hammer; vehicles including a bus, a police car, and a pickup truck; food items like bottles of pills and a pig-shaped cookie jar; musical instruments like a saxophone and a guitar pick; sports equipment such as a tennis racket and a rugby ball; and various other items like a sailboat, a lighthouse, a piggy bank, and a sewing machine. Some images also feature people engaged in activities like skiing or working with tools.

Figure 20. More visualizations selected from the distilled ImageNet-1K (Class 700-799)

Image /page/21/Picture/0 description: This is a grid of 60 images, arranged in 6 rows and 10 columns. The images depict a wide variety of subjects, including objects, animals, people, and scenes. Some of the notable images include a slot machine, a person swimming, a snowmobile, a tractor, a soap dispenser, a soccer ball, a keyboard, a spider web, a rocket launch, a wooden spoon, a boat, a teddy bear, a drum set, a stethoscope, a scarf, a stone wall, a man in a suit, a clock tower, a beach, a man in an orange sweater, colorful shorts, a totem pole, a fire hydrant, a truck, a bicycle, a sailboat, a tripod, the Arc de Triomphe, a double-decker bus, a piano, a vacuum cleaner, a vase, a coiled rope, a man in a robe, a bridge, a violin, a wooden cabinet, an airplane, a faucet, washing machines, and a pitcher.

Figure 21. More visualizations selected from the distilled ImageNet-1K (Class 800-899)

Image /page/22/Picture/0 description: This is a grid of 42 images, each depicting a different subject. The images are arranged in 7 rows and 6 columns. The subjects include various foods like pasta, pizza, fruits, vegetables, and bread; natural landscapes such as mountains, oceans, and fields; animals like fish and birds; and man-made objects like traffic lights, buildings, and clothing.

Figure 22. More visualizations selected from the distilled ImageNet-1K (Class 900-999)