# DANCE: Dual-View Distribution Alignment for Dataset Condensation

Hansong Zhang $^{1,2}$  , <PERSON><PERSON><PERSON> $^{1,2}$  , <PERSON><PERSON><PERSON> $^{1,2}$  , <PERSON><PERSON> $^{1,2}$ <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><PERSON> Ge<sup>1,2\*</sup>

<sup>1</sup>Institute of Information Engineering, Chinese Academy of Sciences, Beijing 100092, China <sup>2</sup>School of Cyber Security, University of Chinese Academy of Sciences, Beijing 100049, China <sup>3</sup>School of Computer Science, Fudan University, Shanghai 200433, China

{zhanghansong,lishikun,linfanzhao,wangweiping,geshiming}@iie.ac.cn, <EMAIL>

## Abstract

Dataset condensation addresses the problem of data burden by learning a small synthetic training set that preserves essential knowledge from the larger real training set. To date, the state-of-the-art (SOTA) results are often yielded by optimizationoriented methods, but their inefficiency hinders their application to realistic datasets. On the other hand, the Distribution-Matching (DM) methods show remarkable efficiency but sub-optimal results compared to optimization-oriented methods. In this paper, we reveal the limitations of current DM-based methods from the inner-class and interclass views, *i.e.*, *Persistent Training* and *Distribution Shift*. To address these problems, we propose a new DM-based method named Dual-view distribution AligNment for dataset CondEnsation (DANCE), which exploits a few pre-trained models to improve DM from both inner-class and interclass views. Specifically, from the inner-class view, we construct multiple "middle encoders" to perform pseudo long-term distribution alignment, making the condensed set a good proxy of the real one during the whole training process; while from the inter-class view, we use the expert models to perform distribution calibration, ensuring the synthetic data remains in the real class region during condensing. Experiments demonstrate the proposed method achieves a SOTA performance while maintaining comparable efficiency with the original DM across various scenarios. Source codes are available at [https://github.com/](https://github.com/Hansong-Zhang/DANCE) [Hansong-Zhang/DANCE.](https://github.com/Hansong-Zhang/DANCE)

## 1 Introduction

Recently, the reliance on large-scale datasets, which may include millions or even billions of examples, has become essential for developing state-of-the-art (SOTA) models [\[Zhao](#page-8-0) [and Bilen, 2021a;](#page-8-0) Xia *et al.*[, 2022;](#page-8-1) Li *et al.*[, 2023;](#page-7-0) [Li](#page-7-1) *et al.*[, 2024;](#page-7-1) Zhang *et al.*[, 2024b\]](#page-8-2). However, this reliance brings significant challenges, primarily due to the substantial storage costs and computational expenses required for

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image illustrates a two-stage process involving synthetic data. The top section, labeled 'Training Process', shows multiple clusters of blue dots within dashed circles, arranged in two rows. An arrow indicates 'Pseudo Long-Term Distribution Alignment' moving from left to right, suggesting a progression or refinement of these clusters. The bottom section, labeled 'Distribution Calibration', displays a different arrangement. It features several clusters of blue dots and orange dots, some within dashed circles and some outside. A dashed line separates the blue dots on the left from the orange dots on the right. An arrow labeled 'Distribution Calibration' points from a cluster of blue dots to a cluster of orange dots, indicating a transformation or adjustment. A legend at the bottom clarifies that blue dots represent 'Synthetic Data of Different Classes', orange dots also represent 'Synthetic Data of Different Classes', light blue areas denote 'Inner-Class View', light orange areas denote 'Inter-Class View', and dashed circles represent 'Real Class Region'.

Figure 1: Two views of the proposed DANCE. For inner-class view, it ensures that the synthetic data remains a faithful proxy of the real data throughout the training process. For inter-class view, it also prevents the synthetic data from falling outside the real class region (the domain where all real data points of that class reside), which may change the decision boundary of the learned classifier.

training such models. These challenges pose formidable obstacles, particularly for startups and non-profit organizations, making advanced model training often unattainable [\[Cole](#page-7-2)man *et al.*[, 2020;](#page-7-2) [Sorscher](#page-7-3) *et al.*, 2022; Zheng *et al.*[, 2023;](#page-8-3) Jin *et al.*[, 2022;](#page-7-4) Yang *et al.*[, 2023;](#page-8-4) Geng *et al.*[, 2023;](#page-7-5) Xia *et al.*[, 2024\]](#page-8-5).

As a remedy, *Dataset Condensation* (DC), also known as *Dataset Distillation*, has emerged as a prominent solution to address the challenges of data burden [Wang *et al.*[, 2018;](#page-8-6) [Cui](#page-7-6) *et al.*[, 2022;](#page-7-6) Yu *et al.*[, 2024\]](#page-8-7). It involves learning a small condensed training set to replicate the performance of models trained on larger real datasets. Pioneer methods in this area typically focus on matching either the gradients [\[Zhao and](#page-8-0) [Bilen, 2021a;](#page-8-0) [Zhao and Bilen, 2021b;](#page-8-8) Kim *et al.*[, 2022;](#page-7-7) [Wang](#page-8-9) *et al.*[, 2023\]](#page-8-9) or parameters [\[Cazenavette](#page-7-8) *et al.*, 2022; [Du](#page-7-9) *et al.*[, 2023;](#page-7-9) Guo *et al.*[, 2024;](#page-7-10) Liu *et al.*[, 2022\]](#page-7-11) between real and synthetic data, which can be categorized as *Optimization-*

<sup>∗</sup> Shiming Ge is the corresponding author.

*Oriented* methods. While these methods have shown success, their reliance on the bi-level optimization or nested gradients often results in prohibitively high computational costs [\[Zhang](#page-8-10) *et al.*[, 2023;](#page-8-10) Sajedi *et al.*[, 2023;](#page-7-12) Liu *et al.*[, 2021;](#page-7-13) [Zhang](#page-8-11) *et al.*, [2024a\]](#page-8-11), limiting their practical application in wider scenarios.

To address the scalability challenges in DC, *Distribution Matching* (DM) [\[Zhao and Bilen, 2023\]](#page-8-12) has been proposed. It focuses on aligning the latent representations extracted by randomly-initialized encoders, based on the rationale that the condensed set should represents the real training set in the feature space. Unlike previous *Optimization-Oriented* methods, DM avoids the computationally intensive nested optimization loops, significantly reducing the time required for condensation and thereby enhancing its applicability in diverse scenarios [Loo *et al.*[, 2022;](#page-7-14) Zhou *et al.*[, 2023;](#page-8-13) [Cazenavette](#page-7-15) *et al.*, 2023; Liu *et al.*[, 2023a;](#page-7-16) [Nguyen](#page-7-17) *et al.*, [2021b\]](#page-7-17). However, despite these advantages, DM's performance still falls short of SOTA optimization-oriented methods such as MTT [\[Cazenavette](#page-7-8) *et al.*, 2022], IDC [\[Kim](#page-7-7) *et al.*, [2022\]](#page-7-7), and DREAM [Liu *et al.*[, 2023b\]](#page-7-18).

In this paper, we conduct an in-depth analysis of DM from the inner-class and inter-class views, pointing out the limitations of current DM-based methods and provide our corresponding remedies. Specifically, from the inner-class view, to ensure an alignment during the whole training process, previous works like IDM [Zhao *et al.*[, 2023\]](#page-8-14) and CAFE [\[Wang](#page-8-15) *et al.*[, 2022\]](#page-8-15) naively use the models trained from scratch to extract the latent representations. While effective, the *Persistent Training*, i.e. numerous model updating steps, is very timeconsuming thus greatly hinders their efficiency. To counter this, we introduce Pseudo Long-Term Distribution Alignment (PLTDA), where we use the convex combination of initialized and trained expert models to perform inner-class distribution alignment, eliminating the need for persistent training. From the inter-class view, we reveal the *Distribution Shift* phenomenon in DM, i.e., the synthetic data will diverge from the real class region during condensation, which may change the decision boundary of the learned classifier. To address this, we employ expert models for Distribution Calibration, ensuring the synthetic data remains within the real class region. We term the proposed method as  $\mathbf{D}$ ual-view distribution AlignmeNt for dataset CondEnsation (DANCE), for we enhance DM by utilizing the knowledge of expert models from the above two views, which is illustrated in Fig. [1.](#page-0-0) As will be shown in the experiments, DANCE can achieve comparable results to SOTA optimization-oriented methods even with only a single expert model.

Our main contributions are outlined as follows:

[C1]: We identify and analyze the limitations of current DM-based dataset condensation methods from inner- and inter-class views, which reveals two major issues: persistent training and distribution shift.

[C2]: We introduce DANCE by incorporating two modules to effectively mitigate the above two issues inherent in DMbased methods.

[C3]: We conduct extensive experiments across a variety of datasets under different resolutions. The results demonstrate that DANCE establishes a strong baseline in dataset condensation, significantly advancing both performance and efficiency, particularly in the realm of distribution matching.

## 2 Preliminaries

In this section, we initially formalize the concept of dataset condensation (DC) and then recap the DM method [\[Zhao and](#page-8-12) [Bilen, 2023\]](#page-8-12), which is pivotal as it represents the pioneering work in the realm of distribution matching within DC and lays the groundwork for our research.

**Problem Definition.** Given a large real training set  $\mathcal{D}_{\text{real}} =$  $\{(x_i^{\text{real}}, y_i^{\text{real}})\}_{i=1}^{|\mathcal{D}_{\text{real}}|}$ , *Dataset Condensation* or *Dataset Distillation* aims to generate a small training set  $\mathcal{D}_{syn}$  =  $\{(x_j^{\text{syn}}, y_j^{\text{syn}})\}_{j=1}^{|D_{\text{syn}}|}$  ( $|D_{\text{syn}}| \ll |D_{\text{real}}|$ ), so that the model trained on  $\mathcal{D}_{syn}$  and the model trained on  $\mathcal{D}_{real}$  (denoted as  $\theta_{syn}$  and  $\theta_{real}$  respectively) will have similar performance on the unseen data. Formally, let  $P_D$  represent the distribution of the real data,  $\ell$  be the loss operation such as cross-entropy, the synthetic training set can be obtained by minimizing the performance gap between the two models:

<span id="page-1-0"></span>
$$
\mathcal{D}_{\text{syn}}^{\star} = \underset{\mathbb{E}_{(\mathbf{x},y)\sim P_D}}{\arg \min} \, ||\ell(\boldsymbol{\theta}_{\text{syn}}(\mathbf{x}), y) - \ell(\boldsymbol{\theta}_{\text{real}}(\mathbf{x}), y)||. \quad (1)
$$

Distribution Matching. To solve Eq. [\(1\)](#page-1-0), previous optimization-oriented methods have attempted to 1) update the  $\mathcal{D}_{syn}$  using a meta-learning framework. 2) match the gradient or parameter induced by  $\mathcal{D}_{syn}$  and  $\mathcal{D}_{real}$ . However, both the above methods involves a bi-level optimization, which is computationally inefficient due to the calculation of nested gradients. To improve the condensing efficiency, DM [\[Zhao](#page-8-12) [and Bilen, 2023\]](#page-8-12) first proposed Distribution Matching, which learns the condensed set by aligning the feature distributions of  $\mathcal{D}_{syn}$  and  $\mathcal{D}_{real}$ . Specifically, the condensed set in DM is optimized by:

<span id="page-1-1"></span>
$$
\mathcal{D}_{\mathsf{syn}}^{\star} = \underset{\mathbb{E}_{\phi_0 \sim P_{\phi_0}}}{\arg \min} \left\| \frac{\sum_{i=1}^{|\mathcal{D}_{\mathsf{real}}|} \phi_0(\boldsymbol{x}_i^{\mathsf{real}})}{|\mathcal{D}_{\mathsf{real}}|} - \frac{\sum_{j=1}^{|\mathcal{D}_{\mathsf{syn}}|} \phi_0(\boldsymbol{x}_j^{\mathsf{syn}})}{|\mathcal{D}_{\mathsf{syn}}|} \right\|^2, \tag{2}
$$

where  $\phi_0 \sim P_{\phi_0}$  denotes the randomly-initialized feature extractor (instanced by a random DNN  $\theta_0$  without the linear classification layer). Compared to optimization-oriented methods, DM significantly enhances the computational efficiency and has shown better generalization ability across different architectures [\[Zhao and Bilen, 2023\]](#page-8-12).

## 3 Methodology

While DM [\[Zhao and Bilen, 2023\]](#page-8-12) brings remarkable efficiency and cross-architecture performance, the quality of the condensed set it generates typically falls short of those produced by SOTA optimization-oriented methods like IDC [Kim *et al.*[, 2022\]](#page-7-7) and MTT [\[Cazenavette](#page-7-8) *et al.*, 2022]. In this paper, we aim to enhance the alignment between the distributions of  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{\text{syn}}$ , considering both the **inner**class view and the inter-class view. Sections [3.1](#page-2-0) and [3.2](#page-3-0) will detail the limitations of current DM-based methods from these two perspectives and introduce our proposed solutions. Subsequently, we describe our overall training algorithm in Section [3.3.](#page-3-1) Our method, termed **D**ual-view distribution AligNment for dataset CondEnsation (DANCE), is depicted in Fig. [2.](#page-2-1)

<span id="page-2-1"></span>Image /page/2/Figure/0 description: This diagram illustrates a machine learning model training process with two views: Inner-Class View and Inter-Class View. The Inner-Class View involves a Real Training Set and a Synthetic Training Set, processed through multiple stages represented by phi\_0 and phi\_mid. Feature representations are stored in green and orange cylinders. A PLTDA Loss is calculated and fed back. The Inter-Class View involves an expert model, phi\_expert, which receives input from the training process and calculates L\_calib and CE Loss. Arrows indicate forward and backward data flow. The diagram also includes a legend for 'Forward' (solid black arrow), 'Backward' (dashed red arrow), and 'Feature Representations' (green and orange cylinders).

Figure 2: The framework of DANCE. From the inner-class view, multiple middle encoders are constructed to perform Pseudo Long-Term Distribution Alignment so that the synthetic set can remain a good proxy of its class during training. From the inter-class view, the Distribution Calibration is performed, ensuring the synthetic data stay within the real class region during condensing process.

<span id="page-2-0"></span>

### 3.1 Inner-Class View

Limitation of DM. For data from the same class, DM [\[Zhao and Bilen, 2023\]](#page-8-12) employs various randomlyinitialized deep encoders to extract latent representations of  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{\text{syn}}$ . It then minimizes the discrepancy of feature distributions to ensure they are aligned (Eq. [\(2\)](#page-1-1)). However, we contend that aligning feature distributions from randomly initialized extractors, which are sampled from a probability distribution over parameters  $P_{\phi_0}$ , does not ensure that  $\mathcal{D}_{syn}$  remains a reliable proxy for  $\mathcal{D}_{real}$  throughout all training stages. This divergence may ultimately cause the model trained on  $\mathcal{D}_{syn}$  to deviate from the one trained on  $\mathcal{D}_{real}$ . To illustrate this divergence throughout the entire training process by the real training data, we compute the discrepancy between the distribution of  $\mathcal{D}_{syn}$  and  $\mathcal{D}_{real}$  at various stages of training. As depicted in Fig. [3a,](#page-3-2) DM fails to maintain the informativeness of the condensed set throughout the training procedure. During training, the distribution of the data condensed by DM increasingly deviates from that of the real data.

*Remark*. The misalignment issue is also noted in CAFE [Wang *et al.*[, 2022\]](#page-8-15) and IDM [Zhao *et al.*[, 2023\]](#page-8-14). As a remedy, these approaches involve training multiple models from scratch to extract features of  $\mathcal{D}_{syn}$  and  $\mathcal{D}_{real}$  during the condensation process. While such *Persistent Training* yields improved performance, it is hampered by the following two drawbacks:

Drawbacks. 1) *Hyper-parameter tuning*: The models used for condensation need to be persistently trained during the condensing process in CAFE and IDM to enrich the distilled knowledge. This process, however, involves meticulous tuning of multiple parameters, including the number of training steps, iteration count, and learning rate during the modelupdating phase. Moreover, as model performance often sees a significant rise at the onset of training, the models at early stages are prone to be skipped due to overdoing the update steps, thereby hampering the effectiveness of the condensed images. 2) *Inefficiency*: Both CAFE and IDM necessitate optimizing hundreds of random models each time a dataset is condensed, which is impractical, especially for datasets with larger resolution.

Pseudo Long-Term Distribution Alignment (PLTDA). To tackle the misalignment issue from the inner-class perspective, we introduce a straightforward and effective module, termed Pseudo Long-Term Distribution Alignment (PLTDA). Specifically, rather than relying on models trained on real data, we employ a convex combination of randomlyinitialized encoders and their corresponding trained counterparts. We refer to these trained encoders as "expert encoders" ( $\phi_{\text{expert}}$ ), because their corresponding "expert models" ( $\theta_{\text{expert}}$ ) represent the upper bound of the performance of  $\mathcal{D}_{syn}$  and the end of the training process. We term this combination as "middle encoders" ( $\phi_{mid}$ ) of "middle models" ( $\theta_{mid}$ ), which is calculated by:

<span id="page-2-2"></span>
$$
\begin{array}{rcl}\n\phi_0 & \to & \lambda \cdot \phi_0 + (1 - \lambda) \cdot \phi_{\text{expert}} \\
\downarrow & & \\
\phi_{\text{mid}}\n\end{array} \qquad \rightarrow \qquad \phi_{\text{expert}}\n\tag{3}
$$

where  $\lambda \sim U(0, 1)$  is a randomly generated coefficient for encoder combination. After obtaining the middle encoder, we calculate the loss in PLTDA as:

<span id="page-2-3"></span>
$$
\mathcal{L}_{\text{PLTDA}} = \left\| \frac{\sum_{i=1}^{|\mathcal{D}_{\text{real}}|} \phi_{\text{mid}}(x_i^{\text{real}})}{|\mathcal{D}_{\text{real}}|} - \frac{\sum_{j=1}^{|\mathcal{D}_{\text{syn}}|} \phi_{\text{mid}}(x_j^{\text{syn}})}{|\mathcal{D}_{\text{syn}}|} \right\|^2.
$$
\n(4)

Compared to CAFE and IDM, which update the model during the condensation process, our expert encoders do not introduce additional hyper-parameters. Furthermore, as illustrated in Fig. [3b,](#page-3-2) the performance of the middle models changes smoothly across different values of  $\lambda$ , allowing for the generation of models with various performances. As shown in Fig. [3a,](#page-3-2) this way can make the distribution of the condensed data match the real data at all training stages. Additionally, the middle encoders rely solely on the randomlyinitialized encoder  $\phi_0$  and the expert encoders  $\phi_{\text{expect}}$ . Both

<span id="page-3-2"></span>Image /page/3/Figure/0 description: The image displays three plots labeled (a), (b), and (c). Plot (a) is a line graph showing Discrepancy on the y-axis and Training Epoch on the x-axis, with two lines representing DM (red triangles) and DANCE (green squares). The DM line starts at approximately 4 and increases to about 14 by epoch 100, with shaded regions indicating variability. The DANCE line starts at approximately 2 and stays between 0 and 2 throughout the 100 epochs. Plot (b) is a line graph showing Test Accuracy (%) on the y-axis and Value of lambda on the x-axis, with a single red line starting at about 85% at lambda 0.0 and decreasing to about 40% at lambda 1.0. Plot (c) is a line graph showing Accuracy (%) on the y-axis and Condensation Steps on the x-axis, with three lines: Real (blue dashed line) at 95%, DM (red triangles) starting at 95% and decreasing to about 62% at 5k steps, and DANCE (green squares) starting at 95% and remaining at 100% from 0.5k steps onwards.

Figure 3: (a) The discrepancy between the feature distribution of  $\mathcal{D}_{real}$  and  $\mathcal{D}_{syn}$  of DM and DANCE across the whole training process with the real training data. (b) The test accuracy(%) of the middle model  $\phi_{mid}$  at different value of  $\lambda$ . (c) The accuracy (%) of the expert model on the real test data, and the synthesized data of DM and DANCE. The evaluations are conducted on CIFAR-10, where (a) and (c) adopts 10 images per class.

can be pre-trained offline and reused for different values of images per class (IPCs). Notably, our method does not require a large number of pre-trained expert models like IDC [Kim *et al.*[, 2022\]](#page-7-7) or MTT [\[Cazenavette](#page-7-8) *et al.*, 2022]. As we will demonstrate in Sec. [4.3,](#page-6-0) our approach achieves stateof-the-art results across various scenarios with just a single expert model.

<span id="page-3-0"></span>

### 3.2 Inter-Class View

Limitation of DM. As depicted in [\[Zhao and Bilen, 2023\]](#page-8-12), DM aligns the distribution between  $\mathcal{D}_{syn}$  and  $\mathcal{D}_{real}$  in a classwise manner, while overlooking inter-class constraints. Since many existing DM-based methods [\[Sajedi](#page-7-12) *et al.*, 2023; [Zhang](#page-8-11) *et al.*[, 2024a\]](#page-8-11) follow the class-wise learning manner of DM, these approaches may have the following drawback:

Drawback. *Distribution Shift*: The synthetic data may fall outside the real class region during condensing, which will affect the decision boundary of the learned classifier. As shown in Fig[.3c,](#page-3-2) the expert model trained by real training data has an excellent performance on real test data, while it cannot achieve a high classification accuracy on the synthetic data generated by DM. It can be inferred from this phenomenon that, many of the synthetic examples are not within the real class region and even cross the decision boundary of the expert model.

Distribution Calibration. To address the above issue from the inter-class view, we integrate a module called *Distribution Calibration* into our approach. This module utilizes expert models  $\theta_{\text{expert}}$  to calibrate the inter-class distribution of  $\mathcal{D}_{syn}$  after PLTDA. Specifically, once the inner-class matching is completed, we impose the following calibration loss, computed using the chosen  $\theta_{\text{expert}}$ , to prevent the synthetic data from straying from their respective categories:

<span id="page-3-3"></span>
$$
\mathcal{L}_{\text{calib}} = \frac{1}{|\mathcal{D}_{\text{syn}}|} \sum_{j=1}^{|\mathcal{D}_{\text{syn}}|} \ell(\boldsymbol{\theta}_{\text{expert}}(\boldsymbol{x}_j^{\text{syn}}), y_j^{\text{syn}}). \tag{5}
$$

It is important to note that, although "Discrimination Loss" in CAFE [Wang *et al.*[, 2022\]](#page-8-15) and "Distribution Regularization" in IDM [Zhao *et al.*[, 2023\]](#page-8-14) employ a similar concept, they utilize un-converged models for calculating their losses, instead of expert models. This may lead to sub-optimal outcomes due to the relatively poorer generalization ability of such models compared to expert models.

<span id="page-3-4"></span>Algorithm 1 Dual-View Distribution Alignment for Dataset Condensation

**Input:** Real training set  $\mathcal{D}_{\text{real}}$ 

**Parameter:** Number of expert models N; Number of condensation iterations I; Learning rate of the condensed set  $\eta$ ; Calibration interval  $I_c$ 

- **Output:** The condensed set  $\mathcal{D}_{syn}$
- 1: Initialize  $\mathcal{D}_{syn}$  with randomly selected real data
- 2: Pre-train N expert encoders  $\{\phi^n_{\text{expert}}\}_{n=1}^N$  and save their corresponding initial encoders  $\{\phi_0^n\}_{n=1}^N$
- 3: for  $i = 1, 2, ..., I$  do
- 4: Randomly select an expert encoder  $\phi_{\text{expert}}^n$  and generate the middle encoder  $\phi_{mid}^n$  by Eq. [\(3\)](#page-2-2)
- 5: Calculate the matching  $\cos \mathcal{L}_{\text{PLTDA}}$  by Eq. [\(4\)](#page-2-3)
- 6: Update the  $\mathcal{D}_{syn}$  by  $\mathcal{D}_{syn} = \mathcal{D}_{syn} \eta \nabla_{\mathcal{D}_{syn}} \mathcal{L}_{PLTDA}$ <br>7: if  $i\% I_c = 0$  then
- if  $i\%I_c = 0$  then
- 8: Calculate the calibration loss  $\mathcal{L}_{\text{calib}}$  by Eq. [\(5\)](#page-3-3)
- 9: Update the  $\mathcal{D}_{syn}$  by  $\mathcal{D}_{syn} = \mathcal{D}_{syn} \eta \nabla_{\mathcal{D}_{syn}} \mathcal{L}_{calib}$ <br>10: **end if**
- end if
- 11: end for
- 12: **Return**:  $\mathcal{D}_{syn}$

<span id="page-3-1"></span>

### 3.3 Training Algorithm

The pseudo-code of DANCE is detailed in Algorithm [1.](#page-3-4) Besides incorporating the PLTDA (Sec. [3.1\)](#page-2-0) and Distribution Calibration (Sec. [3.2\)](#page-3-0), our approach also integrates a prevalent data augmentation technique known as "Factoring & Upsampling". In this technique, each image space in  $\mathcal{D}_{syn}$  is divided into  $l \times l$  smaller sections in order to host multiple synthetic images. These mini-images are subsequently up-sampled to their original dimensions during model training. This augmentation strategy was initially introduced by IDC [Kim *et al.*[, 2022\]](#page-7-7) and has since been widely employed in various dataset condensation works [Liu *et al.*[, 2023b;](#page-7-18) Zhao *et al.*[, 2023\]](#page-8-14).

## 4 Experiments

### 4.1 Experimental Setup

Datasets. We assess our method using three low-resolution datasets: Fashion-MNIST [Xiao *et al.*[, 2017\]](#page-8-16) with a resolution of  $28 \times 28$ , and CIFAR-10/100 [\[Krizhevsky, 2009\]](#page-7-19)

<span id="page-4-0"></span>

|               | Fashion-MNIST      |                    |                    | CIFAR-10           |                    |                    | CIFAR-100          |                    |                    | TinyImageNet       |                    |                    |
|---------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|--------------------|
|               | \$28 \times 28\$   |                    |                    | \$32 \times 32\$   |                    |                    | \$32 \times 32\$   |                    |                    | \$64 \times 64\$   |                    |                    |
| Resolution    | \$28 \times 28\$   |                    |                    | \$32 \times 32\$   |                    |                    | \$32 \times 32\$   |                    |                    | \$64 \times 64\$   |                    |                    |
| <b>IPC</b>    | 1                  | 10                 | 50                 | 1                  | 10                 | 50                 | 1                  | 10                 | 50                 | 1                  | 10                 | 50                 |
| Ratio (%)     | 0.017              | 0.17               | 0.83               | 0.02               | 0.2                | 1                  | 0.02               | 0.2                | 1                  | 0.2                | 2                  | 10                 |
| Random        | \$51.4_{\pm 3.8}\$ | \$73.8_{\pm 0.7}\$ | \$82.5_{\pm 0.7}\$ | \$14.4_{\pm 2.0}\$ | \$26.0_{\pm 1.2}\$ | \$43.4_{\pm 1.0}\$ | \$4.2_{\pm 0.3}\$  | \$14.6_{\pm 0.5}\$ | \$30.0_{\pm 0.4}\$ | \$1.4_{\pm 0.1}\$  | \$5.0_{\pm 0.2}\$  | \$15.0_{\pm 0.4}\$ |
| Herding       | \$67.0_{\pm 1.9}\$ | \$71.1_{\pm 0.7}\$ | \$71.9_{\pm 0.8}\$ | \$21.5_{\pm 1.2}\$ | \$31.6_{\pm 0.7}\$ | \$40.4_{\pm 0.6}\$ | \$8.4_{\pm 0.3}\$  | \$17.3_{\pm 0.3}\$ | \$33.7_{\pm 0.5}\$ | \$2.8_{\pm 0.2}\$  | \$6.3_{\pm 0.2}\$  | \$16.7_{\pm 0.3}\$ |
| K-Center      | \$66.9_{\pm 1.8}\$ | \$54.7_{\pm 1.5}\$ | \$68.3_{\pm 0.8}\$ | \$21.5_{\pm 1.3}\$ | \$14.7_{\pm 0.9}\$ | \$27.0_{\pm 1.4}\$ | \$8.3_{\pm 0.3}\$  | \$7.1_{\pm 0.2}\$  | \$30.5_{\pm 0.3}\$ | -                  | -                  | -                  |
| DC            | \$70.5_{\pm 0.6}\$ | \$82.3_{\pm 0.4}\$ | \$83.6_{\pm 0.4}\$ | \$28.3_{\pm 0.5}\$ | \$44.9_{\pm 0.5}\$ | \$53.9_{\pm 0.5}\$ | \$12.8_{\pm 0.3}\$ | \$25.2_{\pm 0.3}\$ | -                  | \$5.3_{\pm 0.1}\$  | \$12.9_{\pm 0.1}\$ | \$12.7_{\pm 0.4}\$ |
| DSA           | \$70.6_{\pm 0.6}\$ | \$84.6_{\pm 0.3}\$ | \$88.7_{\pm 0.2}\$ | \$28.8_{\pm 0.7}\$ | \$52.1_{\pm 0.5}\$ | \$60.6_{\pm 0.5}\$ | \$13.9_{\pm 0.3}\$ | \$32.3_{\pm 0.3}\$ | \$42.8_{\pm 0.4}\$ | \$5.7_{\pm 0.1}\$  | \$16.3_{\pm 0.2}\$ | \$5.1_{\pm 0.2}\$  |
| IDC           | \$81.0_{\pm 0.2}\$ | \$86.0_{\pm 0.3}\$ | \$86.2_{\pm 0.2}\$ | \$50.6_{\pm 0.4}\$ | \$67.5_{\pm 0.5}\$ | \$74.5_{\pm 0.1}\$ | -                  | \$45.1_{\pm 0.4}\$ | -                  | -                  | -                  | -                  |
| DREAM         | \$81.3_{\pm 0.2}\$ | \$86.4_{\pm 0.3}\$ | \$86.8_{\pm 0.3}\$ | \$51.1_{\pm 0.3}\$ | \$69.4_{\pm 0.4}\$ | \$74.8_{\pm 0.1}\$ | \$29.5_{\pm 0.3}\$ | \$46.8_{\pm 0.7}\$ | \$52.6_{\pm 0.4}\$ | \$10.0_{\pm 0.4}\$ | -                  | \$29.5_{\pm 0.3}\$ |
| MTT           | -                  | -                  | -                  | \$31.9_{\pm 1.2}\$ | \$56.4_{\pm 0.7}\$ | \$65.9_{\pm 0.6}\$ | \$24.3_{\pm 0.3}\$ | \$40.1_{\pm 0.4}\$ | \$47.7_{\pm 0.2}\$ | \$6.2_{\pm 0.4}\$  | \$17.3_{\pm 0.2}\$ | \$26.5_{\pm 0.3}\$ |
| CAFE          | \$77.1_{\pm 0.9}\$ | \$83.0_{\pm 0.4}\$ | \$84.8_{\pm 0.4}\$ | \$30.3_{\pm 1.1}\$ | \$46.3_{\pm 0.6}\$ | \$55.5_{\pm 0.6}\$ | \$12.9_{\pm 0.3}\$ | \$27.8_{\pm 0.3}\$ | \$37.9_{\pm 0.3}\$ | -                  | -                  | -                  |
| CAFE+DSA      | \$73.7_{\pm 0.7}\$ | \$83.0_{\pm 0.3}\$ | \$88.2_{\pm 0.3}\$ | \$31.6_{\pm 0.8}\$ | \$50.9_{\pm 0.5}\$ | \$62.3_{\pm 0.4}\$ | \$14.0_{\pm 0.3}\$ | \$31.5_{\pm 0.2}\$ | \$42.9_{\pm 0.2}\$ | -                  | -                  | -                  |
| DM            | \$70.7_{\pm 0.6}\$ | \$83.5_{\pm 0.3}\$ | \$88.1_{\pm 0.6}\$ | \$26.0_{\pm 0.8}\$ | \$48.9_{\pm 0.6}\$ | \$63.0_{\pm 0.4}\$ | \$11.4_{\pm 0.3}\$ | \$29.7_{\pm 0.3}\$ | \$43.6_{\pm 0.4}\$ | \$3.9_{\pm 0.2}\$  | \$12.9_{\pm 0.4}\$ | \$24.1_{\pm 0.3}\$ |
| IDM           | -                  | -                  | -                  | \$45.6_{\pm 0.7}\$ | \$58.6_{\pm 0.1}\$ | \$67.5_{\pm 0.1}\$ | \$20.1_{\pm 0.3}\$ | \$45.1_{\pm 0.1}\$ | \$50.0_{\pm 0.2}\$ | \$10.1_{\pm 0.2}\$ | \$21.9_{\pm 0.2}\$ | \$27.7_{\pm 0.3}\$ |
| DataDAM       | -                  | -                  | -                  | \$32.0_{\pm 1.2}\$ | \$54.2_{\pm 0.8}\$ | \$67.0_{\pm 0.4}\$ | \$14.5_{\pm 0.5}\$ | \$34.8_{\pm 0.5}\$ | \$49.4_{\pm 0.3}\$ | \$8.3_{\pm 0.4}\$  | \$18.7_{\pm 0.3}\$ | \$28.7_{\pm 0.3}\$ |
| DANCE         | \$81.5_{\pm 0.4}\$ | \$86.3_{\pm 0.2}\$ | \$86.9_{\pm 0.1}\$ | \$47.1_{\pm 0.2}\$ | \$70.8_{\pm 0.2}\$ | \$76.1_{\pm 0.1}\$ | \$27.9_{\pm 0.2}\$ | \$49.8_{\pm 0.1}\$ | \$52.8_{\pm 0.1}\$ | \$11.6_{\pm 0.2}\$ | \$26.4_{\pm 0.3}\$ | \$28.9_{\pm 0.4}\$ |
| Whole Dataset |                    | \$93.5_{\pm 0.1}\$ |                    |                    | \$84.8_{\pm 0.1}\$ |                    |                    | \$56.2_{\pm 0.3}\$ |                    |                    | \$37.6_{\pm 0.4}\$ |                    |

Table 1: Comparison with previous coreset selection and dataset condensation methods on low-resolution datasets and medium**resolution datasets.** IPC: image(s) per class. Ratio  $(\%)$ : the ratio of condensed examples to the whole training set. Best results are highlighted and the second best results are in bold. Note that some entries are marked as "-" because of scalability issues or the results are not reported.

<span id="page-4-1"></span>

| IPC<br>Ratio (%) | ImageNette       |                  | ImageWoof        |                  | ImageFruit       |                  | ImageMeow        |                  | ImageSquawk      |                  | ImageYellow      |                  |
|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|
|                  | 1                | 10               | 1                | 10               | 1                | 10               | 1                | 10               | 1                | 10               | 1                | 10               |
| Random           | $23.5_{\pm 4.8}$ | $47.7_{\pm 2.4}$ | $14.2_{\pm 0.9}$ | $27.0_{\pm 1.9}$ | $13.2_{\pm 0.8}$ | $21.4_{\pm 1.2}$ | $13.8_{\pm 0.6}$ | $29.0_{\pm 1.1}$ | $21.8_{\pm 0.5}$ | $40.2_{\pm 0.4}$ | $20.4_{\pm 0.6}$ | $37.4_{\pm 0.5}$ |
| <b>MTT</b>       | $47.7_{\pm 0.9}$ | $63.0_{\pm 1.3}$ | $28.6_{\pm 0.8}$ | $35.8_{\pm 1.8}$ | $26.6_{\pm 0.8}$ | $40.3_{\pm 1.3}$ | $30.7_{\pm 1.6}$ | $40.4_{\pm 2.2}$ | $39.4_{\pm 1.5}$ | $52.3_{\pm 1.0}$ | $45.2_{\pm 0.8}$ | $60.0_{\pm 1.5}$ |
| DM               | $32.8_{\pm 0.5}$ | $58.1_{\pm 0.3}$ | $21.1_{\pm 1.2}$ | $31.4_{\pm 0.5}$ | –                | –                | –                | –                | $31.2_{\pm 0.7}$ | $50.4_{\pm 1.2}$ | –                | –                |
| DataDAM          | $34.7_{\pm 0.9}$ | $59.4_{\pm 0.4}$ | $24.2_{\pm 0.5}$ | $34.4_{\pm 0.4}$ | –                | –                | –                | –                | $36.4_{\pm 0.8}$ | $55.4_{\pm 0.9}$ | –                | –                |
| <b>DANCE</b>     | $57.2_{\pm 0.5}$ | $80.2_{\pm 0.7}$ | $30.6_{\pm 0.3}$ | $57.8_{\pm 1.1}$ | $30.6_{\pm 0.8}$ | $52.8_{\pm 0.7}$ | $39.4_{\pm 0.8}$ | $60.4_{\pm 1.1}$ | $52.0_{\pm 0.5}$ | $77.2_{\pm 0.3}$ | $51.8_{\pm 1.1}$ | $78.8_{\pm 0.7}$ |
| Whole Dataset    | $87.4_{\pm 1.0}$ | $67.0_{\pm 1.3}$ | $63.9_{\pm 2.0}$ | $66.7_{\pm 1.1}$ |                  |                  |                  |                  | $87.5_{\pm 0.3}$ | $84.4_{\pm 0.6}$ |                  |                  |

Table 2: Comparison with previous coreset selection and dataset condensation methods on high-resolution ( $128 \times 128$ ) Imagenet-Subsets. All the datasets are condensed using a 5-layer ConvNet.

with a resolution of  $32 \times 32$ . For medium-resolution data, we utilize the resized TinyImageNET [\[Le and Yang, 2015\]](#page-7-20), which has a resolution of  $64 \times 64$ . Furthermore, in alignment with MTT [\[Cazenavette](#page-7-8) *et al.*, 2022], we employ various subsets of the high-resolution ImageNet-1K [\[Deng](#page-7-21) *et al.*[, 2009\]](#page-7-21) dataset (resolution  $128 \times 128$ ) in our experiments. These subsets include ImageNette, ImageWoof, ImageFruit, ImageWeow, ImageSquawk, and ImageYellow. Additional details about the datasets are provided in the Appendix.

Network Architectures. Following previous studies [\[Cazenavette](#page-7-8) *et al.*, 2022], we implement the condensation process using a ConvNet [Sagun *et al.*[, 2018\]](#page-7-22). The ConvNet we employ consists of three identical convolutional blocks, each featuring a 128-kernel  $3 \times 3$  convolutional layer, instance normalization, ReLU activation, and  $3 \times 3$  average pooling with a stride of 2. For low-resolution datasets, we use a three-layer ConvNet, while a four-layer ConvNet is utilized for TinyImageNet. To accommodate the higher resolutions of the high-resolution ImageNet-1K subsets, we employ a five-layer ConvNet.

Evaluation Metric. We utilize the test accuracy of networks trained on the condensed set  $\mathcal{D}_{syn}$  as our primary evaluation metric. Each network is trained from scratch multiple times: 10 times for low-resolution datasets and TinyImageNet, and 3 times for the ImageNet-1K subsets. We report both the average accuracy and the standard deviation. To assess training efficiency, we consider run time per step and peak GPU memory usage as criteria, where the run time is calculated as an average over 1000 iterations.

Implementation Details. For training, we employ an SGD optimizer with a learning rate of 0.01, momentum of 0.9, and weight decay of 0.0005. The expert models  $\theta_{\text{expert}}$  are trained for 60 epochs on low-resolution datasets and TinyImageNet, and for 80 epochs on ImageNet-1K subsets. We consistently

<span id="page-5-0"></span>

| Method       | IPC | ConvNet-3                    | ResNet-10                    | DenseNet-121                 |
|--------------|-----|------------------------------|------------------------------|------------------------------|
| <b>DSA</b>   | 10  | $52.1±0.5$                   | $32.9±0.3$                   | $34.5±0.1$                   |
|              | 50  | $60.6±0.5$                   | $49.7±0.4$                   | $49.1±0.2$                   |
| IDC          | 10  | $67.5±0.5$                   | $63.5±0.1$                   | $61.6±0.6$                   |
|              | 50  | $74.5±0.1$                   | <b><math>72.4±0.5</math></b> | <b><math>71.8±0.6</math></b> |
| <b>MTT</b>   | 10  | $56.4±0.7$                   | $34.5±0.8$                   | $41.5±0.5$                   |
|              | 50  | $65.9±0.6$                   | $43.2±0.4$                   | $51.9±0.3$                   |
| DM           | 10  | $48.9±0.6$                   | $42.3±0.5$                   | $39.0±0.1$                   |
|              | 50  | $63.0±0.4$                   | $58.6±0.3$                   | $57.4±0.3$                   |
| <b>DANCE</b> | 10  | <b><math>70.8±0.2</math></b> | <b><math>67.0±0.2</math></b> | <b><math>64.5±0.3</math></b> |
|              | 50  | <b><math>76.1±0.1</math></b> | <b><math>68.0±0.1</math></b> | <b><math>64.8±0.3</math></b> |

Table 3: Cross-architecture generalization performance (%) on CIFAR-10. The synthetic data is condensed using ConvNet-3 and evaluated using other architectures. The best results are in bold.

use 5 expert models for all datasets as the default setting. The number of iterations for Distribution Calibration is fixed at 1 across all datasets. During the condensing process, the SGD optimizer is set with a learning rate of 0.1 for ImageNet-1K subsets and 0.01 for other datasets, with the learning rate being scaled by the number of images per class (IPC). Following IDC [Kim *et al.*[, 2022\]](#page-7-7), we train the networks using a sequence of color transformation, cropping, and CutMix [\[Yun](#page-8-17) *et al.*[, 2019\]](#page-8-17). The factor parameter  $l$  is set to 2 for lowresolution datasets and Tiny-ImageNet, and 3 for ImageNet-1K subsets. All synthetic data are initially generated from randomly selected real data to expedite optimization. The experiments are conducted on a GPU group comprising GTX 3090, RTX-2080, and NVIDIA-A100 GPUs.

### 4.2 Comparison to State-of-The-Art Methods

Baselines. We include a comprehensive range of methods as baselines in our study. For coreset selection methods, our choose Random Selection, Herding [\[Welling, 2009\]](#page-8-18), and K-Center [\[Farahani and Hekmatfar, 2009\]](#page-7-23). In the category of Optimization-Oriented methods, we consider DC [\[Zhao and Bilen, 2021b\]](#page-8-8), DSA [\[Zhao and Bilen, 2021a\]](#page-8-0), IDC [Kim *et al.*[, 2022\]](#page-7-7), DREAM [Liu *et al.*[, 2023b\]](#page-7-18), and MTT [\[Cazenavette](#page-7-8) *et al.*, 2022]. Additionally, for Distribution-Matching-based methods, our baselines include CAFE and CAFE+DSA [Wang *et al.*[, 2022\]](#page-8-15), DM [\[Zhao and](#page-8-12) [Bilen, 2023\]](#page-8-12), IDM [Zhao *et al.*[, 2023\]](#page-8-14), and DataDAM [\[Sajedi](#page-7-12) *et al.*[, 2023\]](#page-7-12). Further details about these baseline methods are provided in the Appendix, due to page constraints.

Performance Comparison. Tab. [1](#page-4-0) and Tab. [2](#page-4-1) present the comparison of our method with coreset selection methods and dataset condensation/distillation methods. The proposed method, DANCE, demonstrates remarkable performance across various datasets and resolutions. On low-resolution datasets such as Fashion-MNIST, CIFAR-10, CIFAR-100, and the medium-resolution dataset TinyImageNet, DANCE consistently outperforms or rivals leading methods in different IPC (images per class) settings. For instance, on Fashion-MNIST, it achieves the highest test accuracy of 81.5% with a single IPC. On CIFAR-10 and CIFAR-100, DANCE sets

<span id="page-5-1"></span>

|                       |    | IPC   | DC    | DSA  | DM   | MTT  | IDM  | DANCE |
|-----------------------|----|-------|-------|------|------|------|------|-------|
| Run<br>Time<br>(Sec)  | 1  | 0.16  | 0.22  | 0.08 | 0.36 | 0.50 | 0.11 |       |
|                       | 10 | 3.31  | 4.47  | 0.08 | 0.40 | 0.48 | 0.12 |       |
|                       | 50 | 15.74 | 20.13 | 0.08 | X    | 0.58 | 0.12 |       |
| GPU<br>Memory<br>(MB) | 1  | 3515  | 3513  | 3323 | 2711 | 3223 | 2906 |       |
|                       | 10 | 3621  | 3639  | 3455 | 8049 | 3179 | 3045 |       |
|                       | 50 | 4527  | 4539  | 3605 | X    | 4027 | 3549 |       |

Table 4: Time and GPU memeory cost comparison of SOTA datasets condensation methods. Run Time: the time for a single iteration. GPU memory: the peak memory usage during condensing. Both run time and GPU memory is averaged over 1000 iterations. All experiments are conducted on CIFAR-10 with a single NVIDIA-A100 GPU. " $\chi$ " denotes out-of-memory issue.

new benchmarks with 70.8% and 52.8% accuracy respectively at 50 IPC, even surpassing the SOTA optimizationoriented methods DREAM and IDC. Particularly notable is its performance on TinyImageNet, where it attains an accuracy of  $11.6\%$  at 1 IPC and  $26.4\%$  at 10 IPC, significantly ahead of the next best method, IDM. For the highresolution ImageNet-1K subsets, DANCE still yield SOTA results across various scenarios. Remarkably, across all ImageNet-1K subsets with 10 images per class, our DANCE brings over 10% accuracy increase compared to the second best results, showcasing its efficacy in handling diverse image complexities. These results show the superiority of DANCE in dataset condensation tasks, especially considering the wide margin by which it leads in many categories. Overall, DANCE not only establishes new standards in dataset condensation but also demonstrates its robustness across varying resolutions and dataset complexities.

Cross-Architecture Evaluation. We also evaluated the performance of our condensed set across various architectures, as detailed in Tab. [3.](#page-5-0) The results demonstrate that DANCE excels not only on the architecture employed during the condensation process but also exhibits impressive generalization capabilities on a range of unseen architectures

Training Efficiency Evaluation. In the context of dataset condensation, it is of great importance to consider the resource and time costs, as extensively discussed in previous studies [\[Sajedi](#page-7-12) *et al.*, 2023; Zhang *et al.*[, 2023\]](#page-8-10). Some of the methods entail significantly higher time costs in comparison to the time required for training the entire dataset, rendering them less than optimal in balancing effectiveness and efficiency. Our evaluation encompasses both time and peak GPU memory costs incurred during the condensation process for various baseline methods and DANCE. As presented in Tab. [4,](#page-5-1) DANCE exhibits remarkable efficiency compared to optimization-oriented methods such as DC [\[Zhao](#page-8-8) [and Bilen, 2021b\]](#page-8-8), DSA [\[Zhao and Bilen, 2021a\]](#page-8-0), and MTT [\[Cazenavette](#page-7-8) *et al.*, 2022]. Much like DM [\[Zhao and](#page-8-12) [Bilen, 2023\]](#page-8-12), our method demonstrates scalability across different IPCs. However, IDM, being rooted in the DM-based approach, displays higher time costs when contrasted with both DM [\[Zhao and Bilen, 2023\]](#page-8-12) and DANCE.

<span id="page-6-3"></span>Image /page/6/Picture/0 description: The image displays three rows of images, each labeled with a dataset name on the left. The top row, labeled "CIFAR-10", shows images of an airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. The middle row, labeled "ImageSquawk", shows images of a peacock, flamingo, macaw, pelican, king penguin, bald eagle, toucan, ostrich, black swan, and cockatoo. The bottom row, labeled "ImageFruit", shows images of a pineapple, banana, strawberry, orange, lemon, pomegranate, fig, bell pepper, cucumber, and green apple. Each image is a small, pixelated representation of the object it is meant to depict.

Figure 4: Example condensed images of  $32 \times 32$  CIFAR-10,  $128 \times 128$  ImageSquawk, and  $128 \times 128$  ImageFruit.

<span id="page-6-1"></span>

|  | Fac. PLTDA Dist. Calib. | 10. | $CIFAR-10$<br>50                                                       | 10 | CIFAR-100<br>50 |
|--|-------------------------|-----|------------------------------------------------------------------------|----|-----------------|
|  |                         |     | $56.1_{\pm 0.2}$ 71.4 $_{\pm 0.4}$ 40.3 $_{\pm 0.2}$ 50.6 $_{\pm 0.1}$ |    |                 |
|  |                         |     | 64.8 $\pm$ 0.1 68.2 $\pm$ 0.1 37.2 $\pm$ 0.1 45.6 $\pm$ 0.2            |    |                 |
|  |                         |     | $65.6_{\pm 0.3}$ 69.8 $_{\pm 0.2}$ 43.5 $_{\pm 0.3}$ 47.5 $_{\pm 0.2}$ |    |                 |
|  |                         |     | $70.8_{\pm 0.2}$ 76.1 $_{\pm 0.1}$ 49.8 $_{\pm 0.1}$ 52.8 $_{\pm 0.1}$ |    |                 |

Table 5: Ablation study on three main modules of DANCE. "✔" denotes the module is included, and "-" ortherwise. "Fac." denotes the Factoring technique. "Dist. Calib." denotes the module of Distribution Calibration.

<span id="page-6-0"></span>

### 4.3 Ablation Studies

Effectiveness of Each Module. We evaluate three primal Modules of our method, namely Pseudo Long-Term Distribution Alignment (Sec. [3.1\)](#page-2-0), Distribution Calibration (Sec. [3.2\)](#page-3-0), and Factoring & Up-sampling technique (Sec. [3.3\)](#page-3-1). As shown in Tab. [5,](#page-6-1) both the proposed PLTDA and Distribution Calibration bring significant improvement across various datasets. The most significant improvement is observed when all three modules are included. The results highlight the effectiveness of the three modules, demonstrating their collective importance in enhancing the DANCE framework's performance across different datasets.

Impact on the Number of Expert Models. The expert models  $\theta_{\text{expert}}$  are integral to both the PLTDA and Distribution Calibration modules within DANCE. To ascertain their impact, we investigated how the number of expert models (NEM) affects DANCE's performance. As Tab. [6](#page-6-2) illustrates, there is a noticeable increase in DANCE's performance with the rise in NEM. Notably, even with just a single expert model, DANCE achieves competitive results, scoring 69.2% on CIFAR-10 with 10 images per class. This underscores DANCE's ability to efficiently leverage the pre-trained knowledge embedded in expert models.

<span id="page-6-2"></span>

| NEM  | 1    | 2    | 3    | 4    | 5    | 10   | 15   | 20   |
|------|------|------|------|------|------|------|------|------|
| Acc. | 69.2 | 70.1 | 70.2 | 70.2 | 70.8 | 71.2 | 71.1 | 71.1 |

Table 6: Ablation on the number of expert models (NEM). The evaluation is conducted on CIFAR-10 with 10 images per class.

### 4.4 Visualization Results

In Fig. [4,](#page-6-3) we present the synthetic images condensed by DANCE, showcasing distinct characteristics across different datasets. For the low-resolution dataset CIFAR-10, the condensed images are quite discernible, with each clearly representing its respective class. In contrast, the condensed images from the high-resolution ImageNet-1K subsets appear more abstract and outlined. Unlike the images produced by DM [\[Zhao and Bilen, 2023\]](#page-8-12), which feature class-independent textures, our synthetic images encapsulate richer information pertinent to classification tasks. Additional visualizations are available in the Appendix due to page limitations.

## 5 Conclusion

In this study, we introduce a novel framework called Dual-view distribution Alignment for dataset Condensation (DANCE), which enhances the Distribution Matching (DM) method by focusing on both inner- and inter-class views. DANCE consists of two meticulously designed modules: Pseudo Long-Term Distribution Alignment (PLTDA) for inner-class view and Distribution Calibration for inter-class view. PLTDA ensures that the data condensed by DANCE effectively represents its class throughout the entire training process while eliminating the need for persistent training. In contrast, Distribution Calibration maintains the synthetic data within its respective class region. Extensive experimental results on various datasets show that DANCE consistently surpasses state-of-the-art methods while requiring less computational costs. This makes DANCE highly suitable for practical and complex scenarios.

## Acknowledgments

This work was partially supported by grants from the Pioneer R&D Program of Zhejiang Province (2024C01024).

## References

- <span id="page-7-8"></span>[Cazenavette *et al.*, 2022] George Cazenavette, Tongzhou Wang, Antonio Torralba, et al. Dataset distillation by matching training trajectories. In *CVPR*, pages 4750– 4759, 2022.
- <span id="page-7-15"></span>[Cazenavette *et al.*, 2023] George Cazenavette, Tongzhou Wang, Antonio Torralba, et al. Generalizing dataset distillation via deep generative prior. In *CVPR*, pages 3739– 3748, 2023.
- <span id="page-7-2"></span>[Coleman *et al.*, 2020] Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. In *ICLR*, 2020.
- <span id="page-7-6"></span>[Cui *et al.*, 2022] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. In *NeurIPS*, pages 810–822, 2022.
- <span id="page-7-21"></span>[Deng *et al.*, 2009] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, pages 248–255, 2009.
- <span id="page-7-9"></span>[Du *et al.*, 2023] Jiawei Du, Yidi Jiang, Vincent T. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, pages 3749–3758, 2023.
- <span id="page-7-23"></span>[Farahani and Hekmatfar, 2009] Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media, 2009.
- <span id="page-7-5"></span>[Geng *et al.*, 2023] Zongxion Geng, Jiahui andg Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. In *IJCAI*, 2023.
- <span id="page-7-10"></span>[Guo *et al.*, 2024] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *ICLR*, 2024.
- <span id="page-7-4"></span>[Jin *et al.*, 2022] Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bing Yin. Condensing graphs via one-step gradient matching. In *ACM KDD*, pages 720–730, 2022.
- <span id="page-7-7"></span>[Kim *et al.*, 2022] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, pages 11102–11118, 2022.
- <span id="page-7-19"></span>[Krizhevsky, 2009] Alex Krizhevsky. Learning multiple layers of features from tiny images. Technical report, University of Toronto, 2009.

- <span id="page-7-20"></span>[Le and Yang, 2015] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-7-0"></span>[Li *et al.*, 2023] Shikun Li, Tongliang Liu, Jiyong Tan, Dan Zeng, and Shiming Ge. Trustable co-label learning from multiple noisy annotators. *IEEE TMM*, 25:1045–1057, 2023.
- <span id="page-7-1"></span>[Li *et al.*, 2024] Shikun Li, Xiaobo Xia, Jiankang Deng, Shiming Ge, and Tongliang Liu. Transferring annotatorand instance-dependent transition matrix for learning from crowds. *IEEE TPAMI*, pages 1–15, 2024.
- <span id="page-7-13"></span>[Liu *et al.*, 2021] Risheng Liu, Jiaxin Gao, Jin Zhang, et al. Investigating bi-level optimization for learning and vision from a unified perspective: A survey and beyond. *IEEE TPAMI*, 44(12):10045–10067, 2021.
- <span id="page-7-11"></span>[Liu *et al.*, 2022] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, pages 1100–1113, 2022.
- <span id="page-7-16"></span>[Liu *et al.*, 2023a] Songhua Liu, Jingwen Ye, Runpeng Yu, and Xinchao Wang. Slimmable dataset condensation. In *CVPR*, pages 3759–3768, 2023.
- <span id="page-7-18"></span>[Liu *et al.*, 2023b] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: Efficient dataset distillation by representative matching. In *ICCV*, pages 17268–17278, 2023.
- <span id="page-7-14"></span>[Loo *et al.*, 2022] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, pages 13877– 13891, 2022.
- <span id="page-7-25"></span>[Loo *et al.*, 2023] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, pages 22649–22674, 2023.
- <span id="page-7-24"></span>[Nguyen *et al.*, 2021a] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *ICLR*, 2021.
- <span id="page-7-17"></span>[Nguyen *et al.*, 2021b] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, pages 5186–5198, 2021.
- <span id="page-7-22"></span>[Sagun *et al.*, 2018] Levent Sagun, Utku Evci, V. Ugur Güney, Yann N. Dauphin, and Léon Bottou. Empirical analysis of the hessian of over-parametrized neural networks. In *ICLR Workshop*, 2018.
- <span id="page-7-12"></span>[Sajedi *et al.*, 2023] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, et al. Datadam: Efficient dataset distillation with attention matching. In *ICCV*, pages 17097–17107, 2023.
- <span id="page-7-26"></span>[Sener and Savarese, 2018] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A coreset approach. In *ICLR*, 2018.
- <span id="page-7-3"></span>[Sorscher *et al.*, 2022] Ben Sorscher, Robert Geirhos, Shashank Shekhar, et al. Beyond neural scaling laws: beating power law scaling via data pruning. In *NeurIPS*, pages 19523–19536, 2022.

- <span id="page-8-6"></span>[Wang *et al.*, 2018] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv*, 2018.
- <span id="page-8-15"></span>[Wang *et al.*, 2022] Kai Wang, Bo Zhao, Xiangyu Peng, et al. CAFE: Learning to condense dataset by aligning features. In *CVPR*, pages 12196–12205, 2022.
- <span id="page-8-9"></span>[Wang *et al.*, 2023] Cheng Wang, Jiacheng Sun, Zhenhua Dong, Ruixuan Li, and Rui Zhang. Gradient matching for categorical data distillation in ctr prediction. In *CRS*, pages 161–170, 2023.
- <span id="page-8-18"></span>[Welling, 2009] Max Welling. Herding dynamical weights to learn. In *ICML*, pages 1121–1128, 2009.
- <span id="page-8-1"></span>[Xia *et al.*, 2022] Xiaobo Xia, Jiale Liu, Jun Yu, Xu Shen, Bo Han, and Tongliang Liu. Moderate coreset: A universal method of data selection for real-world data-efficient deep learning. In *ICLR*, 2022.
- <span id="page-8-5"></span>[Xia *et al.*, 2024] Xiaobo Xia, Jiale Liu, Shaokun Zhang, Qingyun Wu, Hongxin Wei, and Tongliang Liu. Refined coreset selection: Towards minimal coreset size under model performance constraints. In *ICML*, 2024.
- <span id="page-8-16"></span>[Xiao *et al.*, 2017] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv*, 2017.
- <span id="page-8-4"></span>[Yang *et al.*, 2023] Shuo Yang, Zeke Xie, Hanyu Peng, Min Xu, Mingming Sun, and Ping Li. Dataset pruning: Reducing training data by examining generalization influence. In *ICLR*, 2023.
- <span id="page-8-7"></span>[Yu *et al.*, 2024] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE TPAMI*, 46(1):150–170, 2024.
- <span id="page-8-17"></span>[Yun *et al.*, 2019] Sangdoo Yun, Dongyoon Han, Sanghyuk Chun, et al. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *ICCV*, pages 6022– 6031, 2019.
- <span id="page-8-10"></span>[Zhang *et al.*, 2023] Lei Zhang, Jie Zhang, Bowen Lei, et al. Accelerating dataset distillation via model augmentation. In *CVPR*, pages 11950–11959, 2023.
- <span id="page-8-11"></span>[Zhang *et al.*, 2024a] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *AAAI*, 2024.
- <span id="page-8-2"></span>[Zhang *et al.*, 2024b] Hansong Zhang, Shikun Li, Dan Zeng, Chenggang Yan, and Shiming Ge. Coupled confusion correction: Learning from crowds with sparse annotations. In *AAAI*, 2024.
- <span id="page-8-0"></span>[Zhao and Bilen, 2021a] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, pages 12674–12685, 2021.
- <span id="page-8-8"></span>[Zhao and Bilen, 2021b] Bo Zhao and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-8-12"></span>[Zhao and Bilen, 2023] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, pages 6503–6512, 2023.

- <span id="page-8-14"></span>[Zhao *et al.*, 2023] Ganlong Zhao, Guanbin Li, Yipeng Oin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *CVPR*, pages 7856–7865, 2023.
- <span id="page-8-3"></span>[Zheng *et al.*, 2023] Haizhong Zheng, Rui Liu, Fan Lai, and Atul Prakash. Coverage-centric coreset selection for high pruning rates. In *ICLR*, 2023.
- <span id="page-8-19"></span>[Zhou *et al.*, 2022] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, pages 9813–9827, 2022.
- <span id="page-8-13"></span>[Zhou *et al.*, 2023] Daquan Zhou, Kai Wang, Jianyang Gu, et al. Dataset quantization. In *ICCV*, pages 17205–17216, 2023.

# Related Works

### Optimization-Oriented Methods

Optimization-oriented methods learn the synthetic dataset via a bi-level optimization or a meta-learning framework [\[Liu](#page-7-13) *et al.*[, 2021;](#page-7-13) [Cazenavette](#page-7-8) *et al.*, 2022; [Zhao and Bilen, 2021b;](#page-8-8) Kim *et al.*[, 2022;](#page-7-7) [Zhao and Bilen, 2021a;](#page-8-0) Zhang *et al.*[, 2023;](#page-8-10) Du *et al.*[, 2023;](#page-7-9) Wang *et al.*[, 2018\]](#page-8-6). The pioneering work [Wang *et al.*[, 2018\]](#page-8-6) poses a strong assumption that a model trained on the synthetic dataset should be identical to that trained on the real dataset. Due to vast parameter space and convergence challenges for matching the converged models, subsequent works adopt a more stringent assumption that the two models trained on synthetic dataset and real dataset should follow a similar optimization path, which can be realized by *performance matching* or *parameter matching*.

Performance Matching. In performance matching, the synthetic dataset is optimized to ensure the model trained on it achieves the lowest loss on the real dataset [Wang *et al.*[, 2018\]](#page-8-6), in which way the performance of models could be matched. Further, the kernel ridge regression (KRR)-based methods are proposed to mitigate the inefficiency of the meta-gradient backpropagation [\[Nguyen](#page-7-24) *et al.*, 2021a; [Nguyen](#page-7-17) *et al.*, 2021b; Zhou *et al.*[, 2022;](#page-8-19) Loo *et al.*[, 2022;](#page-7-14) Loo *et al.*[, 2023\]](#page-7-25). With KRR, the synthetic dataset can be updated by back-propagating meta-gradient through the kernel function [\[Nguyen](#page-7-24) *et al.*, 2021a]. Following the KRR stream, KIP [\[Nguyen](#page-7-17) *et al.*, 2021b] employs infinite-width neural networks as its kernel function, thereby forging a significant link between KRR and the field of deep learning.

Parameter Matching. The concept of parameter matching in dataset condensation was initially introduced by DC [\[Zhao and](#page-8-8) [Bilen, 2021b\]](#page-8-8), and has since been expanded upon in various subsequent studies [\[Zhao and Bilen, 2021a;](#page-8-0) [Cazenavette](#page-7-8) *et al.*, [2022;](#page-7-8) Kim *et al.*[, 2022\]](#page-7-7). The fundamental principle of this approach is to align the parameters induced by real and synthetic datasets. In the initial study [\[Zhao and Bilen, 2021b\]](#page-8-8), the focus was on minimizing the difference between gradients derived from synthetic and real datasets with respect to the model. Subsequently, DSA [\[Zhao and Bilen, 2021a\]](#page-8-0) enhanced this by incorporating differentiable siamese augmentation prior to feeding examples into the model, thereby increasing the synthetic dataset's informativeness. Meanwhile, MTT [\[Cazenavette](#page-7-8) *et al.*, 2022] addressed the potential error accumulation in single-step gradients by introducing a multi-step parameter matching method. This method iteratively updates synthetic data to align the model's training trajectory on synthetic dataset with that on real dataset.

Concurrently, some studies have concentrated on refining the model update process during condensation. DC [\[Zhao and](#page-8-8) [Bilen, 2021b\]](#page-8-8) utilized the synthetic dataset for updating the network, which risked early-stage training over-fitting. IDC [\[Kim](#page-7-7) *et al.*[, 2022\]](#page-7-7) countered this by updating the model using real datasets, reducing over-fitting risks due to the larger size of real datasets. IDC [Kim *et al.*[, 2022\]](#page-7-7) also introduced a factoring technique to enhance the synthetic dataset's richness through factoring and up-sampling. Despite their effectiveness, these parameter matching methods are resource-intensive, requiring numerous differently initialized networks to update the synthetic dataset. To expedite this process, [\[Zhang](#page-8-10) *et al.*, 2023] proposed model augmentation, introducing Gaussian perturbation to early-stage models to reduce the time and storage requirements for dataset condensation.

### Distribution-Matching-based Methods

Distribution-matching-based methods aim to create a synthetic dataset that keeps similar representation distribution of the real dataset [Wang *et al.*[, 2022;](#page-8-15) [Zhao and Bilen, 2023;](#page-8-12) Zhao *et al.*[, 2023;](#page-8-14) Sajedi *et al.*[, 2023\]](#page-7-12). These methods differ from optimization-oriented ones in that they bypass the need for bi-level optimization or meta-gradient use in dataset condensation, significantly cutting down on time and memory costs. DM [\[Zhao and Bilen, 2023\]](#page-8-12), for instance, simplifies the process by aligning the representation embeddings of real and synthetic examples, and omits the model updating step, as this has minimal impact on synthetic example performance. CAFE [Wang *et al.*[, 2022\]](#page-8-15) takes this a step further by aligning embeddings not just in the last layer but also in earlier layers, enhancing the synthetic dataset's discriminative qualities through a discriminant loss term. IDM [Zhao *et al.*[, 2023\]](#page-8-14) introduces a "partitioning and expansion" technique to boost the number of representations drawn from the synthetic dataset, addressing the class misalignment issue found in DM [\[Zhao and Bilen, 2023\]](#page-8-12) by using a trained model with a cross-entropy regularization loss. DataDAM [Sajedi *et al.*[, 2023\]](#page-7-12) adds spatial attention matching to improve the synthetic set's performance.

### Coreset Selection

Coreset selection methods, as opposed to synthesizing data, focus on choosing a subset from the entire training set based on specific criteria, as seen in approaches like Herding [\[Welling, 2009\]](#page-8-18), K-center [\[Farahani and Hekmatfar, 2009;](#page-7-23) [Sener and](#page-7-26) [Savarese, 2018\]](#page-7-26), and others. Herding [\[Welling, 2009\]](#page-8-18), for example, picks samples near the centers of their respective classes, while K-center [\[Farahani and Hekmatfar, 2009\]](#page-7-23) aims to minimize the maximum distance between chosen samples and their nearest class center by selecting several center points within a class. However, the effectiveness of these coresets is not always assured due to the heuristic nature of the selection criteria. Additionally, the potential of coresets is limited by the quality of the original training examples, posing a challenge to their use in reducing data requirements.

## Details of Datasets

### Low-Resolution Datasets

- Fashion-MNIST [Xiao *et al.*[, 2017\]](#page-8-16), a widely recognized dataset, is frequently employed for assessing machine learning models. It encompasses 60,000 images for training and 10,000 for testing, each rendered in gray-scale and measuring  $28 \times 28$  pixels. This dataset features an array of 10 distinct fashion categories, encompassing various items such as T-shirts, dresses, and shoes.
- CIFAR-10/100 [\[Krizhevsky, 2009\]](#page-7-19) are extensively utilized benchmark datasets in the realm of object recognition and classification. CIFAR-10 is composed of 60,000 color images, divided into 50,000 for training and 10,000 for testing, spanning 10 diverse object classes, such as cars, birds, and cats. In contrast, CIFAR-100 encompasses a broader range of 100 object classes, allocating 600 images to each class. Each image in both datasets is  $32 \times 32$  pixels, rendering them ideal for testing and evaluating algorithms in image classification and object recognition tasks.

### Medium-Resolution Datasets

• TinyImageNet [\[Le and Yang, 2015\]](#page-7-20), a streamlined version of the larger ImageNet dataset [Deng *et al.*[, 2009\]](#page-7-21), is a widely recognized benchmark in the field of image recognition and classification. This dataset is composed of 100,000 color images for training, alongside 10,000 images reserved for validation and another 10,000 for testing. Each image in TinyImageNet is  $64 \times 64$  pixels, providing a more compact yet challenging dataset for algorithm evaluation. It covers 200 diverse object classes, ranging from everyday items to various animals and scenes, offering a rich and varied dataset for tasks in image classification and object recognition.

### High-Resolution Datasets

• ImageNet Subsets Following MTT [\[Cazenavette](#page-7-8) *et al.*, 2022], we adopt six subsets of ImageNet [Deng *et al.*[, 2009\]](#page-7-21) as high-resolution (128  $\times$  128) datasets to evaluate our method, including ImageNette (assorted objects) and ImageWoof (dog breeds), ImageFruit (fruits), ImageMeow (cats), ImageSquawk (birds), and ImageYellow (yellow-ish things). Each of these subsets consists of 10 distinct classes.

## Details of Baselines

### Coreset-Selection

- Random: randomly select a subset of the original dataset for training.
- Herding: selecting data points that are close to the class centres [\[Welling, 2009\]](#page-8-18).
- K-center: selecting the subset using K-center algorithm, which iteratively selects centers and including points that are closest to these centers [\[Farahani and Hekmatfar, 2009;](#page-7-23) [Sener and Savarese, 2018\]](#page-7-26).

### Dataset-Condensation

### Optimization-Oriented.

- DC: iteratively updating the network on the synthetic dataset and matching the gradient induced by the real and synthetic images [\[Zhao and Bilen, 2021b\]](#page-8-8).
- DSA: applying a differentiable siamese augmentation to images before feed them to the network [\[Zhao and Bilen, 2021a\]](#page-8-0).
- IDC: using a factoring technique that split one image into several lower-resolution ones. Besides, IDC update the network on the original real set instead of the condensed set [Kim *et al.*[, 2022\]](#page-7-7).
- DREAM: combining IDC with a distribution-aware data sampler, which emploit K-Means method to select a more evenlydistributed real data to guild the update of synthetic dataset [Liu *et al.*[, 2023b\]](#page-7-18).
- MTT: matching the training trajectories induced by real and synthetic datasets [\[Cazenavette](#page-7-8) *et al.*, 2022].

### Distribution-Matching-based.

- CAFE: aligning the feature embedding of the real and synthetic images in a layer-wise manner. Moreover, CAFE utilizes a discriminant loss to enhance the discriminative properties of the synthetic dataset [Wang *et al.*[, 2022\]](#page-8-15).
- CAFE+DSA: additionally combining DSA strategy to images compared to CAFE [Wang *et al.*[, 2022\]](#page-8-15).
- DM: aligning the feature embedding of the real and synthetic datasets [\[Zhao and Bilen, 2023\]](#page-8-12).
- IDM: introducing the partitioning and expansion technique and a distribution regularization to improve the original DM [Zhao *et al.*[, 2023\]](#page-8-14).
- DataDAM: except for the loss of DM [\[Zhao and Bilen, 2023\]](#page-8-12), DataDAM also incorporate the attention matching loss term to utilize the spatial attention information [\[Sajedi](#page-7-12) *et al.*, 2023].

### More Visualization Results

Image /page/11/Picture/0 description: A grid of 100 images, each 28x28 pixels, displays various fashion items. The items are arranged in 10 rows and 10 columns. The first two rows show different types of shirts and tops. The next two rows feature trousers and skirts. Rows five and six display dresses. Rows seven and eight showcase shoes, including sneakers and heels. The final two rows present bags and accessories. Each image is a grayscale representation of a fashion item, with some variations in style and detail within each category.

Figure 5: Condensed images of Fashion-MNIST with 10 images per class.

Image /page/12/Picture/0 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a square and appears to be a sample from a dataset of images. The grid is organized into horizontal bands, with each band containing images of a similar category. The top band features airplanes, followed by cars, birds, cats, deer, dogs, frogs, horses, ships, and trucks, respectively. The overall impression is a collection of diverse images, likely representing different classes in a machine learning dataset.

Figure 6: Condensed images of CIFAR-10 with 10 images per class.

Image /page/13/Picture/0 description: The image displays a large grid of small images, likely representing a dataset of images. The grid is organized into rows and columns, with each cell containing a distinct image. The overall impression is a mosaic of diverse visual content, possibly categorized or sampled from a larger collection. The images themselves appear to be of varying subjects, including nature scenes, animals, objects, and abstract patterns. The arrangement suggests a systematic presentation of visual information.

Figure 7: Condensed images of CIFAR-100 with 1 image per class.

Image /page/14/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image appears to be a generated image, possibly from a machine learning model, as they are somewhat abstract and pixelated. The overall color palette is varied, with many images featuring natural elements like green foliage, brown earth tones, and blue water. Some images show recognizable objects or creatures, such as fish, birds, animals, and vehicles, though they are often distorted or stylized. The grid is labeled as "Figure 8: Condensed images of TinyImageNet with 1 image per class (part 1: class 0 to 99)" below the grid.

Figure 8: Condensed images of TinyImageNet with 1 images per class (part 1: class 0 to 99).

Image /page/15/Picture/0 description: The image is a grid of 100 small images, arranged in 10 rows and 10 columns. Each small image appears to be a generated image, possibly from a machine learning model, as they are somewhat abstract and pixelated. The overall impression is a collage of diverse, low-resolution visuals. The images depict a variety of subjects, including what appear to be landscapes, vehicles, food items, and possibly people or animals, though the details are often unclear due to the low resolution and abstract nature of the generated images. The colors vary widely across the grid, with some images featuring blues and greens, others browns and yellows, and some with more vibrant reds and oranges. The arrangement suggests a systematic display, perhaps showcasing results from a model trained on different categories or classes of images.

Figure 9: Condensed images of TinyImageNet with 1 images per class (part 2: class 100 to 199).

Image /page/16/Picture/0 description: The image is a grid of many small images, arranged in rows and columns. The overall impression is a collage of diverse scenes and objects. The top rows appear to feature animals, particularly dogs, in various settings. Below these, there are sections with images of electronic devices, such as cameras and stereos. Further down, the grid displays scenes with people, buildings, and landscapes. The bottom rows show a collection of colorful objects, including balloons, parachutes, and what appear to be golf balls or similar spherical items. The arrangement suggests a dataset or a visual representation of different categories of images.

Figure 10: Condensed images of ImageNette with 10 images per class.

Image /page/17/Picture/0 description: A grid of many small images of dogs, arranged in rows and columns. The images are diverse, showing dogs of various breeds, colors, and poses, often in outdoor settings. The overall impression is a collage of dog photographs.

Figure 11: Condensed images of ImageWoof with 10 images per class.

Image /page/18/Figure/0 description: The image is a grid of 100 smaller images, each depicting a different fruit or vegetable. The grid is arranged in 10 rows and 10 columns. The fruits and vegetables are diverse and include items such as apples, bananas, oranges, strawberries, blueberries, pomegranates, figs, peppers, cucumbers, and various leafy greens. The images are presented in a condensed format, suggesting a dataset or a visual representation of different food items.

Figure 12: Condensed images of **ImageFruit** with 10 images per class.

Image /page/19/Figure/0 description: The image is a grid of many small images, each depicting a cat. The grid is organized into rows and columns, with each cell containing a distinct cat image. The cats are shown in various poses, colors, and settings, suggesting a diverse collection of feline photographs. The overall impression is a mosaic of cat portraits.

Figure 13: Condensed images of ImageMeow with 10 images per class.

Image /page/20/Figure/0 description: A grid of many small images, each depicting a cat. The images are arranged in rows and columns, forming a larger mosaic. The cats in the images vary in breed, color, pose, and background. Some cats are looking at the camera, while others are looking away or are in mid-action. The overall impression is a diverse collection of cat photographs.

Figure 14: Condensed images of ImageSquawk with 10 images per class.

Image /page/21/Figure/0 description: The image is a grid of many small images, arranged in rows and columns. The overall impression is a collage of diverse subjects. The top rows appear to feature flowers and insects, with vibrant colors like pink, purple, and yellow. Below these, there are sections dedicated to fruits, predominantly yellow and orange hues, showcasing items like lemons, oranges, and possibly bananas. Further down, the grid displays images of yellow school buses, suggesting a theme of transportation. The middle and lower sections of the grid contain images of animals, including what appear to be lions or big cats, and various birds, many of which are small and yellow, perched on branches or in flight. The arrangement is dense, with each small image bordered by a thin black line, creating a mosaic effect.

Figure 15: Condensed images of **ImageYellow** with 10 images per class.