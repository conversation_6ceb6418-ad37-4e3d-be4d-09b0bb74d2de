{"table_of_contents": [{"title": "DC-BENCH: Dataset Condensation Benchmark", "heading_level": null, "page_id": 0, "polygon": [[127.5, 99.0], [483.75, 99.0], [483.75, 115.9189453125], [127.5, 115.9189453125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.5419921875, 220.5], [329.25, 220.5], [329.25, 231.837890625], [282.5419921875, 231.837890625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.75634765625, 480.75], [191.25, 480.75], [191.25, 492.29296875], [106.75634765625, 492.29296875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 646.59375], [198.2724609375, 646.59375], [198.2724609375, 658.1953125], [106.98046875, 658.1953125]]}, {"title": "2.1 Coreset selection methods", "heading_level": null, "page_id": 1, "polygon": [[106.8310546875, 676.5], [241.9013671875, 676.5], [241.9013671875, 687.5859375], [106.8310546875, 687.5859375]]}, {"title": "2.2 Dataset condensation methods", "heading_level": null, "page_id": 2, "polygon": [[107.25, 195.75], [260.25, 195.75], [260.25, 205.734375], [107.25, 205.734375]]}, {"title": "2.3 Existing benchmarks", "heading_level": null, "page_id": 3, "polygon": [[106.5, 72.896484375], [223.224609375, 72.896484375], [223.224609375, 83.8212890625], [106.5, 83.8212890625]]}, {"title": "3 Dataset Condensation Benchmark", "heading_level": null, "page_id": 3, "polygon": [[107.05517578125, 176.2470703125], [303.01171875, 176.2470703125], [303.01171875, 188.6220703125], [107.05517578125, 188.6220703125]]}, {"title": "3.1 Evaluation protocol", "heading_level": null, "page_id": 3, "polygon": [[106.5, 340.505859375], [217.248046875, 340.505859375], [217.248046875, 352.494140625], [106.5, 352.494140625]]}, {"title": "3.1.1 Performance under different data augmentations", "heading_level": null, "page_id": 3, "polygon": [[106.5, 407.6015625], [349.330078125, 407.6015625], [349.330078125, 419.203125], [106.5, 419.203125]]}, {"title": "3.1.2 Compression ratios", "heading_level": null, "page_id": 4, "polygon": [[106.5, 108.087890625], [223.224609375, 108.087890625], [223.224609375, 118.916015625], [106.5, 118.916015625]]}, {"title": "3.1.3 Transferability across architectures", "heading_level": null, "page_id": 4, "polygon": [[106.5, 316.3359375], [290.4609375, 316.3359375], [290.4609375, 326.77734375], [106.5, 326.77734375]]}, {"title": "3.1.4 Neural Architecture Search", "heading_level": null, "page_id": 4, "polygon": [[106.5, 681.0], [256.8427734375, 681.0], [256.8427734375, 692.2265625], [106.5, 692.2265625]]}, {"title": "3.2 Implementation details", "heading_level": null, "page_id": 5, "polygon": [[106.5, 229.5], [229.5, 229.5], [229.5, 239.572265625], [106.5, 239.572265625]]}, {"title": "3.2.1 Method of selection", "heading_level": null, "page_id": 5, "polygon": [[106.5, 249.75], [222.92578125, 249.75], [222.92578125, 259.875], [106.5, 259.875]]}, {"title": "3.2.2 Combining dataset selection with condensation methods", "heading_level": null, "page_id": 5, "polygon": [[106.5, 445.5], [378.017578125, 445.5], [378.017578125, 455.5546875], [106.5, 455.5546875]]}, {"title": "4 Empirical studies", "heading_level": null, "page_id": 5, "polygon": [[107.25, 558.0], [216.75, 558.0], [216.75, 568.86328125], [107.25, 568.86328125]]}, {"title": "4.1 Experimental setup", "heading_level": null, "page_id": 5, "polygon": [[106.8310546875, 582.75], [215.3056640625, 582.75], [215.3056640625, 592.83984375], [106.8310546875, 592.83984375]]}, {"title": "4.2 Data augmentation", "heading_level": null, "page_id": 6, "polygon": [[106.5, 343.986328125], [213.064453125, 343.986328125], [213.064453125, 354.427734375], [106.5, 354.427734375]]}, {"title": "4.3 Different compression ratios", "heading_level": null, "page_id": 7, "polygon": [[107.20458984375, 203.25], [252.0, 203.25], [252.0, 213.46875], [107.20458984375, 213.46875]]}, {"title": "4.4 Transferability", "heading_level": null, "page_id": 7, "polygon": [[107.25, 554.25], [195.75, 554.25], [195.75, 564.99609375], [107.25, 564.99609375]]}, {"title": "4.5 Neural Architecture Search (NAS)", "heading_level": null, "page_id": 8, "polygon": [[106.5, 386.25], [277.5, 386.25], [277.5, 396.7734375], [106.5, 396.7734375]]}, {"title": "4.6 Combining Data-Selection methods with Data-Synthesis methods", "heading_level": null, "page_id": 9, "polygon": [[107.25, 244.5], [408.0, 244.5], [408.0, 254.654296875], [107.25, 254.654296875]]}, {"title": "5 DC-BENCH library", "heading_level": null, "page_id": 9, "polygon": [[106.5, 444.7265625], [228.90234375, 444.7265625], [228.90234375, 455.5546875], [106.5, 455.5546875]]}, {"title": "6 Outlook", "heading_level": null, "page_id": 9, "polygon": [[106.5, 551.07421875], [168.75, 551.07421875], [168.75, 561.90234375], [106.5, 561.90234375]]}, {"title": "Acknowledgments and Disclosure of Funding", "heading_level": null, "page_id": 10, "polygon": [[107.1298828125, 72.75], [340.5, 72.75], [340.5, 83.91796875], [107.1298828125, 83.91796875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 134.25], [165.0, 134.25], [165.0, 144.826171875], [107.25, 144.826171875]]}, {"title": "Checklist", "heading_level": null, "page_id": 13, "polygon": [[107.25, 72.0], [157.5, 72.0], [157.5, 83.724609375], [107.25, 83.724609375]]}, {"title": "A Appendix", "heading_level": null, "page_id": 14, "polygon": [[106.5322265625, 72.17138671875], [179.25, 72.17138671875], [179.25, 84.54638671875], [106.5322265625, 84.54638671875]]}, {"title": "A.1 Hyperparameters", "heading_level": null, "page_id": 14, "polygon": [[107.25, 98.7099609375], [209.478515625, 98.7099609375], [209.478515625, 109.3447265625], [107.25, 109.3447265625]]}, {"title": "A.2 Data augmentation", "heading_level": null, "page_id": 14, "polygon": [[106.5, 267.416015625], [215.15625, 267.416015625], [215.15625, 277.857421875], [106.5, 277.857421875]]}, {"title": "A.3 Transferability", "heading_level": null, "page_id": 14, "polygon": [[107.25, 327.357421875], [197.82421875, 327.357421875], [197.82421875, 337.798828125], [107.25, 337.798828125]]}, {"title": "A.4 Combining selection based methods with synthesis based methods", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 657.75], [413.578125, 657.75], [413.578125, 667.86328125], [107.1298828125, 667.86328125]]}, {"title": "Algorithm 1 K-Center", "heading_level": null, "page_id": 15, "polygon": [[106.5, 73.5], [201.41015625, 73.5], [201.41015625, 83.57958984375], [106.5, 83.57958984375]]}, {"title": "A.5 Different compression ratios", "heading_level": null, "page_id": 15, "polygon": [[107.25, 482.23828125], [254.900390625, 482.23828125], [254.900390625, 493.06640625], [107.25, 493.06640625]]}, {"title": "A.6 Impact of different optimizers", "heading_level": null, "page_id": 15, "polygon": [[106.5, 537.15234375], [261.17578125, 537.15234375], [261.17578125, 547.98046875], [106.5, 547.98046875]]}, {"title": "A.7 Computation resources", "heading_level": null, "page_id": 15, "polygon": [[107.25, 614.49609375], [233.5341796875, 614.49609375], [233.5341796875, 625.32421875], [107.25, 625.32421875]]}, {"title": "A.8 Training time", "heading_level": null, "page_id": 15, "polygon": [[106.5, 681.0], [193.1923828125, 681.0], [193.1923828125, 691.83984375], [106.5, 691.83984375]]}, {"title": "A.9 K-Center", "heading_level": null, "page_id": 16, "polygon": [[107.25, 111.0], [174.0, 111.0], [174.0, 121.3330078125], [107.25, 121.3330078125]]}, {"title": "A.10 Distribution bias of synthetic dataset", "heading_level": null, "page_id": 16, "polygon": [[107.25, 344.25], [295.5, 344.25], [295.5, 354.234375], [107.25, 354.234375]]}, {"title": "A.11 Example real images and synthetic images", "heading_level": null, "page_id": 16, "polygon": [[107.05517578125, 588.75], [318.0, 588.75], [318.0, 599.4140625], [107.05517578125, 599.4140625]]}, {"title": "A.12 Source code and leaderboard", "heading_level": null, "page_id": 16, "polygon": [[106.681640625, 647.25], [262.5, 647.25], [262.5, 657.03515625], [106.681640625, 657.03515625]]}, {"title": "A.13 Assets license", "heading_level": null, "page_id": 17, "polygon": [[106.5, 72.75], [197.**********, 72.75], [197.**********, 83.**********], [106.5, 83.**********]]}, {"title": "Ethics Statements", "heading_level": null, "page_id": 17, "polygon": [[106.5, 181.5], [186.0, 181.5], [186.0, 192.19921875], [106.5, 192.19921875]]}, {"title": "Appendix resumes on the next page.", "heading_level": null, "page_id": 17, "polygon": [[107.80224609375, 261.75], [265.5, 261.75], [265.5, 272.830078125], [107.80224609375, 272.830078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 45], ["Text", 4], ["SectionHeader", 3], ["Footnote", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8892, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 52], ["Text", 5], ["ListItem", 5], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 99], ["Text", 8], ["Equation", 6], ["Reference", 6], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 50], ["Text", 10], ["SectionHeader", 4], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 52], ["Text", 11], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 50], ["Text", 9], ["SectionHeader", 5], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 59], ["Text", 4], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 72], ["Text", 9], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["TableCell", 175], ["Line", 82], ["Text", 11], ["Reference", 3], ["Caption", 2], ["Table", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 14823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 53], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 699, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 54], ["ListItem", 21], ["Reference", 21], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 56], ["ListItem", 25], ["Reference", 25], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 24], ["ListItem", 11], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 37], ["ListItem", 23], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 68], ["SectionHeader", 5], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 758, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 46], ["Text", 7], ["SectionHeader", 5], ["TextInlineMath", 2], ["Reference", 2], ["Code", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 62], ["TableCell", 56], ["Text", 5], ["SectionHeader", 4], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 14], ["SectionHeader", 3], ["Text", 2], ["ListItem", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1499], ["TableCell", 512], ["Line", 87], ["Table", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 13674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 1474], ["TableCell", 323], ["Line", 87], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 14664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 620], ["TableCell", 156], ["Line", 42], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Reference", 3], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 3, "llm_tokens_used": 9440, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 110], ["Span", 15], ["Text", 5], ["Line", 5], ["Figure", 1], ["Table", 1], ["Picture", 1], ["Footnote", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 4445, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DC-BENCH__Dataset_Condensation_Benchmark"}