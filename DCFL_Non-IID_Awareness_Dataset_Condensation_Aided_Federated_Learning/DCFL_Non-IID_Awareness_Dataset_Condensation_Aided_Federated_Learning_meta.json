{"table_of_contents": [{"title": "DCFL: Non-IID Awareness Dataset Condensation\nAided Federated Learning", "heading_level": null, "page_id": 0, "polygon": [[51.042821158690174, 53.8583984375], [544.2065491183879, 52.48441674087266], [544.2065491183879, 104.22216796875], [51.042821158690174, 104.22216796875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[126.1057934508816, 509.0988423864648], [206.42317380352642, 509.0988423864648], [206.42317380352642, 519.2607421875], [126.1057934508816, 519.2607421875]]}, {"title": "II. OVERVIEW OF DCFL", "heading_level": null, "page_id": 1, "polygon": [[375.31486146095716, 494.10329474621545], [483.0859375, 494.10329474621545], [483.0859375, 505.2822265625], [375.31486146095716, 505.2822265625]]}, {"title": "III. DESIGN DETAILS", "heading_level": null, "page_id": 2, "polygon": [[118.59949622166246, 368.89047195013353], [212.42821158690174, 368.89047195013353], [212.42821158690174, 379.064453125], [118.59949622166246, 379.064453125]]}, {"title": "A. CKA-based Client Complementarity", "heading_level": null, "page_id": 2, "polygon": [[39.032745591939545, 426.62333036509347], [201.16876574307304, 426.62333036509347], [201.16876574307304, 437.0341796875], [39.032745591939545, 437.0341796875]]}, {"title": "Algorithm 1 The Server Execution Flow of DCFL.", "heading_level": null, "page_id": 2, "polygon": [[303.2544080604534, 56.23330365093499], [516.433249370277, 56.23330365093499], [516.433249370277, 67.0146484375], [303.2544080604534, 67.0146484375]]}, {"title": "B. Transfer of Condensed Data with Non-IID Awareness", "heading_level": null, "page_id": 3, "polygon": [[303.2544080604534, 462.5244140625], [538.37890625, 462.5244140625], [538.37890625, 472.3916015625], [303.2544080604534, 472.3916015625]]}, {"title": "C. Utilization of Condensate Data and Data Augmentation", "heading_level": null, "page_id": 4, "polygon": [[39.032745591939545, 224.93321460373997], [285.9228515625, 224.93321460373997], [285.9228515625, 234.7568359375], [39.032745591939545, 234.7568359375]]}, {"title": "IV. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[126.009765625, 569.8308103294746], [206.42317380352642, 569.8308103294746], [206.42317380352642, 579.2861328125], [126.009765625, 579.2861328125]]}, {"title": "A. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[39.032745591939545, 584.826357969724], [134.158203125, 584.826357969724], [134.158203125, 594.9091796875], [39.032745591939545, 594.9091796875]]}, {"title": "B. Performance Comparison", "heading_level": null, "page_id": 5, "polygon": [[39.032745591939545, 620.8156723063223], [159.13350125944584, 620.8156723063223], [159.13350125944584, 631.5], [39.032745591939545, 631.5]]}, {"title": "C. Communication Comparison", "heading_level": null, "page_id": 6, "polygon": [[39.032745591939545, 429.62243989314334], [171.69921875, 429.62243989314334], [171.69921875, 440.3232421875], [39.032745591939545, 440.3232421875]]}, {"title": "V. RELATED WORK", "heading_level": null, "page_id": 6, "polygon": [[123.10327455919395, 558.5841495992876], [209.42569269521408, 558.5841495992876], [209.42569269521408, 569.0078125], [123.10327455919395, 569.0078125]]}, {"title": "A. Federated Learning", "heading_level": null, "page_id": 6, "polygon": [[39.032745591939545, 573.5796972395369], [135.11335012594458, 573.5796972395369], [135.11335012594458, 584.2197265625], [39.032745591939545, 584.2197265625]]}, {"title": "B. Dataset Condensation", "heading_level": null, "page_id": 6, "polygon": [[303.2544080604534, 155.20391807658058], [407.712890625, 155.20391807658058], [407.712890625, 165.583740234375], [303.2544080604534, 165.583740234375]]}, {"title": "C. Centered Kernel Alignment", "heading_level": null, "page_id": 6, "polygon": [[302.947265625, 407.12911843276936], [429.360201511335, 407.12911843276936], [429.360201511335, 417.09423828125], [302.947265625, 417.09423828125]]}, {"title": "VI. CONCLUSION", "heading_level": null, "page_id": 6, "polygon": [[389.669921875, 657.5547640249332], [469.14357682619647, 657.5547640249332], [469.14357682619647, 668.501953125], [389.669921875, 668.501953125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 7, "polygon": [[137.3652392947103, 196.44167408726625], [195.16372795969772, 196.44167408726625], [195.16372795969772, 206.1887800534283], [137.3652392947103, 206.1887800534283]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 108], ["Text", 14], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4170, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 134], ["Text", 9], ["ListItem", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 873, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1128], ["Line", 127], ["Code", 31], ["Text", 12], ["SectionHeader", 3], ["Equation", 2], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableCell", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 886, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 118], ["TableCell", 96], ["Text", 6], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2480, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 961], ["TableCell", 264], ["Line", 186], ["Text", 5], ["SectionHeader", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6301, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 40], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["TableCell", 260], ["Line", 121], ["Text", 8], ["SectionHeader", 6], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2546, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["Line", 131], ["ListItem", 38], ["ListGroup", 2], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DCFL_Non-IID_Awareness_Dataset_Condensation_Aided_Federated_Learning"}