{"table_of_contents": [{"title": "DD-RobustBench: An Adversarial Robustness\nBenchmark for Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[78.0, 58.5], [533.70703125, 58.5], [533.70703125, 107.701171875], [78.0, 107.701171875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[132.978515625, 561.0], [213.75, 561.0], [213.75, 571.5703125], [132.978515625, 571.5703125]]}, {"title": "II. RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[129.75, 469.5], [218.25, 469.5], [218.25, 478.5], [129.75, 478.5]]}, {"title": "A. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[47.25, 483.75], [143.138671875, 483.75], [143.138671875, 493.453125], [47.25, 493.453125]]}, {"title": "B. Adversarial Robustness", "heading_level": null, "page_id": 1, "polygon": [[311.25, 279.75], [420.15234375, 279.75], [420.15234375, 289.845703125], [311.25, 289.845703125]]}, {"title": "<PERSON>. <PERSON> on Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[311.25, 579.0], [473.25, 579.0], [473.25, 589.359375], [311.25, 589.359375]]}, {"title": "III. OUR INVESTIGATIONS", "heading_level": null, "page_id": 2, "polygon": [[115.1982421875, 495.75], [232.5, 495.75], [232.5, 505.5], [115.1982421875, 505.5]]}, {"title": "A. Experimental Overview", "heading_level": null, "page_id": 2, "polygon": [[47.25, 639.0], [159.0, 639.0], [159.0, 649.30078125], [47.25, 649.30078125]]}, {"title": "B. Experimental Setup", "heading_level": null, "page_id": 2, "polygon": [[309.75, 469.5], [405.75, 469.5], [405.75, 478.7578125], [309.75, 478.7578125]]}, {"title": "C. Implement Details", "heading_level": null, "page_id": 6, "polygon": [[48.0, 396.0], [138.0, 396.0], [138.0, 406.0546875], [48.0, 406.0546875]]}, {"title": "<PERSON>. Experimental Results", "heading_level": null, "page_id": 6, "polygon": [[309.5859375, 450.0], [413.25, 450.0], [413.25, 460.1953125], [309.5859375, 460.1953125]]}, {"title": "E. Component Analysis", "heading_level": null, "page_id": 7, "polygon": [[310.5, 483.0], [409.5, 483.0], [409.5, 492.29296875], [310.5, 492.29296875]]}, {"title": "IV. CONCLUSION", "heading_level": null, "page_id": 11, "polygon": [[135.0, 184.5], [213.0, 184.5], [213.0, 193.74609375], [135.0, 193.74609375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 11, "polygon": [[145.5, 397.5], [202.5, 397.5], [202.5, 406.828125], [145.5, 406.828125]]}, {"title": "SUPPLEMENTAL MATERIALS", "heading_level": null, "page_id": 13, "polygon": [[112.5, 56.25], [236.25, 56.25], [236.25, 66.6123046875], [112.5, 66.6123046875]]}, {"title": "A. <PERSON>look", "heading_level": null, "page_id": 13, "polygon": [[47.25, 71.25], [96.14794921875, 71.25], [96.14794921875, 82.0810546875], [47.25, 82.0810546875]]}, {"title": "B. Implement Details for MTT and TESLA", "heading_level": null, "page_id": 13, "polygon": [[47.88720703125, 303.75], [225.0, 303.75], [225.0, 314.208984375], [47.88720703125, 314.208984375]]}, {"title": "<PERSON><PERSON>-architecture experiments", "heading_level": null, "page_id": 13, "polygon": [[48.0, 405.66796875], [189.0, 405.66796875], [189.0, 414.94921875], [48.0, 414.94921875]]}, {"title": "<PERSON><PERSON> in Practice", "heading_level": null, "page_id": 13, "polygon": [[47.88720703125, 660.75], [157.78125, 660.75], [157.78125, 671.34375], [47.88720703125, 671.34375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 107], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Footnote", 3], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4293, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 114], ["Text", 7], ["SectionHeader", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 110], ["Text", 7], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 1912], ["Span", 739], ["Line", 73], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 23459, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 697], ["Span", 611], ["Line", 121], ["Text", 4], ["Caption", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 21869, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 816], ["Span", 646], ["Line", 61], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7161, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 594], ["Span", 537], ["Line", 96], ["Text", 7], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Caption", 1], ["Table", 1], ["Footnote", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12448, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["TableCell", 205], ["Line", 105], ["Text", 8], ["Table", 4], ["Caption", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 6204, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 1895], ["Span", 739], ["Line", 73], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 20451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 969], ["Span", 739], ["Line", 73], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 13535, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 880], ["Span", 661], ["Line", 71], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["Reference", 2], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 24958, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 143], ["ListItem", 46], ["Reference", 40], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 49], ["ListItem", 20], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 110], ["Text", 14], ["SectionHeader", 5], ["ListItem", 5], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["TableCell", 276], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["Reference", 2], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 21523, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 782], ["Span", 112], ["Line", 44], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5834, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DD-RobustBench__An_Adversarial_Robustness_Benchmark_for_Dataset_Distillation"}