# DDFAD: Dataset Distillation Framework for Audio Data

Wenbo Jiang, *Member, IEEE,* <PERSON><PERSON>, *Student Member, IEEE,* <PERSON><PERSON> (Corresponding author), *Fellow, IEEE,* <PERSON><PERSON>, *Student Member, IEEE,* <PERSON><PERSON><PERSON>, *Senior Member, IEEE,* and <PERSON><PERSON>, *Fellow, IEEE*

**Abstract**—Deep neural networks (DNNs) have achieved significant success in numerous applications. The remarkable performance of DNNs is largely attributed to the availability of massive, high-quality training datasets. However, processing such massive training data requires huge computational and storage resources. Dataset distillation is a promising solution to this problem, offering the capability to compress a large dataset into a smaller distilled dataset. The model trained on the distilled dataset can achieve comparable performance to the model trained on the whole dataset.

While dataset distillation has been demonstrated in image data, none have explored dataset distillation for audio data. In this work, for the first time, we propose a Dataset Distillation Framework for Audio Data (DDFAD). Specifically, we first propose the Fused Differential MFCC (FD-MFCC) as extracted features for audio data. After that, the FD-MFCC is distilled through the matching training trajectory distillation method. Finally, we propose an audio signal reconstruction algorithm based on the Griffin-Lim Algorithm to reconstruct the audio signal from the distilled FD-MFCC. Extensive experiments demonstrate the effectiveness of DDFAD on various audio datasets. In addition, we show that DDFAD has promising application prospects in many applications, such as continual learning and neural architecture search.

✦

**Index Terms**—Deep Learning, Dataset Distillation, Audio Classification.

## 1 INTRODUCTION

**DENS** have achieved remarkable performance in a wide range of applications. The superior performance of DNNs often requires large-scale training datasets. For ex-NNS have achieved remarkable performance in a wide range of applications. The superior performance of ample, the commonly used image classification dataset ImageNet [\[1\]](#page-8-0) has about 1.2 million training samples and 100,000 testing samples, taking up about 148G of storage space; the commonly used object detection dataset COCO [\[2\]](#page-8-1) has 118,287 training samples and 40,670 testing samples, taking up about 44G of storage space. However, the management of such massive data entails significant challenges, including the collection, storage, transmission, and pre-processing. Moreover, training models on such massive data comes with huge computational overhead. Such storage and computing requirements are huge and impractical for personal users. To address this problem, an emerging technology known as dataset distillation has garnered considerable research attention in recent years. As illustrated in Figure [1,](#page-0-0) dataset distillation extracts the knowledge from a large-scale dataset and generates a smaller synthetic distilled dataset. The models trained on the distilled dataset can achieve performance comparable to those trained on the original dataset. This technique can significantly decrease the computational resources of training a DNN.

<span id="page-0-0"></span>Image /page/0/Figure/9 description: This figure illustrates the process of dataset distillation in machine learning. On the left, an 'Original image dataset' of 50,000 images is shown, with a grid of various objects like airplanes, cars, animals, and boats. An arrow labeled 'Dataset distillation' points to the right, leading to a 'Distilled image dataset' containing only one image per class, also displayed in a grid. Below both datasets, an arrow labeled 'Train' points downwards to a stylized representation of a neural network. A dotted line labeled 'Similar performance' connects the neural network trained on the original dataset to the neural network trained on the distilled dataset, indicating comparable results.

Fig. 1: Dataset distillation for image data.

Existing works of dataset distillation are all focused on distilling image data, none of them has investigated dataset distillation for audio data. In fact, similar to the image dataset, the audio dataset also suffers from excessive volume, which needs huge storage and computational resources. For instance, the AudioSet dataset [\[3\]](#page-8-2) contains about 2 million audio clips from YouTube videos; the UrbanSound-8K dataset [\[4\]](#page-8-3) includes 8,732 audio clips of 10 categories of urban sounds, taking up about 5.6G of storage

<sup>•</sup> *W. Jiang, R. Zhang, H. Li, X. Liu and H. Yang are with the School of Computer Science and Engineering, University of Electronic Science and Technology of China, China (e-mail: wenbo <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>).*

<sup>•</sup> *S. Yu is a Professor of the School of Computer Science in the Faculty of Engineering and Information Technology at University of Technology Sydney, Sydney, Australia (e-mail: <EMAIL>).*

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image illustrates a process of dataset distillation for audio data. On the left, an 'Original audio dataset' with 8K sound clips is shown, with multiple waveform plots representing different audio samples. An arrow labeled 'Dataset distillation for audio dataset' points to the right, leading to a 'Distilled audio dataset' containing only 1 sound clip per class, also depicted by waveform plots. Below both datasets, there is an arrow labeled 'Train' pointing to a neural network diagram. A dotted line labeled 'Similar performance' connects the two neural network diagrams, indicating that training with the distilled dataset yields comparable results to training with the original dataset.

Fig. 2: Dataset distillation for audio data.

space; There is also an urgent need for dataset distillation schemes for audio data. In this work, for the first time, we propose the Dataset Distillation Framework for Audio Data (DDFAD) for audio classification tasks. As illustrated in Figure [2,](#page-1-0) similar with dataset distillation for image data, DDFAD can compress a large number of audio clips into a smaller number of audio clips. Training with the distilled audio dataset can also achieve comparable performance to those trained on the original audio data.

However, it is non-trivial to distill audio data. While traditional feature extraction methods, such as linear predictive cepstral coefficient (LPCC) [\[5\]](#page-8-4) and Mel frequency cepstral coefficient (MFCC) [\[6\]](#page-8-5), can extract audio data as feature spectrograms for DNN training, they prove insufficient in providing discriminative features in the case of the small-scale distilled dataset. To address this limitation, we propose the Fused Differential MFCC (FD-MFCC). It fuses the features of MFCC, the first-order difference of MFCC and the second-order difference of MFCC to make the extracted features more informative. After feature extraction, the dataset of FD-MFCC is distilled through the matching training trajectory (MTT) distillation method  $[7]^1$  $[7]^1$  $[7]^1$ . Finally, we propose an audio signal reconstruction algorithm based on the Griffin-Lim Algorithm (GLA) [\[8\]](#page-8-7) that rebuilds the distilled audio signal from the distilled FD-MFCC.

In practice, DDFAD offers a method to compress largescale audio dataset and is conductive to training audio data classification models and other downstream tasks such as continual learning and neural architecture search. The contributions of this work are summarized as follows:

• We present DDFAD, the first dataset distillation framework for audio data. Specifically, we propose FD-MFCC to make the extracted features of the audio data more informative. After that, the dataset of FD-MFCC is distilled through the MTT distillation method. Finally, we propose an audio signal reconstruction algorithm based on GLA to reconstruct the audio signal from the distilled FD-MFCC.

 $\overline{2}$ 

DDFAD. • We carry out experiments to show that DDFAD can greatly improve various downstream applications, such as continual learning and neural architecture search.

effectiveness and cross-architecture generalization of

The remainder of this paper is organized as follows: related work is presented in Section [2.](#page-1-2) Section [3](#page-2-0) provides the details of our attack methodologies. Experimental evaluations are shown in Section [4.](#page-4-0) Potential applications are discussed in Section [5.](#page-6-0) Finally, Section [6](#page-8-8) concludes the paper.

<span id="page-1-2"></span>

### 2 RELATED WORK

#### 2.1 Audio Data Classification

Audio data classification is an important topic with many potential applications, such as speech classification in industrial automation, environmental sound classification in weather prediction, etc. Early works on audio classification used Support Vector Machines (SVM) [\[9\]](#page-8-9), K-Nearest Neighbors (KNN) [\[10\]](#page-8-10) and Hidden Markov Models (HMMs) [\[11\]](#page-8-11) for classification. More recently, audio classification based on DNN is gradually becoming mainstream and achieving leading performance [\[12\]](#page-8-12), [\[13\]](#page-8-13), [\[14\]](#page-8-14), [\[15\]](#page-8-15). Thus, we mainly focus on DNN-based audio classification methods in this work.

In addition to choosing different machine learning models for classification, the extraction of audio features is also critical for audio data classification. Concretely, audio features can broadly be categorized into time-domain, frequency-domain, and cepstral-domain features.

**Time-domain feature** characterizes audio signals in relation to time. It directly uses the one-dimensional (1D) audio signals as the input of the classification model, which is computation-efficient. Notable time-domain features include Short-Time Energy (STE) [\[16\]](#page-8-16), Zero Crossing Rate (ZCR) [\[17\]](#page-8-17), Short-Time Autocorrelation Function (STAF) [\[18\]](#page-8-18), etc.

**Frequency-domain feature** refers to the characteristics of audio signals varying in frequency. It no longer uses the original 1D signals, but 2D signals (i.e., spectrograms) as the model input. Therefore, frequency-domain features contain more information compared to time-domain features. Common frequency domain features include Fourier Transform (FT) [\[19\]](#page-8-19), Discrete Cosine Transform (DCT) [\[20\]](#page-8-20), etc.

**Cepstral-domain feature** is mostly obtained by inverting some frequency-domain signals and their variants. Currently, the cepstral-domain feature is the most commonly used feature type and achieves leading performance in DNN-based audio classification. Representative cepstraldomain features include linear predictive cepstral coefficient (LPCC) [\[5\]](#page-8-4), Mel frequency cepstral coefficient (MFCC) [\[6\]](#page-8-5), etc.

#### 2.2 Dataset Distillation

The concept of dataset distillation is originated from knowledge distillation [\[21\]](#page-8-21). Knowledge distillation is designed to transfer the knowledge of a large-scale model to a more

<span id="page-1-1"></span><sup>1.</sup> It is worth noting that other distillation methods are also applicable to DDFAD. In our experiments, we also consider other state-of-the-art distillation methods for evaluations.

<span id="page-2-1"></span>Image /page/2/Figure/0 description: This figure illustrates a three-step process for audio signal reconstruction. Step 1, labeled 'FD-MFCC Feature Extraction,' shows an original audio signal being processed into three feature representations: MFCC, ΔMFCC, and Δ²MFCC. These features are then fused to create a DF-MFCC representation. Step 2, 'Dataset Distillation for FD-MFCC,' shows multiple DF-MFCC inputs leading to a 'Distilled DF-MFCC' output, indicated by an ellipsis suggesting multiple stages. Step 3, 'Audio Signal Reconstruction,' takes the 'Distilled DF-MFCC' and reconstructs the audio signal, which is then presented as the final output.

Fig. 3: The workflow of the proposed dataset distillation framework for audio data.

lightweight model, whereas dataset distillation is a technique to distill the knowledge of the large-scale dataset a small-scale distilled dataset. Their underlying technologies are different.

In recent years, various dataset distillation algorithms have been proposed and they can be mainly categorized into three approaches: performance matching, parameter matching, and distribution matching.

**Performance matching.** This approach focuses on optimizing a distilled dataset to ensure that neural networks trained on it exhibit minimal loss on the original dataset, thereby achieving comparable performance between models trained on distilled and original datasets. Performance matching was first proposed by Wang et al. [\[22\]](#page-8-22), who formulated dataset distillation as a bi-level optimization problem. However, the inner loops in their approach require extensive backpropagation gradient computation, which is highly inefficient. Subsequent works [\[23\]](#page-9-0), [\[24\]](#page-9-1), [\[25\]](#page-9-2) proposed to replace the neural network in the inner loop with a kernel model, bypassing the backpropagation gradient computation process.

**Parameter matching.** This approach focuses on optimizing the consistency of trained model parameters between the distilled dataset and the original dataset. It was initially proposed by *Zhao et al.* [\[26\]](#page-9-3), who formulated the objective as a minimization problem between two sets of gradients of the network parameters. Following [\[26\]](#page-9-3), numerous algorithms [\[7\]](#page-8-6), [\[27\]](#page-9-4), [\[28\]](#page-9-5), [\[29\]](#page-9-6), [\[30\]](#page-9-7) have been proposed to improve parameter matching. For example, *Cazenavette et al.* [\[7\]](#page-8-6) proposed a multi-step parameter matching approach known as matching training trajectory (MTT); *Zhao et al.* [\[30\]](#page-9-7) employed model augmentation techniques, such as utilizing earlystage models and parameter perturbation, to accelerate the training speed of dataset distillation.

**Distribution matching.** Instead of matching training effects or model parameters, distribution matching focuses on obtaining a distilled dataset whose distribution closely approximates that of the original dataset. For instance, *Zhao et al.* [\[31\]](#page-9-8) utilized the metric of Maximum Mean Discrepancy (MMD) metric to optimize the distance between the distribution of the distilled dataset and the original dataset; *Wang et al.* [\[32\]](#page-9-9) proposed CAFE, which ensures that statistics of features for the distilled and original samples extracted by each network layer except the final one are consistent.

#### 2.3 Coreset Selection

Coreset selection is another strategy to select a representative subset of the whole dataset through heuristic selection criteria. It is commonly used in active learning to identify training samples. For instance, random selection [\[33\]](#page-9-10) picks samples arbitrarily; Herding selection [\[34\]](#page-9-11) chooses samples closest to each class center; In K-Center selection [\[35\]](#page-9-12), multiple center points are chosen for each class in order to minimize the maximum distance between data points and their nearest center point. However, these coreset selection methods may not always yield optimal results for downstream audio data classification task. Furthermore, identifying an informative coreset can be challenging, particularly when dataset information is not concentrated in a few samples. In contrast, the dataset distillation methods can achieves better result for downstream audio data classification task through generating synthetic distilled data.

<span id="page-2-0"></span>

## 3 METHODOLOGY

#### 3.1 Overview

In this section, we present the details of DDFAD. It mainly contains three phases: feature extraction for audio data, dataset distillation for FD-MFCC and audio signal reconstruction. The workflow of DDFAD is illustrated in Figure [3.](#page-2-1) Below we describe the details of each phase.

#### 3.2 FD-MFCC Feature Extraction

Currently, MFCC is the most commonly used feature type for audio data and achieves leading performance in DNNbased audio classification. Although MFCC feature performs well on the entire source dataset, in the case of dataset distillation, MFCC struggles to extract sufficient discriminative features to maintain the accuracy of the model trained on the distilled dataset. To overcome this limitation, we propose the Fused Differential MFCC (FD-MFCC), which fuses the features of MFCC, the first-order difference of MFCC and the second-order difference of MFCC. Since different difference orders of MFCC can represent features from different aspects, FD-MFCC can make full use of the complementarity between them and enhance the feature representation capability.

As illustrated in Figure [4,](#page-3-0) the feature extraction process of FD-MFCC consists of 9 steps:

<span id="page-3-0"></span>Image /page/3/Figure/1 description: This is a flowchart illustrating the process of generating DF-MFCC features. The process begins with an input audio signal, which is then subjected to pre-emphasis, frame blocking, and windowing. Following this, the signal is processed through FFT, Mel Filter Banks, and Log operations. The output of the Log operation is then fed into a DCT. The DCT output is used to calculate MFCC. Subsequently, the first-order difference of MFCC and the second-order difference of MFCC are computed. Finally, these three components (MFCC, first-order difference, and second-order difference) are fused together to produce the DF-MFCC features.

Fig. 4: The feature extraction process of FD-MFCC.

- 1) **Pre-emphasis.** This step compensates for the loss of the high-frequency part of the audio signal, which is beneficial for feature extraction.
- 2) **Frame Blocking.** This step splits the audio data into small segments with a frame length of 20ms in order to ensure the smoothness of the audio signal.
- 3) **Windowing.** This step enhances the strength of the middle part of the signal in each frame and weakens the discontinuities at the endpoints.
- 4) **FFT.** FFT can transform time-domain signals into frequency-domain signals, which is conducive to obtaining more information in audio data.
- 5) **Mel Filter Banks.** Since human ears are not sensitive to the low-frequency part of the audio signal, the Mel filter bank is designed to enhance the signal in the middle of the triangle wave region and weaken the signal on both sides, which is beneficial for feature extraction.
- 6) **Log.** Since the perception of human ears grows in a logarithmic way, it also needs to take the logarithm of the obtained features to simulate the perception of human ears.
- 7) **DCT.** The function of DCT is to remove the correlation between the signals of different orders and map the signals back into a lower dimensional space. The MFCC feature can be obtained by performing DCT on the Mel spectrum feature.
- 8) **The first-order and second-order difference of MFCC.** After obtaining MFCC, we further calculate the firstorder and second-order difference of MFCC to extract more features:

$$
\Delta MFCC(t) = \frac{MFCC(t+1) - MFCC(t)}{2}, \quad (1)
$$

$$
\Delta^2 MFCC(t) = \frac{\Delta MFCC(t+1) - \Delta MFCC(t)}{2}, \tag{2}
$$

where MFCC(t) denotes the value of MFCC at time t;  $\triangle$ MFCC and  $\triangle$ <sup>2</sup>MFCC denote the first-order and second-order difference of MFCC, respectively.

9) **Feature Fusion.** The MFCC, ∆MFCC and ∆<sup>2</sup>MFCC are spliced and fused together to get FD-MFCC.

In our experiments, we conduct extensive ablation studies to show the superiority of our proposed FD-MFCC compared with traditional MFCC (refer to Section [4.4](#page-5-0) for more details).

#### 3.3 Dataset Distillation for FD-MFCC

Given the source training dataset S with  $|S|$  samples, the objective of dataset distillation is to extract the knowledge of S into a small distilled dataset D with  $|\mathcal{D}|$  samples ( $|\mathcal{S}| \gg$  $|\mathcal{D}|$ ), and the model trained on  $\mathcal D$  can achieve comparable performance to the model trained on S.

In this work, we adopt the state-of-the-art (SOTA) matching training trajectory (MTT) distillation method [\[7\]](#page-8-6) in DDFAD to distill FD-MFCC. Algorithm [1](#page-3-1) illustrates the detailed process of dataset distillation for FD-MFCC. Specifically, MTT first trains models on  $S$  and collects the trajectories of the model (referred to as the teacher model) in the buffer. Subsequently, ingredients in the buffer are randomly chosen to initialize the student model (the model trained on D). After collecting the trajectories of the student model, the distilled dataset is updated by matching the two training trajectories. The objective loss of MTT is defined as:

<span id="page-3-2"></span>
$$
\mathcal{L}_o = \frac{\left\| \boldsymbol{\theta}_D^{(t+N)} - \boldsymbol{\theta}_S^{(t+M)} \right\|_2^2}{\left\| \boldsymbol{\theta}_S^{(t)} - \boldsymbol{\theta}_S^{(t+M)} \right\|_2^2},\tag{3}
$$

where  $\theta_{\mathcal{S}}^{(t)}$  represents the parameter of the teacher model at training epoch  $t$ , which is stored in the buffer;  $\boldsymbol{\theta}_{\mathcal{D}}^{(t+N)}$ represents the parameter of the student model trained on D for  $N$  epochs with the initialization of  $\pmb{\theta}_{\mathcal{S}}^{(t)}$ .

<span id="page-3-1"></span>

## Algorithm 1 Dataset Distillation for FD-MFCC

- **Input:**  $\{\boldsymbol{\theta}_{\mathcal{S}}^{(i)}\}_{i=1}^T$ : trajectories of the teacher model; M: number of iterations for dataset distillation; N: number of updates between starting and target expert parameters;  $T'$ : the maximum start epoch.
- **Output:** the distilled dataset  $D$  and the learning rate  $\alpha$ .
- 1: randomly initialize the distilled dataset  $D$  and the trainable learning rate  $\alpha$
- 2: **for**  $m = 0 \rightarrow M$  **do**
- 3: Choose random start epoch  $t < T'$
- 4: Initialize student network with teacher trajectories:  $\boldsymbol{\theta}_{\mathcal{D}}^{(t)} = \boldsymbol{\theta}_{\mathcal{S}}^{(t)}$
- 5: **for**  $n = 0 \rightarrow N$  **do**
- 6: Sample a batch of distilled data:  $b_{t+n} \in \mathcal{D}$
- 7: Train the student model with gradient descent method:  $\boldsymbol{\theta}_{\mathcal{D}}^{(t+n+1)} = \boldsymbol{\theta}_{\mathcal{D}}^{(t+n)} - \alpha \nabla \mathcal{L}(b_{t+n}; \boldsymbol{\theta}_{\mathcal{D}}^{(t+n)})$
- 8: **end for**
- 9: Compute the objective  $\mathcal{L}_o$  according to Eq.[\(3\)](#page-3-2)
- 10: Update  $D$  and  $\alpha$  w.r.t.  $\mathcal{L}_o$
- 11: **end for**
- 12: **return**  $D$  and  $\alpha$

## 3.4 Audio Signal Reconstruction from the Distilled FD-MFCC

After distilling the dataset of FD-MFCC, the final phase is to reconstruct the audio signal from the distilled FD-MFCC. We propose an audio signal reconstruction algorithm based on GLA to reconstruct the audio signal from the distilled FD-MFCC.

As presented in Algorithm [2,](#page-4-1) we begin by applying the inverse DCT (IDCT) to the distilled FD-MFCC to obtain

the dB-scaled spectrogram. Subsequently, we employ the dB-to-power function<sup>[2](#page-4-2)</sup> to map the dB-scaled spectrogram to the mel power spectrogram. After that, we use the mel-to-stft function<sup>[2](#page-4-2)</sup> to approximate Short-Time Fourier Transform (STFT) magnitude from a Mel power spectrogram. Finally, we employ GLA [\[8\]](#page-8-7) to reconstruct the audio signal from the STFT magnitude.

Concretely, the objective of GLA is to reconstruct a spectrogram that is consistent with the given amplitude A. This is achieved through the following alternative projection procedure:

$$
\mathbf{X}^{[i]} = P_{\mathcal{C}}\left(P_{\mathcal{A}}\left(\mathbf{X}^{[i-1]}\right)\right),\tag{4}
$$

where  $X^{[i]}$  denotes the reconstructed audio signal at the ith iteration,  $C$  is defined as the set of all spectrograms that corresponds to a time-domain signal,  $A$  is defined as the set of all spectrograms that have the given magnitude spectrogram A.  $P_{A,C}$  represents the projection onto set A, C and they are defined as follows:

$$
P_{\mathcal{A}}(\mathbf{X}) = \mathbf{A} \frac{\mathbf{X}}{|\mathbf{X}|},\tag{5}
$$

$$
P_{\mathcal{C}}(\mathbf{X}) = \text{STFT}(\text{iSTFT}(\mathbf{X})),\tag{6}
$$

where STFT represents the short-time Fourier transform and iSTFT represents the inverse STFT. The reconstruction process is executed iteratively for  $I$  rounds to obtain the final reconstructed audio signal. The reconstruct distilled audio data can be seen in Figure [6.](#page-7-0)

<span id="page-4-1"></span>**Algorithm 2** Audio Signal Reconstruction from the Distilled FD-MFCC

- **Input:** D: the distilled dataset of FD-MFCC; I: the maximum number of iteration.
- **Output:** the reconstructed audio signal.
- 1: Compute dB-scaled spectrograms:

 $Spectrogram_{dB} = \text{IDCT}(\mathcal{D})$ 

- 2: Compute mel power spectrograms:  $Spectrogram_{mel} = dB-to-power(Spectrogram_{dB})$
- 3: Compute STFT magnitude:  $A = mel-to-stft(Spectrogram_{mel})$
- 4: Randomly initialize  $X^{[0]}$

5: for 
$$
i = 1 \rightarrow I
$$
 do  
6:  $\mathbf{X}^{[i]} = P_{\mathcal{C}} \left( P_{\mathcal{A}} \left( \mathbf{X}^{[i-1]} \right) \right)$ 

- 7: **end for**
- 8: **return**  $X^{[i]}$

<span id="page-4-0"></span>

## 4 EVALUATION

#### 4.1 Experimental Setup

##### 4.1.1 Model Architecture

We consider three model architectures, including ResNet18 [\[36\]](#page-9-13), ConvNet (which mainly contains multiple Conv-ReLU-AvgPooling blocks) and VGG11 [\[37\]](#page-9-14) for audio data classification.

<span id="page-4-2"></span>2. Available in librosa package: https://github.com/librosa/librosa.

- *4.1.2 Datasets*
  - **Free Spoken Digit Dataset (FSDD) [\[38\]](#page-9-15).** FSDD comprises recordings of spoken digits at a sampling rate of 8kHz. It contains 3,000 audio data clips of English pronunciation of numbers (0-9) recorded by six speakers.
  - **UrbanSound-8k Dataset [\[4\]](#page-8-3).** The UrbanSound dataset includes 8,732 labeled audio clips, each lasting up to 4 seconds. It covers 10 categories of urban sounds, such as air conditioner, car horn, drilling, etc.
  - **Ryerson Audio-Visual Database of Emotional Speech and Song (RAVDESS) [\[39\]](#page-9-16).** The RAVDESS dataset contains 7,356 audio clips depicting seven different speech emotions, e.g., calm, happy, sad, etc.

##### 4.1.3 Distillation Methods

In our experiments, we consider three SOTA distillation methods for experimental evaluations.

- **Dataset Condensation with Gradient Matching (DCGM) [\[26\]](#page-9-3).** DCGM aims to minimize the gap between two sets of gradients of the network parameters, where the gradients are computed based on the training loss over both the original dataset and the distilled dataset.
- **Dataset Condensation with Differentiable Siamese Augmentation (DCDSA) [\[40\]](#page-9-17).** DCDSA proposes a differentiable siamese augmentation method to synthesize distilled data to obtain better performance.
- **Matching Training Trajectory (MTT) [\[7\]](#page-8-6).** MTT first trains models on the source training dataset and collects the trajectories of the model in the buffer. After that, it also collects the trajectories of the network trained on the distilled dataset. The distilled dataset is updated by matching the two training trajectories.

Besides, we also consider two coreset selection methods for comparison.

- **Random selection [\[33\]](#page-9-10).** This is a simple approach that randomly selects samples as the coreset.
- **Herding selection [\[34\]](#page-9-11).** This is a distance-based algorithm that selects samples whose center is close to the center of each class.

#### 4.2 Distillation Performance

We perform our DDFAD with five considered distillation methods (DCGM [\[26\]](#page-9-3), DCDSA [\[40\]](#page-9-17), MTT [\[7\]](#page-8-6), Random selection [\[33\]](#page-9-10), Herding selection [\[34\]](#page-9-11)), to synthesize 1, 10, and 50 clips per class (CPC) respectively.

As presented in Table [1,](#page-5-1) dataset distillation methods are much more effective than coreset selection methods with the same CPC. This is because the dataset information can not be concentrated in a few samples. The synthetic distilled data can better represent the information of the whole dataset. Furthermore, among these dataset distillation methods, the DDFAD incorporating with the MTT outperforms other dataset distillation methods. Thus, for subsequent experiments, we adopt the MTT distillation method for evaluations of DDFAD.

For instance, in the case of the FSDD dataset with CPC=50, where the distilled dataset accounts for only 1/6 of the whole dataset, the test accuracies of DDFAD with

TABLE 1: The test accuracy (%) of DDFAD under different dataset distillation methods.

<span id="page-5-1"></span>

| Dataset           | Architecture | <b>CPC</b> | Distillation methods |           |        | Coreset selection methods |              | Whole dataset |
|-------------------|--------------|------------|----------------------|-----------|--------|---------------------------|--------------|---------------|
|                   |              |            | DCGM [26]            | DCDSA[40] | MTT[7] | Random [33]               | Herding [34] |               |
| <b>FSDD</b>       | ResNet18     | 1          | 17.75                | 17.86     | 28.42  | 11.57                     | 14.31        |               |
|                   |              | 10         | 58.97                | 56.05     | 75.30  | 42.98                     | 45.69        | 98.73         |
|                   |              | 50         | 87.46                | 94.12     | 97.30  | 80.78                     | 85.13        |               |
|                   | ConvNet      | 1          | 14.32                | 17.01     | 29.88  | 11.02                     | 13.20        |               |
|                   |              | 10         | 55.75                | 54.33     | 65.60  | 42.66                     | 47.50        | 90.52         |
|                   |              | 50         | 77.81                | 80.29     | 89.67  | 69.79                     | 73.08        |               |
|                   | VGG11        | 1          | 21.18                | 14.97     | 21.96  | 11.97                     | 14.21        |               |
|                   |              | 10         | 55.44                | 52.50     | 58.98  | 44.08                     | 48.27        | 98.44         |
|                   |              | 50         | 87.07                | 89.26     | 94.38  | 81.13                     | 85.59        |               |
| <b>UrbanSound</b> | ResNet18     | 1          | 12.48                | 12.04     | 19.88  | 10.84                     | 11.99        |               |
|                   |              | 10         | 38.30                | 34.57     | 40.69  | 21.80                     | 23.26        | 93.89         |
|                   |              | 50         | 53.81                | 52.58     | 62.75  | 43.77                     | 45.84        |               |
|                   | ConvNet      | 1          | 11.30                | 12.55     | 21.59  | 10.35                     | 11.51        |               |
|                   |              | 10         | 30.08                | 22.92     | 42.97  | 21.29                     | 22.08        | 70.78         |
|                   |              | 50         | 53.56                | 55.19     | 68.22  | 42.62                     | 43.64        |               |
|                   | VGG11        | 1          | 10.82                | 11.60     | 19.63  | 10.11                     | 11.37        |               |
|                   |              | 10         | 21.34                | 17.55     | 27.47  | 15.33                     | 16.88        | 89.04         |
|                   |              | 50         | 42.65                | 44.32     | 59.10  | 33.29                     | 33.30        |               |
| <b>RAVDESS</b>    | ResNet18     | 1          | 12.54                | 14.77     | 17.30  | 11.52                     | 12.63        |               |
|                   |              | 10         | 23.28                | 25.98     | 32.50  | 18.38                     | 20.71        | 67.75         |
|                   |              | 50         | 36.60                | 39.78     | 48.96  | 33.04                     | 34.12        |               |
|                   | ConvNet      | 1          | 16.30                | 14.64     | 22.17  | 10.89                     | 12.72        |               |
|                   |              | 10         | 20.83                | 21.51     | 29.22  | 11.97                     | 13.65        | 49.81         |
|                   |              | 50         | 33.70                | 32.89     | 46.91  | 24.25                     | 25.88        |               |
|                   | VGG11        | 1          | 13.55                | 12.79     | 19.20  | 11.22                     | 11.89        |               |
|                   |              | 10         | 26.85                | 23.69     | 27.33  | 18.83                     | 19.07        | 62.32         |
|                   |              | 50         | 39.02                | 38.45     | 47.91  | 34.46                     | 35.44        |               |

MTT (97.30% for ResNet18, 89.67% for ConvNet and 94.38% for VGG11) are very close to the test accuracies of the model trained on the whole dataset (98.73% for ResNet18, 90.52% for ConvNet and 98.44% for VGG11). The storage space and computational resources required by the whole dataset is six times more than the storage space and computational resources required by the distilled dataset.

#### 4.3 Cross-architecture Generalization

In dataset distillation, it is crucial for the distilled dataset constructed on one model to yield similar training effects on downstream models with arbitrary architectures. Thus, in this subsection, we evaluate the cross-architecture generalization performance of DDFAD. Specifically, we utilize the distilled datasets constructed on ResNet18, ConvNet and VGG11 to train models with different architectures, including ResNet18, ConvNet, and VGG11. The results in Table [2](#page-5-2) show that the performance of DDFAD remains consistent across different models used for distillation, which demonstrates the good cross-architecture generalization of DDFAD.

<span id="page-5-0"></span>

#### 4.4 Ablation Study of FD-MFCC

In this subsection, we conduct ablation studies to compare the effectiveness of our proposed FD-MFCC with MFCC [\[6\]](#page-8-5) and LPCC [\[5\]](#page-8-4) in DDFAD. As depicted in Figure [5,](#page-6-1) for different datasets and CPC settings, FD-MFCC consistently outperforms traditional MFCC and LPCC. This superiority arises from the ability of FD-MFCC to leverage the complementary nature of different difference orders of MFCC,

<span id="page-5-2"></span>TABLE 2: Cross-architecture generalization of DDFAD (CPC=50).

| Dataset    | Distillation architecture | Evaluation architecture |         |       |
|------------|---------------------------|-------------------------|---------|-------|
|            |                           | ResNet18                | ConvNet | VGG11 |
| FSDD       | ResNet18                  | 97.30                   | 78.33   | 80.15 |
|            | ConvNet                   | 96.07                   | 89.65   | 92.33 |
|            | VGG11                     | 96.31                   | 85.89   | 94.38 |
| UrbanSound | ResNet18                  | 62.75                   | 56.79   | 51.88 |
|            | ConvNet                   | 62.06                   | 68.22   | 39.81 |
|            | VGG11                     | 66.33                   | 58.87   | 59.10 |
| RAVDESS    | ResNet18                  | 48.96                   | 47.11   | 48.35 |
|            | ConvNet                   | 47.54                   | 46.91   | 49.63 |
|            | VGG11                     | 47.28                   | 47.10   | 47.91 |

thereby enhancing feature representation capability. In scenarios where the number of training samples is limited, such as dataset distillation, FD-MFCC is more informative and therefore can achieve higher accuracy.

#### 4.5 Analysis of the Resources Requirement of DDFAD

For dataset distillation, it is also important to consider the cost of resources of the algorithm. Hence, we report the computational overhead and the occupied GPU memory of DDFAD across different datasets, model architectures and CPC settings in Table [3.](#page-6-2) All the experiments are run on NVIDIA RTX A6000 GPUs.

The results indicate that more complex model architectures are often accompanied by larger computational overhead and more GPU memory. Overall, the time overhead and occupied GPU memory of DDFAD is also acceptable for data owners and can be further reduced on better devices.

<span id="page-6-1"></span>Image /page/6/Figure/0 description: This image contains three bar charts, labeled (a) FSDD, (b) UrbanSound, and (c) RAVDESS. Each chart displays the test accuracy (%) on the y-axis against CPC values of 1, 10, and 50 on the x-axis. Within each chart, there are three bars representing LPCC, MFCC, and FD-MFCC. For FSDD, at CPC=1, LPCC is ~22%, MFCC is ~30%, and FD-MFCC is ~30%. At CPC=10, LPCC is ~44%, MFCC is ~70%, and FD-MFCC is ~82%. At CPC=50, LPCC is ~58%, MFCC is ~80%, and FD-MFCC is ~98%. For UrbanSound, at CPC=1, LPCC is ~14%, MFCC is ~18%, and FD-MFCC is ~19%. At CPC=10, LPCC is ~32%, MFCC is ~41%, and FD-MFCC is ~41%. At CPC=50, LPCC is ~43%, MFCC is ~63%, and FD-MFCC is ~64%. For RAVDESS, at CPC=1, LPCC is ~12%, MFCC is ~17%, and FD-MFCC is ~18%. At CPC=10, LPCC is ~20%, MFCC is ~28%, and FD-MFCC is ~30%. At CPC=50, LPCC is ~36%, MFCC is ~47%, and FD-MFCC is ~50%.

Fig. 5: Ablation Study of FD-MFCC.

TABLE 3: The resources requirement of DDFAD.

<span id="page-6-2"></span>

| Dataset        | Architecture | Distillation time (min) |        |        |        | GPU memory (GB) |        |        |        |
|----------------|--------------|-------------------------|--------|--------|--------|-----------------|--------|--------|--------|
|                |              | CPC=1                   | CPC=10 | CPC=20 | CPC=50 | CPC=1           | CPC=10 | CPC=20 | CPC=50 |
| <b>FSDD</b>    | ResNet18     | 80.67                   | 92.11  | 100.59 | 116.04 | 31.01           | 35.61  | 38.45  | 44.22  |
|                | ConvNet      | 12.31                   | 15.16  | 15.79  | 17.20  | 17.79           | 21.00  | 23.29  | 25.24  |
|                | VGG11        | 50.21                   | 52.84  | 59.38  | 70.81  | 15.50           | 20.59  | 24.23  | 29.81  |
| UrbanSound     | ResNet18     | 179.56                  | 311.55 | 316.80 | 337.91 | 36.08           | 46.71  | 52.61  | 60.07  |
|                | ConvNet      | 17.16                   | 21.66  | 23.82  | 29.57  | 20.65           | 25.90  | 28.58  | 34.36  |
|                | VGG11        | 93.13                   | 105.18 | 108.20 | 11.89  | 18.31           | 26.52  | 36.92  | 46.12  |
| <b>RAVDESS</b> | ResNet18     | 105.11                  | 107.30 | 111.56 | 113.88 | 37.94           | 44.00  | 57.18  | 72.80  |
|                | ConvNet      | 18.33                   | 21.19  | 22.42  | 23.86  | 19.49           | 26.88  | 30.19  | 47.08  |
|                | VGG11        | 83.51                   | 85.35  | 86.83  | 104.01 | 24.01           | 43.09  | 50.58  | 56.91  |

## 4.6 Visualizations of the Distilled Audio Data

We select the digital seven audio data from the FSDD dataset; dog bark and gun shot audio data from the UrbanSound-8k dataset; and calm speech emotions audio data from the RAVDESS dataset as examples to show the waveform diagram of the original audio data and distilled audio data (recovered by Algorithm [2\)](#page-4-1). As presented in Figure [6,](#page-7-0) similar with dataset distillation for image data (see Figure [1\)](#page-0-0), the distilled audio data may not closely resemble the original audio data. It may be synthetic audio data with no practical meaning, but is conductive to the subsequent training process of audio data classification tasks.

## 4.7 Robustness against Noise

In this subsection, we evaluate the performance of DDFAD in the presence of additional noise. Specifically, we select the FSDD dataset as an example, and artificially add Gaussian noise with different variance  $σ$  to the waveforms of the distilled data to train the classification model. As presented in Figure [7,](#page-7-1) the accuracy remains stable with increasing Gaussian noise levels. It demonstrates that the distilled training dataset is robust to additional Gaussian noise.

<span id="page-6-0"></span>

## 5 POTENTIAL APPLICATIONS

In this section, we discuss some potential applications of DDFAD, such as continual learning and neural architecture search.

#### 5.1 Continual Learning

We apply our DDFAD to a Class Incremental Continual Learning (CICL) task, where the objective of CICL is to learn a new class while preserving the performance in old classes. Following the SOTA continual learning baseline EEIL [\[34\]](#page-9-11), we construct a limited budget rehearsal memory comprising representative samples from old classes. The function of this memory is to alleviate the catastrophic forgetting problem of DNNs. In our experiments, we replace the representative sample selection strategy in EEIL (i.e. herding), with our DDFAD and random selection and keep the rest the same to perform the CICL task. The representative samples pool is set to 20 audio clips for each old class.

As illustrated in Table [4,](#page-6-3) DDFAD outperforms EEIL and random selection in the CICL task. This suggests that the memory of old classes constructed by our DDFAD contains more informative data for model training compared to EEIL and random selection methods. This improvement stems from the ability of DDFAD to distill knowledge from old classes more effectively, thereby aiding in mitigating catastrophic forgetting in CICL scenarios.

<span id="page-6-3"></span>TABLE 4: The performance of DDFAD in class incremental continual learning (%).

| Dataset    | Architecture | CICL methods |           |             |
|------------|--------------|--------------|-----------|-------------|
|            |              | DDFAD        | EEIL [34] | Random [33] |
| FSDD       | ResNet18     | 92.05        | 80.51     | 87.10       |
|            | ConvNet      | 92.92        | 76.41     | 83.33       |
|            | VGG11        | 91.67        | 74.15     | 80.77       |
| UrbanSound | ResNet18     | 46.25        | 21.58     | 38.55       |
|            | ConvNet      | 51.00        | 13.62     | 15.22       |
|            | VGG11        | 47.12        | 26.74     | 31.74       |
| RAVDESS    | ResNet18     | 39.31        | 15.94     | 22.54       |
|            | ConvNet      | 34.03        | 13.19     | 11.11       |
|            | VGG11        | 36.25        | 16.72     | 28.31       |

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image displays a diagram illustrating the process of distilling audio data using a method labeled DDFAD. The diagram is divided into four sections, labeled (a), (b), (c), and (d), each representing different types of audio data. Section (a) shows "Digital seven audio data from the FSDD dataset," with multiple original audio waveforms leading to a distilled audio waveform. Section (b) depicts "Dog bark audio data from the UrbanSound-8k dataset," also showing original waveforms processed into a distilled waveform. Section (c) illustrates "Gun shot audio data from the UrbanSound-8k dataset," with a similar flow from original to distilled audio. Finally, section (d) presents "Calm speech emotions audio data from the RAVDESS dataset," again demonstrating the transformation from original audio data to distilled audio data. Each section includes plots of the audio waveforms, with the x-axis representing time and the y-axis representing amplitude.

Fig. 6: Visualizations of the recovered distilled audio data.

<span id="page-7-1"></span>Image /page/7/Figure/3 description: This image contains three line graphs, labeled (a) ResNet18, (b) ConvNet, and (c) VGG11. Each graph plots accuracy on the y-axis against sigma (σ) on the x-axis, ranging from 0 to 1. There are three lines in each graph, representing different CPC values: CPC=1 (blue triangles), CPC=10 (green circles), and CPC=50 (red squares). In all three graphs, the accuracy for CPC=1 is around 0.25 and decreases slightly as sigma increases. The accuracy for CPC=10 is around 0.75 and decreases gradually as sigma increases. The accuracy for CPC=50 is around 0.95 and decreases slightly as sigma increases. The overall trend shows that higher CPC values generally result in higher accuracy, and the accuracy tends to decrease slightly with increasing sigma for all CPC values.

Fig. 7: Robustness against Gaussian noise with different variance  $\sigma$ .

#### 5.2 Neural Architecture Search

Our proposed DDFAD can also be applied to neural architecture search (NAS), which searches for the best network architecture for a given dataset. It typically needs significant training efforts of many candidate neural network architectures on the given dataset. Benefiting from the small size of the distilled dataset, it can be served as a sub-dataset to accelerate model evaluation in NAS.

Specifically, we follow the previous work [\[26\]](#page-9-3) to design a set of candidate neural network architectures based on the considered ConvNet. We vary the depth, width, pooling, activation, and normalization layers of the ConvNet, producing 720 candidate architectures. These models are trained on the entire FSDD training dataset to establish ground-truth performance. Four NAS methods are considered for comparison, including random selection, herding selection, early-stopping and DDFAD. In terms of random selection, herding selection and DDFAD, we generate three sub-datasets using these methods with 20 audio clips per class. The models are trained for 100 epochs. For earlystopping, we train the model on the entire original training dataset for 10 epochs. Finally, we identify the topperforming architectures of these NAS methods and report their testing performance.

We illustrate the distribution of correlation between the NAS-based performance and the ground-truth performance on 5% top-performing architectures in Figure [8.](#page-8-23) It can be seen that the NAS-based performance by DDFAD achieves the highest correlation (0.73) with the ground-truth performance. It demonstrates the promising applications of our DDFAD in NAS.

In addition to continual learning and neural architecture search, DDFAD can also be utilized to protect the privacy of the training dataset. Because the original training data

<span id="page-8-23"></span>Image /page/8/Figure/1 description: This image contains four scatter plots, labeled (a) DDFAD, (b) Random, (c) Herding, and (d) Early-stopping. Each plot shows the relationship between 'Sub-dataset Acc.' on the x-axis and 'Whole-set Acc.' on the y-axis. The correlation coefficients for each plot are displayed at the top: 0.73 for DDFAD, 0.33 for Random, 0.42 for Herding, and 0.26 for Early-stopping. The DDFAD plot shows a positive correlation, while the other plots show weaker or no clear correlation.

Fig. 8: The performance of DDFAD in NAS.

is hard to recover from the distilled data, even in the case of training data leakage and member inference attacks [\[41\]](#page-9-18). The evaluations of data privacy protection are left for future work.

<span id="page-8-8"></span>

## 6 CONCLUSIONS AND FUTURE WORKS

In this work, we propose a dataset distillation framework for audio data (DDFAD). Specifically, we propose the Fused Differential MFCC (FD-MFCC) as extracted features for audio data. It fuses the features of MFCC, the first-order difference of MFCC and the second-order difference of MFCC, which is more informative in the case of the smallscale distilled dataset. After that, we employ the matching training trajectory (MTT) distillation method to distill the FD-MFCC features. Finally, we propose an audio signal reconstruction algorithm based on the Griffin-Lim to rebuild the audio signal from the distilled FD-MFCC. Extensive experiments demonstrate the effectiveness and promising application prospects of DDFAD.

For future works, we intend to explore other potential applications of audio dataset distillation, such as data privacy protection. In addition, we aim to investigate dataset distillation methods under other tasks, such as object detection and natural language processing.

## REFERENCES

- <span id="page-8-0"></span>[1] O. Russakovsky, J. Deng, H. Su, J. Krause, S. Satheesh, S. Ma, Z. Huang, A. Karpathy, A. Khosla, M. Bernstein, A. C. Berg, and L. Fei-Fei, "ImageNet Large Scale Visual Recognition Challenge," *International Journal of Computer Vision (IJCV)*, vol. 115, no. 3, pp. 211–252, 2015.
- <span id="page-8-1"></span>[2] T.-Y. Lin, M. Maire, S. Belongie, J. Hays, P. Perona, D. Ramanan, P. Dollár, and C. L. Zitnick, "Microsoft coco: Common objects in context," in *Proceedings of ECCV*. Springer, 2014, pp. 740–755.
- <span id="page-8-2"></span>[3] J. F. Gemmeke, D. P. Ellis, D. Freedman, A. Jansen, W. Lawrence, R. C. Moore, M. Plakal, and M. Ritter, "Audio set: An ontology and human-labeled dataset for audio events," in *2017 IEEE international conference on acoustics, speech and signal processing (ICASSP)*. IEEE, 2017, pp. 776–780.
- <span id="page-8-3"></span>[4] J. Salamon, C. Jacoby, and J. P. Bello, "A dataset and taxonomy for urban sound research," in *Proceedings of the 22nd ACM international conference on Multimedia*, 2014, pp. 1041–1044.
- <span id="page-8-4"></span>[5] A. Chowdhury and A. Ross, "Fusing mfcc and lpc features using 1d triplet cnn for speaker recognition in severely degraded audio signals," *IEEE transactions on information forensics and security*, vol. 15, pp. 1616–1629, 2019.
- <span id="page-8-5"></span>[6] O. K. Toffa and M. Mignotte, "Environmental sound classification using local binary pattern and audio features collaboration," *IEEE Transactions on Multimedia*, vol. 23, pp. 3978–3985, 2020.

- <span id="page-8-6"></span>[7] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of CVPR*, 2022, pp. 4750–4759.
- <span id="page-8-7"></span>[8] R. Nenov, D.-K. Nguyen, P. Balazs, and R. Ioan Bot, "Accelerated griffin-lim algorithm: A fast and provably converging numerical method for phase retrieval," *IEEE Transactions on Signal Processing*, vol. 72, pp. 190–202, 2024.
- <span id="page-8-9"></span>[9] C.-C. Lin, S.-H. Chen, T.-K. Truong, and Y. Chang, "Audio classification and categorization based on wavelets and support vector machine," *IEEE Transactions on Speech and Audio Processing*, vol. 13, no. 5, pp. 644–651, 2005.
- <span id="page-8-10"></span>[10] L. Lu, H.-J. Zhang, and H. Jiang, "Content analysis for audio classification and segmentation," *IEEE Transactions on speech and audio processing*, vol. 10, no. 7, pp. 504–516, 2002.
- <span id="page-8-11"></span>[11] A. Pikrakis, S. Theodoridis, and D. Kamarotos, "Classification of musical patterns using variable duration hidden markov models," *IEEE Transactions on Audio, Speech, and Language Processing*, vol. 14, no. 5, pp. 1795–1807, 2006.
- <span id="page-8-12"></span>[12] J. Li, C. Wang, J. Chen, H. Zhang, Y. Dai, L. Wang, L. Wang, and A. K. Nandi, "Explainable cnn with fuzzy tree regularization for respiratory sound analysis," *IEEE Transactions on Fuzzy Systems*, vol. 30, no. 6, pp. 1516–1528, 2022.
- <span id="page-8-13"></span>[13] N. W. Hasan, A. S. Saudi, M. I. Khalil, and H. M. Abbas, "A genetic algorithm approach to automate architecture design for acoustic scene classification," *IEEE Transactions on Evolutionary Computation*, vol. 27, no. 2, pp. 222–236, 2022.
- <span id="page-8-14"></span>[14] S. K. Ghosh, R. Ponnalagu, R. K. Tripathy, G. Panda, and R. B. Pachori, "Automated heart sound activity detection from pcg signal using time–frequency-domain deep neural network," *IEEE Transactions on Instrumentation and Measurement*, vol. 71, pp. 1–10, 2022.
- <span id="page-8-15"></span>[15] A. M. Tripathi and O. J. Pandey, "Divide and distill: new outlooks on knowledge distillation for environmental sound classification, *IEEE Transactions on Audio, Speech, and Language Processing*, vol. 31, pp. 1100–1113, 2023.
- <span id="page-8-16"></span>[16] H. Li, G. Xu, Z. Li, K. Zhang, X. Zheng, C. Du, C. Han, J. Kuang, Y. Du, and S. Zhang, "A precise frequency recognition method of short-time ssvep signals based on signal extension," *IEEE Transactions on Neural Systems and Rehabilitation Engineering*, 2023.
- <span id="page-8-17"></span>[17] R. G. Bachu, S. Kopparthi, B. Adapa, and B. D. Barkana, "Voiced/unvoiced decision for speech signals based on zerocrossing rate and energy," *Advanced Techniques in Computing Sciences and Software Engineering*, pp. 279–282, 2010.
- <span id="page-8-18"></span>[18] L. Tang, X. Wu, D. Wang, and X. Liu, "A comparative experimental study of vibration and acoustic emission on fault diagnosis of low-speed bearing," *IEEE Transactions on Instrumentation and Measurement*, 2023.
- <span id="page-8-19"></span>[19] J. Shi, J. Zheng, X. Liu, W. Xiang, and Q. Zhang, "Novel shorttime fractional fourier transform: Theory, implementation, and applications," *IEEE Transactions on Signal Processing*, vol. 68, pp. 3280–3295, 2020.
- <span id="page-8-20"></span>[20] C. Geng and L. Wang, "End-to-end speech enhancement based on discrete cosine transform," in *Proceedings of IEEE International Conference on Artificial Intelligence and Computer Applications (ICAICA)*, 2020, pp. 379–383.
- <span id="page-8-21"></span>[21] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-8-22"></span>[22] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.

- <span id="page-9-0"></span>[23] Y. Zhou, E. Nezhadarya, and J. Ba, "Dataset distillation using neural feature regression," in *Proceedings of NIPS*, vol. 35, 2022, pp. 9813–9827.
- <span id="page-9-1"></span>[24] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-9-2"></span>[25] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," in *Proceedings of NIPS* , vol. 34, 2021, pp. 5186–5198.
- <span id="page-9-3"></span>[26] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *Proceedings of ICLR*, 2020.
- <span id="page-9-4"></span>[27] S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon, "Dataset condensation with contrastive signals," in *Proceedings of ICML*, 2022, pp. 12 352– 12 364.
- <span id="page-9-5"></span>[28] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan, "Delving into effective gradient matching for dataset condensation," in *Proceedings of IEEE International Conference on Omni-layer Intelligent Systems (COINS)* , 2023, pp. 1–6.
- <span id="page-9-6"></span>[29] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient synthetic-data parameterization," in *Proceedings of ICML*, 2022, pp. 11 102–11 118.
- <span id="page-9-7"></span>[30] L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and D. Xu, "Accelerating dataset distillation via model augmentation," in *Proceedings of CVPR*, 2023, pp. 11 950–11 959.
- <span id="page-9-8"></span>[31] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *Proceedings of CVPR*, 2023, pp. 6514–6523.
- <span id="page-9-9"></span>[32] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *Proceedings of CVPR*, 2022, pp. 12 196– 12 205.
- <span id="page-9-10"></span>[33] S.-A. Rebuffi, A. Kolesnikov, G. Sperl, and C. H. Lampert, "icarl: Incremental classifier and representation learning," in *Proceedings of CVPR*, 2017, pp. 2001–2010.
- <span id="page-9-11"></span>[34] F. M. Castro, M. J. Marín-Jiménez, N. Guil, C. Schmid, and K. Alahari, "End-to-end incremental learning," in *Proceedings of ECCV*, 2018, pp. 233–248.
- <span id="page-9-12"></span>[35] A. Chembu, S. Sanner, H. Khurram, and A. Kumar, "Scalable and globally optimal generalized  $l_1$  k-center clustering via constraint generation in mixed integer linear programming," in *Proceedings of AAAI*, vol. 37, no. 6, 2023, pp. 7015–7023.
- <span id="page-9-13"></span>[36] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of CVPR*, 2016, pp. 770–778.
- <span id="page-9-14"></span>[37] K. Simonyan and A. Zisserman, "Very deep convolutional networks for large-scale image recognition," in *Proceedings of ICLR*, 2015.<br>[38] Z. Jackson,
- <span id="page-9-15"></span>Z. Jackson, "Free spoken digit dataset," Online,<br>2016. [Online]. Available: https://github.com/Jakobovski/ Available: [https://github.com/Jakobovski/](https://github.com/Jakobovski/free-spoken-digit-dataset) [free-spoken-digit-dataset](https://github.com/Jakobovski/free-spoken-digit-dataset)
- <span id="page-9-16"></span>[39] S. Livingstone and F. Russo, "Ryerson audiovisual database of emotional speeches and songs (ravdess): a dynamic, multimodal set of north american english face and voice expressions," *Plos One*, vol. 13, p. e0196391, 2018.
- <span id="page-9-17"></span>[40] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *Proceedings of ICML*, 2021, pp. 12 674– 12 685.
- <span id="page-9-18"></span>[41] M. Bertran, S. Tang, A. Roth, M. Kearns, J. H. Morgenstern, and S. Z. Wu, "Scalable membership inference attacks via quantile regression," vol. 36, 2024.