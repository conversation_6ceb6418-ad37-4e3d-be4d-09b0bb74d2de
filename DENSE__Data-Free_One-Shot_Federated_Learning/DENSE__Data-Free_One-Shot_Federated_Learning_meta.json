{"table_of_contents": [{"title": "DENSE: Data-Free One-Shot Federated Learning", "heading_level": null, "page_id": 0, "polygon": [[120.75, 97.5], [489.75, 99.0], [489.75, 117.0], [120.75, 116.3056640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 263.25], [328.5, 263.25], [328.5, 273.796875], [282.75, 273.796875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 483.0], [191.3994140625, 483.0], [191.3994140625, 495.0], [107.25, 495.0]]}, {"title": "2 Data-Free One-Shot Federated Learning", "heading_level": null, "page_id": 1, "polygon": [[106.5, 597.75], [334.6875, 597.75], [334.6875, 608.6953125], [106.5, 608.6953125]]}, {"title": "2.1 Framework Overview", "heading_level": null, "page_id": 1, "polygon": [[106.3828125, 620.25], [224.419921875, 620.25], [224.419921875, 631.51171875], [106.3828125, 631.51171875]]}, {"title": "2.2 Data Generation", "heading_level": null, "page_id": 2, "polygon": [[106.5, 372.41015625], [202.5, 372.41015625], [202.5, 382.078125], [106.5, 382.078125]]}, {"title": "Algorithm 1 Training process of DENSE", "heading_level": null, "page_id": 4, "polygon": [[106.5, 334.5], [274.5, 334.5], [274.5, 344.953125], [106.5, 344.953125]]}, {"title": "2.3 Model Distillation", "heading_level": null, "page_id": 4, "polygon": [[106.98046875, 620.25], [207.75, 620.25], [207.75, 631.125], [106.98046875, 631.125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 5, "polygon": [[106.5, 576.75], [192.0, 576.75], [192.0, 588.19921875], [106.5, 588.19921875]]}, {"title": "3.1 Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[106.5, 594.75], [216.75, 594.75], [216.75, 604.828125], [106.5, 604.828125]]}, {"title": "3.1.1 Datasets", "heading_level": null, "page_id": 5, "polygon": [[106.5, 609.75], [174.814453125, 609.75], [174.814453125, 619.13671875], [106.5, 619.13671875]]}, {"title": "3.1.2 Data partition", "heading_level": null, "page_id": 6, "polygon": [[106.98046875, 169.0927734375], [198.75, 169.0927734375], [198.75, 177.9873046875], [106.98046875, 177.9873046875]]}, {"title": "3.1.3 Baselines", "heading_level": null, "page_id": 6, "polygon": [[106.5, 245.25], [178.5, 245.25], [178.5, 254.654296875], [106.5, 254.654296875]]}, {"title": "3.1.4 Settings", "heading_level": null, "page_id": 6, "polygon": [[106.5, 387.75], [173.25, 387.75], [173.25, 397.546875], [106.5, 397.546875]]}, {"title": "3.2 Results", "heading_level": null, "page_id": 6, "polygon": [[106.5, 497.25], [162.75, 497.25], [162.75, 506.98828125], [106.5, 506.98828125]]}, {"title": "3.2.1 Evaluation on real-world datasets", "heading_level": null, "page_id": 6, "polygon": [[106.3828125, 518.25], [282.0, 518.25], [282.0, 527.87109375], [106.3828125, 527.87109375]]}, {"title": "3.2.2 Impact of model distillation", "heading_level": null, "page_id": 7, "polygon": [[106.5, 347.66015625], [258.0, 347.66015625], [258.0, 358.1015625], [106.5, 358.1015625]]}, {"title": "3.2.3 Results in heterogeneous FL", "heading_level": null, "page_id": 7, "polygon": [[106.8310546875, 498.48046875], [259.5, 498.48046875], [259.5, 509.30859375], [106.8310546875, 509.30859375]]}, {"title": "3.3 Analysis of Our Method", "heading_level": null, "page_id": 7, "polygon": [[106.5, 651.0], [234.0, 651.0], [234.0, 661.2890625], [106.5, 661.2890625]]}, {"title": "3.3.1 Impact of the number of clients", "heading_level": null, "page_id": 7, "polygon": [[106.5, 670.5], [273.0, 670.5], [273.0, 681.3984375], [106.5, 681.3984375]]}, {"title": "3.3.2 Combination with imbalanced learning", "heading_level": null, "page_id": 8, "polygon": [[106.5, 378.75], [306.0, 378.75], [306.0, 389.0390625], [106.5, 389.0390625]]}, {"title": "3.3.3 Visualization of synthetic data", "heading_level": null, "page_id": 9, "polygon": [[106.45751953125, 74.25], [267.0, 74.25], [267.0, 83.6279296875], [106.45751953125, 83.6279296875]]}, {"title": "3.3.4 Extend to multiple rounds", "heading_level": null, "page_id": 9, "polygon": [[106.98046875, 198.0], [249.75, 198.0], [249.75, 208.248046875], [106.98046875, 208.248046875]]}, {"title": "3.3.5 Contribution of \\mathcal{L}_{BN} and \\mathcal{L}_{div}", "heading_level": null, "page_id": 9, "polygon": [[106.5, 366.609375], [267.0, 366.609375], [267.0, 376.27734375], [106.5, 376.27734375]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[107.25, 549.0], [183.779296875, 549.0], [183.779296875, 560.7421875], [107.25, 560.7421875]]}, {"title": "5 Acknowledgement", "heading_level": null, "page_id": 9, "polygon": [[107.05517578125, 652.5], [220.833984375, 652.5], [220.833984375, 663.609375], [107.05517578125, 663.609375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[106.5, 72.0], [164.25, 72.0], [164.25, 83.724609375], [106.5, 83.724609375]]}, {"title": "Checklist", "heading_level": null, "page_id": 14, "polygon": [[107.25, 72.0], [157.5, 72.0], [157.5, 83.67626953125], [107.25, 83.67626953125]]}, {"title": "6 Appendix", "heading_level": null, "page_id": 15, "polygon": [[106.681640625, 72.0], [176.25, 72.0], [176.25, 83.8212890625], [106.681640625, 83.8212890625]]}, {"title": "6.1 Preliminaries and Related Works", "heading_level": null, "page_id": 15, "polygon": [[106.98046875, 96.75], [272.25, 96.75], [272.25, 106.8310546875], [106.98046875, 106.8310546875]]}, {"title": "6.1.1 Federated Learning", "heading_level": null, "page_id": 15, "polygon": [[107.25, 117.0], [224.2705078125, 117.0], [224.2705078125, 127.23046875], [107.25, 127.23046875]]}, {"title": "6.1.2 One-shot Federated Learning", "heading_level": null, "page_id": 15, "polygon": [[107.25, 307.5], [264.75, 307.5], [264.75, 317.49609375], [107.25, 317.49609375]]}, {"title": "6.1.3 Knowledge Distillation in FL", "heading_level": null, "page_id": 15, "polygon": [[106.5, 589.5], [261.0, 589.5], [261.0, 599.80078125], [106.5, 599.80078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 45], ["Text", 5], ["SectionHeader", 3], ["Footnote", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 54], ["Text", 5], ["ListItem", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 415], ["Line", 75], ["TextInlineMath", 3], ["Reference", 3], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 88], ["Text", 7], ["Equation", 2], ["Reference", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 727], ["Line", 55], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Reference", 2], ["Code", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 55], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 3], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["TableCell", 248], ["Line", 82], ["SectionHeader", 5], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Text", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9481, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 85], ["TableCell", 63], ["SectionHeader", 4], ["Caption", 2], ["Text", 2], ["TextInlineMath", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 6451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["TableCell", 192], ["Line", 79], ["Caption", 4], ["Reference", 4], ["Table", 2], ["Text", 2], ["Figure", 2], ["TableGroup", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4873, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["TableCell", 146], ["Line", 62], ["Text", 6], ["SectionHeader", 5], ["Table", 3], ["Reference", 3], ["TextInlineMath", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 9086, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 54], ["ListItem", 19], ["Reference", 19], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 54], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 54], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 35], ["ListItem", 12], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 36], ["ListItem", 23], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 58], ["TextInlineMath", 7], ["SectionHeader", 5], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Line", 9], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DENSE__Data-Free_One-Shot_Federated_Learning"}