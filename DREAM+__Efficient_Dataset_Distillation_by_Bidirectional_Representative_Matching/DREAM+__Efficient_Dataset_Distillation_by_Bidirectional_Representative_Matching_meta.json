{"table_of_contents": [{"title": "DREAM+: Efficient Dataset Distillation by\nBidirectional Representative Matching", "heading_level": null, "page_id": 0, "polygon": [[87.0, 54.75], [525.33984375, 54.75], [525.33984375, 106.6376953125], [87.0, 106.6376953125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 334.5], [140.25, 334.5], [140.25, 345.146484375], [48.0, 345.146484375]]}, {"title": "2 RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[311.25, 243.0], [417.75, 243.0], [417.75, 253.880859375], [311.25, 253.880859375]]}, {"title": "2.1 Coreset Selection", "heading_level": null, "page_id": 1, "polygon": [[310.482421875, 260.25], [415.5, 260.25], [415.5, 270.509765625], [310.482421875, 270.509765625]]}, {"title": "2.2 Dataset distillation", "heading_level": null, "page_id": 1, "polygon": [[310.5, 430.5], [420.0, 430.5], [420.0, 441.24609375], [310.5, 441.24609375]]}, {"title": "2.3 Clustering", "heading_level": null, "page_id": 2, "polygon": [[45.75, 360.0], [117.75, 360.0], [117.75, 370.283203125], [45.75, 370.283203125]]}, {"title": "2.4 Differences from Related Works", "heading_level": null, "page_id": 2, "polygon": [[45.75, 603.75], [214.5, 603.75], [214.5, 613.72265625], [45.75, 613.72265625]]}, {"title": "3 METHOD", "heading_level": null, "page_id": 2, "polygon": [[311.25, 475.5], [373.5, 476.82421875], [373.5, 486.87890625], [311.25, 486.87890625]]}, {"title": "3.1 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[311.25, 615.0], [395.25, 615.0], [395.25, 624.9375], [311.25, 624.9375]]}, {"title": "3.2 Observations on Training Efficiency", "heading_level": null, "page_id": 3, "polygon": [[45.75, 699.0], [233.25, 699.0], [233.25, 708.85546875], [45.75, 708.85546875]]}, {"title": "3.3 Bidirectional Representative Matching", "heading_level": null, "page_id": 4, "polygon": [[46.5, 513.75], [243.75, 513.75], [243.75, 523.6171875], [46.5, 523.6171875]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[311.25, 611.25], [400.5, 611.25], [400.5, 621.84375], [311.25, 621.84375]]}, {"title": "4.1 Datasets and Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[309.884765625, 627.75], [501.732421875, 627.75], [501.732421875, 637.69921875], [309.884765625, 637.69921875]]}, {"title": "4.2 Comparison with State-of-the-art Methods", "heading_level": null, "page_id": 5, "polygon": [[45.75, 571.5], [261.0, 571.5], [261.0, 581.625], [45.75, 581.625]]}, {"title": "4.3 Efficiency comparison", "heading_level": null, "page_id": 5, "polygon": [[310.5, 523.5], [436.5, 523.5], [436.5, 533.671875], [310.5, 533.671875]]}, {"title": "4.4 Ablation Study and Analysis", "heading_level": null, "page_id": 5, "polygon": [[309.884765625, 698.25], [463.5, 698.25], [463.5, 708.46875], [309.884765625, 708.46875]]}, {"title": "IEEE TRANSACTIONS ON PATTERN ANALYSIS AND <PERSON><PERSON><PERSON><PERSON> INTELLIGENCE 9", "heading_level": null, "page_id": 8, "polygon": [[47.25, 26.25], [563.25, 26.25], [563.25, 36.859130859375], [47.25, 36.859130859375]]}, {"title": "4.5 Visualizations", "heading_level": null, "page_id": 8, "polygon": [[45.75, 570.75], [135.0, 570.75], [135.0, 580.078125], [45.75, 580.078125]]}, {"title": "4.6 Application on Continual Learning", "heading_level": null, "page_id": 9, "polygon": [[46.5, 696.75], [226.810546875, 696.75], [226.810546875, 707.30859375], [46.5, 707.30859375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[311.25, 379.5], [396.0, 380.25], [396.0, 390.19921875], [311.25, 390.19921875]]}, {"title": "6 LIMITATIONS AND FUTURE WORKS", "heading_level": null, "page_id": 9, "polygon": [[311.25, 570.75], [500.25, 570.75], [500.25, 581.625], [311.25, 581.625]]}, {"title": "", "heading_level": null, "page_id": 10, "polygon": [[277.611328125, 355.201171875], [340.962890625, 355.201171875], [340.962890625, 364.482421875], [277.611328125, 364.482421875]]}, {"title": "APPENDIX\nMORE VISUALIZATION RESULTS", "heading_level": null, "page_id": 11, "polygon": [[47.25, 451.5], [205.5, 451.5], [205.5, 475.6640625], [47.25, 475.6640625]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 11, "polygon": [[311.25, 528.75], [417.75, 528.75], [417.75, 539.47265625], [311.25, 539.47265625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 11, "polygon": [[311.25, 657.0], [380.25, 657.0], [380.25, 666.703125], [311.25, 666.703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 89], ["Text", 11], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5427, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 112], ["Text", 6], ["Caption", 3], ["ListItem", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["FigureGroup", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1446, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["Line", 121], ["Text", 8], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1507, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 113], ["Text", 8], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 661], ["Span", 509], ["Line", 95], ["Text", 11], ["SectionHeader", 3], ["Caption", 1], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["Line", 127], ["TableCell", 88], ["Caption", 6], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 6797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 705], ["TableCell", 224], ["Line", 153], ["Text", 7], ["Caption", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 20773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 158], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1915, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 119], ["TableCell", 30], ["Text", 7], ["Reference", 3], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2829, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 82], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Reference", 2], ["Picture", 1], ["Figure", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1380, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 141], ["Span", 34], ["Line", 7], ["Caption", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 62], ["Text", 6], ["Reference", 4], ["Caption", 3], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 716, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 153], ["ListItem", 57], ["Reference", 57], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 41], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DREAM+__Efficient_Dataset_Distillation_by_Bidirectional_Representative_Matching"}