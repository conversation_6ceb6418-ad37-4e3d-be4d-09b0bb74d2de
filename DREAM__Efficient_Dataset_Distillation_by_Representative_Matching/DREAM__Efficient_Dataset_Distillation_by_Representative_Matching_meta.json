{"table_of_contents": [{"title": "DREAM: Efficient Dataset Distillation by Representative Matching", "heading_level": null, "page_id": 0, "polygon": [[89.349609375, 106.5], [504.0, 106.5], [504.0, 118.8193359375], [89.349609375, 118.8193359375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 243.0], [191.25, 243.0], [191.25, 253.494140625], [144.75, 253.494140625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 570.0], [127.5, 570.0], [127.5, 580.8515625], [48.75, 580.8515625]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[307.5, 195.0], [396.75, 195.0], [396.75, 206.701171875], [307.5, 206.701171875]]}, {"title": "2.1. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[307.5, 216.0], [418.5, 216.0], [418.5, 226.23046875], [307.5, 226.23046875]]}, {"title": "2.2. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[48.75, 105.0], [119.25, 105.0], [119.25, 115.822265625], [48.75, 115.822265625]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[48.0, 288.75], [102.0, 288.75], [102.0, 299.70703125], [48.0, 299.70703125]]}, {"title": "3.1. Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[48.0, 423.0], [132.8291015625, 423.0], [132.8291015625, 433.8984375], [48.0, 433.8984375]]}, {"title": "3.2. Observations on Training Efficiency", "heading_level": null, "page_id": 2, "polygon": [[307.5, 288.0], [497.25, 288.0], [497.25, 297.966796875], [307.5, 297.966796875]]}, {"title": "3.3. Representative Matching", "heading_level": null, "page_id": 3, "polygon": [[48.0, 551.25], [187.5, 551.25], [187.5, 561.515625], [48.0, 561.515625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 72.75], [128.25, 72.75], [128.25, 83.8212890625], [48.75, 83.8212890625]]}, {"title": "4.1. Datasets and Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[48.75, 93.0], [243.0, 93.0], [243.0, 104.02734375], [48.75, 104.02734375]]}, {"title": "4.2. Comp<PERSON>on with State-of-the-art Methods", "heading_level": null, "page_id": 4, "polygon": [[48.75, 437.25], [270.0, 437.25], [270.0, 447.43359375], [48.75, 447.43359375]]}, {"title": "4.3. Ablation Study and Analysis", "heading_level": null, "page_id": 4, "polygon": [[48.75, 648.0], [204.0, 648.0], [204.0, 658.58203125], [48.75, 658.58203125]]}, {"title": "4.4. Visualizations", "heading_level": null, "page_id": 6, "polygon": [[307.5, 576.75], [393.75, 576.75], [393.75, 587.42578125], [307.5, 587.42578125]]}, {"title": "4.5. Application on Continual Learning", "heading_level": null, "page_id": 7, "polygon": [[307.5, 622.5], [493.962890625, 624.0], [493.962890625, 634.60546875], [307.5, 634.60546875]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[48.75, 207.75], [120.0, 207.75], [120.0, 218.8828125], [48.75, 218.8828125]]}, {"title": "6. Limitations and Future Works", "heading_level": null, "page_id": 8, "polygon": [[48.75, 385.5], [218.25, 385.5], [218.25, 397.16015625], [48.75, 397.16015625]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 8, "polygon": [[48.0, 528.0], [145.5, 528.0], [145.5, 539.0859375], [48.0, 539.0859375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.0, 661.5], [106.5, 661.5], [106.5, 672.1171875], [48.0, 672.1171875]]}, {"title": "7. <PERSON><PERSON><PERSON><PERSON> with More Methods", "heading_level": null, "page_id": 11, "polygon": [[48.63427734375, 72.75], [234.75, 72.75], [234.75, 83.53125], [48.63427734375, 83.53125]]}, {"title": "8. Acc<PERSON>cy Curve Visualization", "heading_level": null, "page_id": 11, "polygon": [[48.0, 298.5], [216.0, 298.5], [216.0, 309.568359375], [48.0, 309.568359375]]}, {"title": "9. DREAM on Distribution Matching", "heading_level": null, "page_id": 11, "polygon": [[48.75, 488.25], [240.75, 488.25], [240.75, 499.640625], [48.75, 499.640625]]}, {"title": "10. Distilled Dataset Visualization", "heading_level": null, "page_id": 11, "polygon": [[307.5, 180.0], [481.5, 180.0], [481.5, 190.9423828125], [307.5, 190.9423828125]]}, {"title": "11. Differences from Related Works", "heading_level": null, "page_id": 11, "polygon": [[307.5, 342.0], [492.0, 342.0], [492.0, 352.494140625], [307.5, 352.494140625]]}, {"title": "12. Clustering Analysis", "heading_level": null, "page_id": 11, "polygon": [[307.494140625, 635.25], [427.5, 635.25], [427.5, 646.20703125], [307.494140625, 646.20703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 77], ["Text", 6], ["SectionHeader", 3], ["Footnote", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 103], ["Text", 9], ["ListItem", 3], ["SectionHeader", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 99], ["Text", 5], ["TextInlineMath", 5], ["SectionHeader", 4], ["Equation", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 107], ["Text", 8], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 102], ["Text", 10], ["SectionHeader", 4], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1276], ["TableCell", 380], ["Line", 104], ["Reference", 6], ["Table", 5], ["Caption", 5], ["Text", 4], ["TableGroup", 3]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 4, "llm_tokens_used": 26383, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 112], ["TableCell", 12], ["Text", 5], ["Reference", 3], ["Caption", 2], ["TextInlineMath", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2117, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 112], ["Text", 5], ["Reference", 4], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["Picture", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2396, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 103], ["Reference", 18], ["ListItem", 17], ["Text", 6], ["SectionHeader", 4], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 115], ["ListItem", 33], ["Reference", 33], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 31], ["ListItem", 10], ["Reference", 10], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 96], ["Text", 9], ["SectionHeader", 6], ["Equation", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["TableCell", 218], ["Line", 67], ["Caption", 4], ["Reference", 4], ["Table", 2], ["Text", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4543, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Line", 73], ["Span", 17], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Picture", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1288, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DREAM__Efficient_Dataset_Distillation_by_Representative_Matching"}