{"table_of_contents": [{"title": "DYNAFED: Tackling Client Data Heterogeneity with Global Dynamics", "heading_level": null, "page_id": 0, "polygon": [[81.0, 106.5], [513.0, 106.5], [513.0, 119.2060546875], [81.0, 119.2060546875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 228.75], [191.25, 228.75], [191.25, 239.958984375], [144.75, 239.958984375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 638.25], [127.5, 638.25], [127.5, 649.6875], [48.75, 649.6875]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 622.5], [392.25, 622.5], [392.25, 634.21875], [307.5, 634.21875]]}, {"title": "3. Preliminary", "heading_level": null, "page_id": 2, "polygon": [[48.75, 568.5], [123.75, 568.5], [123.75, 579.3046875], [48.75, 579.3046875]]}, {"title": "4. Proposed Method", "heading_level": null, "page_id": 2, "polygon": [[307.5, 354.0], [412.083984375, 354.0], [412.083984375, 364.095703125], [307.5, 364.095703125]]}, {"title": "4.1. Acquiring Global Knowledge by Data Synthesis", "heading_level": null, "page_id": 2, "polygon": [[307.5, 476.25], [546.0, 476.25], [546.0, 486.75], [307.5, 486.75]]}, {"title": "4.2. Overall Algorithm of DynaFed", "heading_level": null, "page_id": 3, "polygon": [[48.75, 466.5], [213.0, 466.5], [213.0, 476.82421875], [48.75, 476.82421875]]}, {"title": "Algorithm 2 DYNAFED", "heading_level": null, "page_id": 3, "polygon": [[307.5, 73.5], [409.693359375, 73.5], [409.693359375, 83.2412109375], [307.5, 83.2412109375]]}, {"title": "5. Theoretical Analysis", "heading_level": null, "page_id": 4, "polygon": [[48.75, 72.0], [167.25, 72.75], [167.25, 84.0], [48.75, 83.67626953125]]}, {"title": "6. Experiments", "heading_level": null, "page_id": 4, "polygon": [[307.5, 331.998046875], [387.0, 331.998046875], [387.0, 342.439453125], [307.5, 342.439453125]]}, {"title": "6.1. Main Experiments with Data Heterogeneity", "heading_level": null, "page_id": 5, "polygon": [[307.1953125, 510.75], [532.5, 510.75], [532.5, 520.5234375], [307.1953125, 520.5234375]]}, {"title": "6.2. Detailed Analysis", "heading_level": null, "page_id": 6, "polygon": [[48.75, 468.0], [151.13232421875, 468.0], [151.13232421875, 478.7578125], [48.75, 478.7578125]]}, {"title": "6.3. Architecture Generalization and Efficiency", "heading_level": null, "page_id": 7, "polygon": [[48.75, 675.2109375], [270.75, 675.2109375], [270.75, 685.265625], [48.75, 685.265625]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 607.5], [378.0, 607.5], [378.0, 618.75], [307.5, 618.75]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 96.0], [106.5322265625, 96.0], [106.5322265625, 107.314453125], [48.75, 107.314453125]]}, {"title": "<PERSON><PERSON> of Theorem 1", "heading_level": null, "page_id": 11, "polygon": [[48.0, 72.0], [168.75, 72.0], [168.75, 83.337890625], [48.0, 83.337890625]]}, {"title": "<PERSON><PERSON> Detailed Experiment Settings", "heading_level": null, "page_id": 12, "polygon": [[48.0, 585.75], [213.75, 585.75], [213.75, 597.09375], [48.0, 597.09375]]}, {"title": "B.1. Detailed Descriptions of Baselines", "heading_level": null, "page_id": 13, "polygon": [[48.0, 431.25], [230.25, 431.25], [230.25, 441.24609375], [48.0, 441.24609375]]}, {"title": "<PERSON><PERSON> Experiment with Other Participation Ratio", "heading_level": null, "page_id": 13, "polygon": [[48.75, 570.0], [285.75, 570.0], [285.75, 581.23828125], [48.75, 581.23828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 83], ["Text", 6], ["SectionHeader", 3], ["Reference", 2], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5685, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 104], ["Text", 4], ["TextInlineMath", 4], ["ListItem", 3], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["Line", 111], ["Text", 5], ["TextInlineMath", 5], ["Reference", 5], ["Equation", 4], ["SectionHeader", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1098], ["Line", 114], ["ListItem", 24], ["Text", 11], ["ListGroup", 6], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1217], ["Line", 149], ["TextInlineMath", 10], ["Equation", 9], ["Text", 6], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1115], ["TableCell", 263], ["Line", 209], ["Reference", 4], ["Caption", 3], ["Table", 2], ["Text", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 6310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 618], ["Line", 118], ["TableCell", 8], ["TextInlineMath", 5], ["Reference", 4], ["Caption", 3], ["Figure", 2], ["Text", 2], ["FigureGroup", 2], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 877], ["TableCell", 216], ["Line", 143], ["Text", 7], ["Table", 3], ["Caption", 3], ["Reference", 3], ["SectionHeader", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 6849, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 114], ["ListItem", 27], ["Reference", 27], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 115], ["ListItem", 29], ["Reference", 29], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 20], ["Line", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 861], ["Line", 91], ["Text", 14], ["Reference", 9], ["TextInlineMath", 8], ["Equation", 7], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 810], ["Line", 129], ["Equation", 7], ["Text", 7], ["TextInlineMath", 6], ["Reference", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 85], ["TableCell", 80], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["Text", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3530, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DYNAFED__Tackling_Client_Data_Heterogeneity_with_Global_Dynamics"}