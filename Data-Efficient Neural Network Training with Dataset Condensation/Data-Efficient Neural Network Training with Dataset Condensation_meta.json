{"table_of_contents": [{"title": "THE UNIVERSITY\nof EDINBURGH", "heading_level": null, "page_id": 0, "polygon": [[186.90680100755668, 213.3779296875], [451.365234375, 213.3779296875], [451.365234375, 285.7373046875], [186.90680100755668, 285.7373046875]]}, {"title": "Data-Efficient Neural Network Training with\nDataset Condensation", "heading_level": null, "page_id": 1, "polygon": [[127.60705289672543, 143.9572573463936], [511.9294710327456, 143.9572573463936], [511.9294710327456, 197.652099609375], [127.60705289672543, 197.652099609375]]}, {"title": "Abstract", "heading_level": null, "page_id": 2, "polygon": [[279.98488664987406, 55.2459716796875], [358.801007556675, 55.2459716796875], [358.801007556675, 73.4385986328125], [279.98488664987406, 73.4385986328125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 4, "polygon": [[230.44332493702768, 53.983971504897596], [407.59193954659946, 53.983971504897596], [407.59193954659946, 73.181640625], [230.44332493702768, 73.181640625]]}, {"title": "Declaration", "heading_level": null, "page_id": 5, "polygon": [[266.47355163727957, 52.48441674087266], [372.3123425692695, 52.48441674087266], [372.3123425692695, 74.20947265625], [266.47355163727957, 74.20947265625]]}, {"title": "Table of Contents", "heading_level": null, "page_id": 6, "polygon": [[222.93702770780857, 137.20926090828138], [415.09823677581863, 137.20926090828138], [415.09823677581863, 163.322509765625], [222.93702770780857, 163.322509765625]]}, {"title": "Bibliography 95", "heading_level": null, "page_id": 8, "polygon": [[109.59193954659949, 456.6144256455921], [527.6926952141058, 456.6144256455921], [527.6926952141058, 472.3916015625], [109.59193954659949, 472.3916015625]]}, {"title": "Chapter 1", "heading_level": null, "page_id": 9, "polygon": [[265.98828125, 140.093505859375], [369.30982367758185, 140.093505859375], [369.30982367758185, 164.350341796875], [265.98828125, 164.350341796875]]}, {"title": "Introduction", "heading_level": null, "page_id": 9, "polygon": [[252.7470703125, 200.1905609973286], [384.72265625, 200.1905609973286], [384.72265625, 222.833984375], [252.7470703125, 222.833984375]]}, {"title": "1.1 Contributions", "heading_level": null, "page_id": 12, "polygon": [[112.59445843828715, 527.093499554764], [252.96221662468514, 527.093499554764], [252.96221662468514, 542.2841796875], [112.59445843828715, 542.2841796875]]}, {"title": "Chapter 2", "heading_level": null, "page_id": 14, "polygon": [[266.47355163727957, 139.99072265625], [371.5617128463476, 139.99072265625], [371.5617128463476, 163.83642578125], [266.47355163727957, 163.83642578125]]}, {"title": "Background and Related Research", "heading_level": null, "page_id": 14, "polygon": [[133.61209068010075, 200.94033837934103], [504.62109375, 200.94033837934103], [504.62109375, 223.65625], [133.61209068010075, 223.65625]]}, {"title": "2.1 Sample Selection", "heading_level": null, "page_id": 14, "polygon": [[111.84382871536523, 274.41852181656276], [279.375, 274.41852181656276], [279.375, 290.05419921875], [111.84382871536523, 290.05419921875]]}, {"title": "2.2 Sample Synthesis", "heading_level": null, "page_id": 16, "polygon": [[112.59445843828715, 239.17898486197683], [284.4886649874055, 239.17898486197683], [284.4886649874055, 254.4912109375], [112.59445843828715, 254.4912109375]]}, {"title": "2.2.1 Generative Models", "heading_level": null, "page_id": 16, "polygon": [[112.59445843828715, 275.9180765805877], [270.9773299748111, 275.9180765805877], [270.9773299748111, 289.4375], [112.59445843828715, 289.4375]]}, {"title": "2.2.2 Dataset Distillation", "heading_level": null, "page_id": 18, "polygon": [[112.59445843828715, 156.7034728406055], [272.4785894206549, 156.7034728406055], [272.4785894206549, 169.695068359375], [112.59445843828715, 169.695068359375]]}, {"title": "2.3 Other Related Research", "heading_level": null, "page_id": 19, "polygon": [[112.40478515625, 353.89492430988423], [326.5239294710327, 353.89492430988423], [326.5239294710327, 368.99169921875], [112.40478515625, 368.99169921875]]}, {"title": "2.3.1 Few-shot Learning", "heading_level": null, "page_id": 19, "polygon": [[112.59445843828715, 392.83740234375], [270.9773299748111, 392.83740234375], [270.9773299748111, 405.58251953125], [112.59445843828715, 405.58251953125]]}, {"title": "2.3.2 Knowledge Distillation", "heading_level": null, "page_id": 19, "polygon": [[112.1865234375, 652.3063223508459], [294.99748110831234, 652.3063223508459], [294.99748110831234, 665.212890625], [112.1865234375, 665.212890625]]}, {"title": "2.3.3 Image Compression", "heading_level": null, "page_id": 20, "polygon": [[112.55029296875, 233.93054318788958], [279.98488664987406, 233.93054318788958], [279.98488664987406, 247.0908203125], [112.55029296875, 247.0908203125]]}, {"title": "2.3.4 Continual Learning", "heading_level": null, "page_id": 20, "polygon": [[112.4775390625, 533.2392578125], [273.2292191435768, 533.2392578125], [273.2292191435768, 546.3955078125], [112.4775390625, 546.3955078125]]}, {"title": "2.3.5 Neural Architecture Search", "heading_level": null, "page_id": 21, "polygon": [[112.25927734375, 176.19768477292965], [322.77078085642313, 176.19768477292965], [322.77078085642313, 189.12109375], [112.25927734375, 189.12109375]]}, {"title": "2.3.6 Federated Learning & Data Privacy", "heading_level": null, "page_id": 21, "polygon": [[112.59445843828715, 456.6144256455921], [370.0604534005038, 456.6144256455921], [370.0604534005038, 470.3359375], [112.59445843828715, 470.3359375]]}, {"title": "Chapter 3", "heading_level": null, "page_id": 22, "polygon": [[266.47355163727957, 140.1962890625], [371.626953125, 140.1962890625], [371.626953125, 163.83642578125], [266.47355163727957, 163.83642578125]]}, {"title": "Dataset Condensation with Gradient\nMatching", "heading_level": null, "page_id": 22, "polygon": [[124.9912109375, 200.1905609973286], [513.3515625, 200.1905609973286], [513.3515625, 262.91943359375], [124.9912109375, 262.91943359375]]}, {"title": "3.1 Introduction", "heading_level": null, "page_id": 22, "polygon": [[112.59445843828715, 545.0881567230632], [241.7027707808564, 545.0881567230632], [241.7027707808564, 560.78515625], [112.59445843828715, 560.78515625]]}, {"title": "3.2 Method", "heading_level": null, "page_id": 25, "polygon": [[112.59445843828715, 276.6678539626002], [206.42317380352642, 276.6678539626002], [206.42317380352642, 291.08203125], [112.59445843828715, 291.08203125]]}, {"title": "3.2.1 Dataset Condensation", "heading_level": null, "page_id": 25, "polygon": [[112.59445843828715, 314.5166015625], [292.74559193954656, 314.5166015625], [292.74559193954656, 326.8505859375], [112.59445843828715, 326.8505859375]]}, {"title": "3.2.2 Dataset Condensation with Parameter Matching", "heading_level": null, "page_id": 26, "polygon": [[111.8955078125, 387.6349065004452], [447.37531486146094, 387.6349065004452], [447.37531486146094, 400.23779296875], [111.8955078125, 400.23779296875]]}, {"title": "3.2.3 Dataset Condensation with Curriculum Gradient Matching", "heading_level": null, "page_id": 27, "polygon": [[112.59445843828715, 449.1166518254675], [511.9294710327456, 449.1166518254675], [511.9294710327456, 462.935546875], [112.59445843828715, 462.935546875]]}, {"title": "Algorithm 1: Dataset condensation with gradient matching", "heading_level": null, "page_id": 29, "polygon": [[117.279296875, 204.68922528940337], [408.3425692695214, 204.68922528940337], [408.3425692695214, 217.4892578125], [117.279296875, 217.4892578125]]}, {"title": "Input: Training set \\mathcal T", "heading_level": null, "page_id": 29, "polygon": [[125.427734375, 220.43455031166516], [237.19899244332493, 220.43455031166516], [237.19899244332493, 232.90673828125], [125.427734375, 232.90673828125]]}, {"title": "3.3 Experiments", "heading_level": null, "page_id": 30, "polygon": [[112.59445843828715, 311.15761353517365], [244.70528967254407, 311.15761353517365], [244.70528967254407, 326.8505859375], [112.59445843828715, 326.8505859375]]}, {"title": "3.3.1 Dataset Condensation", "heading_level": null, "page_id": 30, "polygon": [[112.59445843828715, 348.646482635797], [293.4962216624685, 348.646482635797], [293.4962216624685, 362.2080078125], [112.59445843828715, 362.2080078125]]}, {"title": "Chapter 3. Dataset Condensation with Gradient Matching 26", "heading_level": null, "page_id": 34, "polygon": [[112.59445843828715, 21.74354407836153], [526.191435768262, 21.74354407836153], [526.191435768262, 34.3809814453125], [112.59445843828715, 34.3809814453125]]}, {"title": "3.3.2 Applications", "heading_level": null, "page_id": 34, "polygon": [[112.4775390625, 631.3125556544968], [234.19647355163727, 631.3125556544968], [234.19647355163727, 644.2451171875], [112.4775390625, 644.2451171875]]}, {"title": "3.4 Conclusion and Limitation", "heading_level": null, "page_id": 37, "polygon": [[112.59445843828715, 161.20213713268032], [346.0403022670025, 161.20213713268032], [346.0403022670025, 176.787109375], [112.59445843828715, 176.787109375]]}, {"title": "Chapter 4", "heading_level": null, "page_id": 38, "polygon": [[265.8427734375, 140.20837043633125], [371.5617128463476, 140.20837043633125], [371.5617128463476, 163.528076171875], [265.8427734375, 163.528076171875]]}, {"title": "Dataset Condensation with\nDifferentiable Siamese Augmentation", "heading_level": null, "page_id": 38, "polygon": [[120.10075566750629, 200.1905609973286], [517.1838790931989, 200.1905609973286], [517.1838790931989, 262.7138671875], [120.10075566750629, 262.7138671875]]}, {"title": "4.1 Introduction", "heading_level": null, "page_id": 38, "polygon": [[111.84382871536523, 621.5654496883348], [241.7027707808564, 621.5654496883348], [241.7027707808564, 637.255859375], [111.84382871536523, 637.255859375]]}, {"title": "4.2 Related Work", "heading_level": null, "page_id": 41, "polygon": [[111.96826171875, 566.8317008014247], [249.95969773299748, 566.8317008014247], [249.95969773299748, 581.7529296875], [111.96826171875, 581.7529296875]]}, {"title": "4.3 Method", "heading_level": null, "page_id": 42, "polygon": [[112.59445843828715, 644.0587711487088], [206.42317380352642, 644.0587711487088], [206.42317380352642, 659.45703125], [112.59445843828715, 659.45703125]]}, {"title": "4.3.1 Dataset Condensation Review", "heading_level": null, "page_id": 43, "polygon": [[111.38623046875, 58.482635796972396], [340.0352644836272, 58.482635796972396], [340.0352644836272, 71.537109375], [111.38623046875, 71.537109375]]}, {"title": "4.3.2 Differentiable Siamese Augmentation (DSA)", "heading_level": null, "page_id": 43, "polygon": [[112.4775390625, 671.791015625], [424.1057934508816, 671.791015625], [424.1057934508816, 684.947265625], [112.4775390625, 684.947265625]]}, {"title": "4.3.3 Training Algorithm", "heading_level": null, "page_id": 45, "polygon": [[112.33203125, 594.573463935886], [270.9773299748111, 594.573463935886], [270.9773299748111, 608.4765625], [112.33203125, 608.4765625]]}, {"title": "4.4 Experiments", "heading_level": null, "page_id": 46, "polygon": [[112.59445843828715, 316.4060552092609], [244.70528967254407, 316.4060552092609], [244.70528967254407, 332.1953125], [112.59445843828715, 332.1953125]]}, {"title": "4.4.1 Datasets & Implementation Details", "heading_level": null, "page_id": 46, "polygon": [[112.59445843828715, 354.64470169189667], [369.30982367758185, 354.64470169189667], [369.30982367758185, 367.34716796875], [112.59445843828715, 367.34716796875]]}, {"title": "4.4.2 <PERSON>mp<PERSON><PERSON> to State of the Art", "heading_level": null, "page_id": 47, "polygon": [[112.59445843828715, 467.11130899376667], [345.2896725440806, 467.11130899376667], [345.2896725440806, 480.203125], [112.59445843828715, 480.203125]]}, {"title": "4.4.3 Cross-architecture Generalization", "heading_level": null, "page_id": 51, "polygon": [[111.16796875, 58.482635796972396], [364.05541561712846, 58.482635796972396], [364.05541561712846, 71.537109375], [111.16796875, 71.537109375]]}, {"title": "4.4.4 Ablation Study", "heading_level": null, "page_id": 51, "polygon": [[112.59445843828715, 299.9109528049866], [247.70780856423173, 299.9109528049866], [247.70780856423173, 312.8720703125], [112.59445843828715, 312.8720703125]]}, {"title": "4.4.5 Continual Learning", "heading_level": null, "page_id": 53, "polygon": [[112.59445843828715, 275.16829919857526], [273.2292191435768, 275.16829919857526], [273.2292191435768, 288.82080078125], [112.59445843828715, 288.82080078125]]}, {"title": "4.4.6 Neural Architecture Search", "heading_level": null, "page_id": 55, "polygon": [[112.041015625, 58.482635796972396], [322.77078085642313, 58.482635796972396], [322.77078085642313, 71.537109375], [112.041015625, 71.537109375]]}, {"title": "4.5 Discussion", "heading_level": null, "page_id": 56, "polygon": [[112.59445843828715, 582.5770258236865], [234.19647355163727, 582.5770258236865], [234.19647355163727, 598.1982421875], [112.59445843828715, 598.1982421875]]}, {"title": "4.5.1 Why Does DSA Work?", "heading_level": null, "page_id": 56, "polygon": [[112.59445843828715, 621.5654496883348], [291.9949622166247, 621.5654496883348], [291.9949622166247, 634.3779296875], [112.59445843828715, 634.3779296875]]}, {"title": "4.5.2 Initializing Synthetic Images", "heading_level": null, "page_id": 57, "polygon": [[112.25927734375, 392.13357079252], [329.5264483627204, 392.13357079252], [329.5264483627204, 405.17138671875], [112.25927734375, 405.17138671875]]}, {"title": "4.5.3 Disentangling Semantics and Prior Knowledge", "heading_level": null, "page_id": 57, "polygon": [[112.4775390625, 614.0676758682101], [442.8715365239294, 614.0676758682101], [442.8715365239294, 626.9775390625], [112.4775390625, 626.9775390625]]}, {"title": "4.6 Conclusion and Limitation", "heading_level": null, "page_id": 58, "polygon": [[112.59445843828715, 414.62689225289404], [346.0403022670025, 414.62689225289404], [346.0403022670025, 429.6337890625], [112.59445843828715, 429.6337890625]]}, {"title": "Chapter 5", "heading_level": null, "page_id": 59, "polygon": [[266.4248046875, 140.20837043633125], [371.5617128463476, 140.20837043633125], [371.5617128463476, 163.83642578125], [266.4248046875, 163.83642578125]]}, {"title": "Dataset Condensation with\nDistribution Matching", "heading_level": null, "page_id": 59, "polygon": [[173.4453125, 200.1905609973286], [463.87890625, 200.1905609973286], [463.87890625, 263.125], [173.4453125, 263.125]]}, {"title": "5.1 Introduction", "heading_level": null, "page_id": 59, "polygon": [[112.11376953125, 621.5654496883348], [241.7027707808564, 621.5654496883348], [241.7027707808564, 637.255859375], [112.11376953125, 637.255859375]]}, {"title": "5.2 Methodology", "heading_level": null, "page_id": 62, "polygon": [[112.4775390625, 373.38913624220834], [248.45843828715363, 373.38913624220834], [248.45843828715363, 388.72607421875], [112.4775390625, 388.72607421875]]}, {"title": "5.2.1 Dataset Condensation Problem", "heading_level": null, "page_id": 62, "polygon": [[112.59445843828715, 411.62778272484417], [348.29219143576825, 411.62778272484417], [348.29219143576825, 424.7001953125], [112.59445843828715, 424.7001953125]]}, {"title": "5.2.2 Dataset Condensation with Distribution Matching", "heading_level": null, "page_id": 63, "polygon": [[112.59445843828715, 614.0676758682101], [457.88413098236776, 614.0676758682101], [457.88413098236776, 626.9775390625], [112.59445843828715, 626.9775390625]]}, {"title": "5.2.3 Training Algorithm", "heading_level": null, "page_id": 65, "polygon": [[111.75, 57.73285841495993], [270.9773299748111, 57.73285841495993], [270.9773299748111, 71.639892578125], [111.75, 71.639892578125]]}, {"title": "5.2.4 Discussion", "heading_level": null, "page_id": 65, "polygon": [[112.33203125, 517.3463935886019], [225.93954659949623, 517.3463935886019], [225.93954659949623, 531.5947265625], [112.33203125, 531.5947265625]]}, {"title": "5.3 Experiments", "heading_level": null, "page_id": 66, "polygon": [[112.59445843828715, 625.3143365983972], [244.70528967254407, 625.3143365983972], [244.70528967254407, 640.9560546875], [112.59445843828715, 640.9560546875]]}, {"title": "5.3.1 Experimental Settings", "heading_level": null, "page_id": 66, "polygon": [[112.55029296875, 663.552983081033], [292.74559193954656, 663.552983081033], [292.74559193954656, 677.1357421875], [112.55029296875, 677.1357421875]]}, {"title": "5.3.2 <PERSON>mp<PERSON><PERSON> to State of the Art", "heading_level": null, "page_id": 68, "polygon": [[112.33203125, 118.46482635796971], [345.2896725440806, 118.46482635796971], [345.2896725440806, 131.870849609375], [112.33203125, 131.870849609375]]}, {"title": "5.3.3 Cross-architecture Generalization", "heading_level": null, "page_id": 73, "polygon": [[112.59445843828715, 567.5814781834372], [364.05541561712846, 567.5814781834372], [364.05541561712846, 581.341796875], [112.59445843828715, 581.341796875]]}, {"title": "5.3.4 Ablation Study on Network Distribution", "heading_level": null, "page_id": 74, "polygon": [[112.33203125, 368.89047195013353], [397.0831234256927, 368.89047195013353], [397.0831234256927, 381.9423828125], [112.33203125, 381.9423828125]]}, {"title": "5.3.5 Continual Learning", "heading_level": null, "page_id": 74, "polygon": [[112.33203125, 648.5574354407836], [273.2292191435768, 648.5574354407836], [273.2292191435768, 661.923828125], [112.33203125, 661.923828125]]}, {"title": "5.3.6 Neural Architecture Search", "heading_level": null, "page_id": 76, "polygon": [[112.59445843828715, 207.68833481745324], [322.02015113350126, 207.68833481745324], [322.02015113350126, 220.57275390625], [112.59445843828715, 220.57275390625]]}, {"title": "5.4 Conclusion and Limitation", "heading_level": null, "page_id": 77, "polygon": [[112.59445843828715, 122.96349065004452], [346.0403022670025, 122.96349065004452], [346.0403022670025, 139.682373046875], [112.59445843828715, 139.682373046875]]}, {"title": "Chapter 6", "heading_level": null, "page_id": 78, "polygon": [[266.279296875, 140.20837043633125], [371.5617128463476, 140.20837043633125], [371.5617128463476, 163.939208984375], [266.279296875, 163.939208984375]]}, {"title": "Conclusion and Future Work", "heading_level": null, "page_id": 78, "polygon": [[165.88916876574308, 200.1905609973286], [472.1460957178841, 200.1905609973286], [472.1460957178841, 223.2451171875], [165.88916876574308, 223.2451171875]]}, {"title": "6.1 Impact", "heading_level": null, "page_id": 79, "polygon": [[111.6044921875, 56.23330365093499], [202.67002518891687, 56.23330365093499], [202.67002518891687, 71.9482421875], [111.6044921875, 71.9482421875]]}, {"title": "6.2 Limitations", "heading_level": null, "page_id": 80, "polygon": [[111.6044921875, 56.23330365093499], [234.19647355163727, 56.23330365093499], [234.19647355163727, 71.639892578125], [111.6044921875, 71.639892578125]]}, {"title": "6.3 Future Work", "heading_level": null, "page_id": 81, "polygon": [[111.458984375, 56.98308103294746], [242.4534005037783, 56.98308103294746], [242.4534005037783, 71.74267578125], [111.458984375, 71.74267578125]]}, {"title": "6.3.1 Large-scale and Complex Tasks", "heading_level": null, "page_id": 81, "polygon": [[112.40478515625, 94.47195013357079], [352.04534005037783, 94.47195013357079], [352.04534005037783, 107.716796875], [112.40478515625, 107.716796875]]}, {"title": "6.3.2 More Applications", "heading_level": null, "page_id": 81, "polygon": [[112.59445843828715, 431.87177203918077], [268.7254408060453, 431.87177203918077], [268.7254408060453, 445.2568359375], [112.59445843828715, 445.2568359375]]}, {"title": "6.3.3 Dataset Condensation with Generative Models", "heading_level": null, "page_id": 82, "polygon": [[112.1865234375, 98.97061442564559], [441.3702770780856, 98.97061442564559], [441.3702770780856, 112.2392578125], [112.1865234375, 112.2392578125]]}, {"title": "6.3.4 Joint Use of Real and Synthetic Data", "heading_level": null, "page_id": 82, "polygon": [[112.4775390625, 398.88156723063224], [381.3198992443325, 398.88156723063224], [381.3198992443325, 412.16064453125], [112.4775390625, 412.16064453125]]}, {"title": "Appendix A", "heading_level": null, "page_id": 83, "polygon": [[255.21410579345087, 140.20837043633125], [382.0705289672544, 140.20837043633125], [382.0705289672544, 163.528076171875], [255.21410579345087, 163.528076171875]]}, {"title": "Appendix: Dataset Condensation with\nGradient Matching", "heading_level": null, "page_id": 83, "polygon": [[115.3876953125, 200.1905609973286], [522.08203125, 200.1905609973286], [522.08203125, 262.302734375], [115.3876953125, 262.302734375]]}, {"title": "A.1 Implementation Details", "heading_level": null, "page_id": 83, "polygon": [[112.59445843828715, 314.15672306322347], [324.272040302267, 314.15672306322347], [324.272040302267, 329.728515625], [112.59445843828715, 329.728515625]]}, {"title": "A.2 Further Analysis", "heading_level": null, "page_id": 86, "polygon": [[112.40478515625, 547.3374888691006], [276.23173803526447, 547.3374888691006], [276.23173803526447, 563.6630859375], [112.40478515625, 563.6630859375]]}, {"title": "A.3 Comparison to More Baselines", "heading_level": null, "page_id": 90, "polygon": [[112.59445843828715, 161.9519145146928], [382.0705289672544, 161.9519145146928], [382.0705289672544, 176.684326171875], [112.59445843828715, 176.684326171875]]}, {"title": "A.4 Further Comparison to Dataset Distillation", "heading_level": null, "page_id": 93, "polygon": [[112.4775390625, 368.89047195013353], [466.8916876574307, 368.89047195013353], [466.8916876574307, 384.4091796875], [112.4775390625, 384.4091796875]]}, {"title": "A.5 Extended Related Work", "heading_level": null, "page_id": 94, "polygon": [[112.59445843828715, 606.4208984375], [327.27455919395464, 606.4208984375], [327.27455919395464, 621.2216796875], [112.59445843828715, 621.2216796875]]}, {"title": "Appendix B", "heading_level": null, "page_id": 97, "polygon": [[255.21410579345087, 140.20837043633125], [381.3198992443325, 140.20837043633125], [381.3198992443325, 163.83642578125], [255.21410579345087, 163.83642578125]]}, {"title": "Appendix: Dataset Condensation with\nDistribution Matching", "heading_level": null, "page_id": 97, "polygon": [[115.533203125, 200.1905609973286], [521.791015625, 200.1905609973286], [521.791015625, 262.7138671875], [115.533203125, 262.7138671875]]}, {"title": "B.1 Implementation details", "heading_level": null, "page_id": 97, "polygon": [[112.59445843828715, 314.15672306322347], [322.02015113350126, 314.15672306322347], [322.02015113350126, 329.728515625], [112.59445843828715, 329.728515625]]}, {"title": "B.2 Comparison to More Baselines and Related Works", "heading_level": null, "page_id": 98, "polygon": [[112.59445843828715, 521.8450578806768], [525.44080604534, 521.8450578806768], [525.44080604534, 537.76171875], [112.59445843828715, 537.76171875]]}, {"title": "B.3 Extended Visualization and Analysis", "heading_level": null, "page_id": 100, "polygon": [[112.4775390625, 354.64470169189667], [423.3551637279597, 354.64470169189667], [423.3551637279597, 369.81396484375], [112.4775390625, 369.81396484375]]}, {"title": "B.4 Connection to Gradient Matching", "heading_level": null, "page_id": 100, "polygon": [[112.1865234375, 644.8085485307213], [398.5843828715365, 644.8085485307213], [398.5843828715365, 661.1015625], [112.1865234375, 661.1015625]]}, {"title": "Bibliography", "heading_level": null, "page_id": 103, "polygon": [[250.71032745591938, 140.20837043633125], [387.6328125, 140.20837043633125], [387.6328125, 163.630859375], [250.71032745591938, 163.630859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 16], ["ListItem", 5], ["Picture", 1], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 16370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 8], ["Text", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 36], ["Text", 4], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 30], ["Line", 14], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 31], ["Text", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 6], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 192], ["Span", 181], ["Line", 26], ["SectionHeader", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 272], ["Span", 249], ["Line", 35], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9871, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["TableCell", 120], ["Line", 21], ["<PERSON>Footer", 2], ["TableOfContents", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14247, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 28], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 39], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 40], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 33], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 20], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 33], ["SectionHeader", 3], ["TextInlineMath", 3], ["Text", 2], ["Reference", 2], ["ListItem", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 39], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 76], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1154, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 60], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 32], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 33], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 24], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 755, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 45], ["TextInlineMath", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Equation", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 70], ["Reference", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 67], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Reference", 2], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1143, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 440], ["Line", 64], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 491], ["Line", 38], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["Equation", 1], ["ListItem", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 41], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Reference", 2], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["TableCell", 227], ["Line", 38], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["TableCell", 77], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["TableCell", 75], ["Line", 36], ["Text", 3], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 65], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1036, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 873, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 27], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Text", 3], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 24], ["SectionHeader", 3], ["Reference", 2], ["Text", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 43], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 52], ["Reference", 4], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1030, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 48], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Equation", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 36], ["Text", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 34], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["TableCell", 275], ["Line", 44], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4413, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 34], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 105], ["TableCell", 43], ["Caption", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TableGroup", 2], ["Picture", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 4215, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 34], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["TableCell", 68], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["TableCell", 35], ["Line", 31], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 62], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["TableCell", 50], ["Line", 38], ["Reference", 4], ["Caption", 2], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2141, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 36], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Equation", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 28], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 51], ["Line", 24], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 642, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 60], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2150, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 65], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 56], ["TableCell", 34], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Table", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 33], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 36], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 34], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["TableCell", 167], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["TableCell", 107], ["Line", 34], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9255, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 42], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 33], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["TableCell", 59], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1367, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["TableCell", 60], ["Line", 35], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 65], ["Line", 20], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 27], ["Text", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 34], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 29], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 33], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 32], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 25], ["SectionHeader", 3], ["Reference", 2], ["Text", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["TableCell", 39], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1173, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 62], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1060, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["TableCell", 44], ["Line", 32], ["Text", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4378, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["TableCell", 72], ["Line", 35], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["TableCell", 50], ["Line", 35], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3082, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 52], ["TableCell", 50], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["Text", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2313, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["TableCell", 44], ["Line", 31], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2503, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 80], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Text", 1], ["Caption", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1311, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["TableCell", 91], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6928, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["TableCell", 97], ["Line", 37], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["TableCell", 60], ["Line", 33], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3016, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 17], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 62], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 25], ["SectionHeader", 3], ["Text", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 58], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 34], ["TableCell", 16], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 32], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 67], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10295, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 67], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 25], ["Reference", 10], ["ListItem", 9], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 29], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 31], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 31], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 31], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 31], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["Line", 29], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 30], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 30], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 16], ["ListItem", 7], ["Reference", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Data-Efficient Neural Network Training with Dataset Condensation"}