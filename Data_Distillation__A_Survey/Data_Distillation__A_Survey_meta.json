{"table_of_contents": [{"title": "Data Distillation: A Survey", "heading_level": null, "page_id": 0, "polygon": [[70.5, 80.25], [287.25, 81.017578125], [287.25, 97.646484375], [70.5, 97.646484375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.0, 252.75], [330.0, 252.75], [330.0, 264.322265625], [282.0, 264.322265625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[69.440185546875, 433.5], [159.0, 433.5], [159.0, 445.11328125], [69.440185546875, 445.11328125]]}, {"title": "2 The Data Distillation Framework", "heading_level": null, "page_id": 2, "polygon": [[70.5, 170.9296875], [272.3818359375, 170.9296875], [272.3818359375, 182.14453125], [70.5, 182.14453125]]}, {"title": "2.1 Data Distillation by Meta-model Matching", "heading_level": null, "page_id": 3, "polygon": [[70.5, 360.0], [291.75, 360.0], [291.75, 370.283203125], [70.5, 370.283203125]]}, {"title": "2.2 Data Distillation by Gradient Matching", "heading_level": null, "page_id": 4, "polygon": [[70.5, 664.5], [276.71484375, 664.5], [276.71484375, 674.82421875], [70.5, 674.82421875]]}, {"title": "2.3 Data Distillation by Trajectory Matching", "heading_level": null, "page_id": 5, "polygon": [[70.5, 581.25], [283.5, 581.25], [283.5, 591.29296875], [70.5, 591.29296875]]}, {"title": "2.4 Data Distillation by Distribution Matching", "heading_level": null, "page_id": 6, "polygon": [[70.5, 433.5], [291.0, 433.5], [291.0, 443.56640625], [70.5, 443.56640625]]}, {"title": "2.5 Data Distillation by Factorization", "heading_level": null, "page_id": 7, "polygon": [[70.5, 231.0], [249.0, 231.0], [249.0, 241.119140625], [70.5, 241.119140625]]}, {"title": "3 Data Modalities", "heading_level": null, "page_id": 9, "polygon": [[70.5, 447.75], [180.0, 447.75], [180.0, 458.6484375], [70.5, 458.6484375]]}, {"title": "4 Applications", "heading_level": null, "page_id": 11, "polygon": [[70.5, 429.2578125], [159.275390625, 429.2578125], [159.275390625, 441.6328125], [70.5, 441.6328125]]}, {"title": "5 Challenges & Future Directions", "heading_level": null, "page_id": 12, "polygon": [[70.5, 325.23046875], [265.060546875, 325.23046875], [265.060546875, 337.21875], [70.5, 337.21875]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 13, "polygon": [[70.5, 279.75], [170.4814453125, 279.75], [170.4814453125, 290.8125], [70.5, 290.8125]]}, {"title": "References", "heading_level": null, "page_id": 13, "polygon": [[70.5, 344.25], [131.25, 344.25], [131.25, 355.201171875], [70.5, 355.201171875]]}, {"title": "A Notation", "heading_level": null, "page_id": 20, "polygon": [[69.75, 80.25], [143.25, 80.25], [143.25, 94.50439453125], [69.75, 94.50439453125]]}, {"title": "Dataset related", "heading_level": null, "page_id": 20, "polygon": [[262.5205078125, 114.0], [345.75, 114.0], [345.75, 125.25], [262.5205078125, 125.25]]}, {"title": "Learning related", "heading_level": null, "page_id": 20, "polygon": [[261.75, 361.5], [348.75, 361.5], [348.75, 373.5], [261.75, 373.5]]}, {"title": "General", "heading_level": null, "page_id": 20, "polygon": [[284.25, 464.25], [327.0, 464.25], [327.0, 476.25], [284.25, 476.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 46], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5447, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 52], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 701], ["Line", 55], ["TextInlineMath", 7], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 74], ["TextInlineMath", 4], ["Reference", 4], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 851, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 804], ["Line", 85], ["TextInlineMath", 4], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableCell", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2468, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 822], ["Line", 129], ["TextInlineMath", 5], ["Equation", 4], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 576], ["Line", 92], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 117], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 2169], ["Line", 449], ["TableCell", 336], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 60], ["TextInlineMath", 4], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 542], ["Line", 87], ["Text", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 50], ["TextInlineMath", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 49], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 45], ["ListItem", 12], ["Reference", 12], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 47], ["Reference", 20], ["ListItem", 18], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 48], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 49], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 48], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["TableCell", 62], ["Line", 56], ["SectionHeader", 4], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5701, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Data_Distillation__A_Survey"}