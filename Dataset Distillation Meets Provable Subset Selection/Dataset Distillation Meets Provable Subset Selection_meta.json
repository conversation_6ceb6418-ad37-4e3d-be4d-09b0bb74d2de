{"table_of_contents": [{"title": "Dataset Distillation Meets Provable Subset Selection", "heading_level": null, "page_id": 0, "polygon": [[112.5, 99.0], [498.0, 99.0], [498.0, 116.3056640625], [112.5, 116.3056640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 230.25], [328.5, 230.25], [328.5, 241.69921875], [282.75, 241.69921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 492.0], [191.25, 492.0], [191.25, 503.89453125], [107.25, 503.89453125]]}, {"title": "2 Key ideas", "heading_level": null, "page_id": 2, "polygon": [[107.25, 315.5625], [176.90625, 315.5625], [176.90625, 326.77734375], [107.25, 326.77734375]]}, {"title": "2.1 Provable initialization", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 375.0], [225.9140625, 375.0], [225.9140625, 384.978515625], [106.98046875, 384.978515625]]}, {"title": "2.2 Data distillation meets subset selection", "heading_level": null, "page_id": 3, "polygon": [[106.5, 73.5], [294.75, 73.5], [294.75, 83.38623046875], [106.5, 83.38623046875]]}, {"title": "2.3 Our contribution", "heading_level": null, "page_id": 3, "polygon": [[107.1298828125, 232.5], [204.0, 232.5], [204.0, 242.47265625], [107.1298828125, 242.47265625]]}, {"title": "3 Method", "heading_level": null, "page_id": 3, "polygon": [[106.5, 565.5], [166.5, 565.5], [166.5, 577.37109375], [106.5, 577.37109375]]}, {"title": "3.1 Smart initialization via Gaussian kernel coresets for NTK-based distillation methods", "heading_level": null, "page_id": 4, "polygon": [[107.25, 180.0], [489.75, 180.0], [489.75, 189.87890625], [107.25, 189.87890625]]}, {"title": "3.2 Smart initialization via K-means coresets for NNLMDT distillation methods", "heading_level": null, "page_id": 5, "polygon": [[107.25, 74.25], [456.01171875, 74.25], [456.01171875, 83.4345703125], [107.25, 83.4345703125]]}, {"title": "3.3 Provable initializer: Overview of Algorithm 1", "heading_level": null, "page_id": 5, "polygon": [[107.05517578125, 351.0], [324.75, 351.0], [324.75, 361.001953125], [107.05517578125, 361.001953125]]}, {"title": "3.4 Subset selection policy for better distillation", "heading_level": null, "page_id": 6, "polygon": [[106.5, 504.0], [316.7578125, 504.66796875], [316.7578125, 513.94921875], [106.5, 513.94921875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 7, "polygon": [[107.25, 606.375], [192.0, 606.375], [192.0, 617.9765625], [107.25, 617.9765625]]}, {"title": "5 Conclusions and future work", "heading_level": null, "page_id": 9, "polygon": [[106.30810546875, 72.0], [275.25, 72.0], [275.25, 83.57958984375], [106.30810546875, 83.57958984375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 201.75], [165.75, 201.75], [165.75, 212.6953125], [107.25, 212.6953125]]}, {"title": "A Coreset tools", "heading_level": null, "page_id": 13, "polygon": [[106.5, 72.0], [195.75, 72.0], [195.75, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "B Random Fourier features", "heading_level": null, "page_id": 13, "polygon": [[106.5, 396.0], [258.75, 396.0], [258.75, 407.6015625], [106.5, 407.6015625]]}, {"title": "C Proof of our technical results", "heading_level": null, "page_id": 14, "polygon": [[106.30810546875, 252.75], [278.5078125, 252.75], [278.5078125, 263.7421875], [106.30810546875, 263.7421875]]}, {"title": "C.1 Proof of Theorem 3", "heading_level": null, "page_id": 14, "polygon": [[106.5, 276.0], [216.7998046875, 276.0], [216.7998046875, 285.978515625], [106.5, 285.978515625]]}, {"title": "C.2 Proof of Theorem 5", "heading_level": null, "page_id": 15, "polygon": [[107.25, 474.75], [217.845703125, 474.75], [217.845703125, 484.55859375], [107.25, 484.55859375]]}, {"title": "D Extensions", "heading_level": null, "page_id": 16, "polygon": [[106.5, 129.0], [184.5, 129.0], [184.5, 140.572265625], [106.5, 140.572265625]]}, {"title": "E Limitations", "heading_level": null, "page_id": 16, "polygon": [[106.5, 395.033203125], [187.5, 395.033203125], [187.5, 405.66796875], [106.5, 405.66796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 53], ["Text", 7], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4911, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 72], ["Text", 5], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 41], ["Text", 7], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 51], ["ListItem", 5], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 56], ["TextInlineMath", 6], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 932], ["Line", 59], ["TextInlineMath", 6], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 963], ["Line", 119], ["TextInlineMath", 15], ["Reference", 8], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 719], ["Line", 54], ["Text", 6], ["TextInlineMath", 6], ["Reference", 6], ["TableCell", 3], ["Table", 1], ["Code", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2144, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 984], ["TableCell", 99], ["Line", 81], ["Text", 3], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1064, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 48], ["ListItem", 12], ["Reference", 11], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 50], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 23], ["ListItem", 10], ["Reference", 10], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 997], ["Line", 47], ["TextInlineMath", 7], ["Reference", 6], ["Text", 5], ["Equation", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1272], ["Line", 140], ["Reference", 8], ["TextInlineMath", 6], ["Text", 5], ["Equation", 4], ["SectionHeader", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 781], ["Line", 106], ["Text", 9], ["TextInlineMath", 6], ["Equation", 5], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 45], ["TableCell", 25], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3912, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset Distillation Meets Provable Subset Selection"}