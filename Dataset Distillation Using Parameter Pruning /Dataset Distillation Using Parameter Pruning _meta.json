{"table_of_contents": [{"title": "Dataset Distillation Using Parameter Pruning", "heading_level": null, "page_id": 0, "polygon": [[82.5, 58.5], [527.25, 58.5], [527.25, 81.16259765625], [82.5, 81.16259765625]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 252.0], [214.1103515625, 252.0], [214.1103515625, 260.841796875], [133.5, 260.841796875]]}, {"title": "II. METHODOLOGY", "heading_level": null, "page_id": 0, "polygon": [[393.0, 633.75], [480.0, 633.75], [480.0, 642.7265625], [393.0, 642.7265625]]}, {"title": "<PERSON><PERSON> Teacher-Student Architecture Training", "heading_level": null, "page_id": 0, "polygon": [[309.0, 711.0], [480.75, 711.0], [480.75, 720.45703125], [309.0, 720.45703125]]}, {"title": "B. Teacher-Student Parameter Matching", "heading_level": null, "page_id": 1, "polygon": [[48.0, 536.25], [214.5, 536.25], [214.5, 545.66015625], [48.0, 545.66015625]]}, {"title": "Algorithm 1 Dataset Distillation Using Parameter Pruning", "heading_level": null, "page_id": 1, "polygon": [[311.25, 276.75], [554.25, 276.75], [554.25, 287.71875], [311.25, 287.71875]]}, {"title": "C. Optimized Distilled Dataset Generation", "heading_level": null, "page_id": 2, "polygon": [[48.0, 477.0], [225.75, 477.75], [225.75, 487.5], [48.0, 486.4921875]]}, {"title": "III. EXPERIMENTS", "heading_level": null, "page_id": 2, "polygon": [[132.0, 648.0], [215.25, 648.0], [215.25, 657.421875], [132.0, 657.421875]]}, {"title": "<PERSON><PERSON> Settings", "heading_level": null, "page_id": 2, "polygon": [[47.25, 663.75], [150.908203125, 663.75], [150.908203125, 673.27734375], [47.25, 673.27734375]]}, {"title": "<PERSON><PERSON> Comparison", "heading_level": null, "page_id": 2, "polygon": [[310.5, 615.0], [424.5, 615.0], [424.5, 624.9375], [310.5, 624.9375]]}, {"title": "<PERSON><PERSON>-Architecture Generalization", "heading_level": null, "page_id": 3, "polygon": [[310.78125, 219.0], [465.0, 219.0], [465.0, 228.55078125], [310.78125, 228.55078125]]}, {"title": "IV. CONCLUSION", "heading_level": null, "page_id": 3, "polygon": [[398.0390625, 502.5], [475.5, 502.5], [475.5, 512.015625], [398.0390625, 512.015625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 3, "polygon": [[408.75, 631.5], [465.0, 631.5], [465.0, 640.40625], [408.75, 640.40625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 103], ["Text", 11], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["ListItem", 2], ["Footnote", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3364, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 959], ["Line", 117], ["ListItem", 15], ["Equation", 10], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["SectionHeader", 2], ["ListGroup", 2], ["Reference", 2], ["Caption", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1287, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 611], ["Line", 115], ["Text", 5], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 110], ["TableCell", 102], ["Text", 7], ["Reference", 7], ["Caption", 4], ["ListItem", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 9105, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 57], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset Distillation Using Parameter Pruning "}