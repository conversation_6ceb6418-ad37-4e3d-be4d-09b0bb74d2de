{"table_of_contents": [{"title": "Dataset Distillation via Vision-Language Category Prototype", "heading_level": null, "page_id": 0, "polygon": [[117.8876953125, 105.75], [492.0, 105.75], [492.0, 119.109375], [117.8876953125, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[152.77587890625, 201.0], [200.25, 201.0], [200.25, 211.921875], [152.77587890625, 211.921875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[57.0, 573.75], [136.5, 573.75], [136.5, 586.265625], [57.0, 586.265625]]}, {"title": "2. Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[315.75, 334.5], [398.25, 334.5], [398.25, 345.533203125], [315.75, 345.533203125]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[57.0, 427.5], [111.0, 427.5], [111.0, 438.5390625], [57.0, 438.5390625]]}, {"title": "3.1. Paired Description Generation", "heading_level": null, "page_id": 2, "polygon": [[57.0, 541.40625], [222.0, 541.40625], [222.0, 551.4609375], [57.0, 551.4609375]]}, {"title": "3.2. Outlier Removal", "heading_level": null, "page_id": 2, "polygon": [[315.75, 420.0], [415.5, 420.0], [415.5, 430.41796875], [315.75, 430.41796875]]}, {"title": "3.3. Cross-Modal Information Distillation", "heading_level": null, "page_id": 2, "polygon": [[315.75, 549.75], [513.0, 549.75], [513.0, 561.515625], [315.75, 561.515625]]}, {"title": "3.3.1. Image Prototypes", "heading_level": null, "page_id": 2, "polygon": [[314.96484375, 568.5], [417.75, 568.5], [417.75, 578.91796875], [314.96484375, 578.91796875]]}, {"title": "3.3.2. Text Prototypes", "heading_level": null, "page_id": 3, "polygon": [[57.0, 388.845703125], [151.43115234375, 388.845703125], [151.43115234375, 398.70703125], [57.0, 398.70703125]]}, {"title": "Algorithm 2 Generate text prototype for each cluster", "heading_level": null, "page_id": 3, "polygon": [[316.5, 73.5], [530.25, 73.5], [530.25, 83.724609375], [316.5, 83.724609375]]}, {"title": "3.4. Image Synthesis via LDM", "heading_level": null, "page_id": 3, "polygon": [[315.0, 414.75], [458.40234375, 414.75], [458.40234375, 425.390625], [315.0, 425.390625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 3, "polygon": [[315.75, 594.75], [395.25, 594.75], [395.25, 604.828125], [315.75, 604.828125]]}, {"title": "4.1. Datasets", "heading_level": null, "page_id": 3, "polygon": [[315.75, 614.25], [376.5, 614.25], [376.5, 624.1640625], [315.75, 624.1640625]]}, {"title": "4.2. Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[57.0, 383.25], [188.26171875, 383.25], [188.26171875, 393.486328125], [57.0, 393.486328125]]}, {"title": "4.3. <PERSON><PERSON><PERSON><PERSON> with the SOTA Methods", "heading_level": null, "page_id": 4, "polygon": [[57.0, 565.5], [251.314453125, 565.5], [251.314453125, 575.05078125], [57.0, 575.05078125]]}, {"title": "4.4. Ablation Study", "heading_level": null, "page_id": 5, "polygon": [[57.0, 601.5], [149.25, 601.5], [149.25, 611.40234375], [57.0, 611.40234375]]}, {"title": "4.4.1. Text prototype", "heading_level": null, "page_id": 5, "polygon": [[315.75, 483.0], [404.25, 483.0], [404.25, 493.06640625], [315.75, 493.06640625]]}, {"title": "4.5. Visualization", "heading_level": null, "page_id": 6, "polygon": [[57.0, 514.3359375], [138.8056640625, 514.3359375], [138.8056640625, 524.390625], [57.0, 524.390625]]}, {"title": "4.6. Parameter Analysis", "heading_level": null, "page_id": 6, "polygon": [[315.75, 575.82421875], [429.416015625, 575.82421875], [429.416015625, 586.65234375], [315.75, 586.65234375]]}, {"title": "5. Conclusion and Future Work", "heading_level": null, "page_id": 7, "polygon": [[57.0, 656.25], [220.5, 656.25], [220.5, 667.86328125], [57.0, 667.86328125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[57.0, 72.0], [114.75, 72.0], [114.75, 83.57958984375], [57.0, 83.57958984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 76], ["Text", 6], ["SectionHeader", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 108], ["Text", 7], ["ListItem", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 133], ["Text", 5], ["SectionHeader", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1119, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 822], ["Line", 106], ["ListItem", 27], ["Text", 7], ["Reference", 6], ["SectionHeader", 5], ["Equation", 5], ["TextInlineMath", 4], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["TableCell", 182], ["Line", 85], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["TableCell", 255], ["Line", 99], ["Text", 8], ["Reference", 5], ["Table", 4], ["Caption", 4], ["TableGroup", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 17907, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 133], ["TableCell", 50], ["Text", 5], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8304, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 92], ["Text", 4], ["Caption", 2], ["Reference", 2], ["Picture", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 115], ["ListItem", 28], ["Reference", 27], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 86], ["ListItem", 20], ["Reference", 20], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset Distillation via Vision-Language Category Prototype"}