{"table_of_contents": [{"title": "Dataset condensation with latent quantile matching", "heading_level": null, "page_id": 0, "polygon": [[138.0, 106.5], [455.4140625, 106.5], [455.4140625, 119.3994140625], [138.0, 119.3994140625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 243.0], [191.25, 243.0], [191.25, 253.880859375], [144.75, 253.880859375]]}, {"title": "1. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.75, 538.5], [157.5, 538.5], [157.5, 550.6875], [48.75, 550.6875]]}, {"title": "2. RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[307.5, 622.5], [426.75, 622.5], [426.75, 634.21875], [307.5, 634.21875]]}, {"title": "3. PRELIMINARIES", "heading_level": null, "page_id": 2, "polygon": [[307.5, 348.0], [418.95703125, 348.0], [418.95703125, 359.26171875], [307.5, 359.26171875]]}, {"title": "4. METHODOLOGY", "heading_level": null, "page_id": 3, "polygon": [[48.75, 430.5], [161.25, 430.5], [161.25, 442.01953125], [48.75, 442.01953125]]}, {"title": "4.1. Shortcomings of MMD", "heading_level": null, "page_id": 3, "polygon": [[48.75, 553.5], [178.400390625, 553.5], [178.400390625, 563.8359375], [48.75, 563.8359375]]}, {"title": "4.2. Proposed Method", "heading_level": null, "page_id": 3, "polygon": [[307.5, 310.5], [411.78515625, 310.5], [411.78515625, 320.396484375], [307.5, 320.396484375]]}, {"title": "5. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[307.5, 381.75], [408.75, 381.75], [408.75, 393.099609375], [307.5, 393.099609375]]}, {"title": "6. RESULTS", "heading_level": null, "page_id": 5, "polygon": [[307.5, 278.25], [374.25, 278.25], [374.25, 289.072265625], [307.5, 289.072265625]]}, {"title": "6.1. Image datasets", "heading_level": null, "page_id": 5, "polygon": [[307.5, 297.75], [398.337890625, 297.75], [398.337890625, 308.21484375], [307.5, 308.21484375]]}, {"title": "6.2. Graph datasets", "heading_level": null, "page_id": 6, "polygon": [[48.0, 564.609375], [141.75, 564.609375], [141.75, 575.4375], [48.0, 575.4375]]}, {"title": "7. CONCLUSION", "heading_level": null, "page_id": 7, "polygon": [[48.0, 537.5390625], [143.736328125, 537.5390625], [143.736328125, 549.9140625], [48.0, 549.9140625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 7, "polygon": [[307.5, 655.5], [408.0, 655.5], [408.0, 667.08984375], [307.5, 667.08984375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 84.06298828125], [48.75, 84.06298828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 85], ["Text", 10], ["SectionHeader", 3], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4275, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 110], ["Text", 5], ["ListItem", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 108], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 523], ["Line", 121], ["Text", 8], ["Equation", 3], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 666], ["Line", 90], ["Text", 8], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 143], ["TableCell", 102], ["Text", 8], ["Caption", 3], ["Table", 2], ["Equation", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["TableCell", 169], ["Line", 94], ["Text", 7], ["Table", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 85], ["TableCell", 74], ["Text", 6], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 114], ["ListItem", 33], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 218], ["Line", 55], ["ListItem", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset condensation with latent quantile matching"}