{"table_of_contents": [{"title": "Dataset Condensation Driven Machine Unlearning", "heading_level": null, "page_id": 0, "polygon": [[57.375, 58.5], [553.4296875, 58.5], [553.4296875, 81.59765625], [57.375, 81.59765625]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 446.25], [213.75, 446.25], [213.75, 456.328125], [133.5, 456.328125]]}, {"title": "II. RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[127.5, 445.5], [220.5, 445.5], [220.5, 455.16796875], [127.5, 455.16796875]]}, {"title": "III. PRELIMINARIES AND NOTATION", "heading_level": null, "page_id": 1, "polygon": [[357.099609375, 697.5], [516.076171875, 697.5], [516.076171875, 707.30859375], [357.099609375, 707.30859375]]}, {"title": "Algorithm 2: Dataset Condensation via Fast Distribu-\ntion Matching", "heading_level": null, "page_id": 2, "polygon": [[51.47314453125, 199.5], [274.5, 199.5], [274.5, 221.203125], [51.47314453125, 221.203125]]}, {"title": "IV. METHODOLOGY", "heading_level": null, "page_id": 2, "polygon": [[129.75, 568.86328125], [218.25, 568.86328125], [218.25, 577.5], [129.75, 577.5]]}, {"title": "A. Retain Dataset Reduction Framework", "heading_level": null, "page_id": 2, "polygon": [[47.25, 698.25], [216.75, 698.25], [216.75, 708.08203125], [47.25, 708.08203125]]}, {"title": "B. Online Phase", "heading_level": null, "page_id": 3, "polygon": [[47.25, 228.0], [117.75, 228.0], [117.75, 237.83203125], [47.25, 237.83203125]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 3, "polygon": [[48.0, 650.25], [136.5, 650.25], [136.5, 660.90234375], [48.0, 660.90234375]]}, {"title": "D. Instrumentation of Unlearning", "heading_level": null, "page_id": 3, "polygon": [[310.5, 686.25], [450.75, 686.25], [450.75, 695.3203125], [310.5, 695.3203125]]}, {"title": "E. Applications of Unlearning", "heading_level": null, "page_id": 4, "polygon": [[48.0, 302.25], [174.0, 302.25], [174.0, 312.08203125], [48.0, 312.08203125]]}, {"title": "V. PERFORMANCE EVALUATION", "heading_level": null, "page_id": 4, "polygon": [[368.15625, 282.75], [506.25, 282.75], [506.25, 292.359375], [368.15625, 292.359375]]}, {"title": "<PERSON><PERSON> Settings", "heading_level": null, "page_id": 4, "polygon": [[309.75, 301.5], [414.0, 301.5], [414.0, 311.115234375], [309.75, 311.115234375]]}, {"title": "<PERSON><PERSON> Between Major Unlearning Metrics", "heading_level": null, "page_id": 6, "polygon": [[47.9619140625, 255.75], [245.25, 255.75], [245.25, 264.708984375], [47.9619140625, 264.708984375]]}, {"title": "C. Relationship between Unlearning Metric and Membership\nInference Attack", "heading_level": null, "page_id": 6, "polygon": [[47.25, 501.75], [300.75, 501.75], [300.75, 523.23046875], [47.25, 523.23046875]]}, {"title": "<PERSON><PERSON> to Differential Privacy", "heading_level": null, "page_id": 6, "polygon": [[47.25, 663.0], [205.5, 663.0], [205.5, 672.890625], [47.25, 672.890625]]}, {"title": "<PERSON><PERSON>learn<PERSON> in Dataset Condensation", "heading_level": null, "page_id": 6, "polygon": [[310.5, 118.5], [477.0, 118.5], [477.0, 127.9072265625], [310.5, 127.9072265625]]}, {"title": "VI. CONCLUSION", "heading_level": null, "page_id": 6, "polygon": [[398.25, 288.0], [477.0, 288.0], [477.0, 297.966796875], [398.25, 297.966796875]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 6, "polygon": [[390.75, 422.25], [483.75, 422.25], [483.75, 431.578125], [390.75, 431.578125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 6, "polygon": [[408.498046875, 508.5], [465.0, 508.5], [465.0, 517.4296875], [408.498046875, 517.4296875]]}, {"title": "APPENDIX", "heading_level": null, "page_id": 7, "polygon": [[413.25, 555.0], [462.287109375, 555.0], [462.287109375, 564.22265625], [413.25, 564.22265625]]}, {"title": "A. Relation between Original and Unlearned Parameters", "heading_level": null, "page_id": 8, "polygon": [[46.5, 108.75], [285.0, 108.75], [285.0, 118.142578125], [46.5, 118.142578125]]}, {"title": "Remark:", "heading_level": null, "page_id": 8, "polygon": [[56.85205078125, 568.5], [97.5, 568.5], [97.5, 579.3046875], [56.85205078125, 579.3046875]]}, {"title": "B. Persistance of Loss of Unlearned Model on Retain Dataset", "heading_level": null, "page_id": 8, "polygon": [[310.5, 514.5], [563.25, 514.5], [563.25, 524.390625], [310.5, 524.390625]]}, {"title": "C. Case-1: Perturbations in <PERSON><PERSON> Layer's Parameters", "heading_level": null, "page_id": 10, "polygon": [[48.0, 478.5], [282.990234375, 478.5], [282.990234375, 488.25], [48.0, 488.25]]}, {"title": "<PERSON><PERSON> Case-2: Perturbations in <PERSON><PERSON> Layer's Parameters", "heading_level": null, "page_id": 10, "polygon": [[309.5859375, 363.75], [543.0, 363.75], [543.0, 374.537109375], [309.5859375, 374.537109375]]}, {"title": "E. Case-3: Perturbations in Whole Model's Parameters", "heading_level": null, "page_id": 10, "polygon": [[307.79296875, 543.75], [540.0, 543.75], [540.0, 553.78125], [307.79296875, 553.78125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 107], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6333, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 118], ["Text", 6], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 772, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1341], ["Line", 111], ["Text", 7], ["TextInlineMath", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 613], ["Line", 122], ["Text", 9], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["Line", 105], ["Text", 9], ["SectionHeader", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 948, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1208], ["TableCell", 588], ["Line", 77], ["Text", 5], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 113], ["Text", 8], ["ListItem", 8], ["Reference", 8], ["SectionHeader", 7], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 800], ["Line", 146], ["ListItem", 42], ["Reference", 41], ["TextInlineMath", 2], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 1352], ["Line", 152], ["Equation", 13], ["TextInlineMath", 9], ["Text", 8], ["SectionHeader", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1316], ["Line", 201], ["TextInlineMath", 11], ["Equation", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 14151, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 968], ["Line", 138], ["Equation", 10], ["Text", 7], ["SectionHeader", 3], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 11769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 824], ["Line", 146], ["TableCell", 96], ["TextInlineMath", 3], ["Equation", 3], ["Text", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4426, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 649], ["Line", 129], ["Equation", 6], ["TextInlineMath", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 735], ["Line", 156], ["Equation", 7], ["TextInlineMath", 7], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4059, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 100], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1390, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 47], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_Driven_Machine_Unlearning"}