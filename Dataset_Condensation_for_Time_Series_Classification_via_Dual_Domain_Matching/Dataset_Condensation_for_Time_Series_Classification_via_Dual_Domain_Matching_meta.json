{"table_of_contents": [{"title": "Dataset Condensation for Time Series Classification via Dual\nDomain Matching", "heading_level": null, "page_id": 0, "polygon": [[68.25, 81.75], [544.46484375, 81.75], [544.46484375, 119.5927734375], [68.25, 119.5927734375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[52.5, 201.75], [113.4052734375, 201.75], [113.4052734375, 213.662109375], [52.5, 213.662109375]]}, {"title": "CCS CONCEPTS", "heading_level": null, "page_id": 0, "polygon": [[52.5, 499.5], [135.66796875, 499.5], [135.66796875, 510.46875], [52.5, 510.46875]]}, {"title": "KEYWORDS", "heading_level": null, "page_id": 0, "polygon": [[52.5, 535.5], [116.25, 535.5], [116.25, 545.2734375], [52.5, 545.2734375]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[52.5, 564.75], [141.0, 564.75], [141.0, 573.50390625], [52.5, 573.50390625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[317.25, 356.25], [420.75, 356.25], [420.75, 367.576171875], [317.25, 367.576171875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[316.16015625, 84.75], [422.25, 84.75], [422.25, 95.18115234375], [316.16015625, 95.18115234375]]}, {"title": "2.1 Time Series Compression", "heading_level": null, "page_id": 1, "polygon": [[316.5, 101.25], [468.75, 101.25], [468.75, 111.2783203125], [316.5, 111.2783203125]]}, {"title": "2.2 Dataset Condensation", "heading_level": null, "page_id": 1, "polygon": [[316.16015625, 273.0], [452.25, 273.0], [452.25, 283.46484375], [316.16015625, 283.46484375]]}, {"title": "2.3 Frequency-enhanced Time series analysis", "heading_level": null, "page_id": 1, "polygon": [[317.25, 564.99609375], [549.0, 564.99609375], [549.0, 575.05078125], [317.25, 575.05078125]]}, {"title": "Figure 2: The diagram of CondTSC. LPF indicates low pass filter. FTPP indicates Fourier transform phase perturbation and\nFTMP indicates Fourier transform magnitude perturbation.", "heading_level": null, "page_id": 2, "polygon": [[52.5, 306.75], [558.75, 306.75], [558.75, 327.744140625], [52.5, 327.744140625]]}, {"title": "3 PRELIMINARY", "heading_level": null, "page_id": 2, "polygon": [[52.5, 390.75], [146.25, 390.75], [146.25, 401.4140625], [52.5, 401.4140625]]}, {"title": "3.1 Problem Overview", "heading_level": null, "page_id": 2, "polygon": [[52.5, 406.5], [170.25, 406.5], [170.25, 417.65625], [51.75, 417.65625]]}, {"title": "4 METHOD", "heading_level": null, "page_id": 2, "polygon": [[316.16015625, 333.75], [383.25, 333.75], [383.25, 344.373046875], [316.16015625, 344.373046875]]}, {"title": "4.1 Initializing S", "heading_level": null, "page_id": 2, "polygon": [[316.458984375, 505.5], [408.0, 505.5], [408.0, 515.49609375], [316.458984375, 515.49609375]]}, {"title": "4.2 Multi-view Data Augmentation", "heading_level": null, "page_id": 3, "polygon": [[52.5, 272.056640625], [232.5, 272.056640625], [232.5, 281.337890625], [52.5, 281.337890625]]}, {"title": "4.3 Dual Domain Training", "heading_level": null, "page_id": 3, "polygon": [[316.5, 394.5], [455.25, 394.5], [455.25, 405.66796875], [316.5, 405.66796875]]}, {"title": "4.4 Dual Domain Surrogate Objective Matching", "heading_level": null, "page_id": 4, "polygon": [[52.5, 674.25], [294.046875, 674.25], [294.046875, 685.265625], [52.5, 685.265625]]}, {"title": "5 EXPERIMENT", "heading_level": null, "page_id": 5, "polygon": [[52.5, 271.5], [143.138671875, 271.5], [143.138671875, 282.3046875], [52.5, 282.3046875]]}, {"title": "5.1 Datasets", "heading_level": null, "page_id": 5, "polygon": [[52.5, 288.0], [120.75, 288.0], [120.75, 297.7734375], [52.5, 297.7734375]]}, {"title": "5.2 Experiment Setting", "heading_level": null, "page_id": 5, "polygon": [[52.5, 476.25], [174.814453125, 477.75], [174.814453125, 487.5], [52.5, 487.265625]]}, {"title": "5.3 Overall Performance", "heading_level": null, "page_id": 5, "polygon": [[316.5, 188.25], [447.0, 188.25], [447.0, 198.966796875], [316.5, 198.966796875]]}, {"title": "5.4 Downstream Task: NAS", "heading_level": null, "page_id": 6, "polygon": [[52.5, 477.75], [195.75, 476.25], [195.75, 488.0390625], [52.5, 488.0390625]]}, {"title": "5.5 Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[316.5, 620.25], [417.75, 620.25], [417.75, 629.578125], [316.5, 629.578125]]}, {"title": "5.6 Case study", "heading_level": null, "page_id": 7, "polygon": [[52.5, 663.75], [132.0, 663.75], [132.0, 674.4375], [52.5, 674.4375]]}, {"title": "5.7 Different Initialization", "heading_level": null, "page_id": 7, "polygon": [[316.5, 597.0], [455.25, 597.0], [455.25, 608.30859375], [316.5, 608.30859375]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[52.5, 138.75], [143.51220703125, 138.75], [143.51220703125, 149.9501953125], [52.5, 149.9501953125]]}, {"title": "ACKNOWLEDGMENT", "heading_level": null, "page_id": 8, "polygon": [[52.5, 250.5], [166.5, 250.5], [166.5, 260.841796875], [52.5, 260.841796875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[52.5, 384.0], [123.75, 384.0], [123.75, 394.453125], [52.5, 394.453125]]}, {"title": "A SURROGATE OBJECTIVE MATCHING", "heading_level": null, "page_id": 9, "polygon": [[317.25, 180.0], [528.75, 180.0], [528.75, 190.7490234375], [317.25, 190.7490234375]]}, {"title": "A.1 Gradient Matching", "heading_level": null, "page_id": 9, "polygon": [[314.666015625, 291.0], [439.576171875, 291.0], [439.576171875, 302.02734375], [314.666015625, 302.02734375]]}, {"title": "A.2 Distribution Matching", "heading_level": null, "page_id": 9, "polygon": [[314.96484375, 471.75], [456.75, 471.75], [456.75, 482.23828125], [314.96484375, 482.23828125]]}, {"title": "B EXPERIMENT DETAILS AND EFFICIENCY", "heading_level": null, "page_id": 10, "polygon": [[52.5, 554.25], [285.0, 554.25], [285.0, 565.3828125], [52.5, 565.3828125]]}, {"title": "C CROSS ARCHITECTURE PERFORMANCE", "heading_level": null, "page_id": 11, "polygon": [[52.5, 550.5], [284.484375, 550.5], [284.484375, 561.515625], [52.5, 561.515625]]}, {"title": "D PARAMETER SENSITIVITY", "heading_level": null, "page_id": 11, "polygon": [[316.5, 303.0], [474.75, 303.0], [474.75, 314.7890625], [316.5, 314.7890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 118], ["Text", 15], ["SectionHeader", 6], ["Footnote", 2], ["ListItem", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8126, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 109], ["Text", 7], ["SectionHeader", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["Line", 195], ["Text", 7], ["SectionHeader", 5], ["Equation", 4], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1276, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 487], ["Line", 90], ["Text", 6], ["TextInlineMath", 6], ["Reference", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1173], ["Line", 233], ["Text", 9], ["Equation", 8], ["TextInlineMath", 4], ["Reference", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 107], ["TableCell", 72], ["Text", 8], ["SectionHeader", 4], ["TextInlineMath", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1894, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1001], ["TableCell", 754], ["Line", 104], ["Text", 5], ["Caption", 4], ["Reference", 4], ["Table", 3], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 26888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 82], ["TableCell", 37], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 802, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 142], ["ListItem", 36], ["Reference", 36], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 741], ["Line", 157], ["ListItem", 29], ["Reference", 28], ["Text", 7], ["SectionHeader", 3], ["Equation", 2], ["TextInlineMath", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 596, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1353], ["TableCell", 363], ["Line", 79], ["Text", 6], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 2709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 668], ["Line", 107], ["Text", 6], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 863, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 18], ["Reference", 2], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_for_Time_Series_Classification_via_Dual_Domain_Matching"}