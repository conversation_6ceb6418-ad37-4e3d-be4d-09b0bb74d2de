# Dataset Condensation via Efficient Synthetic-Data Parameterization

Jang-<PERSON><PERSON>¹ <PERSON>¹ <PERSON>-<PERSON><PERSON>-<PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>23</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><PERSON><sup>4</sup> <PERSON><PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>1</sup>

# Abstract

The great success of machine learning with massive amounts of data comes at a price of huge computation costs and storage for training and tuning. Recent studies on dataset condensation attempt to reduce the dependence on such massive data by synthesizing a compact training dataset. However, the existing approaches have fundamental limitations in optimization due to the limited representability of synthetic datasets without considering any data regularity characteristics. To this end, we propose a novel condensation framework that generates multiple synthetic data with a limited storage budget via efficient parameterization considering data regularity. We further analyze the shortcomings of the existing gradient matching-based condensation methods and develop an effective optimization technique for improving the condensation of training data information. We propose a unified algorithm that drastically improves the quality of condensed data against the current state-of-the-art on CIFAR-10, ImageNet, and Speech Commands.

## 1. Introduction

Deep learning has achieved great success in various fields thanks to the recent advances in technology and the availability of massive real-world data [\(Le<PERSON>un et al.,](#page-9-0) [2015\)](#page-9-0). However, this success with massive data comes at a price: huge computational and environmental costs for large-scale neural network training, hyperparameter tuning, and architecture search [\(Patterson et al.,](#page-9-1) [2021;](#page-9-1) [Brown et al.,](#page-9-2) [2020;](#page-9-2) [Cubuk](#page-9-3) [et al.,](#page-9-3) [2019;](#page-9-3) [Zoph & Le,](#page-10-0) [2017\)](#page-10-0).

An approach to reduce the costs is to construct a compact

Image /page/0/Figure/10 description: This diagram illustrates a machine learning process involving original and synthetic training data. A legend indicates that a dark blue arrow represents a 'Forward Pass' and a red arrow represents 'Backpropagation'. The process begins with 'Original training data' (represented by stacked gray squares) undergoing a forward pass to a 'Classifier (shared)' (a blue geometric shape). This classifier's output is then used to calculate a 'Matching loss D' (a rounded rectangle). The 'Matching loss D' then influences the classifier through backpropagation. Simultaneously, 'Condensed data' (stacked squares with patterned sections) is transformed into 'Synthetic training data' (stacked squares with different patterns) via a 'Multi-formation f' process. The synthetic data also undergoes a forward pass to the shared classifier, and the matching loss is calculated and backpropagated, creating a feedback loop.

<span id="page-0-0"></span>Figure 1. Illustration of the proposed dataset condensation framework with multi-formation. Under the fixed-size storage for condensed data, multi-formation synthesizes multiple data used to train models. We optimize the condensed data in an end-to-end fashion by using the differentiable multi-formation functions.

dataset that contains sufficient information from the original dataset to train models. A classic approach to construct such a dataset is to select the *coreset* [\(Phillips,](#page-9-4) [2016\)](#page-9-4). However, selection-based approaches have limitations in that they depend on heuristics and assume the existence of representative samples in the original data [\(Zhao & Bilen,](#page-10-1) [2021b\)](#page-10-1). To overcome these limitations, recent studies, called *dataset condensation* or *dataset distillation*, propose to synthesize a compact dataset that has better storage efficiency than the coresets [\(Wang et al.,](#page-10-2) [2018\)](#page-10-2). The synthesized datasets have a variety of applications such as increasing the efficiency of replay exemplars in continual learning and accelerating neural architecture search [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3).

The natural data satisfy regularity conditions that form a low-rank data subspace [\(Huang & Mumford,](#page-9-5) [1999\)](#page-9-5), *e.g.*, spatially nearby pixels in a natural image look similar and temporally adjacent signals have similar spectra in speech [\(Zhang et al.,](#page-10-4) [2017\)](#page-10-4). However, the existing condensation approaches directly optimize each data element, *e.g.,* pixel by pixel, without imposing any regularity conditions on the synthetic data [\(Nguyen et al.,](#page-9-6) [2021;](#page-9-6) [Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5). Under the limited storage budget, this inefficient parameterization of synthetic datasets results in the synthesis of a limited number of data, having fundamental limitations on optimization. Furthermore, optimizing the synthetic data that have comparable training performance to the original data is challenging because it requires unrolling the entire training procedure. Recent studies propose surrogate ob-

<sup>&</sup>lt;sup>1</sup>Department of Computer Science and Engineering, Seoul National University <sup>2</sup>NAVER AI Lab  $3$ University of Tübingen 4 Image Vision, NAVER Clova. Correspondence to: Hyun Oh Song <<EMAIL>>.

*Proceedings of the*  $39<sup>th</sup>$  *International Conference on Machine Learning*, Baltimore, Maryland, USA, PMLR 162, 2022. Copyright 2022 by the author(s).

jectives to address the challenge above, however, there are remaining questions on why certain objectives are better proxies for the true objective [\(Zhao & Bilen,](#page-10-5) [2021a;](#page-10-5)[b\)](#page-10-1).

In this work, we pay attention to making better use of condensed data elements and propose a novel optimization framework resolving the previous limitations. Specifically, we introduce a *multi-formation* process that creates multiple synthetic data under the same storage constraints as existing approaches (Figure [1\)](#page-0-0). Our proposed process naturally imposes regularity on synthetic data while increasing the number of synthetic data, resulting in an enlarged and regularized dataset. In Section [3.3,](#page-2-0) we theoretically analyze the multi-formation framework and examine the conditions where the improvement is guaranteed. We further analyze the optimization challenges in the gradient matching method by [Zhao & Bilen](#page-10-1) [\(2021b\)](#page-10-1) in Section [4.](#page-3-0) Their approach induces imbalanced network gradient norms between synthetic and real data, which is problematic during optimization. Based on our analysis and empirical findings, we develop improved optimization techniques utilizing networks trained on the real data with stronger regularization and effectively mitigate the mentioned problems.

In this regard, we present an end-to-end optimization algorithm that creates information-intensive condensed data significantly outperforming all existing condensation methods. Given fixed storage and computation budgets, neural networks trained on our synthetic data show performance improvements of 10∼20%p compared to state-of-the-art methods in experimental settings with various datasets and domains including ImageNet and Speech Commands [\(War](#page-10-6)[den,](#page-10-6) [2018\)](#page-10-6). We further verify the utility of our condensed data through experiments on continual learning, demonstrating significant performance improvements compared to existing condensation and coreset methods. We release the source code at [https://github.com/snu-mllab/](https://github.com/snu-mllab/Efficient-Dataset-Condensation) [Efficient-Dataset-Condensation](https://github.com/snu-mllab/Efficient-Dataset-Condensation).

# 2. Preliminary

Given the storage budget, the goal of data condensation is to build a surrogate dataset  $S$  of the original training dataset  $T$  such that an arbitrary model trained on  $S$ is similar to the one trained on  $\mathcal T$  [\(Wang et al.,](#page-10-2) [2018\)](#page-10-2). Oftentimes, the measure of similarity is in terms of the model performance on the test set because that leads to meaningful applications such as continual learning and neural architecture search [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3). Instead of solving this ultimate objective, previous methods have proposed different surrogates. For example, [Wang et al.](#page-10-2) [\(2018\)](#page-10-2) propose to optimize S such that a model trained on S minimizes the loss values over  $T$ . However, this approach involves a nested optimization with unrolling multiple training iterations, requiring expensive computation costs.

Rather than direct optimization of model performance, [Zhao](#page-10-3) [et al.](#page-10-3) [\(2021\)](#page-10-3) propose a simpler optimization framework that matches the network gradients on S to the gradients on T. Let us assume a data point is m-dimensional and  $S \in \mathbb{R}^{n \times m}$ , where *n* is the number of data points in  $S$ . [Zhao et al.](#page-10-3) [\(2021\)](#page-10-3) optimize the synthetic data as

<span id="page-1-0"></span>maximize 
$$
\sum_{\mathcal{S} \in \mathbb{R}^{n \times m}}^{\tau} \sum_{t=0}^{\tau} \text{Cos}(\nabla_{\theta} \ell(\theta_t; \mathcal{S}), \nabla_{\theta} \ell(\theta_t; \mathcal{T}))
$$
 (1)  
subject to  $\theta_{t+1} = \theta_t - \eta \nabla_{\theta} \ell(\theta_t; \mathcal{S})$  for  $t = 0, ..., \tau - 1$ ,

where  $\theta_t$  denotes the network weights at  $t^{\text{th}}$  training step from the randomly initialized weights  $\theta_0$  given S,  $\ell(\theta; S)$ denotes the training loss for weight  $\theta$  and the dataset S.  $Cos(\cdot, \cdot)$  denotes the channel-wise cosine similarity. [Zhao](#page-10-3) [et al.](#page-10-3) [\(2021\)](#page-10-3) have reported that the class-wise gradient matching objective is effective for dataset condensation. They propose an alternating optimization algorithm with the following update rules for each class  $c$ :

$$
S_c \leftarrow S_c + \lambda \nabla_{S_c} \cos(\nabla_{\theta} \ell(\theta; S_c), \nabla_{\theta} \ell(\theta; T_c))
$$
  
$$
\theta \leftarrow \theta - \eta \nabla_{\theta} \ell(\theta; S),
$$

where  $S_c$  and  $T_c$  denote the mini-batches from the datasets  $S$  and  $T$ , respectively. Under the formulation, [Zhao & Bilen](#page-10-1) [\(2021b\)](#page-10-1) propose to utilize differentiable siamese augmentation (DSA) for a better optimization of the synthetic data. DSA performs gradient matching on augmented data where the objective becomes  $\mathbb{E}_{\omega \sim \mathcal{W}}[\cos(\nabla_{\theta} \ell(\theta; a_{\omega}(S)), \nabla_{\theta} \ell(\theta; a_{\omega}(T)))]$ . Here,  $a_{\omega}$  means a parameterized augmentation function and W denotes an augmentation parameter space. Subsequently, [Zhao & Bilen](#page-10-5) [\(2021a\)](#page-10-5) propose to match the hidden features rather than the gradients for fast optimization. However, the feature matching approach has some performance degradation compared to gradient matching [\(Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5). Although this series of works have made great contributions, there are remaining challenges and questions on their surrogate optimization problems. In this work, we try to resolve the challenges by providing a new optimization framework with theoretical analysis and empirical findings.

## 3. Multi-Formation Framework

In this section, we pay attention to the synthetic-data parameterization in optimization and present a novel data formation framework that makes better use of condensed data. We first provide our motivating observations and introduce a multi-formation framework with theoretical analysis.

### 3.1. Observation

We first provide our empirical observations on the effects of the number and resolution of the synthetic data in the matching problem. The existing condensation approaches aim to synthesize a predetermined number of data about

Image /page/2/Figure/1 description: The image contains two plots. The plot on the left is a line graph titled "Matching loss" with "Iteration" on the x-axis and "Matching loss" on the y-axis. There are three lines representing n=1 (red), n=10 (tan), and n=50 (green). All lines show a decrease in matching loss as the iteration increases, with n=50 showing the lowest matching loss. The plot on the right is a heatmap titled "Matching loss" with "Resolution" on the x-axis and "Number of data" on the y-axis. The color bar on the right indicates that darker colors represent higher matching loss values, ranging from 0 to 400. The heatmap shows that matching loss generally increases with higher resolution and a smaller number of data points.

<span id="page-2-1"></span>Figure 2. (Left) Matching loss curves over an increasing number of synthetic data per class  $(n)$ . (Right) Matching loss heat map over various resolutions and numbers of data per class. The x-axis refers to the downsampled image resolution. We measure values on the same network after resizing data to the original size (CIFAR-10).

10 to 50 per class [\(Zhao & Bilen,](#page-10-1) [2021b;](#page-10-1) [Nguyen et al.,](#page-9-6) [2021\)](#page-9-6). The left subfigure in Figure [2](#page-2-1) shows the condensation matching loss curves of DSA over various numbers of synthetic data per class. As shown in the figure, more synthetic data lead to a smaller matching loss, indicating the importance of the number of synthetic data in the matching problem. For a comparison under the same data storage budget, we measure the matching loss on the same network after reducing the resolution of the optimized synthetic data and resizing the data to the original size. In the right subfigure in Figure [2,](#page-2-1) we find the resolution produces a moderate change in matching loss as the number of data does, even if we do not take the resolution modification into account during the condensation stage. For example, points at (16, 48) and (32, 12), which require an equal storage size, have similar loss values. Motivated by these results, we propose a multi-formation framework that makes better use of the condensed data and forms the increased number of synthetic data under the same storage budget.

### 3.2. Multi-Formation

The existing approaches directly match condensed data  $\mathcal S$ to the original training data  $\mathcal T$  and use  $\mathcal S$  as the synthetic training data. Instead, we add an intermediate process that creates an increased number of synthetic data from  $S$  by mapping a data element in  $S$  to multiple data elements in the synthetic data (Figure [1\)](#page-0-0). The previous work by Zhao  $\&$ [Bilen](#page-10-1) [\(2021b\)](#page-10-1) reports that the use of random augmentations in matching problems degrades performance due to the misalignment problem. They argue the importance of the deterministic design of the matching problem. In this regard, we propose to use a deterministic process rather than a random process.

Consistent to existing approaches, we optimize and store condensed data  $S \in \mathbb{R}^{n \times m}$ . For  $n' > n$ , we propose a multiformation function  $f : \mathbb{R}^{n \times m} \to \mathbb{R}^{n' \times m}$  that augments the number of condensed data  $S$  and creates multiple synthetic training data  $f(S)$  in a deterministic fashion. For any match-

Image /page/2/Figure/7 description: The image displays a comparison between two data formation processes: uniform formation and multi-scale formation. Both processes start with 'Condensed data' represented by a stack of four square grids, each divided into four colored quadrants (yellow, light green, light brown, and orange). The uniform formation process transforms this condensed data into 'Synthetic data' which is also a stack of four grids, but all grids are now uniformly colored in shades of green and yellow. The multi-scale formation process also starts with the same condensed data and transforms it into 'Synthetic data'. This resulting synthetic data is a stack of four grids, where the colors vary across the grids, showing a progression from green and yellow in the initial grids to yellow, light brown, and orange in the later grids, suggesting a multi-scale representation.

<span id="page-2-2"></span>Figure 3. Illustration of the proposed multi-formation functions, in the case of multi-formation by a factor of 2.

ing objective  $D$  (lower the better) and target task objective  $\ell$ , the optimization and evaluation stages of condensed data  $S$  with multi-formation function  $f$  are

$$
S^* = \underset{S \in \mathbb{R}^{n \times m}}{\operatorname{argmin}} D(f(S), \mathcal{T})
$$
 (Optimization)

$$
\theta^* = \operatorname*{argmin}_{\theta} \ell(\theta; f(\mathcal{S}^*)).
$$
 (Evaluation)

That is, we perform matching on  $\mathcal T$  using  $f(\mathcal S)$  and use them for evaluation. This enables us to optimize the synthetic dataset with an increased number of data, using the same storage budget. Figure [1](#page-0-0) illustrates the optimization process with multi-formation. Note, we can use conventional data augmentations following the multi-formation.

Given a differentiable multi-formation function and matching objective, we optimize  $S$  in an end-to-end fashion by gradient descent. In this work, we design a simple differentiable multi-formation function and evaluate the effectiveness of our approach. The idea is to locally interpolate data elements while preserving the locality of natural data, *i.e.*, spatially nearby pixels in a natural image look similar and temporally adjacent signals have similar spectra in speech [\(Huang & Mumford,](#page-9-5) [1999;](#page-9-5) [Zhang et al.,](#page-10-4) [2017\)](#page-10-4). Specifically, we partition each data and resize the partitioned data to the original size by using bilinear upsampling (Figure [3\)](#page-2-2). Note, this formation function has negligible computation overhead. Furthermore, the formation function creates locally smooth synthetic data that might naturally regularize the optimization from numerous local minima. We use a fixed uniform partition function in our main experiments in Section [5](#page-4-0) and further analyze multi-scale and learnable formation functions in Appendix [D.](#page-13-0)

<span id="page-2-0"></span>

### 3.3. Theoretical Analysis

In this section, we aim to theoretically analyze our multiformation framework. Here, we assume a data point is  $m$ dimensional. The natural data have regularity that makes difference from random noise [\(Huang & Mumford,](#page-9-5) [1999\)](#page-9-5). We assume that data satisfying this regularity form a subspace  $\mathcal{N} \subset \mathbb{R}^m$ . That is, the original training dataset  $\mathcal{T} = \{t_i\}_{i=1}^{n_t}$ satisfies  $t_i \in \mathcal{N}$  for  $i = 1, \ldots, n_t$ . With abuse of notation, we denote the space of datasets with  $n$  data points as  $\mathbb{R}^{n \times m} = \{ \{d_i\}_{i=1}^n \mid d_i \in \mathbb{R}^m \text{ for } i = 1, ..., n \}.$  We further define the space of all datasets  $\mathcal{D} = \bigcup_{n \in \mathbb{N}} \mathbb{R}^{n \times m}$  and the synthetic-dataset space of a multi-formation function  $f: \mathbb{R}^{n \times m} \to \mathbb{R}^{n' \times m}$ ,  $\mathcal{M}_f = \{f(\mathcal{S}) \mid \mathcal{S} \in \mathbb{R}^{n \times m}\}$ . We now introduce our definition of distance measure between

datasets. We say data *d* is closer to dataset  $X = \{d_i\}_{i=1}^k$ than  $d'$ , if  $\forall i \in [1, ..., k]$ ,  $||d - d_i|| \le ||d' - d_i||$ .

**Definition 1.** *A function*  $D : \mathcal{D} \times \mathcal{D} \rightarrow [0, \infty)$  *is a dataset distance measure, if it satisfies the followings:*  $\forall X, X' \in \mathcal{D}$ *where*  $X = \{d_i\}_{i=1}^k$ ,  $\forall i \in [1, \ldots, k]$ ,

- *1.*  $D(X, X) = 0$  and  $D(X, X') = D(X', X)$ .
- 2.  $\forall d \in \mathbb{R}^m$  *s.t. d is closer to* X' *than*  $d_i$ ,  $D(X \setminus \{d_i\} \cup$  $\{d\}, X' \leq D(X, X').$
- *3.*  $D(X, X' \cup \{d_i\}) \leq D(X, X').$

The definition above states reasonable conditions for dataset distance measurement. Specifically, the second condition states that the distance decreases if a data point in a dataset moves closer to the other dataset. The third condition states that the distance decreases if a data point in a dataset is added to the other dataset. Based on the definition, we introduce the following proposition. We provide the proof in Appendix [A.1.](#page-11-0)

<span id="page-3-1"></span>**Proposition 1.** If  $N^{n'} \subseteq M_f$ , then for any dataset distance *measure* D*,*

$$
\min_{\mathcal{S}\in\mathbb{R}^{n\times m}} D(f(\mathcal{S}),\mathcal{T}) \leq \min_{\mathcal{S}\in\mathbb{R}^{n\times m}} D(\mathcal{S},\mathcal{T}).
$$

Proposition [1](#page-3-1) states that our multi-formation framework achieves the better optimum, *i.e.*, the synthetic dataset that is closer to the original dataset under any dataset distance measure. Note, the assumption  $\mathcal{N}^{n'} \subseteq \mathcal{M}_f$  means that the synthetic-dataset space by  $f$  is sufficiently large to contain all data points in  $N$ . In Appendix [A.2,](#page-11-1) we provide theoretical results under a more relaxed assumption.

<span id="page-3-0"></span>

# 4. Improved Optimization Techniques

In this section, we develop optimization techniques for dataset condensation. We first analyze gradient matching [\(Zhao & Bilen,](#page-10-1) [2021b\)](#page-10-1) and seek to provide an interpretation of why gradient matching on condensation works better than feature matching [\(Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5). We then examine some of the shortcomings of existing gradient matching methods and propose improved techniques.

## 4.1. Interpretation

Convolutional or fully-connected layers in neural networks linearly operate on hidden features. From the linearity, it is possible to represent network gradients as features as in Proposition [2.](#page-3-2) For simplicity, we consider one-dimensional convolution on hidden features and drop channel notations.

<span id="page-3-2"></span>**Proposition 2.** Let  $w_t \in \mathbb{R}^K$  and  $h_t \in \mathbb{R}^W$  each denote *the convolution weights and hidden features at the* t *th layer* given the input data x. Then, for a loss function  $\ell$ ,  $\frac{d\ell(x)}{dw_t}$  =  $\sum_{i} a_{t,i} h_{t,i}$ , where  $h_{t,i} \in \mathbb{R}^K$  denotes the i<sup>th</sup> convolution patch of  $h_t$  and  $a_{t,i} = \frac{d\ell(x)}{dw_t^{\mathsf{T}} h_{t,i}} \in \mathbb{R}$ .

Image /page/3/Figure/15 description: The image is a line graph showing the L1 norm over training steps. The x-axis is labeled "Training steps" and ranges from 0 to 500. The y-axis is labeled "L1 norm" and ranges from 0 to 1,000. There are two lines plotted: a red line labeled "real" and a blue line labeled "syn". The "real" line starts at approximately 300, increases to about 900 at 100 training steps, and then continues to increase gradually to about 1,100 at 500 training steps. The "syn" line starts at approximately 300, decreases to about 100 at 100 training steps, and then continues to decrease gradually to about 50 at 500 training steps.

<span id="page-3-3"></span>Figure 4. Evolution of  $L^1$  norm of the network gradients given real or synthetic data. The x-axis represents the number of training steps of the networks. Here, both networks are trained on the synthetic data with augmentations. We measure the values on CIFAR-10 with ConvNet-3 used in DSA.

Proposition [2](#page-3-2) states the gradients with respect to convolution weights can be regarded as the weighted sum of local features  $h_{t,i}$ . Note, the weight  $a_{t,i}$  means the loss function sensitivity of the  $i<sup>th</sup>$  output at the  $t<sup>th</sup>$  layer, and we can interpret the network gradients as the saliency-weighted average local features. In this respect, we can view gradient matching as saliency-weighted average local feature matching.

Intuitively, saliency-weighting selectively extracts information corresponding to target labels. In addition, by matching averaged local features, we globally compare features regardless of location, which might be beneficial for datasets where target objects are non-aligned, *e.g.*, ImageNet [\(Deng](#page-9-7) [et al.,](#page-9-7) [2009\)](#page-9-7). We conjecture these properties explain why gradient matching performs better than feature matching. In the following, we propose an improved gradient matching method by examining the shortcomings of existing gradient matching approaches.

## 4.2. Problems and Solutions

The existing gradient matching approach by DSA uses network weights  $\theta_t$  trained on a condensed dataset S (see Equation [\(1\)](#page-1-0)). However, this approach has some drawbacks: 1) In the optimization process, S and  $\theta_t$  are strongly coupled, resulting in a chicken-egg problem that generally requires elaborate optimization techniques and initialization [\(McLachlan & Krishnan,](#page-9-8) [2007\)](#page-9-8). 2) Due to the small size of  $S \sim 1\%$  of the original training set), overfitting occurs in the early stage of the training and the network gradients vanish quickly. Figure [4](#page-3-3) shows that the gradient norm on S vanishes whereas the gradient norm on the real data  $\tau$ increases when the network is trained on  $S$ . This leads to undesirable matching between two data sources, resulting in degraded performance when using distance-based matching objectives, such as mean squared error [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3).

To overcome these issues, we propose to utilize networks trained on  $T$  instead. By doing so, we optimize  $S$  with networks that are no longer dependent on  $S$ , resulting in a decoupled optimization problem:

$$
\underset{\mathcal{S} \in \mathbb{R}^{n \times m}}{\text{minimize}} \ \bar{D} \left( \nabla_{\theta} \ell(\theta^{\mathcal{T}}; f(\mathcal{S})), \nabla_{\theta} \ell(\theta^{\mathcal{T}}; \mathcal{T}) \right)
$$

.

Image /page/4/Figure/1 description: The image contains two plots. The left plot shows "Condensation acc." on the y-axis and "Pretrained epoch" on the x-axis, ranging from 0 to 100. The plot displays a blue line with points showing an increase from approximately 45 at epoch 0 to a peak of about 57 at epoch 10, followed by a decrease to about 43 at epoch 100. The right plot has two y-axes. The left y-axis shows "L1 norm" ranging from 200 to 800, and the right y-axis shows "Cosine similarity" ranging from 0.2 to 0.5. The x-axis for both is "Pretrained epoch" from 0 to 100. A red line labeled "cos" and a blue line labeled "norm" are plotted. The red line starts at about 450 at epoch 0, peaks at about 800 at epoch 10, and then gradually decreases to about 0.35 at epoch 100. The blue line starts at about 250 at epoch 0, peaks at about 800 at epoch 10, and then decreases to about 0.32 at epoch 100, with some fluctuations.

<span id="page-4-1"></span>Figure 5. (Left) Condensation performance from fixed pretrained networks. The x-axis represents the number of epochs a network is trained on. (Right) Gradient analysis of the pretrained networks. The left axis measures the  $L^1$  norm of the network gradients given a batch of data consisting of the same class. The right axis measures the average pairwise cosine-similarity between the gradients on a single data of the same class. The values are measured on ImageNet with 10 subclasses.

Here,  $\theta^{\mathcal{T}}$  represents network weights trained on  $\mathcal{T}$  and  $\bar{D}$  denotes a distance-based matching objective. In addition, the large size of  $\mathcal T$  alleviates the gradient vanishing from overfitting [\(Bishop,](#page-9-9) [2006\)](#page-9-9). To further enhance the effect, we utilize stronger regularization for training networks. In detail, rather than a single random augmentation strategy adopted in DSA, we propose to use a sequence of augmentations and CutMix [\(Yun et al.,](#page-10-7) [2019\)](#page-10-7). Note, the mixup techniques such as CutMix effectively resolve the neural networks' over-confidence issue by using soft labels for training [\(Kim](#page-9-10) [et al.,](#page-9-10) [2020;](#page-9-10) [2021\)](#page-9-11). To sum up, the proposed utilization of real data and stronger augmentations effectively resolve the gradient vanishing problem and enable the use of distancebased objective functions, resulting in the better distillation of learning information onto the synthetic data.

### 4.3. Algorithm

We further analyze the effect of network weights  $\theta^{\mathcal{T}}$  on condensation. In detail, we examine when networks show the best condensation performance during the learning process on  $\mathcal T$ . Here, the performance means the test accuracy of neural networks trained on the condensed data. The left subfigure in Figure [5](#page-4-1) shows the performance of condensed data optimized by a network trained for a specific epoch. We observe the best condensation performance by the networks in the early phase of training near 10 epochs.

To clarify the observation, we measure the networks' gradient norm given an intra-class mini-batch (right subfigure in Figure [5\)](#page-4-1). As a result, we find that the gradient norm increases in the early phase of training and then decreases during the further training epochs. We also observe a similar pattern when we measure pairwise cosine-similarity between the gradients given a single data of the same class. These results indicate the gradient directions among intraclass data coincide at the early phase of training but diverge as the training progresses. This phenomenon is similarly observed by [Jastrzebski et al.](#page-9-12) [\(2019\)](#page-9-12); the first eigenvalue of

<span id="page-4-2"></span>

| <b>Algorithm 1</b> Information-Intensive Dataset Condensation                                                   |
|-----------------------------------------------------------------------------------------------------------------|
| <b>Input:</b> Training data $\mathcal T$                                                                        |
| <b>Notation:</b> Multi-formation function $f$ , parameterized                                                   |
| augmentation function $a_{\omega}$ , mixup function h, loss func-                                               |
| tion l, number of classes $N_c$                                                                                 |
| <b>Definition:</b> $D(B, B'; \theta) =   \nabla_{\theta} \ell(\theta; B)) - \nabla_{\theta} \ell(\theta; B')  $ |
| Initialize condensed dataset $S$                                                                                |
| repeat                                                                                                          |
| Initialize or load pretrained network $\theta_1$                                                                |
| for $i=1$ to M do                                                                                               |
| for $c=1$ to $N_c$ do                                                                                           |
| Sample an intra-class mini-batch $T_c \sim \mathcal{T}, S_c \sim \mathcal{S}$                                   |
| Update $S_c \leftarrow S_c - \lambda \nabla_{S_c} D(a_\omega(f(S_c)), a_\omega(T_c); \theta_i)$                 |
| end for                                                                                                         |
| Sample a mini-batch $T \sim \mathcal{T}$                                                                        |
| Update $\theta_{i+1} \leftarrow \theta_i - \eta \nabla_{\theta} \ell(\theta_i; h(a_{\omega'}(T)))$              |
| end for                                                                                                         |
| until convergence                                                                                               |
| Output: $S$                                                                                                     |

the networks' hessian matrix increases in the early phase and decreases after a few epochs. Based on the observation, we argue that intra-class network gradients in the early training phase have more useful information to distill, and propose to utilize networks in the early training phase for condensation. Additionally, using the early phase neural networks has advantages in terms of the training cost.

We empirically observe that using multiple network weights for condensation rather than the fixed network weights improves the generalization of the condensed data over various test models. Therefore, we alternately update S and  $\theta^{\tau}$ during the optimization process. In detail, we first initialize  $\theta^{\mathcal{T}}$  by random initialization or loading pretrained weights trained only for a few epochs, and then we alternatively update S and  $\theta^{\mathcal{T}}$ . In addition, we periodically reinitialize  $\theta^{\mathcal{T}}$  to maintain the network to be in the early training phase. Putting together with our multi-formation framework, we propose a unified algorithm optimizing informationintensive condensed data that compactly contain the original training data information. We name the algorithm as *Information-intensive Dataset Condensation* (IDC) and describe the algorithm in Algorithm [1.](#page-4-2) Note, we adopt the siamese augmentation strategy by DSA.

<span id="page-4-0"></span>

## 5. Experimental Results

In this section, we evaluate the performance of our condensation algorithm over various datasets and tasks. We first evaluate our condensed data from CIFAR-10, ImageNetsubset, and Speech Commands by training neural networks from scratch on the condensed data [\(Krizhevsky et al.,](#page-9-13) [2009;](#page-9-13) [Deng et al.,](#page-9-7) [2009;](#page-9-7) [Warden,](#page-10-6) [2018\)](#page-10-6). Next, we investigate the proposed algorithm by performing ablation analysis and con-

<span id="page-5-0"></span>

| Table 1. Top-1 test accuracy of test models trained on condensed datasets from CIFAR-10. We optimize the condensed data using           |
|-----------------------------------------------------------------------------------------------------------------------------------------|
| ConvNet-3 and evaluate the data on three types of networks. Pixel/Class means the number of pixels per class of the condensed data      |
| and we denote the compression ratio to the original dataset in the parenthesis. We evaluate each case with 3 repetitions and denote the |
| standard deviations in the parenthesis. † denotes the reported results from the original papers.                                        |

| Pixel/Class        | Test Model                             | Random               | Herding              | DSA                   | KIP             | DM                   | IDC-I                                  | IDC                                    | Full dataset         |
|--------------------|----------------------------------------|----------------------|----------------------|-----------------------|-----------------|----------------------|----------------------------------------|----------------------------------------|----------------------|
| 10×32×32<br>(0.2%) | ConvNet-3<br>ResNet-10<br>DenseNet-121 | 37.2<br>34.1<br>36.5 | 41.7<br>35.9<br>36.7 | 52.1†<br>32.9<br>34.5 | 49.2†<br>-<br>- | 53.8<br>42.3<br>39.0 | 58.3 (0.3)<br>50.2 (0.4)<br>49.5 (0.6) | 67.5 (0.5)<br>63.5 (0.1)<br>61.6 (0.6) | 88.1<br>92.7<br>94.2 |
| 50×32×32<br>(1%)   | ConvNet-3<br>ResNet-10<br>DenseNet-121 | 56.5<br>51.2<br>55.8 | 59.8<br>56.5<br>59.0 | 60.6†<br>49.7<br>49.1 | 56.7†<br>-<br>- | 65.6<br>58.6<br>57.4 | 69.5 (0.3)<br>65.7 (0.7)<br>63.1 (0.2) | 74.5 (0.1)<br>72.4 (0.5)<br>71.8 (0.6) | 88.1<br>92.7<br>94.2 |

trolled experiments. Finally, we validate the efficacy of our condensed data on continual learning settings as a practical application [\(Parisi et al.,](#page-9-14) [2019\)](#page-9-14). We use multi-formation by a factor of 2 in our main experiments except for ImageNet where use a factor of 3. The other implementation details and hyperparameter settings of our algorithm are described in Appendix [C.1.](#page-12-0) We also provide experimental results on SVHN, MNIST, and FashionMNIST in Appendix [E.1.](#page-13-1)

### 5.1. Condensed Dataset Evaluation

A common evaluation method for condensed data is to measure the test accuracy of the neural networks trained on the condensed data [\(Zhao & Bilen,](#page-10-1) [2021b\)](#page-10-1). It is widely known that test accuracy is affected by the type of test models as well as the quality of the data [\(Zoph & Le,](#page-10-0) [2017\)](#page-10-0). However, some previous works overlook the contribution from test model types and compare algorithms on different test models [\(Nguyen et al.,](#page-9-6) [2021\)](#page-9-6). In this work, we emphasize specifying the test model and comparing the condensation performance on an identical test model for fair comparison. This procedure isolates the effect of the condensed data, thus enabling us to purely measure the condensation quality. We further evaluate the condensed data on multiple test models to measure the generalization ability of the condensed data across different architectures.

Baselines we consider are a random selection, Herding coreset selection [\(Welling,](#page-10-8) [2009\)](#page-10-8), and the previous state-of-theart condensation methods; DSA, KIP, and DM [\(Zhao &](#page-10-1) [Bilen,](#page-10-1) [2021b;](#page-10-1) [Nguyen et al.,](#page-9-6) [2021;](#page-9-6) [Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5). We downloaded the publicly available condensed data, and otherwise, we re-implement the algorithms following the released author codes. For the implementation details of the baselines, please refer to Appendix [C.2.](#page-12-1) We denote our condensed data with multi-formation as IDC and without multi-formation as IDC-I which can also be regarded as a method with the identity formation function. Finally, it is worth noting that KIP considers test models with ZCA pre-processing [\(Nguyen et al.,](#page-9-6) [2021\)](#page-9-6). However, we believe test models with standard normalization pre-processing are much more common to be used in classification and contin-

<span id="page-5-1"></span>Table 2. Top-1 test accuracy of test models with the fixed training steps. Each row matches the same dataset storage size and evaluation cost. *CN* denotes ConvNet-3, *RN* denotes ResNet-10, and *DN* denotes DenseNet-121. We measure training times on an RTX-3090 GPU.

| Pixel<br>Ratio | Test<br>Model | <b>DSA</b> | <b>KIP</b> | DM   | IDC-I | <b>IDC</b> | Evaluation<br>Time |
|----------------|---------------|------------|------------|------|-------|------------|--------------------|
| 0.2%           | CN            | 52.1       | 49.1       | 53.8 | 58.3  | 65.3       | 10s                |
|                | <b>RN</b>     | 32.9       | 40.8       | 42.3 | 50.2  | 57.7       | 20s                |
|                | DN            | 34.5       | 42.1       | 39.0 | 49.5  | 60.6       | 100s               |
| 1%             | CN            | 60.6       | 57.9       | 65.6 | 69.5  | 73.6       | 50s                |
|                | <b>RN</b>     | 49.7       | 52.9       | 58.6 | 65.7  | 72.3       | 90s                |
|                | DN            | 49.1       | 54.4       | 57.4 | 63.1  | 71.6       | 400s               |

ual learning settings [\(Cubuk et al.,](#page-9-3) [2019;](#page-9-3) [Dosovitskiy et al.,](#page-9-15) [2021;](#page-9-15) [Rebuffi et al.,](#page-9-16) [2017\)](#page-9-16). In this section, we focus on test models with standard normalization pre-processing. For experimental results with ZCA, please refer to Appendix [E.6.](#page-14-0)

CIFAR-10. The CIFAR-10 training set consists of 5,000 images per class each with  $32 \times 32$  pixels. Following the condensation baselines, we condense the training set with the storage budgets of 10 and 50 images per class by using 3-layer convolutional networks (ConvNet-3). For network architecture effect on condensation, please refer to Appendix [E.4.](#page-14-1) We evaluate the condensed data on multiple test models: ConvNet-3, ResNet-10, and DenseNet-121 [\(He](#page-9-17) [et al.,](#page-9-17) [2016;](#page-9-17) [Huang et al.,](#page-9-18) [2017\)](#page-9-18). It is worth noting that [Zhao & Bilen](#page-10-1) [\(2021b\)](#page-10-1) used data augmentation when evaluating DSA but did not apply any data augmentation when evaluating simple baselines Random and Herding. This is not a fully fair way to compare the quality of data. In our paper, we re-evaluate all baselines including DSA by using the same augmentation strategy as ours and report the best performance for fair comparison. For the more detailed results on augmentation, please refer to Appendix [E.2.](#page-13-2)

Table [1](#page-5-0) summarizes the test accuracy of neural networks trained on each condensed data. From the table, we confirm that both IDC and IDC-I significantly outperform all the baselines. Specifically, IDC outperforms the best baseline by over 10%p across all the test models and compression

| Class | Pixel/Class                         | <b>Test Model</b>                           | Random               | Herding              | <b>DSA</b>           | DM                   | <b><math>IDC-I</math></b>                                   | <b>IDC</b>                                                  | <b>Full Dataset</b>  |
|-------|-------------------------------------|---------------------------------------------|----------------------|----------------------|----------------------|----------------------|-------------------------------------------------------------|-------------------------------------------------------------|----------------------|
| 10    | $10\times224\times224$<br>$(0.8\%)$ | ResNetAP-10<br>ResNet-18<br>EfficientNet-B0 | 46.9<br>43.3<br>46.3 | 50.4<br>47.0<br>50.2 | 52.7<br>44.1<br>48.3 | 52.3<br>41.7<br>45.0 | <b>61.4 (0.8)</b><br><b>56.2 (1.2)</b><br><b>58.7 (1.4)</b> | <b>72.8 (0.6)</b><br><b>73.6 (0.4)</b><br><b>74.7 (0.5)</b> | 90.8<br>93.6<br>95.9 |
| 10    | $20\times224\times224$<br>$(1.6\%)$ | ResNetAP-10<br>ResNet-18<br>EfficientNet-B0 | 51.8<br>54.3<br>60.3 | 57.5<br>57.9<br>59.0 | 57.4<br>56.9<br>62.5 | 59.3<br>53.7<br>57.7 | <b>65.5 (1.0)</b><br><b>66.0 (0.7)</b><br><b>66.3 (0.5)</b> | <b>76.6 (0.4)</b><br><b>75.7 (1.0)</b><br><b>78.1 (1.0)</b> | 90.8<br>93.6<br>95.9 |
| 100   | $10\times224\times224$<br>$(0.8\%)$ | ResNetAP-10<br>ResNet-18<br>EfficientNet-B0 | 20.7<br>15.7<br>22.4 | 22.6<br>15.9<br>24.5 | 21.8<br>13.5<br>19.9 | 22.3<br>15.8<br>20.7 | <b>29.2 (0.4)</b><br><b>23.3 (0.3)</b><br><b>27.7 (0.6)</b> | <b>46.7 (0.2)</b><br><b>40.1 (0.5)</b><br><b>36.3 (0.6)</b> | 82.0<br>84.6<br>85.6 |
| 100   | $20\times224\times224$<br>$(1.6\%)$ | ResNetAP-10<br>ResNet-18<br>EfficientNet-B0 | 29.7<br>24.3<br>33.2 | 31.1<br>23.4<br>35.6 | 30.7<br>20.0<br>30.6 | 30.4<br>23.4<br>31.0 | <b>34.5 (0.1)</b><br><b>29.8 (0.2)</b><br><b>33.2 (0.5)</b> | <b>53.7 (0.9)</b><br><b>46.4 (1.6)</b><br><b>49.6 (1.2)</b> | 82.0<br>84.6<br>85.6 |

<span id="page-6-0"></span>Table 3. Top-1 test accuracy of test models trained on condensed datasets from ImageNet-subset. We optimize the condensed data using ResNetAP-10 and evaluate the data on three types of networks. We evaluate the condensed data by using the identical training strategy.

ratios. However, IDC requires additional training steps to converge due to the formation process in general. Considering applications where training cost matters, such as architecture search, we compare methods under the fixed training steps and report the results in Table [2.](#page-5-1) That is, we reduce the training epochs when evaluating IDC, and match the number of gradient descent steps identical to the other baselines. In the case of KIP, which originally uses a neural tangent kernel for training networks, we re-evaluate the dataset by using stochastic gradient descent as others to match the computation costs. Table [2](#page-5-1) shows IDC still consistently outperforms baselines by a large margin.

ImageNet. Existing condensation methods only perform the evaluation on small-scale datasets, such as MNIST or CIFAR-10. To the best of our knowledge, our work is the first to evaluate condensation methods on challenging high-resolution data, ImageNet [\(Deng et al.,](#page-9-7) [2009\)](#page-9-7), to set a benchmark and analyze how the condensation works on large-scale datasets. We implement condensation methods on ImageNet-subset consisting of 10 and 100 classes [\(Tian](#page-10-9) [et al.,](#page-10-9) [2020\)](#page-10-9), where each class consists of approximately 1200 images. We provide a detailed dataset description in Appendix [B.](#page-12-2) Note, KIP requires hundreds of GPUs for condensing CIFAR-10 and does not scale on ImageNet. In the ImageNet experiment, we use ResNetAP-10 for condensation, which is a modified ResNet-10 by replacing strided convolution as average pooling for downsampling [\(Zhao &](#page-10-5) [Bilen,](#page-10-5) [2021a\)](#page-10-5). For test models, we consider ResNetAP-10, ResNet-18, and EfficientNet-B0 [\(Tan & Le,](#page-10-10) [2019\)](#page-10-10).

Table [3](#page-6-0) summarizes the test accuracy of neural networks trained on the condensed data. The table shows IDC and IDC-I significantly outperform all the baselines across the various numbers of classes, compression ratios, and test models. One of the notable results is that the existing condensation methods do not transfer well to other test models. For example, DM performs better on ResNetAp-10 com-

Image /page/6/Figure/6 description: The image displays a 3x3 grid of abstract, distorted images. The top row shows what appear to be distorted flowers or faces, a distorted overhead view of a person, and two colorful parrots in a tree. The middle row features a distorted black car, a pattern of hexagonal shapes, and a close-up of a fluffy dog's face. The bottom row contains a distorted monkey or primate, a wavy pattern resembling roof tiles, and a set of wooden chairs in a room.

<span id="page-6-1"></span>Figure 6. Representative samples from IDC-I condensed data on ImageNet-100. The corresponding class labels are as follows: bottle cap, cabbage, lorikeet, car wheel, honeycomb, Shih-Tzu, gibbon, tile roof, and rocking chair.

pared to Random selection but performs poorly on other test models. On contrary, IDC consistently outperforms other methods regardless of test model types. This indicates that our networks trained on large real datasets extract more task-relevant information with less architectural inductive bias than randomly initialized networks (DM) or networks trained on synthetic datasets (DSA). In Figure [6,](#page-6-1) we provide representative condensed samples from IDC-I. Note, these samples are initialized by random real training samples. We provide the qualitative comparison of our condensed data and real training data in Appendix [F.](#page-14-2)

Speech Domain. We evaluate our algorithm on speech domain data to verify the generality of our algorithm.

| Spectrogram/<br>Class   | Rand. | Herd. | DSA  | DM   | IDC-I | IDC         | Full<br>Dataset |
|-------------------------|-------|-------|------|------|-------|-------------|-----------------|
| $10	imes64	imes64$ (1%) | 42.6  | 56.2  | 65.0 | 69.1 | 73.3  | <b>82.9</b> | 93.4            |
| $20	imes64	imes64$ (2%) | 57.0  | 72.9  | 74.0 | 77.2 | 83.0  | <b>86.6</b> |                 |

<span id="page-7-0"></span>Table 4. Top-1 test accuracy of ConvNet-4 trained on condensed spectrograms. *Rand.* and *Herd.* denote Random and Herding.

<span id="page-7-1"></span>Table 5. Ablation study of the proposed techniques (50 images per class on CIFAR-10). *Syn* denotes condensing with networks trained on the synthetic dataset and *Real* denotes condensing with networks trained on the real dataset. *Cos* denotes cosine-similarity matching objective, *MSE* denotes mean-square-error matching objective, and *Reg.* denotes our proposed stronger regularization.

| Test Model | Syn+ Cos (DSA) | Syn+ MSE | Real+ Cos | Real+ MSE | Real+Reg.+ MSE (Ours) |
|------------|----------------|----------|-----------|-----------|-----------------------|
| ConvNet-3  | 60.6           | 25.8     | 63.4      | 67.0      | 69.5                  |
| ResNet-10  | 49.7           | 25.7     | 59.1      | 61.6      | 65.7                  |

In detail, we condense Mini Speech Commands that contains 8,000 one-second audio clips of 8 command classes [\(Warden,](#page-10-6) [2018\)](#page-10-6). We preprocess speech data and obtain magnitude spectrograms each of size  $64 \times 64$ . For a detailed description of the dataset and preprocessing, please refer to Appendix [B.](#page-12-2) In the case of speech data, we use a one-dimensional multi-formation function by a factor of 2 along the time-axis of a spectrogram. Table [4](#page-7-0) shows the test accuracy on the speech dataset. IDC consistently outperforms baseline methods by large margins and achieves test performance close to the full dataset training, verifying its effectiveness on speech domain as well as on image domain.

### 5.2. Analysis

Ablation Study. In this section, we perform an ablation study on our gradient matching techniques described in Section [4.](#page-3-0) Specifically, we measure the isolated effect of 1) networks trained on real training data, 2) distance-based matching objective, and 3) stronger regularization on networks. Table [5](#page-7-1) shows the ablation results of IDC-I on CIFAR-10 condensed with 50 images per class. From the table, we find that using MSE matching objective with networks trained on the synthetic dataset (*Syn+MSE*) degenerates the performance significantly. However, when we use the MSE objective with networks trained on the real training dataset, the performance significantly increases compared to the baseline (*DSA*), especially on ResNet-10. Furthermore, we find that strong regularization on networks brings additional performance improvements on both test models. The results demonstrate that the distance-based objective (*MSE*) better distills training information than the similarity-based objective (*Cos*) when using well-trained networks.

Comparison to Post-Downsampling. One of the simple ways to save storage budget is to reduce the resolution of

<span id="page-7-2"></span>Table 6. Test performance comparison of IDC and IDC-I with postdownsampling (IDC-I-post) on CIFAR-10. We denote the number of stored pixels in parenthesis.

| <b>Test Model</b> | IDC.                   | $IDC-I-post$                | IDC-I                              |
|-------------------|------------------------|-----------------------------|------------------------------------|
|                   | $(50\times32\times32)$ | $(200 \times 16 \times 16)$ | $\left(200\times32\times32\right)$ |
| $ConvNet-3$       | 74.5                   | 68.8                        | 76.6                               |
| ResNet-10         | 72.4                   | 63.1                        | 74.9                               |

<span id="page-7-3"></span>Table 7. Condensation performance over various multi-formation factors on CIFAR-10 and ImageNet-10.

| Dataset<br>(Pixel/Class) | Test<br>Model | Multi-Formation Factor |             |             |             |
|--------------------------|---------------|------------------------|-------------|-------------|-------------|
|                          |               | 1                      | 2           | 3           | 4           |
| CIFAR-10                 | ConvNet-3     | 69.5                   | <b>74.5</b> | 68.9        | 62.0        |
| (50×32×32)               | ResNet-10     | 65.7                   | <b>72.4</b> | 62.9        | 59.1        |
| ImageNet-10              | ResNetAP-10   | 65.5                   | 73.3        | 76.6        | <b>77.5</b> |
| (20×224×224)             | ResNet-18     | 66.0                   | 70.8        | <b>75.7</b> | 75.2        |

the synthesized data. In this subsection, we compare our end-to-end optimization framework to a post-downsampling approach which reduces the resolution of the optimized synthetic data and resizes the data to the original size at evaluation. Table [6](#page-7-2) shows IDC significantly outperforms IDC-I with post-downsampling under the same number of stored pixels, even approaching the performance of IDC-I without downsampling which stores 4 times more pixels. This result verifies the effectiveness of the end-to-end approach considering the formation function during the optimization process, *i.e.*, finding the optimal condensed data given a fixed formation function.

On Multi-Formation Factor. We further study the effect of multi-formation factor (*i.e.*, upsampling factor). Table [7](#page-7-3) summarizes the test accuracy of condensed data with different multi-formation factors on various data scales. Note, the higher multi-formation factor results in a larger number of synthetic data but each with a lower resolution. Table [7](#page-7-3) shows that datasets have different optimal multi-formation factors; 2 is optimal for CIFAR-10 and 3-4 are optimal for ImageNet. These results mean that there is a smaller room for trading off resolution in the case of CIFAR-10 than ImageNet where the input size is much larger.

### 5.3. Application: Continual Learning

Recent continual learning approaches include the process of constructing a small representative subset of data that has been seen so far and training it with newly observed data [\(Rebuffi et al.,](#page-9-16) [2017;](#page-9-16) [Bang et al.,](#page-9-19) [2021\)](#page-9-19). This implies that the quality of the data subset is bound to affect the continual learning performance. In this section, we utilize the condensed data as exemplars for the previously seen classes or tasks and evaluate its effectiveness under the two types of continual learning settings: class incremental and task incremental [\(Zhao & Bilen,](#page-10-5) [2021a](#page-10-5)[;b\)](#page-10-1). Due to lack of space, we describe the detailed settings and results of task incremental setting in Appendix [C.3.](#page-12-3)

We follow the class incremental setting from [Zhao & Bilen](#page-10-5) [\(2021a\)](#page-10-5), where the CIFAR-100 dataset is given across 5 steps with a memory budget of 20 images per class. This setting trains a model continuously and purely on the latest data memory at each stage [\(Prabhu et al.,](#page-9-20) [2020\)](#page-9-20). We synthesize the exemplars by only using the data samples of currently available classes at each stage with ConvNet-3. We evaluate the condensed data on two types of networks, ConvNet-3 and ResNet-10, and compare our condensation methods with Herding, DSA, and DM.

Figure [7](#page-8-0) shows that IDC-I and IDC are superior to other baselines, both in ConvNet-3 and ResNet-10. Particularly, our multi-formation approach considerably increases the performance by over 10%p on average. In addition, from the results on ResNet-10, we find that DSA and DM do not maintain their performance under the network transfer, whereas our condensation methods outperform the baselines regardless of the networks types. That is, it is possible to efficiently condense data with small networks (ConvNet-3) and use the data on deeper networks when using our methods.

## 6. Related Work

One of the classic approaches to establishing a compact representative subset of a huge dataset is coreset selection [\(Phillips,](#page-9-4) [2016;](#page-9-4) [Toneva et al.,](#page-10-11) [2019\)](#page-10-11). Rather than selecting a subset, [Maclaurin et al.](#page-9-21) [\(2015\)](#page-9-21) originally proposed synthesizing a training dataset by optimizing the training performance. Following the work, [Such et al.](#page-9-22) [\(2020\)](#page-9-22) introduce generative modeling for the synthetic dataset. However, these works do not consider storage efficiency. The seminal work by [Wang et al.](#page-10-2) [\(2018\)](#page-10-2) studies synthesizing small training data with a limited storage budget. Building on this work, [Sucholutsky & Schonlau](#page-9-23) [\(2021\)](#page-9-23) attempt to co-optimize soft labels as well as the data, but they suffer from overfitting. Subsequently, [Nguyen et al.](#page-9-6) [\(2021\)](#page-9-6) formulate the problem as kernel ridge regression and optimize the data based on neural tangent kernel. However, this approach requires hundreds of GPUs for condensation. [Zhao et al.](#page-10-3) [\(2021\)](#page-10-3) propose a scalable algorithm by casting the original bi-level optimization as a simpler matching problem. Following the work, [Zhao & Bilen](#page-10-1) [\(2021b\)](#page-10-1) exploit siamese augmentation to improve performance, and [Zhao & Bilen](#page-10-5) [\(2021a\)](#page-10-5) suggest feature matching to accelerate optimization. Concurrently, [Cazenavette et al.](#page-9-24) [\(2022\)](#page-9-24) proposes to optimize the condensed data by matching training trajectories on the networks trained on real data.

Discussion on Dataset Structure In this work, we constrain the condensation optimization variables (*i.e.*, S)

Image /page/8/Figure/7 description: This figure contains two line graphs side-by-side, labeled (a) ConvNet-3 and (b) ResNet-10. Both graphs plot Test Accuracy on the y-axis against the Number of classes on the x-axis, with values ranging from 20 to 100 for the x-axis and 20 to 70 for the y-axis. A legend at the top indicates five different lines: Herding (purple), DSA (green), DM (yellow), IDC-I (brown), and IDC (red). In graph (a), the IDC line starts at approximately 69 and ends at 50, the IDC-I line starts at approximately 60 and ends at 43, the DM line starts at approximately 56 and ends at 39, the Herding line starts at approximately 49 and ends at 33, and the DSA line starts at approximately 38 and ends at 31. In graph (b), the IDC line starts at approximately 68 and ends at 45, the IDC-I line starts at approximately 55 and ends at 36, the DM line starts at approximately 53 and ends at 34, the Herding line starts at approximately 47 and ends at 32, and the DSA line starts at approximately 37 and ends at 20.

<span id="page-8-0"></span>Figure 7. Top-1 test accuracy of continual learning with condensed exemplars on CIFAR-100.

to have the same shape as the original training data. This enables us to design an intuitive and efficient formation function that has negligible computation and storage overhead. However, if we deviate from pursuing the same shape, there exist a variety of considerable condensed data structures. For example, we can parameterize a dataset as dictionary phrase coding or neural network generator [\(Mairal et al.,](#page-9-25) [2009;](#page-9-25) [Goodfellow et al.,](#page-9-26) [2014\)](#page-9-26). Nonetheless, it is not trivial to tailor these approaches for efficient data condensation. That is, it may require more storage or expensive computation costs for synthesis. For example, [Sitzmann et al.](#page-9-27) [\(2020\)](#page-9-27) use multi-layer neural networks that require much more storage than a single image to completely reconstruct a single image.

# 7. Conclusion

In this study, we address difficulties in optimization and propose a novel framework and techniques for dataset condensation. We propose a multi-formation process that defines enlarged and regularized data space for synthetic data optimization. We further analyze the shortcomings of the existing gradient matching algorithm and provide effective solutions. Our algorithm optimizes condensed data that achieve state-of-the-art performance in various experimental settings including speech domain and continual learning.

# Acknowledgement

We are grateful to Junsuk Choe for helpful discussions. This work was supported by SNU-NAVER Hyperscale AI Center, Institute of Information & Communications Technology Planning & Evaluation (IITP) grant funded by the Korea government (MSIT) (No. 2020-0-00882, (SW STAR LAB) Development of deployable learning intelligence via selfsustainable and trustworthy machine learning and No. 2022- 0-00480, Development of Training and Inference Methods for Goal-Oriented Artificial Intelligence Agents), and Basic Science Research Program through the National Research Foundation of Korea (NRF) (2020R1A2B5B03095585). Hyun Oh Song is the corresponding author.

# References

- <span id="page-9-19"></span>Bang, J., Kim, H., Yoo, Y., Ha, J.-W., and Choi, J. Rainbow memory: Continual learning with a memory of diverse samples. In *CVPR*, 2021.
- <span id="page-9-9"></span>Bishop, C. M. *Pattern recognition and machine learning*. springer, 2006.
- <span id="page-9-2"></span>Brown, T. B., Mann, B., Ryder, N., Subbiah, M., Kaplan, J., Dhariwal, P., Neelakantan, A., Shyam, P., Sastry, G., Askell, A., et al. Language models are few-shot learners. In *NeurIPS*, 2020.
- <span id="page-9-24"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Dataset distillation by matching training trajectories. *arXiv preprint arXiv:2203.11932*, 2022.
- <span id="page-9-3"></span>Cubuk, E. D., Zoph, B., Mane, D., Vasudevan, V., and Le, Q. V. Autoaugment: Learning augmentation strategies from data. In *CVPR*, 2019.
- <span id="page-9-7"></span>Deng, J., Dong, W., Socher, R., Li, L.-J., Li, K., and Fei-Fei, L. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-9-29"></span>Dong, C., Loy, C. C., and Tang, X. Accelerating the superresolution convolutional neural network. In *ECCV*, pp. 391–407. Springer, 2016.
- <span id="page-9-15"></span>Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*, 2021.
- <span id="page-9-26"></span>Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial nets. In *NeurIPS*, 2014.
- <span id="page-9-17"></span>He, K., Zhang, X., Ren, S., and Sun, J. Deep residual learning for image recognition. In *CVPR*, 2016.
- <span id="page-9-18"></span>Huang, G., Liu, Z., Van Der Maaten, L., and Weinberger, K. Q. Densely connected convolutional networks. In *CVPR*, 2017.
- <span id="page-9-5"></span>Huang, J. and Mumford, D. Statistics of natural images and models. In *CVPR*, 1999.
- <span id="page-9-12"></span>Jastrzebski, S., Kenton, Z., Ballas, N., Fischer, A., Bengio, Y., and Storkey, A. On the relation between the sharpest directions of dnn loss and the sgd step length. In *ICLR*, 2019.
- <span id="page-9-10"></span>Kim, J.-H., Choo, W., and Song, H. O. Puzzle mix: Exploiting saliency and local statistics for optimal mixup. In *International Conference on Machine Learning (ICML)*, 2020.

- <span id="page-9-11"></span>Kim, J.-H., Choo, W., Jeong, H., and Song, H. O. Co-mixup: Saliency guided joint mixup with supermodular diversity. In *ICLR*, 2021.
- <span id="page-9-13"></span>Krizhevsky, A., Hinton, G., et al. Learning multiple layers of features from tiny images. *Citeseer*, 2009.
- <span id="page-9-0"></span>LeCun, Y., Bengio, Y., and Hinton, G. Deep learning. *Nature*, 521(7553), 2015.
- <span id="page-9-28"></span>Li, Z. and Hoiem, D. Learning without forgetting. *IEEE transactions on pattern analysis and machine intelligence*, 40(12), 2017.
- <span id="page-9-21"></span>Maclaurin, D., Duvenaud, D., and Adams, R. Gradientbased hyperparameter optimization through reversible learning. In *ICML*, 2015.
- <span id="page-9-25"></span>Mairal, J., Bach, F., Ponce, J., and Sapiro, G. Online dictionary learning for sparse coding. In *ICML*, 2009.
- <span id="page-9-8"></span>McLachlan, G. J. and Krishnan, T. *The EM algorithm and extensions*, volume 382. John Wiley & Sons, 2007.
- <span id="page-9-6"></span>Nguyen, T., Novak, R., Xiao, L., and Lee, J. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021.
- <span id="page-9-14"></span>Parisi, G. I., Kemker, R., Part, J. L., Kanan, C., and Wermter, S. Continual lifelong learning with neural networks: A review. *Neural Networks*, 113, 2019.
- <span id="page-9-1"></span>Patterson, D., Gonzalez, J., Le, Q., Liang, C., Munguia, L.- M., Rothchild, D., So, D., Texier, M., and Dean, J. Carbon emissions and large neural network training. *arXiv preprint arXiv:2104.10350*, 2021.
- <span id="page-9-4"></span>Phillips, J. M. Coresets and sketches. *arXiv preprint arXiv:1601.00617*, 2016.
- <span id="page-9-20"></span>Prabhu, A., Torr, P. H., and Dokania, P. K. Gdumb: A simple approach that questions our progress in continual learning. In *ECCV*, 2020.
- <span id="page-9-16"></span>Rebuffi, S.-A., Kolesnikov, A., Sperl, G., and Lampert, C. H. icarl: Incremental classifier and representation learning. In *CVPR*, 2017.
- <span id="page-9-27"></span>Sitzmann, V., Martel, J. N., Bergman, A. W., Lindell, D. B., and Wetzstein, G. Implicit neural representations with periodic activation functions. In *NeurIPS*, 2020.
- <span id="page-9-22"></span>Such, F. P., Rawal, A., Lehman, J., Stanley, K., and Clune, J. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020.
- <span id="page-9-23"></span>Sucholutsky, I. and Schonlau, M. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, 2021.

- <span id="page-10-10"></span>Tan, M. and Le, Q. Efficientnet: Rethinking model scaling for convolutional neural networks. In *ICML*, 2019.
- <span id="page-10-9"></span>Tian, Y., Krishnan, D., and Isola, P. Contrastive multiview coding. In *ECCV*, 2020.
- <span id="page-10-11"></span>Toneva, M., Sordoni, A., Combes, R. T. d., Trischler, A., Bengio, Y., and Gordon, G. J. An empirical study of example forgetting during deep neural network learning. In *ICLR*, 2019.
- <span id="page-10-2"></span>Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-6"></span>Warden, P. Speech commands: A dataset for limited-vocabulary speech recognition. *arXiv preprint arXiv:1804.03209*, 2018.
- <span id="page-10-8"></span>Welling, M. Herding dynamical weights to learn. In *ICML*, 2009.
- <span id="page-10-7"></span>Yun, S., Han, D., Oh, S. J., Chun, S., Choe, J., and Yoo, Y. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *ICCV*, 2019.
- <span id="page-10-4"></span>Zhang, Y., Pezeshki, M., Brakel, P., Zhang, S., Bengio, C. L. Y., and Courville, A. Towards end-to-end speech recognition with deep convolutional neural networks. *arXiv preprint arXiv:1701.02720*, 2017.
- <span id="page-10-5"></span>Zhao, B. and Bilen, H. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021a.
- <span id="page-10-1"></span>Zhao, B. and Bilen, H. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021b.
- <span id="page-10-3"></span>Zhao, B., Mopuri, K. R., and Bilen, H. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-10-0"></span>Zoph, B. and Le, Q. V. Neural architecture search with reinforcement learning. In *ICLR*, 2017.

# A. Theoretical Analysis

<span id="page-11-0"></span>

## A.1. Proofs

**Definition 1.** A function  $D : \mathcal{D} \times \mathcal{D} \rightarrow [0, \infty)$  is a dataset *distance measure, if it satisfies the followings:*  $\forall X, X' \in \mathcal{D}$ *where*  $X = \{d_i\}_{i=1}^k$ ,  $\forall i \in [1, \ldots, k]$ ,

- *1.*  $D(X, X) = 0$  and  $D(X, X') = D(X', X)$ .
- 2.  $\forall d \in \mathbb{R}^m$  *s.t. d is closer to* X' *than*  $d_i$ ,  $D(X \setminus \{d_i\} \cup$  $\{d\}, X' \leq D(X, X').$
- <span id="page-11-2"></span>*3.*  $D(X, X' \cup \{d_i\}) \leq D(X, X').$

Note, we say data d is closer to dataset  $X = \{d_i\}_{i=1}^k$  than  $d'$ , if  $\forall i \in [1, ..., k], ||d - d_i|| ≤ ||d' - d_i||.$ 

**Proposition 1.** If  $N^{n'} \subseteq M_f$ , then for any dataset distance *measure* D*,*

$$
\min_{\mathcal{S}\in\mathbb{R}^{n\times m}} D(f(\mathcal{S}),\mathcal{T}) \leq \min_{\mathcal{S}\in\mathbb{R}^{n\times m}} D(\mathcal{S},\mathcal{T}).
$$

*Proof.* For simplicity, we denote  $[1, \ldots, n]$  as  $[n]$ . Let us denote  $\mathcal{T} = \{t_i\}_{i=1}^{n_t}$  and  $\mathcal{S} = \{s_j\}_{j=1}^n$ , where  $t_i \in \mathcal{N} \subset$  $\mathbb{R}^m$  and  $s_j \in \mathbb{R}^m$ ,  $\forall i \in [n_t]$  and  $\forall j \in [n]$ . Under the assumption that  $N$  is a subspace of  $\mathbb{R}^m$ , there exists the projection of  $s_j$  onto  $\mathcal{N}, \bar{s}_j \in \mathcal{N}$ . Because  $t_i \in \mathcal{N}$  for  $i = 1, ..., n_t, ||\bar{s}_j - t_i|| \leq ||s_j - t_i||, \forall j \in [n]$  and  $\forall i \in [n_t]$ . This means the projection  $\bar{s}_j$  is closer to  $\mathcal T$  than  $s_j, \forall j \in [n]$ . Let us define a partially projected dataset  $\bar{S}_k = {\bar{s}_j}_{j=1}^k \cup$  ${s_j}_{j=k+1}^n$ . Then by the second axiom of Definition [1,](#page-11-2)

$$
D(\bar{S}_n, \mathcal{T}) \leq D(\bar{S}_{n-1}, \mathcal{T}) \leq \ldots \leq D(\mathcal{S}, \mathcal{T}).
$$

This result means that the optimum  $S^* = \argmin D(S, \mathcal{T})$ satisfies  $S^* \in \mathcal{N}^n$ . Note our multi-formation augments the number of data from *n* to *n'* where  $n < n'$ . Let us denote  $k' = n' - n$  and  $S^*_{add} = S^* \cup \{t_i\}_{i=1}^{k'}$ . By the third axiom of Definition [1,](#page-11-2)

$$
D(S^*_{add}, \mathcal{T}) \leq D(\mathcal{S}^*, \mathcal{T}).
$$

The elements of  $S^*_{add}$  lie in  $\mathcal N$  and  $S^*_{add} \in \mathcal N^{n'}$ . From the assumption  $\mathcal{N}^{n'} \subseteq \mathcal{M}_f$ ,  $\exists \mathcal{S} \in \mathbb{R}^{n \times m}$  s.t.  $f(\mathcal{S}) = S^*_{add}$ . Thus,

$$
\min_{S \in \mathbb{R}^{n \times m}} D(f(S), T) \le D(S_{add}^*, T)
$$

$$
\le D(S^*, T) = \min_{S \in \mathbb{R}^{n \times m}} D(S, T).
$$

**Proposition 2.** Let  $w_t \in \mathbb{R}^K$  and  $h_t \in \mathbb{R}^W$  each denote *the convolution weights and hidden features at the* t *th layer* given the input data x. Then, for a loss function  $\ell$ ,  $\frac{d\ell(x)}{dw}$  =  $\sum_i a_{t,i} h_{t,i}$ , where  $h_{t,i} \in \mathbb{R}^K$  denotes the i<sup>th</sup> convolution  $\sum_i a_{t,i} h_{t,i}$ , where  $h_{t,i} \in \mathbb{R}^K$  denotes the i<sup>th</sup> convolution patch of  $h_t$  and  $a_{t,i} = \frac{d\ell(x)}{dw_t^{\intercal} h_{t,i}} \in \mathbb{R}$ .

*Proof.* To clarify, we note that we are learning flipped kernel during training. Then the convolution output of the  $t<sup>th</sup>$  layer  $o_t$  becomes  $[w_t^\mathsf{T} h_{t,1}, \dots, w_t^\mathsf{T} h_{t,n_o}]$ , where  $n_o = W - K - 1$ denotes the number of convolution patches given the convolution stride of 1. Then from the chain rule,

$$
\frac{d\ell(x)}{dw_t} = \frac{d\ell(x)}{do_t} \frac{do_t}{dw_t}
$$

$$
= \left[\frac{d\ell(x)}{dw_t^{\tau}h_{t,1}}, \dots, \frac{d\ell(x)}{dw_t^{\tau}h_{t,n_o}}\right] [h_{t,1}, \dots, h_{t,n_o}]^{\text{T}}
$$

$$
= \sum_{i=1}^{n_0} \frac{d\ell(x)}{dw_t^{\tau}h_{t,i}} h_{t,i}.
$$

 $\Box$ 

<span id="page-11-1"></span>

### A.2. Proposition 1 with Relaxed Assumption

In Proposition [1,](#page-3-1) we assume  $\mathcal{N}^{n'} \subseteq \mathcal{M}_f$  that the syntheticdataset space by  $f$  is sufficiently large to contain all data points in  $N$ . Relaxing the assumption, we consider when  $\mathcal{M}_f$  approximately covers  $\mathcal{N}^{n'}$ . With the following notion of  $\epsilon$ -cover, we describe the trade-off between the effects from the increase in the number of data and the decrease in representability of the synthetic datasets.

**Definition 2.** *Given a dataset distance measure D,*  $M_f$ *is a*  $\epsilon$ -cover of  $\mathcal{N}^{n'}$  on  $D$  *if*  $\forall X' \in \mathcal{N}^{n'}$ ,  $\exists S \in \mathbb{R}^{n \times m}$  *s.t.*  $D(f(S), X') \leq \epsilon$ .

Here, we assume a dataset distance measure  $D$  satisfies the triangular inequality. From the proof above in Proposition 1, ∃ $S_{add}^*$  ∈  $\mathcal{N}^{n^7}$  *s.t.*  $D(S_{add}^*, \mathcal{T})$  ≤ min<sub>S∈ℝ<sup>n</sup>×m</sub>  $D(\mathcal{S}, \mathcal{T})$ . Let us denote the gain  $G = \min_{S} D(S, \mathcal{T}) - D(S^*_{add}, \mathcal{T})$ . If  $\mathcal{M}_f$ is a  $\epsilon$ -cover of  $\mathcal{N}^{n'}$  on D, then  $\exists \mathcal{S} \in \mathbb{R}^{n \times m}$  s.t.

$$
D(f(S), \mathcal{T}) \le D(S_{add}^*, \mathcal{T}) + D(f(S), S_{add}^*)
$$
  
$$
\le D(S_{add}^*, \mathcal{T}) + \epsilon.
$$

Note, we use the triangular inequality in the first inequality above and use the definition of  $\epsilon$ -cover in the second inequality. We can conclude that

$$
\min_{\mathcal{S} \in \mathbb{R}^{n \times m}} D(f(\mathcal{S}), \mathcal{T}) \le D(S_{add}^*, \mathcal{T}) + \epsilon
$$
$$
= \min_{\mathcal{S} \in \mathbb{R}^{n \times m}} D(\mathcal{S}, \mathcal{T}) - G + \epsilon.
$$

To summarize, the optimization with multi-formation function f can generate a synthetic dataset that has at least  $G - \epsilon$  smaller distance to the original data T compared to when not using  $f$ . We can interpret  $G$  as a possible gain by the increase in the number of data, *i.e.*, from  $n$  to  $n'$ , and  $\epsilon$  as the representability loss in parameterization by f. This formulates a new research problem on data condensation: parameterization of a larger synthetic dataset that sufficiently satisfies the data regularity conditions.

<span id="page-12-2"></span>

# B. Datasets

ImageNet-subset. Many recent works in machine learning evaluate their proposed algorithms using subclass samples from IamgeNet to validate the algorithms on large-scale data with a reasonable amount of computation resources [\(Rebuffi et al.,](#page-9-16) [2017;](#page-9-16) [Tian et al.,](#page-10-9) [2020\)](#page-10-9). In our ImageNet experiment, we borrow a subclass list containing 100 classes from [Tian et al.](#page-10-9)  $(2020)^1$  $(2020)^1$  $(2020)^1$ . We use the first 10 classes from the list in our ImageNet-10 experiments. We performed the experiments after preprocessing the images to a fixed size of  $224 \times 224$  using resize and center crop functions.

Mini Speech Commands. This dataset contains onesecond audio clips of 8 command classes, which is a subset of the original Speech Commands dataset [\(Warden,](#page-10-6) [2018\)](#page-10-6). The dataset consists of 1,000 samples for each class. We split the dataset randomly and use 7 of 8 as training data and 1 of 8 as test data. We downloaded the Mini Speech Commands from the official TensorFlow page<sup>[2](#page-12-5)</sup>. Following the guideline provided on the official page, we load the audio clips with 16,000 sampling rates and process the waveform data with Short-time Fourier transform to obtain the magnitude spectrogram of size  $128 \times 125$ . Then, we apply zero-padding and perform downsampling to reduce the input size to  $64 \times 64$ . Finally, we use log-scale magnitude spectrograms for experiments.

## C. Implementation Details

<span id="page-12-0"></span>

### C.1. Ours

In all of the experiments, we fix the number of inner iterations  $M = 100$  (Algorithm [1\)](#page-4-2). For CIFAR-10, we use data learning rate  $\lambda = 0.005$ , network learning rate  $\eta = 0.01$ , and the MSE objective. For other datasets, we use  $L^1$  matching objective. For ImageNet-10, we use data learning rate  $\lambda = 0.003$  and network learning rate  $\eta = 0.01$ . For ImageNet-100, we use data learning rate  $\lambda = 0.001$  and network learning rate  $\eta = 0.1$ . For the speech dataset, we use data learning rate  $\lambda = 0.003$  and network learning rate  $\eta = 0.0003$ . Rather than a single random augmentation strategy by DSA, we use a sequence of color transform, crop, and cutout for gradient matching. We train networks that are used for the matching with a sequence of color transform, crop, and CutMix [\(Yun et al.,](#page-10-7) [2019\)](#page-10-7). Following [Zhao](#page-10-1) [& Bilen](#page-10-1) [\(2021b\)](#page-10-1), we initialize the synthetic data as random real training data samples, which makes the optimization faster compared to the random noise initialization.

We follow the evaluation setting by DSA in the case of CIFAR-10 [\(Zhao & Bilen,](#page-10-1) [2021b\)](#page-10-1). We train neural networks

on the condensed data for 1,000 epochs with a 0.01 learning rate. We use the DSA augmentation and CutMix. Note, we apply CutMix for other baselines unless it degrades the performance. In the case of ImageNet, we train networks on the condensed data by using random resize-crop and CutMix. We use 0.01 learning rate and train models until convergence: 2,000 epochs for 10 image/class and 1,500 epochs for 20 image/class. We use an identical evaluation strategy for all cases in Table [3.](#page-6-0)

<span id="page-12-1"></span>

#### C.2. Baselines

In the case of DSA [\(Zhao & Bilen,](#page-10-1) [2021b\)](#page-10-1), we download the author-released condensed dataset and evaluate the dataset<sup>[3](#page-12-6)</sup>. We train neural networks by following the training strategy from the official Github codes. We find that evaluation with CutMix degrades the performance of DSA, and report better results without CutMix in Table [1.](#page-5-0) For all of the other baselines, we use an identical evaluation strategy to ours.

In the case of DM [\(Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5), the codes are not released. Following the paper, we implemented the algorithm and tuned learning rates. As a result, we obtain superior performance than the reported values in [Zhao](#page-10-5) [& Bilen](#page-10-5) [\(2021a\)](#page-10-5). Specifically, the original paper reports CIFAR-10 performance on ConvNet-3 of 63.0 whereas we obtain the performance of 65.6 (50 images per class). We report our improved results of DM in Table [1.](#page-5-0)

<span id="page-12-3"></span>

#### C.3. Continual Learning

Class Incremental Setting. We reproduce the result based on the author released condensed dataset by [Zhao &](#page-10-1) [Bilen](#page-10-1) [\(2021b\)](#page-10-1). Instead of training the network from scratch as in previous works [\(Prabhu et al.,](#page-9-20) [2020;](#page-9-20) [Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5), we adopt distillation loss described in [Li & Hoiem](#page-9-28) [\(2017\)](#page-9-28) and train continuously by loading weights from the previous step and expanding the output dimension of the last fully-connected layer, which is a more realistic scenario in continual learning [\(Rebuffi et al.,](#page-9-16) [2017\)](#page-9-16). We train the model for 1000 epochs each stage using SGD with a learning rate of 0.01, decaying by a factor of 0.2 at epochs 600 and 800. We use 0.9 for momentum and 0.0005 for weight decay.

Task Incremental Setting. We follow the task incremental setting by [Zhao & Bilen](#page-10-1) [\(2021b\)](#page-10-1), which consists of three digit-datasets (SVHN  $\rightarrow$  MNIST  $\rightarrow$  USPS). At each training stage, a new set of the corresponding data is provided, whereas the previously seen datasets are prohibited except for a few exemplars. We compare our methods with Herding and DSA, excluding DM where the data under this setting is not released. As shown in Figure [8,](#page-13-3) we verify that our condensed data significantly outperform the baselines.

<span id="page-12-5"></span><span id="page-12-4"></span><sup>1</sup><https://github.com/HobbitLong/CMC>

<sup>2</sup>[https://www.tensorflow.org/tutorials/](https://www.tensorflow.org/tutorials/audio/simple_audio) [audio/simple\\_audio](https://www.tensorflow.org/tutorials/audio/simple_audio)

<span id="page-12-6"></span><sup>3</sup>[https://github.com/VICO-UoE/](https://github.com/VICO-UoE/DatasetCondensation) [DatasetCondensation](https://github.com/VICO-UoE/DatasetCondensation)

Image /page/13/Figure/1 description: This image contains two line graphs side-by-side, labeled (a) ConvNet-3 and (b) ResNet-10. Both graphs plot Test Accuracy on the y-axis against Stage on the x-axis, with stages numbered 1, 2, and 3. A legend at the top indicates four lines: Herding (blue), DSA (green), IDC-I (brown), and IDC (red). In graph (a), all lines start around 96% accuracy at Stage 1. They decrease to around 95% at Stage 2, with Herding being the lowest. At Stage 3, IDC shows the highest accuracy at approximately 96.7%, followed by IDC-I at 96.2%, DSA at 95.8%, and Herding at 95.2%. In graph (b), all lines also start around 97.3% accuracy at Stage 1. They decrease to around 96.5% at Stage 2. At Stage 3, IDC shows the highest accuracy at approximately 97.9%, followed by IDC-I at 97.4%, DSA at 97.2%, and Herding at 97.1%. Error bars are present for each data point.

Figure 8. Continual learning performance with exemplars on digit datasets (SVHN-MNIST-USPS).

<span id="page-13-0"></span>

# D. Other Multi-Formation Functions

In this section, we study other types of multi-formation functions that are worth considering under our framework.

Multi-Scale Multi-Formation Function. The synthetic data by the uniform multi-formation function do not share data elements with each other (Figure [3\)](#page-2-2). Here, we design a multi-scale formation function that increases the number of synthetic data by sharing condensed data elements across multiple synthetic data (right subfigure in Figure [3\)](#page-2-2). Table [8](#page-13-4) compares the test accuracy to the default formation function on CIFAR-10. The table shows that the multi-scale approach outperforms the uniform formation function under the small storage budget where the uniform approach does not create sufficiently many synthetic data.

Learnable Multi-Formation Function. We further study the potential direction of exploiting learnable multiformation function which can synthesize diverse representative images at the cost of additional computation overhead and storage. In this experiment, we replace the upsampling by a learnable function using Fast Super-Resolution Convolutional Neural Networks (FSRCNN) with a reduced number of parameters [\(Dong et al.,](#page-9-29) [2016\)](#page-9-29). Table [9](#page-13-5) summarizes the condensation performance of learnable multi-formation function with different factors on CIFAR-10. While the extra learnable module does not improve performance with the formation factor of 2, it improves the performance with the factor of 3. We conjecture that the upsampling can generate sufficiently informative synthetic data in the lower factor, but suffers from the lack of representability in the higher factor. In such scenarios with the larger factor, the learnable multi-formation function shows promising results.

## E. Additional Experiments

<span id="page-13-1"></span>

### E.1. Experimental Results on Other Datasets

We evaluate our method on SVHN, MNIST, FashionMNIST, and CIFAR-10 including 1 img/cls setting and verify that

<span id="page-13-4"></span>Table 8. Test performance comparison of the uniform and multiscale formation functions on CIFAR-10.

| Pixel/Class                    | Test Model | Uniform (default) | Multi-Scale |
|--------------------------------|------------|-------------------|-------------|
| $10\times32\times32$<br>(0.2%) | ConvNet-3  | 67.5              | <b>69.2</b> |
|                                | ResNet-10  | 63.5              | <b>64.8</b> |
| $50\times32\times32$<br>(1.0%) | ConvNet-3  | <b>74.5</b>       | 73.1        |
|                                | ResNet-10  | <b>72.4</b>       | 69.7        |

<span id="page-13-5"></span><span id="page-13-3"></span>Table 9. Condensation performance comparison of learnable multiformation functions to upsampling (10 images per class on CIFAR-10). *CN* denotes ConvNet-3 and *RN* denotes ResNet-10.

| Test<br>Model | Factor 2 |        | Factor 3 |        |
|---------------|----------|--------|----------|--------|
|               | Upsample | FSRCNN | Upsample | FSRCNN |
| CN            | 67.5     | 66.2   | 66.7     | 67.9   |
| RN            | 63.5     | 62.0   | 60.6     | 64.4   |

our methods consistently outperform baselines. Table [10](#page-14-3) shows multi-formation is much more effective at low compression rates (1 img/cls) and improves performance by up to 30%p (on SVHN) compared to the best baseline. We also find that the effect of multi-formation is diminishing at (FashionMNIST, 50 img/cls) where IDC-I is the best. We conjecture that the representation loss by multi-formation at this point is greater than the gain by an increased number of data, which can be backed up by analysis in Appendix [A.2.](#page-11-1)

<span id="page-13-2"></span>

### E.2. Isolated Effect of Strong Augmentation

We conduct an ablation study investigating the effect of strong augmentation (S.A.), i.e., CutMix, in Table [11.](#page-14-4) We implement our algorithm without S.A. and evaluate all baselines under the two evaluation strategies: with or without S.A. The table shows that the gain by S.A. is only about 1%p whereas the gain by multi-formation and algorithmic development is about 14%p and 9%p (by comparing IDC *w/o* S.A. with DSA and DM). The result verifies that our algorithm does not mainly rely on augmentation.

### E.3. Larger Data Storage

In this subsection, we measure the performance of condensation with larger storage budgets. Figure [9](#page-14-5) shows the performance of condensed data with large storage budgets of up to 500 images per class on CIFAR-10. The figure shows that IDC outperforms other methods under the storage budgets of 200 images per class, however, IDC underperforms at 500 images per class. This result indicates that increasing the number of synthetic data via multi-formation shows diminishing returns when there are enough storage budgets to represent the original training data diversity (see Appendix [A.2](#page-11-1) for theoretical analysis). Nonetheless, IDC-I outperforms baselines in all settings, demonstrating the effectiveness of our condensation algorithm with large storage budgets.

Dataset Condensation via Efficient Synthetic-Data Parameterization

Table 10. Top-1 test accuracy of ConvNet-3 trained on condensed datasets (average score with 3 evaluation repetitions).

<span id="page-14-3"></span>

| Img/<br>Cls | SVHN                |                     |      |       |             | MNIST               |                     |      |       |             | FashionMNIST        |                     |      |       |             | CIFAR-10            |                     |      |       |             |
|-------------|---------------------|---------------------|------|-------|-------------|---------------------|---------------------|------|-------|-------------|---------------------|---------------------|------|-------|-------------|---------------------|---------------------|------|-------|-------------|
|             | DSA	ext{	extdagger} | KIP	ext{	extdagger} | DM   | IDC-I | IDC         | DSA	ext{	extdagger} | KIP	ext{	extdagger} | DM   | IDC-I | IDC         | DSA	ext{	extdagger} | KIP	ext{	extdagger} | DM   | IDC-I | IDC         | DSA	ext{	extdagger} | KIP	ext{	extdagger} | DM   | IDC-I | IDC         |
| 1           | 27.5                | 39.5                | 24.2 | 46.7  | <b>68.5</b> | 88.7                | 90.1                | 89.7 | 88.9  | <b>94.2</b> | 70.6                | 73.5                | 70.0 | 70.7  | <b>81.0</b> | 28.8                | 38.6                | 28.9 | 36.7  | <b>50.6</b> |
| 10          | 79.2                | 64.2                | 72.0 | 77.0  | <b>87.5</b> | 97.8                | 97.5                | 97.5 | 98.0  | <b>98.4</b> | 84.6                | 86.8                | 84.8 | 85.3  | 86.0        | 52.1                | 49.2                | 53.8 | 58.3  | <b>67.5</b> |
| 50          | 84.4                | 73.2                | 84.3 | 87.9  | <b>90.1</b> | 99.2                | 98.3                | 98.6 | 98.8  | <b>99.1</b> | 88.7                | 88.0                | 88.6 | 89.1  | 86.2        | 60.6                | 56.7                | 65.6 | 69.5  | <b>74.5</b> |

<span id="page-14-4"></span>Table 11. Ablation study on strong augmentation (S.A.), CIFAR-10 (ConvNet, 50 img/cls). We report bold values in Tables [1](#page-5-0) and [2.](#page-5-1) Evaluation *w/o* S.A. is identical to the method by DSA and DM.

| Evaluation | Random      | Herding     | DSA         | KIP         | DM          | IDC-I w/o S.A. | IDC-I       | IDC w/o S.A. | IDC         |
|------------|-------------|-------------|-------------|-------------|-------------|----------------|-------------|--------------|-------------|
| w/o S.A.   | 54.7        | 57.5        | <b>60.6</b> | 55.8        | 63.0        | 67.4           | 68.6        | 72.8         | 73.5        |
| w/ S.A.    | <b>56.5</b> | <b>59.8</b> | 59.5        | <b>57.9</b> | <b>65.6</b> | 67.0           | <b>69.5</b> | 74.3         | <b>74.5</b> |

Image /page/14/Figure/5 description: This is a line graph showing the test accuracy of different methods as a function of the number of images per class. The x-axis represents the number of images per class, with values ranging from 1 to 500. The y-axis represents the test accuracy, ranging from 0 to 100. There are four lines representing different methods: Random (purple), Herding (green), IDC-I (tan), and IDC (red). A dotted black line labeled 'Full' is also present, indicating a baseline accuracy. The graph shows that as the number of images per class increases, the test accuracy for all methods generally improves. The IDC method consistently achieves the highest accuracy among the tested methods, closely followed by IDC-I. Herding and Random methods show lower accuracy, with Random performing the worst. All methods approach the 'Full' accuracy level as the number of images per class increases, but none reach it within the tested range.

Figure 9. Top-1 test accuracy of ConvNet-3 trained on condensed datasets with increasing data storage (CIFAR-10).

<span id="page-14-1"></span>

#### E.4. Network Architecture Effect on Condensation

We analyze the effects of networks' architecture by comparing the performance of condensed data on CIFAR-10. In Table [12,](#page-14-6) we compare the performance of condensed data with different network architectures; simple convolution networks with various widths and depths, ResNet-10, and ResNetAP-10. Interestingly, simple ConvNets perform better than the deeper ResNet architectures on both test models. Furthermore, ConvNet-4 (*CN-D*) has lower condensation performance than ConvNet-3 (*CN*), although it has more convolution layers. The results indicate that a complex network is not always effective when compressing a large amount of learning information on a small storage capacity.

#### E.5. Multi-Formation with Another Algorithm

Our multi-formation strategy can be orthogonally applied to other condensation methods. To verify the generality of our strategy, we apply the multi-formation function on another condensation method, DM [\(Zhao & Bilen,](#page-10-5) [2021a\)](#page-10-5), which uses a feature matching objective. Table [13](#page-14-7) summarizes the test performance of condensed data on CIFAR-10. The table shows that our multi-formation framework consistently improves the performance of DM over various test models, demonstrating the general applicability of our framework.

<span id="page-14-0"></span>

# E.6. Dataset Condensation with ZCA

We implement ZCA following the official KIP code [\(Nguyen et al.,](#page-9-6) [2021\)](#page-9-6) and test IDC on CIFAR-10 (Table [14\)](#page-14-8). <span id="page-14-6"></span>Table 12. Condensation network architecture comparison (10 img/cls on CIFAR-10). *CN* denotes ConvNet-3, *CN-W* denotes ConvNet-3 with twice more channels (256), *CN-D* denotes ConvNet-4 (4 convolution layers), *RN* denotes ResNet-10, and *RN-AP* denotes ResNet-10 with average pooling instead of strided convolutions for downsampling. Note, we use instance normalization as in [Zhao et al.](#page-10-3) [\(2021\)](#page-10-3).

<span id="page-14-5"></span>

| Test Model | Condensation Network Architecture |      |      |      |      |
|------------|-----------------------------------|------|------|------|------|
|            | CN                                | CN-W | CN-D | RN   | RNAP |
| ConvNet-3  | 58.3                              | 58.8 | 56.6 | 51.4 | 53.5 |
| ResNet-10  | 50.2                              | 50.5 | 48.7 | 47.5 | 48.8 |

<span id="page-14-7"></span>Table 13. Test accuracy of feature matching objective by DM with our multi-formation strategy (DM+MF) on CIFAR-10.

| Pixel/Class                | Test Model | DM   | DM+MF | IDC  |
|----------------------------|------------|------|-------|------|
| 50 $\times$ 32 $\times$ 32 | ConvNet-3  | 65.6 | 68.4  | 74.5 |
| (1.0%)                     | ResNet-10  | 58.6 | 63.1  | 72.4 |

<span id="page-14-8"></span>Table 14. Effects of ZCA whitening on IDC (CIFAR-10 with ConvNet-3). Here, *S.A.* means strong augmentation.

| Pixel/Class | IDC w/o S.A. + ZCA | IDC + ZCA | IDC  |
|-------------|--------------------|-----------|------|
| 10×32×32    | 66.6               | 66.7      | 67.5 |
| 50×32×32    | 72.0               | 72.5      | 74.5 |

We find that ZCA results in mild degradation in performance. We speculate that ZCA whitening, which removes pixel correlation, is not suitable for IDC's upsampling process.

<span id="page-14-2"></span>

### F. Visual Examples

We provide visual examples of IDC on CIFAR-10, ImageNet, MNIST, and SVHN in the following pages. In Figures [10](#page-15-0) to [13,](#page-15-1) we compare our synthetic data samples to the real training data samples, which we used as initialization of the synthetic data. From the figure, we find that our synthesized data looks more class-representative. We provide the full condensed data in Figures [14](#page-16-0) to [17,](#page-16-1) under the storage budget of 10 images per class.

<span id="page-15-0"></span>Image /page/15/Figure/1 description: The image displays a comparison of real and synthetic images across three different datasets: MNIST, SVHN, and CIFAR-10. For MNIST, the top row shows real handwritten digits from 0 to 9, and the bottom row shows corresponding synthetic digits. For SVHN, the top row shows real street view house numbers, and the bottom row shows synthetic versions. For CIFAR-10, the top row displays real images of various objects like an airplane, car, bird, cat, dog, cow, ship, and building, while the bottom row shows their synthetic counterparts. The image also includes a section comparing real and synthetic images from ImageNet, featuring close-ups of objects like candies, a cabbage, parrots in a tree, a damaged car, and a honeycomb. Below these are corresponding synthetic versions. Finally, the last section shows real images of a dog, a gibbon, a tiled roof with a plant, chairs, and a green shed, with their synthetic versions below. Captions for each section are provided: "Figure 10. Comparison of real and synthetic images on MNIST.", "Figure 11. Comparison of real and synthetic images on SVHN.", "Figure 12. Comparison of real and synthetic images on CIFAR-10.", and "Figure 13. ImageNet; bottle cap, cabbage, lorikeet, car wheel, honeycomb, Shih-Tzu, gibbon, tile roof, rocking chair, and boat house."

<span id="page-15-1"></span>Figure 13. ImageNet: bottle cap, cabbage, lorikeet, car wheel, honeycomb, Shih-Tzu, gibbon, tile roof, rocking chair, and boat house.

Image /page/16/Picture/1 description: The image displays a grid of handwritten digits from the MNIST dataset. The digits are arranged in rows and columns, with each row dedicated to a specific digit from 0 to 9. There are 10 rows and 10 columns of digits, totaling 100 digits. The digits are white on a black background. The first row shows the digit 0, the second row shows the digit 1, the third row shows the digit 2, and so on, up to the tenth row which shows the digit 9. Some digits are clearer than others, with variations in thickness and style. The bottom of the image contains text that reads "Figure 14. Grid-wise images of MNIST dataset with IDC (10".

Figure 14. Condensed images of MNIST dataset with IDC (10  $\times$  $28 \times 28$  pixels per class).

Image /page/16/Picture/3 description: This is a grid of 100 images, each containing a single digit from 0 to 9. The digits are arranged in a 10x10 grid. The first row contains images of the digit 0, the second row contains images of the digit 1, and so on, up to the tenth row which contains images of the digit 9. Each digit is rendered in a variety of styles and colors, with some digits appearing more clearly than others. The overall impression is a collection of handwritten or generated digits.

Figure 16. Condensed images of SVHN dataset with IDC (10  $\times$  $32 \times 32$  pixels per class).

<span id="page-16-0"></span>Image /page/16/Picture/5 description: A grid of 100 images, arranged in 10 rows and 10 columns. The images appear to be samples from a dataset, with each image depicting a different object or animal. The top rows feature airplanes and cars, followed by various birds, cats, dogs, deer, horses, ships, and trucks. The images are small and square, with a black border separating each one. The overall impression is a diverse collection of visual data.

Figure 15. Condensed images of CIFAR-10 dataset with IDC ( $10 \times$  $32 \times 32$  pixels per class). Each row correspond to the condensed data of a single class. We list the class labels from the first row as follows: 1) airplane, 2) automobile, 3) bird, 4) cat, 5) deer, 6) dog, 7) frog, 8) horse, 9) ship, and 10) truck.

<span id="page-16-1"></span>Image /page/16/Picture/7 description: The image is a large grid of smaller images, arranged in 10 rows and 10 columns. The overall grid is a square. The smaller images depict a variety of subjects, including people (likely babies and children), animals (dogs, cats, horses, birds, insects), plants and nature scenes, and some man-made objects or abstract patterns. The images appear to be generated or sampled from a dataset, possibly for machine learning purposes, given the caption below the image which reads "Figure 17. Generated images of ImageNet-10 dataset with IDC."

Figure 17. Condensed images of ImageNet-10 dataset with IDC with a multi-formation factor of  $2(10 \times 224 \times 224$  pixels per class). Each row correspond to the condensed data of a single class. We list the class labels from the first row as follows: 1) poke bonnet, 2) green mamba, 3) langur, 4) Doberman pinscher, 5) gyromitra, 6) gazelle hound, 7) vacuum cleaner, 8) window screen, 9) cocktail shaker, and 10) garden spider.