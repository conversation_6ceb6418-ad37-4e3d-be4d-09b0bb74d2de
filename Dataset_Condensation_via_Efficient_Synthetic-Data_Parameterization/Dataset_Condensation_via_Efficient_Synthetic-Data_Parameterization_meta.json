{"table_of_contents": [{"title": "Dataset Condensation via Efficient Synthetic-Data Parameterization", "heading_level": null, "page_id": 0, "polygon": [[88.5, 89.25], [508.0078125, 89.25], [508.0078125, 103.7373046875], [88.5, 103.7373046875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 187.5], [195.8818359375, 187.5], [195.8818359375, 198.966796875], [148.5, 198.966796875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 496.5], [132.75, 496.5], [132.75, 506.98828125], [54.0, 506.98828125]]}, {"title": "2. Preliminary", "heading_level": null, "page_id": 1, "polygon": [[54.0, 527.25], [129.0, 527.25], [129.0, 539.0859375], [54.0, 539.0859375]]}, {"title": "3. Multi-Formation Framework", "heading_level": null, "page_id": 1, "polygon": [[306.0, 559.96875], [469.5, 559.96875], [469.5, 570.796875], [306.0, 570.796875]]}, {"title": "3.1. Observation", "heading_level": null, "page_id": 1, "polygon": [[306.0, 652.5], [377.71875, 652.5], [377.71875, 663.22265625], [306.0, 663.22265625]]}, {"title": "3.2. Multi-Formation", "heading_level": null, "page_id": 2, "polygon": [[54.0, 491.25], [145.5, 491.25], [145.5, 501.57421875], [54.0, 501.57421875]]}, {"title": "3.3. Theoretical Analysis", "heading_level": null, "page_id": 2, "polygon": [[306.0, 544.5], [412.681640625, 544.5], [412.681640625, 554.94140625], [306.0, 554.94140625]]}, {"title": "4. Improved Optimization Techniques", "heading_level": null, "page_id": 3, "polygon": [[54.0, 456.75], [249.0, 456.75], [249.0, 468.31640625], [54.0, 468.31640625]]}, {"title": "4.1. Interpretation", "heading_level": null, "page_id": 3, "polygon": [[54.0, 573.75], [134.25, 573.75], [134.25, 583.9453125], [54.0, 583.9453125]]}, {"title": "4.2. Problems and Solutions", "heading_level": null, "page_id": 3, "polygon": [[305.25, 427.5], [426.0, 427.5], [426.0, 437.765625], [305.25, 437.765625]]}, {"title": "4.3. <PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[54.0, 455.25], [117.75, 455.25], [117.75, 465.22265625], [54.0, 465.22265625]]}, {"title": "5. Experimental Results", "heading_level": null, "page_id": 4, "polygon": [[305.25, 615.0], [429.75, 615.0], [429.75, 625.7109375], [305.25, 625.7109375]]}, {"title": "5.1. Condensed Dataset Evaluation", "heading_level": null, "page_id": 5, "polygon": [[54.0, 334.5], [204.0, 334.5], [204.0, 344.759765625], [54.0, 344.759765625]]}, {"title": "5.2. Analysis", "heading_level": null, "page_id": 7, "polygon": [[54.0, 450.75], [109.5, 450.75], [109.5, 460.58203125], [54.0, 460.58203125]]}, {"title": "5.3. Application: Continual Learning", "heading_level": null, "page_id": 7, "polygon": [[306.0, 604.5], [466.5, 604.5], [466.5, 614.8828125], [306.0, 614.8828125]]}, {"title": "6. Related Work", "heading_level": null, "page_id": 8, "polygon": [[54.0, 383.25], [138.8056640625, 383.25], [138.8056640625, 394.259765625], [54.0, 394.259765625]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 411.75], [377.25, 411.75], [377.25, 423.45703125], [306.0, 423.45703125]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 8, "polygon": [[306.0, 555.0], [402.75, 555.0], [402.75, 566.54296875], [306.0, 566.54296875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.3740234375], [54.0, 79.3740234375]]}, {"title": "A. Theoretical Analysis", "heading_level": null, "page_id": 11, "polygon": [[54.0, 68.25], [175.5, 68.25], [175.5, 78.55224609375], [54.0, 78.55224609375]]}, {"title": "A.1. Proofs", "heading_level": null, "page_id": 11, "polygon": [[54.0, 87.75], [102.75, 87.75], [102.75, 99.4833984375], [54.0, 99.4833984375]]}, {"title": "A.2. Proposition 1 with Relaxed Assumption", "heading_level": null, "page_id": 11, "polygon": [[306.0, 248.25], [495.75, 248.25], [495.75, 258.0], [306.0, 258.0]]}, {"title": "B. Datasets", "heading_level": null, "page_id": 12, "polygon": [[54.0, 68.25], [112.5, 68.25], [112.5, 79.470703125], [54.0, 79.470703125]]}, {"title": "C. Implementation Details", "heading_level": null, "page_id": 12, "polygon": [[54.0, 407.25], [191.25, 407.25], [191.25, 418.4296875], [54.0, 418.4296875]]}, {"title": "C.1. <PERSON><PERSON>", "heading_level": null, "page_id": 12, "polygon": [[54.0, 427.5], [97.716796875, 427.5], [97.716796875, 438.5390625], [54.0, 438.5390625]]}, {"title": "C.2. Baselines", "heading_level": null, "page_id": 12, "polygon": [[306.0, 190.458984375], [367.5, 190.458984375], [367.5, 199.740234375], [306.0, 199.740234375]]}, {"title": "C.3. Continual Learning", "heading_level": null, "page_id": 12, "polygon": [[306.0, 406.5], [412.681640625, 406.5], [412.681640625, 416.8828125], [306.0, 416.8828125]]}, {"title": "<PERSON><PERSON> Other Multi-Formation Functions", "heading_level": null, "page_id": 13, "polygon": [[54.0, 242.25], [243.75, 242.25], [243.75, 253.6875], [54.0, 253.6875]]}, {"title": "<PERSON><PERSON> Additional Experiments", "heading_level": null, "page_id": 13, "polygon": [[54.0, 656.25], [193.5, 656.25], [193.5, 667.08984375], [54.0, 667.08984375]]}, {"title": "E.1. Experimental Results on Other Datasets", "heading_level": null, "page_id": 13, "polygon": [[54.0, 676.5], [246.75, 676.5], [246.75, 686.8125], [54.0, 686.8125]]}, {"title": "E.2. Isolated Effect of Strong Augmentation", "heading_level": null, "page_id": 13, "polygon": [[306.0, 407.25], [493.5, 407.25], [493.5, 417.26953125], [306.0, 417.26953125]]}, {"title": "E.3. Larger Data Storage", "heading_level": null, "page_id": 13, "polygon": [[306.0, 547.5], [415.5, 547.5], [415.5, 557.26171875], [306.0, 557.26171875]]}, {"title": "E.4. Network Architecture Effect on Condensation", "heading_level": null, "page_id": 14, "polygon": [[54.0, 365.25], [270.75, 365.25], [270.75, 375.310546875], [54.0, 375.310546875]]}, {"title": "E.5. Multi-Formation with Another Algorithm", "heading_level": null, "page_id": 14, "polygon": [[54.0, 538.5], [252.75, 538.5], [252.75, 548.75390625], [54.0, 548.75390625]]}, {"title": "E.6. Dataset Condensation with ZCA", "heading_level": null, "page_id": 14, "polygon": [[54.0, 675.75], [212.6162109375, 675.75], [212.6162109375, 686.8125], [54.0, 686.8125]]}, {"title": "<PERSON>. <PERSON> Examples", "heading_level": null, "page_id": 14, "polygon": [[305.25, 597.0], [405.75, 597.0], [405.75, 607.921875], [305.25, 607.921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 89], ["Text", 7], ["SectionHeader", 3], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 596], ["Line", 103], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 126], ["TextInlineMath", 5], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["SectionHeader", 2], ["Equation", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1498, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 112], ["Text", 7], ["TextInlineMath", 6], ["Reference", 4], ["ListItem", 3], ["SectionHeader", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 746, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 118], ["TableCell", 19], ["Text", 5], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3251, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["TableCell", 176], ["Line", 96], ["Text", 5], ["Table", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 10537, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["TableCell", 100], ["Line", 69], ["Text", 5], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10303, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["TableCell", 153], ["Line", 96], ["Text", 8], ["Table", 4], ["Reference", 4], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 14866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 111], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 924, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 91], ["ListItem", 30], ["Reference", 30], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 31], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 1724], ["Line", 134], ["TextInlineMath", 10], ["Equation", 7], ["Text", 4], ["SectionHeader", 3], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 99], ["Text", 8], ["Reference", 7], ["SectionHeader", 5], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 105], ["TableCell", 75], ["Text", 7], ["Reference", 6], ["SectionHeader", 5], ["Caption", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8799, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["TableCell", 325], ["Line", 87], ["Reference", 9], ["Caption", 6], ["Text", 6], ["Table", 5], ["SectionHeader", 4], ["TableGroup", 3], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 19827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 37], ["Line", 16], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 898, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Line", 146], ["Span", 89], ["Picture", 4], ["Caption", 4], ["PictureGroup", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2562, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_via_Efficient_Synthetic-Data_Parameterization"}