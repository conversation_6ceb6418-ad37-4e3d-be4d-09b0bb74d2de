# Dataset Condensation via Generative Model

<PERSON><sup>1</sup>; <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><sup>2</sup>, <PERSON><sup>1†</sup>

<sup>1</sup> Show Lab, National University of Singapore<sup>2</sup> Bytedance

# Abstract

*Dataset condensation aims to condense a large dataset with a lot of training samples into a small set. Previous methods usually condense the dataset into the pixels format. However, it suffers from slow optimization speed and large number of parameters to be optimized. When increasing image resolutions and classes, the number of learnable parameters grows accordingly, prohibiting condensation methods from scaling up to large datasets with diverse classes. Moreover, the relations among condensed samples have been neglected and hence the feature distribution of condensed samples is often not diverse. To solve these problems, we propose to condense the dataset into another format, a generative model. Such a novel format allows for the condensation of large datasets because the size of the generative model remains relatively stable as the number of classes or image resolution increases.Furthermore, an intra-class and an inter-class loss are proposed to model the relation of condensed samples. Intra-class loss aims to create more diverse samples for each class by pushing each sample away from the others of the same class. Meanwhile, inter-class loss increases the discriminability of samples by widening the gap between the centers of different classes. Extensive comparisons with state-of-the-art methods and our ablation studies confirm the effectiveness of our method and its individual component. To our best knowledge, we are the first to successfully conduct condensation on ImageNet-1k.*

## 1. Introduction

Despite the great success of deep neural networks on a wide range of computer vision and machine learning tasks[\[33,](#page-9-0) [15,](#page-8-0) [55,](#page-9-1) [64,](#page-10-0) [3,](#page-8-1) [39,](#page-9-2) [34,](#page-9-3) [36,](#page-9-4) [65\]](#page-10-1), it requires large-scale datasets to perform training and many runs of parameters tuning. Consequently, computational and environmental resources turn out to be heavily consumed.

<span id="page-0-0"></span>Image /page/0/Figure/8 description: This figure illustrates two approaches to condensing information from a dataset. On the left, labeled (a) Pixel Formats, it's noted as 'hard to scale up on ImageNet'. This section shows a representation of data condensed into pixel formats, with 'Real Data' represented by green and gray dots and 'Synthesis Data' by green and orange triangles. Below this, in section (c) DC, the data is described as 'compact in a small region', visualized as clusters of green, gray, and orange dots within tight boundaries. On the right, labeled (b) Learnable Generative Model, it's described as 'success to scale up on ImageNet'. This section shows data condensed using a generative model, with 'Real Data' again as green and gray dots and 'Synthesis Data' as green and orange triangles. Section (d) Ours shows the data 'spread out and diverse', visualized as more dispersed clusters of green, gray, and orange dots, with some overlap between the clusters.

Figure 1: (a) Existing works condense information into pixels. (b) We condense information into a generative model. (c) The distribution of condensed images from DC [\[70\]](#page-10-2). (d) The distribution of condensed images from our method.

Several works [\[53,](#page-9-5) [1,](#page-8-2) [25,](#page-8-3) [42,](#page-9-6) [6,](#page-8-4) [52,](#page-9-7) [40,](#page-9-8) [51,](#page-9-9) [32,](#page-9-10) [11\]](#page-8-5), attempt to reduce the high training costs by mining and compressing large-scale datasets. Traditional approaches settle on a subset of the original dataset, including active learning for valuable samples labeling [\[49\]](#page-9-11) and coreset selection [\[43,](#page-9-12) [2\]](#page-8-6). These approaches are limited by the representation capability and coverage of the selected samples. Recent studies propose a new task, namely dataset condensation (DC) or distillation (DD), to condense the large dataset into a very small number of synthetic images, while maintaining promising results of the network trained on such a small set. Starting from the seminal work [\[53\]](#page-9-5), various methods have been developed to improve dataset condensation, in-

<sup>\*</sup>Work is partially done during an internship at ByteDance. †Corresponding Author.

cluding keeping network gradient [\[70\]](#page-10-2), feature [\[52\]](#page-9-7), mutual information [\[22\]](#page-8-7) and parameter trajectory [\[6\]](#page-8-4) consistent between synthetic and original sets. All of them aim to compress the information contained in the original large dataset into a small set of synthetic images.

Despite being promising, existing dataset condensation approaches usually face two major challenges. (1) First, the condensation process is slow and difficult to scale up to large datasets such as ImageNet-1K. This is because that, current methods try to distill information of original dataset directly into pixels by treating each pixel of synthetic images as a learnable parameter, whose number is proportional to the resolution and number of classes. For example, 1K classes,  $128 \times 128$  resolutions and 10 images per class would lead to a total number of 1.5G parameters. Backpropagation on such a large number of parameters makes the optimization process extremely slow. Also, it is hard to optimize such a large amount of parameters, prohibiting dataset condensation methods from scaling up to large datasets *e.g., ImageNet-1K*. (2) Second, since network used for data distillation is usually optimizing towards intra-class compactness [\[58\]](#page-9-13), the distribution of synthesis images in each class tends to be clustered into a compact small region as shown in Fig[.1](#page-0-0) (c). Such lacking of diversity precludes the synthetic images being representative enough, often leading to overfitting when training on them.

To addresse the above challenge (1), as illustrated in Fig[.1](#page-0-0) (b), we propose to condense the information into an alternative format, namely a generative model rather than the pixels. Specifically, the generative model comprises of a codebook and a subsequent generator, both of which are optimized to contain as much information as possible from the original dataset. To synthesize an image, we initially sample a code from the learned codebook, and the generator serves as an information carrier, conveying the information from latent space to images. It takes the sampled code as input and generates an image containing dense information. Additionally, the generator is conditioned on a class embedding, which controls the synthetic image to be classspecific. Importantly, both the codebook and generator are shared by different classes, and each code is a 1D learnable vector, resulting in fewer parameters that are less affected by the increasing number of classes and resolutions. Therefore, this innovative condensed format facilitates condensation of datasets with diverse classes and higher resolutions, such as ImageNet-1K [\[12\]](#page-8-8).

To overcome the above challenge (2), we introduce an intra-class diversity loss and an inter-class discrimination loss to improve the representation capability of the condensed dataset. Concretely, the intra-class loss regards every two samples of the same class as a negative pair, while each sample and its corresponding class center as a positive pair. In this manner, synthetic samples of the same class are spread out while are not too far away from their respective class centers. Meanwhile, the inter-class loss enlarges the distance between samples of different classes and therefore the samples can be more easily distinguished. Thanks to these two losses, our condensed dataset exhibits a higher degree of diversity and wider coverage of information, hence is more favorable than previous condensed datasets.

We summarize our contributions as below:

- Instead of directly condensing information of a large dataset into synthetic images, we propose to condense the information into a generative model, which consists of an information carrier generator and a codebook.
- Such a novel condensed format enables us to successfully scale up condensation on ImageNet-1K [\[12\]](#page-8-8) for the first time.
- We devise the intra-class diversity loss and the interclass discrimination loss to enhance the representation capability of synthetic images and improve the generalization ability.
- Extensive experiments on standard benchmarks prove the superiority of our method over the existing condensation methods. The ablation studies confirm the effectiveness of each proposed component.

## 2. Related Work

Dataset Condensation. Dataset condensation techniques have advanced in a number of applications including data privacy [\[46,](#page-9-14) [50,](#page-9-15) [14\]](#page-8-9), neural architecture search [\[70,](#page-10-2) [68\]](#page-10-3), federate learning [\[18,](#page-8-10) [72\]](#page-10-4) and continue learning [\[70,](#page-10-2) [68,](#page-10-3) [67\]](#page-10-5) since the seminal work [\[53\]](#page-9-5). Following works significantly improve the results by surrogating optimization objectives or proposing efficient optimization approaches. Worthwhile objective, e.g., trajectory matching [\[6\]](#page-8-4), distribution [\[68\]](#page-10-3) and feature alignments [\[52\]](#page-9-7) as well as valuable optimization adjustment e.g., synthetic-data parameterization [\[22\]](#page-8-7), neural feature regression [\[71\]](#page-10-6), soft label [\[47\]](#page-9-16), infinitely wide convolution networks [\[37\]](#page-9-17), contrastive signals [\[28\]](#page-8-11) and differentiable siamese augmentation [\[67\]](#page-10-5) are conducive to synthesizing informative images. Besides, [\[13,](#page-8-12) [31\]](#page-9-18) propose to factorize synthetic images into the image basis and a couple of coefficients to retrieve.

However, above methods regard pixels or image basis as learnable parameters, the number of which grows linearly with number of classes and resolution. This hinders their scalability to the dataset with diverse classes. Instead of directly optimizing pixels/image basis, we condense information into a generative model then synthesize images. Moreover, modeling relations among synthesis images are ignored in previous approaches. On the contrary, we design an inter and inra class loss to mining relations thereby generating more informative and diverse synthesis images. For the first time to scale up condensation methods on Imagenet-1K, our work is cocurrent with [\[10\]](#page-8-13).

Coreset Selection. The selection of a coreset or subset is the classical method for decreasing the overall size of the training set [\[25,](#page-8-3) [8,](#page-8-14) [17,](#page-8-15) [57\]](#page-9-19). The majority of these techniques involve making small, incremental selections of key data points on the basis of heuristic selection criteria. [\[48\]](#page-9-20) assesses the forgettability of trained samples during the network's training process and eliminates difficult-to-forget samples. [\[1\]](#page-8-2) attempts to achieve maximal diversity of samples within the gradient space. [\[42\]](#page-9-6) selects data points that are in close proximity to the cluster centers for consideration. However, it cannot be ensured that the selected subset is optimal for training deep neural networks as these heuristic selection criteria were not specifically designed to function with them. Furthermore, greedy algorithms for sample selection cannot ensure that the selected subset is the most optimal to meet the desired criteria. Instead of selecting samples, we aims to synthesize a small set of samples to benefit network training.

Generative Model. Generative models, such as those explored in [\[7,](#page-8-16) [35,](#page-9-21) [38,](#page-9-22) [5,](#page-8-17) [21,](#page-8-18) [19\]](#page-8-19), have been developed for the purpose of creating realistic images, and have been applied in a range of applications, including image manipulation [\[54,](#page-9-23) [73\]](#page-10-7), inpainting  $[62, 61]$  $[62, 61]$  $[62, 61]$ , super-resolution [\[27\]](#page-8-20), imageto-image translation [\[9\]](#page-8-21), and object detection [\[56,](#page-9-24) [29\]](#page-9-25). On one hand, the effectiveness of these GAN-generated images for training models is comparable to that of randomly selected real images. On the other hand, some methods have utilized GAN to generate datasets, such as the work of [\[59\]](#page-9-26) which proposes to generate features for unseen classes in zero-shot learning, and [\[66\]](#page-10-10) which introduces a Dataset-GAN for creating semantic labels. However, these methods aim to generate massive training samples and/or annotate pixel labels. Differently, IT-GAN [\[69\]](#page-10-11) proposes to inverse information into latent codes only, but the number of synthetic training samples is not reduced and is equal to the number of samples in the original dataset. Thus, the training cost is still high. Furthermore, the relation among generated samples is ignored in these methods.

Apart from above methods, our method aims to condense a dataset into a whole generative model and generate a very small set of training samples to reduce training cost. Meanwhile, we minimize our inter and intra class relation loss to ensure that the synthesized images are diverse and discriminative enough, resulting in a condensed dataset with strong representative capability. Differences in details between our method and IT-GAN [\[69\]](#page-10-11) can be found in Section [4.5.](#page-6-0) Moreover, our work is cocurrent with DiM [\[51\]](#page-9-9).

<span id="page-2-0"></span>

|           | format               | scalable | sample relations |
|-----------|----------------------|----------|------------------|
| DD [53]   | pixels as parameters | hard     | ignored          |
| CAFE [52] | pixels as parameters | hard     | ignored          |
| MTT [6]   | pixels as parameters | hard     | ignored          |
| OURS      | codebooks+generator  | ✓        | ✓                |

Table 1: Comparisons with other SOTA methods. Scalable means scalability to dataset with diverse classes.

# 3. Method

### 3.1. Overview

Given a large dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  with large amounts of training samples  $x_i$  and corresponding labels  $y_i$ , dataset condensation aims to get a small synthetic set  $S = \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^{|\mathcal{T}|}$  whose size  $|\mathcal{S}|$  is much smaller than  $|\mathcal{T}|$ . As shown in Fig[.2,](#page-3-0) instead of directly condensing information into pixels, we distill information into a generative model. The class embeddings are sent into the generator together with sampled codes from the codebook. They serve as a detonator to boom the information squeezed in the generator and enable it synthesizing informative images. Meanwhile, the relation of synthetic images is constrained with our inter and intra-class loss. The main differences with other SOTA methods are summarized in Tab[.1.](#page-2-0)

### 3.2. Condense Dataset into Generative Model

Codebook. Instead of treating condensed images as learnable parameters and directly optimizing them, we propose a learnable codebook  $Z \in \mathbb{R}^{K \times C}$ , where K is the number of images per class and  $C$  is the latent dimension. In this way, the number of learnable parameters is no longer significantly effected by the number of classes and spatial resolutions. This makes it possible to condense dataset that has a large number of diverse classes. Since different classes share the same codebook, the learned codebook contains valuable mutual information across classes and has strong representative capability.

Generative Models. Given sampled information squeezed codes z from Z, our generator is optimized to comprise maximum information and acts as an information carrier, conveying the information from the latent space to the images. The primary goal of the generator is to synthesize informative images, rather than realistic ones. As indicated by [\[53\]](#page-9-5), the generated images are usually out of the distribution of original datasets. Therefore, training on these images would lead to inferior performance. To alleviate this effect, we take the feature embeddings instead of images as input into discriminator, which enables the feature distribution of synthetic images to align or cover the distribution of original datasets better. Thus we arrive at an adversarial loss as follows,

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This figure illustrates the overall framework of a generative model. The process begins with a codebook Z, from which samples are drawn to form sampled codes z. These codes, along with class embedding conditions c(y), are fed into a Generator to produce synthetic images. The model also utilizes original images T, which are processed by a Network ∅. The outputs of the Network ∅ are then matched with the synthetic images. A Discriminator evaluates these matches, considering both intra-class diversity and inter-class discrimination. The figure shows examples of original images and corresponding synthetic images, categorized by class. Arrows indicate the flow of information and the relationships between different components of the model, including the generation of synthetic image features and original image features.

Figure 2: Overall Method. We aim to condense a whole dataset into a generative model. Given the sampled codes from the codebook and class conditions, generator synthesizes images. The generative model is optimized to contain maximum information by aligning synthesis images features to real image features and modeling the relations among synthesis samples.

$$
\min_{G} \max_{D} \mathcal{L}_{adv} = E[\log D(\phi_{\theta}(x), c(y))] +
$$
$$
E[\log(1 - D(\phi_{\theta}(\tilde{x}), c(y)))],
$$
 (1)

where D is a discriminator,  $\tilde{x}=G(z, c(y))$ , c(y) is a class condition, G is a generator and  $\phi_{\theta}$ () represents a feature extractor on a task specific dataset.

Furthermore, we add a classifier head on top of the features from both real and synthetic images inspired by [\[38\]](#page-9-22). Updating the generator and codebooks via this classification loss  $\mathcal{L}_c$  enables the synthesis images to share the same category-based semantics as real images. The overall GAN loss can be denoted as  $\mathcal{L}_{GAN} = \mathcal{L}_{adv} + \mathcal{L}_{c}$ .

Class Embedding. The class embedding plays a role of a fuse to enable generator to boom the condensed information. Hence, we propose to condition on more informative class embeddings instead of one-hot labels. Specifically, we extract and spatially pool the lowest resolution features of all images in dataset using a well-trained feature extractor and calculate the mean per class in the dataset. Then a projection layer is applied to make it balance with  $z$  code and we concatenate them together as the input for generator.

Matching Loss. To make synthetic images more informative and representative, we simply adopt the feature matching loss [\[52\]](#page-9-7) as our training objective to update our generator and codebook. For each synthetic image  $\tilde{x}$ , since the number of original images that belong to the same class is large, we sample a subset of original images as a association. As shown in Fig[.2,](#page-3-0) we pass the synthetic image and its association respectively through a network denoted as  $\phi_{\theta}$ . Then the feature  $f_l^S$  of  $\tilde{x}$  and the mean feature  $\overline{f}_l^T$ l of its association from every layer  $l$  are obtained. In the end, the MSE is applied to calculate the feature distribution matching loss  $\mathcal{L}_{f}$  between the synthetic image and its association(original images) for every layer. The loss is formulated as

$$
\mathcal{L}_{\mathrm{f}} = \sum_{l=1}^{L} \left| \boldsymbol{f}_{l}^{\mathcal{S}} - \overline{\boldsymbol{f}}_{l}^{\mathcal{T}} \right|^{2}.
$$
 (2)

### 3.3. Intra-Class Diversity and Inter-Class Discrimination.

Intra-Class Diversity. As mentioned in the introduction, relations among synthetic images are neglected and therefore, as shown in Fig[.1](#page-0-0) (c), the synthetic images of each class have pretty low diversity. Since the synthetic images for the same class are not diverse enough, training on them can easily result in overfitting. To overcome this issue, we treat any two samples of the same class as a negative pair, pushing their feature embedding away from each other. However, this alone may widen the sample distribution of one class too much, and hence the sample distributions of different classes are overlapped. To remedy this effect, we aim to constrain the scope of distribution of each class. Specifically, we introduce class center for each class; each sample and its corresponding class center form a positive pair, and their features are to be pulled closer. With these two kinds of pairs, we can achieve a balance that fea-

tures of the same class samples are diverse enough while can still be distinguished against features of other classes. Formally, we design an intra-class diversity loss:

$$
\mathcal{L}_{intra} = -\log \frac{e\left(\langle \boldsymbol{f}^{\mathcal{S}}, c(y) \rangle\right)}{\sum_{i=1}^{K-1} e\left(\langle \boldsymbol{f}^{\mathcal{S}}, \boldsymbol{f}_{i}^{S_{y}} \rangle\right) + e\left(\langle \boldsymbol{f}^{\mathcal{S}}, c(y) \rangle\right)},\tag{3}
$$

where *e* is  $\exp\left(\frac{1}{\tau}\right)$ ,  $\langle\rangle$  is the dot product and  $f^{\mathcal{S}}$  is the feature of the synthetic image  $\tilde{x}$  obtained from the last layer of feature extraction network  $\phi_{\theta}$ .  $c(y)$  is a class embedding and  $f_i^{\mathcal{S}_y}$  is the feature of another sample, whose class is same as  $\tilde{x}$ .

Inter-Class Discrimination. To further make our synthetic samples of different classes distinguishable from each other, we aim to enlarge the margin between feature distributions of different classes. Inspired by [\[63\]](#page-10-12), we propose an interclass discrimination loss:

$$
\mathcal{L}_{inter} = \sum_{c_A=1, c_A \neq c_B}^{C} \sum_{c_B=1}^{C} \max \left( \tau_m - \left\Vert \overline{\boldsymbol{f}}^{\mathcal{S}_{c_A}} - \overline{\boldsymbol{f}}^{\mathcal{S}_{c_B}} \right\Vert, 0 \right), \tag{4}
$$

where  $\overline{f}^{S_{c_B}}$  and  $\overline{f}^{S_{c_A}}$  are the mean values of features of different class in each synthetic images batch. When the distance between two class centers is large than margin  $\tau_m$ , the penalty is zero. This loss forces the mean center of each class to be far away from other classes and hence makes the samples of different classes more discriminative.

### 3.4. Optimization Framework

We adopt the bi-level optimization following [\[52,](#page-9-7) [70,](#page-10-2) [53,](#page-9-5) [13\]](#page-8-12). The optimization framework is illustrated in Algorithm [1.](#page-4-0) In the outer-loop, the overall condensation loss  $\mathcal{L}_{con} = \mathcal{L}_{GAN} + \mathcal{L}_{f} + \mathcal{L}_{intra} + \mathcal{L}_{inter}$  is used to update the generator  $G$  and codebook  $Z$ . Meanwhile, the network  $\phi_{\theta}$  for feature matching is updated by general classification loss  $\mathcal{L}_{cls}$ . Unlike previous methods [\[70,](#page-10-2) [52\]](#page-9-7), the  $\phi_{\theta}$ is dependent on real images  $|T|$  rather than synthetic images  $|S|$  for training. Hence, it can force the layer statistics e.g., mean and variance produced by our synthetic images to align with those of the real data during feature matching. Moreover, it further forces the distribution of synthetic images to align with real images so that fool the discriminator.

### 4. Experiments

#### 4.1. Dataset

MNIST [\[26\]](#page-8-22) contains 60000 training images and 10000 test images with  $28 \times 28$  size for handwritten digit recognition. Fashion MINIST [\[60\]](#page-10-13) has a training set of 60,000 samples and a test set of 10,000 examples, It is a dataset of Zalando

<span id="page-4-0"></span>

| <b>Input:</b> Training data $\mathcal T$                                                |
|-----------------------------------------------------------------------------------------|
| <b>Notation:</b> Generator $G$ , codebooks $Z$ , Synthetic dataset                      |
| S, Network for feature matching $\phi_{\theta}$ and hyperparameters                     |
| $\lambda$ , $\alpha$ are learning rates.                                                |
| repeat                                                                                  |
| Initialize network parameter $\theta_1$                                                 |
| for $n = 1$ to N do                                                                     |
| for $i=1$ to M do                                                                       |
| Sample multiple z codes from $ Z $                                                      |
| Generate the synthetic images set $S_i$ via $G$                                         |
| Sample their associations $\mathcal{T}_i$ from $\mathcal{T}$                            |
| Compute $\mathcal{L}_{con}$ via $\mathcal{T}_i$ and $\mathcal{S}_i$ using $\phi_\theta$ |
| Update $(Z, G) \leftarrow (Z, G) - \alpha \nabla_{(Z, G)} \mathcal{L}_{con}$            |
| end for                                                                                 |
| Inner loop update $\theta \leftarrow \theta - \nabla \mathcal{L}_{cls}(\mathcal{T})$    |
| end for                                                                                 |
| <b>until</b> Converge                                                                   |

article photos. Each example consists of a  $28 \times 28$  image paired with a label from one of ten classes.

SVHN [\[44\]](#page-9-27) is a real-world picture dataset to create machine learning and object recognition algorithms. It has more than 600,000 digitized photos drawn from actual data. All images are cropped into  $32 \times 32$  size.

**CIFAR10/100** [\[23\]](#page-8-23) comprise  $32 \times 32$  small colored natural images from 10 and 100 classes, respectively. Each dataset has 50,000 and 10,000 images for training and testing.

ImageNet-1K [\[12\]](#page-8-8) spans 1000 object classes and contains 1,281,167 training images, 50,000 validation images. The version of  $64 \times 64$  is what we use.

#### 4.2. Implementation Details

We optimize Z codebook and generator using three/four blocks Convolutional Network(ConvNet). Each block has a convolution, an instance norm, a relu and a pooling layer. The generator is pretrained on task-specific datasets and consists of three blocks for 32 image size datasets or four blocks for 64 size dataset. Each block has two activationconvolution-bn layers and an upsampling layer. The discriminator is a multi-layer perceptron with a sigmoid function as the last layer. As indicated in [\[71\]](#page-10-6), hard one-hot labels do not work well on dataset with diverse classes. This is because that, the classes are not completely mutually exclusive. Therefore, we use the soft label extracted from a pre-trained ConvNet for classification in GAN loss to update  $Z$  and  $G$ , when conducting experiments in Imagenet-1K dataset only. The learning rate for updating  $Z$ ,  $G$ , and  $\phi_{\theta}$  is 0.01, 0.001, 0.01 with the SGD optimizer and a linear epoch decay, respectively. Following [\[70,](#page-10-2) [52,](#page-9-7) [6\]](#page-8-4), for the evaluation protocol, each model is evaluated on 20 randomly initialized models, trained for 300 epochs on a syn-

<span id="page-5-0"></span>

|              | IPC Ratio % |       |             |                                             | <b>Coreset Selection</b>                                    |                                                                            | Condensation   |  |                                              |                                                                                           |                          |                |                      |
|--------------|-------------|-------|-------------|---------------------------------------------|-------------------------------------------------------------|----------------------------------------------------------------------------|----------------|--|----------------------------------------------|-------------------------------------------------------------------------------------------|--------------------------|----------------|----------------------|
|              |             |       | Random      |                                             |                                                             | Herding K-Center Forgetting $DD^{\dagger}$ [53]                            | $LD^{\dagger}$ |  |                                              | DC [70] DSA [67] CAFE [52] MTT [6]                                                        |                          | <b>OURS</b>    | <b>Whole Dataset</b> |
|              |             | 0.017 |             |                                             |                                                             | $64.9 \pm 3.5$ 89.2 $\pm$ 1.6 89.3 $\pm$ 1.5 35.5 $\pm$ 5.6                |                |  |                                              | $60.9 \pm 3.2$ 91.7 $\pm$ 0.5 88.7 $\pm$ 0.6 93.1 $\pm$ 0.3                               | $\equiv$                 | $93.7 + 0.9$   |                      |
| <b>MNIST</b> | 10          | 0.17  |             |                                             |                                                             | $95.1 \pm 0.9$ $93.7 \pm 0.3$ $86.4 \pm 1.7$ $68.1 \pm 3.3$ $79.5 \pm 8.1$ |                |  | $87.3+0.7$ 97.4+0.2 97.8+0.1                 | $97.5 + 0.1$                                                                              | $\equiv$                 | $98.4 + 0.1$   | $99.6 + 0.0$         |
|              | 50          | 0.83  |             |                                             |                                                             | $97.9+0.2$ $94.8+0.2$ $97.4+0.3$ $88.2+1.2$                                |                |  |                                              | $93.3 \pm 0.3$ $98.8 \pm 0.2$ $99.2 \pm 0.1$ $98.9 \pm 0.2$                               |                          | $99.2 \pm 0.1$ |                      |
|              |             | 0.017 |             |                                             |                                                             | $51.4 \pm 3.8$ 67.0 $\pm 1.9$ 66.9 $\pm 1.8$ 42.0 $\pm 5.5$                | $\sim$         |  |                                              | $70.5 \pm 0.6$ $70.6 \pm 0.6$ $77.1 \pm 0.9$                                              | $\overline{\phantom{m}}$ | $79.2 \pm 1.0$ |                      |
| FashionMNIST | 10          | 0.17  |             |                                             |                                                             | $73.8+0.7$ $71.1+0.7$ $54.7+1.5$ $53.9+2.0$                                | $\blacksquare$ |  | $82.3 + 0.4$ $84.6 + 0.3$                    | $83.0 + 0.4$                                                                              | $\equiv$                 | $87.3 + 0.4$   | $93.5 \pm 0.1$       |
|              | 50          | 0.83  |             |                                             | $82.5+0.7$ 71.9+0.8 68.3+0.8 55.0+1.1                       |                                                                            |                |  | $83.6 \pm 0.4$ $88.7 \pm 0.2$                | $88.2 + 0.3$                                                                              | $\equiv$                 | $88.8 \pm 0.3$ |                      |
|              |             | 0.014 |             |                                             |                                                             | $14.6 \pm 1.6$ 20.9 $\pm$ 1.3 21.0 $\pm$ 1.5 12.1 $\pm$ 1.7                | $\blacksquare$ |  | $31.2 \pm 1.4$ 27.5 $\pm$ 1.4                | $42.9 \pm 3.0$                                                                            | ÷                        | $51.8 \pm 2.2$ |                      |
| <b>SVHN</b>  | 10          | 0.14  |             |                                             |                                                             | $35.1 \pm 4.1$ $50.5 \pm 3.3$ $14.0 \pm 1.3$ $16.8 \pm 1.2$                | $\blacksquare$ |  |                                              | $76.1 \pm 0.6$ $79.2 \pm 0.5$ $77.9 \pm 0.6$                                              | $\qquad \qquad -$        | $82.1 + 0.5$   | $95.4 \pm 0.1$       |
|              | 50          | 0.7   |             |                                             |                                                             | $70.9+0.9$ $72.6+0.8$ $20.1+1.4$ $27.2+1.5$                                |                |  | $82.3+0.3$ 84.4+0.4 82.3+0.4                 |                                                                                           | $\qquad \qquad -$        | $84.4 \pm 0.5$ |                      |
|              |             | 0.02  |             |                                             |                                                             | $14.4 \pm 2.0$ $21.5 \pm 1.2$ $21.5 \pm 1.3$ $13.5 \pm 1.2$                |                |  |                                              | $25.7 \pm 0.7$ $28.3 \pm 0.5$ $28.8 \pm 0.7$ $31.6 \pm 0.8$ $46.3 \pm 0.8$ $48.2 \pm 0.8$ |                          |                |                      |
| CIFAR10      | 10          | 0.2   |             |                                             |                                                             | $26.0 \pm 1.2$ 31.6 $\pm$ 0.7 14.7 $\pm$ 0.9 23.3 $\pm$ 1.0 36.8 $\pm$ 1.2 |                |  | $38.3+0.4$ $43.4+0.5$ $52.1+0.5$             | $50.9 + 0.5$                                                                              | $65.3 + 0.7$ 66.2+0.5    |                | $84.8 + 0.1$         |
|              | 50          |       |             |                                             | $43.4 \pm 1.0$ $40.4 \pm 0.6$ $27.0 \pm 1.4$ $23.3 \pm 1.1$ |                                                                            |                |  | $42.5 \pm 0.4$ 53.9 $\pm$ 0.5 60.6 $\pm$ 0.5 | $62.3 + 0.4$                                                                              | $72.8 + 0.2$ 73.8 + 0.1  |                |                      |
|              |             | 0.2   | $4.2 + 0.3$ |                                             | $8.4 \pm 0.3$ $8.3 \pm 0.3$                                 | $4.5 + 0.3$                                                                |                |  | $11.5+0.4$ $12.8+0.3$ $13.9+0.3$             | $14.0 \pm 0.3$ $24.3 \pm 0.3$ <b>26.1</b> $\pm$ <b>0.3</b>                                |                          |                |                      |
| CIFAR100     | 10          | 2     |             | $14.6 \pm 0.5$ 17.3 $\pm$ 0.3 7.1 $\pm$ 0.2 |                                                             | $9.8 \pm 0.2$                                                              | $\blacksquare$ |  | $25.2 + 0.3$ 32.3+0.3                        | $31.5 + 0.2$                                                                              | $40.1 + 0.4$ 41.9 + 0.3  |                | $56.17 + 0.3$        |
|              | 50          | 10    |             |                                             | $30.0 \pm 0.4$ 33.7 $\pm$ 0.5 30.5 $\pm$ 0.3                | $\overline{\phantom{a}}$                                                   |                |  | $42.8 + 0.4$                                 | $42.9 \pm 0.2$ $47.7 \pm 0.2$ $48.5 \pm 0.2$                                              |                          |                |                      |

Table 2: The performance (testing acc. %) comparison to state-of-the-art methods.  $LD^{\dagger}$  and  $DD^{\dagger}$  use LeNet for MNIST and AlexNet for CIFAR10, while the rest use ConvNet for training and testing. IPC: Images Per Class, Ratio (%): the ratio of condensed images to whole training set. Comparisons under store memory budget are shown in Section A of supplement.

<span id="page-5-1"></span>

| <b>IPC</b> | Random       | FrePo         | DМ            | <b>Ours</b>    | <b>Whole Dataset</b> |
|------------|--------------|---------------|---------------|----------------|----------------------|
|            | $0.6 + 0.1$  | $7.5 \pm 0.3$ | $1.5 \pm 0.1$ | $7.9 + 0.5$    |                      |
| 2          | $0.9 + 0.1$  | $9.7 + 0.2$   | $1.7 + 0.1$   | $10.0 + 0.6$   | $34.2 + 0.4$         |
| 10         | $3.8 + 0.1$  |               |               | $17.6 \pm 1.7$ |                      |
| 50         | $15.4 + 1.6$ |               |               | $27.2 \pm 1.6$ |                      |

Table 3: ImageNet-1K results. 1/2/10/50 images per class settings are reported.

<span id="page-5-2"></span>

| <b>TPC</b>         |             |              | 10           | 50           |
|--------------------|-------------|--------------|--------------|--------------|
| $BigGAN$ [5]       | $0.5 + 0.1$ | $0.9 + 0.1$  | $3.7 + 0.1$  | $14.3 + 0.9$ |
| VQGAN $[16]$       | $0.8 + 0.1$ | $0.9 + 0.1$  | $3.8 + 0.2$  | $15.7 + 0.8$ |
| StyleGAN-XL $[41]$ | $0.8 + 0.2$ | $0.9 + 0.1$  | $3.7 + 0.1$  | $15.9 + 1.0$ |
| Ours               | $7.9 + 0.5$ | $10.0 + 0.6$ | $17.6 + 1.7$ | $27.2 + 1.6$ |

Table 4: Use the synthesis images from different generative model for ImageNet-1K training. All images are resize into  $64 \times 64$  for fair comparisons.

thetic dataset and reported with mean and standard deviation. All experiments are conducted on one A100 GPU.

#### 4.3. Comparisons with State-Of-The-Art

We compare our method with other approaches in Tab[.2.](#page-5-0) The comparisons follow the standard 1/5/50 images per class (IPC) evaluation setting. The ConvNet is utilized during the training and testing stage. Overall, the table can be separated into two groups. One is classic core selection, including Random, K-Center, and Herding selection, and the other one is recent dataset condensation methods, including LD [\[4\]](#page-8-25), DC [\[70\]](#page-10-2), CAFE [\[52\]](#page-9-7), MTT [\[6\]](#page-8-4), and DSA [\[67\]](#page-10-5).

Since [\[13,](#page-8-12) [22,](#page-8-7) [30\]](#page-9-29) use the memory story budget instead of standard IPC as the target, which leads to more images per class, we do not include them in Tab[.2](#page-5-0) for fair comparisons. Compared with the coreset selection method, our method surpasses them by a large margin. Compared with the SOTA condensation approach MTT [\[6\]](#page-8-4), our method achieves 1.9 % test accuracy improvement when training with one condensed image per class on CIFAR 10 dataset. With more condensed images (10 and 50) per class, our method consistently beat the performance of MTT. Moreover, with more complex labels on CIFRAR-100, our method also obtains outstanding results with different numbers of condensed images per class. All above performance demonstrates the superiority of our format, which condenses information into a generative model, over the format that defines synthesis images as learnable parameters.

#### 4.4. ImageNet-1K Results

It is difficult for other methods to be applied on ImageNet-1K [\[12\]](#page-8-8). This is because that, heavy number of parameters in synthesis images which is proportional to the number of classes and resolutions is hard to be optimized, causing high GPU memory. Instead, our generative model successfully scales up the dataset condensation methods into ImageNet-1K with diverse classes and color spaces. Concretely, as shown in Tab[.3,](#page-5-1) our method can achieve 17.6% accuracy and 27.2 % accuracy with only 10 and 50 condensed images per class for training. Given that the upper bound 34.2 % for training the whole dataset, our method achieves the promising result training on such less training samples. These results further validate that condensing the information into a generative model instead of the original pixel space is meaningful and effective.

<span id="page-6-1"></span>

| C \setminus T | ConvNet | AlexNet                          | VGG11                            | ResNet18                         | MLP                              |                                  |
|---------------|---------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
| DC            | ConvNet | $53.9 \pm 0.5$                   | $28.77 \pm 0.7$                  | $38.76 \pm 1.1$                  | $20.85 \pm 1.0$                  | $28.71 \pm 0.7$                  |
| CAFE          | ConvNet | $55.5 \pm 0.4$                   | $34.0 \pm 0.6$                   | $40.6 \pm 0.8$                   | $25.3 \pm 0.9$                   | $36.7 \pm 0.6$                   |
| Our           | ConvNet | <b><math>73.8 \pm 0.3</math></b> | <b><math>53.9 \pm 0.4</math></b> | <b><math>60.1 \pm 0.2</math></b> | <b><math>54.9 \pm 0.7</math></b> | <b><math>51.1 \pm 0.5</math></b> |

Table 5: The generalization ability (%) on unseen architectures. C means the network used for condensing and T means netwoks used for testing.

<span id="page-6-2"></span>

| C-10 C-100 IN-Sub |      |      |      | C-10 C-100 IN-Sub     |      |      |      |
|-------------------|------|------|------|-----------------------|------|------|------|
| one-hot           | 62.2 | 38.1 | 20.5 | uniform sample        | 55.3 | 27.1 | 12.2 |
| online feature    | 64.0 | 39.9 | 22.1 | random with threshold | 55.9 | 28.4 | 12.5 |
| class feature     | 66.2 | 41.9 | 26.6 | learnable codebooks   | 66.2 | 41.9 | 26.6 |

Table 6: Ablation study Table 7: Ablation study on difon different conditions. ferent input samples.

<span id="page-6-0"></span>

#### 4.5. Comparisons with Other Generative Models

Generative model optimised for high-fidelity. We also utilize the synthetic images generated by different generative models including BigGAN [\[5\]](#page-8-17), VQGAN [\[16\]](#page-8-24), StyleGAN-XL [\[41\]](#page-9-28) for training. All images are resized to  $64\times64$ . As shown in Tab[.4,](#page-5-2) although above generative models can synthesis high quality images, the results training on these synthetic images are nearly same as random selections. On the contrary, our method can synthesize more informative samples for training. The result shows that information is successfully condensed into our generative model. IT-GAN [\[69\]](#page-10-11). Our approach differs from IT-GAN in several ways. (1) Motivation. IT-GAN aims to explore if a fixed gan can generate the informative images without changing the size of dataset and reducing training cost. Our method aims to condense a dataset into a generative model and synthesize a very small size dataset to reduce training cost. (2) Latent input. In IT-GAN, given a fixed pre-trained generator  $G$ , it learns the whole latent set  $Z \in \mathbb{R}^{\mathcal{T} \times d_z}$ . Each latent vector  $z \in \mathbb{R}^{d_z}$  corresponds to a real image x in the original dataset  $\mathcal{T} = \{x_i, y_i\}_{i=1}^{|\mathcal{T}|}$  and generate one synthesis image. The number of synthesis images is not reduced and equal to the original number of images in datasets. In contrast, our codebook  $Z \in \mathbb{R}^{K \times d_z}$  is shared by different class, where K is the condensed number of images per class with  $K \ll |T|$ . (3) Condition. IT-GAN is an unconditional GAN and relies only on the z code, whereas our method is conditioned on the class embedding, which controls the class of the synthesized image. Above two differences bring the feasibility of our method on large scale IN1K. Note that  $|T| = 1.2$  million on IN1K is huge so it is hard to apply IT-GAN on IN1K. (4) Generator. The generator of IT-GAN is fixed but our generator is optimized to contain more information. (5) Results and loss. If we follow IT-GAN to fix generator and exclude our proposed inter and intra-class loss, the accuracy under 10-IPC on CIRAR-10 drops from 66.2% to 57.6%, which shows the superiority and novelty of our method.

<span id="page-6-3"></span>

| 1_real | 1_feature | 1_intra | 1_inter | CIFAR-10 | CIFAR-100 | IN-SUB |
|--------|-----------|---------|---------|----------|-----------|--------|
| ✓      |           |         |         | 63.2     | 37.2      | 20.1   |
| ✓      | ✓         |         |         | 64.7     | 39.1      | 23.4   |
| ✓      | ✓         | ✓       |         | 64.9     | 39.4      | 23.5   |
| ✓      | ✓         | ✓       | ✓       | 65.4     | 40.8      | 25.3   |
| ✓      | ✓         | ✓       | ✓       | 66.2     | 41.9      | 26.6   |

Table 8: Ablation study of loss.

### 4.6. Ablation Study

In this section, we ablate the effect of variations for proposed components including codebook, class embedding, intra-class diversity, and inter-class discrimination loss in our method. All experiments are conducted with 10 images per class setting on three datasets including CIFAR-10/100 and ImageNet. For ImageNet, we select 100 classes as a subset from the original 1000 classes. The performance trend of different architecture on these 100 classes is similar to the original dataset.

Generalization ability. Since the condensed images are generated with ConvNet feature matching, we explore the generalization ability of our condensed images with unseen architectures, including AlexNet [\[24\]](#page-8-26), VGG11 [\[45\]](#page-9-30), ResNet18 [\[20\]](#page-8-27) and MLP. As shown in Tab[.5,](#page-6-1) our method achieves outstanding generalization performance on different architectures. This could be attributed to that our inter and intra-class loss improve the diversity of synthesis images for more robust generalization.

Different class embedding. We evaluate three different formats of class embedding as a detonator to boom the information. One-hot is a regular label for each class. Online features represent that using the feature of images extracted by network  $\phi_{\theta}$  at the last step so it varies during training. The class feature is the mean feature of each class as introduced in the method. As shown in Tab[.6,](#page-6-2) the class feature achieves the best results since it contains more information acquired from all images in each class. It provides strong prior emerged from a specific class and enables the generator to synthesize informative images. Such a prior brings more improvement on the complex dataset-ImageNet, which has mutual information across classes.

Codebook. We demonstrate the representative capability of  $|Z|$  codebook in Tab[.7.](#page-6-2) Uniform sampling means that a couple of Z codes are sampled uniformly in a gaussian distribution. Random with threshold represents using truncation trick as [\[5\]](#page-8-17). Compared with other two sample methods, the learnable codebook brings 10.9%, 14.8%, 14.4% and  $10.3\%, 13.5\%, 14.1\%$  on three datasets respectively. These improvements indicate the effectiveness of condensing the information into our learnable codebook.

Loss designs. We evaluate the different loss designs as shown in Tab[.8.](#page-6-3) All losses are equipped with feature matching. Compared with the realistic real/fake  $loss(l_{real})$  in GAN, our distribution real/fake loss with classification( $l_{feature}$ ) achieves the 1.5%, 1.9%, 2.7% im-

<span id="page-7-0"></span>Image /page/7/Picture/0 description: The image displays three grids of images, each containing 100 smaller images arranged in a 10x10 grid. The leftmost grid is labeled as "(a) Original CIFAR10 images." The middle grid is labeled as "(b) The synthesized images of GAFFE." The rightmost grid is labeled as "(c) The synthesized images of GAN." All three grids showcase a variety of subjects, including airplanes, cars, birds, cats, deer, dogs, horses, ships, and trucks. The images in grid (a) appear to be clear and distinct, representing the original dataset. The images in grids (b) and (c) are synthesized and show varying degrees of clarity and realism, with some images appearing more abstract or distorted than others.

(a) Original CIFAR10 images. (b) The synthetic images of CAFE. (c) The synthetic images of ours.

Figure 3: Visualizations of original images in CIFAR10, and the synthetic images generated by CAFE and our method. CAFE is initialized from random noise. The condensation ratio is 10 images per class.

<span id="page-7-1"></span>Image /page/7/Figure/5 description: This image displays four scatter plots, labeled (a) DC, (b) Intra-loss w/o center, (c) Intra-loss, and (d) Intra and Inter-loss. Each plot shows three clusters of data points represented by dots in gray, green, and orange. Additionally, triangles in gray, green, and orange are overlaid on the dots, representing synthesis data. The legend indicates that dots represent real data and triangles represent synthesis data. The plots illustrate the distribution of real and synthesized data across different loss functions.

Figure 4: Visualization for distribution of synthetic samples by DC [\[70\]](#page-10-2) and our method with different components of losses.

provement on three datasets, respectively. This indicates that acquiring realism may lead to less informative samples. When intra-class diversity loss is added, the test accuracy is improved by 0.2% and 0.3% on CIFAR-10/100 but dropped by 0.2% on ImageNet-sub. This is because that classes in this dataset are not completely mutually exclusive and the margins of each class are not clear. Our intra class diversity loss may cause synthetic samples to invade the margins of other classes. With inter-class discrimination loss, the performance gain-0.7%, 1.7%, 1.9% is expected since the loss makes the samples of different classes more discriminative. With the cooperation of inter-class and intra-class loss, our methods get the best results. Because synthesis images in each class are diverse, which have strong representative capabilities, and features of the different classes have a more clear margin at the same time.

#### 4.7. Visualizations

In this subsection, we visualize the synthetic images as well as their distribution from our method and CAFE [\[52\]](#page-9-7) in Fig[.3,](#page-7-0)[4.](#page-7-1) As shown in Fig[.3,](#page-7-0) some classes have very similar images synthesized by CAFE [\[52\]](#page-9-7). On the contrary, our synthetic images are more diverse on account of inter and intra-class loss. Moreover, as shown in Fig[.4,](#page-7-1) our distribution of synthesis images is more diverse than DC [\[70\]](#page-10-2). Specifically, Fig[.4](#page-7-1) (b) shows the distribution with intra-class diversity loss but without class center constraint. The distribution of samples with different classes is crossed because the samples are spread out too thinly. With the class center as a constraint (Fig[.4](#page-7-1)  $(c)$ ), the samples are distributed diversely and also are aligned within a meaningful class space. Moreover, equipped with inter-class dis-crimination loss as an addition (Fig[.4](#page-7-1)  $(d)$ ), the samples in different classes are more discriminative and have a more clear class margin. These visualizations prove the effectiveness of our intra-class diversity and inter-class discrimination loss.

# 5. Conclusions

In this paper, we explore a new condensed format, a generative model for dataset condensation. The dataset is condensed into a generative model rather than pixels. Such a new format brings feasibility of condensation on ImageNet-1K with diverse classes. Equipped with intra-class and inter-class loss, our condensed format also achieves the state-of-the-art performance on popular benchmarks.

## References

- <span id="page-8-2"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. In *NeurIPS*, 2019.
- <span id="page-8-6"></span>[2] Cenk Baykal, Murad Tukan, Dan Feldman, and Daniela Rus. Small coresets to represent large training data for support vector machines. 2018.
- <span id="page-8-1"></span>[3] Gedas Bertasius, Heng Wang, and Lorenzo Torresani. Is space-time attention all you need for video understanding? In *Proceedings of the International Conference on Machine Learning (ICML)*, 2021.
- <span id="page-8-25"></span>[4] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *NeurIPS Workshop*, 2020.
- <span id="page-8-17"></span>[5] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *arXiv preprint arXiv:1809.11096*, 2018.
- <span id="page-8-4"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 4750–4759, 2022.
- <span id="page-8-16"></span>[7] Xi Chen, Yan Duan, Rein Houthooft, John Schulman, Ilya Sutskever, and Pieter Abbeel. Infogan: Interpretable representation learning by information maximizing generative adversarial nets. In *Neural Information Processing Systems*, pages 2172–2180, 2016.
- <span id="page-8-14"></span>[8] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *UAI*, 2010.
- <span id="page-8-21"></span>[9] Casey Chu, Andrey Zhmoginov, and Mark Sandler. Cyclegan, a master of steganography. *arXiv preprint arXiv:1712.02950*, 2017.
- <span id="page-8-13"></span>[10] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-8-5"></span>[11] Zhou Daquan, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2023.
- <span id="page-8-8"></span>[12] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-8-12"></span>[13] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural net-

works. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.

- <span id="page-8-9"></span>[14] Tian Dong, Bo Zhao, and Lingjuan Liu. Privacy for free: How does dataset condensation help privacy? In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 5378–5396, 2022.
- <span id="page-8-0"></span>[15] Xiaoyi Dong, Jianmin Bao, Dongdong Chen, Weiming Zhang, Nenghai Yu, Lu Yuan, Dong Chen, and B. Guo. Cswin transformer: A general vision transformer backbone with cross-shaped windows. *ArXiv*, abs/2107.00652, 2021.
- <span id="page-8-24"></span>[16] Patrick Esser, Robin Rombach, and Bjorn Ommer. Taming transformers for high-resolution image synthesis. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 12873–12883, 2021.
- <span id="page-8-15"></span>[17] Dan Feldman, Melanie Schmidt, and Christian Sohler. Turning big data into tiny data: Constant-size coresets for kmeans, pca and projective clustering. In *SODA*, 2013.
- <span id="page-8-10"></span>[18] Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020.
- <span id="page-8-19"></span>[19] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial networks. *Communications of the ACM*, 63(11):139–144, 2020.
- <span id="page-8-27"></span>[20] Kaiming He, X. Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. *2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2016.
- <span id="page-8-18"></span>[21] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. In *NeurIPS*, 2020.
- <span id="page-8-7"></span>[22] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 11102– 11118, 2022.
- <span id="page-8-23"></span>[23] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009.
- <span id="page-8-26"></span>[24] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *Advances in Neural Information Processing Systems*, 2012.
- <span id="page-8-3"></span>[25] Piyush Kumar, Joseph SB Mitchell, and E Alper Yildirim. Approximate minimum enclosing balls in high dimensions using core-sets. *Journal of Experimental Algorithmics (JEA)*, 8:1–1, 2003.
- <span id="page-8-22"></span>[26] Yann LeCun. The mnist database of handwritten digits. *http://yann. lecun. com/exdb/mnist/*, 1998.
- <span id="page-8-20"></span>[27] Christian Ledig, Lucas Theis, Ferenc Huszár, Jose Caballero, Andrew Cunningham, Alejandro Acosta, Andrew Aitken, Alykhan Tejani, Johannes Totz, Zehan Wang, et al. Photorealistic single image super-resolution using a generative adversarial network. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 4681–4690, 2017.
- <span id="page-8-11"></span>[28] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with con-

trastive signals. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 12352–12364, 2022.

- <span id="page-9-25"></span>[29] Jianan Li, Xiaodan Liang, Yunchao Wei, Tingfa Xu, Jiashi Feng, and Shuicheng Yan. Perceptual generative adversarial networks for small object detection. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 1222– 1230, 2017.
- <span id="page-9-29"></span>[30] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-9-18"></span>[31] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset factorization for condensation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-9-10"></span>[32] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *ICCV-2023*, 2023.
- <span id="page-9-0"></span>[33] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2021.
- <span id="page-9-3"></span>[34] Ze Liu, Jia Ning, Yue Cao, Yixuan Wei, Zheng Zhang, S. Lin, and Han Hu. Video swin transformer. *ArXiv*, abs/2106.13230, 2021.
- <span id="page-9-21"></span>[35] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014.
- <span id="page-9-4"></span>[36] Daniel Neimark, Omri Bar, Maya Zohar, and Dotan Asselmann. Video transformer network. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2021.
- <span id="page-9-17"></span>[37] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021.
- <span id="page-9-22"></span>[38] Augustus Odena, Christopher Olah, and Jonathon Shlens. Conditional image synthesis with auxiliary classifier gans. In *International conference on machine learning*, pages 2642– 2651. PMLR, 2017.
- <span id="page-9-2"></span>[39] Mandela Patrick, Dylan Campbell, Yuki Asano, Ishan Misra, Florian Metze, Christoph Feichtenhofer, Andrea Vedaldi, and João F Henriques. Keeping your eye on the ball: Trajectory attention in video transformers. *Advances in Neural Information Processing Systems*, 2021.
- <span id="page-9-8"></span>[40] Ziheng Qin, Kai Wang, Zangwei Zheng, Jianyang Gu, Xiangyu Peng, Daquan Zhou, and Yang You. Infobatch: Lossless training speed up by unbiased dynamic data pruning. *arXiv preprint arXiv:2303.04947*, 2023.
- <span id="page-9-28"></span>[41] Axel Sauer, Katja Schwarz, and Andreas Geiger. Styleganxl: Scaling stylegan to large diverse datasets. volume abs/2201.00273, 2022.
- <span id="page-9-6"></span>[42] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *ICLR*, 2018.
- <span id="page-9-12"></span>[43] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.

- <span id="page-9-27"></span>[44] Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *ICPR*, 2012.
- <span id="page-9-30"></span>[45] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-9-14"></span>[46] Ilia Sucholutsky and Matthias Schonlau. Secdd: Efficient and secure method for remotely training neural networks. *arXiv preprint arXiv:2009.09155*, 2020.
- <span id="page-9-16"></span>[47] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *Proceedings of the International Joint Conference on Neural Networks (IJCNN)*, pages 1–8, 2021.
- <span id="page-9-20"></span>[48] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *ICLR*, 2019.
- <span id="page-9-11"></span>[49] Simon Tong and Daphne Koller. Support vector machine active learning with applications to text classification. *JMLR*, 2(Nov):45–66, 2001.
- <span id="page-9-15"></span>[50] Nikolaos Tsilivis, Jingtong Su, and Julia Kempe. Can we achieve robustness from data alone? *arXiv preprint arXiv:2207.11727*, 2022.
- <span id="page-9-9"></span>[51] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-9-7"></span>[52] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *arXiv preprint arXiv:2203.01531*, 2022.
- <span id="page-9-5"></span>[53] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-9-23"></span>[54] Ting-Chun Wang, Ming-Yu Liu, Jun-Yan Zhu, Andrew Tao, Jan Kautz, and Bryan Catanzaro. High-resolution image synthesis and semantic manipulation with conditional gans. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 8798–8807, 2018.
- <span id="page-9-1"></span>[55] Wenhai Wang, Enze Xie, Xiang Li, Deng-Ping Fan, Kaitao Song, Ding Liang, Tong Lu, P. Luo, and L. Shao. Pyramid vision transformer: A versatile backbone for dense prediction without convolutions. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2021.
- <span id="page-9-24"></span>[56] Xiaolong Wang, Abhinav Shrivastava, and Abhinav Gupta. A-fast-rcnn: Hard positive generation via adversary for object detection. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 2606–2615, 2017.
- <span id="page-9-19"></span>[57] Kai Wei, Rishabh Iver, and Jeff Bilmes. Submodularity in data subset selection and active learning. In *ICML*, 2015.
- <span id="page-9-13"></span>[58] Yandong Wen, Kaipeng Zhang, Zhifeng Li, and Yu Qiao. A discriminative feature learning approach for deep face recognition. In *European conference on computer vision*, pages 499–515. Springer, 2016.
- <span id="page-9-26"></span>[59] Yongqin Xian, Tobias Lorenz, Bernt Schiele, and Zeynep Akata. Feature generating networks for zero-shot learning. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 5542–5551, 2018.

- <span id="page-10-13"></span>[60] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-10-9"></span>[61] Raymond A Yeh, Chen Chen, Teck Yian Lim, Alexander G Schwing, Mark Hasegawa-Johnson, and Minh N Do. Semantic image inpainting with deep generative models. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 5485–5493, 2017.
- <span id="page-10-8"></span>[62] Jiahui Yu, Zhe Lin, Jimei Yang, Xiaohui Shen, Xin Lu, and Thomas S Huang. Generative image inpainting with contextual attention. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 5505–5514, 2018.
- <span id="page-10-12"></span>[63] Zehao Yu, Jia Zheng, Dongze Lian, Zihan Zhou, and Shenghua Gao. Single-image piece-wise planar 3d reconstruction via associative embedding. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 1029–1037, 2019.
- <span id="page-10-0"></span>[64] Li Yuan, Y. Chen, Tao Wang, Weihao Yu, Yujun Shi, Francis E. H. Tay, Jiashi Feng, and Shuicheng Yan. Tokens-to-token vit: Training vision transformers from scratch on imagenet. *ArXiv*, abs/2101.11986, 2021.
- <span id="page-10-1"></span>[65] Yanyi Zhang, Xinyu Li, Chunhui Liu, Bing Shuai, Yi Zhu, Biagio Brattoli, Hao Chen, Ivan Marsic, and Joseph Tighe. Vidtr: Video transformer without convolutions. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2021.
- <span id="page-10-10"></span>[66] Yuxuan Zhang, Huan Ling, Jun Gao, Kangxue Yin, Jean-Francois Lafleche, Adela Barriuso, Antonio Torralba, and Sanja Fidler. Datasetgan: Efficient labeled data factory with minimal human effort. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 10145–10155, 2021.
- <span id="page-10-5"></span>[67] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-10-3"></span>[68] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021.
- <span id="page-10-11"></span>[69] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-10-2"></span>[70] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020.
- <span id="page-10-6"></span>[71] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-10-4"></span>[72] Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020.
- <span id="page-10-7"></span>[73] Jun-Yan Zhu, Philipp Krähenbühl, Eli Shechtman, and Alexei A Efros. Generative visual manipulation on the natural image manifold. In *European Conference on Computer Vision*, pages 597–613, 2016.