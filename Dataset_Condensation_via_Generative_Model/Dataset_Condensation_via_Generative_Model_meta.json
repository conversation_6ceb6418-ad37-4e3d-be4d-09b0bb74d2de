{"table_of_contents": [{"title": "Dataset Condensation via Generative Model", "heading_level": null, "page_id": 0, "polygon": [[161.25, 105.75], [433.5, 105.75], [433.5, 119.302734375], [161.25, 119.302734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.4833984375, 228.0], [191.25, 228.0], [191.25, 238.9921875], [144.4833984375, 238.9921875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 597.0], [127.5, 597.0], [127.5, 607.921875], [48.75, 607.921875]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 384.0], [392.25, 384.0], [392.25, 395.419921875], [307.5, 395.419921875]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[307.5, 186.0], [361.5, 186.0], [361.5, 197.419921875], [307.5, 197.419921875]]}, {"title": "3.1. Overview", "heading_level": null, "page_id": 2, "polygon": [[307.5, 206.25], [373.5, 206.25], [373.5, 217.529296875], [307.5, 217.529296875]]}, {"title": "3.2. Condense Dataset into Generative Model", "heading_level": null, "page_id": 2, "polygon": [[307.5, 396.0], [520.5, 396.0], [520.5, 406.0546875], [307.5, 406.0546875]]}, {"title": "3.3. Intra-Class Diversity and Inter-Class Discrim-\nination.", "heading_level": null, "page_id": 3, "polygon": [[307.5, 492.75], [544.5, 492.75], [544.5, 514.72265625], [307.5, 514.72265625]]}, {"title": "3.4. Optimization Framework", "heading_level": null, "page_id": 4, "polygon": [[48.0, 432.75], [189.75, 432.75], [189.75, 443.1796875], [48.0, 443.1796875]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 629.25], [128.25, 629.25], [128.25, 639.75], [48.75, 639.75]]}, {"title": "4.1. Dataset", "heading_level": null, "page_id": 4, "polygon": [[48.75, 648.75], [105.75, 648.75], [105.75, 658.96875], [48.75, 658.96875]]}, {"title": "4.2. Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[307.5, 469.86328125], [438.380859375, 469.86328125], [438.380859375, 479.14453125], [307.5, 479.14453125]]}, {"title": "4.3. <PERSON><PERSON><PERSON><PERSON> with State-Of-The-Art", "heading_level": null, "page_id": 5, "polygon": [[48.75, 565.5], [240.0, 565.5], [240.0, 575.4375], [48.75, 575.4375]]}, {"title": "4.4. ImageNet-1K Results", "heading_level": null, "page_id": 5, "polygon": [[307.5, 507.0], [428.25, 507.0], [428.25, 517.81640625], [307.5, 517.81640625]]}, {"title": "4.5. Comp<PERSON><PERSON> with Other Generative Models", "heading_level": null, "page_id": 6, "polygon": [[48.75, 251.25], [277.5, 251.25], [277.5, 261.228515625], [48.75, 261.228515625]]}, {"title": "4.6. Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[307.5, 153.0], [399.75, 153.0], [399.75, 163.6787109375], [307.5, 163.6787109375]]}, {"title": "4.7. Visualizations", "heading_level": null, "page_id": 7, "polygon": [[48.75, 671.25], [135.0, 671.25], [135.0, 682.55859375], [48.75, 682.55859375]]}, {"title": "5. Conclusions", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.0], [124.5, 72.0], [124.5, 83.8212890625], [48.75, 83.8212890625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 210.75], [106.5, 210.75], [106.5, 221.58984375], [48.75, 221.58984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 81], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 1], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5232, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 104], ["Text", 8], ["ListItem", 4], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 97], ["TableCell", 40], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 104], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 758, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 737], ["Line", 126], ["TableCell", 36], ["Text", 8], ["SectionHeader", 4], ["TextInlineMath", 3], ["Equation", 2], ["Table", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 635], ["TableCell", 289], ["Line", 84], ["Text", 5], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Reference", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 3, "llm_tokens_used": 1430, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["TableCell", 169], ["Line", 109], ["Text", 5], ["Reference", 4], ["Table", 3], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 16336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 44], ["Caption", 4], ["Text", 3], ["Reference", 2], ["Picture", 1], ["Figure", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1346, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 109], ["ListItem", 28], ["Reference", 28], ["SectionHeader", 2], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 114], ["ListItem", 31], ["Reference", 31], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 51], ["ListItem", 14], ["Reference", 14], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_via_Generative_Model"}