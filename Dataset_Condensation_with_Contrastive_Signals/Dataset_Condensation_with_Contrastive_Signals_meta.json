{"table_of_contents": [{"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 186.8818359375], [148.5, 186.8818359375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 576.0], [132.75, 576.0], [132.75, 587.42578125], [54.0, 587.42578125]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[54.0, 449.25], [139.7021484375, 449.25], [139.7021484375, 460.1953125], [54.0, 460.1953125]]}, {"title": "3. Method", "heading_level": null, "page_id": 1, "polygon": [[306.0, 547.5], [359.25, 547.5], [359.25, 558.80859375], [306.0, 558.80859375]]}, {"title": "3.1. Preliminary: DC with Grad<PERSON> Matching", "heading_level": null, "page_id": 1, "polygon": [[306.0, 663.75], [503.25, 665.25], [503.25, 675.0], [305.25, 674.82421875]]}, {"title": "3.2. A Motivating Example", "heading_level": null, "page_id": 2, "polygon": [[54.0, 361.5], [170.25, 361.5], [170.25, 371.443359375], [54.0, 371.443359375]]}, {"title": "3.3. Dataset Condensation with Contrastive Signals", "heading_level": null, "page_id": 3, "polygon": [[306.0, 507.75], [525.75, 507.75], [525.75, 517.81640625], [306.0, 517.81640625]]}, {"title": "4. Experimental Results and Discussion", "heading_level": null, "page_id": 5, "polygon": [[54.0, 108.75], [257.25, 108.75], [257.25, 119.8828125], [54.0, 119.8828125]]}, {"title": "4.1. Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[54.0, 129.75], [159.0, 129.75], [159.0, 139.60546875], [54.0, 139.60546875]]}, {"title": "4.2. Dataset Condensation", "heading_level": null, "page_id": 5, "polygon": [[54.0, 565.5], [167.25, 565.5], [167.25, 575.82421875], [54.0, 575.82421875]]}, {"title": "4.3. Application: Continual Learning", "heading_level": null, "page_id": 7, "polygon": [[306.0, 449.25], [465.0, 449.25], [465.0, 460.1953125], [306.0, 460.1953125]]}, {"title": "5. Conclusion and Future Directions", "heading_level": null, "page_id": 8, "polygon": [[54.0, 67.5], [240.0, 67.5], [240.0, 79.5673828125], [54.0, 79.5673828125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 546.75], [111.75, 546.75], [111.75, 558.421875], [54.0, 558.421875]]}, {"title": "A. A Motivating Example", "heading_level": null, "page_id": 10, "polygon": [[54.0, 68.25], [186.75, 68.25], [186.75, 78.9873046875], [54.0, 78.9873046875]]}, {"title": "B. Application: Continual Learning", "heading_level": null, "page_id": 11, "polygon": [[54.0, 344.953125], [237.8671875, 344.953125], [237.8671875, 356.5546875], [54.0, 356.5546875]]}, {"title": "C. Datasets", "heading_level": null, "page_id": 11, "polygon": [[54.0, 416.8828125], [114.75, 416.8828125], [114.75, 428.484375], [54.0, 428.484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 83], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4439, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 103], ["Text", 6], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1199], ["Line", 166], ["TextInlineMath", 7], ["Reference", 7], ["Equation", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 646], ["Line", 134], ["Text", 6], ["Reference", 4], ["TextInlineMath", 3], ["Equation", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3012, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["Line", 101], ["ListItem", 27], ["Text", 4], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["TableCell", 184], ["Line", 89], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Footnote", 1], ["Table", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4487, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["TableCell", 185], ["Line", 83], ["Text", 5], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["TableCell", 215], ["Line", 91], ["Caption", 4], ["Reference", 4], ["Table", 3], ["Text", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 10266, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 97], ["ListItem", 14], ["Reference", 14], ["SectionHeader", 2], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 87], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1411], ["Line", 351], ["TextInlineMath", 4], ["Equation", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 30], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 35], ["TableCell", 28], ["Line", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3151, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_with_Contrastive_Signals"}