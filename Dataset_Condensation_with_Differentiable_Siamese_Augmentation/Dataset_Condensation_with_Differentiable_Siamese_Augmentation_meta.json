{"table_of_contents": [{"title": "Dataset Condensation with Differentiable Siamese Augmentation", "heading_level": null, "page_id": 0, "polygon": [[97.5, 89.25], [498.0, 89.25], [498.0, 103.447265625], [97.5, 103.447265625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[149.25, 174.0], [195.75, 174.0], [195.75, 185.044921875], [149.25, 185.044921875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 498.0], [132.75, 498.0], [132.75, 509.30859375], [54.0, 509.30859375]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[306.0, 228.0], [391.5, 228.0], [391.5, 239.572265625], [306.0, 239.572265625]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 420.75], [108.17578125, 420.75], [108.17578125, 431.578125], [54.0, 431.578125]]}, {"title": "3.1. Dataset Condensation Review", "heading_level": null, "page_id": 2, "polygon": [[54.0, 477.75], [200.25, 477.75], [200.25, 488.0390625], [54.0, 488.0390625]]}, {"title": "3.2. Differentiable Siamese Augmentation (DSA)", "heading_level": null, "page_id": 2, "polygon": [[306.0, 538.5], [513.75, 538.5], [513.75, 548.3671875], [306.0, 548.3671875]]}, {"title": "3.3. Training Algorithm", "heading_level": null, "page_id": 3, "polygon": [[54.0, 593.25], [157.5, 593.25], [157.5, 603.28125], [54.0, 603.28125]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 3, "polygon": [[305.25, 280.5], [385.5, 280.5], [385.5, 291.5859375], [305.25, 291.5859375]]}, {"title": "4.1. Datasets & Implementation Details", "heading_level": null, "page_id": 3, "polygon": [[306.0, 301.5], [474.75, 301.5], [474.75, 310.921875], [306.0, 310.921875]]}, {"title": "4.2. <PERSON><PERSON><PERSON><PERSON> to State-of-the-Art", "heading_level": null, "page_id": 4, "polygon": [[54.0, 581.23828125], [207.75, 581.23828125], [207.75, 590.51953125], [54.0, 590.51953125]]}, {"title": "4.3. Cross-Architecture Generalization", "heading_level": null, "page_id": 6, "polygon": [[54.0, 69.0], [220.5, 69.0], [220.5, 79.22900390625], [54.0, 79.22900390625]]}, {"title": "4.4. Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[54.0, 268.5], [138.0, 268.5], [138.0, 278.05078125], [54.0, 278.05078125]]}, {"title": "4.5. Continual Learning", "heading_level": null, "page_id": 6, "polygon": [[306.0, 593.2265625], [410.25, 593.2265625], [410.25, 602.5078125], [306.0, 602.5078125]]}, {"title": "4.6. Neural Architecture Search", "heading_level": null, "page_id": 7, "polygon": [[54.0, 652.5], [190.5, 652.5], [190.5, 662.8359375], [54.0, 662.8359375]]}, {"title": "5. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 8, "polygon": [[54.0, 487.5], [122.25, 487.5], [122.25, 498.48046875], [54.0, 498.48046875]]}, {"title": "5.1. Why Does DSA Work?", "heading_level": null, "page_id": 8, "polygon": [[54.0, 507.75], [170.25, 507.75], [170.25, 518.203125], [54.0, 518.203125]]}, {"title": "5.2. Initializing Synthetic Images", "heading_level": null, "page_id": 8, "polygon": [[306.0, 343.5], [447.75, 343.5], [447.75, 353.267578125], [306.0, 353.267578125]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 521.25], [377.25, 521.25], [377.25, 532.51171875], [306.0, 532.51171875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.0, 68.25], [111.0, 79.42236328125], [54.0, 79.42236328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 90], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4498, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 105], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 103], ["Text", 6], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1792, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["Line", 109], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 687], ["Line", 117], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8398, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 614], ["TableCell", 169], ["Line", 139], ["Caption", 5], ["Text", 5], ["Table", 3], ["TableGroup", 3], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 8016, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 367], ["TableCell", 106], ["Line", 105], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7538, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["Line", 126], ["TableCell", 100], ["Text", 6], ["Caption", 4], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4146, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 106], ["TableCell", 50], ["Text", 7], ["Caption", 4], ["SectionHeader", 4], ["<PERSON>Footer", 2], ["Table", 1], ["Figure", 1], ["Equation", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2155, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 94], ["ListItem", 25], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 94], ["ListItem", 25], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 44], ["ListItem", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_with_Differentiable_Siamese_Augmentation"}