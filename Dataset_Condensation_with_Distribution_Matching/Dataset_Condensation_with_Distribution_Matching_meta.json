{"table_of_contents": [{"title": "Dataset Condensation with Distribution Matching", "heading_level": null, "page_id": 0, "polygon": [[142.83984375, 106.5], [450.75, 106.5], [450.75, 119.3994140625], [142.83984375, 119.3994140625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 213.75], [191.25, 213.75], [191.25, 225.650390625], [144.75, 225.650390625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 513.0], [128.3466796875, 513.0], [128.3466796875, 524.00390625], [48.75, 524.00390625]]}, {"title": "2. Methodology", "heading_level": null, "page_id": 1, "polygon": [[307.5, 203.25], [387.87890625, 203.25], [387.87890625, 214.2421875], [307.5, 214.2421875]]}, {"title": "2.1. Dataset Condensation Problem", "heading_level": null, "page_id": 1, "polygon": [[307.5, 221.25], [473.25, 222.75], [473.25, 232.611328125], [307.5, 232.611328125]]}, {"title": "2.2. Dataset Condensation with Distribution\nMatching", "heading_level": null, "page_id": 2, "polygon": [[48.0, 239.25], [287.25, 239.25], [287.25, 261.228515625], [48.0, 261.228515625]]}, {"title": "2.3. Training Algorithm", "heading_level": null, "page_id": 2, "polygon": [[307.5, 313.5], [420.75, 313.5], [420.75, 323.876953125], [307.5, 323.876953125]]}, {"title": "2.4. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[307.1953125, 457.5], [377.25, 457.5], [377.25, 467.54296875], [307.1953125, 467.54296875]]}, {"title": "3. Experiments", "heading_level": null, "page_id": 3, "polygon": [[48.75, 592.453125], [128.25, 592.453125], [128.25, 604.0546875], [48.75, 604.0546875]]}, {"title": "3.1. <PERSON> Settings", "heading_level": null, "page_id": 3, "polygon": [[48.0, 611.7890625], [174.216796875, 611.7890625], [174.216796875, 623.390625], [48.0, 623.390625]]}, {"title": "3.2. Comp<PERSON>on to the State-of-the-art", "heading_level": null, "page_id": 4, "polygon": [[48.0, 142.5], [233.384765625, 142.5], [233.384765625, 153.720703125], [48.0, 153.720703125]]}, {"title": "3.3. Cross-architecture Generalization", "heading_level": null, "page_id": 5, "polygon": [[307.1953125, 636.15234375], [487.5, 636.15234375], [487.5, 646.98046875], [307.1953125, 646.98046875]]}, {"title": "3.4. Ablation Study on Network Distribution", "heading_level": null, "page_id": 6, "polygon": [[48.0, 636.75], [258.0, 636.75], [258.0, 646.98046875], [48.0, 646.98046875]]}, {"title": "3.5. Continual Learning", "heading_level": null, "page_id": 6, "polygon": [[307.5, 467.25], [421.5, 467.25], [421.5, 478.7578125], [307.5, 478.7578125]]}, {"title": "3.6. Neural Architecture Search", "heading_level": null, "page_id": 7, "polygon": [[48.0, 468.0], [198.0, 468.0], [198.0, 478.7578125], [48.0, 478.7578125]]}, {"title": "4. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.494140625, 435.0], [378.75, 435.0], [378.75, 445.88671875], [307.494140625, 445.88671875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 84.2080078125], [48.75, 84.2080078125]]}, {"title": "A. Implementation details", "heading_level": null, "page_id": 10, "polygon": [[48.75, 72.4130859375], [183.75, 72.4130859375], [183.75, 84.0146484375], [48.75, 84.0146484375]]}, {"title": "A.1. Dataset Condensation", "heading_level": null, "page_id": 10, "polygon": [[48.75, 92.25], [174.75, 92.25], [174.75, 103.640625], [48.75, 103.640625]]}, {"title": "A.2. Continual Learning", "heading_level": null, "page_id": 10, "polygon": [[48.75, 310.341796875], [165.75, 310.341796875], [165.75, 321.943359375], [48.75, 321.943359375]]}, {"title": "A.3. Neural Architecture Search", "heading_level": null, "page_id": 10, "polygon": [[48.75, 504.28125], [201.75, 504.28125], [201.75, 515.8828125], [48.75, 515.8828125]]}, {"title": "<PERSON><PERSON>mp<PERSON>on to More Baselines and Related\nWorks", "heading_level": null, "page_id": 10, "polygon": [[307.5, 107.25], [546.75, 107.25], [546.75, 132.3544921875], [307.5, 132.3544921875]]}, {"title": "B.1. Comparison to Generative Models", "heading_level": null, "page_id": 10, "polygon": [[307.5, 140.37890625], [492.0, 140.37890625], [492.0, 151.59375], [307.5, 151.59375]]}, {"title": "B.2. <PERSON><PERSON><PERSON><PERSON> to MMD Baseline", "heading_level": null, "page_id": 10, "polygon": [[307.5, 611.7890625], [473.25, 611.7890625], [473.25, 622.6171875], [307.5, 622.6171875]]}, {"title": "B.3. Comparison to GTN and KIP Methods", "heading_level": null, "page_id": 11, "polygon": [[48.0, 307.5], [255.0, 307.5], [255.0, 318.65625], [48.0, 318.65625]]}, {"title": "C. Extended Visualization and Analysis", "heading_level": null, "page_id": 11, "polygon": [[48.75, 611.25], [252.0, 611.25], [252.0, 623.00390625], [48.75, 623.00390625]]}, {"title": "D. Connection to Gradient Matching", "heading_level": null, "page_id": 11, "polygon": [[307.5, 349.5], [498.0, 349.5], [498.0, 360.80859375], [307.5, 360.80859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 85], ["Text", 7], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5975, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 609], ["Line", 119], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1803, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 643], ["Line", 130], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 3], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 87], ["Text", 9], ["ListItem", 4], ["SectionHeader", 2], ["TextInlineMath", 1], ["Code", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 133], ["Text", 9], ["SectionHeader", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 638, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 681], ["TableCell", 279], ["Line", 96], ["Text", 4], ["Reference", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 13017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 86], ["TableCell", 60], ["Text", 6], ["Reference", 4], ["Caption", 3], ["Figure", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2975, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 126], ["TableCell", 24], ["Text", 7], ["Reference", 4], ["Figure", 3], ["Caption", 3], ["SectionHeader", 2], ["Table", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 8116, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 378], ["Line", 113], ["ListItem", 32], ["Reference", 32], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 75], ["ListItem", 21], ["Reference", 21], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 99], ["TableCell", 14], ["Text", 10], ["SectionHeader", 7], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 133], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4195, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 84], ["Text", 5], ["Equation", 3], ["TextInlineMath", 2], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1730, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_with_Distribution_Matching"}