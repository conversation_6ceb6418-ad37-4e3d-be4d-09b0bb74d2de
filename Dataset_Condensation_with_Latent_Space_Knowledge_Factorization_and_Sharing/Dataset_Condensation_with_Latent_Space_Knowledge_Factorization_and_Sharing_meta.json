{"table_of_contents": [{"title": "DATASET CONDENSATION WITH LATENT SPACE\nK<PERSON><PERSON>LED<PERSON> FACTORIZATION AND SHARING", "heading_level": null, "page_id": 0, "polygon": [[106.5, 81.0], [458.40234375, 81.0], [458.40234375, 116.4990234375], [106.5, 116.4990234375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 200.25], [334.6875, 200.25], [334.6875, 210.76171875], [276.75, 210.76171875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 417.0], [207.5361328125, 417.0], [207.5361328125, 426.9375], [107.25, 426.9375]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[106.75634765625, 690.0], [212.25, 690.0], [212.25, 700.734375], [106.75634765625, 700.734375]]}, {"title": "3 APPROACH", "heading_level": null, "page_id": 2, "polygon": [[107.25, 608.25], [183.75, 608.25], [183.75, 620.296875], [107.25, 620.296875]]}, {"title": "3.1 K<PERSON>OWLEDGE FACTORIZATION AND SHARING IN LATENT SPACE", "heading_level": null, "page_id": 2, "polygon": [[106.5, 672.0], [404.61328125, 672.0], [404.61328125, 682.171875], [106.5, 682.171875]]}, {"title": "3.2 TRAINING WITH UNBIASED AND LOW-VARIANCE DISTRIBUTION MATCHING", "heading_level": null, "page_id": 3, "polygon": [[106.5, 493.83984375], [457.8046875, 493.83984375], [457.8046875, 503.89453125], [106.5, 503.89453125]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 379.5], [200.8125, 379.5], [200.8125, 390.19921875], [106.98046875, 390.19921875]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 7, "polygon": [[107.25, 122.8798828125], [194.537109375, 122.8798828125], [194.537109375, 134.2880859375], [107.25, 134.2880859375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 7, "polygon": [[107.25, 284.818359375], [176.607421875, 284.818359375], [176.607421875, 295.259765625], [107.25, 295.259765625]]}, {"title": "A EXPERIMENTAL SETUP", "heading_level": null, "page_id": 9, "polygon": [[107.25, 81.75], [249.22265625, 81.75], [249.22265625, 93.97265625], [107.25, 93.97265625]]}, {"title": "B DERIVATION OF EQ. (2)", "heading_level": null, "page_id": 10, "polygon": [[106.5, 81.0], [252.0615234375, 81.0], [252.0615234375, 93.779296875], [106.5, 93.779296875]]}, {"title": "C DERIVATION OF EQ. (3)", "heading_level": null, "page_id": 11, "polygon": [[106.5, 79.5], [251.61328125, 81.0], [251.61328125, 96.0], [106.5, 95.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 51], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3270, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 57], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 56], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 530], ["Line", 87], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 121], ["TextInlineMath", 5], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1207, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1052], ["TableCell", 538], ["Line", 63], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13346, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 74], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1860, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 49], ["ListItem", 11], ["Reference", 11], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 44], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["TableCell", 108], ["Line", 47], ["ListItem", 5], ["Reference", 4], ["Caption", 3], ["Table", 3], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 5166, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 740], ["Line", 189], ["Text", 3], ["Equation", 3], ["Reference", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 1196], ["Line", 366], ["Equation", 4], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 21347, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["Line", 75], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1543, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Condensation_with_Latent_Space_Knowledge_Factorization_and_Sharing"}