{"table_of_contents": [{"title": "DATASET DISTILLATION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 81.75], [286.875, 81.75], [286.875, 96.53466796875], [107.25, 96.53466796875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 207.75], [334.986328125, 207.75], [334.986328125, 218.689453125], [276.75, 218.689453125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 389.25], [206.7890625, 389.25], [206.7890625, 399.48046875], [107.25, 399.48046875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[107.25, 675.75], [213.2138671875, 675.75], [213.2138671875, 687.5859375], [107.25, 687.5859375]]}, {"title": "3 APPROACH", "heading_level": null, "page_id": 2, "polygon": [[107.25, 524.25], [183.75, 524.25], [183.75, 534.83203125], [107.25, 534.83203125]]}, {"title": "3.1 OPTIMIZING DISTILLED DATA", "heading_level": null, "page_id": 3, "polygon": [[107.25, 130.5], [260.279296875, 130.5], [260.279296875, 139.798828125], [107.25, 139.798828125]]}, {"title": "3.2 DISTILLATION FOR RANDOM INITIALIZATIONS", "heading_level": null, "page_id": 3, "polygon": [[107.25, 375.0], [330.75, 375.0], [330.75, 384.3984375], [107.25, 384.3984375]]}, {"title": "3.3 ANALYSIS OF A SIMPLE LINEAR CASE", "heading_level": null, "page_id": 3, "polygon": [[106.5, 599.25], [296.25, 599.25], [296.25, 609.46875], [106.5, 609.46875]]}, {"title": "Algorithm 1 Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[106.5, 83.25], [239.361328125, 83.25], [239.361328125, 93.15087890625], [106.5, 93.15087890625]]}, {"title": "3.4 MULTIPLE GRADIENT DESCENT STEPS AND MULTIPLE EPOCHS", "heading_level": null, "page_id": 4, "polygon": [[106.5, 566.25], [403.5, 566.25], [403.5, 575.4375], [106.5, 575.4375]]}, {"title": "3.5 DISTILLATION WITH DIFFERENT INITIALIZATIONS", "heading_level": null, "page_id": 5, "polygon": [[107.25, 145.3095703125], [345.75, 145.3095703125], [345.75, 154.9775390625], [107.25, 154.9775390625]]}, {"title": "3.6 DISTILLATION WITH DIFFERENT OBJECTIVES", "heading_level": null, "page_id": 5, "polygon": [[107.25, 393.0], [326.25, 393.0], [326.25, 402.9609375], [107.25, 402.9609375]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 6, "polygon": [[107.05517578125, 81.75], [201.75, 81.75], [201.75, 93.392578125], [107.05517578125, 93.392578125]]}, {"title": "4.1 DATASET DISTILLATION", "heading_level": null, "page_id": 6, "polygon": [[106.5, 349.400390625], [236.5224609375, 349.400390625], [236.5224609375, 359.841796875], [106.5, 359.841796875]]}, {"title": "4.2 DISTILLATION WITH DIFFERENT INITIALIZATIONS AND OBJECTIVES", "heading_level": null, "page_id": 6, "polygon": [[106.5, 679.5], [423.73828125, 679.5], [423.73828125, 689.90625], [106.5, 689.90625]]}, {"title": "5 DISCUSSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 430.5], [191.25, 430.5], [191.25, 442.40625], [107.25, 442.40625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[106.681640625, 82.5], [176.009765625, 82.5], [176.009765625, 93.1025390625], [106.681640625, 93.1025390625]]}, {"title": "S-1 SUPPLEMENTARY MATERIAL", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 81.75], [287.3232421875, 81.75], [287.3232421875, 93.779296875], [107.1298828125, 93.779296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 54], ["Text", 8], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4373, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 85], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 979, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 63], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 781], ["Line", 67], ["TextInlineMath", 7], ["Equation", 5], ["Reference", 4], ["Text", 3], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 887], ["Line", 74], ["Text", 6], ["TextInlineMath", 6], ["ListItem", 6], ["Equation", 4], ["Reference", 4], ["Code", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 53], ["Text", 7], ["ListItem", 4], ["Reference", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 52], ["Text", 5], ["ListItem", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["TableCell", 68], ["Line", 56], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 729], ["Line", 91], ["TableCell", 58], ["Caption", 4], ["Figure", 3], ["Text", 3], ["Reference", 3], ["Table", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 5740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 76], ["Text", 4], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 955, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 47], ["Reference", 24], ["ListItem", 23], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 48], ["Reference", 24], ["ListItem", 22], ["ListGroup", 2], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 11], ["Reference", 6], ["ListItem", 4], ["Text", 4], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 19], ["ListItem", 4], ["Text", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation"}