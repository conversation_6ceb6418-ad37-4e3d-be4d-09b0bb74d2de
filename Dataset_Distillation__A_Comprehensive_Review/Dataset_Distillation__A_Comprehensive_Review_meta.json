{"table_of_contents": [{"title": "Dataset Distillation: A Comprehensive Review", "heading_level": null, "page_id": 0, "polygon": [[61.5, 54.6240234375], [550.44140625, 54.6240234375], [550.44140625, 79.9541015625], [61.5, 79.9541015625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 288.0], [140.2998046875, 288.0], [140.2998046875, 297.966796875], [48.0, 297.966796875]]}, {"title": "2 RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[47.25, 519.75], [154.5, 519.75], [154.5, 530.19140625], [47.25, 530.19140625]]}, {"title": "2.1 Knowledge Distillation", "heading_level": null, "page_id": 1, "polygon": [[46.5, 640.5], [173.25, 640.5], [173.25, 651.234375], [46.5, 651.234375]]}, {"title": "2.2 Core-set or Instance Selection", "heading_level": null, "page_id": 1, "polygon": [[310.5, 445.5], [471.75, 444.75], [471.75, 454.78125], [310.5, 454.78125]]}, {"title": "2.3 Generative Models", "heading_level": null, "page_id": 2, "polygon": [[46.5, 235.5], [156.0, 235.5], [156.0, 245.56640625], [46.5, 245.56640625]]}, {"title": "2.4 Hyperparameter Optimization", "heading_level": null, "page_id": 2, "polygon": [[45.75, 570.75], [204.0, 570.75], [204.0, 580.078125], [45.75, 580.078125]]}, {"title": "3 DATASET DISTILLATION METHODS", "heading_level": null, "page_id": 2, "polygon": [[311.080078125, 290.25], [498.75, 290.25], [498.75, 301.25390625], [311.080078125, 301.25390625]]}, {"title": "3.1 Definition of Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[310.5, 507.0], [478.5, 507.0], [478.5, 515.8828125], [310.5, 515.8828125]]}, {"title": "3.2 General Workflow of Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[46.5, 255.0], [251.25, 255.0], [251.25, 264.708984375], [46.5, 264.708984375]]}, {"title": "3.3 Optimization Objectives in DD", "heading_level": null, "page_id": 3, "polygon": [[310.5, 43.5], [471.75, 43.5], [471.75, 53.25], [310.5, 53.25]]}, {"title": "3.3.1 Performance Matching", "heading_level": null, "page_id": 3, "polygon": [[311.25, 152.4638671875], [438.0, 152.4638671875], [438.0, 161.5517578125], [311.25, 161.5517578125]]}, {"title": "3.3.2 Parameter Matching", "heading_level": null, "page_id": 4, "polygon": [[47.0654296875, 688.5], [165.0, 688.5], [165.0, 698.02734375], [47.0654296875, 698.02734375]]}, {"title": "3.3.3 Distribution Matching", "heading_level": null, "page_id": 6, "polygon": [[46.5, 617.203125], [167.25, 617.203125], [167.25, 626.484375], [46.5, 626.484375]]}, {"title": "3.3.4 Connections between Objectives in DD", "heading_level": null, "page_id": 6, "polygon": [[309.5859375, 688.5], [509.80078125, 688.5], [509.80078125, 697.640625], [309.5859375, 697.640625]]}, {"title": "3.4 Synthetic Data Parameterization", "heading_level": null, "page_id": 8, "polygon": [[46.5, 512.25], [216.0, 512.25], [216.0, 522.0703125], [46.5, 522.0703125]]}, {"title": "3.4.1 Differentiable Siamese Augmentation", "heading_level": null, "page_id": 8, "polygon": [[311.25, 172.5], [500.25, 172.5], [500.25, 181.951171875], [311.25, 181.951171875]]}, {"title": "3.4.2 Upsampling", "heading_level": null, "page_id": 8, "polygon": [[311.25, 372.0], [393.85546875, 372.0], [393.85546875, 381.69140625], [311.25, 381.69140625]]}, {"title": "3.4.3 Generators and Latent Vectors", "heading_level": null, "page_id": 8, "polygon": [[311.25, 525.75], [471.75, 525.75], [471.75, 535.60546875], [311.25, 535.60546875]]}, {"title": "3.5 Label Distillation Methods.", "heading_level": null, "page_id": 9, "polygon": [[309.884765625, 174.75], [455.25, 174.75], [455.25, 184.3681640625], [309.884765625, 184.3681640625]]}, {"title": "4 APPLICATIONS", "heading_level": null, "page_id": 9, "polygon": [[311.25, 356.25], [402.75, 356.25], [402.75, 366.416015625], [311.25, 366.416015625]]}, {"title": "4.1 Continual Learning", "heading_level": null, "page_id": 9, "polygon": [[311.25, 444.0], [422.25, 444.0], [422.25, 454.0078125], [311.25, 454.0078125]]}, {"title": "4.2 Federated Learning", "heading_level": null, "page_id": 10, "polygon": [[45.75, 180.0], [159.0, 180.0], [159.0, 190.5556640625], [45.75, 190.5556640625]]}, {"title": "4.3 Neural Architecture Search", "heading_level": null, "page_id": 10, "polygon": [[310.5, 43.5], [456.75, 43.5], [456.75, 52.59375], [310.5, 52.59375]]}, {"title": "4.4 Privacy, Security and Robustness", "heading_level": null, "page_id": 10, "polygon": [[311.25, 213.75], [487.5, 213.75], [487.5, 223.13671875], [311.25, 223.13671875]]}, {"title": "4.5 Graph Neural Network", "heading_level": null, "page_id": 11, "polygon": [[46.5, 181.5], [171.75, 181.5], [171.75, 191.619140625], [46.5, 191.619140625]]}, {"title": "4.6 Recommender System", "heading_level": null, "page_id": 11, "polygon": [[46.5, 522.0], [173.25, 522.0], [173.25, 531.75], [46.5, 531.75]]}, {"title": "4.7 Text Classification", "heading_level": null, "page_id": 11, "polygon": [[310.5, 43.5], [418.5, 43.5], [418.5, 53.**********], [310.5, 53.**********]]}, {"title": "4.8 Knowledge Distillation", "heading_level": null, "page_id": 11, "polygon": [[310.5, 269.25], [436.5, 269.25], [436.5, 279.2109375], [310.5, 279.2109375]]}, {"title": "4.9 Medical", "heading_level": null, "page_id": 11, "polygon": [[311.25, 450.0], [369.75, 450.0], [369.75, 459.421875], [311.25, 459.421875]]}, {"title": "4.10 Fashion, Art and Design", "heading_level": null, "page_id": 11, "polygon": [[311.25, 652.5], [449.25, 652.5], [449.25, 662.44921875], [311.25, 662.44921875]]}, {"title": "5 EXPERIMENTS", "heading_level": null, "page_id": 12, "polygon": [[47.25, 150.75], [136.564453125, 150.75], [136.564453125, 161.6484375], [47.25, 161.6484375]]}, {"title": "5.1 Experimental Setup", "heading_level": null, "page_id": 12, "polygon": [[45.75, 237.0], [159.75, 237.0], [159.75, 247.5], [45.75, 247.5]]}, {"title": "5.2 Performance Evaluation", "heading_level": null, "page_id": 12, "polygon": [[308.98828125, 43.5], [443.25, 43.5], [443.25, 53.51220703125], [308.98828125, 53.51220703125]]}, {"title": "5.3 Training Cost Evaluation", "heading_level": null, "page_id": 12, "polygon": [[311.25, 513.0], [447.0, 513.0], [447.0, 523.23046875], [311.25, 523.23046875]]}, {"title": "5.4 Empirical Studies", "heading_level": null, "page_id": 13, "polygon": [[45.75, 706.53515625], [150.75, 706.53515625], [150.75, 716.58984375], [45.75, 716.58984375]]}, {"title": "6 CHALLENGES AND <PERSON><PERSON><PERSON><PERSON><PERSON> IMPROVEMENTS", "heading_level": null, "page_id": 13, "polygon": [[311.25, 663.75], [555.8203125, 663.75], [555.8203125, 674.05078125], [311.25, 674.05078125]]}, {"title": "6.1 Computational Cost", "heading_level": null, "page_id": 14, "polygon": [[46.5, 257.25], [162.0, 257.25], [162.0, 267.416015625], [46.5, 267.416015625]]}, {"title": "6.2 Scaling Up", "heading_level": null, "page_id": 14, "polygon": [[46.5, 663.0], [120.12890625, 663.0], [120.12890625, 672.50390625], [46.5, 672.50390625]]}, {"title": "6.3 Generalization across Different Architectures", "heading_level": null, "page_id": 15, "polygon": [[45.75, 499.5], [275.25, 499.5], [275.25, 509.30859375], [45.75, 509.30859375]]}, {"title": "6.4 Design for Other Tasks and Applications", "heading_level": null, "page_id": 15, "polygon": [[310.5, 92.25], [518.466796875, 92.25], [518.466796875, 101.900390625], [310.5, 101.900390625]]}, {"title": "6.5 Security and Privacy", "heading_level": null, "page_id": 15, "polygon": [[311.25, 189.75], [428.25, 189.75], [428.25, 199.546875], [311.25, 199.546875]]}, {"title": "7 CONCLUSION", "heading_level": null, "page_id": 15, "polygon": [[311.25, 311.25], [396.0, 311.25], [396.0, 322.*********], [311.25, 322.*********]]}, {"title": "ACKNOWLEDGMENT", "heading_level": null, "page_id": 15, "polygon": [[310.*********, 516.0], [411.0, 516.0], [411.0, 525.55078125], [310.*********, 525.55078125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 15, "polygon": [[310.18359375, 568.4765625], [379.51171875, 568.4765625], [379.51171875, 578.53125], [310.18359375, 578.53125]]}, {"title": "APPENDIX A\nTAXONOMY OF EXISTING DD METHODS", "heading_level": null, "page_id": 19, "polygon": [[311.37890625, 383.*********], [507.75, 383.*********], [507.75, 408.375], [311.37890625, 408.375]]}, {"title": "APPENDIX B\nPERFORMANCE OF EXISTING DD METHODS", "heading_level": null, "page_id": 19, "polygon": [[310.18359375, 601.734375], [530.12109375, 601.734375], [530.12109375, 626.484375], [310.18359375, 626.484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 88], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["TextInlineMath", 1], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10601, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 118], ["Text", 9], ["ListItem", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 114], ["Text", 7], ["SectionHeader", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 662], ["Line", 123], ["TableCell", 41], ["Text", 7], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1588, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1078], ["Line", 191], ["TextInlineMath", 9], ["Equation", 6], ["Text", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 7861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 802], ["Line", 165], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1992, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 882], ["Line", 192], ["Text", 7], ["TextInlineMath", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3014, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1020], ["Line", 168], ["TextInlineMath", 9], ["Equation", 6], ["Text", 6], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3977, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 778], ["Line", 160], ["TextInlineMath", 6], ["Text", 5], ["SectionHeader", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2387, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 803], ["Line", 137], ["TextInlineMath", 7], ["Text", 5], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1181, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 120], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 113], ["Text", 6], ["SectionHeader", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 118], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 832], ["TableCell", 342], ["Line", 90], ["Text", 8], ["ListItem", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 24804, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 155], ["Text", 6], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1892, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 120], ["TableCell", 117], ["Text", 9], ["Reference", 9], ["SectionHeader", 6], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13183, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 790], ["Line", 154], ["ListItem", 44], ["Reference", 44], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 690], ["Line", 153], ["ListItem", 43], ["Reference", 43], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["Line", 153], ["ListItem", 46], ["Reference", 46], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 119], ["Reference", 31], ["ListItem", 30], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 813], ["TableCell", 485], ["Line", 96], ["Table", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Caption", 2], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 6867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 1248], ["TableCell", 784], ["Line", 132], ["Table", 4], ["Caption", 3], ["TableGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 2, "llm_tokens_used": 14832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 319], ["Span", 164], ["Line", 34], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Text", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12452, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation__A_Comprehensive_Review"}