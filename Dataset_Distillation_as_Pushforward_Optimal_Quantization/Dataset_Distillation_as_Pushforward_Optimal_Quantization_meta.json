{"table_of_contents": [{"title": "Dataset Distillation as Pushforward Optimal Quantization", "heading_level": null, "page_id": 0, "polygon": [[126.0, 91.41064453125], [485.296875, 91.41064453125], [485.296875, 105.0908203125], [126.0, 105.0908203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 240.75], [328.7109375, 240.75], [328.7109375, 253.107421875], [282.75, 253.107421875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[89.2001953125, 469.5], [170.25, 469.5], [170.25, 481.078125], [89.2001953125, 481.078125]]}, {"title": "1.1. Bi-level formulation of dataset distillation.", "heading_level": null, "page_id": 1, "polygon": [[89.2001953125, 177.75], [308.25, 177.75], [308.25, 188.8154296875], [89.2001953125, 188.8154296875]]}, {"title": "1.1.1. GENERATIVE PRIORS.", "heading_level": null, "page_id": 2, "polygon": [[89.25, 428.25], [225.0, 428.25], [225.0, 440.0859375], [89.25, 440.0859375]]}, {"title": "1.2. Disentangled methods.", "heading_level": null, "page_id": 3, "polygon": [[89.05078125, 208.5], [218.25, 208.5], [218.25, 219.65625], [89.05078125, 219.65625]]}, {"title": "1.3. Contributions.", "heading_level": null, "page_id": 4, "polygon": [[89.12548828125, 268.5], [180.75, 268.5], [180.75, 279.59765625], [89.12548828125, 279.59765625]]}, {"title": "2. Background", "heading_level": null, "page_id": 5, "polygon": [[88.5, 92.25], [168.75, 92.25], [168.75, 104.4140625], [88.5, 104.4140625]]}, {"title": "2.1. Definitions and Notation", "heading_level": null, "page_id": 5, "polygon": [[89.25, 209.25], [226.5, 209.25], [226.5, 220.236328125], [89.25, 220.236328125]]}, {"title": "2.2. Optimal Quantization", "heading_level": null, "page_id": 5, "polygon": [[88.5, 399.0], [214.5, 399.0], [214.5, 409.1484375], [88.5, 409.1484375]]}, {"title": "2.2.1. SOLVING THE OPTIMAL QUANTIZATION PROBLEM", "heading_level": null, "page_id": 8, "polygon": [[89.05078125, 93.0], [358.5, 93.0], [358.5, 103.9306640625], [89.05078125, 103.9306640625]]}, {"title": "Algorithm 1: CLVQ", "heading_level": null, "page_id": 9, "polygon": [[95.84912109375, 94.5], [190.5, 94.5], [190.5, 106.6376953125], [95.84912109375, 106.6376953125]]}, {"title": "2.3. Score-based diffusion.", "heading_level": null, "page_id": 9, "polygon": [[88.3037109375, 589.359375], [214.5, 589.359375], [214.5, 609.0], [88.3037109375, 609.0]]}, {"title": "2.3.1. STRONG LOCAL LIPSCHITZ CONTINUITY.", "heading_level": null, "page_id": 11, "polygon": [[89.25, 447.0], [313.5, 447.0], [313.5, 458.26171875], [89.25, 458.26171875]]}, {"title": "3. Dataset Distillation as Optimal Quantization", "heading_level": null, "page_id": 12, "polygon": [[89.12548828125, 425.25], [333.0, 425.25], [333.0, 436.9921875], [89.12548828125, 436.9921875]]}, {"title": "3.1. Preliminary: D^4M method", "heading_level": null, "page_id": 15, "polygon": [[89.25, 401.25], [237.0, 401.25], [237.0, 412.62890625], [89.25, 412.62890625]]}, {"title": "3.2. Proposed method: Dataset Distillation by Optimal Quantization", "heading_level": null, "page_id": 15, "polygon": [[89.2001953125, 558.75], [410.25, 558.75], [410.25, 568.86328125], [89.2001953125, 568.86328125]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 17, "polygon": [[88.5, 529.03125], [171.0, 529.03125], [171.0, 540.6328125], [88.5, 540.6328125]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 18, "polygon": [[89.25, 619.5], [162.75, 619.5], [162.75, 630.73828125], [89.25, 630.73828125]]}, {"title": "References", "heading_level": null, "page_id": 19, "polygon": [[89.25, 658.5], [146.2763671875, 658.5], [146.2763671875, 669.796875], [89.25, 669.796875]]}, {"title": "Appendix <PERSON> of Lemma 12", "heading_level": null, "page_id": 24, "polygon": [[89.25, 346.5], [259.5, 346.5], [259.5, 358.294921875], [89.25, 358.294921875]]}, {"title": "Appendix B. Approximate Tim<PERSON>", "heading_level": null, "page_id": 25, "polygon": [[89.25, 92.25], [273.75, 92.25], [273.75, 104.3173828125], [89.25, 104.3173828125]]}, {"title": "Appendix C. Experiment Hyperparameters", "heading_level": null, "page_id": 25, "polygon": [[89.25, 341.25], [316.5, 341.25], [316.5, 354.234375], [89.25, 354.234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 44], ["Text", 10], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6421, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 58], ["Text", 7], ["Equation", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 76], ["Text", 7], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1313, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 207], ["Line", 45], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 40], ["Text", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 61], ["TextInlineMath", 6], ["Equation", 4], ["Reference", 4], ["SectionHeader", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 481], ["Line", 49], ["TextInlineMath", 5], ["Text", 4], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 681], ["Line", 93], ["TextInlineMath", 5], ["Text", 5], ["Equation", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1338, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["Line", 63], ["TextInlineMath", 7], ["Text", 4], ["Equation", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 55], ["Text", 4], ["TextInlineMath", 3], ["Reference", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["Line", 53], ["Text", 5], ["Equation", 4], ["TextInlineMath", 4], ["ListItem", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 682], ["Line", 43], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 641], ["Line", 67], ["Text", 5], ["Equation", 5], ["TextInlineMath", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 530], ["Line", 41], ["TextInlineMath", 6], ["Equation", 5], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 849], ["Line", 102], ["TextInlineMath", 8], ["Equation", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1325, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 46], ["Text", 7], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 547], ["Line", 43], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Code", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 492], ["Line", 79], ["TableCell", 61], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Equation", 1], ["Text", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8583, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 41], ["Text", 3], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 647, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["TableCell", 198], ["Line", 48], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 38], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 38], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 39], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 39], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 80], ["Reference", 7], ["ListItem", 6], ["Equation", 6], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["TableCell", 84], ["Line", 33], ["Text", 3], ["SectionHeader", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2356, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_as_Pushforward_Optimal_Quantization"}