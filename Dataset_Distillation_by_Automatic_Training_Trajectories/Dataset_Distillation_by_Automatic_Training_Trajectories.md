# Dataset Distillation by Automatic Training Trajectories

Dai <PERSON><sup>1</sup><sup>®</sup>[,](https://orcid.org/0000-0002-6750-3652) <PERSON><PERSON><sup>2</sup><sup>®</sup>, <PERSON><sup>[1](https://orcid.org/0000-0001-9013-435X)</sup><sup>®</sup>, Carsten Trinitis<sup>1</sup><sup>®</sup>, and <PERSON><sup>1</sup>

<sup>1</sup> Technical University of Munich, Germany <sup>2</sup> University of Oxford, United Kingdom

Abstract. Dataset Distillation is used to create a concise, yet informative, synthetic dataset that can replace the original dataset for training purposes. Some leading methods in this domain prioritize long-range matching, involving the unrolling of training trajectories with a fixed number of steps  $(N_S)$  on the synthetic dataset to align with various expert training trajectories. However, traditional long-range matching methods possess an overfitting-like problem, the fixed step size  $N<sub>S</sub>$  forces synthetic dataset to distortedly conform seen expert training trajectories, resulting in a loss of generality—especially to those from unencountered architecture. We refer to this as the Accumulated Mismatching Problem (AMP), and propose a new approach, Automatic Training Trajectories (ATT), which dynamically and adaptively adjusts trajectory length  $N_S$  to address the AMP. Our method outperforms existing methods particularly in tests involving cross-architectures. Moreover, owing to its adaptive nature, it exhibits enhanced stability in the face of parameter variations. Our source code is publicly available at <https://github.com/NiaLiu/ATT>

Keywords: Dataset Distillation · Task-Specific Dataset Compression · Dataset Condensation

# 1 Introduction

Deep learning has showcased remarkable achievements across various computer vision problems [\[24,](#page-16-0) [43,](#page-17-0) [56\]](#page-17-1). Nevertheless, its success often hinges on extensive datasets, resulting in substantial computational demands. Recognizing the escalating computational costs [\[41,](#page-17-2)[47\]](#page-17-3), numerous academic publications have delved into addressing these challenges. A key focus has been on mitigating the intensive training process for end-users by developing significantly reduced datasets. A conventional method for dataset reduction is Coreset Selection (CS) [\[4,](#page-15-0)[18,](#page-15-1)[48,](#page-17-4) [53,](#page-17-5)[62\]](#page-18-0), which involves curating a subset of the most informative training samples. However, many CS methods are characterized as greedy algorithms, leading to a trade-off between speed and accuracy [\[2,](#page-15-2) [9,](#page-15-3) [48,](#page-17-4) [52,](#page-17-6) [64\]](#page-18-1). Additionally, the information provided by these methods is constrained by the selected samples from the original dataset. In 2018, Wang et al. [\[59\]](#page-18-2) introduced Dataset Distillation

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays two scatter plots side-by-side, both with "Trajectory Length" on the x-axis and "L1 Distance" on the y-axis. The left plot shows data for "Iteration 10" (blue dots), "Iteration 11" (orange dots), and "Iteration 12" (green dots), with "ATT's Choice" marked by red dots and "FTL's Choice" by purple dots. The y-axis ranges from 450 to 470. The right plot shows data for "Iteration 1002" (blue dots), "Iteration 1003" (orange dots), and "Iteration 1004" (green dots), with "ATT's Choice" marked by red dots and "FTL's Choice" by purple dots. The y-axis in the right plot ranges from 535 to 560. Both plots show a general trend of increasing L1 Distance with increasing Trajectory Length, with some fluctuations.

Fig. 1: The plots shows L1 distance between each network from a training trajectory and the corresponding target, and chosen target by different method. The experiments are carried on CIFAR-10. Existing methods employs Fixed Training Length (FTL), which select network at the end of a trajectory. But our method ATT dynamically selects network possessing closest distance to targets. Left plot shows examples from beginning iterations, and right plot shows higher iterations. ATT dynamically adjusts matching target, thus avoid large matching error and unwanted stretching of trajectories.

(DD), a technique that prioritizes the extraction of an informative and compact dataset capable of replacing the original training dataset in various training tasks. The technique not only provides a significantly smaller synthetic dataset for training but also conceals details in the original dataset, thereby enhancing privacy [\[15\]](#page-15-4). DD methods have been widely applied in various fields, including federated learning [\[45,](#page-17-7) [50,](#page-17-8) [57,](#page-17-9) [72,](#page-18-3) [73\]](#page-18-4), continual learning [\[46,](#page-17-10) [59,](#page-18-2) [60\]](#page-18-5), studies on attacks [\[36,](#page-16-1) [37,](#page-16-2) [59\]](#page-18-2), and many other areas [\[7,](#page-15-5) [14,](#page-15-6) [26,](#page-16-3) [61,](#page-18-6) [71\]](#page-18-7).

The DD methodologies can be classified into two distinct categories based on their alignment targets: short-range matching methods [\[40,](#page-17-11)[58,](#page-18-8)[66,](#page-18-9)[68,](#page-18-10)[69\]](#page-18-11) and long-range matching methods [\[6,](#page-15-7)[16,](#page-15-8)[31\]](#page-16-4). Short-range matching strategies, notably initiated by a gradient-matching approach [\[69\]](#page-18-11), concentrate on aligning a single training step executed on distilled data with that performed on the original data. Conversely, long-range matching Dataset Distillation (LDD) entail the alignment of multiple training steps. Empirical validation by Cazenavette et al. [\[6\]](#page-15-7) demonstrates that long-range matching consumes more computation but exhibits superior performance in terms of test outcomes comparably, and attributed the great test performance to the ability to circumvent short-sightedness. Following research improved LDD further, such as with flat trajectories [\[16\]](#page-15-8), with prunning to filter out the hidden noise [\[31\]](#page-16-4), or investigating training stages [\[20\]](#page-16-5).

Nonetheless, our empirical findings expose a common trend in conventional LDD methods, where inaccurate predictions are consistently reinforced throughout the majority of iterations. We attribute this issue to their reliance on Fixed Trajectory Length (FTL), which lacks the adaptability to align various steps of expert trajectories, as shown in Fig. [1.](#page-1-0) This reinforcement, driven by the FTL, results in the accumulation of matching errors over the distillation process. Importantly, these errors not only persist, but accumulate in the synthetic dataset. Consequently, the synthetic dataset tends to overly conform to observed matching targets while struggling to generalize effectively to unseen matching targets. We term this issue the AMP (Accumulated Mismatching Problem).

Our studies underscore the significance of this observation, and we propose ATT (Automatic Training Trajectories). ATT is specifically designed to dynamically and adaptively adjust matching objects and trajectory length, aiming to diminish AMP throughout the distillation process. Our method paves the way towards adaptive and dynamical matching, fostering improved generalization and accuracy in synthetic dataset distillation.

In summary, our main contributions are as follows:

- We revisit the domain of long-range matching dataset distillation, and identify the Accumulated Mismatching Problem (AMP).
- We illustrate how AMP contributes to the iterative matching error and establish a clear correlation between AMP and Accumulated Trajectory Error (ATE).
- We present an innovative Long-range Matching Dataset Distillation (LDD) method named Automatic Training Trajectories (ATT). ATT incorporates a dynamic and adaptive approach to the selection of matching objects by adjusting the matching length, leveraging a minimum distance strategy to effectively address the challenges posed by AMP.

# 2 Related Work

### 2.1 Dataset Distillation

Dataset Distillation (DD) was initially introduced by Wang et al. [\[59\]](#page-18-2), drawing inspiration from Knowledge Distillation [\[23\]](#page-16-6). Existing DD baselines can be categorized into short-range and long-range matching methods based on the number of steps unrolled on the original dataset. In this context, kernel ridge regressionbased methods are included under short-range matching.

Short-Range Matching Dataset Distillation (SDD): Dataset Condensation (DC), introduced by Zhao et al. [\[69\]](#page-18-11), has been a subject of extensive research and it involves matching one-step updated gradients. Subsequent research by Zhao et al. [\[68\]](#page-18-10) enhanced performance and reduced synthesis costs by incorporating Batch Normalization and aligning feature distributions. [\[70\]](#page-18-12), [\[68\]](#page-18-10) further addressed the problem of miss-alignment among classes. [\[30\]](#page-16-7) focuses on capturing differences between classes, and [\[58\]](#page-18-8) overcomes the bias of gradient matching methods by aligning layer-wise features. [\[66\]](#page-18-9) enhanced DC by boosting the distillation process with Differentiable Siamese Augmentation (DSA). [\[63\]](#page-18-13) presents a new DC framework decouples bi-level optimization, and makes a step towards real world applications. Approaches applying a kernel-based metalearning framework with an infinitely wide neural network [\[40\]](#page-17-11) have also sparked significant research. This includes methods implementing model pool and label learning [\[74\]](#page-18-14), and adopting convexified implicit gradients [\[38\]](#page-16-8).

Long-Range Matching Dataset Distillation (LDD): DD, as introduced by [\[59\]](#page-18-2), initially focused on one-step matching. The concept of Matching Training Trajectories (MTT) was later introduced by [\[6\]](#page-15-7) to replicate the long-range

training dynamics of the original dataset. This approach overcomes the shortsightedness in single-step distillation and introduced the first long-range matching method, referred to as the vanilla LDD method in this work. Li et al. [\[31\]](#page-16-4) implements Parameter Pruning (PP) to enforce matching on significant parameters. Additionally, [\[16\]](#page-15-8) discovers the Accumulated Trajectory Error (ATE) in the distillation and evaluation phases, adopting flat expert trajectories to alleviate ATE, and [\[20\]](#page-16-5) reveals different training stages contains different information.

Divergent Research Focus: Other concurrent works have improved DD baselines with various approaches. Some have adopted soft labels [\[5,](#page-15-9) [11,](#page-15-10) [51\]](#page-17-12), while others have implemented data parameterization [\[14,](#page-15-6) [25,](#page-16-9) [34\]](#page-16-10) and data factorization [\[8,](#page-15-11)[29,](#page-16-11)[32,](#page-16-12)[67\]](#page-18-15). Another approach involves encoding labels and synthetic datasets into a learnable addressing matrix [\[14\]](#page-15-6). Some works focus on selecting representative real samples [\[35\]](#page-16-13), using model augmentation [\[65\]](#page-18-16), and reducing the budget for storage and transmissions with slimmable DD [\[34\]](#page-16-10). Additionally, there are efforts focusing on using only a few networks [\[33\]](#page-16-14).

### 2.2 Sample Selection

Like DD, instance selection [\[42\]](#page-17-13), coreset selection [\[4,](#page-15-0) [18,](#page-15-1) [48\]](#page-17-4), and dataset pruning [\[62\]](#page-18-0) also aim to identify representatives from a given dataset. These sample selection methods have been widely applied in diverse fields [\[3,](#page-15-12) [12,](#page-15-13) [21,](#page-16-15) [39,](#page-17-14) [54\]](#page-17-15). Established techniques, such as random selection, herding methods [\[9,](#page-15-3) [10\]](#page-15-14), and forgetting [\[44,](#page-17-16)[52\]](#page-17-6), can be adapted as comparisons to dataset distillation methods. While similar to other selection methods, DD, in its aim of creating informationrich images that may not necessarily be considered actual samples. Multiple studies [\[6,](#page-15-7) [66,](#page-18-9) [69\]](#page-18-11) have empirically proven that dataset distillation, especially at higher compression ratio, significantly outperforms sample selection.

# 3 Method

Conventional LDD methods typically unroll a fixed length of trajectory on the synthetic dataset to match expert trajectories. However, a fixed trajectory length cannot effectively handle variations within expert trajectories, leading to mismatched predictions and accumulated errors in the synthetic dataset, which we refer to the Accumulated Mismatching Problem (AMP). In this section, we first introduce the conventional LDD and the typical matching strategy involving Fixed Trajectory Length (FTL) in Sec. [3.1.](#page-3-0) Subsequently, we empirically demonstrate the AMP in conventional LDD and its impact on Accumulated Trajectory Error (ATE) in Sec. [3.2.](#page-5-0) Lastly, we present our proposed method, Automatic Training Trajectories (ATT), designed to address AMP, and provide the pseudo-code in Sec. [3.3.](#page-7-0)

<span id="page-3-0"></span>

### 3.1 LDD

Problem Description: LDD methods reveal the information within the real dataset  $D_T$  through the creation of training trajectories. Specifically, we define a training trajectory as a function  $T_{D,f}(\theta_0, N)$ . The function utilizes classifier f initialized with network parameters  $\theta_0$  and optimizes the classifier iteratively for a total of n steps over a dataset D.  $T_{D,f}(\theta_0, N)$  outputs the difference between the network parameters at the N-th iteration and those at the initial iteration. Namely,

<span id="page-4-1"></span>
$$
T_{D,f}(\theta_0, N) = \theta_N - \theta_0. \tag{1}
$$

<span id="page-4-3"></span>LDD endeavors to enhance the synthetic dataset  $D<sub>S</sub>$  by aligning it with the expanded information in the form of trajectories. Specifically, the objective is to address the following optimization problem, as shown in the following:

$$
\Delta T = \|T_{D_{S,f}}(\theta_i, N_S) - T_{D_T,f}(\theta_i, N_T)\|_2^2, \tag{2}
$$

$$
D_S = \underset{D_S}{\text{arg min}} \underset{\theta_0 \sim P_{\theta_0}}{\mathbb{E}} [\Delta T]. \tag{3}
$$

<span id="page-4-2"></span>We use subscripts  $S$  and  $T$  to denote parameters associated with synthetic data and real data, respectively.  $P_{\theta_0}$  is the probability distribution of network initialization.

In practical scenarios, the training trajectory is unrolled on the real dataset  $D_T$ , generating expert trajectories sets as  $\{\theta_{0,0}^*, \theta_{0,1}^*, \cdots, \theta_{0,M}^*, \theta_{1,0}^*, \cdots, \theta_{N,M}^*\}$ . N is the number of trajectories, M is trajectory length. The expert set is precollected and stored in buffers, with additional disk storage to alleviate memory constraints.

Following the collection of the buffer, we initialize the student trajectory using any expert from the set of expert trajectories, such as  $\theta'_{i,0} = \theta^*_{i,0}$ . Subsequently, a similar training trajectory is unfolded on the synthetic dataset  $D<sub>S</sub>$ for t steps, represented as  $\theta'_{i,t}$ . In conventional LDD methods, a fixed value  $N_S$ is chosen for  $t$ . The objective is to minimize the discrepancy between the student prediction  $\theta'_{i,t}$  and the expert trajectory unrolled at  $N_T$  steps, denoted as  $\theta_{i,NT}^*$ . The optimization of  $D_S$  is then carried out by minimizing the defined loss function

$$
L = \frac{\|\theta'_{i,N_S} - \theta^*_{i,N_T}\|_2^2}{\|\theta^*_{i,0} - \theta^*_{i,N_T}\|_2^2}.
$$
\n(4)

<span id="page-4-0"></span>This loss function quantifies the distance between the final state of the student trajectory and the expert trajectory using the squared L2 norm. Subsequently, the distance is normalized by the squared Euclidean distance between the initial and final states of the expert trajectory, allowing for the amplification of weak signals in the expert sets.

Fixed Trajectory Length LDD methods conventionally adopt a Fixed Trajectory Length (FTL) strategy, wherein a predetermined step size denoted as  $N<sub>S</sub>$ is fixed for all instances of trajectory matching. This fixed step size is uniformly applied to match various experts from the expert trajectory set. Previous studies, as observed in studies such as [\[6,](#page-15-7)[16\]](#page-15-8), often conducted experiments involving an exhaustive search to determine an optimal  $N<sub>S</sub>$ . However, our investigation

<span id="page-5-1"></span>Image /page/5/Figure/0 description: This figure displays six line graphs, each representing a different value of Ns (30, 50, 70, 90, 110, and 130). The x-axis for all graphs is labeled "N'th interval" and ranges from 0 to 50. The y-axis for all graphs is labeled "# of iterations" and ranges from 0 to 40. Each graph shows a blue line representing "line a: # of cases matched with higher errors" and an orange dashed line representing "line b: mean value of line a". The blue lines fluctuate, while the orange dashed lines indicate a relatively stable mean value across the intervals for each Ns. The title "6 D. Liu et al." is present at the top.

Fig. 2: The figure illustrates that Fixed Trajectory Length (FTL) matches all experts with avoidable matching error throughout the distillation process. The figure is generated from experiments conducted on CIFAR-10 with Images Per Class (IPC) set to 1. We collect the number of cases matches with larger matching errors  $||N_S - N_{opt}|| \ge \gamma$ , at every 50 iterations throughout the distillation process. The number of cases matched with larger errors fluctuates over entire process. The same can be observed with the mean value of line a. From left to right, we observe the persistence of this issue across different step size  $N<sub>S</sub>$  for FTL. Notation: #: number, N'th interval: the N'th 50 iters.

reveals that a fixed trajectory length introduces the Accumulated Mismatching Problem (AMP). This problem leads to persistent matching errors that do not diminish, even with an optimal  $N<sub>S</sub>$ . The issue persists across variations in  $N<sub>S</sub>$  and remains throughout the entire distillation process. Further details are discussed in the subsequent Sec. [3.2.](#page-5-0)

<span id="page-5-0"></span>

### 3.2 Accumulated Mismatching Problem (AMP)

In traditional LDD methods, a specific trajectory length denoted as  $N<sub>S</sub>$  is fixed while matching various segments of expert trajectories. Network parameters are predicted for all instances of matching based on this FTL, namely,  $\theta'_{i,N_S} = \theta'_{i,0} +$  $T_{D_S,f}(\theta'_{i,0}, N_S)$ . However, due to variations in expert trajectories arising from different steps on trajectories and different initialization of them, the designated prediction  $\theta'_{i,N_S}$  lacks the necessary adjustments to accommodate such variations among different experts. This discrepancy leads to the so-called Accumulated Mismatching Problem (AMP).

We provide details in experiments conducted on CIFAR-10 with a configuration of 1 Image Per Class (IPC). The chosen baseline is the vanilla LDD method [\[6\]](#page-15-7), to mitigate any unintended buffered effects other than FTL. In our experimental analysis, we initiate the process by generating an exhaustive set of potential predictions  $pred = \{\theta'_{i,1}, \theta'_{i,2}, \cdots \theta'_{i,N_S}\}\$  under diverse values of  $N_S$ ranging from 30 to 130.

Subsequently, we measure the mean square error between each prediction and the corresponding target. The pred associated with the minimal error is identified at  $\theta'_{i,N_{opt}}$ , at the optimal step  $N_{opt}$ . The existance of the gap between  $N_{opt}$  and  $N_S$  introduces extra unwanted matching error. To account for minor discrepancies, we introduce a parameter  $\gamma = 2$  as a tolerance threshold for errors. We then collect the number of cases where matches with larger error, specifically  $||N_S - N_{opt}||_1 \ge \gamma$ . This collection is performed at each 50 iterations throughout the distillation process, and the results are depicted in Fig. [2.](#page-5-1) Additionally, we present supplementary results with varying  $\gamma$  in Appendix [A.1.](#page-19-0)

As observed in the figure, FTL methods consistently show a tendency to align steps with larger errors across different values of  $N<sub>S</sub>$ . This behavior indicates that FTL methods compel trajectory matching with an inappropriate trajectory length. Consequently, student trajectories with an inappropriate length undergo either compression or stretching to conform to the diverse patterns exhibited by expert trajectories. This compels the synthetic dataset to overfit to the pre-buffered expert trajectories but sacrifices generality for unseen expert trajectories. We validate this observation with experimental results in Sec. [4.2,](#page-10-0) where AMP has been addressed. Additionally, the results obtained in Sec. [4.2](#page-10-0) support that the challenge becomes particularly pronounced when synthetic data encounters unseen trajectories from unseen architectures.

AMP is Accumulative: We investigate into  $N<sub>S</sub>$  by mimicing it as a small fluctuation  $\delta$ . An additional term emerges in the prediction, denoted as  $\theta_{Ns+\delta}$  =  $\theta_{N_S} + T_{D_S,f}(\theta_{N_S}, \delta)$ . The presence of the additional term has implications for both the loss function, as depicted in Eq. [\(4\)](#page-4-0), and the objectives outlined in Eq. [\(2\)](#page-4-1) and Eq. [\(3\)](#page-4-2). Consequently, this additional term accumulates with each update to the synthetic dataset  $D<sub>S</sub>$ , a phenomenon we identify as accumulative.

AMP and Accumulated Trajectory Error: Previous research [\[16\]](#page-15-8) revealed the existence of Accumulated Trajectory Error (ATE), which results from discrepancies between the initialization from previous student parameters and the assigned initial expert parameters in each iteration. Conversely, while AMP primarily focuses on the end of the trajectory for matching, it is noteworthy that AMP also contributes to ATE. In this subsection, we show the correlation between AMP and ATE.

As outlined in from [\[16\]](#page-15-8) Sec. 3.1, the ATE is composed of the initialization error, the matching error  $\epsilon_0$ , and the ATE from the previous iteration. The matching error  $\epsilon_0$ , as part of ATE, is defined as

<span id="page-6-0"></span>
$$
\epsilon_0 = T_{D_S, f}(\theta_{i,0}^*, N_S) - T_{D_T, f}(\theta_{i,0}^*, N_T). \tag{5}
$$

 $\theta_{i,0}^*$  is any network parameters from expert trajectories. We plug Eq. [\(1\)](#page-4-3) in Eq. [\(5\)](#page-6-0) and take L2 norm as distance measure. We define  $e_t = ||\epsilon_0||_2^2$ , and AMP is indeed solving the following in each iteration as shown:

<span id="page-6-1"></span>
$$
\min_{t} e_t = \min_{t \in \{0, 1, \cdots, N_S\}} \|\theta'_{i,t} - \theta^*_{i, N_T}\|_2^2.
$$
\n(6)

By addressing the AMP, we minimize matching errors at each step, thus iteratively reducing the ATE.

<span id="page-7-1"></span>Image /page/7/Figure/1 description: The image displays a comparison between "Vanilla LDD methods: (with FLT)" and "Our ATT:". Both sections illustrate a sequence of yellow triangles representing "Expert network parameters" and orange triangles representing "Student network parameters (all possible predictions)". Dotted arrows indicate "One step update". The "Vanilla LDD methods" section shows a matching between an orange triangle and a yellow triangle, both circled in red, with a red arrow labeled "Matching" connecting them. The "Our ATT:" section also shows a similar matching between a circled orange triangle and a circled yellow triangle, with a red arrow labeled "Matching" connecting them. A legend at the bottom clarifies the symbols: yellow triangles for expert parameters, orange triangles for student parameters, dotted arrows for one-step updates, and red circles with connecting arrows for matching selected network parameters.

Fig. 3: The figure displays the core idea of our method ATT in comparison to traditional LDD methods employing FTL. Left: The Vanilla LDD bypasses all possible predictions and matches the inaccurate prediction. Right: our method ATT adopts an adaptive approach, aligning predictions with expert network parameters using a minimum distance policy. ATT avoid cases where compressing or stretching trajectories happen, and prevents the accumulation of errors resulting from those cases within the synthetic dataset at each iteration, thus achieves better distillation performances.

<span id="page-7-0"></span>

### 3.3 Automatic Training Trajectories (ATT)

The core idea behind ATT is to dynamically and adaptively select a suitable trajectory length based on the minimum distance policy. And ATT essentially is a simple solution to Eq.  $(6)$ . It computes all possible  $e_t$  and selects the t that gives the minimum  $e_t$ .

Fig. [3](#page-7-1) provides an illustration of the ATT process. The process begins by unrolling a trajectory starting from the expert input  $\theta_{i,0}^*$  on a synthetic dataset. Within each trajectory, every updated step  $t \in \{0, 1, \dots, N_S\}$  conveys a potential prediction  $\theta'_{i,t}$ . We evaluate the squared L2 distance between each prediction and the corresponding expert target with  $e_i = ||\theta'_{i,t} - \theta^*_{i,N_T}||_2^2$ . The step  $N_{opt}$  with the minimum distance is selected out of all possible  $t$ , as depicted in Eq. [\(6\)](#page-6-1). Subsequently, back-propagation is performed when matching at the selected prediction  $\theta'_{i,N_{opt}}$ .

We summarize pseudocode for ATT in Algorithm [1,](#page-8-0) which provides a stepby-step description of the proposed method. By employing this approach, we directly eliminate AMP, and thus reduce matching error. Further, given the significance of trajectory length in LDD methods, we conduct an ablation study in Sec. [4.4](#page-11-0) to investigate its impact on ATT further.

# 4 Experiments

In this section, we present an overview of the dataset used, the experimental setups, and the baselines employed in our experiments in Sec. [4.1.](#page-8-1) Next, we showcase and compare the performance of our proposed method to the

<span id="page-8-0"></span>Algorithm 1 Automatic Training Trajectories

| <b>Input:</b>  |                                                                                                |
|----------------|------------------------------------------------------------------------------------------------|
|                | \$N_S\$: the number of training steps on \$D_S\$.                                              |
|                | \$N_M\$: the number of training steps on \$D_T\$.                                              |
|                | \$lr'\$: learning rate for learning \$D_S\$                                                    |
| 1:             | initialize \$lr'\$, \$D_S\$                                                                    |
| 2:             | while not converged do                                                                         |
| 3:             | randomly sample a pair of \$	heta^*_{i,0}\$, \$	heta^*_{i,N_M}\$ from expert trajectories      |
| 4:             | initialize \$	heta'_{i,0} = 	heta^*_{i,0}, d_t = []\$                                          |
| 5:             | for \$t=0\$ to \$N_S\$ do                                                                      |
| 6:             | get \$	heta'_{i,t+1}\$ by a one-step update on \$	heta'_{i,t}\$                                |
| 7:             | calculate \$e_t\$ from Eq. (6) and append it to \$d_t\$                                        |
| 8:             | end for                                                                                        |
| 9:             | find the index \$N_{opt}\$, which minimizes \$d_t\$                                            |
| 10:            | conduct alternating optimization on Eq. (4) at early step \$N_{opt}\$ over \$lr'\$ and \$D_S\$ |
| 11:            | end while                                                                                      |
| <b>Output:</b> |                                                                                                |
|                | learning rate \$lr'\$ and distilled data \$D_S\$                                               |

baseline approaches in cross-architecture performance Sec. [4.2,](#page-10-0) and on different datasets Sec. [4.3.](#page-11-1) Furthermore, we perform an ablation study on ATT and highlight its positive impact on boosting stability in Sec. [4.4.](#page-11-0) Lastly, we detail ATT's storage and computation requirement in Sec. [4.5.](#page-13-0)

<span id="page-8-1"></span>

### 4.1 Dataset and Experimental Setup

**CIFAR-10/100 [\[27\]](#page-16-16):** The dataset comprises  $50,000$  training images and  $10,000$ test images, each with a size of 32x32 pixels. CIFAR-10/100 consists of 10 classes and 100 classes respectively. they are colored and natural.

64x64 Tiny ImageNet [\[13\]](#page-15-15): The dataset consists of 100,000 training images and 5,000 validation images, all with a size of 64x64 pixels. It encompasses 200 classes and and they are colored and natural.

ImageNet Subsets [\[13,](#page-15-15) [17\]](#page-15-16): We focus on subset ImageNette (assorted objects), ImageWoof (dog breeds), ImageFruit (fruits), ImageMeow (cats). Each of the subclasses has 10 classes of with the image size of 128x128.

Evaluation Setups: Following the methodologies established in previous works [\[6,](#page-15-7) [40,](#page-17-11)[58,](#page-18-8)[68,](#page-18-10)[69\]](#page-18-11), we initiate the distillation process by generating a synthetic dataset using ATT, then assess its performance by training the synthetic dataset with five randomly initialized networks. The test accuracy of these networks is then measured on the original test dataset. To provide a comprehensive assessment, we calculate both the mean and variance of the test accuracy. To ensure a fair comparison, we adopt a simple ConvNet architecture, as designed by [\[19\]](#page-16-17), fol-lowing the precedent set by prior work [\[6,](#page-15-7) [58,](#page-18-8) [66,](#page-18-9) [69\]](#page-18-11). This ConvNet architecture incorporates convolutional layers [\[19\]](#page-16-17), instance normalization [\[55\]](#page-17-17), activation function RELU [\[1\]](#page-15-17), and average pooling layers. The normalization layer remains unchanged in our experimental setup to maintain consistency and facilitate a direct comparison of methods.

Baselines: In our comparative analysis, we benchmark our research against the first DD method [\[59\]](#page-18-2) and SDD methods including Dataset Condensation (DC) [\[69\]](#page-18-11), Differentiable Siamese Augmentation (DSA) [\[66\]](#page-18-9), Distribution matching (DM) [\[68\]](#page-18-10), and Aligning Features (CAFE) [\[58\]](#page-18-8). Furthermore, we compare DD methods with a meta-gradient computation-solving method: Kernel Inducing Points (KIP) [\[40\]](#page-17-11). Lastly, we include a comparison with LDD methods, encompassing the first LDD method Matching Training Trajectories (MTT) [\[6\]](#page-15-7), Parameter Pruning (PP) [\[31\]](#page-16-4), and Flat Trajectory Distillation (FTD) [\[16\]](#page-15-8). To facilitate a fair and meaningful comparison with different methods, our experimental setups deliberately exclude the incorporation of soft labels, network pruning, the adoption of batch normalization, or any additional factorization or parameterization methods. This intentional avoidance ensures that the comparison focuses on the core aspects of the compared methods without introducing confounding factors associated with the mentioned techniques. In this study, we refrain from comparing with instance selection approaches such as random selection, forgetting samples [\[52\]](#page-17-6), or Herding [\[10\]](#page-15-14). This decision is based on the understanding that these methods have been significantly surpassed by dataset distillation in many prior works [\[6,](#page-15-7) [58,](#page-18-8) [66,](#page-18-9) [69\]](#page-18-11). Additionally, we do not include research that concentrates on soft-labels [\[5,](#page-15-9)[11,](#page-15-10)[51\]](#page-17-12), data parameterization [\[14,](#page-15-6)[25,](#page-16-9)[34\]](#page-16-10), data factorization [\[8,](#page-15-11)[29,](#page-16-11)[32,](#page-16-12)[67\]](#page-18-15), data and label encoding [\[14\]](#page-15-6), selecting representative real samples [\[35\]](#page-16-13), model augmentation [\[65\]](#page-18-16), reduced budget for storage and transmissions with slimmable DD [\[34\]](#page-16-10), approaches with only a few networks [\[33\]](#page-16-14), or with model pool and label learning [\[74\]](#page-18-14). The exclusion of these methods is motivated by their orthogonality to our specific research focus, most of them can be implemented in conjunction with our methods as needed.

Evaluation Details on CIFAR-10/100: We adopt the simple 3-layer ConvNet architecture [\[19\]](#page-16-17) ensure a justifiable comparison with other approaches. To maintain consistency with prior research  $[6, 40]$  $[6, 40]$ , we incorporate ZCA whitening and simple data augmentation techniques. Nonetheless, we did not employ Differentiable Siamese Augmentation (DSA) [\[66\]](#page-18-9) in our baseline method to align identical setups with other baselines. Lastly, to ensure the justification and fairness of our comparison, we adopt a consistent test and learning setup aligned with other LDD methods [\[6,](#page-15-7) [16\]](#page-15-8). This choice aims at providing a fair platform for evaluating the effectiveness and performance of our approach in comparison to existing LDD methodologies.

Evaluate Details on Tiny ImageNet and ImageNet Subsets: Following previous MTT methods [\[6,](#page-15-7) [16\]](#page-15-8), we extend our methodology to larger datasets. Specifically, we utilize the 4-layer ConvNet and 5-layer ConvNet as distillation models for Tiny ImageNet and ImageNet, respectively. To validate the effectiveness of our approach, we conduct experiments on six ImageNet subsets that were previously introduced to dataset distillation by [\[6\]](#page-15-7). This ensures that the evaluation is consistent and enables a meaningful assessment of the performance of our approach in comparison to existing methodologies.

<span id="page-10-1"></span>Table 1: The table presents the cross-architecture performance of CIFAR-10 with IPC=10. The network architectures listed in the table remain consistent with those used in previous works [\[6,](#page-15-7)[16\]](#page-15-8), with no modifications made to normalization or pooling layers to ensure fair performance comparisons. We reproduced MTT's result on IPC=50 and FTD's result on IPC=10 for a thorough assessment. The scores are all in percentage. Our method excels significantly in cross-architecture performance under various IPCs.

|      | ConvNet                                                                                                                          |    | ResNet18             |     | VGG11                         |  | AlexNet          |    |
|------|----------------------------------------------------------------------------------------------------------------------------------|----|----------------------|-----|-------------------------------|--|------------------|----|
| IPC. | $\overline{10}$                                                                                                                  | 50 | $10 \quad \text{or}$ | -50 | 10 50                         |  | 10 —             | 50 |
|      | KIP [40] $47.6 \pm 0.9$ -                                                                                                        |    | $36.8 \pm 1.0$ -     |     | $42.1 \pm 0.4$ -              |  | $24.4 \pm 3.9$ - |    |
|      | DSA [66] $52.1 \pm 0.4$ -                                                                                                        |    |                      |     | $42.8 \pm 1.0$ $43.2 \pm 0.5$ |  | $35.9 \pm 1.3$ - |    |
|      | MTT [6] 64.3 ± 0.7 71.6 ± 0.2 46.4 ± 0.6 61.9 ± 0.7 50.3 ± 0.8 55.4 ± 0.8 34.2 ± 2.6 48.2 ± 1.0                                  |    |                      |     |                               |  |                  |    |
|      | FTD [16] 66.1 $\pm$ 0.3 73.8 $\pm$ 0.2 53.2 $\pm$ 1.4 65.7 $\pm$ 0.3 47.0 $\pm$ 1.5 58.4 $\pm$ 1.6 35.9 $\pm$ 2.3 53.8 $\pm$ 0.9 |    |                      |     |                               |  |                  |    |
|      | ATT 67.7 $\pm$ 0.6 74.5 $\pm$ 0.4 54.5 $\pm$ 0.9 66.3 $\pm$ 1.1 54.2 $\pm$ 0.8 61.7 $\pm$ 0.9 43.6 $\pm$ 1.4 60.0 $\pm$ 0.9      |    |                      |     |                               |  |                  |    |

<span id="page-10-0"></span>

### 4.2 Cross-Architecture Generalization

In the evaluation of DD, Cross-Architecture (CA) generalization serves as a crucial metric, gauging the synthetic dataset's capacity to be effectively learned by various architectures. Despite its significance, many prior works struggled to exhibit satisfactory generalization across different architectures. In this subsection, we delve into the examination of ATT in terms of cross-architecture generalization.

We evaluate the CA performance of ATT on ResNet18 [\[22\]](#page-16-18), VGG11 [\[49\]](#page-17-18), AlexNet [\[28\]](#page-16-19), and the distillation architecture ConvNet, aligning with architectures studied in prior research. To ensure a fair comparison, we opt for simple network architectures to mitigate the impact of high test performance associated with more powerful structures. In line with closely related works, MTT [\[6\]](#page-15-7) and FTD [\[16\]](#page-15-8), which assessed CA performance on different IPCs, we present CA results for our methods in both scenarios. Additionally, we replicate their missing CA evaluations on the other IPC, contributing to a comprehensive and balanced assessment.

The results, as presented in Tab. [1,](#page-10-1) showcase the remarkable performance of our proposed method. ATT significantly outperforms previous methods in CA generalization. Specifically, compared to the previous best result with  $IPC=10$ , ATT achieves an improvement of 1.3% on ResNet18, 7.2% on VGG11, and 7.7% on AlexNet. Furthermore, for IPC=50, ATT demonstrates improvements of 3.3% on VGG11 and 6.2% on AlexNet.

This substantial improvement can be attributed to the mitigation of AMP, as discussed in Sec. [3.2.](#page-5-0) FTL methods, as revealed in previous discussions, tend to force student trajectories to match with an inappropriate trajectory length, leading to AMP. Consequently, FTL methods exhibit overfitting to seen expert trajectories while sacrificing generalization to unseen trajectories. The CA performance of ATT underscores the significance of addressing the AMP, especially when dealing with unseen trajectories from previously unencountered architectures.

<span id="page-11-2"></span>Table 2: The table showcases performance comparison to benchmark methods in terms of test accuracy. LD and DD use AlexNet for CIFAR-10, and all the rest are DD methods using the simple ConvNet for the distillation process. IPC: Images Per Class. The numbers in table are presented in percentage. As shown, our method generally outperforms existing methods on various dataset and IPCs.

|                  |  | $CIFAR-10$                                                                                              |    | $CIFAR-100$  |                  |    | Tiny ImageNet                                                                                                           |                |  |
|------------------|--|---------------------------------------------------------------------------------------------------------|----|--------------|------------------|----|-------------------------------------------------------------------------------------------------------------------------|----------------|--|
| Full dataset     |  | $84.8 \pm 0.1$                                                                                          |    |              | $56.2 \pm 0.3$   |    | $37.6 \pm 0.4$                                                                                                          |                |  |
| IPC.             |  | 10                                                                                                      | 50 | $\mathbf{1}$ | 10               | 50 |                                                                                                                         | 10             |  |
| DD [59]          |  | $36.8 \pm 1.2$ -                                                                                        |    | $\sim$       | $\sim$ 100 $\mu$ |    |                                                                                                                         |                |  |
| DC [69]          |  | $28.3 \pm 0.5$ $44.9 \pm 0.5$ $53.9 \pm 0.5$ $12.8 \pm 0.3$ $25.2 \pm 0.3$ -                            |    |              |                  |    |                                                                                                                         |                |  |
| $DSA$ [66]       |  | $28.8 \pm 0.7$ $52.1 \pm 0.5$ $60.6 \pm 0.5$ $13.9 \pm 0.3$ $32.3 \pm 0.3$ $42.8 \pm 0.4$ -             |    |              |                  |    |                                                                                                                         | $\sim$         |  |
| DM [68]          |  | $26.0 \pm 0.8$ $48.9 \pm 0.6$ $63.0 \pm 0.4$ $11.4 \pm 0.3$ $29.7 \pm 0.3$ $43.6 \pm 0.4$ $3.9 \pm 0.2$ |    |              |                  |    |                                                                                                                         | $12.9 \pm 0.4$ |  |
| <b>CAFE</b> [58] |  | $30.0 \pm 1.1$ $46.3 \pm 0.6$ $55.5 \pm 0.6$ $12.9 \pm 0.3$ $27.8 \pm 0.3$ $37.9 \pm 0.3$ -             |    |              |                  |    |                                                                                                                         |                |  |
| KIP [40]         |  | $49.9 \pm 0.2$ 62.7 $\pm$ 0.3 68.6 $\pm$ 0.2 15.7 $\pm$ 0.2 28.3 $\pm$ 0.1 -                            |    |              |                  |    |                                                                                                                         |                |  |
| $MTT$ [6]        |  | $46.2 \pm 0.8$ 65.4 $\pm$ 0.7 71.6 $\pm$ 0.2 24.3 $\pm$ 0.3 39.7 $\pm$ 0.4 47.7 $\pm$ 0.2 8.8 $\pm$ 0.3 |    |              |                  |    |                                                                                                                         | $23.2 \pm 0.2$ |  |
| PP [31]          |  | $46.4 \pm 0.6$ 65.5 $\pm$ 0.3 71.9 $\pm$ 0.2 24.6 $\pm$ 0.1 43.1 $\pm$ 0.3 48.4 $\pm$ 0.3 -             |    |              |                  |    |                                                                                                                         |                |  |
| $FTD$ [16]       |  |                                                                                                         |    |              |                  |    | $46.8 \pm 0.3$ $66.6 \pm 0.3$ $73.8 \pm 0.2$ $25.2 \pm 0.2$ $43.4 \pm 0.3$ $50.7 \pm 0.3$ $10.4 \pm 0.3$ $24.5 \pm 0.2$ |                |  |
| <b>ATT</b>       |  |                                                                                                         |    |              |                  |    | $48.3 \pm 1$ 67.7 $\pm$ 0.6 74.5 $\pm$ 0.4 26.1 $\pm$ 0.3 44.2 $\pm$ 0.5 51.2 $\pm$ 0.3 11.0 $\pm$ 0.5 25.8 $\pm$ 0.4   |                |  |

<span id="page-11-1"></span>

### 4.3 Benchmark Comparison

Evaluation of distilled architecture performance serves as a crucial metric in dataset distillation, gauging the efficacy of synthetic dataset learning on refined architectures. Our test performance results are detailed in Tab. [2](#page-11-2) and Tab. [3.](#page-12-0) ATT, addressing the AMP, aims at improving generality on trajectories not encountered before. Thus, the result additionally reflect how much they suffer from AMP.

Notably, on datasets with substantial image sizes, such as ImageNet with IPC=10, ATT exhibits a significant improvement over the previous best method: 2.2% on subset Meow, 1.6% on subset Nette, 1.2% on subset Woof, and 1% on subset Fruit. The impact extends to datasets with increased class complexity, exemplified on Tiny ImageNet, where improvements of 1.3% on IPC=10 and  $0.6\%$  on IPC=1 are observed. CIFAR-10 experiences notable enhancements as well, with improvements of  $1.5\%$  on IPC=1 over FTD,  $1.1\%$  on IPC=10 over previous best result. CIFAR-100 also sees improvements, with up to 0.9% at different IPCs. While KIP demonstrate commendable performance in IPC=1 on the CIFAR-10 dataset, ATT significantly outperforms them on larger IPCs and more extensive datasets.

It is crucial to note that, while test performance on distilled architecture is generally significant, it is not as pronounced as those on unseen networks from unfamiliar architectures, as discussed in Sec. [4.2.](#page-10-0) This disparity arises from FTL methods potentially overfitting to pre-buffered expert trajectories, which does not include other network architectures other than the distilled architecture.

<span id="page-11-0"></span>

### 4.4 Ablation Study on Parameters

**Trajectory Bounds**  $N<sub>S</sub>$ : In order to accommodate diverse memory constraints, ATT incorporates the trajectory length parameter  $N<sub>S</sub>$  to examine the scope of the search for the optimal length. In our experimental analysis, we delve into

<span id="page-12-0"></span>Table 3: Performance comparison of our method ATT to other baselines on ImageNet subset. All the scores are presented in percentage. As shown, our method demonstrates significant improvement on large dataset ImageNet, especially on  $IPC=10$ 

| Methods             | IPC.    | ImageNet Subsets<br>ImageMeow<br>ImageNette<br>ImageWoof ImageFruit |                              |                                |                                |  |  |  |
|---------------------|---------|---------------------------------------------------------------------|------------------------------|--------------------------------|--------------------------------|--|--|--|
| <b>Full Dataset</b> |         | $87.4 \pm 1.0$                                                      | $67.0 + 1.3$                 | $63.9 \pm 2.0$                 | $66.7 \pm 1.1$                 |  |  |  |
| $MTT$ [6]           | 1<br>10 | $47.7 + 0.9$<br>$63.0 + 1.3$                                        | $28.6 + 0.8$<br>$35.8 + 1.8$ | $26.6 + 0.8$<br>$40.3 + 1.3$   | $30.7 + 1.6$<br>$40.4 + 2.2$   |  |  |  |
| FTD [16]            | 1<br>10 | $52.2 + 1.0$<br>$67.7 + 0.7$                                        | $30.1 + 1.0$<br>$38.8 + 1.4$ | $29.1 + 0.9$<br>$44.9 + 1.5$   | $33.8 + 1.5$<br>$43.3 + 0.6$   |  |  |  |
| <b>ATT</b>          | 10      | $52.4 + 1.1$<br>$69.3 + 0.8$                                        | $31.2 + 1.8$<br>$40.0 + 1.4$ | $30.0 + 0.8$<br>$45.9 \pm 1.2$ | $34.0 + 1.4$<br>$45.5 \pm 1.6$ |  |  |  |

<span id="page-12-1"></span>Image /page/12/Figure/3 description: The image displays a set of plots comparing the performance of FTL methods and ATT across different parameters. The left side features three line plots, each labeled with a different value for Ns (30, 50, and 70). These plots track the 'Selected step' on the y-axis against 'Iteration' on the x-axis. For Ns=30 and Ns=50, both FTL methods (blue line) and ATT (orange line) converge to approximately 30 and 50 selected steps, respectively. For Ns=70, ATT shows a more volatile performance, reaching up to 70 selected steps, while FTL methods stabilize around 50. The right side of the image contains two bar charts, one for 'Parameter lr(sz)' and another for 'Parameter lr(img)'. Both charts plot the '# of cases succeeded' on the y-axis against 'Multiplier' on the x-axis, with multipliers shown on a logarithmic scale. In the 'Parameter lr(sz)' chart, FTL methods succeed in 20 cases for multipliers 10^1, 10^2, and 10^3, then drop significantly. ATT succeeds in 20 cases for multipliers 10^1, 10^2, 10^3, 10^4, and 10^5. In the 'Parameter lr(img)' chart, FTL methods succeed in 20 cases for multipliers 10^1, 10^3, and 10^5, and then show a decreasing trend. ATT consistently succeeds in 20 cases for multipliers 10^1, 10^3, 10^5, 10^7, and 10^8, with a slight drop at 10^9.

Fig. 4: Left: This plot illustrates ATT's step selection during the distillation phase, highlighting its preference for smaller steps initially, contributing to enhanced stability during parameter tuning. Right: The plot demonstrates ATT's superior stability in varying parameters, showcasing the number of successful cases when alter parameter to mutiplier times. Different multipliers are presented as distinct cases.

the behavior of ATT, specifically examining the step selection process during distillation. This investigation focuses on delineating how the optimal step  $N_{opt}$ evolves under different trajectory length constraints throughout the distillation process.

Our experiments are conducted on CIFAR-10 with IPC=1, and we emperically choose  $N<sub>S</sub>$  to be 30, 50, 70, indicating cases with not sufficient steps, close to optimal steps, and too much steps. The results, as depicted on left of Fig. [4,](#page-12-1) highlight the distinction between FTL and ATT. While FTL consistently opts for a fixed trajectory length, ATT tends to dynamically adjust its trajectory length, starting with smaller values at the beginning and subsequently adapting, fluctuating below  $N<sub>S</sub>$  to minimize errors. The behavior remains with different  $N<sub>S</sub>$ , and we will show more results on various  $N<sub>S</sub>$  and Iterations in Appendix [A.2.](#page-19-1) This adaptive behavior allows ATT to perform short trajectory matching initially instead of enforcing larger trajectory matching.

ATT and Parameter Variations: We empirically substantiate ATT's stability toward parameter variation by introducing variations in two sensitive parame-

ters: the learning rate of pixels, denoted as lr(img) and the learning rate of step size, denoted as  $\text{lr}(sz)$ . They are tuning parameters that determine the step size for the synthetic dataset and the trainable learning rate, respectively. We align the initial parameters with the vanilla LDD method, and multiply the examining parameters by factors of multiplier. Specifically, we do not make additional changes to variables other than the one under examination. Each experiment is repeated twenty times, and we document the number of successful cases under each parameter setup. Cases of failure are identified by divergent loss or negative trainable learning rate, while cases with convergent loss are considered successful.

As demonstrated on the right of Fig. [4,](#page-12-1) ATT exhibits higher stability under different parameter sets. This is attributed to ATT allowing and selecting a small step size for the student trajectory, facilitating the synthetic data to learn from early trajectories at the beginning, instead of enforcing larger trajectory matching. This advantage holds significant support when undertaking DD on a new dataset.

<span id="page-13-0"></span>

### 4.5 Storage and Computation

Similar to other LDD methods such as [\[6,](#page-15-7)[16\]](#page-15-8), computational overhead and storage requirements present challenges. However, LDD methods yield synthetic datasets with superior test performances across various network architectures, benefiting end-users at the cost of increased resources for producers. In our experiments, we primarily use five A100 GPUs, and utilize 100 expert trajectories as the default setting. Assessing the storage requirements for expert trajectories, we find approximately 60MB for each CIFAR expert, and 120MB for each ImageNet expert. We list more details in Appendix [A.5.](#page-21-0)

While ATT incurs time in employing distance measurement with L2 norm, it strategically prioritizes matching steps where  $t < N<sub>S</sub>$ , enabling early backpropagation on trajectories and avoiding unnecessary computations at the tail end. This approach serves as a time-efficient compensation for the distance calculation. To illustrate, we conduct experiments on CIFAR-10 with  $IPC=1$ , comparing our method to the vanilla LDD approach. The experiments employ identical parameters and hardware setups, repeated ten times, and the average time per iteration is determined. As a result, both the vanilla LDD method and ATT exhibit a similar runtime of 0.8s per iteration.

# 5 Conclusions

In this paper, we delved into long-range matching dataset distillation methods, and reveal that traditional long-range matching methods exhibit persistent limitations and mismatches in aligning trajectories, resulting in the Accumulated Mismatching Problem (AMP). We further reveal AMP's contribution to matching error, and propose ATT to address AMP. Empirical results showcase that ATT not only rectifies the AMP, but also enhances stability when confronted with parameter changes, and the method exhibits superior cross-architecture performance.

As we continue to explore innovative methodologies in dataset distillation, the insights gained from this research pave the way for more effective and adaptable approaches in the field.

Acknowledgment This work is funded by Bayerische Forschungsstiftung under the research grants Von der Edge zur Cloud und zurück: Skalierbare und Adaptive Sensordatenverarbeitung (AZ-1468-20), and supported by AI systems hosted and operated by the Leibniz-Rechenzentrum (LRZ) der Bayerischen Akademie der Wissenschaften. Further, part of the results have been obtained on systems in the test environment BEAST (Bavarian Energy Architecture & Software Testbed) at the Leibniz Supercomputing Centre.

# References

- <span id="page-15-17"></span>1. Agarap, A.F.: Deep learning using rectified linear units (relu). arXiv preprint arXiv:1803.08375 (2018)
- <span id="page-15-2"></span>2. Aljundi, R., Lin, M., Goujaud, B., Bengio, Y.: Gradient based sample selection for online continual learning. Advances in neural information processing systems 32 (2019)
- <span id="page-15-12"></span>3. Assadi, S., Bateni, M., Bernstein, A., Mirrokni, V., Stein, C.: Coresets meet edcs: algorithms for matching and vertex cover on massive graphs. In: Proceedings of the Thirtieth Annual ACM-SIAM Symposium on Discrete Algorithms. pp. 1616–1635. SIAM (2019)
- <span id="page-15-0"></span>4. Bachem, O., Lucic, M., Krause, A.: Practical coreset constructions for machine learning. arXiv preprint arXiv:1703.06476 (2017)
- <span id="page-15-9"></span>5. Bohdal, O., Yang, Y., Hospedales, T.: Flexible dataset distillation: Learn labels instead of images. arXiv preprint arXiv:2006.08572 (2020)
- <span id="page-15-7"></span>6. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 4750–4759 (2022)
- <span id="page-15-5"></span>7. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Wearable imagenet: Synthesizing tileable textures via dataset distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 2278– 2282 (2022)
- <span id="page-15-11"></span>8. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Generalizing dataset distillation via deep generative prior. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3739–3748 (2023)
- <span id="page-15-3"></span>9. Chen, Y., Welling, M.: Parametric herding. In: Proceedings of the Thirteenth International Conference on Artificial Intelligence and Statistics. pp. 97–104. JMLR Workshop and Conference Proceedings (2010)
- <span id="page-15-14"></span>10. Chen, Y., Welling, M., Smola, A.: Super-samples from kernel herding. arXiv preprint arXiv:1203.3472 (2012)
- <span id="page-15-10"></span>11. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory (2022)
- <span id="page-15-13"></span>12. Dasgupta, A., Drineas, P., Harb, B., Kumar, R., Mahoney, M.W.: Sampling algorithms and coresets for \ell\_p regression. SIAM Journal on Computing  $38(5)$ , 2060–2078 (2009)
- <span id="page-15-15"></span>13. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A large-scale hierarchical image database. In: 2009 IEEE Conference on Computer Vision and Pattern Recognition. pp. 248–255 (2009). [https://doi.org/10.1109/CVPR.2009.](https://doi.org/10.1109/CVPR.2009.5206848) [5206848](https://doi.org/10.1109/CVPR.2009.5206848)
- <span id="page-15-6"></span>14. Deng, Z., Russakovsky, O.: Remember the past: Distilling datasets into addressable memories for neural networks (2022)
- <span id="page-15-4"></span>15. Dong, T., Zhao, B., Lyu, L.: Privacy for free: How does dataset condensation help privacy? In: International Conference on Machine Learning. pp. 5378–5396. PMLR (2022)
- <span id="page-15-8"></span>16. Du, J., Jiang, Y., Tan, V.T., Zhou, J.T., Li, H.: Minimizing the accumulated trajectory error to improve dataset distillation. arXiv preprint arXiv:2211.11004 (2023)
- <span id="page-15-16"></span>17. Fastai: A smaller subset of 10 easily clas- sified classes from imagenet, and a little more french
- <span id="page-15-1"></span>18. Feldman, D.: Core-sets: Updated survey. Sampling techniques for supervised or unsupervised tasks pp. 23–44 (2020)

- <span id="page-16-17"></span>19. Gidaris, S., Komodakis, N.: Dynamic few-shot visual learning without forgetting. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 4367–4375 (2018)
- <span id="page-16-5"></span>20. Guo, Z., Wang, K., Cazenavette, G., Li, H., Zhang, K., You, Y.: Towards lossless dataset distillation via difficulty-aligned trajectory matching. arXiv preprint arXiv:2310.05773 (2023)
- <span id="page-16-15"></span>21. Har-Peled, S., Kushal, A.: Smaller coresets for k-median and k-means clustering. In: Proceedings of the twenty-first annual symposium on Computational geometry. pp. 126–134 (2005)
- <span id="page-16-18"></span>22. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition (2015)
- <span id="page-16-6"></span>23. Hinton, G., Vinyals, O., Dean, J.: Distilling the knowledge in a neural network. arXiv preprint arXiv:1503.02531 (2015)
- <span id="page-16-0"></span>24. Kendall, A., Gal, Y.: What uncertainties do we need in bayesian deep learning for computer vision? Advances in neural information processing systems 30 (2017)
- <span id="page-16-9"></span>25. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: International Conference on Machine Learning. pp. 11102–11118. PMLR (2022)
- <span id="page-16-3"></span>26. Kiyasseh, D., Zhu, T., Clifton, D.A.: Pcps: Patient cardiac prototypes to probe ai-based medical diagnoses, distill datasets, and retrieve patients. Transactions on Machine Learning Research (2022)
- <span id="page-16-16"></span>27. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images (2009)
- <span id="page-16-19"></span>28. Krizhevsky, A., Sutskever, I., Hinton, G.E.: Imagenet classification with deep convolutional neural networks. Advances in neural information processing systems 25 (2012)
- <span id="page-16-11"></span>29. Lee, H.B., Lee, D.B., Hwang, S.J.: Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494 (2022)
- <span id="page-16-7"></span>30. Lee, S., Chun, S., Jung, S., Yun, S., Yoon, S.: Dataset condensation with contrastive signals. In: International Conference on Machine Learning. pp. 12352– 12364. PMLR (2022)
- <span id="page-16-4"></span>31. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Dataset distillation using parameter pruning. arXiv preprint arXiv:2209.14609 (2022)
- <span id="page-16-12"></span>32. Liu, S., Wang, K., Yang, X., Ye, J., Wang, X.: Dataset distillation via factorization (2022)
- <span id="page-16-14"></span>33. Liu, S., Wang, X.: Few-shot dataset distillation via translative pre-training. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 18654–18664 (October 2023)
- <span id="page-16-10"></span>34. Liu, S., Ye, J., Yu, R., Wang, X.: Slimmable dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3759–3768 (2023)
- <span id="page-16-13"></span>35. Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., You, Y.: Dream: Efficient dataset distillation by representative matching (2023)
- <span id="page-16-1"></span>36. Liu, Y., Li, Z., Backes, M., Shen, Y., Zhang, Y.: Backdoor attacks against dataset distillation. arXiv preprint arXiv:2301.01197 (2023)
- <span id="page-16-2"></span>37. Loo, N., Hasani, R., Lechner, M., Rus, D.: Dataset distillation fixes dataset reconstruction attacks. arXiv preprint arXiv:2302.01428 (2023)
- <span id="page-16-8"></span>38. Loo, N., Hasani, R., Lechner, M., Rus, D.: Dataset distillation with convexified implicit gradients (2023)

- 18 D. Liu et al.
- <span id="page-17-14"></span>39. Mirrokni, V., Zadimoghaddam, M.: Randomized composable core-sets for distributed submodular maximization. In: Proceedings of the forty-seventh annual ACM symposium on Theory of computing. pp. 153–162 (2015)
- <span id="page-17-11"></span>40. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34, 5186–5198 (2021)
- <span id="page-17-2"></span>41. Nickolls, J., Dally, W.J.: The gpu computing era. IEEE micro 30(2), 56–69 (2010)
- <span id="page-17-13"></span>42. Olvera-López, J.A., Carrasco-Ochoa, J.A., Martínez-Trinidad, J.F., Kittler, J.: A review of instance selection methods. Artificial Intelligence Review 34, 133–143 (2010)
- <span id="page-17-0"></span>43. O'Mahony, N., Campbell, S., Carvalho, A., Harapanahalli, S., Hernandez, G.V., Krpalkova, L., Riordan, D., Walsh, J.: Deep learning vs. traditional computer vision. In: Advances in Computer Vision: Proceedings of the 2019 Computer Vision Conference (CVC), Volume 1 1. pp. 128–144. Springer (2020)
- <span id="page-17-16"></span>44. Paul, M., Ganguli, S., Dziugaite, G.K.: Deep learning on a data diet: Finding important examples early in training. Advances in Neural Information Processing Systems 34, 20596–20607 (2021)
- <span id="page-17-7"></span>45. Pi, R., Zhang, W., Xie, Y., Gao, J., Wang, X., Kim, S., Chen, Q.: Dynafed: Tackling client data heterogeneity with global dynamics. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 12177–12186 (2023)
- <span id="page-17-10"></span>46. Sangermano, M., Carta, A., Cossu, A., Bacciu, D.: Sample condensation in online continual learning. In: 2022 International Joint Conference on Neural Networks (IJCNN). pp. 01–08. IEEE (2022)
- <span id="page-17-3"></span>47. Schwartz, R., Dodge, J., Smith, N.A., Etzioni, O.: Green ai. Communications of the ACM 63(12), 54–63 (2020)
- <span id="page-17-4"></span>48. Sener, O., Savarese, S.: Active learning for convolutional neural networks: A coreset approach. arXiv preprint arXiv:1708.00489 (2017)
- <span id="page-17-18"></span>49. Simonyan, K., Zisserman, A.: Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556 (2014)
- <span id="page-17-8"></span>50. Song, R., Liu, D., Chen, D.Z., Festag, A., Trinitis, C., Schulz, M., Knoll, A.: Federated learning via decentralized dataset distillation in resource-constrained edge environments. arXiv preprint arXiv:2208.11311 (2022)
- <span id="page-17-12"></span>51. Sucholutsky, I., Schonlau, M.: Soft-label dataset distillation and text dataset distillation. In: 2021 International Joint Conference on Neural Networks (IJCNN). pp. 1–8. IEEE (2021)
- <span id="page-17-6"></span>52. Toneva, M., Sordoni, A., Combes, R.T.d., Trischler, A., Bengio, Y., Gordon, G.J.: An empirical study of example forgetting during deep neural network learning. arXiv preprint arXiv:1812.05159 (2018)
- <span id="page-17-5"></span>53. Tsang, I.W., Kwok, J.T., Cheung, P.M., Cristianini, N.: Core vector machines: Fast svm training on very large data sets. Journal of Machine Learning Research  $6(4)$ (2005)
- <span id="page-17-15"></span>54. Tukan, M., Maalouf, A., Feldman, D.: Coresets for near-convex functions. Advances in Neural Information Processing Systems 33, 997–1009 (2020)
- <span id="page-17-17"></span>55. Ulyanov, D., Vedaldi, A., Lempitsky, V.: Instance normalization: The missing ingredient for fast stylization. arXiv preprint arXiv:1607.08022 (2016)
- <span id="page-17-1"></span>56. Voulodimos, A., Doulamis, N., Doulamis, A., Protopapadakis, E., et al.: Deep learning for computer vision: A brief review. Computational intelligence and neuroscience 2018 (2018)
- <span id="page-17-9"></span>57. Wang, J., Guo, S., Xie, X., Qi, H.: Protect privacy from gradient leakage attack in federated learning. In: IEEE INFOCOM 2022-IEEE Conference on Computer Communications. pp. 580–589. IEEE (2022)

- <span id="page-18-8"></span>58. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: Cafe: Learning to condense dataset by aligning features. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 12196–12205 (2022)
- <span id="page-18-2"></span>59. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-18-5"></span>60. Wiewel, F., Yang, B.: Condensed composite memory continual learning. In: 2021 International Joint Conference on Neural Networks (IJCNN). pp. 1–8. IEEE (2021)
- <span id="page-18-6"></span>61. Xu, Z., Chen, Y., Pan, M., Chen, H., Das, M., Yang, H., Tong, H.: Kernel ridge regression-based graph dataset distillation. In: Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. pp. 2850–2861 (2023)
- <span id="page-18-0"></span>62. Yang, S., Xie, Z., Peng, H., Xu, M., Sun, M., Li, P.: Dataset pruning: Reducing training data by examining generalization influence. arXiv preprint arXiv:2205.09329 (2022)
- <span id="page-18-13"></span>63. Yin, Z., Xing, E., Shen, Z.: Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-18-1"></span>64. Yoon, J., Madaan, D., Yang, E., Hwang, S.J.: Online coreset selection for rehearsalbased continual learning. arXiv preprint arXiv:2106.01085 (2021)
- <span id="page-18-16"></span>65. Zhang, L., Zhang, J., Lei, B., Mukherjee, S., Pan, X., Zhao, B., Ding, C., Li, Y., Xu, D.: Accelerating dataset distillation via model augmentation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11950–11959 (2023)
- <span id="page-18-9"></span>66. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: International Conference on Machine Learning. pp. 12674–12685. PMLR (2021)
- <span id="page-18-15"></span>67. Zhao, B., Bilen, H.: Synthesizing informative training samples with gan. arXiv preprint arXiv:2204.07513 (2022)
- <span id="page-18-10"></span>68. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. pp. 6514–6523 (2023)
- <span id="page-18-11"></span>69. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020)
- <span id="page-18-12"></span>70. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation (2023)
- <span id="page-18-7"></span>71. Zhmoginov, A., Sandler, M., Miller, N., Kristiansen, G., Vladymyrov, M.: Decentralized learning with multi-headed distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 8053–8063 (2023)
- <span id="page-18-3"></span>72. Zhou, Y., Ma, X., Wu, D., Li, X.: Communication-efficient and attack-resistant federated edge learning with dataset distillation. IEEE Transactions on Cloud Computing (2022)
- <span id="page-18-4"></span>73. Zhou, Y., Pu, G., Ma, X., Li, X., Wu, D.: Distilled one-shot federated learning. arXiv preprint arXiv:2009.07999 (2020)
- <span id="page-18-14"></span>74. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. arXiv preprint arXiv:2206.00719 (2022)

<span id="page-19-2"></span>Image /page/19/Figure/0 description: This figure displays five line graphs, each representing a different value of gamma (γ=1, γ=2, γ=3, γ=4, γ=5). The x-axis for all graphs is labeled "N'th interval" and ranges from 0 to 60. The y-axis for all graphs is labeled "# of iterations" and ranges from 0 to 50. Each graph shows two lines: a blue line labeled "line a: # of cases matched with lower errors" and an orange line labeled "line b: # of cases matched with higher errors". Additionally, each graph includes a dashed green line representing the "line c: mean value of line a" and a dashed red line representing the "line d: mean value of line b". The blue line generally stays between 35 and 45 iterations, with its mean around 40. The orange line fluctuates between 5 and 15 iterations, with its mean around 10. The title "20 D. Liu et al." appears above the graphs.

Fig. 5: The figure illustrates the impact of the tolerance parameter,  $\gamma$ , on the performance of FTL. As depicted, the number of iterations exhibits fluctuations under 50, indicating the consistent presence of this phenomenon throughout the entire distillation process. Consequently, it is evident that AMP persists even when a certain level of tolerance for mistakes, as represented by  $\gamma$ , is applied. This finding emphasizes the resilience of AMP under varying degrees of error tolerance, underscoring the importance of addressing and mitigating this phenomenon in distillation processes.

# A Appendix

<span id="page-19-0"></span>

### A.1 FTL and tolerance $\gamma$

As previously elucidated in Sec. 3.2, we introduce the parameter  $\gamma$  as a measure of tolerance for errors occurring in traditional LDD utilizing FTL. In Sec. 3.2, we illustrate the implementation of  $\gamma = 2$  in our experiments. In this subsection, we additionally explore the effect of varying  $\gamma$  within the range of [0, 5], allowing for a maximum deviation of 10% from the expected outcomes. Notably, previous traditional methods have commonly employed  $N<sub>S</sub> = 50$  as the optimal parameter for experiments conducted on CIFAR-10 with IPC=1. Consequently, we extend our experimental investigations to specifically focus on the parameter  $N<sub>S</sub> = 50$ .

As depicted in Fig. [5,](#page-19-2) our findings indicate that, even with different tolerance levels for errors, FTL continues to make incorrect decisions throughout the distillation process. This phenomenon persists despite the introduction of tolerance measures, highlighting the inherent challenges associated with error mitigation in traditional distillation methods.

<span id="page-19-1"></span>

### A.2 ATT and extensive $N_S$

As presented in Section 4.4, we conduct experiments to examine the trajectory selection behavior of the ATT under different trajectory bounds, specifically  $N<sub>S</sub>$ values of 30, 50, and 70, using the CIFAR-10 dataset with IPC=1. The results reveal a consistent pattern wherein ATT consistently favored choosing smaller steps in the initial stages of the learning process. We demonstrate more of our results from additional trajectory bounds at 10, 50, 90, 110, and 130.

<span id="page-20-0"></span>Image /page/20/Figure/1 description: This figure displays six subplots, each representing a different value of Ns (10, 50, 90, 110, 130, and 50). The x-axis of each subplot is labeled 'Iteration', and the y-axis is labeled 'Selected step'. Two lines are plotted in each subplot: a purple dashed line with dots representing 'FTL methods' and a green solid line representing 'ATT'. The 'FTL methods' line generally stays at a constant 'Selected step' value, which varies across the subplots (around 10, 50, 90, 110, 130, and 50 respectively). The 'ATT' line shows a fluctuating trend, generally increasing over iterations and sometimes reaching or exceeding the 'FTL methods' line, particularly in the subplots with higher Ns values. The last subplot with Ns=50 shows the 'ATT' line fluctuating around a value of 50 over a much larger range of iterations (0 to 2000).

Fig. 6: The figure illustrates the influence of extended trajectory bounds, denoted as  $N<sub>S</sub>$ , on the behavior of ATT. As depicted in the first five small plots, regardless of the specific value of  $N<sub>S</sub>$ , ATT consistently exhibits a preference for selecting steps that progress from smaller to larger magnitudes. With the last small plot, we showcase ATT's selection over long distillation, where adjustments maintains. The observed behavior highlights a consistent pattern in the step selection process of ATT, emphasizing its tendency to prioritize learning from smaller steps initially before transitioning to larger ones, irrespective of the trajectory bounds under consideration.

As depicted in the accompanying figure Fig. [6,](#page-20-0) ATT consistently adheres to its established pattern of step selection across a broad range of trajectory bounds, including suitable, extensively small and extensively large values (i.e.10, 50, 90, 110, 130). Notably, ATT demonstrates a predisposition to learn from smaller steps initially, gradually transitioning towards larger steps. This observed tendency supports ATT's stability as discussed in Sec. 4.4. We also demonstrate how ATT's test accuracy varies with  $N<sub>S</sub>$  in Fig. [7,](#page-21-1) in comparison with MTT. ATT demonstrates stability when varying  $N<sub>S</sub>$ .

### A.3 Random Seeds

To ensure randomness and non-repetitiveness in our experiments, we maintain the random seeds used in MTT [\[6\]](#page-15-7) for model initialization. These random seeds are based on time, which means that each time we run the experiments, a different random seed is generated. By using time-based random seeds, we can ensure that the initialized models are random and unique for each initialization and each iteration.

This approach helps to avoid any potential biases or repetitive patterns that may arise from using the same initial model configurations in multiple experiments. By introducing randomness through time-based random seeds, we enhance the reliability and validity of our experimental results by exploring a wider range of possible model initialization.

<span id="page-21-1"></span>Image /page/21/Figure/1 description: The image is a line graph showing the relationship between trajectory length and test accuracy for two different methods, MTT and ATT. The x-axis represents trajectory length, with values ranging from 20 to 100. The y-axis represents test accuracy, with values ranging from 63 to 68. The MTT line (blue) starts at approximately 64.2 at a trajectory length of 10, increases to about 65.6 at 30, then to 65.8 at 50, drops to 64.0 at 70, rises to 64.4 at 90, and finally drops to 61.8 at 110. The ATT line (orange) starts at approximately 64.5 at a trajectory length of 10, rises sharply to 67.6 at 30, and then plateaus around 67.8 from 40 to 110. The legend indicates that the blue line represents MTT and the orange line represents ATT.

Fig. 7: The figure demonstrate variation on ATT's test accuracy with  $N<sub>S</sub>$ . ATT's performance increasing with  $N<sub>S</sub>$  and then saturates. The experiments performed on CIFRA-10, and is in comparison with MTT.

<span id="page-21-2"></span>

| Dataset                    | Model        |                |                |                                                    | IPC $N_S$ $N_T$ Max start epoch $\ln(\text{img})$ |                                  | lr(sc)                              | $\ln$                               | zca                        |
|----------------------------|--------------|----------------|----------------|----------------------------------------------------|---------------------------------------------------|----------------------------------|-------------------------------------|-------------------------------------|----------------------------|
| $CIFAR-10$                 | ConvNetD3 10 | 1<br>50        | 80<br>30<br>50 | $\overline{4}$<br>$\overline{2}$<br>$\overline{2}$ | $\overline{2}$<br>20<br>40                        | $10^{2}$<br>$10^{4}$<br>$10^{2}$ | $10^{-7}$<br>$10^{-4}$<br>$10^{-5}$ | $10^{-2}$<br>$10^{-2}$<br>$10^{-3}$ | yes<br>yes<br>$\mathbf{n}$ |
| $CIFAR-100$                | ConvNetD3    | 1<br>-10<br>50 | 50<br>20<br>80 | $\overline{4}$<br>$\overline{2}$<br>$\overline{2}$ | 20<br>40<br>40                                    | 500<br>$10^3$<br>10 <sup>3</sup> | $10^{-5}$<br>$10^{-5}$<br>$10^{-5}$ | $10^{-2}$<br>$10^{-2}$<br>$10^{-2}$ | yes<br>no<br>yes           |
| Tiny ImageNet ConvNetD4 10 |              | 1              | 30<br>20       | $\overline{2}$<br>$\overline{2}$                   | 10<br>40                                          | $10^{4}$<br>10 <sup>4</sup>      | $10^{-4}$<br>$10^{-6}$              | $10^{-2}$<br>$10^{-3}$              | no<br>no                   |
| ImageNet                   | ConvNetD5    | 1<br>-10       | 20<br>20       | $\overline{2}$<br>$\overline{2}$                   | 10<br>10                                          | $10^{4}$<br>$10^{4}$             | $5 * 10^{-8}$<br>$10^{-4}$          | $10^{-2}$<br>$10^{-2}$              | no<br>no                   |

Table 4: Perparameters for different datasets

### A.4 Experimental Parameters

Important parameters in our method comprise, e.g. the bounds on synthetic steps  $N<sub>S</sub>$ , the number of steps to match on expert trajectories  $N<sub>T</sub>$ , the maximum starting epoch, the learning rate of images  $lr(img)$ , the learning rate of trainable step size  $lr(sc)$ , and the initialization of trainable step size  $lr$ . Further, we maintain the same ZCA normalization as the vanilla LDD method. The listed parameters for each dataset is presented in the table Tab. [4.](#page-21-2)

<span id="page-21-0"></span>

### A.5 Hardware and Storage Details

We primarily conduct our experiments on A100 GPUs. The largest requirement for GPUs in our experiment was five A100. The following Table [5](#page-22-0) presented the number of GPU we used for our experiments in the column  $\mu$  Cards", and we also listed the storage requirement for storing one expert trajectory. In our experiments, we use 100 expert trajectories as default for each experts creation.

<span id="page-22-0"></span>Table 5: The table presents the number of A100 GPUs and storage used for different experiments. The column " $\text{\#Cards}$ " indicates the number of A100 cards we used, and the column "Storage" indicates the storage requirement for storing one expert trajectory

| Dataset          | Model     | Storage | IPC | #Cards |
|------------------|-----------|---------|-----|--------|
| CIFAR-10         | ConvNetD3 | ~ 60MB  | 1   | 1      |
|                  |           |         | 10  | 1      |
|                  |           |         | 50  | 4      |
| CIFAR-100        | ConvNetD3 | ~ 100MB | 1   | 2      |
|                  |           |         | 10  | 2      |
|                  |           |         | 50  | 5      |
| Tiny ImageNet    | ConvNetD4 | ~ 170MB | 1   | 3      |
|                  |           |         | 10  | 5      |
| ImageNet subsets | ConvNetD5 | ~ 120MB | 1   | 1      |
|                  |           |         | 10  | 3      |

Long-range Dataset Distillation methods (LDD) generally have high computation overhead and storage requirements. Thus, we keep pre-computed trajectories from the previous method to save memory. Our overheads are roughly similar to the vanilla LDD method.

### A.6 Results Visualization

In this section, we present a selection of results obtained from diverse datasets. Notably, we have previously showcased the results for CIFAR-10 with  $IPC=1$ in the Introduction; therefore, for brevity, these results are not reiterated here. Primarily, we exhibit our findings on CIFAR-100 with  $IPC=1$  in Fig. [8.](#page-23-0) Subsequently, we demonstrate our results on the ImageNet subset, Imagenette, with IPC=1 in Fig. [9.](#page-23-1) Finally, we illustrate our outcomes on Tiny ImageNet with  $IPC=1$  in Fig. [10.](#page-24-0)

<span id="page-23-0"></span>Image /page/23/Picture/1 description: A grid of 100 small images, arranged in 10 rows and 10 columns. Each small image is a colorful depiction of an object or scene. The images include a red apple, a bicycle, a bottle, a bus, a chair, elephants, a fox, a house, a lamp, a motorcycle, a pear, a red car, a rose, a sunflower, a whale, and trees, among other subjects.

 ${\bf Fig. 8:}$  Results demonstration on CIFAR-100 with  ${\rm IPC}{=}1$ 

<span id="page-23-1"></span>Image /page/23/Picture/3 description: A grid of 12 images, each showing a different abstract pattern with a mix of colors and textures. The images appear to be generated or processed in some way, as they lack clear, recognizable objects and instead display swirling lines, blurred shapes, and fragmented elements. The overall impression is one of artistic experimentation or data visualization.

Fig. 9: Results demonstration on ImageNet subset imagenette with  $IPC=1$ 

<span id="page-24-0"></span>Image /page/24/Picture/1 description: The image displays a grid of 8x8 images, totaling 64 smaller images. Each smaller image appears to be a visualization of features learned by a neural network, possibly related to image recognition. The visualizations are abstract and colorful, with some resembling textures, patterns, or distorted representations of objects like animals, clothing, or landscapes. The overall impression is a mosaic of abstract art generated by artificial intelligence.

 ${\bf Fig. 10:}$  Partial results demonstration on Tiny ImageNet with  ${\rm IPC}{=}1$