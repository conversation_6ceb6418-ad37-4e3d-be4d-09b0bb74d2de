{"table_of_contents": [{"title": "Dataset Distillation by Matching Training Trajectories", "heading_level": null, "page_id": 0, "polygon": [[129.0, 105.0], [465.0, 105.0], [465.0, 119.3994140625], [129.0, 119.3994140625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.333984375, 430.5], [191.25, 430.5], [191.25, 441.6328125], [144.333984375, 441.6328125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 659.25], [127.5, 659.25], [127.5, 670.18359375], [48.75, 670.18359375]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 371.25], [393.0, 371.25], [393.0, 382.078125], [307.5, 382.078125]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[306.59765625, 301.5], [361.5, 301.5], [361.5, 313.62890625], [306.59765625, 313.62890625]]}, {"title": "3.1. <PERSON>pert Trajectories", "heading_level": null, "page_id": 2, "polygon": [[307.5, 528.0], [420.75, 528.0], [420.75, 538.69921875], [307.5, 538.69921875]]}, {"title": "Algorithm 1 Dataset Distillation via Trajectory Matching", "heading_level": null, "page_id": 3, "polygon": [[48.75, 74.25], [282.0, 74.25], [282.0, 83.48291015625], [48.75, 83.48291015625]]}, {"title": "3.2. Long-Range Parameter Matching", "heading_level": null, "page_id": 3, "polygon": [[48.0, 459.0], [228.0, 459.0], [228.0, 469.08984375], [48.0, 469.08984375]]}, {"title": "3.3. Memory Constraints", "heading_level": null, "page_id": 3, "polygon": [[307.5, 563.25], [426.0, 564.609375], [426.0, 574.5], [306.75, 573.890625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 438.75], [128.25, 438.75], [128.25, 450.140625], [48.75, 450.140625]]}, {"title": "4.1. Low-Resolution Data (32×32)", "heading_level": null, "page_id": 4, "polygon": [[307.5, 477.75], [468.0, 477.75], [468.0, 488.42578125], [307.5, 488.42578125]]}, {"title": "4.2. Short-Range vs. Long-Range Matching", "heading_level": null, "page_id": 5, "polygon": [[307.5, 444.0], [512.25, 444.0], [512.25, 454.39453125], [307.5, 454.39453125]]}, {"title": "4.3. Tiny ImageNet (64\\times64)", "heading_level": null, "page_id": 6, "polygon": [[48.0, 493.5], [180.0, 493.5], [180.0, 504.28125], [48.0, 504.28125]]}, {"title": "4.4. ImageNet Subsets (128×128)", "heading_level": null, "page_id": 6, "polygon": [[307.5, 261.75], [463.5, 261.75], [463.5, 272.056640625], [307.5, 272.056640625]]}, {"title": "5. Discussion and Limitations", "heading_level": null, "page_id": 7, "polygon": [[48.75, 525.9375], [200.25, 525.9375], [200.25, 537.5390625], [48.75, 537.5390625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.96630859375], [48.75, 83.96630859375]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 10, "polygon": [[48.0, 72.75], [114.75, 72.75], [114.75, 83.96630859375], [48.0, 83.96630859375]]}, {"title": "A.1. Additional Visualizations", "heading_level": null, "page_id": 10, "polygon": [[48.75, 92.25], [189.75, 92.25], [189.75, 103.25390625], [48.75, 103.25390625]]}, {"title": "A.2. Additional Quantitative Results", "heading_level": null, "page_id": 10, "polygon": [[48.0, 203.25], [220.5, 203.25], [220.5, 213.85546875], [48.0, 213.85546875]]}, {"title": "A.2.1 Additional Ablation Studies", "heading_level": null, "page_id": 10, "polygon": [[48.75, 610.5], [201.708984375, 610.5], [201.708984375, 621.0703125], [48.75, 621.0703125]]}, {"title": "A.3. Experiment Details", "heading_level": null, "page_id": 11, "polygon": [[48.0, 462.75], [163.5, 462.75], [163.5, 473.73046875], [48.0, 473.73046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 60], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 89], ["Text", 7], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 96], ["Text", 7], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 834], ["Line", 114], ["Text", 9], ["Reference", 8], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 908], ["Line", 99], ["TableCell", 56], ["Text", 9], ["TextInlineMath", 4], ["ListItem", 3], ["Reference", 3], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["TableCell", 143], ["Line", 95], ["Text", 9], ["Caption", 6], ["Reference", 5], ["Table", 3], ["TableGroup", 3], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 13858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 105], ["TableCell", 48], ["Text", 8], ["Reference", 7], ["Caption", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Figure", 1], ["Table", 1], ["Picture", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 81], ["Text", 6], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 120], ["ListItem", 38], ["Reference", 38], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 40], ["ListItem", 12], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 129], ["Text", 7], ["TableCell", 6], ["SectionHeader", 4], ["Reference", 4], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 2932, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["TableCell", 148], ["Line", 112], ["Text", 7], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 7613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["TableCell", 69], ["Line", 34], ["Caption", 4], ["Reference", 3], ["Text", 2], ["Table", 1], ["Figure", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3179, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 5], ["Picture", 4], ["Caption", 4], ["PictureGroup", 4], ["Reference", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 4], ["Line", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 583, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 615, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_by_Matching_Training_Trajectories_Cazenavette_et_al_2022"}