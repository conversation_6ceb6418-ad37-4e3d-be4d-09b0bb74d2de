{"table_of_contents": [{"title": "Dataset Distillation for Histopathology Image\nClassification\n1 Dataset Distillation for Histopathology Image<br>\n∴<br>\nClassification<br>\n\\frac{50}{2} Cong Cong<sup>1</sup>, <PERSON><PERSON><sup>2,3</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><br>\n<PERSON><sup>3</sup>, and <PERSON><sup>4</sup><br>\n∴<br>\n<sup>1</sup> Australian Institute of Hea", "heading_level": null, "page_id": 0, "polygon": [[49.5, 40.726318359375], [528.75, 39.75], [528.75, 168.0], [60.75, 171.75]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[81.87890625, 675.0], [205.5, 675.0], [205.5, 689.90625], [81.87890625, 689.90625]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 3, "polygon": [[82.5, 44.***********], [215.25, 44.***********], [215.25, 59.***********], [82.5, 59.***********]]}, {"title": "2.1 Coreset Selection", "heading_level": null, "page_id": 3, "polygon": [[82.4765625, 76.***********], [230.*********, 76.***********], [230.*********, 90.***********], [82.4765625, 90.***********]]}, {"title": "2.2 Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[81.75, 326.*********], [244.5, 326.*********], [244.5, 340.*********], [81.75, 340.*********]]}, {"title": "3 Methods", "heading_level": null, "page_id": 4, "polygon": [[81.75, 301.5], [176.25, 301.5], [176.25, 315.755859375], [81.75, 315.755859375]]}, {"title": "3.1 Synthetic Patch Learning", "heading_level": null, "page_id": 4, "polygon": [[82.5, 589.359375], [282.0, 588.0], [282.0, 601.734375], [82.5, 601.734375]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 7, "polygon": [[81.75, 42.97412109375], [205.5, 42.97412109375], [205.5, 60.56982421875], [81.75, 60.56982421875]]}, {"title": "4.1 Dataset Details", "heading_level": null, "page_id": 7, "polygon": [[81.75, 74.9267578125], [219.041015625, 74.9267578125], [219.041015625, 90.87890625], [81.75, 90.87890625]]}, {"title": "4.2 Experimental Setup", "heading_level": null, "page_id": 7, "polygon": [[82.5, 663.609375], [246.533203125, 663.609375], [246.533203125, 680.625], [82.5, 680.625]]}, {"title": "5 Results", "heading_level": null, "page_id": 8, "polygon": [[82.177734375, 597.0], [165.75, 597.0], [165.75, 611.015625], [82.177734375, 611.015625]]}, {"title": "5.1 Comparison to coreset selection methods", "heading_level": null, "page_id": 8, "polygon": [[82.40185546875, 633.0], [383.25, 633.0], [383.25, 645.046875], [82.40185546875, 645.046875]]}, {"title": "5.2 Ablation study", "heading_level": null, "page_id": 9, "polygon": [[82.3271484375, 635.37890625], [214.5, 635.37890625], [214.5, 649.30078125], [82.3271484375, 649.30078125]]}, {"title": "5.3 Synthetic patch evaluation", "heading_level": null, "page_id": 11, "polygon": [[82.10302734375, 144.24609375], [289.86328125, 142.69921875], [289.86328125, 157.78125], [82.10302734375, 159.328125]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[81.0, 541.40625], [192.75, 541.40625], [192.75, 558.421875], [81.0, 558.421875]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[81.0, 43.45751953125], [165.0, 43.45751953125], [165.0, 59.***********], [81.0, 59.***********]]}, {"title": "Supplementary Material", "heading_level": null, "page_id": 18, "polygon": [[195.75, 40.629638671875], [415.96875, 40.629638671875], [415.96875, 60.328125], [195.75, 60.328125]]}, {"title": "1 Dataset Details", "heading_level": null, "page_id": 18, "polygon": [[83.213623046875, 232.669677734375], [226.02003479003906, 232.669677734375], [226.02003479003906, 248.466796875], [83.213623046875, 248.466796875]]}, {"title": "2 Visualisation Results", "heading_level": null, "page_id": 18, "polygon": [[81.0, 585.0], [267.75, 585.0], [267.75, 600.57421875], [81.0, 600.57421875]]}, {"title": "3 Sensitivity study on hyper-parameters.", "heading_level": null, "page_id": 19, "polygon": [[81.0, 642.0], [404.25, 641.25], [404.25, 657.03515625], [81.0, 657.75]]}, {"title": "4 More ablations on the usage of stain normalisation.", "heading_level": null, "page_id": 20, "polygon": [[81.75, 177.75], [500.25, 177.75], [500.25, 192.2958984375], [81.75, 192.2958984375]]}, {"title": "5 Extra Use Case: Continual Learning", "heading_level": null, "page_id": 20, "polygon": [[82.3271484375, 474.75], [385.5, 474.75], [385.5, 489.5859375], [82.3271484375, 489.5859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 41], ["Text", 9], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 46], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 43], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 42], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 448], ["Line", 70], ["TableCell", 20], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 39], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 43], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 765], ["TableCell", 282], ["Line", 57], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13507, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 105], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 774, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 800], ["TableCell", 155], ["Line", 60], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3029, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 853], ["TableCell", 152], ["Line", 55], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["TableCell", 123], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 49], ["ListItem", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 51], ["ListItem", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 51], ["ListItem", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 34], ["ListItem", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["TableCell", 53], ["Line", 36], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 35], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1684, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["TableCell", 67], ["Line", 38], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 35], ["Line", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 734, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_for_Histopathology_Image_Classification"}