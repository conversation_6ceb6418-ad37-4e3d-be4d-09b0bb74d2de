{"table_of_contents": [{"title": "Dataset Distillation for Offline Reinforcement Learning", "heading_level": null, "page_id": 0, "polygon": [[108.7734375, 101.9970703125], [501.75, 101.9970703125], [501.75, 115.3388671875], [108.7734375, 115.3388671875]]}, {"title": "<PERSON>", "heading_level": null, "page_id": 0, "polygon": [[88.5, 135.0615234375], [177.0, 135.0615234375], [177.0, 145.3095703125], [88.5, 145.3095703125]]}, {"title": "<PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[89.25, 188.8154296875], [162.75, 188.8154296875], [162.75, 199.740234375], [89.25, 199.740234375]]}, {"title": "", "heading_level": null, "page_id": 0, "polygon": [[89.947265625, 244.40625], [138.955078125, 244.40625], [138.955078125, 255.234375], [89.947265625, 255.234375]]}, {"title": "Editor:", "heading_level": null, "page_id": 0, "polygon": [[88.5, 347.25], [125.25, 347.25], [125.25, 357.908203125], [88.5, 357.908203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[279.0, 381.0], [331.5, 381.0], [331.5, 392.90625], [279.0, 392.90625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[89.25, 513.0], [177.0, 513.0], [177.0, 524.390625], [89.25, 524.390625]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 1, "polygon": [[89.25, 539.25], [180.0, 539.25], [180.0, 551.07421875], [89.25, 551.07421875]]}, {"title": "2.1 Offline reinforcement learning problem setting", "heading_level": null, "page_id": 1, "polygon": [[89.12548828125, 605.6015625], [367.857421875, 605.6015625], [367.857421875, 616.4296875], [89.12548828125, 616.4296875]]}, {"title": "2.2 Behavioral cloning", "heading_level": null, "page_id": 2, "polygon": [[88.5, 457.5], [214.5, 457.5], [214.5, 467.9296875], [88.5, 467.9296875]]}, {"title": "2.3 Synthetic dataset", "heading_level": null, "page_id": 3, "polygon": [[89.05078125, 93.0], [208.5, 93.0], [208.5, 104.02734375], [89.05078125, 104.02734375]]}, {"title": "3 Experimental Setup", "heading_level": null, "page_id": 3, "polygon": [[89.2001953125, 642.75], [220.9833984375, 642.75], [220.9833984375, 654.328125], [89.2001953125, 654.328125]]}, {"title": "3.1 Environment", "heading_level": null, "page_id": 4, "polygon": [[89.25, 93.0], [183.75, 93.0], [183.75, 104.1240234375], [89.25, 104.1240234375]]}, {"title": "3.2 Model architectures and training", "heading_level": null, "page_id": 4, "polygon": [[89.25, 558.0], [294.345703125, 558.0], [294.345703125, 568.4765625], [89.25, 568.4765625]]}, {"title": "3.2.1 Model training", "heading_level": null, "page_id": 4, "polygon": [[89.12548828125, 578.25], [208.5, 578.25], [208.5, 588.97265625], [89.12548828125, 588.97265625]]}, {"title": "3.2.2 DATA CONSTRUCTION", "heading_level": null, "page_id": 5, "polygon": [[88.5, 593.61328125], [226.5, 593.61328125], [226.5, 604.44140625], [88.5, 604.44140625]]}, {"title": "4 Results", "heading_level": null, "page_id": 6, "polygon": [[89.25, 92.25], [146.25, 92.25], [146.25, 104.4140625], [89.25, 104.4140625]]}, {"title": "5 Related work", "heading_level": null, "page_id": 6, "polygon": [[89.2001953125, 626.25], [181.5, 626.25], [181.5, 636.5390625], [89.2001953125, 636.5390625]]}, {"title": "5.1 Deep Reinforcement Learning", "heading_level": null, "page_id": 6, "polygon": [[89.05078125, 648.0], [277.91015625, 648.0], [277.91015625, 658.58203125], [89.05078125, 658.58203125]]}, {"title": "Table 4: Dataset size used on various data collection methods with respect to\ndifferent environments.", "heading_level": null, "page_id": 7, "polygon": [[89.2001953125, 322.5], [522.75, 322.5], [522.75, 346.693359375], [89.2001953125, 346.693359375]]}, {"title": "5.2 Knowledge Distillation", "heading_level": null, "page_id": 7, "polygon": [[89.25, 462.515625], [238.5, 462.515625], [238.5, 474.1171875], [89.25, 474.1171875]]}, {"title": "5.3 Policy distillation", "heading_level": null, "page_id": 8, "polygon": [[89.25, 296.25], [208.7314453125, 296.25], [208.7314453125, 306.861328125], [89.25, 306.861328125]]}, {"title": "5.4 Dataset Distillation", "heading_level": null, "page_id": 8, "polygon": [[89.2001953125, 528.0], [220.5, 528.0], [220.5, 538.3125], [89.2001953125, 538.3125]]}, {"title": "5.5 Task Generalization", "heading_level": null, "page_id": 9, "polygon": [[88.5, 93.0], [222.0, 93.0], [222.0, 104.3173828125], [88.5, 104.3173828125]]}, {"title": "5.6 Other Works", "heading_level": null, "page_id": 9, "polygon": [[88.5, 273.75], [184.078125, 273.75], [184.078125, 284.818359375], [88.5, 284.818359375]]}, {"title": "6 Conclusion and limitations", "heading_level": null, "page_id": 9, "polygon": [[89.2001953125, 458.25], [260.25, 458.25], [260.25, 469.4765625], [89.2001953125, 469.4765625]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[89.25, 92.25], [153.0, 92.25], [153.0, 104.607421875], [89.25, 104.607421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 43], ["Text", 10], ["SectionHeader", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5394, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 43], ["Text", 5], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 689], ["Line", 71], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 56], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 33], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 808, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["TableCell", 48], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1173, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["TableCell", 58], ["Line", 42], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6373, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["TableCell", 79], ["Line", 54], ["Caption", 4], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 2712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 40], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 41], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 39], ["Reference", 12], ["ListItem", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 39], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 26], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_for_Offline_Reinforcement_Learning"}