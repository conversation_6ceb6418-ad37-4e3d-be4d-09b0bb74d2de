{"table_of_contents": [{"title": "Dataset Distillation from First Principles: Integrating Core\nInformation Extraction and Purposeful Learning", "heading_level": null, "page_id": 0, "polygon": [[123.75, 101.513671875], [488.8828125, 101.513671875], [488.8828125, 131.87109375], [123.75, 131.87109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 418.5], [329.25, 418.5], [329.25, 429.64453125], [282.75, 429.64453125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[88.97607421875, 92.25], [165.75, 92.25], [165.75, 104.220703125], [88.97607421875, 104.220703125]]}, {"title": "2 The Formal Dataset Distillation (DD) Optimization Problem", "heading_level": null, "page_id": 3, "polygon": [[88.5, 259.5], [408.0, 258.0], [408.0, 270.703125], [89.2001953125, 270.75]]}, {"title": "3 Inference Tasks as DD applications", "heading_level": null, "page_id": 6, "polygon": [[89.05078125, 151.5], [279.75, 151.5], [279.75, 162.7119140625], [89.05078125, 162.7119140625]]}, {"title": "4 Properties of the Optimization Problem", "heading_level": null, "page_id": 9, "polygon": [[89.25, 591.75], [303.75, 591.75], [303.75, 602.89453125], [89.25, 602.89453125]]}, {"title": "DD FIRST PRINCIPLES", "heading_level": null, "page_id": 10, "polygon": [[254.6015625, 39.75], [353.25, 39.75], [353.25, 49.9833984375], [254.6015625, 49.9833984375]]}, {"title": "5 Classical Methods", "heading_level": null, "page_id": 12, "polygon": [[88.5, 92.25], [194.25, 92.25], [194.25, 103.5439453125], [88.5, 103.5439453125]]}, {"title": "5.1 DD by Trajectory Matching", "heading_level": null, "page_id": 12, "polygon": [[88.5, 114.0], [240.0, 114.0], [240.0, 124.716796875], [88.5, 124.716796875]]}, {"title": "5.2 Other Methods", "heading_level": null, "page_id": 13, "polygon": [[89.05078125, 573.0], [181.**********, 573.0], [181.**********, 583.9453125], [89.05078125, 583.9453125]]}, {"title": "6 Mixture Extended AutoRegressive Model and an Invitation of DD for Categorical\nData", "heading_level": null, "page_id": 15, "polygon": [[89.25, 354.75], [516.0, 354.75], [516.0, 380.53125], [89.25, 380.53125]]}, {"title": "7 Case Study Description: DD to construct PGMs for Medical Models", "heading_level": null, "page_id": 16, "polygon": [[89.25, 524.25], [447.0, 524.25], [447.0, 535.21875], [89.25, 535.21875]]}, {"title": "Algorithm 1 DD Loss Evaluation", "heading_level": null, "page_id": 18, "polygon": [[89.25, 92.25], [239.0625, 92.25], [239.0625, 103.447265625], [89.25, 103.447265625]]}, {"title": "7.1 Experimental Setup", "heading_level": null, "page_id": 19, "polygon": [[88.5, 408.0], [203.25, 408.0], [203.25, 419.203125], [88.5, 419.203125]]}, {"title": "7.2 Numerical Results", "heading_level": null, "page_id": 21, "polygon": [[88.5, 417.0], [195.75, 417.0], [195.75, 427.32421875], [88.5, 427.32421875]]}, {"title": "8 Case Study Description: DD to Construct Boundary-Generalizable PINNs", "heading_level": null, "page_id": 22, "polygon": [[88.5, 356.25], [479.25, 356.25], [479.25, 368.349609375], [88.5, 368.349609375]]}, {"title": "8.1 Experimental Setup", "heading_level": null, "page_id": 22, "polygon": [[88.5, 559.5], [203.25, 559.5], [203.25, 570.0234375], [88.5, 570.0234375]]}, {"title": "8.2 Numerical Results", "heading_level": null, "page_id": 23, "polygon": [[88.5, 579.75], [195.75, 579.75], [195.75, 590.90625], [88.5, 590.90625]]}, {"title": "9 Conclusion", "heading_level": null, "page_id": 24, "polygon": [[88.5, 643.5], [158.25, 643.5], [158.25, 655.48828125], [88.5, 655.48828125]]}, {"title": "References", "heading_level": null, "page_id": 25, "polygon": [[89.25, 375.0], [146.25, 375.0], [146.25, 385.9453125], [89.25, 385.9453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 45], ["Text", 20], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 7837, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 46], ["Text", 6], ["ListItem", 4], ["ListGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 43], ["Text", 7], ["ListItem", 7], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 42], ["ListItem", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 517], ["Line", 62], ["TextInlineMath", 2], ["ListItem", 2], ["Text", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1157, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 63], ["Text", 5], ["ListItem", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["Line", 60], ["Text", 6], ["Equation", 4], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 68], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2422, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 63], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2315, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 374], ["Line", 67], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 54], ["Text", 6], ["TextInlineMath", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1000, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 779], ["Line", 117], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2540, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 130], ["Text", 10], ["Equation", 6], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2334, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 72], ["Text", 9], ["Equation", 6], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 44], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 57], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 89], ["Text", 5], ["Equation", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1294, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 355], ["Line", 41], ["TextInlineMath", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 55], ["TableCell", 15], ["Text", 9], ["Equation", 4], ["Reference", 4], ["ListItem", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1137, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 69], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 875, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Line", 107], ["Span", 58], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 52], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 68], ["Text", 5], ["Equation", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 3313, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["TableCell", 51], ["Line", 41], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 72], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 879, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 36], ["Reference", 8], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 638, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 38], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 38], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 37], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 38], ["Reference", 14], ["ListItem", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_from_First_Principles__Integrating_Core_Information_Extraction_and_Purposeful_L"}