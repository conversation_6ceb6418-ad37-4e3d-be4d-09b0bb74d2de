{"table_of_contents": [{"title": "Dataset Distillation in Latent Space", "heading_level": null, "page_id": 0, "polygon": [[187.5, 106.5], [406.40625, 106.5], [406.40625, 119.5927734375], [187.5, 119.5927734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.25927734375, 213.75], [191.25, 213.75], [191.25, 225.84375], [144.25927734375, 225.84375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 553.5], [128.12255859375, 553.5], [128.12255859375, 564.22265625], [48.75, 564.22265625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 438.5390625], [392.25, 438.5390625], [392.25, 450.140625], [307.5, 450.140625]]}, {"title": "3. Latent Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[48.75, 567.0], [197.25, 567.0], [197.25, 578.14453125], [48.75, 578.14453125]]}, {"title": "3.1. Problem Definition", "heading_level": null, "page_id": 2, "polygon": [[48.0, 586.5], [159.0, 586.5], [159.0, 597.48046875], [48.0, 597.48046875]]}, {"title": "3.2. From Pixel to Latent Space", "heading_level": null, "page_id": 2, "polygon": [[307.5, 287.25], [456.75, 287.25], [456.75, 297.580078125], [307.5, 297.580078125]]}, {"title": "3.3. Latent Dataset Distillation Algorithms", "heading_level": null, "page_id": 3, "polygon": [[306.896484375, 636.75], [507.75, 636.75], [507.75, 646.59375], [306.896484375, 646.59375]]}, {"title": "3.3.1 LatentDC", "heading_level": null, "page_id": 4, "polygon": [[48.0, 123.75], [123.0, 123.75], [123.0, 133.998046875], [48.0, 133.998046875]]}, {"title": "3.3.2 LatentDM", "heading_level": null, "page_id": 4, "polygon": [[307.5, 515.25], [384.0, 515.25], [384.0, 524.77734375], [307.5, 524.77734375]]}, {"title": "3.3.3 LatentMTT", "heading_level": null, "page_id": 5, "polygon": [[48.0, 195.0], [131.25, 195.0], [131.25, 205.34765625], [48.0, 205.34765625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 5, "polygon": [[48.75, 542.25], [128.25, 542.25], [128.25, 553.0078125], [48.75, 553.0078125]]}, {"title": "4.1. <PERSON> Settings", "heading_level": null, "page_id": 5, "polygon": [[48.75, 561.75], [174.0, 561.75], [174.0, 571.95703125], [48.75, 571.95703125]]}, {"title": "4.2. LatentDD vs. Baselines", "heading_level": null, "page_id": 5, "polygon": [[307.5, 620.25], [438.0, 620.25], [438.0, 630.73828125], [307.5, 630.73828125]]}, {"title": "4.3. Ablation Studies", "heading_level": null, "page_id": 6, "polygon": [[307.5, 672.75], [405.75, 672.75], [405.75, 682.55859375], [307.5, 682.55859375]]}, {"title": "5. Conclusions", "heading_level": null, "page_id": 7, "polygon": [[307.5, 574.5], [384.0, 574.5], [384.0, 586.265625], [307.5, 586.265625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.0, 163.5], [106.5, 163.5], [106.5, 174.216796875], [48.0, 174.216796875]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 10, "polygon": [[48.0, 71.20458984375], [110.25, 71.20458984375], [110.25, 84.73974609375], [48.0, 84.73974609375]]}, {"title": "A. Implementation Detail", "heading_level": null, "page_id": 10, "polygon": [[48.0, 93.75], [180.0, 93.75], [180.0, 105.4775390625], [48.0, 105.4775390625]]}, {"title": "A.1. Hyperparameters", "heading_level": null, "page_id": 10, "polygon": [[48.0, 275.25], [155.25, 275.25], [155.25, 286.365234375], [48.0, 286.365234375]]}, {"title": "A.2. Training/Evaluation Protocol", "heading_level": null, "page_id": 10, "polygon": [[48.75, 444.75], [208.5, 444.75], [208.5, 455.94140625], [48.75, 455.94140625]]}, {"title": "A.3. Analysis of Autoencoder", "heading_level": null, "page_id": 10, "polygon": [[307.5, 73.5], [444.75, 73.5], [444.75, 83.86962890625], [307.5, 83.86962890625]]}, {"title": "A.4. Dataset Selection and Preprocessing", "heading_level": null, "page_id": 10, "polygon": [[307.5, 433.125], [500.25, 433.125], [500.25, 443.1796875], [307.5, 443.1796875]]}, {"title": "A.5. Pseudocodes of LatentDD Algorithms", "heading_level": null, "page_id": 12, "polygon": [[48.0, 494.25], [249.0, 494.25], [249.0, 505.0546875], [48.0, 505.0546875]]}, {"title": "<PERSON>. Additional Results", "heading_level": null, "page_id": 12, "polygon": [[48.0, 568.5], [159.0, 568.5], [159.0, 580.46484375], [48.0, 580.46484375]]}, {"title": "B.1. Latent Codes Before vs. After Distillation", "heading_level": null, "page_id": 12, "polygon": [[307.5, 210.75], [525.0, 210.75], [525.0, 221.009765625], [307.5, 221.009765625]]}, {"title": "B.2. Cross-architecture Performance", "heading_level": null, "page_id": 12, "polygon": [[307.5, 367.5], [480.75, 367.5], [480.75, 377.82421875], [307.5, 377.82421875]]}, {"title": "B.3. Qualitative Results", "heading_level": null, "page_id": 12, "polygon": [[307.5, 609.08203125], [419.25, 609.08203125], [419.25, 619.13671875], [307.5, 619.13671875]]}, {"title": "Algorithm 2: LatentDM", "heading_level": null, "page_id": 13, "polygon": [[54.8349609375, 324.75], [155.25, 324.75], [155.25, 335.671875], [54.8349609375, 335.671875]]}, {"title": "Algorithm 3: LatentMTT", "heading_level": null, "page_id": 13, "polygon": [[54.498779296875, 481.5], [159.75, 481.5], [159.75, 491.1328125], [54.498779296875, 491.1328125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 84], ["Text", 7], ["SectionHeader", 3], ["Footnote", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 86], ["Text", 6], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 806, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 732], ["Line", 127], ["Text", 6], ["TextInlineMath", 4], ["SectionHeader", 3], ["Reference", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 573], ["Line", 113], ["Text", 5], ["TextInlineMath", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 700, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 722], ["Line", 141], ["TextInlineMath", 5], ["Text", 4], ["Reference", 4], ["Equation", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1833, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 681], ["Line", 157], ["Text", 7], ["Reference", 5], ["SectionHeader", 4], ["TextInlineMath", 4], ["Equation", 3], ["Footnote", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4415, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["TableCell", 280], ["Line", 86], ["Text", 4], ["Reference", 4], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["TableCell", 223], ["Line", 95], ["Text", 5], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Reference", 3], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["Line", 111], ["ListItem", 33], ["Reference", 32], ["ListGroup", 2], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 53], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 102], ["ListItem", 10], ["SectionHeader", 6], ["Text", 5], ["Reference", 5], ["TextInlineMath", 3], ["ListGroup", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["TableCell", 208], ["Line", 52], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["TableCell", 244], ["Line", 83], ["Reference", 7], ["Text", 6], ["SectionHeader", 5], ["ListItem", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1332], ["Line", 48], ["ListItem", 12], ["TextInlineMath", 9], ["Reference", 3], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 24], ["Line", 4], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 24], ["Line", 4], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 4], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_in_Latent_Space"}