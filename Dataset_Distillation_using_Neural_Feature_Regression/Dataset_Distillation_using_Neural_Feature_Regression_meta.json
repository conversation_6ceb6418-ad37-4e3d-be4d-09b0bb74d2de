{"table_of_contents": [{"title": "Dataset Distillation using Neural Feature Regression", "heading_level": null, "page_id": 0, "polygon": [[111.0, 99.75], [498.75, 99.75], [498.75, 116.8857421875], [111.0, 116.8857421875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 295.06640625], [329.25, 295.06640625], [329.25, 305.89453125], [282.75, 305.89453125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 516.0], [191.6982421875, 516.0], [191.6982421875, 526.5], [107.25, 526.5]]}, {"title": "Summary of Contributions:", "heading_level": null, "page_id": 1, "polygon": [[107.25, 570.0234375], [227.408203125, 570.0234375], [227.408203125, 580.078125], [107.25, 580.078125]]}, {"title": "2 Method", "heading_level": null, "page_id": 2, "polygon": [[107.25, 255.0], [166.5, 255.0], [166.5, 266.0625], [107.25, 266.0625]]}, {"title": "2.1 Dataset Distillation as Bi-level Optimization", "heading_level": null, "page_id": 2, "polygon": [[106.5, 279.0], [317.25, 279.0], [317.25, 288.87890625], [106.5, 288.87890625]]}, {"title": "2.2 Dataset Distillation using Neural Feature Regression with Pooling (FRePo)", "heading_level": null, "page_id": 2, "polygon": [[106.5, 544.5], [447.0, 544.5], [447.0, 554.5546875], [106.5, 554.5546875]]}, {"title": "1: while not converged do", "heading_level": null, "page_id": 3, "polygon": [[111.75, 124.5], [219.75, 124.5], [219.75, 133.611328125], [111.75, 133.611328125]]}, {"title": "8: end while", "heading_level": null, "page_id": 3, "polygon": [[110.267578125, 201.0], [166.2978515625, 201.0], [166.2978515625, 210.955078125], [110.267578125, 210.955078125]]}, {"title": "3 Related Work", "heading_level": null, "page_id": 4, "polygon": [[106.5, 186.0], [198.8701171875, 186.0], [198.8701171875, 198.0], [106.5, 198.0]]}, {"title": "4 Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[106.5, 505.44140625], [225.017578125, 505.44140625], [225.017578125, 517.04296875], [106.5, 517.04296875]]}, {"title": "4.1 Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[106.5, 531.73828125], [232.189453125, 531.73828125], [232.189453125, 541.79296875], [106.5, 541.79296875]]}, {"title": "4.2 Standard Benchmarks", "heading_level": null, "page_id": 5, "polygon": [[106.8310546875, 516.0], [228.7529296875, 516.0], [228.7529296875, 525.9375], [106.8310546875, 525.9375]]}, {"title": "4.3 ImageNet", "heading_level": null, "page_id": 7, "polygon": [[107.25, 298.16015625], [173.25, 298.16015625], [173.25, 308.6015625], [107.25, 308.6015625]]}, {"title": "5 Application", "heading_level": null, "page_id": 7, "polygon": [[107.25, 484.55859375], [186.0, 484.55859375], [186.0, 496.16015625], [107.25, 496.16015625]]}, {"title": "5.1 Continual Learning", "heading_level": null, "page_id": 7, "polygon": [[106.5, 510.46875], [216.650390625, 510.46875], [216.650390625, 521.296875], [106.5, 521.296875]]}, {"title": "5.2 Membership Inference Defense", "heading_level": null, "page_id": 8, "polygon": [[106.5, 491.90625], [264.75, 491.90625], [264.75, 501.9609375], [106.5, 501.9609375]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[107.20458984375, 199.93359375], [184.376953125, 199.93359375], [184.376953125, 211.1484375], [107.20458984375, 211.1484375]]}, {"title": "Acknowledgments and Disclosure of Funding", "heading_level": null, "page_id": 9, "polygon": [[107.25, 388.5], [340.5, 388.5], [340.5, 399.48046875], [107.25, 399.48046875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 506.25], [164.35546875, 506.25], [164.35546875, 517.4296875], [107.25, 517.4296875]]}, {"title": "A Experimental Details", "heading_level": null, "page_id": 16, "polygon": [[106.5, 71.8330078125], [237.568359375, 71.8330078125], [237.568359375, 84.3046875], [106.5, 84.3046875]]}, {"title": "A.1 Implementation Details", "heading_level": null, "page_id": 16, "polygon": [[106.98046875, 98.806640625], [233.982421875, 98.806640625], [233.982421875, 109.828125], [106.98046875, 109.828125]]}, {"title": "Algorithm 2 Dataset Distillation using Neural Feature Regression with Pooling (FRePo)", "heading_level": null, "page_id": 17, "polygon": [[107.25, 85.5], [463.482421875, 85.5], [463.482421875, 95.25], [107.25, 95.25]]}, {"title": "A.2 Experimental Setups", "heading_level": null, "page_id": 18, "polygon": [[106.5, 194.25], [223.224609375, 194.25], [223.224609375, 204.57421875], [106.5, 204.57421875]]}, {"title": "B Additional Results", "heading_level": null, "page_id": 19, "polygon": [[106.5, 456.328125], [224.419921875, 456.328125], [224.419921875, 468.703125], [106.5, 468.703125]]}, {"title": "C Ablation Study", "heading_level": null, "page_id": 19, "polygon": [[106.5, 662.44921875], [208.58203125, 662.44921875], [208.58203125, 674.82421875], [106.5, 674.82421875]]}, {"title": "C.1 FRePo vs TBPTT", "heading_level": null, "page_id": 20, "polygon": [[107.25, 466.76953125], [208.880859375, 466.76953125], [208.880859375, 477.59765625], [107.25, 477.59765625]]}, {"title": "C.2 Model pool and Batchsize", "heading_level": null, "page_id": 21, "polygon": [[106.5, 426.0], [243.0, 426.0], [243.0, 435.83203125], [106.5, 435.83203125]]}, {"title": "C.3 Initialization", "heading_level": null, "page_id": 21, "polygon": [[107.25, 613.5], [187.962890625, 613.5], [187.962890625, 623.390625], [107.25, 623.390625]]}, {"title": "C.4 Label Learning", "heading_level": null, "page_id": 23, "polygon": [[106.5, 594.75], [200.25, 594.75], [200.25, 606.375], [106.5, 606.375]]}, {"title": "C.5 Training Cost Analysis", "heading_level": null, "page_id": 24, "polygon": [[106.5, 423.75], [231.0, 423.75], [231.0, 435.05859375], [106.5, 435.05859375]]}, {"title": "C.6 Model Architectures", "heading_level": null, "page_id": 26, "polygon": [[107.1298828125, 73.3798828125], [221.5810546875, 73.3798828125], [221.5810546875, 83.91796875], [107.1298828125, 83.91796875]]}, {"title": "D Hyperparameter Tuning Guideline", "heading_level": null, "page_id": 32, "polygon": [[105.6357421875, 70.962890625], [308.98828125, 70.962890625], [308.98828125, 84.111328125], [105.6357421875, 84.111328125]]}, {"title": "E Additional Visualization", "heading_level": null, "page_id": 33, "polygon": [[105.75, 70.76953125], [253.5, 70.76953125], [253.5, 84.0146484375], [105.75, 84.0146484375]]}, {"title": "E.1 Distilled Image Visualization", "heading_level": null, "page_id": 33, "polygon": [[106.5, 95.08447265625], [255.75, 95.08447265625], [255.75, 107.2177734375], [106.5, 107.2177734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 49], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 58], ["Text", 5], ["ListItem", 3], ["Caption", 2], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 908, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 63], ["SectionHeader", 3], ["TextInlineMath", 3], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 796, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 645], ["Line", 63], ["TableCell", 8], ["ListItem", 7], ["Text", 6], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Equation", 2], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 941, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 51], ["Text", 7], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1174], ["TableCell", 186], ["Line", 154], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15375, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1022], ["Line", 100], ["TableCell", 100], ["Text", 3], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7223, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 50], ["TableCell", 24], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 506], ["Line", 103], ["TableCell", 49], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1071, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 49], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 52], ["ListItem", 13], ["Reference", 13], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 53], ["ListItem", 13], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 48], ["ListItem", 14], ["Reference", 13], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 55], ["ListItem", 10], ["Reference", 8], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 51], ["ListItem", 12], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 12], ["ListItem", 3], ["Reference", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 57], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 56], ["Text", 7], ["ListItem", 7], ["TextInlineMath", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 53], ["Text", 12], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["TableCell", 59], ["Line", 48], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11406, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["TableCell", 92], ["Line", 73], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12413, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 81], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["Text", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 143], ["Caption", 5], ["Figure", 3], ["FigureGroup", 3], ["Reference", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 52], ["Line", 16], ["Picture", 3], ["Caption", 3], ["Reference", 3], ["Text", 2], ["PictureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 951], ["TableCell", 224], ["Line", 52], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1974, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["TableCell", 230], ["Line", 59], ["Caption", 4], ["Reference", 4], ["Table", 3], ["TableGroup", 3], ["Figure", 1], ["<PERSON>Footer", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7425, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 32], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 8], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1369, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 8], ["Caption", 3], ["Picture", 2], ["PictureGroup", 2], ["Reference", 2], ["Figure", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1974, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 759], ["TableCell", 527], ["Line", 52], ["Text", 2], ["Caption", 2], ["Table", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 1910], ["TableCell", 290], ["Line", 80], ["Caption", 3], ["Table", 3], ["TableGroup", 3], ["Reference", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 12688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 907], ["TableCell", 136], ["Line", 48], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 48], ["ListItem", 12], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 50], ["Line", 22], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["Picture", 2], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1985, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 6], ["Picture", 4], ["Caption", 3], ["PictureGroup", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2593, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 11], ["Line", 4], ["Text", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 11], ["Line", 4], ["Figure", 2], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1329, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 8], ["Picture", 5], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 8, "llm_error_count": 0, "llm_tokens_used": 4840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 4], ["Caption", 2], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1246, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 7], ["Caption", 4], ["Picture", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_using_Neural_Feature_Regression"}