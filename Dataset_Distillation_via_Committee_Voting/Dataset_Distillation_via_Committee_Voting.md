<span id="page-0-1"></span><PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON> Bi<sup>2</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><PERSON><sup>1</sup> <sup>1</sup>VILA Lab, MBZUAI <sup>2</sup>University of Ottawa <sup>3</sup>Technical University of Denmark

{jiacheng.cui, zhaoyi.li, xiaochen.ma, zhiqiang.shen}@mbzuai.ac.ae <EMAIL>, <EMAIL>

# Abstract

*Dataset distillation aims to synthesize a smaller, representative dataset that preserves the essential properties of the original data, enabling efficient model training with reduced computational resources. Prior work has primarily focused on improving the alignment or matching process between original and synthetic data, or on enhancing the efficiency of distilling large datasets. In this work, we introduce* C*ommittee* V*oting for* D*ataset* D*istillation (CV-DD), a novel and orthogonal approach that leverages the collective wisdom of multiple models or experts to create high-quality distilled datasets. We start by showing how to establish a strong baseline that already achieves state-of-the-art accuracy through leveraging recent advancements and thoughtful adjustments in model design and optimization processes. By integrating distributions and predictions from a committee of models while generating high-quality soft labels*[1](#page-0-0) *, our method captures a wider spectrum of data features, reduces modelspecific biases and the adverse effects of distribution shifts, leading to significant improvements in generalization. This voting-based strategy not only promotes diversity and robustness within the distilled dataset but also significantly reduces overfitting, resulting in improved performance on post-eval tasks. Extensive experiments across various datasets and IPCs (images per class) demonstrate that Committee Voting leads to more reliable and adaptable distilled data compared to single/multi-model distillation methods, demonstrating its potential for efficient and accurate dataset distillation. Code is available at: [https://github.com/Jiacheng8/CV-DD.](https://github.com/Jiacheng8/CV-DD)*

## 1. Introduction

The rapid growth of large datasets has significantly advanced computer vision and deep learning applications, enabling models to achieve high accuracy and generalization across diverse domains. However, training on massive datasets

Image /page/0/Figure/8 description: The image displays a comparison of different neural network models on the ImageNet-1k dataset. At the top, three groups of figures represent ResNet18, ShuffleNetV2, and MobileNetV2. ResNet18 is associated with a speech bubble stating "I am better on CIFAR100!". ShuffleNetV2 has a speech bubble saying "I am better on ImageNet-1k!". MobileNetV2 is depicted with a larger group and a speech bubble stating "We can assist you for generalization!". Below this, a bar chart illustrates the Top-1 Accuracy (%) for two methods, CV-DD (ours) and RDED (SoTA), across different IPC (Image Per Class) values on ImageNet-1k. For an IPC of 50, CV-DD achieves 59.5% accuracy, while RDED achieves 56.5%. For an IPC of 10, CV-DD reaches 46.0% accuracy, and RDED reaches 42.0%. For an IPC of 1, CV-DD has 9.2% accuracy, and RDED has 6.6% accuracy.

Figure 1. Top illustrates the motivation of our committee votingbased dataset distillation, highlighting its ability to reduce bias from individual model knowledge. Bottom shows the performance improvement over previous state-of-the-art method RDED [\[28\]](#page-9-0).

presents challenges such as high computational cost, memory usage, and long training times, especially for resourceconstrained environments. To address these issues, *dataset distillation* has emerged as an effective technique to condense large datasets into smaller, representative sets, allowing for efficient model training with minimal performance loss. Despite its promise, a key challenge in dataset distillation remains: *capturing the essential features of the original data while avoiding overfitting to specific patterns or noise.*

Prior dataset distillation methods [\[28,](#page-9-0) [31,](#page-9-1) [35,](#page-9-2) [36\]](#page-9-3) often rely on single-model frameworks that may struggle to generalize across complex, diverse datasets and architectures. These approaches can introduce biases specific to the model used, resulting in distilled datasets that may not fully capture the richness of the original data. To overcome these limitations, we propose Committee Voting for Dataset Distillation (CV-DD), a framework that leverages multiple models' perspectives to create a high-quality, balanced distilled dataset. Our first contribution in this work is to identify pitfalls, dis-

<span id="page-0-0"></span><sup>1</sup>Our high-quality soft labels are generated by enabling the *train mode* for teacher model during post-eval, which improves accuracy significantly.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This figure outlines the process of CV-DD, starting with Data Initialization to generate synthetic data from original data. The process includes a Voting Strategy with two voters and a committee of five models (ResNet18, ResNet50, ShuffleNetV2, MobileNetV2, DenseNet121). It also details Batch-Specific Soft Labeling, involving convolution, embeddings, and Batch Normalization on small batches, with formulas for mean and variance. Finally, it shows a Smoothed Learning Rate mechanism with a neural network and learning rate schedules, including a formula for the learning rate based on steps and epochs.

Figure 2. Overview of CV-DD. The process begins with Data Initialization to generate synthetic data from the original data distribution. In Voting Strategy section, a committee of models collectively decides on the distributions for synthetic data, where the voting mechanism considers prior performance and calculates a weighted gradient update based on each model's distribution and prediction. Batch-Specific Soft Labeling generates soft labels tailored to small batch sizes by embedding batch norm statistics from synthetic data batch. Finally, a **Smoothed Learning Rate** strategy is applied to the post-training process, adjusting dynamically with a cosine schedule to stabilize training.

enchant design choices in recent advances on dataset distillation and train an exceptionally strong baseline framework which already achieves state-of-the-art performance. By using a committee of models with different architectures and training strategies, CV-DD further enables the capture of a more comprehensive features, enhancing the robustness and adaptability of the distilled dataset with better accuracy.

Specifically, the proposed CV-DD framework introduces a Prior Performance Guided (PPG) Voting Mechanism, which aggregates distributions and predictions from multiple models to identify representative data points. This approach reduces model-specific biases, promotes diversity in the distilled dataset, and mitigates overfitting by leveraging the unique strengths of each model. Additionally, CV-DD enables fine-grained control over the distillation process through our dynamic voting design, where model weights and voting thresholds can be adjusted to prioritize specific features or dataset attributes. We further propose a Batch-Specific Soft Labeling (BSSL) to mitigate the distribution shift between the original dataset and the synthetic data for better post-evaluation performance.

Through extensive experiments on benchmark datasets of CIFAR, Tiny-ImageNet, ImageNet-1K and its subsets, we demonstrate that CV-DD achieves significant improvements over traditional single/multi-model distillation methods in both accuracy and cross-model generalization. Our results show that datasets distilled for committee voting consistently

yield better performance on post-eval tasks, even in low-data or limited-compute scenarios. By harnessing the collective knowledge of multiple models, CV-DD provides a robust solution for dataset distillation, highlighting its potential for applications where efficient data usage and computational efficiency are essential.

We make the following contributions in this paper:

- We propose a novel framework, *Committee Voting for Dataset Distillation* (CV-DD), which integrates multiple model perspectives to synthesize a distilled dataset that encapsulates rich features and produces high-quality soft labels by batch-specific normalization.
- By integrating recent advancements, refining framework design and optimization techniques, we establish a strong baseline within CV-DD framework that already achieves state-of-the-art performance in dataset distillation.
- Through experiments across multiple datasets, we demonstrate that CV-DD improves generalization, mitigates overfitting, and outperforms prior methods in various datalimited scenarios, highlighting its effectiveness as a scalable and reliable solution for dataset distillation.

## 2. Related Work

Dataset Distillation. Dataset distillation aims to generate a compact, synthetic dataset that retains essential information from a large dataset. This approach facilitates easier <span id="page-2-0"></span>data processing, reduces training time, and achieves performance comparable to training with the full dataset. Existing solutions typically fall into five main categories: 1) *Meta-Model Matching*: This method optimizes for model transferability on distilled data, involving an outer loop for updating synthetic data and an inner loop for training the network. Examples include DD [\[31\]](#page-9-1), KIP [\[19\]](#page-8-0), RFAD [\[17\]](#page-8-1), FRePo [\[42\]](#page-9-4), LinBa [\[5\]](#page-8-2), and MDC [\[9\]](#page-8-3). 2) *Gradient Matching*: This approach performs one-step distance matching between models, focusing on aligning gradients. Methods in this category include DC [\[40\]](#page-9-5), DSA [\[38\]](#page-9-6), DCC [\[15\]](#page-8-4), IDC [\[13\]](#page-8-5), and MP [\[41\]](#page-9-7). 3) *Distribution Matching*: Here, the distribution of original and synthetic data is directly matched through a single-level optimization. Approaches include DM [\[39\]](#page-9-8), CAFE [\[30\]](#page-9-9), HaBa [\[16\]](#page-8-6), KFS [\[15\]](#page-8-4), DataDAM [\[22\]](#page-8-7), FreD [\[27\]](#page-9-10), and GUARD [\[33\]](#page-9-11). 4) *Trajectory Matching*: This method matches the weight trajectories of models trained on original and synthetic data over multiple steps. Examples include MTT [\[1\]](#page-8-8), TESLA [\[3\]](#page-8-9), APM [\[2\]](#page-8-10), and DATM [\[7\]](#page-8-11). 5) *Decoupled Optimization with BatchNorm Matching*: SRe<sup>2</sup>L [\[36\]](#page-9-3) first proposes to decouple the model training and data synthesis for dataset distillation. After that, many decoupled methods have been proposed, such as G-VBSM [\[24\]](#page-9-12), EDC [\[25\]](#page-9-13), CDA [\[35\]](#page-9-2) and LPLD [\[32\]](#page-9-14).

Ensemble Multi-Model Dataset Distillation. Ensemble multi-model strategies in dataset distillation seek to harness the strengths of multiple models to improve the quality and generalization of distilled datasets. While most dataset distillation approaches rely on a single model, only two prior methods have explored the use of multi-model ensembles: MTT series  $[1, 3, 6]$  $[1, 3, 6]$  $[1, 3, 6]$  $[1, 3, 6]$  $[1, 3, 6]$  and G-VBSM  $[24]$ . MTT leverages a collection of independently trained teacher models on the real dataset, saving their snapshot parameters at each epoch to generate expert trajectories that guide the distillation process. G-VBSM uses a diverse set of local-to-global matching signals derived from multiple backbones and statistical metrics, enabling more precise and effective matching compared to single-model approaches. However, as the diversity of matching models increases, the framework's overall complexity also grows, which can reduce its efficiency. Both MTT and G-VBSM rely on static ensemble configurations and lack adaptive weighting mechanisms to dynamically adjust each model's contribution. Our proposed approach, *Committee Voting*, addresses these limitations by introducing an adaptive voting system that adjusts model weights based on their prior performance, resulting in a more refined and effective distilled dataset with much better performance.

### 3. Approach

### 3.1. Preliminaries

The goal of dataset distillation is to create a compact synthetic dataset that retains essential information from

the original dataset. Given a large labeled dataset  $\mathcal{D} =$  $\{(u_1, v_1), \ldots, (u_{|\mathcal{D}|}, v_{|\mathcal{D}|})\}$ , we aim to learn a smaller synthetic dataset  $\mathcal{D}_{syn} = \{(\hat{u}_1, \hat{v}_1), \dots, (\hat{u}_{|\mathcal{D}_{syn}|}, \hat{v}_{|\mathcal{D}_{syn}|})\}$ , where  $|\mathcal{D}_{syn}| \ll |\mathcal{D}|$ . The objective is to minimize the performance gap between models trained on  $\mathcal{D}_{syn}$  and those trained on  $\mathcal{D}$ :

$$
\sup_{(u,v)\sim\mathcal{D}}\left|\mathcal{L}\left(f_{\psi_{\mathcal{D}}}(u),v\right)-\mathcal{L}\left(f_{\psi_{\mathcal{D}_{syn}}}(u),v\right)\right|\leq\delta,\quad(1)
$$

where  $\delta$  is the allowable gap. This leads to the following optimization problem:

$$
\underset{\mathcal{D}_{syn,}|\mathcal{D}_{syn}|}{\arg \min} \underset{(u,v)\sim\mathcal{D}}{\sup} \left| \mathcal{L}\left(f_{\psi_{\mathcal{D}}}(u),v\right) - \mathcal{L}\left(f_{\psi_{\mathcal{D}_{syn}}}(u),v\right) \right| \tag{2}
$$

The goal is to synthesize  $\mathcal{D}_{syn}$  while determining the optimal number of samples per class.

### 3.2. Pitfalls of Latest Methods

Diversity and bias issues. SRe<sup>2</sup>L [\[36\]](#page-9-3) is a recently proposed optimization-based method that generates distilled data by aligning the Batch Normalization (BN) statistics of synthetic data with those from the training process while simultaneously ensuring the alignment between synthetic data labels and their true labels. The primary limitation of this method is its reliance on a single backbone network for generating distilled data, resulting in limited diversity and increased model-specific bias.

Informativeness and realistic issues. Prior ensemble-based dataset distillation methods, such as G-VBSM [\[24\]](#page-9-12) and MTT [\[1\]](#page-8-8), utilize multiple backbones to generate distilled data. However, these methods assume that all pre-trained models contribute equally to the recovery of synthetic data, failing to prioritize the contributions of more informative models during optimization. Moreover, both MTT and G-VBSM encounter efficiency challenges: MTT matches the training trajectories of multiple models, rendering it highly inefficient and incapable of scaling to large datasets. In contrast, while G-VBSM is scalable to large datasets, it incurs substantial computational overhead due to the additional alignment of convolutional statistics.

Suboptimal soft labels. Prior generative dataset distillation methods [\[1,](#page-8-8) [24,](#page-9-12) [36\]](#page-9-3) have overlooked the distributional shift between synthetic and original images, a critical factor that influences the fidelity and representativeness of the distilled data. This oversight has led to the generation of suboptimal soft labels, ultimately resulting in a significant reduction in generalization capability.

## 3.3. Overview of CV-DD

The overall framework of our proposed CV-DD is illustrated in Fig. [2.](#page-1-0) Essentially, CV-DD builds upon the enhanced baseline and employs a Prior Performance Guided Voting Strategy during the optimization of synthetic data, addressing the limitation of previous ensemble-based method, which

<span id="page-3-3"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: A line graph displays the test accuracy (%) of two models, SRe2L++ (purple line with circles) and SRe2L (pink dashed line with squares), across five datasets: CIFAR-10, CIFAR-100, Tiny-Imagenet, ImageNette, and ImageNet-1k. The y-axis ranges from 0 to 70, representing test accuracy. The x-axis lists the datasets. For CIFAR-10, SRe2L++ has approximately 43% accuracy and SRe2L has approximately 30%, with a +14% difference. For CIFAR-100, SRe2L++ has approximately 52% accuracy and SRe2L has approximately 26%, with a +25% difference. For Tiny-Imagenet, SRe2L++ has approximately 46% accuracy and SRe2L has approximately 16%, with a +31% difference. For ImageNette, SRe2L++ has approximately 63% accuracy and SRe2L has approximately 30%, with a +33% difference. For ImageNet-1k, SRe2L++ has approximately 43% accuracy and SRe2L has approximately 22%, with a +22% difference. The graph highlights the performance improvement of SRe2L++ over SRe2L on each dataset.

Figure 3. Performance comparison between the original SRe<sup>2</sup>L and the enhanced  $SRe^2L++$  baseline across five datasets with IPC=10 during the post-evaluation stage.

assigns equal importance to all models in the dataset distillation process. Moreover, CV-DD eliminates the reliance on running statistics for feature normalization during the soft label generation process, ensuring higher-quality soft labels and improved generalization performance for post-eval task.

### 3.4. Building a Strong Baseline

Many dataset distillation methods use  $SRe<sup>2</sup>L$  [\[36\]](#page-9-3) as a baseline for performance comparison [\[24,](#page-9-12) [25,](#page-9-13) [28\]](#page-9-0). However, due to suboptimal design and insufficient hyper-parameter tuning in post-evaluation, some methods appear to surpass  $SRe<sup>2</sup>L$  without truly outperforming it. This subsection introduces  $SRe<sup>2</sup>L++$ , a more robust baseline that achieves stateof-the-art performance. The performance improvements of  $SRe<sup>2</sup>L++ over SRe<sup>2</sup>L are illustrated in Fig. 3.$  $SRe<sup>2</sup>L++ over SRe<sup>2</sup>L are illustrated in Fig. 3.$ 

Real Image Initialization: The original SRe<sup>2</sup>L method uses Gaussian noise for initialization during the recover stage. However, EDC [\[25\]](#page-9-13) shows that initializing with real data improves quality at the same optimization cost. Thus,  $SRe<sup>2</sup>L$ is enhanced by replacing Gaussian noise initialization with real image patches generated by RDED [\[28\]](#page-9-0).

Data Augmentation for Small Datasets: The original SRe<sup>2</sup>L omitted data augmentation (e.g., random cropping, resizing, flipping) during recovery on small-resolution datasets (e.g., CIFAR-10, CIFAR-100), limiting performance. This has been addressed by incorporating data augmentation, with its impact shown in the Fig. [7.](#page-7-0)

Batch-Specific Soft Labeling: To further enhance the performance of  $SRe<sup>2</sup>L$ , we apply the proposed Batch-Specific Soft Labeling technique, which will be elaborated in a later subsection [3.7.](#page-4-0)

Smoothed Learning Rate and Smaller Batch Size: Prior studies [\[25,](#page-9-13) [28,](#page-9-0) [35\]](#page-9-2) suggest reducing batch size to increase the number of iterations per epoch, thereby mitigating underconvergence, and adopting a smoothed learning rate scheduler to avoid convergence to suboptimal minima.

<span id="page-3-1"></span>Image /page/3/Figure/9 description: This is a line graph titled "Intra-class Cosine Similarity Comparison". The x-axis is labeled "Class Index" and ranges from 0 to 1000. The y-axis is labeled "Intra-class Cosine Similarity" and ranges from 0.65 to 0.90. Two sets of data are plotted: "SRe2L++" and "CV-DD". Both datasets have a raw line and a smoothed line. The "SRe2L++" data, represented by a blue dashed line, fluctuates around 0.82. The "CV-DD" data, represented by a red dashed line, fluctuates around 0.76. The raw data for both sets appears as a series of vertical lines around their respective smoothed lines.

Figure 4. Illustration of the average cosine similarity (lower is the better) between feature embeddings of pairwise samples within the same class on ImageNet-1K with IPC=10.

### 3.5. Committee Choices

Inspired by ensemble-based dataset distillation methods like MTT [\[1\]](#page-8-8), FTD [\[6\]](#page-8-12), and G-VBSM [\[24\]](#page-9-12), which utilize multiple backbones to enhance performance, CV-DD incorporates a diverse set of five backbones: DenseNet121 [\[11\]](#page-8-13), ResNet18 [\[8\]](#page-8-14), ResNet50 [\[8\]](#page-8-14), MobileNetV2 [\[23\]](#page-9-15), and ShuffleNetV2 [\[37\]](#page-9-16). This mix of lightweight and standard architectures improves the diversity and generalization of distilled data. Specifically, CV-DD retains the same backbone throughout the optimization process and switches to different backbones only when generating new synthetic data, i.e., an approach we refer to as "Switch Per IPC". This strategy, as illustrated in Fig. [2,](#page-1-0) ensures that each distilled dataset is optimized under consistent backbones, thereby promoting a more stable learning process. By utilizing diverse backbones, CV-DD improves the diversity of distilled data. As shown in Fig. [4,](#page-3-1) CV-DD consistently surpasses  $SRe<sup>2</sup>L++$  in data diversity across various classes.

## 3.6. Committees Voting Strategy

To address the limitation of previous Ensemble Based Method, we propose a *Prior Performance Guided Voting Strategy* that ensures the models with stronger prior performance exert a greater influence on the optimization process. This subsection details the computation of prior performance scores and illustrates how CV-DD effectively utilizes them to optimize the distilled data.

Prior Performance Assignment: Given a model optimized using the specified loss function in Equation [3,](#page-3-2) where  $\mathcal T$ represents the Dataset and  $p_{\theta}(x)$  denotes the predicted probability distribution from the model parameterized by  $\theta$ .

<span id="page-3-2"></span>
$$
\theta_T = \arg\min_{\theta} \mathbb{E}_{(x,y)\sim\mathcal{T}} \left[ -\sum_{i=1}^C y_i \log \left( p_\theta(x)_i \right) \right] \quad (3)
$$

We leverage the information embedded in the pre-trained model to generate distilled data, with the quality of the dis<span id="page-4-7"></span>tilled data being directly proportional to the critical information contained within the model. During post-evaluation, the generalization performance of models trained on distilled data reflects its quality and quantifies the information in the pre-trained models. Thus, each model's prior performance can be represented by the generalization ability of models trained on the distilled datasets each model generates.

Voting Strategy: First, let us define some notations to facilitate the subsequent explanation. We denote the set of backbone candidates as S, and a randomly selected subset of N indices as  $\mathcal{I}_N$ , as defined in Equation [4:](#page-4-1)

<span id="page-4-1"></span>
$$
\mathcal{I}_N \subset \{1, \ldots, |S|\}, \quad \text{where} \quad 2 \le N \le |S|. \tag{4}
$$

The *i*-th indices of  $\mathcal{I}_N$  are denoted by  $\mathcal{I}_N^i$ . For each sampled index *i*, the prior performance is represented as  $\alpha^{\mathcal{I}_{N}^{i}}$ , and the corresponding backbone is denoted as  $S^{\mathcal{I}_N^i}$ . Then the prior voting loss function used to optimize the synthesized data at a specific iteration is defined as follows:

$$
\mathcal{L}(\tilde{\mathbf{x}}) = \sum_{i=1}^{N} \frac{\exp(\alpha^{\mathcal{I}_{N}^{i}}/T)}{\sum_{j=1}^{N} \exp(\alpha^{\mathcal{I}_{N}^{j}}/T)} \mathcal{L}_{S^{\mathcal{I}_{N}^{i}}}(\tilde{\mathbf{x}})
$$
(5)

where  $\mathcal{L}_{S^{T_i}}(\tilde{\mathbf{x}})$  denotes the loss of the synthetic data evaluated on the selected i-th pre-trained backbone model. As shown in Equation [5,](#page-4-2) CV-DD uses the SoftMax function to assign weights to each model's loss based on prior performance, with  $T$  controlling sensitivity to performance differences. Essentially, the better a model's prior performance, the higher its corresponding weight, thereby assigning greater significance to its loss in the overall optimization [\[12\]](#page-8-15). After rigorous testing in later sections, we found that the is the optimal value as illustrated in Section [4.5.](#page-6-0)

### <span id="page-4-0"></span>3.7. Batch-Specific Soft Labeling

<span id="page-4-6"></span>Algorithm 1 Batch-Specific Soft Labeling

**Require:** Teacher model  $T$ , Distilled image  $I$ 

1:  $T \cdot \text{train}() \geq \text{Enable training mode for the teacher model}$ 2: soft\_labels  $\leftarrow T(I)$   $\triangleright$  Generate soft labels using the

teacher model 3: return soft labels

In the post-evaluation stage, a teacher model is commonly employed to pre-generate soft labels [\[26\]](#page-9-17), thereby enhancing the generalization of the student model [\[10,](#page-8-16) [18\]](#page-8-17).

Typically, the teacher model includes Batch Normalization layers [\[20,](#page-8-18) [24,](#page-9-12) [28,](#page-9-0) [36\]](#page-9-3), which utilize running statistics to normalize features. These statistics are progressively updated during training, as detailed in Equations [6](#page-4-3) and [7.](#page-4-4)

<span id="page-4-3"></span>
$$
\mu_{\text{running}} \leftarrow \alpha \,\mu_{\text{running}} + (1 - \alpha) \,\mu_B \tag{6}
$$

<span id="page-4-4"></span>
$$
\sigma_{\text{running}}^2 \leftarrow \alpha \sigma_{\text{running}}^2 + (1 - \alpha) \sigma_B^2 \tag{7}
$$

<span id="page-4-5"></span>Image /page/4/Figure/16 description: This image contains four scatter plots arranged in a 2x2 grid. The top row displays plots for Layer 1, and the bottom row displays plots for Layer 15. The left column shows mean values, and the right column shows variances. All plots have the x-axis labeled "Index of batch (Iteration)" ranging from 0 to 3000. The top-left plot, "Mean Values, Layer 1", shows values ranging from -1.2 to -0.8, with two sets of data points: blue points labeled "Original Dataset" clustered around -1.0, and red points labeled "SRe2L++" clustered around -0.9. The top-right plot, "Variances, Layer 1", shows values ranging from 1.0 to 2.5, with blue points "Original Dataset" clustered around 1.5, and red points "SRe2L++" scattered between 1.5 and 2.5. The bottom-left plot, "Mean Values, Layer 15", shows values ranging from -0.50 to -0.35, with blue points "Original Dataset" clustered around -0.42, and red points "SRe2L++" scattered between -0.40 and -0.35. The bottom-right plot, "Variances, Layer 15", shows values ranging from 0.15 to 0.30, with blue points "Original Dataset" clustered around 0.20, and red points "SRe2L++" scattered between 0.15 and 0.25.

Figure 5. Feature-level statistical discrepancies between synthetic data generated by  $SRe^2L++$  and the training data on ImageNet-1K, evaluated across different batches in a pre-trained ResNet18 model.

<span id="page-4-2"></span>where  $\alpha$  is the momentum, and  $\mu_B$ ,  $\sigma_B^2$  are the mean and variance of the current batch, respectively.

However, as shown in Fig. [5,](#page-4-5) we observe that even if the generated images match the BN distribution during synthesis, there is still a significant gap between the BN distribution of the synthetic images and that of the original dataset, due to the influence of regularization terms and optimization randomness.

To address this, we propose *Batch-Specific Soft Labeling* (BSSL): Instead of using pre-trained BN statistics from the original images in a real dataset, we recompute the BN statistics directly from each batch of synthetic images, keeping all other parameters frozen with the teacher's original pre-trained values each time soft labels are generated. Algorithm [1](#page-4-6) presents a simple implementation of BSSL. In the post-evaluation phase, this method generates soft labels by setting the teacher model to training mode. This straightforward adjustment significantly improves the performance of the model during post-training on synthetic data using these soft labels. Specifically, for a given distilled data batch  $B = \{x_i \mid i = 1, 2, \dots, N\}$ , where each  $x_i$  represents a sample in the batch and is a vector of features, represented as  $x_i \in \mathbb{R}^{C \times H \times W}$ , where C is the number of channels, and  $H$  and  $W$  are the height and width, respectively. For BN layer, the mean and variance are calculated per channel as:

$$
\mu_{B,c} = \frac{1}{N \times H \times W} \sum_{i=1}^{N} \sum_{h=1}^{H} \sum_{w=1}^{W} x_{i,c,h,w}
$$
 (8)

$$
\sigma_{B,c}^2 = \frac{1}{N \times H \times W} \sum_{i=1}^N \sum_{h=1}^H \sum_{w=1}^W (x_{i,c,h,w} - \mu_{B,c})^2 + \epsilon
$$
\n(9)

where  $\mu_{B,c}$  and  $\sigma_{B,c}^2$  are the batch-specific mean and variance for each channel c. Here,  $\epsilon$  is a small constant added

<span id="page-5-1"></span><span id="page-5-0"></span>

| Dataset       | IPC | ResNet18 |             |         |             | ResNet50    |           |         |             | ResNet101   |           |         |             |
|---------------|-----|----------|-------------|---------|-------------|-------------|-----------|---------|-------------|-------------|-----------|---------|-------------|
|               |     | CDA [35] | RDED [28]   | SRe²L++ | CV-DD       | CDA [35]    | RDED [28] | SRe²L++ | CV-DD       | CDA [35]    | RDED [28] | SRe²L++ | CV-DD       |
| CIFAR10       | 1   | -        | 22.9        | 24.4    | <b>26.1</b> | -           | 10.2      | 22.8    | 25.7        | -           | 18.7      | 22.4    | 24.2        |
|               | 10  | -        | 37.1        | 42.5    | <b>44.6</b> | -           | 33.1      | 42.3    | 44.7        | -           | 33.7      | 33.4    | <b>39.6</b> |
|               | 50  | -        | 62.1        | 63.0    | <b>70.1</b> | -           | 54.2      | 60.9    | <b>69.6</b> | -           | 51.6      | 61.0    | <b>69.3</b> |
| CIFAR-100     | 1   | 13.4     | 11.0        | 13.5    | <b>17.2</b> | -           | 10.9      | 12.7    | <b>16.0</b> | -           | 10.8      | 11.2    | <b>14.8</b> |
|               | 10  | 49.8     | 42.6        | 52.1    | <b>54.3</b> | -           | 41.6      | 46.1    | <b>49.5</b> | -           | 41.1      | 50.6    | <b>52.5</b> |
|               | 50  | 64.4     | 62.6        | 64.0    | <b>65.1</b> | -           | 64.0      | 64.1    | <b>64.8</b> | -           | 63.4      | 65.6    | <b>66.3</b> |
| Tiny-ImageNet | 1   | 3.3      | 9.7         | 9.3     | <b>10.1</b> | -           | 8.2       | 7.6     | <b>8.5</b>  | -           | 3.8       | 7.5     | <b>9.1</b>  |
|               | 10  | 43.0     | 41.9        | 46.5    | <b>47.8</b> | -           | 38.4      | 42.8    | <b>43.8</b> | -           | 22.9      | 45.4    | <b>47.4</b> |
|               | 50  | 48.7     | <b>58.2</b> | 53.5    | 54.1        | 49.7        | 45.6      | 53.7    | <b>54.7</b> | <b>50.6</b> | 41.2      | 53.7    | <b>54.1</b> |
| ImageNette    | 1   | -        | 35.8        | 29.7    | <b>36.2</b> | -           | 27.0      | 25.9    | <b>27.6</b> | -           | 25.1      | 22.1    | <b>25.3</b> |
|               | 10  | -        | 61.4        | 62.4    | <b>64.1</b> | -           | 55.0      | 57.4    | <b>61.4</b> | -           | 54.0      | 55.1    | <b>61.0</b> |
|               | 50  | -        | 80.4        | 80.3    | <b>81.6</b> | -           | 81.8      | 80.3    | <b>82.0</b> | -           | 75.0      | 77.9    | <b>80.0</b> |
| ImageNet-1k   | 1   | -        | 6.6         | 8.6     | <b>9.2</b>  | -           | 8.0       | 8.0     | <b>10.0</b> | -           | 5.9       | 6.2     | <b>7.0</b>  |
|               | 10  | 33.6     | 42.0        | 43.1    | <b>46.0</b> | -           | 49.7      | 47.3    | <b>51.3</b> | -           | 48.3      | 51.2    | <b>51.7</b> |
|               | 50  | 53.5     | 56.5        | 57.6    | <b>59.5</b> | <b>61.3</b> | 62.0      | 61.8    | <b>63.9</b> | <b>61.6</b> | 61.2      | 61.0    | <b>62.7</b> |

Table 1. Comparison with SOTA Baseline Methods. All models are trained with 300 epochs.

for numerical stability. This adjustment aligns the normalization process in each Batch Normalization layer with the true distribution of the distilled data, thereby enhancing the quality of the soft labels and improving the generalization performance of the post-evaluation task, as demonstrated in Section [4.5.](#page-6-0)

## 4. Experiments

This section evaluates the performance of our proposed method, CV-DD, against state-of-the-art approaches across various datasets, neural architectures, and IPC configurations. Additionally, we conduct comprehensive ablation studies, overfitting mitigation analysis and cross-architecture generalization experiments to further validate its effectiveness.

## 4.1. Dataset and Experimental Configuration

Detailed configurations, including the hyper-parameters for each stage, are provided in the Appendix.

Datasets. To comprehensively evaluate the performance of CV-DD, we test it on both low-resolution and highresolution datasets. The low-resolution datasets include CIFAR-10 [\[14\]](#page-8-19) and CIFAR-100 [\[14\]](#page-8-19), both with a resolution of  $32\times32$ . For high-resolution datasets, we use Tiny-ImageNet (64×64) [\[34\]](#page-9-18), ImageNet-1K (224×224) [\[4\]](#page-8-20), and ImageNette [\[4\]](#page-8-20), which is a subset of ImageNet-1K.

Baseline Methods. We selected RDED [\[28\]](#page-9-0), a recent stateof-the-art dataset distillation method, as our primary baseline due to its strong performance. Additionally, we incorporated MTT [\[1\]](#page-8-8) and G-VBSM [\[24\]](#page-9-12), two ensemble-based approaches, to further evaluate the effectiveness of our tailored ensemble method, CV-DD. Finally, we included CDA [\[35\]](#page-9-2), a recent optimization-based method, and SRe2L++, which integrates the latest advancements and achieves the best performance among these methods, to comprehensively assess CV-DD's effectiveness.

## 4.2. Main Results

High-Resolution Datasets. To evaluate the effectiveness of our approach on large-scale and high-resolution datasets, we compare it against state-of-the-art dataset distillation methods on Tiny-ImageNet, ImageNet-1K, and its subset ImageNette. As shown in Table [1,](#page-5-0) our method, CV-DD, consistently outperforms previous SOTA methods across all IPC settings. Notably, on ImageNet-1K at 50 IPC with ResNet18, CV-DD achieves an impressive 59.5%, surpassing our strong baseline  $SRe^2L++$  by  $+1.9\%$ , CDA by  $+6\%$ , and RDED by +3%. The only exception occurs on Tiny-ImageNet with IPC  $= 50$ , where **CV-DD** falls slightly behind. However, given its substantial improvements on other datasets, this isolated result does not undermine CV-DD's overall effectiveness on high-resolution datasets.

Low-Resolution Datasets. To demonstrate the applicability of CV-DD beyond high-resolution datasets, we conducted additional experiments on small datasets. As shown in Table [1,](#page-5-0) CV-DD consistently delivers outstanding performance across all IPC settings and backbone networks, significantly exceeding the results of prior SOTA baseline methods. For instance, on CIFAR-100 with ResNet18 at IPC=10, CV-DD reaches 53.6% accuracy, outperforming RDED by +11%,  $SRe<sup>2</sup>L++ by ****%$ , and CDA by ****%. These findings further validate the robustness and adaptability of CV-DD, emphasizing its capability to perform effectively across datasets of varying resolutions and complexities.

Comparison with State-of-the-Art Ensemble Methods. To ensure a fair comparison, we trained CV-DD for 1000 epochs on small-resolution datasets *e.g.,* CIFAR-10 and CIFAR-100. Given the substantial variance in training configurations, G-VBSM's results are reported separately from Table [1.](#page-5-0) In Table [2,](#page-6-1) we compare the performance of our method (CV-DD) with previous ensemble-based methods across different datasets and resolutions. Notably, CV-DD demonstrates superior performance in IPC=50 set-

<span id="page-6-5"></span><span id="page-6-1"></span>

| Dataset       | IPC | MTT [1] | G-VBSM [24] | CV-DD (Ours) |
|---------------|-----|---------|-------------|--------------|
| Tiny-ImageNet | 1   | 8.8     | -           | 10.1         |
|               | 10  | 23.2    | -           | 47.8         |
|               | 50  | 28.0    | 47.6        | 54.1         |
| ImageNet-1k   | 10  | -       | 31.5        | 46.0         |
|               | 50  | -       | 51.8        | 59.5         |
| CIFAR-10      | 10  | 65.3    | 53.5        | 64.1         |
|               | 50  | 71.6    | 59.2        | 74.0         |
| CIFAR-100     | 1   | 24.3    | 25.9        | 28.3         |
|               | 10  | 40.1    | 59.5        | 62.7         |
|               | 50  | 47.7    | 65.0        | 67.1         |

Table 2. Performance comparison with prior vanilla ensemblebased methods of MTT, G-VBSM, and our prior performance guided committee voting based CV-DD, using ResNet18 as the student model for G-VBSM and ours, and Conv128 for MTT. All the training settings follow G-VBSM, i.e., 300 epochs training budget on Tiny-ImageNet and ImageNet-1K, and 1,000 epochs on CIFAR-10 and 100 datasets.

<span id="page-6-2"></span>Image /page/6/Figure/2 description: This is a line graph comparing the Top-1 Accuracy (%) on the y-axis against Epoch on the x-axis. There are four lines representing different training methods: SRe2L++ Train Accuracy (dark purple), CV-DD Train Accuracy (light purple), SRe2L++ Test Accuracy (dashed pink with circles), and CV-DD Test Accuracy (dashed light pink with triangles). The x-axis ranges from 0 to 300 epochs, and the y-axis ranges from 10% to 80%. Both train accuracies start at around 20% and increase rapidly, with SRe2L++ Train Accuracy reaching a peak of around 68% and fluctuating, while CV-DD Train Accuracy plateaus around 60-62%. The test accuracies also start around 20% and increase more gradually. SRe2L++ Test Accuracy reaches about 62% by epoch 250 and then slightly declines, while CV-DD Test Accuracy reaches about 70% by epoch 300.

Figure 6. Comparison of Top-1 accuracy curve between CV-DD and  $SRe^2L++$  on CIFAR-10 with 50 IPC.

tings, achieving improvements of +7.7% on ImageNet-1K and +6.5% on Tiny-ImageNet compared to G-VBSM, and +26.1% on Tiny-ImageNet compared to MTT, highlighting its ability to generate more effective data than traditional ensemble approaches. Overall, these results validate the effectiveness of our tailored ensemble approach in handling diverse datasets, affirming CV-DD as a reliable and efficient method in various settings.

### 4.3. Analysis

Overfitting Analysis. CV-DD effectively mitigates overfitting during the post-training phase. Fig. [6](#page-6-2) shows the train and test top-1 accuracy of  $CV-DD$  and  $SRe<sup>2</sup>L++$  over epochs. Notably, CV-DD maintains lower training accuracy but consistently achieves higher test accuracy compared to  $SRe<sup>2</sup>L++$ . These results demonstrate the effectiveness of CV-DD's Prior Performance Guided Voting Strategy as a regularization method in overfitting-prone scenarios.

Efficiency analysis. We compared the efficiency of CV-DD

with previous state-of-the-art ensemble-based methods on various datasets, as shown in Table [3.](#page-6-3) Specifically, MTT incurs the highest computational cost and lacks scalability to ImageNet-1K. In contrast, CV-DD achieves significantly higher efficiency (approximately 1.11 ms faster per iteration than G-VBSM) on ImageNet-1k. These results collectively highlight the superior efficiency of the proposed CV-DD comparing to previous ensemble-based method.

<span id="page-6-3"></span>

| <b>Datasets</b> | <b>MTT</b> | <b>G-VBSM</b> | <b>CV-DD</b>   |
|-----------------|------------|---------------|----------------|
| CIFAR-100       | 2.02 ms    | 0.97 ms       | <b>0.52</b> ms |
| Tiny-ImageNet   | 12.64 ms   | 1.05 ms       | <b>0.62</b> ms |
| ImageNet-1k     | N/A        | 2.1 ms        | <b>0.95</b> ms |

Table 3. Efficiency comparison of previous state-of-the-art ensemble-based methods. The table presents the time consumption (in milliseconds) required to optimize a single image per iteration on a single RTX-4090 GPU. The measurements were calculated using a batch size of 100 across the same set of committee models. N/A indicates that the method is not scalable to the given dataset due to inefficiency issues.

<span id="page-6-4"></span>

| Model                  | #Params | G-VBSM | RDED | SRe2L++ | CV-DD |
|------------------------|---------|--------|------|---------|-------|
| ResNet18 [8]           | 11.7M   | 31.4   | 42.0 | 43.1    | 46.0  |
| ResNet50 [8]           | 25.6M   | 35.4   | 49.7 | 47.3    | 51.3  |
| ResNet101 [8]          | 44.5M   | 38.2   | 48.3 | 51.2    | 51.7  |
| RegNet-X-8gf [21]      | 39.6M   | 39.8   | 51.9 | 53.4    | 55.6  |
| MobileNetV2 [23]       | 3.4M    | 27.6   | 34.4 | 37.1    | 39.0  |
| ShuffleNetV2-0.5x [37] | 1.4M    | 16.0   | 19.6 | 22.9    | 27.4  |
| EfficientNet-B0 [29]   | 5.3M    | 40.1   | 33.3 | 39.9    | 43.2  |
| Wide ResNet50-2 [8]    | 68.9M   | 48.6   | 50.0 | 50.2    | 53.9  |
| DenseNet121 [11]       | 8.0M    | 44.7   | 49.4 | 46.7    | 50.9  |
| DenseNet169 [11]       | 14.2M   | 48.1   | 50.9 | 51.8    | 53.6  |
| DenseNet201 [11]       | 20.0M   | 47.4   | 49.0 | 52.1    | 54.8  |

Table 4. Top-1 accuracy on ImageNet-1K for cross-architecture generalization with IPC=10.

### 4.4. Cross-Architecture Generalization

A key criterion for evaluating distilled data is its ability to generalize across diverse network architectures, ensuring broader applicability in real-world scenarios. To assess this, we compared the performance of CV-DD committee distilled data against RDED, G-VBSM, and  $SRe<sup>2</sup>L++arccos$ eleven architectures, from lightweight models like ShuffleNetV2 [\[37\]](#page-9-16) to complex networks such as Wide ResNet50- 2 [\[8\]](#page-8-14). As shown in Table [4,](#page-6-4) CV-DD consistently outperformed other methods. Notably, architectures such as RegNet-X-8GF [\[21\]](#page-8-21), EfficientNet [\[29\]](#page-9-19), among others, which were not included in the committee, also demonstrated strong performance, further highlighting the robustness and versatility of CV-DD. A visualization of the performance trends with respect to parameter size is provided in Appendix.

### <span id="page-6-0"></span>4.5. Ablation Study

Effect of the Number of Selected Experts. To validate that using two experts for updating distilled data  $(N = 2)$  per gradient step is optimal, we conducted ablation studies to examine how performance varies with different N. Since increasing the number of experts adds computational overhead,

<span id="page-7-0"></span>Image /page/7/Picture/0 description: The image displays a comparison of generated images with and without data augmentation. The top row, labeled 'w/o data augmentation', shows generated images categorized under 'Apple', 'Baby', and 'Fish'. The bottom row, labeled 'w/ data augmentation', also shows generated images categorized under 'Apple', 'Baby', and 'Fish'. Each category contains a grid of 16 generated images. The 'Apple' categories show images that resemble apples, the 'Baby' categories show images that resemble babies, and the 'Fish' categories show images that resemble fish. The images generated with data augmentation appear to be of higher quality and more distinct than those generated without it.

Figure 7. Comparison of distilled data on CIFAR-100 generated by  $SRe<sup>2</sup>L++$  with and without data augmentation.

we restricted our experiments to  $N = 2$  and  $N = 3$ , with  $N = 3$  as the upper limit. As shown in Table [5,](#page-7-1) although  $N = 3$  incurs higher computational costs, it results in performance degradation. This indicates that incorporating information from too many experts can lead to suboptimal optimization. Hence,  $N = 2$  proves to be the most efficient and effective choice.

| IPC | N = 2       | N = 3 |
|-----|-------------|-------|
| 1   | <b>17.2</b> | 16.4  |
| 10  | <b>54.3</b> | 53.6  |
| 50  | <b>65.1</b> | 64.3  |

<span id="page-7-1"></span>Table 5. Comparison of model performance using different numbers of experts for synthetic data optimization on CIFAR-100 dataset.

Effectiveness of Prior-Based Voting. To evaluate the impact of prior-based voting, we conducted ablation studies summarized in Table [6.](#page-7-2) The Prior-Based Voter consistently outperforms the Equal and Random Voters across all IPC settings, achieving 15.9%, 51.3%, and 65.1% accuracy for IPC1, IPC10, and IPC50, respectively. In contrast, the Random Voter yields suboptimal results, with corresponding accuracies of 14.1%, 51.1%, and 64.2%. These results underscore the importance of leveraging prior performance, and demonstrating that incorporating prior knowledge enhances model generalization.

<span id="page-7-2"></span>

| IPC | Equal Voter | Random Voter | Prior Voter |
|-----|-------------|--------------|-------------|
| 1   | 17.0        | 16.8         | 17.2        |
| 10  | 53.8        | 53.6         | 54.3        |
| 50  | 64.4        | 64.2         | 65.1        |

Table 6. Performance comparison of models trained on distilled datasets generated using different voter configurations across various IPC settings on CIFAR-100. Specifically, equal voter assigns uniform weights (0.5) to the prediction and distribution of each model, while the random voter assigns weights randomly.

Impact of Batch-Specific Soft Labeling. To assess the im-

<span id="page-7-4"></span>Image /page/7/Picture/9 description: This image displays a grid of generated images, categorized by model (G-VBSM and CV-DD) and object class. The top row shows G-VBSM generated images for Apple, Fish, Baby, Bear, Elephant, and Bicycle. The second row shows CV-DD generated images for the same object classes. The bottom two rows present larger generated images. The third row shows G-VBSM generated images for Goose, Goldfish, and Garbage Truck. The fourth row shows CV-DD generated images for Goose, Goldfish, and Garbage Truck. The image is labeled as Figure 8, illustrating distilled data from G-VBSM and CV-DD.

Figure 8. Distilled data from G-VBSM and CV-DD. The top two rows are from CIFAR-100 and bottom two are from ImageNet-1K.

pact of BSSL, we conducted ablation studies on both the  $SRe<sup>2</sup>L++$  and **CV-DD** methods. As presented in Table [7,](#page-7-3) the inclusion of BSSL leads to significant performance improvements, particularly under the IPC=10 setting. Specifically, BSSL yields a 3.4% improvement in SRe<sup>2</sup>L++ and a 7.9% improvement in CV-DD. These findings demonstrate the effectiveness of BSSL in mitigating the impact of distributional discrepancies between synthetic and real data, underscoring its importance in enhancing model generalization.

<span id="page-7-3"></span>

| method       | IPC           | Without BSSL         | With BSSL                                 |
|--------------|---------------|----------------------|-------------------------------------------|
| SRe2L++      | 1<br>10<br>50 | 12.1<br>46.6<br>61.9 | <b>13.5</b><br><b>52.1</b><br><b>64.0</b> |
| <b>CV-DD</b> | 1<br>10<br>50 | 13.0<br>49.3<br>64.0 | <b>17.2</b><br><b>54.3</b><br><b>65.1</b> |

Table 7. Comparison of model performance on distilled datasets with and without BSSL (Batch-Specific Soft Labeling) on CIFAR-100 Dataset.

### 4.6. Visualization

Data augmentation. As illustrated in the Fig. [7,](#page-7-0) distilled data generated with data augmentation incorporates a greater amount of main target compared to its non-augmented counterpart. This enhancement increases the information density of each distilled image, thereby potentially improving the generalization capability of models during the postevaluation phase.

Comparison with G-VBSM Distilled Data. Fig. [8](#page-7-4) compares the distilled data generated by G-VBSM and CV-DD on CIFAR-100 and ImageNet-1K. G-VBSM's distilled data exhibits significantly lower information density, with much of its ImageNet-1K content resembling Gaussian noise, reducing the overall useful information. On CIFAR-100, applying data augmentation allows CV-DD to capture more primary features, further boosting information density.

## 5. Conclusion

We propose *Committee Voting* for dataset distillation, a novel framework that synthesizes high-quality distilled datasets by leveraging multiple experts and produces high-quality soft labels through Batch-Specific Soft Labeling. Our approach first establishes a strong baseline that achieves state-of-theart accuracy through recent advancements and carefully optimized framework design. By combining the distributions and predictions from a committee of models, our method captures rich data features, reduces model-specific biases, and enhances generalization. Complementing this, the generation of high-quality soft labels provides precise supervisory signals, effectively mitigating the adverse effects of distribution shifts and further enhancing model performance. Building on these strengths, CV-DD not only promotes diversity and robustness within the distilled dataset but also reduces overfitting, resulting in consistent improvements across various configurations and datasets. Our future work will focus on applying the idea of *Committee Voting* to more modalities and applications of dataset distillation tasks.

## References

- <span id="page-8-8"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [3,](#page-2-0) [4,](#page-3-3) [6,](#page-5-1) [7](#page-6-5)
- <span id="page-8-10"></span>[2] Mingyang Chen, Bo Huang, Junda Lu, Bing Li, Yi Wang, Minhao Cheng, and Wei Wang. Dataset distillation via adversarial prediction matching. *arXiv preprint arXiv:2312.08912*, 2023. [3](#page-2-0)
- <span id="page-8-9"></span>[3] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [3](#page-2-0)
- <span id="page-8-20"></span>[4] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [6](#page-5-1)
- <span id="page-8-2"></span>[5] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *arXiv preprint arXiv:2206.02916*, 2022. [3](#page-2-0)
- <span id="page-8-12"></span>[6] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023. [3,](#page-2-0) [4](#page-3-3)

- <span id="page-8-11"></span>[7] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024. [3](#page-2-0)
- <span id="page-8-14"></span>[8] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [4,](#page-3-3) [7](#page-6-5)
- <span id="page-8-3"></span>[9] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. *ICLR*, 2024. [3](#page-2-0)
- <span id="page-8-16"></span>[10] Geoffrey Hinton. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015. [5](#page-4-7)
- <span id="page-8-13"></span>[11] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4700–4708, 2017. [4,](#page-3-3) [7](#page-6-5)
- <span id="page-8-15"></span>[12] Alex Kendall, Yarin Gal, and Roberto Cipolla. Multi-task learning using uncertainty to weigh losses for scene geometry and semantics. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 7482–7491, 2018. [5](#page-4-7)
- <span id="page-8-5"></span>[13] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, 2022. [3](#page-2-0)
- <span id="page-8-19"></span>[14] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, University of Toronto, Toronto, ON, Canada, 2009. [6](#page-5-1)
- <span id="page-8-4"></span>[15] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [3](#page-2-0)
- <span id="page-8-6"></span>[16] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022. [3](#page-2-0)
- <span id="page-8-1"></span>[17] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022. [3](#page-2-0)
- <span id="page-8-17"></span>[18] Rafael Müller, Simon Kornblith, and Geoffrey E Hinton. When does label smoothing help? *Advances in neural information processing systems*, 32, 2019. [5](#page-4-7)
- <span id="page-8-0"></span>[19] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [3](#page-2-0)
- <span id="page-8-18"></span>[20] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. A label is worth a thousand images in dataset distillation. In *Advances in Neural Information Processing Systems*, 2024. [5](#page-4-7)
- <span id="page-8-21"></span>[21] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollar. Designing network design spaces. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2020. [7](#page-6-5)
- <span id="page-8-7"></span>[22] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis.

Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 17097–17107, 2023. [3](#page-2-0)

- <span id="page-9-15"></span>[23] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018. [4,](#page-3-3) [7](#page-6-5)
- <span id="page-9-12"></span>[24] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16709–16718, 2024. [3,](#page-2-0) [4,](#page-3-3) [5,](#page-4-7) [6,](#page-5-1) [7](#page-6-5)
- <span id="page-9-13"></span>[25] Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024. [3,](#page-2-0) [4](#page-3-3)
- <span id="page-9-17"></span>[26] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pages 673–690. Springer, 2022. [5](#page-4-7)
- <span id="page-9-10"></span>[27] Donghyeok Shin, Seungjae Shin, and Il-Chul Moon. Frequency domain-based dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024. [3](#page-2-0)
- <span id="page-9-0"></span>[28] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390– 9399, 2024. [1,](#page-0-1) [4,](#page-3-3) [5,](#page-4-7) [6](#page-5-1)
- <span id="page-9-19"></span>[29] Mingxing Tan and Quoc Le. Efficient netv2: Smaller models and faster training. In *International conference on machine learning*, pages 10096–10106. PMLR, 2021. [7](#page-6-5)
- <span id="page-9-9"></span>[30] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2022. [3](#page-2-0)
- <span id="page-9-1"></span>[31] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation, 2020. [1,](#page-0-1) [3](#page-2-0)
- <span id="page-9-14"></span>[32] Lingao Xiao and Yang He. Are large-scale soft labels necessary for large-scale dataset distillation? *arXiv preprint arXiv:2410.15919*, 2024. [3](#page-2-0)
- <span id="page-9-11"></span>[33] Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024. [3](#page-2-0)
- <span id="page-9-18"></span>[34] Lian Yao, Yin Li, and Li Fei-Fei. Image classification using deep convolutional neural networks. [https:](https://cs231n.stanford.edu/reports/2015/pdfs/yle_project.pdf) [//cs231n.stanford.edu/reports/2015/pdfs/](https://cs231n.stanford.edu/reports/2015/pdfs/yle_project.pdf) [yle\\_project.pdf](https://cs231n.stanford.edu/reports/2015/pdfs/yle_project.pdf), 2015. CS231n: Convolutional Neural Networks for Visual Recognition, Stanford University, Course Project Report. [6](#page-5-1)
- <span id="page-9-2"></span>[35] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *arXiv preprint arXiv:2311.18838*, 2023. [1,](#page-0-1) [3,](#page-2-0) [4,](#page-3-3) [6](#page-5-1)
- <span id="page-9-3"></span>[36] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024. [1,](#page-0-1) [3,](#page-2-0) [4,](#page-3-3) [5](#page-4-7)

- <span id="page-9-16"></span>[37] Xiangyu Zhang, Xinyu Zhou, Mengxiao Lin, and Jian Sun. Shufflenet: An extremely efficient convolutional neural network for mobile devices. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 6848–6856, 2018. [4,](#page-3-3) [7](#page-6-5)
- <span id="page-9-6"></span>[38] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [3](#page-2-0)
- <span id="page-9-8"></span>[39] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision, WACV 2023, Waikoloa, HI, USA, January 2-7, 2023*, 2023. [3](#page-2-0)
- <span id="page-9-5"></span>[40] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [3](#page-2-0)
- <span id="page-9-7"></span>[41] Binglin Zhou, Linhao Zhong, and Wentao Chen. Improve cross-architecture generalization on dataset distillation. *arXiv preprint arXiv:2402.13007*, 2024. [3](#page-2-0)
- <span id="page-9-4"></span>[42] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [3](#page-2-0)

# Appendix

## A. Hyper-Parameters Setting

Overall, the generation of distilled data follows consistent hyperparameter settings, as shown in Table [8.](#page-10-0) The only variations occur during post-evaluation and model pre-training, where hyperparameters are adjusted based on the models' parameter sizes and the specific datasets used. Additionally, in the pre-training phase, we trained five models across all datasets: ResNet18, ResNet50, ShuffleNetV2, MobileNetV2, and DenseNet121.

<span id="page-10-0"></span>

| <b>Hyperparameters for Synthetic Data Generation</b> |                                    |
|------------------------------------------------------|------------------------------------|
| <b>Optimizer</b>                                     | Adam                               |
| <b>Learning rate</b>                                 | 0.1                                |
| <b>beta</b>                                          | 0.5, 0.9                           |
| <b>epsilon</b>                                       | 1e-8                               |
| <b>Batch Size</b>                                    | 100 or 10 (if C < 100)             |
| <b>Iterations</b>                                    | 4,000                              |
| <b>Scheduler</b>                                     | Cosine Annealing                   |
| <b>Augmentation</b>                                  | RandomResizedCrop, Horizontal Flip |

Table 8. Hyperparameters for generating synthetic data across all five datasets.

### A.1. CIFAR-10

This subsection provides a detailed explanation of all hyperparameter configurations used in the experiments with CIFAR-10, ensuring reproducibility in future work.

Training Pre-trained Models. Table [9](#page-10-1) outlines the hyperparameters used to train the models on the original CIFAR-10.

<span id="page-10-1"></span>

| <b>Hyperparameters for Model Pre-training</b> |                                    |
|-----------------------------------------------|------------------------------------|
| Optimizer                                     | Adam                               |
| Learning rate                                 | 0.001                              |
| Weight Decay                                  | 1e-4                               |
| Batch Size                                    | 512                                |
| Epoch                                         | 200                                |
| Scheduler                                     | Cosine Annealing                   |
| Augmentation                                  | RandomResizedCrop, Horizontal Flip |
| Loss Function                                 | Cross-Entropy                      |

Table 9. Hyperparameters for CIFAR-10 Pre-trained Models.

Post Evaluation Phase. Table [10](#page-10-2) summarizes the hyperparameters used for post-evaluation on the distilled CIFAR-10.

### <span id="page-10-2"></span>Hyperparameters for Post-Eval on R18, R50 and R101

| Optimizer            | Adamw                            |
|----------------------|----------------------------------|
| Learning Rate        | $0.001$ (ResNet18, ResNet50) or  |
|                      | $0.0005$ (ResNet101)             |
| <b>Loss Function</b> | KL-Divergence                    |
| <b>Batch Size</b>    | 16 or 10 (if $ S  \le 16$ )      |
| Epochs               | 300                              |
| Scheduler            | Cosine Annealing with $\eta = 1$ |
| Augmentation         | RandomResizedCrop,               |
|                      | Horizontal Flip, CutMix          |

Table 10. Hyperparameters for post-evaluation task on ResNet18, ResNet50 and ResNet101 for CIFAR-10.

### A.2. CIFAR-100

This subsection outlines the hyperparameter configurations employed in the CIFAR-100 experiments, providing the necessary details to ensure reproducibility in future research. Training Pre-trained Models. Table [11](#page-10-3) summarizes the hyperparameters used for training the models on the original CIFAR-100 dataset.

<span id="page-10-3"></span>

| <b>Hyperparameters for Model Pre-training</b> |                                    |
|-----------------------------------------------|------------------------------------|
| Optimizer                                     | Adam                               |
| Learning rate                                 | 0.001                              |
| Weight Decay                                  | $1e-4$                             |
| <b>Batch Size</b>                             | 512                                |
| Epoch                                         | 200                                |
| Scheduler                                     | Cosine Annealing                   |
| Augmentation                                  | RandomResizedCrop, Horizontal Flip |
| <b>Loss Function</b>                          | Cross-Entropy                      |

Table 11. Hyperparameters for CIFAR-100 Pre-trained Models.

Post Evaluation Phase. Table [12](#page-11-0) summarizes the hyperparameters used for post-evaluation on the Distilled CIFAR-100 dataset.

### A.3. Tiny-ImageNet

Training Pre-trained Models. Table [13](#page-11-1) summarizes the hyperparameters used for training the models on the original Tiny-ImageNet dataset.

Post Evaluation Phase. Table [14](#page-11-2) summarizes the hyperparameters used for post-evaluation on the Distilled Tiny-ImageNet dataset.

## A.4. ImageNette

This subsection provides a comprehensive overview of all hyperparameter configurations employed in the ImageNette experiments, facilitating reproducibility for future research. Training Pre-trained Models. Table [15](#page-11-3) outlines the hyperparameters used to train the models for recovery on the

<span id="page-11-0"></span>

### Hyperparameters for Post-Eval on R18, R50 and R101

| Optimizer     | Adamw                                                                                                                                                                                  |
|---------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Learning Rate | 0.001 (ResNet18, ResNet50) or<br>0.0005 (ResNet101)                                                                                                                                    |
| Loss Function | KL-Divergence                                                                                                                                                                          |
| Batch Size    | 16                                                                                                                                                                                     |
| Epochs        | 300                                                                                                                                                                                    |
| Scheduler     | Cosine Annealing with<br>$η = 1$ (ResNet18, IPC=1, 50) or<br>$η = 1$ (ResNet50, IPC=1, 50) or<br>$η = 1$ (ResNet101) or<br>$η = 2$ (ResNet18, IPC=10) or<br>$η = 2$ (ResNet50, IPC=10) |
| Augmentation  | RandomResizedCrop,<br>Horizontal Flip, CutMix                                                                                                                                          |

Table 12. Hyperparameters for post-evaluation task on ResNet18, ResNet50 and ResNet101 for CIFAR-100.

<span id="page-11-1"></span>

| <b>Hyperparameters for Model Pre-training</b> |                                    |
|-----------------------------------------------|------------------------------------|
| Optimizer                                     | SGD                                |
| Learning rate                                 | 0.1                                |
| Momentum                                      | 0.9                                |
| Batch Size                                    | 128                                |
| Epoch                                         | 50                                 |
| Scheduler                                     | Cosine Annealing                   |
| Augmentation                                  | RandomResizedCrop, Horizontal Flip |
| Loss Function                                 | Cross-Entropy                      |

Table 13. Hyperparameters for Training Tiny-ImageNet Pre-trained Models.

<span id="page-11-2"></span>

| Hyperparameters for Post-Eval on R18, R50 and R101 |                                    |
|----------------------------------------------------|------------------------------------|
| Optimizer                                          | Adamw                              |
| Learning Rate                                      | 0.001 (ResNet18) or                |
|                                                    | 0.001 (ResNet50) or                |
|                                                    | 0.0005 (ResNet101)                 |
| Loss Function                                      | KL-Divergence                      |
| Batch Size                                         | 16                                 |
| Epochs                                             | 300                                |
| Scheduler                                          | Cosine Annealing with              |
|                                                    | $\eta = 1$ (ResNet18 IPC=50) or    |
|                                                    | $\eta = 1$ (ResNet50) or           |
|                                                    | $\eta = 2$ (ResNet18 IPC=1, 10) or |
|                                                    | $\eta = 2$ (ResNet101)             |
| Augmentation                                       | RandomResizedCrop,                 |
|                                                    | Horizontal Flip, CutMix            |

Table 14. Hyperparameters for post-evaluation task on ResNet18, ResNet50 and ResNet101 for Tiny-ImageNet.

### original ImageNette dataset.

Post Evaluation Phase. Table [16](#page-11-4) summarizes the hyper-

<span id="page-11-3"></span>

| <b>Hyperparameters for Model Pre-training</b> |                                    |
|-----------------------------------------------|------------------------------------|
| Optimizer                                     | SGD                                |
| Learning rate                                 | 0.01                               |
| Momentum                                      | 0.9                                |
| weight decay                                  | 1e-4                               |
| Batch Size                                    | 64                                 |
| Epoch                                         | 300                                |
| Scheduler                                     | Cosine Annealing                   |
| Augmentation                                  | RandomResizedCrop, Horizontal Flip |
| Loss Function                                 | Cross-Entropy                      |

Table 15. Hyperparameters for Training ImageNette Pre-trained Models.

parameters used for post-evaluation on the Distilled ImageNette dataset.

<span id="page-11-4"></span>

| Hyperparameters for post-eval on R18, R50 and R101 |                                                                                                                                                                                                          |
|----------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Optimizer                                          | Adamw                                                                                                                                                                                                    |
| Learning Rate                                      | $5e-4$ (ResNet18) or<br>5e-4 (ResNet50, IPC=1, 50) or<br>5e-4 (ResNet101, IPC=1, 10) or<br>$0.001$ (ResNet50, IPC=10) or<br>$0.001$ (ResNet101, IPC=50)                                                  |
| Loss Function                                      | KL-Divergence                                                                                                                                                                                            |
| <b>Batch Size</b>                                  | 10                                                                                                                                                                                                       |
| Epochs                                             | 300                                                                                                                                                                                                      |
| Scheduler                                          | Cosine Annealing with<br>$\eta = 1$ (ResNet18, IPC=10) or<br>$\eta = 1$ (ResNet50, IPC=10, 50)<br>or<br>$\eta = 2$ (ResNet18, IPC=1, 50) or<br>$\eta = 2$ (ResNet50, IPC=1) or<br>$\eta = 2$ (ResNet101) |
| Augmentation                                       | RandomResizedCrop,<br>Horizontal Flip, CutMix                                                                                                                                                            |

Table 16. Hyperparameters for post-evaluation task on ResNet18, ResNet50 and ResNet101 for ImageNette.

### A.5. ImageNet-1K

This subsection provides a detailed explanation of the hyperparameter configurations used in the experiments with ImageNet-1K, ensuring reproducibility in future work. Since we utilize PyTorch's officially trained models for generating the distilled data, only the hyperparameters for postevaluation are provided as shown in Table [17.](#page-12-0)

## A.6. Cross-Architecture Generalization

This subsection provides a comprehensive explanation of the hyperparameter configurations used in the Cross-Architecture Generalization experiments, ensuring repro-

<span id="page-12-0"></span>

| Optimizer     | Adamw                                                                                                                                                                                                 |
|---------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Learning Rate | 0.0005(ResNet101, IPC=50) or<br>0.001                                                                                                                                                                 |
| Loss Function | KL-Divergence                                                                                                                                                                                         |
| Batch Size    | 16                                                                                                                                                                                                    |
| Epochs        | 300                                                                                                                                                                                                   |
| Scheduler     | Cosine Annealing with<br>$\eta = 1$ (ResNet18, IPC=50) or<br>$\eta = 1$ (ResNet50, IPC=50) or<br>$\eta = 2$ (ResNet18, IPC=1, 10) or<br>$\eta = 2$ (ResNet50, IPC=1, 10) or<br>$\eta = 2$ (ResNet101) |
| Augmentation  | RandomResizedCrop,<br>Horizontal Flip, CutMix                                                                                                                                                         |

Table 17. Hyperparameters for post-evaluation task on ResNet18, ResNet50 and ResNet101 for ImageNet-1K.

ducibility for future research. Specifically, architectures with larger parameter sizes, DenseNet201, adopt the same hyperparameter settings as ResNet101. In contrast, models with smaller parameter sizes follow the configurations of ResNet18 and ResNet50, as detailed in Table [17.](#page-12-0)

## B. Additional Ablation Study

Impact of Committee Choices. Table [18](#page-12-1) illustrates the impact of different committee combinations on the generalization performance of the final model during post-evaluation. The results demonstrate that the generalization performance improves as the number of models in the committee increases, indicating that expanding the committee size can enhance the model's generalization ability. Additionally, it is noteworthy that the inclusion of ResNet50 leads to the most significant improvement in generalization. This aligns with expectations, as ResNet50, apart from ResNet18, exhibits the best prior performance among all committee members, thereby contributing the most substantial enhancement to the overall performance.

<span id="page-12-1"></span>

| Candidates (Backbone) |          |          |     | Evaluation Model |          |          |
|-----------------------|----------|----------|-----|------------------|----------|----------|
| ResNet18              | ResNet50 | Dense121 | SV2 | Mobile           | ResNet18 | ResNet50 |
| ✓                     |          |          |     |                  | 64.00    | 64.08    |
| ✓                     | ✓        |          |     |                  | 64.48    | 64.30    |
| ✓                     | ✓        | ✓        |     |                  | 64.66    | 64.32    |
| ✓                     | ✓        | ✓        | ✓   |                  | 64.88    | 64.31    |
| ✓                     | ✓        | ✓        | ✓   | ✓                | 65.10    | 64.83    |

Table 18. Comparison of model performance on distilled datasets with different committee choices on CIFAR-100 under IPC=50.

## C. Prior Performance

This section presents the prior performance of each model across various datasets. Specifically, we generate 50 images

per class (IPC) for small-resolution datasets (CIFAR-10, CIFAR-100) and 10 images per class for high-resolution datasets (ImageNet-1K, ImageNette, and Tiny-ImageNet). The corresponding results are summarized in Table [19.](#page-12-2)

<span id="page-12-2"></span>

| Model        | CIFAR-10 | CIFAR-100 | Tiny-ImageNet | ImageNette | ImageNet-1K |
|--------------|----------|-----------|---------------|------------|-------------|
| ResNet18     | 63.01    | 64.00     | 46.50         | 62.40      | 43.10       |
| ResNet50     | 65.25    | 60.58     | 28.37         | 52.00      | 41.37       |
| DenseNet121  | 67.57    | 56.36     | 41.5          | 60.80      | 38.97       |
| ShuffleNetV2 | 68.52    | 51.62     | 43.56         | 57.20      | 43.73       |
| MobileNetV2  | 67.61    | 59.34     | 44.84         | 54.00      | 39.15       |

Table 19. Prior Performance for different models across different datasets.

## D. Additional Visualization

### D.1. Cross Generalization visualization

Fig. [9](#page-12-3) illustrates how performance evolves with increasing parameter size when training on different distilled datasets: baseline RDED and CV-DD (ours). It is evident that the data distilled by CV-DD demonstrates remarkable robustness, consistently achieving better generalization across architectures with varying parameter sizes compared to data generated by RDED. This highlights the effectiveness of the CV-DD method.

<span id="page-12-3"></span>Image /page/12/Figure/16 description: This is a scatter plot showing the relationship between the number of parameters (in millions) on the x-axis and test accuracy (in percentage) on the y-axis. The plot displays data points for various neural network architectures, categorized by two methods: CV-DD(ours) represented by pink triangles, and RDED represented by blue circles. Several models are labeled, including DenseNet169, DenseNet201, Wide ResNet50-2, RegNet-X-8gf, ResNet101, EfficientNet-B0, and ShuffleNetV2-0.5x. The y-axis ranges from 0% to 60%, and the x-axis ranges from 0 to 80 million parameters. For example, DenseNet201 (CV-DD) is plotted at approximately 40 million parameters and 55% accuracy, while DenseNet201 (RDED) is plotted at approximately 20 million parameters and 50% accuracy. ShuffleNetV2-0.5x (CV-DD) is at around 10 million parameters and 30% accuracy, and ShuffleNetV2-0.5x (RDED) is at around 10 million parameters and 20% accuracy.

Figure 9. Visualization of Top-1 Test Accuracy trends on ImageNet-1K as model size increases for various architectures with IPC 10.

### D.2. BN Layer Statistical Visualization

As shown in Fig. [10](#page-13-0) and Fig. [11,](#page-14-0) the significant statistical discrepancy is not confined to Layer 0 and Layer 15. Instead, it is evident across all Batch Normalization layers, highlighting the crucial role of applying BSSL.

### D.3. Additional Distilled Data Visualization

More visualizations of the distilled data generated by CV-DD are presented in Figures [12](#page-15-0) (CIFAR-10), [13](#page-16-0) (CIFAR-100), [14](#page-17-0) (Tiny-ImageNet), [15](#page-18-0) (ImageNette), and [16](#page-19-0) (ImageNet-1K).

<span id="page-13-0"></span>Image /page/13/Figure/0 description: The image displays a grid of 20 scatter plots, each titled "Mean Values for Input Tensor in BatchNorm Layer X", where X ranges from 0 to 19. Each plot compares the mean values of input tensors for two datasets, SRe2L (blue dots) and ImageNet-1k (orange dots), across the index of batch iteration from 0 to 3000. The y-axis represents the mean value, which varies across the plots. For example, Layer 0 shows values between -0.04 and 0.03, while Layer 1 shows values between -1.2 and -0.8. The plots generally show a similar distribution pattern for both datasets within each layer, with some fluctuations. The overall caption for the figure is "Figure 10. Feature-level mean discrepancies between synthetic data generated by SRe2L++ and the training data on ImageNet-1K."

Figure 10. Feature-level mean discrepancies between synthetic data generated by SRe<sup>2</sup>L++ and the training data on ImageNet-1K, evaluated across different batches in a pre-trained ResNet18 model.

<span id="page-14-0"></span>Image /page/14/Figure/0 description: The image displays a grid of 20 scatter plots, each titled 'Variances for Input Tensor in BatchNorm Layer X', where X ranges from 0 to 19. Each plot compares the variances for two datasets, 'SR2L' (represented by blue dots) and 'ImageNet-1k' (represented by orange dots), against the index of the batch iteration, which spans from 0 to 3000. The y-axis in each plot represents the 'Value' of the variance. The plots show a general trend where the variances for both datasets fluctuate over the iterations, with 'SR2L' generally exhibiting higher variances than 'ImageNet-1k' in the earlier layers (0-3) and then showing lower variances in the later layers (4-19). The scale of the y-axis varies significantly across the layers, with Layer 0 showing variances around 2.0 to 5.0, Layer 1 around 1.0 to 2.75, and subsequent layers showing progressively smaller variance values, down to around 0.008 to 0.020 in Layer 19. The overall figure is titled 'Figure 11. Feature-level variance discrepancies between synthetic data generated by SR2L++ and the training data on ImageNet-1K'.

Figure 11. Feature-level variance discrepancies between synthetic data generated by SRe<sup>2</sup>L++ and the training data on ImageNet-1K, evaluated across different batches in a pre-trained ResNet18 model.

<span id="page-15-0"></span>Image /page/15/Picture/0 description: The image displays a grid of 80 small images, arranged in 8 rows and 10 columns. Each small image appears to be a generated image, possibly from a machine learning model, with varying degrees of abstraction and color. The images depict a range of subjects, including what appear to be animals, objects, and abstract patterns. Some images are blurry or distorted, while others are more defined. The overall impression is a collection of diverse visual outputs.

Figure 12. Visualization of synthetic data on CIFAR-10 generated by CV-DD.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. The smaller images appear to be generated synthetic data, likely from a machine learning model trained on the CIFAR-100 dataset, as indicated by the caption below the grid. The images display a variety of subjects, including animals (dogs, cats, birds, lions, deer), vehicles (cars, trucks, buses), objects (cups, chairs, doors), and natural scenes (trees, landscapes, water). Some images are clear and recognizable, while others are more abstract or distorted, showing the model's varying levels of success in generating realistic images. The overall impression is a diverse collection of AI-generated visuals.

Figure 13. Visualization of synthetic data on CIFAR-100 generated by CV-DD.

<span id="page-17-0"></span>Image /page/17/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. The smaller images appear to be generated synthetic data, possibly for an AI model, as indicated by the caption below the grid which reads "Figure 14. Visualization of synthetic data on Tiny ImageNet generated by CV-DD." The images themselves are diverse, showing a variety of subjects including animals (cats, birds, dogs, elephants), objects (lamps, bottles, cars, furniture), and abstract patterns or scenes. Some images are clear and recognizable, while others are more abstract or distorted, suggesting a range of quality or focus in the generated data.

Figure 14. Visualization of synthetic data on Tiny-ImageNet generated by CV-DD.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: The image is a grid of 80 small images, arranged in 8 rows and 10 columns. Each small image appears to be a generated image, possibly from a machine learning model, with abstract and somewhat distorted visuals. The colors vary across the grid, with some images featuring bright, saturated colors and others being more muted or monochromatic. The overall impression is a collection of diverse, artistic, or experimental visual outputs.

Figure 15. Visualization of synthetic data on ImageNette generated by CV-DD.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image appears to be a generated artwork or photograph with a painterly or abstract style. The subjects vary widely, including animals (dogs, birds, bison, deer), landscapes, abstract patterns, still lifes, and human figures. Some images have a vibrant color palette, while others are more muted. The overall impression is a diverse collection of visual content, possibly generated by an AI model.

Figure 16. Visualization of synthetic data on ImageNet-1K generated by CV-DD.