# BOOM! **Dataset Distillation via Curriculum Data Synthesis in Large Data Era**

**<PERSON><PERSON><PERSON> Yin** *<EMAIL> VILA Lab <PERSON> bin Zayed University of Artificial Intelligence*

*<EMAIL>*

*<EMAIL>*

**<PERSON><PERSON>qiang Shen**<sup>∗</sup> *VILA Lab <PERSON> bin Zayed University of Artificial Intelligence*

**Reviewed on OpenReview:** *[https: // openreview. net/ forum? id= PlaZD2nGCl](https://openreview.net/forum?id=PlaZD2nGCl)*

### **Abstract**

Dataset distillation or condensation aims to generate a smaller but representative subset from a large dataset, which allows a model to be trained more efficiently, meanwhile evaluating on the original testing data distribution to achieve decent performance. Previous decoupled methods like  $\text{SRe}^2L$  simply use a unified gradient update scheme for synthesizing data from Gaussian noise, while, we notice that the initial several update iterations will determine the final outline of synthesis, thus an improper gradient update strategy may dramatically affect the final generation quality. To address this, we introduce a simple yet effective *global-to-local* gradient refinement approach enabled by curriculum data augmentation (CDA) during data synthesis. The proposed framework achieves the current published highest accuracy on both large-scale ImageNet-1K and 21K with 63.2\% under IPC (Images Per Class) 50 and 36.1% under IPC 20, using a regular input resolution of  $224 \times 224$  with faster convergence speed and less synthetic time. The proposed model outperforms the current state-of-the-art methods like  $SRe^{2}L$ , TESLA, and MTT by more than 4% Top-1 accuracy on ImageNet- $1K/21K$  and for the first time, reduces the gap to its full-data training counterparts to less than absolute 15%. Moreover, this work represents the inaugural success in dataset distillation on the larger-scale ImageNet-21K dataset under the standard 224×224 resolution. Our code and distilled ImageNet-21K dataset of 20 IPC, 2K recovery budget are available at <https://github.com/VILA-Lab/SRe2L/tree/main/CDA>.

## **1 Introduction**

Dataset distillation or condensation [\(Wang et al.,](#page-15-0) [2018\)](#page-15-0) has attracted considerable attention across various fields of computer vision [\(Cazenavette et al.,](#page-13-0) [2022b;](#page-13-0) [Cui et al., 2023;](#page-13-1) [Yin et al., 2023\)](#page-15-1) and natural language processing [\(Sucholutsky & Schonlau, 2021;](#page-15-2) [Maekawa et al., 2023\)](#page-14-0). This task aims to optimize the process of condensing a massive dataset into a smaller, yet representative subset, preserving the essential features and characteristics that would allow a model to learn from scratch as effectively from the distilled dataset as it would from the original large

<span id="page-0-0"></span>Resnet-18 Resnet-50 Resnet-101 DenseNet-121 RegNet-Y-8GF  $4<sup>5</sup>$ 50 55∤⊹ 60∤∸  $65$ Top-1 Accuarcy (%)  $S$ Re<sup>2</sup>L (4K) Our CDA (1K)  $\Box$  Our CDA (4K)

<sup>∗</sup>Corresponding author.

Figure 1: ImageNet-1K comparison with  $SRe<sup>2</sup>L$ .

dataset. As the scale of data and models continue to grow, this *dataset distillation* concept becomes even more critical in the large data era, where datasets are often voluminous that they pose storage, computational, and processing challenges. Generally, dataset distillation can level the playing field, allowing researchers with limited computation and storage resources to participate in state-of-the-art foundational model training and application development, such as affordable ChatGPT [\(Brown et al., 2020;](#page-12-0) [OpenAI, 2023\)](#page-14-1) and Stable Diffusion [\(Rombach et al., 2022\)](#page-15-3), in the current large data and large model regime. Moreover, by working with distilled datasets, which are synthesized to retain the most representative information from Gaussian noise initialization through gradient optimization at a high-level abstraction instead of closely resembling the original dataset, there is potential to alleviate data privacy concerns, as raw, personally identifiable data points might be excluded from the distilled version.

Recently, there has been a significant trend in adopting large models and large data across various research and application areas. Yet, many prior dataset distillation methods [\(Wang et al., 2018;](#page-15-0) [Zhao et al., 2020;](#page-16-0) [Zhou et al., 2022;](#page-16-1) [Cazenavette et al., 2022a;](#page-13-2) [Kim et al., 2022a;](#page-14-2) [Cui et al., 2023\)](#page-13-1) predominantly target datasets like CIFAR, Tiny-ImageNet and downsampled ImageNet-1K, finding it challenging to scale their frameworks for larger datasets, such as full ImageNet-1K [\(Deng et al., 2009\)](#page-13-3). This suggests that these approaches have not fully evolved in line with contemporary advancements and dominant methodologies.

In this study, we extend our focus even beyond the ImageNet-1K dataset, venturing into the uncharted territories of the full ImageNet-21K [\(Deng](#page-13-3) [et al., 2009;](#page-13-3) [Ridnik et al., 2021\)](#page-14-3) at a conventional resolution of 224×224. This marks a pioneering effort in handling such a vast dataset for dataset distillation task. Our approach harnesses a straightforward yet effective global-to-local learning framework. We meticulously address each aspect and craft a robust strategy to effectively train on the complete ImageNet-21K, ensuring comprehensive knowledge is captured. Specifically, following a prior study [\(Yin et al., 2023\)](#page-15-1), our approach initially trains a model to encapsulate knowledge from the original datasets within its dense parameters. However, we introduce a refined training recipe that surpasses the results of [Ridnik et al.](#page-14-3) [\(2021\)](#page-14-3) on ImageNet-21K. During the data recovery/synthesis phase, we employ a strategic learning scheme where partial image crops are sequentially updated based on the difficulty of regions: transitioning either from simple to difficult, or vice versa. This progression is modulated by adjusting the lower and upper bounds

<span id="page-1-0"></span>Image /page/1/Figure/4 description: This image displays a progression of three sets of images, labeled "initial," "intermediate," and "final." Each set contains three distinct images arranged vertically. The top image in each set depicts a bird perched on a branch, with the "final" version showing more detail and color. The middle image in each set shows a fox-like creature lying down in foliage, with the "final" version exhibiting clearer features and vibrant colors, including a blue flower. The bottom image in each set presents an abstract, colorful, swirling pattern, with the "final" version appearing slightly more defined and vibrant than the initial and intermediate versions. Orange arrows indicate the transition from the "initial" to the "intermediate" and then to the "final" stage for each row.

Figure 2: Motivation of our work. The left column is the synthesized images after a few gradient update iterations from Gaussian noise. Middle and right columns are intermediate and final synthesized images.

of the *RandomReiszedCrop* data augmentation throughout varying training iterations. Remarkably, we observe that this straightforward learning approach substantially improves the quality of synthesized data. In this paper, we delve into three learning paradigms for data synthesis linked to the curriculum learning framework. The first is the standard curriculum learning, followed by its alternative approach, reverse curriculum learning. Lastly, we also consider the basic and previously employed method of constant learning.

**Motivation and Intuition.** We aim to maximize the global informativeness of the synthetic data. Both SRe<sup>2</sup>L [\(Yin et al., 2023\)](#page-15-1) and our proposed approach utilize local mini-batch data's mean and variance statistics to match the global statistics of the entire original dataset, synthesizing data by applying gradient updates directly to the image. The impact of such a strategy is that the initial few iterations set the stage for the global structure of the ultimately generated image, as shown in Figure [2.](#page-1-0) Building upon the insights derived from the analysis, we can leverage the *global-to-local* gradient refinement scheme for more expressive synthesized data, in contrast,  $SRe<sup>2</sup>L$  does not capitalize on this characteristic. Specifically, our proposed approach exploits this by initially employing large crops to capture a more accurate and complete outline of

<span id="page-2-0"></span>Image /page/2/Figure/1 description: The image displays a grid of 24 squares, arranged in three rows and eight columns. Each square contains a visual representation of data, with the top row showing clusters of colorful dots, the middle row displaying overlapping colored rectangles, and the bottom row also showing overlapping colored rectangles, but with fewer and larger rectangles. Above the top row, a horizontal gradient bar spans from the word "Hardest" on the left to "Easiest" on the right. Below each square in the top row, there is a numerical range, starting with [0.08, 0.20] and progressing to [0.80, 1.00] across the columns. The overall presentation suggests an illustration of varying difficulty levels or parameters, with corresponding visual outputs.

Figure 3: Illustration of crop distribution from different lower and upper bounds in *RandomResizedCrop*. The first row is the central points of bounding boxes from different sampling scale hyperparameters. The second and last rows correspond to 30 and 10 boxes of the crop distributions. In each row, from left to right, the difficulty of crop distribution is decreasing.

objects, for building a better foundation. As the process progresses, it incrementally reduces the crop size to enhance the finer, local details of the object, significantly elevating the quality of the synthesized data.

**Global-to-local via Curriculum Sampling.** *RandomResizedCrop* randomly crops the image to a certain area and then resizes it back to the pre-defined size, ensuring that the model is exposed to different regions and scales of the original image during training. As illustrated in Figure [3,](#page-2-0) the difficulty level of the cropped region can be controlled by specifying the lower and upper bounds for the area ratio of the crop. This can be used to ensure that certain portions of the image (small details or larger context) are present in the cropped region. If we aim to make the learning process more challenging, reduce the minimum crop ratio. This way, the model will often see only small portions of the image and will have to learn from those limited contexts. If we want the model to see a larger context more frequently, increase the minimum crop ratio. In this paper, we perform a comprehensive study on how the gradual difficulty changes by sampling strategy influence the optimization of data generation and the quality of synthetic data for dataset distillation. Our proposed curriculum data augmentation (CDA) is a heuristic and intuitive approach to simulate a *global-tolocal* learning procedure. Moreover, it is highly effective on large-scale datasets like ImageNet-1K and 21K, achieving state-of-the-art performance on dataset distillation.

**The Significance of Large-scale Dataset Condensation.** Large models trained on large-scale datasets consistently outperform smaller models and those trained on limited data [\(Dosovitskiy et al., 2020;](#page-13-4) [Dehghani](#page-13-5) [et al., 2023;](#page-13-5) [OpenAI, 2023\)](#page-14-1). Their ability to capture intricate patterns and understand nuanced contextual information makes them exceptionally effective across a wide range of tasks and domains. These models play a crucial role in solving complex industrial challenges and accelerating the development of AI-driven products and services, thereby contributing to economic growth and innovation. Therefore, adapting dataset condensation or distillation methods for large-scale data scenarios is vital to unlocking their full potential in both academic and industrial applications.

We conduct extensive experiments on the CIFAR, Tiny-ImageNet, ImageNet-1K, and ImageNet-21K datasets. Employing a resolution of  $224 \times 224$  and IPC 50 on ImageNet-1K, the proposed approach attains an impressive accuracy of 63.2%, surpassing all prior state-of-the-art methods by substantial margins. As illustrated in Figure [1,](#page-0-0) our proposed CDA outperforms SRe<sup>2</sup>L by 4∼6% across different architectures under 50 IPC, on both 1K and 4K recovery budgets. When tested on ImageNet-21K with IPC 20, our method achieves a top-1 accuracy of 35.3%, which is closely competitive, exhibiting only a minimal gap compared to the model pre-trained with full data, at  $44.5\%$ , while using  $50\times$  fewer training samples.

Our contributions of this work:

• We propose a new curriculum data augmentation (CDA) framework enabled by *global-to-local* gradient update in data synthesis for large-scale dataset distillation.

- We are the first to distill the ImageNet-21K dataset, which reduces the gap to its full-data training counterparts to less than an absolute 15% accuracy.
- We conduct extensive experiments on CIFAR-100, Tiny-ImageNet, ImageNet-1K and ImageNet-21K datasets to demonstrate the effectiveness of the proposed approach.

## **2 Related Work**

Dataset condensation or distillation strives to form a compact, synthetic dataset, retaining crucial information from the original large-scale dataset. This approach facilitates easier handling, reduces training time, and aims for performance comparable to using the full dataset. Prior solutions typically fall under four categories: *Meta-Model Matching* optimizes for model transferability on distilled data, with an outer-loop for synthetic data updates, and an inner-loop for network training, such as DD [\(Wang et al., 2020\)](#page-15-4), KIP [\(Nguyen](#page-14-4) [et al., 2021\)](#page-14-4), RFAD [\(Loo et al., 2022\)](#page-14-5), FRePo [\(Zhou et al., 2022\)](#page-16-1), LinBa [\(Deng & Russakovsky, 2022\)](#page-13-6), and MDC [\(He et al., 2024\)](#page-14-6); *Gradient Matching* performs a one-step distance matching between models, such as DC [\(Zhao et al., 2020\)](#page-16-0), DSA [\(Zhao & Bilen, 2021\)](#page-15-5), DCC [\(Lee et al., 2022\)](#page-14-7), IDC [\(Kim et al., 2022b\)](#page-14-8), and MP [\(Zhou et al., 2024a\)](#page-16-2); *Distribution Matching* directly matches the distribution of original and synthetic data with a single-level optimization, such as DM [\(Zhao & Bilen, 2023\)](#page-15-6), CAFE [\(Wang et al., 2022\)](#page-15-7), HaBa [\(Liu et al., 2022a\)](#page-14-9), KFS [\(Lee et al., 2022\)](#page-14-7), DataDAM [\(Sajedi et al., 2023\)](#page-15-8), FreD [Shin et al.](#page-15-9) [\(2024\)](#page-15-9), and GUARD [\(Xue et al., 2024\)](#page-15-10); *Trajectory Matching* matches the weight trajectories of models trained on original and synthetic data in multiple steps, methods include MTT [\(Cazenavette et al., 2022b\)](#page-13-0), TESLA [\(Cui](#page-13-1) [et al., 2023\)](#page-13-1), APM [\(Chen et al., 2023\)](#page-13-7), and DATM [\(Guo et al., 2024\)](#page-13-8).

Moreover, there are some recent methods out of these categories that have further improved the existing dataset distillation. SeqMatch [\(Du et al., 2023\)](#page-13-9) reorganizes the synthesized dataset during the distillation and evaluation phases to extract both low-level and high-level features from the real dataset, which can be integrated into existing dataset distillation methods. Deep Generative Prior [\(Cazenavette et al., 2023\)](#page-13-10) utilizes the learned prior from the pre-trained deep generative models to synthesize the distilled images. RDED [\(Sun et al., 2024\)](#page-15-11) proposes a non-optimization method to concatenate multiple cropped realistic patches from the original data to compose the distilled dataset. D3M [\(Abbasi et al., 2024\)](#page-12-1) condenses an entire category of images into a single textual prompt of latent diffusion models. SC-DD [\(Zhou et al.,](#page-16-3) [2024b\)](#page-16-3) proposes a self-supervised paradigm by applying the self-supervised pre-trained backbones for dataset distillation. EDC [\(Shao et al., 2024b\)](#page-15-12) explores a comprehensive design space that includes multiple specific, effective strategies like soft category-aware matching and learning rate schedule to establishe a benchmark for both small and large-scale dataset distillation. Ameliorate Bias [\(Cui et al., 2024\)](#page-13-11) studies the impact of bias within the original dataset on the performance of dataset condensation. It introduces a simple yet effective approach based on a sample reweighting scheme that utilizes kernel density estimation.

SRe<sup>2</sup>L [\(Yin et al., 2023\)](#page-15-1) is the first and mainstream framework to distill large-scale datasets, such as ImageNet-1K, and achieve significant performance. Thus, we consider it as our closest baseline. More specifically, SRe<sup>2</sup>L proposes a decoupling framework to avoid the bilevel optimization of model and synthesis during distillation, which consists of three stages of squeezing, recovering, and relabeling. In the first squeezing stage, a model is trained on the original dataset and serves as a frozen pre-train model in the following two stages. During the recovering stage, the distilled images are synthesized with the knowledge recovered from the pre-train model. At the last relabeling stage, the soft labels corresponding to synthetic images are generated and saved by leveraging the pre-train model. Recently, distilling on large-scale datasets has received significant attention in the community, and many works have been proposed, including [\(Sun et al.,](#page-15-13) [2023;](#page-15-13) [Liu et al., 2023;](#page-14-10) [Chen et al., 2023;](#page-13-7) [Shao et al., 2024a;](#page-15-14) [Zhou et al., 2024a;](#page-16-2) [Wu et al., 2024;](#page-15-15) [Abbasi](#page-12-1) [et al., 2024;](#page-12-1) [Zhou et al., 2024b;](#page-16-3) [Shao et al., 2024b;](#page-15-12) [Xue et al., 2024;](#page-15-10) [Qin et al., 2024;](#page-14-11) [Gu et al., 2023;](#page-13-12) [Ma](#page-14-12) [et al., 2024;](#page-14-12) [Shang et al., 2024\)](#page-15-16). Theses recent methods represent a comprehensive study and literature on framework design space [\(Shao et al., 2024b\)](#page-15-12) and adversarial robustness benchmarks [\(Wu et al., 2024\)](#page-15-15) in dataset distillation. They are substantially different from our input-optimization-based approach. Additionally, GUARD [\(Xue et al., 2024\)](#page-15-10) incorporates curvature regularization to embed adversarial robustness, focusing on a different objective than our CDA.

Compared to earlier traditional dataset distillation baselines, CDA is fundamentally different in its approach: (1) CDA exhibits better scalability. The previous works like DM [\(Zhao & Bilen, 2023\)](#page-15-6), DSA [\(Zhao & Bilen,](#page-15-5) [2021\)](#page-15-5), and FRePo [\(Zhou et al., 2022\)](#page-16-1), work well on small-scale dataset distillation, but they are limited by the huge computational cost and cannot be scaled to large datasets and models. (2) Different generation paradigms. MTT [\(Cazenavette et al., 2022a\)](#page-13-2) matches the model trajectories (weights) of training on distilled and raw datasets; RDED [\(Sun et al., 2023\)](#page-15-13) selects and combines the raw image patches with diversity; D3M [\(Abbasi et al., 2024\)](#page-12-1) leverages text-to-image diffusion models to generate distilled images. Thus, MTT proposes matching trajectories, RDED proposes non-optimizing, and D3M proposes diffusion-model-based generation paradigms which do not align with to our knowledge-distillation-based generation approach. (3) Unique evaluation recipes. For instance, RDED utilizes a unique smoothed LR schedule for the learning rate reduction throughout the evaluation, which improves evaluation performance effectively<sup>[1](#page-4-0)</sup>.

## **3 Approach**

#### **3.1 Preliminary: Dataset Distillation**

The goal of dataset distillation is to derive a concise synthetic dataset that maintains a significant proportion of the information contained in the original, much larger dataset. Suppose there is a large labeled dataset  $\mathcal{D}_o = \{(\boldsymbol{x}_1, \boldsymbol{y}_1), \dots, (\boldsymbol{x}_{|\mathcal{D}_o|}, \boldsymbol{y}_{|\mathcal{D}_o|})\},$  our target is to formulate a compact distilled dataset, represented as  $\mathcal{D}_d = \left\{ (\boldsymbol{x}'_1, \boldsymbol{y}'_1), \ldots, \left( \boldsymbol{x}'_{|\mathcal{D}_d|}, \boldsymbol{y}'_{|\mathcal{D}_d|} \right) \right\}$ , where  $\boldsymbol{y}'$  is the soft label corresponding to synthetic data  $\boldsymbol{x}'$ , and  $|\mathcal{D}_d| \ll |\mathcal{D}_o|$ , preserving the essential information from the original dataset  $\mathcal{D}_o$ . The learning objective based on this distilled synthetic dataset is:

$$
\boldsymbol{\theta}_{\mathcal{D}_d} = \underset{\boldsymbol{\theta}}{\arg\min} \mathcal{L}_{\mathcal{D}_d}(\boldsymbol{\theta})
$$
\n(1)

$$
\mathcal{L}_{\mathcal{D}_d}(\boldsymbol{\theta}) = \mathbb{E}_{(\mathbf{x'}, \mathbf{y'}) \in \mathcal{D}_d} \left[ \ell(\phi_{\boldsymbol{\theta}_{\mathcal{D}_d}}(\mathbf{x'}), \mathbf{y'}) \right]
$$
(2)

where  $\ell$  is the regular loss function such as the soft cross-entropy, and  $\phi_{\theta_{\mathcal{D}_d}}$  is model. The primary objective of the dataset distillation task is to generate synthetic data aimed at attaining a specific or minimal performance disparity on the original validation data when the same models are trained on the synthetic data and the original dataset, respectively. Thus, we aim to optimize the synthetic data  $\mathcal{D}_d$  by:

$$
\underset{\mathcal{D}_{d},|\mathcal{D}_{d}|}{\arg\min} \left( \sup \left\{ \left| \ell\left(\phi_{\boldsymbol{\theta}_{\mathcal{D}_{o}}}(\boldsymbol{x}_{val}),\boldsymbol{y}_{val}\right)-\ell\left(\phi_{\boldsymbol{\theta}_{\mathcal{D}_{d}}}(\boldsymbol{x}_{val}),\boldsymbol{y}_{val}\right) \right| \right\}_{(\boldsymbol{x}_{val},\boldsymbol{y}_{val})\sim\mathcal{D}_{o}} \right) \tag{3}
$$

where  $(x_{val}, y_{val})$  are sample and label pairs in the validation set of the real dataset  $\mathcal{D}_o$ . Then, we learn  $\langle$  data, label $\rangle \in \mathcal{D}_d$  with the corresponding number of distilled data in each class.

#### **3.2 Dataset Distillation on Large-scale Datasets**

Currently, the prevailing majority of research studies within dataset distillation mainly employ datasets of a scale up to ImageNet-1K [\(Cazenavette et al., 2022b;](#page-13-0) [Cui et al., 2023;](#page-13-1) [Yin et al., 2023\)](#page-15-1) as their benchmarking standards. In this work, we are the pioneer in showing how to construct a strong baseline on ImageNet-21K (the approach is equivalently applicable to ImageNet-1K) by incorporating insights presented in recent studies, complemented by conventional optimization techniques. Our proposed baseline is demonstrated to achieve state-of-the-art performance over prior counterparts. We believe this provides substantial significance towards understanding the true impact of proposed methodologies on dataset distillation task and towards assessing the true gap with full original data training. We further propose a curriculum training paradigm to achieve a more informative representation of synthetic data. Following prior work in dataset distillation [\(Yin](#page-15-1) [et al., 2023\)](#page-15-1), we focus on the decoupled training framework, *Squeeze-Recover-Relabel*, to save computation and memory consumption on large-scale ImageNet-21K, the procedures are listed below:

**Squeeze: Building A Strong Pre-trained Model on ImagNet-21K**. To obtain a squeezing model, we use a relatively large label smooth of 0.2 together with Cutout [\(DeVries & Taylor, 2017\)](#page-13-13) and RandAugment [\(Cubuk et al., 2020\)](#page-13-14), as shown in Appendix [B.4.](#page-20-0) This recipe helps achieve ∼2% improvement over the default training [\(Ridnik et al., 2021\)](#page-14-3) on ImageNet-21K, as provided in Table [21.](#page-21-0)

<span id="page-4-0"></span><sup>&</sup>lt;sup>1</sup>Therefore, to ensure a fair and straightforward comparison, these baseline results in our experimental section have been taken directly from the best evaluation performance reported by their original papers.

**Recover: Curriculum Training for Better Representation of Synthetic Data**. A well-crafted curriculum data augmentation is employed during the synthesis stage to realize the global-to-local learning scheme and enhance the representational capability of the synthetic data. This step is crucial, serving to enrich the generated images by embedding more knowledge accumulated from the original dataset, thereby making them more informative. Detailed procedures will be further described in the following Section [3.3.](#page-5-0)

**Relabel: Post-training on Larger Models with Stronger Training Recipes**. Prior studies, such as TESLA [\(Cui et al., 2023\)](#page-13-1), have encountered difficulties, particularly, a decline in accuracy when utilizing models of larger scale. The reason may be that the trajectory-based matching approaches, e.g., MTT and TESLA, generate images by excessively optimizing to align the dense training trajectories of model weights at each epoch between real and distilled datasets on specific backbone models. As a result, the distilled dataset becomes overly dependent on these models, potentially leading to overfitting and reduced effectiveness when training other models, particularly larger ones. This further suggests that the synthetic data used is potentially inadequate for training larger models. Conversely, the data we relabel show improvement with the use of larger models combined with enhanced post-training methodologies, displaying promise when applied to larger datasets in distillation processes.

We have also observed that maintaining a smaller batch size is crucial for post-training on synthetic data to achieve commendable accuracy. This is attributed to the *Generalization Gap* [\(Keskar et al., 2016;](#page-14-13) [Hoffer](#page-14-14) [et al., 2017\)](#page-14-14), which suggests that when there is a deficiency in the total training samples, the model's capacity to generalize to new, unseen data is not robust. In the context of synthetic data, the generalization gap can be exacerbated due to the inherent differences between synthetic and real data distributions. Smaller batch sizes tend to introduce more details/noises into the gradient updates during training, which, counterintuitively, can help in better generalizing to unseen data by avoiding overfitting to the synthetic dataset's general patterns. The noise can also act as a regularizer, preventing the model from becoming too confident in its predictions on the synthetic data, which may not fully capture the complexities of large batch-size data. Employing smaller batch sizes while training on the small-scale synthetic data allows models to explore the loss landscape more meticulously before converging to an optimal minimum. In Table [6,](#page-10-0) we empirically notice that utilizing a small batch size can improve model evaluation performance. This observed phenomenon aligns with the *Generalization Gap* theory, which arises when there is a lack of training samples.

#### <span id="page-5-0"></span>**3.3 Global-to-local Gradient Update via Curriculum**

In  $S\text{Re}^2L$  [\(Yin et al., 2023\)](#page-15-1) approach, the key of data synthesis revolves around utilizing the gradient information emanating from both the semantic class and the predictions of the pre-trained squeezing model, paired with BN distribution matching. Let  $(x, y)$  be an example x for optimization and its corresponding one-hot label *y* for the pre-trained squeezing model. Throughout the synthesis process, the squeezing model is frozen to recover the encoded information and ensure consistency and reliability in the generated data. Let  $\mathcal{T}(\boldsymbol{x})$  be the target training distribution from which the data synthesis process should ultimately learn a function of desired trajectories, where  $\mathcal T$  is a data transformation function to augment input samples to various levels of difficulties. Following [Bengio et al.](#page-12-2) [\(2009\)](#page-12-2), a weight  $0 \leq W_s(x) \leq 1$  is defined and applied to example **x** at stage *s* in the curriculum sequence. The training distribution  $D_s(x)$  is:

$$
D_s(x) \propto W_s(x)\mathcal{T}(x) \quad \forall x \tag{4}
$$

In our scenario, since the varying difficulties are governed by the data transformation function  $\mathcal{T}$ , we can straightforwardly employ  $W_s(x) = 1$  across all stages. Consequently, the training distribution solely depends on  $\mathcal{T}(x)$  and can be simplified as follows:

$$
D(x) \propto \mathcal{T}(x) \quad \forall x \tag{5}
$$

By integrating curriculum learning within the data synthesis phase, this procedure can be defined as:

**Definition 1** (Curriculum Data Synthesis)**.** *In the data synthesis optimization, the corresponding sequence of distributions*  $D(x)$  *will be a curriculum if there is an increment in the entropy of these distributions, i.e., the difficulty of the transformed input samples escalates and becomes increasingly challenging for the pre-trained model to predict as the training progresses.*

to design  $\mathcal{T}(x)$  across different training iterations. The follow-Thus, the key for our curriculum data synthesis becomes how ing section discusses several strategies to construct this in the curriculum scheme.

equally. Each sample from the training dataset has an equal **Baseline: Constant Learning (CTL)**. This is the regular training method where all training examples are typically treated chance of being transformed in a given batch, assuming no difficulty imbalance or biases across different training iterations.

CTL is straightforward to implement since we do not have to rank or organize examples based on difficulty. In practice, we use *RandomResizedCrop* to crop a small region via current crop ratio randomly sampled from a given interval [min\_crop*,* max\_crop] and then resize the cropped image to its original size, formulated as follows:

$$
x_{\mathcal{T}} \leftarrow RandomResizedCrop(x_s, \text{min\_crop} = \alpha_l, \text{max\_crop} = \alpha_u)
$$
  
(6)

where  $\alpha_l$  and  $\alpha_u$  are the constant lower and upper bounds of crop scale.

**Curriculum Learning (CL)**. As shown in Algorithm [1,](#page-7-0) in our CL, data samples are organized based on their difficulty. The difficulty level of the cropped region can be managed by defining the lower and upper scopes for the area ratio of the crop. This enables the assurance that specific crops of the image (small details or broader context) are included in the cropped region. For the difficulty adjustment, the rate at which more difficult examples

are introduced and the criteria used to define difficulty are adjusted dynamically as predetermined using the following schedulers.

*Step.* Step scheduler reduces the minimal scale by a factor for every fixed or specified number of iterations, as shown in Figure [5](#page-6-0) right.

Image /page/6/Figure/9 description: The image contains text describing a linear scheduler. The text states that a linear scheduler begins with a high initial value and then decreases this value linearly by a factor gamma to a minimum value throughout the entire training process.

*Cosine*. Cosine scheduler modulates the distribution according to the cosine function of the current iteration number, yielding a smoother and more gradual adjustment compared to step-based methods.

As shown in Figure [5,](#page-6-0) the factor distribution manages the difficulty level of crops with adjustable  $\alpha_u$  and  $\alpha_l$ for CTL and milestone for CL.

### **Data Synthesis by Recovering and Relabeling.**

After receiving the transformed input  $x_{\mathcal{T}}$ , we update it by aligning between the final classification label and intermediate Batch Normalization (BN) statistics, i.e., mean and variance from the original data. This stage forces the synthesized images to capture a shape of the original image distribution. The learning goal for this stage can be formulated as follows:

<span id="page-6-2"></span>
$$
\mathbf{x}'_{\mathcal{T}} = \arg\min \ell\left(\phi_{\theta}\left(\mathbf{x}_{\mathcal{T}}\right), \mathbf{y}\right) + \mathcal{R}_{\text{reg}}\tag{7}
$$

where  $\phi_{\theta}$  is the pre-trained squeezing model and will be frozen in this stage. During synthesis, only the input crop area will be updated by the gradient from the objective. The entire training procedure is illustrated in Figure [4.](#page-6-1) After synthesizing the data, we follow the relabeling process in  $SRe^{2}L$  to generate soft labels using

<span id="page-6-1"></span>Image /page/6/Figure/16 description: This diagram illustrates a curriculum learning process for image generation. It begins with an 'initial' noisy image. The process involves sequential steps, labeled Crop(1), Crop(2), ..., Crop(S), within a 'Curriculum' framework. Each crop step involves selecting a region of the image, indicated by nested squares. Below each crop step, an 'Opt(s)' optimizer is applied, represented by green boxes with arrows indicating progression. The output of each optimizer is a progressively clearer image, shown below the optimizer boxes. The final output is labeled 'synthetic', depicting a clear image of a rooster. A legend at the bottom indicates that 'Opt(s)' represents the optimizer at step s.

Figure 4: Illustration of global-to-local data synthesis. This figure shows our specific curriculum procedure in data synthesis to provide a comprehensive overview of our dataset distillation framework. It starts with a large area (single boundingbox in each step) to optimize the image, building a better initialization, and then gradually narrows down the image area of learning process so that it can focus on more detailed areas.

<span id="page-6-0"></span>Image /page/6/Figure/18 description: The image contains two plots. The left plot shows the relationship between distillation progress (in %) on the x-axis and crop ratio on the y-axis. The y-axis ranges from 0.08 to 1. The plot displays a green gradient from a lighter shade at the bottom to a darker shade at the top, spanning the entire distillation progress range. There are shaded regions at the top and bottom, indicating upper and lower bounds for the crop ratio, labeled as \u03b1\_u and \u03b1\_l respectively. The right plot also shows distillation progress (%) on the x-axis, ranging from 0 to 100. The y-axis is not explicitly labeled but appears to represent a value that decreases from 1 to 0. This plot shows three curves: a peach-colored 'Step' curve, a green 'Linear' curve, and a blue 'Cosine' curve. All three curves start at a value of 1 at 0% distillation progress and decrease to a value of 0 at approximately 50% distillation progress. The 'Step' curve remains at 1 until around 50% and then drops to 0. The 'Linear' and 'Cosine' curves show a more gradual decrease. The term 'milestone' is indicated on the x-axis, likely around the point where the curves drop to 0.

Figure 5: Crop ratio schedulers of prior CTL solution (left) and our *Global-to-local* (right) enabled by curriculum. The colored regions depict the random sampling intervals for the crop ratio value in each iteration under different schedulers.

**Algorithm 1:** Our CDA via *RandomResizedCrop*

<span id="page-7-0"></span>**Input:** squeezed model  $\phi_{\theta}$ , recovery iteration *S*, curriculum milestone *T*, target label *y*, default lower and upper bounds of crop scale  $\beta_l$  and  $\beta_u$  in *RandomResizedCrop*, decay of lower scale bound  $\gamma$ **Output:** synthetic image *x*

**Initialize:**  $x_0$  from a standard normal distribution

**for** step *s* from 0 to *S*-1 **do**

**if**  $s \leq T$  **then** *α* ←  $\sqrt{ }$  $\int$  $\overline{\mathcal{L}}$  $\beta$ u if step  $\beta_l + \gamma * (\beta_u - s/T)$  if linear  $\beta_l + \gamma * (\beta_u + \cos(\pi * s/T))/2$  if cosine **else** *α* ← *β<sup>l</sup>* **end**  $\boldsymbol{x}_{\mathcal{T}} \leftarrow RandomResizedCrop(\boldsymbol{x}_s, \texttt{min\_crop} = \alpha, \texttt{max\_crop} = \beta_u)$  $x'_{\mathcal{T}} \leftarrow x_{\mathcal{T}}$  is optimized w.r.t  $\phi_{\theta}$  and  $y$  in Eq. [7.](#page-6-2)  $\boldsymbol{x}_{s+1} \leftarrow ReverseRandomResizedCrop(\boldsymbol{x}_s, \boldsymbol{x}'_{\mathcal{T}})$ **end**  $\mathbf{return }\ x \leftarrow x_S$ 

FKD [\(Shen & Xing, 2022\)](#page-15-17) with the integration of the small batch size setting for post-training.  $\mathcal{R}_{\text{reg}}$  is the regularization term used in [Yin et al.](#page-15-1) [\(2023\)](#page-15-1), its detailed formulation using channel-wise mean and variance matching is:

\mathcal{R}\_{\text{reg}}\left(\boldsymbol{x}^{\prime}\right) = \sum\_{k} \left\| \mu\_{k}\left(\boldsymbol{x}^{\prime}\right) - \mathbb{E}\left(\mu\_{k} \mid \mathcal{D}\_{o}\right) \right\|\_{2} + \sum\_{k} \left\| \sigma\_{l}^{2}\left(\boldsymbol{x}^{\prime}\right) - \mathbb{E}\left(\sigma\_{k}^{2} \mid \mathcal{D}\_{o}\right) \right\|\_{2}

\approx \sum\_{k} \left\| \mu\_{k}\left(\boldsymbol{x}^{\prime}\right) - \mathbf{BN}\_{k}^{\text{RM}} \right\|\_{2} + \sum\_{k} \left\| \sigma\_{k}^{2}\left(\boldsymbol{x}^{\prime}\right) - \mathbf{BN}\_{k}^{\text{RV}} \right\|\_{2} \quad (8)

where *k* is the index of BN layer,  $\mu_k(\mathbf{x}')$  and  $\sigma_k^2(\mathbf{x}')$  are the channel-wise mean and variance in current batch data.  $BN_k^{\rm RM}$  and  $BN_k^{\rm RV}$  are mean and variance in the pre-trained model at *k*-th BN layer, which are globally counted.

**Advantages of Global-to-local Synthesis**. The proposed CDA enjoys several advantages: **(1)** Stabilized training: Curriculum synthesis can provide a more stable training process as it reduces drastic loss fluctuations that can occur when the learning procedure encounters a challenging sample early on. **(2)** Better generalization: By gradually increasing the difficulty, the synthetic data can potentially achieve better generalization on diverse model architectures in post-training. It reduces the chance of the synthesis getting stuck in poor local minima early in the training process. **(3)** Avoid overfitting: By ensuring that the synthetic data is well-tuned on simpler examples before encountering outliers or more challenging data, there is a potential to reduce overfitting. Specifically, *better generalization* here refers to the ability of models trained on the distilled datasets to perform well across a wider range of evaluation scenarios. However, *avoiding overfitting* particularly refers to our curriculum strategy during the distillation process, where we use a flexible region update in each iteration to prevent overfitting that could occur with a fixed region update.

### **4 Experiments**

#### **4.1 Datasets and Implementation Details**

We verify the effectiveness of our approach on small-scale CIFAR-100 and various ImageNet scale datasets, including Tiny-ImageNet [\(Le & Yang, 2015\)](#page-14-15), ImageNet-1K [\(Deng et al., 2009\)](#page-13-3), and ImageNet-21K [\(Ridnik](#page-14-3) [et al., 2021\)](#page-14-3). For evaluation, we train models from scratch on synthetic distilled datasets and report the Top-1 accuracy on real validation datasets. Default lower and upper bounds of crop scales *β<sup>l</sup>* and *β<sup>u</sup>* are 0.08 and 1.0, respectively. The decay  $\gamma$  is 0.92. In Curriculum Learning (CL) settings, the actual lower bound is dynamically adjusted to control difficulty, whereas the upper bound is fixed to the default value of 1.0 to

<span id="page-8-1"></span>

| Dataset      |                | $CIFAR-100$    |                  | Tiny-ImageNet  |                |                  | $ImageNet-1K$  |                |                | $ImageNet-21K$   |                 |
|--------------|----------------|----------------|------------------|----------------|----------------|------------------|----------------|----------------|----------------|------------------|-----------------|
| <b>IPC</b>   | 10             | 50             | 10               | 50             | 100            | 10               | 50             | 100            | 200            | 10               | 20              |
| Ratio $(\%)$ | $\overline{2}$ | 10             | $\overline{2}$   | 10             | 20             | 0.8              | 4              | 8              | 16             | 0.8              | 1.6             |
| DM           | $29.7 \pm 0.3$ | $43.6 \pm 0.4$ | $12.9 \pm 0.4$   | $24.1 \pm 0.3$ |                | $5.7 \pm 0.1$    | $11.4 \pm 0.9$ | $\sim$         | ۰              | ÷                |                 |
| DSA          | $32.3 \pm 0.3$ | $42.8 \pm 0.4$ |                  |                |                |                  | ۰              | ۰              | ۰              |                  |                 |
| FRePo        | $42.5 \pm 0.2$ | $44.3 \pm 0.2$ | $25.4 \pm 0.2$   |                |                |                  | ۰              | ۰              | ۰              |                  |                 |
| MTT          | $39.7 \pm 0.4$ | $47.7 \pm 0.2$ | $23.2 \pm 0.2$   | $28.0 \pm 0.3$ | $\sim$         |                  | ۰              | $\sim$         | ۰              |                  |                 |
| DataDAM      | $34.8 \pm 0.5$ | $49.4 \pm 0.3$ | $18.7 \pm 0.3$   | $28.7 \pm 0.3$ | $\sim$         | $6.3 \pm 0.0$    | $15.5 \pm 0.2$ | ۰              | ۰              |                  |                 |
| <b>TESLA</b> | $41.7 \pm 0.3$ | $47.9 \pm 0.3$ | ۰                |                | $\sim$         | $17.8 \pm 1.3$   | $27.9 \pm 1.2$ |                | ۰              |                  |                 |
| DATM         | $47.2 \pm 0.4$ | $55.0 \pm 0.2$ | $31.1 \pm 0.3$   | $39.7 \pm 0.3$ | ÷              |                  | ۰              | $\sim$         | ۰              |                  |                 |
| Full Dataset |                | 79.1           |                  | 61.2           |                |                  | 69.8           |                |                | 38.5             |                 |
| $SRe^2L$     | $23.5 \pm 0.8$ | $51.4 \pm 0.8$ | $17.7 \pm 0.7^*$ | $41.1 \pm 0.4$ | $49.7 \pm 0.3$ | $21.3 \pm 0.6^*$ | $46.8 \pm 0.2$ | $52.8 \pm 0.4$ | $57.0 \pm 0.3$ | $18.5 \pm 0.2^*$ | $21.8 \pm 0.1*$ |
| $CDA$ (Ours) | $49.8 \pm 0.6$ | $64.4 \pm 0.5$ | $21.3 \pm 0.3$   | $48.7 \pm 0.1$ | $53.2 \pm 0.1$ | $33.5 \pm 0.3$   | $53.5 \pm 0.3$ | $58.0 \pm 0.2$ | $63.3 \pm 0.2$ | $22.6 \pm 0.2$   | $26.4 \pm 0.1$  |

Table 1: Comparison with state-of-the-art methods on various datasets.

<sup>∗</sup> Replicated experiment results are marked with <sup>∗</sup> , while the other baseline results are referenced from original papers.

<sup>1</sup> The full dataset results refer to Top-1 val accuracy achieved by a ResNet-18 model trained on the full dataset, the architecture is the same as  $SRe^2L$  and our CDA.

ensure there is a probability of cropping and optimizing the entire image in any progress. More details are provided in the Appendix [B.](#page-17-0)

#### **4.2 CIFAR-100**

Result comparisons with baseline methods, including DM [\(Zhao & Bilen, 2023\)](#page-15-6), DSA [\(Zhao & Bilen, 2021\)](#page-15-5), FRePo [\(Zhou](#page-16-1) [et al., 2022\)](#page-16-1), MTT [\(Cazenavette et al., 2022b\)](#page-13-0), DataDAM [\(Sajedi et al., 2023\)](#page-15-8), TESLA [\(Cui](#page-13-1) [et al., 2023\)](#page-13-1), DATM [\(Guo et al., 2024\)](#page-13-8), and

<span id="page-8-0"></span>

| CIFAR-100 (IPC) DC DSA DM MTT SRe <sup>2</sup> L Ours |                             |                |                  |      |
|-------------------------------------------------------|-----------------------------|----------------|------------------|------|
|                                                       | $12.8$ $13.9$ $11.4$ $24.3$ |                | $\sim$ 100 $\mu$ | 13.4 |
| 10                                                    | $25.2$ $32.3$ $29.7$ $40.1$ |                | $\sim$           | 49.8 |
| 50                                                    |                             | 42.8 43.6 47.7 | - 49.4           | 64.4 |

Table 2: Comparison on CIFAR-100.

SRe<sup>2</sup>L [\(Yin et al., 2023\)](#page-15-1) on CIFAR-100 are presented in Table [2](#page-8-0) and Table [1.](#page-8-1) Our model is trained with an 800ep budget. It can be observed that our CDA validation accuracy outperforms all baselines under 10 and 50 IPC. And our reported results have the potential to be further improved as training budgets increase. Overall, our CDA method is also applicable to small-scale dataset distillation.

#### **4.3 Tiny-ImageNet**

Results on the Tiny-ImageNet dataset are detailed in the second group of Table [1](#page-8-1) and the first group of Table [4.](#page-9-0) Our CDA outperforms all baselines except DATM under 10 IPC. Compared to  $SRe^{2}L$ , our CDA achieves average improvements of 7.7% and 3.4% under IPC 50 and IPC 100 settings across ResNet-{18, 50, 101} validation models, respectively. Importantly, CDA stands as the inaugural approach to diminish the Top-1 accuracy performance disparity to less than 10% between the distilled dataset employing IPC 100 and the full Tiny-ImageNet, signifying a breakthrough on this dataset.

#### **4.4 ImageNet-1K**

<span id="page-8-2"></span>Table 3: Constant learning result. *α<sup>l</sup>* and *α<sup>u</sup>* stand for the min\_crop and max\_crop parameters in *Random-ResizedCrop*.<sup> $\ddagger$ </sup> represents the results from SRe<sup>2</sup>L implementation but following the setting in the table.

| Constant learning type $\setminus \alpha$             | 0.08   | 0.2   | 0.4   | 0.6   | 0.8   | 1.0   |
|-------------------------------------------------------|--------|-------|-------|-------|-------|-------|
| Easy $(\alpha_l = \alpha, \alpha_u = \beta_u (1.0))$  | 44.90‡ | 47.88 | 46.34 | 45.35 | 43.48 | 41.30 |
| Hard $(\alpha_l = \beta_l (0.08), \alpha_u = \alpha)$ | 22.99  | 34.75 | 42.76 | 44.61 | 45.76 | 44.90 |

**Constant Learning (CTL)**. We leverage a ResNet-18 and employ synthesized data with 1K recovery iterations. As observed in Table [3,](#page-8-2) the results for exceedingly straightforward or challenging scenarios fall below the reproduced SRe<sup>2</sup>L baseline accuracy of 44.90%, especially when  $\alpha \geq 0.8$  in *easy* and  $\alpha \leq 0.4$  in *hard* type. Thus, the results presented in Table [3](#page-8-2) suggest that adopting a larger cropped range assists in circumventing extreme scenarios, whether easy or hard, culminating in enhanced performance. A noteworthy

<span id="page-9-0"></span>

| Dataset | IPC | ResNet-18 |            | ResNet-50 |            | ResNet-101 |            |
|---------|-----|-----------|------------|-----------|------------|------------|------------|
|         |     | SRe²L     | Ours       | SRe²L     | Ours       | SRe²L      | Ours       |
| Tiny-IN | 50  | 41.1      | $48.7+7.6$ | 42.2      | $49.7†7.5$ | 42.5       | $50.6↑8.1$ |
|         | 100 | 49.7      | $53.2↑3.5$ | 51.2      | $54.4+3.2$ | 51.5       | $55.0↑3.5$ |
| IN-1K   | 50  | 46.8      | $53.5+6.7$ | 55.6      | $61.3+5.7$ | 57.6       | $61.6+4.0$ |
|         | 100 | 52.8      | $58.0↑5.2$ | 61.0      | $65.1↑4.1$ | 62.8       | $65.9↑3.1$ |
|         | 200 | 57.0      | $63.3↑6.3$ | 64.6      | $67.6↑3.0$ | 65.9       | $68.4↑2.5$ |
| IN-21K  | 10  | 18.5      | $22.6+4.1$ | 27.4      | $32.4↑5.0$ | 27.3       | $34.2+6.9$ |
|         | 20  | 21.8      | $26.4↑4.6$ | 31.3      | $35.3+4.0$ | 33.2       | $36.1↑2.9$ |

Table 4: Comparison with baseline on various datasets.

observation is the crucial role of appropriate lower and upper bounds for constant learning in boosting validation accuracy. This highlights the importance of employing curriculum data augmentation strategies in data synthesis.

**Curriculum Learning (CL).** We follow the recovery recipe of  $\text{SRe}^2L$ 's best result for 4K recovery iterations. As illustrated in Table [1](#page-8-1) and the second group of Table [4,](#page-9-0) when compared to the strong baseline  $SRe^{2}L$ , CDA enhances the validation accuracy, exhibiting average margins of  $6.1\%$ ,  $4.3\%$ , and  $3.2\%$  on ResNet- $\{18,$ 50, 101} across varying IPC settings. Furthermore, as shown in Figure [1,](#page-0-0) the results achieve with our CDA utilizing merely 1K recovery iterations surpass those of  $\text{SRe}^2L$  encompassing the entire 4K iterations. These results substantiate the efficacy and effectiveness of applying CDA in large-scale dataset distillation.

#### **4.5 ImageNet-21K**

**Pre-training Results.** Table [21](#page-21-0) of the Appendix presents the accuracy for ResNet-18 and ResNet-50 on ImageNet-21K-P, considering varying initial weight configurations. Models pre-trained by us and initialized with ImageNet-1K weight exhibit commendable accuracy, showing a 2.0% improvement, while models initialized randomly achieve marginally superior accuracy. We utilize these pre-trained models to recover ImageNet-21K data and to assign labels to the synthetic images generated. An intriguing observation is the heightened difficulty in data recovering from pre-trained models that are initialized randomly compared to those initialized with ImageNet-1K weight. Thus, our experiments employ CDA specifically on pre-trained models that are initialized with ImageNet-1K weight.

**Validation Results.** As illustrated in Table [1](#page-8-1) and the final group of Table [4,](#page-9-0) we perform validation experiments on the distilled ImageNet-21K employing IPC 10 and 20. This yields an extreme compression ratio of  $100\times$  and  $50\times$ . When applying IPC 10, i.e., the models are trained utilizing a distilled dataset that is a mere 1% of the full dataset. Remarkably, validation accuracy surpasses 20% and 30% on ResNet-18 and ResNet- $\{50, 101\}$ , respectively. Compared to reproduced  $SRe<sup>2</sup>L$  on ImageNet-21K, our approach attains an elevation of 5.3% on average under IPC 10/20. This not only highlights the efficacy of our approach in maintaining dataset essence despite high compression but also showcases the potential advancements in accuracy over existing methods.

#### <span id="page-9-1"></span>**4.6 Ablations**

Table 5: Applicability of our proposed method on SC-DD [\(Zhou et al., 2024a\)](#page-16-2).

| Method                    | Tiny-ImageNet        | $ImageNet-1K$                              |
|---------------------------|----------------------|--------------------------------------------|
| SC-DD                     | 45.5                 | -53.1                                      |
| $SC-DD+Curriculum (Ours)$ | $46.5^{\text{+1.0}}$ | $54.0$ <sup><math>\uparrow</math>0.9</sup> |

**Applicability.** Our method is designed to be applicable to general decoupled dataset distillation approaches. To demonstrate this, we apply our approach to the self-supervised SC-DD [\(Zhou et al., 2024b\)](#page-16-3). As shown in Table [5,](#page-9-1) our method achieves 1.0 and 0.9 improvements on Tiny-ImageNet and ImageNet-1K, respectively.

**Curriculum Scheduler.** To schedule the global-to-local learning, we present three distinct types of curriculum schedulers, *step*, *linear*, and *cosine* to manipulate the lower bounds on data cropped augmentation. As illustrated in Figure [5,](#page-6-0) the dataset distillation progress is divided into two phases by a milestone. It is observed that both *linear* and *cosine* with continuous decay manifest robustness across diverse milestone configurations and reveal a trend of enhancing accuracy performance when the milestone is met at a later phase, as shown in Figure [6.](#page-10-1) Moreover, *cosine* marginally outperforms *linear* in terms of accuracy towards the end. Consequently, we choose to implement the *cosine* scheduler, assigning a milestone percentage of 1.0, to modulate the minimum crop ratio adhering to the principles of curriculum learning throughout the progression of synthesis.

<span id="page-10-1"></span>Image /page/10/Figure/2 description: The image is a bar chart showing the Top-1 Accuracy (%) on the y-axis against Milestone Percentage (%) on the x-axis. The x-axis ranges from 10 to 100 in increments of 10. The y-axis ranges from 41 to 48. There are three sets of bars representing 'Step', 'Linear', and 'Cosine' learning rate schedules. At 10% milestone, all three schedules have an accuracy of approximately 45.5%. At 20%, Step is at 46.2%, Linear is at 46.3%, and Cosine is at 46.3%. At 30%, Step is at 46.5%, Linear is at 46.7%, and Cosine is at 46.4%. At 40%, Step is at 46.8%, Linear is at 46.9%, and Cosine is at 46.7%. At 50%, Step is at 46.5%, Linear is at 47.1%, and Cosine is at 47.0%. At 60%, Step is at 45.5%, Linear is at 47.4%, and Cosine is at 47.3%. At 70%, Step is at 45.2%, Linear is at 47.5%, and Cosine is at 47.4%. At 80%, Step is at 45.0%, Linear is at 47.7%, and Cosine is at 47.5%. At 90%, Step is at 42.3%, Linear is at 48.0%, and Cosine is at 47.9%. At 100%, Step is at 41.1%, Linear is at 48.0%, and Cosine is at 48.0%.

Figure 6: Ablation study on three different schedulers with varied milestone settings. Each number is obtained by averaging repeated experiments with three different seeds. The figure shows that the difference between linear and cosine schedulers is marginal and the best result for linear is at the milestone of 90% while the cosine scheduler performs similarly or better in the end. To avoid manually setting the milestone percentage for the linear scheduler, we adopt the cosine scheduler with a milestone percentage of 100% in our experiments.

**Batch Size in Post-training.** We perform an ablation study to assess the influence of utilizing smaller batch sizes on the generalization performance of models when the synthetic data is limited. We report results on the distilled ImageNet-21K from ResNet-18. In Table [6,](#page-10-0) a rise in validation accuracy is observed as batch size reduces, peaking at 16. This suggests that smaller batch sizes enhance performance on small-scale synthetic datasets. However, this leads to more frequent data loading and lower GPU utilization in our case, extending training times. To balance training time with performance, we chose a batch size of 32 for our experiments.

#### **4.7 Analysis**

**Cross-Model Generalization**. The challenge of ensuring distilled datasets generalize effectively across models unseen during the recovery phase remains significant, as in prior approaches [\(Zhao et al., 2020;](#page-16-0) [Cazenavette et al., 2022a\)](#page-13-2), synthetic images were optimized to overfit the recovery model. In the first group of Table [7,](#page-11-0) we deploy our ImageNet-1K distilled datasets to train various validation models, and we attain over 60% Top-1 accuracy with most of these models. Additionally, our performance in Top-1 accuracy surpasses that of  $SRe<sup>2</sup>L$  across all validation models spanning various architectures. Although DeiT-Tiny is not validated with a comparable Top-1 accuracy to other CNN models due to the ViT's inherent characteristic requiring more training data, CDA achieves double cross-model gen-

<span id="page-10-0"></span>

| Table 6: Ablation on      |
|---------------------------|
| batch size in validation. |

| Batch Size | Acc. $(\%)$  |
|------------|--------------|
| 128        | 20.79        |
| 64         | 21.85        |
| 32         | 22.54        |
| 16         | <b>22.75</b> |
| 8          | 22.41        |

eration performance on the DeiT-Tiny validation model, compared with  $\text{SRe}^2L$ . More validation models on distilled ImageNet-1K are included in Table [19](#page-20-1) of the Appendix. The second group of Table [7](#page-11-0) supports further empirical substantiation of the CDA's efficacy in the distillation of large-scale ImageNet-21K datasets. The results demonstrate that the CDA's distilled datasets exhibit reduced dependency on specific recovery models, thereby further alleviating the overfitting optimization issues.

| Dataset | Method     | Validation Model |              |              |              |              |               |              |
|---------|------------|------------------|--------------|--------------|--------------|--------------|---------------|--------------|
|         |            | R18              | <b>R50</b>   | R101         | DenseNet-121 | RegNet-Y-8GF | ConvNeXt-Tiny | DeiT-Tiny    |
| IN-1K   | SRe2L      | 46.80            | 55.60        | 57.60        | 49.74        | 60.34        | 53.53         | 15.41        |
|         | CDA (ours) | <b>53.45</b>     | <b>61.26</b> | <b>61.57</b> | <b>57.35</b> | <b>63.22</b> | <b>62.58</b>  | <b>31.95</b> |
| IN-21K  | SRe2L      | 21.83            | 31.26        | 33.24        | 24.66        | 34.22        | 34.95         | 15.76        |
|         | CDA (ours) | <b>26.42</b>     | <b>35.32</b> | <b>36.12</b> | <b>28.66</b> | <b>36.13</b> | <b>36.31</b>  | <b>18.56</b> |

<span id="page-11-0"></span>Table 7: Cross-model generation on distilled ImageNet-1K with 50 IPC and ImageNet-21K with 20 IPC.

**Impact of Curriculum**. To study the curriculum's advantage on synthetic image characteristics, we evaluate the Top-1 accuracy on CDA, SRe<sup>2</sup>L and real ImageNet-1K training set, using a mean of random 10-crop and global images. We employ PyTorch's pre-trained MobileNet-V2 to classify these images. As shown in Table [8,](#page-11-1) CDA images closely resemble real ImageNet images in prediction accuracies, better than SRe<sup>2</sup>L. Consequently, curriculum data augmentation improves global image prediction and reduces bias and overfitting post-training on simpler, cropped images of SRe<sup>2</sup>L.

**Visualization and Discussion**. Figure [7](#page-11-2) provides a comparative visualization of the gradient synthetic images at recovery steps of {100, 500, 1K, 2K} to illustrate the differences between SRe<sup>2</sup>L and CDA within the dataset distillation process.  $SRe<sup>2</sup>L$  images in the upper line exhibit a significant amount of noise, indicating a slow recovery progression in the early recovery stage. On the contrary, due to the mostly entire image optimization in the early stage, CDA images in the lower line can establish the layout of the entire image and reduce noise rapidly. And the final synthetic images contain more visual

<span id="page-11-1"></span>Table 8: Classification accuracy using MobileNet-V2.

| Top-1 (%) | Dataset |            |       |
|-----------|---------|------------|-------|
|           | SRe2L   | CDA (ours) | Real  |
| global    | 79.34   | 81.25      | 82.16 |
| cropped   | 87.48   | 82.44      | 72.73 |

<span id="page-11-2"></span>Image /page/11/Picture/7 description: The image displays a grid of eight smaller images, arranged in two rows and four columns. Each smaller image features a close-up view of a plant with purple and green leaves, set against a sandy or rocky background. The images appear to be variations or processed versions of the same subject, with subtle differences in color saturation, focus, and artistic effects, possibly indicating different stages of image processing or analysis.

Figure 7: Synthetic ImageNet-21K images (*Plant*).

information directly related to the target class *Plant*. Therefore, the comparison highlights CDA's ability to synthesize images with enhanced visual coherence to the target class, offering a more efficient recovery process. More visualizations are provided in Appendix [D.](#page-21-1)

**Synthesis Cost**. We highlight that there is no additional synthesis cost incurred in our CDA to  $S\text{Re}^2L$  [\(Yin](#page-15-1) [et al., 2023\)](#page-15-1) under the same recovery iteration setting. Specifically, for ImageNet-1K, it takes about 29 hours to generate the distilled ImageNet-1K with 50 IPC on a single A100 (40G) GPU and the peak GPU memory utilization is 6.7GB. For ImageNet-21K, it takes 11 hours to generate ImageNet-21K images per IPC on a single RTX 4090 GPU and the peak GPU memory utilization is 15GB. In our experiment, it takes about 55 hours to generate the entire distilled ImageNet-21K with 20 IPC on  $4 \times RTX$  4090 GPUs in total. Selecting representative real images for input data initialization instead of the Gaussian noise initialization will be an effective way to further accelerate image distillation and reduce synthesis costs. It can potentially reduce the number of recovery iterations required to achieve high-quality distilled data and this approach could lead to faster convergence and lower synthesis costs. Nevertheless, we present our detailed training time on several tested models, as shown in Table [9.](#page-11-3)

Table 9: Detailed training time on different models.

<span id="page-11-3"></span>

| Model                          | ResNet-18 | ResNet-50 | ResNet-101 | DenseNet-121 | RegNet-Y-8GF | ConvNeXt-Tiny | DeiT-Tiny |
|--------------------------------|-----------|-----------|------------|--------------|--------------|---------------|-----------|
| Training time (A100 GPU hours) | 2.3       | 7.1       | 12.5       | 9.1          | 13.6         | 18.3          | 4.6       |

#### **4.8 Application: Continual Learning**

The distilled datasets, comprising high-semantic images, possess a boosted representation capacity compared to the original datasets. This attribute can be strategically harnessed to combat catastrophic forgetting in continual learning. We have further validated the effectiveness of our introduced CDA synthesis within various continual learning scenarios. Following the setting introduced in  $S\text{Re}^{2}L$  [\(Yin et al., 2023\)](#page-15-1), we conducted 5-step and 10-step class-incremental experiments on Tiny-ImageNet, aligning our results against the baseline SRe<sup>2</sup>L and a randomly selected subset on Tiny-ImageNet for comparative analysis. As illustrated in Figure [8,](#page-12-3) our CDA distilled dataset notably surpasses  $\text{SRe}^2L$ , exhibiting an average advantage of 3.8% and 4.5% on 5-step and 10-step class-incremental learning assignments respectively. This demonstrates the substantial benefits inherent in the generation of CDA, particularly in mitigating the complexities associated with continual learning.

<span id="page-12-3"></span>Image /page/12/Figure/2 description: The image contains two line graphs side-by-side, both plotting Top-1 Accuracy (%) against the Number of classes. The left graph shows data points for 'CDA' (red squares) and 'SRe^2L' (blue diamonds) with the x-axis ranging from 40 to 200 in increments of 40, and the y-axis ranging from 35 to 55. The 'CDA' line starts at approximately 52% accuracy at 40 classes, rises to about 53.5% at 80 classes, and then slightly decreases to around 52.5% at 160 and 200 classes. The 'SRe^2L' line starts at approximately 46% accuracy at 40 classes, increases to about 49% at 80 classes, and then plateaus around 50% for 120 to 200 classes. The right graph also plots 'CDA' (red squares) and 'SRe^2L' (blue diamonds) with the x-axis ranging from 20 to 200 in increments of 20, and the y-axis ranging from 35 to 55. The 'CDA' line shows a general upward trend, starting at about 45% at 20 classes, peaking at around 54% at 60 classes, and then gradually decreasing to about 53% at 200 classes. The 'SRe^2L' line shows a similar upward trend, starting at about 39% at 20 classes, rising to about 47% at 60 classes, and then fluctuating between 47% and 49% for the remaining data points up to 200 classes.

Figure 8: 5/10-step class-incremental learning on Tiny-IN.

## **5 Conclusion**

We presented a new framework focused on *global-to-local* gradient refinement through curriculum data synthesis for large-scale dataset distillation. Our approach involves a practical paradigm with detailed pertaining for compressing knowledge, data synthesis for recovery, and post-training recipes. The proposed approach enables the distillation of ImageNet-21K to  $50\times$  smaller while maintaining competitive accuracy levels. In regular benchmarks, such as ImageNet-1K and CIFAR-100, our approach also demonstrated superior performance, surpassing prior state-of-the-art methods by substantial margins. We further show the capability of our synthetic data on downstream tasks of cross-model generalization and continual learning. With the recent substantial growth in the size of both models and datasets, the critical need for dataset distillation on large-scale datasets and models has become increasingly prominent and urgent. Our future work will focus on distilling more modalities like language and speech.

**Limitations.** Our proposed approach is robust to generate informative images, while we also clarify that the quality of our generated data is not comparable to the image quality achieved by state-of-the-art generative models on large-scale datasets. This difference is expected, given the distinct goals of dataset distillation versus generative models. Generative models aim to synthesize highly realistic images with detailed features, whereas dataset distillation methods focus on producing images that capture the most representative information possible for efficient learning in downstream tasks. Realism is not the primary objective in dataset distillation.

## **Acknowledgments**

This research is supported by the MBZUAI-WIS Joint Program for AI Research and the Google Research award grant.

### **References**

- <span id="page-12-1"></span>Ali Abbasi, Ashkan Shahbazi, Hamed Pirsiavash, and Soheil Kolouri. One category one prompt: Dataset distillation using diffusion models. *arXiv preprint arXiv:2403.07142*, 2024.
- <span id="page-12-2"></span>Yoshua Bengio, Jérôme Louradour, Ronan Collobert, and Jason Weston. Curriculum learning. In *Proceedings of the 26th annual international conference on machine learning*, pp. 41–48, 2009.
- <span id="page-12-0"></span>Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020.

- <span id="page-13-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022a.
- <span id="page-13-0"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Wearable imagenet: Synthesizing tileable textures via dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, 2022b.
- <span id="page-13-10"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 3739–3748, 2023.
- <span id="page-13-7"></span>Mingyang Chen, Bo Huang, Junda Lu, Bing Li, Yi Wang, Minhao Cheng, and Wei Wang. Dataset distillation via adversarial prediction matching. *arXiv preprint arXiv:2312.08912*, 2023.
- <span id="page-13-14"></span>Ekin D Cubuk, Barret Zoph, Jonathon Shlens, and Quoc V Le. Randaugment: Practical automated data augmentation with a reduced search space. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops*, pp. 702–703, 2020.
- <span id="page-13-1"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pp. 6565–6590. PMLR, 2023.
- <span id="page-13-11"></span>Justin Cui, Ruochen Wang, Yuanhao Xiong, and Cho-Jui Hsieh. Ameliorate spurious correlations in dataset condensation. In *Forty-first International Conference on Machine Learning*, 2024.
- <span id="page-13-5"></span>Mostafa Dehghani, Josip Djolonga, Basil Mustafa, Piotr Padlewski, Jonathan Heek, Justin Gilmer, Andreas Peter Steiner, Mathilde Caron, Robert Geirhos, Ibrahim Alabdulmohsin, et al. Scaling vision transformers to 22 billion parameters. In *International Conference on Machine Learning*, pp. 7480–7512. PMLR, 2023.
- <span id="page-13-3"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pp. 248–255. Ieee, 2009.
- <span id="page-13-6"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *arXiv preprint arXiv:2206.02916*, 2022.
- <span id="page-13-13"></span>Terrance DeVries and Graham W Taylor. Improved regularization of convolutional neural networks with cutout. *arXiv preprint arXiv:1708.04552*, 2017.
- <span id="page-13-4"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-13-9"></span>Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2023.
- <span id="page-13-12"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. *arXiv preprint arXiv:2311.15529*, 2023.
- <span id="page-13-8"></span>Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-13-16"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-13-15"></span>Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. Momentum contrast for unsupervised visual representation learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 9729–9738, 2020.

<span id="page-14-6"></span>Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. *ICLR*, 2024.

- <span id="page-14-14"></span>Elad Hoffer, Itay Hubara, and Daniel Soudry. Train longer, generalize better: closing the generalization gap in large batch training of neural networks. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-14-16"></span>Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4700–4708, 2017.
- <span id="page-14-13"></span>Nitish Shirish Keskar, Dheevatsa Mudigere, Jorge Nocedal, Mikhail Smelyanskiy, and Ping Tak Peter Tang. On large-batch training for deep learning: Generalization gap and sharp minima. *arXiv preprint arXiv:1609.04836*, 2016.
- <span id="page-14-2"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102–11118. PMLR, 2022a.
- <span id="page-14-8"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, 2022b.
- <span id="page-14-15"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-14-7"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pp. 12352–12364. PMLR, 2022.
- <span id="page-14-10"></span>Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023.
- <span id="page-14-9"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022a.
- <span id="page-14-18"></span>Zhuang Liu, Hanzi Mao, Chao-Yuan Wu, Christoph Feichtenhofer, Trevor Darrell, and Saining Xie. A convnet for the 2020s. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 11976–11986, 2022b.
- <span id="page-14-5"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.
- <span id="page-14-12"></span>Zhiheng Ma, Anjia Cao, Funing Yang, and Xing Wei. Curriculum dataset distillation. *arXiv preprint arXiv:2405.09150*, 2024.
- <span id="page-14-0"></span>Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. Dataset distillation with attention labels for fine-tuning bert. In *Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers)*, pp. 119–127, 2023.
- <span id="page-14-4"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-14-1"></span>OpenAI. Gpt-4 technical report, 2023.
- <span id="page-14-11"></span>Tian Qin, Zhiwei Deng, and David Alvarez-Melis. Distributional dataset distillation with subtask decomposition. *arXiv preprint arXiv:2403.00999*, 2024.
- <span id="page-14-17"></span>Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollár. Designing network design spaces. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 10428–10436, 2020.
- <span id="page-14-3"></span>Tal Ridnik, Emanuel Ben-Baruch, Asaf Noy, and Lihi Zelnik-Manor. Imagenet-21k pretraining for the masses. *arXiv preprint arXiv:2104.10972*, 2021.

- <span id="page-15-3"></span>Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 10684–10695, 2022.
- <span id="page-15-8"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pp. 17097–17107, October 2023.
- <span id="page-15-16"></span>Xinyi Shang, Peng Sun, and Tao Lin. Gift: Unlocking full potential of labels in distilled dataset at near-zero cost. *arXiv preprint arXiv:2405.14736*, 2024.
- <span id="page-15-14"></span>Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *CVPR*, 2024a.
- <span id="page-15-12"></span>Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024b.
- <span id="page-15-17"></span>Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pp. 673–690. Springer, 2022.
- <span id="page-15-9"></span>Donghyeok Shin, Seungjae Shin, and Il-Chul Moon. Frequency domain-based dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-15-2"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pp. 1–8. IEEE, 2021.
- <span id="page-15-13"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. *arXiv preprint arXiv:2312.03526*, 2023.
- <span id="page-15-11"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024.
- <span id="page-15-18"></span>Hugo Touvron, Matthieu Cord, Matthijs Douze, Francisco Massa, Alexandre Sablayrolles, and Hervé Jégou. Training data-efficient image transformers & distillation through attention. In *International conference on machine learning*, pp. 10347–10357. PMLR, 2021.
- <span id="page-15-7"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2022.
- <span id="page-15-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-15-4"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation, 2020.
- <span id="page-15-15"></span>Yifan Wu, Jiawei Du, Ping Liu, Yuewei Lin, Wenqing Cheng, and Wei Xu. Dd-robustbench: An adversarial robustness benchmark for dataset distillation. *arXiv preprint arXiv:2403.13322*, 2024.
- <span id="page-15-10"></span>Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024.
- <span id="page-15-1"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023.
- <span id="page-15-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-15-6"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision, WACV 2023, Waikoloa, HI, USA, January 2-7, 2023*, 2023.

- <span id="page-16-0"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-16-2"></span>Binglin Zhou, Linhao Zhong, and Wentao Chen. Improve cross-architecture generalization on dataset distillation. *arXiv preprint arXiv:2402.13007*, 2024a.
- <span id="page-16-3"></span>Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need. *arXiv preprint arXiv:2404.07976*, 2024b.
- <span id="page-16-1"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.

## **Appendix**

### **A Datasets Details**

We conduct experiments on three ImageNet scale datasets, Tiny-ImageNet [\(Le & Yang, 2015\)](#page-14-15), ImageNet-1K [\(Deng et al., 2009\)](#page-13-3), and ImageNet-21K [\(Ridnik et al., 2021\)](#page-14-3). The dataset details are as follows:

- CIFAR-100 dataset composes 500 training images per class, each with a resolution of  $32\times32$  pixels, across 100 classes.
- Tiny-ImageNet dataset is derived from ImageNet-1K and consists of 200 classes. Within each category, there are 500 images with a uniform  $64\times64$  resolution.
- ImageNet-1K dataset comprises 1,000 classes and 1,281,167 images in total. We resize all images into standard 224×224 resolution during the data loading stage.
- The original ImageNet-21K dataset is an extensive visual recognition dataset containing 21,841 classes and 14,197,122 images. We use ImageNet-21K-P [\(Ridnik et al., 2021\)](#page-14-3) which utilizes data processing to remove infrequent classes and resize all images to  $224 \times 224$  resolution. After data processing, ImageNet-21K-P dataset consists of 10,450 classes and 11,060,223 images.

<span id="page-17-0"></span>

### **B Implementation Details**

### **B.1 CIFAR-100**

**Hyper-parameter Setting.** We train a modified ResNet-18 model [\(He et al., 2020\)](#page-13-15) on CIFAR-100 training data with a Top-1 accuracy of 79.1% using the parameter setting in Table [10a.](#page-17-1) The well-trained model serves as the recovery model under the recovery setting in Table [10b.](#page-17-1)

<span id="page-17-1"></span>

|                        | (a) Squeezing/validation setting. | (b) Recovery setting.  |                               |  |  |
|------------------------|-----------------------------------|------------------------|-------------------------------|--|--|
| config                 | value                             | config                 | value                         |  |  |
| optimizer              | <b>SGD</b>                        | $\alpha_{\rm BN}$      | 0.01                          |  |  |
| base learning rate     | 0.1                               | optimizer              | Adam                          |  |  |
| momentum               | 0.9                               | base learning rate     | 0.25                          |  |  |
| weight decay           | $5e-4$                            | momentum               | $\beta_1, \beta_2 = 0.5, 0.9$ |  |  |
| batch size             | 128 (squeeze) / $8 \text{ (val)}$ | batch size             | 100                           |  |  |
| learning rate schedule | cosine decay                      | learning rate schedule | cosine decay                  |  |  |
| training epoch         | $800$ (val)<br>200 (squeeze)      | recovery iteration     | 1,000                         |  |  |
| augmentation           | RandomResizedCrop                 | augmentation           | RandomResizedCrop             |  |  |

Table 10: Hyper-parameter settings on CIFAR-100.

Due to the low resolution of CIFAR images, the default lower bound *β<sup>l</sup>* needs to be raised from 0.08 (ImageNet setting) to a higher reasonable value in order to avoid the training inefficiency caused by extremely small cropped areas with little information. Thus, we conducted the ablation to select the optimal value for the default lower bound *β<sup>l</sup>* in RandomResizedCrop operations in Table [11.](#page-17-2) We choose 0.4 as the default lower bound *β<sup>l</sup>* in Algorithm [1](#page-7-0) to exhibit the best distillation performance on CIFAR-100. We adopt a small batch size value of 8 and extend the training budgets in the following validation stage, which aligns with the strong training recipe on inadequate datasets.

Table 11: Ablation on the lower bound *β<sup>l</sup>* setting in distilling CIFAR-100.

<span id="page-17-2"></span>

| default lower bound $\beta_l$   | 0.08 | 0.2   | 0.4         | 0.6   | 0.8   | 1.0   |
|---------------------------------|------|-------|-------------|-------|-------|-------|
| validation accuracy (800ep) (%) | 58.5 | 62.14 | <b>64.0</b> | 63.36 | 61.65 | 54.43 |

#### **B.2 Tiny-ImageNet**

**Hyper-parameter Setting.** We train a modified ResNet-18 model [\(He et al., 2020\)](#page-13-15) on Tiny-ImageNet training data with the parameter setting in Table [12a](#page-18-0) and use the well-trained ResNet-18 model with a Top-1 accuracy of 61.2% as a recovery model for CDA. The recovery setting is provided in Table [12b.](#page-18-0) Table 12: Hyper-parameter settings on Tiny-ImageNet.

(a) Squeezing/validation setting.

(b) Recovery setting.

<span id="page-18-0"></span>

| config                 | value                        | config                 | value                         |
|------------------------|------------------------------|------------------------|-------------------------------|
| optimizer              | <b>SGD</b>                   | $\alpha_{\rm BN}$      | 1.0                           |
| base learning rate     | 0.2                          | optimizer              | Adam                          |
| momentum               | 0.9                          | base learning rate     | 0.1                           |
| weight decay           | $1e-4$                       | momentum               | $\beta_1, \beta_2 = 0.5, 0.9$ |
| batch size             | $256$ (squeeze) / $64$ (val) | batch size             | 100                           |
| learning rate schedule | cosine decay                 | learning rate schedule | cosine decay                  |
| training epoch         | $50$ (squeeze) / $100$ (val) | recovery iteration     | 4,000                         |
| augmentation           | RandomResizedCrop            | augmentation           | RandomResizedCrop             |

**Small IPC Setting Comparison.** Table [13](#page-18-1) presents the result comparison among our CDA, DM [\(Zhao &](#page-15-6) [Bilen, 2023\)](#page-15-6) and MTT [\(Cazenavette et al., 2022b\)](#page-13-0). Consider that our approach is a decoupled process of dataset compression followed by recovery through gradient updating. It is well-suited to large-scale datasets but less so for small IPC values. As anticipated, there is no advantage when IPC value is extremely low, such as  $IPC = 1$ . However, when the IPC is increased slightly, our method demonstrates considerable benefits on accuracy over other counterparts. Furthermore, we emphasize that our approach yields substantial improvements when afforded a larger training budget, i.e., more training epochs.

Table 13: Comparison with baseline methods on Tiny-ImageNet.

<span id="page-18-1"></span>

| Tiny-ImageNet IPC DM MTT CDA (200ep) CDA (400ep) |      |      |                  |                                   | CDA (800ep)                       |
|--------------------------------------------------|------|------|------------------|-----------------------------------|-----------------------------------|
|                                                  | -3.9 | 8.8  | $2.38 \pm 0.08$  | $2.82 \pm 0.06$                   | $3.29 \pm 0.26$                   |
| 10                                               | 12.9 | 23.2 | $30.41 \pm 1.53$ | $37.41 \pm 0.02$                  | $43.04 \pm 0.26$                  |
| 20                                               |      |      |                  | $43.93 \pm 0.20$ $47.76 \pm 0.19$ | $50.46 \pm 0.14$                  |
| 50                                               | 24.1 | 28.0 | $50.26 \pm 0.09$ |                                   | $51.52 \pm 0.17$ 55.50 $\pm$ 0.18 |

**Continual Learning.** We adhere to the continual learning codebase outlined in [Zhao et al.](#page-16-0) [\(2020\)](#page-16-0) and validate provided  $S\text{Re}^2L$  and our CDA distilled Tiny-ImageNet dataset under IPC 100 as illustrated in Figure [8.](#page-12-3) Detailed values are presented in the Table [14](#page-18-2) and Table [15.](#page-18-3)

<span id="page-18-2"></span>Table 14: 5-step class-incremental learning on Tiny-ImageNet. This complements details in the left subfigure of Figure [8.](#page-12-3)

| # class    | 40    | 80    | 120   | 160   | 200   |
|------------|-------|-------|-------|-------|-------|
| SRe2L      | 45.60 | 48.71 | 49.27 | 50.25 | 50.27 |
| CDA (ours) | 51.93 | 53.63 | 53.02 | 52.60 | 52.15 |

<span id="page-18-3"></span>Table 15: 10-step class-incremental learning on Tiny-ImageNet. This complements details in the right subfigure of Figure [8.](#page-12-3)

| # class    | 20    | 40    | 60    | 80    | 100   | 120   | 140   | 160   | 180   | 200   |
|------------|-------|-------|-------|-------|-------|-------|-------|-------|-------|-------|
| SRe2L      | 38.17 | 44.97 | 47.12 | 48.48 | 47.67 | 49.33 | 49.74 | 50.01 | 49.56 | 50.13 |
| CDA (ours) | 44.57 | 52.92 | 54.19 | 53.67 | 51.98 | 53.21 | 52.96 | 52.58 | 52.40 | 52.18 |

#### **B.3 ImageNet-1K**

**Hyper-parameter Settings.** We employ PyTorch off-the-shelf ResNet-18 and DenseNet-121 with the Top-1 accuracy of {69.8%, 74.4%} which are trained with the official recipe in Table [16a.](#page-19-0) And the recovery settings are provided in Table [16c,](#page-19-0) and it is noteworthy that we tune and set distinct parameters  $\alpha_{\rm BN}$  and learning rate for different recovery models in Table [16d.](#page-19-0) Then, we employ ResNet-{18, 50, 101, 152} [\(He](#page-13-16) [et al., 2016\)](#page-13-16), DenseNet-121 [\(Huang et al., 2017\)](#page-14-16), RegNet [\(Radosavovic et al., 2020\)](#page-14-17), ConvNeXt [\(Liu et al.,](#page-14-18) [2022b\)](#page-14-18), and DeiT-Tiny [\(Touvron et al., 2021\)](#page-15-18) as validation models to evaluate the cross-model generalization on distilled ImageNet-1K dataset under the validation setting in Table [16b.](#page-19-0)

<span id="page-19-0"></span>

|                               | (a) Squeezing setting.        |                                      |      | (b) Validation setting. |                   |
|-------------------------------|-------------------------------|--------------------------------------|------|-------------------------|-------------------|
| config                        | value                         | config                               |      | value                   |                   |
| optimizer                     | SGD                           | optimizer                            |      | AdamW                   |                   |
| base learning rate            | 0.1                           | base learning rate                   |      | $1e-3$                  |                   |
| momentum                      | 0.9                           | weight decay                         |      | $1e-2$                  |                   |
| weight decay                  | $1e-4$                        | batch size                           |      | 128                     |                   |
| batch size                    | 256                           | learning rate schedule               |      | cosine decay            |                   |
| lr step size                  | 30                            | training epoch                       |      | 300                     |                   |
| lr gamma                      | 0.1                           | augmentation                         |      |                         | RandomResizedCrop |
| training epoch                | 90                            |                                      |      |                         |                   |
| augmentation                  | RandomResizedCrop             |                                      |      |                         |                   |
|                               | (c) Shared recovery setting.  | (d) Model-specific recovery setting. |      |                         |                   |
| config                        | value                         | config                               |      | ResNet-18               | DenseNet-121      |
| $\mathop{\mathrm{optimizer}}$ | Adam                          | $\alpha_{\rm BN}$                    | 0.01 |                         | 0.01              |
| momentum                      | $\beta_1, \beta_2 = 0.5, 0.9$ | base learning rate                   | 0.25 |                         | 0.5               |
| batch size                    | 100                           | recovery iteration                   |      | 1,000 / 4,000           | 1,000             |
| learning rate schedule        | cosine decay                  |                                      |      |                         |                   |
| augmentation                  | RandomResizedCrop             |                                      |      |                         |                   |

Table 16: Hyper-parameter settings on ImageNet-1K.

**Histogram Values.** The histogram data of ImageNet-1K comparison with SRe2L in Figure [1](#page-0-0) can be conveniently found in the following Table [17](#page-19-1) for reference.

Table 17: ImageNet-1K comparison with  $\text{SRe}^2L$ . This table complements details in Figure [1.](#page-0-0)

<span id="page-19-1"></span>

| Method \Validation Model | ResNet-18 | ResNet-50 | ResNet-101 | DenseNet-121 | RegNet-Y-8GF |
|--------------------------|-----------|-----------|------------|--------------|--------------|
| SRe2L (4K)               | 46.80     | 55.60     | 57.59      | 49.74        | 60.34        |
| Our CDA (1K)             | 52.88     | 60.70     | 61.10      | 57.26        | 62.94        |
| Our CDA (4K)             | 53.45     | 61.26     | 61.57      | 57.35        | 63.22        |

To conduct the ablation studies efficiently in Table [3,](#page-8-2) Table [22](#page-21-0) and Figure [6,](#page-10-1) we recover the data for 1,000 iterations and validate the distilled dataset with a batch size of 1,024, keeping other settings the same as Table [16.](#page-19-0) Detailed values of the ablation study on schedulers are provided in Table [18.](#page-20-2)

| Scheduler \ Milestone | 0.1   | 0.2   | 0.3   | 0.4   | 0.5   | 0.6   | 0.7   | 0.8   | 0.9   | 1     |
|-----------------------|-------|-------|-------|-------|-------|-------|-------|-------|-------|-------|
| Step                  | 45.46 | 46.08 | 46.65 | 46.75 | 46.87 | 46.13 | 45.27 | 44.97 | 42.49 | 41.18 |
| Linear                | 45.39 | 46.30 | 46.59 | 46.51 | 46.60 | 47.18 | 47.13 | 47.37 | 48.06 | 47.78 |
| Cosine                | 45.41 | 45.42 | 46.15 | 46.90 | 46.93 | 47.42 | 46.86 | 47.33 | 47.80 | 48.05 |

<span id="page-20-2"></span>Table 18: Ablation study on three different schedulers with varied milestone settings. It complements details in Figure [6.](#page-10-1)

**Cross-Model Generalization.** To supplement the validation models on distilled ImageNet-1K in Table [7,](#page-11-0) including more different architecture models to evaluate the cross-architecture performance. We have conducted validation experiments on a broad range of models, including SqueezeNet, MobileNet, EfficientNet, MNASNet, ShuffleNet, ResMLP, AlexNet, DeiT-Base, and VGG family models. These validation models are selected from a wide variety of architectures, encompassing a vast range of parameters, shown in Table [19.](#page-20-1) In the upper group of the table, the selected models are relatively small and efficient. There is a trend that its validation performance improves as the number of model parameters increases. In the lower group, we validated earlier models AlexNet and VGG. These models also show a trend of performance improvement with increasing size, but due to the simplicity of early model architectures, such as the absence of residual connections, their performance is inferior compared to more recent models. Additionally, we evaluated our distilled dataset on ResMLP, which is based on MLPs, and the DeiT-Base model, which is based on transformers. In summary, the distilled dataset created using our CDA method demonstrates strong validation performance across a wide range of models, considering both architectural diversity and parameter size.

<span id="page-20-1"></span>

|  | Table 19: ImageNet-1K Top-1 on cross-model generation. Our CDA dataset consists of 50 IPC. |  |  |  |  |  |  |  |  |  |
|--|--------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|
|--|--------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|

| Model        | SqueezeNet | MobileNet | EfficientNet | MNASNet | ShuffleNet | ResMLP |
|--------------|------------|-----------|--------------|---------|------------|--------|
| #Params (M)  | 1.2        | 3.5       | 5.3          | 6.3     | 7.4        | 30.0   |
| accuracy (%) | 19.70      | 49.76     | 55.10        | 55.66   | 54.69      | 54.18  |
| Model        | AlexNet    | DeiT-Base | VGG-11       | VGG-13  | VGG-16     | VGG-19 |
| #Params (M)  | 61.1       | 86.6      | 132.9        | 133.0   | 138.4      | 143.7  |
| accuracy (%) | 14.60      | 30.27     | 36.99        | 38.60   | 42.28      | 43.30  |

#### <span id="page-20-0"></span>**B.4 ImageNet-21K**

**Hyper-parameter Setting.** ImageNet-21K-P [\(Ridnik et al., 2021\)](#page-14-3) proposes two training recipes to train ResNet-{18, 50} models. One way is to initialize the models from well-trained ImageNet-1K weight and train on ImageNet-21K-P for 80 epochs, another is to train models with random initialization for 140 epochs, as shown in Table [20a.](#page-21-2) The accuracy metrics on both training recipes are reported in Table [21.](#page-21-0) In our experiments, we utilize the pre-trained ResNet-{18, 50} models initialized by ImageNet-1K weight with the Top-1 accuracy of {38.1%, 44.2%} as recovery model. And the recovery setting is provided in Table [20c.](#page-21-2) Then, we evaluate the quality of the distilled ImageNet-21K dataset on ResNet-{18, 50, 101} validation models under the validation setting in Table [20b.](#page-21-2) To accelerate the ablation study on the batch size setting in Table [6,](#page-10-0) we train the validation model ResNet-18 for 140 epochs.

### **C Reverse Curriculum Learning**

**Reverse Curriculum Learning (RCL).** We use a reverse step scheduler in the RCL experiments, starting with the default cropped range from  $\beta_l$  to  $\beta_u$  and transitioning at the milestone point to optimize the whole image, shifting from challenging to simpler optimizations. Other settings follow the recovery recipe on ResNet-18 for 1K recovery iterations. Table [22](#page-21-0) shows the RCL results, a smaller step milestone indicates an earlier difficulty transition. The findings reveal that CRL does not improve the generated dataset's quality compared to the baseline SRe<sup>2</sup>L, which has 44.90% accuracy.

<span id="page-21-2"></span>

| (a) Squeezing setting. |              |                        | (b) Validation setting. |
|------------------------|--------------|------------------------|-------------------------|
| config                 | value        | config                 | value                   |
| optimizer              | Adam         | optimizer              | AdamW                   |
| base learning rate     | $3e-4$       | base learning rate     | $2e-3$                  |
| weight decay           | $1e-4$       | weight decay           | $1e-2$                  |
| batch size             | 1,024        | batch size             | 32                      |
| learning rate schedule | cosine decay | learning rate schedule | cosine decay            |
| label smooth           | 0.2          | label smooth           | 0.2                     |
| training epoch         | 80/140       | training epoch         | 300                     |
| augmentation           | CutoutPIL,   | augmentation           | CutoutPIL,              |
|                        | RandAugment  |                        | RandomResizedCrop       |
|                        |              | (c) Recovery setting.  |                         |
| confix                 |              | مبياديد                |                         |

Table 20: Hyper-parameter settings on ImageNet-21K.

| config                 | value                             |
|------------------------|-----------------------------------|
| $\alpha_{\rm BN}$      | 0.25                              |
| optimizer              | Adam                              |
| base learning rate     | 0.05 (ResNet-18), 0.1 (ResNet-50) |
| momentum               | $\beta_1, \beta_2 = 0.5, 0.9$     |
| batch size             | 100                               |
| learning rate schedule | cosine decay                      |
| recovery iteration     | 2,000                             |
| augmentation           | RandomResizedCrop                 |

Table 21: Accuracy of ResNet-{18, 50} on ImageNet-21K-P.

<span id="page-21-0"></span>

| ResNet-18 (Ours)     | ImageNet-1K | 38.1                                         | 67.2                                         |
|----------------------|-------------|----------------------------------------------|----------------------------------------------|
|                      | Random      | 38.5                                         | 67.8                                         |
| Ridnik et al. (2021) | ImageNet-1K | 42.2                                         | 72.0                                         |
| ResNet-50 (Ours)     | ImageNet-1K | 44.2 <span style="color:green;">↑</span> 2.0 | 74.6 <span style="color:green;">+</span> 2.6 |
|                      | Random      | 44.5 <span style="color:green;">+</span> 2.3 | 75.1 <span style="color:green;">↑</span> 3.1 |

|  |  | Table 22: Ablation of reverse curriculum learning. |  |
|--|--|----------------------------------------------------|--|

| Step Milestone | Accuracy (%) |
|----------------|--------------|
| 0.2            | 41.38        |
| 0.4            | 41.59        |
| 0.6            | 42.60        |
| 0.8            | 44.39        |

### <span id="page-21-1"></span>**D Visulization**

We provide additional comparisons of four groups of visualizations on synthetic ImageNet-21K images at recovery steps of  $\{100, 500, 1,000, 1,500, 2,000\}$  between  $SRe^{2}L$  (upper) and CDA (lower) in Figure [9.](#page-22-0) The chosen target classes are *Benthos*, *Squash Rackets*, *Marine Animal*, and *Scavenger*.

In addition, we present our CDA's synthetic ImageNet-1K images in Figure [10](#page-23-0) and ImageNet-21K images in Figure [11](#page-24-0) and Figure [12.](#page-25-0)

<span id="page-22-0"></span>Image /page/22/Picture/1 description: This is a grid of images, likely a figure from a research paper, displaying various visualizations. The grid is organized into rows and columns, with each cell containing a distinct image. The images appear to be abstract or stylized representations, possibly generated by a neural network, as suggested by the title "Synthetic ImageNet-21K data visualization comparison" which is partially visible at the bottom. Some rows show similar patterns or objects, such as what appear to be flowers, aquatic life like turtles, and birds or raptors, with variations in color, texture, and detail across the columns within each row. The overall impression is a comparison of different data visualization techniques or outputs.

Figure 9: Synthetic ImageNet-21K data visualization comparison.

<span id="page-23-0"></span>Image /page/23/Picture/1 description: This is a grid of 30 images, arranged in 5 rows and 6 columns. Each image appears to be a synthetic generation, possibly from a dataset like ImageNet, as indicated by the caption below the grid. The images depict a variety of subjects including animals (fish, chickens, birds, penguins, cats, dogs, insects), objects (food, bottles, umbrellas, boats, cars), and scenes (landscapes, buildings). The overall style of the images is painterly and somewhat abstract, with vibrant colors and soft edges, suggesting a generative art or data visualization context.

Figure 10: Synthetic ImageNet-1K data visualization from CDA.

<span id="page-24-0"></span>Image /page/24/Picture/1 description: The image is a grid of 36 smaller images, each with a painterly, impressionistic style. The grid is arranged in 6 rows and 6 columns. The smaller images depict a variety of subjects including food items like fruit and pancakes, animals such as birds, dogs, deer, and tigers, people engaged in activities like cycling and playing sports, and scenes of nature and architecture. The overall impression is a collection of diverse artistic renderings of everyday objects and scenes.

Figure 11: Synthetic ImageNet-21K data distilled from ResNet-18 by CDA.

<span id="page-25-0"></span>Image /page/25/Picture/1 description: The image is a grid of 35 smaller images, each with a distinct subject matter. The overall style of the images is abstract and painterly, with vibrant colors and visible brushstrokes. The subjects range from nature scenes with flowers and animals to man-made objects like cars, bicycles, and sports equipment. Some images depict people engaged in activities such as cycling or playing tennis. The grid is arranged in five rows and seven columns. The bottom of the image contains text that reads "Figure 12: Synthetic ImageNet-21K data distilled from ResNet-50 by CDA."

Figure 12: Synthetic ImageNet-21K data distilled from ResNet-50 by CDA.