{"table_of_contents": [{"title": "BOOM!\nDataset Distillation via Curriculum Data Synthesis in\nLarge Data Era", "heading_level": null, "page_id": 0, "polygon": [[70.5, 89.47705078125], [540.87890625, 89.47705078125], [540.87890625, 134.578125], [70.5, 134.578125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[281.25, 291.0], [330.0, 291.0], [330.0, 302.80078125], [281.25, 302.80078125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.14990234375, 559.5], [159.0, 559.5], [159.0, 571.18359375], [70.14990234375, 571.18359375]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 3, "polygon": [[70.5, 165.9990234375], [167.4931640625, 165.9990234375], [167.4931640625, 179.3408203125], [70.5, 179.3408203125]]}, {"title": "3 Approach", "heading_level": null, "page_id": 4, "polygon": [[70.5, 231.0], [143.96044921875, 231.0], [143.96044921875, 241.69921875], [70.5, 241.69921875]]}, {"title": "3.1 Preliminary: Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[70.5, 253.5], [245.7861328125, 253.5], [245.7861328125, 263.7421875], [70.5, 263.7421875]]}, {"title": "3.2 Dataset Distillation on Large-scale Datasets", "heading_level": null, "page_id": 4, "polygon": [[70.5, 507.0], [298.23046875, 507.0], [298.23046875, 517.4296875], [70.5, 517.4296875]]}, {"title": "3.3 Global-to-local Gradient Update via Curriculum", "heading_level": null, "page_id": 5, "polygon": [[70.5, 441.75], [313.5, 441.75], [313.5, 452.07421875], [70.5, 452.07421875]]}, {"title": "Data Synthesis by Recovering and Relabeling.", "heading_level": null, "page_id": 6, "polygon": [[70.5, 603.75], [305.701171875, 603.28125], [305.701171875, 613.5], [70.5, 614.8828125]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 7, "polygon": [[70.5, 620.68359375], [159.75, 620.68359375], [159.75, 632.28515625], [70.5, 632.28515625]]}, {"title": "4.1 Datasets and Implementation Details", "heading_level": null, "page_id": 7, "polygon": [[70.5, 643.5], [267.75, 643.5], [267.75, 653.5546875], [70.5, 653.5546875]]}, {"title": "4.2 CIFAR-100", "heading_level": null, "page_id": 8, "polygon": [[70.5, 299.126953125], [147.0, 299.126953125], [147.0, 309.568359375], [70.5, 309.568359375]]}, {"title": "4.3 Tiny-ImageNet", "heading_level": null, "page_id": 8, "polygon": [[70.5, 451.5], [166.5, 451.5], [166.5, 461.7421875], [70.5, 461.7421875]]}, {"title": "4.4 ImageNet-1K", "heading_level": null, "page_id": 8, "polygon": [[70.5, 558.75], [159.0, 558.75], [159.0, 568.86328125], [70.5, 568.86328125]]}, {"title": "4.5 ImageNet-21K", "heading_level": null, "page_id": 9, "polygon": [[70.5, 384.0], [165.0, 384.0], [165.0, 394.453125], [70.5, 394.453125]]}, {"title": "4.6 Ablations", "heading_level": null, "page_id": 9, "polygon": [[70.5, 613.5], [140.25, 613.5], [140.25, 623.77734375], [70.5, 623.77734375]]}, {"title": "4.7 Analysis", "heading_level": null, "page_id": 10, "polygon": [[70.5, 532.125], [134.25, 532.125], [134.25, 542.1796875], [70.5, 542.1796875]]}, {"title": "4.8 Application: Continual Learning", "heading_level": null, "page_id": 11, "polygon": [[70.5, 676.37109375], [243.0, 676.37109375], [243.0, 687.19921875], [70.5, 687.19921875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 12, "polygon": [[70.5, 314.25], [150.75, 314.25], [150.75, 326.390625], [70.5, 326.390625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 12, "polygon": [[70.5, 560.25], [154.0458984375, 560.25], [154.0458984375, 570.41015625], [70.5, 570.41015625]]}, {"title": "References", "heading_level": null, "page_id": 12, "polygon": [[70.5, 619.5], [132.380859375, 619.5], [132.380859375, 630.73828125], [70.5, 630.73828125]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 17, "polygon": [[70.5, 81.75], [123.75, 81.75], [123.75, 94.60107421875], [70.5, 94.60107421875]]}, {"title": "A Datasets Details", "heading_level": null, "page_id": 17, "polygon": [[70.5, 108.66796875], [185.4228515625, 108.66796875], [185.4228515625, 120.26953125], [70.5, 120.26953125]]}, {"title": "B Implementation Details", "heading_level": null, "page_id": 17, "polygon": [[70.5, 326.583984375], [222.75, 326.583984375], [222.75, 338.185546875], [70.5, 338.185546875]]}, {"title": "B.1 CIFAR-100", "heading_level": null, "page_id": 17, "polygon": [[70.5, 351.75], [149.8623046875, 351.75], [149.8623046875, 363.12890625], [70.5, 363.12890625]]}, {"title": "B.2 Tiny-ImageNet", "heading_level": null, "page_id": 18, "polygon": [[70.5, 83.25], [168.0, 83.25], [168.0, 93.63427734375], [70.5, 93.63427734375]]}, {"title": "B.3 ImageNet-1K", "heading_level": null, "page_id": 19, "polygon": [[70.5, 83.25], [160.5, 83.25], [160.5, 93.92431640625], [70.5, 93.92431640625]]}, {"title": "B.4 ImageNet-21K", "heading_level": null, "page_id": 20, "polygon": [[70.5, 493.06640625], [165.75, 493.06640625], [165.75, 503.89453125], [70.5, 503.89453125]]}, {"title": "C Reverse Curriculum Learning", "heading_level": null, "page_id": 20, "polygon": [[70.5, 636.92578125], [251.912109375, 636.92578125], [251.912109375, 649.30078125], [70.5, 649.30078125]]}, {"title": "D Visulization", "heading_level": null, "page_id": 21, "polygon": [[69.75, 597.0], [158.25, 597.0], [158.25, 609.08203125], [69.75, 609.08203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 58], ["Text", 6], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6274, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 59], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 41], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 51], ["Text", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 81], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 50], ["Text", 5], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 90], ["Text", 11], ["Figure", 3], ["Reference", 3], ["Equation", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2163, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 99], ["Text", 6], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1350, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["TableCell", 211], ["Line", 54], ["Text", 8], ["Table", 3], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 6220, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["TableCell", 144], ["Line", 47], ["Text", 6], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 14242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 63], ["TableCell", 32], ["Text", 4], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4894, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["TableCell", 151], ["Line", 58], ["Text", 7], ["Reference", 4], ["Table", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4552, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 61], ["Text", 4], ["Reference", 4], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 46], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 45], ["Reference", 19], ["ListItem", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 44], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 28], ["Line", 10], ["ListItem", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["TableCell", 65], ["Line", 52], ["SectionHeader", 4], ["Text", 4], ["ListItem", 4], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 11258, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["TableCell", 162], ["Line", 53], ["Text", 6], ["Table", 4], ["Reference", 4], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 5719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["TableCell", 156], ["Line", 53], ["Text", 3], ["Table", 2], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1248, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 183], ["Span", 135], ["Line", 45], ["Table", 3], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 11092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["TableCell", 154], ["Line", 55], ["Table", 5], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 2, "llm_tokens_used": 11264, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 641, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 618, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 644, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_via_Curriculum_Data_Synthesis_in_Large_Data_Era"}