{"table_of_contents": [{"title": "DATASET DISTILLATION VIA KNOWLEDGE DISTILLA-\nTION: TOWARDS EFFICIENT SELF-SUPERVISED PRE-\nTRAINING OF DEEP NETWORKS", "heading_level": null, "page_id": 0, "polygon": [[104.888671875, 81.0], [507.708984375, 81.0], [507.708984375, 136.9951171875], [104.888671875, 136.9951171875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 215.25], [334.5, 215.25], [334.5, 226.23046875], [276.75, 226.23046875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 450.0], [206.9384765625, 450.0], [206.9384765625, 461.35546875], [107.25, 461.35546875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[107.25, 438.5390625], [213.5126953125, 438.5390625], [213.5126953125, 451.6875], [107.25, 451.6875]]}, {"title": "2.1 DATASET DISTILLATION", "heading_level": null, "page_id": 1, "polygon": [[107.25, 465.99609375], [236.970703125, 465.99609375], [236.970703125, 477.59765625], [107.25, 477.59765625]]}, {"title": "2.2 DATA-EFFICIENT LEARNING VIA SUBSET SELECTION", "heading_level": null, "page_id": 2, "polygon": [[107.05517578125, 360.80859375], [359.19140625, 360.80859375], [359.19140625, 372.0234375], [107.05517578125, 372.0234375]]}, {"title": "2.3 KNOWLEDGE DISTILLATION", "heading_level": null, "page_id": 2, "polygon": [[107.1298828125, 482.625], [253.2568359375, 482.625], [253.2568359375, 494.2265625], [107.1298828125, 494.2265625]]}, {"title": "3 PROBLEM FORMULATION", "heading_level": null, "page_id": 2, "polygon": [[107.25, 663.609375], [258.1875, 663.609375], [258.1875, 675.2109375], [107.25, 675.2109375]]}, {"title": "4 MATCHING TRAINING TRAJECTORIES FOR SSL", "heading_level": null, "page_id": 3, "polygon": [[106.5322265625, 362.25], [368.25, 362.25], [368.25, 373.18359375], [106.5322265625, 373.18359375]]}, {"title": "4.1 CHALLENGES OF MATCHING SSL TRAINING TRAJECTORIES", "heading_level": null, "page_id": 3, "polygon": [[106.5, 455.25], [388.5, 455.25], [388.5, 465.609375], [106.5, 465.609375]]}, {"title": "4.2 MATCHING <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DISTILLATION TRAJECTORIES", "heading_level": null, "page_id": 5, "polygon": [[106.5, 256.5], [370.546875, 256.5], [370.546875, 266.8359375], [106.5, 266.8359375]]}, {"title": "Table 1: Pre-training on CIFAR10 (2% of Full Data)", "heading_level": null, "page_id": 7, "polygon": [[199.5, 81.75], [411.486328125, 81.75], [411.486328125, 91.60400390625], [199.5, 91.60400390625]]}, {"title": "5 EXPERIMENTS", "heading_level": null, "page_id": 7, "polygon": [[106.45751953125, 303.75], [200.25, 303.75], [200.25, 315.5625], [106.45751953125, 315.5625]]}, {"title": "Table 4: Pre-training with Larger Distilled Set Size (5% of Full Data)", "heading_level": null, "page_id": 8, "polygon": [[166.1484375, 81.75], [445.5, 81.75], [445.5, 91.79736328125], [166.1484375, 91.79736328125]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 622.6171875], [196.62890625, 622.6171875], [196.62890625, 633.4453125], [107.25, 633.4453125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.05517578125, 82.5], [175.5, 82.5], [175.5, 93.19921875], [107.05517578125, 93.19921875]]}, {"title": "A EXPERIMENT DETAILS", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 81.93603515625], [246.6826171875, 81.93603515625], [246.6826171875, 93.92431640625], [107.1298828125, 93.92431640625]]}, {"title": "A.1 ADDITIONAL DETAILS FOR EX<PERSON>ERIMENTS IN TABLES 1, 2, 3, 4, 5, 6, 7", "heading_level": null, "page_id": 13, "polygon": [[106.681640625, 106.34765625], [435.69140625, 106.34765625], [435.69140625, 116.7890625], [106.681640625, 116.7890625]]}, {"title": "A.2 ADDITIONAL DETAILS ON EXPERIMENTS IN FIG. 1", "heading_level": null, "page_id": 14, "polygon": [[107.20458984375, 332.96484375], [351.123046875, 332.96484375], [351.123046875, 343.01953125], [107.20458984375, 343.01953125]]}, {"title": "B ADDITIONAL COMPARISON OF RANDOM SUBSET SSL PRE-TRAINING TO\nKRR-ST", "heading_level": null, "page_id": 15, "polygon": [[106.5, 81.6943359375], [503.2265625, 81.6943359375], [503.2265625, 107.12109375], [106.5, 107.12109375]]}, {"title": "C PROOF FOR THEOREM 4.1", "heading_level": null, "page_id": 16, "polygon": [[106.5, 81.75], [261.17578125, 81.75], [261.17578125, 93.05419921875], [106.5, 93.05419921875]]}, {"title": "Analyzing cases for SL", "heading_level": null, "page_id": 16, "polygon": [[106.5, 277.5], [205.59375, 277.5], [205.59375, 287.912109375], [106.5, 287.912109375]]}, {"title": "Analyzing cases for SSL", "heading_level": null, "page_id": 18, "polygon": [[105.75, 268.5], [211.5, 268.5], [211.5, 279.2109375], [105.75, 279.2109375]]}, {"title": "Comparing each term, element-wise", "heading_level": null, "page_id": 20, "polygon": [[106.5, 129.0], [262.669921875, 129.0], [262.669921875, 139.21875], [106.5, 139.21875]]}, {"title": "Conclusion", "heading_level": null, "page_id": 21, "polygon": [[106.5, 168.75], [157.5, 168.75], [157.5, 179.82421875], [106.5, 179.82421875]]}, {"title": "D PROPOSITION D.1: <PERSON><PERSON><PERSON><PERSON><PERSON>ZED ANALYSIS OF VARIANCE OF\nTRAJECTORY UNDER SYNCHRONOUS PARALLEL SGD", "heading_level": null, "page_id": 22, "polygon": [[106.681640625, 82.5], [447.64453125, 82.5], [447.64453125, 107.314453125], [106.681640625, 107.314453125]]}, {"title": "Variance of Trajectory in Supervised Learning", "heading_level": null, "page_id": 22, "polygon": [[106.5, 528.75], [306.75, 528.75], [306.75, 538.69921875], [106.5, 538.69921875]]}, {"title": "Variance of Trajectory in Self-Supervised Learning", "heading_level": null, "page_id": 23, "polygon": [[106.5, 148.9833984375], [327.515625, 148.9833984375], [327.515625, 159.0], [106.5, 159.0]]}, {"title": "E EXAMPLES FROM MKDTAND KRR-ST (LEE ET AL., 2023)", "heading_level": null, "page_id": 24, "polygon": [[107.25, 81.16259765625], [434.794921875, 81.16259765625], [434.794921875, 94.11767578125], [107.25, 94.11767578125]]}, {"title": "F DM AND MTT-SSL EXPERIMENTS", "heading_level": null, "page_id": 24, "polygon": [[106.5, 596.3203125], [308.98828125, 596.3203125], [308.98828125, 609.46875], [106.5, 609.46875]]}, {"title": "G SOCIETAL IMPACT", "heading_level": null, "page_id": 26, "polygon": [[106.5, 81.25927734375], [224.25, 81.25927734375], [224.25, 93.73095703125], [106.5, 93.73095703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 53], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6906, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 53], ["Text", 9], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 53], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 81], ["TextInlineMath", 6], ["Text", 5], ["Equation", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 110], ["Text", 5], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1150, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 60], ["Text", 8], ["Equation", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 929], ["Line", 64], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 2230], ["TableCell", 234], ["Line", 67], ["Reference", 4], ["Table", 3], ["SectionHeader", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 3, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 1522], ["TableCell", 246], ["Line", 63], ["Text", 4], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 11636, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1824], ["TableCell", 147], ["Line", 63], ["Text", 4], ["Table", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 3, "llm_tokens_used": 11397, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 49], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 49], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 30], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 55], ["TableCell", 32], ["Text", 7], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["TableCell", 56], ["Line", 32], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2520, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["TableCell", 47], ["Line", 19], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 6994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 925], ["Line", 145], ["Equation", 10], ["TextInlineMath", 3], ["ListItem", 3], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12018, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 1139], ["Line", 217], ["Equation", 10], ["Text", 5], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1031], ["Line", 230], ["Equation", 12], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 1024], ["Line", 283], ["Equation", 9], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9749, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 1346], ["Line", 248], ["Equation", 6], ["Text", 6], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7746, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 47], ["TableCell", 15], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Form", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2080, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 837], ["Line", 85], ["Equation", 8], ["TextInlineMath", 6], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 627], ["Line", 34], ["TextInlineMath", 8], ["Text", 7], ["Equation", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 21], ["Text", 13], ["Picture", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 3569, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 482], ["TableCell", 163], ["Line", 16], ["Table", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 5605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_via_Knowledge_Distillation__Towards_Efficient_Self-Supervised_Pre-Training_of_D"}