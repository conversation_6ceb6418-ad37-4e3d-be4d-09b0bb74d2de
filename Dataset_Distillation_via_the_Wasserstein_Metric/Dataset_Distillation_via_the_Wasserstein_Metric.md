# Dataset Distillation via the Wasserstein Metric

Haoyang <PERSON><sup>1</sup>, Yijiang Li<sup>2†</sup>, Tiancheng Xing<sup>3†</sup>, <PERSON><PERSON><PERSON><sup>4</sup> <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>1</sup>

<sup>1</sup>University of Illinois at Urbana-Champaign <sup>2</sup>Johns Hopkins University <sup>3</sup>Nanjing University <sup>4</sup>Sri Aurobindo International Centre of Education {hl57, luweili2, jingrui, haohanw}@illinois.edu <EMAIL> <EMAIL> <EMAIL>

### Abstract

Dataset Distillation (DD) emerges as a powerful strategy to encapsulate the expansive information of large datasets into significantly smaller, synthetic equivalents, thereby preserving model performance with reduced computational overhead. Pursuing this objective, we introduce the <PERSON>serstein distance, a metric grounded in optimal transport theory, to enhance distribution matching in DD. Our approach employs the Wasserstein barycenter to provide a geometrically meaningful method for quantifying distribution differences and capturing the centroid of distribution sets efficiently. By embedding synthetic data in the feature spaces of pretrained classification models, we facilitate effective distribution matching that leverages prior knowledge inherent in these models. Our method not only maintains the computational advantages of distribution matching-based techniques but also achieves new state-of-the-art performance across a range of high-resolution datasets. Extensive testing demonstrates the effectiveness and adaptability of our method, underscoring the untapped potential of Wasserstein metrics in dataset distillation.

# 1 Introduction

In recent years, dataset distillation [\(Wang et al.,](#page-12-0) [2018a;](#page-12-0) [Zhao et al.,](#page-12-1) [2021\)](#page-12-1) has gained significant traction within the computer vision community. It aims to generate a compact synthetic dataset from a training dataset, such that the model trained on this smaller synthetic set can achieve performance similar to one trained on the original set. This modern technique promises to alleviate the issue of increasing training costs due to the rapid growth of available data, thus facilitating wider experimentation for researchers to develop new models in various application scenarios [\(Zhang et al.,](#page-12-2) [2022;](#page-12-2) [Goetz and Tewari,](#page-11-0) [2020;](#page-11-0) [Such](#page-12-3) [et al.,](#page-12-3) [2020;](#page-12-3) [Sachdeva and McAuley,](#page-12-4) [2023\)](#page-12-4).

Following the promises and the goal of dataset distillation (DD), the central question is about how to capture the essence of an extensive dataset in a much smaller, synthetic version. This endeavor extends beyond merely reducing data volume or selecting the most representative samples; it involves distilling the dataset's core representation to encapsulate the vast amount of information within a few synthetic samples that retain the rich distributional characteristics of the original dataset. Starting from its core objective, DD can be approached as a bi-level optimization problem [\(Wang et al.,](#page-12-5) [2020;](#page-12-5) [Loo et al.,](#page-11-1) [2022;](#page-11-1) [Nguyen et al.,](#page-11-2) [2021\)](#page-11-2). Following this, various innovative approaches have been proposed such as gradient matching [\(Zhao et al.,](#page-12-1) [2021;](#page-12-1) [Zhao and Bilen,](#page-12-6) [2021\)](#page-12-6), trajectory matching [\(Cazenavette et al.,](#page-11-3) [2022\)](#page-11-3), and curvature matching [\(Shin et al.,](#page-12-7) [2023\)](#page-12-7), focusing on aligning the optimization dynamics of a model trained on synthetic data with one trained on the original, larger dataset.

Preprint. Under review.

<sup>†</sup> Equal contribution as second authors.

We will release our code upon acceptance.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays four panels labeled (a), (b), (c), and (d). Panel (a) shows a tic-tac-toe board with several 'X's and 'O's placed in various squares. Panels (b) and (c) show abstract arrangements of overlapping blue 'X's and 'O's, with panel (c) having more overlapping shapes than panel (b). Panel (d) shows a single, blurred blue square shape in the center of a white background.

Figure 1: The capability of Wasserstein barycenter in condensing the core idea of samples: (a) shows some distributions defined on  $\mathbb{R}^2$ , each uniformly distributed over a geometric shape (circle or cross). We consider several such distributions, weigh them arbitrarily, and then aim to find their barycenter (centroid) while considering different distance metrics: (b) Euclidean distance, (c) Kullback-Leibler divergence, and (d) Wasserstein distance.

However, solving the bi-level optimization problem directly poses significant compuational challenges. As a viable alternative, another line of research aims to directly align the distributions between synthetic data to the original dataset [\(Zhao and Bilen,](#page-12-8) [2023;](#page-12-8) [Wang et al.,](#page-12-9) [2022\)](#page-12-9), usually based on the Maximum Mean Discrepancy (MMD) metric [\(Gretton et al.,](#page-11-4) [2012;](#page-11-4) [Tolstikhin et al.,](#page-12-10) [2016\)](#page-12-10). While these methods have higher computational efficiency, (as a direct alignment of distributions), they seem to underperform in comparison to the optimization-based methods. We conjecture that the reason behind this undesired performance is that MMD metric fails to comprehensively describe the differences between distributions.

Wasserstein distance has been known for measuring the differences between distributions. Rooted in optimal transport theory [\(Villani,](#page-12-11) [2008\)](#page-12-11), Wasserstein distance measures the minimal movement required to transform one probability distribution to another on a given metric space, offering a principled and geometrically meaningful approach to quantify the difference between two distributions. Building upon that, the Wasserstein barycenter [\(Agueh and Carlier,](#page-11-5) [2011\)](#page-11-5) was proposed to depict the centroid of a set of distributions in the space of distributions. We illustrate the ability of Wasserstein barycenter to capture the essence of samples in Fig. [1.](#page-1-0) We simulate distributions uniformly spread on circles and crosses (Fig. [1a\)](#page-1-0). When computing their barycenters, methods minimizing Euclidean distance (Fig. [1b\)](#page-1-0) or using Kullback-Leibler divergence (Fig. [1c\)](#page-1-0) produce barycenters that do not adequately capture the input distributions' essence. In contrast, the Wasserstein barycenter (Fig. [1d\)](#page-1-0) produces a shape that intermediates between a circle and a cross, effectively preserving the geometric properties of the original distributions.

Motivated by this impressive ability to capture distribution differences, we introduce a new DD method that utilizes the Wasserstein distance for distribution matching. Unlike prior work using the MMD metric [\(Gretton et al.,](#page-11-4) [2012;](#page-11-4) [Tolstikhin et al.,](#page-12-10) [2016\)](#page-12-10), the Wasserstein barycenter [\(Agueh and](#page-11-5) [Carlier,](#page-11-5) [2011\)](#page-11-5) does not rely on heuristically designed kernels and can naturally take into account the geometry and structure of the distribution. This allows us to statistically summarize the empirical distribution of the real dataset within a fixed number of images, which are representative and diverse enough to cover the variety of real images in each class, to equip a classification model with higher performance.

Further, to address the challenges brought by high-dimensional data optimization, we follow the method in [Yin et al.](#page-12-12) [\(2023\)](#page-12-12); [Zhao and Bilen](#page-12-8) [\(2023\)](#page-12-8); [Zhao et al.](#page-12-13) [\(2023\)](#page-12-13) to embed the synthetic data into the feature space of a pre-trained image classification model, and further align the intra-class data distributions using the feature map statistics in the BatchNorm [\(Ioffe and Szegedy,](#page-11-6) [2015\)](#page-11-6) layer. By employing an efficient algorithm [\(Cuturi and Doucet,](#page-11-7) [2014\)](#page-11-7) to compute the Wasserstein barycenter [\(Agueh and Carlier,](#page-11-5) [2011\)](#page-11-5), our method preserves the efficiency benefits of distribution matching-based methods [\(Zhao and Bilen,](#page-12-8) [2023\)](#page-12-8) and can scale to large, high-resolution datasets like ImageNet-1k [\(Deng](#page-11-8) [et al.,](#page-11-8) [2009\)](#page-11-8). Experiments show that our method achieves SOTA performance on various benchmarks, validating the effectiveness of our approach. The contributions of our work include:

- We present a novel dataset distillation technique that integrates distribution matching [\(Zhao and](#page-12-8) [Bilen,](#page-12-8) [2023\)](#page-12-8) with Wasserstein metrics, representing an intersection of dataset distillation with the principles of optimal transport theory.
- By leveraging the Wasserstein barycenter [\(Agueh and Carlier,](#page-11-5) [2011\)](#page-11-5) and an efficient dataset distil-lation method [\(Yin et al.,](#page-12-12)  $2023$ ), we have crafted a solution that balances computational feasibility with improved synthetic data quality.

• Comprehensive experimental results across diverse high-resolution datasets highlight the robustness and effectiveness of our approach, showing notable improvements in synthetic data generation when compared to existing methods. This extensive testing demonstrates the practical applicability of our method in condensing large datasets.

The remainder of this paper is organized as follows: Section [2](#page-2-0) introduces related work. Section [3](#page-2-1) provides necessary background about the problem. Section [4](#page-3-0) elucidates the motivation and design of our method. Section [5](#page-6-0) describes our experimental framework and results, and the implications of our findings. Section [6](#page-10-0) concludes our paper with potential directions for future research.

<span id="page-2-0"></span>

# 2 Related Work

## 2.1 Data Distillation

Dataset Distillation (DD) [\(Wang et al.,](#page-12-14) [2018b\)](#page-12-14) is the process of creating a compact, synthetic training set that enables a model to perform similarly on real test data as it would with a larger, actual training set. Previous DD methods can be divided into three major categories [\(Yu et al.,](#page-12-15) [2023\)](#page-12-15): Performance Matching seeks to minimize loss of the synthetic dataset by aligning the performance of models trained on synthetic and original datasets, methods include DD [\(Wang et al.,](#page-12-14) [2018b\)](#page-12-14), FRePo [\(Zhou et al.,](#page-13-0) [2022\)](#page-13-0), AddMem [\(Deng and Russakovsky,](#page-11-9) [2022\)](#page-11-9), KIP [\(Nguyen et al.,](#page-11-2) [2021\)](#page-11-2), RFAD [\(Loo et al.,](#page-11-1) [2022\)](#page-11-1); Parameter Matching is an approach to train two neural networks on the real and synthetic datasets respectively, with the aim to promote similarity in their parameters, methods include DC [\(Zhao et al.,](#page-12-1) [2021\)](#page-12-1), DSA [\(Zhao and Bilen,](#page-12-6) [2021\)](#page-12-6), MTT [\(Cazenavette et al.,](#page-11-3) [2022\)](#page-11-3), HaBa [\(Liu et al.,](#page-11-10) [2022\)](#page-11-10), FTD [\(Du et al.,](#page-11-11) [2023\)](#page-11-11), TESLA [\(Cui et al.,](#page-11-12) [2023\)](#page-11-12); Distribution Matching aims to obtain synthetic data that closely matches the distribution of real data, methods include DM [\(Zhao and Bilen,](#page-12-8) [2023\)](#page-12-8), IT-GAN [\(Zhao and Bilen,](#page-12-16) [2022b\)](#page-12-16), KFS [\(Lee et al.,](#page-11-13) [2022\)](#page-11-13), CAFE [\(Wang et al.,](#page-12-9) [2022\)](#page-12-9), SRe2L [\(Yin et al.,](#page-12-12) [2023\)](#page-12-12), IDM [\(Zhao et al.,](#page-12-13) [2023\)](#page-12-13).

## 2.2 Distribution Matching

Distribution Matching (DM) techniques, initially proposed in [Zhao and Bilen](#page-12-17) [\(2022a\)](#page-12-17), aim to directly align the probability distributions of the original and synthetic datasets [\(Sachdeva and McAuley,](#page-12-4) [2023;](#page-12-4) [Geng et al.,](#page-11-14) [2023\)](#page-11-14). The fundamental premise underlying these methods is that when two datasets exhibit similarity based on a specific distribution divergence metric, they lead to comparably trained models [\(Lei](#page-11-15) [and Tao,](#page-11-15) [2024\)](#page-11-15). DM typically employs parametric encoders for projecting data onto a lower dimensional latent space, and approximates the Maximum Mean Discrepancy for assessing distribution mismatch [\(Yin et al.,](#page-12-12) [2023;](#page-12-12) [Wang et al.,](#page-12-9) [2022;](#page-12-9) [Zhao et al.,](#page-12-13) [2023;](#page-12-13) [Zhang et al.,](#page-12-18) [2024;](#page-12-18) [Sun et al.,](#page-12-19) [2023;](#page-12-19) [Zhao and](#page-12-17) [Bilen,](#page-12-17) [2022a\)](#page-12-17). Notably, DM avoids reliance on model parameters and bilevel optimization, diverging from gradient and trajectory matching approaches. This distinction reduces memory requirements. However, the empirical evidence so far suggests that DM may underperform compared to the other approaches [\(Lei and Tao,](#page-11-15) [2024;](#page-11-15) [Zhang et al.,](#page-12-18) [2024\)](#page-12-18).

<span id="page-2-1"></span>

# 3 Preliminary

We begin by an overview of Dataset Distillation and the Wasserstein barycenter.

## 3.1 Dataset Distillation

Notations Let T denote the real dataset with n distinct input-label pairs  $(\mathbf{x}_i, y_i)$ , such that  $\mathbf{x}_i \sim$  $\mu_{\mathcal{T}}$ ,  $i = 1, \ldots, n$ . Let S denote the synthetic dataset with up to m distinct input-label pairs  $(\tilde{\mathbf{x}}_j, \tilde{y}_j)$ , such that  $\tilde{\mathbf{x}}_j \sim \mu_{\mathcal{S}}, j = 1, \ldots, m$ . Each input datapoint is defined on  $\Omega = \mathbb{R}^d$ . An empirical distribution can be specified by a set of distinct datapoints (or later termed "positions") and a probability distribution over it. Let  $\mathbf{X} \in \mathbb{R}^{n \times d}$  and  $\tilde{\mathbf{X}} \in \mathbb{R}^{m \times d}$  denote the unique positions in  $\mathcal{T}$  and  $\mathcal{S}$  respectively, and let w denote the probability distribution for  $\tilde{\mathbf{X}}$ , where  $w_j$  is the probability (or later termed "weight") associated with  $\tilde{\mathbf{x}}_i$ .  $\ell(\mathbf{x}, y; \boldsymbol{\theta})$  denotes the loss function of a model parameterized by  $\boldsymbol{\theta}$  on a sample  $(\mathbf{x}, y)$ .

Dataset Distillation (DD) aims at finding the optimal synthetic set  $S^*$  for a given  $\mathcal T$  by solving a bi-level optimization problem as below:

$$
S^* = \underset{\mathcal{S}}{\arg\min} \underset{(\mathbf{x}, y) \sim \mu_{\mathcal{T}}}{\mathbb{E}} \ell(\mathbf{x}, y; \theta(\mathcal{S}))
$$
(1)

subject to 
$$
\boldsymbol{\theta}(\mathcal{S}) = \arg\min_{\boldsymbol{\theta}} \sum_{i=1}^{m} \ell(\tilde{\mathbf{x}}_i, \tilde{y}_i; \boldsymbol{\theta}).
$$
 (2)

Solving the bi-level optimization problem directly poses significant challenges. As a viable alternative, a prevalent approach [\(Zhao and Bilen,](#page-12-8) [2023;](#page-12-8) [Wang et al.,](#page-12-9) [2022;](#page-12-9) [Sajedi et al.,](#page-12-20) [2022;](#page-12-20) [Yin et al.,](#page-12-12) [2023\)](#page-12-12) seeks to align the distribution of the synthetic dataset with that of the real dataset. This strategy is based on the assumption that the optimal synthetic dataset should be the one that is distributionally closest to the real dataset subject to a fixed number of synthetic data points. We label this as Assumption A1. While recent methods grounded on this premise have shown promising empirical results, they have not yet managed to bridge the performance gap with state-of-the-art techniques.

### 3.2 Wasserstein Barycenter

A key component in our method is the computation of representative features using the Wasserstein Barycenter [\(Agueh and Carlier,](#page-11-5) [2011\)](#page-11-5). This concept from probability theory and optimal transport extends the idea of the "average" to distributions while considering the geometry of the distribution space [\(Vidal et al.,](#page-12-21) [2019;](#page-12-21) [Cuturi and Doucet,](#page-11-7) [2014\)](#page-11-7). Its definition relies on the Wasserstein Distance, which quantifies the dissimilarity between two probability distributions.

**Definition 1** (Wasserstein Distance): For probability distributions  $\mu$  and  $\nu$  on a metric space  $\Omega$ endowed with a distance metric  $D$ , the  $p$ -Wasserstein distance is given by

$$
W_p(\mu, \nu) \stackrel{\text{def}}{=} \left( \inf_{\pi \in \Pi(\mu, \nu)} \int_{\Omega^2} D(x, y)^p d\pi(x, y) \right)^{\frac{1}{p}}.
$$
 (3)

Here,  $\mu$  and  $\nu$  are the distributions we aim to find the distance between. The space of all such distributions on  $\Omega$  is denoted by  $P(\Omega)$ .  $D(x, y)$  represents the distance between any two points x and y in  $\Omega$ . The collection  $\Pi(\mu, \nu)$  denotes the set of all couplings or joint distributions  $\pi$  on  $\Omega \times \Omega$  that have  $\mu$  and  $\nu$  as marginals. Intuitively, the Wasserstein distance quantifies the minimum "work" required to rearrange  $\mu$  into  $\nu$ , with the "effort" of moving each mass unit captured by the p-th power of the distance  $D$ .

For the notion of central tendency among a collection of distributions in the Wasserstein space, we introduce the concept of Wasserstein Barycenters.

**Definition 2** (Wasserstein Barycenters): For a collection of N distributions  $\nu_1, \ldots, \nu_N$ , in,  $\mathbb{P} \subset P(\Omega)$ , a Wasserstein barycenter is the solution to the optimization problem:

$$
\underset{\mu \in \mathbb{P}}{\arg \min} f(\mu) \quad \text{where} \quad f(\mu) \stackrel{\text{def}}{=} \frac{1}{N} \sum_{i=1}^{N} W_p^p(\mu, \nu_i). \tag{4}
$$

Here, P represents a subset of the distributions in  $P(\Omega)$ , and the term  $W_p^p(\mu, \nu_i)$  denotes the p-Wasserstein distance to the power of p between distribution  $\mu$  and each distribution  $\nu_i$  in the collection.

Computation To compute the Wasserstein Barycenter given a collection of distributions, we adapt the free-support Barycenter computation algorithm proposed in [Cuturi and Doucet](#page-11-7) [\(2014\)](#page-11-7), which uses an alternating optimization scheme (see Algorithm 2 in the appendix). The complete framework involves the joint optimization of both the datapoints in the support of the Wasserstein barycenter, and the weights assigned to them.

## <span id="page-3-0"></span>4 Method

Given the intuitive design of Wasserstein distance as a metric to describe the distances between distributions and its strong empirical performances (Fig. [1\)](#page-1-0), we believe the strengths of Wasserstein distance can boost the performance to bridge the gap and even surpass the SOTA techniques. In this section, we first introduce the potential connections between Wasserstein barycenter and DD, and then detail the computation of the barycenter and the method design.

### 4.1 Wasserstein Barycenter in DD

To begin with, we represent both the real and synthetic dataset as empirical distributions. With no prior knowledge about the samples in the real dataset, and assuming there are no repetitive samples, it is common to assume a discrete uniform distribution over the observed data points. Consequently, the empirical distribution of the real dataset  $\mathcal T$  is:

$$
\mu_{\mathcal{T}} = \frac{1}{n} \sum_{i=1}^{n} \delta_{\mathbf{x}_i},\tag{5}
$$

where  $\delta_{\mathbf {x}_i}$  denotes the Dirac delta function centered at the position  $\mathbf {x}_i$ .

This function is zero everywhere except at  $x_i$ , where it is infinitely high, ensuring its integral over the entire space equals 1.  $\mu_{\mathcal{T}}$  is defined on the set  $\mathbb{P}_n$  of probability distributions that are supported by at most n points in  $\mathbb{R}^d$ , where n represents the size of the real dataset and d denotes the input dimension.

For the synthetic dataset  $S$ , its empirical distribution is:

$$
\mu_{\mathcal{S}} = \sum_{j=1}^{m} w_j \delta_{\tilde{\mathbf{x}}_j},\tag{6}
$$

where  $\sum_{j=1}^{m} w_j = 1$  and each  $w_j \geq 0$ . The probability can be learned to allow more flexibility.  $\mu_{\mathcal{S}}$  is defined on the set  $\mathbb{P}_m$  of probability distributions that are supported by at most m points in  $\mathbb{R}^d$ .

Based on Assumption A1 and chosen metric, the optimal synthetic dataset  $S^*$  should generate an empirical distribution which is the barycenter of the real dataset  $\mathcal{T}$ ,

$$
\mu_{\mathcal{S}^*} = \mu_{\mathcal{S}}^* = \underset{\mu_{\mathcal{S}} \in \mathbb{P}_m}{\arg \min} W_p^p(\mu_{\mathcal{S}}, \mu_{\mathcal{T}}),\tag{7}
$$

where  $\mu_{\mathcal{S}^*}$  denotes the empirical distribution of the optimal dataset,  $\mu_{\mathcal{S}}^*$  denotes the optimal empirical distribution, and  $W_p$  is the Wasserstein distance. This positions the dataset distillation process as a search for the Wasserstein Barycenter of the empirical distribution derived from  $\mathcal{T}$ . Since the barycenter is specified by the matrix of positions  $\tilde{\mathbf{X}}$  and the probability distribution w, we can reframe this problem as minimizing the below function:

$$
f(\mathbf{w}, \mathbf{X}) := W_p^p(\mu_{\mathcal{S}}, \mu_{\mathcal{T}}). \tag{8}
$$

In the below subsection, we discuss our method to solve this problem.

<span id="page-4-1"></span>

### 4.2 Computation of the Barycenter

With formulation provided above, we now proceed to discuss the optimization of the function  $f(\mathbf{w}, \tilde{\mathbf{X}}) =$  $W_n^p(\mu_{\mathcal{S}}, \mu_{\mathcal{T}})$ . We will optimize the function iteratively with two steps: optimizing the weights **w** given fixed positions  $\tilde {\mathbf {X}}$ , and finding the best positions  $\tilde {\mathbf {X}}$  given the weights.

#### 4.2.1 Optimizing Weights Given Fixed Positions

Given fixed positions  $\tilde{\mathbf{X}}$  in the synthetic data distribution, we define the cost matrix  $C \in \mathbb{R}^{n \times m}$ , where each element  $c_{ij} = \|\tilde {\mathbf {x}}_j - \mathbf {x}_i\|^2$  represents the squared Euclidean distance between positions in the two distributions. We also define a nonnegative matrix  $T \in \mathbb{R}^{n \times m}$  representing the transport plan between the two distributions, where each entry  $T_{ij}$  signifies the amount of mass transported from position i to position j.  $W_p^p(\mu_{\mathcal{S}}, \mu_{\mathcal{T}})$  is equal to the optimal value of the below linear programming problem known as the optimal transport problem:

<span id="page-4-0"></span>
$$
\min_{\mathbf{T}} \langle C, \mathbf{T} \rangle_F \tag{9}
$$

subject to 
$$
\sum_{j=1}^{m} T_{ij} = \frac{1}{n}, \forall i, \quad \sum_{i=1}^{n} T_{ij} = w_j, \forall j, \quad T_{ij} \ge 0, \forall i, j,
$$
 (10)

where  $\langle \cdot, \cdot \rangle_F$  is the Frobenius inner product. The problem is to find the optimal transport plan T that minimizes the total transportation cost.

The dual problem introduces dual variables  $\alpha_i$  and  $\beta_j$  for each constraint, formulated as below:

$$
\max_{\alpha,\beta} \left\{ \sum_{i=1}^{n} \frac{\alpha_i}{n} + \sum_{j=1}^{m} w_j \beta_j \right\} \tag{11}
$$

$$
subject to \quad \alpha_i + \beta_j \le c_{ij}, \forall i, j. \tag{12}
$$

Due to the strong duality of linear programming [\(Boyd and Vandenberghe,](#page-11-16) [2004\)](#page-11-16), the optimal values of the primal and dual problems are equal. The subgradient of the optimal value with respect to  $\bf{w}$  is identified as the vector of optimal dual variables  $\beta_i$ , reflecting the sensitivity of the total cost to changes in the weights. Then, we search for the optimal weights  $w^*$  using projected subgradient descent method, which ensures that the solution remains within the probability simplex.

#### 4.2.2 Optimizing Positions Given Weights

Given the weights w, we seek to optimize the positions  $\tilde{\mathbf{X}}$ . Because the total transport cost in Eq. [9](#page-4-0) is quadratic to  $\tilde{\mathbf{x}}_j$  for all j, it is natural to apply the Newton method for learning the positions, taking each step based on the minima of local quadratic approximation.

The gradient of the cost with respect to  $\tilde{\mathbf{x}}_i$  is:

$$
\nabla_{\tilde{\mathbf{x}}_j} f(\tilde{\mathbf{X}}) = 2 \sum_{i=1}^n T_{ij} (\tilde{\mathbf{x}}_j - \mathbf{x}_i).
$$
 (13)

The Hessian matrix is constant and given by 2I scaled by the weight  $w_j$ , indicating convexity in the optimization landscape for positions.

The Newton update formula for  $\tilde{\mathbf{x}}_i$  is:

$$
\tilde{\mathbf{x}}_j^{\text{(new)}} = \tilde{\mathbf{x}}_j - (2\mathbf{I}w_j)^{-1} \left( 2\sum_{i=1}^n T_{ij}(\tilde{\mathbf{x}}_j - \mathbf{x}_i) \right),\tag{14}
$$

simplified to:

$$
\tilde{\mathbf{x}}_j^{\text{(new)}} = \tilde{\mathbf{x}}_j - \frac{1}{w_j} \sum_{i=1}^n T_{ij} (\tilde{\mathbf{x}}_j - \mathbf{x}_i).
$$
\n(15)

This update mechanism progressively reduces the Wasserstein distance by drawing the synthetic positions closer to the real positions weighted by the transport plan.

Through iterative search on the optimal weights and positions, we obtain a local optima upon convergence, yielding a barycenter of the real data distribution. For a more detailed discussion of this method, please refer to Appendix [C.](#page-14-0)

<span id="page-5-0"></span>

### 4.3 Barycenter Matching in the Feature Space

Our above discussion shows that dataset distillation can be cast as the problem of finding the barycenter of the real data distribution, and we provide a valid approach for computing the barycenter. However, for high dimensional data such as images, it is beneficial to use some prior to learn synthetic images that encode meaningful information from the real dataset. Inspired by recent works [\(Yin et al.,](#page-12-22) [2020,](#page-12-22) [2023\)](#page-12-12), we use a pretrained classifier to embed the images into the feature space, in which we compute the Wasserstein barycenter to learn synthetic images. This subsection details our concrete method, also illustrated in Algorithm [1.](#page-6-1)

Suppose the real dataset  $\mathcal T$  has C classes, with n images per class. Let us re-index the samples by classes and denote the training set as  $\mathcal{T} = {\mathbf{x}_{c,i}}_{i=1,\dots,n}^{c=1,\dots,C}$ . Suppose that we want to distill m images per class. Denote the synthetic set  $S = {\{\tilde{\mathbf{x}}_{c,j}\}}_{j=1,\ldots,m}^{c=1,\ldots,C}$ , where  $m \ll n$ .

First, we employ the pretrained model to extract features for all samples within each class in the original dataset T. More specifically, we use the pretrained model f to get the features  $\{f_e(\mathbf{x}_{c,i})\}_{i=1,\dots,n}$ for each class c, where  $f_e(\cdot)$  is the function that produces the final features before the linear classifier.

Next, we compute the Wasserstein barycenter for each set of features computed in the previous step. We treat the feature set for each class as an empirical distribution, and adapt the algorithm in Algorithm 1: Dataset Distillation via the Wasserstein Metric

<span id="page-6-1"></span>**Input:** Real dataset  $\mathcal{T} = {\mathbf{x}_{c,n}}_{n=1,...,N}^{c=1,...,C}$ 1 Train a teacher model f on  $\mathcal{T}$ ; 2 for each class c do 3 for each sample n do 4 | Perform forward pass:  $f(\mathbf{x}_{c,n});$ 5 Store feature:  $f_e(\mathbf{x}_{c,n});$ 6 Compute  $\text{BN}^{\text{mean}}_{c,l}$ ,  $\text{BN}^{\text{var}}_{c,l}$ ; 7 for each class c do 8  $\{ \mathbf{b}_{c,m} \}_{m=1,\ldots,M} \leftarrow$  barycenter  $(\{f_e(\mathbf{x}_{c,n})\}_{n=1,\ldots,N})$  according to Algorithm [2](#page-16-0) in Appendix [D](#page-16-1); 9 Optimize  $\{\tilde{\mathbf{x}}_{c,m}\}_{m=1,\dots,M}$  according to Eq. [\(18\)](#page-6-2); **Output:** Synthetic dataset  $S = {\{\mathbf{\tilde{x}}_{c,m}\}}_{m=1,...,M}^{c=1,...,C}$ 

[Cuturi and Doucet](#page-11-7) [\(2014\)](#page-11-7) to compute the free support barycenters with m points for class c, denoted as  ${\{\mathbf{b}_{c,j}\}}_{j=1,...,m}$ , where m is the predefined number of synthetic samples per class.

Then, in the main distillation process, we use iterative gradient descent to learn the synthetic images by jointly considering two objectives. We match the features of the synthetic images with the corresponding data points in the learned barycenter:

<span id="page-6-3"></span>
$$
\mathcal{L}_{\text{feature}}(\mathcal{S}) = \sum_{c=1}^{C} \sum_{j=1}^{m} ||f_e(\tilde{\mathbf{x}}_{c,j}) - \mathbf{b}_{c,j}||_2^2,
$$
\n(16)

where  $f_e(\cdot)$  is the function to compute features of the last layer.

To further leverage the capability of the pretrained model in aligning the distributions, we improve upon previous works [\(Yin et al.,](#page-12-22) [2020,](#page-12-22) [2023\)](#page-12-12) to compute the per-class BatchNorm statistics of the real data, for better capturing the intra-class distribution. We employ the below loss function for regularizing the synthetic set  $S$ :

$$
\mathcal{L}_{\text{BN}}(S) = \sum_{c=1}^{C} \sum_{l=1}^{L} \Big( \left\| \mathcal{A}_{\text{mean}} \left( f_l(\tilde{\mathbf{x}}_{c,1}), \dots, f_l(\tilde{\mathbf{x}}_{c,m}) \right) - BN_{c,l}^{\text{mean}} \right\|_2^2 + \left\| \mathcal{A}_{\text{var}} \left( f_l(\tilde{\mathbf{x}}_{c,1}), \dots, f_l(\tilde{\mathbf{x}}_{c,m}) \right) - BN_{c,l}^{\text{var}} \right\|_2^2 \Big). \tag{17}
$$

Here, L is the number of BatchNorm layers, and  $f_l(\cdot)$  is the function that computes intermediate features as the input to the l-th BatchNorm layer.  $\mathcal{A}_{mean}(\cdot)$  and  $\mathcal{A}_{var}(\cdot)$  are the aggregate operators in a standard BatchNorm layer, which compute the per-channel mean and variance of the feature map of the synthetic data.  $BN_{c,l}^{\text{mean}}$  and  $BN_{c,l}^{\text{var}}$  are the mean and variance statistics for class c of the real training set at the l-th BatchNorm layer. These statistics are computed with one epoch of forward pass over the training dataset. Our experiments show that per-class BatchNorm statistics result in significantly better performance of the synthetic data.

<span id="page-6-2"></span>Combining these objectives above, we employ the below loss function for learning the synthetic data:

$$
\mathcal{L}(\mathcal{S}) = \mathcal{L}_{\text{feature}}(\mathcal{S}) + \lambda \mathcal{L}_{\text{BN}}(\mathcal{S}),\tag{18}
$$

where  $\lambda$  is a regularization coefficient.

<span id="page-6-0"></span>

# 5 Experiments

#### 5.1 Experiment Setup

For a systematic evaluation of our method, we conducted experiments on three high-resolution datasets: ImageNette [\(Howard,](#page-11-17) [2019\)](#page-11-17), Tiny ImageNet [\(Le and Yang,](#page-11-18) [2015\)](#page-11-18), and ImageNet-1K [\(Deng et al.,](#page-11-8) [2009\)](#page-11-8). We tested our method under different budges of synthetic images: 1, 10, 50 and 100 images per class (IPC), respectively. We pretrained a ResNet-18 model [\(He et al.,](#page-11-19) [2016\)](#page-11-19) on the real training set, from which we distilled the dataset following our method, and used the synthetic data to train a ResNet-18 model from scratch. Then, the performance of the synthetic dataset is measured by the top-1 accuracy of the trained model on the validation set. We report the mean accuracy of the model on the validation set, repeated across 3 different runs. Our implementation of the barycenter algorithm was partly based on the Python Optimal Transport library [\(Flamary et al.,](#page-11-20) [2021\)](#page-11-20). We keep most of the hyperparameter settings the same with [\(Yin et al.,](#page-12-12) [2023\)](#page-12-12), but set the regularization coefficient  $\lambda$  differently to account for our different loss terms. We set  $\lambda$  to be 500, 300, 10 for ImageNet, Tiny ImageNet, and ImageNette, respectively. For more implementation details please refer to Appendix [E.](#page-16-2)

### 5.2 Comparison with Other Methods

<span id="page-7-0"></span>Table 1: Performance comparison of various dataset distillation methods on different datasets. We used the reported results for baseline methods when available. We replicated the result of  $SRe^{2}L$  on the ImageNette dataset, marked by <sup>†</sup>. Results of CDA did not include error bars, and the row is marked by <sup>‡</sup>.

| Methods                        | ImageNette             |                        |                          | Tiny ImageNet          |             |                                                    | $ImageNet-1K$ |                                                            |             |              |              |              |
|--------------------------------|------------------------|------------------------|--------------------------|------------------------|-------------|----------------------------------------------------|---------------|------------------------------------------------------------|-------------|--------------|--------------|--------------|
|                                |                        | 10                     | 50                       | 100                    |             | 10                                                 | 50            | 100                                                        |             | 10           | 50           | 100          |
| Random (Zhao and Bilen, 2023)  | $23.5 + 4.8$           | $47.7 + 2.4$           | $\overline{\phantom{a}}$ | ۰                      | $1.5 + 0.1$ | $6.0 + 0.8$                                        | $16.8 + 1.8$  | ٠                                                          | $0.5 + 0.1$ | $3.6 + 0.1$  | $15.3 + 2.3$ |              |
| DM (Zhao and Bilen, 2023)      | $32.8 \pm 0.5$         | $58.1 + 0.3$           |                          |                        | $3.9 + 0.2$ | $12.9 + 0.4$                                       | $24.1 + 0.3$  |                                                            | $1.5 + 0.1$ | <b>COL</b>   | ж.           |              |
| MTT (Cazenavette et al., 2022) | $47.7 + 0.9$           | $63.0 + 1.3$           |                          |                        | $8.8 + 0.3$ | $23.2 + 0.2$                                       | $28.0 + 0.3$  | $\sim$                                                     |             |              |              |              |
| DataDAM (Sajedi et al., 2022)  | $34.7 + 0.9$           | $59.4 + 0.4$           |                          |                        | $8.3 + 0.4$ | $18.7 + 0.3$                                       | $28.7 + 0.3$  | ٠                                                          | $2.0 + 0.1$ | $6.3 + 0.0$  | $15.5 + 0.2$ |              |
| $SRe2L$ (Yin et al., 2023)     | $20.6^{\dagger} + 0.3$ | $54.2^{\dagger} + 0.4$ | $80.4^{\dagger} + 0.4$   | $85.9^{\dagger} + 0.2$ |             |                                                    | $41.1 + 0.4$  | $49.7 + 0.3$                                               | $\sim$      | $21.3 + 0.6$ | $46.8 + 0.2$ | $52.8 + 0.4$ |
| $CDA‡$ (Yin and Shen, 2023)    |                        |                        |                          |                        |             |                                                    | 48.7          | 53.2                                                       |             |              | 53.5         | 58.0         |
| WMDD (Ours)                    | $40.2 + 0.6$           | $64.8 + 0.4$           | $83.5 + 0.3$             |                        |             | <b>87.1</b> $\pm$ 0.3 7.6 $\pm$ 0.2 41.8 $\pm$ 0.1 |               | $59.4 \pm 0.5$ 61.0 $\pm$ 0.3 3.2 $\pm$ 0.3 38.2 $\pm$ 0.2 |             |              | $57.6 + 0.5$ | $60.7 + 0.2$ |

Currently, the repertoire of dataset distillation methods capable of performing well on ImageNet-scale datasets is limited, resulting in a constrained set of options for benchmark comparisons. Table [1](#page-7-0) shows our experiment results, as well as the reported results from several baselines and contemporary methods in the same setting. Our method consistently showed SOTA performance in most settings across different datasets. Compared to MTT [\(Cazenavette et al.,](#page-11-3) [2022\)](#page-11-3) and DataDAM [\(Sajedi et al.,](#page-12-20) [2022\)](#page-12-20), which show good performance in fewer IPC settings, the performance of our method increases more rapidly with the number of synthetic images. Notably, the top-1 accuracy of our method in the 100 IPC setting are 87.1%, 61.0%, 60.7%, on the three datasets respectively, drawing close to those of the pretrained classifiers used in our experiment, i.e., 89.9%, 63.5%, and 63.1%, each pretrained on the full training set. This superior performance underscores the effectiveness and robustness of our approach in achieving higher accuracy across different datasets.

### 5.3 Cross-architecture Generalization

Synthetic data often overfit to the model used to learn them, a common issue found in dataset distillation research [\(Cazenavette et al.,](#page-11-21) [2023;](#page-11-21) [Lei and Tao,](#page-11-22) [2022\)](#page-11-22). To evaluate whether our synthetic datasets can adapt to new architectures not seen in the distillation process, we did experiments using our learned data to train different models that were randomly initialized, and evaluated their performance on the validation set. We used ResNet-18 to learn the synthetic data, and report the performance of different evaluation models, namely ResNet-18, ResNet-50, ResNet-101 [\(He et al.,](#page-11-19) [2016\)](#page-11-19) and ViT-Tiny [\(Dosovitskiy et al.,](#page-11-23) [2020\)](#page-11-23), in both 10 and 50 IPC settings. The results are shown in Table [2.](#page-8-0) It can be seen that our synthetic data generalize well to other models in the ResNet family, and the performance increases with the model capacity in the settings where the data size is relatively large. Our synthetic dataset shows lower performance on the vision transformer, probably due to its data-hungry property.

<span id="page-7-1"></span>

### 5.4 Ablation Study

To identify the design components in our method that contribute to the improved performance, we conduct ablation study on several important variables: whether to include the cross-entropy loss in [\(Yin](#page-12-12) [et al.,](#page-12-12) [2023\)](#page-12-12) to maximize the class probability from the pretrained model, whether to include the feature matching loss based on Wasserstein barycenter (Eq. [\(16\)](#page-6-3)), and whether to choose standard BatchNorm or per-class BatchNorm statistics for regularization. We experimented these variables on different datasets under the 10 IPC setting. The results in Table [3](#page-8-1) show that compared with the baseline method (Row 5), choosing per-class BatchNorm regularization effectively improves the performance on all datasets (Row 3), and it is best used in combination with our barycenter loss (Row 1). When we additionally use the CE loss (Row 2), the performance does not improve in general, which shows that the distribution matching objectives are enough to learn high-quality datasets.

<span id="page-8-0"></span>Table 2: Cross-architecture generalization performance on Tiny-ImageNet and ImageNet in different IPC settings. We used ResNet-18 for distillation and different architectures for evaluation: ResNet- $\{18,50,101\}$  and ViT-Tiny with a patch size of 16.

| Dataset       | IPC | Evaluation Model |           |            |       |
|---------------|-----|------------------|-----------|------------|-------|
|               |     | ResNet-18        | ResNet-50 | ResNet-101 | ViT-T |
| Tiny-ImageNet | 10  | 41.8             | 34.7      | 35.3       | 11.7  |
|               | 50  | 59.4             | 60.5      | 60.7       | 29.0  |
| ImageNet-1K   | 10  | 38.2             | 43.7      | 42.1       | 7.5   |
|               | 50  | 57.6             | 61.3      | 62.2       | 30.7  |

<span id="page-8-1"></span>Table 3: Ablation study on three variables: whether to include the cross-entropy loss, whether to choose standard BatchNorm or per-class BatchNorm statistics for regularization, and whether to include the feature matching loss based on Wasserstein barycenter.

| $\mathcal{L}_{\mathrm{CE}}$ | $\mathcal{L}_{\rm BN}$ | $\mathcal{L}_{\rm Bary}$ | ImageNette      | Tiny ImageNet   | $ImageNet-1K$   |
|-----------------------------|------------------------|--------------------------|-----------------|-----------------|-----------------|
| X                           | $\checkmark$           | $\checkmark$             | $64.8_{\pm}0.4$ | $41.8_{\pm}0.1$ | $38.2_{\pm}0.2$ |
| $\checkmark$                | $\checkmark$           | $\checkmark$             | $63.9_{\pm}0.3$ | $42.1_{\pm}0.5$ | $37.9_{\pm}0.3$ |
| $\checkmark$                | $\checkmark$           | X                        | $64.0_{\pm}0.1$ | $41.3_{\pm}0.4$ | $37.4_{\pm}0.5$ |
| X                           | X                      | $\checkmark$             | $60.7_{\pm}0.2$ | $36.5_{\pm}0.2$ | $26.7_{\pm}0.3$ |
| $\checkmark$                | X                      | X                        | $54.2_{\pm}0.4$ | $38.1_{\pm}0.2$ | $36.1_{\pm}0.1$ |

Hyperparameter Sensitivity To better understand the effect of the regularization term in our method (Eq. [\(18\)](#page-6-2)), and how the regularization coefficient  $\lambda$  affects the performance, we tested a broad range of values for  $\lambda$ . We experimented with  $\lambda$  ranging from  $10^{-1}$  to  $10^3$ , and evaluated the performance of our distilled data on the three datasets under 10 IPC setting. Fig. [2a](#page-8-2) shows that when  $\lambda$  is small, the performance on all three datasets are lower; as  $\lambda$  gradually increases, the performance first increases and then keeps relatively stable. This shows that the regularization is indeed useful for learning high-quality datasets, particularly in many-class settings.

The impact of regularization is also evident through visual inspection. Fig. [2b](#page-8-2) shows some exemplar synthetic images of the same class. When  $\lambda$  is too small to yield descent performance, the synthetic image contains high-frequency components which may indicate overfitting to the specific model weights and architecture, therefore compromising their generalizability. Synthetic images with a sufficiently large  $\lambda$  tend to align better with human perception.

<span id="page-8-2"></span>Image /page/8/Figure/6 description: The image displays a line graph titled "Performance Trends across Datasets" on the left and a grid of synthetic images on the right. The line graph plots "Performance Metric" on the y-axis against "λ (logarithmic scale, base 10)" on the x-axis. Three lines are shown, representing "ImageNet" (blue circles), "Imagenette" (red squares), and "Tiny ImageNet" (brown triangles). The ImageNet line starts at approximately 12, rises to about 35 at λ=10, and then to about 37 at λ=1000. The Imagenette line starts at approximately 58, stays around 58-60 from λ=0.1 to λ=100, and drops slightly to about 58 at λ=1000. The Tiny ImageNet line starts at approximately 32, rises to about 39 at λ=1, and then fluctuates between 37 and 39 from λ=10 to λ=1000. The right side of the image shows four rows of synthetic images, each row corresponding to a different value of λ: λ = 0.1, λ = 1, λ = 100, and λ = 1000. Each row contains two images. The images generally depict birds and fish in various natural settings, with increasing levels of abstraction or distortion as λ increases. Below the synthetic images, there is a caption indicating "(b) Visualization of synthetic images from Imagenet-1K of..."

(a) Hyperparameter sensitivity of  $\lambda$  on the three datasets.

(b) Visualization of synthetic images from Imagenet-1K of classes indigo bird (left) and tiger shark (right), with different  $\lambda$ .

Figure 2: Analysis of  $\lambda$  hyperparameter effects on synthetic images.

### 5.5 Comparison with the MMD Metric

Central to our method is the use of the Wasserstein metric, which sidesteps the limitations associated with selecting suitable kernels for the MMD metric and its dependency on empirical estimations. As demonstrated in Section [5.4,](#page-7-1) the Wasserstein metric outperforms baseline settings. To critically assess the impact of the MMD and Wasserstein metrics in DD, we compared our method with a seminal MMDbased method [\(Zhao and Bilen,](#page-12-8) [2023\)](#page-12-8) on Tiny-ImageNet. To ensure a fair comparison, we remove all the tricks including fancy augmentations (e.g. rotation, color jitter and mixup) used in both methods and FKD [\(Shen and Xing,](#page-12-24)  $2022$ ) used in  $S\text{Re}^2L$ . We only use random crop and horizontal flip student training. According to the result in Table [4,](#page-9-0) the Wasserstein metric yields better synthetic data in all settings, echoing our prior discussion on its advantages. In 1 IPC setting, the performance of the MMD metric was compromised, likely due to empirical approximation errors and its focus on feature means rather than their geometric properties.

<span id="page-9-0"></span>Table 4: Performance comparison of MMD distance vs. the Wasserstein distance. The evaluation model is ResNet18.

| IPC.        |              | 10            | 50            |
|-------------|--------------|---------------|---------------|
| MMD         | $0.48 + 0.1$ | $9.03 + 0.0$  | $19.10 + 0.1$ |
| Wasserstein | $5.77 + 0.2$ | $10.45 + 0.4$ | $20.41 + 0.1$ |

<span id="page-9-2"></span>

### 5.6 Feature Embedding Distribution

This subsection presents a visualization analysis on how our synthetic data are distributed relative to the real data. To map the real data and synthetic data into the same feature space, we train a model from scratch on a mixture of both data. Then we extract their last-layer features and use the t-SNE [\(Van der](#page-12-25) [Maaten and Hinton,](#page-12-25) [2008\)](#page-12-25) method to visualize their distributions on a 2D plane. For a comparison, we conduct this process for the synthetic data obtained with both our method and the  $SRe^{2}L$  [\(Yin](#page-12-12) [et al.,](#page-12-12) [2023\)](#page-12-12) method as a baseline. Fig. [3](#page-9-1) shows the result. In the synthetic images learned by  $SRe^{2}L$ , synthetic images within the same class tend to collapse, and those from different classes tend to be far apart. This is probably a result of the cross-entropy loss they used, which optimizes the synthetic images toward maximal output probability from the pretrained model. On the other hand, our utilization of the Wasserstein metric enables synthetic images to better represent the distribution of real data.

<span id="page-9-1"></span>Image /page/9/Figure/6 description: The image displays two scatter plots side-by-side, each visualizing clusters of data points. Both plots use a legend indicating 'Original' data points as light pink circles and 'Synthetic' data points as blue triangles. The left plot shows several distinct clusters of points in various colors, including red, orange, yellow, green, blue, and purple, with a few blue triangles scattered within or around these clusters. The right plot presents a similar arrangement of colored clusters, but with a more pronounced distribution of blue triangles, often appearing within the colored clusters, suggesting a comparison or overlay of original and synthetic data.

Figure 3: Distribution visualization of ImageNette. The dots present the distribution of the original dataset using the latent space of the model (e.g. ResNet-101) and the triangles are distilled images. Left: data distilled by SRe<sup>2</sup>L; Right: data distilled by our method.

### 5.7 Increased Variety in Synthetic Images

Visualization of the synthetic images at the pixel level corroborates our finding in Section [5.6,](#page-9-2) with the ImageNet-1K Hay class being one such example, as shown in Fig. [4.](#page-10-1) Compared to the  $SRe^{2}L$ baseline synthetic images, our method leads to improved variety in both the background and foreground information contained in synthetic images. By covering the variety of images in the real data distribution, our method prevents the model from relying on a specific background color or object layout as heuristics

for prediction, thus alleviating the potential overfitting problem and improving the generalization of the model. We provide more visualization on three datasets in Appendix [G.](#page-17-0)

<span id="page-10-1"></span>Image /page/10/Picture/1 description: The image displays two rows of synthetic images, all related to the class 'Hay' with a class ID of 958. The top row contains 10 images, each depicting abstract, somewhat blurry representations of hay bales and possibly farm equipment or structures. The bottom row contains 12 images, which are also abstract representations of hay bales in various outdoor settings, including fields, skies, and water. Some of these images are more colorful and detailed than those in the top row, with some appearing to be artistic interpretations of hay bales.

Our synthetic images of the class Hay (classId: 958)

Figure 4: Visualizations of our synthetic images vs.  $SRe^{2}L$  baseline synthetic images from ImageNet-1K Hay class (classId: 958).

<span id="page-10-0"></span>

# 6 Conclusion

This work introduces a new dataset distillation approach leveraging Wasserstein metrics, grounded in optimal transport theory, to achieve more precise distribution matching. Our method learns synthetic datasets by matching the Wasserstein barycenter of the data distribution in the feature space of pretrained models, combined with a simple regularization technique to leverage the prior knowledge in these models. Through rigorous empirical testing, our approach has demonstrated impressive performance across a variety of benchmarks, highlighting its reliability and practical applicability in diverse scenarios. Findings from our controlled experiments corroborates the utility of Wasserstein metrics for capturing the essence of data distributions. Future work will aim to expand the applicability and efficiency of dataset distillation, exploring the integration of advanced metrics with generative methods and investigating the trustworthy properties of synthetic data. This endeavor aligns with the broader goal of advancing data efficiency, promising to contribute significantly to the field of computer vision.

## References

- <span id="page-11-5"></span>M. Agueh and G. Carlier. Barycenters in the wasserstein space. SIAM Journal on Mathematical Analysis, 43(2): 904–924, 2011.
- <span id="page-11-16"></span>S. P. Boyd and L. Vandenberghe. Convex optimization. Cambridge university press, 2004.
- <span id="page-11-3"></span>G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2022.
- <span id="page-11-21"></span>G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Generalizing dataset distillation via deep generative prior. CVPR, 2023.
- <span id="page-11-12"></span>J. Cui, R. Wang, S. Si, and C.-J. Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In International Conference on Machine Learning, pages 6565–6590. PMLR, 2023.
- <span id="page-11-7"></span>M. Cuturi and A. Doucet. Fast computation of wasserstein barycenters. In International conference on machine learning, pages 685–693. PMLR, 2014.
- <span id="page-11-8"></span>J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei. Imagenet: A large-scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pages 248–255. Ieee, 2009.
- <span id="page-11-9"></span>Z. Deng and O. Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. Advances in Neural Information Processing Systems, 35:34391–34404, 2022.
- <span id="page-11-23"></span>A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly, et al. An image is worth  $16x16$  words: Transformers for image recognition at scale.  $arXiv$ preprint arXiv:2010.11929, 2020.
- <span id="page-11-11"></span>J. Du, Y. Jiang, V. Y. Tan, J. T. Zhou, and H. Li. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3749–3758, 2023.
- <span id="page-11-20"></span>R. Flamary, N. Courty, A. Gramfort, M. Z. Alaya, A. Boisbunon, S. Chambon, L. Chapel, A. Corenflos, K. Fatras, N. Fournier, L. Gautheron, N. T. Gayraud, H. Janati, A. Rakotomamonjy, I. Redko, A. Rolet, A. Schutz, V. Seguy, D. J. Sutherland, R. Tavenard, A. Tong, and T. Vayer. Pot: Python optimal transport. Journal of Machine Learning Research, 22(78):1–8, 2021. URL <http://jmlr.org/papers/v22/20-451.html>.
- <span id="page-11-14"></span>J. Geng, Z. Chen, Y. Wang, H. Woisetschlaeger, S. Schimmler, R. Mayer, Z. Zhao, and C. Rong. A survey on dataset distillation: Approaches, applications and future directions, 2023.
- <span id="page-11-0"></span>J. Goetz and A. Tewari. Federated learning via synthetic data. arXiv preprint arXiv:2008.04489, 2020.
- <span id="page-11-4"></span>A. Gretton, K. M. Borgwardt, M. J. Rasch, B. Schölkopf, and A. Smola. A kernel two-sample test. The Journal of Machine Learning Research, 13(1):723–773, 2012.
- <span id="page-11-19"></span>K. He, X. Zhang, S. Ren, and J. Sun. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770–778, 2016.
- <span id="page-11-17"></span>J. Howard. Imagenette dataset, 2019. Available at: https://github.com/fastai/imagenette.
- <span id="page-11-6"></span>S. Ioffe and C. Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In International Conference on Machine Learning, pages 448–456. PMLR, 2015.
- <span id="page-11-18"></span>Y. Le and X. Yang. Tiny imagenet visual recognition challenge. CS 231N, 7(7):3, 2015.
- <span id="page-11-13"></span>H. B. Lee, D. B. Lee, and S. J. Hwang. Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494, 2022.
- <span id="page-11-22"></span>S. Lei and D. Tao. A comprehensive survey of dataset distillation.  $arXiv$  preprint  $arXiv:2301.05603$ , 2022.
- <span id="page-11-15"></span>S. Lei and D. Tao. A comprehensive survey of dataset distillation. IEEE Transactions on Pattern Analysis and Machine Intelligence, 46(1):17–32, Jan. 2024. ISSN 1939-3539. doi: 10.1109/tpami.2023.3322540. URL <http://dx.doi.org/10.1109/TPAMI.2023.3322540>.
- <span id="page-11-10"></span>S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang. Dataset distillation via factorization. Advances in Neural Information Processing Systems, 35:1100–1113, 2022.
- <span id="page-11-1"></span>N. Loo, R. Hasani, A. Amini, and D. Rus. Efficient dataset distillation using random feature approximation. In A. H. Oh, A. Agarwal, D. Belgrave, and K. Cho, editors, Advances in Neural Information Processing Systems, 2022. URL <https://openreview.net/forum?id=h8Bd7Gm3muB>.
- <span id="page-11-24"></span>T. maintainers and contributors. Torchvision: Pytorch's computer vision library. [https://github.com/pytorch/](https://github.com/pytorch/vision) [vision](https://github.com/pytorch/vision), 2016.
- <span id="page-11-2"></span>T. Nguyen, Z. Chen, and J. Lee. Dataset meta-learning from kernel ridge-regression. In International Conference on Learning Representations, 2021. URL <https://openreview.net/forum?id=l-PrrQrK0QR>.

- <span id="page-12-4"></span>N. Sachdeva and J. McAuley. Data distillation: A survey, 2023.
- <span id="page-12-20"></span>A. Sajedi, S. Khaki, E. Amjadian, L. Z. Liu, Y. A. Lawryshyn, and K. N. Plataniotis. Datadam: Efficient dataset distillation with attention matching. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 17096–17107, 2022.
- <span id="page-12-24"></span>Z. Shen and E. Xing. A fast knowledge distillation framework for visual recognition. In European Conference on Computer Vision, pages 673–690. Springer, 2022.
- <span id="page-12-7"></span>S. Shin, H. Bae, D. Shin, W. Joo, and I.-C. Moon. Loss-curvature matching for dataset selection and condensation, 2023.
- <span id="page-12-3"></span>F. P. Such, A. Rawal, J. Lehman, K. Stanley, and J. Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In International Conference on Machine Learning, pages 9206–9216. PMLR, 2020.
- <span id="page-12-19"></span>P. Sun, B. Shi, D. Yu, and T. Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm, 2023.
- <span id="page-12-10"></span>I. O. Tolstikhin, B. K. Sriperumbudur, and B. Schölkopf. Minimax estimation of maximum mean discrepancy with radial kernels. In D. Lee, M. Sugiyama, U. Luxburg, I. Guyon, and R. Garnett, editors, Advances in Neural Information Processing Systems, volume 29. Curran Associates, Inc., 2016. URL [https://proceedings.](https://proceedings.neurips.cc/paper_files/paper/2016/file/5055cbf43fac3f7e2336b27310f0b9ef-Paper.pdf) [neurips.cc/paper\\_files/paper/2016/file/5055cbf43fac3f7e2336b27310f0b9ef-Paper.pdf](https://proceedings.neurips.cc/paper_files/paper/2016/file/5055cbf43fac3f7e2336b27310f0b9ef-Paper.pdf).
- <span id="page-12-25"></span>L. Van der Maaten and G. Hinton. Visualizing data using t-sne. Journal of machine learning research, 9(11), 2008.
- <span id="page-12-21"></span>J. Vidal, J. Budin, and J. Tierny. Progressive wasserstein barycenters of persistence diagrams. IEEE Transactions on Visualization and Computer Graphics, page 1–1, 2019. ISSN 2160-9306. doi: 10.1109/tvcg.2019.2934256. URL <http://dx.doi.org/10.1109/TVCG.2019.2934256>.
- <span id="page-12-11"></span>C. Villani. Optimal Transport: Old and New, volume 338. Springer Science & Business Media, 2008.
- <span id="page-12-9"></span>K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You. Cafe: Learning to condense dataset by aligning features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 12196–12205, 2022.
- <span id="page-12-0"></span>T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018a.
- <span id="page-12-14"></span>T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018b.
- <span id="page-12-5"></span>T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. arXiv preprint arXiv:2006.08545, 2020.
- <span id="page-12-22"></span>H. Yin, P. Molchanov, J. M. Alvarez, Z. Li, A. Mallya, D. Hoiem, N. K. Jha, and J. Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 8715–8724, 2020.
- <span id="page-12-23"></span>Z. Yin and Z. Shen. Dataset distillation in large data era, 2023.
- <span id="page-12-12"></span>Z. Yin, E. Xing, and Z. Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective, 2023.
- <span id="page-12-15"></span>R. Yu, S. Liu, and X. Wang. Dataset distillation: A comprehensive review, 2023.
- <span id="page-12-18"></span>H. Zhang, S. Li, P. Wang, D. Zeng, and S. Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy, 2024.
- <span id="page-12-2"></span>J. Zhang, C. Chen, B. Li, L. Lyu, S. Wu, S. Ding, C. Shen, and C. Wu. DENSE: Data-free one-shot federated learning. In A. H. Oh, A. Agarwal, D. Belgrave, and K. Cho, editors, Advances in Neural Information Processing Systems, 2022. URL <https://openreview.net/forum?id=QFQoxCFYEkA>.
- <span id="page-12-6"></span>B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning, 2021.
- <span id="page-12-17"></span>B. Zhao and H. Bilen. Dataset condensation with distribution matching, 2022a.
- <span id="page-12-16"></span>B. Zhao and H. Bilen. Synthesizing informative training samples with gan. arXiv preprint arXiv:2204.07513, 2022b.
- <span id="page-12-8"></span>B. Zhao and H. Bilen. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pages 6514–6523, 2023.
- <span id="page-12-1"></span>B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. In International Conference on Learning Representations, 2021. URL <https://openreview.net/forum?id=mSAKhLYLSsl>.
- <span id="page-12-13"></span>G. Zhao, G. Li, Y. Qin, and Y. Yu. Improved distribution matching for dataset condensation, 2023.

<span id="page-13-0"></span>Y. Zhou, E. Nezhadarya, and J. Ba. Dataset distillation using neural feature regression. arXiv preprint arXiv:2206.00719v2, 2022.

# Dataset Distillation via the Wasserstein Metric

## Supplementary Material

The supplementary material is structured as follows:

- Appendix [A](#page-14-1) outlines the potential social impact of our work;
- Appendix [B](#page-14-2) discusses the limitations and future directions of our work;
- Appendix [C](#page-14-0) discusses the method for Wasserstein barycenter computation in more details, and Appendix [D](#page-16-1) presents our algorithmic framework;
- Appendix  $E$  presents the implementation details;
- Appendix  $\bf{F}$  $\bf{F}$  $\bf{F}$  evaluates the efficiency of our method;
- Appendix [G](#page-17-0) provides visualizations of synthetic images generated by our approach.

Additionally, we provide the source code for our experiments in a zip file.

<span id="page-14-1"></span>

## A Discussion on Potential Social Impact

Our method, focused on accurately matching data distributions, inherently reflects existing biases in the source datasets, potentially leading to automated decisions that may not be completely fair. This situation underscores the importance of actively working to reduce bias in distilled datasets, a critical area for further investigation. Despite this, our technique significantly improves efficiency in model training by reducing data size, potentially lowering energy use and carbon emissions. This not only benefits the environment but also makes AI technologies more accessible to researchers with limited resources. While recognizing the concern of bias, the environmental advantages and the democratization of AI research our method offers are believed to have a greater positive impact.

<span id="page-14-2"></span>

### B Limitations and Future Directions

In this work, we introduce the Wasserstein barycenter as a geometrically meaningful approach to quantify distribution differences, enabling efficient Data Distillation (DD) without relying on complex bi-level optimization. However, incorporating the Wasserstein barycenter does lead to some additional computational demands when compared with simpler baseline approaches. Yet, this increase in computation is reasonably small (see efficiency evaluation in Appendix  $\bf{F}$ ) due to our use of an efficient algorithm for barycenter calculations. Additionally, like many DD methods, our approach requires a pretrained classifier in the source domain to facilitate dataset distillation. An intriguing avenue for future exploration involves eliminating this dependency by extending DD applicability to general self-supervised or generative models.

<span id="page-14-0"></span>

## C More Explanations on the Method

In this section, we provide a more detailed discussion on the Wasserstein barycenter computation method introduced in Section [4.2.](#page-4-1)

### C.2.1 Optimizing Weights Given Fixed Positions

The optimization of weights given fixed positions in the optimal transport problem involves solving a linear programming (LP) problem, where the primal form seeks the minimal total transportation cost subject to constraints on mass distribution. Given the cost matrix  $C$  and the transport plan  $T$ , the primal problem is formulated as:

$$
\min_{\mathbf{T}} \langle C, \mathbf{T} \rangle_F \tag{19}
$$

subject to 
$$
\sum_{j=1}^{m} T_{ij} = \frac{1}{n}, \forall i, \quad \sum_{i=1}^{n} T_{ij} = w_j, \forall j, \quad T_{ij} \ge 0, \forall i, j,
$$
 (20)

where  $\langle \cdot , \cdot \rangle_F$  denotes the Frobenius inner product.

The corresponding dual problem introduces dual variables  $\alpha_i$  and  $\beta_j$ , maximizing the objective:

$$
\max_{\alpha,\beta} \left\{ \sum_{i=1}^{n} \frac{\alpha_i}{n} + \sum_{j=1}^{m} w_j \beta_j \right\} \tag{21}
$$

subject to  $\alpha_i + \beta_j \leq c_{ij}, \forall i, j$ . (22)

Given the LP's feasibility and boundedness, strong duality holds, confirming that both the primal and dual problems reach the same optimal value [\(Boyd and Vandenberghe,](#page-11-16) [2004\)](#page-11-16). This equivalence implies that the set of optimal dual variables, denoted as  $\beta$ , acts as a subgradient, guiding the weight updates. Specifically, this subgradient indicates how the marginal costs vary with changes in the weights. To update the weights  $w$  towards their optimal values  $w^*$ , we implement the projected subgradient descent technique. This method ensures that w remains within the probability simplex, and under appropriate conditions on the step sizes, it guarantees convergence to the optimal solution.

#### C.2.2 Optimizing Positions Given Fixed Weights

##### C.2.2.1 Gradient Computation

Given the cost matrix C with elements  $c_{ij} = \|\tilde{\mathbf{x}}_i - \mathbf{x}_i\|^2$ , the gradient of the cost function with respect to a synthetic position  $\tilde {\mathbf {x}}_j$  is derived from the partial derivatives of  $c_{ij}$  with respect to  $\tilde {\mathbf {x}}_j$ . The gradient of  $c_{ij}$  with respect to  $\tilde {\mathbf x}_j$  is:

$$
\nabla_{\tilde{\mathbf{x}}_i} c_{ij} = 2(\tilde{\mathbf{x}}_j - \mathbf{x}_i). \tag{23}
$$

However, the overall gradient depends on the transport plan  $\mathbf {T}$  that solves the optimal transport problem. The gradient of the cost function f with respect to  $\tilde {\mathbf {x}}_j$  takes into account the amount of mass  $T_{ij}$  transported from  $\tilde {\mathbf {x}}_j$  to  $\mathbf {x}_i$ :

$$
\nabla_{\tilde{\mathbf{x}}_j} f(\tilde{\mathbf{X}}) = \sum_{i=1}^n T_{ij} \nabla_{\tilde{\mathbf{x}}_j} c_{ij} = \sum_{i=1}^n T_{ij} 2(\tilde{\mathbf{x}}_j - \mathbf{x}_i).
$$
 (24)

##### C.2.2.2 Hessian Computation

The Hessian matrix H of f with respect to  $\tilde {\mathbf X}$  involves second-order partial derivatives. For  $p=2$ , the second-order partial derivative of  $c_{ij}$  with respect to  $\tilde {\mathbf {x}}_j$  is constant:

$$
\frac{\partial^2 c_{ij}}{\partial \tilde{\mathbf{x}}_i^2} = 2\mathbf{I},\tag{25}
$$

where **I** is the identity matrix. Thus, the Hessian of f with respect to  $\tilde{\mathbf{X}}$  for each synthetic point  $\tilde{\mathbf {x}}_j$  is:

$$
H_j = \sum_{i=1}^{n} T_{ij} 2\mathbf{I} = 2\mathbf{I} \sum_{i=1}^{n} T_{ij} = 2\mathbf{I} w_j,
$$
\n(26)

since  $\sum_{i=1}^{n} T_{ij} = w_j$ , the amount of mass associated with synthetic point  $\tilde {\mathbf {x}}_j$ .

### C.2.3 Newton Update Formula

The Newton update formula for each synthetic position  $\tilde {\mathbf x}_j$  is then:

$$
\tilde{\mathbf{x}}_j^{\text{(new)}} = \tilde{\mathbf{x}}_j - H_j^{-1} \nabla_{\tilde{\mathbf{x}}_j} f(\tilde{\mathbf{X}}) = \tilde{\mathbf{x}}_j - \frac{1}{2w_j} \sum_{i=1}^n T_{ij} 2(\tilde{\mathbf{x}}_j - \mathbf{x}_i). \tag{27}
$$

Simplifying, we obtain:

$$
\tilde{\mathbf{x}}_j^{(\text{new})} = \tilde{\mathbf{x}}_j - \sum_{i=1}^n T_{ij} (\tilde{\mathbf{x}}_j - \mathbf{x}_i) / w_j.
$$
\n(28)

This formula adjusts each synthetic position  $\tilde {\mathbf {x}}_j$  in the direction that reduces the Wasserstein distance, weighted by the amount of mass transported and normalized by the weight  $w_j$ .

<span id="page-16-1"></span>

### D Algorithm details

As discussed in Section [4.3](#page-5-0) (Algorithm [1\)](#page-6-1) in the main paper, our method involves computing the Wasserstein barycenter of the empirical distribution of intra-class features. This section details the algorithm employed.

Let us denote the training set as  $\mathcal{T} = {\mathbf{x}_{c,i}}_{i=1,\dots,n}^{c=1,\dots,C}$ , where C is the number of classes and n is the number of images in that class. In the rest of this section, we only discuss the computation for class c, so we omit the index c from the subscript of related symbols for simplicity, e.g.,  $\mathbf{x}_{c,i}$  is simplified as  $\mathbf{x}_i$ . A feature extractor  $f_e(\cdot)$  embeds the real data of this class into the feature space  $\mathbb{R}^{d_f}$ , yielding a feature matrix  $\mathbf{Z} \in \mathbb{R}^{n \times d_f}$ , where the *i*th row  $\mathbf{z}_i = f_e(\mathbf{x}_i)$ . We employ the algorithm shown in Algorithm [2](#page-16-0) to compute the Wasserstein barycenter of the feature distribution. It takes Z as input and outputs a barycenter matrix  $\mathbf{B}^* \in \mathbb{R}^{m \times d_f}$ , where the jth row  $\mathbf{b}_j^*$  is the feature for learning the jth synthetic image, and an associated weight vector (probability distribution)  $\mathbf{w}^* \in \mathbb{R}^m$ .

Algorithm 2: Iterative Barycenter Learning for Dataset Distillation

- Result: Optimized barycenter matrix B<sup>\*</sup> and weights w<sup>\*</sup>.
- **1 Input:** Feature matrix of real data  $\mathbf{Z} \in \mathbb{R}^{n \times d_f}$ , initial synthetic dataset positions  $\mathbf{B}^{(0)} \in \mathbb{R}^{m \times d_f}$ , number of iterations  $K$ , learning rate  $\eta$ ;
- **2** Initialize weights  $\mathbf{w}^{(0)}$  uniformly;
- 3 for  $k = 1$  to  $K$  do
  - // Optimize weights given positions
- 4 Construct cost matrix  $C^{(k)}$  with  $\mathbf{B}^{(k-1)}$  and  $\mathbf{Z}$ ;
- 5 Solve optimal transport problem to obtain transport plan  $\mathbf{T}^{(k)}$  and dual variables  $\boldsymbol{\beta}^{(k)}$ ;
- 6 Update weights  $\mathbf{w}^{(k)}$  using projected subgradient method:  $\mathbf{w}^{(k)} = \text{Project}\left(\mathbf{w}^{(k-1)} \eta \boldsymbol{\beta}^{(k)}\right),$ 
  - ensuring  $w_j^{(k)} \ge 0$  and  $\sum_j w_j^{(k)} = 1$ ;
  - // Optimize positions given weights
- 7 Compute gradient  $\nabla_{\mathbf{B}} f$  as per:  $\nabla_{\mathbf{b}_j} f = \sum_{i=1}^n T_{ij}^{(k)} 2(\mathbf{b}_j^{(k-1)} \mathbf{z}_i), \forall j;$
- **8** Update positions  $\mathbf{B}^{(k)}$  using Newton's method:  $\mathbf{b}_j^{(k)} = \mathbf{b}_j^{(k-1)} H_j^{-1} \nabla_{\mathbf{b}_j} f$ ,  $\forall j$ , where  $H_j$  is the Hessian;

9 end

<span id="page-16-0"></span> $\mathbf{10} \ \ \mathbf{B}^* \leftarrow \mathbf{B}^{(K)}, \ \mathbf{w}^* \leftarrow \mathbf{w}^{(K)};$ 

<span id="page-16-2"></span>

#### E Implementation details

In our experiments, each experiment run was conducted on a single GPU of type A40, A100, or RTX-3090, depending on the availability. We used torchvision [\(maintainers and contributors,](#page-11-24) [2016\)](#page-11-24) for pretraining of models in the squeeze stage, and slightly modified the model architecture to allow tracking of per-class BatchNorm statistics.

We remained most of the hyperparameters in [Yin et al.](#page-12-12) [\(2023\)](#page-12-12) despite a few modifications. In the squeeze stage, we reduced the batch size to 32 for single-GPU training, and correspondingly reduced the learning rate to 0.025. In addition, we find from preliminary experiments that the weight decay at the recovery stage is detrimental to the performance of synthetic data, so we set them to 0.

For our loss term in Eq.  $(18)$ , we set lambda  $(\lambda)$  to 500 for ImageNet, 300 for Tiny-ImageNet, and 10 for ImageNette. We set the number of iterations to 2000 for all datasets. Table [5b-5d](#page-17-2) shows the hyperparameters used in the recover stage of our method. Hyperparameters in subsequent stages are kept the same as in [Yin et al.](#page-12-12) [\(2023\)](#page-12-12).

<span id="page-17-2"></span>

| config             | value        |
|--------------------|--------------|
| optimizer          | SGD          |
| learning rate      | 0.025        |
| weight decay       | $1e-4$       |
| optimizer momentum | 0.9          |
| batch size         | 32           |
| scheduler          | cosine decay |
| training epoch     | 100          |

batch size 100 scheduler cosine decay recovering iteration 2,000

(a) Squeezing setting for all datasets

| config               | value                         |
|----------------------|-------------------------------|
| lambda               | 300                           |
| optimizer            | Adam                          |
| learning rate        | 0.1                           |
| optimizer momentum   | $\beta_1, \beta_2 = 0.5, 0.9$ |
| batch size           | 100                           |
| scheduler            | cosine decay                  |
| recovering iteration | 2,000                         |

(b) Recovering setting for ImageNette

config value

lambda 10 optimizer Adam learning rate 0.25 optimizer momentum  $\beta _1, \beta _2 = 0.5, 0.9$ 

| config               | value                         |
|----------------------|-------------------------------|
| lambda               | 500                           |
| optimizer            | Adam                          |
| learning rate        | 0.25                          |
| optimizer momentum   | $\beta_1, \beta_2 = 0.5, 0.9$ |
| batch size           | 100                           |
| scheduler            | cosine decay                  |
| recovering iteration | 2,000                         |

(c) Recovering setting for Tiny-ImageNet

(d) Recovering setting for ImageNet-1K

Table 5: Hyperparameter settings for model training and recovering.

<span id="page-17-1"></span>

### F Efficiency Analysis

To evaluate the time and memory efficiency of our method, we measured the time used per iteration, total computation time, and the peak GPU consumption of our method with a 3090 GPU on ImageNette in the 1 IPC setting, and compared these metrics among several different methods. The results are shown in Table [6.](#page-17-3) As the Wsserstein barycenter can be computed efficiently, our method only brings minimal additional computation time compared with [Yin et al.](#page-12-12) [\(2023\)](#page-12-12). This makes it possible to preserve the efficiency benefits of distribution-based method while reaching strong performance.

<span id="page-17-3"></span>Table 6: Synthesis time and memory usage on ImageNette using a single GPU (RTX-3090) for all methods. 'Time/iter' indicates the time taken to update 1 synthetic image per class with a single iteration. This duration is measured in consecutive 100 iterations, and the mean and standard deviation is reported. For a fair comparison, we keep the original image resolution, and use the ResNet-18 model to distill for 2,000 iterations for all methods.

| Method      | Time/iter (s)     | Peak GPU Memory Usage (GB) | Total time (s) |
|-------------|-------------------|----------------------------|----------------|
| DC          | $2.154 \pm 0.104$ | 11.90                      | 6348.17        |
| DM          | $1.965 \pm 0.055$ | 9.93                       | 4018.17        |
| SRe2L       | $0.015 \pm 0.029$ | 1.14                       | 194.90         |
| WMDD (Ours) | $0.013 \pm 0.001$ | 1.22                       | 150.39         |

<span id="page-17-0"></span>

### G Visualizations

In this section, we provide visualization of synthetic images from our condensed dataset in Fig. [5.](#page-18-0) Our observations reveal that the synthetic images produced through our methodology exhibit a remarkable level of semantic clarity, successfully capturing the essential attributes and outlines of the intended class. This illustrates that underscores the fact that our approach yields images of superior quality, which incorporate an abundance of semantic details to enhance validation accuracy and exhibit exceptional visual performance.

Additionally, Fig. [6](#page-18-1) show our synthetic images on smaller datasets. Fig. [7](#page-19-0) shows the effect of the regularization strength. In Fig. [8,](#page-20-0) we compare the synthetic data from our method and [Yin et al.](#page-12-12) [\(2023\)](#page-12-12).

<span id="page-18-0"></span>Figure 5: Visualizations of our synthetic images from ImageNet-1K

<span id="page-18-1"></span>Image /page/18/Picture/2 description: The image displays two rows of images, each with a corresponding label below. The top row, labeled "Tiny-ImageNet", shows images of a goldfish, salamander, bullfrog, tailed frog, alligator, scorpion, penguin, lobster, sea gull, and sea lion. The bottom row, labeled "ImageNette", shows images of a tench, springer, cassette player, chain saw, church, French horn, garbage truck, gas pump, golf ball, and parachute. The images appear to be generated or stylized, with some exhibiting abstract or distorted features.

Figure 6: Visualizations of our synthetic images on smaller datasets

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image displays a grid of images of roosters and hens, organized into five rows, each labeled with a different lambda value: 0.1, 1, 10, 100, and 1000. Each row contains approximately ten images. The images themselves appear to be stylized or generated, with varying degrees of detail and color saturation. The top row, labeled "λ = 0.1", shows images that are highly textured and abstract, with roosters and hens depicted in a mosaic-like fashion. As the lambda value increases, the images become more distinct and recognizable as chickens, though still retaining an artistic or generated quality. The rows for λ = 1, 10, 100, and 1000 show progressively clearer depictions of chickens in various poses and settings, with the lowest lambda values resulting in more abstract and painterly representations, and the higher lambda values producing more photorealistic, albeit still stylized, images.

Figure 7: Visualization of synthetic images in ImageNet-1K with different regularization coefficient  $\lambda$ 

<span id="page-20-0"></span>Image /page/20/Picture/0 description: A series of 12 images, each depicting a shark in the water. The sharks are mostly silver and white with dark fins and tails. They are shown in various poses, some swimming, some with mouths open, and some appearing to breach the surface of the water. The water is a deep blue, with some areas of lighter blue and white foam. There are also some purple and pinkish hues in the water and on the sharks, possibly due to lighting or artistic effect. The images are arranged in a horizontal line, with each image separated by a thin white border.

SRe2L synthetic images of class White Shark (classId: 002)

Image /page/20/Picture/2 description: A collage of images shows various marine animals, primarily sharks and possibly dolphins, in blue and green water. Some images are close-ups of shark heads with visible teeth, while others show the full bodies of sharks swimming. One image depicts a shark breaching the water's surface, with a cityscape visible in the background. Another image shows a person swimming near a shark. The overall impression is a collection of dynamic underwater scenes featuring sharks.

Our synthetic images of the class White Shark (classId: 002)

Image /page/20/Figure/4 description: A series of 10 cropped images show a segmented, robotic snake with black and white bands moving through a dark, underwater environment with coral and rocks. The snake appears to be exploring or navigating the seabed.

SRe2L synthetic images of class Sea Snake (classId: 065)

Image /page/20/Picture/6 description: A collage of 12 images, each featuring a different marine creature, likely a type of eel or snake, in various underwater environments. The creatures display distinct patterns, with some having banded or striped bodies in shades of black, white, and brown, while others have more uniform coloration. The backgrounds vary from sandy seabeds and rocky outcrops to vibrant turquoise and green waters, suggesting diverse aquatic habitats. Some images show the creatures partially submerged or coiled, highlighting their serpentine forms and textures.

Our synthetic images of the class Sea Snake (classId: 065)

Image /page/20/Figure/8 description: A series of 15 images are displayed in a row, each showing a similar scene of a waterfall or geyser with reflections in the water below. The background appears to be a natural landscape with trees and sky. The images are slightly varied, suggesting they might be different captures or renderings of the same subject.

SRe2L synthetic images of class Geyser (classId: 974)

Image /page/20/Picture/10 description: A series of abstract landscape paintings are displayed in a grid. Each painting features a natural scene with elements like water, mountains, and sky, rendered in a somewhat impressionistic or surreal style. Some paintings depict geysers or waterfalls, with water erupting upwards and reflecting in the water below. Others show mountainous terrain with cloudy skies. The overall impression is one of natural beauty, but with a dreamlike or artistic interpretation.

Our synthetic images of the class Geyser (classId: 974)

Image /page/20/Picture/12 description: A series of 11 images show a flock of flamingos in a body of water. The flamingos are pink and are standing on their long legs. Some of the flamingos are dipping their heads into the water, presumably to feed. The water is dark and reflects the flamingos and the surrounding greenery. The images are arranged in a horizontal line, and each image is a slightly different angle or moment in time.

 ${\rm S}{\rm Re}^2{\rm L}$  synthetic images of class Flamingo (classId: 130)

Image /page/20/Picture/14 description: A series of images showing flamingos in various poses and settings. Some images focus on close-ups of the flamingos' heads and feathers, while others show them standing in water or on grassy areas. The overall impression is a collection of artistic interpretations or stylized depictions of flamingos.

Our synthetic images of the class Flamingo (classId: 130)

Figure 8: Comparison of synthetic images obtained from our method vs. SRe<sup>2</sup>L on ImageNet-1K in 10 IPC setting. Our method yields synthetic images that better cover the diversity of real images within each class.