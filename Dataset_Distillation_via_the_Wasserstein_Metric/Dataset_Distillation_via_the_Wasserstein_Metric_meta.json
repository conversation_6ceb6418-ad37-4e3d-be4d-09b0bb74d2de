{"table_of_contents": [{"title": "Dataset Distillation via the Wasserstein Metric", "heading_level": null, "page_id": 0, "polygon": [[133.8671875, 110.21727515583258], [460.88664987405537, 110.21727515583258], [460.88664987405537, 126.526123046875], [133.8671875, 126.526123046875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[276.9823677581864, 339.6491540516474], [317.51637279596974, 339.6491540516474], [317.51637279596974, 349.25732421875], [276.9823677581864, 349.25732421875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.55919395465995, 505.3499554764025], [185.40554156171282, 505.3499554764025], [185.40554156171282, 520.0830078125], [70.55919395465995, 520.0830078125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[70.55919395465995, 194.774169921875], [195.16372795969772, 194.774169921875], [195.16372795969772, 209.574951171875], [70.55919395465995, 209.574951171875]]}, {"title": "2.1 Data Distillation", "heading_level": null, "page_id": 2, "polygon": [[70.55919395465995, 219.33935546875], [201.2373046875, 219.33935546875], [201.2373046875, 232.49560546875], [70.55919395465995, 232.49560546875]]}, {"title": "2.2 Distribution Matching", "heading_level": null, "page_id": 2, "polygon": [[70.55919395465995, 409.0771484375], [237.177734375, 409.0771484375], [237.177734375, 422.64453125], [70.55919395465995, 422.64453125]]}, {"title": "3 Preliminary", "heading_level": null, "page_id": 2, "polygon": [[70.55919395465995, 578.4638671875], [180.15113350125944, 578.4638671875], [180.15113350125944, 594.0869140625], [70.55919395465995, 594.0869140625]]}, {"title": "3.1 Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[70.55919395465995, 630.2666015625], [219.42578125, 630.2666015625], [219.42578125, 643.4228515625], [70.55919395465995, 643.4228515625]]}, {"title": "3.2 Wasserstein Barycenter", "heading_level": null, "page_id": 3, "polygon": [[70.55919395465995, 264.6714158504007], [241.7027707808564, 264.6714158504007], [241.7027707808564, 275.458984375], [70.55919395465995, 275.458984375]]}, {"title": "4 Method", "heading_level": null, "page_id": 3, "polygon": [[70.55919395465995, 689.7951914514692], [152.201171875, 689.7951914514692], [152.201171875, 703.037109375], [70.55919395465995, 703.037109375]]}, {"title": "4.1 Wasserstein Barycenter in DD", "heading_level": null, "page_id": 4, "polygon": [[70.55919395465995, 73.1302490234375], [282.2367758186398, 73.1302490234375], [282.2367758186398, 83.5113525390625], [70.55919395465995, 83.5113525390625]]}, {"title": "4.2 Computation of the Barycenter", "heading_level": null, "page_id": 4, "polygon": [[70.55919395465995, 505.3499554764025], [288.992443324937, 505.3499554764025], [288.992443324937, 515.560546875], [70.55919395465995, 515.560546875]]}, {"title": "4.2.1 Optimizing Weights Given Fixed Positions", "heading_level": null, "page_id": 4, "polygon": [[70.55919395465995, 576.5788067675868], [322.02015113350126, 576.5788067675868], [322.02015113350126, 587.09765625], [70.55919395465995, 587.09765625]]}, {"title": "4.2.2 Optimizing Positions Given Weights", "heading_level": null, "page_id": 5, "polygon": [[70.55919395465995, 231.68121104185218], [290.4937027707808, 231.68121104185218], [290.4937027707808, 241.95166015625], [70.55919395465995, 241.95166015625]]}, {"title": "4.3 Barycenter Matching in the Feature Space", "heading_level": null, "page_id": 5, "polygon": [[70.55919395465995, 567.5814781834372], [353.583984375, 567.5814781834372], [353.583984375, 578.875], [70.55919395465995, 578.875]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 6, "polygon": [[70.55919395465995, 678.5485307212823], [186.3955078125, 678.5485307212823], [186.3955078125, 691.1142578125], [70.55919395465995, 691.1142578125]]}, {"title": "5.1 Experiment Setup", "heading_level": null, "page_id": 6, "polygon": [[70.55919395465995, 704.0409617097062], [210.176322418136, 704.0409617097062], [210.176322418136, 714.9599609375], [70.55919395465995, 714.9599609375]]}, {"title": "5.2 Co<PERSON><PERSON>on with Other Methods", "heading_level": null, "page_id": 7, "polygon": [[70.55919395465995, 184.290283203125], [298.873046875, 184.290283203125], [298.873046875, 196.213134765625], [70.55919395465995, 196.213134765625]]}, {"title": "5.3 Cross-architecture Generalization", "heading_level": null, "page_id": 7, "polygon": [[70.55919395465995, 480.6073018699911], [300.25188916876573, 480.6073018699911], [300.25188916876573, 492.537109375], [70.55919395465995, 492.537109375]]}, {"title": "5.4 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[70.55919395465995, 635.0614425645592], [193.6624685138539, 635.0614425645592], [193.6624685138539, 646.7119140625], [70.55919395465995, 646.7119140625]]}, {"title": "5.5 Comparison with the MMD Metric", "heading_level": null, "page_id": 9, "polygon": [[70.55919395465995, 72.72840605520926], [311.51133501259443, 72.72840605520926], [311.51133501259443, 84.0252685546875], [70.55919395465995, 84.0252685546875]]}, {"title": "5.6 Feature Embedding Distribution", "heading_level": null, "page_id": 9, "polygon": [[70.55919395465995, 339.6491540516474], [294.99748110831234, 339.6491540516474], [294.99748110831234, 350.90185546875], [70.55919395465995, 350.90185546875]]}, {"title": "5.7 Increased Variety in Synthetic Images", "heading_level": null, "page_id": 9, "polygon": [[70.55919395465995, 691.2947462154942], [327.27455919395464, 691.2947462154942], [327.27455919395464, 702.6259765625], [70.55919395465995, 702.6259765625]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 10, "polygon": [[70.55919395465995, 286.5595703125], [175.19140625, 286.5595703125], [175.19140625, 300.94921875], [70.55919395465995, 300.94921875]]}, {"title": "References", "heading_level": null, "page_id": 11, "polygon": [[70.55919395465995, 70.817626953125], [148.41796875, 70.817626953125], [148.41796875, 84.48779296875], [70.55919395465995, 84.48779296875]]}, {"title": "Dataset Distillation via the Wasserstein Metric", "heading_level": null, "page_id": 14, "polygon": [[128.7744140625, 70.47907390917186], [465.04296875, 70.47907390917186], [465.04296875, 84.5391845703125], [128.7744140625, 84.5391845703125]]}, {"title": "Supplementary Material", "heading_level": null, "page_id": 14, "polygon": [[221.4357682619647, 96.72128227960819], [373.06297229219143, 96.72128227960819], [373.06297229219143, 110.491943359375], [221.4357682619647, 110.491943359375]]}, {"title": "A Discussion on Potential Social Impact", "heading_level": null, "page_id": 14, "polygon": [[71.30982367758186, 294.66251113089936], [367.84375, 294.66251113089936], [367.84375, 308.96630859375], [71.30982367758186, 308.96630859375]]}, {"title": "B Limitations and Future Directions", "heading_level": null, "page_id": 14, "polygon": [[70.55919395465995, 433.333984375], [341.536523929471, 433.333984375], [341.536523929471, 446.490234375], [70.55919395465995, 446.490234375]]}, {"title": "C More Explanations on the Method", "heading_level": null, "page_id": 14, "polygon": [[70.55919395465995, 582.5751953125], [346.0403022670025, 582.5751953125], [346.0403022670025, 596.5537109375], [70.55919395465995, 596.5537109375]]}, {"title": "C.1 Optimizing Weights Given Fixed Positions", "heading_level": null, "page_id": 14, "polygon": [[70.55919395465995, 645.478515625], [357.2997481108312, 645.478515625], [357.2997481108312, 656.990234375], [70.55919395465995, 656.990234375]]}, {"title": "C.2 Optimizing Positions Given Fixed Weights", "heading_level": null, "page_id": 15, "polygon": [[70.55919395465995, 269.91985752448795], [356.78515625, 269.91985752448795], [356.78515625, 280.8037109375], [70.55919395465995, 280.8037109375]]}, {"title": "C.2.1 Gradient Computation", "heading_level": null, "page_id": 15, "polygon": [[70.55919395465995, 288.6642920747996], [225.1005859375, 288.6642920747996], [225.1005859375, 298.68798828125], [70.55919395465995, 298.68798828125]]}, {"title": "C.2.2 Hessian Computation", "heading_level": null, "page_id": 15, "polygon": [[70.55919395465995, 452.8655387355298], [218.43324937027705, 452.8655387355298], [218.43324937027705, 463.3466796875], [70.55919395465995, 463.3466796875]]}, {"title": "C.2.3 Newton Update Formula", "heading_level": null, "page_id": 15, "polygon": [[70.55919395465995, 623.8147818343722], [234.19647355163727, 623.8147818343722], [234.19647355163727, 635.2001953125], [70.55919395465995, 635.2001953125]]}, {"title": "D Algorithm details", "heading_level": null, "page_id": 16, "polygon": [[70.55919395465995, 69.789794921875], [224.8095703125, 69.789794921875], [224.8095703125, 84.693359375], [70.55919395465995, 84.693359375]]}, {"title": "E Implementation details", "heading_level": null, "page_id": 16, "polygon": [[70.55919395465995, 498.60195903829026], [262.72040302267, 498.60195903829026], [262.72040302267, 512.271484375], [70.55919395465995, 512.271484375]]}, {"title": "F Efficiency Analysis", "heading_level": null, "page_id": 17, "polygon": [[70.55919395465995, 383.8860195903829], [233.103515625, 383.8860195903829], [233.103515625, 398.798828125], [70.55919395465995, 398.798828125]]}, {"title": "G Visualizations", "heading_level": null, "page_id": 17, "polygon": [[70.55919395465995, 652.3063223508459], [199.7822265625, 652.3063223508459], [199.7822265625, 667.2685546875], [70.55919395465995, 667.2685546875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 48], ["Text", 5], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8118, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 52], ["Text", 2], ["TextInlineMath", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 46], ["Text", 5], ["SectionHeader", 5], ["Reference", 2], ["ListItem", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["Line", 46], ["TextInlineMath", 6], ["Text", 4], ["Equation", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 40], ["Text", 7], ["Equation", 6], ["TextInlineMath", 4], ["SectionHeader", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 42], ["Text", 11], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 42], ["Text", 6], ["TextInlineMath", 4], ["Reference", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2207, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["TableCell", 108], ["Line", 55], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2956, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["TableCell", 128], ["Line", 51], ["Caption", 5], ["Reference", 3], ["Table", 2], ["TextInlineMath", 1], ["Text", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 16307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 42], ["TableCell", 12], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 19], ["Text", 3], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 651, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 195], ["Line", 53], ["ListItem", 25], ["Reference", 25], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 52], ["ListItem", 26], ["Reference", 26], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 3], ["ListItem", 1], ["<PERSON>Footer", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 41], ["SectionHeader", 6], ["Text", 6], ["ListItem", 6], ["Reference", 3], ["Equation", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 38], ["Text", 7], ["Equation", 7], ["TextInlineMath", 5], ["SectionHeader", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 638], ["Line", 43], ["ListItem", 13], ["Text", 7], ["Reference", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["TableCell", 120], ["Line", 63], ["Text", 12], ["Table", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 8527, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 12], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["Line", 8], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 33], ["Line", 13], ["Caption", 7], ["Picture", 6], ["PictureGroup", 5], ["Figure", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 8, "llm_error_count": 0, "llm_tokens_used": 4808, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_via_the_Wasserstein_Metric"}