{"table_of_contents": [{"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 174.75], [195.75, 174.75], [195.75, 185.7216796875], [148.5, 185.7216796875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 471.75], [132.75, 471.75], [132.75, 483.3984375], [54.0, 483.3984375]]}, {"title": "2. Background", "heading_level": null, "page_id": 1, "polygon": [[54.0, 68.25], [130.5, 68.25], [130.5, 79.51904296875], [54.0, 79.51904296875]]}, {"title": "3. Method: Reparameterized Convexified\nImplicit Gradient", "heading_level": null, "page_id": 1, "polygon": [[306.0, 606.75], [518.25, 606.75], [518.25, 631.51171875], [306.0, 631.51171875]]}, {"title": "4. Experimental Results", "heading_level": null, "page_id": 4, "polygon": [[306.0, 574.5], [429.75, 574.5], [429.75, 585.87890625], [306.0, 585.87890625]]}, {"title": "5. Discussions, Limitations, and Conclusion", "heading_level": null, "page_id": 7, "polygon": [[305.25, 507.75], [529.5, 507.75], [529.5, 518.58984375], [305.25, 518.58984375]]}, {"title": "6. Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[54.0, 156.0], [167.25, 156.0], [167.25, 167.44921875], [54.0, 167.44921875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 348.0], [111.75, 348.0], [111.75, 359.068359375], [54.0, 359.068359375]]}, {"title": "A. Memory Requirements and Subsampling Ablation", "heading_level": null, "page_id": 13, "polygon": [[54.0, 68.25], [327.814453125, 68.25], [327.814453125, 79.9541015625], [54.0, 79.9541015625]]}, {"title": "B. Privacy Preservation: Membership Inference Attacks. More details.", "heading_level": null, "page_id": 13, "polygon": [[54.0, 615.0], [416.56640625, 615.0], [416.56640625, 626.09765625], [54.0, 626.09765625]]}, {"title": "C. Implementation details", "heading_level": null, "page_id": 14, "polygon": [[54.0, 375.0], [190.0546875, 375.0], [190.0546875, 386.33203125], [54.0, 386.33203125]]}, {"title": "C.1. FRePo Code Error", "heading_level": null, "page_id": 15, "polygon": [[54.0, 525.1640625], [156.4365234375, 525.1640625], [156.4365234375, 535.9921875], [54.0, 535.9921875]]}, {"title": "<PERSON>. Additional Results", "heading_level": null, "page_id": 15, "polygon": [[54.0, 607.53515625], [165.0, 607.53515625], [165.0, 619.13671875], [54.0, 619.13671875]]}, {"title": "D.1. Training Curves", "heading_level": null, "page_id": 15, "polygon": [[54.0, 628.5], [146.5751953125, 628.5], [146.5751953125, 639.6328125], [54.0, 639.6328125]]}, {"title": "D.2. Total Training Time", "heading_level": null, "page_id": 16, "polygon": [[54.0, 232.41796875], [161.3671875, 232.41796875], [161.3671875, 242.859375], [54.0, 242.859375]]}, {"title": "D.3. Evaluation with/without data augmentation", "heading_level": null, "page_id": 16, "polygon": [[54.0, 312.0], [261.0, 312.0], [261.0, 322.13671875], [54.0, 322.13671875]]}, {"title": "E. Distilled Dataset Visualization", "heading_level": null, "page_id": 16, "polygon": [[54.0, 381.75], [224.25, 381.75], [224.25, 393.6796875], [54.0, 393.6796875]]}, {"title": "CIFAR-10 10 Img/Cls", "heading_level": null, "page_id": 21, "polygon": [[234.87890625, 171.0], [360.0, 171.0], [360.0, 187.5], [234.87890625, 187.5]]}, {"title": "airplane airplane airplane airplane airplane\nairplane airplane airplane airplane airplane\nautomobile automobile automobile automobile automobile automobile automobile automobile automobile automobile\nbird bird bird bird bird bird bird bird bird bird\ncat cat cat cat cat cat cat cat cat cat\ndeer deer deer deer deer deer deer deer deer deer\ndog dog dog dog dog dog dog dog dog dog\nfrog frog frog frog frog frog frog frog frog frog\nhorse horse horse horse horse horse horse horse horse horse\nship ship ship ship ship ship ship ship ship ship\ntruck truck truck truck truck truck truck truck truck truck", "heading_level": null, "page_id": 22, "polygon": [[129.392578125, 179.82421875], [465.57421875, 179.82421875], [465.57421875, 601.734375], [129.392578125, 601.734375]]}, {"title": "CIFAR-10 10 Img/Cls (With BatchNorm)", "heading_level": null, "page_id": 22, "polygon": [[178.1015625, 171.75], [414.75, 171.75], [414.75, 187.6552734375], [178.1015625, 187.6552734375]]}, {"title": "CIFAR-100 1 Img/Cls", "heading_level": null, "page_id": 23, "polygon": [[235.6259765625, 171.0], [360.0, 169.5], [360.0, 187.5], [235.6259765625, 188.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 88], ["Text", 8], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 642], ["Line", 141], ["Text", 7], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 108], ["TextInlineMath", 5], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1249], ["Line", 135], ["TableCell", 116], ["TextInlineMath", 7], ["Equation", 6], ["Reference", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2474, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 803], ["Line", 102], ["TextInlineMath", 14], ["TableCell", 8], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1288], ["TableCell", 240], ["Line", 97], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5695, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1153], ["TableCell", 144], ["Line", 118], ["TextInlineMath", 4], ["Caption", 3], ["Reference", 3], ["Table", 2], ["Text", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 9058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["Line", 126], ["TableCell", 51], ["Text", 5], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Footnote", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2033, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 98], ["ListItem", 14], ["Reference", 14], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 95], ["ListItem", 20], ["Reference", 20], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 98], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 94], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 18], ["ListItem", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 119], ["Reference", 3], ["SectionHeader", 2], ["Figure", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1860, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 639], ["Line", 67], ["TableCell", 52], ["Text", 5], ["Caption", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1074, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 80], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 57], ["SectionHeader", 3], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["TableCell", 230], ["Line", 30], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7296, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["TableCell", 92], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3070, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 19], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1514, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 19], ["Figure", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1467, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 15], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 983, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 25], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1502, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 55], ["Line", 25], ["Picture", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1371, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_with_Convexified_Implicit_Gradients"}