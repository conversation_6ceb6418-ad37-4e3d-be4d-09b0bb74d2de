{"table_of_contents": [{"title": "Dataset Distillation with Infinitely Wide\nConvolutional Networks", "heading_level": null, "page_id": 0, "polygon": [[157.5, 98.25], [453.0, 98.25], [453.0, 135.544921875], [157.5, 135.544921875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 268.3828125], [329.25, 268.3828125], [329.25, 278.82421875], [282.75, 278.82421875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 454.5], [191.548828125, 454.5], [191.548828125, 466.3828125], [107.25, 466.3828125]]}, {"title": "2 Setup", "heading_level": null, "page_id": 1, "polygon": [[106.5, 456.75], [156.0, 456.75], [156.0, 467.9296875], [106.5, 467.9296875]]}, {"title": "2.1 Client-Server Distributed Workflow", "heading_level": null, "page_id": 2, "polygon": [[106.5, 415.5], [283.5, 415.5], [283.5, 425.77734375], [106.5, 425.77734375]]}, {"title": "3 Experimental Results", "heading_level": null, "page_id": 3, "polygon": [[106.5, 479.91796875], [237.0, 479.91796875], [237.0, 492.29296875], [106.5, 492.29296875]]}, {"title": "3.1 Kernel Distillation Results", "heading_level": null, "page_id": 3, "polygon": [[106.3828125, 504.75], [243.84375, 504.75], [243.84375, 515.8828125], [106.3828125, 515.8828125]]}, {"title": "3.2 Kernel Transfer", "heading_level": null, "page_id": 4, "polygon": [[106.5, 449.75390625], [200.25, 449.75390625], [200.25, 459.80859375], [106.5, 459.80859375]]}, {"title": "3.3 Neural Network Transfer", "heading_level": null, "page_id": 4, "polygon": [[106.5, 576.75], [240.0, 576.75], [240.0, 586.265625], [106.5, 586.265625]]}, {"title": "4 Understanding KIP Images and Labels", "heading_level": null, "page_id": 5, "polygon": [[106.681640625, 654.71484375], [326.619140625, 654.71484375], [326.619140625, 667.08984375], [106.681640625, 667.08984375]]}, {"title": "CIFAR-100", "heading_level": null, "page_id": 7, "polygon": [[279.0, 76.5], [326.25, 76.5], [326.25, 85.7548828125], [279.0, 85.7548828125]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 8, "polygon": [[106.5, 610.5], [198.0, 610.5], [198.0, 622.23046875], [106.5, 622.23046875]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.5, 438.75], [183.75, 438.75], [183.75, 450.9140625], [106.5, 450.9140625]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.05517578125, 122.25], [164.654296875, 122.25], [164.654296875, 133.224609375], [107.05517578125, 133.224609375]]}, {"title": "A Experimental Details", "heading_level": null, "page_id": 13, "polygon": [[106.2333984375, 72.0], [237.7177734375, 72.0], [237.7177734375, 83.86962890625], [106.2333984375, 83.86962890625]]}, {"title": "B Computational Costs", "heading_level": null, "page_id": 15, "polygon": [[106.3828125, 131.25], [237.0, 131.25], [237.0, 142.69921875], [106.3828125, 142.69921875]]}, {"title": "C Additional Tables and Figures", "heading_level": null, "page_id": 15, "polygon": [[107.05517578125, 435.75], [282.990234375, 435.75], [282.990234375, 446.66015625], [107.05517578125, 446.66015625]]}, {"title": "C.1 KIP Image Analysis", "heading_level": null, "page_id": 15, "polygon": [[106.5, 460.5], [219.4892578125, 459.0], [219.4892578125, 469.86328125], [106.5, 470.25]]}, {"title": "C.2 Natural Image Baselines", "heading_level": null, "page_id": 15, "polygon": [[106.5, 584.25], [237.7177734375, 584.25], [237.7177734375, 594.38671875], [106.5, 594.38671875]]}, {"title": "C.3 Ablation Studies", "heading_level": null, "page_id": 16, "polygon": [[106.5, 662.25], [204.75, 662.25], [204.75, 672.890625], [106.5, 672.890625]]}, {"title": "C.4 DC/DSA ConvNet", "heading_level": null, "page_id": 17, "polygon": [[106.5, 513.0], [212.466796875, 513.0], [212.466796875, 524.00390625], [106.5, 524.00390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 46], ["Text", 9], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 52], ["Text", 7], ["ListItem", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 662], ["Line", 81], ["TextInlineMath", 6], ["Text", 5], ["Reference", 4], ["Equation", 3], ["Footnote", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["TableCell", 128], ["Line", 53], ["Text", 3], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 77], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 909, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 385], ["TableCell", 105], ["Line", 60], ["Text", 4], ["Reference", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3287, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 432], ["Line", 122], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3486, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 54], ["Text", 7], ["ListItem", 3], ["Caption", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 54], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 61], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1231, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 50], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 39], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 56], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["Footnote", 4], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 628], ["Line", 85], ["Text", 8], ["Equation", 3], ["TextInlineMath", 2], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["Line", 48], ["Text", 8], ["SectionHeader", 4], ["TextInlineMath", 3], ["Reference", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["TableCell", 200], ["Line", 51], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 14117, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 27], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 846, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 88], ["TableCell", 24], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 78], ["Text", 5], ["Figure", 3], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["TableCell", 195], ["Line", 38], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["TableCell", 160], ["Line", 41], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 301], ["Span", 258], ["Line", 42], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 15077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["TableCell", 114], ["Line", 31], ["Table", 3], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 3, "llm_tokens_used": 930, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["TableCell", 145], ["Line", 43], ["Table", 2], ["Reference", 2], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2529, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_with_Infinitely_Wide_Convolutional_Networks"}