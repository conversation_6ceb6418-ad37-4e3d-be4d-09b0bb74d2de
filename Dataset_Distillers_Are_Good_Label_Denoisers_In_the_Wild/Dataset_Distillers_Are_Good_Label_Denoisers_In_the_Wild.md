<span id="page-0-1"></span>

# Dataset Distillers Are Good Label Denoisers In the Wild

Lechao Cheng Hefei University of Technology

<EMAIL>

Shengeng Tang Hefei University <NAME_EMAIL>

Shu<PERSON>i Zhang Shanghai AI Lab

<EMAIL>

## <PERSON><PERSON>i University of Technology

<EMAIL>

#### Abstract

*Learning from noisy data has become essential for adapting deep learning models to real-world applications. Traditional methods often involve first evaluating the noise and then applying strategies such as discarding noisy samples, re-weighting, or re-labeling. However, these methods can fall into a vicious cycle when the initial noise evaluation is inaccurate, leading to suboptimal performance. To address this, we propose a novel approach that leverages dataset distillation for noise removal. This method avoids the feedback loop common in existing techniques and enhances training efficiency, while also providing strong privacy protection through offline processing. We rigorously evaluate three representative dataset distillation methods (DATM, DANCE, and RCIG) under various noise conditions, including symmetric noise, asymmetric noise, and real-world natural noise. Our empirical findings reveal that dataset distillation effectively serves as a denoising tool in random noise scenarios but may struggle with structured asymmetric noise patterns, which can be absorbed into the distilled samples. Additionally, clean but challenging samples, such as those from tail classes in imbalanced datasets, may undergo lossy compression during distillation. Despite these challenges, our results highlight that dataset distillation holds significant promise for robust model training, especially in high-privacy environments where noise is prevalent. The source code is available at [https://github.com/Kciiiman/DD\\_LNL.](https://github.com/Kciiiman/DD_LNL)*

## 1. Introduction

Learning from noisy data [\[2,](#page-8-0) [10,](#page-8-1) [24,](#page-8-2) [34,](#page-9-0) [55\]](#page-9-1) has emerged as an effective strategy for customizing cutting-edge deep learning algorithms to vertical applications. Existing approaches [\[8,](#page-8-3) [14,](#page-8-4) [41,](#page-9-2) [43,](#page-9-3) [50,](#page-9-4) [52\]](#page-9-5) tackling this issue generally follows a core step of first assessing the data noise,

<span id="page-0-0"></span>Image /page/0/Figure/13 description: The image displays two rows, (a) and (b), illustrating the concept of dataset distillation. Row (a) shows a scatter plot with various shapes (circles, squares, stars) and images of airplanes. The process of dataset distillation transforms this into a new scatter plot with only green circles and representative airplane images. This is followed by a graph showing accuracy versus noise rate, with two curves labeled 'Before Distillation' (blue) and 'After Distillation' (orange), indicating improved accuracy after distillation. Row (b) follows a similar pattern, starting with a scatter plot containing stars and images of deer and horses. After dataset distillation, the plot contains only stars and representative images of deer and horses. The accompanying graph also shows the 'Before Distillation' and 'After Distillation' curves, demonstrating the impact of distillation on accuracy under varying noise rates.

Kaifeng Chen Zhejiang University <NAME_EMAIL>

Figure 1. (a) for symmetric noise, existing dataset distillation methods serve as effective denoising tools. (b) structured patterns, such as asymmetric noise, can also be absorbed into the distilled samples, which hinders robust training.

and then either discarding noisy samples [\[14,](#page-8-4) [33,](#page-9-6) [45\]](#page-9-7), applying weighted adjustment [\[3,](#page-8-5) [39,](#page-9-8) [41,](#page-9-2) [61\]](#page-10-0), or reassigning high-confidence labels [\[28,](#page-9-9) [29\]](#page-9-10). Most of the approaches primarily focus on the separation of clean and noisy samples while aiming to prevent overfitting to limited clean data and mitigate biasing model with noisy information.

Status Quo. Although numerous existing methods have achieved remarkable results on nearly ideal datasets (such as noisy data curated from CIFAR-10/100 [\[20\]](#page-8-6)), as mentioned earlier, the general approach involves first evaluating the noise, followed by optimizations such as discarding, reweighting, or re-labeling. We argue that in such a strategic feedback loop, the noise evaluation process itself is inherently indeterminate in terms of causality: the evaluation of noise affects subsequent strategies (e.g., re-labeling), and those strategies in turn influence the noise evaluation. If the initial noise evaluation is poor, it leads to an inescapable vicious cycle. Moreover, a good noise evaluation source can itself fall into a new paradox, akin to: *"It's a Catch-22: You need generalizability to get a good assessment, but you need assessment to gain generalizability"*.

Motivation. Given this, let us step outside the existing

<span id="page-1-2"></span>framework and revisit this task. The core issue here is that we need to use the information from the clean samples to train a robust model while also avoiding bias from noisy data. *Can we, perhaps, sample from data distribution to obtain a support set*[1](#page-1-0) *within the manifold of clean sample space ?* This would allow us to directly leverage these support sets to maximize the retention of clean sample informa-tion and train a more reliable model<sup>[2](#page-1-1)</sup>. This raises an intriguing question, and coincidentally, the idea behind dataset distillation [\[44\]](#page-9-11) seems to share a similar spirit. Dataset distillation [\[44\]](#page-9-11) aims to condense a large dataset into a significantly smaller synthetic dataset that retains the essential information necessary for models to achieve performance comparable to training on the full dataset. In contrast to the "memorization" process  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  $[1, 14, 15, 25, 47, 53]$  in conventional deep learning networks, dataset distillation attempts to synthesize common patterns shared across the dataset, then gradually distills samples with more specific information. This insight has inspired us to explore the use of dataset distillation techniques for noise sample removal.

Empirical Insights. To further investigate the feasibility of motivation, we first review three representative benchmarking dataset distillation methods, namely parameter matching (DATM [\[13\]](#page-8-10)), distribution matching (DANCE [\[54\]](#page-9-14)), and meta-learning (RCIG [\[32\]](#page-9-15)). These methods are then rigorously validated under commonly used noise scenarios, including symmetric noise, asymmetric noise, and natural noise conditions. We expect that doing so can provide novel perspectives in addressing noisy label learning. Empirically, we find some principled yet intuitive insights to achieve robust training: *(I) for symmetric noise, existing dataset distillation methods serve as effective denoising tools. (II) structured patterns, such as asymmetric noise, can also be absorbed into the distilled samples, which hinders robust training. (III) for real-world natural noise, dataset distillation methods remain effective even in the presence of an unknown fixed noise rate.* Although (I) provides effective guidance for random noise removal, the relationship between the amount of data to be distilled and the level of noise remains an open question worth exploring (Figure  $1(a)$  $1(a)$ ). At the same time, (II) reminds us that the common patterns memorized in deep learning are not always beneficial (they may also arise from structured asymmetric noise due to factors like visual similarity), as illustrated in Figure [1\(](#page-0-0)b). In real-world scenarios, data noise is largely random, and dataset distillation methods can clearly serve as effective denoising techniques. These methods hold significant potential for application in high-privacy settings where data noise is prevalent.

<span id="page-1-0"></span><sup>1</sup>A synthetic subset that can effectively cover the original clean data distribution.

Contributions. We summarize major contributions bellow:

- We propose a new perspective for addressing the problem of model learning from noisy data. This perspective not only effectively avoids the issue of vicious cycles, but also improves training efficiency and provides strong privacy protection through offline dataset distillation processing.
- In this new perspective, we conduct in-depth explorations of existing dataset distillation methods and find that, in the context of random noise, dataset distillers serve as effective label denoisers. This conclusion also holds true for natural noise data in a broader sense.
- We further investigate potential pitfalls, such as the paradigm of dataset distillation focuses on compressing common patterns, which are not always beneficial. For example, structured asymmetric noise patterns are likely to be distilled into the synthesized data. Additionally, clean yet challenging samples, such as those from tail classes in imbalanced datasets, are at risk of being lossy compressed during the dataset distillation process.

## 2. Realted Works

## 2.1. Learning with Noisy Labels

Noisy label learning addresses mislabeled data caused by human error, automated systems, or inherent ambiguity in real-world datasets. Several approaches have been proposed to tackle this challenge [\[2,](#page-8-0) [24,](#page-8-2) [34,](#page-9-0) [55\]](#page-9-1). Some methods correct noisy labels by modeling the noise transition matrix, which describes label corruption [\[50,](#page-9-4) [52\]](#page-9-5). Techniques such as [\[7,](#page-8-11) [49\]](#page-9-16) estimate instance-dependent matrices using deep networks to improve label handling. Additionally, Wang et al. [\[43\]](#page-9-3) leverages privileged information for better noise identification. Contrastive learning improves robustness by contrasting noisy samples with reliable ones, reducing sensitivity to label noise [\[8,](#page-8-3) [12\]](#page-8-12). Methods like Twin Contrastive Learning [\[18\]](#page-8-13) build noise-resilient representations, while [\[26\]](#page-8-14) isolates clean data through highconfidence training. Noise filtering and correction methods, such as dual-network frameworks [\[14,](#page-8-4) [33\]](#page-9-6), iteratively remove noisy samples, while dynamic correction approaches like DISC [\[28\]](#page-9-9) adjust training to prevent overfitting. Reweighting methods [\[39,](#page-9-8) [41\]](#page-9-2) assign adaptive weights to samples based on reliability, reducing the influence of noisy data. Recent methods [\[3,](#page-8-5) [61\]](#page-10-0) refine this by incorporating noise modeling and dynamic curricula [\[19\]](#page-8-15), improving robustness.

In particular, approaches [\[1,](#page-8-7) [14,](#page-8-4) [15,](#page-8-8) [25,](#page-8-9) [47,](#page-9-12) [53\]](#page-9-13) such as memorization analysis highlight the tendency of deep networks to overfit noisy data, suggesting the need for methods that reduce the influence of noisy samples. This also motivates us to distill noisy dataset into a compact subset that still enables effective training.

<span id="page-1-1"></span><sup>&</sup>lt;sup>2</sup> for now, we will set aside the potential role of slight noise perturbations in promoting robustness

## <span id="page-2-1"></span>2.2. Dataset Distillation

Dataset distillation [\[44\]](#page-9-11) seeks to reduce large datasets into smaller synthetic ones that retain the crucial information required for models to perform comparably to those trained on the full dataset. Recent research on dataset distillation has explored several approaches, which can be categorized into meta-learning, parameter matching, and distribution matching [\[11,](#page-8-16) [23,](#page-8-17) [51\]](#page-9-17). Meta-learning [\[17\]](#page-8-18) involves a two-loop optimization: in the inner loop, the model is trained on the synthetic dataset, and in the outer loop, the dataset is optimized to ensure the model performs well on real data [\[44\]](#page-9-11). Some approaches have enhanced this framework by using soft labels [\[4,](#page-8-19) [42\]](#page-9-18) or replacing the neural network with kernel models [\[30,](#page-9-19) [35,](#page-9-20) [36\]](#page-9-21). In contrast, parameter matching methods [\[6,](#page-8-20) [16,](#page-8-21) [22,](#page-8-22) [58\]](#page-9-22) focus on aligning model parameters trained on both synthetic and real datasets to ensure similar effects. For example, Zhao et al. [\[58\]](#page-9-22) use gradient matching during training, while He et al. [\[16\]](#page-8-21) propose a multisize dataset condensation approach based on gradient matching. The trajectory matching approach, exemplified by DATM [\[13\]](#page-8-10), aligns training trajectories between synthetic and real datasets, further proposing that the synthetic dataset size should match the difficulty of learning the generated patterns. Additionally, PDD [\[6\]](#page-8-20) incrementally synthesizes groups of synthetic images, training on the union of these subsets, while SelMatch [\[22\]](#page-8-22) addresses large images per class by using selection-based initialization and partial updates through trajectory matching. Distribution matching approaches, such as those by Zhao and Bilen [\[57\]](#page-9-23) using maximum mean discrepancy (MMD), aim to match the feature distributions of synthetic and real datasets, reducing optimization complexity. Improvements to distribution matching [\[59\]](#page-9-24) incorporate partitioning augmentation and classaware regularization, enhancing dataset condensation accuracy while potentially reducing diversity. Recent work [\[54\]](#page-9-14) has focused on modeling inner- and inter-class relationships, with methods like DANCE aligning both feature and label distributions between real and synthetic datasets, further improving model performance. This work first benchmarks existing representative distillation approaches (e.g., DATM [\[13\]](#page-8-10), DANCE [\[54\]](#page-9-14), RCIG [\[32\]](#page-9-15)) and then perform exhaustive experiments on noisy data with different ratios. We further present the observations and finally give some insightful conclusions.

## 3. Setup and Protocol

We first present an introduction of the prevailing settings for learning from noisy data, including mainstream noise types, commonly used noisy datasets, and essential information derived from the training protocol.

#### <span id="page-2-0"></span>3.1. Problem Setup

Noise Type This work mainly explores three types of noises, that is *Symmetric Noise*, *Asymmetric Noise*, and *Natural Noise*. Both of which are widely employed in existing researches [\[24,](#page-8-2) [55\]](#page-9-1). Here we give a brief review.

• Symmetric Noise: Labels are randomly altered to any other class  $y' \neq y$  with a fixed probability. Formally,

$$
p(\tilde{y} = y'|y) = \begin{cases} 1 - \tau, & \text{if } y' = y \\ \frac{\tau}{C - 1}, & \text{if } y' \neq y \end{cases}
$$
 (1)

where  $\tilde{y}$  is the noisy label, C is the total number of classes, and  $\tau$  represents the noise rate. This noise model simulates uniform random label disturbances, wherein every label has an equal likelihood of being randomly changed to any other class, creating a noise structure that is unbiased across classes.

• Asymmetric Noise: This form of noise simulates scenarios where label ambiguity is influenced by inherent class similarities or overlapping boundaries between classes. Mathematically, asymmetric noise can be defined by a conditional probability distribution  $p(\tilde{y} = y' | y)$  that assigns higher probabilities to semantically similar classes. Formally, let the noise probability  $\tau(y \to y')$  denote the likelihood of a clean label  $y$  being corrupted to a specific label y', where  $\tau(y \to y')$  depends on the semantic similarity between  $y$  and  $y'$  such that:

$$
p(\tilde{y} = y' \mid y) = \begin{cases} 1 - \sum_{y' \neq y} \tau(y \to y'), & \text{if } y' = y \\ \tau(y \to y'), & \text{if } y' \neq y \end{cases}
$$

where  $\tilde{y}$  represents the noisy label, and  $\tau(y \to y')$  reflects an asymmetric noise rate specific to pairs of classes  $(y, y')$ . This distribution emphasizes the higher probability of corruption toward semantically close or visually similar classes, capturing realistic noise patterns that occur in scenarios with ambiguous or related class structures.

Natural Noise: Noise in data often originates from the annotation process. Notable datasets that capture natural noise include the CIFAR-N series [\[46\]](#page-9-25), which closely approximates real-world label noise by emulating human annotation inconsistencies through multiple settings (Aggregate, Random1-3, and Worst). Additionally, high-volume datasets like Clothing1M [\[48\]](#page-9-26) and WebVision [\[27\]](#page-8-23) serve as classic benchmarks for studying learning under realistic label noise conditions, providing extensive resources for robust learning research.

Training Protocol Noisy label learning aims to effectively learn from data where labels are often corrupted or inaccurate. Let  $\tilde{S} = \{x_i, \tilde{y}_i\}_{i=1}^n$  denote a dataset with noisy labels, where  $x_i$  is the input image, and  $\tilde{y}_i$  indicates the potentially corrupted label. The dataset  $S$  is sampled from a <span id="page-3-0"></span>noisy joint distribution  $\tilde{\mathcal{D}}$  distinct from the clean one  $\mathcal{D}$ . For this task, Cross-Entropy loss (CE) is commonly used as the loss function to train a robust classifier  $f : X \to Y$  capable of assigning the true label  $y$  to test instances. Formally,

$$
\mathcal{L}_{CE}(f(x), y) = -\sum_{c=1}^{C} y_c \log(f_c(x)),
$$
\n(3)

where C is the total number of classes,  $y_c$  is the label for class c, and  $f_c(x)$  indicates the model predicted probability for class c.

Remark. Existing work [\[1\]](#page-8-7) has explored the effect of "memorization" in DNNs, especially in noisy label learning [\[14,](#page-8-4) [15,](#page-8-8) [25,](#page-8-9) [47,](#page-9-12) [53\]](#page-9-13). This kind of phenomenon reveals that networks always first memorize training data of common patterns (e.g. clean labels) and then those of hard samples (e.g., noisy labels). In this work, we propose a novel perspective that the process of dataset distillation aims to condense informatively subset data that shares the similar spirit of learning from noisy labels, both of which attempt to promote stable model training with common patterns. The distinction lies in their objectives: noise learning aims at achieving a robust learning model, whereas dataset distillation seeks to capture reliable, clean, and common patterns. This raises an important question: *Why not directly distill a clean subset that enables effective training?* This is intuitive, and we begin benchmarking existing methods and provide insights based on experimental results in the following section.

## 3.2. Benchmarking Dataset Distillation

We observe a recent surge in research on dataset distillation [\[44\]](#page-9-11), making it challenging to cover all related works comprehensively. Thus, we select three representative stateof-the-art methods based on their categorization: distribution matching (DANCE [\[54\]](#page-9-14)), meta-learning (RCIG [\[32\]](#page-9-15)), and parameter matching (DATM [\[13\]](#page-8-10)).

Benchmark-I: DATM is an advanced representative parameter matching method grouped on trajectory matching. It optimizes the generation of synthetic datasets by directly matching the model parameters trained on synthetic datasets with those trained on real datasets. In this way, DATM assumes that the performance of synthetic dataset can be achieved by aligning with the training trajectory of the real dataset, thus improving the quality of synthetic data. Specifically, let us define  $\theta_t^*$  as the expert model parameters obtained after t steps of training on real images, and  $\theta_{t+M}$ denotes the expert model parameters after M steps after  $\theta_t^*$ . Similarly,  $\hat{\theta}_{t+T}$  denotes the student model parameters obtained after  $T$  steps of training on the synthetic dataset, initialized from  $\theta_t^*$ . The objective of DATM is to align the student model trained for  $T$  steps on the synthetic data with the performance of the expert model trained for  $M$  steps on real data, typically under the condition  $T \ll M$ . Formally, this objective is defined as follows:

$$
\mathcal{L} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2}{\|\theta_t^* - \theta_{t+M}^*\|_2^2}.
$$
\n(4)

While DATM demonstrates strong performance on smaller datasets, it faces significant scalability challenges on largescale, real-world datasets like ImageNet. Recent work proposed by Cui et al. [\[9\]](#page-8-24) identifies the bottleneck in trajectory matching as the unrolled gradient computation, revealing that the overall memory complexity remains constant with respect to T. The proposed memory-efficient variant requires only a single gradient computational graph, thereby drastically reducing computational overhead while maintaining comparable performance to the original method. Unless otherwise specified, Benchmark-I referenced later denotes this accelerated version.

Benchmark-II: DANCE improves the milestone distribution matching method by addressing both the inner-class and the inter-class limitations. Formally, the condensed set is optimized by:

$$
\mathcal{D}_{\text{syn}}^{*} = \arg \min_{\mathbb{E}_{\phi} \sim P_{\phi}} \|\frac{\sum_{i=1}^{|\mathcal{D}_{\text{real}}|} \phi(\mathbf{x}_{i}^{\text{real}})}{\mathcal{D}_{\text{real}}} - \frac{\sum_{j=1}^{|\mathcal{D}_{\text{syn}}|} \phi(\mathbf{x}_{j}^{\text{syn}})}{\mathcal{D}_{\text{syn}}}\|^{2},\tag{5}
$$

where  $\phi$  combines the randomly initialized encoders  $\phi_0$  and their corresponding trained counterpart  $\phi_{\text{expert}}$ , that is,

$$
\phi \leftarrow \lambda \cdot \phi_0 + (1 - \lambda) \cdot \phi_{\text{expert}}.\tag{6}
$$

Herein  $\lambda \sim U(0, 1)$  is a randomly generated value. Apart from this, DANCE also pushes parameters of  $\phi_{\text{expert}}$  to adapt synthetic data to calibrate the inter-class distribution shift:

$$
\mathcal{L}_{\text{expert}} = \frac{1}{\mathcal{D}_{\text{syn}}} \sum_{j=1}^{|\mathcal{D}_{\text{syn}}|} \ell_{ce}(\theta_{\text{expert}}(x_j^{\text{syn}}), y_j^{\text{syn}}). \tag{7}
$$

This kind of strategy regularizes the synthetic data near the original data distribution. More details can be found in [\[54\]](#page-9-14). Benchmark-III: RCIG proposes a novel meta-learning framework for dataset distillation. It is achieved by reparameterizing and convexifying implicit gradients to enable analytical exploration. Let  $\mathcal{L}_{o} = \mathcal{L}_{T}(\theta), \mathcal{L}_{i} = \mathcal{L}_{S(\psi)}(\theta)$ .  $\mathcal{L}_T(\theta)$  and  $\mathcal{L}_{S(\psi)}(\theta)$  denote the training losses of the full training set and the support set, respectively.  $\psi$  is just the goal of dataset distillation. Specifically, RCIG as a metalearning method, defines its inner and outer loops as follows:

$$
\mathcal{L}_{i}(\theta_{B}) = \mathcal{L}_{S(\psi)}(\theta_{B}, \theta_{F}^{*}(\theta_{B}, \psi))
$$
  
\n
$$
\mathcal{L}_{o}(\theta_{B}, \psi) = \mathcal{L}_{\text{plat}, T}(\theta_{B}, \theta_{F}^{*}(\theta_{B}, \psi), \tau).
$$
\n(8)

Here,  $\theta_B$  and  $\theta_F$  denote the backbone parameters and the final layer parameters, respectively.  $\tau$  is the learnable

<span id="page-4-1"></span><span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays three line graphs side-by-side, each plotting accuracy (%) against images per class. The x-axis is on a logarithmic scale, with points at 1, 10, and 50 (or 500 for the first graph). The y-axis ranges from 20 to 80. Each graph corresponds to a different method: DATM, DANCE, and RCIG. Within each graph, there are five lines, each representing a different noise rate: 0.0 (blue), 0.2 (orange), 0.4 (green), 0.6 (red), and 0.8 (purple). Dotted horizontal lines are present at approximately 20, 40, 60, and 80 on the y-axis, with additional dotted lines corresponding to the approximate accuracy levels for each noise rate. In general, accuracy increases with the number of images per class, and higher noise rates tend to result in lower accuracy. The DATM graph shows a peak for the purple line (noise rate 0.8) around 10 images per class, after which accuracy decreases. The DANCE and RCIG graphs show a more consistent trend where accuracy generally increases with images per class, with the purple line (noise rate 0.8) consistently showing the lowest accuracy.

(a) Symmetric noisy dataset distillation for different noisy ratio on CIFAR-10.

Image /page/4/Figure/2 description: The image displays three line graphs side-by-side, each plotting accuracy (%) against the number of images per class on a logarithmic scale. Each graph corresponds to a different method: DATM, DANCE, and RCIG. Within each graph, five lines represent different noise rates: 0.0 (blue), 0.2 (orange), 0.4 (green), 0.6 (red), and 0.8 (purple). The y-axis ranges from 10% to 50%, with dashed lines indicating the noise rate values. The x-axis for DATM and DANCE ranges from 1 to 100, while the x-axis for RCIG ranges from 1 to 50. In general, accuracy increases with the number of images per class, and higher noise rates lead to lower accuracy across all methods. The DATM and DANCE methods show similar trends, with DATM generally achieving slightly higher accuracy. The RCIG method shows lower overall accuracy compared to DATM and DANCE, especially at higher noise rates and fewer images per class.

(b) Symmetric noisy dataset distillation for different noisy ratio on CIFAR-100.

Image /page/4/Figure/4 description: The image displays three line graphs side-by-side, each plotting accuracy (%) against images per class on a logarithmic scale (1, 10, 50). Each graph represents a different method: DATM, DANCE, and RCIG. Within each graph, five lines, distinguished by color and dotted horizontal reference lines, represent different noise rates: 0.0 (blue), 0.2 (orange), 0.4 (green), 0.6 (red), and 0.8 (purple). For DATM, accuracy increases with images per class for all noise rates, with higher noise rates resulting in lower accuracy. DANCE shows a similar trend, with accuracy plateauing at higher images per class. RCIG exhibits a different pattern, where accuracy generally increases with images per class for lower noise rates but decreases for the highest noise rate (0.8) at 50 images per class. The y-axis ranges from 0 to 40, with tick marks at 10, 20, 30, and 40.

(c) Symmetric noisy dataset distillation for different noisy ratio on Tiny-ImageNet.

Figure 2. The validation performance over symmetric noise for CIFAR-10, CIFAR-100, and Tiny-ImageNet datasets. The solid lines represent the accuracy trend as Image Per Class (IPC) increases, while the horizontal dashed lines of the same color indicate the performance of training on the full dataset at the corresponding noise rate.

temperature parameter.  $\mathcal{L}_{\text{plat}}$  originates from Platt scaling loss [\[38\]](#page-9-27).  $\theta_F^*$  is defined with neural network gaussian process as:

$$
\theta_F^* = h_{\theta_0}(X_S)(K_{X_S, X_S}^{\theta_0} + \lambda I_{|S|})^{-1}\hat{y}_S
$$
  
$$
\hat{y}_S = (y_S - \theta_B^T \frac{\partial f_{\text{lin}, \theta}(X_S)}{\partial \theta_B}),
$$
\n(9)

where  $y_S$  means labels and  $f_{lin}$  is the 1st-order Taylor approximation of learning dynamics linearized dynamics [\[31\]](#page-9-28).  $h_{\theta_0}(X_S)$  defines the embeddings of the hidden layer, with  $|S|$  being the distilled data size and H the final layer dimension. Noted that more information about NTK  $K_{X_S, X_S}^{\theta_0}$  can be found in [\[31,](#page-9-28) [60\]](#page-9-29).

Remark. In this section, we revisit three types of dataset distillation benchmarking approaches. We further explore these representative methods to validate whether the distillation procedure works well on noisy data. We believe that the results of these experiments can provide insightful perspectives for offering an innovative solution to the data noise challenge but also demonstrate substantial potential for application in privacy preservation.

## 4. Observations and Insights

This section follows a structured pipeline: we begin by outlining the experimental details, followed by a summary of

<span id="page-5-1"></span><span id="page-5-0"></span>Image /page/5/Picture/0 description: This image is a grid of 5 rows and 10 columns of images, with labels on the left and top. The labels on the left are \tau = 0.0, \tau = 0.2, \tau = 0.4, \tau = 0.6, and \tau = 0.8. The labels on the top are airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. Each cell in the grid contains a small image. The images appear to be generated samples from a model, with varying degrees of clarity and detail depending on the \tau value. For example, at \tau = 0.0, the images are very blurry, while at \tau = 0.8, they are more defined but still somewhat abstract.

Figure 3. Visualization of images distilled from DATM on CIFAR-10 with one image per class.

key observations, and conclude with insightful conclusions.

Implementations. We evaluate these benchmarks using datasets CIFAR-10/100 [\[20\]](#page-8-6), and tiny-ImageNet [\[21\]](#page-8-25), curating noisy versions [\[37,](#page-9-30) [55\]](#page-9-1) using symmetric and asymmetric described in Sec. [3.1.](#page-2-0) Specifically, for asymmetric noise, labels are flipped to similar classes (e.g., in CIFAR-10: TRUCK  $\rightarrow$  AUTOMOBILE, BIRD  $\rightarrow$  AIRPLANE, DEER  $\rightarrow$  HORSE, CAT  $\longleftrightarrow$  DOG; in CIFAR-100, the 100 classes are grouped into 20 superclasses, with each subclass flipping to the next within the same superclass). Additionally, we also adopt a more challenging version CIFAR-N [\[46\]](#page-9-25) that mimics human annotations. Following [\[5,](#page-8-26) [56,](#page-9-31) [57\]](#page-9-23), we employ a simple ConvNet [\[40\]](#page-9-32) architecture for distillation: a three-layer ConvNet for CIFAR and a four-layer ConvNet for Tiny-ImageNet. Performance is evaluated based on test accuracy on distilled datasets, following the evaluation protocols of DATM, DANCE, and RCIG, with data augmentation applied for RCIG as recommended in the original work. Final test accuracies are reported throughout the distillation process.

#### Experiment-I: ☞ Symmetric Noise Distillation

Figure [2](#page-4-0) shows performance across different IPC (Images Per Class) and noise conditions under symmetric noise for CIFAR-10, CIFAR-100, and Tiny-ImageNet. We use Cross-Entropy loss by default to investigate the impact of dataset distillation on noisy data. The dashed lines in each subplot represent evaluations of training on the full dataset at the current noise rate.

✽ Observation-I: *Once the noise surpasses a certain threshold, the three representative dataset distillation methods consistently demonstrate significant performance gains* *over the baseline trained on the entire noisy dataset, even when distilled to very few samples.*

As shown in Figure  $2(a)$  $2(a)$ , training results after dataset distillation consistently outperform the baseline at noise rates  $\tau = 0.2, 0.4, 0.6, 0.8$ . As noise increases, distillation achieves better performance with fewer distilled samples. For example, at noise rates of 0.6 and 0.8, a single distilled sample per class surpasses the baseline. At a noise rate of 0.2, fewer than 50 samples per class significantly outperform the baseline (indicated by the yellow curve and dashed line). Similar trends are observed in Figures [2\(](#page-4-0)b) and (c). Figure [3](#page-5-0) shows the results of distilled DATM images on CIFAR-10, where a single distilled image retains discriminative features despite high noise ratios.

 $\Delta$  Insight-I: For symmetric noise, existing dataset distillation methods serve as effective denoising tools, which supports with our initial intuitive assumption: the distillation process primarily captures common patterns while ignoring outliers such as noise. However, the critical question remains: How much data should be distilled from a noisy dataset to create a meaningful distilled set? This question could potentially be answered by analyzing the results of dataset distillation, offering a way to estimate the proportion of noise in the data. This presents a compelling and valuable problem for further analysis and exploration.

**Corollary-I.** *Given a balanced dataset*  $\tilde{S} \in \tilde{\mathcal{D}}$  *with symmetric noise* τ, *to perfectly preserve the sample information from the original dataset for each class during dataset distillation, the maximum number of distilled samples per class*

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays two rows of line graphs, each row containing three subplots. The top row is labeled "(a) Asymmetric noisy dataset distillation for different noisy ratios on CIFAR-10." Each subplot in the top row represents a different method: DATM, DANCE, and RCIG. The x-axis for all subplots in the top row is "Images Per Class" with values ranging from 1 to 1000 (with logarithmic scaling for 10, 50, 500, 1000). The y-axis for all subplots is "Accuracy (%)" ranging from 40 to 80. Each subplot shows five colored lines (blue, orange, green, red, purple) representing different noise rates: 0.0, 0.2, 0.4, 0.6, and 0.8, respectively. The second row is labeled "(b) Asymmetric noisy dataset distillation for different noisy ratios on CIFAR-100." Similar to the top row, it has three subplots for DATM, DANCE, and RCIG. The x-axis for these subplots is "Images Per Class" with values ranging from 1 to 50 (with logarithmic scaling for 10, 50). The y-axis is "Accuracy (%)" ranging from 0 to 40. Each subplot again shows five colored lines representing the same noise rates as in the top row.

(b) Asymmetric noisy dataset distillation for different noisy ratios on CIFAR-100.

Figure 4. The validation performance over asymmetric noise for CIFAR-10, CIFAR-100. The solid lines represent the accuracy trend as Image Per Class (IPC) increases, while the horizontal dashed lines of the same color indicate the performance of training on the full dataset at the corresponding noise rate

*(Image Per Class, IPC) required is:*

$$
\text{IPC} \le \frac{|\tilde{\mathcal{S}}| \cdot (1 - \tau)}{C},\tag{10}
$$

*where* C *is the total number of classes. This corollary succinctly states the upper bound for the number of distilled samples per class needed to retain clean data information under the given noise conditions.*

#### Experiment-II: ☞ Asymmetric Noise Distillation

We also perform dataset distillation experiments on CIFAR-10 and CIFAR-100 with asymmetric noise. As illustrated in Figure [4,](#page-6-0) most state-of-the-art methods perform worse on datasets with asymmetric noise compared to the benchmark results from training on the full-noise dataset. Only the DATM method achieves comparable performance to the benchmark at noise rate  $\tau = \{0.2, 0.4\}$ , provided the distilled synthetic dataset is sufficiently large.

✽ Observation-II: *Dataset distillation methods struggle to synthesize the samples that cover the original clean data distribution when confronted with asymmetric noise. Even when a larger number of synthetic samples are distilled, they still fail to accurately capture the true clean data distribution.*

This phenomenon is consistent with intuition. Specifically, in our data construction process,  $\tau(y \to y')$  denotes

the probability of corrupting the label of a clean sample  $y$  to a noisy label  $y'$ . Generally, label corruption tends to occur primarily among visually similar classes, such as TRUCK  $\rightarrow$  AUTOMOBILE, as discussed before. In such cases, the noise process becomes structured or 'patternized'. Consequently, during dataset distillation, this structured noise is inevitably carried over alongside the clean sample patterns, causing the distilled dataset to retain the same label transition patterns. Thus, while the distillation process preserves the clean data patterns, it also captures the inherent noise structure, which hinders the synthetic dataset from accurately representing the true data distribution.

 $\mathbb{Z}$  Insight-II: This observation provides a valuable insight that warrants deeper exploration. Specifically, when employing dataset distillation to address label noise, it is crucial not to assume that common patterns always reflect clean data. Under certain conditions (e.g., when categories with high visual similarity are mislabelled due to human annotation errors), the resulting noise may be structured rather than purely stochastic. This highlights the necessity for a more nuanced approach to dataset distillation, as they may inadvertently preserve and amplify these noise patterns. We further hypothesize that challenging clean samples, such as tail data in imbalanced datasets, are at risk of being lossy

<span id="page-7-1"></span><span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays two rows of plots. The top row, labeled (a) Natural noisy dataset distillation on CIFAR-10N, contains three plots. Each plot shows the accuracy (%) on the y-axis against the number of images per class on a logarithmic x-axis. The first plot is for DATM, the second for DANCE, and the third for RCIG. Each plot in the top row shows multiple colored lines representing different methods (Aggre, Worst, Rand1, Rand2, Rand3), with dashed horizontal lines indicating reference accuracy levels. The bottom row, labeled (b) Natural noisy dataset distillation on CIFAR-100N, also contains three plots, each with accuracy (%) on the y-axis and images per class on a logarithmic x-axis. These plots are for DATM, DANCE, and RCIG, and each shows a single blue line representing a 'Noise' method, with a dashed horizontal line at approximately 40% accuracy.

(b) Natural noisy dataset distillation on CIFAR-100N.

Figure 5. The results for CIFAR-10N/CIFAR-100N. The solid lines illustrate the trend of accuracy as Image Per-Class (IPC) increases. The horizontal dashed lines of the same color indicate the evaluation of training on the full dataset under the corresponding noise rate.

compressed during the dataset distillation process. Given space constraints, we encourage further research into how structured noise/hard clean samples can be identified and well processed during distillation, ensuring that such biases do not undermine the quality of the distilled dataset.

## Experiment-III: ☞ Natural Noise Distillation

Previous experiments have investigated dataset distillation for synthetic noisy labels on CIFAR10, CIFAR100, and Tiny-ImageNet, which are widely used benchmarks. In this part, we empirically validate the robustness on human annotated labels [\[46\]](#page-9-25) as illustrated in Figure [5.](#page-7-0) For CIFAR10N, each training image contains one clean label and three human annotated labels. **Random k** ( $\tau \approx 18\%$ ) means the k−th annotated labels while Worst ( $\tau = 40.21\%$ ) and Ag**gre** ( $\tau = 9.03\%$ ) indicate the selected wrong label and majority voting label, respectively. For CIFAR100N, we chose the "fine" version from [\[46\]](#page-9-25) that contains 40.20% noises.

✽ Observation-III: *Dataset distillation approaches still generalize well on real-world defective data with fixed noisy ratio, especially for high noise ratio.*

We notice that in Figure [5,](#page-7-0) all distillation methods perform well under the Worst scenario, with DATM, DANCE, and RCIG surpassing the CIFAR100N baseline using fewer than 10 distilled images per class. However, for lower noise ratio like Aggre, these distillation approaches struggle to perform well.

 $\triangle$  Insight-III: For real-world natural noise, dataset distillation methods remain largely effective even in the presence of an unknown fixed noise rate. The challenge in this scenario is similar to that with symmetric noise, specifically the need to determine the appropriate amount of distilled data, i.e., how much valid information should be preserved. When the number of retained samples is small, not only is noisy data excluded, but even good clean samples are subjected to lossy compression. This is why data distillation often yields suboptimal results in low-noise settings.

**Corollary-II.** Given a noisy dataset  $\tilde{S} \in \tilde{\mathcal{D}}$  from a real*world scenario with* C *classes, when applying dataset distillation such that each class is synthesized to a size of IPC, and the validation accuracy matches that of the original dataset, we can infer with at least high probability*  $1 - \delta$ *that the noise rate* τ *of the dataset satisfies:*

$$
\mathbb{P}_{\tilde{\mathcal{S}} \in \tilde{\mathcal{D}}} \left[ \tau \ge (1 - \frac{C \cdot \text{IPC}}{|\tilde{\mathcal{S}}|}) \right] \ge 1 - \delta. \tag{11}
$$

This corollary provides a probabilistic bound on the noise rate  $\tau$  in a noisy dataset based on the distillation process and the accuracy achieved after distillation.

## 5. Conclusion

We propose a new approach to model learning from noisy data that avoids vicious cycles, improves training efficiency, and ensures privacy protection through offline dataset distillation. Our findings show that dataset distillation effectively denoises random and natural noise, though it may struggle with structured asymmetric noise and lossy compression of challenging clean samples, especially in imbalanced datasets. Despite these limitations, dataset distillation shows strong potential for robust model training, particularly in high-privacy environments.

## References

- <span id="page-8-7"></span>[1] Devansh Arpit, Stanisław Jastrzębski, Nicolas Ballas, David Krueger, Emmanuel Bengio, Maxinder S Kanwal, Tegan Maharaj, Asja Fischer, Aaron Courville, Yoshua Bengio, et al. A closer look at memorization in deep networks. In *International conference on machine learning*, pages 233– 242. PMLR, 2017. [2,](#page-1-2) [4](#page-3-0)
- <span id="page-8-0"></span>[2] HeeSun Bae, Seungjae Shin, Byeonghu Na, JoonHo Jang, Kyungwoo Song, and Il-Chul Moon. From noisy prediction to true label: Noisy prediction calibration via generative model, 2022. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-8-5"></span>[3] HeeSun Bae, Seungjae Shin, Byeonghu Na, and Il-Chul Moon. Dirichlet-based per-sample weighting by transition matrix for noisy label learning, 2024. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-8-19"></span>[4] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [3](#page-2-1)
- <span id="page-8-26"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories, 2022. [6](#page-5-1)
- <span id="page-8-20"></span>[6] Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. *arXiv preprint arXiv:2310.06982*, 2023. [3](#page-2-1)
- <span id="page-8-11"></span>[7] Hao Cheng, Zhaowei Zhu, Xingyu Li, Yifei Gong, Xing Sun, and Yang Liu. Learning with instance-dependent label noise: A sample sieve approach. *arXiv preprint arXiv:2010.02347*, 2020. [2](#page-1-2)
- <span id="page-8-3"></span>[8] Madalina Ciortan, Romain Dupuis, and Thomas Peel. A framework using contrastive learning for classification with noisy labels, 2021. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-8-24"></span>[9] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [4](#page-3-0)
- <span id="page-8-1"></span>[10] Erik Englesson and Hossein Azizpour. Robust classification via regression for learning with noisy labels. In *ICLR 2024- The Twelfth International Conference on Learning Representations, Messe Wien Exhibition and Congress Center, Vienna, Austria, May 7-11t, 2024*, 2024. [1](#page-0-1)
- <span id="page-8-16"></span>[11] Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming

Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. *arXiv preprint arXiv:2305.01975*, 2023. [3](#page-2-1)

- <span id="page-8-12"></span>[12] Aritra Ghosh and Andrew Lan. Contrastive learning improves model robustness under label noise, 2021. [2](#page-1-2)
- <span id="page-8-10"></span>[13] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [2,](#page-1-2) [3,](#page-2-1) [4](#page-3-0)
- <span id="page-8-4"></span>[14] Bo Han, Quanming Yao, Xingrui Yu, Gang Niu, Miao Xu, Weihua Hu, Ivor Tsang, and Masashi Sugiyama. Coteaching: Robust training of deep neural networks with extremely noisy labels. *Advances in neural information processing systems*, 31, 2018. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-0)
- <span id="page-8-8"></span>[15] Bo Han, Gang Niu, Xingrui Yu, Quanming Yao, Miao Xu, Ivor Tsang, and Masashi Sugiyama. Sigua: Forgetting may make learning with noisy labels more robust. In *International Conference on Machine Learning*, pages 4006–4016. PMLR, 2020. [2,](#page-1-2) [4](#page-3-0)
- <span id="page-8-21"></span>[16] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. *arXiv preprint arXiv:2403.06075*, 2024. [3](#page-2-1)
- <span id="page-8-18"></span>[17] Timothy Hospedales, Antreas Antoniou, Paul Micaelli, and Amos Storkey. Meta-learning in neural networks: A survey. *IEEE transactions on pattern analysis and machine intelligence*, 44(9):5149–5169, 2021. [3](#page-2-1)
- <span id="page-8-13"></span>[18] Zhizhong Huang, Junping Zhang, and Hongming Shan. Twin contrastive learning with noisy labels, 2023. [2](#page-1-2)
- <span id="page-8-15"></span>[19] Lu Jiang, Zhengyuan Zhou, Thomas Leung, Li-Jia Li, and Li Fei-Fei. Mentornet: Learning data-driven curriculum for very deep neural networks on corrupted labels, 2018. [2](#page-1-2)
- <span id="page-8-6"></span>[20] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, University of Toronto, 2009. Technical Report. [1,](#page-0-1) [6](#page-5-1)
- <span id="page-8-25"></span>[21] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6](#page-5-1)
- <span id="page-8-22"></span>[22] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *Forty-first International Conference on Machine Learning*, 2024. [3](#page-2-1)
- <span id="page-8-17"></span>[23] Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023. [3](#page-2-1)
- <span id="page-8-2"></span>[24] Junnan Li, Richard Socher, and Steven CH Hoi. Dividemix: Learning with noisy labels as semi-supervised learning. *arXiv preprint arXiv:2002.07394*, 2020. [1,](#page-0-1) [2,](#page-1-2) [3](#page-2-1)
- <span id="page-8-9"></span>[25] Mingchen Li, Mahdi Soltanolkotabi, and Samet Oymak. Gradient descent with early stopping is provably robust to label noise for overparameterized neural networks. In *International conference on artificial intelligence and statistics*, pages 4313–4324. PMLR, 2020. [2,](#page-1-2) [4](#page-3-0)
- <span id="page-8-14"></span>[26] Shikun Li, Xiaobo Xia, Shiming Ge, and Tongliang Liu. Selective-supervised contrastive learning with noisy labels, 2022. [2](#page-1-2)
- <span id="page-8-23"></span>[27] Wen Li, Limin Wang, Wei Li, Eirikur Agustsson, and Luc Van Gool. Webvision database: Visual learning and understanding from web data. *arXiv preprint arXiv:1708.02862*, 2017. [3](#page-2-1)

- <span id="page-9-9"></span>[28] Yifan Li, Hu Han, Shiguang Shan, and Xilin Chen. Disc: Learning from noisy labels via dynamic instance-specific selection and correction. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 24070–24079, 2023. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-10"></span>[29] Sheng Liu, Kangning Liu, Weicheng Zhu, Yiqiu Shen, and Carlos Fernandez-Granda. Adaptive early-learning correction for segmentation from noisy annotations, 2022. [1](#page-0-1)
- <span id="page-9-19"></span>[30] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877–13891, 2022. [3](#page-2-1)
- <span id="page-9-28"></span>[31] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Evolution of neural tangent kernels under benign and adversarial training. *Advances in Neural Information Processing Systems*, 35:11642–11657, 2022. [5](#page-4-1)
- <span id="page-9-15"></span>[32] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients, 2023. [2,](#page-1-2) [3,](#page-2-1) [4](#page-3-0)
- <span id="page-9-6"></span>[33] Eran Malach and Shai Shalev-Shwartz. Decoupling "when to update" from "how to update", 2018. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-0"></span>[34] Nagarajan Natarajan, Inderjit S Dhillon, Pradeep K Ravikumar, and Ambuj Tewari. Learning with noisy labels. *Advances in neural information processing systems*, 26, 2013. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-20"></span>[35] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [3](#page-2-1)
- <span id="page-9-21"></span>[36] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [3](#page-2-1)
- <span id="page-9-30"></span>[37] Giorgio Patrini, Alessandro Rozza, Aditya Menon, Richard Nock, and Lizhen Qu. Making deep neural networks robust to label noise: a loss correction approach, 2017. [6](#page-5-1)
- <span id="page-9-27"></span>[38] John Platt et al. Probabilistic outputs for support vector machines and comparisons to regularized likelihood methods. *Advances in large margin classifiers*, 10(3):61–74, 1999. [5](#page-4-1)
- <span id="page-9-8"></span>[39] Mengye Ren, Wenyuan Zeng, Bin Yang, and Raquel Urtasun. Learning to reweight examples for robust deep learning, 2019. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-32"></span>[40] Levent Sagun, Utku Evci, V. Ugur Guney, Yann Dauphin, and Leon Bottou. Empirical analysis of the hessian of overparametrized neural networks, 2018. [6](#page-5-1)
- <span id="page-9-2"></span>[41] Jun Shu, Qi Xie, Lixuan Yi, Qian Zhao, Sanping Zhou, Zongben Xu, and Deyu Meng. Meta-weight-net: Learning an explicit mapping for sample weighting, 2019. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-18"></span>[42] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021. [3](#page-2-1)
- <span id="page-9-3"></span>[43] Ke Wang, Guillermo Ortiz-Jimenez, Rodolphe Jenatton, Mark Collier, Efi Kokiopoulou, and Pascal Frossard. Pi-dual: Using privileged information to distinguish clean from noisy labels, 2024. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-11"></span>[44] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [2,](#page-1-2) [3,](#page-2-1) [4](#page-3-0)

- <span id="page-9-7"></span>[45] Yisen Wang, Weiyang Liu, Xingjun Ma, James Bailey, Hongyuan Zha, Le Song, and Shu-Tao Xia. Iterative learning with open-set noisy labels, 2018. [1](#page-0-1)
- <span id="page-9-25"></span>[46] Jiaheng Wei, Zhaowei Zhu, Hao Cheng, Tongliang Liu, Gang Niu, and Yang Liu. Learning with noisy labels revisited: A study using real-world human annotations. *arXiv preprint arXiv:2110.12088*, 2021. [3,](#page-2-1) [6,](#page-5-1) [8](#page-7-1)
- <span id="page-9-12"></span>[47] Xiaobo Xia, Tongliang Liu, Bo Han, Chen Gong, Nannan Wang, Zongyuan Ge, and Yi Chang. Robust early-learning: Hindering the memorization of noisy labels. In *International conference on learning representations*, 2020. [2,](#page-1-2) [4](#page-3-0)
- <span id="page-9-26"></span>[48] Tong Xiao, Tian Xia, Yi Yang, Chang Huang, and Xiaogang Wang. Learning from massive noisy labeled data for image classification. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2691–2699, 2015. [3](#page-2-1)
- <span id="page-9-16"></span>[49] Shuo Yang, Erkun Yang, Bo Han, Yang Liu, Min Xu, Gang Niu, and Tongliang Liu. Estimating instance-dependent bayes-label transition matrix using a deep neural network, 2022. [2](#page-1-2)
- <span id="page-9-4"></span>[50] Yu Yao, Tongliang Liu, Bo Han, Mingming Gong, Jiankang Deng, Gang Niu, and Masashi Sugiyama. Dual t: Reducing estimation error for transition matrix in label-noise learning, 2021. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-17"></span>[51] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023. [3](#page-2-1)
- <span id="page-9-5"></span>[52] Xiyu Yu, Tongliang Liu, Mingming Gong, and Dacheng Tao. Learning with biased complementary labels, 2018. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-13"></span>[53] Xingrui Yu, Bo Han, Jiangchao Yao, Gang Niu, Ivor Tsang, and Masashi Sugiyama. How does disagreement help generalization against label corruption? In *International conference on machine learning*, pages 7164–7173. PMLR, 2019. [2,](#page-1-2) [4](#page-3-0)
- <span id="page-9-14"></span>[54] Hansong Zhang, Shikun Li, Fanzhao Lin, Weiping Wang, Zhenxing Qian, and Shiming Ge. Dance: Dual-view distribution alignment for dataset condensation, 2024. [2,](#page-1-2) [3,](#page-2-1) [4](#page-3-0)
- <span id="page-9-1"></span>[55] Zhilu Zhang and Mert Sabuncu. Generalized cross entropy loss for training deep neural networks with noisy labels. *Advances in neural information processing systems*, 31, 2018. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-1) [6](#page-5-1)
- <span id="page-9-31"></span>[56] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation, 2021. [6](#page-5-1)
- <span id="page-9-23"></span>[57] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [3,](#page-2-1) [6](#page-5-1)
- <span id="page-9-22"></span>[58] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [3](#page-2-1)
- <span id="page-9-24"></span>[59] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation, 2023. [3](#page-2-1)
- <span id="page-9-29"></span>[60] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [5](#page-4-1)

<span id="page-10-0"></span>[61] Yuyin Zhou, Xianhang Li, Fengze Liu, Qingyue Wei, Xuxi Chen, Lequan Yu, Cihang Xie, Matthew P. Lungren, and Lei Xing. L2b: Learning to bootstrap robust models for combating label noise, 2024. [1,](#page-0-1) [2](#page-1-2)