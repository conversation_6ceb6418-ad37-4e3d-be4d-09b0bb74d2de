{"table_of_contents": [{"title": "DATASET META-LEARNING FROM KERNEL RIDGE-\nREGRESSION", "heading_level": null, "page_id": 0, "polygon": [[105.78515625, 80.25], [504.0, 80.25], [504.0, 116.40234375], [105.78515625, 116.40234375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 204.75], [334.6875, 204.75], [334.6875, 215.7890625], [276.75, 215.7890625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 417.0], [206.7890625, 417.0], [206.7890625, 428.87109375], [107.25, 428.87109375]]}, {"title": "This suggests the following", "heading_level": null, "page_id": 0, "polygon": [[106.5, 633.0], [218.25, 633.0], [218.25, 643.11328125], [106.5, 643.11328125]]}, {"title": "1.1 SUMMARY OF CONTRIBUTIONS", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 359.25], [265.658203125, 359.25], [265.658203125, 369.703125], [106.90576171875, 369.703125]]}, {"title": "2 SETUP", "heading_level": null, "page_id": 1, "polygon": [[106.8310546875, 674.82421875], [161.666015625, 674.82421875], [161.666015625, 685.65234375], [106.8310546875, 685.65234375]]}, {"title": "3 KERNEL INDUCING POINTS", "heading_level": null, "page_id": 3, "polygon": [[107.25, 492.0], [267.75, 492.0], [267.75, 503.12109375], [107.25, 503.12109375]]}, {"title": "Algorithm 1: Kernel Inducing Point (KIP)", "heading_level": null, "page_id": 4, "polygon": [[107.25, 87.0], [284.783203125, 87.0], [284.783203125, 96.3896484375], [107.25, 96.3896484375]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[106.681640625, 686.25], [200.513671875, 686.25], [200.513671875, 696.8671875], [106.681640625, 696.8671875]]}, {"title": "4.1 SINGLE KERNEL RESULTS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 331.8046875], [245.4873046875, 331.8046875], [245.4873046875, 342.6328125], [106.5, 342.6328125]]}, {"title": "4.2 KERNEL TO KERNEL RESULTS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 630.3515625], [262.669921875, 630.3515625], [262.669921875, 641.1796875], [106.5, 641.1796875]]}, {"title": "4.3 KERNEL TO NEURAL NETWORKS RESULTS", "heading_level": null, "page_id": 6, "polygon": [[106.5, 560.25], [316.458984375, 560.25], [316.458984375, 571.18359375], [106.5, 571.18359375]]}, {"title": "5 RELATED WORK", "heading_level": null, "page_id": 7, "polygon": [[107.25, 610.5], [213.2138671875, 610.5], [213.2138671875, 623.00390625], [107.25, 623.00390625]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 594.75], [196.4794921875, 594.75], [196.4794921875, 605.98828125], [107.25, 605.98828125]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 9, "polygon": [[106.98046875, 84.0], [202.306640625, 84.0], [202.306640625, 93.4892578125], [106.98046875, 93.4892578125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 143.25], [176.30859375, 143.25], [176.30859375, 153.140625], [107.1298828125, 153.140625]]}, {"title": "A REMARKS ON DEFINITION OF \\epsilon-APPROXIMATION", "heading_level": null, "page_id": 12, "polygon": [[107.25, 82.5], [378.9140625, 82.5], [378.9140625, 93.73095703125], [107.25, 93.73095703125]]}, {"title": "B TUNING KIP", "heading_level": null, "page_id": 12, "polygon": [[107.25, 405.75], [194.25, 405.75], [194.25, 416.8828125], [107.25, 416.8828125]]}, {"title": "C THEORETICAL RESULTS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 133.5], [252.0, 133.5], [252.0, 144.5361328125], [107.25, 144.5361328125]]}, {"title": "D EXPERIMENT DETAILS", "heading_level": null, "page_id": 15, "polygon": [[107.25, 454.5], [245.25, 454.5], [245.25, 465.609375], [107.25, 465.609375]]}, {"title": "E TABLES AND FIGURES", "heading_level": null, "page_id": 17, "polygon": [[107.25, 409.53515625], [243.0966796875, 409.53515625], [243.0966796875, 421.13671875], [107.25, 421.13671875]]}, {"title": "E.1 KERNEL BASELINES", "heading_level": null, "page_id": 17, "polygon": [[106.5, 435.0], [220.833984375, 435.0], [220.833984375, 445.11328125], [106.5, 445.11328125]]}, {"title": "E.2 KIP AND LS TRANSFER ACROSS KERNELS", "heading_level": null, "page_id": 17, "polygon": [[106.5, 678.75], [315.861328125, 678.75], [315.861328125, 689.1328125], [106.5, 689.1328125]]}, {"title": "E.3 <PERSON><PERSON> TRANSFER TO NEURAL NETWORKS AND CORRUPTION EXPERIMENTS", "heading_level": null, "page_id": 19, "polygon": [[106.5, 496.5], [449.25, 496.5], [449.25, 507.375], [106.5, 507.375]]}, {"title": "F EXAMPLES OF KIP LEARNED SAMPLES", "heading_level": null, "page_id": 23, "polygon": [[106.5, 81.75], [327.0, 81.75], [327.0, 94.552734375], [106.5, 94.552734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 48], ["Text", 8], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5921, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 74], ["ListItem", 7], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 791, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 856], ["Line", 69], ["Text", 7], ["TextInlineMath", 5], ["Equation", 5], ["Reference", 4], ["ListItem", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 50], ["TextInlineMath", 5], ["Text", 4], ["Reference", 3], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 661], ["Line", 66], ["ListItem", 6], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 51], ["Text", 8], ["SectionHeader", 2], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 101], ["TableCell", 98], ["Text", 8], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1175, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["TableCell", 203], ["Line", 51], ["Text", 6], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10421, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 78], ["Text", 6], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1016, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 45], ["ListItem", 17], ["Reference", 17], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 73], ["Line", 21], ["ListItem", 9], ["Reference", 9], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 414], ["Line", 51], ["Text", 8], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 737], ["Line", 71], ["TextInlineMath", 6], ["Equation", 5], ["Reference", 5], ["Text", 3], ["ListItem", 2], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1201], ["Line", 72], ["TextInlineMath", 5], ["Text", 3], ["Equation", 3], ["Reference", 3], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 648], ["Line", 56], ["Text", 6], ["TextInlineMath", 5], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1291, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 58], ["Text", 7], ["Reference", 3], ["TextInlineMath", 2], ["Footnote", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["TableCell", 52], ["Line", 48], ["Text", 6], ["SectionHeader", 3], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["TableCell", 204], ["Line", 61], ["Text", 6], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7047, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 92], ["TableCell", 54], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 13655, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["TableCell", 162], ["Line", 36], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 7902, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["TableCell", 90], ["Line", 36], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 5404, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 51], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1193, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Line", 175], ["Span", 16], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 703, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 18], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 709, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Meta-Learning_from_Kernel_Ridge-Regression"}