# Dataset Quantization

<span id="page-0-0"></span><PERSON><PERSON><PERSON> <sup>1\*</sup> <PERSON><sup>2\*</sup> <PERSON><PERSON><PERSON><sup>2\*</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>2</sup> <PERSON><sup>2†</sup> <PERSON><PERSON><sup>1†</sup> <sup>1</sup>Bytedance Inc.  $\frac{2\text{National University of Singapore}}{2}$ 

<EMAIL> <EMAIL>.<NAME_EMAIL>

<EMAIL> <EMAIL> Code: [https://github.com/magic-research/Dataset\\_Quantization](https://github.com/magic-research/Dataset_Quantization)

Code: https://github.com/magic-research/dataset\_quantization

### Abstract

*State-of-the-art deep neural networks are trained with large amounts (millions or even billions) of data. The expensive computation and memory costs make it difficult to train them on limited hardware resources, especially for recent popular large language models (LLM) and computer vision models (CV). Recent popular dataset distillation methods are thus developed, aiming to reduce the number of training samples via synthesizing small-scale datasets via gradient matching. However, as the gradient calculation is coupled with the specific network architecture, the synthesized dataset is biased and performs poorly when used for training unseen architectures. To address these limitations, we present* dataset quantization *(DQ), a new framework to compress large-scale datasets into small subsets which can be used for training any neural network architectures. Extensive experiments demonstrate that DQ is able to generate condensed small datasets for training unseen network architectures with state-of-the-art compression ratios for lossless model training. To the best of our knowledge, DQ is the first method that can successfully distill large-scale datasets such as ImageNet-1k with a stateof-the-art compression ratio. Notably, with 60% data from ImageNet and 20% data from Alpaca's instruction tuning data, the models can be trained with negligible or no performance drop for both vision tasks (including classification, semantic segmentation, and object detection) as well as language tasks (including instruction tuning tasks such as BBH and DROP).*

### 1. Introduction

Deep neural networks have shown superior performance in a wide range of fields such as computer vision [\[23,](#page-9-0) [22,](#page-9-1)

Image /page/0/Figure/11 description: A bar chart displays the Data Keep Ratio (rho) for different Vision and Language Tasks. The x-axis lists the tasks: C10, IN1K, COCO, ADE20K, BBH, DROP, and MMLU. The y-axis ranges from 0.0 to 1.4. Four categories are represented by different colored bars: Vis. DQ (red), Vis. Baseline (gray), LLM. DQ (blue), and LLM. Baseline (yellow). For C10, Vis. DQ is 0.54 and Vis. Baseline is 0.956, with a 40% reduction indicated by a red arrow. For IN1K, Vis. DQ is 0.694 and Vis. Baseline is 0.95, with a 40% reduction. For COCO, Vis. DQ is 0.392 and Vis. Baseline is 0.695, with a 40% reduction. For ADE20K, Vis. DQ is 0.426 and Vis. Baseline is 0.392, with a 20% reduction. For BBH, LLM. DQ is 0.330 and LLM. Baseline is 0.329, with a 98% reduction. For DROP, LLM. DQ is 0.276 and LLM. Baseline is 0.263, with a 98% reduction. For MMLU, LLM. DQ is 0.398 and LLM. Baseline is 0.416, with an 80% reduction.

Figure 1: Lossless dataset compression with Dataset Quantization (DQ) framework. On both vision and language tasks. In the plot, we use ResNet18 as backbone for all tasks and LLaMA-7B for all language tasks with instruction fine-tuning.

[16,](#page-9-2) [61\]](#page-10-0) and natural language processing [\[15,](#page-9-3) [4\]](#page-8-0). Their performance depends heavily on the amount of training data. For example, recent state-of-the-art models [\[33,](#page-9-4) [56,](#page-10-1) [13,](#page-9-5) [60\]](#page-10-2) on ImageNet-1K takes three billion data for pre-training. This is hardly affordable for researchers with limited computational resources. However, are all the data in the large dataset beneficial or necessary to the training? Is it possible to remove some redundant samples without degrading the training performance? What is the performance of the pretrained models with less data on downstream tasks? In this paper, we conduct extensive experiments and conduct detailed explorations on those questions. To address the first question, several Dataset Distillation (DD) algorithms [\[64,](#page-11-0) [62,](#page-10-3) [31,](#page-9-6) [63,](#page-10-4) [54,](#page-10-5) [5,](#page-8-1) [17,](#page-9-7) [53,](#page-10-6) [36\]](#page-10-7) are proposed recently to reduce the training dataset size by synthesizing a new set of data that is significantly smaller than the original one. With the new synthesized dataset, the training cost is reduced significantly, while yielding comparable results with the models trained on the original datasets.

<sup>\*</sup>Equal first author.

<sup>†</sup>Corresponding author.

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a multi-part figure analyzing dataset selection and quantization methods. Part (a) is a line graph titled 'Acc. (%)' versus 'Data keep ratio p'. It shows several lines representing different methods: 'Upper Bond' (dashed maroon line), 'Craig' (brown triangles), 'GradMatch' (yellow triangles), 'GC' (green circles), 'DC' (dark green circles), 'DM\_Random\_Init' (light green circles), 'DM\_Real\_Init' (dark green circles), and 'DQ' (red diamonds). A table in part (a) lists methods like 'DM-60', 'DQ-60', and 'Full' with corresponding 'Hours', 'IN-1K', and 'COCO' values. Parts (b) and (c) are scatter plots showing data distributions. Part (b) is labeled 'Dataset Distillation' and shows plots for different models ('R18', 'Convnext-Tiny', 'Swin-Tiny') at different 'p' values (20% and 10%), with 'Original Samples' in blue and 'Selected Samples' in red. Part (c) is labeled 'Dataset Quantization' and shows similar scatter plots for different models, with 'Original Samples' in blue and 'Synthetic/Selected Samples' in red. The accuracies for each plot are indicated at the top.

Figure 2: Our proposed dataset quantization outperforms existing dataset distillation and coreset selection methods significantly. (a) Model training accuracy from DD (DC  $[64]$  and DM  $[63]$ ), coreset selection (Craig  $[40]$ , GradMatch  $[30]$ , and GC [\[27\]](#page-9-9)), and our proposed DQ across different data keep ratios. 'Hours' denotes the time for compressing ImageNet dataset with 60% data keep ratio. (b) Visualization of the samples diversity of GraphCut and DQ, where  $\rho$  is the data keep ratio (better in color). (c) Cross-architecture visualization of the feature distributions among the dataset generated by a dataset distillation methods 'distribution matching' (DM) and DQ on ResNet-18 on CIFAR-10 bird class. Compared with DM, our proposed DQ effectively captures the whole dataset distribution for all the architectures, thus generalizing better.

Although having made significant progress, two limitations make those algorithms hard to be deployed in an industrial environment: i) Poor generalization capability. They all rely on specific metrics to match the synthetic and real samples [\[65,](#page-11-1) [63\]](#page-10-4). Thus the synthetic datasets are inevitably biased by the model architecture involved in the metric computation, resulting in poor performance when used for training unseen model architectures. For example, as shown in Fig. [2c,](#page-1-0) the dataset synthesized based on ResNet-18 [\[23\]](#page-9-0) suffers a 59.4% accuracy drop when used for training Swin-Tiny  $[37]$   $(81.2\%$  vs  $21.6\%)$ . ii) Low scalability to larger datasets. Different from other deep learning tasks that optimize the parameters of a given architecture, dataset distillation aims to optimize the synthetic set, the computational cost is quadratically proportional to the size of the synthetic set. When the size is large, the computational cost becomes unaffordable. For example, as in Fig. [2a,](#page-1-0) previous SOTA method DM [\[63\]](#page-10-4) needs 28, 000 GPU hours to distill ImageNet-1K with 60% data processing.

To address these limitations, we explore a different direction from synthesizing samples based on our empirical observations that the samples selected by coreset methods [\[28,](#page-9-10) [20,](#page-9-11) [9,](#page-9-12) [2,](#page-8-2) [43\]](#page-10-10) could be used to train unseen network architectures (*i.e.* good cross-architecture generalization). However, as the data keep ratio is small, the selected samples tend to lose the diversity, leading to a low performance for model training. As in the first row in Figure [2b,](#page-1-0) coreset methods tend to sample data points in a biased region. This led to a significant accuracy drop when used for model training. As shown in Fig. 6 in the following section, our proposed DQ is able to achieve 10% (75.7% vs 85.2%) higher accuracy over the previous SOTA coreset method.

In this paper, we aim to develop a method that combines the advantages of Dataset Distillation methods and the Coreset methods: a unified dataset compression method that generates compact datasets useful for training various network architectures while maintaining state-of-the-art training performance under all data keep ratios. We start with investigating the reason behind the poor performance of the coreset selection method [\[46\]](#page-10-11) under low data keep ratio, and we find it lies in the one-time selection strategy, resulting in a low diversity of the selected data. This will lead to a significant performance drop as shown in Fig. [2b.](#page-1-0) More detailed analysis on previous coreset selection methods [\[27,](#page-9-9) [30\]](#page-9-8) can be found in Sec. [3.1](#page-3-0) and in the Appendix.

We thus propose a new pipeline to overcome the aforementioned issues of the coreset algorithm and term it Dataset Quantization (DQ). Specifically, DQ first divides the entire dataset into a set of non-overlapping bins recursively based on the submodular gains [\[27\]](#page-9-9) that aims to maximize the diversity gains as defined in Eqn. [1.](#page-3-1) Then, a small portion of data samples is uniformly sampled from all bins. In this manner, the selected samples are optimized to cover as much as possible the entire dataset with the interdata diversity maximized. We prove mathematically that the dataset selected by DQ indeed has larger diversity than the coreset selection based methods. Motivated by recent patch-based image representation  $[16, 21, 69]$  $[16, 21, 69]$  $[16, 21, 69]$  $[16, 21, 69]$  $[16, 21, 69]$ , we measure the importance scores of patches and save the most impor<span id="page-2-2"></span>tant ones to reduce the storage cost. At the training stage, we reconstruct training images via important patches and a pre-trained MAE [\[21\]](#page-9-13) model.

Different from dataset distillation methods, as shown in the second row in Fig.  $2c$ , the quantized dataset maintains a high coverage over the entire data in the latent feature space across different model architectures. The validation accuracy is also significantly higher than those models trained with DD algorithms (*e.g*., 34.4% higher for ViT-Tiny). Compared with DD methods, DQ only takes 72 GPU hours to quantize ImageNet data with 60% keep ratio, which is  $388 \times (28, 000 \text{ vs } 72 \text{ GPU hours})$  faster, while achieving much higher performance on large data keep ratios. On the other hand, when comparing to coreset selection methods, as shown in Fig. [2a](#page-1-0) and [2b,](#page-1-0) DQ selects samples with larger diversity and achieves better performance when the data keep ratio is low (10% data kept).

We conduct extensive experiments and show that the proposed dataset quantization method is able to generate compact datasets that can be used to train unseen models such as model families from ViT, ResNet and MobileNetV2, LlaMA, etc.

Specifically, for vision tasks, on CIFAR-10 and ImageNet-1K, only 60% of the data are used to train the models to achieve a comparable model performance as those trained with full datasets. for language tasks, on BBH and DROP benchmark, only 2% instruction data are needed to achieve comparable model performance as those trained with full datasets. We further verify that the model weights pre-trained on the quantized dataset can be generalized into downstream tasks such as object detection and seg-mentation. As shown in Fig. [6,](#page-8-3) the ResNet-50 [\[23\]](#page-9-0) model pre-trained on 60% ImageNet also achieves negligible performance drop when finetuned on COCO [\[35\]](#page-9-14) (39.0% vs 39.2%) and ADE20K [\[66\]](#page-11-3) (42.3% vs 42.5%).

Our main contributions are summarized as:

- We propose a new framework, Dataset Quantization (DQ), to compress datasets into a small compact one that can be used for training unseen network architectures with state-of-the-art compression performance.
- We propose a scalable and efficient dataset compression algorithm that can be used for large dataset such as ImageNet-1K. With Dataset Quantization, we are able to remove 40% data from ImageNet-1K dataset and 80% data from the Alpaca instruction dataset with no training performance loss.
- We verify that the models trained with a compressed dataset can be used for downstream tasks. The models pre-trained with 60% of data on ImageNet-1K achieve no performance on COCO for object detection and ADE20K for segmentation.

# <span id="page-2-0"></span>2. Related work

In this section, we review two representative related methods: dataset distillation and coreset selection. We also introduce limitations and analysis of these two kinds of methods.

#### 2.1. Dataset distillation

Dataset distillation (DD) [\[55\]](#page-10-12) is the first method that proposes to synthesize a small amount of informative samples from a large dataset. Specifically, it optimizes the synthetic samples by minimizing the loss on the original training samples of the models trained on the synthetic ones. Afterwards, a series of techniques have been proposed such as Dataset condensation (DC) [\[65\]](#page-11-1), DSA [\[62\]](#page-10-3) and IDC [\[32\]](#page-9-15). These methods propose to match the loss gradient calculated from the original and synthetic data. CAFE [\[54\]](#page-10-5) and DM [\[63\]](#page-10-4) introduce a feature distribution matching strategy to reduce the potential bias from large-gradient samples. A recent work [\[5\]](#page-8-1) tries to minimize the difference of training trajectories between original and synthetic samples.

#### <span id="page-2-1"></span>2.2. Coreset selection

Coreset selection has been actively explored for compressing datasets, which aims to select a subset of the most representative samples out of the target dataset. The previous methods have proposed different selection criteria: geometry-based  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$  $[9, 2, 45, 47]$ , uncertainty-based  $[11]$ , error-based [\[51,](#page-10-15) [42\]](#page-10-16), decision-boundary-based [\[19,](#page-9-17) [39\]](#page-10-17), gradient-matching [\[40,](#page-10-8) [29\]](#page-9-18), bilevel optimization [\[30\]](#page-9-8) and submodularity-based methods [\[27\]](#page-9-9). Among them, the Contextual Diversity (CD) [\[2\]](#page-8-2), Herding [\[58\]](#page-10-18), and k-Center Greedy [\[45\]](#page-10-13) try to remove the redundant samples based on their similarity to the remaining samples. Cal [\[39\]](#page-10-17) and Deepfool [\[19\]](#page-9-17) argue that the coreset should be selected based on their difficulties for learning. Craig [\[40\]](#page-10-8) and GradMatch [\[29\]](#page-9-18) try to find an optimal coreset that has the similar gradient values with the whole dataset when training them on a network. Glister  $[30]$  introduce a validation set to maximize the log-likelihood with the whole dataset, where involves a time-consuming bilevel optimization. FL [\[27\]](#page-9-9) and Graph Cut (GC) [\[27\]](#page-9-9) consider the diversity and information simultaneously.

#### 2.3. Limitations and analysis

DD methods are hard to be applied on large datasets or architectures, such as ImageNet-1K or ResNet series, mainly due to the following limitations: (i) *Poor generalizability*. As shown in Fig. [2b,](#page-1-0) the synthesized images only work well on the same model architecture providing the optimization supervision, while fail training on other model architectures. (ii) *Poor scalability*. As the green line shows in Fig. [2a,](#page-1-0) they saturate fast as the data keep

<span id="page-3-5"></span><span id="page-3-2"></span>Table 1: Comparisons of the Dataset Distillation (DD), Coreset selection and our proposed Dataset Quantization (DQ). DQ combines the advantages of DD and coreset selection and is better at compressing datasets for training modern deep neural networks.

| Method  | Arch.<br>generalized | Scalable | Time<br>Efficient | Diverse | Data<br>Efficient |
|---------|----------------------|----------|-------------------|---------|-------------------|
| DD      | ❌                    | ❌        | ❌                 | ✔       | ✔                 |
| Coreset | ✔                    | ✔        | ✔                 | ❌       | ❌                 |
| DQ      | ✔                    | ✔        | ✔                 | ✔       | ✔                 |

ratio increases and can never reach the performance of the original datasets. (iii) *High computational cost* for large datasets. As shown in the mini table in Fig. [2a,](#page-1-0) compressing the whole ImageNet into 60% subset requires 28K GPU hours in total.

The above shortcomings are overcame by the coreset selection methods. However, the diversity of the coreset samples is not guaranteed under low data keep ratio, leading to worse performance than DD methods at low-data regime [\[65\]](#page-11-1), as shown in Fig. [2a](#page-1-0) and Fig. [4a.](#page-6-0) Tab. [1](#page-3-2) summarizes the differences among DD, coreset selection and DQ. Across all the five aspects, our proposed dataset quantization method consistently performs better.

# 3. Method

As mentioned in Sec. [2,](#page-2-0) the synthetic dataset based on DD methods performs poorly for training unseen network architectures as the matching metrics are coupled with the utilized network. We are thus motivated to explore a data selection strategy that is not sensitive to model architectures. In this section, we first introduce preliminaries about the coreset selection method and theoretically analyze its limitation. In particular, we choose the GraphCut based method [\[27\]](#page-9-9) as an example. Then, we present details of our proposed dataset quantization (DQ) method.

#### <span id="page-3-0"></span>3.1. Preliminary of coreset selection

Coreset-based algorithms [\[6,](#page-8-4) [46,](#page-10-11) [26\]](#page-9-19) address the limitations of DD methods. However, almost all coreset selection methods only select a single subset from the entire dataset in a one-stop manner. We empirically observe that it inevitably introduces severe *selection bias*—the samples lying in the high-density regions of the dataset distribution are more often selected than others—and yields selection results with limited variety. We provide more detailed theoretical analysis for the observation.

Theoretical Analysis for Coreset Selection. As mentioned in Sec. [2.2,](#page-2-1) almost all coreset selection methods utilize a heuristic metric to select samples, which is hard to avoid selecting some samples that have similar performances under the heuristic metric. GraphCut [\[27\]](#page-9-9), a recent state-of-the-art method, we choose it as an example to analyze the coreset selection process.  $\mathbf{D} = \{(x_k, y_k)\}_{k=1}^M$ denotes  $M$  labeled samples. We default to select  $K$  samples from D to form a coreset. The coreset is initialized as  $S_1^1 \leftarrow \emptyset$  and updated as  $S_1^k \leftarrow S_1^{k-1} \cup x_k$ . Note that,  $\mathbf{S}_n$  denotes  $n-$ th bin,  $\mathbf{S}_n^k$  represents first  $k$  samples of  $n-$ th bin and  $x_k$  is the  $k$ −th selected sample. We define the feature extractor as  $f(\cdot)$ . In GraphCut, samples are selected via maximizing submodular gains [\[27\]](#page-9-9)  $P(x_k)$  in feature space, which is defined as follows,

<span id="page-3-1"></span>
$$
P(x_k) = \sum_{p \in \mathbf{S}_1^{k-1}} \underbrace{||f(p) - f(x_k)||_2^2}_{C_1(x_k)} - \sum_{p \in \mathbf{D} \setminus \mathbf{S}_1^{k-1}} \underbrace{||f(p) - f(x_k)||_2^2}_{C_2(x_k)},
$$
(1)

where  $S_1^{k-1}$  denotes the set of selected samples and  $D\backslash S_1^{k-1}$  represents the remained set. GraphCut aims to maximize  $P(x_k)$ : it expects to maximize the diversity between  $x_k$  and the selected set while minimizes the distance between  $x_k$  and the remained set. Thereby  $S_1$  is expected to be a coreset covering the original distribution while maintaining largest diversity. However, as  $K \ll M$ , the sum value of  $C_1(x_k)$  is far smaller than  $C_2(x_k)$ . The distance between  $x_k$  and the remained set takes the dominant position in the gain calculation. Thus the diversity of selected K samples is not guaranteed as expected. Especially when the data keep ratio is low.

Mathmatically, supposing the average feature is at the origin, we define the maximum radius of set  $S_1^{k-1}$  as  $\mathbf{R}_1^{k-1} = \max_{p \in \mathbf{S}_1^{k-1}} ||f(p)||_2$ , we prove the continuous solution of the next selected sample  $x_k$  needs to satisfy

<span id="page-3-3"></span>
$$
||f(x_k)||_2^2 \le (\frac{2k}{M - 2k})^2 (\mathbf{R}_1^{k-1})^2. \tag{2}
$$

As  $M \gg k$ , the exact solution of  $f(x_k)$  is within  $(\mathbf{R}_1^{k-1})^2$ or as an outlier point that is as close as possible to the boundary of ball  $\mathbf{R}_1^{k-1}$ . The theoretical analysis well aligns the visualization in Fig. [2b.](#page-1-0) The diversity of selected samples is hard to be guaranteed for coreset selection. We provide more detailed proof in Appendix.

From the above analysis, the main reason of poor coreset diversity of GraphCut is  $M \gg k$  (*i.e.* over-large denominator) in Eq. [\(2\)](#page-3-3). There naturally rises an idea of recursively selecting from D for several times. Assume that we select  $S_2$  from dataset  $D\backslash S_1$  again. The maximum radius of set  ${\bf S_2^{k-1}}$  can be denoted as  ${\bf R}_2^{k-1} = \max_{p \in {\bf S}_2^{k-1}} ||f(p)||_2$ . As the most compact subset has been selected in  $S_1, R_2^{k-1}$  is obviously larger than  $\mathbb{R}_1^{k-1}$ . On the other hand, in the second selection round, the denominator in Eq. [\(2\)](#page-3-3) is reduced from  $(M - 2k)$  to  $(M - K - 2k)$ . Therefore, the diversity of selected samples in the second round would be enhanced, according to the following equation.

<span id="page-3-4"></span>
$$
(\frac{2k}{M-2k})^2(\mathbf{R}_1^{k-1})^2 \le (\frac{2k}{M-K-2k})^2(\mathbf{R}_2^{k-1})^2. \tag{3}
$$

<span id="page-4-1"></span><span id="page-4-0"></span>Image /page/4/Figure/0 description: This diagram illustrates a process involving data selection and image processing. On the left, a green cylinder labeled 'Data (D)' is shown, with an arrow pointing to a vertical axis labeled 'Selection index n'. This axis leads to a series of steps. First, the data D is processed to create subsets S1, Sn, and SN, indicated by 'Select' operations on scatter plots showing 'Original' (blue dots) and 'Selected' (red dots). Each subset is then passed through a 'sampling function' (labeled 'g'). The sampled data is then subjected to three sequential processes: 'Patchify', 'Scoring', and 'Dropping'. The 'Patchify' step divides the input images into a grid of patches. The 'Scoring' step applies a heatmap-like overlay to these patches, indicating some form of evaluation or importance. Finally, the 'Dropping' step shows a subset of these patches, with some appearing in blue (possibly indicating removal or non-selection) and others retaining their original image content.

Figure 3: An overview of the proposed DQ framework. We first divide the whole dataset  **into**  $N$  **non-overlapping bins**  $S_n$ . Then, the  $S^*$  is aggregated from N bins by a sampling function. After that, to further reduce the redundancy from each image, DQ drops a fraction of patches with the lowest information and reconstruct samples at the training stage via MAE.

Based on Eq.  $(3)$ , the twice selection can be easily extended into recursive selection, so the dataset is divided into several bins with different diversity levels. The visualizations of recursive selection are shown in the center of Fig. [3,](#page-4-0) which also aligns with our analysis well. We provide more visualizations in the Appendix.

### 3.2. Overview of DQ

Based on the above observation and analysis, we propose Dataset quantization (DQ), a novel framework to quantize large-scale datasets for lossless training, where data efficiency, scalability and computation cost are well considered. In this paper, we first divide the dataset into several non-overlapping bins by maximizing submodular gains  $[27]$ . Specifically, as shown in Fig. [3,](#page-4-0) given a dataset D, small informative bins are sampled from D *recursively* with a pre-defined bin size  $K$ , yielding a set of small bins  $[S_1, \ldots, S_n, \ldots, S_N]$  with  $N = M/K$ . Each bin  $S_n =$  $\{(x_j^{(n)}, y_j^{(n)})\}_{j=1}^K \subset \mathbf{D}$  is constrained under both interdata diversity and representativeness of the original feature distribution during the recursive selection. As analyzed in Sec. [3.1,](#page-3-0) the bins generated in early steps are mainly constrained by the distance to the remained set, while the latter bins are more constrained by the inter-data diversity. To better capture the distribution of the full dataset and balance the influence from the above two perspectives, we then integrate a coreset S<sup>\*</sup> for training from these bins via uniform sampling. Eventually, the redundant information is removed by dropping non-informative patches from the images to further reduce the storage burden.

Dataset bin generation Each bin is selected by maximizing the submodular gain  $[27]$  claimed in Eq. [\(1\)](#page-3-1). DQ recursively selects bins from  $D$ , where the selection of *i*-th sample in the  $n$ -th bin is formulated as follows,

$$
x_k \leftarrow \arg \max \left( \sum_{p \in \mathbf{S}_n^{k-1}} C_1(x_k) - \sum_{p \in \mathbf{D} \setminus \mathbf{S}_1 \cup \dots \cup \mathbf{S}_n^{k-1}} C_2(x_k) \right),\tag{4}
$$

where  $C_1(x_k)$  and  $C_2(x_k)$  have been defined in Eq. [\(1\)](#page-3-1),  $\mathbf{D}\backslash\mathbf{S}_1\cup\cdots\cup\mathbf{S}_n^{k-1}$  denotes the rest of the data in the dataset after selecting  $(k - 1)$  samples in *n*-th bin. We iteratively select the  $x$  with the largest submodular gain to form bin  $\mathbf{S}_n$ , as detailed in Algorithm [1.](#page-5-0) The generated bins contain different samples from each other, and each has an emphasis on trade-offs between representativeness and diversity.

Bin sampling After generating the dataset bins with various characteristics, to obtain diverse and informative subset, a sampler  $g(\cdot, \cdot)$  is used to sample a certain portion from each bin and form the final compact set. The process is formally defined as:

$$
\mathbf{S}^* = g(\mathbf{S}_1, \rho) \cup \cdots \cup g(\mathbf{S}_n, \rho) \cup \cdots \cup g(\mathbf{S}_N, \rho), \quad (5)
$$

where  $\rho$  denotes the data keep ratio. We set  $\mathbf{g}(\cdot,\cdot)$  as the uniform sampler by default.

Furthermore, we remove the redundant data within each sample by dividing them into patches. Motivated by the Masked Auto-Encoder (MAE) [\[21\]](#page-9-13), which recovers images with only some patches of them, we drop less important patches to reduce the number of pixels utilized for describ<span id="page-5-4"></span><span id="page-5-0"></span>Algorithm 1 Data bin generation.

**Input:** original dataset  $D$ , bin number N, bin size  $K$ , the score function  $P(\cdot)$ .

For  $n = 1, \ldots, N-1$  {Indices of sequentially selection}  $\mathbf{S}_n^1 \leftarrow \emptyset, \mathbf{S}_n^0 \leftarrow \emptyset$  {Initialization of  $\mathbf{S}_n$ }

For  $k = 1, \ldots, K$  {Find K most informative samples for  $\mathbf{S}_n$ }

For  $x_i \in D\backslash \mathbf{S}_n^k$ , calculate submodular gains  $P(x_i)$  using Eq. [1](#page-3-1)

 $x^* \leftarrow \arg \max_{x \in \mathbf{D} \setminus \mathcal{S}_n^k} P(x_i)$  $\mathbf{S}_n^k \leftarrow \mathbf{S}_n^{k-1} \cup x^*$ **Output:** N dataset bins  $S_1, \ldots S_n, \ldots S_N$ .

ing each image. We set  $\theta$  as the patch drop ratio and evaluate its sensitiveness in experiments section. When the data is required for training, the patches are passed through a strong pre-trained MAE decoder to reconstruct the images. The detailed patch dropping strategy is presented in the Appendix.

# 4. Experiments

### <span id="page-5-2"></span>4.1. Datasets and Implementation details

Datasets We mainly evaluate the proposed dataset quantization method on image classification datasets CIFAR-10 [\[34\]](#page-9-20) and ImageNet-1K [\[14\]](#page-9-21). CIFAR-10 consists of tiny colored natural images with the size of  $32\times32$  of 10 categories. In CIAFR-10, 50,000 images are used for training and 10,000 images for testing. ImageNet-1K includes 128,1126 images from 1000 categories for training and each category has 50 images for validation. Here, we report the results of the validation set. To better evaluate the transferability of the pre-trained weights on the compressed dataset from DQ, we also conduct experiments on downstreaming tasks including semantic segmentation and object detection on ADE20K  $[66]$  and COCO  $[35]$ . We report mAP and Seg-mAP on COCO. For segmentation experiments on ADE20K, we report mIoU and aACC.

For large language model (LLM) instruction fine-tuning, we use the alpaca dataset  $[50]$ , which consists of 52k instructions. The alpaca dataset is generated by the selfinstruct [\[57\]](#page-10-20) method. To evaluate the fine-tuned LLMs, we follow the benchmark proposed in  $[10]$ , which consists of MMLU  $[24]$ , BBH  $[49]$ , DROP  $[18]$ , and HumanEval  $[8]$ datasets.

Implementation details Following the previous works [\[31,](#page-9-6) [65\]](#page-11-1), we mainly use ResNet-18  $[23]$  as the model architecture for the ablation studies, unless specified otherwise. When verifying the generalization capability of the compressed dataset, we use ResNet-18 as the feature extractor during data compression and use the compressed dataset to train representative transformer and CNN architectures, including ViT [\[16\]](#page-9-2), Swin transformer [\[37\]](#page-10-9), ConvNeXt [\[38\]](#page-10-22) and MobilenetV2 [\[44\]](#page-10-23) models with their official training recipes. For experiments of bin generation, we use ResNet-18 and Vision Transformer (ViT-Base) models to extract features of CIFAR-10 and ImageNet-1K, respectively. The models are pre-trained on the corresponding full dataset with 10 epochs. The number of dataset bins  $N$  is set to 10 by default. And the drop ratio  $\theta$  is set to 25. We use pytorch-cifar<sup>[1](#page-5-1)</sup> and timm library [\[59\]](#page-10-24) for model training on CIFAR-10 and ImageNet-1K datasets. We train 200 epochs for CIFAR-10 with a batch size of 128 and a cosineannealed learning rate of 0.1. We train ImageNet in DDP manner with the default scripts of different architectures from timm. For downstream tasks, we follow the popular settings of mmdetection [\[7\]](#page-9-26) and mmsegmentation [\[12\]](#page-9-27) as used in  $[69, 68]$  $[69, 68]$  $[69, 68]$ . We choose distribution matching  $[63]$  and graph cut  $(GC)$  [\[27\]](#page-9-9) as two strong baselines, as well as other well-established dataset compression methods.

For LLM instruction tunning, we follow the training process of alpaca  $[50]$ . We fine-tune the LLaMA-7B  $[52]$ model on the sampled datasets with hyper-parameters introduced in [\[67\]](#page-11-5) for a smaller dataset. We use OpenAI's Embedding API [\[41\]](#page-10-26) as the feature extractor during data compression.

#### <span id="page-5-3"></span>4.2. Analysis

In this section, we investigate the effects of different components of DQ and provide apple-to-apple comparisons among DQ, DM [\[63\]](#page-10-4) and GC [\[27\]](#page-9-9).

Hyper-parameter analysis. There are two hyperparameters for DQ: the number of bins N and the drop ratio  $\theta$ . We run the experiments with four different values of the bin number: 1, 5, 10, and 20. As shown in Fig.  $4c$ , the performance drops significantly when the bin number is set to 1. This is the same case of coreset selection where the dataset distribution is not quantized. This gap comes from the fact that a one-time subset selection has limited diversity. When the number of bins is too large, our DQ degrades into random selection, so the performance is worse than our default setting.  $\theta$  is the patch drop ratio. With a fixed dataset bin number ( $N = 10$ ), we vary the drop ratio and the results are shown in Fig. [4d.](#page-6-0) It is observed that a large drop ratio improves the model training performance at large data keep ratio but the performance drops significantly at small data keep ratio. We empirically observe that the combination of  $N = 10$  and  $\theta = 25\%$  give the best trade-off.

Generalizability of the compressed datasets. We investigate the generalizability of the compressed datasets for training different architectures. Fig. [2c](#page-1-0) has demonstrated DQ can well preserve the dataset distribution for various architectures. We further look into the impact on the quantitative performance. We use DQ and DM to compress the

<span id="page-5-1"></span><sup>1</sup>https://github.com/kuangliu/pytorch-cifar

<span id="page-6-4"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays four plots related to data selection strategies. Plot (a) "Low Ratio" and plot (b) "High Ratio" are line graphs showing accuracy (%) against data keep ratio ρ. Both plots include lines for DM\_Random\_Init, DM\_Real\_Init, GC, Random Selection, and DQ, with a dashed line representing the Upper Bond. Plot (c) "Ablation on N" is a bar chart comparing accuracy (%) for different values of N (N=20, N=10, N=5, N=1) at various data keep ratios. Plot (d) "Ablation on θ (%)" is a line graph showing accuracy (%) against data keep ratio ρ for different values of θ (θ=0, θ=25, θ=50, θ=75), also including an Upper Bond line.

Figure 4: Testing performance of DM  $[63]$ , random selection, GC  $[27]$  and DQ on CIFAR-10 at (a) low and (b) high data keep ratio; and sensitiveness of DQ performance w.r.t. (c) the bin number N and (d) patch drop ratio  $\theta$  across varying data keep ratios. All results are averaged over three runs. The x-axes represent the data keep ratio.

<span id="page-6-1"></span>Table 2: Comparisons of cross-architecture generalization of DM and DQ on CIFAR-10. The R18 (first column) is the source architecture used to obtain distilled data or S<sup>\*</sup>. All architectures are trained from scratch. The top-1 accuracy is reported. CNext stands for the ConvNext architecture.

(a) DM on CIFAR-10.

(b) DQ on CIFAR-10.

| ρ (%) | R18  | R50  | ViT  | Swin | CNext | Avg. | ρ (%) | R18  | R50  | ViT  | Swin | CNext | Avg.         |
|-------|------|------|------|------|-------|------|-------|------|------|------|------|-------|--------------|
| 10    | 74.0 | 35.0 | 21.6 | 25.1 | 41.8  | 39.5 | 10    | 84.1 | 82.7 | 58.4 | 69.2 | 52.8  | 69.4 (+29.9) |
| 20    | 82.2 | 36.2 | 25.5 | 30.1 | 48.3  | 44.5 | 20    | 87.6 | 88.1 | 66.8 | 79.1 | 61.8  | 76.7 (+32.2) |
| 30    | 82.8 | 43.9 | 23.1 | 27.3 | 47.9  | 45   | 30    | 91.0 | 90.8 | 72.0 | 84.4 | 64.2  | 80.5 (+35.5) |
| 100   | 95.6 | 95.5 | 80.2 | 90.3 | 73.0  | 86.9 | 100   | 95.6 | 95.5 | 80.2 | 90.3 | 73.0  | 86.9         |

<span id="page-6-2"></span>Table 3: Evaluation of dropping patches randomly and ours with the drop ratio  $\theta = 25\%$ . Bold entries are best results.

| $\rho (%)$      | 1    | 3    | 5    | 10   | 30   | 50   |
|-----------------|------|------|------|------|------|------|
| Random Acc. (%) | 41.5 | 69.2 | 77.1 | 83.6 | 90.2 | 93.2 |
| Ours Acc. (%)   | 42.3 | 70.4 | 77.8 | 84.0 | 90.6 | 93.5 |

Table 4: Evaluation of the GPU hours of DM and DQ. We assign 0 to values that are negligible.

|    | $\rho$ (%)   Bin creation   10 20 40 60 Total |                                                       |                     |  |
|----|-----------------------------------------------|-------------------------------------------------------|---------------------|--|
| DM |                                               | $\begin{vmatrix} 7 & 14 & 29 & 41 & 91 \end{vmatrix}$ |                     |  |
| DO |                                               |                                                       | $0 \quad 0 \quad 0$ |  |

<span id="page-6-3"></span>Table 5: Impacts of DQ on instruction tuning with LLaMA-7B.

| \$\rho\$ (%) | BBH  | DROP | MMLU | Human-Eval | Avg. |
|--------------|------|------|------|------------|------|
| 2            | 32.9 | 27.6 | 36.6 | 8.5        | 26.3 |
| 20           | 32.7 | 26.7 | 39.8 | 9.2        | 27.1 |
| 100          | 32.9 | 26.3 | 41.6 | 10.0       | 27.7 |

dataset by 90%, 80% and 70% respectively, and use the gen-

erated dataset to train the selected models as detailed in Sec. [4.1.](#page-5-2) The results are shown in Tab. [2.](#page-6-1) As observed, under all data keep ratios, the dataset generated by DM suffers a significant performance drop when trained on unseen architectures. The drop is relatively small on CNN models and larger on transformer-based models. When used for training the ViT and Swin models, the performance drops by up to 70 percentage with DM generated dataset. In contrast, the datasets compressed by DQ offer better performance. Tab. [2b](#page-6-1) shows the average benefits of DQ relative to DM in the final column. In average, DQ performs better than DM by a range of 29.9% to 35.5% under different data keep ratios. It validates that the compression process of DQ is modelagnostic, indicating better generalizability.

Compression scalability. We investigate how the performance of different compression methods changes under different data keep ratio. We use DQ, DM and GC to compress the CIFAR-10 dataset to the same ratio and then use the compressed dataset to train ResNet-18 from scratch. The results under low and high ratios are shown in Fig. [4a](#page-6-0) and [4b,](#page-6-0) respectively. It is clearly observed that when the data keep ratio ratio is extremely low (e.g. 1%), the coreset based algorithm GC gives the lowest accuracy. Under high data keep ratio, the dataset distillation-based method DM saturates quickly and the final accuracy is 5% lower than the random sampling. Under both cases, DQ achieves the <span id="page-7-0"></span>highest accuracy when used for model training, demonstrating outperforming scalability.

Impact of the image patch attention. As mentioned above, we calculate a patch importance score to drop less important patches to decrease the redundancy of the original dataset. Randomly removing these patches is a simple and basic approach. We compare the efficacy of randomly dropping patches versus using GradCAM-based drops. As illustrated in Tab. [3,](#page-6-2) our method outperforms the random strategy for all data retention ratios. More details are provided in the Appendix.

Computational cost analysis. Due to the synthesizing strategy used in DM, a large tensor (*i.e.* initialization of synthetic images) needs to be defined. As a result, both the computational cost and memory consumption increase linearly to the size of the dataset. We directly measure the GPU hours needed for synthesizing the dataset and the results are shown in Tab. [3.](#page-6-2) Once the data keep ratio changes, the whole process of DM needs to be repeated. In contrast, DQ only needs to quantize the whole dataset into several bins. The following sampling step takes negligible GPU computations (N.A. in Tab. [3\)](#page-6-2).

#### 4.3. Comparison with state-of-the-art methods

We compare our method to previous state-of-the-art methods on both CIFAR-10 and ImageNet. We compare our proposed DQ with 3 dataset distillation and 14 coreset selection methods. The results are shown in Fig. [5.](#page-8-5) We would like to highlight that the results of dataset distillation methods are only shown on CIFAR-10 dataset. Due to the extremely large computational cost, it is not feasible to verify those methods on the ImageNet-1K dataset intuitively. The computational cost of dataset distillation methods can be checked from Fig. [2a.](#page-1-0) To better understand the characteristics of DQ, we also scope the performance comparisons under low data keep ratios. DQ outperforms other coreset selection and dataset distillation methods by a large margin, which indicates that DO provides stronger data efficiency under the same data keep ratio. Our method is based on GC algorithm, while outperforms GC by a large margin on all data keep ratios. On both CIFAR-10 and ImageNet-1K, we obtain lossless results when using only 60% data, setting a new state-of-art for dataset compression. Actually, DQ works as a play-and-plug module that could be combined with most coreset selection methods.

### 4.4. Performance on language tasks

To evaluate the effectiveness of DQ on language tasks, we choose four popular benchmarks of BBH, DROP, MMLU, and Human-Eval, following Alpaca. The results are shown in Tab. [5.](#page-6-3) As observed, with only 20% of the instruction tuning data extracted with DQ, comparable performance can be achieved as the model is finetuned with full data.

### 4.5. Performance on downstream tasks

To further evaluate the data efficiency of DQ on downstream tasks, we finetune the pretrained models with different data keep ratios (from 20% to 80%) on COCO and ADE20K datasets. Here, we make a comparison with random selection strategy. As shown in Fig. [6,](#page-8-3) our proposed DQ achieves comparable mAP and mIOU results as training on full data when the data keep ratio is 60%. Setting the data keep ratio as 80% can achieve lossless results, which indicates the samples selected by DQ are informative. From 20% to 40% data keep ratio, DQ achieve obvious higher results than random selection strategy. We would like to highlight that this is not feasible for DM or other dataset distillation methods due to the unaffordable computation cost to compress ImageNet and obtain the pre-trained model as mentioned in Sec. [4.2.](#page-5-3)

### 4.6. Robustness Evaluation

In order to investigate the robustness of our proposed DQ, we compared its performance with that of GC and Random selection by evaluating their performance on the corrupted dataset of CIFAR10 with the same methods used in  $[25, 69]$  $[25, 69]$  $[25, 69]$ . The results, depicted in Figure [6c,](#page-8-3) demonstrate that our proposed DQ method achieves superior results at all data retention ratios. The performance gap between GC and random selection narrows as the data retention ratio increases, which can be attributed to the fact that the samples selected by GC lack diversity. More detailed results can be found in Appendix.

# 5. Conclusion

We present a new dataset compression pipeline, termed dataset quantization (DQ), that is able to achieve lossless compression and be used to train unseen network architectures. We conduct extensive experiments showing that DQ achieves new state-of-the-art compression ratios. For the first time, we verify that models pre-trained with the compressed dataset can be used for training downstream tasks such as object detection and semantic segmentation. We hope this work could motivate more research works toward more generalizable dataset compression algorithms.

Limitations and future works Our DQ needs to select samples recursively from the whole dataset, resulting in extra computational efforts. In the future, we aim to design a more advanced DQ that only selects once from the whole dataset. Meanwhile, we plan to explore DQ on other tasks, such as video understanding  $[48]$ , AIGC  $[3]$ , and so on.

<span id="page-8-7"></span><span id="page-8-5"></span>Image /page/8/Figure/0 description: The image displays two plots, (a) and (b), comparing the accuracy of different dataset distillation and coreset selection methods against the data keep ratio \$\rho\$. Both plots show multiple lines, each representing a different method, with accuracy on the y-axis and data keep ratio on the x-axis. Plot (a) covers a data keep ratio from 0 to 60, while plot (b) covers a data keep ratio from 0 to 40. Both plots include an inset showing a "Low ratio scope" with a data keep ratio from 0 to 5. The legend in plot (a) lists various methods including DQ, Upper\_bond, DM\_Real\_Init, DM\_Random\_Init, DC, Random, CD, Herding, k\_Center\_Greedy, Least\_Confidence, Entropy, Margin, GradNd, Cal, Craig, GradMatch, Glister, FL, and GC. The "Dataset distillation" and "Coreset selection" labels are also present, indicating categories of methods. The "Upper\_bond" is represented by a dashed red line, indicating a maximum achievable accuracy.

Figure 5: Comparison of DQ with previous state-of-the-arts with different data keep ratios on (a) CIFAR-10 and (b) ImageNet-1K.

<span id="page-8-3"></span>Image /page/8/Figure/2 description: This image displays three line graphs labeled (a), (b), and (c), comparing different data sampling strategies. Graph (a) shows mAP (%) on the y-axis against Data keep ratio ρ on the x-axis, with two lines: DQ (red diamonds) and Random (blue circles). A dashed red line indicates 'Full data'. The table shows Seg\_mAP (%) values for different ρ values, with DQ and Random achieving 35.4% at full data. Graph (b) shows mIoU (%) on the y-axis against Data keep ratio ρ on the x-axis, with two lines: DQ (yellow diamonds) and Random (green crosses). A dashed red line indicates 'Full data'. The table shows aAcc (%) values for different ρ values, with DQ and Random achieving 80.3% and 80.4% respectively at full data. Graph (c) shows Acc. (%) on the y-axis against Data keep ratio ρ on the x-axis, with four lines: DQ (red diamonds), Random (blue circles), GC (green crosses), and DM (yellow triangles). The data points for DQ are approximately 50, 53, 56, 63 at ρ values of 2, 4, 6, 8 respectively. Random points are approximately 47, 49, 50, 52. GC points are approximately 49, 52, 54, 56. DM points are approximately 50, 52, 53, 54.

Figure 6: Comparisons of the performances for downstream tasks on (a) COCO and (b) ADE20K. (c) Comparisons of the robustness of trained models via random selection, DM [\[63\]](#page-10-4), GC [\[27\]](#page-9-9), and DQ on CIFAR10-C dataset. We report average performances of 15 types of corruption on 5 levels. More detailed results can be found in Appendix.

# Acknowledgement

This research is supported by the National Research Foundation, Singapore under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08- 008). Yang You's research group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant and Alibaba grant.

*Special Acknowledgement*. We would like to thank Zangwei Zheng for his help on the implementation of DQ in language tasks and Ge Yan for his advice on the mathematical proof of the submodular part.

# **References**

<span id="page-8-8"></span>[1] C Aditya, S Anirban, D Abhishek, and H Prantik. Gradcam++: Improved visual explanations for deep convolutional networks. *arXiv preprint arXiv:1710.11063*, 2017. [14](#page-13-0)

- <span id="page-8-2"></span>[2] Sharat Agarwal, Himanshu Arora, Saket Anand, and Chetan Arora. Contextual diversity for active learning. In *ECCV*, pages 137–153. Springer, 2020. [2,](#page-1-1) [3](#page-2-2)
- <span id="page-8-6"></span>[3] Max Bain, Arsha Nagrani, Gül Varol, and Andrew Zisserman. Frozen in time: A joint video and image encoder for end-to-end retrieval. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 1728–1738, 2021. [8](#page-7-0)
- <span id="page-8-0"></span>[4] Hangbo Bao, Li Dong, and Furu Wei. Beit: Bert pre-training of image transformers. *arXiv preprint arXiv:2106.08254*, 2021. [1](#page-0-0)
- <span id="page-8-1"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022. [1,](#page-0-0) [3](#page-2-2)
- <span id="page-8-4"></span>[6] Ke Chen. On coresets for k-median and k-means clustering in metric and euclidean spaces and their applications. *SIAM Journal on Computing*, 39(3):923–947, 2009. [4](#page-3-5)

- <span id="page-9-26"></span>[7] Kai Chen, Jiaqi Wang, Jiangmiao Pang, Yuhang Cao, Yu Xiong, Xiaoxiao Li, Shuyang Sun, Wansen Feng, Ziwei Liu, Jiarui Xu, et al. Mmdetection: Open mmlab detection toolbox and benchmark. *arXiv:1906.07155*, 2019. [6](#page-5-4)
- <span id="page-9-25"></span>[8] Mark Chen, Jerry Tworek, Heewoo Jun, Qiming Yuan, Henrique Ponde de Oliveira Pinto, Jared Kaplan, Harri Edwards, Yuri Burda, Nicholas Joseph, Greg Brockman, et al. Evaluating large language models trained on code. *arXiv preprint arXiv:2107.03374*, 2021. [6](#page-5-4)
- <span id="page-9-12"></span>[9] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *The Twenty-Sixth Conference Annual Conference on Uncertainty in Artificial Intelligence*, 2010. [2,](#page-1-1) [3](#page-2-2)
- <span id="page-9-22"></span>[10] Yew Ken Chia, Pengfei Hong, Lidong Bing, and Soujanya Poria. Instructeval: Towards holistic evaluation of instruction-tuned large language models. *arXiv preprint arXiv:2306.04757*, 2023. [6](#page-5-4)
- <span id="page-9-16"></span>[11] Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. In *ICLR*, 2019. [3](#page-2-2)
- <span id="page-9-27"></span>[12] MMSegmentation Contributors. MMSegmentation: Openmmlab semantic segmentation toolbox and benchmark. [https://github.com/open-mmlab/](https://github.com/open-mmlab/mmsegmentation) [mmsegmentation](https://github.com/open-mmlab/mmsegmentation), 2020. [6](#page-5-4)
- <span id="page-9-5"></span>[13] Jeff Dean. Introducing pathways: A nextgeneration ai architecture. *Google Blog*, 2021. [1](#page-0-0)
- <span id="page-9-21"></span>[14] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [6](#page-5-4)
- <span id="page-9-3"></span>[15] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-0)
- <span id="page-9-2"></span>[16] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1,](#page-0-0) [2,](#page-1-1) [6](#page-5-4)
- <span id="page-9-7"></span>[17] Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3749–3758, 2023. [1](#page-0-0)
- <span id="page-9-24"></span>[18] Dheeru Dua, Yizhong Wang, Pradeep Dasigi, Gabriel Stanovsky, Sameer Singh, and Matt Gardner. Drop: A reading comprehension benchmark requiring discrete reasoning over paragraphs. *arXiv preprint arXiv:1903.00161*, 2019. [6](#page-5-4)
- <span id="page-9-17"></span>[19] Melanie Ducoffe and Frederic Precioso. Adversarial active learning for deep networks: a margin based approach. *arXiv preprint arXiv:1802.09841*, 2018. [3](#page-2-2)
- <span id="page-9-11"></span>[20] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning. *arXiv preprint arXiv:2204.08499*, 2022. [2](#page-1-1)
- <span id="page-9-13"></span>[21] Kaiming He, Xinlei Chen, Saining Xie, Yanghao Li, Piotr Dollar, and Ross Girshick. Masked autoencoders are scalable ´

vision learners. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16000– 16009, 2022. [2,](#page-1-1) [3,](#page-2-2) [5,](#page-4-1) [14](#page-13-0)

- <span id="page-9-1"></span>[22] Kaiming He, Georgia Gkioxari, Piotr Dollár, and Ross Girshick. Mask r-cnn. In *Proceedings of the IEEE international conference on computer vision*, pages 2961–2969, 2017. [1](#page-0-0)
- <span id="page-9-0"></span>[23] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-2) [6,](#page-5-4) [14](#page-13-0)
- <span id="page-9-23"></span>[24] Dan Hendrycks, Collin Burns, Steven Basart, Andy Zou, Mantas Mazeika, Dawn Song, and Jacob Steinhardt. Measuring massive multitask language understanding. *arXiv preprint arXiv:2009.03300*, 2020. [6](#page-5-4)
- <span id="page-9-28"></span>[25] Dan Hendrycks and Thomas Dietterich. Benchmarking neural network robustness to common corruptions and perturbations. *Proceedings of the International Conference on Learning Representations*, 2019. [8](#page-7-0)
- <span id="page-9-19"></span>[26] Lingxiao Huang, K Sudhir, and Nisheeth Vishnoi. Coresets for regressions with panel data. *Advances in Neural Information Processing Systems*, 33:325–337, 2020. [4](#page-3-5)
- <span id="page-9-9"></span>[27] Rishabh Iyer, Ninad Khargoankar, Jeff Bilmes, and Himanshu Asanani. Submodular combinatorial information measures with applications in machine learning. In *Algorithmic Learning Theory*, pages 722–754. PMLR, 2021. [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-5) [5,](#page-4-1) [6,](#page-5-4) [7,](#page-6-4) [9,](#page-8-7) [13,](#page-12-0) [15](#page-14-0)
- <span id="page-9-10"></span>[28] Rishabh K Iyer and Jeff A Bilmes. Submodular optimization with submodular cover and submodular knapsack constraints. *Advances in neural information processing systems*, 26, 2013. [2](#page-1-1)
- <span id="page-9-18"></span>[29] Krishnateja Killamsetty, S Durga, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *ICML*, pages 5464–5474, 2021. [3](#page-2-2)
- <span id="page-9-8"></span>[30] Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Proceedings of the AAAI Conference on Artificial Intelligence*, 2021. [2,](#page-1-1) [3](#page-2-2)
- <span id="page-9-6"></span>[31] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. *arXiv preprint arXiv:2205.14959*, 2022. [1,](#page-0-0) [6](#page-5-4)
- <span id="page-9-15"></span>[32] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning (ICML)*, 2022. [3](#page-2-2)
- <span id="page-9-4"></span>[33] Alexander Kolesnikov, Lucas Beyer, Xiaohua Zhai, Joan Puigcerver, Jessica Yung, Sylvain Gelly, and Neil Houlsby. Big transfer (bit): General visual representation learning. In *European conference on computer vision*, pages 491–507. Springer, 2020. [1](#page-0-0)
- <span id="page-9-20"></span>[34] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [6](#page-5-4)
- <span id="page-9-14"></span>[35] Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence

Zitnick. Microsoft coco: Common objects in context. In *European conference on computer vision*, pages 740–755. Springer, 2014. [3,](#page-2-2) [6](#page-5-4)

- <span id="page-10-7"></span>[36] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *ICCV-2023*, 2023. [1](#page-0-0)
- <span id="page-10-9"></span>[37] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 10012–10022, 2021. [2,](#page-1-1) [6](#page-5-4)
- <span id="page-10-22"></span>[38] Zhuang Liu, Hanzi Mao, Chao-Yuan Wu, Christoph Feichtenhofer, Trevor Darrell, and Saining Xie. A convnet for the 2020s. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11976–11986, 2022. [6](#page-5-4)
- <span id="page-10-17"></span>[39] Katerina Margatina, Giorgos Vernikos, Loïc Barrault, and Nikolaos Aletras. Active learning by acquiring contrastive examples. *arXiv preprint arXiv:2109.03764*, 2021. [3](#page-2-2)
- <span id="page-10-8"></span>[40] Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *ICML*. PMLR, 2020. [2,](#page-1-1) [3](#page-2-2)
- <span id="page-10-26"></span>[41] Arvind Neelakantan, Tao Xu, Raul Puri, Alec Radford, Jesse Michael Han, Jerry Tworek, Qiming Yuan, Nikolas Tezak, Jong Wook Kim, Chris Hallacy, et al. Text and code embeddings by contrastive pre-training. *arXiv preprint arXiv:2201.10005*, 2022. [6](#page-5-4)
- <span id="page-10-16"></span>[42] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *arXiv preprint arXiv:2107.07075*, 2021. [3](#page-2-2)
- <span id="page-10-10"></span>[43] Ziheng Qin, Kai Wang, Zangwei Zheng, Jianyang Gu, Xiangyu Peng, Daquan Zhou, and Yang You. Infobatch: Lossless training speed up by unbiased dynamic data pruning. *arXiv preprint arXiv:2303.04947*, 2023. [2](#page-1-1)
- <span id="page-10-23"></span>[44] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018. [6](#page-5-4)
- <span id="page-10-13"></span>[45] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018. [3](#page-2-2)
- <span id="page-10-11"></span>[46] Jae-hun Shim, Kyeongbo Kong, and Suk-Ju Kang. Coreset sampling for efficient neural architecture search. *arXiv preprint arXiv:2107.06869*, 2021. [2,](#page-1-1) [4](#page-3-5)
- <span id="page-10-14"></span>[47] Samarth Sinha, Han Zhang, Anirudh Goyal, Yoshua Bengio, Hugo Larochelle, and Augustus Odena. Small-gan: Speeding up gan training using core-sets. In *ICML*. PMLR, 2020. [3](#page-2-2)
- <span id="page-10-27"></span>[48] Khurram Soomro, Amir Roshan Zamir, and Mubarak Shah. Ucf101: A dataset of 101 human actions classes from videos in the wild. *arXiv preprint arXiv:1212.0402*, 2012. [8](#page-7-0)
- <span id="page-10-21"></span>[49] Aarohi Srivastava, Abhinav Rastogi, Abhishek Rao, Abu Awal Md Shoeb, Abubakar Abid, Adam Fisch, Adam R Brown, Adam Santoro, Aditya Gupta, Adria Garriga- ` Alonso, et al. Beyond the imitation game: Quantifying

and extrapolating the capabilities of language models. *arXiv preprint arXiv:2206.04615*, 2022. [6](#page-5-4)

- <span id="page-10-19"></span>[50] Rohan Taori, Ishaan Gulrajani, Tianyi Zhang, Yann Dubois, Xuechen Li, Carlos Guestrin, Percy Liang, and Tatsunori B. Hashimoto. Stanford alpaca: An instruction-following llama model. [https://github.com/tatsu-lab/](https://github.com/tatsu-lab/stanford_alpaca) [stanford\\_alpaca](https://github.com/tatsu-lab/stanford_alpaca), 2023. [6](#page-5-4)
- <span id="page-10-15"></span>[51] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *ICLR*, 2018. [3](#page-2-2)
- <span id="page-10-25"></span>[52] Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, et al. Llama: Open and efficient foundation language models. *arXiv preprint arXiv:2302.13971*, 2023. [6](#page-5-4)
- <span id="page-10-6"></span>[53] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023. [1](#page-0-0)
- <span id="page-10-5"></span>[54] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [1,](#page-0-0) [3](#page-2-2)
- <span id="page-10-12"></span>[55] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [3](#page-2-2)
- <span id="page-10-1"></span>[56] Wenhui Wang, Hangbo Bao, Li Dong, Johan Bjorck, Zhiliang Peng, Qiang Liu, Kriti Aggarwal, Owais Khan Mohammed, Saksham Singhal, Subhojit Som, et al. Image as a foreign language: Beit pretraining for all vision and visionlanguage tasks. *arXiv preprint arXiv:2208.10442*, 2022. [1](#page-0-0)
- <span id="page-10-20"></span>[57] Yizhong Wang, Yeganeh Kordi, Swaroop Mishra, Alisa Liu, Noah A Smith, Daniel Khashabi, and Hannaneh Hajishirzi. Self-instruct: Aligning language model with self generated instructions. *arXiv preprint arXiv:2212.10560*, 2022. [6](#page-5-4)
- <span id="page-10-18"></span>[58] Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, pages 1121–1128, 2009. [3](#page-2-2)
- <span id="page-10-24"></span>[59] Ross Wightman. Pytorch image models. [https://github.com/rwightman/](https://github.com/rwightman/pytorch-image-models) [pytorch-image-models](https://github.com/rwightman/pytorch-image-models), 2019. [6](#page-5-4)
- <span id="page-10-2"></span>[60] Jiahui Yu, Zirui Wang, Vijay Vasudevan, Legg Yeung, Mojtaba Seyedhosseini, and Yonghui Wu. Coca: Contrastive captioners are image-text foundation models. *arXiv preprint arXiv:2205.01917*, 2022. [1](#page-0-0)
- <span id="page-10-0"></span>[61] Yifan Zhang, Daquan Zhou, Bryan Hooi, Kai Wang, and Jiashi Feng. Expanding small-scale datasets with guided imagination. *arXiv preprint arXiv:2211.13976*, 2022. [1](#page-0-0)
- <span id="page-10-3"></span>[62] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [1,](#page-0-0) [3](#page-2-2)
- <span id="page-10-4"></span>[63] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv*, 1(2):3, 2021. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-2) [6,](#page-5-4) [7,](#page-6-4) [9](#page-8-7)

- <span id="page-11-0"></span>[64] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-11-1"></span>[65] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021. [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-5) [6](#page-5-4)
- <span id="page-11-3"></span>[66] Bolei Zhou, Hang Zhao, Xavier Puig, Tete Xiao, Sanja Fidler, Adela Barriuso, and Antonio Torralba. Semantic understanding of scenes through the ade20k dataset. *International Journal of Computer Vision*, 127(3):302–321, 2019. [3,](#page-2-2) [6](#page-5-4)
- <span id="page-11-5"></span>[67] Chunting Zhou, Pengfei Liu, Puxin Xu, Srini Iyer, Jiao Sun, Yuning Mao, Xuezhe Ma, Avia Efrat, Ping Yu, Lili Yu, et al. Lima: Less is more for alignment. *arXiv preprint arXiv:2305.11206*, 2023. [6](#page-5-4)
- <span id="page-11-4"></span>[68] Daquan Zhou, Bingyi Kang, Xiaojie Jin, Linjie Yang, Xiaochen Lian, Zihang Jiang, Qibin Hou, and Jiashi Feng. Deepvit: Towards deeper vision transformer. *arXiv preprint arXiv:2103.11886*, 2021. [6](#page-5-4)
- <span id="page-11-2"></span>[69] Daquan Zhou, Zhiding Yu, Enze Xie, Chaowei Xiao, Animashree Anandkumar, Jiashi Feng, and Jose M Alvarez. Understanding the robustness in vision transformers. In *International Conference on Machine Learning*, pages 27378– 27394. PMLR, 2022. [2,](#page-1-1) [6,](#page-5-4) [8](#page-7-0)

# <span id="page-12-0"></span>6. Appendix

We present more explanations of the proposed dataset quantization, experiment results and visualizations in this section.

#### 6.1. Proof of Sec. 3.1

Given the whole dataset  $D$ ,  $|D| = M \gg 1$ .  $\forall p \in D$ ,  $f(p) \in \mathbb{R}^{m \times 1}$ . To make a simple proof, we assume  $\frac{1}{M} \sum_{p \in D} f(p) =$ 0.

For  $n = 0, 1, 2, \dots, n, \dots, N$ , define set  $\mathbf{S}_n \in \mathbf{D}$ . And, we define  $C_1(x)$  and  $C_2(x)$  as follows,

$$
C_1(x) = \sum_{p \in \mathbf{S}_1^n} ||f(p) - f(x)||_2^2; \qquad C_2(x) = \sum_{p \in \mathbf{D} \setminus \mathbf{S}_1^i} ||f(p) - f(x)||_2^2; \tag{6}
$$

By the policy of GraphCut [\[27\]](#page-9-9), it aims to maximize  $C_1(x)$  and minimize  $C_2(x)$  to select  $S_1$ . We write it into a united target function to choose  $x_{k+1}$  as,

$$
x_{k+1} \leftarrow \underset{x \in \mathbf{D} \backslash \mathbf{S}_1^k}{\arg \max} (C_1(x) - C_2(x)). \tag{7}
$$

We initialize  $S_1^1$  using  $\emptyset$  and  $S_1^{k+1} = S_1^k \cup x_{k+1}$ , where  $k = 1, 2, \dots, k, \dots, K$ , and  $|S_1^k| = k$ .

**Claim:** (a).  $S_1^1 = \emptyset$ ,  $x_1 = \arg \min_{x \in \mathbf{D}} ||x||_2^2$ , *i.e*, the closest point to **0** in **D** 

(b).  $x_{k+1}$  is very close to set  $S_1^k$ . **Proof:**  $S_1^1 = \emptyset$ , so  $C_1(x) = 0$ .

$$
C_2(x) = \sum_{p \in \mathbf{D}} ||f(p) - f(x)||_2^2 \tag{8}
$$

$$
= M||f(x)||_2^2 - 2(\sum_{p \in \mathbf{D}} f(p))^{\top} f(x) + \sum_{p \in \mathbf{D}} ||f(p)||_2^2
$$
\n(9)

$$
= M||f(x)||_2^2 + \sum_{p \in \mathbf{D}} ||f(p)||_2^2,
$$
\n(10)

where  $\sum_{p \in \mathbf{D}} f(p) = 0$ . Then, we have

$$
x_1 = \underset{x \in \mathbf{D}}{\arg \max} -C_2(x) \tag{11}
$$

$$
= \underset{x \in \mathbf{D}}{\arg \min} C_2(x) \tag{12}
$$

$$
= \underset{x \in \mathbf{D}}{\arg \min} \, M ||f(x)||_2^2,\tag{13}
$$

(b) Let  $C_1(x_k) - C_2(x_k) = 2C_1(x_k) - (C_2(x_k) + C_1(x_k))$ . We have:

$$
(C_2(x_k) + C_1(x_k)) = \sum_{p \in \mathbf{D} \backslash \mathbf{S}_1^k} ||f(p) - f(x_k)||_2^2 + \sum_{p \in \mathbf{S}_1^k} ||f(p) - f(x_k)||_2^2 \tag{14}
$$

$$
=\sum_{p\in D}||f(p)-f(x)||_2^2\tag{15}
$$

$$
= M||f(x)||_2^2 + \sum_{p \in \mathbf{D}} ||f(p)||_2^2 \tag{16}
$$

$$
= M||f(x)||_2^2 + Const.,
$$
\n(17)

where 'Const.' denotes constant number.

For  $C_1(x_k)$ , we have

$$
C_1(x_k) = \sum_{p \in \mathbf{S}_1^k} ||f(p) - f(x_k)||_2^2 = k||f(x_k)||_2^2 - 2(\sum_{p \in \mathbf{S}_1^k} f(p)^\top f(x_k) + \sum_{p \in \mathbf{S}_1^k} ||f(p)||_2^2
$$
(18)

<span id="page-13-0"></span>Define  $Q_k = \frac{1}{k} \sum_{p \in \mathbf{S}_1^k} f(p)$  as the weighted center of  $\mathbf{S}_1^k$ . Then, we can write the submodular gains function as follows,

$$
P(x_k) = 2C_1(x_k) - (C_2(x_k) + C_1(x_k))
$$
\n(19)

$$
= 2k||f(x_k)||_2^2 - 4kQ_k^{\top}f(x_k) - M||f(x_k)||_2^2 + Const.
$$
\n(20)

$$
= (2k - M) ||f(x_k) - \frac{2kQ_k}{2k - M}||_2^2 + Const.
$$
\n(21)

 $x_{k+1}$  is selected as follows,

<span id="page-13-1"></span>
$$
x_{k+1} = \underset{x \in \mathbf{D} \backslash \mathbf{S}_1^k}{\arg \max} P(x_k) = \underset{x \in \mathbf{D} \backslash \mathbf{S}_1^k}{\arg \max} ||f(x_k) - \frac{2kC_k}{2k - M}||_2^2.
$$
 (22)

Let  $\delta_k = \frac{2kC_k}{2k-M}$ . We define radius  $R_1^k$  of set  $\mathbf{S}_1^k$  as,

$$
R_1^k = \max_{p \in \mathbf{S}_1^k} ||f(p)||_2. \tag{23}
$$

Therefore,  $\forall p \in \mathbf{S}_1^k, ||f(p)||_2^2 \leq (R_1^k)^2$ , which means  $\mathbf{S}_1^k$  is included in a ball  $\mathbf{B}_k = \{p|||f(p)||_2^2 \leq (R_1^k)^2\}$ . Note that,

$$
||\delta_k||_2^2 = (2k/2k - M)^2 ||Q_k||_2^2
$$
\n(24)

$$
= \left(\frac{2k}{2k-M}\right)^2 ||\frac{1}{k}\sum_{p\in\mathbf{S}_1^k} f(p)||_2^2
$$
\n(25)

$$
\leq \left(\frac{2k}{2k-M}\right)^2 \frac{1}{k} \sum_{p \in \mathbf{S}_1^k} ||f(p)||_2^2 \tag{26}
$$

$$
\leq (\frac{2k}{2k-M})^2 (R_1^k)^2. \tag{27}
$$

 $M \gg k$ , so  $||\delta_k||_2^2 \leq (R_1^k)^2$  and  $\delta_k \in \mathbf{B}_k$  According to Eq. [22,](#page-13-1)  $x_{k+1} = \arg \min_{x \in \mathbf{D} \setminus \mathbf{S}_1^k} ||x - \delta_k||_2^2$  is the closest point in  $\mathbf{D}\backslash\mathbf{S}_1^k$  to  $\delta_k$ , which is in the ball  $\mathbf{B}_k$ . As  $M \gg 1$ ,  $f(x_{k+1})$  is very close to  $\mathbf{B}_k$ , and thus to  $\mathbf{S}_1^k$ .

By the proof, GraphCut cannot guarantee the samples diversity under small data keep ratio. Our DQ recursively select samples from  $D$ , as the total number of  $D$  reduces, the radius of the ball  $B_k$  will be extended. Therefore the sample diversity is higher than GraphCut method.

# 6.2. Details of Patch Dropping and Reconstruction

As pointed out in Masked Auto-Encoder (MAE) [\[21\]](#page-9-13), with a pre-trained decoder, some image patches can be dropped without affecting the reconstruction quality of the image. Motivated by it, we propose to reduce the number of pixels utilized for describing each image. Specifically, as shown in pipeline, given an image  $x$ , we first feed it into a pretrained feature extractor (ResNet-18 [\[23\]](#page-9-0)) to obtain the last feature map M and a prediction score  $y^c$  of the image class c. A group of attention scores is then calculated with the gradient values of each pixel in the last feature map following GradCAM++ [\[1\]](#page-8-8):

$$
a^{c} = \sum_{i,j} \left[ \frac{\frac{\partial^{2} y^{c}}{(\partial \mathcal{M}_{ij})^{2}}}{2 \frac{\partial^{2} y^{c}}{(\partial \mathcal{M}_{ij})^{2}} + \sum_{m,n} \mathcal{M}_{mn} \left\{ \frac{\partial^{3} y^{c}}{(\partial \mathcal{M}_{ij})^{3}} \right\}} \right] \text{ReLU} \left( \frac{\partial y^{c}}{\partial \mathcal{M}_{ij}} \right), \tag{28}
$$

where  $a^c$  is the attention scores for each pixel w.r.t. class  $c$ , ReLU is the Rectified Linear Unit activation function, and  $(i, j)$  and  $(m, n)$  are iterators over the feature map A. The pixel-wise attention score  $a^c$  is upsampled to fully cover the original input image. In order to integrate the attention information into image patches, we unify the attention scores of the corresponding pixels of a patch by their average value to generate the patch-wise importance scores  $p_c^c$  as follows,

$$
p_k^c = \frac{1}{hw} \sum_{i=h_k}^{h_k+h} \sum_{j=w_k}^{w_k+w} a^c(i,j),
$$
\n(29)

where  $h_k$  and  $w_k$  are the coordinates of the upper left corner of the patch k, and h and w are the height and width of image patches. According to the patch-wise attention scores, we drop a percentage of  $\theta$  non-informative patches with smallest attention scores to further save the storage cost. At the training stage, we employ a strong pre-trained MAE decoder to reconstruct the dropped patches and the original images.

<span id="page-14-1"></span><span id="page-14-0"></span>Image /page/14/Figure/0 description: The image displays five line graphs, labeled (a) Level 1 through (e) Level 5. Each graph plots accuracy (Acc. (%)) on the y-axis against the data keep ratio (ρ) on the x-axis, with values ranging from 2 to 8. Four lines are present in each graph, representing different methods: DQ (red diamonds), GC (green squares), Random (blue circles), and DM (yellow triangles). In Level 1, DQ starts at approximately 57% and rises to 75%, GC starts at 56% and rises to 67%, Random starts at 47% and rises to 66%, and DM starts at 56% and rises to 68%. In Level 2, DQ starts at 54% and rises to 70%, GC starts at 54% and rises to 67%, Random starts at 45% and rises to 58%, and DM starts at 54% and rises to 64%. In Level 3, DQ starts at 51% and rises to 59%, GC starts at 49% and rises to 56%, Random starts at 43% and rises to 56%, and DM starts at 49% and rises to 53%. In Level 4, DQ starts at 47% and rises to 56%, GC starts at 45% and rises to 53%, Random starts at 40% and rises to 56%, and DM starts at 45% and rises to 53%. In Level 5, DQ starts at 44% and rises to 52%, GC starts at 41% and rises to 47%, Random starts at 38% and rises to 51%, and DM starts at 42% and rises to 46%.

Figure 7: Comparisons of the robustness of trained models via DQ, GC and Random selection on CIFAR10-C dataset.

# 6.3. Robustness Evaluation

We show the overall robustness evaluation in our paper. Here, we report the detailed results at different corruption levels in Fig. [7.](#page-14-1) Our proposed DQ achieves state-of-the-art results in all cases.

#### 6.4. Differences between coreset selection and dataset quantization

Coreset VS DQ We here give more detailed explanations on the difference between the coreset selection methods and our proposed dataset quantization. As shown in Fig. [8,](#page-14-2) the coreset selection only select one subset from the full data distribution. This practice will suffer from a selection bias, resulting in selection results with limited diversity. Besides, when the the size of the selected subset is small, it will suffer a large selection variance. Differently, dataset quantization first divides the full distribution into non-overlapping bins and then sampling from each bin uniformaly. As a result, the sampled data could maximally preserve the original data distribution. To verify this, we use GraphCut [\[27\]](#page-9-9) as a representation of the coreset based method and 10% and 20% data from ImageNet dataset and compare the results with the data distribution sampled with dataset quantization. We use a pre-trained ResNet-18 model to extract the features of the data and then visualize the extracted data via t-SNE. The results are shown in Fig. [9.](#page-15-0) It is clearly observed that the data sampled via dataset quantization do capture a more diverse distribution.

<span id="page-14-2"></span>Image /page/14/Figure/6 description: The image illustrates two processes: Coreset Selection and Dataset Quantization. On the left, under the heading "Coreset Selection," a green cloud-like shape with a gradient from light green to dark green contains a light orange oval labeled "Coreset." On the right, under the heading "Dataset Quantization," a similar green cloud-like shape is divided into a grid of cells, each containing yellow oval shapes. An arrow points from this grid to a square labeled "Bins," which contains three yellow oval shapes. Another arrow points from "Bins" to a final yellow oval labeled "Coreset." Below the quantization process, a gradient bar indicates "Sample Density" from "Low" on the left to "High" on the right.

Figure 8: Differences between coreset selection methods and our dataset quantization.

Bin diversity of DQ To dig deeper for the reason why DQ can better preserved the data distribution. We use the same visualization method as aforementioned for the data contained within each bin. The results are shown in Fig. [10.](#page-15-1) Each bin contains 20% of the total data in the left column and 10% data in the right column. As shown, different bins are capturing different distributions. As a results, after sampling uniformly from each bin, the combined dataset enjoys a large diversity as well as representativeness over the whole data distribution.

<span id="page-15-0"></span>Image /page/15/Figure/0 description: This image displays four scatter plots arranged in a 2x2 grid. Each plot visualizes data points, colored blue for 'Original' and red for 'Selected'. The top row plots are labeled 'GC' and 'DQ' with a parameter \"ρ = 20%\". The bottom row plots are also labeled 'GC' and 'DQ' but with a parameter \"ρ = 10%\". The plots show distinct clusters of points, with the 'Selected' points interspersed within the 'Original' points in varying densities across the different plots.

Figure 9: Visualization of the feature distributions among data selected by GraphCut and SQ.

<span id="page-15-1"></span>Image /page/15/Figure/2 description: The image displays a collection of scatter plots, each visualizing data points colored blue for 'Original' and red for 'Selected'. The plots are arranged in two main sections, separated by a dashed vertical line. The left section contains five plots labeled B1, B2, B3, B4, and B5, followed by a plot labeled B\*. The right section contains ten plots labeled B1 through B10, also followed by a plot labeled B\*. Each plot shows a similar distribution of points, suggesting a comparison or analysis across different subsets or views of the data.

Figure 10: Visualization of the feature distributions among data selected in each bin and the final output of SQ on ImageNet dataset tench class. The bin number N and the data keep ratio  $\rho$  are set as (5, 20), (10, 10), respectively for the left and right column.

Cross-architecture generalization of DQ We further present more feature distribution visualizations with different net-work architectures on ImageNet-1K in Fig. [11.](#page-16-0) The samples are originally selected by ResNet-18 and reconstructed with MAE. Each set contains 10% of the total data. As shown, across all architectures, the generated compact set can effectively cover the whole data distribution, presenting significant cross-architecture generalization capability.

<span id="page-16-0"></span>Image /page/16/Figure/1 description: The image displays six scatter plots, arranged in two rows and three columns, each representing a different model: ViT-Base, ResNet18, ResNet50, Swin-Base, ConvNeXt-Base, and MobileNetV2. Each plot visualizes data points colored either blue for 'Original' or red for 'Selected', with a legend at the bottom indicating these color assignments. The plots show the distribution of these points in a 2D space, suggesting a comparison of original data versus selected data across different model architectures.

Figure 11: Cross-architecture visualization of the feature distributions among the dataset generated by DQ on ViT-Base on ImageNet dataset tench class.