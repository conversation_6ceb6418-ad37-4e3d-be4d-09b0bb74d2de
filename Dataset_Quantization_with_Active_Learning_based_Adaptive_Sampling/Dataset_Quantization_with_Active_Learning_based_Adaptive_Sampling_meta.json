{"table_of_contents": [{"title": "Dataset Quantization with Active Learning based\nAdaptive Sampling", "heading_level": null, "page_id": 0, "polygon": [[134.25, 115.5], [479.3203125, 115.5], [479.3203125, 146.4697265625], [134.25, 147.75]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 523.5], [228.75, 523.5], [228.75, 534.4453125], [133.5, 534.4453125]]}, {"title": "2 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 1, "polygon": [[133.5, 92.25], [223.5234375, 92.25], [223.5234375, 101.8037109375], [133.5, 101.8037109375]]}, {"title": "2 Related work", "heading_level": null, "page_id": 2, "polygon": [[133.5, 545.25], [234.0, 544.5], [234.0, 556.875], [133.5, 556.875]]}, {"title": "4 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 3, "polygon": [[133.5, 92.25], [223.224609375, 92.25], [223.224609375, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 4, "polygon": [[133.5, 117.0], [232.5, 117.0], [232.5, 128.1005859375], [133.5, 128.1005859375]]}, {"title": "3.1 Preliminaries", "heading_level": null, "page_id": 4, "polygon": [[133.5, 141.75], [228.005859375, 141.75], [228.005859375, 151.59375], [133.5, 151.59375]]}, {"title": "3.2 Observations", "heading_level": null, "page_id": 6, "polygon": [[133.5, 331.5], [225.75, 331.5], [225.75, 341.47265625], [133.5, 341.47265625]]}, {"title": "3.3 Active Learning based Adaptive Sampling", "heading_level": null, "page_id": 6, "polygon": [[133.5, 609.75], [372.041015625, 609.75], [372.041015625, 620.296875], [133.5, 620.296875]]}, {"title": "8 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 7, "polygon": [[133.5, 92.25], [223.0751953125, 92.25], [223.0751953125, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "3.4 Patchified-image-aware dataset quantization", "heading_level": null, "page_id": 9, "polygon": [[133.5, 476.25], [383.25, 476.25], [383.25, 486.4921875], [133.5, 486.4921875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 10, "polygon": [[133.5, 602.25], [229.5, 602.25], [229.5, 612.94921875], [133.5, 612.94921875]]}, {"title": "4.1 Datasets and Implementation Details", "heading_level": null, "page_id": 10, "polygon": [[133.5, 624.75], [349.330078125, 624.75], [349.330078125, 634.9921875], [133.5, 634.9921875]]}, {"title": "12 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 11, "polygon": [[133.5, 92.25], [222.75, 92.25], [222.75, 102.09375], [133.5, 102.09375]]}, {"title": "4.2 Comparison with State-of-the-art Methods", "heading_level": null, "page_id": 11, "polygon": [[133.5, 335.25], [375.92578125, 335.25], [375.92578125, 345.7265625], [133.5, 345.7265625]]}, {"title": "4.3 Ablation Study", "heading_level": null, "page_id": 11, "polygon": [[133.5, 614.8828125], [238.5, 614.8828125], [238.5, 624.9375], [133.5, 624.9375]]}, {"title": "4.4 Analysis", "heading_level": null, "page_id": 12, "polygon": [[133.5, 456.0], [204.0, 456.0], [204.0, 465.99609375], [133.5, 465.99609375]]}, {"title": "Analysis on class-wise sam-\nple counts and accuracy.", "heading_level": null, "page_id": 12, "polygon": [[133.5, 534.75], [267.0, 534.75], [267.0, 556.875], [133.5, 556.875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[133.5, 527.25], [219.75, 527.25], [219.75, 538.3125], [133.5, 538.3125]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[133.5, 117.0], [198.5712890625, 117.0], [198.5712890625, 127.9072265625], [133.5, 127.9072265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 41], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3870, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 77], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 44], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 42], ["Text", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 343], ["Line", 39], ["Text", 5], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 46], ["Text", 5], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["TableCell", 198], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 425], ["Line", 46], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["Line", 41], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Reference", 2], ["Caption", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 64], ["Text", 4], ["Figure", 2], ["Reference", 2], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1292, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 744], ["Span", 294], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 43], ["Text", 6], ["SectionHeader", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 59], ["Text", 7], ["TableCell", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 50], ["TableCell", 19], ["Text", 6], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 3366, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 50], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 42], ["ListItem", 21], ["Reference", 20], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Quantization_with_Active_Learning_based_Adaptive_Sampling"}