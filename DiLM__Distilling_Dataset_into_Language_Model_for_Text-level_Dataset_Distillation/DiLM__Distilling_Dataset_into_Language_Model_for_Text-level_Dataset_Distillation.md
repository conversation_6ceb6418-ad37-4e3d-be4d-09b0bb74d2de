# DiLM: Distilling Dataset into Language Model for Text-level Dataset Distillation

A<PERSON>

Tokyo Institute of Technology

[{maeka<PERSON>,](mailto:maeka<PERSON>@lr.pi.titech.ac.jp) [kosugi,](mailto:<EMAIL>) [funakoshi,](mailto:<EMAIL>) [oku}](mailto:<EMAIL>)@lr.pi.titech.ac.jp

## Abstract

Dataset distillation aims to compress a training dataset by creating a small number of informative synthetic samples such that neural networks trained on them perform as well as those trained on the original training dataset. Current text dataset distillation methods create each synthetic sample as a sequence of word embeddings instead of a text to apply gradient-based optimization; however, such embedding-level distilled datasets cannot be used for training other models whose word embedding weights are different from the model used for distillation. To address this issue, we propose a novel text dataset distillation approach, called *Distilling dataset into Language Model (DiLM)*, which trains a language model to generate informative synthetic training samples as text data, instead of directly optimizing synthetic samples. We evaluated <PERSON><PERSON> on various text classification datasets and showed that distilled synthetic datasets from DiLM outperform those from current coreset selection methods. DiLM achieved remarkable generalization performance in training different types of models and in-context learning of large language models. Our code will be available at <https://github.com/arumaekawa/DiLM>.

### 1 Introduction

The successful advancements in machine learning in a wide range of fields are due to the scaling-up of deep neural networks and large training datasets. In the natural language processing (NLP) field, large language models (LLMs), which are pre-trained with a huge amount of text, such as BERT- and GPT-family models [\(Devlin et al.,](#page-8-0) [2019;](#page-8-0) [Liu et al.,](#page-9-0) [2019;](#page-9-0) [Radford et al.,](#page-9-1) [2019;](#page-9-1) [Brown et al.,](#page-8-1) [2020\)](#page-8-1), have shown remarkable capabilities for various NLP tasks. However, training such large-scale models requires large computational resources and a long time, which makes it difficult to develop new LLMs, and even to fine-tune them.

To address this issue, dataset distillation [\(Wang](#page-10-0) [et al.,](#page-10-0) [2018b\)](#page-10-0) has attracted much attention in the machine learning community, which aims to reduce training costs by compressing training datasets. In contrast to traditional coreset selection approaches [\(Wolf,](#page-10-1) [2011;](#page-10-1) [Sener and Savarese,](#page-9-2) [2018;](#page-9-2) [Welling,](#page-10-2) [2009\)](#page-10-2), which heuristically select a small subset of representative training samples from the original dataset, dataset distillation creates more informative synthetic samples by distilling the knowledge from the original dataset. With this approach, synthetic samples are optimized with gradient descent according to objective functions for dataset distillation, including meta-learning [\(Wang et al.,](#page-10-0) [2018b\)](#page-10-0), gradient matching [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3), training trajectory matching [\(Cazenavette et al.,](#page-8-2) [2022\)](#page-8-2), and feature distribution matching [\(Wang et al.,](#page-10-4) [2022;](#page-10-4) [Zhao and Bilen,](#page-10-5) [2023\)](#page-10-5). The recent remarkable performance of dataset distillation, especially in the computer vision (CV) field, has also led to studies of its various applications, including neural architecture search [\(Such et al.,](#page-9-3) [2020;](#page-9-3) [Medvedev](#page-9-4) [and D'yakonov,](#page-9-4) [2021\)](#page-9-4), federated learning [\(Zhang](#page-10-6) [et al.,](#page-10-6) [2022a;](#page-10-6) [Xiong et al.,](#page-10-7) [2023\)](#page-10-7), continual learning [\(Wiewel and Yang,](#page-10-8) [2021;](#page-10-8) [Sangermano et al.,](#page-9-5) [2022\)](#page-9-5), and privacy preservation [\(Dong et al.,](#page-9-6) [2022;](#page-9-6) [Chen et al.,](#page-8-3) [2022\)](#page-8-3).

While most previous studies applied dataset distillation only to image classification datasets, some studies focused on text dataset distillation [\(Su](#page-9-7)[cholutsky and Schonlau,](#page-9-7) [2021;](#page-9-7) [Li and Li,](#page-9-8) [2021;](#page-9-8) [Maekawa et al.,](#page-9-9) [2023;](#page-9-9) [Sahni and Patel,](#page-9-10) [2023\)](#page-9-10). In contrast to the image, which can be applied gradient-based optimization by considering it as a pixel-wise continuous data, the discrete nature of text makes dataset distillation challenging [\(Geng](#page-9-11) [et al.,](#page-9-11) [2023;](#page-9-11) [Yu et al.,](#page-10-9) [2023\)](#page-10-9). To address this issue, all existing text dataset distillation methods used the widely used neural NLP technique called embedding, i.e., optimizing a synthetic dataset as continuous input word embeddings instead of dis-

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates a machine learning framework with two main branches: (a) Representative teacher and (b) Diverse mini-batch sampling. The original dataset, denoted as D\_real, is processed using K-centers to obtain representative samples. These samples are fed into a Learner (with parameters \(\\theta\)) which outputs predictions \(l\_{\\theta}(x\_i)\) that contribute to a \(L\_{real}\) loss. The second branch involves a Generator (with parameters \(\\(phi)\\\)) that produces synthetic samples \(\tilde{x}\_i\) through top-p sampling and clustering. These synthetic samples are also fed into a Learner (\(\\theta\)), producing outputs \(l\_{\\theta}(\tilde{x}\_i) \times a\_i\) which are normalized and contribute to a \(L\_{syn}\) loss. The overall objective is a Matching loss, \(L\_{GM} = D(\nabla\_{\\theta}L\_{real}, \nabla\_{\\theta}L\_{syn})\), which aims to match the gradients of the real and synthetic losses. The diagram also indicates forward (solid gray arrows) and backward (dashed red arrows) propagation paths for the gradients. The Generator's output probabilities \(p\_{\\(phi)}(\tilde{x}\_i)\) are used to update the Generator.

Figure 1: Overview of training with DiLM. Gradient matching loss is computed on the learner model between real samples from the original dataset and generated samples from the generator model. It is then back-propagated to the generator model via generation probabilities, which weight the learner loss for each generated sample. (a) Representative teacher for computing real sample's gradients, which improves the performance and accelerates convergence by using K-center samples, representing the original dataset, rather than randomly sampled ones. (b) Diverse mini-batch sampling, which enables the generator model to explore diverse synthetic samples in each training step.

crete text. However, such embedding-level distilled synthetic datasets cannot be used for training other models that have different word embedding weights, which is a crucial issue in terms of practical applications. Furthermore, distilled word embedding sequences are also completely unreadable to humans, which makes it difficult to interpret and analyze the original training dataset by observing distilled synthetic samples.

Motivated by these shortcomings, this paper explores the text dataset distillation to obtain distilled synthetic datasets at the text-level as the first study. We propose the first text-level dataset distillation approach called "Distilling dataset into Language Model (DiLM)". To overcome the optimization difficulty of discrete text, DiLM uses a language model as a surrogate continuous optimization target instead of directly optimizing a synthetic sample's text. Specifically, DiLM trains a language model to minimize the gradient matching loss [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3) of generated synthetic samples as a dataset distillation objective. To enable back-propagating the gradient matching loss to the language model, we design a differentiable backward pass via loss weighting with generation probabilities to bypass the non-differentiable generated text (Figure [1\)](#page-1-0).

In our experiments, we applied DiLM to distill three text classification datasets from the GLUE benchmark [\(Wang et al.,](#page-10-10) [2018a\)](#page-10-10), SST-2, QQP, and MNLI-m. The results indicate that the synthetic datasets distilled with DiLM outperformed representative real samples selected from the original

datasets with current coreset selection methods. Our distilled datasets also achieved remarkable generalization performance not only for training different types of pre-trained models but also for in-context learning of LLMs as few-shot prompts.

Our main contributions are as follows:

- To the best of our knowledge, this is the first study to distill a text dataset into a text-level synthetic dataset that are applicable for training models independent of word embedding weights.
- We present DiLM, which addresses the discreteness of text by using a language model as a surrogate optimization target and backpropagating the distillation loss to the model, bypassing non-differentiable generated text.
- Our experimental results indicate that DiLM outperformed the current coreset selection methods not only for training the same model used for distillation, but also for training different models independent of the word embedding weights, architectures, and training processes.

## 2 Related Work

## 2.1 Dataset Distillation

Dataset distillation was first proposed by [Wang](#page-10-0) [et al.](#page-10-0) [\(2018b\)](#page-10-0), motivated by theoretical interests as well as practical applications for reducing network training costs. Inspired by meta-learning based hyperparameter optimization [\(Maclaurin et al.,](#page-9-12) [2015\)](#page-9-12), [Wang et al.](#page-10-0) [\(2018b\)](#page-10-0) optimized a small synthetic dataset by gradient descent such that models trained on it have a lower training loss for the original dataset. Recently, several surrogate objectives have been proposed to improve the performance and efficiency of dataset distillation. DC [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3) and DSA [\(Zhao and Bilen,](#page-10-11) [2021\)](#page-10-11) focused on gradient matching between real and synthetic samples. DM [\(Zhao and Bilen,](#page-10-5) [2023\)](#page-10-5) and CAFE [\(Wang](#page-10-4) [et al.,](#page-10-4) [2022\)](#page-10-4) proposed feature distribution matching, which requires less GPU memory for optimizing synthetic datasets. MTT [\(Cazenavette et al.,](#page-8-2) [2022\)](#page-8-2) and TESLA [\(Cui et al.,](#page-8-4) [2023\)](#page-8-4) optimized synthetic samples to approximate trajectories of model parameters trained with real data. SLDD [\(Sucholut](#page-9-7)[sky and Schonlau,](#page-9-7) [2021\)](#page-9-7) and LDD [\(Bohdal et al.,](#page-8-5) [2020\)](#page-8-5) introduced learnable soft-labels, which are optimized together with input images to make each synthetic sample more informative.

While the most current research on dataset distillation involves only image classification datasets, some studies also focused on text classification datasets. [Sucholutsky and Schonlau](#page-9-7) [\(2021\)](#page-9-7) and [Li](#page-9-8) [and Li](#page-9-8) [\(2021\)](#page-9-8) applied the original meta-learning based method by [Wang et al.](#page-10-0) [\(2018b\)](#page-10-0) to text datasets. To overcome the discrete nature of text, which makes applying gradient-based methods difficult, they optimized synthetic samples in the pretrained GloVe word embedding space [\(Penning](#page-9-13)[ton et al.,](#page-9-13) [2014\)](#page-9-13) instead of actual words of text as the optimization target. [Maekawa et al.](#page-9-9) [\(2023\)](#page-9-9) extended the text dataset distillation to the pre-trained BERT model and improved its performance by introducing learnable attention labels, which directly guide the self-attention probabilities of the models. [Sahni and Patel](#page-9-10) [\(2023\)](#page-9-10) explored dataset distillation in multilingual text classification datasets in the context of fairness, interpretability, and crossarchitecture generalization. Although these methods perform well for text classification datasets, distilled synthetic datasets obtained with them cannot be used for training other models that have different word embedding weights. Although [Sucholutsky](#page-9-7) [and Schonlau](#page-9-7) [\(2021\)](#page-9-7) and [Sahni and Patel](#page-9-10) [\(2023\)](#page-9-10) transformed their distilled synthetic samples to text by finding a word that has the nearest neighbor embedding, the converted text consists of unrelated words and does not make sense, which makes it difficult to interpret and analyze them. Moreover, the performance of distilled datasets after being converted to text has also not been investigated.

#### <span id="page-2-0"></span>2.2 Generative Models

Recent studies on dataset distillation in the CV field used generative adversarial networks (GANs) [\(Goodfellow et al.,](#page-9-14) [2014\)](#page-9-14), i.e., training the model parameters and/or their latent input noises instead of synthetic images. These methods generalize distilled synthetic images to different model architectures by restricting them to the generative distribution learned from the original dataset. DiM [\(Wang et al.,](#page-10-12) [2023\)](#page-10-12) fine-tuned a GAN to generate informative synthetic images from randomly sampled latent noises, where distilled datasets of different sizes can be produced without retraining the model. GTNs [\(Such et al.,](#page-9-3) [2020\)](#page-9-3) trained a GAN to generate informative images, instead of realistic images, to accelerate neural architecture search. GTNs also learned a latent noise for each synthetic image as a curriculum of training learner networks. IT-GAN [\(Zhao and Bilen,](#page-10-13) [2022\)](#page-10-13) and GLaD [\(Cazenavette et al.,](#page-8-6) [2023\)](#page-8-6) used a pre-trained GAN as a generative prior of synthetic samples and only optimized the latent noises.

Inspired by these studies, we also introduce a generative model with a different motivation for text dataset distillation: to avoid the difficulties of directly optimizing discrete text, we instead optimize the continuous parameters of a generative model to generate distilled synthetic samples. However, since all previous studies that used generative models for image dataset distillation trained them and/or their input latent noises by back-propagating the distillation loss to them via generated images, none of them can be applied to text data, which are non-differentiable due to their discrete nature.

### 3 Methodology

In this section, we introduce DiLM, which distills text datasets into text data, not word embeddings, for the model-agnostic applicability and interpretability of the distilled synthetic datasets. The main idea of DiLM is to avoid the optimization difficulties of discrete text by instead training continuous parameters of a language model as a surrogate optimization target of dataset distillation.

### 3.1 Overview

Given a training dataset  $\mathcal{D}_{\text{real}} = \{x_i\}_{i=1}^{|\mathcal{D}_{\text{real}}|}$ , the goal of DiLM is to obtain a generator model, parameterized by  $\phi$ , that generates a distilled synthetic dataset  $\mathcal{D}_{syn} = \{ \tilde{x}_i \}_{i=1}^{|\mathcal{D}_{syn}|} (|\mathcal{D}_{syn}| \ll |\mathcal{D}_{real}|),$  such that a learner model, parameterized by  $\theta$ , trained

## Algorithm 1: Optimization for DiLM

- 1. We first simply train the generator model to generate synthetic training samples that belong to the same distribution as in the original dataset  $\mathcal{D}_{\text{real}}$  (Section [3.2\)](#page-3-0).
- 2. We then fine-tune the generator model to generate "informative" training samples by minimizing the gradient matching loss between generated and real samples (Section [3.3\)](#page-3-1).
- 3. We obtain distilled dataset  $\mathcal{D}_{syn}$  by generating synthetic samples with the generator model and selecting representative samples from them by using a clustering-based coreset selection method (Section [3.4\)](#page-5-0).

We describe the details of each step in the following sections.

## <span id="page-3-0"></span>3.2 Synthetic Training Data Generation with Language Model

Inspired by the remarkable text generation capability of pre-trained transformer language models [\(Radford et al.,](#page-9-1) [2019\)](#page-9-1), we use them as the generator model to generate synthetic training samples of sufficient quality to be used for training models. Before training the generator model to generate more informative synthetic samples than real samples in the original dataset, we first simply train a language model to generate training samples that belong to the same distribution as in the original training dataset for the initial parameters of the generator model.

When we target at text classification tasks, we need to control the generator model to generate samples for each specific class. Therefore, we introduce class-specific beginning-of-sentence tokens  $\langle$ bos\_i $>$ , which are added to the head of each training sample to train the generator model to generate samples of the corresponding class following it. For each training sample, an end-of-sentence token <eos> is also added, and the sample is fed to the generator model as follows:

### <bos\_i> *sentence of class* i <eos>.

To involve text classification tasks that specify the relation between two sentences, such as semantic similarity and natural language inference (NLI), we use a separate token <sep> to split two sentences as

### <bos\_i> *sentence 1* <sep> *sentence 2* <eos>.

The generator model is trained on them with the language modeling loss  $l_{\phi}(x_i)$  as

$$
l_{\phi}(x_i) = -\frac{1}{|x_i|} \sum_{w_t \in x_i} \log p_{\phi}(w_t | w_{< t}), \quad (1)
$$

where  $w_t$  is a token in  $x_i$  and  $|x_i|$  is the length of  $x_i$ . In this way, we pre-train the generator model parameters  $\phi$  to generate synthetic training data like real data, and use them as the initial parameter for training for gradient matching, described in the following section.

### <span id="page-3-1"></span>3.3 Training for Gradient Matching

In this section, we explain how to fine-tune the pretrained generator model, described in Section [3.2,](#page-3-0) to generate synthetic training samples that are more informative than real samples in the original dataset. Specifically, we describe gradient matching, which is an optimization objective for dataset distillation, and the model updating procedure to deal with the discreteness of text. We also introduce two techniques to improve DiLM: representative teacher and diverse mini-batch sampling.

Gradient Matching. To distill the knowledge of the original dataset  $\mathcal{D}_{\text{real}}$  into generated synthetic samples from the generator model, we optimize the gradient matching loss [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3) as the objective for dataset distillation. Given a minibatch of real samples  ${x_i}_{i=1}^M$  and a mini-batch of synthetic samples  $\{\tilde{x}_i\}_{i=1}^N$ , which is generated from the generator model, the gradient matching loss  $\mathcal{L}_{GM}$  on the learner model parameters  $\theta$  is calculated as

$$
\mathcal{L}_{GM} = D \left( \nabla_{\theta} \mathcal{L}_{real}, \nabla_{\theta} \mathcal{L}_{syn} \right) \quad \text{where}
$$
$$
\mathcal{L}_{real} = \frac{1}{M} \sum_{i=1}^{M} l_{\theta}(x_i), \quad \mathcal{L}_{syn} = \frac{1}{N} \sum_{i=1}^{N} l_{\theta}(\tilde{x}_i), \tag{2}
$$

where  $l_{\theta}(\cdot)$  is the loss function for learning tasks such as cross-entropy loss, and  $D(\cdot, \cdot)$  is the cosine similarity-based distance function, expressed as

<span id="page-3-3"></span><span id="page-3-2"></span>
$$
D(A, B) = 1 - \frac{A \cdot B}{\|A\| \|B\|}.
$$
 (3)

Following a previous study [\(Zhao et al.,](#page-10-3) [2021\)](#page-10-3), we separately calculate the gradient matching loss for

#### Algorithm 1: Optimization for DiLM

**Input** :  $\mathcal{D}_{real}$ : original dataset;  $\phi$ : generator model; θ: learner model; S: # of outer loop; T: # of inner loop;  $K:$  # of learner updating loop in each inner step; M: batch size of real data;  $N:$  batch size of synthetic data;  $\eta:$  learning rate of  $\theta$ ;  $\alpha$ : learning rate of  $\phi$ .

// Outer loop 1 **for**  $s = 1, ..., S$  **do** // Initialize learner 2 | Initialize  $\theta \sim p(\theta_0)$ // Inner loop  $\mathbf{3} \mid \text{for } t = 1, \ldots, T \text{ do}$ // Compute gradient matching loss for each class 4 **for**  $c = 1, ..., C$  do // Compute loss with real samples  $\mathfrak{s}\quad\Big|\quad\Big|\quad\setminus\{x_i^{(c)}\}_{i=1}^M\sim\mathcal{D}_{\text{real}}^{(c)}$ 6  $\Box$   $\Box$   $\mathcal{L}_{\text{real}}^{(c)} \leftarrow \frac{1}{M} \sum_{i=1}^{M} l_{\theta}(x_i^{(c)})$ // Compute loss with synthetic samples  $\tau \quad | \quad | \quad \mid \{\tilde{x}^{(c)}_i\}_{i=1}^N \sim p_\phi(\tilde{x})$ 8 **for**  $i = 1, ..., N$  do  $\quad \ \ \, \Theta \quad \quad \bigg| \quad \ \, \bigg| \quad \ \ \bigg| \quad \ \ a_i \leftarrow p_\phi(\tilde{x}_i^{(c)}) / \sum_{j=1}^N p_\phi(\tilde{x}_j^{(c)})$ 10  $\Box$   $\Box_{\text{syn}}^{(c)} \leftarrow \sum_{i=1}^{N} a_i l_{\theta}(\tilde{x}_i^{(c)})$ // Gradient matching loss (Eq. [\(3\)](#page-3-2))  $\begin{array}{c|c} \hline \text{11} & \end{array} \left| \begin{array}{c} \end{array} \right| & \left[ \begin{array}{c} \mathcal{L}_{\text{GM}}^{(c)} \leftarrow D(\nabla_{\theta} \mathcal{L}_{\text{real}}^{(c)}, \nabla_{\theta} \mathcal{L}_{\text{syn}}^{(c)}) \end{array} \right) \right| \nonumber$ // Update generator 12  $\phi \leftarrow \phi - \alpha \nabla_{\phi} \frac{1}{C} \sum_{c=1}^{C} \mathcal{L}_{GM}^{(c)}$ // Update learner for K steps 13 **for**  $k = 1, ..., K$  do 14 | |  $X_{\text{real}} \sim \mathcal{D}_{\text{real}}$ 15  $\left| \begin{array}{c} \end{array} \right|$   $\theta \leftarrow \theta - \eta \nabla_{\theta} \mathcal{L}_{\theta}(X_{\text{real}})$ **Output :** $\phi$ : parameters of generator model.

<span id="page-4-0"></span>each class and combine them to update the generator model parameters  $\phi$ . To consider the gradient on the learner model parameters  $\theta$  throughout the entire training process, the generator model is trained with the nested loop algorithm, including the outer loop, which initializes  $\theta$  at the beginning, and the inner loop, which updates  $\theta$  for K steps with real samples (see Algorithm [1\)](#page-4-0).

Generator Updating. As we described in Sec-tion [2.2,](#page-2-0) the gradient matching loss  $\mathcal{L}_{GM}$  cannot be directly back-propagated to the generator model parameters  $\phi$  via generated samples  $\{\tilde{x}_i\}_{i=1}^N$ , like the case with image datasets, because they consist of discrete text. Although some solutions to the discrete back-propagation issue in text generation have been explored in the NLP research field, most of standard approaches, including softargmax [\(Zhang et al.,](#page-10-14) [2017\)](#page-10-14) and policy gradient [\(Yu](#page-10-15) [et al.,](#page-10-15) [2017\)](#page-10-15), cannot be applied to this case (see details in Appendix [A\)](#page-11-0). To address this issue, we design an alternative backward pass, inspired by a previous study [\(Hiraoka et al.,](#page-9-15) [2020\)](#page-9-15), which optimizes a tokenization model for the downstream

task's loss through a non-differentiable procedure. When computing the generated sample's loss  $\mathcal{L}_{syn}$ , instead of simply averaging the losses for each generated sample as in Eq. [\(2\)](#page-3-3), we weight them with their generation probabilities  $p_{\phi}(\tilde{x}_i)$  as

$$
\mathcal{L}_{syn} = \sum_{i=1}^{N} a_i l_{\theta}(\tilde{x}_i), \tag{4}
$$

$$
a_i = \frac{p_{\phi}(\tilde{x}_i)}{\sum_{j=1}^{N} p_{\phi}(\tilde{x}_j)}.
$$
 (5)

Therefore,  $\mathcal{L}_{GM}$  can be back-propagated to  $\phi$ through the differentiable pass via loss weights  $a_i$ , as illustrated in Figure [1.](#page-1-0) Intuitively, the generator model is updated to increase its generation probabilities of synthetic samples that improve gradient similarity.

Representative Teacher. To improve DiLM, we consider enhancing the gradient teacher of real samples by using representative samples for each minibatch of real samples instead of randomly selected ones. Inspired by [Liu et al.](#page-9-16) [\(2023\)](#page-9-16), we select the representative samples with K-centers [\(Wolf,](#page-10-1) [2011;](#page-10-1) [Sener and Savarese,](#page-9-2) [2018\)](#page-9-2), a clustering-based coreset selection method (Figure [1a](#page-1-0)). Specifically, we divide all the real training samples for each class into  $M$  sub-clusters by using the K-means algorithm on the feature space of the learner model, and choose the center sample of each sub-cluster. As shown in [\(Liu et al.,](#page-9-16) [2023\)](#page-9-16), the representative samples selected by K-centers provide the proper teacher gradient by including diverse samples that cover the overall distribution for each class and eliminating samples near the decision boundaries, which have dominant gradients with large norms. Considering coverage and robustness, we generate 10 representative sample sets by running the Kmeans algorithm with different random seeds at the beginning of training and use one as a mini-batch of real samples in each training step.<sup>[1](#page-4-1)</sup>

Diverse Mini-batch Sampling. Diversity in a mini-batch of generated samples for each step affects the sample space that the generator model explores in training. If the generator model only generates many samples that are similar to each other, this leads to the biased optimization of the generator model. To address this issue, we intro-

<span id="page-4-1"></span><sup>&</sup>lt;sup>1</sup>[Liu et al.](#page-9-16) [\(2023\)](#page-9-16) repeatedly re-generated the K-center representative samples by conducting clustering on the feature space of the different learner model's states throughout the inner loop. However, it is very time consuming with BERT as the learner model, as in our study.

duce diverse mini-batch sampling of generated samples in the training process of DiLM (Figure [1b](#page-1-0)). Instead of generating  $N$  synthetic samples for each step, the generator model generates  $N \times I_{int}$  synthetic samples at the same time, where  $I_{int}$  is the generation interval. The generated synthetic samples are then divided into  $N$  sub-clusters with the K-means algorithm, and a mini-batch of synthetic samples for each step is constructed by randomly choosing one sample from each sub-cluster.

### <span id="page-5-0"></span>3.4 Generate Synthetic Dataset

We obtain distilled dataset  $\mathcal{D}_{syn}$  by generating synthetic samples with the trained generator model. To include representative samples of the model's generative distribution  $p_{\phi}(\tilde{x})$ , we use the coreset selection method again to select generated synthetic samples. Specifically, we generate 100 times as many synthetic samples as the distilled dataset size  $|\mathcal{D}_{syn}|$  by top-p sampling with  $p = 0.95$ , considering the diversity, and then construct  $\mathcal{D}_{syn}$  with K-center representative samples. This makes  $\mathcal{D}_{syn}$ to include diverse synthetic samples by removing redundant samples caused by the biased generative distribution of the model.

## 4 Experimental Settings

Datasets. We evaluated DiLM in distilling three major text classification datasets, SST-2, QQP, and MNLI-m, from the GLUE benchmark [\(Wang et al.,](#page-10-10) [2018a\)](#page-10-10). Following [Wang et al.](#page-10-10) [\(2018a\)](#page-10-10), we report accuracy for SST-2 and MNLI-m, and the average of accuracy and F1 score for QQP as our results. More details about each dataset are shown in Appendix [B.](#page-11-1)

Baselines. Following previous studies on dataset distillation in the CV field, we compared the performance of DiLM with three coreset selection methods, Random, K-centers [\(Wolf,](#page-10-1) [2011;](#page-10-1) [Sener and Savarese,](#page-9-2) [2018\)](#page-9-2), and Herding [\(Welling,](#page-10-2) [2009\)](#page-10-2), as well as TDD [\(Sucholutsky and Schonlau,](#page-9-7) [2021\)](#page-9-7), which is a recent embedding-level distillation method. Note that TDD also trains the learnable soft-labels and learning rates for each training step together with the input word embeddings. We also evaluated the vanilla LM, which skips training for gradient matching (in Section [3.3\)](#page-3-1), to validate its effectiveness. Note that we applied K-center representative sample selection (in Section [3.4\)](#page-5-0) to the vanilla LM as well. The details of each baseline are given in Appendix [C.](#page-11-2)

Evaluation. For evaluation, we used  $BERT_{BASE}$ and other three pre-trained models,  $RoBERTa<sub>BASE</sub>$ ,  $BERT<sub>LARGE</sub>$ , and  $XLNet<sub>BASE</sub>$ , as learner models (see more details in Appendix [D\)](#page-12-0). We trained a learner model on the distilled datasets for 200 steps by using AdamW [\(Loshchilov and Hutter,](#page-9-17) [2019\)](#page-9-17) with a learning rate of  $1.0 \times 10^{-4}$  and a batch size of 64.<sup>[2](#page-5-1)</sup> For Herding and TDD, we trained the learner model on their datasets for 100 times. For other methods, we generated 20 datasets with different random seeds and trained the learner model on each of them for 5 times. We report the average and standard deviation for these 100 models. In the result tables, '∗' indicates significant difference of DiLM from K-centers ( $p < 0.05$ , Welch's t-test). Note that the standard deviations in our results inevitably become large because we trained models with few selected/generated samples from different initial model parameters. However, our evaluation procedure, which includes 100 runs, supports the reliability of our experimental results enough to discuss the effectiveness of the proposed method.

Implementation. We used the 128M parameter version of GPT- $2<sup>3</sup>$  $2<sup>3</sup>$  $2<sup>3</sup>$  [\(Radford et al.,](#page-9-1) [2019\)](#page-9-1) as the gen-erator model of DiLM, and used BERTBASE [\(De](#page-8-0)[vlin et al.,](#page-8-0) [2019\)](#page-8-0) as the learner model, on which we calculated the gradient matching loss. To reduce the computational costs, we calculated the gradient matching loss only for the randomly initialized last layer parameters, which tend to have dominantly larger gradient than the pre-trained parameters. We set the number of each loop for training DiLM to  $S = 2000$ ,  $T = 10$ , and  $K = 20$ , and the generation interval to  $I_{int} = 200$  according to our preliminary experiments. The mini-batch size of real and synthetic samples were respectively set to  $M = 200$  and  $N = 64$ . More details of our implementation are given in Appendix [E.](#page-12-1)

## <span id="page-5-3"></span>5 Results and Discussion

#### 5.1 Performance for $BERT_{BASE}$

As shown in Table [1,](#page-6-0) we first compared DiLM with the other baselines for training  $BERT_{BASE}$ , on which DiLM trained gradient matching. We evaluated them for different sizes of distilled synthetic datasets of 5/10/20 data-per-class (DPC) settings.

We first found that the vanilla LM, which was

<span id="page-5-1"></span> $2$ We did not follow this training protocol for TDD, since TDD optimizes learning rates as well for each step with a specific synthetic sample order.

<span id="page-5-2"></span><sup>3</sup> <https://huggingface.co/gpt2>

<span id="page-6-0"></span>

| Data/class   | SST-2 (2 classes, 67.3k) |                |                | QQP (2 classes, 364k) |                  |                  | MNLI-m (3 classes, 393k) |                  |                  |
|--------------|--------------------------|----------------|----------------|-----------------------|------------------|------------------|--------------------------|------------------|------------------|
|              | 5                        | 10             | 20             | 5                     | 10               | 20               | 5                        | 10               | 20               |
| Random       | $58.1 \pm 5.2$           | $64.3 \pm 7.4$ | $70.3 \pm 6.8$ | $51.5 \pm 5.6$        | $56.0 \pm 4.8$   | $59.1 \pm 3.8$   | $35.6 \pm 2.1$           | $37.7 \pm 2.6$   | $40.1 \pm 3.2$   |
| K-centers    | $70.8 \pm 4.1$           | $75.9 \pm 4.7$ | $79.8 \pm 3.5$ | $60.7 \pm 3.8$        | $60.9 \pm 3.1$   | $62.6 \pm 2.7$   | $36.2 \pm 2.4$           | $41.8 \pm 3.2$   | $45.3 \pm 3.0$   |
| Herding      | $70.2 \pm 5.7$           | $73.2 \pm 5.7$ | $76.9 \pm 4.4$ | $56.0 \pm 5.6$        | $59.7 \pm 4.1$   | $62.3 \pm 3.4$   | $36.2 \pm 3.8$           | $38.7 \pm 3.7$   | $42.8 \pm 3.5$   |
| TDD (embed.) | $89.6 \pm 0.4$           | -              | -              | $81.5 \pm 0.2$        | -                | -                | $75.6 \pm 0.2$           | -                | -                |
| TDD (text)   | $50.2 \pm 1.6$           | -              | -              | $39.6 \pm 6.8$        | -                | -                | $33.4 \pm 1.8$           | -                | -                |
| Vanilla LM   | $65.2 \pm 6.8$           | $71.7 \pm 6.8$ | $77.6 \pm 4.1$ | $56.7 \pm 4.4$        | $59.3 \pm 3.8$   | $62.5 \pm 3.3$   | $36.3 \pm 2.7$           | $40.5 \pm 2.9$   | $43.6 \pm 3.1$   |
| DiLM         | $72.5 \pm 5.9^*$         | $76.3 \pm 4.6$ | $80.3 \pm 2.8$ | $58.8 \pm 5.2$        | $62.2 \pm 3.3^*$ | $64.4 \pm 2.6^*$ | $39.7 \pm 2.7^*$         | $44.8 \pm 3.1^*$ | $48.7 \pm 2.6^*$ |
| Full dataset |                          | 92.7           |                |                       | 89.6             |                  |                          | 86.7             |                  |

Table 1: Performance comparison of DiLM with coreset selection methods and TDD for training the BERT<sub>BASE</sub> model. Green highlighted results indicate that DiLM outperformed the coreset selection methods. Red highlighted results indicate performance degradation of distilled datasets from TDD after being converted to text. Note that we could not conduct the experiments for TDD with larger DPC settings due to GPU memory requirements.

only trained for synthetic training sample generation without gradient matching, clearly underperformed the coreset selection methods. This indicates that, as can be expected, the quality of the generated synthetic samples becomes lower than that of real samples in the original datasets. However, DiLM, which fine-tuned the vanilla LM with gradient matching, improved its performance and even outperformed the coreset selection methods overall. Note that the performance gains from K-centers indicate that DiLM generated synthetic training samples that are more effective for model training than the real samples in the original datasets.

When focusing on the difference between the three datasets, the performance gains of DiLM on QQP and MNLI-m were larger than that on SST-2. We believe this is because QQP and MNLI-m, which are the tasks to specify the relationship between two sentences, are intuitively less likely to have real samples that represent the task than SST-2, which is a relatively simple negative/positive classification task. In addition, it may also be related to the size of the original training dataset of QQP and MNLI-m, which is five times larger than that of SST-2. Since the generator model was trained by gradient matching with self-generated synthetic samples, it can explore broader sample space by pre-training with the original dataset that contains enough diversity samples, which results in the effective performance of DiLM.

For TDD, we also evaluated its distilled datasets as text data by converting them to discrete tokens that have nearest neighbor embeddings. When directly using the distilled datasets as word embeddings, TDD achieved remarkable performance even compared with the full datasets. However, after converting to text, its performance catastrophically

<span id="page-6-1"></span>

| <b>Dataset</b> | Model                                                                            | Random                                           | <b>K-centers</b>                                 | DiLM                                                 |
|----------------|----------------------------------------------------------------------------------|--------------------------------------------------|--------------------------------------------------|------------------------------------------------------|
|                | $BERT_{BASE}$ (S)                                                                | $70.3 + 6.8$                                     | $79.8 + 3.5$                                     | $80.3 \pm 2.8^*$                                     |
| $SST-2$        | <b>ROBERTaBASE</b><br><b>BERT</b> <sub>LARGE</sub><br><b>XLNetBASE</b>           | $74.4 \pm 5.3$<br>$74.7 + 8.4$<br>$69.9 + 6.2$   | $73.9 \pm 5.2$<br>$80.4 \pm 9.1$<br>$71.8 + 5.8$ | $78.1 \pm 3.8^*$<br>$83.1 + 6.2^*$<br>$77.9 + 4.7^*$ |
|                | $BERT_{BASE}$ (S)                                                                | $59.1 + 3.8$                                     | $62.6 \pm 2.7$                                   | $64.4 \pm 2.6^*$                                     |
| QQP            | ROBERTARASE<br><b>BERT</b> <sub>LARGE</sub><br><b>XLNet<sub>BASE</sub></b>       | $60.1 + 4.0$<br>$58.8 + 6.9$<br>$59.1 + 3.5$     | $63.9 \pm 3.2$<br>$59.0 + 8.9$<br>$60.9 \pm 3.0$ | $66.4 + 2.3*$<br>$62.9 + 8.6^*$<br>$64.4 + 2.2^*$    |
|                | $BERT_{BASE}$ (S)                                                                | $40.1 + 3.2$                                     | $45.3 + 3.0$                                     | $48.7 + 2.6$                                         |
| MNLI-m         | <b>ROBERTaBASE</b><br><b>BERT<sub>LARGE</sub></b><br><b>XLNet<sub>BASE</sub></b> | $39.6 + 2.5$<br>$40.9 \pm 4.5$<br>$39.0 \pm 2.0$ | $44.5 + 2.6$<br>$48.7 \pm 4.2$<br>$43.5 \pm 2.7$ | $45.0 + 2.8$<br>$49.6 + 4.4$<br>$44.7 + 2.7*$        |

Table 2: Cross-model generalization performance for settings of DPC=20. (S) indicates the source model for gradient matching of DiLM and feature extractor for K-centers.

degraded even to the lower-bound performances with random prediction. This suggests that the distilled datasets from TDD are strictly overfitted at the word embedding level and cannot be converted to text without acceptable performance degradation, which is necessary for applying them to other models. This point is the clear advantage of DiLM, which distills synthetic datasets at the text-level.

## 5.2 Cross-model Generalization

In contrast to the current embedding-level distillation methods, text-level synthetic datasets from DiLM can be leveraged for training different models independent of their word embedding weights. To emphasize this advantage, we evaluated the distilled synthetic datasets for training three models different from  $BERT_{BASE}$ , with which the distilled synthetic datasets were obtained, i.e., RoBERTaBASE, BERT<sub>LARGE</sub>, and XLNetBASE. Ta-

<span id="page-7-1"></span>

| Models           | <b>Random</b>   | K-centers       | DiLM              | RT           | <b>DMS</b>     | <b>Selection</b>         | SST-2        | <b>OOP</b>                                   | <b>MNLI-m</b>  |
|------------------|-----------------|-----------------|-------------------|--------------|----------------|--------------------------|--------------|----------------------------------------------|----------------|
| $GPT-2-XL(1.5B)$ | $64.8 \pm 12.0$ | $64.8 \pm 13.3$ | $71.1 \pm 13.0^*$ |              |                | $\overline{\mathcal{L}}$ |              | $72.5 \pm 5.9$ $58.8 \pm 5.2$ $39.7 \pm 2.7$ |                |
| OPT $(2.7B)$     | $89.3 + 5.9$    | $91.5 + 3.1$    | $92.7 + 1.9^*$    |              | $\epsilon$     |                          | $70.9 + 5.9$ | $57.6 + 5.0$                                 | $39.5 \pm 2.8$ |
| Llama $2(7B)$    | $93.6 + 2.9$    | $94.6 + 0.7$    | $95.1 + 0.7^*$    |              | $\blacksquare$ |                          | $71.3 + 5.6$ | $57.5 + 4.4$                                 | $38.8 + 3.0$   |
|                  |                 |                 | $65.2 + 7.0$      | $53.9 + 5.6$ | $37.9 + 3.2$   |                          |              |                                              |                |

Table 3: Performance of distilled datasets as 5-shot prompts for in-context learning of SST-2. Each score is the average and standard deviation for 100 prompts with 20 distilled datasets and 5 random orders.

ble [2](#page-6-1) summarizes the performances of Random, K-centers, and DiLM with DPC=20, where DiLM achieved stably good performances.[4](#page-7-0) The results indicate that the distilled datasets from DiLM consistently performed well for training the different models, even though DiLM trained gradient matching only for the  $BERT_{BASE}$  model's parameters. It is worth noting that our distilled datasets show successful generalization performance not only for training RoBERTa $_{BASE}$  and BERT<sub>LARGE</sub>, which have the same model architecture as  $BERT_{BASE}$ , but also for training XLNet<sub>BASE</sub>, which is an autoregressive model using the hidden state of the  $\le$ eos> token for classification, while BERT $_{\text{BASE}}$  is an autoencoding model using the hidden state of the [CLS] token.

We also evaluated the distilled datasets from DiLM as few-shot prompts for in-context learning of LLMs. Table [3](#page-7-1) shows the performance of Random, K-centers, and DiLM for in-context learning for SST-2 with three different sizes of LLMs, GPT-2-XL [\(Radford et al.,](#page-9-1) [2019\)](#page-9-1), OPT [\(Zhang et al.,](#page-10-16) [2022b\)](#page-10-16), and Llama 2 [\(Touvron et al.,](#page-9-18) [2023\)](#page-9-18). Surprisingly, the distilled datasets from DiLM consistently performed well for the in-context learning, compared with Random and K-centers.

These remarkable generalization performances across models and training processes strongly support the advantage of DiLM to distill datasets at the text-level.

#### 5.3 Analysis and Discussion

Ablation Study. Table [4](#page-7-2) shows the results of the ablation study for the performance improvement techniques of the representative teacher for gradient matching, the diverse mini-batch sampling of synthetic samples during training of DiLM (in Section [3.3\)](#page-3-1), and the representative sample selection with K-centers during synthetic dataset generation (in Section [3.4\)](#page-5-0). The results demonstrated that all

<span id="page-7-2"></span>

Table 4: Ablation study on the performance improvement techniques of DiLM with the DPC=5 setting. RT, DMS, and Selection indicate representative teacher, diverse mini-batch sampling, and sample selection with K-centers, respectively.

<span id="page-7-3"></span>Image /page/7/Figure/10 description: This image contains three line graphs stacked vertically, each showing the performance of different language models on various datasets as a function of DPC (data/class). The top graph is for SST-2, the middle graph is for QQP, and the bottom graph is for MNLI-m. The y-axis for SST-2 and MNLI-m represents 'Performance (Acc.)', while the y-axis for QQP represents 'Performance (Acc./F1)'. The x-axis for all graphs is 'DPC (data/class)' with values ranging from 1 to 200. Four models are plotted: BERTBASE (red circles), RoBERTaBASE (blue diamonds), BERTLARGE (green squares), and XLNetBASE (cyan triangles). In the SST-2 graph, performance generally increases with DPC, with XLNetBASE and BERTLARGE performing slightly better at higher DPC values. The QQP graph shows a similar trend, with all models improving as DPC increases, and XLNetBASE and BERTLARGE leading at higher DPC. The MNLI-m graph also demonstrates performance improvement with DPC, with BERTLARGE and XLNetBASE generally outperforming BERTBASE and RoBERTaBASE.

Figure 2: Performance for increasing number of synthetic samples with DPC  $\in \{1, 5, 10, 20, 50, 100, 200\}.$ We plot the mean and 95% confidence interval for 100 models trained on distilled datasets from DiLM.

the three techniques are consistently effective for DiLM.

Scaling of DPC. We investigated the performance of DiLM when increasing the size of synthetic datasets. Note that DiLM does not require retraining the generator model for generating distilled synthetic datasets for different DPCs, which is also the advantage of using generative models for dataset distillation. As shown in Figure [2,](#page-7-3) the performance of the distilled datasets generally scaled with increasing DPC.

Distilled Data Examples. We gave examples of distilled synthetic samples for each dataset in Appendix [G.](#page-12-3) We found that DiLM successfully generated interpretable synthetic samples that are appropriate for the tasks of the original datasets. Although DiLM consistently generated high quality synthetic samples for SST-2 and QQP, the repetition problem can be observed in some lengthy

<span id="page-7-0"></span><sup>&</sup>lt;sup>4</sup>We also show the results with other DPC settings in Appendix [F.](#page-12-2)

samples for MNLI-m. This suggests that there is still room for performance improvements of DiLM by using a larger and more sophisticated pre-trained language model for the generator model than the small GPT-2 used in our current experiments.

## 6 Conclusion

We proposed the first text-level dataset distillation approach, called DiLM, which trains a language model to generate informative synthetic samples as text data for model-agnostic applicability and interpretability of distilled datasets. Experimental results across various text classification datasets indicated that the distilled datasets from DiLM achieve successful performance for training various types of models beyond the source model used for distillation, even for in-context learning of LLMs.

## Limitations

The following three points are the limitations of this work. (i) Although DiLM achieved remarkable performance as a text-level distillation method, there is still a performance gap from the full datasets. However, the performance improvements from Kcenters are large enough to demonstrate the effectiveness of DiLM, considering the fundamental difficulty of the dataset distillation problem itself in cases when synthetic data are restricted to the textlevel. Moreover, DiLM has room for further performance improvement by employing larger and more sophisticated pre-trained language models as the generator model or using other dataset distillation objectives as an alternative to the gradient matching. (ii) In our experiments, we applied DiLM to distill only text classification task datasets. DiLM can be applied to text generation tasks as well by just considering the entire original training dataset as the data for a single label. In future work, we should explore the application of DiLM for more difficult settings, such as the text generation tasks and fullscratch training of language models. (iii) While privacy preservation of the original training datasets is one of the applications of dataset distillation, it is difficult to apply DiLM to the privacy preservation because the distilled synthetic datasets from DiLM may include real samples from the original dataset due to the training data memorization of the language model. However, we believe that the advantage of DiLM to generate distilled synthetic datasets at the text-level, enabling the training of models independent of word embedding weights, is more valuable than the application to the privacy preservation in terms of practical applications.

## References

- <span id="page-8-5"></span>Ondrej Bohdal, Yongxin Yang, and Timothy M. Hospedales. 2020. [Flexible dataset distilla](http://arxiv.org/abs/2006.08572)[tion: Learn labels instead of images.](http://arxiv.org/abs/2006.08572) *CoRR*, abs/2006.08572.
- <span id="page-8-1"></span>Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel Ziegler, Jeffrey Wu, Clemens Winter, Chris Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. 2020. [Language models are few-shot learners.](https://proceedings.neurips.cc/paper_files/paper/2020/file/1457c0d6bfcb4967418bfb8ac142f64a-Paper.pdf) In *Advances in Neural Information Processing Systems*, volume 33, pages 1877–1901. Curran Associates, Inc.
- <span id="page-8-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. 2022. [Dataset](https://doi.org/10.1109/CVPRW56347.2022.00521) [distillation by matching training trajectories.](https://doi.org/10.1109/CVPRW56347.2022.00521) In *IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops, CVPR Workshops 2022, New Orleans, LA, USA, June 19-20, 2022*, pages 4749–4758. IEEE.
- <span id="page-8-6"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. 2023. [General](https://doi.org/10.1109/CVPR52729.2023.00364)[izing dataset distillation via deep generative prior.](https://doi.org/10.1109/CVPR52729.2023.00364) In *IEEE/CVF Conference on Computer Vision and Pattern Recognition, CVPR 2023, Vancouver, BC, Canada, June 17-24, 2023*, pages 3739–3748. IEEE.
- <span id="page-8-3"></span>Dingfan Chen, Raouf Kerkouche, and Mario Fritz. 2022. [Private set generation with discriminative informa](https://proceedings.neurips.cc/paper_files/paper/2022/file/5e1a87dbb7e954b8d9d6c91f6db771eb-Paper-Conference.pdf)[tion.](https://proceedings.neurips.cc/paper_files/paper/2022/file/5e1a87dbb7e954b8d9d6c91f6db771eb-Paper-Conference.pdf) In *Advances in Neural Information Processing Systems*, volume 35, pages 14678–14690. Curran Associates, Inc.
- <span id="page-8-4"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. 2023. [Scaling up dataset distillation to ImageNet-](https://proceedings.mlr.press/v202/cui23e.html)[1K with constant memory.](https://proceedings.mlr.press/v202/cui23e.html) In *Proceedings of the 40th International Conference on Machine Learning*, volume 202 of *Proceedings of Machine Learning Research*, pages 6565–6590. PMLR.
- <span id="page-8-0"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. 2019. [BERT: Pre-training of](https://doi.org/10.18653/v1/N19-1423) [deep bidirectional transformers for language under](https://doi.org/10.18653/v1/N19-1423)[standing.](https://doi.org/10.18653/v1/N19-1423) In *Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers)*, pages 4171–4186, Minneapolis, Minnesota. Association for Computational Linguistics.

- <span id="page-9-6"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. 2022. [Pri](https://proceedings.mlr.press/v162/dong22c.html)[vacy for free: How does dataset condensation help](https://proceedings.mlr.press/v162/dong22c.html) [privacy?](https://proceedings.mlr.press/v162/dong22c.html) In *Proceedings of the 39th International Conference on Machine Learning*, volume 162 of *Proceedings of Machine Learning Research*, pages 5378–5396. PMLR.
- <span id="page-9-11"></span>Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. 2023. [A sur](https://doi.org/10.24963/ijcai.2023/741)[vey on dataset distillation: Approaches, applications](https://doi.org/10.24963/ijcai.2023/741) [and future directions.](https://doi.org/10.24963/ijcai.2023/741) In *Proceedings of the Thirty-Second International Joint Conference on Artificial Intelligence, IJCAI-23*, pages 6610–6618. International Joint Conferences on Artificial Intelligence Organization. Survey Track.
- <span id="page-9-14"></span>Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. 2014. [Generative ad](https://proceedings.neurips.cc/paper_files/paper/2014/file/5ca3e9b122f61f8f06494c97b1afccf3-Paper.pdf)[versarial nets.](https://proceedings.neurips.cc/paper_files/paper/2014/file/5ca3e9b122f61f8f06494c97b1afccf3-Paper.pdf) In *Advances in Neural Information Processing Systems*, volume 27. Curran Associates, Inc.
- <span id="page-9-15"></span>Tatsuya Hiraoka, Sho Takase, Kei Uchiumi, Atsushi Keyaki, and Naoaki Okazaki. 2020. [Optimizing](https://doi.org/10.18653/v1/2020.findings-emnlp.120) [word segmentation for downstream task.](https://doi.org/10.18653/v1/2020.findings-emnlp.120) In *Findings of the Association for Computational Linguistics: EMNLP 2020*, pages 1341–1351, Online. Association for Computational Linguistics.
- <span id="page-9-8"></span>Yongqi Li and Wenjie Li. 2021. [Data distillation for](http://arxiv.org/abs/2104.08448) [text classification.](http://arxiv.org/abs/2104.08448) *CoRR*, abs/2104.08448.
- <span id="page-9-16"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. 2023. Dream: Efficient dataset distillation by representative matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 17314–17324.
- <span id="page-9-0"></span>Yinhan Liu, Myle Ott, Naman Goyal, Jingfei Du, Mandar Joshi, Danqi Chen, Omer Levy, Mike Lewis, Luke Zettlemoyer, and Veselin Stoyanov. 2019. [Roberta: A robustly optimized BERT pretraining](http://arxiv.org/abs/1907.11692) [approach.](http://arxiv.org/abs/1907.11692) *CoRR*, abs/1907.11692.
- <span id="page-9-17"></span>Ilya Loshchilov and Frank Hutter. 2019. [Decoupled](https://openreview.net/forum?id=Bkg6RiCqY7) [weight decay regularization.](https://openreview.net/forum?id=Bkg6RiCqY7) In *7th International Conference on Learning Representations, ICLR 2019, New Orleans, LA, USA, May 6-9, 2019*. OpenReview.net.
- <span id="page-9-12"></span>Dougal Maclaurin, David Duvenaud, and Ryan P. Adams. 2015. [Gradient-based hyperparameter opti](http://proceedings.mlr.press/v37/maclaurin15.html)[mization through reversible learning.](http://proceedings.mlr.press/v37/maclaurin15.html) In *Proceedings of the 32nd International Conference on Machine Learning, ICML 2015, Lille, France, 6-11 July 2015*, volume 37 of *JMLR Workshop and Conference Proceedings*, pages 2113–2122. JMLR.org.
- <span id="page-9-9"></span>Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. 2023. [Dataset distillation](https://doi.org/10.18653/v1/2023.acl-short.12) [with attention labels for fine-tuning BERT.](https://doi.org/10.18653/v1/2023.acl-short.12) In *Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers)*, pages 119–127, Toronto, Canada. Association for Computational Linguistics.

- <span id="page-9-4"></span>Dmitry Medvedev and Alexander D'yakonov. 2021. [Learning to generate synthetic training data using](https://doi.org/10.1007/978-3-031-15168-2_12) [gradient matching and implicit differentiation.](https://doi.org/10.1007/978-3-031-15168-2_12) In *Recent Trends in Analysis of Images, Social Networks and Texts - 10th International Conference, AIST 2021, Tbilisi, Georgia, December 16-18, 2021, Revised Supplementary Proceedings*, volume 1573 of *Communications in Computer and Information Science*, pages 138–150. Springer.
- <span id="page-9-13"></span>Jeffrey Pennington, Richard Socher, and Christopher Manning. 2014. [GloVe: Global vectors for word](https://doi.org/10.3115/v1/D14-1162) [representation.](https://doi.org/10.3115/v1/D14-1162) In *Proceedings of the 2014 Conference on Empirical Methods in Natural Language Processing (EMNLP)*, pages 1532–1543, Doha, Qatar. Association for Computational Linguistics.
- <span id="page-9-1"></span>Alec Radford, Jeffrey Wu, Rewon Child, David Luan, Dario Amodei, Ilya Sutskever, et al. 2019. Language models are unsupervised multitask learners. *OpenAI blog*, 1(8):9.
- <span id="page-9-10"></span>Shivam Sahni and Harsh M. Patel. 2023. [Explor](https://doi.org/10.48550/arXiv.2308.04982)[ing multilingual text data distillation.](https://doi.org/10.48550/arXiv.2308.04982) *CoRR*, abs/2308.04982.
- <span id="page-9-5"></span>Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. 2022. [Sample condensation in online](https://doi.org/10.1109/IJCNN55064.2022.9892299) [continual learning.](https://doi.org/10.1109/IJCNN55064.2022.9892299) In *International Joint Conference on Neural Networks, IJCNN 2022, Padua, Italy, July 18-23, 2022*, pages 1–8. IEEE.
- <span id="page-9-2"></span>Ozan Sener and Silvio Savarese. 2018. [Active learning](https://openreview.net/forum?id=H1aIuk-RW) [for convolutional neural networks: A core-set ap](https://openreview.net/forum?id=H1aIuk-RW)[proach.](https://openreview.net/forum?id=H1aIuk-RW) In *6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 - May 3, 2018, Conference Track Proceedings*. OpenReview.net.
- <span id="page-9-3"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. 2020. [Generative](https://proceedings.mlr.press/v119/such20a.html) [teaching networks: Accelerating neural architecture](https://proceedings.mlr.press/v119/such20a.html) [search by learning to generate synthetic training data.](https://proceedings.mlr.press/v119/such20a.html) In *Proceedings of the 37th International Conference on Machine Learning*, volume 119 of *Proceedings of Machine Learning Research*, pages 9206–9216. PMLR.
- <span id="page-9-7"></span>Ilia Sucholutsky and Matthias Schonlau. 2021. [Soft](https://doi.org/10.1109/IJCNN52387.2021.9533769)[label dataset distillation and text dataset distillation.](https://doi.org/10.1109/IJCNN52387.2021.9533769) In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8.
- <span id="page-9-18"></span>Hugo Touvron, Louis Martin, Kevin Stone, Peter Albert, Amjad Almahairi, Yasmine Babaei, Nikolay Bashlykov, Soumya Batra, Prajjwal Bhargava, Shruti Bhosale, Dan Bikel, Lukas Blecher, Cristian Canton-Ferrer, Moya Chen, Guillem Cucurull, David Esiobu, Jude Fernandes, Jeremy Fu, Wenyin Fu, Brian Fuller, Cynthia Gao, Vedanuj Goswami, Naman Goyal, Anthony Hartshorn, Saghar Hosseini, Rui Hou, Hakan Inan, Marcin Kardas, Viktor Kerkez, Madian Khabsa, Isabel Kloumann, Artem Korenev, Punit Singh Koura,

Marie-Anne Lachaux, Thibaut Lavril, Jenya Lee, Diana Liskovich, Yinghai Lu, Yuning Mao, Xavier Martinet, Todor Mihaylov, Pushkar Mishra, Igor Molybog, Yixin Nie, Andrew Poulton, Jeremy Reizenstein, Rashi Rungta, Kalyan Saladi, Alan Schelten, Ruan Silva, Eric Michael Smith, Ranjan Subramanian, Xiaoqing Ellen Tan, Binh Tang, Ross Taylor, Adina Williams, Jian Xiang Kuan, Puxin Xu, Zheng Yan, Iliyan Zarov, Yuchen Zhang, Angela Fan, Melanie Kambadur, Sharan Narang, Aurélien Rodriguez, Robert Stojnic, Sergey Edunov, and Thomas Scialom. 2023. [Llama 2: Open foundation and fine](https://doi.org/10.48550/ARXIV.2307.09288)[tuned chat models.](https://doi.org/10.48550/ARXIV.2307.09288) *CoRR*, abs/2307.09288.

- <span id="page-10-10"></span>Alex Wang, Amanpreet Singh, Julian Michael, Felix Hill, Omer Levy, and Samuel Bowman. 2018a. [GLUE: A multi-task benchmark and analysis plat](https://doi.org/10.18653/v1/W18-5446)[form for natural language understanding.](https://doi.org/10.18653/v1/W18-5446) In *Proceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting Neural Networks for NLP*, pages 353–355, Brussels, Belgium. Association for Computational Linguistics.
- <span id="page-10-12"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. 2023. [Dim: Distilling dataset](https://doi.org/10.48550/ARXIV.2303.04707) [into generative model.](https://doi.org/10.48550/ARXIV.2303.04707) *CoRR*, abs/2303.04707.
- <span id="page-10-4"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. 2022. [Cafe: Learning](https://doi.org/10.1109/CVPR52688.2022.01188) [to condense dataset by aligning features.](https://doi.org/10.1109/CVPR52688.2022.01188) In *2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 12186–12195.
- <span id="page-10-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. 2018b. [Dataset distillation.](http://arxiv.org/abs/1811.10959) *CoRR*, abs/1811.10959.
- <span id="page-10-2"></span>Max Welling. 2009. [Herding dynamical weights to](https://doi.org/10.1145/1553374.1553517) [learn.](https://doi.org/10.1145/1553374.1553517) In *Proceedings of the 26th Annual International Conference on Machine Learning*, ICML '09, page 1121–1128, New York, NY, USA. Association for Computing Machinery.
- <span id="page-10-8"></span>Felix Wiewel and Bin Yang. 2021. [Condensed com](https://doi.org/10.1109/IJCNN52387.2021.9533491)[posite memory continual learning.](https://doi.org/10.1109/IJCNN52387.2021.9533491) In *International Joint Conference on Neural Networks, IJCNN 2021, Shenzhen, China, July 18-22, 2021*, pages 1–8. IEEE.
- <span id="page-10-1"></span>Gert W. Wolf. 2011. [Facility location: concepts, mod](https://doi.org/10.1080/13658816.2010.528422)[els, algorithms and case studies. series: Contribu](https://doi.org/10.1080/13658816.2010.528422)[tions to management science.](https://doi.org/10.1080/13658816.2010.528422) *Int. J. Geogr. Inf. Sci.*, 25(2):331–333.
- <span id="page-10-7"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. 2023. [Feddm: Iterative dis](https://doi.org/10.1109/CVPR52729.2023.01566)[tribution matching for communication-efficient fed](https://doi.org/10.1109/CVPR52729.2023.01566)[erated learning.](https://doi.org/10.1109/CVPR52729.2023.01566) In *IEEE/CVF Conference on Computer Vision and Pattern Recognition, CVPR 2023, Vancouver, BC, Canada, June 17-24, 2023*, pages 16323–16332. IEEE.
- <span id="page-10-18"></span>Zhilin Yang, Zihang Dai, Yiming Yang, Jaime G. Carbonell, Ruslan Salakhutdinov, and Quoc V. Le. 2019. [Xlnet: Generalized autoregressive pretraining for](https://proceedings.neurips.cc/paper/2019/hash/dc6a7e655d7e5840e66733e9ee67cc69-Abstract.html)

[language understanding.](https://proceedings.neurips.cc/paper/2019/hash/dc6a7e655d7e5840e66733e9ee67cc69-Abstract.html) In *Advances in Neural Information Processing Systems 32: Annual Conference on Neural Information Processing Systems 2019, NeurIPS 2019, December 8-14, 2019, Vancouver, BC, Canada*, pages 5754–5764.

- <span id="page-10-15"></span>Lantao Yu, Weinan Zhang, Jun Wang, and Yong Yu. 2017. [Seqgan: Sequence generative adversarial](https://doi.org/10.1609/AAAI.V31I1.10804) [nets with policy gradient.](https://doi.org/10.1609/AAAI.V31I1.10804) In *Proceedings of the Thirty-First AAAI Conference on Artificial Intelligence, February 4-9, 2017, San Francisco, California, USA*, pages 2852–2858. AAAI Press.
- <span id="page-10-9"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. 2023. [Dataset distillation: A comprehensive review.](https://doi.org/10.48550/ARXIV.2301.07014) *CoRR*, abs/2301.07014.
- <span id="page-10-6"></span>Jie Zhang, Chen Chen, Bo Li, Lingjuan Lyu, Shuang Wu, Shouhong Ding, Chunhua Shen, and Chao Wu. 2022a. [Dense: Data-free one-shot federated learn](https://proceedings.neurips.cc/paper_files/paper/2022/file/868f2266086530b2c71006ea1908b14a-Paper-Conference.pdf)[ing.](https://proceedings.neurips.cc/paper_files/paper/2022/file/868f2266086530b2c71006ea1908b14a-Paper-Conference.pdf) In *Advances in Neural Information Processing Systems*, volume 35, pages 21414–21428. Curran Associates, Inc.
- <span id="page-10-16"></span>Susan Zhang, Stephen Roller, Naman Goyal, Mikel Artetxe, Moya Chen, Shuohui Chen, Christopher Dewan, Mona T. Diab, Xian Li, Xi Victoria Lin, Todor Mihaylov, Myle Ott, Sam Shleifer, Kurt Shuster, Daniel Simig, Punit Singh Koura, Anjali Sridhar, Tianlu Wang, and Luke Zettlemoyer. 2022b. [OPT: open pre-trained transformer language mod](https://doi.org/10.48550/ARXIV.2205.01068)[els.](https://doi.org/10.48550/ARXIV.2205.01068) *CoRR*, abs/2205.01068.
- <span id="page-10-17"></span>Y Zhang, Z Gan, and L Carin. 2016. Generating text via adversarial training. In *NIPS workshop on Adversarial Training*. academia. edu.
- <span id="page-10-14"></span>Yizhe Zhang, Zhe Gan, Kai Fan, Zhi Chen, Ricardo Henao, Dinghan Shen, and Lawrence Carin. 2017. [Adversarial feature matching for text generation.](https://proceedings.mlr.press/v70/zhang17b.html) In *Proceedings of the 34th International Conference on Machine Learning*, volume 70 of *Proceedings of Machine Learning Research*, pages 4006–4015. PMLR.
- <span id="page-10-11"></span>Bo Zhao and Hakan Bilen. 2021. [Dataset condensa](https://proceedings.mlr.press/v139/zhao21a.html)[tion with differentiable siamese augmentation.](https://proceedings.mlr.press/v139/zhao21a.html) In *Proceedings of the 38th International Conference on Machine Learning*, volume 139 of *Proceedings of Machine Learning Research*, pages 12674–12685. PMLR.
- <span id="page-10-13"></span>Bo Zhao and Hakan Bilen. 2022. [Synthesizing in](https://doi.org/10.48550/ARXIV.2204.07513)[formative training samples with GAN.](https://doi.org/10.48550/ARXIV.2204.07513) *CoRR*, abs/2204.07513.
- <span id="page-10-5"></span>Bo Zhao and Hakan Bilen. 2023. [Dataset condensa](https://doi.org/10.1109/WACV56688.2023.00645)[tion with distribution matching.](https://doi.org/10.1109/WACV56688.2023.00645) In *IEEE/CVF Winter Conference on Applications of Computer Vision, WACV 2023, Waikoloa, HI, USA, January 2-7, 2023*, pages 6503–6512. IEEE.
- <span id="page-10-3"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2021. [Dataset condensation with gradient matching.](https://openreview.net/forum?id=mSAKhLYLSsl) In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*. OpenReview.net.

### <span id="page-11-0"></span>A Background Details of DiLM

In this section, we provide the detailed background of the techniques that we introduced in Section [3.3](#page-3-1) to enable back-propagation computing by bypassing the non-differentiable discrete text via loss weighting according to the generation probabilities.

As we described in Section [3.3,](#page-3-1) although there existed two standard approaches to the nondifferentiable problem in the discrete text generation, that is, soft-argmax [\(Zhang et al.,](#page-10-17) [2016\)](#page-10-17) and policy gradient [\(Yu et al.,](#page-10-15) [2017\)](#page-10-15), both of them cannot be applied in training DiLM. For soft-argmax and the same type of approaches, it is necessary that the vocabulary of the generator model be the same as that of the learner model, which receives the text generated by the generator model as an input. However, it is not true in the case of DiLM, where we used GPT-2 for the generator model and BERT<sub>BASE</sub> for the learner model.

As for policy gradient, we can apply it with sample-level gradient similarity as the reward function. However, the gradients for synthetic data for gradient matching loss should be calculated as an average for samples in a mini-batch, not for a single sample. Moreover, calculating per-sample gradients is computationally inefficient. These are the reasons why we did not use the policy gradient with the per-sample gradient similarity.

However, the basic idea of our approach, which aims to update the generator model to increase its generation probabilities for synthetic samples that improve gradient similarity, is essentially the same as the policy gradient. In addition, it is worth noting that our approach can also be formulated as the policy gradient manner. Letting  $g_{\theta}(\tilde{x}) = \frac{\partial l_{\theta}(\tilde{x})}{\partial \theta}$ and  $r(\cdot)$  be the reward function, the gradient of the generator parameters  $\phi$  is represented as

$$
\nabla_{\phi} \mathcal{L}_{\phi} = \sum_{n=1}^{N} \nabla_{\phi} l_{\phi}(\tilde{x}_n) \cdot r(\tilde{x}_n) \text{ where}
$$
$$
r(\tilde{x}_n) = a_n \left\{ g_{\theta}(\tilde{x}_n) - g_{\theta}(\tilde{X}) \right\}^T \left( -\frac{\partial \mathcal{L}_{GM}}{\partial g_{\theta}(\tilde{X})} \right).
$$

## <span id="page-11-1"></span>B Datasets

We used three text classification datasets in the GLUE benchmark [\(Wang et al.,](#page-10-10) [2018a\)](#page-10-10) from hug-gingface datasets.<sup>[5](#page-11-3)</sup> SST-2 is a banally sentiment classification (negative/positive) task for movie review sentences. QQP is a task to identify whether a question pair is semantically equivalent or not. MNLI-m is a natural language inference task to predict a premise sentence entails or contradicts a hypothesis sentence or neither (neutral). We reported the evaluation results on the validation set in Section [5,](#page-5-3) since the test set is not publicly available. For MNLI-m, we used the matched-domain validation set for evaluation. We summarize the statistics of each dataset in Table [5.](#page-11-4)

<span id="page-11-4"></span>

| <b>Dataset</b> | <b>Metric</b> | #Train | #Dev | #Class |
|----------------|---------------|--------|------|--------|
| SST-2          | accuracy      | 67k    | 872  | 2      |
| QQP            | accuracy/F1   | 364k   | 40k  | 2      |
| MNLI-m         | accuracy      | 393k   | 9.8k | 3      |

Table 5: Summary of statistics of evaluation datasets

### <span id="page-11-2"></span>C Baselines Details

In this section, we explain the details of the baseline methods used in our experiments.

### C.1 Coreset Selection

Random is the simplest baseline, which randomly selects real samples from the original training dataset.

K-centers [\(Wolf,](#page-10-1) [2011;](#page-10-1) [Sener and Savarese,](#page-9-2) [2018\)](#page-9-2) is a standard coreset selection method that selects the center samples of sub-clusters as a coreset, which eliminates redundant samples and covers the distribution of the original dataset.

Herding [\(Welling,](#page-10-2) [2009\)](#page-10-2) is also a standard coreset selection method that greedily selects real samples to match their mean embedding with that of the original dataset.

For K-centers and Herding, we used the last hidden state of the  $[CLS]$  token in the  $BERT_{BASE}$ model as a feature of each training sample.

### C.2 Embedding-level Dataset Distillation

 $TDD<sup>6</sup>$  $TDD<sup>6</sup>$  $TDD<sup>6</sup>$  [\(Sucholutsky and Schonlau,](#page-9-7) [2021\)](#page-9-7) is the current embedding level text dataset distillation method. TDD also optimizes learnable softlabels and learning rates together with input word embeddings by the original meta-learning approach [\(Wang et al.,](#page-10-0) [2018b\)](#page-10-0). Following the best performing settings in [Maekawa et al.](#page-9-9) [\(2023\)](#page-9-9), which

<span id="page-11-3"></span><sup>5</sup> <https://huggingface.co/datasets/glue>

<span id="page-11-5"></span> $6$ We used the implementation by [Maekawa et al.](#page-9-9) [\(2023\)](#page-9-9), because it also employs BERT as the learner model.

applied this approach to the BERT model, we used one synthetic sample per class as a mini-batch of a single gradient step and fixed the order of synthetic samples, which means the learner model is trained with 5 gradient steps in the experiments in Section [5](#page-5-3) with DPC=5. Similar to DiLM, TDD also used  $BERT_{BASE}$  as the learner model for distillation.

<span id="page-12-0"></span>

## D Learner Models

 $\text{BERT}_{\text{BASE}}^7$  $\text{BERT}_{\text{BASE}}^7$  [\(Devlin et al.,](#page-8-0) [2019\)](#page-8-0) was used as the source model for training for dataset distillation and the feature extractor of the coreset selection methods. Following the fine-tuning settings in [De](#page-8-0)[vlin et al.](#page-8-0) [\(2019\)](#page-8-0), we used a randomly initialized linear layer on the top of the last hidden state of the **[CLS]** token.

RoBERTa<sub>BASE</sub><sup>[8](#page-12-5)</sup> is a BERT derivative model proposed by [Liu et al.](#page-9-0) [\(2019\)](#page-9-0). This model has the same size and architecture as BERT<sub>BASE</sub>, but has different parameters pre-trained with the masked language modeling (MLM) task, without the next sentence prediction (NSP) task, on a larger corpus than the BERT models.

**BERT**<sub>LARGE</sub><sup>[9](#page-12-6)</sup> is the 24 layer, 340M parameter version of BERT, while BERT<sub>BASE</sub> has 12 layers and 110M parameters.

 $\mathbf{XLNet}_{\mathbf{BASE}}^{10}$  $\mathbf{XLNet}_{\mathbf{BASE}}^{10}$  $\mathbf{XLNet}_{\mathbf{BASE}}^{10}$  is an autoregressive model in contrast to BERT and RoBERTa. Following [\(Yang](#page-10-18) [et al.,](#page-10-18) [2019\)](#page-10-18), we used a randomly initialized linear layer on the top of the last hidden state of the <eos> token, which involves entire tokens in the sequence.

<span id="page-12-1"></span>

## E Implementation Details

Table [6](#page-12-8) shows the details of hyperparameter settings in our experiments. Our implementation was based on PyTorch 2.1.0, and we used pre-trained models from Hugging Face Transformers 4.30.0. All model training and evaluation in our experiments were conducted with the half-precision (BFloat16) on a single RTX 3090 (24GB), RTX A6000 (48GB), or A100 PCIe (80GB) according to the required GPU memory size for each experiment.

<span id="page-12-8"></span>

| <b>Pre-training settings of DiLM</b>     |                                        |
|------------------------------------------|----------------------------------------|
| Optimizer                                | AdamW                                  |
| Learning rate                            | $1.0 \times 10^{-5}$                   |
| Learning rate scheduler                  | Linear warm-up and<br>cosine annealing |
| Warmup ratio                             | 0.05                                   |
| Weight decay                             | 0.01                                   |
| Gradient clipping                        | 1.0                                    |
| Dropout ratio                            | 0.1                                    |
| # of training steps                      | 80,000                                 |
| Batch size                               | 64                                     |
| <b>Fine-tuning settings of DiLM</b>      |                                        |
| Optimizer                                | AdamW                                  |
| Learning rate                            | $3.0 \times 10^{-7}$                   |
| Learning rate scheduler                  | Linear warm-up and<br>cosine annealing |
| Warmup ratio                             | 0.05                                   |
| Weight decay                             | 0.01                                   |
| Gradient clipping                        | 1.0                                    |
| Dropout ratio                            | 0.1                                    |
| # of outer loop $(S)$                    | 20,000                                 |
| # of inner loop $(T)$                    | 10                                     |
| # of learner updating steps $(K)$        | 20                                     |
| Batch size of real samples $(M)$         | 200                                    |
| Batch size of synthetic samples $(N)$    | 64                                     |
| Generation interval $(I_{\text{int}})$   | 200                                    |
| Learner training settings for evaluation |                                        |
| Optimizer                                | AdamW                                  |
| Learning rate                            | $1.0 \times 10^{-4}$                   |
| Learning rate scheduler                  | Linear warm-up and<br>cosine annealing |
| Warmup ratio                             | 0.5                                    |
| Weight decay                             | 0.01                                   |
| Gradient clipping                        | 1.0                                    |
| Dropout ratio                            | 0.1                                    |
| # of training steps                      | 200                                    |
| Batch size                               | 64                                     |

Table 6: Hyperparameter settings in our experiments

<span id="page-12-2"></span>

### F Results for Cross-model Generalization

Tables [7](#page-13-0) and [8](#page-13-1) show the cross-model generalization performances with DPC=5,10 settings. As in the setting of DPC=20 in Table [2,](#page-6-1) DiLM also performed well in training different models than the source model.

## <span id="page-12-3"></span>G Distilled Synthetic Data Examples

We gave examples of distilled synthetic samples from DiLM in Tables [9,](#page-14-0) [10,](#page-14-1) and [11.](#page-15-0) Generated synthetic examples with DiLM were interpretable and seem to represent the tasks of the original training dataset.

<span id="page-12-4"></span><sup>7</sup> <https://huggingface.co/bert-base-uncased>

<span id="page-12-5"></span><sup>8</sup> <https://huggingface.co/roberta-base>

<span id="page-12-6"></span><sup>9</sup> <https://huggingface.co/bert-large-uncased>

<span id="page-12-7"></span><sup>10</sup><https://huggingface.co/xlnet-base-cased>

<span id="page-13-0"></span>

| Dataset | Model        | Random         | K-centers                        | DiLM             |
|---------|--------------|----------------|----------------------------------|------------------|
| SST-2   | BERTBASE (S) | $58.1 	pm 5.2$ | $70.8 	pm 4.1$                   | $72.5 	pm 5.9^*$ |
|         | RoBERTaBASE  | $60.6 	pm 7.6$ | $74.2 	pm 4.9$                   | $75.1 	pm 4.6$   |
|         | BERTLARGE    | $60.4 	pm 8.4$ | $70.0 	pm 8.2$                   | $73.7 	pm 8.4^*$ |
|         | XLNetBASE    | $57.0 	pm 5.5$ | $66.4 	pm 5.0$                   | $69.5 	pm 6.6^*$ |
| QQP     | BERTBASE (S) | $51.5 	pm 5.6$ | $60.7 	pm 3.8$                   | $58.8 	pm 5.2$   |
|         | RoBERTaBASE  | $52.5 	pm 6.0$ | <b><math>63.9 	pm 3.3</math></b> | $62.4 	pm 3.7$   |
|         | BERTLARGE    | $53.3 	pm 6.7$ | $58.3 	pm 5.8$                   | $58.8 	pm 5.7$   |
|         | XLNetBASE    | $52.6 	pm 5.2$ | <b><math>62.6 	pm 3.1</math></b> | $60.2 	pm 4.6$   |
| MNLI-m  | BERTBASE (S) | $35.6 	pm 2.1$ | $36.2 	pm 2.4$                   | $39.7 	pm 2.7^*$ |
|         | RoBERTaBASE  | $35.8 	pm 2.1$ | $37.4 	pm 2.1$                   | $38.8 	pm 3.0^*$ |
|         | BERTLARGE    | $36.9 	pm 2.8$ | $37.4 	pm 2.9$                   | $41.5 	pm 3.7^*$ |
|         | XLNetBASE    | $35.4 	pm 1.4$ | $37.0 	pm 1.5$                   | $37.3 	pm 1.9$   |

Table 7: Cross-model generalization performance for the setting of DPC=5. (S) indicates the source model for gradient matching of DiLM and feature extractor for K-centers.

<span id="page-13-1"></span>

| <b>Dataset</b>   | Model                         | Random         | <b>K-centers</b> | DiLM             |
|------------------|-------------------------------|----------------|------------------|------------------|
|                  | $BERT_{BASE}$ (S)             | $64.3 + 7.4$   | 75.9+4.7         | 76.3+4.6         |
| SST <sub>2</sub> | <b>ROBERTaBASE</b>            | $68.6 + 7.1$   | 74.6+5.6         | $77.1 + 4.1*$    |
|                  | <b>BERT</b> LARGE             | $67.2 \pm 8.5$ | $76.6 \pm 8.4$   | $79.2 + 7.8^*$   |
|                  | <b>XLNetBASE</b>              | $63.7 + 7.5$   | 68.0+6.1         | $74.2 + 4.9*$    |
|                  | $BERT_{BASE}$ (S)             | $56.0 + 4.8$   | $60.9 \pm 3.1$   | $62.2 \pm 3.3^*$ |
| QQP              | <b>ROBERTa<sub>BASE</sub></b> | $56.4 \pm 5.3$ | $64.0 + 2.7$     | $63.9 \pm 4.3$   |
|                  | <b>BERT<sub>LARGE</sub></b>   | $53.7 + 8.5$   | $59.4 + 5.6$     | $60.6 + 7.5$     |
|                  | <b>XLNet<sub>BASE</sub></b>   | $55.0 \pm 4.5$ | $61.4 + 3.2$     | $62.8 + 2.2^*$   |
|                  | $BERT_{BASE}$ (S)             | $37.7 + 2.6$   | $41.8 \pm 3.2$   | $44.8 \pm 3.1^*$ |
| MNLI-m           | <b>ROBERTaBASE</b>            | $37.1 + 2.2$   | $42.1 + 2.6$     | $40.9 \pm 2.6^*$ |
|                  | <b>BERT<sub>LARGE</sub></b>   | $39.7 + 3.6$   | $43.4 + 4.4$     | $45.4 + 4.1*$    |
|                  | <b>XLNetRASE</b>              | 37.0±1.4       | $41.5 + 2.6^*$   | $40.6 \pm 1.9$   |

Table 8: Cross-model generalization performance for the setting of DPC=10. (S) indicates the source model for gradient matching of DiLM and feature extractor for K-centers.

<span id="page-14-0"></span>

| Label    | <b>Sentence</b>                                                                                                                                            |
|----------|------------------------------------------------------------------------------------------------------------------------------------------------------------|
| negative | is too amateurishly square to work as storytelling, and the ensemble cast lacks depth and resonance.                                                       |
|          | is so lousy that you can not enjoy it                                                                                                                      |
|          | incredibly lifeless, with the lack-of-attention span                                                                                                       |
|          | the script's contrived, lame screenplay and listless direction are just the ticket cost.                                                                   |
|          | a cheap scam that only weak claims to dramatic impact and creepy-crawly humor.                                                                             |
| positive | is a wondrous accomplishment of veracity and narrative grace.                                                                                              |
|          | very best                                                                                                                                                  |
|          | a fully realized story with keen insights into parapsychological phenomena and the soulful nuances of the<br>grieving process                              |
|          | it one of the best-sustained ideas i have ever seen on the screen.                                                                                         |
|          | a surprisingly sweet, tender drama that does a superb job contrasting the sleekness of the film's present with the<br>playful paranoia of the film's past. |

Table 9: Distilled synthetic samples for SST-2 with DPC=5

<span id="page-14-1"></span>

| Label         | <b>Question 1</b>                                                                              | <b>Question 2</b>                                                                                                                                                                   |
|---------------|------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|               | Why should I write a good backmatter for an interna-<br>tional conference?                     | Where can I study internationally on business logic?                                                                                                                                |
|               | How long does it take you to learn the German lan-<br>guage?                                   | How long does it take to learn the English language?                                                                                                                                |
| not duplicate | What are some unexpected things first-time visitors to<br>Colombia notice?                     | What are some unexpected things first-time visitors to<br>Canada notice?                                                                                                            |
|               | Why is red in PFUS something I can't see when I tap<br>PFUS?                                   | Did one have a chance to see one of the real masterpieces<br>being played by Richard Bachardo in MS Dhoni Cricket:<br>Live Streaming, in the Permanent XI Test Center at<br>Mumbai? |
|               | How does digital gate keeper disable ads on a WiFi band?                                       | How can I enabled sable my WiFi network on my HTC<br>phone?                                                                                                                         |
|               | How do I recover my Gmail account after recovery?                                              | How do I recover my Gmail account from recovery?                                                                                                                                    |
|               | How do you prevent hair loss without touching hair?                                            | How do I prevent hair loss without touching hair?                                                                                                                                   |
| duplicate     | How do I get successful in C.E.?                                                               | How can I get successful in C.E.?                                                                                                                                                   |
|               | What is the best word or link you use to explain the<br>meaning of a certain book to a friend? | What is the best word or link you use to explain the<br>meaning of a certain book to a friend?                                                                                      |
|               | How will the ban of Rs 500 and Rs 1000 notes affect<br>Indian economy?                         | How will the 500 and 1000 rupee notes ban affect the<br>Indian economy?                                                                                                             |

Table 10: Distilled synthetic samples for QQP with DPC=5

<span id="page-15-0"></span>

| Label         | Premise                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | Hypothesis                                                                                                                                                                                     |
|---------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|               | Guess we are all here, friends.                                                                                                                                                                                                                                                                                                                                                                                                                                                     | We were all here, friends.                                                                                                                                                                     |
|               | The costs to the Service, often estimated to be between<br>\$100 and \$150 million, will be higher because of the<br>reduced volume of post-1991 pleadings by six states and<br>28 other states requiring service members to produce<br>basic records electronically.                                                                                                                                                                                                               | Costs to the Service are higher because of reduced<br>volume of post-1991 pleadings by six states and 28<br>other states requiring service members to produce basic<br>records electronically. |
|               | uh-huh is that right because like i say a lot of people tell<br>me we could make it cheaper if we wanted but we didn't<br>i mean our family life is just so far so far that                                                                                                                                                                                                                                                                                                         | It seems that a lot of people tell me that it could be<br>cheaper if we wanted but we don't really think we could<br>make it cheaper.                                                          |
| entailment    | However, the CEF report suggested that some of the fol-<br>lowing could serve to reduce the burden on small entities<br>with federally or nonfederal support for compliance with<br>the rule and to minimize the number of affected entities<br>receiving small reductions of federal payments.                                                                                                                                                                                     | Some things could be considered part of the CEF report<br>for reducing burdens on small entities.                                                                                              |
|               | If you are a casino business owner looking to expand<br>your profits, opportunities and experiences, or even to<br>retain some intellectual property you acquired during<br>your travels in other countries, it is best to visit Can-<br>cio, Parnell's (National Cancia) resort in Montego Bay,<br>where prices and travel policies range from a very rea-<br>sonable \$50.                                                                                                        | The casino has plenty of opportunities you can expand<br>your profits with in Cancio, Parnell's resort.                                                                                        |
|               | oh in that case you have to give them uh six months to<br>come and you know and let them go on                                                                                                                                                                                                                                                                                                                                                                                      | They don't have to get their first six months if they<br>return.                                                                                                                               |
| neutral       | This is highly valued nationally because of its steeply<br>pro-retirement payment culture, which is perceived as<br>a great success rate by the profession and outside of<br>its area of employment, particularly among the field's<br>young professionals.                                                                                                                                                                                                                         | Out of all the fields in the population, it is highly val-<br>ued by the professional community because it provides<br>confidence that the community will care more about its<br>growth.       |
|               | yeah right now i i still wish they were a little more                                                                                                                                                                                                                                                                                                                                                                                                                               | The idea of having people tell us what to do is good for<br>their business and prospects.                                                                                                      |
|               | In fact, there is one wonder why Republican leaders are<br>afraid to mention his name.                                                                                                                                                                                                                                                                                                                                                                                              | Republican leaders are not afraid of his name because<br>he is in need of attention.                                                                                                           |
|               | To me, it's an excellent system.                                                                                                                                                                                                                                                                                                                                                                                                                                                    | I think it could be a good system for a number of reasons.                                                                                                                                     |
| contradiction | yeah well you know i can't i can't i know sometimes<br>i just i'll remember remembering for once the former<br>minister might be sympathetic to some of the Serbian<br>government cases that they might say well there's no<br>way out um no matter what their approach to the possi-<br>bility of a peace dividend a lot of people i think i think<br>are are willing to compromise and and to stand up and<br>say who's right and who's wrong and i think it's a good<br>idea and | I can't recall the minister's views on different Serbian<br>government cases.                                                                                                                  |
|               | I suppose you could say, if it were not for the gleam<br>of light in the hour of your death-boom, that the fatal<br>effects were of a furtive rather than a ferocious nature?                                                                                                                                                                                                                                                                                                       | I don't think you could confirm it is a furtive either.                                                                                                                                        |
|               | i think something has to change there                                                                                                                                                                                                                                                                                                                                                                                                                                               | They have no plans at all to change.                                                                                                                                                           |
|               | The revisions take into account the range of factors that<br>varying units of measure represent when evaluating new<br>disclosure requirements and when determining whether<br>it should be possible to offer various types of similar<br>products for different reasons.                                                                                                                                                                                                           | The revisions go against the current practice and do not<br>consider whether it should be possible to offer different<br>types of similar products for different reasons.                      |
|               | yeah i uh i uh i don't think there's that's a bad place to<br>live in some part of the world and do everything else that<br>it's really not because people have gotten up in arms but<br>it's all it's all a lot of money to run a very very wealthy<br>individual home                                                                                                                                                                                                             | I don't think we should be buying a very wealthy home<br>in an undeveloped area in the developed world.                                                                                        |

Table 11: Distilled synthetic samples for MNLI-m with DPC=5