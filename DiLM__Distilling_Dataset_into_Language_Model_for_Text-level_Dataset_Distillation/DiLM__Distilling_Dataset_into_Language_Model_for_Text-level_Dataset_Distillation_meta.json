{"table_of_contents": [{"title": "DiLM: Distilling Dataset into Language Model\nfor Text-level Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[153.87909319899245, 79.47640249332146], [441.76171875, 79.47640249332146], [441.76171875, 107.716796875], [153.87909319899245, 107.716796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[156.8816120906801, 221.9341050756901], [203.419921875, 221.9341050756901], [203.419921875, 232.49560546875], [156.8816120906801, 232.49560546875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[69.77099609375, 580.3276936776491], [154.62972292191435, 580.3276936776491], [154.62972292191435, 591.6201171875], [69.77099609375, 591.6201171875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[304.7556675062972, 671.0507569011576], [396.33249370277076, 671.0507569011576], [396.33249370277076, 682.8916015625], [304.7556675062972, 682.8916015625]]}, {"title": "2.1 Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[304.7556675062972, 692.0445235975067], [421.390625, 692.0445235975067], [421.390625, 703.859375], [304.7556675062972, 703.859375]]}, {"title": "2.2 Generative Models", "heading_level": null, "page_id": 2, "polygon": [[304.40234375, 73.284423828125], [419.6020151133501, 73.284423828125], [419.6020151133501, 83.87109375], [304.40234375, 83.87109375]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 549.586821015138], [391.8287153652393, 549.6845703125], [391.8287153652393, 561.1962890625], [304.7556675062972, 561.1962890625]]}, {"title": "3.1 Overview", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 689.7951914514692], [376.0654911838791, 689.7951914514692], [376.0654911838791, 700.9814453125], [304.7556675062972, 700.9814453125]]}, {"title": "3.2 Synthetic Training Data Generation with\nLanguage Model", "heading_level": null, "page_id": 3, "polygon": [[69.05793450881612, 350.89581478183436], [288.396484375, 350.89581478183436], [288.396484375, 375.98095703125], [69.05793450881612, 375.98095703125]]}, {"title": "<bos_i> sentence of class i <eos>.", "heading_level": null, "page_id": 3, "polygon": [[92.32745591939546, 688.2956366874444], [246.2065491183879, 688.2956366874444], [246.2065491183879, 699.748046875], [92.32745591939546, 699.748046875]]}, {"title": "<bos_i> sentence 1 <sep> sentence 2\n<eos>.", "heading_level": null, "page_id": 3, "polygon": [[326.5239294710327, 73.284423828125], [504.62109375, 73.284423828125], [504.62109375, 96.924560546875], [326.5239294710327, 96.924560546875]]}, {"title": "3.3 Training for Gradient Matching", "heading_level": null, "page_id": 3, "polygon": [[304.7556675062972, 283.4158504007124], [481.904282115869, 283.4158504007124], [481.904282115869, 295.193359375], [304.7556675062972, 295.193359375]]}, {"title": "Algorithm 1: Optimization for DiLM", "heading_level": null, "page_id": 4, "polygon": [[73.69970703125, 75.72751558325912], [242.4534005037783, 75.72751558325912], [242.4534005037783, 85.7725830078125], [73.69970703125, 85.7725830078125]]}, {"title": "3.4 Generate Synthetic Dataset", "heading_level": null, "page_id": 5, "polygon": [[69.05793450881612, 220.3671875], [224.43828715365237, 220.3671875], [224.43828715365237, 231.87890625], [69.05793450881612, 231.87890625]]}, {"title": "4 Experimental Settings", "heading_level": null, "page_id": 5, "polygon": [[69.05793450881612, 427.37310774710596], [202.6923828125, 427.37310774710596], [202.6923828125, 439.912109375], [69.05793450881612, 439.912109375]]}, {"title": "5 Results and Discussion", "heading_level": null, "page_id": 5, "polygon": [[304.693359375, 600.5716829919858], [441.3702770780856, 600.5716829919858], [441.3702770780856, 613.41015625], [304.693359375, 613.41015625]]}, {"title": "5.1 Performance for BERT_{BASE}", "heading_level": null, "page_id": 5, "polygon": [[304.7556675062972, 624.5645592163846], [460.88664987405537, 624.5645592163846], [460.88664987405537, 635.611328125], [304.7556675062972, 635.611328125]]}, {"title": "5.2 Cross-model Generalization", "heading_level": null, "page_id": 6, "polygon": [[304.7556675062972, 634.3116651825467], [462.3879093198992, 634.3116651825467], [462.3879093198992, 646.7119140625], [304.7556675062972, 646.7119140625]]}, {"title": "5.3 Analysis and Discussion", "heading_level": null, "page_id": 7, "polygon": [[69.05793450881612, 620.8156723063223], [207.92443324937028, 620.8156723063223], [207.92443324937028, 631.9111328125], [69.05793450881612, 631.9111328125]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[69.05793450881612, 151.399658203125], [147.1234256926952, 151.399658203125], [147.1234256926952, 164.144775390625], [69.05793450881612, 164.144775390625]]}, {"title": "Limitations", "heading_level": null, "page_id": 8, "polygon": [[69.05793450881612, 320.68359375], [131.360201511335, 320.68359375], [131.360201511335, 333.4287109375], [69.05793450881612, 333.4287109375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[305.5062972292191, 124.162109375], [363.30478589420653, 124.162109375], [363.30478589420653, 136.29052734375], [305.5062972292191, 136.29052734375]]}, {"title": "A Background Details of DiLM", "heading_level": null, "page_id": 11, "polygon": [[69.05793450881612, 72.72840605520926], [240.669921875, 72.72840605520926], [240.669921875, 84.0252685546875], [69.05793450881612, 84.0252685546875]]}, {"title": "B Datasets", "heading_level": null, "page_id": 11, "polygon": [[69.05793450881612, 697.292965271594], [135.61328125, 697.292965271594], [135.61328125, 709.2041015625], [69.05793450881612, 709.2041015625]]}, {"title": "C Baselines Details", "heading_level": null, "page_id": 11, "polygon": [[304.40234375, 341.1487088156723], [413.82421875, 341.1487088156723], [413.82421875, 352.751953125], [304.40234375, 352.751953125]]}, {"title": "C.1 Coreset Selection", "heading_level": null, "page_id": 11, "polygon": [[305.5062972292191, 401.88067675868206], [415.5703125, 401.88067675868206], [415.5703125, 412.3662109375], [305.5062972292191, 412.3662109375]]}, {"title": "C.2 Embedding-level Dataset Distillation", "heading_level": null, "page_id": 11, "polygon": [[304.7556675062972, 633.5618878005342], [505.17380352644835, 633.5618878005342], [505.17380352644835, 644.65625], [304.7556675062972, 644.65625]]}, {"title": "D Learner Models", "heading_level": null, "page_id": 12, "polygon": [[68.3073047858942, 197.94122885129119], [174.14609571788412, 197.94122885129119], [174.14609571788412, 210.70556640625], [68.3073047858942, 210.70556640625]]}, {"title": "E Implementation Details", "heading_level": null, "page_id": 12, "polygon": [[69.05793450881612, 557.9072265625], [211.67758186397984, 557.9072265625], [211.67758186397984, 570.2412109375], [69.05793450881612, 570.2412109375]]}, {"title": "F Results for Cross-model Generalization", "heading_level": null, "page_id": 12, "polygon": [[304.40234375, 487.1923828125], [526.191435768262, 487.1923828125], [526.191435768262, 498.7041015625], [304.40234375, 498.7041015625]]}, {"title": "G Distilled Synthetic Data Examples", "heading_level": null, "page_id": 12, "polygon": [[304.7556675062972, 584.826357969724], [502.92191435768257, 584.826357969724], [502.92191435768257, 597.3759765625], [304.7556675062972, 597.3759765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 90], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 195], ["Line", 91], ["Text", 6], ["ListItem", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 100], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 114], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 4], ["ListItem", 3], ["Equation", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1064, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 848], ["Line", 112], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2], ["Reference", 2], ["SectionHeader", 1], ["ListItem", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 99], ["TextInlineMath", 4], ["SectionHeader", 4], ["Text", 4], ["Reference", 4], ["Footnote", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 868], ["TableCell", 228], ["Line", 104], ["Text", 5], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 8084, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 109], ["TableCell", 46], ["Text", 6], ["Reference", 4], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Footnote", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 5428, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 103], ["ListItem", 7], ["Reference", 7], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 113], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 113], ["ListItem", 19], ["Reference", 19], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 103], ["TableCell", 40], ["Text", 10], ["Reference", 6], ["SectionHeader", 5], ["TextInlineMath", 2], ["Footnote", 2], ["Equation", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3973, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["TableCell", 138], ["Line", 97], ["Text", 9], ["Reference", 9], ["SectionHeader", 4], ["Footnote", 4], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 687], ["TableCell", 156], ["Line", 55], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 14896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["TableCell", 63], ["Line", 49], ["Table", 2], ["Reference", 2], ["Caption", 1], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["TableCell", 92], ["Line", 88], ["Table", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6309, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DiLM__Distilling_Dataset_into_Language_Model_for_Text-level_Dataset_Distillation"}