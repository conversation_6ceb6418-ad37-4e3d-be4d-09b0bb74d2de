{"table_of_contents": [{"title": "DiM: Distilling Dataset into Generative Model", "heading_level": null, "page_id": 0, "polygon": [[154.5, 106.5], [439.5, 106.5], [439.5, 119.2060546875], [154.5, 119.2060546875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 243.0], [191.25, 243.0], [191.25, 253.6875], [144.75, 253.6875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 603.0], [126.75, 603.0], [126.75, 614.109375], [48.75, 614.109375]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[307.5, 339.75], [397.5, 339.75], [397.5, 351.333984375], [307.5, 351.333984375]]}, {"title": "2.1. Dataset Distillation.", "heading_level": null, "page_id": 1, "polygon": [[307.5, 359.25], [420.0, 359.25], [420.0, 369.703125], [307.5, 369.703125]]}, {"title": "2.2. Generative Models", "heading_level": null, "page_id": 1, "polygon": [[307.5, 648.0], [417.0, 648.75], [417.0, 659.35546875], [307.5, 659.35546875]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[48.75, 422.25], [102.0, 422.25], [102.0, 433.125], [48.75, 433.125]]}, {"title": "3.1. Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[48.0, 501.0], [132.75, 501.0], [132.75, 511.2421875], [48.0, 511.2421875]]}, {"title": "3.2. Overview of DiM", "heading_level": null, "page_id": 2, "polygon": [[307.5, 564.75], [409.5, 564.75], [409.5, 574.27734375], [307.5, 574.27734375]]}, {"title": "3.3. <PERSON><PERSON><PERSON> into Model", "heading_level": null, "page_id": 3, "polygon": [[48.0, 189.75], [151.0576171875, 189.75], [151.0576171875, 199.93359375], [48.0, 199.93359375]]}, {"title": "3.4. Discussions with GANs and GAN Inversion", "heading_level": null, "page_id": 4, "polygon": [[48.75, 337.5], [273.0, 337.5], [273.0, 348.240234375], [48.75, 348.240234375]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[307.5, 337.5], [387.0, 337.5], [387.0, 348.240234375], [307.5, 348.240234375]]}, {"title": "4.1. Datasets and Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[307.5, 406.5], [501.75, 406.5], [501.75, 416.25], [307.5, 416.25]]}, {"title": "4.2. Comparison to coreset and DiI Methods", "heading_level": null, "page_id": 5, "polygon": [[48.75, 362.25], [257.25, 362.25], [257.25, 372.796875], [48.75, 372.796875]]}, {"title": "4.3. Ablation Studies", "heading_level": null, "page_id": 5, "polygon": [[307.5, 321.0], [405.80859375, 321.0], [405.80859375, 331.41796875], [307.5, 331.41796875]]}, {"title": "5. Conclusion and Discussion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 575.25], [457.20703125, 575.25], [457.20703125, 586.265625], [307.5, 586.265625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 204.0], [106.5, 204.0], [106.5, 215.015625], [48.75, 215.015625]]}, {"title": "6. More Evaluations and Comparisons", "heading_level": null, "page_id": 10, "polygon": [[48.75, 72.75], [247.4296875, 72.75], [247.4296875, 83.4345703125], [48.75, 83.4345703125]]}, {"title": "7. More Visualizations", "heading_level": null, "page_id": 10, "polygon": [[48.0, 455.25], [165.0, 455.25], [165.0, 465.99609375], [48.0, 465.99609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 102], ["Text", 8], ["SectionHeader", 3], ["Figure", 3], ["Reference", 2], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 6120, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 98], ["TableCell", 24], ["Text", 6], ["ListItem", 3], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 960, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 97], ["Text", 5], ["TextInlineMath", 5], ["SectionHeader", 3], ["Equation", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 610], ["Line", 99], ["TableCell", 27], ["Text", 7], ["TextInlineMath", 5], ["Equation", 5], ["Reference", 2], ["SectionHeader", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11318, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 841], ["TableCell", 112], ["Line", 85], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["TableCell", 245], ["Line", 99], ["Text", 7], ["Reference", 4], ["Table", 3], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 17401, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 455], ["TableCell", 186], ["Line", 108], ["Reference", 4], ["Caption", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 16973, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 99], ["TableCell", 60], ["Text", 5], ["TextInlineMath", 4], ["Reference", 3], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5418, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 112], ["Reference", 30], ["ListItem", 29], ["ListGroup", 2], ["Text", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 59], ["ListItem", 19], ["Reference", 19], ["ListGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 243], ["Line", 53], ["TableCell", 20], ["Text", 8], ["SectionHeader", 2], ["Reference", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 35], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1479, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 20], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1698, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/DiM__Distilling_Dataset_into_Generative_Model"}