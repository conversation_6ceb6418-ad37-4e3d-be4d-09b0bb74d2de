{"table_of_contents": [{"title": "Differentially Private Dataset Condensation", "heading_level": null, "page_id": 0, "polygon": [[96.0, 54.75], [522.94921875, 54.75], [522.94921875, 76.18359375], [96.0, 76.18359375]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[132.0, 339.0], [217.248046875, 339.0], [217.248046875, 348.8203125], [132.0, 348.8203125]]}, {"title": "II. <PERSON><PERSON><PERSON><PERSON>OUND AND RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[84.75, 145.5], [264.0, 145.5], [264.0, 155.3642578125], [84.75, 155.3642578125]]}, {"title": "A. Dataset Condensation", "heading_level": null, "page_id": 1, "polygon": [[46.5, 162.75], [153.0, 162.75], [153.0, 171.703125], [46.5, 171.703125]]}, {"title": "B. Differential Privacy", "heading_level": null, "page_id": 1, "polygon": [[47.25, 686.25], [144.75, 686.25], [144.75, 696.8671875], [47.25, 696.8671875]]}, {"title": "III. DIFFERENTALLY PRIVATE DATASET CONDENSATION\n(DPDC)", "heading_level": null, "page_id": 2, "polygon": [[49.0078125, 434.25], [298.529296875, 434.25], [298.529296875, 455.16796875], [49.0078125, 455.16796875]]}, {"title": "A. DPDC Algorithms", "heading_level": null, "page_id": 2, "polygon": [[46.5, 542.953125], [138.0, 542.953125], [138.0, 553.0078125], [46.5, 553.0078125]]}, {"title": "B. Theoretical Results", "heading_level": null, "page_id": 3, "polygon": [[48.0, 456.75], [141.75, 456.75], [141.75, 466.76953125], [48.0, 466.76953125]]}, {"title": "b) Additional Explanation About Releasing Labels:", "heading_level": null, "page_id": 4, "polygon": [[66.33984375, 106.34765625], [294.75, 106.34765625], [294.75, 117.0], [66.33984375, 117.0]]}, {"title": "IV. COMPARISON WITH DPMIX AND DP-MERF", "heading_level": null, "page_id": 4, "polygon": [[67.5, 499.5], [282.0, 499.5], [282.0, 509.30859375], [67.5, 509.30859375]]}, {"title": "V. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[403.5, 438.75], [486.0, 438.75], [486.0, 448.59375], [403.5, 448.59375]]}, {"title": "A. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[317.25, 456.75], [413.876953125, 456.75], [413.876953125, 466.3828125], [317.25, 466.3828125]]}, {"title": "B. Main Results", "heading_level": null, "page_id": 5, "polygon": [[48.0, 555.0], [117.0, 555.0], [117.0, 565.3828125], [48.0, 565.3828125]]}, {"title": "C. Visualization", "heading_level": null, "page_id": 5, "polygon": [[317.25, 587.0390625], [385.5, 587.0390625], [385.5, 596.3203125], [317.25, 596.3203125]]}, {"title": "<PERSON><PERSON> Study on Tabular Data", "heading_level": null, "page_id": 6, "polygon": [[317.25, 566.25], [450.75, 566.25], [450.75, 576.2109375], [317.25, 576.2109375]]}, {"title": "E. Ablation Study on NDPDC", "heading_level": null, "page_id": 7, "polygon": [[48.0, 372.75], [175.412109375, 372.75], [175.412109375, 382.078125], [48.0, 382.078125]]}, {"title": "VI. CONCLUSION", "heading_level": null, "page_id": 7, "polygon": [[402.75, 609.75], [487.08984375, 609.75], [487.08984375, 619.13671875], [402.75, 619.13671875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[146.25, 57.75], [204.0, 57.75], [204.0, 66.56396484375], [146.25, 66.56396484375]]}, {"title": "APPENDIX", "heading_level": null, "page_id": 9, "polygon": [[151.3564453125, 57.75], [198.5712890625, 57.75], [198.5712890625, 67.1923828125], [151.3564453125, 67.1923828125]]}, {"title": "OMITTED PROOF", "heading_level": null, "page_id": 9, "polygon": [[138.0, 76.5], [211.5, 76.5], [211.5, 86.1416015625], [138.0, 86.1416015625]]}, {"title": "ADDITIONAL RELATED WORK", "heading_level": null, "page_id": 9, "polygon": [[378.75, 372.75], [510.0, 372.75], [510.0, 381.498046875], [378.75, 381.498046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 104], ["Text", 11], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 1190], ["Line", 140], ["TextInlineMath", 11], ["Text", 10], ["Equation", 7], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1451], ["Line", 106], ["Text", 9], ["TextInlineMath", 8], ["SectionHeader", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1727], ["Line", 145], ["TextInlineMath", 14], ["Text", 7], ["Equation", 5], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 118], ["TextInlineMath", 6], ["SectionHeader", 4], ["Text", 4], ["Footnote", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["TableCell", 183], ["Line", 108], ["Text", 7], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8145, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["TableCell", 108], ["Line", 63], ["Caption", 9], ["Picture", 6], ["PictureGroup", 6], ["Text", 4], ["Table", 3], ["TableGroup", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 8, "llm_error_count": 1, "llm_tokens_used": 6082, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["TableCell", 144], ["Line", 128], ["Text", 7], ["Caption", 5], ["Table", 4], ["TableGroup", 4], ["SectionHeader", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 9934, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 136], ["ListItem", 45], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1646], ["Line", 173], ["TextInlineMath", 11], ["Equation", 6], ["Text", 4], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 6010, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Differentially Private Dataset Condensation"}