{"table_of_contents": [{"title": "Diffusion Models in Vision: A Survey", "heading_level": null, "page_id": 0, "polygon": [[112.5, 54.140625], [499.640625, 54.140625], [499.640625, 79.6640625], [112.5, 79.6640625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[51.47314453125, 334.5], [140.25, 334.5], [140.25, 345.146484375], [51.47314453125, 345.146484375]]}, {"title": "2 GENERIC FRAMEWORK", "heading_level": null, "page_id": 1, "polygon": [[311.25, 709.5], [444.0, 709.5], [444.0, 720.45703125], [311.25, 720.45703125]]}, {"title": "2.1 Denoising Diffusion Probabilistic Models (DDPMs)", "heading_level": null, "page_id": 2, "polygon": [[311.25, 513.0], [563.25, 513.0], [563.25, 523.23046875], [311.25, 523.23046875]]}, {"title": "Algorithm 1 DDPM sampling method", "heading_level": null, "page_id": 3, "polygon": [[311.25, 42.49072265625], [475.435546875, 42.49072265625], [475.435546875, 53.75390625], [311.25, 53.75390625]]}, {"title": "Output:", "heading_level": null, "page_id": 3, "polygon": [[311.25, 103.5], [345.75, 103.5], [345.75, 113.0185546875], [311.25, 113.0185546875]]}, {"title": "Computation:", "heading_level": null, "page_id": 3, "polygon": [[310.482421875, 131.25], [373.236328125, 131.25], [373.236328125, 140.2822265625], [310.482421875, 140.2822265625]]}, {"title": "2.2 Noise Conditioned Score Networks (NCSNs)", "heading_level": null, "page_id": 3, "polygon": [[310.5, 549.0], [534.75, 549.0], [534.75, 558.421875], [310.5, 558.421875]]}, {"title": "Algorithm 2 Annealed Langevin dynamics", "heading_level": null, "page_id": 4, "polygon": [[311.25, 42.2490234375], [496.0546875, 42.2490234375], [496.0546875, 56.4609375], [311.25, 56.4609375]]}, {"title": "Input:", "heading_level": null, "page_id": 4, "polygon": [[310.5, 55.5], [357.99609375, 55.5], [357.99609375, 66.66064453125], [310.5, 66.66064453125]]}, {"title": "Output:", "heading_level": null, "page_id": 4, "polygon": [[311.25, 105.0], [347.25, 105.0], [347.25, 116.1123046875], [311.25, 116.1123046875]]}, {"title": "Computation:", "heading_level": null, "page_id": 4, "polygon": [[310.482421875, 132.1611328125], [373.5, 132.1611328125], [373.5, 142.5], [310.482421875, 142.5]]}, {"title": "2.3 Stochastic Differential Equations (SDEs)", "heading_level": null, "page_id": 4, "polygon": [[310.5, 411.0], [517.5, 411.0], [517.5, 421.13671875], [310.5, 421.13671875]]}, {"title": "2.4 Relation to Other Generative Models", "heading_level": null, "page_id": 5, "polygon": [[46.5, 708.75], [235.5, 708.75], [235.5, 718.91015625], [46.5, 718.91015625]]}, {"title": "3 A CATEGORIZATION OF DIFFUSION MODELS", "heading_level": null, "page_id": 6, "polygon": [[47.25, 513.0], [283.5, 513.0], [283.5, 523.23046875], [47.25, 523.23046875]]}, {"title": "3.1 Unconditional Image Generation", "heading_level": null, "page_id": 6, "polygon": [[311.25, 105.75], [481.5, 105.75], [481.5, 116.208984375], [311.25, 116.208984375]]}, {"title": "3.1.1 Denoising Diffusion Probabilistic Models", "heading_level": null, "page_id": 6, "polygon": [[311.25, 191.25], [513.0, 191.25], [513.0, 200.70703125], [311.25, 200.70703125]]}, {"title": "TABLE 1", "heading_level": null, "page_id": 7, "polygon": [[288.75, 44.25], [323.25, 44.25], [323.25, 53.25], [288.75, 53.25]]}, {"title": "IEEE TRANSACTIONS ON PATTERN ANALYSIS AND <PERSON><PERSON><PERSON><PERSON> INTELLIGENCE, VOL. 14, NO. 8, AUGUST 2022 10", "heading_level": null, "page_id": 9, "polygon": [[46.5, 26.25], [564.75, 26.25], [564.75, 35.360595703125], [46.5, 35.360595703125]]}, {"title": "3.1.2 Score-Based Generative Models", "heading_level": null, "page_id": 10, "polygon": [[311.25, 237.75], [480.75, 237.0], [480.75, 247.88671875], [311.25, 247.88671875]]}, {"title": "3.1.3 Stochastic Differential Equations", "heading_level": null, "page_id": 10, "polygon": [[311.25, 675.75], [480.75, 675.75], [480.75, 685.5], [311.25, 685.5]]}, {"title": "3.2 Conditional Image Generation", "heading_level": null, "page_id": 11, "polygon": [[310.78125, 106.5], [471.0, 106.5], [471.0, 116.1123046875], [310.78125, 116.1123046875]]}, {"title": "3.2.1 Denoising Diffusion Probabilistic Models", "heading_level": null, "page_id": 11, "polygon": [[310.78125, 191.25], [513.0, 191.25], [513.0, 200.513671875], [310.78125, 200.513671875]]}, {"title": "3.2.2 Score-Based Generative Models", "heading_level": null, "page_id": 12, "polygon": [[47.25, 491.25], [216.75, 491.25], [216.75, 501.1875], [47.25, 501.1875]]}, {"title": "3.2.3 Stochastic Differential Equations", "heading_level": null, "page_id": 12, "polygon": [[47.25, 596.25], [216.0, 596.25], [216.0, 605.98828125], [47.25, 605.98828125]]}, {"title": "3.3 Image-to-Image Translation", "heading_level": null, "page_id": 13, "polygon": [[46.5, 44.25], [195.0, 44.25], [195.0, 53.25], [46.5, 53.25]]}, {"title": "3.4 Text-to-Image Synthesis", "heading_level": null, "page_id": 13, "polygon": [[45.75, 699.0], [181.5, 699.0], [181.5, 708.08203125], [45.75, 708.08203125]]}, {"title": "3.5 Image Super-Resolution", "heading_level": null, "page_id": 14, "polygon": [[46.5, 207.75], [180.0, 207.75], [180.0, 217.916015625], [46.5, 217.916015625]]}, {"title": "3.6 Image Editing", "heading_level": null, "page_id": 14, "polygon": [[46.5, 375.75], [133.5, 375.75], [133.5, 385.365234375], [46.5, 385.365234375]]}, {"title": "3.7 Image Inpainting", "heading_level": null, "page_id": 14, "polygon": [[311.25, 93.75], [410.25, 93.75], [410.25, 104.02734375], [311.25, 104.02734375]]}, {"title": "3.8 Image Segmentation", "heading_level": null, "page_id": 14, "polygon": [[310.78125, 333.0], [428.25, 333.0], [428.25, 342.24609375], [310.78125, 342.24609375]]}, {"title": "3.9 Multi-Task Approaches", "heading_level": null, "page_id": 14, "polygon": [[310.78125, 641.25], [438.75, 641.25], [438.75, 650.84765625], [310.78125, 650.84765625]]}, {"title": "3.10 Medical Image Generation and Translation", "heading_level": null, "page_id": 16, "polygon": [[46.5, 651.75], [267.0, 651.75], [267.0, 660.90234375], [46.5, 660.90234375]]}, {"title": "3.11 Anomaly Detection in Medical Images", "heading_level": null, "page_id": 16, "polygon": [[311.25, 570.75], [510.0, 570.75], [510.0, 580.078125], [311.25, 580.078125]]}, {"title": "3.12 Video Generation", "heading_level": null, "page_id": 17, "polygon": [[46.5, 396.0], [155.25, 396.0], [155.25, 405.66796875], [46.5, 405.66796875]]}, {"title": "3.13 Other Tasks", "heading_level": null, "page_id": 17, "polygon": [[311.25, 112.5], [393.75, 111.0], [393.75, 121.**********], [311.25, 121.**********]]}, {"title": "3.14 Theoretical Contributions", "heading_level": null, "page_id": 18, "polygon": [[45.75, 603.75], [192.0, 603.75], [192.0, 613.72265625], [45.75, 613.72265625]]}, {"title": "4 CLOSING REMARKS AND FUTURE DIRECTIONS", "heading_level": null, "page_id": 18, "polygon": [[47.14013671875, 696.75], [296.736328125, 696.75], [296.736328125, 706.921875], [47.14013671875, 706.921875]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 19, "polygon": [[47.25, 196.5], [153.0, 196.5], [153.0, 206.89453125], [47.25, 206.89453125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 19, "polygon": [[46.5, 271.4765625], [116.25, 271.4765625], [116.25, 281.91796875], [46.5, 281.91796875]]}, {"title": "APPENDIX A\nVARIATIONAL BOUND.", "heading_level": null, "page_id": 23, "polygon": [[47.25, 42.75], [155.25, 42.75], [155.25, 67.5791015625], [47.25, 67.5791015625]]}, {"title": "APPENDIX B\nNOISE ESTIMATION.", "heading_level": null, "page_id": 23, "polygon": [[311.25, 558.421875], [412.083984375, 558.421875], [412.083984375, 583.9453125], [311.25, 583.9453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 85], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9805, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 101], ["Text", 20], ["Picture", 17], ["Caption", 4], ["PictureGroup", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 17, "llm_error_count": 0, "llm_tokens_used": 9836, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 689], ["Line", 91], ["Text", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1063, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1384], ["Line", 137], ["TextInlineMath", 12], ["Reference", 6], ["SectionHeader", 4], ["Text", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1179], ["Line", 170], ["TextInlineMath", 9], ["Equation", 7], ["Text", 6], ["Reference", 6], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 622], ["Line", 126], ["TableCell", 25], ["Text", 8], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Equation", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3619, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 118], ["Text", 14], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 656], ["TableCell", 540], ["Line", 89], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Text", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 634], ["TableCell", 475], ["Line", 112], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 22201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 604], ["TableCell", 350], ["Line", 131], ["Text", 4], ["SectionHeader", 1], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 21640, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 353], ["Line", 121], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 120], ["Text", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 120], ["Text", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 121], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 115], ["Text", 13], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 122], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 119], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 120], ["Text", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 119], ["Text", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 495], ["Line", 147], ["ListItem", 42], ["Reference", 42], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 153], ["ListItem", 51], ["Reference", 46], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 586], ["Line", 155], ["ListItem", 51], ["Reference", 51], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 135], ["ListItem", 23], ["Reference", 23], ["Text", 9], ["Picture", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2275, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 1576], ["Line", 192], ["Equation", 10], ["Reference", 9], ["Text", 8], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 7, "llm_error_count": 0, "llm_tokens_used": 8680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 464], ["Line", 62], ["TextInlineMath", 6], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Diffusion Models in Vision- A Survey"}