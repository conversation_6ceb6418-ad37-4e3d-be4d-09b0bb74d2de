# Distill Gold from Massive Ores: Bi-level Data Pruning towards Efficient Dataset Distillation

Y[u](https://orcid.org/0000-0001-7489-7269)e <PERSON><sup>on1</sup>, <PERSON><PERSON><PERSON> L[i](https://orcid.org/0000-0003-0478-0692)<sup>ont×</sup>, <PERSON><PERSON><PERSON><sup>on[1](https://orcid.org/0009-0006-7709-8271)</sup>, <PERSON><PERSON><PERSON>[g](https://orcid.org/0009-0006-4480-6374)<sup>on1</sup>, Cew[u](https://orcid.org/0000-0003-1533-8576) Lu $\mathbb{O}^1$ , Yu-W[i](https://orcid.org/0000-0002-3148-0380)n[g](https://orcid.org/0000-0001-7155-2919) Tai $\mathbb{O}^2$ , and <PERSON>-Keung Tang $\mathbb{O}^3$ 

<sup>1</sup> Shanghai Jiao Tong University {silicxuyue,yonglu\_li,ckt0704,wang<PERSON><PERSON>i2021,lucewu}@sjtu.edu.cn, <EMAIL> <sup>2</sup> <NAME_EMAIL> <sup>3</sup> Hong Kong University of Science <NAME_EMAIL>

Abstract. Data-efficient learning has garnered significant attention, especially given the current trend of large multi-modal models. Recently, dataset distillation has become an effective approach by synthesizing data samples that are essential for network training. However, it remains to be explored which samples are essential for the dataset distillation process itself. In this work, we study the data efficiency and selection for the dataset distillation task. By re-formulating the dynamics of distillation, we provide insight into the inherent redundancy in the real dataset, both theoretically and empirically. We propose to use the empirical loss value as a static data pruning criterion. To further compensate for the variation of the data value in training, we find the most contributing samples based on their causal effects on the distillation. The proposed selection strategy can efficiently exploit the training dataset, outperform the previous SOTA distillation algorithms, and consistently enhance the distillation algorithms, even on much larger-scale and more heterogeneous datasets, e.g., full ImageNet-1K and Kinetics-400. We believe this paradigm will open up new avenues in the dynamics of distillation and pave the way for efficient dataset distillation. Our code is available on <https://github.com/silicx/GoldFromOres-BiLP> .

Keywords: Dataset Distillation · Data Pruning

# 1 Introduction

Data is the core of deep learning. In the era of large data and models [\[29,](#page-15-0)[44,](#page-16-0)[52\]](#page-16-1) as their size and complexity continue to grow, data-efficient learning is increasingly crucial for achieving high performance with a limited computing budget. Techniques such as pruning, quantization, and knowledge distillation have been developed to reduce model size without sacrificing performance. Recently, *dataset* 

<sup>⋆</sup> Corresponding author.

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image illustrates a machine learning process involving CIFAR10 horse data. On the left, a scatter plot shows data points, with several highlighted regions representing different data selections: 'Full real data', 'Random 10%', 'Our selected 10%', and 'Our selected 0.04%'. Each selection is associated with an image of a horse and a performance metric (e.g., 28.6±0.2, 28.5±0.3, 30.0±0.2, 28.4±0.2). Arrows indicate a process of 'Distill' and 'Classify'. The right side of the image depicts a meta-learning framework. It shows 'Real Data' with various animal icons (kangaroo, kiwi, dog, dolphin, horse) and 'Synthetic Data' with abstract creature icons. A meta-learning loss function, denoted as 'ℒmeta', is applied to both real and synthetic data. Gradients are calculated and used in an equation: ITE(horse) = Gw/ - Gw/o, suggesting an analysis of feature importance or model behavior.

Fig. 1: (1) Left: Severe data redundancy in the dataset distillation process. Taking CIFAR10 and DC [\[63\]](#page-17-0) as an example, pruning 90% of the real data does not reduce the performance. With our proposed empirical loss criterion, only 0.04% real samples are sufficient for distillation and 10% samples outperform the full real dataset. (2) Right: Building upon the empirical loss, we propose to dynamically select samples by their ITE value, which is the difference of meta gradient with/without the sample.

distillation [\[54\]](#page-16-2) has emerged as a promising approach toward data-efficient AI, where a small and condensed dataset (namely *synthetic* dataset) is learned from the whole large dataset (namely real dataset), to maximize the performance of models trained on distilled synthetic data.

Currently, the existing distillation algorithms focus on the evolution of the matching strategies for real and synthetic data [\[3,](#page-14-0) [43,](#page-16-3) [47,](#page-16-4) [62,](#page-17-1) [63\]](#page-17-0) or refining and accelerating the bi-level optimization [\[33,](#page-15-1) [34,](#page-15-2) [40,](#page-16-5) [54\]](#page-16-2). However, preliminary experiments show that the real dataset could be so redundant during the dataset distillation itself that pruning part of the dataset will not reduce the distillation performance (as shown in Fig. [1\)](#page-1-0). Therefore, an appropriate approach to finding the most valuable real data would help the distillation algorithms to learn the most key data patterns and knowledge. Currently, it is still under-explored how to analyze the samples' value on the dataset distillation task and efficiently select the real dataset. Recently, YOCO [\[12\]](#page-14-1) proposes an interesting task of pruning the synthetic dataset. DREAM [\[31\]](#page-15-3) prunes the real data within each batch from the aspect of feature distribution. In comparison, we would principally analyze the intrinsic worth of each real data sample and its relevance to the distillation optimization process from a more holistic perspective.

In this work, we revisit the selection and utilization of real datasets for dataset distillation. We first describe the dataset distillation as the matching of training dynamics on real and synthetic data, where the dynamics are formulated with the neural tangent kernel (NTK) [\[15\]](#page-14-2). Further analysis shows that the small scale of synthetic data motivates pruning those real data samples with large empirical loss values. The stability of NTK for wide neural networks also allows for dropping these real samples at the beginning of dataset distillation, which indicates redundancy in real data and offers efficiency. To support the theoretical analysis, we conduct comprehensive experiments of pruning real data before distillation. In most scenarios, pruning some real data does not reduce the distillation performance. For instance, with CIFAR10 for DC [\[63\]](#page-17-0) and single instance-per-class (IPC=1), removing over  $99.9\%$  of the real data does not affect its performance. We argue that leveraging redundancy in dataset distillation is more significant than regular machine learning applications since synthetic datasets usually possess low capacity, as shown in Fig. [1.](#page-1-0)

However, pruning real data before distillation does not consistently enhance the distillation performance, which implies that the value or utility of the real samples could *vary* during the training process. To identify the importance of real samples, we measure their contribution to the learning of the synthetic data from the perspective of causal effect. We compare the synthetic gradient with or without each real sample, which is essentially the individual treatment effect (ITE) of the real samples for the dataset distillation task. Based on the principles, we propose a new and concise real data sampling algorithm, which can be implemented as a plug-and-play for most dataset distillation methods. In comprehensive experiments, our approach can thus efficiently use the key samples and consistently enhance the base algorithm.

Overall, our contributions are: 1) In-depth insight on data redundancy for dataset distillation. 2) Two core principles for real sample selection, supported both theoretically and empirically. 3) A plug-and-play selection strategy for practical dataset distillation to enhance SOTA methods.

## 2 Related Work

Dataset Distillation is a process of condensing a large dataset into a smaller and more representative dataset while maintaining the performance, which has been applied to various domains including images, text [\[35,](#page-15-4) [36\]](#page-15-5), videos [\[55\]](#page-16-6), graph [\[16,](#page-14-3) [17\]](#page-14-4), time series [\[9,](#page-14-5) [32\]](#page-15-6), medical [\[27,](#page-15-7) [28\]](#page-15-8) and multimodal data [\[56,](#page-16-7) [57\]](#page-16-8), etc. Existing approaches can be roughly classified into: 1) Meta-Model Matching maintains the transferability of the synthetic data, by optimizing the loss on the original dataset of models trained on the synthetic data. In [\[54\]](#page-16-2) the task of data distillation and the meta-model matching framework was first proposed. In [\[40\]](#page-16-5) kernel ridge regression was exploited to reduce computational cost and is further extended to infinite wide networks [\[41\]](#page-16-9). In [\[65\]](#page-17-2) the optimization of synthetic data/classifier and feature extractor was separated. In [\[34\]](#page-15-2) the metagradient was computed by exploiting implicit gradients. 2) Gradient Matching aligns the gradients of the synthetic and real datasets. It was proposed in [\[63\]](#page-17-0) and further improved in [\[61\]](#page-17-3) to perform the same image augmentations on both the real and synthetic data. 3) Distribution Matching, where [\[62\]](#page-17-1) matches feature distributions of the synthetic and real data, which is simple but effective. In [\[53\]](#page-16-10) layer-wise feature alignment and early exit conditions are used to promote it. In [\[64\]](#page-17-4) it was further enhanced with regularizers and model pool. 4) Trajectory Matching: In [\[3\]](#page-14-0) the authors proposed MTT to match the training trajectory of the model parameters and in [\[6\]](#page-14-6) the memory consumption of MTT was reduced and label learning was used. 5) Factorization of synthetic data can reduce the storage burden and share knowledge among instances. For example, [\[21\]](#page-15-9) uses a

strategy of putting multiple images on one synthetic sample. [\[8\]](#page-14-7) decomposes the synthetic data to the linear network hallucinators and bases, while [\[30\]](#page-15-10) uses a convolutional network. [\[26\]](#page-15-11) maintains a smaller base space to further reduce the storage. [\[46\]](#page-16-11) employs frequency domain factorization. 6) Bayesian Pseudocoreset is a family of algorithms that learn the synthetic data with Bayesian inference [\[20,](#page-15-12) [37,](#page-15-13) [50\]](#page-16-12). Beyond these categories, SRe2L [\[59\]](#page-17-5) utilizes a 3-stage learning paradigm to decouple the segregation of inner-loop and outer-loop optimization.

Data Selection/Pruning reduces the training data without significantly affecting performance. Classic data selection often calculates a scalar utility score for each sample based on predefined criteria [\[2,](#page-14-8) [45,](#page-16-13) [51\]](#page-16-14) and filters samples based on scores. Some data pruning methods also consider the interaction between samples. For example, [\[58\]](#page-17-6) examines generalization influence to reduce training data, which aims to identify the smallest subset to satisfy the expected generalization ability. In comparison, data distillation [\[54\]](#page-16-2) synthesizes new and smaller data, and significantly outperforms data pruning with the same data storage.

### 3 Preliminaries

#### 3.1 Formulation of Dataset Distillation

Before delving deeper into the real data selection, we would investigate the training dynamics of dataset distillation. Given a real dataset  $\mathcal{D}_r = \{x_r^{(i)}, y_r^{(i)}\}_{i=1}^{M_r}$ , dataset distillation is to learn a synthetic dataset  $\mathcal{D}_s = \{x_s^{(i)}, y_s^{(i)}\}_{i=1}^{M_s}$  that has smaller size  $(M_s \ll M_r)$  and could train a network to similar performance to full real dataset on specific task. More formally, let  $u = f(x; \theta)$  be a scalar output network <sup>[4](#page-3-0)</sup> and  $\mathcal{L}(\mathcal{D}, \theta) = \sum_{i=1}^{M} \ell(u^{(i)}, y^{(i)})$  be the loss function that measures the model empirical risk on a dataset  $\mathcal{D}$ . The dynamics of gradient descent optimization are described by differential equations:

$$
\dot{\theta}_r = -\frac{\partial \mathcal{L}(\mathcal{D}_r, \theta)}{\partial \theta} = -\sum_{i=1}^{M_r} \frac{\partial \ell(u_r^{(i)}, y_r^{(i)})}{\partial u_r^{(i)}} \frac{\partial u_r^{(i)}}{\partial \theta},
$$

$$
\dot{\theta}_s = -\frac{\partial \mathcal{L}(\mathcal{D}_s, \theta)}{\partial \theta} = -\sum_{i=1}^{M_s} \frac{\partial \ell(u_s^{(i)}, y_s^{(i)})}{\partial u_s^{(i)}} \frac{\partial u_s^{(i)}}{\partial \theta},
$$
 $(1)$ 

where  $\theta_r(t)$  and  $\theta_s(t)$  are functions of timestamp t which describe the training trajectory of parameters on the real or synthetic dataset. We omit " $(t)$ " for clarity.  $\dot{\theta}$  indicates its derivative w.r.t  $t: \frac{\partial \theta}{\partial t}, \dot{\theta}_s = \frac{\partial \theta_s}{\partial t}$ , and as a common process, the learning rate of gradient descent is absorbed to timestamp  $t$  for simplicity. The outputs of both networks on the real dataset are computed for evaluation:

<span id="page-3-1"></span>
$$
u_r^{(i)} = f(x_r^{(i)}; \theta_r), \ \ u_s^{(i)} = f(x_s^{(i)}; \theta_s). \tag{2}
$$

Note that  $u_r^{(i)}$  and  $u_s^{(i)}$  are also function of t.

The current dataset distillation can be categorized into two types:

<span id="page-3-0"></span><sup>&</sup>lt;sup>4</sup> The analysis covers scalar functions, but can also generalize to vector functions.

(1) Empirical Risk Minimization includes DD [\[54\]](#page-16-2), KIP [\[40,](#page-16-5) [41\]](#page-16-9) and their variants [\[33,](#page-15-1)[34\]](#page-15-2), which minimize the empirical risk on the real dataset of the network trained on synthetic data:

$$
S = \underset{S}{\arg\min} \sum_{i=1}^{M_r} \ell(u_s^{(i)}|_{t=+\infty}, y_r^{(i)}).
$$
 (3)

(2) Training Dynamics Matching is a simpler agent task for dataset distillation since the converged network  $\theta_s^{(i)}|_{t=+\infty}$  is usually intractable for bi-level optimization. Its ultimate target is to match the whole training trajectory on the real and synthetic datasets:

<span id="page-4-0"></span>
$$
S = \underset{S}{\arg\min} D(u_s^{(i)}(t), u_r^{(i)}(t)),\tag{4}
$$

where  $D$  is a distance metric. The strategies include gradient trajectory matching  $[3, 21, 31, 63]$  $[3, 21, 31, 63]$  $[3, 21, 31, 63]$  $[3, 21, 31, 63]$ , feature matching  $[53, 62, 64]$  $[53, 62, 64]$  $[53, 62, 64]$ , loss matching  $[47]$ , *etc.* 

The generation-based methods [\[4,](#page-14-9) [59\]](#page-17-5) are exceptions since they do not involve the network dynamics.

### 3.2 Redundancy in Dataset Distillation

Currently, the intrinsic value of each real sample under the dataset distillation scenario has not been thoroughly investigated, yet we observe the redundancy within the real dataset which underscores the potential significance of this research area. We randomly drop some portions of the real data to find their maximum pruning ratio that could maintain the distillation accuracy. Comprehensive experiments are conducted on various datasets, networks, distillation algorithms, initialization methods, and synthetic data sizes (represented by instance-per-class, IPC). We use the default parameters of each method, which are detailed in the supplementary material. As shown in Tab. [1,](#page-5-0) severe data redundancy widely exists in various dataset distillation settings. We will analyze and propose selection criteria dedicated to dataset distillation in the following.

## 4 Methodology

### 4.1 Empirical Loss Pruning

We study the real data selection of dataset distillation based on the previous formulation. Considering that  $\dot{u} = \langle \frac{\partial u}{\partial \theta}, \dot{\theta} \rangle$ , the dynamics of outputs can be depicted by differential equations:

<span id="page-4-1"></span>
$$
\dot{u}_r^{(i)} = -\sum_{j=1}^{M_r} \frac{\partial \ell(u_r^{(j)}, y_r^{(j)})}{\partial u_r^{(j)}} \langle \frac{\partial u_r^{(i)}}{\partial \theta_r}, \frac{\partial u_r^{(j)}}{\partial \theta_r} \rangle = -\sum_{j=1}^{M_r} \frac{\partial \ell(u_r^{(j)}, y_r^{(j)})}{\partial u_r^{(j)}} K_r^{(ij)},
$$

$$
\dot{u}_s^{(i)} = -\sum_{j=1}^{M_s} \frac{\partial \ell(u_s^{(j)}, y_s^{(j)})}{\partial u_s^{(j)}} \langle \frac{\partial u_s^{(i)}}{\partial \theta_s}, \frac{\partial u_s^{(j)}}{\partial \theta_s} \rangle = -\sum_{j=1}^{M_s} \frac{\partial \ell(u_s^{(j)}, y_s^{(j)})}{\partial u_s^{(j)}} K_s^{(ij)},
$$
(5)

| Dataset          |    | IPC | DC [63] | DSA [61] | DM [62] | MTT [3] | IDC [21] | IDM [64] |
|------------------|----|-----|---------|----------|---------|---------|----------|----------|
| CIFAR10[22]      | 1  | 90% | 85%     | 85%      | 60%     | 50%     | 40%      |          |
|                  | 10 | 70% | 70%     | 60%      | 10%     | 30%     | 10%      |          |
|                  | 50 | 50% | 70%     | 50%      | 20%     | 40%     | 20%      |          |
| CIFAR100[22]     | 1  | 50% | 40%     | 70%      | 20%     | 60%     | 50%      |          |
|                  | 10 | 50% | 40%     | 50%      | 10%     | 50%     | 20%      |          |
| SVHN[39]         | 1  | 60% | 60%     | 85%      | 90%     | 60%     | 30%      |          |
|                  | 10 | 80% | 70%     | 95%      | 80%     | 30%     | 5%       |          |
| TinyImageNet[24] | 11 | 40% | 30%     | 60%      | 50%     | 40%     | 60%      |          |
|                  | 10 | 50% | 50%     | 50%      | 20%     | 30%     | 50%      |          |

<span id="page-5-0"></span>Table 1: Maximum pruning ratio with random selection among different datasets, algorithms and synthetic data size.

where  $K_r$  and  $K_s$  are neural tangent kernels [\[15\]](#page-14-2) (NTK), and matching the derivatives  $\dot{u}_r$  and  $\dot{u}_s$  is a sufficient and necessary condition of target Eq. [\(4\)](#page-4-0).

Based on the formulation of dataset distillation dynamics, we notice that  $K_r^{(ij)}$  is rank-deficient since synthetic data is much smaller than the real dataset. Since  $\dot{u}^i$  is the linear combination of kernel matrix  $K$ , the rank-deficiency of  $\mathbf{K}_r^{(ij)}$  leads to the mismatch of complexity of  $\dot{u}_s^i$  and  $\dot{u}_r^i$ , making synthetic dynamics  $\dot{u}_s^i$  hard to approximate to  $\dot{u}_r^i$ . So we argue that **the real dataset has** significant redundancy for dataset distillation. To reduce the optimization difficulty for a more stable distillation process, and also for a more data-efficient distillation algorithm, the dataset distillation needs to select the most valuable real data samples. A more intuitive explanation is that the small synthetic data cannot memorize the larger scale real dataset, so selecting the important but easy samples will help the algorithm.

Furthermore, Eq. [\(5\)](#page-4-1) also reveals that samples with small gradient value  $\|\frac{\partial \ell(u_r,y_r)}{\partial u}\|$  $\frac{u_r, y_r}{\partial u_r}$  are more important for distillation, since pruning the samples with large gradient is equivalent to reduce the kernel rank of real data. Note that for common loss functions like MSE or cross-entropy, the  $\|\frac{\partial \ell(u_r, y_r)}{\partial u_r}\|$  $\frac{u_r, y_r}{\partial u_r}$  has the same monotonicity to  $\ell(u_r, y_r)$ , so we directly adopt the empirical loss  $\ell(u_r, y_r)$ as our real data selection criterion to avoid additional computation. Please refer to the supplementary for the proofs.

Early Pruning. A principal property of the neural tangent kernels is they tend to be constant for wide neural networks. So in Eq. [\(5\)](#page-4-1), the NTK for real data  $K_r$  is stable during training. So the empirical loss can be regarded as a static criterion which does not vary during training. In practice, to maximize the efficiency, we prune the real data in one time before the beginning of the distillation algorithm rather than select different samples at each iteration. The empirical loss of each sample is computed by training the network on the real dataset for multiple trials and taking the average loss value.

| Dataset           |    | IPC   | DC [63] | DSA [61] | DM [62] | MTT [3] | IDC [21] | RFAD [33] | IDM [64] |
|-------------------|----|-------|---------|----------|---------|---------|----------|-----------|----------|
| CIFAR10 [22]      | 1  | 99.5% | 99.5%   | 99.5%    | 60%     | 85%     | 70%      | 30%       |          |
|                   | 10 | 30%   | 60%     | 50%      | 20%     | 50%     | 20%      | 10%       |          |
|                   | 50 | 70%   | 85%     | 40%      | 20%     | 30%     | 20%      | 30%       |          |
| CIFAR100 [22]     | 1  | 95%   | 97%     | 99.5%    | 60%     | 80%     | 80%      | 70%       |          |
|                   | 10 | 90%   | 80%     | 90%      | 20%     | 60%     | 80%      | 20%       |          |
| SVHN [39]         | 1  | 80%   | 95%     | 99%      | 99%     | 40%     | 20%      | 60%       |          |
|                   | 10 | 20%   | 60%     | 85%      | 95%     | 50%     | 5%       | 10%       |          |
| TinyImageNet [24] | 1  | 97%   | 99%     | 99.5%    | 70%     | 40%     | -        | 95%       |          |
|                   | 10 | 80%   | 70%     | 97%      | 30%     | 40%     | -        | 60%       |          |

<span id="page-6-0"></span>Table 2: Maximum pruning ratio with empirical loss selection among different datasets, algorithms and synthetic data sizes.

<span id="page-6-1"></span>Image /page/6/Figure/3 description: The image is a plot showing the maximum pruning ratio in percentage on the y-axis. The x-axis represents different data points or experiments. There are several vertical green arrows indicating increases in the pruning ratio, and some red arrows pointing downwards, indicating decreases. Gray dots are also present, possibly representing baseline values or intermediate points. The pruning ratio generally increases across the x-axis, with some fluctuations and notable drops indicated by the red arrows.

Fig. 2: The comparison of maximum pruning ratio between random selection and empirical loss-based selection: the arrows point from random to the loss-based method. Green: selection by loss is superior; Red: selection by loss is inferior to random.

Empirical Justification Besides the theoretical analysis, we conduct extensive experiments to support our selection strategy. To examine the effectiveness of pruning, we compare the maximum pruning ratio that maintains the distillation performance, and a larger maximum pruning ratio indicates a larger redundancy.

Comprehensive comparison experiments on various datasets, networks, distillation algorithms, initialization methods, and synthetic data sizes (represented by instance-per-class, IPC) are given in Tab. [2.](#page-6-0) We take the mean and standard deviation of the accuracy of 5 random removal trials. We use the default parameters of each method, which are detailed in the supplementary. The results show that severe data redundancy widely exists in various dataset distillation settings. For many datasets and algorithms, less than 30% samples are sufficient for dataset distillation. We also visualize the comparison between random selection and empirical loss selection in Fig. [2.](#page-6-1) Pruning with empirical loss consistently outperforms random selection. We also find dropping data does not drop the cross-architecture transferability in supplementary.

Surprisingly, we also observe that dropping real data can sometimes improve performance. We show the best performance during data pruning in Tab. [3.](#page-7-0) In almost all cases, the data selection can notably promote distillation accuracy. This implies that some samples may be "detrimental" to the distillation and the empirical loss is a proper criterion to cancel these negative impacts. This

<span id="page-7-0"></span>Table 3: Best performance with data dropping. The performance difference between the full real dataset and pruned dataset are shown in parentheses (†: compare to our reproduced accuracy).

| Dataset                | <b>IPC</b>     | DC [63]                                                                                                                                                                                          | $DSA$ [61] | DM [62] | MTT [3]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | RFAD [33] | IDM $[64]$                                     |
|------------------------|----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|---------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------|------------------------------------------------|
| CIFAR10<br>$[22]$      | $\overline{1}$ |                                                                                                                                                                                                  |            |         | $30.0 \pm 0.2$ (+1.7) $30.9 \pm 0.1$ (+2.6) $29.7 \pm 0.3$ (+3.7) $46.3 \pm 0.8$ (+0.2) $53.7 \pm 0.9$ (+0.1) $46.3 \pm 0.4$ (+0.7)<br>$10\mid 44.9\pm 0.4\mid +0.0$ $52.4\pm 0.2\mid +0.2$ $50.0\pm 0.2\mid +1.1$ $65.7\pm 0.3\mid +0.4$ $66.7\pm 0.2\mid +0.4$ $58.9\pm 0.3\mid +0.3$<br>$50\left 54.9\pm0.5\left(+1.0\right)\right.$ $61.5\pm0.7\left(+0.9\right)\left.63.4\pm0.2\left(+0.4\right)\right.$ $72.0\pm0.4\left(+0.4\right)\left.71.9\pm0.2\left(+0.8\right)\right.$ $67.8\pm0.2\left(+0.3\right)$ |           |                                                |
| CIFAR100<br>$[22]$     |                |                                                                                                                                                                                                  |            |         | $14.1\pm0.1$ (+1.3) $15.6\pm0.1$ (+1.7) $14.9\pm0.5$ (+3.5) $24.6\pm0.4$ (+0.3) $30.4\pm0.6$ (+4.1) $25.9\pm0.3$ (+5.8)<br>$10 26.5\pm0.3(+1.3)$ $32.5\pm0.4(+0.2)$ $32.4\pm0.3(+2.7)$ $40.1\pm0.5(+0.0)$ $38.4\pm0.2(+6.4)$ $45.9\pm0.1(+0.8)$                                                                                                                                                                                                                                                                   |           |                                                |
| <b>SVHN</b> [39]       | <sup>1</sup>   |                                                                                                                                                                                                  |            |         | $32.2\pm0.5$ (+1.0) $28.9\pm1.3$ (+0.1) $29.8\pm0.5$ (+6.3 <sup>†</sup> ) $43.0\pm1.1$ (+3.2 <sup>†</sup> ) $53.0\pm0.2$ (+0.8 <sup>†</sup> ) $71.9\pm1.0$ (+1.6)<br>$10\begin{array}{ l} 76.2 \pm 0.6 \ (-0.1) \end{array}$ 80.0 $\pm$ 0.8 (+0.8) 74.6 $\pm$ 0.3 (+0.9 <sup>†</sup> ) 78.1 $\pm$ 0.5 (+0.9 <sup>†</sup> ) 74.2 $\pm$ 0.2 (+0.1 <sup>†</sup> ) 81.9 $\pm$ 0.5 (+0.1)                                                                                                                              |           |                                                |
| TinyImage-<br>Net [24] | $\mathbf{1}$   | $\vert$ 4.9±0.1 (+0.2 <sup>†</sup> ) 4.3±0.0 (+0.6 <sup>†</sup> ) 4.8±0.1 (+0.9) 9.0±0.4 (+0.2)<br>10 $ 12.8\pm0.0(+0.2^{\dagger}) 14.8\pm0.4(+2.3^{\dagger}) 17.5\pm0.1(+4.6) 23.8\pm0.3(+0.6)$ |            |         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | $\sim$    | $11.4 \pm 0.2 (+1.3)$<br>$22.9 \pm 0.4 (+1.0)$ |

observation inspires new approaches that leverage data utility and exploit all data samples in different quality, enabling future analysis of network dynamics and dataset distillation.

#### 4.2 Causal Effect on Synthetic Data

However, the pruning lacks availability in some scenarios when IPC is large on certain algorithms, indicating that sample importance may vary at different stages of the training process; some initially pruned samples can be beneficial in the latter stage. So to take a step further, we investigate the real samples' contribution to the synthetic data during training as compensation for the static empirical loss criterion.

We first generalize the optimization targets of various dataset distillations into a meta-loss function  $\mathcal{L}_{meta}(\mathcal{D}_r, \mathcal{D}_s)$ . E.g., the meta-loss of DC [\[63\]](#page-17-0) is the cosine distance between the gradients of real and synthetic; the meta-loss of DM  $[62]$  is the MMD between real and synthetic features. The synthetic data  $\mathcal{D}_s$  is learned by gradient descent on the meta-loss function. To examine the causal effects of real samples on the updating and learning of synthetic data, we could observe the consequence on the meta gradient after applying causal intervention on the real dataset, *i.e.*, removing a certain real sample. More specifically, we regard the presence of real samples  $x_r \in \mathcal{D}_r$  as some binary "treatment" (T), and the meta gradient of synthetic data  $\frac{\partial L_{\text{meta}}}{\partial \mathcal{D}_s}$  as some "effect"  $(Y)$ . Thus for a certain individual  $\mathcal{D}_s$ , the causal effect of each real sample to the distillation can be quantized by the individual treatment effect (ITE) [\[42\]](#page-16-16):

<span id="page-7-1"></span>
$$
ITE(x_r) = Y_{T=1} - Y_{T=0} = \frac{\partial L_{meta}(\mathcal{D}_r, \mathcal{D}_s)}{\partial \mathcal{D}_s} - \frac{\partial L_{meta}(\mathcal{D}_r \setminus \{x_r\}, \mathcal{D}_s)}{\partial \mathcal{D}_s}, \quad (6)
$$

which is the difference between the gradient on the synthetic dataset with or without the real sample  $x_r$ , and can be obtained on any dataset distillation algorithm by computing two meta gradients for each real sample. We use the L2 norm of ITE gradient  $||ITE(x_r)||_2$  as the causal criterion of a real sample  $x_r$ . We remove the samples with both the smallest and the largest ITE criterion values since the small ITE value samples contribute little to the synthetic learning, while the large ITE value samples could be outliers for the distillation, which enhances the variance and instability during training. The ablation analysis in Sec. [5.4](#page-10-0) also shows the samples with intermediate-level ITE are more important to the distillation task.

Due to the heavy computation of the meta gradient, we apply three optimization techniques for applicable efficiency.

Taylor Approximation of ITE. The causal criterion needs additional meta gradient computations for each of the  $K$  real samples (the second term in Eq.  $(6)$ ), therefore increasing the training time by nearly K times. Thankfully, we can reduce the computation burden on a special family of meta loss functions that are *additive variable separable:*  $\mathcal{L}_{meta}(G, \mathcal{D}_s)$ ,  $G = \sum_{x \in \mathcal{D}_r} g(x)$ , which covers most of the meta matching algorithms, e.g.,  $g(x)$  indicates per-sample gradient for DC [\[63\]](#page-17-0), sample feature vector for DM [\[62\]](#page-17-1). In this case, we could leverage the Taylor approximation for ITE value:

<span id="page-8-1"></span>
$$
ITE(x_r) = \frac{\partial}{\partial D_s} [\mathcal{L}_{meta}(G, D_s) - \mathcal{L}_{meta}(G - g(x_r), D_s)]
$$

$$
= \frac{\partial}{\partial D_s} [(\frac{\partial L_{meta}(G, D_s)}{\partial G})^{\top} g(x_r) + h.o.t]
$$

$$
\approx \frac{\partial^2 L_{meta}(G, D_s)}{\partial D_s \partial G} \cdot g(x_r), 
(7)
$$

where we vectorize G,  $g(x)$ , and  $\mathcal{D}_s$  for clarity. The first term is part of the Hessian matrix of  $\mathcal{L}_{meta}$  and not related to specific real sample  $x_r$  so it could be computed in one backward pass. The second term has to be computed during the forward pass of meta loss. So we could compute the ITE values for the real batch with one meta gradient computation and a matrix multiplication, which significantly reduces the pruning time. We also analyze the error of the approximation in the supplementary and ignoring high order terms only brings 3% shifting of the sample ranking that is negligible for pruning.

Estimation of Global ITE Distribution. We hope to prune the samples with small ITE values among the full real dataset, but it is computationally expensive to exhaustively evaluate ITE values of the full dataset at each iteration, especially with mini-batch optimization methods. Inspired by batch normaliza-tion [\[14\]](#page-14-10), we maintain the global running mean  $\hat{\mu}$  and variance  $\hat{\sigma}^2$  of ITE values of the real dataset. For each mini-batch, we compute the mean  $\mu_B$  and variance  $\sigma_B^2$  within the batch, and update the global statistics by:

<span id="page-8-0"></span>
$$
\hat{\mu} \leftarrow (1 - \eta)\hat{\mu} + \eta \mu_B, \quad \hat{\sigma}^2 \leftarrow (1 - \eta)\hat{\sigma}^2 + \eta \sigma_B^2. \tag{8}
$$

Then, given a pruning ratio  $\beta$ , we find the two quantile points  $z_{\frac{\beta}{2}}$  and  $z_{1-\frac{\beta}{2}}$  of the normal distribution  $z \sim \mathcal{N}(\hat{\mu}, \hat{\sigma}^2)$ , and select the samples with ITE value within  $(z_{\frac{\beta}{2}}, z_{1-\frac{\beta}{2}})$ , so that the overall selection rate is  $1-\beta$ .

**Lazy Selection**. We prune and update the real data batch every several iterations to reduce the overhead of selection.

<span id="page-9-0"></span>

#### Algorithm 1 Bi-Level Data Pruning (BiLP)

**Input:** Real dataset  $\mathcal{D}_r$ , preemptive pruning rate  $\alpha$ , adaptive pruning rate  $\beta$ , meta loss function  $\mathcal{L}_{meta}$ 

**Output:** Synthetic dataset  $\mathcal{D}_s$ ,

1: Train networks on full dataset  $\mathcal{D}_r$  and record the average per-sample empirical loss  $\ell(x_r)$ ,  $\forall x_r \in \mathcal{D}_r$  with Eq. [\(2\)](#page-3-1),

2:  $\mathcal{D}_r' \leftarrow \{x_r \mid \ell(x_r) < \tau, x_r \in \mathcal{D}_r\}, \; s.t. \; \; |\mathcal{D}_r'| = (1-\alpha)|\mathcal{D}_r| \; \text{(preemptive pruning)},$ 

- 3: Initialize synthetic dataset  $\mathcal{D}_s$ ,
- 4: Initialize running mean  $\hat{\mu}$  and variance  $\hat{\sigma}^2$  of ITE,
- 5: repeat
- 6: Sample a mini-batch  $\mathcal{B}_r$  from real dataset,
- 7: Compute meta loss  $\mathcal{L}_{meta}(\mathcal{B}_r, \mathcal{D}_s)$ ,
- 8: Compute meta loss after causal intervention  $\mathcal{L}_{meta}(\mathcal{B}_r \setminus \{x_r\}, \mathcal{D}_s)$ ,  $\forall x_r \in \mathcal{B}_r$ ,
- 9: Compute ITE values of real data  $ITE(x_r)$ ,  $\forall x_r \in \mathcal{B}_r$ ,
- 10: Update running stats  $\hat{\mu}$ ,  $\hat{\sigma}^2$  with Eq. [\(8\)](#page-8-0),
- $11:$  $C'_r \gets \{x_r \mid z_{\frac{\beta}{2}} < ITE(x_r) < z_{1-\frac{\beta}{2}}, z \sim \mathcal{N}(\hat{\mu}, \hat{\sigma}^2)\}$  (adaptive pruning),
- 12: Update synthetic data:  $\mathcal{D}_s \leftarrow \mathcal{D}_s \frac{\partial}{\partial \mathcal{D}_s} \mathcal{L}_{meta}(\mathcal{B}'_r, \mathcal{D}_s)$ .
- 13: until convergence

The three optimizations could reduce the ITE computation to  $1/60,000$  of the original time. Overall, our method only increases the training time by 7%.

### 4.3 Bi-level Data Pruning for Dataset Distillation

In the analysis and discussion above, we propose two levels of data selection: the preemptive pruning by sample-wise empirical loss which is applied before the distillation process, and *adaptive* pruning by causal effect metric which is used during the distillation. We combine the two levels to propose a plug-and-play data pruning algorithm whose pseudo-code is shown in Algorithm [1.](#page-9-0)

## 5 Experiments

### 5.1 Datasets and Metrics

In this work, the experiments are conducted on common datasets for dataset distillation task, including CIFAR10 [\[22\]](#page-15-14)  $(60,000 \text{ 32x32 images in } 10 \text{ classes}),$ CIFAR100 [\[22\]](#page-15-14) (60,000 32x32 images in 100 classes), SVHN [\[39\]](#page-16-15) (over 99,000  $32\times32$  images in 10 classes) and TinyImageNet [\[24\]](#page-15-15) (100,000 64x64 images in 200 classes). We report the top-1 classification accuracy.

### 5.2 Implementation Details

We apply BiLP on gradient-matching based SOTAs DC [\[63\]](#page-17-0) and IDC [\[21\]](#page-15-9), so  $q(x)$ in Eq. [\(7\)](#page-8-1) indicates the per-sample gradient of the classification task. The multiformation factor is 2 by default to fairly compare to the IDC baseline, and we also

| Method<br>IPC            | CIFAR10 [22]   |                |                | CIFAR100 [22]  |                | SVHN [39]      |                |                |
|--------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                          | 1              | 10             | 50             | 1              | 10             | 1              | 10             | 50             |
| Full Dataset             |                | $84.8 \pm 0.1$ |                |                | $56.2 \pm 0.3$ |                | $95.4 \pm 0.1$ |                |
| Random                   | $14.4 \pm 2.0$ | $26.0 \pm 1.2$ | $43.4 \pm 1.0$ | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $14.6 \pm 1.6$ | $35.1 \pm 4.1$ | $70.9 \pm 0.9$ |
| Herding                  | $21.5 \pm 1.2$ | $31.6 \pm 0.7$ | $40.4 \pm 0.6$ | $8.4 \pm 0.3$  | $17.3 \pm 0.3$ | $20.9 \pm 1.3$ | $50.5 \pm 3.3$ | $72.6 \pm 0.8$ |
| DC [63]                  | $28.3 \pm 0.5$ | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ | $12.8 \pm 0.3$ | $25.2 \pm 0.3$ | $31.2 \pm 1.4$ | $76.1 \pm 0.6$ | $82.3 \pm 0.3$ |
| KIP [40]                 | $49.9 \pm 0.2$ | $62.7 \pm 0.3$ | $68.6 \pm 0.2$ | $15.7 \pm 0.2$ | $28.3 \pm 0.1$ | $57.3 \pm 0.1$ | $75.0 \pm 0.1$ | $80.5 \pm 0.1$ |
| MTT [3]                  | $46.3 \pm 0.8$ | $65.6 \pm 0.7$ | $71.6 \pm 0.2$ | $24.3 \pm 0.3$ | $40.1 \pm 0.4$ |                |                |                |
| FRePo [65]               | $46.8 \pm 0.7$ | $65.5 \pm 0.4$ | $71.7 \pm 0.2$ | $28.7 \pm 0.1$ | $42.5 \pm 0.2$ |                |                |                |
| HaBa [30]                | $48.3 \pm 0.8$ | $69.9 \pm 0.4$ | $74.0 \pm 0.2$ | $33.4 \pm 0.4$ | $40.2 \pm 0.2$ | $69.8 \pm 1.3$ | $83.2 \pm 0.4$ | $88.3 \pm 0.1$ |
| IDC [21]                 | $50.6 \pm 0.4$ | $67.5 \pm 0.5$ | $74.5 \pm 0.1$ |                | $45.1 \pm 0.4$ | $68.5 \pm 0.9$ | $87.5 \pm 0.3$ | $90.1 \pm 0.1$ |
| RFAD-NN [33]             | $53.6 \pm 1.2$ | $66.3 \pm 0.5$ | $71.1 \pm 0.4$ | $26.3 \pm 1.1$ | $33.0 \pm 0.3$ | $52.2 \pm 2.2$ | $74.9 \pm 0.4$ | $80.9 \pm 0.3$ |
| IDM [64]                 | $45.6 \pm 0.7$ | $58.6 \pm 0.1$ | $67.5 \pm 0.1$ | $20.1 \pm 0.3$ | $45.1 \pm 0.1$ |                |                |                |
| Zhang <i>et al.</i> [60] | 49.2           | 67.1           | 73.8           | 29.8           | 45.6           |                |                |                |
| DREAM [31]               | $51.1 \pm 0.3$ | $69.4 \pm 0.4$ | $74.8 \pm 0.1$ | $29.5 \pm 0.3$ | $46.8 \pm 0.7$ | $69.8 \pm 0.8$ | $87.9 \pm 0.4$ | $90.5 \pm 0.1$ |
| PDD [5]                  |                | $67.9 \pm 0.2$ | $76.5 \pm 0.4$ |                | $45.8 \pm 0.5$ |                |                |                |
| BiLP+DC                  | $30.5 \pm 0.3$ | $45.2 \pm 0.4$ | $54.9 \pm 0.3$ | $13.7 \pm 0.7$ | $26.0 \pm 0.5$ | $32.2 \pm 0.3$ | $76.4 \pm 0.5$ | $82.8 \pm 0.6$ |
| BiLP+IDC                 | $51.5 \pm 0.3$ | $69.4 \pm 0.5$ | $75.4 \pm 0.2$ | $30.1 \pm 0.4$ | $47.2 \pm 0.6$ | $70.3 \pm 0.6$ | $88.3 \pm 0.1$ | $90.8 \pm 0.4$ |
| BiLP+IDC (x3)            | $55.9 \pm 0.5$ | $69.8 \pm 1.1$ | $76.9 \pm 0.9$ | $34.0 \pm 0.7$ | $48.0 \pm 1.0$ | $77.2 \pm 0.6$ | $88.7 \pm 0.4$ | $91.0 \pm 0.7$ |

<span id="page-10-1"></span>Table 4: Dataset distillation performance of state-of-the-art and the proposed BiLP.

show the results with factor 3 ( $BiLP+IDC(x3)$  in Tab. [4\)](#page-10-1). All the experiments including the efficiency analysis are conducted on one single RTX4090. For more details please refer to the supplementary.

#### 5.3 Results

We compare our BiLP to various baselines in Tab. [4.](#page-10-1) Our selection strategy could notably enhance the current distillation algorithms. On average, BiLP consistently enhances DC and IDC by 0.8% and 1.2% (BiLP+DC and BiLP+IDC), especially on more diversified dataset CIFAR100. Moreover, with a larger multiformation factor (BiLP+IDC x3), our method could surpass most of the stateof-the-art. Note that our method is also efficient due to the preemptive pruning, e.g., BiLP on CIFAR100 only required  $50\%$  samples of the real dataset. This experiment shows the feasibility of embedding the data selection mechanism into the current distillation paradigm to boost performance and enhance efficiency, especially the preemptive pruning before the distillation.

#### 5.4 Ablation Study

<span id="page-10-0"></span>Pruning Criteria We analyze the impact of different pruning criteria in Table [5,](#page-11-0) involving the preemptive pruning by empirical loss and adaptive pruning with causal effects in two directions (prune the samples with large ITE or small ITE). The results indicate that across all datasets and IPC settings, the full BiLP consistently achieves the highest accuracy. In addition, both preemptive

| Preemptive Pruning | x        | ✓        | ✓        | ✓        | ✓                |
|--------------------|----------|----------|----------|----------|------------------|
| Prune Small ITE    | x        | x        | x        | ✓        | ✓                |
| Prune Large ITE    | x        | x        | ✓        | x        | ✓                |
| CIFAR10, IPC=1     | 50.6±0.4 | 51.0±0.3 | 51.2±0.3 | 51.0±0.3 | <b>51.3</b> ±0.3 |
| CIFAR10, IPC=10    | 67.5±0.5 | 68.5±0.1 | 68.9±0.5 | 68.7±0.4 | <b>69.2</b> ±0.1 |
| CIFAR100, IPC=1    | 28.2±0.6 | 29.1±0.8 | 29.8±0.2 | 29.4±0.2 | <b>30.1</b> ±0.4 |

<span id="page-11-0"></span>Table 5: Ablation study on the pruning criteria on CIFAR10 and CIFAR100.

<span id="page-11-1"></span>Table 6: Comparison of training time with ITE computation methods. We report the training seconds per iteration (s/iter) on CIFAR10 and IPC=10.

| Running Stats Estimation | $\times$ | $\checkmark$ | $\checkmark$ | $\checkmark$ |
|--------------------------|----------|--------------|--------------|--------------|
| Taylor Approximation     | $\times$ | $\times$     | $\checkmark$ | $\checkmark$ |
| Lazy Selection           | $\times$ | $\times$     | $\times$     | $\checkmark$ |
| ITE Computation (s/iter) | 1799.22  | 23.03        | 0.27         | 0.03         |
| Total Iteration (s/iter) | 1799.53  | 23.38        | 0.65         | 0.47         |

and adaptive pruning brings performance gain. Interestingly, pruning large ITE values demonstrates a slightly better performance than pruning small ITE values, achieving the highest accuracy of 69.2%. This could imply that removing larger ITE may be more beneficial in scenarios where the synthetic data's complexity is limited. But overall, applying pruning of both large and small ITE would outperform any pruning in a single direction.

Time Complexity Since the computation time of ITE would pose a major bottleneck to the algorithm efficiency of BiLP, we offer a comparative analysis of the training time per iteration for different optimization levels in Table [6.](#page-11-1) The experiment is conducted on the CIFAR10 dataset with IPC=10 on IDC [\[21\]](#page-15-9). Without any optimization, the computation of ITE takes 5,800x more time than the rest steps of the distillation due to its computational intensity on the multiple meta gradients. The running estimation of global ITE distribution would help the mini-batch-based optimization. The Taylor approximation could effectively reduce the computation time by 98.8%. With the lazy selection (update the data per 10 iterations like [\[31\]](#page-15-3)), our full optimization techniques could reduce the training overhead to ignorable 7% of the original algorithm.

<span id="page-11-2"></span>Computation of Empirical Loss Criterion For the preemptive pruning, we train the networks on the full dataset and take the per-sample loss after convergence as the empirical loss criterion. However, we find that the loss values in very early classification epochs are also informative enough for data selection, probably as the early dynamics of samples can reflect their training difficulty and importance. Moreover, the number of trials to obtain loss values has little influence on the selection performance.

<span id="page-12-0"></span>Image /page/12/Figure/1 description: This image contains four line graphs, each plotting accuracy (%) against trials (S1 to S10). The x-axis labels indicate the number of epochs and the overall accuracy: 100 epochs (69.3% accuracy), 10 epochs (51.9% accuracy), 5 epochs (44.8% accuracy), and 1 epoch (32.4% accuracy). Each graph displays five lines representing different trial counts: 'Full data' (a horizontal yellow line around 29%), '50 Trials' (orange line with circles), '10 Trials' (pink line with crosses), '5 Trials' (teal line with diamonds), and '1 Trial' (purple line with squares). In the first graph (100 epochs), all trial-based lines closely follow the 'Full data' line, reaching an accuracy of around 30%. The subsequent graphs show a divergence, with lower epoch counts resulting in lower overall accuracies and more pronounced differences between the trial counts. Specifically, as the number of epochs decreases, the accuracy generally drops, and the lines for fewer trials tend to lag behind the 'Full data' line.

Fig. 3: Stratified distillation performance of different classification trials and epochs numbers. An ideal stratification yields a monotonously increasing performance curve.

We use a stratified experiment to examine the two factors: we sort the samples from large to small by the loss value and stratify them into 10 partitions S1∼S10, where S1 contains the samples with the largest loss values. We take the average loss curves of 1, 5, 10, and 50 trials of classification, and take the mean loss value for the first 1, 5, 10, and 100 epochs when the training converges at around 100 epochs. As shown in Fig. [3](#page-12-0) by the empirical results with DC [\[63\]](#page-17-0) on CIFAR10, IPC=1, most loss values produce a good stratification and could distinguish the important samples unless only train the network for 1 epoch. Therefore we argue that early epoch loss in very few trials is also accurate for selection, which can be utilized with reduced computational burden of generating loss values, and can also be ignored or naturally embedded in the distillation process itself, extending our paradigm to broader applications.

#### 5.5 Extended Discussion and Limitation

Efficient Distillation of Large-scale Datasets Our data utility paradigm can be efficiently extended to larger-scale and more heterogeneous datasets. We apply the data utility selection to the distillation of ImageNet-1K [\[7\]](#page-14-12) and scale up to IPC=50, and also the large-scale video dataset Kinetics-400 [\[1\]](#page-14-13) (detailed in supplementary). The results are listed in Tab. [7.](#page-13-0) Most methods struggle with high IPC due to demanding GRAM, except DM which allows class-separate training. MTT is extremely expensive for large-scale data due to its expert training. Our BiLP could mitigate training costs by its preemptive pruning, which significantly reduces the training time by at most 60% while maintaining or enhancing the performance. It is especially suitable for large-scale scenarios when the size increases, whose signal-to-noise ratio continues to decrease.

Higher-order Interaction of Data Utility Both our proposed preemptive and adaptive pruning is based on per-sample criteria, i.e. we assume each sample independently contributes to the distillation process. However, high-order information and interactions between samples may exist, and they have more complex causal mechanisms to the data synthesizing than the individual ITE in our method. In some extreme scenarios, these interactions are not negligible. For

| Dataset                              |    | [PC Algorithm]                |                                    | Full Data<br>Accuracy Training Time                                |                                                   | BiLP<br>Accuracy Training Time                 | Random Real     |
|--------------------------------------|----|-------------------------------|------------------------------------|--------------------------------------------------------------------|---------------------------------------------------|------------------------------------------------|-----------------|
| $ImageNet-1K [7]$<br>$(1.3M$ images) | 1  | DC [63]<br>DM [62]<br>MTT [3] | $1.79 \pm 0.04$<br>$1.58 \pm 0.11$ | 23.6 <sub>h</sub><br>22.4 <sub>h</sub><br>$205.0h$ ( <i>est.</i> ) | $2.02 + 0.10$<br>$1.95 + 0.12$<br>$2.10 \pm 0.08$ | 17.3h<br>9.9 <sub>h</sub><br>31.2 <sub>h</sub> | $0.43 \pm 0.02$ |
|                                      | 10 | DM 62                         | $3.86 \pm 0.16$                    | 24.7 <sub>h</sub>                                                  | $5.21 + 0.11$                                     | 15.3 <sub>h</sub>                              | $1.57 \pm 0.21$ |
|                                      | 50 | DM [62]                       | $8.22 \pm 0.86$                    | 35.0 <sub>h</sub>                                                  | $9.41 \pm 0.38$                                   | 20.6 <sub>h</sub>                              | $5.29 \pm 0.70$ |
| Kinetics-400 [1]<br>$(300K$ videos)  |    | DM [62]<br>MTT [3]            | $2.78 \pm 0.14$                    | 37.3h<br>$460.8h$ ( <i>est.</i> )                                  | $2.92 \pm 0.15$<br>$2.77 + 0.21$                  | 29.6 <sub>h</sub><br>76.8h                     | $0.90 \pm 0.23$ |
|                                      | 10 | DM 62                         | $9.48 \pm 0.15$                    | 43.5 <sub>h</sub>                                                  | $9.70 \pm 0.12$                                   | 32.2 <sub>h</sub>                              | $3.33 \pm 0.43$ |

<span id="page-13-0"></span>Table 7: Dataset distillation on large-scale image and video datasets (est.: estimated).

example, data diversity is a higher-order data utility indicator since it is a property of the population rather than an individual. We observe the data selection paradigm always performs poorly on low diversity datasets like MNIST, which is due to the diversity vanishing in the small loss samples. We conduct similar stratified experiments as Sec. [5.4](#page-11-2) and use the quotient of intraclass variance and interclass variance as the diversity metric (a large value indicates larger diversity). As shown in Fig. [4,](#page-13-1) only MNIST severely drops the diversity for the subgroups with a small loss. It can be challenging to incorporate sample interactions like diversity into consideration without

<span id="page-13-1"></span>Image /page/13/Figure/4 description: A line graph displays the diversity metric across ten stages (S1-S10) for four different datasets: MNIST, SVHN, CIFAR100, and CIFAR10. The y-axis represents diversity, ranging from 0.3 to 1.0. The MNIST dataset shows a steadily decreasing trend in diversity, starting at approximately 0.88 and ending around 0.52. The SVHN dataset also shows a decreasing trend, beginning at about 0.98 and finishing near 0.79. The CIFAR100 dataset follows a similar pattern, starting at roughly 0.98 and declining to approximately 0.85. The CIFAR10 dataset exhibits the highest diversity throughout, starting at 0.99 and ending around 0.87. The legend indicates that the purple line represents MNIST, the green line represents SVHN, the light green line represents CIFAR100, and the yellow line represents CIFAR10.

Fig. 4: The diversity of each stratified subgroup. MNIST suffers from diversity vanishing.

sacrificing efficiency (e.g. use annealing or Monte-Carlo algorithms), so we leave it to future work. In-depth discussion and modeling of high-order data utility involve complex systems which is beyond our scope. It is worth noting that the impact of diversity vanishing can be negligible in most realistic scenarios  $(e.g.,$ CIFAR), especially for large-scale datasets due to their large overall diversity. We provide the stratified visualization in the supplementary material.

# 6 Conclusion

This paper introduces a novel bi-level data pruning approach for efficient dataset distillation, which leverages the inherent redundancy in large datasets. We propose preemptive pruning from the dynamics of distillation, which is applied before the distillation, accompanied by further adaptive pruning based on causal effects. Our method would consistently enhance the performance of distillation algorithms while reducing the computational burden. We believe that our findings will inspire future research to explore more sophisticated data utility models and optimization techniques, ultimately leading to more efficient and effective dataset distillation methods.

# Acknowledgments

This work is supported in part by the National Natural Science Foundation of China under Grants No.62306175.

# References

- <span id="page-14-13"></span>1. Carreira, J., Zisserman, A.: Quo vadis, action recognition? a new model and the kinetics dataset. In: CVPR (2017)
- <span id="page-14-8"></span>2. Castro, F.M., Marin-Jimenez, M.J., Guil, N., Schmid, C., Alahari, K.: End-to-end incremental learning. In: ECCV (2018)
- <span id="page-14-0"></span>3. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: CVPR (2022)
- <span id="page-14-9"></span>4. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Generalizing dataset distillation via deep generative prior. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3739–3748 (2023)
- <span id="page-14-11"></span>5. Chen, X., Yang, Y., Wang, Z., Mirzasoleiman, B.: Data distillation can be like vodka: Distilling more times for better quality. arXiv preprint arXiv:2310.06982 (2023)
- <span id="page-14-6"></span>6. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. arXiv preprint arXiv:2211.10586 (2022)
- <span id="page-14-12"></span>7. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A large-scale hierarchical image database. In: CVPR (2009)
- <span id="page-14-7"></span>8. Deng, Z., Russakovsky, O.: Remember the past: Distilling datasets into addressable memories for neural networks. arXiv preprint arXiv:2206.02916 (2022)
- <span id="page-14-5"></span>9. Ding, J., Liu, Z., Zheng, G., Jin, H., Kong, L.: CondTSF: One-line plugin of dataset condensation for time series forecasting. arXiv preprint arXiv:2406.02131 (2024)
- <span id="page-14-16"></span>10. Guo, Z., Wang, K., Cazenavette, G., Li, H., Zhang, K., You, Y.: Towards lossless dataset distillation via difficulty-aligned trajectory matching. arXiv preprint arXiv:2310.05773 (2023)
- <span id="page-14-14"></span>11. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-14-1"></span>12. He, Y., Xiao, L., Zhou, J.T.: You only condense once: Two rules for pruning condensed datasets. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-14-15"></span>13. Huang, G., Liu, Z., Van Der Maaten, L., Weinberger, K.Q.: Densely connected convolutional networks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 4700–4708 (2017)
- <span id="page-14-10"></span>14. Ioffe, S., Szegedy, C.: Batch normalization: Accelerating deep network training by reducing internal covariate shift. In: International conference on machine learning. pp. 448–456. pmlr (2015)
- <span id="page-14-2"></span>15. Jacot, A., Gabriel, F., Hongler, C.: Neural tangent kernel: Convergence and generalization in neural networks. Advances in neural information processing systems 31 (2018)
- <span id="page-14-3"></span>16. Jin, W., Tang, X., Jiang, H., Li, Z., Zhang, D., Tang, J., Ying, B.: Condensing graphs via one-step gradient matching. In: Proceedings of the ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD) (2022)
- <span id="page-14-4"></span>17. Jin, W., Zhao, L., Zhang, S., Liu, Y., Tang, J., Shah, N.: Graph condensation for graph neural networks. In: Proceedings of the International Conference on Learning Representations (ICLR) (2022)

- 16 Y. Xu et al.
- <span id="page-15-16"></span>18. Killamsetty, K., Durga, S., Ramakrishnan, G., De, A., Iyer, R.: Grad-match: Gradient matching based data subset selection for efficient deep model training. In: International Conference on Machine Learning. pp. 5464–5474. PMLR (2021)
- <span id="page-15-17"></span>19. Killamsetty, K., Sivasubramanian, D., Ramakrishnan, G., Iyer, R.: Glister: Generalization based data subset selection for efficient and robust learning. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 35, pp. 8110–8118 (2021)
- <span id="page-15-12"></span>20. Kim, B., Choi, J., Lee, S., Lee, Y., Ha, J.W., Lee, J.: On divergence measures for bayesian pseudocoresets. arXiv preprint arXiv:2210.06205 (2022)
- <span id="page-15-9"></span>21. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: ICML (2022)
- <span id="page-15-14"></span>22. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images (2009)
- <span id="page-15-19"></span>23. Krizhevsky, A., Sutskever, I., Hinton, G.E.: Imagenet classification with deep convolutional neural networks. Communications of the ACM 60(6), 84–90 (2017)
- <span id="page-15-15"></span>24. Le, Y., Yang, X.: Tiny imagenet visual recognition challenge. CS 231N 7(7), 3 (2015)
- <span id="page-15-18"></span>25. LeCun, Y., Bottou, L., Bengio, Y., Haffner, P.: Gradient-based learning applied to document recognition. Proceedings of the IEEE 86(11), 2278–2324 (1998)
- <span id="page-15-11"></span>26. Lee, H.B., Lee, D.B., Hwang, S.J.: Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494 (2022)
- <span id="page-15-7"></span>27. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Soft-label anonymous gastric x-ray image distillation. In: Proceedings of the IEEE International Conference on Image Processing (ICIP). pp. 305–309 (2020)
- <span id="page-15-8"></span>28. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. Computer Methods and Programs in Biomedicine 227, 107189 (2022)
- <span id="page-15-0"></span>29. Li, J., Li, D., Savarese, S., Hoi, S.: Blip-2: Bootstrapping language-image pretraining with frozen image encoders and large language models. arXiv preprint arXiv:2301.12597 (2023)
- <span id="page-15-10"></span>30. Liu, S., Wang, K., Yang, X., Ye, J., Wang, X.: Dataset distillation via factorization. arXiv preprint arXiv:2210.16774 (2022)
- <span id="page-15-3"></span>31. Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., You, Y.: Dream: Efficient dataset distillation by representative matching. arXiv preprint arXiv:2302.14416 (2023)
- <span id="page-15-6"></span>32. Liu, Z., Hao, K., Zheng, G., Yu, Y.: Dataset condensation for time series classification via dual domain matching. In: Proceedings of the ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD) (2024)
- <span id="page-15-1"></span>33. Loo, N., Hasani, R., Amini, A., Rus, D.: Efficient dataset distillation using random feature approximation. arXiv preprint arXiv:2210.12067 (2022)
- <span id="page-15-2"></span>34. Loo, N., Hasani, R., Lechner, M., Rus, D.: Dataset distillation with convexified implicit gradients. arXiv preprint arXiv:2302.06755 (2023)
- <span id="page-15-4"></span>35. Maekawa, A., Kobayashi, N., Funakoshi, K., Okumura, M.: Dataset distillation with attention labels for fine-tuning bert. In: Proceedings of the Annual Meeting of the Association for Computational Linguistics (ACL). pp. 119–127 (2023)
- <span id="page-15-5"></span>36. Maekawa, A., Kosugi, S., Funakoshi, K., Okumura, M.: DiLM: Distilling dataset into language model for text-level dataset distillation. In: Proceedings of the Annual Conference of the North American Chapter of the Association for Computational Linguistics (NAACL) (2024)
- <span id="page-15-13"></span>37. Manousakas, D., Xu, Z., Mascolo, C., Campbell, T.: Bayesian pseudocoresets. In: NeurIPS (2020)

- <span id="page-16-17"></span>38. Mirzasoleiman, B., Bilmes, J., Leskovec, J.: Coresets for data-efficient training of machine learning models. In: International Conference on Machine Learning. pp. 6950–6960. PMLR (2020)
- <span id="page-16-15"></span>39. Netzer, Y., Wang, T., Coates, A., Bissacco, A., Wu, B., Ng, A.Y.: Reading digits in natural images with unsupervised feature learning (2011)
- <span id="page-16-5"></span>40. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression. arXiv preprint arXiv:2011.00050 (2020)
- <span id="page-16-9"></span>41. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. In: NeurIPS (2021)
- <span id="page-16-16"></span>42. Rubin, D.B.: Causal inference using potential outcomes: Design, modeling, decisions. Journal of the American Statistical Association 100(469), 322–331 (2005)
- <span id="page-16-3"></span>43. Sajedi, A., Khaki, S., Amjadian, E., Liu, L.Z., Lawryshyn, Y.A., Plataniotis, K.N.: Datadam: Efficient dataset distillation with attention matching. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 17097–17107 (2023)
- <span id="page-16-0"></span>44. Schuhmann, C., Vencu, R., Beaumont, R., Kaczmarczyk, R., Mullis, C., Katta, A., Coombes, T., Jitsev, J., Komatsuzaki, A.: Laion-400m: Open dataset of clip-filtered 400 million image-text pairs. arXiv preprint arXiv:2111.02114 (2021)
- <span id="page-16-13"></span>45. Sener, O., Savarese, S.: A geometric approach to active learning for convolutional neural networks. ArXiv  $abs/1708.00489$  (2017)
- <span id="page-16-11"></span>46. Shin, D., Shin, S., Moon, I.C.: Frequency domain-based dataset distillation. arXiv preprint arXiv:2311.08819 (2023)
- <span id="page-16-4"></span>47. Shin, S., Bae, H., Shin, D., Joo, W., Moon, I.C.: Loss-curvature matching for dataset selection and condensation. In: International Conference on Artificial Intelligence and Statistics. pp. 8606–8628. PMLR (2023)
- <span id="page-16-18"></span>48. Simonyan, K., Zisserman, A.: Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556 (2014)
- <span id="page-16-19"></span>49. Tan, M., Le, Q.: Efficientnet: Rethinking model scaling for convolutional neural networks. In: International conference on machine learning. pp. 6105–6114. PMLR (2019)
- <span id="page-16-12"></span>50. Tiwary, P., Shubham, K., Kashyap, V., et al.: Constructing bayesian pseudocoresets using contrastive divergence. arXiv preprint arXiv:2303.11278 (2023)
- <span id="page-16-14"></span>51. Toneva, M., Sordoni, A., des Combes, R.T., Trischler, A., Bengio, Y., Gordon, G.J.: An empirical study of example forgetting during deep neural network learning. ArXiv abs/1812.05159 (2018)
- <span id="page-16-1"></span>52. Touvron, H., Lavril, T., Izacard, G., Martinet, X., Lachaux, M.A., Lacroix, T., Rozière, B., Goyal, N., Hambro, E., Azhar, F., et al.: Llama: Open and efficient foundation language models. arXiv preprint arXiv:2302.13971 (2023)
- <span id="page-16-10"></span>53. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: Cafe: Learning to condense dataset by aligning features. In: CVPR (2022)
- <span id="page-16-2"></span>54. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-16-6"></span>55. Wang, Z., Xu, Y., Lu, C., Li, Y.L.: Dancing with still images: Video distillation via static-dynamic disentanglement. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 6296–6304 (2024)
- <span id="page-16-7"></span>56. Wu, X., Zhang, B., Deng, Z., Russakovsky, O.: Vision-language dataset distillation. arXiv preprint arXiv:2308.07545 (2023)
- <span id="page-16-8"></span>57. Xu, Y., Lin, Z., Qiu, Y., Lu, C., Li, Y.L.: Low-rank similarity mining for multimodal dataset distillation. In: Proceedings of the International Conference on Machine Learning (ICML) (2024)

- 18 Y. Xu et al.
- <span id="page-17-6"></span>58. Yang, S., Xie, Z., Peng, H., Xu, M., Sun, M., Li, P.: Dataset pruning: Reducing training data by examining generalization influence. ArXiv abs/2205.09329 (2022)
- <span id="page-17-5"></span>59. Yin, Z., Xing, E., Shen, Z.: Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. arXiv preprint arXiv:2306.13092 (2023)
- <span id="page-17-7"></span>60. Zhang, L., Zhang, J., Lei, B., Mukherjee, S., Pan, X., Zhao, B., Ding, C., Li, Y., Xu, D.: Accelerating dataset distillation via model augmentation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11950–11959 (2023)
- <span id="page-17-3"></span>61. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: ICML (2021)
- <span id="page-17-1"></span>62. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: WACV (2023)
- <span id="page-17-0"></span>63. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020)
- <span id="page-17-4"></span>64. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7856–7865 (2023)
- <span id="page-17-2"></span>65. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. arXiv preprint arXiv:2206.00719 (2022)

# Supplementary Materials

We provide the following details and analyses in the supplementary:

Sec. [7:](#page-18-0) Relation between Loss and Its Derivative.

Sec. [8:](#page-18-1) Error of ITE Estimation.

Sec. [9:](#page-19-0) Comparison to Coreset Selection Methods.

Sec. [10:](#page-20-0) Overfitting Analysis.

Sec. [11:](#page-21-0) Model Generalization.

Sec. [12:](#page-23-0) Implementation Details.

Sec. [13:](#page-27-0) More Visualizations.

Sec. [14:](#page-28-0) Licenses.

<span id="page-18-0"></span>

## 7 Relation between Loss and Its Derivative

In BiLP, we use  $\ell(u_r, y_r)$  as preemptive selection criterion since it is monotonous concerning  $\Vert \frac{\partial \ell(u_r, y_r)}{\partial u} \Vert$  $\frac{u_r, y_r}{\partial u_r}$  || for common loss functions like Mean Squared Error (MSE) or cross-entropy, or say  $\ell(u_r, y_r)$  and  $\|\frac{\partial \ell(u_r, y_r)}{\partial u_r}\|$  $\frac{u_r, y_r}{\partial u_r}$  are positive correlated. Let's consider the two common loss functions mentioned:

- 1. **Mean Squared Error (MSE)**: The MSE loss function is defined as  $\ell(u, y)$  =  $\frac{1}{2}(u-y)^2$ , where u is the predicted value and y is the actual value. The gradient of the MSE with respect to u is  $\frac{\partial \ell}{\partial u} = u - y$ . The gradient norm is then  $\|\frac{\partial \ell}{\partial u}\| = |u - y|$ . Thus, larger  $\|\frac{\partial \ell}{\partial u}\|$  leads to larger  $\ell(u, y)$ .
- 2. Cross-Entropy: The cross-entropy loss function for binary classification is defined as  $\ell(\mathbf{u}, y) = -\log(u_y)$ , where **u** is the predicted logits of the N class and each component  $u_i > 0$ , and y is the actual class. The gradient of the cross-entropy with respect to each component  $u_i$  is  $\frac{\partial \ell}{\partial u_i}$  =  $\int -\frac{1}{u_y}$ , if  $i=y$ 0, otherwise . The gradient norm is  $\|\frac{\partial \ell}{\partial u}\| = \frac{1}{u_0 \sqrt{2}}$  $\frac{1}{u_y\sqrt{N}}$ . So larger  $\|\frac{\partial \ell}{\partial u}\|$

indicating a small  $u_y$  and therefore the  $\ell(\boldsymbol{u}, y)$  is large.

Overall, for these two loss functions,  $\|\frac{\partial \ell}{\partial u_r}\|$  and  $\ell(u_r, y_r)$  are positively correlated.

<span id="page-18-1"></span>

## 8 Error of ITE Estimation

In BiLP, we have implemented the Taylor approximation technique to enhance the efficiency of ITE value computations. To quantitatively assess the error of the Taylor approximation, we conducted an experiment where both the original and the Taylor-approximated ITE values were calculated within a mini-batch consisting of  $N$  samples. The samples were then sorted based on their ITE values, resulting in rankings represented by  $r_i$  and  $r'_i$  for each  $i^{\text{th}}$  sample within

19

<span id="page-19-1"></span>Image /page/19/Figure/1 description: The image displays a grid of ten scatter plots, arranged in two rows of five plots each. Each plot is titled with a category number from 1 to 10. The x-axis and y-axis of each plot range from 0 to 250. A blue line is plotted in each scatter plot, generally following a diagonal trend from the bottom left to the top right, indicating a positive correlation between the x and y values within each category. The lines exhibit some variation and fluctuations, particularly in categories 4 and 6, where the lines show more pronounced deviations from a perfect diagonal.

Fig. 5: Visualization of the original and the Taylor-approximated ITE rankings for CIFAR10 [\[22\]](#page-15-14) with IPC=10. An ideal approximation would reveal a 45-degree diagonal line.

<span id="page-19-2"></span>Table 8: Comparison to coreset selection on CIFAR10, IPC=1 and DC algorithm.

| Selection<br>Criterion  | Pruning Ratio                     |                                   |                                   |                                   |                                   |                                   | 0% (Full dataset) |
|-------------------------|-----------------------------------|-----------------------------------|-----------------------------------|-----------------------------------|-----------------------------------|-----------------------------------|-------------------|
|                         | 99%                               | 97%                               | 95%                               | 90%                               | 80%                               | 70%                               |                   |
| Random                  | 25.6 $	extpm$ 0.6                 | 27.6 $	extpm$ 0.8                 | 27.6 $	extpm$ 0.6                 | 28.2 $	extpm$ 0.3                 | 28.5 $	extpm$ 0.4                 | 28.7 $	extpm$ 0.3                 |                   |
| Loss (remove large)     | <b>29.7<math>	extpm</math>0.1</b> | <b>29.7<math>	extpm</math>0.0</b> | <b>30.0<math>	extpm</math>0.1</b> | <b>30.0<math>	extpm</math>0.2</b> | <b>30.2<math>	extpm</math>0.1</b> | <b>30.2<math>	extpm</math>0.2</b> | 28.3 $	extpm$ 0.5 |
| Coreset: CRAIG [38]     | 25.4 $	extpm$ 0.6                 | 27.8 $	extpm$ 0.2                 | 28.8 $	extpm$ 0.5                 | 28.6 $	extpm$ 0.4                 | 29.0 $	extpm$ 0.1                 | 29.0 $	extpm$ 0.2                 |                   |
| Coreset: GradMatch [18] | 26.5 $	extpm$ 0.5                 | 28.0 $	extpm$ 0.3                 | 28.7 $	extpm$ 0.5                 | 28.4 $	extpm$ 0.3                 | 28.9 $	extpm$ 0.2                 | 29.4 $	extpm$ 0.3                 |                   |
| Coreset: GLISTER [19]   | 23.8 $	extpm$ 0.5                 | 26.9 $	extpm$ 0.2                 | 23.5 $	extpm$ 0.1                 | 21.3 $	extpm$ 0.6                 | 24.0 $	extpm$ 0.9                 | 24.9 $	extpm$ 0.8                 |                   |

the range of  $[1, N]$ . To evaluate the discrepancy between the two sets of rankings, we utilized the average relative error metric, defined as  $\frac{1}{N(N-1)} \sum_{i=1}^{N} |r_i - r'_i|$ , with a range of  $[0, 1)$ . Experimenting on the CIFAR10 dataset with an IPC of 10, our analysis revealed an average error rate of 2.7%, which is considered negligible for pruning.

Furthermore, to provide a more intuitive understanding of our approximation's performance, we visualize the rankings in Fig. [5.](#page-19-1) The x-axis corresponds to the original ITE rankings, while the y-axis depicts the rankings derived from the Taylor approximation, with each of the 10 classes distinctly plotted. The closer the distribution of points to a 45-degree diagonal line, the higher the similarity between the two computation methods. The figure demonstrates that our Taylor approximation closely mirrors the original ITE rankings, thereby validating its efficacy.

<span id="page-19-0"></span>

## 9 Comparison to Coreset Selection Methods

The existing coreset selection methods can also be exploited as sample selection criteria. So we conduct a comparison with recent coreset selection methods on CIFAR10 [\[22\]](#page-15-14) and DC [\[63\]](#page-17-0) algorithm, including CRAIG [\[38\]](#page-16-17), GradMatch [\[18\]](#page-15-16) and

| Selection<br>Criterion  | Pruning Ratio                    |                                  |                                  |                                  |                |
|-------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------|
|                         | 70%                              | 60%                              | 50%                              | 30%                              | 0% (Full)      |
| Random                  | $53.0 \pm 0.2$                   | $53.6 \pm 0.3$                   | $54.0 \pm 0.5$                   | $54.2 \pm 0.3$                   |                |
| Loss (remove large)     | <b><math>54.1 \pm 0.2</math></b> | <b><math>54.9 \pm 0.3</math></b> | <b><math>55.3 \pm 0.3</math></b> | <b><math>56.0 \pm 0.2</math></b> | $54.1 \pm 0.3$ |
| Coreset: CRAIG [38]     | $48.8 \pm 0.4$                   | $48.8 \pm 0.2$                   | $49.0 \pm 0.4$                   | $49.1 \pm 0.4$                   |                |
| Coreset: GradMatch [18] | $49.0 \pm 0.3$                   | $49.1 \pm 0.3$                   | $49.1 \pm 0.4$                   | $49.1 \pm 0.5$                   |                |
| Coreset: GLISTER [19]   | $41.9 \pm 0.4$                   | $43.9 \pm 0.5$                   | $44.0 \pm 0.5$                   | $47.0 \pm 0.5$                   |                |

<span id="page-20-1"></span>Table 9: Comparison to coreset selection on CIFAR10, IPC=50 and DC algorithm.

rithms.

<span id="page-20-2"></span>Table 10: Maximum pruning pruning ratio of vari- pruning ratio of varratio on more distillation algo-ous initializations on CI-ious networks on CI-Table 11: Maximum Table 12: Maximum FAR10 [\[22\]](#page-15-14). FAR10 [\[22\]](#page-15-14).

| Dataset                                             |    | IPC CAFE [53] LinBa [8] IDC [21] |            |              |    | <b>IPC</b> lInit | DC [63] DM [62] |            |    | <b>IPC</b> Net                                      | DC [63] DM [62] |            |
|-----------------------------------------------------|----|----------------------------------|------------|--------------|----|------------------|-----------------|------------|----|-----------------------------------------------------|-----------------|------------|
| CIFAR10 $[22]\begin{bmatrix} 1 \\ 10 \end{bmatrix}$ |    | 85%<br>89%                       | 30%<br>70% | 50%<br>90%   |    | Noise<br>Real    | 90%<br>90%      | 90%<br>85% |    | $Conv$ [54]<br>MLP                                  | 90%<br>97%      | 85%<br>95% |
| <b>SVHN</b> [39]                                    | 10 | 70%<br>40%                       | 50%<br>40% | 40%<br>70%   |    | Herd             | 90%             | 85%        |    | ResNet [11]<br><b>VGG [48]</b>                      | 95%<br>90%      | 85%<br>95% |
| MNIST <sup>[25]</sup>                               | 10 | 90%<br>$1\%$                     | 70%<br>60% | 99.5%<br>60% | 10 | Noise<br>Real    | 70%<br>70%      | 70%<br>60% |    | AlexNet [23]<br>$\lfloor$ Conv $\lfloor 54 \rfloor$ | 95%<br>70%      | 95%<br>60% |
|                                                     |    |                                  |            |              |    | Herd             | 70%             | 70%        | 10 | <b>MLP</b>                                          | 60%             | 60%        |

GLISTER [\[19\]](#page-15-17). We adopt these methods as a preemptive pruning criterion. We use the algorithms implemented by the CORDS package. The results are shown in Tab. [8](#page-19-2) and [9.](#page-20-1) CRAIG and GradMatch coreset selection can achieve a 97% maximum pruning ratio when IPC=1, though still worse than the loss criterion. The GLISTER algorithm does not perform well on dataset distillation and is worse than random selection. Thus, on the data pruning for dataset distillation, our loss indicator can surpass some sophisticated selection algorithms.

### 9.1 Data Redundancy on Various Architectures and Initialization

In the main paper, we examine the real data redundancy in dataset distillation by randomly removing some real samples before the training. Here we present more results in Tabs. [10](#page-20-2) to [12](#page-20-2) with more algorithms and various initialization and network architectures, which exhibit large pruning ratios and indicate significant data redundancy.

<span id="page-20-0"></span>

## 10 Overfitting Analysis

We compare the train and test loss curve on distilled data with different pruning rates in Figure [6](#page-21-1) on CIFAR10 and IPC=1 (5 repeats). The test loss curves do not drastically increase after the convergence of training, and all the loss curves are similar, showing that a large pruning rate does not enhance overfitting problems. This is consistent with the explanation in Sec. 4.1: due to the limited capacity of

<span id="page-21-1"></span>Image /page/21/Figure/1 description: The image contains two plots side-by-side. The left plot is titled "Train Loss" and shows the training loss over 200 epochs. The x-axis is labeled "Epoch" and ranges from 0 to 200. The y-axis is labeled "Train Loss" and ranges from 0.0 to 2.5. There are seven colored lines representing different percentages: -0.0% (purple), -50.0% (blue), -60.0% (cyan), -70.0% (light green), -80.0% (yellow), -90.0% (orange), and -95.0% (red). All lines show a decreasing trend, with the loss decreasing rapidly in the initial epochs and then leveling off. The right plot is titled "Test Loss" and shows the test loss over 200 epochs. The x-axis is labeled "Epoch" and ranges from 0 to 200. The y-axis is labeled "Test Loss" and ranges from 2.05 to 2.35. The same seven colored lines are present, representing the same percentages. The test loss also shows a decreasing trend initially, but then starts to slightly increase or plateau after around 50-100 epochs, with the -95.0% line showing the lowest test loss at the end of the training.

Fig. 6: Train and test loss curves for different pruning rates.

the synthetic dataset, removing some unimportant or outlier data samples does not harm the distillation process.

<span id="page-21-0"></span>

## 11 Model Generalization

### 11.1 Cross-Architecture Generalization of Preemptive Pruning

We conducted a cross-architecture evaluation to verify whether the data pruning harms the generalization ability. We first follow DC [\[63\]](#page-17-0) to experiment on MNIST and IPC $=1$ . We remove the training samples with the largest loss values and we compare the training subsets with 100% (full data, original setting in DC paper), 10%, 5%, and 3%. The results are shown in Tab. [13](#page-22-0) in the rebuttal PDF file, showing that pruning the training dataset does not damage the generalization ability of the distilled data. On the contrary, in most slots (28/36), data pruning can even enhance the generalization ability.

We also conduct experiments on larger IPCs with DC [\[63\]](#page-17-0) and MTT [\[3\]](#page-14-0). The results are shown in Tab. [14](#page-23-1) and [15.](#page-23-2) On larger IPCs, pruning the training dataset still does not damage the generalization ability of the distilled data.

#### 11.2 Cross-Architecture Generalization of Full BiLP

In this study, we present a cross-architecture evaluation that demonstrates the efficacy of synthetic data trained on the ConvNet-D3 architecture while being assessed across different neural network architectures including ResNet [\[11\]](#page-14-14), EfficientNet [\[49\]](#page-16-19), and DenseNet [\[13\]](#page-14-15). The experiments are conducted using the CIFAR10 dataset at three IPC levels 1, 10, and 50. As shown in Tab. [16,](#page-24-0) the synthetic data generalize well when applied to other network architectures,  $e.g.$ at IPC=50, all three model types achieve performance that is on par with those obtained from the in-situ evaluation using ConvNet-D3. This indicates that the synthetic data is capable of maintaining its predictive accuracy across diverse

| Evaluate<br>Network | Pruning<br>Ratio | Distill Network |                |                |                |                |                |
|---------------------|------------------|-----------------|----------------|----------------|----------------|----------------|----------------|
|                     |                  | MLP             | ConvNet        | LeNet          | AlexNet        | VGG            | ResNet         |
| MLP                 | $0%$ (Full)      | $24.3 \pm 1.8$  | $26.5 \pm 0.2$ | $24.1 \pm 0.4$ | $26.5 \pm 0.1$ | $25.1 \pm 0.3$ | $24.9 \pm 0.4$ |
|                     | 60%              | $23.0 \pm 1.4$  | $26.6 \pm 0.3$ | $24.2 \pm 0.5$ | $26.6 \pm 0.1$ | $25.1 \pm 0.1$ | $25.1 \pm 0.1$ |
|                     | 80%              | $23.7 \pm 0.9$  | $26.5 \pm 0.4$ | $24.3 \pm 0.7$ | $26.6 \pm 0.1$ | $25.2 \pm 0.1$ | $24.6 \pm 0.3$ |
|                     | 95%              | $25.1 \pm 1.4$  | $26.8 \pm 0.2$ | $24.3 \pm 0.2$ | $26.6 \pm 0.2$ | $25.3 \pm 0.3$ | $24.8 \pm 0.4$ |
| ConvNet [54]        | $0%$ (Full)      | $22.7 \pm 1.1$  | $28.8 \pm 0.4$ | $21.8 \pm 0.2$ | $27.0 \pm 0.5$ | $25.8 \pm 0.4$ | $26.2 \pm 0.3$ |
|                     | 60%              | $23.0 \pm 1.7$  | $28.7 \pm 0.1$ | $22.1 \pm 0.3$ | $26.9 \pm 0.2$ | $25.8 \pm 0.3$ | $26.2 \pm 0.2$ |
|                     | 80%              | $23.4 \pm 0.9$  | $28.8 \pm 0.2$ | $22.2 \pm 0.3$ | $26.8 \pm 0.3$ | $25.6 \pm 0.2$ | $26.2 \pm 0.2$ |
|                     | 95%              | $23.7 \pm 0.8$  | $29.1 \pm 0.2$ | $22.3 \pm 0.3$ | $26.9 \pm 0.4$ | $25.8 \pm 0.1$ | $26.1 \pm 0.3$ |
| LeNet [25]          | $0%$ (Full)      | $16.0 \pm 2.6$  | $17.9 \pm 0.8$ | $14.9 \pm 1.1$ | $15.7 \pm 1.5$ | $18.2 \pm 1.3$ | $16.6 \pm 1.1$ |
|                     | 60%              | $16.8 \pm 1.9$  | $17.6 \pm 0.8$ | $15.6 \pm 0.2$ | $16.1 \pm 0.7$ | $18.8 \pm 0.4$ | $16.4 \pm 1.1$ |
|                     | 80%              | $18.9 \pm 1.5$  | $17.3 \pm 0.6$ | $14.0 \pm 0.6$ | $16.2 \pm 1.1$ | $19.3 \pm 0.6$ | $15.8 \pm 0.9$ |
|                     | 95%              | $17.9 \pm 1.0$  | $17.9 \pm 1.2$ | $15.1 \pm 0.7$ | $15.6 \pm 0.6$ | $19.1 \pm 0.8$ | $16.8 \pm 1.2$ |
| AlexNet [23]        | $0%$ (Full)      | $20.8 \pm 0.5$  | $22.9 \pm 0.3$ | $22.7 \pm 0.6$ | $21.1 \pm 0.2$ | $22.7 \pm 0.2$ | $22.3 \pm 0.2$ |
|                     | 60%              | $19.4 \pm 1.2$  | $22.7 \pm 0.4$ | $23.0 \pm 0.4$ | $21.1 \pm 0.4$ | $22.7 \pm 0.1$ | $22.3 \pm 0.2$ |
|                     | 80%              | $21.2 \pm 0.6$  | $23.0 \pm 0.3$ | $23.1 \pm 0.7$ | $20.6 \pm 0.1$ | $22.6 \pm 0.1$ | $22.2 \pm 0.3$ |
|                     | 95%              | $21.3 \pm 0.4$  | $22.9 \pm 0.2$ | $23.1 \pm 0.4$ | $21.2 \pm 0.5$ | $22.8 \pm 0.2$ | $22.3 \pm 0.2$ |
| VGG [48]            | $0%$ (Full)      | $20.3 \pm 3.1$  | $15.7 \pm 0.6$ | $20.4 \pm 0.8$ | $23.6 \pm 0.7$ | $18.0 \pm 0.8$ | $18.5 \pm 0.4$ |
|                     | 60%              | $20.2 \pm 1.6$  | $15.8 \pm 0.4$ | $20.3 \pm 0.5$ | $24.0 \pm 0.6$ | $18.6 \pm 0.4$ | $18.1 \pm 0.3$ |
|                     | 80%              | $19.9 \pm 2.2$  | $15.3 \pm 0.8$ | $20.6 \pm 0.8$ | $23.6 \pm 0.6$ | $18.1 \pm 0.7$ | $18.2 \pm 0.7$ |
|                     | 95%              | $20.4 \pm 1.7$  | $15.3 \pm 0.7$ | $20.4 \pm 0.8$ | $23.0 \pm 0.8$ | $18.0 \pm 0.9$ | $18.2 \pm 0.7$ |
| ResNet [11]         | $0%$ (Full)      | $18.3 \pm 1.6$  | $16.7 \pm 0.9$ | $15.1 \pm 0.8$ | $17.5 \pm 0.5$ | $16.0 \pm 0.7$ | $18.1 \pm 1.1$ |
|                     | 60%              | $18.9 \pm 1.8$  | $17.2 \pm 1.0$ | $15.2 \pm 0.5$ | $17.8 \pm 0.6$ | $15.6 \pm 1.0$ | $18.5 \pm 0.6$ |
|                     | 80%              | $17.0 \pm 1.1$  | $16.3 \pm 0.7$ | $13.9 \pm 1.0$ | $17.6 \pm 1.3$ | $15.2 \pm 0.6$ | $19.2 \pm 0.8$ |
|                     | 95%              | $18.8 \pm 1.4$  | $16.7 \pm 0.6$ | $14.3 \pm 0.7$ | $17.0 \pm 1.4$ | $15.0 \pm 0.4$ | $18.3 \pm 0.7$ |

<span id="page-22-0"></span>Table 13: Cross-architecture generalization on CIFAR10 [\[22\]](#page-15-14) and IPC=1 with DC [\[63\]](#page-17-0) algorithm. Best results are marked in bold green. The generalization ability of pruned data is superior or comparable to the originals.

architectures. Furthermore, our comparative analysis reveals that the BiLP consistently outperforms IDC in the majority of the conducted experiments.

### 11.3 Generalization to Larger Data

We add experiments on larger dataset  $(e.g. \text{ImageNet})$  and larger IPC (we adopt DATM [\[10\]](#page-14-16) algorithm). We re-implement DATM with TESLA for efficiency and compare BiLP (preemptive pruning 10% data) to the reproduced DATM results. As shown in Table [17,](#page-24-1) BiLP could enhance DATM on small IPCs and is comparable at large IPCs, and could also achieve lossless performance at IPC=1000. We also conduct experiment of SRe2L [\[59\]](#page-17-5) in Table [18.](#page-24-1) BiLP could enhance the performance with less data, e.g., BiLP with only 50% data could significantly enhance SRe2L.

<span id="page-23-1"></span>Table 14: Cross-architecture generalization on CIFAR10 [\[22\]](#page-15-14) and IPC=50 with DC [\[63\]](#page-17-0) algorithm.

|              |     | Evaluate Network |                                                                                                                                                                                                                          |            |     |                                                                                                       |  |  |  |
|--------------|-----|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------|-----|-------------------------------------------------------------------------------------------------------|--|--|--|
| Sample Ratio | MLP | ConvNet          | LeNet                                                                                                                                                                                                                    | A lex Net. | VGG | <b>ResNet</b>                                                                                         |  |  |  |
| $0\%$ (Full) |     |                  | $28.01 \pm 0.40 \quad 54.02 \pm 0.51 \quad 28.12 \pm 2.16 \quad 29.48 \pm 0.58 \quad 39.44 \pm 0.67 \quad 22.72 \pm 1.08$                                                                                                |            |     |                                                                                                       |  |  |  |
| 30%          |     |                  | $29.48 \pm 0.28 \hspace{0.15cm} \textbf{55.96} \pm \textbf{0.40} \hspace{0.15cm} 30.83 \pm 1.51 \hspace{0.15cm} \textbf{29.54} \pm 2.60 \hspace{0.15cm} \textbf{41.99} \pm 0.47 \hspace{0.15cm} \textbf{24.35} \pm 0.42$ |            |     |                                                                                                       |  |  |  |
| 50%          |     |                  |                                                                                                                                                                                                                          |            |     | $30.40 \pm 0.28$ $55.25 \pm 0.32$ $31.15 \pm 1.25$ $30.53 \pm 2.21$ $43.09 \pm 0.41$ $25.81 \pm 1.04$ |  |  |  |
| 70%          |     |                  | $30.15 \pm 0.21$ $54.77 \pm 0.47$ $31.44 \pm 0.72$ $33.45 \pm 1.18$ $43.35 \pm 0.60$ $25.48 \pm 0.53$                                                                                                                    |            |     |                                                                                                       |  |  |  |

<span id="page-23-2"></span>Table 15: Cross-architecture generalization on CIFAR10 [\[22\]](#page-15-14) and IPC=10 with MTT [\[3\]](#page-14-0) algorithm.

| $\begin{tabular}{c c} Sample Ratio & ConvNet & AlexNet & VGG \\ \hline \end{tabular}$ |  | Evaluate Network | ResNet                                                                                                                                                                                          |
|---------------------------------------------------------------------------------------|--|------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| $0\%$ (Full)<br>$10\%$                                                                |  |                  | $\begin{array}{ ccccccccccccccc } 64.3 {\pm} 0.7 & 34.2 {\pm} 2.6 & 50.3 {\pm} 0.8 & 46.4 {\pm} 0.6 \\ \hline 64.6 {\pm} 0.4 & 34.3 {\pm} 2.4 & 51.1 {\pm} 1.1 & 48.6 {\pm} 0.4 \\ \end{array}$ |

<span id="page-23-0"></span>

## 12 Implementation Details

### 12.1 Datasets and Metric

Our experiments are conducted on the following datasets and we report the top-1 accuracy as the metric, most of which are widely adopted in dataset distillation.

- CIFAR10 [\[22\]](#page-15-14): image dataset of common objects with 10 classes and 50,000 image samples. The images are 32x32 with 3 channels.
- CIFAR100 [\[22\]](#page-15-14): image dataset of common objects with 100 classes and 50,000 samples. The images are 32x32 with 3 channels.
- SVHN [\[39\]](#page-16-15): street digit dataset with 10 classes and 73,257 samples. The images are 32x32 with 3 channels.
- TinyImageNet [\[24\]](#page-15-15): a subset of ImageNet with 200 classes and 100,000 images. The images are 64x64 with 3 channels.
- ImageNet [\[7\]](#page-14-12): image datasets of common objects with 1000 classes and 1,281,167 samples. We resize the images to 64x64 with 3 channels following the previous setting [\[65\]](#page-17-2).
- Kinetics-400 [\[1\]](#page-14-13): human action video dataset with 400 classes and 215,617 video samples. The videos are resampled to 8 frames per clip and resized to 64x64.

#### 12.2 Network Architectures

Following the previous work, in most of the experiments, we adopt ConvNetD3 as the network to probe the data. This network consists of 3 convolutional layers with a 3x3 filter, each of which has 128 channels and is followed by a ReLU non-linearity and an InstaceNorm layer. The average pooling layer aggregates the feature map to a 128d vector and produces the logit with a linear layer.

| Architecture      | Method   | IPC             |                 |                 |
|-------------------|----------|-----------------|-----------------|-----------------|
|                   |          | 1               | 10              | 50              |
| ConvNet-D3 [54]   | IDC      | 50.6±0.4        | 67.5±0.5        | 74.5±0.1        |
| ConvNet-D3 [54]   | BiLP+IDC | <b>51.5±0.3</b> | <b>69.4±0.5</b> | <b>75.4±0.2</b> |
| ResNet [11]       | IDC      | 42.8±1.2        | 64.8±0.9        | 71.9±1.3        |
| ResNet [11]       | BiLP+IDC | <b>43.3±0.6</b> | <b>65.5±1.0</b> | <b>72.8±0.4</b> |
| EfficientNet [49] | IDC      | 38.5±1.0        | 42.2±0.8        | 71.4±1.5        |
| EfficientNet [49] | BiLP+IDC | <b>38.5±0.6</b> | <b>43.7±2.5</b> | <b>71.9±1.0</b> |
| DenseNet [13]     | IDC      | 37.9±0.6        | 64.8±0.8        | 70.4±0.5        |
| DenseNet [13]     | BiLP+IDC | <b>38.3±0.5</b> | <b>64.6±0.4</b> | <b>70.7±0.7</b> |

<span id="page-24-0"></span>Table 16: Cross-architecture evaluation of synthetic data trained on ConvNet-D3 with BiLP, on CIFAR10 [\[22\]](#page-15-14).

<span id="page-24-1"></span>Table 17: DATM [\[10\]](#page-14-16) and BiLP performance on CIFAR10. Full=84.8%.

Table 18: SRe2L [\[59\]](#page-17-5) and BiLP performance on large dataset ImageNet [\[7\]](#page-14-12).

| IPC       | 1           | 10          | 50          | 500         | 1000        |
|-----------|-------------|-------------|-------------|-------------|-------------|
| DATM      | 47.0        | 65.7        | 72.9        | <b>81.3</b> | <b>84.8</b> |
| DATM+BiLP | <b>47.4</b> | <b>66.1</b> | <b>74.0</b> | <b>81.3</b> | 84.6        |

We also adopt other architectures, including MLP (three linear layers with hidden layer size 128), AlexNet [\[23\]](#page-15-19), ResNet18 [\[11\]](#page-14-14) (ResNet18+BatchNorm with average pooling for DC algorithm), and VGG11 [\[48\]](#page-16-18) (we use VGG11+BatchNorm for DC algorithm).

#### 12.3 Experiments of Random or Loss Selection

In Tab. 1-3 in the main paper, we extensively study the critical sample ratio by random or loss value. We mainly follow the default experiment settings given by each algorithm. The experiments are conducted on RTX 4090 GPU. We list the experiment details:

- 1. For DC [\[63\]](#page-17-0) and DSA [\[61\]](#page-17-3), on all datasets, we run the distillation for 1000 iterations with SGD optimizer and momentum 0.5. The number of inner loop and outer loop are  $(1, 1)$  for IPC=1,  $(10, 50)$  for IPC=10,  $(50, 10)$ for IPC=50. The learning rate of synthetic image and network are 0.1 and 0.01. The batch size for each class is 256 and when the sample ratio is low, we half the batch size until it is less than twice the largest class size. We use color, crop, cutout, scale, rotate DSA augmentation on all datasets and additional flip on the non-digit datasets. By default, noise initialization is used.
- 2. For DM [\[62\]](#page-17-1), we run the distillation for 10000 iterations on TinyImageNet and 20000 iterations for the others with SGD optimizer and momentum 0.5. The learning rate of synthetic image and network are 1.0 and 0.01. The

batch size for each class is 256 and when the sample ratio is low, we half the batch size until it is less than twice the largest class size. The same Siamese augmentation strategy is used as in the DSA experiments. By default, real initialization is used (the initial images are drawn after dropping).

- 3. For MTT [\[3\]](#page-14-0), we drop the same data samples for buffering and distillation. The expert trajectories are trained for 50 epochs for 100 repeats and we run the distillation for 10000 iterations. We appreciate and follow the [detailed](https://user-images.githubusercontent.com/18726777/184226412-7bd0d577-225b-487c-8c9c-23f6462ca7d0.png) [hyper-parameters](https://user-images.githubusercontent.com/18726777/184226412-7bd0d577-225b-487c-8c9c-23f6462ca7d0.png) provided by the authors.
- 4. For CAFE [\[53\]](#page-16-10), as default, we run the distillation for 2000 iterations. The initial learning rate is 0.1 and decays by 0.5 at 1,200, 1,600, and 1,800 iterations. The weight of the inner layer matching loss is 0.01 and an additional loss weight of 0.1 is put on the matching loss of the third and fourth layers. Noise initialization is used.
- 5. For LinBa [\[8\]](#page-14-7), we run distillation for 5000 iterations with SGD optimizer with momentum 0.9. The inner steps of BPTT are 150 and the number of bases is 16. The learning rate of synthetic image and network is 0.1 and 0.01.
- 6. For IDC [\[21\]](#page-15-9), we use the "reproduce" setting of the opened source code, which automatically sets up the tuned hyper-parameters. We use multi-formation factor 2.
- 7. For KIP [\[40\]](#page-16-5), we test on the finite-width model (KIP-NN) and use label learning. We use longer training steps for converged results.
- 8. For FRePo [\[65\]](#page-17-2), we use the official PyTorch implementation and the default parameters, except the learning rate of 0.001 and we run the distillation for 500,000 steps.
- 9. For HaBa [\[30\]](#page-15-10), we follow the official instructions and adopt the parameters from MTT. And for the exclusive parameters for HaBa, we use the values given in the code.
- 10. For RFAD [\[33\]](#page-15-1), we test on the finite-width model (ConvNet) and load the training hyperparameters for finite training results in the paper. The choice of label learning follows the remarks in the paper.
- 11. For IDM [\[64\]](#page-17-4), we thank the authors and we directly adopt the official running commands.

The removal of data samples is class-wise. Each experiment is repeated 5 times for mean  $\mu$  and standard deviation  $\sigma$  and we regard the experiment performance as comparable to the experiment on full data if its mean accuracy is within the  $[\mu - \sigma, \mu + \sigma]$  of full data performance.

### 12.4 Computation of Empirical Loss Criterion

The parameters and settings of the empirical loss criterion for the preemptive pruning are as follows: We train the ConvNetD3 model on each dataset (ConvNetD3+GRU for Kinetics-400) for multiple trials for the loss indicator. We take the average loss curve of multiple trials. By default, we use a Gaussian filter with  $\sigma = 3$  to smooth the loss curve and take the loss value at the last epoch, which is approximately equivalent to the weighted mean loss value of the last 8 epochs. The training details are:

<span id="page-26-0"></span>

| Dataset                                | CIFAR10 [22] |     |     | CIFAR100 [22] |     | SVHN [39] |     |     |
|----------------------------------------|--------------|-----|-----|---------------|-----|-----------|-----|-----|
|                                        | 1            | 10  | 50  | 1             | 10  | 1         | 10  | 50  |
| IPC                                    | 1            | 10  | 50  | 1             | 10  | 1         | 10  | 50  |
| Preemptive pruning rate $\alpha$       | 0.1          | 0.3 | 0.1 | 0.6           | 0.4 | 0.2       | 0.3 | 0.3 |
| Adaptive pruning rate $\beta$          | 0.3          | 0.3 | 0.3 | 0.3           | 0.3 | 0.2       | 0.2 | 0.2 |
| Data update frequency (lazy selection) | 10           | 5   | 5   | 10            | 10  | 10        | 5   | 5   |

Table 19: Hyper-parameters of BiLP in different experiments.

- CIFAR10: 50 trials for 100 epochs with learning rate 3.0e-3 and batch size 512.
- CIFAR100: 50 trials for 250 epochs with learning rate 5.0e-3 and batch size 512.
- MNIST: 50 trials for 50 epochs with learning rate 3.0e-4 and batch size 512.
- SVHN: 50 trials for 100 epochs with learning rate 1.0e-3 and batch size 512.
- TinyImageNet: 50 trials for 100 epochs with learning rate 5.0e-3 and batch size 512.
- ImageNet: 30 trials for 20 epochs with learning rate 3.0e-3 and batch size 256 (early stop).
- Kinetics-400: 10 trials for 20 epochs with learning rate 1.0e-2 and batch size 128 (early stop).

Note that considering the conclusion in Sec. 5.5, we have adopted an early stop on large-scale datasets to reduce the training cost.

#### 12.5 Experiments of BiLP

We use 3-layer ConvNet for CIFAR and SVHN datasets, and 4-layer ConvNet for TinyImageNet. For BiLP, the momentum for running stats of ITE is set to 0.1 and we set  $\beta = 30\%$  by default. The other hyperparameters vary among different datasets and please refer to the supplementary. We take the mean and standard deviation of the accuracy of 5 random trials. Specifically, we show the hyper-parameters of different experiment settings in Tab. [19.](#page-26-0) We use the same parameters for multi-formation factor 2 or 3 for IDC.

### 12.6 Experiments on the Large-scale Datasets

We apply our selection paradigm on larger-scale datasets in Sec. 5.2 in the main paper. The experiments are conducted on at most 4 RTX 3090 GPUs and the details are as follows:

– ImageNet, DC: the training of DC exceeds the usual GPU capacity so in compromise we separate the 1000 classes into ten 100 class splits, which will slightly decrease the accuracy. The other hyper-parameters are the same as the previous experiments. For our paradigm, we prune 50% samples and early stop at 800 iterations due to its faster convergence.

- 28 Y. Xu et al.
  - ImageNet, DM: the DM algorithm is safe for class-separate training so we separate the classes into 4 splits at IPC=1, 8 splits at IPC=10, and 20 splits at IPC=50. We run the distillation for 5000 iterations with a learning rate of 5.0. For our paradigm, we prune 50% samples and early stop at 2,000, 3,000, and 3,000 iterations for IPC=1/10/50 respectively.
  - ImageNet, MTT: the expert trajectory is too expensive to compute so we only run MTT with our selection method. We prune 90% samples which reduces 84% of the trajectory training time. We train 60 trajectories for 50 epochs. MTT also requires large GPU memory due to the unrolling of backpropagation, so we use synthetic steps=5, expert epochs=2, and maximum start epoch=5. We run the distillation for 5000 iterations with an image learning rate of 30,000 and a step size learning rate of 1.0e-6.
  - Kinetics-400, DM: on Kinetics, we run DM for 5000 iterations with a learning rate of 5.0 and batch size of 128. We separate the classes into 8 splits at IPC=1 and 20 splits at IPC=10. For our paradigm, we prune  $50\%$  samples and early stop at 4000 iterations. We do not use DSA augmentation for Kinetics.
  - Kinetics-400, MTT: we prune 90% samples and train 40 trajectories for 50 epochs with batch size 128. We use synthetic steps=5, expert epochs=2, and maximum start epoch=5. We run the distillation for 5000 iterations with an image learning rate of 30,000, step size learning rate of 1.0e-6, real batch size 128, and synthetic batch size 64. We do not use DSA augmentation for Kinetics.

<span id="page-27-0"></span>

## 13 More Visualizations

In this section, we present some data samples at different loss levels to qualitatively visualize the selection criterion.

We first stratify various datasets into 10 layers according to per-sample loss values and visualize some samples in the layer with the smallest or largest utility in Fig. [7,](#page-29-0) including the large-scale datasets (ImageNet and Kinetics-400). As shown in the figure, the samples with small loss are noisy and usually hard and corner cases, e.g. only part of the birds are shown, some dogs are acting in strange poses, or the images of ships are captured with unusual viewing angles. Meanwhile, the samples with large losses are easy cases that have ideal saliency, viewing angle, and clean background.

The digit datasets (MNIST, SVHN) show significantly more diversity vanishing than the rest of the realistic datasets. Moreover, the diversity vanishing issue is mild for large-scale datasets such as ImageNet since the intra-class discrepancy is large such that any subset is diversified enough.

To extend our discussion on the data diversity (Sec. 5.4 in the main paper), we give some more examples to compare the diversity for data strata with different loss values in Fig. [8](#page-30-0) on MNIST. The groups with large loss values are mainly corner cases. Furthermore, as the loss value decreases (S7 or S10), the diversity significantly drops as shown in Fig.  $8$  (c, d, g, h).

<span id="page-28-0"></span>

# 14 Licenses

Here are the source and license of the assets involved in our work. We sincerely appreciate and thank the authors and creators. Datasets:

- CIFAR10, CIFAR100 [\[22\]](#page-15-14): [URL,](https://www.cs.toronto.edu/~kriz/cifar.html) unknown license.
- MNIST [\[25\]](#page-15-18): [URL,](http://yann.lecun.com/exdb/mnist/) MIT License.
- SVHN [\[39\]](#page-16-15): [URL,](http://ufldl.stanford.edu/housenumbers/) unknown license.
- Tiny-ImageNet [\[24\]](#page-15-15): [URL,](https://www.kaggle.com/competitions/tiny-imagenet/) unknown license.
- ImageNet [\[7\]](#page-14-12): [URL,](https://www.image-net.org) custom license, research, non-commercial.
- Kinetics-400 [\[1\]](#page-14-13): [URL,](https://www.deepmind.com/open-source/kinetics) Creative Commons Attribution 4.0 International License.

## Code:

- DC [\[63\]](#page-17-0), DSA [\[61\]](#page-17-3), DM [\[62\]](#page-17-1): [URL,](https://github.com/VICO-UoE/DatasetCondensation) MIT License.
- MTT [\[3\]](#page-14-0): [URL,](https://github.com/GeorgeCazenavette/mtt-distillation) MIT License.
- CAFE [\[53\]](#page-16-10): [URL,](https://github.com/kaiwang960112/CAFE) no license.
- LinBa [\[8\]](#page-14-7): [URL,](https://github.com/princetonvisualai/RememberThePast-DatasetDistillation) no license.
- IDC [\[21\]](#page-15-9): [URL,](https://github.com/snu-mllab/Efficient-Dataset-Condensation/) MIT License.
- KIP [\[40\]](#page-16-5): [URL,](https://colab.research.google.com/github/google-research/google-research/blob/master/kip/KIP.ipynb) no license.
- FRePo [\[65\]](#page-17-2): [URL,](https://github.com/yongchao97/FRePo) no license.
- HaBa [\[30\]](#page-15-10): [URL,](https://github.com/Huage001/DatasetFactorization) Apache-2.0 License.
- IDM [\[64\]](#page-17-4): [URL,](https://github.com/uitrbn/idm) no license.
- RFAD [\[33\]](#page-15-1): [URL,](https://github.com/yolky/RFAD) no license.
- CORDS: [URL,](https://github.com/decile-team/cords) MIT license.

<span id="page-29-0"></span>Image /page/29/Figure/1 description: The image displays a grid of nine subfigures, each labeled with a letter from (a) to (i). Each subfigure contains a collection of images, and is further categorized by dataset (CIFAR100, MNIST, SVHN, ImageNet, Kinetics 400) and a description related to 'loss indicator' and 'utility' (small or large). Subfigures (a), (c), (e), (g), and (i) are labeled as 'small utility', while subfigures (b), (d), (f), and (h) are labeled as 'large utility'. The subfigures showcase different types of images, including natural scenes, handwritten digits, street view house numbers, and action clips.

(i) Kinetics-400, loss indicator, small utility (j) Kinetics-400, loss indicator, large utility

Fig. 7: Qualitative comparison of multiple datasets. We conduct stratified experiments with loss indicators and show samples in the layers with the smallest utility (left column) or largest utility (right column). We show three classes for each dataset.

<span id="page-30-0"></span>Image /page/30/Figure/1 description: The image displays a grid of handwritten digits, divided into two rows and four columns. The top row, labeled (a) through (d), shows variations of the digit '1' under different sampling conditions (S1, S4, S7, S10). The bottom row, labeled (e) through (h), shows variations of the digit '7' under the same sampling conditions (S1, S4, S7, S10). Each cell contains multiple instances of the respective digit, demonstrating different styles and orientations.

Fig. 8: More examples of different strata in the MNIST dataset. The data are stratified by classification loss. The samples in S1 have the lowest loss values and those in S10 have the largest loss. The diversity significantly drops when the sample loss decreases  $(e.g. S7, S10).$