{"table_of_contents": [{"title": "Distilled Datamodel with Reverse Gradient Matching", "heading_level": null, "page_id": 0, "polygon": [[132.08203125, 106.5], [461.25, 106.5], [461.25, 119.2060546875], [132.08203125, 119.2060546875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 213.75], [191.25, 213.75], [191.25, 226.037109375], [144.75, 226.037109375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 576.75], [127.5, 576.75], [127.5, 587.8125], [48.75, 587.8125]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[48.75, 387.0], [134.25, 385.5], [134.25, 398.3203125], [48.75, 398.3203125]]}, {"title": "2.1. Data-based Model Analysis", "heading_level": null, "page_id": 1, "polygon": [[48.75, 406.5], [198.0, 406.5], [198.0, 417.65625], [48.75, 417.65625]]}, {"title": "2.2. Machine Unlearning", "heading_level": null, "page_id": 1, "polygon": [[307.5, 94.5], [425.830078125, 94.5], [425.830078125, 104.7041015625], [307.5, 104.7041015625]]}, {"title": "2.3. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[307.5, 480.75], [418.5, 480.75], [418.5, 491.1328125], [307.5, 491.1328125]]}, {"title": "3. Proposed Method", "heading_level": null, "page_id": 2, "polygon": [[48.75, 345.0], [153.298828125, 345.0], [153.298828125, 356.748046875], [48.75, 356.748046875]]}, {"title": "3.1. Problem Statement", "heading_level": null, "page_id": 2, "polygon": [[48.75, 409.1484375], [160.5, 409.1484375], [160.5, 419.9765625], [48.75, 419.9765625]]}, {"title": "3.2. Offline Training", "heading_level": null, "page_id": 2, "polygon": [[307.5, 527.09765625], [404.25, 527.09765625], [404.25, 537.92578125], [307.5, 537.92578125]]}, {"title": "3.3. Online Evaluation", "heading_level": null, "page_id": 4, "polygon": [[48.75, 132.0], [155.25, 132.0], [155.25, 142.6025390625], [48.75, 142.6025390625]]}, {"title": "————————– Online Evaluation ————————–", "heading_level": null, "page_id": 4, "polygon": [[383.396484375, 567.75], [462.0, 567.75], [462.0, 576.984375], [383.396484375, 576.984375]]}, {"title": "3.4. Algorithm and Discussions.", "heading_level": null, "page_id": 5, "polygon": [[48.75, 248.25], [199.318359375, 248.25], [199.318359375, 258.521484375], [48.75, 258.521484375]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 5, "polygon": [[48.75, 387.75], [128.25, 387.75], [128.25, 398.70703125], [48.75, 398.70703125]]}, {"title": "4.1. <PERSON> Settings", "heading_level": null, "page_id": 5, "polygon": [[48.75, 407.25], [174.216796875, 407.25], [174.216796875, 418.4296875], [48.75, 418.4296875]]}, {"title": "4.2. Experimental Results", "heading_level": null, "page_id": 5, "polygon": [[307.5, 589.5], [430.013671875, 589.5], [430.013671875, 599.4140625], [307.5, 599.4140625]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 336.05859375], [378.75, 336.05859375], [378.75, 347.66015625], [307.5, 347.66015625]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 7, "polygon": [[307.5, 629.25], [404.25, 629.25], [404.25, 640.40625], [307.5, 640.40625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 84.0146484375], [48.75, 84.0146484375]]}, {"title": "Distilled Datamodel with Reverse Gradient Matching", "heading_level": null, "page_id": 10, "polygon": [[133.4267578125, 71.0595703125], [461.25, 71.0595703125], [461.25, 84.3046875], [133.4267578125, 84.3046875]]}, {"title": "Supplementary Material", "heading_level": null, "page_id": 10, "polygon": [[227.25, 96.75], [367.55859375, 96.75], [367.55859375, 109.9248046875], [227.25, 109.9248046875]]}, {"title": "6. More Details of DDM Framework", "heading_level": null, "page_id": 10, "polygon": [[48.75, 218.8828125], [236.25, 218.8828125], [236.25, 229.7109375], [48.75, 229.7109375]]}, {"title": "6.1. Data Clustering of DDM", "heading_level": null, "page_id": 10, "polygon": [[48.75, 238.5], [185.25, 238.5], [185.25, 249.626953125], [48.75, 249.626953125]]}, {"title": "6.2. Accelerating with Hierarchical DDM", "heading_level": null, "page_id": 10, "polygon": [[48.0, 454.5], [243.0, 454.5], [243.0, 464.8359375], [48.0, 464.8359375]]}, {"title": "6.3. <PERSON><PERSON> Gradient Matching for Matching Per-\nformance Enhancement", "heading_level": null, "page_id": 10, "polygon": [[307.5, 170.25], [544.5, 170.25], [544.5, 192.19921875], [307.5, 192.19921875]]}, {"title": "7. Experimental Setting", "heading_level": null, "page_id": 10, "polygon": [[307.5, 501.75], [429.75, 501.75], [429.75, 513.17578125], [307.5, 513.17578125]]}, {"title": "8. More Experiments", "heading_level": null, "page_id": 10, "polygon": [[307.5, 593.25], [418.060546875, 593.25], [418.060546875, 604.0546875], [307.5, 604.0546875]]}, {"title": "8.1. Class-wise DDM vs Cluster-wise DDM", "heading_level": null, "page_id": 10, "polygon": [[306.896484375, 612.0], [507.75, 612.0], [507.75, 623.00390625], [306.896484375, 623.00390625]]}, {"title": "8.2. More Visualization Results", "heading_level": null, "page_id": 12, "polygon": [[48.0, 348.0], [195.75, 348.0], [195.75, 359.068359375], [48.0, 359.068359375]]}, {"title": "8.3. Comparing DDM for Machine Unlearning with\nExact Unlearn", "heading_level": null, "page_id": 12, "polygon": [[48.0, 501.0], [287.47265625, 501.75], [287.47265625, 523.6171875], [48.0, 523.6171875]]}, {"title": "8.4. How Did Different Initializations Influence the\nNetwork?", "heading_level": null, "page_id": 12, "polygon": [[307.5, 292.5], [546.0, 292.5], [546.0, 315.5625], [307.5, 315.5625]]}, {"title": "9. DDM for Analysis of Other Model Behaviors", "heading_level": null, "page_id": 12, "polygon": [[307.5, 537.75], [546.0, 537.75], [546.0, 548.75390625], [307.5, 548.75390625]]}, {"title": "9.1. Inference Function of Certain Test Samples", "heading_level": null, "page_id": 12, "polygon": [[307.5, 660.0], [532.5, 660.0], [532.5, 670.95703125], [307.5, 670.95703125]]}, {"title": "9.2. Model Diagnostic for Low-quality Training\nSamples", "heading_level": null, "page_id": 13, "polygon": [[48.0, 237.75], [288.0, 237.75], [288.0, 261.228515625], [48.0, 261.228515625]]}, {"title": "9.3. Transferability Between Different Networks.", "heading_level": null, "page_id": 13, "polygon": [[48.0, 592.5], [278.25, 592.5], [278.25, 602.25], [48.0, 602.25]]}, {"title": "9.4. To be Explored.", "heading_level": null, "page_id": 13, "polygon": [[307.5, 347.25], [402.75, 347.25], [402.75, 358.48828125], [307.5, 358.48828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 84], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8531, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 102], ["Text", 10], ["SectionHeader", 4], ["ListItem", 3], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["Line", 104], ["Text", 6], ["ListItem", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["ListGroup", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1133], ["Line", 217], ["TextInlineMath", 8], ["Equation", 6], ["Reference", 6], ["Text", 2], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 5889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 891], ["Line", 110], ["TableCell", 48], ["TextInlineMath", 8], ["Text", 7], ["ListItem", 7], ["Equation", 4], ["Reference", 3], ["SectionHeader", 2], ["Table", 1], ["Code", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11931, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 677], ["TableCell", 148], ["Line", 95], ["Text", 5], ["SectionHeader", 4], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2942, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["TableCell", 120], ["Line", 97], ["ListItem", 6], ["Text", 5], ["Reference", 4], ["Caption", 3], ["Table", 2], ["ListGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4530, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["TableCell", 96], ["Line", 90], ["Text", 7], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2334, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["Line", 112], ["ListItem", 29], ["Reference", 27], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 106], ["ListItem", 28], ["Reference", 27], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 107], ["SectionHeader", 9], ["TextInlineMath", 8], ["Text", 4], ["Equation", 3], ["Reference", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 148], ["Span", 89], ["Line", 42], ["Caption", 4], ["Reference", 4], ["Figure", 3], ["FigureGroup", 3], ["Table", 1], ["Text", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5945, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 69], ["Text", 9], ["SectionHeader", 5], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 74], ["TableCell", 72], ["Text", 11], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["Equation", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3974, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Distilled_Datamodel_with_<PERSON><PERSON>_Gradient_Matching"}