# Distilled One-Shot Federated Learning

Yan<PERSON><sup>∗</sup> NSF Center for Big Learning University <NAME_EMAIL>

<PERSON><sup>∗</sup> NSF Center for Big Learning University <NAME_EMAIL>

Xiaolin Li Cognization Lab Palo Alto, CA <EMAIL>

Dapeng Wu NSF Center for Big Learning University <NAME_EMAIL>

Xiyao Ma NSF Center for Big Learning University <NAME_EMAIL>

# Abstract

Current federated learning algorithms take tens of communication rounds transmitting unwieldy model weights under ideal circumstances and hundreds when data is poorly distributed. Inspired by recent work on dataset distillation and distributed one-shot learning, we propose Distilled One-Shot Federated Learning (DOSFL) to significantly reduce the communication cost while achieving comparable performance. In just one round, each client distills their private dataset, sends the synthetic data (e.g. images or sentences) to the server, and collectively trains a global model. The distilled data look like noise and are only useful to the specific model weights, *i.e.,* become useless after the model updates. With this weight-less and gradient-less design, the total communication cost of DOSFL is up to three orders of magnitude less than FedAvg while preserving between 93% to 99% performance of a centralized counterpart. Afterwards, clients could switch to traditional methods such as FedAvg to finetune the last few percent to fit personalized local models with local datasets. Through comprehensive experiments, we show the accuracy and communication performance of DOSFL on both vision and language tasks with different models including CNN, LSTM, Transformer, *etc.* We demonstrate that an eavesdropping attacker cannot properly train a good model using the leaked distilled data, without knowing the initial model weights. DOSFL serves as an inexpensive method to quickly converge on a performant pre-trained model with less than 0.1% communication cost of traditional methods.

# 1 Introduction

Conventional supervised learning dictates that data be gathered into a central location where it can be used to train a model. However, this is intrusive and difficult, if data is spread across multiple devices or clients. For this reason, federated learning (FL) has garnered attention due to its ability to collectively train neural networks while keeping data private. The most popular FL algorithm is FedAvg [\[22\]](#page-11-0). Each iteration, clients perform local training and forward the resulting model weights to a server. The server averages these to obtain a global model. Since learning processes happen at local level, neither the server nor other clients directly observe a client's data.

Federated learning introduces distinct challenges not present in classical distributed machine learning [\[17\]](#page-10-0). The main focus of this paper are expensive communication and statistical heterogeneity. Previous approaches try to learn faster when data is poorly distributed [\[25\]](#page-11-1). They include modifying

<sup>∗</sup>Equal contribution.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates a federated learning process involving a federated server and multiple clients. The process begins with '1. Model Initialization' at 10%, where a model is sent from the federated server to clients. The clients, represented by a tablet, smartphone, autonomous vehicle, and hospital, then engage in '2. Dataset Distillation' using their respective training data, which includes images of a street scene, medical scans, and abstract data representations. The distilled data is then sent back to the server. Step '4. Train on Distilled Data' shows a graph of loss decreasing over steps, indicating model training. Finally, '5. Final Deployment' at 95% shows the updated model being deployed back to the clients.

**3. Send Distilled Data to Server (Only Once)**

Figure 1: Distilled One-Shot Federated Learning. (1) The server initializes a model which is broadcast to all clients. (2) Each client distills their private dataset and (3) transmits synthetic data, labels and learning rates to the server. (4) The server fits its model on the distilled data and (5) distributes the final model to all clients.

the training loss [\[18\]](#page-10-1), using lifelong learning to prevent forgetting [\[32\]](#page-11-2), and correcting local updates using control variates [\[13\]](#page-10-2). These methods improve upon FedAvg, but can still take hundreds of communication rounds, while increasing the amount of information sent to the server per round.

Inspired by dataset distillation [\[37\]](#page-11-3), we propose Distilled One-shot Federated Learning (DOSFL) (see Figure [1\)](#page-1-0) to reduce the communication cost by up to 3 orders of magnitude. DOSFL requires only one round of communication between a server and its clients. Each client distills their data and uploads learned synthetic data, label and learning rate to the server, instead of transmitting bulky gradients or weights. Even large datasets containing thousands of examples can be compressed to only a few fabricated examples. The server then interleaves the clients' distilled data together, using them to train a global model. To achieve good results even when client data is poorly distributed, we leverage soft labels [\[33\]](#page-11-4) and introduce two new techniques: soft reset and random masking.

Due to the reduction in rounds, we claim a communication reduction of up to 99.9% compared to FedAvg while achieving similar accuracy. DOSFL can also preserve between 93% to 99% of centralized training's performance when data is independently and identically distributed (IID). Furthermore, under more challenging assumptions of low participation and asynchronous update with multiple rounds, DOSFL can achieve even better results and preserve higher centralized performance, *i.e.,* almost 99% on IID Federated MNIST. Unlike dataset compression, fabricated inputs in dataset distillation (see Appendix [B.1](#page-13-0) and [B.2\)](#page-13-1)—along with the corresponding labels and learning rate—are generated by a specific model weight distribution and become useless after the model updates. We experimentally show that the final performance of a DOSFL is strongly dependent on the model parameters used to train on distilled data. Without knowing the the exact initial weights distributed by the server, an eavesdropper cannot reproduce the global model with leaked distilled data.

We believe DOSFL is part of a new paradigm in the field of FL. So far, nearly every FL algorithm communicates model weights or gradients. While effective, breaking from this pattern offers many benefits, such as low communication [\[9\]](#page-10-3) or private model architectures [\[16\]](#page-10-4). We hope that DOSFL, along with related work, may inspire the machine learning community to explore possible techniques for weight-less and gradient-less FL.

# 2 Related Work

## 2.1 Federated Learning

Since the introduction of FedAvg in 2016 [\[22\]](#page-11-0), there has been an explosion of work directed towards the problem of statistical heterogeneity. When statistical hetergeniety is high, convergence for FedAvg slows and becomes unstable [\[17\]](#page-10-0). The issue is that, the difference between the local losses and the global objective—their weighted sum—may be large. As such, minimizing a particular local loss does not ensure that the global loss is also minimized. This is problematic, even when the losses are convex and smooth [\[19\]](#page-10-5). In applications where privacy loss can be tolerated, Zhao et al. demonstrate massive gains in performance by making as little as 5% of data public [\[40\]](#page-12-0).

Numerous successors to FedAvg have been suggested. Server momentum introduces a global momentum parameter that improves convergence theoretically and experimentally [\[20\]](#page-11-5). In Shoham et al., the loss is modified with Elastic Weight Consolidation to prevent forgetting as clients perform local training [\[32\]](#page-11-2). SCAFFOLD uses control variates, namely the gradient of the global model, to address drifting among clients during local training [\[13\]](#page-10-2). These schemes, while effective, at least double the per round communication cost. More efforts have not had this drawback. The work in [\[26\]](#page-11-6) proposes several federated version of optimizers, FedAdaGrad, FedAdam and FedYogi, to provide better convergence for non-IID data. The issue of objective inconsistency was discussed in [\[36\]](#page-11-7) and solved by the proposed FedNova, which averages the normalized local gradients.

While faster learning decreases the total number of communication rounds, strategies have been devised to explicitly reduce communication costs. FedPAQ quantizes local updates before transmission, with averaging happens at both client and server sides [\[27\]](#page-11-8). Sparsifying the weights may perform better than FedAvg alone [\[29\]](#page-11-9). Asynchronous model updates have also been explored, using adaptive weighted averaging to combat staleness, combined with a proximal loss, [\[39\]](#page-12-1) and updating deeper layer less frequently than shallower layers [\[4\]](#page-10-6). SAFA takes a semi-synchronous approach; only up-to-date and deprecated clients synchronize with the server [\[38\]](#page-12-2).

A few papers have made first steps towards one-shot FL. Guha et al. try different heuristics for selecting the client models which would form the best ensemble [\[9\]](#page-10-3). By swapping weight averaging for ensembling, only one round of communication is necessary. Upper and lower bounds have been proven for one-shot distributed optimization, along with an order optimal algorithm [\[28,](#page-11-10) [31\]](#page-11-11). The extent to which these results apply to FL of neural networks is unknown as the local losses must be convex and drawn from the same probability distribution. Recently, knowledge distillation has been introduced to the field of FL to reduce the computation costs for edge devices [\[10\]](#page-10-7). In addition, group knowledge transfer reduces communication costs by accelerating convergence.

## 2.2 Distillation

There is a wealth of literature studying dataset compression, while maintaining the most crucial features for training models. These methods include dataset pruning [\[1\]](#page-10-8) and core set construction [\[2,](#page-10-9) [34,](#page-11-12) [30\]](#page-11-13), which keep the examples that are measured to be more useful for training and remove the rest. The drawback of drawing distilled images from the original dataset is that the level of compression achieved is much lower than that of dataset distillation, which is exempt from the requirement that distilled data be real [\[37\]](#page-11-3). Dataset distillation [\[37\]](#page-11-3) was introduced by Wang et al., to compress a large dataset with thousands to millions of images down to only a few synthetic training images. The key idea is to use gradient descent to learn the features most helpful for rapidly training a neural network. Given some model parameters  $\theta_0$ , dataset distillation minimizes the loss of adapted parameters  $\theta_1$ , obtained by performing gradient descent on  $\theta_0$  and the distilled data. This procedure resembles meta-learning, which performs task-specific adaption followed by a meta-update [\[6\]](#page-10-10). With dataset distillation, 10 synthetic digits can train a neural network from 13% to 94% test accuracy in 3 iterations, near the 98% test accuracy reached by training on MNIST.

Dataset distillation originally was limited to only image classification tasks, because the distilled labels were predetermined and fixed. Learnable or soft labels not only decrease the number of required labels, but also expand dataset distillation to language tasks such as sentiment classification [\[33\]](#page-11-4). Soft labels have a long history, being proposed for model distillation by Hinton et al. [\[11\]](#page-10-11) and for k-nearest neighbors by El Gayar et al. [\[5\]](#page-10-12). Using soft label dataset distillation, Sucholutsky et al.

<span id="page-3-0"></span>Algorithm 1 Distilled One-Shot Federated Learning

1: Initialize server weights  $\theta_0$ 2: for clients  $k = 1, \ldots, N$  do 3: DISTILLDATA $(k, \theta_0)$ 4: Send distilled data to the server 5: end for 6: Merge clients' distilled data into a single sequence  $\{(\tilde{x}_j, \tilde{y}_j, \tilde{\eta}_j)\}_{j=1}^{NS_d}$ 7: for  $i = 0, 1, \ldots, E_d - 1$  do 8: **for**  $j = 0, 1, ..., NS_d$  **do**<br>9: **Number of adaptation** 9: Number of adaptations  $a = iNS_d + j$ <br>10:  $\theta_{a+1} \leftarrow \theta_a - \tilde{n}_i \nabla \ell(\theta_a; \tilde{x}_i, \tilde{y}_i)$ 10:  $\theta_{a+1} \leftarrow \theta_a - \tilde{\eta}_j \nabla \ell(\theta_a; \tilde{x}_j, \tilde{y}_j)$ <br>11: **end for** end for 12: end for 13: 14: function DISTILLDATA $(k, \theta_0)$ 15: Initialize  $\{(\tilde{x}_j, \tilde{y}_j, \tilde{\eta}_j)\}_{j=1}^{S_d}$ 16: **for**  $e = 1, 2, ..., E$  **do** 17: Get a minibatch B from client's dataset  $\mathcal{X}_k$ 18: **for**  $i = 0, 1, ..., E_d - 1$  **do** 19: **for**  $j = 0, 1, ..., S_d - 1$  **do** 20: Number of adaptations  $a = iS_d + j$ 21:  $\theta_{a+1} = \theta_a - \tilde{\eta}_j \nabla \ell(\theta_a; \tilde{x}_j, \tilde{y}_j)$ <br>22: **end for** end for 23: end for 24:  $\tilde{x} \leftarrow \tilde{x} - \alpha \nabla_{\tilde{x}} \ell(\theta_{E_dS_d}; \mathcal{B})$ 25:  $\tilde{\eta} \leftarrow \tilde{\eta} - \alpha \nabla_{\tilde{\eta}} \ell(\theta_{E_dS_d}; \mathcal{B})$ 26: if using soft labels then 27:  $\tilde{y} \leftarrow \tilde{y} - \alpha \nabla_{\tilde{y}} \ell(\theta_{E_dS_d}; \mathcal{B})$ 28: end if 29: end for 30: **return**  $\{(\tilde{x}_i, \tilde{y}_i, \tilde{\eta}_i)\}_{i=1}^{S_d}$ 31: end function

were able to train LeNet to 96\% accuracy with only 10 images [\[33\]](#page-11-4). Examples of distilled data (i.e., text, grey image and RGB image) are shown in Figure [1.](#page-1-0)

# 3 Distilled One Shot Federated Learning

Suppose we have numbered clients  $1, \ldots, N$  each with their own local models  $f_{\theta_1}, \ldots, f_{\theta_N}$  with parameters  $\theta_1, \ldots, \theta_N$  and loss functions  $L_1, \ldots, L_N$ . Given some probability vector  $p = (p_1, \ldots, p_N)$ (each  $0 \le p_k \le 1$  and  $\sum_k p_k = 1$ ), our goal is to find some parameters  $\theta^*$  that minimize the weighted sum  $L = \sum_{k=1}^{N} p_k L_k(\theta)$ .

$$
\theta^* = \arg\min_{\theta} \sum_{k=1}^{N} p_k L_k(\theta)
$$
\n(1)

However, we often do not have distinct loss functions but rather the same loss function  $\ell$  evaluated on distinct private datasets. Let  $\ell(\theta; x, y)$  to be the loss of a single example  $(x, y)$ . Following [\[37\]](#page-11-3), we define  $\ell(\theta; \{(x_i, y_i)\})$  to be average loss of all the data points in the set  $\{(x_i, y_i)\}$ . Thus, for each client  $k = 1, ..., N$  with a dataset  $\mathcal{X}_k, L_k(\theta) = \ell(\theta; \mathcal{X}_k)$ .

Our solution consists of 3 steps. These steps are summarized in Algorithm [1.](#page-3-0)

- 1. A central server randomly initializes model parameters  $\theta_0$ . This can be distributed to the clients as a random seed.
- 2. The clients distill their datasets. Start by initializing the distilled data  $\tilde{x}$ , distilled label  $\tilde{y}$ , and distilled learning rate  $\tilde{\eta}$ . Each entry in  $\tilde{x}$  is drawn from a standard normal distribution, while

 $\tilde{\eta}$  is set to a predefined value  $\eta_0$ . The distilled labels are initialized as either one-hot vectors for classification problems or normal distributed random vectors for regression problems. Adapt these into  $\theta_1$  via gradient descent.

$$
\theta_1 = \theta_0 - \tilde{\eta} \ell(\theta_0; \tilde{x}, \tilde{y}) \tag{2}
$$

Afterwards, minimize the loss of  $\theta_1$  evaluated on a batch of real data B.

This can be done with a sequence of distilled data  $\{(\tilde{x}_j, \tilde{y}_j, \tilde{\eta}_j)\}_{j=1}^{S_d}$  ( $S_d$  is the distill step), repeated distill epoch  $E_d$  times. Each example successively adapts  $\theta_0$  until we have  $\theta_{E_dS_d}$ after  $E_dS_d$  gradient descent updates. This dramatically increases the expressive power of dataset distillation at the expense of compute time.

3. The clients upload the distilled data to the server. If  $S_d > 1$ , the server sorts the distilled data by index, e.g.  $\{x_1, x_2, x_3\}, \{y_1, y_2, y_3\}$  from clients 1 and 2 become  $\{x_1, y_1, x_2, y_2, x_3, y_3\}$ where  $x_j, y_j$  are 3-tuples. The server then trains its own model on the combined sequence.

The last step can cause issues when the data is non-IID. Consider two clients 1 and 2 with distilled examples  $x_1$  and  $y_1$  respectively with  $E_d = 1$ . The server first trains  $\theta_0$  on  $x_1$ , arriving at  $\theta_1$ , which is then trained on  $y_1$ . But  $y_1$  has been distilled to train  $\theta_0$ . To combat this interference, we introduce two new techniques for improving performance on non-IID data.

Soft resets sample the starting parameters,  $\theta_0$  from a Gaussian distribution, around the server's parameters  $\mathcal{N}(\bar{\theta}_0, \sigma_{sr}^2)$ . By sampling  $\theta_0$  between distillation iterations, dataset distillation learns more robust examples capable of training any model with weights  $\theta \sim \mathcal{N}(\theta_0, \sigma_{sr}^2)$ . This technique is based off of the "hard resets" introduced in [\[37\]](#page-11-3), which completely re-initializes  $\theta_0$ . Data distilled with "hard resets" can be used on any randomly initialized model, but cannot train models to the same level of accuracy as models trained on data distilled without resets.

Random masking randomly selects a fraction  $p_{rm}$  of the distilled data at each training iteration and replaces it with a random tensor. The random tensors randomly adjusts the model during training, while also reducing the amount of distilled data to actually train the starting parameters. After the training iteration, the original distilled data are restored. Now, sequences of distilled data can still train a model even when there is interference from other distilled steps. However, resetting and storing distilled data is compute and memory intensive, which slows down distillation.

<span id="page-4-0"></span>

# 4 Experiments

We evaluate DOSFL on several federated classification tasks. Because of this, cross entropy loss is used for all experiments. To train the distilled data, we use ADAM [\[14\]](#page-10-13) with a learning rate  $\alpha$  that is halved every  $\tau$  epochs. We have  $\alpha = 0.01, \tau = 40, \alpha = 0.01, \tau = 10$ , and  $\alpha = 0.1, \tau = 30$  for federated MNIST, IMDB, and TREC-6 respectively. These hyperparameters are mirrored from [\[33\]](#page-11-4) and have been found to be near-optimal. For federated Sent140, we replicated the hyperparmeters from federated IMDB. Clients distill the data for  $E = 30$  epochs for image datasets and  $E = 50$ epochs for text datasets with a batch size of  $B = 512$ . All experiments were run on an Nvidia M40 GPU and Intel Xeon E5-2695 CPU, taking 2-3 minutes per client for 100 client federated MNIST, < 1 minute per client for 29 client federated IMDB and 100 client TREC-6, and 14-15 minutes for 100 client federated Sent140. We use the default train and test splits associated with each dataset.

The client-server architecture is simulated by partitioning a dataset into subsets, and then distilling these subsets. The server models have their weights Xavier initialized [\[7\]](#page-10-14). The weights are then replicated across each client. Following the methodology of McMahan et al., IID partitions are created by randomly dividing the dataset into subsets [\[22\]](#page-11-0). For non-IID partitions, we first sort the entire dataset by label and then divide it into  $Ns$  shards of equal length. Starting from  $N$  empty subsets, the shards are randomly assigned to the subsets until each has  $s$  shards. As  $s$  increases, the partition becomes more IID, with subsets more likely to contain examples from each class.

## 4.1 Image Classification

We first test DOSFL on 10 and 100 client Federated MNIST with different combinations of soft labels, soft resets, and random masking. All federated MNIST experiments use LeNet as the model architecture [\[15\]](#page-10-15). Our distilled data are not single examples but batches with size  $B_d = 10$ . Within

| Additions           | 10 clients    |               | 100 clients   |               |
|---------------------|---------------|---------------|---------------|---------------|
|                     | IID           | non-IID       | IID           | non-IID       |
| None                | 94.02%        | 51.55%        | 88.27%        | 59.25%        |
| Soft label (SL)     | <b>95.64%</b> | 61.26%        | <b>91.53%</b> | 63.32%        |
| Soft reset (SR)     | 93.05%        | 77.71%        | 88.41%        | <b>78.29%</b> |
| Random masking (RM) | 93.68%        | 70.13%        | 87.34%        | 69.87%        |
| SL, SR              | 90.87%        | <b>78.83%</b> | 85.54%        | 76.55%        |
| SR, RM              | 88.07%        | 78.54%        | 82.38%        | 73.62%        |
| SR, RM, SL          | 88.95%        | 78.22%        | 83.71%        | 77.58%        |

<span id="page-5-0"></span>Table 1: DOSFL accuracy on federated MNIST. For reference, LeNet can reach a test set accuracy of  $> 99\%$  on MNIST and 93% with dataset distillation. The highest value in each column is **bold**.

<span id="page-5-1"></span>Table 2: DOSFL performance on all federated learning tasks. Instead of 10 and 100 clients for TREC-6, we have 2 and 29 respectively. For the comparison with non-IID FedAvg, we bold the greater accuracy.

| Dataset                     | 10 clients       |                  |                  | 100 clients      |                  |                  | Centralized     |                 |
|-----------------------------|------------------|------------------|------------------|------------------|------------------|------------------|-----------------|-----------------|
|                             | IID              | non-IID          |                  | IID              | non-IID          |                  | DOSFL           | -               |
|                             | <b>DOSFL</b>     | <b>DOSFL</b>     | FedAvg           | DOSFL            | <b>DOSFL</b>     | FedAvg           | <b>DOSFL</b>    |                 |
| <b>MNIST</b><br><b>IMDB</b> | 90.87%<br>81.04% | 78.83%<br>79.86% | 44.01%<br>50.02% | 85.54%<br>71.94% | 76.55%<br>70.75% | 95.26%<br>60.45% | 93.61%<br>78.3% | 98.86%<br>86.1% |
| TREC-6<br>Sent140           | 83.60%<br>78.10% | 73.40%<br>78.00% | 49.80%<br>36.55% | 79.00%<br>73.50% | 73.60%<br>69.50% | 13.70%<br>52.58% | 79.2%<br>76.62% | 89.4%<br>80.1%  |

each batch, the labels are initialized to one of each class, *e.g.* one label is '1', one label is '2', etc. Using soft labels,  $B_d$  could be made much smaller without loss of performance. After the server model has trained on distilled data from the clients, its accuracy is measured on a test set. Each experiment is run 5 times, and the best result is reported in Table [1.](#page-5-0)

The distill steps are  $S_d = 30$ , the distill epochs are  $E_d = 3$ , and the initial distill learning rate is  $\eta_0 = 0.02$ . Of the proposed additions to dataset distillation, soft resets provide the largest jump in non-IID performance, followed by random masking and soft labels. The reset variance  $\sigma_{sr}^2 = 0.2$ and masking probability  $p_{rm} = 0.3$  were chosen from the search spaces  $\{0.1, 0.2, \ldots, 0.6\}$  and  $\{0.1, 0.2, 0.3, 0.5, 1\}$ . However, soft labels also boost accuracy when data is IID, where as the other two methods cause dips in the final accuracy. The distillation additions are not additive; even with all add-ons, non-IID DOSFL caps at ∼ 79% test accuracy. Surprisingly, the behavior of these additions changes depending on the number of clients. While accuracies in the 100 client case are lower in general, soft resets and no additions work better.

## 4.2 Text Classification

To show that DOSFL is not limited to image-based tasks, we test DOSFL on federated IMDB (sentiment analysis) [\[21\]](#page-11-14), federated TREC-6 (question classification) [\[35\]](#page-11-15), and federated Sent140 (sentiment analysis) [\[8\]](#page-10-16). Directly applying dataset distillation for language tasks is challenging as text data is discrete. Each token is a one-hot vector with dimension equal to the number of tokens, which can be in the thousands. To overcome this issue, we use pre-trained GloVe embeddings with a look up table to convert one-hot token ids to word vectors in 100D Euclidean space [\[24\]](#page-11-16). Distilled sentences now are fixed-size real-valued matrices. Real sentences are also padded or truncated to the same fixed length: 200 for federated IMDB and Sent140, 30 for federated TREC-6.

The results, best out of 5 runs, are provided in Table [2.](#page-5-1) We also provide the accuracy of non-IID FedAvg after an equivalent amount of communication. This allows DOSFL and FedAvg to be compared in an equal communication cost setting. We tuned the FedAvg hyperparameters to maximize initial learning: local epochs  $E = 5$ , batch size  $B = 10$ , and learning rate  $\eta = 0.01$ . The batch size was the lowest in the considered range  $\{10, 20, 50, 100, 200, 500\}$  and the learning rate

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image contains two line graphs side-by-side, both plotting accuracy against the number of distilled clients. The left graph, labeled (a) Accuracy for 10 clients, shows two lines: a blue line representing IID and an orange line representing Non-IID. The blue line quickly rises to nearly 100% accuracy with 2 distilled clients and stays there. The orange line rises more gradually, reaching about 70% accuracy with 4 clients, peaking around 80% at 6 clients, and then fluctuating slightly to end around 80% at 10 clients. The right graph, labeled (b) Accuracy for 100 clients, also shows IID (blue line) and Non-IID (orange line) accuracy. The blue line rapidly increases to over 90% accuracy within the first 10 clients and plateaus around 95-98%. The orange line shows a much more volatile trend, starting around 10% accuracy, rising to about 50% by 10 clients, then fluctuating significantly between 40% and 80% as the number of distilled clients increases to 100, ending around 80% accuracy.

(a) Accuracy for 10 clients. The final accuracies are 98.85% and 79.79%.

(b) Accuracy for 100 clients. The final accuracies are 94.28% and 79.53%.

Figure 2: Performance of LP-DOSFL with low participation on Federated MNIST, with soft resets and soft labels, vs. the number of clients distilled.

the largest in  $\{10^{-2}, 3 \times 10^{-3}, 10^{-3}, 3 \times 10^{-4}, 10^{-4}\}$ . Note that, the preserved accuracy, defined as the DOSFL accuracy over baseline performance, of all tasks is at least 93%. This shows that DOSFL is capable of handling different tasks with small or large datasets in one round.

For federated IMDB, we use a simple CNN model called TextCNN. We test DOSFL with soft labels for 10 and 100 clients, IID and non-IID federated IMDB. Here the distill steps  $S_d = 5$ , the distill epochs  $E_d = 10$ , the distill batch size  $B_d = 1$ , and the starting distill learning rate is  $\eta_0 = 0.01$ . Since there exists only 2 classes in IMDB dataset (positive or negative sentiment), non-IID performance is within 2% of IID. Approximately  $\frac{3}{4}$  clients contain labels from all classes, whereas in federated MNIST no client can have more than 4 classes.

For federated TREC-6, we adopt a Bi-LSTM model to show that DOSFL can be used with non-CNN models. We use 2 and 29 for the number of clients, since the size of the dataset is 5452 and the client dataset sizes must be divisible by the shard count  $s = 2$ . The amount of training data for the 2 client federated TREC-6 and 10 client federated IMDB are almost equal (2726  $\sim$  2500). Similarly, 29 client federated TREC-6 is comparable with 100 client federated IMDB (188  $\sim$  250). We have  $S_d = 2$ ,  $E_d = 1$ ,  $B_d = 1$ , and  $\eta_0 = 1.5$ . Due to the low number of clients, we were able to reduce the amount of distilled data needed compared to the previous two tasks. Unlike federated IMDB, there is a larger  $\sim 6\%$  gap in accuracy between the IID and non-IID settings. Furthermore, we extend DOSFL to a larger dataset, Sent140, using TextCNN. The hyperparameters are distill steps  $S_d = 5$ , distill epochs  $E_d = 15$ ,  $B_d = 1$ , and initial learning rate  $\eta_0 = 0.3$ .

## 4.3 DOSFL with Stragglers and Low Participation

So far The above mentioned settings assumes synchronous full participation which is highly unlikely in the real-world. We design an alternate version of DOSFL for when the participation rate is extremely low such that client communication is almost one-by-one. First, the server selects only one client to distill its data. Afterwards, the server updates the global model by training on the distilled data. A different client then performs dataset distillation targeting the updated parameters. This process repeats until each client has distilled their data. Hence, the global model is updated  $N$  times, once for each client. The communication is highly serial; the next client can only begin distillation after the current client finishes.

We name this setting LP-DOSFL and evaluate its performance on MNIST in Figure [2,](#page-6-0) using the best out of 5 trials again. Surprisingly, LP-DOSFL achieves almost 99% accuracy when MNIST is IID, 7% more than vanilla DOSFL. This advantage disappears when the data becomes non-IID. The final accuracy of LP-DOSFL is only  $1\%$  larger than plain DOSFL. The reason for this is simple: when the clients' datasets are IID, a model trained on one client's dataset will transfer to the others. Encouragingly, the server model achieves its final accuracy after as few as 15% of the clients finish dataset distillation. This is true even when the data is non-IID, although it takes longer (around 40% of the clients). Thus, the total amount of communication needed for LP-DOSFL is less than DOSFL.

| Dataset      | Model          | Data size        | Model size   | Break even round |
|--------------|----------------|------------------|--------------|------------------|
| <b>MNIST</b> | LeNet          | $28 \times 28$   | 61,706       | 19.83            |
| <b>IMDB</b>  | <b>TextCNN</b> | $200 \times 100$ | 120,601      | 8.29             |
| TREC-6       | Bi-LSTM        | $30 \times 100$  | 404, 406     | 0.14             |
| Sent140      | <b>TextCNN</b> | $200 \times 100$ | 120,601      | 8.29             |
| Sent140      | Transformer    | $200 \times 100$ | 13, 151, 238 | 0.54             |

<span id="page-7-0"></span>Table 3: Communication comparison between DOSFL and FedAvg. The accuracy at the break even round is given for FedAvg on the non-IID partition among 100 clients.

<span id="page-7-1"></span>Table 4: Communication comparison between DOSFL and FedAvg. The accuracy at the break even round is given for FedAvg on the non-IID partition among 100 clients.

| Dataset        | Model              | 10 Clients               |                          | 100 Clients        |                          |
|----------------|--------------------|--------------------------|--------------------------|--------------------|--------------------------|
|                |                    | Comm.<br>Reduction       | DOSFL to<br>FedAvg Ratio | Comm.<br>Reduction | DOSFL to<br>FedAvg Ratio |
| <b>MNIST</b>   | <b>LeNet</b>       | FedAvg does not converge | N/A                      | 0.44               | 0.44                     |
| <b>IMDB</b>    | <b>TextCNN</b>     | FedAvg does not converge | 66.83%                   | 3.0                | 3.0                      |
| <b>TREC-6</b>  | <b>Bi-LSTM</b>     | 99.22%                   | 127.76                   | 99.71%             | 356.4                    |
| <b>Sent140</b> | <b>TextCNN</b>     | 87.98%                   | 8.3                      | 93.26%             | 14.8                     |
| <b>Sent140</b> | <b>Transformer</b> | FedAvg does not converge | 99.90%                   | 99.90%             | 1012.5                   |

# 5 Discussion

## 5.1 Communication

We now compare the total communication cost (TCC) of DOSFL with that of FedAvg measured in the amount of scalar values sent between the clients and the server. Since the server model's initialization can be distributed as a random seed, we ignore the cost of the first server-to-client transmission. Let  $C = 0.1$  be the fraction of the N clients that participate each round. FedAvg sends  $\Theta$  server model parameters to each client, who responds with locally trained parameters. Let  $T$  be the number of communication rounds.

$$
TCC_{fedavg} = NCO(2T - 1)
$$
\n(3)

For DOSFL, we only need to consider the single expense of sending distilled data to the server. Let  $n_{data}$  be the number of elements in each data point and  $B_d$  be the batch size of the distilled data.

$$
TCC_{dosfl} = NS_d(n_{data} + n_{cls} + 1)B_d
$$
\n<sup>(4)</sup>

Both formulas can be used to calculate the number of communication rounds—the break even round—needed for lifetime cost of FedAvg to equal DOSFL for the tasks in Section [4.](#page-4-0)

$$
T_{break\;even} = \frac{1}{2} \left( \frac{S_d (n_{data} + n_{cls} + 1)B_d}{C\Theta} + 1 \right) \tag{5}
$$

Note that this value is independent of the number of clients  $N$ . Break even rounds for federated MNIST, IMDB, TREC-6, and Sent140 are provided in Table [3](#page-7-0) along with the data and model size. We also investigate potential communication savings when using larger models, such as Transformers, for Sent140. The higher break even round for MNIST, compared to the text tasks, is due to LeNet having significantly fewer parameters than either TextCNN or Bi-LSTM. The best accuracy at the break even round  $T_{break\ even}$  is reported in Table [2.](#page-5-1)

Finally, we conclude our discussion of communication efficiency by comparing DOSFL with FedAvg under an iso-accuracy setting. Let  $T_{iso\ accuracy}$  be the number of communication rounds required for FedAvg to reach the accuracy achieved in Table [2.](#page-5-1) Define the DOSFL to FedAvg ratio as

$$
ratio = \frac{TCC_{dosfl}}{TCC_{fedavg}} = \frac{S_d(n_{data} + n_{cls} + 1)B_d}{NC\Theta(2T_{iso\ accuracy} - 1)}
$$
(6)

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image contains two plots. The left plot is a heatmap showing the accuracy of eavesdropper initialization based on different actual initializations. The rows represent the actual initialization methods: Xavier, Xavier Unif, Kaiming, Kaiming out, Orthogonal, and Default. The columns represent the eavesdropper initialization methods: Xavier, Xavier Unif, Kaiming, Kaiming out, Orthogonal, and Default. The values in the heatmap range from 11 to 72, with darker shades of blue indicating higher accuracy. The right plot is a line graph showing the accuracy over distance from the original initialization for two rays, Ray 1 and Ray 2. The x-axis represents the distance from the original initialization, ranging from 0 to 50. The y-axis represents accuracy, ranging from 0 to 80. Ray 1 shows a sharp drop in accuracy from around 90 to 20 at a distance of approximately 20. Ray 2 also shows a similar sharp drop in accuracy from around 90 to 20 at a distance of approximately 20.

(a) Performance matrix of eavesdroppers training on (b) Accuracy of eavesdropper on leaked distilled data leaked distilled data without knowing the initialization vs. distance from initialized weights in two random method used for distillation. directions.

Figure 3: Privacy and security analysis of DOSFL.

We can also calculate the percent communication reduction using the above ratio.

$$
reduction = \frac{TCC_{fedavg} - TCC_{dosfl}}{TCC_{fedavg}} = 1 - \frac{1}{ratio}
$$
\n<sup>(7)</sup>

We choose the smallest iso-accuracy round  $T_{iso\; accuracy}$  out of 5 trials. The values for both quantities is shown in Table [4.](#page-7-1) For some tasks, FedAvg fails to converge due to having only 1 client update the gradient each round. In general, DOSFL saves more communication when the size of the model increases or when the dataset becomes more challenging. Besides MNIST, communication savings are up to 3 orders of magnitude.

DOSFL provides an efficient way to trade computation and a few-to-none percentage points of accuracy for a great amount of communication cost reduction. Next, clients can choose to either adopt another Federated Learning algorithm to continue improving the global model or personalize by training on local data. As such, DOSFL is best suited for cross-silo FL, where 2-100 organizations seek to learn a shared model without sharing data [\[12\]](#page-10-17). In cross-silo learning, participants likely would be able to dedicate hardware for the sole purpose of FL. Big models are also probable, and the communication savings of DOSFL increase as the models get larger. Computation resources are also cheaper to acquire compared to communication resources. A company can purchase dedicated GPUs and use them for years at reasonable cost without losing much of their intrinsic value.

## 5.2 Privacy and security

Suppose the server or a client attempted to learn private information about other clients. To the human eye, the distilled images or text appear random (see Appendix [B.1,](#page-13-0) [B.2\)](#page-13-1). So the only known way to extract information from the distilled data is train the targeted model. Because distilled data are targeted towards a *specific* initialization, training a differently initialized model on distilled data should result in a low accuracy model. Therefore, we examine the security of DOSFL against an eavesdropping attack and show that an attacker cannot reproduce the global model without the server's initial weights. Referring to Figure [1,](#page-1-0) assume that the attacker intercepts the distilled data, labels and learning rates from each client between Step 2 and 3.

We assume that the model initialization, Step 1, is unknown to the attacker. This can be easily achieved by being predefined offline or via encryption. In Figure [3a,](#page-8-0) the attacker does not have access to the model distribution and tries to guess different initialization methods. All results are averages of 5 trials. These include Xavier, Xavier uniform, Kaiming, Kaiming uniform, orthogonal and the default PyTorch initialization methods [\[23\]](#page-11-17). All attacks fail to reproduce the same level of performance of the global model as most attacks struggle to reach over 50% accuracy. The accuracy of the eavesdropper drops off with increased distance from the original model. Given the initial weights  $\theta_0$  and a vector v whose components follow a standard Normal distribution, we can create perturbed parameters  $\ddot{\theta}_0 = \theta_0 + kv/||v||$  that is k distance away. As seen in Figure [3b,](#page-8-0) the final accuracy decreases suddenly with increased distance from the original weights.

As such, the privacy of DOSFL should be *no worse* than plain FedAvg. There are some security risks from other types of attacks. In [\[37\]](#page-11-3), the authors used dataset distillation for data poisoning attacks. Images were distilled such that, after one gradient descent step, the final model would misclassify the attack category. However, this security risk is also present with FedAvg and most other FL algorithms: clients can deliberately upload poisoned weights to the server [\[3\]](#page-10-18). For DOSFL, bounding the gradient value or using momentum when training the global model on distilled data could mitigate this threat. While defenses against other types of attacks are beyond the scope of this paper, we believe that existing differential privacy and secure multi-party computation tools will prove sufficient.

# References

- <span id="page-10-8"></span>[1] Anelia Angelova, Yaser Abu-Mostafam, and Pietro Perona. Pruning training sets for learning of object categories. In *2005 IEEE Computer Society Conference on Computer Vision and Pattern Recognition (CVPR'05)*, volume 1, pages 494–501. IEEE, 2005.
- <span id="page-10-9"></span>[2] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-10-18"></span>[3] Eugene Bagdasaryan, Andreas Veit, Yiqing Hua, Deborah Estrin, and Vitaly Shmatikov. How to backdoor federated learning. In *International Conference on Artificial Intelligence and Statistics*, pages 2938–2948. PMLR, 2020.
- <span id="page-10-6"></span>[4] Yang Chen, Xiaoyan Sun, and Yaochu Jin. Communication-efficient federated deep learning with asynchronous model update and temporally weighted aggregation. *arXiv preprint arXiv:1903.07424*, 2019.
- <span id="page-10-12"></span>[5] Neamat El Gayar, Friedhelm Schwenker, and Günther Palm. A study of the robustness of knn classifiers trained using soft labels. In *Proceedings of the Second International Conference on Artificial Neural Networks in Pattern Recognition*, ANNPR'06, page 67–80, Berlin, Heidelberg, 2006. Springer-Verlag.
- <span id="page-10-10"></span>[6] Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In *Proceedings of the 34th International Conference on Machine Learning-Volume 70*, pages 1126–1135. JMLR. org, 2017.
- <span id="page-10-14"></span>[7] Xavier Glorot and Yoshua Bengio. Understanding the difficulty of training deep feedforward neural networks. In *Proceedings of the thirteenth international conference on artificial intelligence and statistics*, pages 249–256, 2010.
- <span id="page-10-16"></span>[8] Alec Go, Richa Bhayani, and Lei Huang. Twitter sentiment classification using distant supervision. *CS224N project report, Stanford*, 1(12):2009, 2009.
- <span id="page-10-3"></span>[9] Neel Guha, Ameet Talwlkar, and Virginia Smith. One-shot federated learning. *arXiv preprint arXiv:1902.11175*, 2019.
- <span id="page-10-7"></span>[10] Chaoyang He, Murali Annavaram, and Salman Avestimehr. Group knowledge transfer: Federated learning of large cnns at the edge. In *Advances in Neural Information Processing Systems 33*, 2020.
- <span id="page-10-11"></span>[11] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-10-17"></span>[12] Peter Kairouz, H Brendan McMahan, Brendan Avent, Aurélien Bellet, Mehdi Bennis, Arjun Nitin Bhagoji, Keith Bonawitz, Zachary Charles, Graham Cormode, Rachel Cummings, et al. Advances and open problems in federated learning. *arXiv preprint arXiv:1912.04977*, 2019.
- <span id="page-10-2"></span>[13] Sai Praneeth Karimireddy, Satyen Kale, Mehryar Mohri, Sashank J Reddi, Sebastian U Stich, and Ananda Theertha Suresh. Scaffold: Stochastic controlled averaging for on-device federated learning. *arXiv preprint arXiv:1910.06378*, 2019.
- <span id="page-10-13"></span>[14] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014.
- <span id="page-10-15"></span>[15] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-10-4"></span>[16] Daliang Li and Junpu Wang. Fedmd: Heterogenous federated learning via model distillation. *arXiv preprint arXiv:1910.03581*, 2019.
- <span id="page-10-0"></span>[17] Tian Li, Anit Kumar Sahu, Ameet Talwalkar, and Virginia Smith. Federated learning: Challenges, methods, and future directions. *arXiv preprint arXiv:1908.07873*, 2019.
- <span id="page-10-1"></span>[18] Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated optimization in heterogeneous networks. *arXiv preprint arXiv:1812.06127*, 2018.
- <span id="page-10-5"></span>[19] Xiang Li, Kaixuan Huang, Wenhao Yang, Shusen Wang, and Zhihua Zhang. On the convergence of fedavg on non-iid data. *arXiv preprint arXiv:1907.02189*, 2019.

- <span id="page-11-5"></span>[20] Wei Liu, Li Chen, Yunfei Chen, and Wenyi Zhang. Accelerating federated learning via momentum gradient descent. *arXiv preprint arXiv:1910.03197*, 2019.
- <span id="page-11-14"></span>[21] Andrew L. Maas, Raymond E. Daly, Peter T. Pham, Dan Huang, Andrew Y. Ng, and Christopher Potts. Learning word vectors for sentiment analysis. In *Proceedings of the 49th Annual Meeting of the Association for Computational Linguistics: Human Language Technologies*, pages 142–150, Portland, Oregon, USA, June 2011. Association for Computational Linguistics.
- <span id="page-11-0"></span>[22] H Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, et al. Communicationefficient learning of deep networks from decentralized data. *arXiv preprint arXiv:1602.05629*, 2016.
- <span id="page-11-17"></span>[23] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, Alban Desmaison, Andreas Kopf, Edward Yang, Zachary DeVito, Martin Raison, Alykhan Tejani, Sasank Chilamkurthy, Benoit Steiner, Lu Fang, Junjie Bai, and Soumith Chintala. Pytorch: An imperative style, highperformance deep learning library. In H. Wallach, H. Larochelle, A. Beygelzimer, F. d'Alché-Buc, E. Fox, and R. Garnett, editors, *Advances in Neural Information Processing Systems 32*, pages 8024–8035. Curran Associates, Inc., 2019.
- <span id="page-11-16"></span>[24] Jeffrey Pennington, Richard Socher, and Christopher D Manning. Glove: Global vectors for word representation. In *Proceedings of the 2014 conference on empirical methods in natural language processing (EMNLP)*, pages 1532–1543, 2014.
- <span id="page-11-1"></span>[25] George Pu, Yanlin Zhou, Dapeng Wu, and Xiaolin Li. Server averaging for federated learning. *ArXiv*, abs/2103.11619, 2021.
- <span id="page-11-6"></span>[26] Sashank Reddi, Zachary Charles, Manzil Zaheer, Zachary Garrett, Keith Rush, Jakub Konečný, Sanjiv Kumar, and H Brendan McMahan. Adaptive federated optimization. *arXiv preprint arXiv:2003.00295*, 2020.
- <span id="page-11-8"></span>[27] Amirhossein Reisizadeh, Aryan Mokhtari, Hamed Hassani, Ali Jadbabaie, and Ramtin Pedarsani. Fedpaq: A communication-efficient federated learning method with periodic averaging and quantization. *arXiv preprint arXiv:1909.13014*, 2019.
- <span id="page-11-10"></span>[28] Saber Salehkaleybar, Arsalan Sharifnassab, and S Jamaloddin Golestani. One-shot federated learning: theoretical limits and algorithms to achieve them. *arXiv preprint arXiv:1905.04634*, 2019.
- <span id="page-11-9"></span>[29] Felix Sattler, Simon Wiedemann, Klaus-Robert Müller, and Wojciech Samek. Robust and communication-efficient federated learning from non-iid data. *IEEE transactions on neural networks and learning systems*, 2019.
- <span id="page-11-13"></span>[30] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-11-11"></span>[31] Arsalan Sharifnassab, Saber Salehkaleybar, and S Jamaloddin Golestani. Order optimal one-shot distributed learning. In *Advances in Neural Information Processing Systems*, pages 2165–2174, 2019.
- <span id="page-11-2"></span>[32] Neta Shoham, Tomer Avidor, Aviv Keren, Nadav Israel, Daniel Benditkis, Liron Mor-Yosef, and Itai Zeitak. Overcoming forgetting in federated learning on non-iid data. *arXiv preprint arXiv:1910.07796*, 2019.
- <span id="page-11-4"></span>[33] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation, 2019.
- <span id="page-11-12"></span>[34] Ivor W Tsang, James T Kwok, and Pak-Ming Cheung. Core vector machines: Fast svm training on very large data sets. *Journal of Machine Learning Research*, 6(Apr):363–392, 2005.
- <span id="page-11-15"></span>[35] Ellen M Voorhees and Donna Harman. Overview of the sixth text retrieval conference (trec-6). *Information Processing & Management*, 36(1):3–35, 2000.
- <span id="page-11-7"></span>[36] Jianyu Wang, Qinghua Liu, Hao Liang, Gauri Joshi, and H Vincent Poor. Tackling the objective inconsistency problem in heterogeneous federated optimization. *arXiv preprint arXiv:2007.07481*, 2020.
- <span id="page-11-3"></span>[37] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.

- <span id="page-12-2"></span>[38] Wentai Wu, Ligang He, Weiwei Lin, Stephen Jarvis, et al. Safa: a semi-asynchronous protocol for fast federated learning with low overhead. *arXiv preprint arXiv:1910.01355*, 2019.
- <span id="page-12-1"></span>[39] Cong Xie, Sanmi Koyejo, and Indranil Gupta. Asynchronous federated optimization. *arXiv preprint arXiv:1903.03934*, 2019.
- <span id="page-12-0"></span>[40] Yue Zhao, Meng Li, Liangzhen Lai, Naveen Suda, Damon Civin, and Vikas Chandra. Federated learning with non-iid data. *arXiv preprint arXiv:1806.00582*, 2018.

# A Hyperparameters

| Hyperparameter                  | Symbol          | Value |      |       |         |
|---------------------------------|-----------------|-------|------|-------|---------|
|                                 |                 | MNIST | IMDB | TREC6 | Sent140 |
| Batch size                      |                 | 512   | -    | -     | -       |
| Distill batch size              | $B_d$           | 40    | 1    | 1     | 1       |
| Distill steps                   | $S_d$           | 30    | 5    | 2     | 5       |
| Distill epochs                  | $E_d$           | 3     | 10   | 1     | 15      |
| Initial distilled learning rate | $\eta_0$        | 0.02  | 0.01 | 1.5   | 0.3     |
| Learning rate                   | $\alpha$        | 0.01  | 0.01 | 0.1   | 0.01    |
| Learning rate decay             |                 | 0.5   | -    | -     | -       |
| Learning rate decay period      | $\tau$          | 40    | 10   | 30    | 10      |
| Local epochs                    | E               | 30    | 50   | 50    | 50      |
| Random masking probability      | $p_{rm}$        | 0.3   | -    | -     | -       |
| Soft reset variance             | $\sigma_{sr}^2$ | 0.2   | -    | -     | -       |

Table 5: DOSFL hyperparameters. A dash (–) indicates that the value is constant across different tasks.

# B Distilled Data

<span id="page-13-0"></span>

## B.1 MNIST

Image /page/13/Picture/5 description: A grid of 10 small, square images, each labeled with a number from 0 to 9. All images are grayscale and appear to be noisy or pixelated, with no discernible shapes or patterns. The overall impression is of random static or noise.

Figure 4: First step of distilled images from 1 out of 10 clients for IID federated MNIST with no additions (i.e. soft labels, soft resets, random masking).

Image /page/13/Figure/7 description: A series of ten small, square images are displayed horizontally, each with a single digit above it, ranging from 0 to 9. All ten images are filled with a dense pattern of black and white pixels, giving them a noisy or static appearance. There are no discernible shapes or figures within any of the individual images.

Figure 5: First step of distilled images from 1 out of 10 clients for non-IID federated MNIST with no additions (i.e. soft labels, soft resets, random masking).

<span id="page-13-1"></span>

## B.2 IMDB

We provide a distilled sentence from one of 100 clients for federated IMDB with IID distribution. The logit is 1.63 for the positive class and 0 for the negative class. The corresponding distill learning rate is 0.0272.

shaw malone assembled shelly pendleton tha insanity vietnam finishes morton leather watts respectable mastery funky idle watched peripheral ely glossy 1934 honed periods suppress setting eden arises resides moses aura succumb prc missing dyer angela emulate showcased meredith embraces bonnie translates replicate potts segment affects enhances stein juliet bumping mystic resistance token alienate hays unnamed mira rewarded fateful aspire uniformly bliss mermaid burnt joins unforgettable martino namely marshal ivan morse segment pleads boasting victorian closeness rafael reid saddle boot hawks lingered landon ...

Image /page/14/Picture/0 description: The image displays a grid of 10 small, noisy, black and white images, each with text above it. The text above each image appears to be a set of three lines, with the first line indicating a digit and a probability (e.g., '0: 1.0', '1: 1.0', etc.). The second and third lines for most of the images are '9: 0.0' and '8: 0.0', respectively. However, the last two images have slightly different text: the second to last image has '8: 1.0', '9: 0.0', '7: 0.0', and the last image has '9: 1.0', '8: 0.0', '7: 0.0'. The noisy images themselves are difficult to discern specific features from due to the high level of random pixelation.

Figure 6: First step of distilled images from 1 out of 100 clients for IID federated MNIST with soft labels. The values above are the 3 labels with the highest logits.

Further, we exhibit a distilled sentence for non-IID federated IMDB. The logit is 1.68 for the positive class and 0 for the negative class. The corresponding distill learning rate is 0.0284.

outset wed burroughs grossly contacted reginald anticipating dimitri returns nap housed feeds pitting woodward potts graduates attendant inherit superficial pleasure yanks pills salem tombstone mcintyre finishes ponder pa concede thru herzog getting supports claudio board elevated lieu chaney cashing meantime denise disposition mess whopping comprehend slicing haley cronies screens zombie assures separately ill. debacle helm aroused scrape minuscule dozen wears devoid bio drunken recommendation shrewd denying decaying blocks primal housekeeper moviegoers mates crook useless dictates cap ...

## B.3 TREC6

In addition, we show a distilled sentence from 1 out of 29 clients for federated TREC6 with IID distribution. The logit is 1.96 for class 1, and 0 for the remaining classes. The corresponding distill learning rate is 2.25.

conversion loop monster manufactured causing besides stealing yankee 1932 igor supplier nicholas lloyd sees businessman alternate alternate photograph portrayed tale trials 49 principal sequel authors topped donation fictional bull philip

At last, we illustrate a distilled sentence from 1 out of 29 clients for federated TREC6 with non-IID distribution. The logit is 1.58 for class 2, and 0 for the remaining classes. The corresponding distill learning rate is 1.87.

fair listen programming helps lose remembered block changed classical learning break tap klein stole quick reed solomon mouse extension sisters virtual holmes knight medieval norman newton rider nobel rhode murdered

# C Additional Results

## C.1 LP-DOSFL

We provide results for LP-DOSFL on non-federated MNIST tasks in Table [6:](#page-15-0) federated IMDB, TREC-6, and Sent140. Hyperparameters and methodology are identical to those used for regular DOSFL in Section [4,](#page-4-0) other than the change in distillation order from parallel to serial.

## C.2 Moderate Non-IID

We perform an analysis of the impact non-IIDness has on DOSFL. We ran vanilla and LP-DOSFL on 10 client Federated MNIST for shard counts 2 through 30. The results are given in Figure [7.](#page-15-1) Importantly, plain DOSFL and LP-DOSFL maintain their IID performance even as the shard count  $s$ drops to 10. This is a moderately non-IID setting; each client on average still contains examples of all 10 digits. However, LP-DOSFL slightly curves downward as s decreases while vanilla DOSFL is flat. Beyond this point, test accuracy degrades quickly until both vanilla and LP-DOSFL have similar test accuracies once  $s = 2$ .

| Dataset | Setting  | 10 clients |         | 100 clients |         |
|---------|----------|------------|---------|-------------|---------|
|         |          | IID        | non-IID | IID         | non-IID |
| IMDB    | Vanilla  | 81.04%     | 79.86%  | 71.94%      | 70.75%  |
| IMDB    | LP-DOSFL | 85.07%     | 83.28%  | 81.65%      | 80.78%  |
| TREC-6  | Vanilla  | 83.60%     | 73.40%  | 79.00%      | 73.60%  |
| TREC-6  | LP-DOSFL | 86.20%     | 74.20%  | 82.80%      | 75.00%  |
| Sent140 | Vanilla  | 78.10%     | 78.00%  | 73.50%      | 69.50%  |
| Sent140 | LP-DOSFL | 81.40%     | 80.60%  | 77.90%      | 74.60%  |

<span id="page-15-0"></span>Table 6: DOSFL and LP-DOSFL performance on federated IMDB, TREC-6, and Sent140. Instead of 10 and 100 clients for TREC-6, we have 2 and 29 respectively. Note that, in every case, LP-DOSFL outperforms vanilla DOSFL.

<span id="page-15-1"></span>Image /page/15/Figure/2 description: This image contains two bar charts side-by-side, both plotting accuracy (%) against the number of shards for each client. The x-axis for both charts is labeled "Number of Shards for Each Client" and ranges from 2 to 30, with tick marks at 2, 5, 10, 15, 20, 25, and 30. The y-axis for both charts is labeled "Accuracy (%)" and ranges from 0 to 100, with tick marks at 0, 20, 40, 60, 80, and 100. Chart (a) is titled "Vanilla DOSFL accuracy for different s" and shows bars with heights approximately at 79, 85, 87, 87, 87, 88, and 90 for the respective shard numbers. Chart (b) is titled "LP-DOSFL accuracy for different s" and shows bars with heights approximately at 80, 86, 89, 91, 92, 93, and 94 for the respective shard numbers.

Figure 7: DOSFL Performance on Federated non-IID MNIST with soft labels and soft resets.