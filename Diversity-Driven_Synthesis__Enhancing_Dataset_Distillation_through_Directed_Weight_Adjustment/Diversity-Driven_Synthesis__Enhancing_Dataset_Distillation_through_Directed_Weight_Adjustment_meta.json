{"table_of_contents": [{"title": "Diversity-Driven Synthesis: Enhancing Dataset\nDistillation through Directed Weight Adjustment", "heading_level": null, "page_id": 0, "polygon": [[124.5, 99.75], [486.75, 99.75], [486.75, 136.8984375], [124.5, 136.8984375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[281.49609375, 251.25], [329.90625, 251.25], [329.90625, 261.80859375], [281.49609375, 261.80859375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 481.5], [191.548828125, 481.5], [191.548828125, 492.6796875], [107.25, 492.6796875]]}, {"title": "2 Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 663.0], [195.75, 663.0], [195.75, 674.05078125], [106.90576171875, 674.05078125]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 2, "polygon": [[106.8310546875, 374.25], [193.5, 374.25], [193.5, 385.365234375], [106.8310546875, 385.365234375]]}, {"title": "3.1 Batch Normalization Loss Enhances Diversity of S", "heading_level": null, "page_id": 3, "polygon": [[106.3828125, 74.25], [346.640625, 74.25], [346.640625, 83.337890625], [106.3828125, 83.337890625]]}, {"title": "3.2 Random Perturbation on \\theta_{\\mathcal{T}} Helps Improve Diversity", "heading_level": null, "page_id": 3, "polygon": [[106.5, 496.5], [358.5, 496.5], [358.5, 506.6015625], [106.5, 506.6015625]]}, {"title": "3.3 Directed Weight Adjustment on \\theta_{\\mathcal{T}}", "heading_level": null, "page_id": 4, "polygon": [[106.5, 240.75], [280.5, 240.75], [280.5, 250.59375], [106.5, 250.59375]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[106.5, 72.0], [192.0, 72.0], [192.0, 84.111328125], [106.5, 84.111328125]]}, {"title": "4.1 Results & Discussions", "heading_level": null, "page_id": 5, "polygon": [[107.25, 341.25], [225.4658203125, 341.25], [225.4658203125, 352.494140625], [107.25, 352.494140625]]}, {"title": "4.2 Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[107.25, 626.25], [195.75, 626.25], [195.75, 635.765625], [107.25, 635.765625]]}, {"title": "5 Related Works", "heading_level": null, "page_id": 8, "polygon": [[107.1298828125, 177.0], [203.25, 177.0], [203.25, 188.6220703125], [107.1298828125, 188.6220703125]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 512.25], [183.75, 512.25], [183.75, 524.00390625], [106.5, 524.00390625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.75], [207.984375, 72.75], [207.984375, 83.8212890625], [106.5, 83.8212890625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 146.8564453125], [164.35546875, 146.8564453125], [164.35546875, 157.1044921875], [107.25, 157.1044921875]]}, {"title": "A Appendix", "heading_level": null, "page_id": 13, "polygon": [[106.5, 72.0], [179.25, 72.0], [179.25, 83.6279296875], [106.5, 83.6279296875]]}, {"title": "A.1 Minimizing \\mathcal{L}_{\\text{mean}} and \\mathcal{L}_{\\text{var}} can be contradictory", "heading_level": null, "page_id": 13, "polygon": [[106.5, 98.25], [339.75, 98.25], [339.75, 108.75], [106.5, 108.75]]}, {"title": "A.2 Experiments", "heading_level": null, "page_id": 13, "polygon": [[106.5, 660.0], [187.5, 660.0], [187.5, 670.95703125], [106.5, 670.95703125]]}, {"title": "A.2.1 Hyper-parameter Settings", "heading_level": null, "page_id": 13, "polygon": [[106.5, 681.0], [252.75, 681.0], [252.75, 691.453125], [106.5, 691.453125]]}, {"title": "A.2.2 Feature Distance Calculation", "heading_level": null, "page_id": 14, "polygon": [[106.5, 549.0], [264.75, 549.0], [264.75, 560.35546875], [106.5, 560.35546875]]}, {"title": "A.2.3 Generalization to Vision Transformer-based Models", "heading_level": null, "page_id": 14, "polygon": [[106.5, 670.5], [361.880859375, 670.5], [361.880859375, 681.3984375], [106.5, 681.3984375]]}, {"title": "A.2.4 Application to Downstream Tasks", "heading_level": null, "page_id": 15, "polygon": [[107.25, 226.5], [285.75, 226.5], [285.75, 236.671875], [107.25, 236.671875]]}, {"title": "A.2.5 Computational Overhead of Distillation", "heading_level": null, "page_id": 15, "polygon": [[106.5, 418.5], [310.5, 418.5], [310.5, 429.2578125], [106.5, 429.2578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 48], ["SectionHeader", 3], ["Text", 3], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4700, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 83], ["Text", 5], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 830, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 888], ["Line", 59], ["TextInlineMath", 12], ["Equation", 3], ["Text", 3], ["Reference", 3], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1161], ["Line", 141], ["TextInlineMath", 8], ["Equation", 6], ["Reference", 5], ["SectionHeader", 2], ["Text", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1032], ["Line", 124], ["Equation", 8], ["Text", 5], ["TextInlineMath", 5], ["Reference", 5], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 859], ["TableCell", 146], ["Line", 55], ["Text", 7], ["SectionHeader", 2], ["Table", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2503, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 95], ["Caption", 4], ["Reference", 4], ["Figure", 3], ["FigureGroup", 3], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2228, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 903], ["TableCell", 139], ["Line", 79], ["Text", 4], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 5736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 59], ["TableCell", 29], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 2], ["Table", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 952, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 48], ["ListItem", 14], ["Reference", 14], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 16], ["ListItem", 7], ["Reference", 7], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 963], ["Line", 165], ["Reference", 5], ["SectionHeader", 4], ["TextInlineMath", 4], ["Equation", 4], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11061, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["TableCell", 187], ["Line", 68], ["Reference", 5], ["Table", 4], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["TableGroup", 2], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["TableCell", 88], ["Line", 44], ["Text", 5], ["Table", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 7320, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Diversity-Driven_Synthesis__Enhancing_Dataset_Distillation_through_Directed_Weight_Adjustment"}