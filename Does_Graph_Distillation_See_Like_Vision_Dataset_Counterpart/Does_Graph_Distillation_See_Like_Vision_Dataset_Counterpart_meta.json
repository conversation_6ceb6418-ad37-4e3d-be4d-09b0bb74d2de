{"table_of_contents": [{"title": "Does Graph Distillation See Like Vision Dataset\nCounterpart?", "heading_level": null, "page_id": 0, "polygon": [[127.599609375, 99.75], [482.607421875, 99.75], [482.607421875, 136.4150390625], [127.599609375, 136.4150390625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 267.0], [329.25, 267.0], [329.25, 278.05078125], [282.75, 278.05078125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.05517578125, 541.5], [192.0, 541.5], [192.0, 553.0078125], [107.05517578125, 553.0078125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[107.25, 311.25], [197.25, 311.25], [197.25, 322.13671875], [107.25, 322.13671875]]}, {"title": "3 Preliminary and Analysis", "heading_level": null, "page_id": 2, "polygon": [[106.5, 565.5], [256.5, 565.5], [256.5, 576.2109375], [106.5, 576.2109375]]}, {"title": "3.1 Formulation of graph condensation", "heading_level": null, "page_id": 2, "polygon": [[106.5, 589.5], [282.0, 589.5], [282.0, 599.80078125], [106.5, 599.80078125]]}, {"title": "3.2 Analysis of the structure of the condensed graph and its effects", "heading_level": null, "page_id": 3, "polygon": [[106.5, 278.25], [397.5, 278.25], [397.5, 288.298828125], [106.5, 288.298828125]]}, {"title": "4 Structure-broadcasting Graph Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[106.5322265625, 487.5], [384.0, 487.5], [384.0, 498.8671875], [106.5322265625, 498.8671875]]}, {"title": "4.1 Learning graph structure via graphon approximation", "heading_level": null, "page_id": 4, "polygon": [[106.5, 546.75], [359.25, 546.75], [359.25, 556.875], [106.5, 556.875]]}, {"title": "4.2 Optimizing the graph structure via optimal transport", "heading_level": null, "page_id": 5, "polygon": [[106.681640625, 222.75], [358.5, 224.25], [358.5, 234.0], [106.681640625, 233.578125]]}, {"title": "4.3 Training pipeline of SGDD", "heading_level": null, "page_id": 6, "polygon": [[106.5, 418.5], [245.25, 418.5], [245.25, 428.87109375], [106.5, 428.87109375]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 6, "polygon": [[106.5, 600.0], [192.146484375, 600.0], [192.146484375, 610.5], [106.5, 610.5]]}, {"title": "5.1 Datasets and implementation details", "heading_level": null, "page_id": 6, "polygon": [[106.5, 626.25], [285.2314453125, 626.25], [285.2314453125, 635.37890625], [106.5, 635.37890625]]}, {"title": "5.2 Comparison with state-of-the-art methods", "heading_level": null, "page_id": 7, "polygon": [[106.5, 300.0], [310.5, 300.0], [310.5, 310.341796875], [106.5, 310.341796875]]}, {"title": "5.3 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[106.5, 659.25], [195.75, 659.25], [195.75, 669.796875], [106.5, 669.796875]]}, {"title": "Evaluation of the scalability of SGDD.", "heading_level": null, "page_id": 8, "polygon": [[106.45751953125, 515.25], [271.5, 515.25], [271.5, 525.55078125], [106.45751953125, 525.55078125]]}, {"title": "5.4 Visualizations", "heading_level": null, "page_id": 9, "polygon": [[106.5, 345.0], [190.5, 345.0], [190.5, 355.39453125], [106.5, 355.39453125]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[107.25, 486.0], [183.75, 486.0], [183.75, 498.09375], [107.25, 498.09375]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 9, "polygon": [[106.75634765625, 643.5], [204.0, 643.5], [204.0, 654.71484375], [106.75634765625, 654.71484375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[106.5, 72.75], [164.654296875, 72.75], [164.654296875, 83.4345703125], [106.5, 83.4345703125]]}, {"title": "A More Preliminary and Related Work", "heading_level": null, "page_id": 17, "polygon": [[106.15869140625, 72.75], [318.0, 72.75], [318.0, 83.6279296875], [106.15869140625, 83.6279296875]]}, {"title": "A.1 More preliminary", "heading_level": null, "page_id": 17, "polygon": [[106.98046875, 97.5], [210.0, 97.5], [210.0, 107.2177734375], [106.98046875, 107.2177734375]]}, {"title": "A.2 More related work", "heading_level": null, "page_id": 17, "polygon": [[106.5, 261.75], [213.0, 261.75], [213.0, 271.08984375], [106.5, 271.08984375]]}, {"title": "B Proofs", "heading_level": null, "page_id": 17, "polygon": [[106.5, 375.0], [162.0, 375.0], [162.0, 385.55859375], [106.5, 385.55859375]]}, {"title": "B.1 Proof of Proposition 1", "heading_level": null, "page_id": 17, "polygon": [[106.5, 399.0], [225.75, 399.0], [225.75, 408.375], [106.5, 408.375]]}, {"title": "B.2 Proof of Proposition 2", "heading_level": null, "page_id": 18, "polygon": [[106.5, 288.0], [226.5, 288.0], [226.5, 297.7734375], [106.5, 297.7734375]]}, {"title": "B.3 Time complexity analysis and running time", "heading_level": null, "page_id": 19, "polygon": [[106.90576171875, 385.5], [317.25, 385.5], [317.25, 395.806640625], [106.90576171875, 395.806640625]]}, {"title": "C Experimental Details and More Experiments", "heading_level": null, "page_id": 20, "polygon": [[106.60693359375, 72.75], [358.5, 72.75], [358.5, 83.91796875], [106.60693359375, 83.91796875]]}, {"title": "C.1 Dataset statistics", "heading_level": null, "page_id": 20, "polygon": [[107.25, 96.75], [204.75, 96.75], [204.75, 107.0244140625], [107.25, 107.0244140625]]}, {"title": "C.2 Implementation details", "heading_level": null, "page_id": 20, "polygon": [[106.5, 384.0], [233.2353515625, 384.0], [233.2353515625, 394.259765625], [106.5, 394.259765625]]}, {"title": "C.3 Objective loss function and training algorithm", "heading_level": null, "page_id": 21, "polygon": [[107.25, 428.25], [330.75, 428.25], [330.75, 438.92578125], [107.25, 438.92578125]]}, {"title": "C.4 Stochastic Block Model experments setting", "heading_level": null, "page_id": 21, "polygon": [[106.5, 647.25], [316.16015625, 647.25], [316.16015625, 657.80859375], [106.5, 657.80859375]]}, {"title": "Algorithm 1: SGDD for Graph Condensation", "heading_level": null, "page_id": 22, "polygon": [[106.5, 76.5], [294.0, 76.5], [294.0, 86.818359375], [106.5, 86.818359375]]}, {"title": "C.5 More explorations of the sensitivity of \\beta", "heading_level": null, "page_id": 22, "polygon": [[106.5, 375.0], [302.25, 375.0], [302.25, 385.751953125], [106.5, 385.751953125]]}, {"title": "C.6 More cross-architecture experiments", "heading_level": null, "page_id": 23, "polygon": [[107.1298828125, 204.75], [289.5, 204.75], [289.5, 215.015625], [107.1298828125, 215.015625]]}, {"title": "C.7 Ablation of the sampling operation in OT distance", "heading_level": null, "page_id": 23, "polygon": [[106.5, 282.0], [347.25, 282.0], [347.25, 292.166015625], [106.5, 292.166015625]]}, {"title": "C.8 More discussion of the condensed graphs.", "heading_level": null, "page_id": 23, "polygon": [[106.45751953125, 545.25], [310.5, 545.25], [310.5, 555.71484375], [106.45751953125, 555.71484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 46], ["Text", 4], ["SectionHeader", 3], ["Footnote", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8515, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 66], ["Reference", 5], ["Text", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 920, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 520], ["Line", 61], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 3], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 671], ["Line", 81], ["TextInlineMath", 5], ["Reference", 5], ["Text", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1026, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 689], ["Line", 87], ["Reference", 5], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 778, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 739], ["Line", 108], ["TextInlineMath", 6], ["Text", 4], ["Equation", 4], ["Reference", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1122, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1462], ["TableCell", 104], ["Line", 68], ["SectionHeader", 3], ["Text", 2], ["TextInlineMath", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12837, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 467], ["TableCell", 309], ["Line", 88], ["Reference", 6], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 67], ["TableCell", 53], ["TextInlineMath", 4], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 44], ["Reference", 6], ["Text", 4], ["SectionHeader", 3], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 50], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 46], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 47], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Line", 4], ["ListItem", 1], ["<PERSON>Footer", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["Line", 113], ["TextInlineMath", 6], ["SectionHeader", 5], ["Equation", 5], ["Reference", 3], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 877], ["Line", 137], ["TextInlineMath", 10], ["Equation", 7], ["Text", 4], ["Reference", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 714], ["Line", 87], ["TableCell", 56], ["TextInlineMath", 5], ["Equation", 4], ["Text", 1], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["TableCell", 113], ["Line", 53], ["Text", 5], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["Equation", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 441], ["Line", 51], ["TextInlineMath", 6], ["Text", 5], ["Equation", 4], ["ListItem", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 834], ["Line", 114], ["Reference", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1459, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 87], ["TableCell", 42], ["Reference", 4], ["SectionHeader", 3], ["Text", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1400, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 118], ["Span", 73], ["Line", 10], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7854, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Does_Graph_Distillation_See_Like_Vision_Dataset_Counterpart"}