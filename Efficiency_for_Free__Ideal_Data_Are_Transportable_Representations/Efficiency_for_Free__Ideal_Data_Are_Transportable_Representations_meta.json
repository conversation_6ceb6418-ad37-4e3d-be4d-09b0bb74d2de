{"table_of_contents": [{"title": "Efficiency for Free: Ideal Data Are\nTransportable Representations", "heading_level": null, "page_id": 0, "polygon": [[176.25, 99.75], [434.25, 99.75], [434.25, 136.318359375], [176.25, 136.318359375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 243.0], [328.5, 243.0], [328.5, 253.880859375], [282.75, 253.880859375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 432.73828125], [191.3994140625, 432.73828125], [191.3994140625, 443.56640625], [107.25, 443.56640625]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[106.5, 123.169921875], [198.720703125, 123.169921875], [198.720703125, 134.96484375], [106.5, 134.96484375]]}, {"title": "2.1 Dataset Distillation: Efficient yet Effective Learning Using Fewer Data", "heading_level": null, "page_id": 2, "polygon": [[106.5, 182.25], [431.5078125, 182.25], [431.5078125, 193.166015625], [106.5, 193.166015625]]}, {"title": "2.2 Self-supervised Learning: Representation Learning Using Unlabeled Data", "heading_level": null, "page_id": 2, "polygon": [[106.5, 495.0], [447.046875, 495.0], [447.046875, 505.0546875], [106.5, 505.0546875]]}, {"title": "3 Revealing Critical Properties of Efficient Learning over Data", "heading_level": null, "page_id": 3, "polygon": [[106.5, 72.65478515625], [437.25, 72.65478515625], [437.25, 83.48291015625], [106.5, 83.48291015625]]}, {"title": "3.1 Unifying (Self-)Supervised Learning from a Data-Centric Perspective", "heading_level": null, "page_id": 3, "polygon": [[107.25, 364.5], [426.0, 364.5], [426.0, 374.73046875], [107.25, 374.73046875]]}, {"title": "3.2 Empirical and Theoretical Investigation of Data-Centric Efficient Learning", "heading_level": null, "page_id": 3, "polygon": [[106.5, 561.75], [450.75, 561.75], [450.75, 571.95703125], [106.5, 571.95703125]]}, {"title": "3.3 Extended Understanding of Data-Centric Efficient Learning", "heading_level": null, "page_id": 4, "polygon": [[106.5, 608.25], [387.75, 608.25], [387.75, 618.75], [106.5, 618.75]]}, {"title": "(a) Loss landscape for \\Sigma", "heading_level": null, "page_id": 5, "polygon": [[127.5, 186.0], [222.626953125, 186.0], [222.626953125, 195.1962890625], [127.5, 195.1962890625]]}, {"title": "3.4 Generalization-bounded Efficient Data Synthesis", "heading_level": null, "page_id": 5, "polygon": [[107.25, 680.625], [339.0, 680.625], [339.0, 690.6796875], [107.25, 690.6796875]]}, {"title": "4 Methodology", "heading_level": null, "page_id": 6, "polygon": [[107.25, 660.0], [193.5, 660.0], [193.5, 671.73046875], [107.25, 671.73046875]]}, {"title": "4.1 RELA-D \\left( \\mathcal{L} \\right): Synthesis of Efficient Dataset", "heading_level": null, "page_id": 7, "polygon": [[107.25, 73.5], [319.5, 73.5], [319.5, 83.48291015625], [107.25, 83.48291015625]]}, {"title": "4.2 RELA-F (\\bigtriangledown): Assist Learning with Generated Efficient Dataset", "heading_level": null, "page_id": 7, "polygon": [[107.25, 290.25], [402.75, 290.25], [402.75, 300.09375], [107.25, 300.09375]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 7, "polygon": [[107.25, 577.5], [192.4453125, 577.5], [192.4453125, 588.5859375], [107.25, 588.5859375]]}, {"title": "5.1 Primary Experimental Results and Analysis", "heading_level": null, "page_id": 8, "polygon": [[106.5, 516.75], [318.251953125, 516.75], [318.251953125, 527.09765625], [106.5, 527.09765625]]}, {"title": "6 Conclusion and Limitation", "heading_level": null, "page_id": 9, "polygon": [[107.20458984375, 575.05078125], [264.462890625, 575.05078125], [264.462890625, 586.65234375], [107.20458984375, 586.65234375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 72.75], [164.654296875, 72.75], [164.654296875, 83.67626953125], [107.25, 83.67626953125]]}, {"title": "Contents", "heading_level": null, "page_id": 15, "polygon": [[105.75, 69.0], [156.0, 69.0], [156.0, 83.48291015625], [105.75, 83.48291015625]]}, {"title": "A Ablation Study", "heading_level": null, "page_id": 16, "polygon": [[106.5, 228.744140625], [206.25, 228.744140625], [206.25, 240.345703125], [106.5, 240.345703125]]}, {"title": "B Proof of Theorem 1", "heading_level": null, "page_id": 17, "polygon": [[106.5, 72.0], [227.5576171875, 72.0], [227.5576171875, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "B.1 Setup", "heading_level": null, "page_id": 17, "polygon": [[106.5, 145.5], [157.5, 145.5], [157.5, 155.4609375], [106.5, 155.4609375]]}, {"title": "B.2 Algorithm", "heading_level": null, "page_id": 17, "polygon": [[106.5, 511.5], [177.0, 511.5], [177.0, 521.296875], [106.5, 521.296875]]}, {"title": "B.3 Bounds with V<PERSON>ce", "heading_level": null, "page_id": 17, "polygon": [[106.5, 635.25], [226.51171875, 635.25], [226.51171875, 645.046875], [106.5, 645.046875]]}, {"title": "B.4 Nonlinear case", "heading_level": null, "page_id": 18, "polygon": [[106.5, 303.0], [195.75, 303.0], [195.75, 313.435546875], [106.5, 313.435546875]]}, {"title": "B.5 From the Perspective of Feature Learning", "heading_level": null, "page_id": 18, "polygon": [[106.5, 647.25], [310.5, 647.25], [310.5, 656.6484375], [106.5, 656.6484375]]}, {"title": "C Proof of Theorem 2", "heading_level": null, "page_id": 19, "polygon": [[106.5, 228.0], [228.75, 228.0], [228.75, 239.572265625], [106.5, 239.572265625]]}, {"title": "C.1 Setting", "heading_level": null, "page_id": 19, "polygon": [[106.5, 252.75], [163.9072265625, 252.75], [163.9072265625, 262.96875], [106.5, 262.96875]]}, {"title": "C.2 Bounds with \\rho", "heading_level": null, "page_id": 19, "polygon": [[106.5, 348.0], [195.75, 348.0], [195.75, 358.48828125], [106.5, 358.48828125]]}, {"title": "C.3 Nonlinear case", "heading_level": null, "page_id": 19, "polygon": [[106.5, 565.5], [196.03125, 565.5], [196.03125, 575.82421875], [106.5, 575.82421875]]}, {"title": "D Proof of Theorem 3", "heading_level": null, "page_id": 20, "polygon": [[105.75, 71.25], [229.5, 71.25], [229.5, 83.77294921875], [105.75, 83.77294921875]]}, {"title": "D.1 Relation between data distribution and Rademacher complexity", "heading_level": null, "page_id": 24, "polygon": [[106.5, 231.75], [404.25, 231.75], [404.25, 241.69921875], [106.5, 241.69921875]]}, {"title": "E Explanation of Rescaling Samples", "heading_level": null, "page_id": 24, "polygon": [[106.5, 654.75], [302.25, 654.75], [302.25, 665.9296875], [106.5, 665.9296875]]}, {"title": "F Detailed Methodology of RELA-D", "heading_level": null, "page_id": 26, "polygon": [[106.5, 72.0], [303.01171875, 72.0], [303.01171875, 83.6279296875], [106.5, 83.6279296875]]}, {"title": "F.1 Proof for Ideal Properties of Prior Models", "heading_level": null, "page_id": 26, "polygon": [[106.5, 216.75], [309.75, 216.75], [309.75, 226.037109375], [106.5, 226.037109375]]}, {"title": "1. Definitions and Setup", "heading_level": null, "page_id": 26, "polygon": [[106.5, 335.25], [210.0, 335.25], [210.0, 345.533203125], [106.5, 345.533203125]]}, {"title": "2. InfoNCE as a Mutual Information Lower Bound", "heading_level": null, "page_id": 26, "polygon": [[107.25, 467.25], [325.5, 467.25], [325.5, 476.82421875], [107.25, 476.82421875]]}, {"title": "G Analysis of Different Self-Supervised Learning Methods", "heading_level": null, "page_id": 27, "polygon": [[106.5, 404.25], [416.25, 404.25], [416.25, 415.3359375], [106.5, 415.3359375]]}, {"title": "G.1 A Unified Framework for SSL", "heading_level": null, "page_id": 27, "polygon": [[106.45751953125, 486.0], [262.96875, 486.0], [262.96875, 495.7734375], [106.45751953125, 495.7734375]]}, {"title": "G.2 Contrastive Learning Methods", "heading_level": null, "page_id": 27, "polygon": [[106.60693359375, 681.0], [264.75, 681.0], [264.75, 691.453125], [106.60693359375, 691.453125]]}, {"title": "G.3 Asymmetric Network Methods", "heading_level": null, "page_id": 28, "polygon": [[106.5, 503.89453125], [264.75, 503.89453125], [264.75, 513.17578125], [106.5, 513.17578125]]}, {"title": "G.4 Feature Decorrelation Methods", "heading_level": null, "page_id": 29, "polygon": [[107.25, 269.25], [267.0, 269.25], [267.0, 279.791015625], [107.25, 279.791015625]]}, {"title": "H Budget of RELA for Data Synthesis", "heading_level": null, "page_id": 30, "polygon": [[106.5, 284.625], [312.873046875, 284.625], [312.873046875, 295.453125], [106.5, 295.453125]]}, {"title": "I RELA in Labeled Dataset Distillation and Human-Supervised Learning", "heading_level": null, "page_id": 30, "polygon": [[106.5, 414.0], [492.0, 414.0], [492.0, 425.00390625], [106.5, 425.00390625]]}, {"title": "I.1 Experimental Setup", "heading_level": null, "page_id": 30, "polygon": [[106.5, 469.86328125], [215.15625, 469.86328125], [215.15625, 479.91796875], [106.5, 479.91796875]]}, {"title": "I.2 Main Results", "heading_level": null, "page_id": 30, "polygon": [[106.30810546875, 678.75], [186.46875, 678.75], [186.46875, 689.1328125], [106.30810546875, 689.1328125]]}, {"title": "J RELA Algorithm", "heading_level": null, "page_id": 31, "polygon": [[106.5, 234.75], [216.75, 234.75], [216.75, 246.533203125], [106.5, 246.533203125]]}, {"title": "K Experimental Details", "heading_level": null, "page_id": 31, "polygon": [[106.5, 665.25], [238.5, 665.25], [238.5, 677.53125], [106.5, 677.53125]]}, {"title": "K.1 Detailed Setup for Experiments in Section 3.2", "heading_level": null, "page_id": 31, "polygon": [[106.5, 690.75], [326.3203125, 690.75], [326.3203125, 701.12109375], [106.5, 701.12109375]]}, {"title": "K.2 Detailed Setup for Experiments in Section 5", "heading_level": null, "page_id": 32, "polygon": [[106.5, 328.5], [321.240234375, 328.5], [321.240234375, 338.765625], [106.5, 338.765625]]}, {"title": "L Batch PCA Reduction", "heading_level": null, "page_id": 32, "polygon": [[106.5, 514.5], [241.751953125, 514.5], [241.751953125, 525.9375], [106.5, 525.9375]]}, {"title": "L.1 Principal Component Analysis", "heading_level": null, "page_id": 32, "polygon": [[106.5, 575.25], [263.25, 575.25], [263.25, 585.10546875], [106.5, 585.10546875]]}, {"title": "M Analyze the Practical Data Augmentations", "heading_level": null, "page_id": 34, "polygon": [[106.5, 592.5], [348.75, 592.5], [348.75, 603.66796875], [106.5, 603.66796875]]}, {"title": "M.1 Assumptions and Definitions", "heading_level": null, "page_id": 34, "polygon": [[106.5, 706.5], [258.3369140625, 706.5], [258.3369140625, 716.203125], [106.5, 716.203125]]}, {"title": "M.2 Augmented Distributions", "heading_level": null, "page_id": 35, "polygon": [[106.5, 339.75], [242.25, 339.75], [242.25, 349.787109375], [106.5, 349.787109375]]}, {"title": "M.3 Increased Variance Leads to Increased Overlap", "heading_level": null, "page_id": 35, "polygon": [[106.5, 595.5], [337.5, 595.5], [337.5, 605.25], [106.5, 605.25]]}, {"title": "M.4 Conclusion", "heading_level": null, "page_id": 36, "polygon": [[107.25, 433.5], [183.0, 433.5], [183.0, 442.79296875], [107.25, 442.79296875]]}, {"title": "M.5 Empirical Analysis for Real-world Datasets", "heading_level": null, "page_id": 36, "polygon": [[106.5, 590.25], [320.25, 590.25], [320.25, 600.1875], [106.5, 600.1875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 52], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12939, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 55], ["Text", 10], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 51], ["Text", 7], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 682], ["Line", 63], ["Reference", 8], ["TextInlineMath", 6], ["Text", 4], ["Equation", 4], ["SectionHeader", 3], ["ListItem", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 756], ["Line", 61], ["TextInlineMath", 9], ["Reference", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 152], ["Text", 9], ["Reference", 5], ["Caption", 3], ["ListItem", 3], ["Figure", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 972], ["Line", 48], ["TextInlineMath", 11], ["Reference", 6], ["Text", 4], ["ListItem", 2], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 589], ["Line", 49], ["Text", 6], ["TextInlineMath", 6], ["Reference", 4], ["SectionHeader", 3], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 831], ["TableCell", 183], ["Line", 54], ["Text", 6], ["Reference", 3], ["ListItem", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12390, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 720], ["TableCell", 139], ["Line", 57], ["Text", 4], ["ListItem", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 12800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 47], ["ListItem", 18], ["Reference", 18], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 49], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 28], ["Line", 8], ["ListItem", 3], ["Reference", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["TableCell", 50], ["Line", 44], ["SectionHeader", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 96], ["TableCell", 20], ["Text", 5], ["Reference", 2], ["TableOfContents", 1], ["SectionHeader", 1], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3540, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 904], ["Line", 98], ["TextInlineMath", 8], ["Equation", 6], ["SectionHeader", 4], ["Reference", 4], ["Text", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1112, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 914], ["Line", 142], ["TextInlineMath", 6], ["Equation", 4], ["SectionHeader", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 969], ["Line", 94], ["Text", 7], ["TextInlineMath", 5], ["SectionHeader", 4], ["Reference", 4], ["Equation", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 761], ["Line", 105], ["Text", 12], ["Equation", 8], ["TextInlineMath", 5], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 765], ["Line", 56], ["Text", 9], ["Equation", 9], ["TextInlineMath", 6], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 746], ["Line", 66], ["Text", 11], ["Equation", 11], ["TextInlineMath", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1084, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 55], ["Equation", 9], ["Text", 8], ["TextInlineMath", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 98], ["Text", 8], ["Equation", 7], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1195, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 684], ["Line", 121], ["Equation", 10], ["Text", 10], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 741], ["Line", 86], ["TextInlineMath", 7], ["Equation", 5], ["Text", 5], ["SectionHeader", 4], ["Reference", 3], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 74], ["TableCell", 19], ["Text", 6], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["Equation", 2], ["Table", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1425, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 630], ["Line", 126], ["TextInlineMath", 8], ["Text", 5], ["Equation", 5], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 876], ["Line", 175], ["Text", 8], ["Equation", 6], ["TextInlineMath", 5], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2215, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 63], ["Text", 8], ["SectionHeader", 4], ["Reference", 4], ["TextInlineMath", 3], ["ListItem", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 1186], ["TableCell", 78], ["Line", 74], ["Reference", 5], ["SectionHeader", 3], ["Text", 3], ["Figure", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1501, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["TableCell", 82], ["Line", 47], ["Reference", 6], ["Text", 5], ["Table", 3], ["Caption", 3], ["SectionHeader", 3], ["TableGroup", 3], ["Equation", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4205, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 494], ["Line", 47], ["TableCell", 46], ["Text", 8], ["Equation", 2], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1304, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 92], ["Text", 12], ["Equation", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2244, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["Line", 73], ["Equation", 7], ["Text", 6], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 473], ["Line", 89], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 51], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 989, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Efficiency_for_Free__Ideal_Data_Are_Transportable_Representations"}