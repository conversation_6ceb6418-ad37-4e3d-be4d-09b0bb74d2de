{"table_of_contents": [{"title": "Efficient Dataset Distillation\nusing Random Feature Approximation", "heading_level": null, "page_id": 0, "polygon": [[162.0, 99.0], [448.5, 99.0], [448.5, 136.4150390625], [162.0, 136.4150390625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 253.5], [328.5, 253.5], [328.5, 264.515625], [282.75, 264.515625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.8310546875, 463.5], [191.25, 463.5], [191.25, 474.890625], [106.8310546875, 474.890625]]}, {"title": "2 Background and Related Work", "heading_level": null, "page_id": 1, "polygon": [[107.25, 413.7890625], [286.5, 413.7890625], [286.5, 425.390625], [107.25, 425.390625]]}, {"title": "3 Algorithm Setup and Design", "heading_level": null, "page_id": 2, "polygon": [[107.25, 317.25], [271.634765625, 317.25], [271.634765625, 328.517578125], [107.25, 328.517578125]]}, {"title": "3.1 KIP Revisit", "heading_level": null, "page_id": 2, "polygon": [[106.5, 381.75], [180.75, 381.75], [180.75, 392.1328125], [106.5, 392.1328125]]}, {"title": "3.2 Replacing the NTK Kernel with an NNGP Kernel", "heading_level": null, "page_id": 2, "polygon": [[106.5, 540.0], [342.0, 540.0], [342.0, 550.30078125], [106.5, 550.30078125]]}, {"title": "Algorithm 1 Dataset distillation with NNGP random features", "heading_level": null, "page_id": 3, "polygon": [[107.25, 73.5], [354.41015625, 73.5], [354.41015625, 83.337890625], [107.25, 83.337890625]]}, {"title": "3.3 Replacing NNGP with an Empirical NNGP", "heading_level": null, "page_id": 3, "polygon": [[106.5, 298.5], [314.25, 298.5], [314.25, 308.21484375], [106.5, 308.21484375]]}, {"title": "3.4 Loss Function in dataset distillation", "heading_level": null, "page_id": 3, "polygon": [[106.5, 631.5], [282.69140625, 631.5], [282.69140625, 641.56640625], [106.5, 641.56640625]]}, {"title": "4 Experiments with RFAD", "heading_level": null, "page_id": 4, "polygon": [[106.5, 552.62109375], [252.75, 552.62109375], [252.75, 564.22265625], [106.5, 564.22265625]]}, {"title": "4.1 Time Savings during training", "heading_level": null, "page_id": 5, "polygon": [[107.25, 446.25], [256.5, 446.25], [256.5, 456.328125], [107.25, 456.328125]]}, {"title": "4.2 NTK Kernel and Finite Network Transfer", "heading_level": null, "page_id": 6, "polygon": [[106.8310546875, 411.75], [309.0, 411.75], [309.0, 422.296875], [106.8310546875, 422.296875]]}, {"title": "4.3 Empirical NNGP Performance at Inference", "heading_level": null, "page_id": 7, "polygon": [[106.90576171875, 263.25], [315.75, 263.25], [315.75, 273.41015625], [106.90576171875, 273.41015625]]}, {"title": "5 RFAD Application I: Interpretability", "heading_level": null, "page_id": 7, "polygon": [[106.5, 518.25], [315.0, 518.25], [315.0, 529.41796875], [106.5, 529.41796875]]}, {"title": "6 RFAD Application II: Privacy", "heading_level": null, "page_id": 9, "polygon": [[106.2333984375, 72.75], [279.2548828125, 72.75], [279.2548828125, 83.91796875], [106.2333984375, 83.91796875]]}, {"title": "7 Conclusions", "heading_level": null, "page_id": 9, "polygon": [[106.8310546875, 345.75], [187.6640625, 345.75], [187.6640625, 358.1015625], [106.8310546875, 358.1015625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 669.0], [207.75, 669.0], [207.75, 680.23828125], [107.1298828125, 680.23828125]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[106.98046875, 72.0], [165.0, 72.0], [165.0, 83.4345703125], [106.98046875, 83.4345703125]]}, {"title": "Checklist", "heading_level": null, "page_id": 14, "polygon": [[107.25, 158.25], [157.78125, 158.25], [157.78125, 169.2861328125], [107.25, 169.2861328125]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 15, "polygon": [[107.25, 72.509765625], [159.0, 72.509765625], [159.0, 83.91796875], [107.25, 83.91796875]]}, {"title": "A Code", "heading_level": null, "page_id": 15, "polygon": [[107.25, 102.75], [156.5859375, 102.75], [156.5859375, 113.7919921875], [107.25, 113.7919921875]]}, {"title": "B Notations", "heading_level": null, "page_id": 15, "polygon": [[106.5, 164.25], [177.75, 164.25], [177.75, 176.4404296875], [106.5, 176.4404296875]]}, {"title": "C Time Complexity Discussion", "heading_level": null, "page_id": 15, "polygon": [[106.75634765625, 389.25], [273.75, 389.25], [273.75, 400.25390625], [106.75634765625, 400.25390625]]}, {"title": "D Implementation details", "heading_level": null, "page_id": 16, "polygon": [[106.5, 72.4130859375], [246.75, 72.4130859375], [246.75, 84.0146484375], [106.5, 84.0146484375]]}, {"title": "D.1 Preprocessing", "heading_level": null, "page_id": 16, "polygon": [[106.5, 101.6103515625], [193.640625, 101.6103515625], [193.640625, 112.0517578125], [106.5, 112.0517578125]]}, {"title": "D.2 Architectures", "heading_level": null, "page_id": 16, "polygon": [[106.5, 229.7109375], [191.9970703125, 229.7109375], [191.9970703125, 240.15234375], [106.5, 240.15234375]]}, {"title": "D.3 Training", "heading_level": null, "page_id": 16, "polygon": [[106.5, 450.0], [171.0, 450.0], [171.0, 460.96875], [106.5, 460.96875]]}, {"title": "D.4 Runtime experiments", "heading_level": null, "page_id": 17, "polygon": [[106.5, 195.29296875], [225.017578125, 195.29296875], [225.017578125, 204.9609375], [106.5, 204.9609375]]}, {"title": "D.5 Finite Network Transfer", "heading_level": null, "page_id": 17, "polygon": [[106.5, 285.0], [237.0, 285.0], [237.0, 295.646484375], [106.5, 295.646484375]]}, {"title": "D.6 Privacy experiments", "heading_level": null, "page_id": 17, "polygon": [[106.5, 457.5], [220.53515625, 457.5], [220.53515625, 467.54296875], [106.5, 467.54296875]]}, {"title": "E Time taken additional results", "heading_level": null, "page_id": 17, "polygon": [[106.5, 638.25], [277.611328125, 638.25], [277.611328125, 649.6875], [106.5, 649.6875]]}, {"title": "F Centering and label scaling for finite networks ablations", "heading_level": null, "page_id": 18, "polygon": [[106.3828125, 425.00390625], [414.0, 425.00390625], [414.0, 437.37890625], [106.3828125, 437.37890625]]}, {"title": "G Effect of InstanceNorm", "heading_level": null, "page_id": 18, "polygon": [[106.5, 521.25], [249.3720703125, 521.25], [249.3720703125, 533.671875], [106.5, 533.671875]]}, {"title": "H Interpretability additional examples", "heading_level": null, "page_id": 18, "polygon": [[106.5, 638.859375], [315.5625, 638.859375], [315.5625, 651.234375], [106.5, 651.234375]]}, {"title": "I Empirical NNGP at Inference additional results", "heading_level": null, "page_id": 18, "polygon": [[106.5, 665.15625], [369.75, 665.15625], [369.75, 676.7578125], [106.5, 676.7578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 67], ["SectionHeader", 3], ["Text", 3], ["Figure", 2], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8298, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 54], ["Text", 9], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 51], ["TextInlineMath", 4], ["SectionHeader", 3], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["Line", 53], ["TextInlineMath", 5], ["SectionHeader", 3], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1080], ["Line", 55], ["TableCell", 52], ["Text", 4], ["TextInlineMath", 3], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 614], ["Line", 96], ["TableCell", 85], ["Text", 5], ["Caption", 2], ["Reference", 2], ["Table", 1], ["SectionHeader", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 759, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 75], ["Text", 15], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1638, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 60], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 837, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 82], ["Text", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1443, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 74], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 47], ["ListItem", 17], ["Reference", 17], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 48], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 47], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 46], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 41], ["ListItem", 24], ["ListGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 44], ["TableCell", 40], ["TextInlineMath", 5], ["SectionHeader", 4], ["Text", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4620, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 47], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 44], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 575], ["Line", 77], ["TableCell", 56], ["SectionHeader", 4], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 784], ["TableCell", 100], ["Line", 47], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Line", 108], ["Span", 9], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1418, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Line", 104], ["Span", 17], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1548, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Line", 68], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 762, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Line", 72], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 917, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Line", 123], ["Span", 5], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1502, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Line", 70], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Line", 67], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Line", 76], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 836, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Line", 117], ["Span", 5], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1463, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Efficient_Dataset_Distillation_using_Random_Feature_Approximation"}