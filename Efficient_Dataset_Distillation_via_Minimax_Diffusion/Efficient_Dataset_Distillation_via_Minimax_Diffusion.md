# <span id="page-0-1"></span>Efficient Dataset Distillation via Minimax Diffusion

Jianyang Gu<sup>1</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>4</sup> <PERSON><sup>1\*</sup> <PERSON><sup>4</sup> <PERSON><PERSON><sup>2</sup> <sup>1</sup>Zhejiang University <sup>2</sup>Duke University <sup>3</sup>Czech Technical University  $\dagger$ <sup>4</sup>National University of Singapore

{gu jianyang, jiangwei zju}@zju.edu.cn

## Abstract

*Dataset distillation reduces the storage and computational consumption of training a network by generating a small surrogate dataset that encapsulates rich information of the original large-scale one. However, previous distillation methods heavily rely on the sample-wise iterative optimization scheme. As the images-per-class (IPC) setting or image resolution grows larger, the necessary computation will demand overwhelming time and resources. In this work, we intend to incorporate generative diffusion techniques for computing the surrogate dataset. Observing that key factors for constructing an effective surrogate dataset are representativeness and diversity, we design additional minimax criteria in the generative training to enhance these facets for the generated images of diffusion models. We present a theoretical model of the process as hierarchical diffusion control demonstrating the flexibility of the diffusion process to target these criteria without jeopardizing the faithfulness of the sample to the desired distribution. The proposed method achieves state-of-the-art validation performance while demanding much less computational resources. Under the 100-IPC setting on ImageWoof, our method requires less than one-twentieth the distillation time of previous methods, yet yields even better performance. Source code and generated data are available in [https://github.com/vimar-gu/MinimaxDiffusion.](https://github.com/vimar-gu/MinimaxDiffusion)*

## 1. Introduction

Data, as a necessary resource for deep learning, has concurrently promoted algorithmic advancements while imposing challenges on researchers due to heavy demands on storage and computational resources [\[6,](#page-8-0) [10,](#page-8-1) [18,](#page-8-2) [47\]](#page-9-0). Confronted with the conflict between the requirement for high-precision

<span id="page-0-0"></span>Image /page/0/Figure/9 description: This is a scatter plot showing the relationship between Distillation Time (h) on the x-axis and Validation Accuracy (%) on the y-axis. The plot displays data points for several methods: 'Ours', 'DM', 'IDC', and 'GLaD'. The 'Ours' method has data points at approximately (0, 39), (0, 45), (0, 55), (0, 58), and (0, 64), labeled 'Ours-10', 'Ours-20', 'Ours-50', 'Ours-70', and 'Ours-100' respectively. The 'DM' method has points at approximately (10, 32), (12, 36), (15, 47), (18, 52), and (20, 56), labeled 'DM-10', 'DM-20', 'DM-50', 'DM-70', and 'DM-100'. The 'IDC' method has points at approximately (30, 39), (35, 42), (50, 48), (60, 53), and (85, 56), labeled 'IDC-10', 'IDC-20', 'IDC-50', 'IDC-70', and 'IDC-100'. There is also a point for 'GLaD-10' at approximately (10, 32). A legend in the bottom right corner indicates that the size of the circles represents GPU Memory, with smaller circles for 10G, medium for 20G, and larger for 30G. The 'DM-10' and 'GLaD-10' points are represented by larger circles, suggesting 30G GPU Memory.

Figure 1. The validation accuracy and distillation time of different methods on ImageWoof [\[15\]](#page-8-3), with a number following each method denoting the Image-Per-Class (IPC) setting. Previous methods are restricted by the heavier running time and memory consumption as IPC grows larger. In comparison, our proposed method notably reduces the demanding computational resources and also achieves state-of-the-art validation performance.

models and overwhelming resource demands, dataset distillation is proposed to condense the rich information of a large-scale dataset into a small surrogate one [\[5,](#page-8-4) [21,](#page-8-5) [47,](#page-9-0) [57\]](#page-10-0). Such a surrogate dataset is expected to achieve training performance comparable to that attained with the original one.

Previous dataset distillation methods mostly engage in iterative optimization on fixed-number samples at the pixel level [\[21,](#page-8-5) [26,](#page-8-6) [30,](#page-9-1) [31,](#page-9-2) [44,](#page-9-3) [54,](#page-9-4) [57\]](#page-10-0) or embedding level [\[4,](#page-8-7) [55\]](#page-10-1). However, the sample-wise iterative optimization scheme suffers from problems of two perspectives. (1) The parameter space of optimization is positively correlated with the size of the target surrogate dataset and the image resolution [\[3,](#page-8-8) [57\]](#page-10-0). Consequently, substantial time and computational resources are required for distilling larger datasets. As shown in Fig. [1,](#page-0-0) IDC-1 [\[21\]](#page-8-5) takes over 90 hours to distill a 100-image-per-class (IPC) set from ImageWoof [\[15\]](#page-8-3), while training on ImageWoof itself only requires a matter of

<sup>\*</sup>Corresponding author

<sup>†</sup>This work has received funding from the European Union's Horizon Europe research and innovation program under grant agreement No. 101084642.

<span id="page-1-2"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a comparison between 'original' and 'distilled' image generation results. The top row, labeled 'original', shows a series of clear images of dogs and a person with a dog. The bottom section, labeled 'distilled', is divided into two rows, 'IPC 10' and 'IPC 100'. Both distilled rows contain images that are less clear and appear to be generated with artifacts, with the 'IPC 100' row showing slightly more recognizable images than the 'IPC 10' row.

Figure 2. Sample images distilled by the pixel-level sample-wise optimization method DM [\[56\]](#page-10-2) on ImageWoof. As the parameter space increases along with the Image-Per-Class (IPC) setting, with the same initialization, the appearance disparity between original and distilled images is smaller.

hours. (2) The expanded parameter space also increases the optimization complexity. As shown in Fig. [2,](#page-1-0) while distillation yields significant information condensation under small IPC settings, the pixel modification diminishes when distilling larger-IPC datasets. The reduced disparity also leads to smaller performance gain compared with original images, with instances where the distilled set even performs worse. Especially when distilling data of fine-grained classes, the sample-wise optimization scheme fails to provide adequate discriminative information. These constraints severely hinder individual researchers from distilling personalized data. A more practical training scheme is urgently needed to facilitate the broader application of dataset distillation.

In this work, we explore the possibility of incorporating generative diffusion techniques [\[20,](#page-8-9) [22,](#page-8-10) [32\]](#page-9-5) to efficiently compute effective surrogate datasets. We first conduct empirical analysis on the suitability of data generated by raw diffusion models for training networks. Based on the observations, we conclude that constructing an effective surrogate dataset hinges on two key factors: representativeness and diversity. Accordingly, we design extra minimax criteria for the generative training to enhance the capability of generating more effective surrogate datasets without explicit prompt designs. The minimax criteria involve two aspects: enforcing the generated sample to be close to the farthest real sample, while being far away from the most similar generated one. We provide theoretical analysis to support that the proposed minimax scheme aims to solve a well defined problem with all the criteria, including the generative accuracy and the minimax criteria, can be targeted simultaneously without detriment to the others.

Compared with the astronomical training time consumption of the sample-wise iterative optimization schemes, the proposed method takes less than 1 hour to distill a 100-IPC surrogate dataset for a 10-class ImageNet subset, including

<span id="page-1-1"></span>Image /page/1/Figure/5 description: This image displays four t-SNE plots, each representing a different method: Random, DiT, DiffFit, and Ours. Each plot shows a scatter of blue and orange dots, with the accuracy of each method indicated in the top right corner. The Random plot has an accuracy of 59.4%, DiT has an accuracy of 58.3%, DiffFit has an accuracy of 55.7%, and Ours has an accuracy of 64.5%. The plots are arranged in a 2x2 grid.

Figure 3. The feature distribution comparison of different image generation methods with the original set. The validation performance of each surrogate set is listed in the upper-right corner.

the fine-tuning and image generation processes. Remarkably, the GPU consumption remains consistent across all IPC settings. Furthermore, the distilled surrogate dataset attains superior validation performance compared with other state-of-the-art methods. Especially on the challenging finegrained ImageWoof subset, the proposed method outperforms the second-best DD method by 5.5% and 8.1% under the IPC settings of 70 and 100, respectively. The source code is provided in the supplementary material.

The contributions of this work are summarized into:

- We analyze the data generated by diffusion models, and emphasize the importance of representativeness and diversity for constructing effective surrogate datasets.
- We propose a novel dataset distillation scheme based on extra minimax criteria for diffusion models targeting the representativeness and diversity of generated data.
- We theoretically justify the proposed minimax criteria as enforceable without trade-offs in the generation quality of the individual data points.
- We conduct extensive experiments to validate that our proposed method achieves state-of-the-art performance while demanding significantly reduced training time in comparison to previous dataset distillation methods.

## 2. Method

#### 2.1. Problem Definition

The general purpose of dataset distillation is to generate a small surrogate dataset  $S = \{(\mathbf{x}_i, y_i)\}_{i=1}^{N_S}$  from a largescale one  $\mathcal{T} = \{(\mathbf{x}_i, y_i)\}_{i=1}^{N_T}$  [\[47,](#page-9-0) [57\]](#page-10-0). Here each  $\mathbf{x}_i$  denotes an image with a corresponding class label  $y_i$ , and

<span id="page-2-3"></span> $N_S \ll N_T$ . The surrogate dataset S is expected to encapsulate substantial information from the original  $\mathcal T$ , such that training a model on  $S$  achieves performance comparable with that on  $T$ . After distilling, we train network models on  $S$  and validate the performance on the original test set.

#### 2.2. Diffusion for Distillation

Diffusion models learn a dataset distribution by gradually adding Gaussian noise to images and reversing back. Taking the latent diffusion model (LDM) as an example, given a training image x, the training process is separated into two parts. An encoder  $E$  transforms the image into the latent space  $z = E(x)$  and a decoder D reconstructs a latent code back to the image space  $\hat{\mathbf{x}} = D(\mathbf{z})$ . The forward noising process gradually adds noise  $\epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$  to the original latent code  $\mathbf{z}_0$ :  $\mathbf{z}_t = \sqrt{\overline{\alpha}_t} \mathbf{z}_0 + \sqrt{1 - \overline{\alpha}_t} \epsilon$ , where  $\overline{\alpha}_t$  is a hyper-parameter. Provided with a conditioning vector c encoded with class labels, the diffusion models are trained by the squared error between the predicted noise  $\epsilon_{\theta}(\mathbf{z}_t, \mathbf{c})$  and the ground truth  $\epsilon$ :

<span id="page-2-4"></span>
$$
\mathcal{L}_{simple} = ||\epsilon_{\theta}(\mathbf{z}_t, \mathbf{c}) - \epsilon||_2^2, \tag{1}
$$

where  $\epsilon_{\theta}$  is a noise prediction network parameterized by  $\theta$ . Diffusion models are proven to generate images of higher quality compared with GANs [\[8\]](#page-8-11). There are also some Parameter Efficient Fine-Tuning (PEFT) methods updating a small number of model parameters in order for the model to be better adapted to specific data domains [\[34,](#page-9-6) [50\]](#page-9-7). We adopt DiT [\[33\]](#page-9-8) as the baseline and Difffit [\[50\]](#page-9-7) as the naive fine-tuning method for image generation. The generated images are compared with the original data from the perspective of embedding distribution in Fig. [3.](#page-1-1)

The samples of random selection and pre-trained diffusion models present two extreme ends of the distribution. Random selection faithfully reflects the original distribution, yet fails to emphasize some high-density regions. In contrast, diffusion models are over-fitted to those dense areas, leaving a large part of the original distribution uncovered. We attribute these two distributions to two properties, respectively. The randomly selected data holds extraordinary *diversity*, and the diffusion-generated data shows *representativeness* to the original distribution. We claim that both properties are essential for constructing an effective surrogate dataset. By naive fine-tuning, Difffit better captures the representative regions but leaves more regions uncovered. To this end, we propose extra minimax criteria for the diffusion model to enhance both of the properties.

#### 2.3. Minimax Diffusion Criteria

Based on the observation that *representativeness* and *diversity* are two key factors to construct an effective surrogate dataset, we accordingly design extra minimax criteria to enhance these two essential properties for the diffusion model.

<span id="page-2-0"></span>Algorithm 1: Minimax Diffusion Fine-tuning **Input:** initialized model parameter  $\theta$ , original dataset  $\mathcal{T} = \{(\mathbf{x}, y)\}\text{, encoder } E\text{, class encoder } E_c\text{, time}$ step t, variance schedule  $\bar{\alpha}_t$ , real embedding memory M, predicted embedding memory D **Output:** optimized model parameter  $\theta^*$ for *each step* do Obtain the original embedding:  $z_0 = E(x)$ Obtain the class embedding:  $\mathbf{c} = E_c(y)$ Sample random noise:  $\epsilon \sim \mathcal{N}(\mathbf{0}, \mathbf{I})$ Add noise to the embedding:  $\mathbf{z}_t = \sqrt{\bar{\alpha}_t} \mathbf{z}_0 + \sqrt{1-\bar{\alpha}_t} \epsilon$ Predict the noise  $\epsilon_{\theta}(\mathbf{z}_t, \mathbf{c})$  and recovered embedding  $\hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c}) = \mathbf{z}_t - \epsilon_{\theta}(\mathbf{z}_t, \mathbf{c})$ Update the model parameter with Eq. [\(5\)](#page-3-0) Enqueue the real embedding:  $\mathcal{M}_r \leftarrow \mathbf{z}_0$ Enqueue the predicted embedding:  $\mathcal{M}_d \leftarrow \hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c})$ end

Representativeness It is essential for the small surrogate dataset to sufficiently represent the original data. A naive approach to improve the representativeness is aligning the embedding distribution between synthetic and real samples:

<span id="page-2-1"></span>
$$
\mathcal{L}_r = \arg \max_{\theta} \sigma \left( \hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c}), \frac{1}{N_B} \sum_{i=0}^{N_B} \mathbf{z}_i \right), \qquad (2)
$$

where  $\sigma(\cdot, \cdot)$  is the cosine similarity,  $\hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c})$  is the predicted original embedding by subtracting the noise from the noisy embedding  $\hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c}) = \mathbf{z}_t - \epsilon_{\theta}(\mathbf{z}_t, \mathbf{c})$ , and  $N_B$  is the size of the sampled real sample mini-batch. However, the naive alignment tends to draw the predicted embedding towards the center of the real distribution, which severely limits the diversity. Therefore, we propose to maintain an auxiliary memory  $\mathcal{M} = {\mathbf{z}_m}_{m=1}^{N_M}$  to store the real samples utilized in adjacent iterations, and design a minimax optimization objective as:

<span id="page-2-2"></span>
$$
\mathcal{L}_r = \arg \max_{\theta} \min_{m \in [N_M]} \sigma(\hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c}), \mathbf{z}_m). \tag{3}
$$

By pulling close the least similar sample pairs, the diffusion model is encouraged to generate images that better cover the original distribution. It is notable that the diffusion training objective  $\mathcal{L}_{simple}$  itself encourages the generated images to resemble the original ones. Thus, the minimax criterion allows the preservation of diversity to the maximum extent.

Diversity Although the pre-trained diffusion models already achieve satisfactory generation quality, the remaining defect is limited diversity compared with the original data, as shown in Fig. [3.](#page-1-1) We expect the data generated by the diffusion model can accurately reflect the original distribution, while simultaneously being different from

<span id="page-3-5"></span>each other. Hence, we maintain another auxiliary memory  $D = {\mathbf{z}_d}_{d=1}^{N_D}$  for the predicted embeddings of adjacent iterations and design another minimax objective to explicitly enhance the sample diversity as:

<span id="page-3-1"></span>
$$
\mathcal{L}_d = \arg\min_{\theta} \max_{d \in [N_D]} \sigma(\hat{\mathbf{z}}_{\theta}(\mathbf{z}_t, \mathbf{c}), \mathbf{z}_d).
$$
 (4)

The diversity term has an opposite optimization target compared with the representativeness term, where the predicted embedding is pushed away from the most similar one stored in the memory bank. Although diversity is essential for an effective surrogate set, too much of it will cause the generated data to lose representativeness. The proposed minimax optimization enhances the diversity in a gentle way, with less influence on the class-related features.

Combining all the components, we summarize the training process in Algorithm [1.](#page-2-0) The complete training objective can be formulated as:

<span id="page-3-0"></span>
$$
\mathcal{L} = \mathcal{L}_{simple} + \lambda_r \mathcal{L}_r + \lambda_d \mathcal{L}_d, \tag{5}
$$

where  $\lambda_r$  and  $\lambda_d$  are weighting hyper-parameters.

## 3. Theoretical Analysis

Assume that  $\mu$  is the real distribution of the latent variables **z** associated with the target dataset  $\mathcal{T}$ . We rewrite the optimization problem presented in Eq. [\(5\)](#page-3-0) in a modified form:

<span id="page-3-2"></span>
$$
\min_{\{\theta^{(i)}\}_{i\in[N_D]}} \lambda_{d} \max_{i,j=1,\dots,N_D} \sigma\left(\hat{\mathbf{z}}(\theta^{(i)}), \hat{\mathbf{z}}(\theta^{(j)})\right) + \sum_{i=1}^{N_D} \left\{-\lambda_r Q_{\tilde{q},w\sim\mu} \left[\sigma\left(\hat{\mathbf{z}}(\theta^{(i)}), w\right)\right] + \|\hat{\mathbf{z}}(\theta^{(i)}) - \mathbf{z}_0^{(i)}\|^2\right\}, \tag{6}
$$

where  $Q_{\tilde{q}}[\cdot]$  denotes the quantile function with  $\tilde{q}$  as the quantile percentage. Note that here we consider a theoretical idealized variant of our algorithm wherein we perform simultaneous generation of all the embeddings  $\{\hat{\mathbf{z}}(\theta^{(i)})\},\$ rather than sample by sample. Hence the objectives turn to the sum of pairwise similarities rather than the form in Eq. [\(4\)](#page-3-1). And we minimize the negative to aim for maximal representativeness, as in Eq. [\(5\)](#page-3-0).

It can be considered as a scalarized solution to a multiobjective optimization problem, wherein multiple criteria are weighed (see, *e.g*. [\[13\]](#page-8-12)). This perspective aligns with a Pareto front with trade-offs. It means that one objective decreasing will by necessity result in another increasing.

However, consider that any solution to the following trilevel optimization problem is also a solution for Eq. [\(6\)](#page-3-2):

$$
\min_{\{\theta^{(i)}\}_{i \in [N_D]}} \max_{i,j=1,\dots,N_D} \sigma\left(\hat{\mathbf{z}}(\theta^{(i)}), \hat{\mathbf{z}}(\theta^{(j)})\right)
$$

$$
\text{subj. to } \{\theta^{(i)}\} \in \arg\max \sum_{i=1}^{N_D} Q_{\tilde{q},w \sim \mu} \left[ \sigma\left(\hat{\mathbf{z}}(\theta^{(i)}), w\right) \right]
$$

$$
\text{subj. to } \theta^{(i)} \in \arg\min \|\hat{\mathbf{z}}(\theta) - \mathbf{z}_0^{(i)}\|^2, \forall i \in [N_D].
$$
(7)

If a solution to Eq. [\(7\)](#page-3-3) is discovered, either incidentally through solving Eq. [\(6\)](#page-3-2) or by careful tuning of step sizes, the set of minimizers will be sufficiently large at both levels, with no trade-offs involved. However, can we justify the presumption that there exists a meaningful set of potential minimizers?

Diffusion Process Model One popular framework for the mathematical analysis of diffusion involves analyzing the convergence and asymptotic properties of, appropriately homonymous, diffusion processes. These processes are characterized by the standard stochastic differential equation with a drift and diffusion term. For a time-dependent random variable  $Z_t$ ,

<span id="page-3-6"></span>
$$
dZ_t = V(Z_t)dt + dW_t \tag{8}
$$

where V is a drift function dependent on the current  $Z_t$  and  $dW_t$  is a Wiener (Brownian noise) process. This equation serves as an appropriate continuous approximation of generative diffusion, given that Brownian noise is a continuous limit of adding normal random variables. Consequently, we aim for any realization  $z \sim Z_t$  to have certain desired properties that reflect generative modeling with high probability.

The work [\[43\]](#page-9-9) established a theoretical model utilizing concepts in the control of these diffusions, demonstrating how it can result in sampling from the distribution of a desired data set. In the supplementary material we present a description of their framework and present an argument supporting the well-defined nature of the following problem, indicating that it has non-trivial solutions.

When sampling optimally from the population dataset, we consider a stochastic control problem wherein  $V$  depends also on some chosen control  $u(\mathbf{z}, t)$ . This control aims to find the most representative samples and, among the possible collection of such samples, to obtain the most diverse one while sampling from the desired dataset  $\mu$ . This involves solving:

<span id="page-3-4"></span>
$$
\min_{u(x,t)} \max_{i,j=1,...,N_D} \sigma\left(Z_1^{u,(i)}, Z_1^{u,(j)}\right)
$$
subj. to

$$
u \in \arg \max \sum_{i=1}^{N_D} \int_0^1 \mathbb{E}_{Z_t^{(i)}} Q_{\bar{q},w \sim \mu} \left[\sigma\left(Z_t^{(i)}, w\right)\right] ds
$$

$$
Z_1 \sim \mu,
$$

$$
dZ_t^{u,(i)} = u(Z_t^{u,(i)}, t)dt + dW_t, t \in [0, 1];
$$

$$
Z_0 = \mathbf{z}_0.
$$
 $(9)$ 

<span id="page-3-3"></span>This problem poses a bi-level stochastic control challenge where employing a layered dynamic programming is far from tractable. Additionally, a multi-stage stochastic programming approximation would also be infeasible given the scale of the datasets involved. Instead, we opt for parameterization with a neural network, forego exact sampling, discretize the problem, redefine the criteria to be time independent, and seek to solve an approximate solution for the tri-level optimization problem Eq. [\(7\)](#page-3-3).

| IPC (Ratio)  | <b>Test Model</b>                     | Random                                                              | K-Center $[37]$                                          | Herding $[48]$                                         | $DiT$ [33]                                                                     | DM [56]                                                  | IDC-1 $[21]$                                             | GLaD[4]                       | Ours                                                              | Full                                                                         |
|--------------|---------------------------------------|---------------------------------------------------------------------|----------------------------------------------------------|--------------------------------------------------------|--------------------------------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|-------------------------------|-------------------------------------------------------------------|------------------------------------------------------------------------------|
| $10(0.8\%)$  | ConvNet-6                             | $24.3_{+1.1}$                                                       | $19.4_{+0.9}$                                            | $26.7_{\pm 0.5}$                                       | $34.2_{+1.1}$                                                                  | $26.9_{\pm 1.2}$                                         | $33.3_{\pm 1.1}$                                         | $33.8_{+0.9}$                 | $37.0_{+1.0}$                                                     | $86.4_{\pm 0.2}$                                                             |
|              | ResNetAP-10                           | $29.4_{\pm 0.8}$                                                    | $22.1_{\pm 0.1}$                                         | $32.0_{\pm 0.3}$                                       | $34.7_{\pm0.5}$                                                                | $30.3_{\pm 1.2}$                                         | $39.1_{\pm 0.5}$                                         | $32.9_{\pm 0.9}$              | $39.2_{\pm 1.3}$                                                  | $87.5{\scriptstyle \pm0.5}$                                                  |
|              | ResNet-18                             | $27.7_{\pm 0.9}$                                                    | $21.1_{\pm 0.4}$                                         | $30.2_{\pm 1.2}$                                       | $34.7_{\pm 0.4}$                                                               | $33.4_{\pm 0.7}$                                         | $37.3_{\pm0.2}$                                          | $31.7_{\pm 0.8}$              | $37.6_{\pm 0.9}$                                                  | $89.3_{\pm 1.2}$                                                             |
| $20(1.6\%)$  | ConvNet-6                             | $29.1_{\pm 0.7}$                                                    | $21.5_{\pm 0.8}$                                         | $29.5_{\pm 0.3}$                                       | $36.1_{\pm 0.8}$                                                               | $29.9_{\pm 1.0}$                                         | $35.5_{\pm 0.8}$                                         | $\overline{\phantom{0}}$      | $37.6_{+0.2}$                                                     | $86.4_{+0.2}$                                                                |
|              | ResNetAP-10                           | $32.7_{\pm 0.4}$                                                    | $25.1_{\pm 0.7}$                                         | 34.9 $\pm$ 0.1                                         | $41.1_{\pm 0.8}$                                                               | $35.2_{\pm 0.6}$                                         | $43.4_{\pm 0.3}$                                         | $\overline{\phantom{a}}$      | $\textbf{45.8}_{\pm 0.5}$                                         | $87.5{\scriptstyle \pm0.5}$                                                  |
|              | ResNet-18                             | $29.7{\scriptstyle \pm 0.5}$                                        | $23.6{\scriptstyle \pm0.3}$                              | $32.2_{\pm 0.6}$                                       | $40.5{\scriptstyle \pm0.5}$                                                    | $29.8{\scriptstyle \pm1.7}$                              | $38.6{\scriptstyle \pm0.2}$                              | $\overline{\phantom{a}}$      | $42.5_{\pm0.6}$                                                   | $89.3{\scriptstyle \pm1.2}$                                                  |
| 50 $(3.8\%)$ | ConvNet-6                             | $41.3{\scriptstyle \pm0.6}$                                         | $36.5{\scriptstyle \pm1.0}$                              | $40.3{\scriptstyle \pm0.7}$                            | $46.5{\scriptstyle \pm 0.8}$                                                   | $44.4_{\pm 1.0}$                                         | $43.9_{\pm 1.2}$                                         | $\overline{\phantom{0}}$      | 53.9 $\pm$ 0.6                                                    | $86.4_{\pm 0.2}$                                                             |
|              | ResNetAP-10                           | $47.2_{\pm 1.3}$                                                    | $40.6{\scriptstyle \pm0.4}$                              | $49.1_{\pm 0.7}$                                       | $49.3{\scriptstyle \pm0.2}$                                                    | $47.1_{\pm 1.1}$                                         | $48.3{\scriptstyle \pm1.0}$                              | $\overline{\phantom{a}}$      | $56.3_{\pm 1.0}$                                                  | $87.5{\scriptstyle \pm0.5}$                                                  |
|              | ResNet-18                             | $47.9_{\pm 1.8}$                                                    | $39.6_{\pm 1.0}$                                         | $48.3_{\pm1.2}$                                        | $50.1_{\pm 0.5}$                                                               | $46.2_{\pm 0.6}$                                         | $48.3_{\pm 0.8}$                                         | ٠                             | $\mathbf{57.1}_{\pm0.6}$                                          | $89.3{\scriptstyle \pm1.2}$                                                  |
| $70(5.4\%)$  | ConvNet-6<br>ResNetAP-10<br>ResNet-18 | $46.3_{\pm 0.6}$<br>$50.8_{\pm 0.6}$<br>$52.1_{\pm 1.0}$            | $38.6_{+0.7}$<br>$45.9_{\pm 1.5}$<br>$44.6_{\pm 1.1}$    | $46.2_{+0.6}$<br>$53.4_{\pm 1.4}$<br>49.7 $\pm$ 0.8    | $50.1_{\pm1.2}$<br>54.3 $\pm$ 0.9<br>$51.5_{\pm 1.0}$                          | $47.5_{\pm 0.8}$<br>$51.7_{\pm 0.8}$<br>$51.9_{\pm 0.8}$ | 48.9 $\pm$ 0.7<br>$52.8_{\pm 1.8}$<br>$51.1_{\pm1.7}$    | $\overline{\phantom{a}}$<br>۰ | $55.7_{\pm0.9}$<br>$58.3_{\pm 0.2}$<br>$\textbf{58.8}_{\pm 0.7}$  | $86.4_{\pm 0.2}$<br>$87.5_{\pm 0.5}$<br>89.3 $\pm$ 1.2                       |
| $100(7.7\%)$ | ConvNet-6<br>ResNetAP-10<br>ResNet-18 | $52.2_{\pm 0.4}$<br>$59.4_{\pm 1.0}$<br>$61.5{\scriptstyle \pm1.3}$ | $45.1_{\pm 0.5}$<br>$54.8_{\pm 0.2}$<br>$50.4_{\pm 0.4}$ | $54.4_{\pm 1.1}$<br>$61.7_{\pm 0.9}$<br>59.3 $\pm$ 0.7 | $53.4_{\pm0.3}$<br>$58.3{\scriptstyle \pm 0.8}$<br>$58.9{\scriptstyle \pm1.3}$ | $55.0_{+1.3}$<br>$56.4_{\pm 0.8}$<br>$60.2_{\pm 1.0}$    | $53.2_{\pm 0.9}$<br>$56.1_{\pm 0.9}$<br>$58.3_{\pm 1.2}$ | ٠<br>$\overline{\phantom{a}}$ | $61.1_{\pm0.7}$<br>$64.5{\scriptstyle \pm 0.2}$<br>65.7 $\pm$ 0.4 | $86.4_{+0.2}$<br>$87.5{\scriptstyle \pm 0.5}$<br>$89.3{\scriptstyle \pm1.2}$ |

<span id="page-4-2"></span><span id="page-4-0"></span>Table 1. Performance comparison with pre-trained diffusion models and other state-of-the-art methods on ImageWoof. All the results are reproduced by us on the 256×256 resolution. The missing results are due to out-of-memory. The best results are marked as bold.

In the supplementary material we provide a rationale for the meaningfulness of the problem in Eq. [\(9\)](#page-3-4) based on the model of generative diffusion [\[43\]](#page-9-9). Specifically, we argue that the set of controls that leads to the desired final distribution and the set of minimizers, is sufficiently large for a low value of the objective at the top layer.

# 4. Experiments

## 4.1. Implementation Details

For the diffusion model, we adopt pre-trained DiT [\[33\]](#page-9-8) as the baseline and conduct PEFT with Difffit [\[50\]](#page-9-7).  $\lambda_r$  and  $\lambda_d$ are set as 0.002 and 0.008 for Eq. [\(5\)](#page-3-0), respectively. The image size for the diffusion fine-tuning and sample generation is set as  $256 \times 256$ . The fine-tuning mini-batch size is set as 8, and the fine-tuning lasts 8 epochs. The learning rate is set as 1e-3 for an AdamW optimizer. After fine-tuning, the images are generated by 50 denoising steps on a pre-defined number of random noise, according to the IPC setting. All the experiments are conducted on a single RTX 4090 GPU.

#### 4.2. Datasets and Evaluation Metric

For practical applicability, the experiments are exclusively conducted on full-sized ImageNet [\[6\]](#page-8-0) subsets in this work. The selected subsets include ImageWoof, ImageNette [\[15\]](#page-8-3) and the 10-class split adopted in  $[21, 42]$  $[21, 42]$  $[21, 42]$ , denoted as ImageIDC afterward. ImageWoof is a challenging subset, containing only classes of dog breeds, while ImageNette and ImageIDC contain classes with less similarity, and hence are easier to discriminate. For evaluation, we adopt the same setting as in [\[21\]](#page-8-5). The surrogate dataset is trained on different model architectures, with a learning rate of 0.01, <span id="page-4-1"></span>Table 2. The Maximum Mean Discrepancy (MMD) between the extracted features of surrogate dataset and the original one.

| IPC | DiT [33] | Diffit [50] | DM [56] | IDC-1 [21] | Ours |
|-----|----------|-------------|---------|------------|------|
| 50  | 5.4      | 5.4         | 4.8     | 6.7        | 4.0  |
| 100 | 5.5      | 5.3         | 4.0     | 6.4        | 4.3  |

and a scheduler decaying the learning rate at 2/3 and 5/6 of the whole training iterations. The top-1 accuracy on the original testing set is reported to illustrate the performance.

## 4.3. Comparison with State-of-the-art Methods

We compare our method with other state-of-the-art methods across different IPC settings and model architectures. For a fair comparison, the results are all reproduced by us under the same evaluation protocol. ResNet-10 [\[18\]](#page-8-2) with average pooling is adopted for matching the feature distribution (DM [\[56\]](#page-10-2), GLaD [\[4\]](#page-8-7)) and training gradients (IDC-1 [\[21\]](#page-8-5)). DM is implemented on IDC-1 by only modifying the matching objective from training gradients to feature distribution, such that better performance is achieved. Each experiment is conducted 3 times, with the mean value and standard variance reported. Firstly, we present the validation results on the challenging ImageWoof subset [\[15\]](#page-8-3) in Tab. [1.](#page-4-0)

With the target of distilling surrogate datasets of small IPCs (*e.g*., 10 and 20), the pixel-level optimization method IDC-1 [\[21\]](#page-8-5) demonstrates outstanding performance gain over random original images. However, as the IPC increases, the performance gain drastically drops. Especially under the 100-IPC setting, the distilled dataset even performs worse than random original images. This observation

<span id="page-5-3"></span><span id="page-5-0"></span>Table 3. Performance comparison with pre-trained diffusion models and state-of-the-art methods on more ImageNet subsets. The results are obtained on ResNet-10 with average pooling. The best results are marked as bold.

|       | IPC | Random           | DiT [33]         | DM [56]          | Ours             | IPC | SRe2L [53]       | RDED [41]        | DiT              | Ours             |
|-------|-----|------------------|------------------|------------------|------------------|-----|------------------|------------------|------------------|------------------|
| Nette | 10  | $54.2_{\pm 1.6}$ | $59.1_{\pm 0.7}$ | $60.8_{\pm 0.6}$ | $62.0_{\pm 0.2}$ | 10  | $21.3_{\pm 0.6}$ | $42.0_{\pm 0.1}$ | $39.6_{\pm 0.4}$ | $44.3_{\pm 0.5}$ |
|       | 20  | $63.5_{\pm 0.5}$ | $64.8_{\pm 1.2}$ | $66.5_{\pm 1.1}$ | $66.8_{\pm 0.4}$ | 50  | $46.8_{\pm 0.2}$ | $56.5_{\pm 0.1}$ | $52.9_{\pm 0.6}$ | $58.6_{\pm 0.3}$ |
|       | 50  | $76.1_{\pm 1.1}$ | $73.3_{\pm 0.9}$ | $76.2_{\pm 0.4}$ | $76.6_{\pm 0.2}$ |     |                  |                  |                  |                  |
| IDC   | 10  | $48.1_{\pm 0.8}$ | $54.1_{\pm 0.4}$ | $52.8_{\pm 0.5}$ | $53.1_{\pm 0.2}$ |     |                  |                  |                  |                  |
|       | 20  | $52.5_{\pm 0.9}$ | $58.9_{\pm 0.2}$ | $58.5_{\pm 0.4}$ | $59.0_{\pm 0.4}$ |     |                  |                  |                  |                  |
|       | 50  | $68.1_{\pm 0.7}$ | $64.3_{\pm 0.6}$ | $69.1_{\pm 0.8}$ | $69.6_{\pm 0.2}$ |     |                  |                  |                  |                  |

aligns with the empirical findings in Fig. [2,](#page-1-0) where pixellevel methods struggle to optimize the expanded parameter space of large IPCs. The embedding-level optimization method GLaD [\[4\]](#page-8-7) yields good performance under the 10- IPC setting. However, it requires overwhelming GPU resources for larger IPC settings, which is inapplicable for resource-restricted scenarios. It is also notable that on large IPCs, the coreset method Herding [\[48\]](#page-9-11) surpasses previous DD methods with far less computational cost.

The pre-trained DiT [\[33\]](#page-9-8) here serves as the baseline for generative diffusion techniques. Under the 50-IPC setting, DiT outperforms both random original images and IDC-1. However, the insufficiency of representativeness and diversity restricts its performance on smaller and larger IPCs, respectively. In contrast, our proposed minimax diffusion consistently provides superior performance across all the IPCs over both original images and Herding. Besides, the proposed method eliminates the need of specific network architectures for matching training metrics. Consequently, the cross-architecture generalization is significantly improved. Under most IPC settings, the performance gap between ConvNet-6 and ResNetAP-10 is even smaller than that of the original images. It validates the universality of the rich information learned by the minimax fine-tuning process.

Furthermore, we extensively assess the Maximum Mean Discrepancy (MMD) between the embedded features of the selected/generated surrogate dataset and the original one in Tab. [2.](#page-4-1) The features are extracted by a ResNet-10 network pre-trained on the full original dataset. Our method achieves the lowest discrepancy by average, where DM [\[56\]](#page-10-2) directly sets MMD as the optimization target, proving the validity of extra minimax criteria in fitting distributions.

Moreover, we show the performance comparison on ImageNette [\[15\]](#page-8-3) and ImageIDC [\[21\]](#page-8-5) in Tab. [3.](#page-5-0) The performance trend generally aligns with that on ImageWoof. More specifically, on these two easier subsets, DiT quickly loses the advantage over original images as IPC increases. Conversely, our proposed minimax diffusion method consistently demonstrates state-of-the-art performance.

<span id="page-5-1"></span>Table 4. Performance comparison on ImageNet-1K.

<span id="page-5-2"></span>Image /page/5/Figure/8 description: This image contains two line graphs side-by-side, titled "ImageWoof" on the left and "ImageIDC" on the right. Both graphs plot "Validation Accuracy (%)" on the y-axis against "IPC" on the x-axis, with values ranging from 10 to 100. The "ImageWoof" graph shows five lines representing different methods: "Difffit" (blue), "DiT" (purple), "Random" (green), "Ours full" (pink), "Ld only" (orange), and "Lr only" (yellow). The "ImageIDC" graph displays the same methods. The "ImageWoof" graph shows validation accuracy ranging from approximately 30% to 65%, while the "ImageIDC" graph shows accuracy ranging from approximately 48% to 76%.

Figure 4. With the help of the minimax diffusion, the proposed method significantly enhances the representativeness and diversity of the generated images. Thereby it consistently provides superior performance compared with random selection and baseline diffusion models by a large margin across different IPC settings.

Experiments on ImageNet-1K. We further conduct experiments on the full ImageNet-1K with the validation protocol of RDED [\[41\]](#page-9-14) and present the results in Tab. [4.](#page-5-1) The synthetic images are resized to  $224 \times 224$  for evaluation. The significant performance advantage over the compared works validates the scalability of the proposed method.

#### 4.4. Ablation Study

Component Analysis. We compare the performance with baseline diffusion models to validate the effectiveness of proposed minimax criteria in Fig. [4.](#page-5-2) The experiments are conducted on ImageWoof and ImageIDC to evaluate the effect on challenging and easy tasks, respectively. Under the IPC of 10 and 20, the raw diffusion models (DiT) generate informative images, with validation performance much higher than randomly selected original samples. However, as the IPC is continuously increased, the performance gap diminishes for ImageWoof, and random original images surpass the DiT-generated ones at the IPC of 100. On ImageIDC the intersection occurs even earlier at the IPC of 50. The main reason is reflected in Fig. [3,](#page-1-1) where the sample diversity remains limited without external guidance. The naive Difffit fine-tuning adapts the model to specific domains, yet on large IPCs, the over-fitted generative model still yields inferior performance than the original images.

<span id="page-6-2"></span><span id="page-6-1"></span>Image /page/6/Picture/0 description: This image displays a grid of images, comparing different methods for image generation. The rows are labeled "Original", "DiT", "Diffifit", and "Ours". The columns are divided by a dashed line, with the left side showing images of golden retrievers and the right side showing images of churches. The "Original" row shows a mix of golden retrievers and churches. The "DiT" row shows generated images of golden retrievers and churches. The "Diffifit" row shows more generated images of golden retrievers and churches. The "Ours" row shows the final set of generated images of golden retrievers and churches, which appear to be the most realistic or highest quality.

Figure 5. Visualization of random original images, images generated by baseline diffusion models (DiT [\[33\]](#page-9-8) and Difffit [\[50\]](#page-9-7)) and our proposed method. For each column, the generated images are based on the same random seed. Comparatively, our method significantly enhances the coverage of original data distribution and the diversity of the surrogate dataset.

<span id="page-6-0"></span>Table 5. The ablation study of the proposed minimax scheme. The result are obtained with ResNet-10 on ImageWoof and ImageIDC. m denotes the proposed minimax optimization scheme.

|              | $\mathcal{L}_r$ | $\mathcal{L}_r \\text{w}\\text{/m}$ | $\mathcal{L}_d$ | $\mathcal{L}_d \\text{w}\\text{/m}$ | ImageWoof      |                | ImageIDC       |                |
|--------------|-----------------|-------------------------------------|-----------------|-------------------------------------|----------------|----------------|----------------|----------------|
|              |                 |                                     |                 |                                     | 10-IPC         | 50-IPC         | 10-IPC         | 50-IPC         |
|              | -               | -                                   | -               | -                                   | 35.6 $\pm 0.9$ | 51.0 $\pm 0.9$ | 53.5 $\pm 0.2$ | 66.3 $\pm 0.2$ |
| $\checkmark$ | -               | -                                   | -               | -                                   | 34.4 $\pm 1.1$ | 47.1 $\pm 0.5$ | 49.6 $\pm 0.7$ | 60.2 $\pm 1.2$ |
|              | -               | $\checkmark$                        | -               | -                                   | 37.4 $\pm 0.4$ | 49.5 $\pm 1.0$ | 54.5 $\pm 1.2$ | 65.0 $\pm 0.8$ |
|              | -               | -                                   | $\checkmark$    | -                                   | 35.7 $\pm 0.8$ | 48.3 $\pm 0.6$ | 51.5 $\pm 0.6$ | 64.8 $\pm 0.8$ |
|              | -               | -                                   | -               | $\checkmark$                        | 38.7 $\pm 0.9$ | 54.9 $\pm 0.7$ | 52.2 $\pm 0.6$ | 68.4 $\pm 0.7$ |
| $\checkmark$ | -               | -                                   | $\checkmark$    | -                                   | 38.3 $\pm 0.5$ | 54.9 $\pm 0.4$ | 53.3 $\pm 0.5$ | 66.8 $\pm 0.5$ |
|              | -               | $\checkmark$                        | -               | $\checkmark$                        | 39.2 $\pm 1.3$ | 56.3 $\pm 1.0$ | 53.1 $\pm 0.2$ | 69.6 $\pm 0.2$ |

The addition of representativeness constraint to the training process further enhances the effect of distribution fitting. At small IPCs, the generated images contain richer information, yet for larger IPCs, the lack of diversity brings a negative influence. The diversity constraint, in contrast, significantly boosts the information contained in the generated surrogate dataset. Despite the performance advantage of  $\mathcal{L}_d$  over  $\mathcal{L}_r$ , combining them still brings stable improvement as our full method. Especially on the easier ImageIDC task, grouping these two constraints together contributes to a consistent performance margin over random original images. The experimental results validate that both representativeness and diversity play essential parts in constructing effective surrogate datasets.

Minimax Scheme. In this work, we propose to enhance the representativeness and diversity each with a minimax objective. We compare the distillation result with or without the minimax operation in Tab. [5.](#page-6-0) The first row presents the performance of naive Difffit fine-tuning. Matching the embeddings to the distribution center as in Eq. [\(2\)](#page-2-1) severely degrades the validation performance across all IPCs. In contrast, the minimax version constraint as in Eq. [\(3\)](#page-2-2) encourages better coverage, where the performance on small IPCs is improved. The effects of diversity constraint and the full method show similar trends. The superior performance suggests the effectiveness in enhancing the essential properties of the generative diffusion techniques.

## 4.5. Visualization

Sample Distribution Visualization. The target of our proposed method is to construct a surrogate dataset with both representativeness and diversity. We visualize the t-SNE distribution of samples generated by our proposed method in Fig. [3.](#page-1-1) In comparison with random original images and baseline diffusion models, our method demonstrates a more thorough coverage over the entire data distribution while maintaining consistency in sample density. At the original high-density region, the generated images also form a dense sub-cluster, which is not reflected by random sampling. On the other hand, at the original sparse regions, our method exhibits better coverage than baseline diffusion models. By simultaneously enhancing the representativeness and diversity in the generative model, the proposed method manages to significantly improve the validation performance of the generated surrogate dataset.

Generated Sample Comparison. The proposed method notably enhances the representativeness and diversity of the generated surrogate dataset. We compare the samples generated with the same random noise (for each column) of

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays four line graphs, labeled (a), (b), (c), and (d), each illustrating the relationship between validation accuracy and a different parameter. The y-axis for all graphs represents 'Validation Accuracy (%)' ranging from 35 to 75. Graph (a) plots accuracy against 'Training Epoch' from 0 to 12. Graph (b) plots accuracy against 'Representativeness Weight \(\lambda\_r\)' from 0.001 to 0.01. Graph (c) plots accuracy against 'Diversity Weight \(\lambda\_d\)' from 0.002 to 0.03. Graph (d) plots accuracy against 'Memory Size \(N\_M\)' from 16 to 1024. Each graph shows five lines representing different configurations: IPC10 (green), IPC20 (brown), IPC50 (blue), IPC70 (orange), and IPC100 (pink). Red dashed lines and red stars indicate specific points of interest on graphs (a), (b), and (c). The shaded areas around the lines represent confidence intervals.

Figure 6. Hyper-parameter analysis on (a) the training epochs; (b) the representativeness weight  $\lambda_r$ ; (c) the diversity weight  $\lambda_d$ ; (d) the memory size  $N_M$ . The results are obtained with ResNetAP-10 on ImageWoof. The dashed line indicates the value adopted in this work.

different generative methods in Fig. [5](#page-6-1) to explicitly demonstrate the improved properties.

The images generated by baseline DiT exhibit a realistic high-quality appearance. However, the images tend to share similar poses and only present the most prominent features of the objects. In the golden retriever case, the generated images mostly present the head part, while for the churches the exterior appearance. Difffit fine-tuning further fits the model to the distribution, but in most cases, the differences only lie in small details. Comparatively, the proposed minimax criteria significantly enhance both the representativeness and diversity of the generated images. On the one hand, there occurs more class-related content in the generated images. The golden retriever images include more body parts and the church images encompass the interior layout. The minimax optimization leads to better coverage over the entire original distribution, with more related features encapsulated. On the other hand, the diversity is significantly enhanced, including variations in pose, background, and appearance styles. In such a way the surrogate dataset better represents the original large-scale one, leading to superior validation performance. More sample visualizations are provided in the supplementary material.

Training Curve Visualization. We visualize the accuracy curve during the training process in Fig. [6a.](#page-7-0) The validation performance is rapidly improved as the fine-tuning process starts. After four epochs, the model tends to converge and reaches the highest performance at the 8th epoch. Further extending the training epochs injects excessive diversity into the model, leading to performance degradation. We demonstrate the influence of training epochs on the generated images in supplementary material.

#### 4.6. Parameter Analysis

**Objective Weight**  $\lambda_r \lambda_d$ . We show the influence of representativeness weight  $\lambda_r$  and diversity weight  $\lambda_d$  in Fig. [6b](#page-7-0) and Fig. [6c,](#page-7-0) respectively. The  $\lambda_r$  variation only produces negligible performance fluctuation on small IPCs, while on large IPCs the performance is also relatively stable. For  $\lambda_d$ , at a proper variation range, the performance is stable. However, continuously increasing the diversity of the generated dataset leads to a lack of representativeness, which results in a negative impact. The negative impact of overdiversity can also validated by the poor performance of K-Center in Tab. [1.](#page-4-0) A uniform performance decrease is observed as  $\lambda_d$  reaches 0.03. Based on the performance of 100 IPC, we set  $\lambda_r$  as 0.002 and  $\lambda_d$  as 0.008.

**Memory Size**  $N_M$ . The memory size  $N_M$  influences the number of samples involved in the objective calculation. We investigate its influence in Fig. [6d.](#page-7-0) When the memory is extremely small  $(N_M=16)$ , the provided supervision is also limited, yet the performance is already higher than naive fine-tuning. As the memory size is increased in a proper range, the model yields stable performance improvement. It is notable that with a larger memory, the performance under the IPC of 10 is better. It can be explained by that a larger memory contains more representative information. Out of the consideration of performance as well as storage burden, we select the memory of 64 in the other experiments.

# 5. Conclusion

In this work, we propose a novel dataset distillation method based on generative diffusion techniques. Through extra minimax criteria, the proposed method significantly enhances the representativeness and diversity of the generated surrogate dataset. With much less computational time consumption, the proposed method achieves state-of-the-art validation performance on challenging ImageNet subsets. It reduces the resource dependency of previous dataset distillation methods and opens up new possibilities for more practical applications for distilling personalized data.

Limitations and Future Works. This work mainly focuses on the classification task. In future works, we will explore the possibility of incorporating generative techniques for more specific data domains.

## References

- <span id="page-8-24"></span>[1] Shekoofeh Azizi, Simon Kornblith, Chitwan Saharia, Mohammad Norouzi, and David J Fleet. Synthetic data from diffusion models improves imagenet classification. *arXiv preprint arXiv:2304.08466*, 2023. [2](#page-1-2)
- <span id="page-8-25"></span>[2] Victor Besnier, Himalaya Jain, Andrei Bursuc, Matthieu Cord, and Patrick Pérez. This Dataset Does Not Exist: Training Models from Generated Images. In *ICASSP*, pages 1–5, 2020. [2](#page-1-2)
- <span id="page-8-8"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pages 4750–4759, 2022. [1](#page-0-1)
- <span id="page-8-7"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, pages 3739–3748, 2023. [1,](#page-0-1) [5,](#page-4-2) [6](#page-5-3)
- <span id="page-8-4"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *NeurIPS*, 35:810–822, 2022. [1](#page-0-1)
- <span id="page-8-0"></span>[6] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. ImageNet: A large-scale hierarchical image database. In *CVPR*, pages 248–255, 2009. [1,](#page-0-1) [5](#page-4-2)
- <span id="page-8-18"></span>[7] Zhiwei Deng and Olga Russakovsky. Remember the Past: Distilling Datasets into Addressable Memories for Neural Networks. In *NeurIPS*, 2022. [1](#page-0-1)
- <span id="page-8-11"></span>[8] Prafulla Dhariwal and Alexander Nichol. Diffusion Models Beat GANs on Image Synthesis. In *NeurIPS*, pages 8780–8794, 2021. [3,](#page-2-3) [2](#page-1-2)
- <span id="page-8-15"></span>[9] Thinh T Doan. Nonlinear two-time-scale stochastic approximation convergence and finite-time performance. *IEEE Transactions on Automatic Control*, 2022. [1](#page-0-1)
- <span id="page-8-1"></span>[10] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale. In *ICLR*, 2022. [1](#page-0-1)
- <span id="page-8-21"></span>[11] Jiawei Du, Yidi Jiang, Vincent Y.F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation. In *CVPR*, pages 3749–3758, 2023. [1](#page-0-1)
- <span id="page-8-22"></span>[12] Lisa Dunlap, Alyssa Umino, Han Zhang, Jiezhi Yang, Joseph E Gonzalez, and Trevor Darrell. Diversify your vision datasets with automatic diffusion-based augmentation. *arXiv preprint arXiv:2305.16289*, 2023. [2](#page-1-2)
- <span id="page-8-12"></span>[13] Gabriele Eichfelder. Scalarizations for adaptively solving multi-objective optimization problems. *Com-*

*putational Optimization and Applications*, 44:249– 273, 2009. [4](#page-3-5)

- <span id="page-8-13"></span>[14] Ronen Eldan and James R Lee. Regularization under diffusion and anticoncentration of the information content. *Duke Mathematical Journal*, 167(5):969– 993, 2018. [1](#page-0-1)
- <span id="page-8-3"></span>[15] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french. [1,](#page-0-1) [5,](#page-4-2) [6](#page-5-3)
- <span id="page-8-16"></span>[16] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memoryrestricted online continual learning. *arXiv preprint arXiv:2305.16645*, 2023. [1](#page-0-1)
- <span id="page-8-26"></span>[17] Swaminathan Gurumurthy, Ravi Kiran Sarvadevabhatla, and R. Venkatesh Babu. DeLiGAN: Generative Adversarial Networks for Diverse and Limited Data. In *CVPR*, pages 4941–4949, 2017. [2](#page-1-2)
- <span id="page-8-2"></span>[18] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, pages 770–778, 2016. [1,](#page-0-1) [5,](#page-4-2) [2](#page-1-2)
- <span id="page-8-23"></span>[19] Ruifei He, Shuyang Sun, Xin Yu, Chuhui Xue, Wenqing Zhang, Philip Torr, Song Bai, and Xiaojuan Qi. Is Synthetic Data from Generative Models Ready for Image Recognition? In *ICLR*, 2022. [2](#page-1-2)
- <span id="page-8-9"></span>[20] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising Diffusion Probabilistic Models. In *NeurIPS*, pages 6840–6851, 2020. [2](#page-1-2)
- <span id="page-8-5"></span>[21] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, pages 11102–11118, 2022. [1,](#page-0-1) [5,](#page-4-2) [6,](#page-5-3) [2,](#page-1-2) [3](#page-2-3)
- <span id="page-8-10"></span>[22] Diederik Kingma, Tim Salimans, Ben Poole, and Jonathan Ho. Variational Diffusion Models. In *NeurIPS*, pages 21696–21707, 2021. [2](#page-1-2)
- <span id="page-8-20"></span>[23] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, pages 12352– 12364, 2022. [1](#page-0-1)
- <span id="page-8-14"></span>[24] Joseph Lehec. Representation formula for the entropy and functional inequalities. In *Annales de l'IHP Probabilites et statistiques ´* , pages 885–899, 2013. [1](#page-0-1)
- <span id="page-8-17"></span>[25] Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta Knowledge Condensation for Federated Learning. In *ICLR*, 2022. [1](#page-0-1)
- <span id="page-8-6"></span>[26] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *ICCV*, 2023. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-8-19"></span>[27] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *NeurIPS*, 35:13877– 13891, 2022. [1](#page-0-1)

- <span id="page-9-18"></span>[28] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023. [1](#page-0-1)
- <span id="page-9-25"></span>[29] Qi Mao, Hsin-Ying Lee, Hung-Yu Tseng, Siwei Ma, and Ming-Hsuan Yang. Mode Seeking Generative Adversarial Networks for Diverse Image Synthesis. In *CVPR*, pages 1429–1437, 2019. [2](#page-1-2)
- <span id="page-9-1"></span>[30] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021. [1](#page-0-1)
- <span id="page-9-2"></span>[31] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 34:5186–5198, 2021. [1](#page-0-1)
- <span id="page-9-5"></span>[32] Alexander Quinn Nichol and Prafulla Dhariwal. Improved Denoising Diffusion Probabilistic Models. In *ICML*, pages 8162–8171, 2021. [2](#page-1-2)
- <span id="page-9-8"></span>[33] William Peebles and Saining Xie. Scalable diffusion models with transformers. In *ICCV*, pages 4195–4205, 2023. [3,](#page-2-3) [5,](#page-4-2) [6,](#page-5-3) [7](#page-6-2)
- <span id="page-9-6"></span>[34] Nataniel Ruiz, Yuanzhen Li, Varun Jampani, Yael Pritch, Michael Rubinstein, and Kfir Aberman. Dreambooth: Fine tuning text-to-image diffusion models for subject-driven generation. In *CVPR*, pages 22500–22510, 2023. [3](#page-2-3)
- <span id="page-9-19"></span>[35] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. DataDAM: Efficient Dataset Distillation with Attention Matching. In *ICCV*, 2023. [1](#page-0-1)
- <span id="page-9-23"></span>[36] Mert Bulent Sariyildiz, Karteek Alahari, Diane Larlus, and Yannis Kalantidis. Fake it Till You Make it: Learning Transferable Representations from Synthetic ImageNet Clones. In *CVPR*, pages 8011–8021, 2023. [2](#page-1-2)
- <span id="page-9-10"></span>[37] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018. [5](#page-4-2)
- <span id="page-9-15"></span>[38] Allahkaram Shafiei, Vyacheslav Kungurtsev, and Jakub Marecek. Trilevel and multilevel optimization using monotone operator theory. *arXiv preprint arXiv:2105.09407*, 2021. [1](#page-0-1)
- <span id="page-9-24"></span>[39] Jordan Shipard, Arnold Wiliem, Kien Nguyen Thanh, Wei Xiang, and Clinton Fookes. Diversity Is Definitely Needed: Improving Model-Agnostic Zero-Shot Classification via Stable Diffusion. In *CVPR*, pages 769–778, 2023. [2](#page-1-2)
- <span id="page-9-17"></span>[40] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative Teaching Networks: Accelerating Neural Architecture Search by Learning to Generate Synthetic Training Data. In *ICML*, pages 9206–9216, 2020. [1](#page-0-1)

- <span id="page-9-14"></span>[41] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *CVPR*, 2024. [6](#page-5-3)
- <span id="page-9-12"></span>[42] Yonglong Tian, Dilip Krishnan, and Phillip Isola. Contrastive multiview coding. In *ECCV*, pages 776– 794, 2020. [5](#page-4-2)
- <span id="page-9-9"></span>[43] Belinda Tzen and Maxim Raginsky. Theoretical guarantees for sampling and inference in generative models with latent diffusions. In *COLT*, pages 3084–3114, 2019. [4,](#page-3-5) [5,](#page-4-2) [1](#page-0-1)
- <span id="page-9-3"></span>[44] Saeed Vahidian, Mingyu Wang, Jianyang Gu, Vyacheslav Kungurtsev, Wei Jiang, and Yiran Chen. Group distributionally robust dataset distillation with risk minimization. *arXiv preprint arXiv:2402.04676*, 2024. [1](#page-0-1)
- <span id="page-9-20"></span>[45] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, pages 12196–12205, 2022. [1](#page-0-1)
- <span id="page-9-21"></span>[46] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023. [1](#page-0-1)
- <span id="page-9-0"></span>[47] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-9-11"></span>[48] Max Welling. Herding dynamical weights to learn. In *ICML*, pages 1121–1128, 2009. [5,](#page-4-2) [6,](#page-5-3) [3](#page-2-3)
- <span id="page-9-22"></span>[49] Xindi Wu, Zhiwei Deng, and Olga Russakovsky. Multimodal dataset distillation for image-text retrieval. *arXiv preprint arXiv:2308.07545*, 2023. [1](#page-0-1)
- <span id="page-9-7"></span>[50] Enze Xie, Lewei Yao, Han Shi, Zhili Liu, Daquan Zhou, Zhaoqiang Liu, Jiawei Li, and Zhenguo Li. Difffit: Unlocking transferability of large diffusion models via simple parameter-efficient fine-tuning. *arXiv preprint arXiv:2304.06648*, 2023. [3,](#page-2-3) [5,](#page-4-2) [7,](#page-6-2) [2](#page-1-2)
- <span id="page-9-16"></span>[51] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. FedDM: Iterative Distribution Matching for Communication-Efficient Federated Learning. In *CVPR*, pages 16323–16332, 2023. [1](#page-0-1)
- <span id="page-9-26"></span>[52] Dingdong Yang, Seunghoon Hong, Yunseok Jang, Tianchen Zhao, and Honglak Lee. Diversity-Sensitive Conditional Generative Adversarial Networks. In *ICLR*, 2018. [2](#page-1-2)
- <span id="page-9-13"></span>[53] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023. [6](#page-5-3)
- <span id="page-9-4"></span>[54] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, pages 12674–12685, 2021. [1,](#page-0-1) [2](#page-1-2)

- <span id="page-10-1"></span>[55] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022. [1](#page-0-1)
- <span id="page-10-2"></span>[56] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, pages 6514–6523, 2023. [2,](#page-1-2) [5,](#page-4-2) [6,](#page-5-3) [1](#page-0-1)
- <span id="page-10-0"></span>[57] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-10-4"></span>[58] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved Distribution Matching for Dataset Condensation. In *CVPR*, pages 7856–7865, 2023. [1](#page-0-1)
- <span id="page-10-3"></span>[59] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *NeurIPS*, 35:9813–9827, 2022. [1](#page-0-1)
- <span id="page-10-5"></span>[60] Yongchao Zhou, Hshmat Sahak, and Jimmy Ba. Training on thin air: Improve image classification with generated data. *arXiv preprint arXiv:2305.15316*, 2023. [2](#page-1-2)

# Efficient Dataset Distillation via Minimax Diffusion

# Supplementary Material

The supplementary material is organized as follows: Section [6](#page-11-0) provides more detailed theoretical analysis; Section [7](#page-11-1) presents the related work to this paper; Section [8](#page-12-0) elaborates upon the method pipeline; Section [9](#page-12-1) contains additional implementation details; Section [10](#page-13-0) presents some ablation studies; Section [11](#page-15-0) discusses the broader impact; and finally, Section [12](#page-15-1) presents ethical considerations.

#### <span id="page-11-0"></span>6. Theoretical Analysis

We present the most relevant parts of the referred work [\[43,](#page-9-9) Section 2.1-2.2]. Consider that the diffusion takes place over the finite interval [0, 1] and let  $\mu$  be the desired sample distribution, such that  $Z_1 \sim \mu$ . Assume  $\mu$  is absolutely continuous with respect to the standard Gaussian, denoted by  $\gamma_d$ , and define the Radon-Nikodym derivative  $f = d\mu/d\gamma_d$ . Then the optimal control, defined in the literature as the Föllmer drift and expressed as

$$
u^*(\mathbf{z},t) = \nabla \log Q_{1-t}(f)
$$
  
=  $\nabla \log \frac{1}{(2\pi(1-t))^{d/2}} \int f(y) \exp \left(-\frac{1}{2(1-t)} ||\mathbf{z} - y||^2\right) dy$ 

would be such that if  $V(Z_t) = u^*(\mathbf{z}, t)$  in Eq. [\(8\)](#page-3-6), then this drift would minimize the cost-to-go function:

$$
J^u(\mathbf{z},t) := \mathbb{E}\left[\frac{1}{2}\int_t^1 \|u_s\|^2 ds - \log f(Z_1^u) | Z_t^u = \mathbf{z}\right].
$$

Equivalently, such a control is the one that, among all such transportation that maps from  $\gamma_d$  to  $\mu$ , minimizes  $\int_0^1 \|u_s\|^2 ds$  [\[14,](#page-8-13) [24\]](#page-8-14).

The structure of this process presents the opportunity for accurately performing diffusion, enforcing  $Z_1^u \sim \mu$ , while simultaneously pursuing additional criteria. Specifically:

1. Immediately we recognize that a nontrivial transportation problem implies the existence of a set (*i.e*., a nonunique solution to the constraint satisfaction problem) of possible drifts such that the final distribution is  $\mu$ . We can consider maximizing representativeness as an alternative cost criterion to  $\int ||u_s||^2 ds$ . To present the criteria in a sensible way, given that the training is conducted on a minimum across mini-batches, we can instead aim to maximize a bottom quantile, by the costto-go functional,

$$
J_r(\mathbf{z},t) = \int_t^1 Q_{\tilde{q},w \sim \mu} \left[ \sigma \left( Z_t, w \right) \right] ds,
$$

where  $\tilde{q}$  is the quantile percentage, *e.g.* 0.02 (for instance, if a mini-batch of fifty samples were given, this would be the minimum).

2. Next, notice that with dataset distillation, the small sample size is significant, which suggests that we can consider the aggregate in a particle framework, where for  $i = 1, ..., N_D$ , we have,

$$
dZ_t^{u,(i)} = u(Z_t^{u,(i)}, t)dt + dW_t, t \in [0,1]; Z_0^{u,(i)} = \mathbf{z}_0
$$

presenting an additional degree of freedom, which we take advantage of by encouraging diversity, *i.e*., minimizing

$$
J_d(\mathbf{z}, 1) = \max_{i, j=1, ..., N_D} \sigma\left(Z_1^{u,(i)}, Z_1^{u,(j)}\right).
$$

Since generation accuracy and representativeness are criteria for individual particles, maximizing diversity across particles can be considered as optimizing with respect to the additional degree of freedom introduced by having multiple particles.

Thus, we can see that it presents the opportunity to consider generative diffusion as a bi-level stochastic control problem.

A brief note on convergence guarantees for Eq. [\(7\)](#page-3-3) presented in the main paper. A straightforward extension of [\[9\]](#page-8-15) to three layers (similar to the extension from bi-level to trilevel convex optimization in [\[38\]](#page-9-15)) yields convergence guarantees in expectation to a stationary point for all objectives. It is important to note that in the case of nonconvex objectives, the asymptotic point will satisfy a fairly weak condition. Specifically, it may not be stationary for the top objective, as the lower levels are not necessarily at global minimizers. This is, however, the best that can be ensured with stochastic gradient based methods and similar.

## <span id="page-11-1"></span>7. Related Works

## 7.1. Dataset Distillation

Dataset distillation (DD) aims to condense the information of large-scale datasets into small amounts of synthetic images with close training performance [\[5,](#page-8-4) [21,](#page-8-5) [47,](#page-9-0) [57\]](#page-10-0). The informative images are also useful for tasks like continual learning [\[16,](#page-8-16) [21\]](#page-8-5), federated learning [\[25,](#page-8-17) [51\]](#page-9-16) and neural architecture search [\[40\]](#page-9-17). Previous DD works can be roughly divided into bi-level optimization and training metric matching methods. Bi-level optimization methods incorporate meta learning into the surrogate image update [\[7,](#page-8-18) [27,](#page-8-19) [28,](#page-9-18) [30,](#page-9-1) [31,](#page-9-2) [59\]](#page-10-3). In comparison, metric matching methods optimize the synthetic images by matching the training gradients  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$  $[21, 23, 26, 44, 54, 57]$ , feature distribution [\[35,](#page-9-19) [45,](#page-9-20) [56,](#page-10-2) [58\]](#page-10-4), predicted logits [\[46\]](#page-9-21) or training trajectories [\[3,](#page-8-8) [11,](#page-8-21) [49\]](#page-9-22) with original images.

<span id="page-12-2"></span>Image /page/12/Figure/0 description: This is a diagram illustrating a machine learning process. On the left, a sequence of images of dogs is fed into an 'Encoder E'. The output of the encoder is represented by five gray circles, which are then subjected to '+ noise', resulting in five speckled circles. These are then processed by 'DiT Blocks', followed by five blue plus signs. On the right, a dashed box contains a scatter plot. Gray circles represent 'real samples', and blue plus signs represent 'memorized samples'. A diagonal line connects a gray circle with a dashed circle outline to a blue plus sign, with labels \"ℒr\" and \"ℒd\" indicating distances along this line.

Figure 7. The training pipeline of the proposed minimax diffusion fine-tuning. The DiT blocks predict the added noise and original embeddings (dark-blue crossings). Then the parameters are updated with the simple diffusion objective and the minimax objectives. The minimax objectives (the right part) enforce the predicted embedding to be close to the farthest real sample and be far away from the closest predicted embedding of adjacent iterations.

#### 7.2. Data Generation with Diffusion

The significantly improved image quality and sample diversity by diffusion models opens up new possibilities for data generation [\[8,](#page-8-11) [20,](#page-8-9) [22,](#page-8-10) [32\]](#page-9-5). Through prompt engineering [\[12,](#page-8-22) [19,](#page-8-23) [36\]](#page-9-23), latent interpolation [\[60\]](#page-10-5) and classifier-free guidance  $[1, 60]$  $[1, 60]$  $[1, 60]$ , the diversity-improved synthetic images are useful to serve as augmentation or expansion for the original samples. The generated images also contribute to zero-shot image classification tasks [\[39\]](#page-9-24). However, these works mainly focus on recovering the original distribution with equal or much larger amounts of data. In contrast, we intend to distill the rich data information into small surrogate datasets. Moreover, prompt engineering usually requires special designs according to different data classes, while our proposed method saves extra effort. As far as we have investigated, there are no previous attempts to incorporate generative diffusion techniques into the dataset distillation task. In addition to diffusion models, there are also some previous works considering the diversity issue for Generative Adversarial Networks (GANs) [\[2,](#page-8-25) [17,](#page-8-26) [29,](#page-9-25) [52\]](#page-9-26). However, the improvement in diversity is not reflected in downstream tasks. In this work, we seek to enhance both representativeness and diversity for constructing a small surrogate dataset with similar training performance compared with original large-scale ones.

## <span id="page-12-0"></span>8. Method Pipeline

We demonstrate the pipeline of the proposed minimax finetuning method in Fig. [7.](#page-12-2) The real images are first passed through the encoder  $E$  to obtain the original embeddings

<span id="page-12-3"></span>Table 6. The training epoch number on different IPC settings for distilled dataset validation.

| IPC    | 10   | 20   | 50   | 70   | 100  |
|--------|------|------|------|------|------|
| Epochs | 2000 | 1500 | 1500 | 1000 | 1000 |

z. Random noise  $\epsilon$  is then added to the embeddings by the diffusion process. The DiT blocks then predict the added noise, with which the predicted original embeddings  $\hat{z}$  (dark-blue crossings in Fig. [7\)](#page-12-2) are also able to be calculated. We maintain two auxiliary memories  $M$  (grey dots) and  $D$  (light-blue crossings) to store the encountered real embeddings and predicted embeddings at adjacent iterations, respectively. The denoised embeddings of the current iteration are pushed away from the most similar predicted embedding and are pulled close to the least similar real embedding. The DiT blocks are optimized with the proposed minimax criteria and the simple diffusion training loss  $\mathcal{L}_{simple}$  as in Eq. [\(1\)](#page-2-4).

At the inference stage, given a random noise together with a specified class label, the DiT network predicts the noise that requires to be subtracted. Then the Decoder D recovers the images from the denoised embeddings.

## <span id="page-12-1"></span>9. More Implementation Details

We conduct experiments on three commonly adopted network architectures in the area of DD, including:

- 1. ConvNet-6 is a 6-layer convolutional network. In previous DD works where small-resolution images are distilled, the most popular network is ConvNet-3 [\[21,](#page-8-5) [26,](#page-8-6) [54\]](#page-9-4). We extend an extra 3 layers for full-sized  $256\times256$ ImageNet data. The network contains 128 feature channels in each layer, and instance normalization is adopted.
- 2. ResNetAP-10 is a 10-layer ResNet [\[18\]](#page-8-2), where the strided convolution is replaced by average pooling for downsampling.
- 3. ResNet-18 is a 18-layer ResNet [\[18\]](#page-8-2) with instance normalization (IN). As the IN version performs better than batch normalization under our protocol, we uniformly adopt IN for the experiments.

For diffusion fine-tuning, an Adam optimizer is adopted with the learning rate set as 0.001, which is consistent with the original Difffit setting [\[50\]](#page-9-7). We set the mini-batch size as 8 mainly due to the GPU memory limitation. The employed augmentations during the fine-tuning stage include random resize-crop and random flip.

For the validation training, we adopt the same protocol as in [\[21\]](#page-8-5). Specifically, a learning rate of 0.01 for an Adam optimizer is adopted. The training epoch setting is presented in Tab. [6.](#page-12-3) The reduced training epochs also partly explain the reason why the performance gap between the IPC set-

<span id="page-13-1"></span>

| IPC (Ratio) | Test Model    | Random           | Herding $[48]$   | IDC-1 $[21]$     | Ours             | Full             |
|-------------|---------------|------------------|------------------|------------------|------------------|------------------|
| $10(0.8\%)$ | ConvNet-6     | $17.0_{+0.3}$    | $17.2_{\pm 0.3}$ | $24.3_{+0.5}$    | $22.3_{+0.5}$    | $79.9_{+0.4}$    |
|             | ResNetAP-10   | $19.1_{\pm 0.4}$ | $19.8_{\pm 0.3}$ | $25.7_{+0.1}$    | $24.8_{\pm 0.2}$ | $80.3_{+0.2}$    |
|             | $ResNet-18$   | $17.5_{\pm 0.5}$ | $16.1_{\pm 0.2}$ | $25.1_{\pm 0.2}$ | $22.5_{\pm 0.3}$ | $81.8_{\pm 0.7}$ |
| $20(1.6\%)$ | ConvNet-6     | $24.8_{\pm 0.2}$ | $24.3_{\pm 0.4}$ | $28.8_{+0.3}$    | $29.3_{+0.4}$    | $79.9_{+0.4}$    |
|             | $ResNetAP-10$ | $26.7_{\pm 0.5}$ | $27.6_{\pm 0.1}$ | $29.9_{\pm 0.2}$ | $32.3_{+0.1}$    | $80.3_{\pm 0.2}$ |
|             | ResNet-18     | $25.5_{+0.3}$    | $24.7_{\pm 0.1}$ | $30.2_{+0.2}$    | $31.2_{\pm 0.1}$ | $81.8_{\pm 0.7}$ |

Table 7. Performance comparison on ImageNet-100. The best results are marked as bold.

<span id="page-13-3"></span>Figure 8. Visualization of images generated by the same model with different denoising steps. For each column, the generated images are based on the same random seed.

<span id="page-13-2"></span>Table 8. The influence of diffusion denoising step number on the generation time of each image and the corresponding validation performance. Performance evaluated with ResNet-10 on Image-Woof. The best results are marked as bold.

|  | IPC | Time (s)           | Denoising Step     |                    |     |
|--|-----|--------------------|--------------------|--------------------|-----|
|  |     |                    | 50                 | 100                | 250 |
|  |     | 0.8                | 1.6                | 3.2                |     |
|  | 10  | 39.2<br>1.3        | 35.7<br>0.7        | <b>39.6</b><br>0.9 |     |
|  | 20  | <b>45.8</b><br>0.5 | 44.5<br>0.6        | 43.7<br>0.7        |     |
|  | 50  | 56.3<br>1.0        | <b>58.4</b><br>0.5 | 55.8<br>0.5        |     |
|  | 70  | 58.3<br>0.2        | <b>59.6</b><br>1.1 | 58.9<br>1.4        |     |
|  | 100 | <b>64.5</b><br>0.2 | 63.3<br>0.7        | 62.8<br>0.6        |     |

tings of 50 and 70 is relatively small. The adopted data augmentations include random resize-crop and CutMix.

## <span id="page-13-0"></span>10. More Analysis and Discussion

# 10.1. Experiments to ImageNet-100

In addition to the 10-class ImageNet subsets and full ImageNet-1K, we also conduct experiments on ImageNet-100, and the results are shown in Tab. [7.](#page-13-1) The validation protocol follows that in IDC [\[21\]](#page-8-5). Due to the limitation of computational resources, here we directly employ the official distilled images of IDC-1 [\[21\]](#page-8-5) for evaluation. The original resolution is  $224 \times 224$ , and we resize the images to  $256 \times 256$  for fair comparison. Under the IPC setting of 10, IDC-1 achieves the best performance. Yet when the IPC increases, the performance gap between the distilled images of IDC-1 and randomly selected original images is smaller. Comparatively, our proposed minimax diffusion method consistently provides a stable performance improvement over original images across different IPC settings. *It is worth noting that for IDC-1, the distillation process on ImageNet-100 demands hundreds of hours, while the proposed minimax diffusion only requires 10 hours*. The significantly reduced training time offers much more application possibilities for the dataset distillation techniques.

## 10.2. Diffusion Denoising Step

In our experiments, we set the diffusion denoising step number as 50. We evaluate its influence on the validation performance in Tab. [8.](#page-13-2) There are no fixed patterns for achieving better performance across all the IPCs. Additionally, we compare the generated images under different step settings in Fig. [8.](#page-13-3) For DiT [\[33\]](#page-9-8), the denoising process is conducted in the embedding space. Therefore, it is reasonable that with different steps the generated images are variant in the pixel

<span id="page-14-0"></span>Image /page/14/Figure/0 description: This image contains four subfigures labeled (a), (b), (c), and (d), each displaying a line graph illustrating validation accuracy (%) against different parameters. All subfigures share a common y-axis labeled "Validation Accuracy (%)" ranging from 50 to 85. Subfigure (a) plots validation accuracy against "Training Epoch" from 0 to 12. Subfigure (b) plots validation accuracy against "Representativeness Weight \(\lambda\_r\)" with values ranging from 0.001 to 0.01. Subfigure (c) plots validation accuracy against "Diversity Weight \(\lambda\_d\)" with values ranging from 0.002 to 0.03. Subfigure (d) plots validation accuracy against "Memory Size \(N\_M\)" with values ranging from 16 to 1024. Each subfigure shows five lines representing different configurations: IPC10 (green), IPC20 (brown), IPC50 (blue), IPC70 (orange), and IPC100 (pink). Red stars are present on the IPC100 line in subfigures (a), (b), and (c), indicating peak performance at specific points. Shaded regions around the lines represent confidence intervals.

Figure 9. Hyper-parameter analysis on (a) the training epochs; (b) the representativeness weight  $\lambda_r$ ; (c) the diversity weight  $\lambda_d$ ; (d) the memory size  $N_M$ . The results are obtained with ResNetAP-10 on ImageIDC. The dashed line indicates the value adopted in this work.

<span id="page-14-2"></span>Image /page/14/Picture/2 description: The image displays a grid of dog photos, organized by epoch. The left side of the grid features golden retrievers, while the right side showcases Samoyeds. The rows are labeled 'Epoch 4', 'Epoch 8', and 'Epoch 12' from top to bottom. Each epoch row contains multiple images of the respective dog breeds, showing them in various poses and settings, including outdoor scenes with snow, grass, and water, as well as indoor shots.

Figure 10. Visualization of images generated by models after different epochs of training. For each column, the images are generated based on the same random noise.

space. It can be observed that under all steps, the model generates high-quality images with sufficient diversity. Taking the calculation time into consideration, we simply select 50 steps in our experiments.

## 10.3. Parameter Analysis on ImageIDC

We extensively demonstrate the parameter analysis on ImageIDC to illustrate the robustness of the hyper-parameters. Fig. [9a](#page-14-0) shows the performance curve along the training epochs. As the training process starts, the representativeness constraint quickly improves the accuracy of small IPCs. Further training enhances the diversity, where the performance on large and small IPCs shows different trends. Generally, the generated images achieve the best performance at the 8th epoch, which is consistent with the ImageWoof experiments.

Compared with the results on ImageWoof, further enlarging the representativeness weight  $\lambda_r$  improves the performance on small IPCs, as illustrated in Fig. [9b.](#page-14-0) In comparison, increasing diversity causes a drastic performance drop in Fig. [9b.](#page-14-0) Although the default settings remain relatively better choices, the balance point between representa<span id="page-14-1"></span>Table 9. The dataset expansion results of the 100-IPC generated images on ImageWoof.

| Test Model  | Original         | Original + 100-IPC                 |
|-------------|------------------|------------------------------------|
| ConvNet-6   | 86.4 $_{\pm0.2}$ | <b>87.0<math>_{\pm0.6}</math></b>  |
| ResNetAP-10 | 87.5 $_{\pm0.5}$ | <b>89.3<math>_{\pm0.6}</math></b>  |
| ResNet-18   | 89.3 $_{\pm1.2}$ | <b>90.1<math>_{\pm 0.3}</math></b> |

tiveness and diversity is worthy of further exploration. The memory size  $N_M$  merely has a mild influence on the performance, which aligns with that of ImageWoof.

## 10.4. Extension to Dataset Expansion

In addition to the standard dataset distillation task, where a small surrogate dataset is generated to replace the original one, we also evaluate the capability of the generated images as an expanded dataset. We add the generated 100-IPC surrogate dataset to the original ImageWoof (approximately 1,300 images per class) and conduct the validation in Tab. [9.](#page-14-1) As can be observed, although the extra images only take up

<span id="page-15-2"></span>Table 10. The averaged generation quality evaluation of 10 classes each with 100 images in ImageWoof.

| Method               | FID   | Precision (%) | Recall (%) | Coverage (%) |
|----------------------|-------|---------------|------------|--------------|
| DM [56]              | 208.6 | 22.1          | 23.8       | 5.8          |
| DiT [33]             | 81.4  | 92.8          | 38.9       | 24.1         |
| DiT+ $\mathcal{L}_r$ | 85.4  | 93.2          | 38.1       | 24.6         |
| DiT+ $\mathcal{L}_d$ | 81.1  | 90.4          | 46.8       | 28.3         |
| Ours full            | 81.5  | 92.4          | 45.3       | 28.6         |

a small ratio compared with the original data, a considerable performance improvement is still achieved. *The results support that the proposed minimax diffusion can also be explored as a dataset expansion method in future works.*

## 10.5. Generated Samples of Different Epochs

We visualize the images generated by models after different epochs of training in Fig. [10](#page-14-2) to explicitly demonstrate the training effect of the proposed minimax diffusion method. As the training proceeds, the generated images present variation trends from several perspectives. Firstly, the images tend to have more complicated backgrounds and environments, such as more realistic water and objects of other categories (*e.g.* human). Secondly, there are more details filled in the images, like the clothes in the first column and the red spots in the sixth. These new facets significantly enhance the diversity of the generated surrogate dataset. Furthermore, through the fine-tuning process, the class-related features are also enhanced. In the ninth and tenth columns, the model at the fourth epoch fails to generate objects with discriminative features. In comparison, the images generated by subsequent models demonstrate substantial improvement regarding the representativeness property.

## 10.6. Generation Quality Evaluation.

We further report quantitative evaluations on the generation quality by adding the proposed minimax criteria in Tab. [10.](#page-15-2) The representativeness and diversity constraints improve the precision and recall of the generated data, respectively. The full method finds a balanced point between these two properties while obtaining the best coverage over the whole distribution. The fine-tuning brings negligible influence on the FID metric. And all the metrics of our proposed method are significantly better than those attained by DM [\[56\]](#page-10-2).

## 10.7. Generated Samples of Different Classes

We present the comparison between the samples selected by Herding [\[48\]](#page-9-11) and those generated by our proposed minimax diffusion method on ImageNet-100 from Fig. [11](#page-16-0) to Fig. [20.](#page-25-0) In most cases, the diffusion model is able to generate realistic images, which cannot easily be told from real samples. Herding also aims to select both representative and diverse

samples. However, the lack of supervision on the semantic level led to the inclusion of noisy samples. For instance, the walking stick class contains images of mantis, which can originally be caused by mislabeling. The proposed minimax diffusion, in comparison, accurately generates images of the corresponding classes, which is also validated by the better performance shown in Tab. [7.](#page-13-1) There are also some failure cases for the diffusion model. The fur texture of hairy animals like Shih-Tzu and langur is unrealistic. The structures of human faces and hands also require further refinement. We treat these defects as exploration directions of future works for both diffusion models and the dataset distillation usage.

# <span id="page-15-0"></span>11. Broader Impacts

The general purpose of dataset distillation is to reduce the demands of storage and computational resources for training deep neural networks. The requirement of saving resource consumption is even tenser at the age of foundation models. Dataset distillation aims to push forward the process of environmental contributions. From this perspective, the proposed minimax diffusion method significantly reduces the requirement resources for the distillation process itself. We hope that through this work, the computer vision society can put more attention on practical dataset distillation methods, which are able to promote the sustainable development of society.

## <span id="page-15-1"></span>12. Ethical Considerations

There are no direct ethical issues attached to this work. We employ the publicly available ImageNet dataset for experiments. In future works, we will also be devoted to considering the generation bias and diversity during constructing a small surrogate dataset.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: This image is a grid comparing animal images generated by two different methods: "Herding" (left) and "Ours" (right). The grid is organized by animal type, with rows labeled "robin", "Gila monster", "hognose snake", "garter snake", "green mamba", "garden spider", "lorikeet", "goose", "rock crab", and "fiddler crab". Each row contains five images for each method, showcasing different examples of the specified animal. The "Herding" column shows four images for each animal, while the "Ours" column shows five images for each animal. The overall layout is a comparison of image generation quality or diversity between the two methods.

Figure 11. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 0-9. The class names are marked at the left of each row.

Image /page/17/Picture/0 description: This is a grid of images comparing results from a method called "Herding" (left column) and "Ours" (right column) for various animal breeds. The breeds listed are American lobster, blue heron, American coot, Chihuahua, Shih Tzu, papillon, toy terrier, Walker hound, English foxhound, and borzoi. Each row displays multiple images of the specified breed, with the left side showing images from "Herding" and the right side showing images from "Ours". The images appear to be generated or selected samples related to these breeds.

Figure 12. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 10-19. The class names are marked at the left of each row.

Image /page/18/Picture/0 description: The image is a grid comparing dog breeds generated by two different methods, labeled "Herding" (left) and "Ours" (right). Each row represents a different dog breed: Saluki, Staffordshire terrier, Chesapeake retriever, vizsla, kuvasz, komondor, Rottweiler, Doberman, boxer, and Great Dane. Each column within the "Herding" and "Ours" sections displays a different sample image of the respective dog breed. The "Herding" section has 5 columns of images for each breed, and the "Ours" section also has 5 columns of images for each breed. The figure is titled "Figure 13. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for" followed by a partial description of the breeds.

Figure 13. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 20-29. The class names are marked at the left of each row.

|                  | Herding                   |                           |                           |                           |                           | Ours                      |                           |                           |                           |                            |
|------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|----------------------------|
| standard poodle  | Image: standard poodle 1  | Image: standard poodle 2  | Image: standard poodle 3  | Image: standard poodle 4  | Image: standard poodle 5  | Image: standard poodle 6  | Image: standard poodle 7  | Image: standard poodle 8  | Image: standard poodle 9  | Image: standard poodle 10  |
| Mexican hairless | Image: Mexican hairless 1 | Image: Mexican hairless 2 | Image: Mexican hairless 3 | Image: Mexican hairless 4 | Image: Mexican hairless 5 | Image: Mexican hairless 6 | Image: Mexican hairless 7 | Image: Mexican hairless 8 | Image: Mexican hairless 9 | Image: Mexican hairless 10 |
| coyote           | Image: coyote 1           | Image: coyote 2           | Image: coyote 3           | Image: coyote 4           | Image: coyote 5           | Image: coyote 6           | Image: coyote 7           | Image: coyote 8           | Image: coyote 9           | Image: coyote 10           |
| hyena dog        | Image: hyena dog 1        | Image: hyena dog 2        | Image: hyena dog 3        | Image: hyena dog 4        | Image: hyena dog 5        | Image: hyena dog 6        | Image: hyena dog 7        | Image: hyena dog 8        | Image: hyena dog 9        | Image: hyena dog 10        |
| red fox          | Image: red fox 1          | Image: red fox 2          | Image: red fox 3          | Image: red fox 4          | Image: red fox 5          | Image: red fox 6          | Image: red fox 7          | Image: red fox 8          | Image: red fox 9          | Image: red fox 10          |
| tabby            | Image: tabby 1            | Image: tabby 2            | Image: tabby 3            | Image: tabby 4            | Image: tabby 5            | Image: tabby 6            | Image: tabby 7            | Image: tabby 8            | Image: tabby 9            | Image: tabby 10            |
| meerkat          | Image: meerkat 1          | Image: meerkat 2          | Image: meerkat 3          | Image: meerkat 4          | Image: meerkat 5          | Image: meerkat 6          | Image: meerkat 7          | Image: meerkat 8          | Image: meerkat 9          | Image: meerkat 10          |
| dung beetle      | Image: dung beetle 1      | Image: dung beetle 2      | Image: dung beetle 3      | Image: dung beetle 4      | Image: dung beetle 5      | Image: dung beetle 6      | Image: dung beetle 7      | Image: dung beetle 8      | Image: dung beetle 9      | Image: dung beetle 10      |
| walking stick    | Image: walking stick 1    | Image: walking stick 2    | Image: walking stick 3    | Image: walking stick 4    | Image: walking stick 5    | Image: walking stick 6    | Image: walking stick 7    | Image: walking stick 8    | Image: walking stick 9    | Image: walking stick 10    |
| leaf-hopper      | Image: leaf-hopper 1      | Image: leaf-hopper 2      | Image: leaf-hopper 3      | Image: leaf-hopper 4      | Image: leaf-hopper 5      | Image: leaf-hopper 6      | Image: leaf-hopper 7      | Image: leaf-hopper 8      | Image: leaf-hopper 9      | Image: leaf-hopper 10      |
|                  | Herding                   |                           |                           |                           |                           | Ours                      |                           |                           |                           |                            |
| hare             | Image: hare 1             | Image: hare 2             | Image: hare 3             | Image: hare 4             | Image: hare 5             | Image: hare 6             | Image: hare 7             | Image: hare 8             | Image: hare 9             | Image: hare 10             |
| wild<br>boar     | Image: wild boar 1        | Image: wild boar 2        | Image: wild boar 3        | Image: wild boar 4        | Image: wild boar 5        | Image: wild boar 6        | Image: wild boar 7        | Image: wild boar 8        | Image: wild boar 9        | Image: wild boar 10        |
| gibbon           | Image: gibbon 1           | Image: gibbon 2           | Image: gibbon 3           | Image: gibbon 4           | Image: gibbon 5           | Image: gibbon 6           | Image: gibbon 7           | Image: gibbon 8           | Image: gibbon 9           | Image: gibbon 10           |
| langur           | Image: langur 1           | Image: langur 2           | Image: langur 3           | Image: langur 4           | Image: langur 5           | Image: langur 6           | Image: langur 7           | Image: langur 8           | Image: langur 9           | Image: langur 10           |
| ambulance        | Image: ambulance 1        | Image: ambulance 2        | Image: ambulance 3        | Image: ambulance 4        | Image: ambulance 5        | Image: ambulance 6        | Image: ambulance 7        | Image: ambulance 8        | Image: ambulance 9        | Image: ambulance 10        |
| bannister        | Image: bannister 1        | Image: bannister 2        | Image: bannister 3        | Image: bannister 4        | Image: bannister 5        | Image: bannister 6        | Image: bannister 7        | Image: bannister 8        | Image: bannister 9        | Image: bannister 10        |
| bassinet         | Image: bassinet 1         | Image: bassinet 2         | Image: bassinet 3         | Image: bassinet 4         | Image: bassinet 5         | Image: bassinet 6         | Image: bassinet 7         | Image: bassinet 8         | Image: bassinet 9         | Image: bassinet 10         |
| boat-<br>house   | Image: boat-house 1       | Image: boat-house 2       | Image: boat-house 3       | Image: boat-house 4       | Image: boat-house 5       | Image: boat-house 6       | Image: boat-house 7       | Image: boat-house 8       | Image: boat-house 9       | Image: boat-house 10       |
| bonnet           | Image: bonnet 1           | Image: bonnet 2           | Image: bonnet 3           | Image: bonnet 4           | Image: bonnet 5           | Image: bonnet 6           | Image: bonnet 7           | Image: bonnet 8           | Image: bonnet 9           | Image: bonnet 10           |
| bottlecap        | Image: bottlecap 1        | Image: bottlecap 2        | Image: bottlecap 3        | Image: bottlecap 4        | Image: bottlecap 5        | Image: bottlecap 6        | Image: bottlecap 7        | Image: bottlecap 8        | Image: bottlecap 9        | Image: bottlecap 10        |

Figure 14. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 30-39. The class names are marked at the left of each row.

Figure 15. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 40-49. The class names are marked at the left of each row.

Image /page/21/Picture/0 description: The image is a comparison of image generation methods, specifically comparing "Herding" (left) with "Ours" (right). It is organized into rows representing different object categories: car wheel, chime, cinema, cocktail shaker, computer keyboard, Dutch oven, football helmet, gasmask, hard disc, and harmonica. Each category has four images from the "Herding" method and four images from the "Ours" method, showcasing generated samples for each category. The figure is titled "Figure 16. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right)."

Figure 16. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 50-59. The class names are marked at the left of each row.

Image /page/22/Picture/0 description: The image is a comparison of image generation results between two methods, labeled "Herding" (left) and "Ours" (right). Each row corresponds to a different object category: honeycomb, iron, jean, lampshade, laptop, milk can, mixing bowl, modem, moped, and mortarboard. Each category has five images from the "Herding" method and five images from the "Ours" method, displayed side-by-side. The "Herding" column shows various examples of each object, while the "Ours" column shows generated images of the same objects. The bottom of the image contains the text "Figure 17. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for" followed by a partial word.

Figure 17. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 60-69. The class names are marked at the left of each row.

Image /page/23/Picture/0 description: This image is a grid comparing samples selected by 'Herding' (left) and 'Ours' (right) for various categories. The categories listed vertically are mousetrap, obelisk, park bench, pedestal, pickup, pirate, purse, reel, rocking chair, and rotisserie. Each category has five images on the left under 'Herding' and five images on the right under 'Ours'. The images show diverse examples within each category, such as different types of mousetraps, obelisks, park benches, pedestals, pickup trucks, pirate ships, purses, fishing reels, rocking chairs, and rotisseries.

Figure 18. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 70-79. The class names are marked at the left of each row.

Image /page/24/Picture/0 description: This is a figure comparing image generation results from two methods: Herding (left) and Ours (right). The figure is organized into rows, each representing a category of object or scene: safety pin, sarong, ski mask, slide rule, stretcher, theater curtain, throne, tile roof, tripod, and tub. Each row contains five images generated by Herding on the left and five images generated by Ours on the right. The images show variations in quality, style, and accuracy between the two methods for each category. For example, the safety pin row shows different interpretations of safety pins, while the throne row displays various ornate chairs.

Figure 19. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 80-89. The class names are marked at the left of each row.

<span id="page-25-0"></span>Image /page/25/Picture/0 description: The image is a grid comparing samples selected by "Herding" (left) and generated by the proposed minimax diffusion method (right). The grid is organized into rows representing different categories: vaccum, window screen, wing, head cabbage, cauliflower, pineapple, carbonara, chocolate sauce, gyromitra, and stinkhorn. Each row contains five images, with the first four images on the left representing "Herding" samples and the fifth image on the right representing an "Ours" sample. The categories are labeled on the left side of the grid. The top of the grid has column headers "Herding" and "Ours" separated by a dashed vertical line.

Figure 20. Comparison between samples selected by Herding (left) and generated by the proposed minimax diffusion method (right) for ImageNet-100 classes 90-99. The class names are marked at the left of each row.