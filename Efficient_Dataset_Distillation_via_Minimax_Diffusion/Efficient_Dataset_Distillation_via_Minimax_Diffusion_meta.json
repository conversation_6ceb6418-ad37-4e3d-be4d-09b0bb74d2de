{"table_of_contents": [{"title": "Efficient Dataset Distillation via Minimax Diffusion", "heading_level": null, "page_id": 0, "polygon": [[138.75, 105.75], [455.25, 105.75], [455.25, 119.109375], [138.75, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 243.0], [191.25, 243.0], [191.25, 253.880859375], [144.75, 253.880859375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 588.75], [127.5, 588.75], [127.5, 599.80078125], [48.75, 599.80078125]]}, {"title": "2. Method", "heading_level": null, "page_id": 1, "polygon": [[307.5, 626.25], [361.5, 626.25], [361.5, 637.3125], [307.5, 637.3125]]}, {"title": "2.1. Problem Definition", "heading_level": null, "page_id": 1, "polygon": [[307.5, 647.25], [417.1640625, 647.25], [417.1640625, 657.80859375], [307.5, 657.80859375]]}, {"title": "2.2. Diffusion for Distillation", "heading_level": null, "page_id": 2, "polygon": [[48.0, 141.0], [183.75, 141.0], [183.75, 151.3037109375], [48.0, 151.3037109375]]}, {"title": "2.3. Minimax Diffusion Criteria", "heading_level": null, "page_id": 2, "polygon": [[48.75, 648.75], [198.75, 648.75], [198.75, 658.96875], [48.75, 658.96875]]}, {"title": "3. Theoretical Analysis", "heading_level": null, "page_id": 3, "polygon": [[48.75, 334.5], [167.25, 334.5], [167.25, 345.33984375], [48.75, 345.33984375]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 408.75], [128.25, 408.75], [128.25, 420.36328125], [48.75, 420.36328125]]}, {"title": "4.1. Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[48.0, 428.25], [180.0, 428.25], [180.0, 439.69921875], [48.0, 439.69921875]]}, {"title": "4.2. Datasets and Evaluation Metric", "heading_level": null, "page_id": 4, "polygon": [[48.75, 576.75], [218.25, 576.75], [218.25, 586.65234375], [48.75, 586.65234375]]}, {"title": "4.3. Comp<PERSON>on with State-of-the-art Methods", "heading_level": null, "page_id": 4, "polygon": [[306.59765625, 467.25], [528.75, 467.25], [528.75, 477.984375], [306.59765625, 477.984375]]}, {"title": "4.4. Ablation Study", "heading_level": null, "page_id": 5, "polygon": [[307.5, 493.5], [399.75, 493.5], [399.75, 503.89453125], [307.5, 503.89453125]]}, {"title": "4.5. Visualization", "heading_level": null, "page_id": 6, "polygon": [[306.896484375, 456.0], [390.0, 456.0], [390.0, 467.15625], [306.896484375, 467.15625]]}, {"title": "4.6. Parameter Analysis", "heading_level": null, "page_id": 7, "polygon": [[48.75, 648.75], [162.0, 648.75], [162.0, 659.35546875], [48.75, 659.35546875]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 525.0], [378.0, 525.0], [378.0, 536.765625], [307.5, 536.765625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.91796875], [48.75, 83.91796875]]}, {"title": "Efficient Dataset Distillation via Minimax Diffusion", "heading_level": null, "page_id": 11, "polygon": [[138.0, 70.5], [455.25, 70.5], [455.25, 83.96630859375], [138.0, 83.96630859375]]}, {"title": "Supplementary Material", "heading_level": null, "page_id": 11, "polygon": [[227.25, 96.75], [367.857421875, 96.75], [367.857421875, 109.44140625], [227.25, 109.44140625]]}, {"title": "6. Theoretical Analysis", "heading_level": null, "page_id": 11, "polygon": [[48.75, 221.25], [167.25, 221.25], [167.25, 231.837890625], [48.75, 231.837890625]]}, {"title": "7. Related Works", "heading_level": null, "page_id": 11, "polygon": [[307.5, 509.25], [397.44140625, 509.25], [397.44140625, 520.5234375], [307.5, 520.5234375]]}, {"title": "7.1. Dataset Distillation", "heading_level": null, "page_id": 11, "polygon": [[307.5, 528.75], [418.5, 528.75], [418.5, 539.859375], [307.5, 539.859375]]}, {"title": "7.2. Data Generation with Diffusion", "heading_level": null, "page_id": 12, "polygon": [[48.75, 342.75], [218.25, 342.75], [218.25, 353.07421875], [48.75, 353.07421875]]}, {"title": "8. <PERSON>", "heading_level": null, "page_id": 12, "polygon": [[48.0, 659.25], [147.0, 659.25], [147.0, 670.5703125], [48.0, 670.5703125]]}, {"title": "9. More Implementation Details", "heading_level": null, "page_id": 12, "polygon": [[307.5, 380.25], [471.75, 380.25], [471.75, 391.166015625], [307.5, 391.166015625]]}, {"title": "10. More Analysis and Discussion", "heading_level": null, "page_id": 13, "polygon": [[48.75, 629.25], [222.0, 629.25], [222.0, 640.40625], [48.75, 640.40625]]}, {"title": "10.1. Experiments to ImageNet-100", "heading_level": null, "page_id": 13, "polygon": [[48.75, 648.0], [216.0, 648.0], [216.0, 659.7421875], [48.75, 659.7421875]]}, {"title": "10.2. Diffusion Denoising Step", "heading_level": null, "page_id": 13, "polygon": [[307.5, 599.25], [450.75, 599.25], [450.75, 610.2421875], [307.5, 610.2421875]]}, {"title": "10.3. Parameter Analysis on ImageIDC", "heading_level": null, "page_id": 14, "polygon": [[48.75, 504.75], [234.0, 504.75], [234.0, 515.8828125], [48.75, 515.8828125]]}, {"title": "10.4. Extension to Dataset Expansion", "heading_level": null, "page_id": 14, "polygon": [[307.5, 612.0], [482.25, 612.0], [482.25, 622.23046875], [307.5, 622.23046875]]}, {"title": "10.5. Generated Samples of Different Epochs", "heading_level": null, "page_id": 15, "polygon": [[48.75, 258.75], [261.0, 258.75], [261.0, 270.31640625], [48.75, 270.31640625]]}, {"title": "10.6. Generation Quality Evaluation.", "heading_level": null, "page_id": 15, "polygon": [[48.75, 489.75], [222.75, 489.75], [222.75, 501.1875], [48.75, 501.1875]]}, {"title": "10.7. Generated Samples of Different Classes", "heading_level": null, "page_id": 15, "polygon": [[48.75, 624.75], [261.0, 624.75], [261.0, 635.765625], [48.75, 635.765625]]}, {"title": "11. <PERSON><PERSON>s", "heading_level": null, "page_id": 15, "polygon": [[307.5, 240.345703125], [414.0, 239.25], [414.0, 251.173828125], [307.5, 251.25]]}, {"title": "12. Ethical Considerations", "heading_level": null, "page_id": 15, "polygon": [[307.5, 414.75], [444.0, 414.75], [444.0, 426.1640625], [307.5, 426.1640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 98], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7344, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 87], ["Text", 5], ["ListItem", 4], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1284, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 672], ["Line", 97], ["TextInlineMath", 5], ["Reference", 5], ["Text", 4], ["Equation", 3], ["SectionHeader", 2], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["Line", 88], ["Text", 10], ["Reference", 7], ["Equation", 6], ["TextInlineMath", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4422, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1015], ["TableCell", 165], ["Line", 81], ["Text", 7], ["SectionHeader", 4], ["Reference", 3], ["Table", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 17140, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["TableCell", 114], ["Line", 113], ["Text", 5], ["Reference", 4], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 12466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["TableCell", 118], ["Line", 71], ["Text", 6], ["Reference", 3], ["Picture", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 140], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1124, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 106], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 105], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 20], ["ListItem", 6], ["Reference", 6], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 87], ["Text", 7], ["TextInlineMath", 6], ["SectionHeader", 5], ["Equation", 5], ["ListItem", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 94], ["TableCell", 20], ["Text", 7], ["Reference", 4], ["SectionHeader", 3], ["ListItem", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 427], ["TableCell", 126], ["Line", 63], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["Table", 2], ["Caption", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 1893, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 108], ["TableCell", 18], ["Text", 5], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Figure", 1], ["Picture", 1], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4281, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 80], ["TableCell", 60], ["Text", 7], ["SectionHeader", 5], ["Reference", 3], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1427, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 21], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 724, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 22], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 34], ["Line", 17], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 190], ["Span", 40], ["Line", 20], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6902, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 179], ["Span", 32], ["Line", 16], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9628, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 41], ["Line", 21], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 32], ["Line", 18], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 33], ["Line", 16], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 35], ["Line", 17], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 18], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 704, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Efficient_Dataset_Distillation_via_Minimax_Diffusion"}