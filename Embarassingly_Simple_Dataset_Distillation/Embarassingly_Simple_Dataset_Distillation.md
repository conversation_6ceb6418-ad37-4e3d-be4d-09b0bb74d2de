# Embarrassingly Simple Dataset Distillation

Yunzhen Feng∗‡ <PERSON><PERSON><PERSON><PERSON><PERSON><sup>∗</sup> <PERSON>†

<sup>∗</sup>Center for Data Science, New York University †Courant Institue of Mathematical Sciences, New York University  $^{\ddagger}$ <EMAIL>

# Abstract

Dataset distillation extracts a small set of synthetic training samples from a large dataset with the goal of achieving competitive performance on test data when trained on this sample. In this work, we tackle dataset distillation at its core by treating it directly as a bilevel optimization problem. Re-examining the foundational back-propagation through time method, we study the pronounced variance in the gradients, computational burden, and long-term dependencies. We introduce an improved method: Random Truncated Backpropagation Through Time (RaT-BPTT) to address them. RaT-BPTT incorporates a truncation coupled with a random window, effectively stabilizing the gradients and speeding up the optimization while covering long dependencies. This allows us to establish new state-of-the-art for a variety of standard dataset benchmarks. A deeper dive into the nature of distilled data unveils pronounced intercorrelation. In particular, subsets of distilled datasets tend to exhibit much worse performance than directly distilled smaller datasets of the same size. Leveraging RaT-BPTT, we devise a boosting mechanism that generates distilled datasets that contain subsets with near optimal performance across different data budgets.

<span id="page-0-0"></span>

## 1. Introduction

Learning deep, overparameterized neural networks with stochastic gradient descent, backpropagation and large scale datasets has led to tremendous advances in deep learning. In practice, it is often observed that for a deep learning algorithm to be effective, a vast amount of training samples and numerous training iterations are needed.

In this work, we aim to explore the genuine necessity of vast training data and numerous training steps for achieving high test accuracy. To investigate, we limit the number of samples in the training set to be small (e.g.,  $1, 5$ , or 10 images per class) and the number of training steps to be small (e.g., on the order of 300 steps). This leads us to the concept of optimizing a small synthetic dataset, such that neural networks trained on this dataset perform well on the desired target distribution, a problem known as Dataset Distillation [\(Wang et al., 2018\)](#page-14-0).

This is an instance of a bilevel optimization problem [\(Dempe, 2020\)](#page-11-0) where the output of one optimization problem (in this instance, the learning algorithm trained on the small dataset) is fed into another optimization problem (the generalization error on the target set) which we intend to minimize. In general, this problem is intractable, as the inner loop involves a multi-step computation with a large number of steps. Early works [\(Deng and Russakovsky, 2022;](#page-11-1) [Sucholutsky and Schonlau, 2021;](#page-14-1) [Wang et al., 2018\)](#page-14-0) directly approached this problem via back-propagation through time (BPTT), unrolling the inner loop for a limited number of steps, before hitting an optimization bottleneck that called for alternative techniques. Later works have made steady progress by replacing the inner loop with closed-form differentiable *surrogates*, like the Neural Tangent Kernel [\(Nguyen et al., 2021a,](#page-13-0)[b\)](#page-13-1), Neural Features [\(Zhou et al., 2022\)](#page-14-2) and Gaussian Process [\(Loo et al., 2022\)](#page-12-0). This approach often requires looping thought a diverse pool of randomly initialized models during the optimization to alleviate the mismatch between the surrogate model and the actual one. Moreover, these approaches are limited to MSE loss; they tend to work better on *wider* models, where the surrogate approximation holds better, but give worse performance on the set of frequently used narrower models. Another

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays three diagrams illustrating different backpropagation through time (BPTT) methods: Full BPTT, Truncated BPTT, and Ra-Truncated BPTT. Each diagram includes a plot of loss versus U, with shaded regions indicating the scope of computation. Below the plots, directed graphs show the forward and backward passes through functions f\_theta^0, f\_theta^1, and f\_theta^2, leading to U and an outer product. A legend at the bottom clarifies that solid arrows represent the forward pass and dashed arrows represent the backward pass. The Ra-Truncated BPTT section also presents an alternative computation path with a different shading and arrow color.

Figure 1: Illustration of bilevel optimization of the outer loss when training for 3 steps. We show Full Backpropagation Through Time (BPTT) (left), Truncated BPTT (middle) and our proposed Randomized Truncated BPTT (right) (RaT-BPTT). RaT-BPTT picks a window in the learning trajectory (randomly) and tracks the gradients on the training dataset  $U$  for the chosen window, as opposed to T-BPTT that uses a fixed window, and BPTT that uses the entire trajectory.

Image /page/1/Figure/2 description: This image shows a progression of image generation stages. Stage 1 shows five columns of images. The first column contains five noisy images. The second column contains five images of deer in a natural setting, labeled "IPC 5". The third column contains five noisy images, labeled "+5". Stage 2 shows a similar progression, with the first column containing five noisy images, the second column containing ten images of deer in a natural setting, labeled "IPC 10", and the third column containing five noisy images, labeled "+5". Stage 3 shows the final progression, with the first column containing five noisy images, and the second column containing fifteen images of deer in a natural setting, labeled "IPC 15".

Figure 2: Boosting Dataset Distillation (Boost-DD). We start with 5 randomly initialized images per class, distill the dataset into them (Stage 1) yielding five images per class (IPC5), then add five more random images and distill while reducing the learning rate on the first 5 (Stage 2) to yield IPC10, and so on, resulting in a nested dataset of different IPC. Boosting reduces higher order dependencies in the distilled datasets.

line of works has modified the outer loop objective using *proxy training-metrics* like matching the trajectories of the network [\(Cazenavette et al., 2022\)](#page-11-2) or the gradients during training [\(Zhao and](#page-14-3) [Bilen, 2021a\)](#page-14-3). However, these methods either necessitate the storage of different trajectories or are impeded by subpar performance. These observations lead to the question: Does there exist a simple and direct method for dataset distillation?

In this paper, we refine BPTT to address distinct challenges in dataset distillation, and achieve state-of-the-art performance across a vast majority of the CIFAR10, CIFAR100, CUB and TinyImageNet benchmarks. We start by re-examining BPTT, the go-to method for bi-level optimization problems [\(Finn et al., 2017;](#page-11-3) [Lorraine et al., 2020\)](#page-13-2). Notably, the inner problem of dataset distillation presents unique challenges – the pronounced non-convex nature when training a neural network from scratch on the distilled data. One has to use long unrolling of BPTT to encapsulate the long dependencies inherent in the inner optimization. However, this results in BPTT suffering from slow optimization and huge memory demands, a consequence of backpropagating through all intermediate steps. This is further complicated by considerable instability in meta-gradients, emerging from the multiplication of Hessian matrices during long unrolling. Therefore, the performance is limited.

To address these challenges, we integrate the concepts of randomization and truncation with BPTT, leading to the Random Truncated Backpropagation Through Time (RaT-BPTT) method. The refined approach unrolls within a randomly anchored smaller fixed-size window along the training trajectory and aggregates gradients within that window (see Figure [1](#page-1-0) for a cartoon illustration). The random window design ensures that the RaT-BPTT gradient serves as a random subsample of the full BPTT gradient, covering the entire trajectory, while the truncated window design enhances gradient stability and alleviates memory burden. Consequently, RaT-BPTT provides expedited training and superior performance compared to BPTT.

Overall, our method is *embarrassingly* simple – we show that a careful analysis and modification of backpropagation lead to results exceeding the current state-of-the-art, without resorting to various approximations, a pool of models in the optimization, or additional heuristics. Since our approach does not depend on large-width approximations, it works for any architecture, in particular commonly used narrower models, for which methods that use inner-loop approximations perform less well. Moreover, our method can be seamlessly combined with prior methods on dataset re-parameterization [\(Deng and Russakovsky, 2022\)](#page-11-1), leading to further improvements. To our knowledge, we are the first to introduce truncated backpropagation through time [\(Shaban et al., 2019\)](#page-13-3) to the dataset distillation setting, and to combine it with *random* positioning of the unrolling window.

Having established the strength of our method, we proceed to dissect the structure of the learned datasets to catalyze further progress. In particular we address the observation, already made in prior work (e.g. [Nguyen et al.](#page-13-1) [\(2021b\)](#page-13-1)), that distilled data seems to show a large degree of *intercorrelation*. When training on a subset of distilled data, for instance 10 images per class extracted from a 50-image per class distilled dataset we observe a large degradation in test accuracy: the resulting dataset performs much worse than if it were distilled from scratch; even worse than training on a dataset of random train images of the same size! This property makes distilled data less versatile since for each desired dataset size we need to distill from scratch. To produce datasets that contain high performing subsamples, we propose *Boost-DD*, a boosting algorithm that produces a nested dataset without these higher-order correlations and only marginal performance loss. It works as a plug-and-play for essentially every existing gradient-based distillation algorithm (see Figure [2](#page-1-0) for an illustration). To further our understanding of the learned dataset, we discuss the role of intercorrelation, as well as what information is captured in the distilled data through the lens of hardness metrics.

Overall, our contributions are as follows:

- RaT-BPTT algorithm: We propose RaT-BPTT by integrating truncation and randomization with BPTT, and achieves state of the art performance on various dataset distillation benchmarks. RaT-BPTT can be combined with data parametrization methods, leading to further improvements.
- Boosting: We propse a boosting-approach to dataset distillation (Boost-DD) that generates a modular synthetic dataset that contains nested high-performance subsets for various budgets.

This paper is structured as follows: Section [2](#page-2-0) surveys prior work, Section [3](#page-3-0) delineates and motivates our algorithm, RaT-BPTT, Section [4](#page-5-0) presents experimental results and compares to prior art, and Section [5](#page-7-0) details and evaluates our boosting algorithm. In Section 6 we summarize and discuss bottlenecks to further improvements.

<span id="page-2-0"></span>

# 2. Background and Related Work

Dataset Distillation, introduced by [Wang et al.](#page-14-0) [\(2018\)](#page-14-0), aims to condense a given dataset into a small synthetic version. When neural networks are trained on this distilled version, they achieve good performance on the original distribution. Dataset distillation shares many characteristics with coreset selection [Jubran et al.](#page-12-1) [\(2019\)](#page-12-1), which finds representative samples from the training set to still accurately represent the full dataset on downstream tasks. However, since dataset distillation generates synthetic samples, it is not limited to the set of images and labels given by the dataset and has the benefit of using continuous gradient-based optimization techniques rather than combinatorial methods, providing added flexibility and performance. Both coresets and distilled datasets have found numerous applications including speeding up model-training [Mirzasoleiman et al.](#page-13-4) [\(2020\)](#page-13-4), reducing catastrophic forgetting [Sangermano et al.](#page-13-5) [\(2022\)](#page-13-5); [Zhou et al.](#page-14-2) [\(2022\)](#page-14-2), federated learning [Hu et al.](#page-12-2) [\(2022\)](#page-12-2); [Song et al.](#page-13-6) [\(2022\)](#page-13-6) and neural architecture search [Such et al.](#page-13-7) [\(2020\)](#page-13-7).

Numerous follow up works have proposed clever strategies to improve upon the original direct bilevel optimization(see [Geng et al.](#page-11-4) [\(2023\)](#page-11-4); [Lei and Tao](#page-12-3) [\(2023\)](#page-12-3); [Sachdeva and McAuley](#page-13-8) [\(2023\)](#page-13-8); [Yu](#page-14-4) [et al.](#page-14-4) [\(2023\)](#page-14-4) for recent surveys and [Cui et al.](#page-11-5) [\(2022\)](#page-11-5) for benchmarking). Yet, given the apparent intractability of the core bilevel optimization problem, most works have focused on 1) approximating the function in the inner-loop with more tractable expressions or 2) changing the outer-loop objective.

Inner-loop surrogates: The first innovative works [Nguyen et al.](#page-13-0) [\(2021a,](#page-13-0)[b\)](#page-13-1) tackle inner-loop intractability by approximating the inner network with the Neural Tangent Kernel (NTK) which describes the neural net in the infinite-width limit with suitable initialization [\(Arora et al.](#page-11-6) [\(2019\)](#page-11-6); [Jacot et al.](#page-12-4) [\(2018\)](#page-12-4); [Lee et al.](#page-12-5) [\(2019\)](#page-12-5)) and allows for convex optimization, but scales unfavorably. To alleviate the scaling, random feature approximations have been proposed: [Loo et al.](#page-12-0) [\(2022\)](#page-12-0) leverage a Neural Network Gaussian process (NNGP) to replace the NTK, using MC sampling to approximate the averaged GP. [Zhou et al.](#page-14-2) [\(2022\)](#page-14-2) propose to use the Gram matrix of the feature extractor as the kernel, equivalent to only training the last layer with MSE loss. A very recent work [Loo et al.](#page-13-9) [\(2023\)](#page-13-9) assumes that the inner optimization is convex by considering linearized training in the lazy regime and replaces the meta-gradient with implicit gradients, thus achieving most recent state-of-the-art. Yet all of these approaches inevitably face the discrepancies between learning in the lazy regime and feature learning in data-adaptive neural nets (e.g. [Ghorbani et al.](#page-11-7) [\(2019\)](#page-11-7) and numerous follow ups) and often need to maintain a large model pool. Moreover, inner-loop surrogates, be it NTK, NNGP or random features, tend to show higher performance on wide networks, where the approximation holds better, and be less effective for the narrower models used in practice.

Modified objective: A great number of interesting works try to replace the elusive test accuracy objective with metrics that match the networks trained on full data and on synthetic data. [Zhao](#page-14-3) [and Bilen](#page-14-3) [\(2021a\)](#page-14-3) propose to match the gradient between the two networks with cosine similarity, with various variations (like differentiable data augmentation [\(Zhao and Bilen, 2021b\)](#page-14-5) (DSA)) and improvements [\(Jiang et al., 2022;](#page-12-6) [Lee et al., 2022b\)](#page-12-7). Other works pioneer feature alignment [\(Wang](#page-14-6) [et al., 2022\)](#page-14-6), matching the training trajectories (MTT, introduced in [Cazenavette et al.](#page-11-2) [\(2022\)](#page-11-2) and refined in [Cui et al.](#page-11-8) [\(2023\)](#page-11-8); [Du et al.](#page-11-9) [\(2023\)](#page-11-9); [Zhang et al.](#page-14-7) [\(2022\)](#page-14-7)), and loss-curvature matching [\(Shin](#page-13-10) [et al., 2023\)](#page-13-10). However, it is unclear how well the modified outer-loop metrics align with the test-loss objective and most of these methods ends up with subpar performance.

<span id="page-3-0"></span>

# 3. Methods

In this section, we start by defining the dataset distillation problem to motivate our RaT-BPTT method. Denote the original training set as  $\mathcal D$  and the distilled set as  $\mathcal U$ . With an initialization  $\theta_0$  for the inner-loop learner A, we perform the optimization for T steps to obtain  $\theta_T(\mathcal{U})$  with loss  $\mathcal{L}(\theta_T(\mathcal{U}), \mathcal{D})$ . We add  $(\mathcal{U})$  to denote its dependence on  $\mathcal{U}$ . The dataset distillation problem can be formulated as

$$
\min_{\mathcal{U}} \mathcal{L}(\theta_T(\mathcal{U}), \mathcal{D}) \text{ (outer loop)} \quad \text{such that} \quad \theta_T(\mathcal{U}) = \mathcal{A}(\theta_0, \mathcal{U}, T) \text{ (inner loop)} \tag{1}
$$

The principal method for tackling bilevel optimization problems is backpropagation through time (BPTT) in reverse mode. When the inner-loop learner A is gradient descent with learning rate  $\alpha$ , we obtain the meta-gradient with respect to the distilled dataset by leveraging the chain rule:

$$
\mathcal{G}_{BPTT} = -\alpha \frac{\partial \mathcal{L}(\theta_T(\mathcal{U}), \mathcal{D})}{\partial \theta} \sum_{i=1}^{T-1} \Pi_{j=i+1}^{T-1} \left[ 1 - \alpha \frac{\partial^2 \mathcal{L}(\theta_j(\mathcal{U}), \mathcal{U})}{\partial \theta^2} \right] \frac{\partial^2 \mathcal{L}(\theta_i(\mathcal{U}), \mathcal{U})}{\partial \theta \partial u} \tag{2}
$$

The aforementioned computation reveals that the meta-gradient can be decomposed into  $T - 1$ parts. Each part essentially represents a matrix product of the form  $\Pi[1-\alpha H]$  where every H matrix corresponds to a Hessian matrix. Nonetheless, computing the meta-gradient demands the storage of all intermediate states to backpropagate through every unrolling step. This imposes a significant strain on GPU memory resources and diminishes computational efficiency.

To circumvent these challenges, the prevalent strategy is truncated BPTT (T-BPTT) method [\(Puskorius and Feldkamp, 1994;](#page-13-11) [Williams and Peng, 1990\)](#page-14-8), which unrolls the inner loop for the same T steps but only propagates backwards through a smaller window of  $M$  steps. In T-BPTT, the gradient is

$$
\mathcal{G}_{T-BPTT} = -\alpha \frac{\partial \mathcal{L}(\theta_T(\mathcal{U}), \mathcal{D})}{\partial \theta} \sum_{i=T-M}^{T-1} \Pi_{j=i+1}^{T-1} \left[ 1 - \alpha \frac{\partial^2 \mathcal{L}(\theta_j(\mathcal{U}), \mathcal{U})}{\partial \theta^2} \right] \frac{\partial^2 \mathcal{L}(\theta_i(\mathcal{U}), \mathcal{U})}{\partial \theta \partial u} \tag{3}
$$

The distinguishing feature of T-BPTT is its omission of the first  $T - M + 1$  terms in the summation; each omitted term is a product of more than M Hessian matrices. Under the assumption that the inner loss function is locally  $\alpha$ –strongly convex, [Shaban et al.](#page-13-3) [\(2019\)](#page-13-3) shows that T-BPTT inherits convergence guarantees. The theoretical result comes from the diminishing contributions of the Hessian products. Strong convexity assumptions endow the Hessian matrices with positive eigenvalues. Consequently,  $1 - \alpha H$  will have all eigenvalues smaller than 1, and the product term  $\Pi[1 - \alpha H]$ 

<span id="page-4-0"></span>Image /page/4/Figure/1 description: This is a line graph showing the gradient norm over steps for four different methods: BPTT, T-BPTT, R-BPTT, and RaTBPTT. The x-axis represents the steps, ranging from 0 to 500. The y-axis represents the gradient norm on a logarithmic scale, with values shown as 10^-1 and 10^0. The BPTT and R-BPTT lines are generally higher and more volatile, fluctuating around 10^0. The T-BPTT and RaTBPTT lines are significantly lower, fluctuating around 10^-1, with RaTBPTT showing a steeper initial decrease and more pronounced step-like changes.

Figure 3: Meta-gradient norm in the first 500 steps. BPTT (unroll 120 steps) have unstable gradients. T-BPTT (unroll 120 steps and backpropagate 40 steps) stabilizes the gradient. For RaT-BPTT, for each epoch (25 batch-update steps) we randomly place the 40-step backpropagation window along the 120 unrolling. CIFAR10, IPC10.

Image /page/4/Figure/3 description: This line graph shows the accuracy of four different methods over 60,000 steps. The y-axis represents accuracy, ranging from 62 to 69, and the x-axis represents the number of steps. The four methods plotted are BPTT (blue line), T-BPTT (orange line), R-BPTT (green line), and RaTBPTT(ours) (red line). The RaTBPTT(ours) method shows the highest accuracy, starting around 62 and quickly rising to over 68, then plateauing. The other three methods show a slower increase in accuracy, with BPTT generally performing better than T-BPTT and R-BPTT, which are very close to each other. All lines have shaded regions indicating variability.

Figure 4: Test Accuracy during distillation with BPTT, T-BPTT, R-BPTT, and our RaT-BPTT. Using random unrolling (R-BPTT) and truncated window (T-BPTT) are both worse than BPTT. Combining them into RaT-BPTT gives the best performance. CIFAR10, IPC10.

vanishes as the number of factors increases. Therefore, T-BPTT could enjoy a similar performance compared with BPTT but with less memory requirement and faster optimization time.

However, the inner task in our context diverges significantly from the realm of strong convexity. It contains training a neural network from scratch on the current distilled data with random initialization. This problem is intrinsically non-convex with multiple local minima. This beckons the question: how do BPTT and T-BPTT fare empirically?

We visualize the training curve and the norm of meta-gradients through outerloop optimization steps in Figure [4.](#page-4-0) The experiment is performed on CIFAR10 with IPC 10. A comparison between BPTT120 and T-BPTT reveals that: 1) The meta-gradients of BPTT manifest significantly greater instability than their T-BPTT counterparts. This observed volatility and norm discrepancy can be attributed to the omitted  $T - M + 1$  gradient terms. It underscores the highly non-convex nature of the inner problem, characterized by Hessian matrices with negative eigenvalues. The compounded effects of these negative eigenvalues amplifies the variance from different initializations, creating the unstable gradient behavior. With the gradient stabilized, T-BPTT achieves faster improvement during the initial phase. 2) BPTT ends up

<span id="page-4-1"></span>

| Algorithm 1 Dataset Distillation with RaT-BPTT. Differences from BPTT are <span style="color:purple;">highlighted in purple</span> . |                                                                                                                |
|--------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------|
| <b>Input:</b> Target dataset $\mathcal{D}$ . T: total number of unrolling steps. M: truncated window size.                           |                                                                                                                |
| 1.                                                                                                                                   | Initialize distilled data $U$ from Gaussian                                                                    |
| 2.                                                                                                                                   | while <span style="color:purple;">Not converged</span> do                                                      |
| 3.                                                                                                                                   | <span style="color:purple;">Uniformly sample N in <math>[M, T]</math> as the current un- rolling length</span> |
| 4.                                                                                                                                   | Sample a batch of data $d \sim \mathcal{D}$                                                                    |
| 5.                                                                                                                                   | Randomly initialize $\theta_0$ from $p(\theta)$                                                                |
| 6.                                                                                                                                   | for $n = 0 \rightarrow N - 1$ do                                                                               |
| 7.                                                                                                                                   | If $n == N - M$ , <span style="color:purple;">start accumulating gradients</span>                              |
| 8.                                                                                                                                   | Sample a mini-batch of distilled data $u_t \sim \mathcal{U}$                                                   |
| 9.                                                                                                                                   | Update network $\theta_{n+1} = \theta_n - \alpha \nabla \ell(u_n; \theta_n)$                                   |
| 10.                                                                                                                                  | end for                                                                                                        |
| 11.                                                                                                                                  | Compute classification loss $\mathcal{L} = \ell(d, \theta_N)$                                                  |
| 12.                                                                                                                                  | Update $U$ with respect to $\mathcal L$ .                                                                      |
| 13.                                                                                                                                  | end while                                                                                                      |

with higher accuracy than T-BPTT. This indicates that important information from the initial phase is disregarded in T-BPTT — a notable concern given that the rapid optimization of neural networks usually happens during the early stage of the inner loop. The challenge thus is how to harmonize the good generalization performance of BPTT with the computational speedup of T-BPTT.

To this end, we propose the Random Truncated BPTT (RaT-BPTT) in Algorithm [1,](#page-4-1) which randomly places the truncated window along the inner unrolling chain. The gradient of RaT-BPTT is

$$
\mathcal{G}_{RaT-BPTT} = -\alpha \frac{\partial \mathcal{L}(\theta_N(\mathcal{U}), \mathcal{D})}{\partial \theta} \sum_{i=N-M}^{N-1} \Pi_{j=i+1}^{N-1} \left[ 1 - \alpha \frac{\partial^2 \mathcal{L}(\theta_j(\mathcal{U}), \mathcal{U})}{\partial \theta^2} \right] \frac{\partial^2 \mathcal{L}(\theta_i(\mathcal{U}), \mathcal{U})}{\partial \theta \partial u} \tag{4}
$$

Looking at the gradients, RaT-BPTT differs by randomly sampling M consecutive parts in  $G_{BPTT}$  and leaving out the shared Hessian matrix products. Therefore, RaT-BPTT is a subsample version of BPTT, spanning the entire learning trajectory. Moreover, the maximum number of Hessians in the product is restricted to less than than M. It thus inherits the benefits of both the accelerated performance and gradient stabilization from T-BPTT. As illustrated in Figure [4,](#page-4-0) RaT-BPTT consistently outperforms other methods throughout the optimization process. We also examine performing full unrolling along trajectories of randomly sampled lengths (R-BPTT) as a sanity check. The gradients are similarly unstable and the performance is worse than full unrolling with BPTT. We further provide an ablation study in Section [4.3](#page-7-1) on the necessity of having a moving truncated window and the rationale of random uniform sampling in Algorithm [1.](#page-4-1)

<span id="page-5-0"></span>

## 4. Experimental Results

In this section, we present an evaluation of our method, RaT-BPTT, comparing it to a range of SOTA methods across multiple benchmark datasets.

**Datasets** We run experiments on four standard datasets, CIFAR-10 (10 classes,  $32 \times 32$ ), CIFAR-100 (100 classes,  $32 \times 32$ , [Krizhevsky et al.](#page-12-8) [\(2009\)](#page-12-8)), Caltech Birds 2011 (200 classes, CUB200,  $32 \times 32$ , [Wah et al.](#page-14-9)  $(2011)$  and Tiny-ImageNet  $(200 \text{ classes}, 64 \times 64, \text{Le} \text{ and Yang } (2015))$  $(200 \text{ classes}, 64 \times 64, \text{Le} \text{ and Yang } (2015))$  $(200 \text{ classes}, 64 \times 64, \text{Le} \text{ and Yang } (2015))$ . We distill datasets with 1, 10, and 50 images per class for the first two datasets and with 1 and 10 images per class for the last two datasets.

Baselines We compare our methods to the first two lines of works as we discussed in related work (Section [2\)](#page-2-0), including 1) inner-loop surrogates: standard BPTT (the non-factorized version of LinBa in [\(Deng and Russakovsky, 2022\)](#page-11-1)), Neural Tangent Kernel (KIP) [\(Nguyen et al., 2021b\)](#page-13-1), Random Gaussian Process (RFAD) [\(Loo et al., 2022\)](#page-12-0), and empirical feature kernel (FRePO) [\(Zhou et al.,](#page-14-2) [2022\)](#page-14-2), and reparameterized convex implicit gradient (RCIG) [\(Loo et al., 2023\)](#page-13-9), 2) Modified objectives: gradient matching with augmentation (DSA) [\(Zhao and Bilen, 2021b\)](#page-14-5), distribution matching (DM) [\(Zhao and Bilen, 2023\)](#page-14-10), trajectory matching (MTT) [\(Cazenavette et al., 2022\)](#page-11-2), and flat trajectory distillation (FTD) [\(Cui et al., 2023\)](#page-11-8). The works on parametrization [\(Deng and Russakovsky, 2022;](#page-11-1) [Kim et al., 2022;](#page-12-10) [Liu et al., 2022\)](#page-12-11) are complementary to our optimization framework and can be combined with RaT-BPP for improved performance, as we illustrate for the SOTA case of linear basis [\(Deng and Russakovsky, 2022\)](#page-11-1) in Section [4.2.](#page-6-0)

Setup Building upon existing literature, we employ standard ConvNet architectures [\(Cazenavette](#page-11-2) [et al., 2022;](#page-11-2) [Deng and Russakovsky, 2022;](#page-11-1) [Zhao and Bilen, 2021b\)](#page-14-5) —three layers for  $32 \times 32$  images and four layers for  $64 \times 64$  images. Our distilled data is trained utilizing Algorithm 1, with the Higher package [\(Grefenstette et al., 2019\)](#page-11-10) aiding in the efficient calculation of meta-gradients. We opt for a simple setup: using Adam for inner optimization with a learning rate of 0.001, and applying standard augmentations (flip and rotation) on the target set. Parameters such as unrolling length and window size are determined via a validation set.

Evaluation During the evaluation phase, we adhere to the standard augmentation protocol as per [Deng and Russakovsky](#page-11-1) [\(2022\)](#page-11-1); [Zhao and Bilen](#page-14-5) [\(2021b\)](#page-14-5). We conduct evaluations of each distilled data set using ten randomly selected neural networks, reporting both the mean and standard deviation of the results. For all other baseline methodologies, we record the best value reported in

the original paper. Note that [Loo et al.](#page-13-9) [\(2023\)](#page-13-9); [Zhou et al.](#page-14-2) [\(2022\)](#page-14-2) employs a 4 or 8 times wider ConvNet to reduce discrepancies between surrogate approximations and actual training. To ensure alignment with this protocol, we provide a transfer evaluation of our method, that is we distill with a narrow network and evaluate with a wide network. We also re-evaluate their checkpoints (when available) for narrow networks using their code. Complete details, along with links to our code and distilled checkpoints, can be found in the Appendix.

<span id="page-6-1"></span>Table 1: Performance of different dataset distillation techniques on standard datasets. The AVG column denotes the average performance across all the other columns. \* denotes works where performance evaluated with wider ConvNets. FRePO and RCIG are the re-evaluated results with narrow networks. Results denotes the best results for narrow networks while results denotes best for wide networks.

| Dataset       |                                   | $CIFAR-10$                  |                                                |                | $CIFAR-100$                     |                                    |                             | CUB200         |                             | T-ImageNet                  |                             | AVG            |
|---------------|-----------------------------------|-----------------------------|------------------------------------------------|----------------|---------------------------------|------------------------------------|-----------------------------|----------------|-----------------------------|-----------------------------|-----------------------------|----------------|
|               | Img/class(IPC)                    |                             | 10                                             | 50             |                                 | 10                                 | 50                          |                | 10                          |                             | 10                          |                |
| Inner<br>Loop | BPTT (Deng and Russakovsky, 2022) | $49.1 \pm 0.6$              | $62.4 \pm 0.4$                                 | $70.5 \pm 0.4$ | $121.3 \pm 0.6$                 | $34.7{\pm}0.5$                     |                             |                |                             |                             |                             |                |
|               | $KIP^*$ (Nguyen et al., 2021b)    | $49.9{\scriptstyle \pm0.2}$ | $62.7 \pm 0.3$ $68.6 \pm 0.2$   15.7 $\pm$ 0.2 |                |                                 | $28.3{\scriptstyle \pm0.1}$        | $\sim$                      |                |                             |                             |                             |                |
|               | $RFAD*$ (Loo et al., 2022)        | $53.6 + 1.2$                | $66.3 + 0.5$                                   | $71.1 \pm 0.4$ | $26.3 + 1.1$                    | $33.0 + 0.3$                       |                             |                |                             |                             |                             |                |
|               | $FRePO^*$ (Zhou et al., 2022)     | $46.8 + 0.7$                | $65.5 + 0.6$                                   |                |                                 | $71.7+0.2$ 28.7+0.1 42.5 $\pm$ 0.2 | $44.3{\scriptstyle \pm0.2}$ | $12.4 + 0.2$   | $16.8 + 0.1$                | $15.4 + 0.3$                | $25.4{\scriptstyle \pm0.2}$ | $36.9 + 0.3$   |
|               | FRePO                             | $45.6 + 0.1$                | $63.5 + 0.1$                                   | $70.7 + 0.1$   | $26.3 + 0.1$                    | $41.3 \pm 0.1$                     | $41.5 \pm 0.1$              |                |                             | $16.9{\scriptstyle \pm0.1}$ | $22.4 + 0.1$                |                |
|               | $RCIG^*$ (Loo et al., 2023)       | $53.9{\pm}1.0$              | $69.1{\scriptstyle \pm 0.4}$                   | $73.5 \pm 0.3$ | $39.3{\pm}0.4$                  | $44.1{\pm}0.4$                     | $46.7{\scriptstyle \pm0.1}$ | $12.1 + 0.2$   | $15.7 \pm 0.3$              | $25.6 + 0.3$                | $29.4 \pm 0.2$              | $40.9{\pm}0.4$ |
|               | RCIG                              | $49.6{\scriptstyle \pm1.2}$ | $66.8{\scriptstyle \pm0.3}$                    | $\sim$         | $35.5 \pm 0.7$                  |                                    |                             |                |                             | $22.4 \pm 0.3$              | $\sim$                      |                |
|               | DSA (Zhao and Bilen, 2021b)       | $28.8 + 0.7$                | $52.1 + 0.5$                                   |                | $60.6 \pm 0.5$   13.9 $\pm$ 0.3 | $32.3 + 0.3$                       | $42.8 + 0.4$                | $1.3 + 0.1$    | $4.5 + 0.3$                 | $6.6 + 0.2$                 | $14.4 \pm 2.0$              | $25.7 + 0.7$   |
| Modified      | DM (Zhao and Bilen, 2023)         | $26.0 + 0.8$                | $48.9{\pm}0.6$                                 | $63.0 + 0.4$   | $11.4 \pm 0.3$                  | $29.7{\scriptstyle \pm0.3}$        | $43.6 + 0.4$                | $1.6 + 0.1$    | $4.4 \pm 0.2$               | $3.9 + 0.2$                 | $12.9\t\pm\t0.4$            | $24.5 + 0.4$   |
| Objectives    | MTT (Cazenavette et al., 2022)    | $46.3{\scriptstyle \pm0.8}$ | $65.3{\scriptstyle \pm 0.7}$                   |                | $71.6 \pm 0.2$   $24.3 \pm 0.3$ | $40.1{\scriptstyle \pm 0.4}$       | $47.7{\scriptstyle \pm0.3}$ | $2.2 + 0.1$    | $\sim$                      | $8.8 + 0.3$                 | $23.2{\scriptstyle \pm0.2}$ |                |
|               | $FTD$ (Du et al., 2023)           | $46.8 \pm 0.3$              | $66.6{\scriptstyle \pm0.3}$                    |                | $73.8 \pm 0.2$   $25.2 \pm 0.2$ | $43.4 \pm 0.3$                     | $50.7{\pm}0.3$              |                | $\sim$                      | $10.4 \pm 0.3$              | $24.5 \pm 0.2$              |                |
|               | Ours                              | $53.2{\pm}0.7$              | $69.4 \pm 0.4$                                 | $75.3 \pm 0.3$ | $35.3 \pm 0.4$                  | $47.5 \pm 0.2$                     | $50.6 + 0.2$                | $13.8 + 0.3$   | $17.7{\scriptstyle \pm0.2}$ | $20.1 \pm 0.3$              | $24.4 \pm 0.2$              | $40.8 \pm 0.3$ |
|               | Ours (transfer to wide)           | $54.1 \pm 0.4$              | $71.0 \pm 0.2$                                 |                | $75.4 \pm 0.2$ 36.5 $\pm$ 0.3   | $47.9 \pm 0.2$                     | $51.0 \pm 0.3$              | $14.2 \pm 0.3$ | $17.9{\scriptstyle \pm0.3}$ | $20.3 \pm 0.1$              | $24.9{\scriptstyle \pm0.1}$ | $41.2 \pm 0.3$ |

### 4.1 Benchmark Performance

Our simple approach to dataset distillation demonstrates competitive performance across a number of datasets (Table [1\)](#page-6-1). With 10 and 50 images per class, our approach gets the state-of-the-art (SOTA) results on the CIFAR-100, CIFAR-10, and CUB200 datasets (Table [1](#page-6-1) last two rows). Moreover, we achieve these results without any approximations to the inner loop. When considering all IPC values in  $\{1, 10, 50\}$ , across all datasets, our approach performs as well as the RCIG method up to statistical significance. Encouragingly, even though our bilevel optimization is not biased towards wider networks, we obtain state-of-the-art performance even for wide networks on CIFAR10, CIFAR100, and CUB200 across all IPC values. Moreover, when the datasets from the wider-network approaches are evaluated on practical, narrower settings we find that they show a significant drop in performance, going from 39.3% to 35.5% (RCIG, CIFAR100, IPC1) or from 25.4% to 22.4% (FrePO, TinyImageNet, IPC10). Thus, our work generalizes gracefully to wider networks that are used by previous work (improving in performance) as well as narrower networks (for which we can tune directly). This is a significant advantage of our work over prior state-of-the-art.

<span id="page-6-0"></span>

#### 4.2 Combination with Parametrization Methods

A separate and complimentary line of work aims to improve the optimization via parameterization of the distilled dataset. [Liu et al.](#page-12-11) [\(2022\)](#page-12-11); [Wang et al.](#page-14-11) [\(2023\)](#page-14-11) leverage encoder-decoders, [Cazenavette](#page-11-11) [et al.](#page-11-11) [\(2023\)](#page-11-11); [Lee et al.](#page-12-12) [\(2022a\)](#page-12-12) use generative priors, [Kim et al.](#page-12-10) [\(2022\)](#page-12-10); [Liu et al.](#page-12-13) [\(2023b\)](#page-12-13) propose multi-scale augmentation, and [Deng and Russakovsky](#page-11-1) [\(2022\)](#page-11-1) designs linear basis with weights for the dataset.

Note that our performance improvements come from a careful study of the bilevel optimization problem. In principle, RaT-BPTT is complimentary to most of these parameterization ideas and can be seamlessly intergrated. For instance, we adopt the linear basis from [Deng and Russakovsky](#page-11-1) [\(2022\)](#page-11-1) within our framework.We only study the case of CIFAR10, as for all the other benchmarks RaT-BPP gives better performance even without data parametrization. Without any hyper-parameter tuning, we can im-

<span id="page-7-2"></span>Figure 5: Combination of RaT-BPTT with linear parameterization leads to further improvement. We only present those settings where parameterization outperforms the standard RaT-BPTT.

| Dataset<br>Img/class(IPC)  | CIFAR-10                           |                           |                           |
|----------------------------|------------------------------------|---------------------------|---------------------------|
|                            | 1                                  | 10                        |                           |
| Para-<br>meteri-<br>zation | IDC (Kim et al., 2022)             | $50.0 	ext{ 	extpm } 0.4$ | $67.5 	ext{ 	extpm } 0.5$ |
|                            | LinBa (Deng and Russakovsky, 2022) | $66.4 	ext{ 	extpm } 0.4$ | $71.2 	ext{ 	extpm } 0.4$ |
|                            | HaBa (Liu et al., 2022)            | $48.3 	ext{ 	extpm } 0.8$ | $69.9 	ext{ 	extpm } 0.4$ |
|                            | Linear + RaT-BPTT                  | $68.2 	ext{ 	extpm } 0.4$ | $72.8 	ext{ 	extpm } 0.4$ |

prove their performance by around 1.6%, leading to astonishing numbers of 68.2% for IPC1 and 72.8% for IPC10 (the numbers w/o parameterization are 53.2% and 69.4% respectively). The results are shown in Table [5.](#page-7-2) We leave the exploration of other combinations to future work.

<span id="page-7-1"></span>

#### 4.3 Ablations on the Random Truncated Window

In Section [3,](#page-3-0) we justify the necessity of performing truncation to speed up and stabilize the gradient, and the necessity of changing the truncated window to cover the entire trajectory. Now we provide an ablation study on how to select the truncated window. We compare three methods, 1) random uniform truncation, 2) backward moving, and 3) forward moving. For the forward (backward) moving method, we initialize the window at the end (beginning). It is then shifted forward (backward) by the window size whenever the loss remains stagnant for 2,000 steps.

From Figure [6,](#page-7-3) it is surprising that randomly uniform window placement achieves the best performance across the whole training process. A closer examination of the forward and backward moving curves suggests that altering the window's positioning can

<span id="page-7-3"></span>Image /page/7/Figure/7 description: A line graph displays the accuracy of three different methods: Random Uniform, Backward, and Forward, over 60,000 steps. The y-axis represents accuracy, ranging from 62 to 69, and the x-axis represents steps, from 0 to 60,000. The Random Uniform method shows the highest accuracy, consistently above 67 and reaching close to 69. The Forward method starts lower but gradually increases, reaching approximately 67 by 40,000 steps and continuing to rise. The Backward method shows a slower initial increase, starting around 62 and climbing to about 66 by 40,000 steps, then rapidly increasing to match the Forward method's accuracy by 50,000 steps. Shaded areas around each line indicate the variability or confidence interval for each method.

Figure 6: Comparison between random uniform truncation, backward moving, and forward moving. Random uniform truncation gives the best performance across the whole training process.  $N=120$ , T=40 for IPC10 with CIFAR10.

spur noticeable enhancements in accuracy. Such findings reinforce the idea that distinct truncation windows capture varied facets of knowledge, bolstering our intuition about the need for a comprehensive trajectory coverage by the window.

One might ask whether uniform sampling is the best design. Actually the answer is no. With careful tuning by sampling more on the initial phase, we find that one can further improve the final accuracy by 0.4% for CIFAR10 with IPC10. However, it introduces an additional hyper-parameter that requires careful tuning. To keep our method simple, we choose to go with the uniform one.

<span id="page-7-0"></span>

## 5. Intercorrelations and Boosting

Having established the strength of our method, we proceed to dissect the structure of the learned datasets to catalyze further progress. Nearly all the current distillation method optimize the data jointly. Such joint optimization often leads to unexpected behavior which is largely absent in the original dataset. For instance, [Nguyen et al.](#page-13-1) [\(2021b\)](#page-13-1) observe that subsampling a large distilled dataset leads to significantly low performance. The data is jointly learned, and therefore can be correlated.

<span id="page-8-0"></span>Image /page/8/Figure/1 description: The figure is a line graph showing the accuracy of different models based on the IPC number. The x-axis is labeled "IPC number" and ranges from 10 to 50. The y-axis is labeled "Accuracy" and ranges from 20 to 70. There are five lines representing different models: "KIP IPC50" (pink), "KIP IPC10" (orange), "RaT-BPTT IPC50" (teal), "RaT-BPTT IPC10" (brown), and "Random Real" (green). The "KIP IPC10" and "RaT-BPTT IPC10" lines show a rapid increase in accuracy from approximately 30 to 70 between IPC numbers 5 and 10. The "RaT-BPTT IPC50" line shows a steady increase from around 25 to over 70 as the IPC number increases from 5 to 50. The "KIP IPC50" line starts at around 18 and increases to about 69 at IPC number 50. The "Random Real" line shows a gradual increase from around 26 to about 53 as the IPC number increases from 5 to 50. Shaded areas around each line indicate variability or confidence intervals.

Figure 7: Test performance of random subsamples of IPC50 distilled dataset from KIP and our method for various sample sizes, compared to performance of real randomly sampled data of the same size. CIFAR10.

Image /page/8/Figure/3 description: This line graph shows the accuracy of three different methods: Distill All, Strongly Boost, and Weakly Boost, as a function of the IPC number. The x-axis represents the IPC number, ranging from 10 to 50. The y-axis represents the Accuracy, ranging from 66 to 74. The 'Distill All' line is teal, 'Strongly Boost' is pink, and 'Weakly Boost' is orange. All three lines show an increasing trend in accuracy as the IPC number increases. The 'Weakly Boost' line generally shows the highest accuracy, followed by 'Distill All', and then 'Strongly Boost'.

Figure 8: Performance of fully distilled, strongly and weakly boosted distilled data. Boosting essentially retains performance compared to jointly distilled data. CIFAR10, RaT-BPTT.

A particularly noticeable consequence of the correlation is the necessity to re-distill the dataset for different distillation budgets, with minimal opportunity to leverage previous efforts. In Figure [7](#page-8-0) , we compare subsampling accuracies of subsamples of various sizes coming from an IPC50 dataset produced with KIP (and RaT-BPTT). We validate previous observation and show its pervalance for various IPC settings. Specifically, we see that subsampling 5 images per class from an IPC50 distilled dataset not only gives performance way below an IPC5 dataset learned from scratch, but performs even worse than training on 5 *random* training images per class. Compared with KIP, our approach, RaT-BPP, demonstrates a reduced degradation in performance, particularly at intermediate IPC values, possibly attributable to its independence from approximations of the entire inner optimization loop. Still, while subsamples from RaT-BPP-distilled data fairs better than those from KIP-distilled data, for smaller sample sizes (up to IPC10) they fare worse than random samples. Is there potential for further improvement? Can we distill datasets containing subsets that lead to good accuracy for downstream tasks, comparable to the one achieved when we distill smaller datasets directly?

To address this challenge, we propose boosted dataset distillation (Boost-DD) (in Algorithm [2\)](#page-8-1). Boost-DD controls intercorrelation within distilled data and can be integrated with any gradient-based dataset distillation algorithm. The central idea is to construct the distilled data iteratively with smaller data groups called "blocks" as illustrated in Figure [2.](#page-1-0) For IPC50, we divide all images into blocks of IPC5. We start from a distilled IPC5. Each time, we add another fresh block of IPC5, and optimize the new block with reduced learning rate on the existing blocks by a factor of  $\beta$  < 1. The extreme case where the learning rate is zero for previous blocks is termed strongly-boost  $(\beta = 0)$ . The advantage of

<span id="page-8-1"></span>Algorithm 2 Boosted Dataset Distillation (Boost-DD) **Input:** Target dataset  $\mathcal{D}$ . Distillation-Algorithm  $\mathcal{A}$ with initiatlization procedure  $\mathcal{I}(size)$  and meta-learning rate  $\alpha$ , outputting distilled data  $\mathcal{U} = \mathcal{A}(\mathcal{D}, \mathcal{I}(size))$  $(|\mathcal{U}| = size)$ . Block size *b*. Number of blocks *J*. Boosting-strength  $\beta \in [0, 1]$ .

**Output:** Distilled data  $\mathcal{U}$  with  $|\mathcal{U}| = b \cdot J$ .

- 1: Distill first block of size b:  $\mathcal{U}_0 := \mathcal{A}(\mathcal{D}, \mathcal{I}(b)).$
- 2: for  $j = 1...J 1$  do
- 3: Distill increased data  $\mathcal{U}_j = \mathcal{A}(\mathcal{D}, \mathcal{U}_{j-1} \cup \mathcal{I}(b))$ using "stale" meta-learning rate  $\alpha_s = \beta \cdot \alpha$  on the first  $(j-1) \cdot b$  data points and  $\alpha$  on the last b.
- 4: end for
- 5:  $U := U_{J-1}$ .

this approach is that initial blocks remain unchanged when the new block is added, ensuring consistent performance. This results in a "nested" dataset where subsampling earlier blocks yields high-performing subsets! We call the algorithm weakly-boost  $\beta$  is non-zero and perform experiments with  $\beta = 0.1$ .

Figure [8](#page-8-0) shows how weakly-boost and strongly-boost compare to distilling each of the sub-datasets from scratch. It is important to highlight that the curve for 'strongly-boost' represents the accuracy curve obtainable when subsampling across different budgets. We observe that even strongly-boost results in exceedingly minor sacrifice in performance compared to joint distillation from scratch, especially with larger distilled datasets. In the Appendix we present a visual comparison of images distilled jointly with images distilled with boosting. Boosted images seem to show larger diversity and seem closer to real images.

### 6. Discussion

In this work, we proposed a simple yet effective method for dataset distillation, based on random truncated backpropagation through time. Through a careful analysis of BPTT, we show that randomizing the window allows to cover long dependencies in the inner problem while truncation addressed the unstable gradient and the computational burden. Our method achieves state of the art performance across multiple standard benchmarks, across both narrow as well as wide networks. Nonetheless, several design choices are guided by intuitions and observations, leaving room for improvement. We defer a detailed limitation discussion to Appendix [A.](#page-0-0)

Further, we address the catastrophic degradation in performance of subsets of distilled data with our boosting method. It allows us to create a single versatile dataset for various distillation budgets with minimal performance degradation. However, the boosted dataset still has inner correlation between blocks. This is evident in Figure [9](#page-10-0) when comparing the performance of the first IPC5 block with the second one obtained via strongly-boost (though both of them are much higher than sampling a random IPC5 from the jointly distilled IPC50). Moreover, as shown in Figure [8,](#page-8-0) weakly-boost for larger IPC eventually outperforms joint training. Since weakly-boost generates less inter-correlated datasets, this hints at the possibility that strong intercorrelations are one reason for diminishing returns observed when increasing the size of distilled datasets. While higher-order correlations may potentially encode more information, they also compromise data interpretability, diverging from the standard IID paradigm. Is it possible to further minimize these correlations, especially in larger IPC datasets? We leave these questions to future research.

We also attempt to understand other factors bottlenecking the gains when scaling up distilled data. Specifically, we try to understand what information is learnt in the distilled data by dissecting the accuracy on evaluation samples. We leverage a hardness score that characterizes whether data is easy or hard, stratify the accuracy by hardness and compare it for the original network and the network trained on distilled data for a range of IPC in Figure [10](#page-10-0) (details in Appendix [D\)](#page-5-0). One would have hoped that adding more distilled data would help to distill more of the hardness tail, but this is not the case. This suggests that future work might benefit from focusing on how one can distill data that is better adapted to larger hardness scores, for instance by infusing validation batches with harder data, placing emphasis on the middle of the distribution. A preliminary promising study is presented in Appendix [D.](#page-5-0)

### 7. Acknowledgements

This work was supported by the National Science Foundation under NSF Award 1922658. This work was also supported in part through the NYU IT High Performance Computing resources, services, and staff expertise. YF would like to thank Di He, Weicheng Zhu, Kangning Liu, and Boyang Yu for discussions and suggestions.

<span id="page-10-0"></span>Image /page/10/Figure/1 description: A bar chart titled "IPC5 analysis" displays the results of an experiment. The y-axis ranges from 0 to 70, representing some measured value. The x-axis lists various categories, including "Real IPC5", "Distilled IPC5", "Random 5 from D-IPC50", "Random 5 from D-IPC25", "Random 5 from D-IPC10", "First 5 from Strong", "Second 5 from Strong", "First 5 from Weak", "Second 5 from Weak", "Random 5 from Strong", "Random 5 from Weak", and "Random 5 from D-IPC10". The chart is divided into three sections by dashed vertical lines, labeled "Inter Correlation", "Boosting", and "Boosting lowers correlation". Within each section, there are several bars of varying heights, each with an error bar indicating variability. A dotted horizontal line is present at the 27 mark across the chart. The bar for "Real IPC5" is approximately 26, "Distilled IPC5" is approximately 67, "Random 5 from D-IPC50" is approximately 21, "Random 5 from D-IPC25" is approximately 22, "Random 5 from D-IPC10" is approximately 32, "First 5 from Strong" is approximately 27, "Second 5 from Strong" is approximately 65, "First 5 from Weak" is approximately 50, "Second 5 from Weak" is approximately 35, "Random 5 from Strong" is approximately 43, "Random 5 from Weak" is approximately 34, and "Random 5 from D-IPC10" is approximately 31.

Figure 9: Performance of subsamples compared to fully distilled and random real images for IPC5: y-axis shows test accuracy a) Random IPC5 from IPC50 and IPC25 performs worse than random real IPC5, indicating the strong inter-correlation learned in the dataset. b) IPC5 building blocks of boosting perform quite well. c) Random IPC5 from boosted IPC10 performs better than random IPC5 from standard IPC10. Boosting some- $\begin{tabular}{|c|c|c|c|c|} \hline & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & &$ 

Image /page/10/Figure/3 description: The image contains two plots. The top plot is a stacked bar chart showing accuracy on the y-axis against hardness score on the x-axis. The bars are stacked with different components labeled as Optimal, IPC50, IPC25, IPC10, IPC5, and IPC1. The accuracy generally decreases as the hardness score increases. The bottom plot is a histogram titled "Histogram of the Hardness Score". The x-axis represents the hardness score, and the y-axis represents the number of data points on a logarithmic scale. The histogram shows the distribution of hardness scores, with the highest frequency occurring at a hardness score of 0.

Figure 10: Top: Hardness score vs accuracy of dataset distillation (for various images per class (IPC)). "Optimal" indicates accuracy when training on the entire real data. Bottom: Histogram of the hardness scores. Notice how score 0 (unforgettable) examples are distilled well (top), but harder examples are progressively harder to distill. Details in Appendix [D](#page-5-0)

# References

- <span id="page-11-6"></span>Sanjeev Arora, Simon S. Du, Wei Hu, Zhiyuan Li, Ruslan Salakhutdinov, and Ruosong Wang. On exact computation with an infinitely wide neural net. In Hanna M. Wallach, Hugo Larochelle, Alina Beygelzimer, Florence d'Alché-Buc, Emily B. Fox, and Roman Garnett, editors, Advances in Neural Information Processing Systems 32: Annual Conference on Neural Information Processing Systems 2019, NeurIPS 2019, December 8-14, 2019, Vancouver, BC, Canada, pages 8139–8148, 2019.
- <span id="page-11-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 4750–4759, 2022.
- <span id="page-11-11"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2023.
- <span id="page-11-5"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. Advances in Neural Information Processing Systems, 35:810–822, 2022.
- <span id="page-11-8"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023.
- <span id="page-11-0"></span>Stephan Dempe. Bilevel optimization: theory, algorithms, applications and a bibliography. Bilevel Optimization: Advances and Next Challenges, pages 581–672, 2020.
- <span id="page-11-1"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), 2022.
- <span id="page-11-9"></span>Jiawei Du, Yidi Jiang, Vincent T. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2023.
- <span id="page-11-12"></span>Yunzhen Feng, Runtian Zhai, Di He, Liwei Wang, and Bin Dong. Transferred discrepancy: Quantifying the difference between representations. arXiv preprint arXiv:2007.12446, 2020.
- <span id="page-11-3"></span>Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In Doina Precup and Yee Whye Teh, editors, *Proceedings of the 34th International* Conference on Machine Learning, volume 70 of Proceedings of Machine Learning Research, pages 1126–1135. PMLR, 06–11 Aug 2017.
- <span id="page-11-4"></span>Zongxion Geng, Jiahui andg Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. In Proceedings of the International Joint Conference on Artificial Intelligence (IJCAI), 2023.
- <span id="page-11-7"></span>Behrooz Ghorbani, Song Mei, Theodor Misiakiewicz, and Andrea Montanari. Limitations of lazy training of two-layers neural network. In H. Wallach, H. Larochelle, A. Beygelzimer, F. d'Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural Information Processing Systems, volume 32. Curran Associates, Inc., 2019.
- <span id="page-11-10"></span>Edward Grefenstette, Brandon Amos, Denis Yarats, Phu Mon Htut, Artem Molchanov, Franziska Meier, Douwe Kiela, Kyunghyun Cho, and Soumith Chintala. Generalized inner loop meta-learning. arXiv preprint arXiv:1910.01727, 2019.

- <span id="page-12-2"></span>Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. In Workshop on Federated Learning: Recent Advances and New Challenges (in Conjunction with NeurIPS 2022), 2022. URL [https:](https://openreview.net/forum?id=lk8VhkQ4eE3) [//openreview.net/forum?id=lk8VhkQ4eE3](https://openreview.net/forum?id=lk8VhkQ4eE3).
- <span id="page-12-4"></span>Arthur Jacot, Clément Hongler, and Franck Gabriel. Neural tangent kernel: Convergence and generalization in neural networks. In Samy Bengio, Hanna M. Wallach, Hugo Larochelle, Kristen Grauman, Nicolò Cesa-Bianchi, and Roman Garnett, editors, Advances in Neural Information Processing Systems 31: Annual Conference on Neural Information Processing Systems 2018, NeurIPS 2018, December 3-8, 2018, Montréal, Canada, pages 8580–8589, 2018.
- <span id="page-12-6"></span>Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z. Pan. Delving into effective gradient matching for dataset condensation. arXiv preprint arXiv:2208.00311, 2022.
- <span id="page-12-1"></span>Ibrahim Jubran, Alaa Maalouf, and Dan Feldman. Introduction to coresets: Accurate coresets. ArXiv, abs/1910.08707, 2019.
- <span id="page-12-10"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In Proceedings of the International Conference on Machine Learning (ICML), pages 11102–11118, 2022.

<span id="page-12-8"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.

- <span id="page-12-9"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. CS 231N, 7(7):3, 2015.
- <span id="page-12-12"></span>Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.00719, 2022a.
- <span id="page-12-5"></span>Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide Neural Networks of Any Depth Evolve as Linear Models Under Gradient Descent. In H. Wallach, H. Larochelle, A. Beygelzimer, F. d'Alché-Buc, E. Fox, and R. Garnett, editors, Advances in Neural Information Processing Systems, volume 32. Curran Associates, Inc., 2019.
- <span id="page-12-7"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In Proceedings of the International Conference on Machine Learning (ICML), pages 12352–12364, 2022b.
- <span id="page-12-3"></span>Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation.  $arXiv$  preprint arXiv:2301.05603, 2023.
- <span id="page-12-11"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), 2022.
- <span id="page-12-14"></span>Songhua Liu, Jingwen Ye, Runpeng Yu, and Xinchao Wang. Slimmable dataset condensation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3759–3768, 2023a.
- <span id="page-12-13"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: Efficient dataset distillation by representative matching. arXiv preprint arXiv:2302.14416, 2023b.
- <span id="page-12-0"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), 2022.

- <span id="page-13-9"></span>Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. arXiv preprint arXiv:2302.06755, 2023.
- <span id="page-13-2"></span>Jonathan Lorraine, Paul Vicol, and David Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In Silvia Chiappa and Roberto Calandra, editors, Proceedings of the Twenty Third International Conference on Artificial Intelligence and Statistics, volume 108 of Proceedings of Machine Learning Research, pages 1540–1552. PMLR, 26–28 Aug 2020.
- <span id="page-13-4"></span>Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In Hal Daumé III and Aarti Singh, editors, Proceedings of the 37th International Conference on Machine Learning, volume 119 of Proceedings of Machine Learning Research, pages 6950–6960. PMLR, 13–18 Jul 2020.
- <span id="page-13-0"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In Proceedings of the International Conference on Learning Representations (ICLR), 2021a.
- <span id="page-13-1"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), pages 5186–5198, 2021b.
- <span id="page-13-12"></span>Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. Wortman Vaughan, editors, Advances in Neural Information Processing Systems, 2021. URL <https://openreview.net/forum?id=Uj7pF-D-YvT>.
- <span id="page-13-11"></span>GV Puskorius and LA Feldkamp. Truncated backpropagation through time and kalman filter training for neurocontrol. In Proceedings of 1994 IEEE International Conference on Neural Networks (ICNN'94), volume 4, pages 2488–2493. IEEE, 1994.
- <span id="page-13-8"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. arXiv preprint arXiv:2301.04272, 2023.
- <span id="page-13-5"></span>Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In 2022 International Joint Conference on Neural Networks (IJCNN), pages 01–08, 2022.
- <span id="page-13-3"></span>Amirreza Shaban, Ching-An Cheng, Nathan Hatch, and Byron Boots. Truncated back-propagation for bilevel optimization. In Kamalika Chaudhuri and Masashi Sugiyama, editors, Proceedings of the Twenty-Second International Conference on Artificial Intelligence and Statistics, volume 89 of Proceedings of Machine Learning Research, pages 1723–1732. PMLR, 16–18 Apr 2019.
- <span id="page-13-10"></span>Seungjae Shin, Heesun Bae, Donghyeok Shin, Weonyoung Joo, and Il-Chul Moon. Loss-curvature matching for dataset selection and condensation. In Proceedings of the International Conference on Artificial Intelligence and Statistics (AISTATS), 2023.
- <span id="page-13-6"></span>Rui Song, Dai Liu, Dave Zhenyu Chen, Andreas Festag, Carsten Trinitis, Martin Schulz, and Alois Knoll. Federated learning via decentralized dataset distillation in resource-constrained edge environments, 2022.
- <span id="page-13-7"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In Hal Daumé III and Aarti Singh, editors, Proceedings of the 37th International Conference on Machine Learning, volume 119 of Proceedings of Machine Learning Research, pages 9206–9216. PMLR, 13–18 Jul 2020.

- <span id="page-14-1"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In Proceedings of the International Joint Conference on Neural Networks (IJCNN), pages 1–8, 2021.
- <span id="page-14-12"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In International Conference on Learning Representations, 2019. URL [https://openreview.net/](https://openreview.net/forum?id=BJlxm30cKm) [forum?id=BJlxm30cKm](https://openreview.net/forum?id=BJlxm30cKm).
- <span id="page-14-14"></span>Nikolaos Tsilivis and Julia Kempe. What can the neural tangent kernel tell us about adversarial robustness? In Advances in Neural Information Processing Systems. Curran Associates, Inc., 2022.
- <span id="page-14-15"></span>Nikolaos Tsilivis, Jingtong Su, and Julia Kempe. Can we achieve robustness from data alone?  $arXiv$  $e\text{-}prints$ , pages arXiv–2207, 2022.
- <span id="page-14-9"></span>Catherine Wah, Steve Branson, Peter Welinder, Pietro Perona, and Serge Belongie. The caltech-ucsd birds-200-2011 dataset. 2011.
- <span id="page-14-6"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. CAFE: Learning to condense dataset by aligning features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 12196–12205, 2022.
- <span id="page-14-11"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. arXiv preprint arXiv:2303.04707, 2023.
- <span id="page-14-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018.
- <span id="page-14-8"></span>Ronald J Williams and Jing Peng. An efficient gradient-based algorithm for on-line training of recurrent network trajectories. Neural computation, 2(4):490–501, 1990.
- <span id="page-14-4"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. A comprehensive survey to dataset distillation.  $arXiv$ preprint arXiv:2301.07014, 2023.
- <span id="page-14-13"></span>Chia-Hung Yuan and Shan-Hung Wu. Neural tangent generalization attacks. In Marina Meila and Tong Zhang, editors, Proceedings of the 38th International Conference on Machine Learning, volume 139 of Proceedings of Machine Learning Research, pages 12230–12240. PMLR, 18–24 Jul 2021.
- <span id="page-14-7"></span>Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. arXiv preprint arXiv:2212.06152, 2022.
- <span id="page-14-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with gradient matching. In Proceedings of the International Conference on Learning Representations (ICLR), 2021a.
- <span id="page-14-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In Proceedings of the International Conference on Machine Learning (ICML), pages 12674–12685, 2021b.
- <span id="page-14-10"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the* IEEE/CVF Winter Conference on Applications of Computer Vision (WACV), 2023.
- <span id="page-14-2"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), 2022.

# Appendix A. Limitations

Algorithm Design The design of our method is primarily guided by intuitions and observations from empirical studies. Throughout the algorithm's development, we aim to strike a balance between scalability and effectiveness. Our approach currently involves tuning the ratio between the unrolling length and window size, scaling the unrolling length in accordance with the IPC number and GPU size. While this approach has demonstrated promise, we acknowledge that the current algorithmic choice might not represent the absolute optimal solution. Further research could investigate alternative algorithm designs.

Application to larger models and datasets A notable strength of our methodology is its versatility: it is compatible with all differentiable loss functions and network architectures, emphasizing its broad applicability. However, we only focus on illustrating the method's capabilities with standard benchmarks in the literature. This decision leaves a promising avenue for future work to apply and validate our method across various domains and tasks beyond image classification. It's also worth highlighting that while surrogate-based techniques are constrained to using the MSE loss to convexify the inner problem, our approach is agnostic to the specific loss function employed. This flexibility paves the way for our method's application in other realms, such as audio and text data distillation. GPU memory usage Despite the significant improvements introduced by RaT-BPTT, it still necessitates unrolling and backpropagating over several steps, which require storing all intermediate parameters in the GPU. Consequently, this method incurs substantial memory consumption, often exceeding that of directly training the model. For larger models, one might need to implement checkpointing techniques to manage memory usage effectively.

## Appendix B. Other Related Work

In this section, we discuss further works related to dataset distillation or hardness metrics.

Boosting: It is noteworthy that [Liu et al.](#page-12-14) [\(2023a\)](#page-12-14) has also identified challenges associated with retraining distilled datasets for varying budgets. Their proposed solution adopts a top-down approach, aiming to slim a large distilled dataset. In contrast, our method follows a bottom-up strategy, producing a modular dataset designed to accommodate various budgets. Moreover, one of our motivations is to address and study the intercorrelation problem.

Hardness metrics: One way to study generalization performance of neural nets is to understand which data points are "easy" or "hard" to learn for a neural network. There is an intimate relationship to *data pruning* which tries to understand and quantify which subsets of the data can be pruned with impunity, while maintaining the performance of the neural net when trained on the remainder<sup>[1](#page-15-0)</sup>. Inspired by the phenomenon of catastrophic forgetting, [Toneva et al.](#page-14-12) [\(2019\)](#page-14-12) are the first to study how the learning process of different examples in the dataset varies. In particular, the authors analyze whether some examples are harder to learn than others (examples that are forgotten and relearned multiple times through learning.) and define a *forgetting score* to quantify this process. To our knowledge, our work is the first to use this tool to understand how learning on distilled data differs from learning on full data, to identify bottlenecks. The idea to "enrich" the validation data during the data distillation process appears in [Liu et al.](#page-12-13) [\(2023b\)](#page-12-13), who chose more "representative" data to learn from, as determined by k-means clustering. To our knowledge, we are the first to propose learning from "harder-to-learn" data towards more efficient data distillation.

Extensions of Dataset Distillation: Beyond the conventional dataset distillation formula that aims to minimize generalization error, there have been advances in optimizing metrics for various objectives. These include dataset generation tailored for generalization attacks [Yuan and Wu](#page-14-13) [\(2021\)](#page-14-13), adversarial perturbations [Tsilivis and Kempe](#page-14-14) [\(2022\)](#page-14-14), and generating distilled data with an emphasis on robustness [Tsilivis et al.](#page-14-15) [\(2022\)](#page-14-15).

<span id="page-15-0"></span><sup>1.</sup> The boundary between coresets and data pruning is fluid; the former term is used for small subsets of the training set, while the latter usually refers to removing only a fraction of the training data, like 25% [Paul et al.](#page-13-12) [\(2021\)](#page-13-12).

# Appendix C. Experiments

## C.1 Experimental Details

Data Preprocessing: Leveraging a regularized ZCA transformation with a regularization strength of  $\lambda = 0.1$  across all datasets, our approach adheres to the methods established by prior studies [Deng](#page-11-1) [and Russakovsky](#page-11-1) [\(2022\)](#page-11-1); [Loo et al.](#page-12-0) [\(2022\)](#page-12-0); [Nguyen et al.](#page-13-0) [\(2021a,](#page-13-0)[b\)](#page-13-1); [Zhou et al.](#page-14-2) [\(2022\)](#page-14-2). We apply the inverse ZCA transformation matrix for distillation visualization, using the mean and standard deviation to reverse-normalize optimized data.

Models Following previous works, we use Instance Normalization for all networks for both training and evaluation.

Initialization In contrast to conventional real initialization widely used in nearly all previous works, we employ random initialization for distilled data, hypothesizing that there is a reduction in bias from such uninformative initialization. Data are initialized via a Gaussian distribution and normalized to norm 1. For RaT-BPTT, we note comparable performance and convergence between random and real initialization.

Label Learning Following previous works that leverage learnable labels, we optimize both the data and label for CIFAR10-IPC50, all IPCs for CIFAR100, CUB-200, and Tiny-ImageNet. We forego normalization for label probability, hence the labels retain their positive real value representation.

Training In addition to the RaT-BPTT algorithm, we incorporate meta-gradient clipping with an exponential moving average to counter gradient explosion. We find that the proper combination of normalizing initialization and learning rate (0.001 for Adam) is crucial for successful distillation image training. While using instance normalization, an image scaled by  $\alpha$  leads to meta-gradient scaling by  $\frac{1}{\alpha}$ . As a result, one should use an  $\alpha$  times larger learning rate for Adam or  $\alpha^2$  times larger for SGD to achieve the same optimization trajectory. We thus adopt a similar initialization scale to that of neural network training (normalized to norm 1), combined with a standard learning rate of 0.001 when using Adam. To maintain meta-gradient stability, we employ batch sizes of 5,000 for CIFAR-10 and CIFAR-100, 3,000 for CUB-200, and 1,000 for Tiny-ImageNet. Note that one should aim to further increase the batch size for Tiny-ImageNet until all the GPU memory is used.

Hyperparameters In an effort to minimize tuning requirements, we adhere to a standard baseline across all configurations. Specifically, we utilize the Adam optimizer for both the inner loop (network unrolling) and the outer loop (distilled dataset optimization) with learning rates uniformly set to 0.001. We refrain from applying weight decay or learning rate schedules that are used in prior works [Loo et al.](#page-13-9) [\(2023\)](#page-13-9); [Zhou et al.](#page-14-2) [\(2022\)](#page-14-2).

Evaluation We evaluate our optimized data using a seperate held-out test dataset (the test set in the corresponding dataset). We adopt the same data augmentation as in previous work [Deng and](#page-11-1) [Russakovsky](#page-11-1) [\(2022\)](#page-11-1). For depth 3 convolutional networks, we train using Adam with a learning rate of 0.001. No learning rate schedule is used.

Code and Checkpoints The code and checkpoints for RaT-BPTT could be found at [https:](https://github.com/fengyzpku/Simple_Dataset_Distillation) [//github.com/fengyzpku/Simple\\_Dataset\\_Distillation](https://github.com/fengyzpku/Simple_Dataset_Distillation)

### C.2 Ablations on curriculum

Our RaT-BPTT implementation hinges on tuning two hyperparameters: unrolling length and backpropagation window size. This section presents an ablation study exploring these parameters for CIFAR-10, IPC10.

#### Unrolling length

We initially fix the window size at 40 while varying the unrolling length. Notably, unrolling length governs the long-term dependencies we can capture within the inner loop. Figure [11](#page-17-0) reveals that a moderate unrolling length, between twice and five times the window size, yields similar performance. However, disproportionate unrolling, as seen with a window size of 40 and unrolling length of 300, detrimentally affects performance.

<span id="page-17-0"></span>Image /page/17/Figure/0 description: The image contains two line graphs side-by-side, both plotting accuracy against steps. The left graph shows the effect of 'unroll' values (80, 120, 200, 300) on accuracy. The right graph shows the effect of 'window' values (40, 60, 100) on accuracy. Both graphs have the x-axis labeled 'Steps' ranging from 0 to 35000, and the y-axis labeled 'Accuracy' ranging from 65.0 to 69.0. In the left graph, the 'unroll 120' line generally shows higher accuracy than 'unroll 80', 'unroll 200', and 'unroll 300' at higher step counts. In the right graph, the 'window 60' and 'window 100' lines show similar and higher accuracy compared to 'window 40' at higher step counts.

Figure 11: Left Test accuracy during distillation for different unrolling length of 80, 120, 200, 300 with fixed window size 40. CIFAR-10, IPC10. Right Test accuracy during distillation for different window size in 40, 60, 100 with fixed unrolling length 200.

##### Window size

Next, we fix the unrolling length at 200 and experiment with window sizes of 40, 60, and 100. Figure [11](#page-17-0) shows the latter two sizes yield comparable performance. In RaT-BPTT, GPU memory consumption is directly proportional to the window size, thus a window size of 60, with an unrolling length of 200 steps, emerges as an optimal balance. As such, we typically maintain a window size to unrolling length ratio of around 1:3.

In our implementation, we employ a window size and unrolling length of (60, 200) for CIFAR-10 IPC1 and CUB-200, (80, 250) for CIFAR-10 IPC10, and (100, 300) for all other datasets.

#### C.3 Other Architectures

We further assessed our method across various architectures to demonstrate its universality. It is noteworthy that our approach is already effective across different widths of the convolutional networks (narrow and wide) we used. Additionally, we conducted tests using the standard ResNet-18, both training it from scratch and transferring from the distilled dataset. To our knowledge, we are the pioneers in applying direct distillation to a standard-sized network like ResNet-18. Direct training of ResNet-18 yields an accuracy of 52.7% on CIFAR10 with IPC10, while the transfer from a 3-layer CNN results in 49.2%. These transfer findings align with prior observations documented in [Deng](#page-11-1) [and Russakovsky](#page-11-1) [\(2022\)](#page-11-1).

### C.4 Visualization

We incorporate visualizations for IPC10 on CIFAR-10, representing standardly trained (Figure [12\)](#page-18-0), weakly boosted (Figure [14\)](#page-20-0), and strongly boosted images (Figure [13\)](#page-19-0). Upon inspection, the images from both boosted categories appear more diverse compared to their standard counterparts.

<span id="page-18-0"></span>Image /page/18/Picture/1 description: This image displays a grid of 100 images, arranged in 10 rows and 10 columns. Each image is labeled with a category name. The first row shows images labeled 'airplane'. The second row shows images labeled 'automobile'. The third row shows images labeled 'bird'. The fourth row shows images labeled 'cat'. The fifth row shows images labeled 'deer'. The sixth row shows images labeled 'dog'. The seventh row shows images labeled 'frog'. The eighth row shows images labeled 'horse'. The ninth row shows images labeled 'ship'. The tenth row shows images labeled 'truck'. Each category has 10 images associated with it.

Figure 12: Visualization for RaT-BPTT standardly trained on CIFAR-10 with IPC10.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each column is dedicated to a specific category of object, with the category name labeled above each column. The categories, from left to right, are: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. Within each category column, the 10 images are identical representations of that object. For example, the first column shows 10 identical images of airplanes, the second column shows 10 identical images of automobiles, and so on, down to the last column which shows 10 identical images of trucks. The images themselves appear to be low-resolution or stylized representations of these objects.

Figure 13: Visualization for RaT-BPTT with strong boosting (Boost-DD). CIFAR-10 with IPC10.

<span id="page-20-0"></span>Image /page/20/Picture/1 description: The image displays a grid of 100 images, arranged in 10 rows and 10 columns. Each image is a small, blurry representation of a common object or animal. The rows are labeled with the names of the objects/animals they contain: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. Each label appears above the corresponding row of images. For example, the first row contains 10 images labeled 'airplane', the second row contains 10 images labeled 'automobile', and so on, down to the last row which contains 10 images labeled 'truck'.

Figure 14: Visualization for RaT-BPTT with weak boosting (Boost-DD). CIFAR-10 with IPC10.

# Appendix D. Hardness Analysis

## D.1 Dissecting the Data

Now, we attempt to understand what is bottlenecking the gains when scaling up the distilled data from say IPC1 to IPC50 (see Table 1) by analyzing the performance of dataset distillation on examples that are easy or hard to learn, using a *hardness score* to stratify the data. We first leverage the forgetting score [Toneva et al.](#page-14-12) [\(2019\)](#page-14-12) as an empirical implementation of the hardness score as it characterizes the difficulty of learning a datapoint along the training trajectory.

**Forgetting score.** For each data point x and a network trained for T epochs, we say x has a forgetting event at time t if x is correctly classified at time t and misclassified at time  $t + 1$ . The forgetting score of a data point is the sum of its forgetting events before time  $T$ , averaged over 10 random initializations. [Toneva et al.](#page-14-12) [\(2019\)](#page-14-12) show that based on the forgetting score a significant amount of training data can be omitted, without sacrificing test performance.

We first compute the forgetting score of the original network for each training data point. We then stratify the accuracy by forgetting score and compare it for the original network and the network trained on distilled data (Figure [10\)](#page-10-0). Notice that the highest boost in performance, especially for the easy datapoints (score 0) comes from simply having one image per class (IPC1). Further, one would have hoped that increasing the number of images per class would help distill more of the tail, enabling models to generalize better to data points with larger forgetting scores. We notice that this happens to some extent till a score of 4, but after that despite there being a lot of datapoints with a larger hardness score (Figure [10,](#page-10-0) bottom) IPC50 seems to yield minimal marginal improvements. This suggests that future works might benefit from focusing on how one can distill datapoints with larger forgetting scores.

### D.2 Hardness Sampler

We present an initial approach to enhance learning on challenging examples through a "hardness sampler" that modifies the data batch distribution. Specifically, our objective is to enrich validation batches with more challenging examples, concentrating primarily on those lying mid-way between the extremes of very easy and very hard examples. This approach is inspired by the parabolic shape depicted in Figure [10.](#page-10-0)

However, the calculation of the forgetting score is often computationally demanding and thus may not be practical for all applications, especially as part of the outer loop in dataset distillation. Moreover, Forgetting score has only been defined for and analysed on networks that are trained on the data that is being scored. To address these challenges, we propose an adaptive hardness score that is both efficient and versatile. This score is computed based on the disagreement in predictions [Feng et al.](#page-11-12) [\(2020\)](#page-11-12) across 8 randomly trained networks using the current distilled dataset. To stay relevant to the evolving challenges, this score is updated adaptively every 50 epochs.

Based on this adaptive hardness score, we down-weight the easiest and hardest examples by giving the following weight w to a sample x with hardness score  $HS(x) \in$  $\{0, \ldots, 8\}$ :

$$
w(x) = thr + abs(HS(x) - 4),
$$

where *thr* is a threshold which we set to either 1 or 4. For

<span id="page-21-0"></span>Image /page/21/Figure/11 description: The image is a line graph showing the accuracy of three different methods: Quaprob 4, Quaprob 1, and Standard, over 100,000 steps. The x-axis represents the number of steps, ranging from 30,000 to 100,000. The y-axis represents the accuracy, ranging from 73.00 to 75.00. All three lines show an increasing trend in accuracy as the number of steps increases. The Quaprob 1 line generally shows the highest accuracy, followed by Quaprob 4, and then the Standard method. At 100,000 steps, Quaprob 1 is around 74.6%, Quaprob 4 is around 74.5%, and Standard is around 74.25%.

Figure 15: Test performance of hardness sampler versus standard for thresholds 1 and 4. We start to sample at 25K steps since we believe it becomes more relevant in the later stages of distillation. Setting: CIFAR10, IPC50, RaT-BPTT.

each update of the meta-gradient we sample from a training data point x proportional to  $w(x)$ , which upweights medium-hard examples the most. Figure [15](#page-21-0) demonstrates a notable performance

improvement for the IPC50 distillation on CIFAR10, and hints that this direction might be fruitful for future work to pursue.