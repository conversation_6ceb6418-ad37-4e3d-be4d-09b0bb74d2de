# <span id="page-0-0"></span>Enhancing Dataset Distillation via Non-Critical Region Refinement

Minh-<PERSON><PERSON>ran $^1$ , Trung Le $^1$ , <PERSON><PERSON>-<PERSON> $^2$ , <PERSON><PERSON>-<PERSON><PERSON> Do $^1$ , <PERSON><PERSON>ung $^1$ <sup>1</sup>Monash University,  ${}^{2}$ The University of Melbourne

> {tuan.tran7,trunglm,toan.do,dinh.phung}@monash.edu <EMAIL>

## Abstract

*Dataset distillation has become a popular method for compressing large datasets into smaller, more efficient representations while preserving critical information for model training. Data features are broadly categorized into two types: instance-specific features, which capture unique, fine-grained details of individual examples, and classgeneral features, which represent shared, broad patterns across a class. However, previous approaches often struggle to balance these features—some focus solely on classgeneral patterns, neglecting finer instance details, while others prioritize instance-specific features, overlooking the shared characteristics essential for class-level understanding. In this paper, we introduce the Non-Critical Region Refinement Dataset Distillation (NRR-DD) method, which preserves instance-specific details and fine-grained regions in synthetic data while enriching non-critical regions with class-general information. This approach enables models to leverage all pixel information, capturing both feature types and enhancing overall performance. Additionally, we present Distance-Based Representative (DBR) knowledge transfer, which eliminates the need for soft labels in training by relying on the distance between synthetic data predictions and one-hot encoded labels. Experimental results show that NRR-DD achieves state-of-the-art performance on both small- and large-scale datasets. Furthermore, by storing only two distances per instance, our method delivers comparable results across various settings. The code is available at* [https://github.com/tmtuan1307/](https://github.com/tmtuan1307/NRR-DD) [NRR-DD](https://github.com/tmtuan1307/NRR-DD)*.*

## 1. Introduction

Dataset distillation, also known as dataset condensation [\[2,](#page-8-0) [21,](#page-8-1) [26,](#page-8-2) [27,](#page-8-3) [32\]](#page-9-0), has gained considerable attention as an effective method for compressing large datasets into smaller, more efficient representations, while preserving essential information critical for model training. By generating compact, high-quality data representations, dataset distillation reduces both storage requirements and computational costs associated with training on full-sized datasets [\[5,](#page-8-4) [8,](#page-8-5) [17\]](#page-8-6). This compression is particularly valuable in resource-constrained environments, such as edge devices or memory-limited systems, where training on large datasets is often impractical  $[1,9,23]$  $[1,9,23]$  $[1,9,23]$ . The primary goal of dataset distillation is to achieve high model performance while drastically reducing the amount of data that needs to be stored and processed [\[26\]](#page-8-2). During this process, synthetic data instances are generated to capture the key properties of the original data, enabling models to generalize effectively using only a fraction of the dataset  $[2, 32]$  $[2, 32]$  $[2, 32]$ .

In recent years, several methods have been proposed to address this task, including Gradient Matching [\[10](#page-8-10)[,32\]](#page-9-0), Distribution Matching  $[31,33]$  $[31,33]$ , Trajectory Matching  $[2,6]$  $[2,6]$ , and more recent approaches for large-scale datasets [\[3,](#page-8-12) [21,](#page-8-1) [27\]](#page-8-3). However, designing an efficient distillation method remains a challenge, as it must capture both class-general features, which represent shared patterns, and instance-specific features, which highlight unique, fine-grained details. Previous methods often fall short by emphasizing one of these feature types over the other. Approaches focusing on classgeneral features  $[2,3,27,32,33]$  $[2,3,27,32,33]$  $[2,3,27,32,33]$  $[2,3,27,32,33]$  $[2,3,27,32,33]$  risk losing crucial instancespecific information, which hinders fine-grained generalization. Conversely, methods that prioritize instance-specific features [\[7,](#page-8-13) [21\]](#page-8-1) may neglect broader class patterns, leading to suboptimal class-level representation. To address these challenges, we introduce the Non-Critical Region Refinement Dataset Distillation (NRR-DD) method, which consists of three key stages:

- (i) Critical-based Initial Data Discovery: This stage involves selecting diverse and significant patches from the original dataset, which are then combined to capture instance-specific features.
- (ii) Non-Critical Region Refinement (NRR): In this stage, we apply Class Activation Mapping (CAM) [\[19,](#page-8-14) [34\]](#page-9-3) to identify critical and non-critical regions in the images. The model preserves the critical regions, which contain fine-grained, instance-specific features, while refining the non-critical regions with more class-

<span id="page-1-1"></span>general information. By balancing these two feature types, NRR enhances the dataset's comprehensiveness, improving both generalization and performance.

(iii) Knowledge Transfer via Relabeling: After training, the synthetic images are relabeled and used to transfer knowledge to a student model.

Additionally, recent research [\[3,](#page-8-12) [21,](#page-8-1) [27\]](#page-8-3) has highlighted the importance of soft labels generated by a pretrained model in enhancing dataset distillation performance, particularly for large-scale, high-resolution datasets like ImageNet1k. However, this approach incurs substantial memory overhead; for instance, with 200 images per class (IPC) in ImageNet1k, it can require over 120 GB of storage [\[27\]](#page-8-3).

To address this challenge, we propose a novel Distance-Based Representative (DBR) knowledge transfer technique that eliminates the need for traditional soft labels. DBR employs a distance-based approach to measure the discrepancy between predictions on synthetic data and one-hot encoded labels, simplifying the training process and reducing label storage requirements. For example, our method requires only 0.2 GB to store ImageNet1k (200 IPC), achieving a 500× reduction in storage while still delivering comparable results. By integrating DBR with NRR, our method enhances dataset distillation by capturing essential features while minimizing training complexity. This results in more compact and efficient datasets, well-suited for a variety of training environments. Figure [1](#page-1-0) illustrates the differences between our method and two popular large-scale dataset distillation techniques, RDED  $[21]$  and SRe<sup>2</sup>L. It is evident that RDED focuses on instance-specific features without refinement, while  $SRe<sup>2</sup>L$  updates all pixels to capture classgeneral features, often at the expense of fine-grained details. In contrast, our NRR-DD method effectively preserves finegrained details by updating only non-critical pixels, while still capturing class-general features.

Contributions. Our major contributions are summarized as follows:

- We introduce the Non-Critical Region Refinement Dataset Distillation (NRR-DD) framework, which consists of three key stages: Critical-based Initial Data Discovery (CIDD), Non-Critical Region Refinement (CRR), and Relabeling. This approach generates synthetic data that captures both instance-specific finegrained features and class-general patterns, significantly enhancing performance.
- We propose the Distance-Based Representative (DBR) method for knowledge transfer, eliminating the need for soft labels and drastically reducing memory requirements. Specifically, our method reduces storage requirements by *500-fold* compared to soft labels on ImageNet1k, while recovering up to 80% of the full performance (see Table [4\)](#page-7-0).
- Experimental results demonstrate that our NRR

<span id="page-1-0"></span>Image /page/1/Figure/8 description: This image compares three different image refinement methods: SRe2L, RDED, and NRR-DD. Each method is shown with an initial image, an image refining process, and a final image. The SRe2L method starts with a noisy image and applies 'Full Pixel Refinement' to produce a stylized final image of a dog. The RDED method starts with a collage of dog images and applies 'No Refinement', resulting in the same collage as the final image. The NRR-DD method also starts with a collage of dog images and applies 'Non-Critical Region Refinement', indicated by a heatmap, to produce a final image that is a refined collage of dog images.

Figure 1. Comparison of our method with two popular frameworks,  $SRe<sup>2</sup>L$  [\[27\]](#page-8-3) and RDED [\[21\]](#page-8-1), for generating synthetic datasets. RDED selects high-confidence, easily classifiable images, while our method focuses on low-confidence, harder-toclassify samples, which helps reduce overfitting and improve model accuracy. Additionally, RDED targets instance-specific features without refinement, and SRe<sup>2</sup>L updates all pixels to capture class-general features, often at the expense of fine details. In contrast, our NRR-DD method preserves fine-grained details while capturing class-general features by updating only non-critical pixels.

method achieves state-of-the-art performance on both small- and large-scale datasets. Additionally, by storing only two distances per instance, it achieves comparable results across various settings.

## 2. Related Works

Dataset Distillation. Several dataset distillation methods have been proposed recently  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$  $[2, 3, 21, 27, 32, 33]$ , which can be classified into two main categories. The first category, Class-General Feature-Based Methods, aims to capture class-wide features. For example, Gradient Matching [\[10,](#page-8-10) [32\]](#page-9-0) generates synthetic data by matching gradients across all samples in a class, while Distribution Matching [\[16,](#page-8-15) [31,](#page-9-1) [33\]](#page-9-2) uses distribution prediction. Trajectory Matching [\[2,](#page-8-0) [6\]](#page-8-11) aligns training trajectories of original and synthetic data, and  $SRe^{2}L$  [\[27\]](#page-8-3) recovers Batch Normalization statistics to capture class-general features. The second category, Instance-Specific Feature-Based Methods  $[7, 21]$  $[7, 21]$  $[7, 21]$ , includes approaches such as RDED  $[21]$ , which extracts high-confidence patches and combines them to create synthetic data, and MDiff [\[7\]](#page-8-13), which utilizes a diffusion model to generate fine-grained images tailored to the task. However, each approach has its limitations: methods focused on class-general features risk losing crucial instancespecific details, hindering fine-grained generalization, while instance-specific methods may neglect broader class patterns, leading to suboptimal class-level representation.

Large-Scale Dataset Distillation via Soft-Label Knowl-

<span id="page-2-4"></span>edge Transfer. Recently, several methods have been proposed for large-scale datasets like ImageNet1k [\[3,](#page-8-12) [21,](#page-8-1) [27\]](#page-8-3). However, all of these approaches require storing the soft labels of each augmented data point for training new student models, leading to significant memory storage overhead. For example, with ImageNet1k, storing the soft labels for 50 Images-Per-Class (IPC) requires approximately 30 GB, and for 200 IPC, over 120 GB of storage [\[27\]](#page-8-3). This highlights the need for novel knowledge transfer techniques to reduce memory storage requirements.

## 3. Proposed Method

In this section, we first provide the necessary preliminaries for the Dataset Distillation Method (Section [3.1\)](#page-2-0), followed by an introduction to our Non-Critical Region Refinement Dataset Distillation (NRR-DD) framework. The framework consists of three key stages: (i) Critical-based Initial Data Discovery (Section [3.2\)](#page-2-1), which selects diverse, important patches to capture instance-specific features; (ii) Non-Critical Region Refinement (Section [3.3\)](#page-3-0), where Class Activation Mapping (CAM) [\[19,](#page-8-14) [34\]](#page-9-3) is employed to identify and refine both critical and non-critical regions, preserving fine-grained details while enriching non-critical areas with class-general information; and (iii) Knowledge Transfer via Relabeling (Section [3.4\)](#page-4-0), in which synthetic images are relabeled for knowledge transfer to a student model. The overall architecture is shown in Figure [2,](#page-3-1) and the pseudo code can be found in Algorithm [1.](#page-2-2)

<span id="page-2-0"></span>

### 3.1. Preliminaries

Consider a training dataset  $\mathcal{D} = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}_{i=1}^m$ , where each input  $x_i \in \mathbb{R}^{c \times h \times w}$  represents a sample, and  $y_i \in$  $\{1, \ldots, K\}$  denotes its label. Let  $\mathcal{T} = \mathcal{T}_{\theta_{\mathcal{T}}}$  be a pretrained model on D. The goal of dataset distillation is to generate a synthetic dataset  $\tilde{\mathcal{D}} = \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^n$  (with  $n \ll m$ ) that retains the essential information from  $D$ , enabling a new model S to achieve performance comparable to that of  $\mathcal T$ .

<span id="page-2-1"></span>

### 3.2. Critical-based Initial Data Discovery

In this section, instead of using Gaussian noise to generate synthetic data for  $M$ , we propose training-free techniques to more effectively select initial data. The motivation behind this approach is that real data inherently contains fine-grained, instance-specific features, which are crucial for training on large-scale datasets [\[7,](#page-8-13) [12–](#page-8-16)[14,](#page-8-17) [21,](#page-8-1) [25\]](#page-8-18).

Given the training dataset  $\mathcal{D} = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}_{i=1}^m$ , for each data example  $(x, y) \in \mathcal{D}$ , we first compute the class activa-tion mapping (CAM) [\[34\]](#page-9-3), producing a matrix  $C(\mathbf{x}, \mathbf{y})$  for the image  $x$  and class  $y$ :

<span id="page-2-3"></span>
$$
C(\boldsymbol{x}, \boldsymbol{y}) = \sum_{k} \boldsymbol{w}_{k}^{\boldsymbol{y}} \mathcal{T}_{k}(\boldsymbol{x}, \boldsymbol{y}), \qquad (1)
$$

<span id="page-2-2"></span>Algorithm 1: NRR-DD **Input:** Pre-trained model  $\mathcal{T}_{\theta_{\mathcal{T}}}$ , training set  $\mathcal{D} = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}_{i=1}^m$ **Output:** Synthetic dataset  $\tilde{\mathcal{D}} = \{(\tilde{\boldsymbol{x}}_i, \tilde{\boldsymbol{y}}_i)\}_{i=1}^n$ 1 Initial  $\tilde{\mathcal{D}} = \emptyset$ ; /\* Critical-based Initial Data Discovery  $\star/$ 2 foreach  $(x, y) \sim \mathcal{D}$  do 3 Calculate CAMs matrix  $C(x)$  for x; 4 Crop x into k patches  $\{p_1, \ldots, p_k\};$  $\mathbf{s}$  Select the top t patches with the highest values in the CAMs matrix; 6 Resize and store them in the patch pool  $P$ ; <sup>7</sup> foreach p ∼ D do Compute the score  $s(p) = \mathcal{L}_{ce}(p);$ 9 Select the top  $q = \beta \times IPC$  patches with the lowest scores; 10 for  $i = 1$  to  $IPC$  do 11 Combine  $\beta$  patches into  $\tilde{x}$ , store  $(\tilde{x}, \tilde{y})$  in  $\tilde{\mathcal{D}}$  $/*$  Non-Critical Region Refinement  $*/$ 12 **foreach**  $(\tilde{x}_{\text{org}}, \tilde{y}_{\text{org}}) \sim \mathcal{D}$  do <sup>13</sup> for I *iterations* do 14  $\Big| \Big| (\tilde{x}_{\text{aug}}, \tilde{y}_{\text{aug}}) \sim \tilde{\mathcal{D}};$ 15  $\vec{x}_{mix} = A(\tilde{x}_{org}, \tilde{x}_{aug});$ 16 | Update  $\tilde{x}_{org}$  by Eq. [11;](#page-5-0) 17  $\hat{\mathbf{y}}_{\text{soft}} = \mathcal{T}(\tilde{\mathbf{x}}_{\text{mix}});$  $\mathbf{18} \quad \big| \quad d_\mathrm{org}^T = \mathcal{L}_{ce}(\tilde{\bm{y}}_\mathrm{soft}, \tilde{\bm{y}}_\mathrm{org});$  $\mathbf{19} \quad \big| \quad d_{\text{aug}}^T = \mathcal{L}_{ce}(\tilde{\textbf{y}}_{\text{soft}}, \tilde{\textbf{y}}_{\text{aug}});$ 20 Store  $(\tilde{\bm{x}}_{\text{org}},(\tilde{\bm{y}}_{\text{org}},\tilde{\bm{y}}_{\text{aug}}), (d_{\text{org}}^T, d_{\text{aug}}^T))$  in  $\tilde{\mathcal{D}}$ ; /\* Distance-Based Representative Knowledge Transfer  $\star/$ 21 foreach  $(\tilde{x}_{org},(\tilde{y}_{org},\tilde{y}_{aug}),({d_{org}^T},d_{aug}^T)) \sim \tilde{\mathcal{D}}$  do 22 | Update new model  $S$  by Eq. [9;](#page-5-1)

where  $w_k^y$  represents the  $k^{\text{th}}$  weight in the final classification head for class  $y$ , and  $\mathcal{T}_k$  denotes the  $k^{\text{th}}$  feature map from the final layers of the model.

Next, the images  $x$  are randomly cropped to extract multiple patches, and the top  $t$  patches, which contain the highest values in the class activation map, are selected. These patches are then resized to the full image size and stored in the patch pool  $P$ . Since the selection is based on the highest values in the class activation map, the chosen patches capture the most important information from the original images.

For each patch  $p$  in  $P$ , we feed it into  $T$  to obtain a confidence score  $s = \mathcal{T}(\boldsymbol{p})$ , which represents the highest prediction probability. Unlike RDED [\[21\]](#page-8-1), which selects patches with the highest scores, we choose the top  $g = \beta \times IPC$ patches with the lowest scores, where  $\beta = 1, 4, 9, \dots$  denotes the number of patches used to form a single synthetic image, and *IPC* specifies the number of images per class. Notably, our strategy of selecting the lowest-scoring patches

<span id="page-3-3"></span><span id="page-3-1"></span>Image /page/3/Figure/0 description: This figure illustrates a three-part process for improving image classification. Part (a), 'Critical-Based Data Discovery,' shows how random crops and crops with high CAM ratios are analyzed to identify low-confidence regions, visualized as data points in a scatter plot. Part (b), 'Non-Critical Region Refinement,' details an image refining process using a convolutional neural network. It involves generating pixel-wise gradients and a non-critical mask, which are then multiplied to refine the image. Part (c), 'Distance-based Representative,' depicts a method for minimizing a distance-based loss function (Ldbr) by comparing original and augmented image representations (one-hot vectors and soft labels) in a feature space.

Figure 2. The architecture of our NRR-DD consists of three key stages: (i) Critical-based Initial Data Discovery (Section [3.2\)](#page-2-1), which selects patches with a high CAM ratio but low confidence level to capture instance-specific features; (ii) Non-Critical Region Refinement (Section [3.3\)](#page-3-0), where CAM [\[34\]](#page-9-3) is used to identify and refine both critical and non-critical regions, preserving fine-grained details while enriching non-critical areas with class-general information; (iii) Knowledge Transfer, which aims to minimize the distance between  $S(\tilde{x}_{mix})$  (student prediction) and  $\mathcal{T}(\tilde{x}_{\text{mix}})$  (pretrained teacher prediction or soft label) by reducing the distance between  $d_{\text{org}}^T$  and  $d_{\text{org}}^S$ , as well as between  $d_{\text{aug}}^T$ and  $d_{\text{aug}}^S$ . By storing only the two values,  $d_{\text{org}}^T$  and  $d_{\text{aug}}^T$ , the new model can effectively mimic the performance of the pretrained one.

identifies the hardest-to-learn samples, while the method of selecting patches with the highest values in the class activation map ensures that the patches contain important information. This provides more opportunity and flexibility in the next phase to further refine the chosen synthetic images. Figure [1](#page-1-0) visualizes the images selected by both the highestand lowest-scoring strategies.

Finally, similar to RDED [\[21\]](#page-8-1), we construct each synthetic image by combining  $\beta$  patches. The selected patches are resized to  $1/\beta$  of their original size and then combined to create  $IPC$  synthetic images per class, all of which are stored in the dataset  $\tilde{\mathcal{D}}$ . Unlike RDED [\[21\]](#page-8-1), our synthetic images in  $D$  are refined to include both *fine-grained*, *instance-specific features* and *class-general features*.

<span id="page-3-0"></span>

### 3.3. Non-Critical Region Refinement

In contrast to RDED  $[21]$ , our synthetic images in  $D$  are refined to incorporate both *detailed, instance-specific* features and *broader, class-general* features. Specifically, for each  $(\tilde{x}, \tilde{y}) \in \tilde{\mathcal{D}}$ , the image  $\tilde{x}$  is refined according to the following loss function:

$$
\mathcal{L}_C = \mathcal{L}_{ce}(\mathcal{T}(\tilde{\boldsymbol{x}}), \tilde{\boldsymbol{y}}) + \alpha_{bn} \mathcal{L}_{bn}(\mathcal{T}(\tilde{\boldsymbol{x}})),
$$
 (2)

where  $\alpha_{bn}$  is a parameter.

In this framework,  $\mathcal{L}_{ce}$  represents the cross-entropy (CE) loss, which aims to move  $\tilde{x}$  with the smallest confidence score into the teacher's high-confidence regions. The batch normalization regularization  $(\mathcal{L}_{bn})$  [\[22,](#page-8-19) [24,](#page-8-20) [27\]](#page-8-3), a standard DFKD loss, aligns the mean and variance at the BatchNorm layer with its running mean and variance:

$$
\mathcal{L}_{bn} = \sum_{l} \left( \|\mu_l(\tilde{\boldsymbol{x}}) - \mu_l\| + \|\sigma_l^2(\tilde{\boldsymbol{x}}) - \sigma_l^2\| \right), \quad (3)
$$

where  $\mu_l(\tilde{x})$  and  $\sigma_l^2(\tilde{x})$  are the mean and variance of the  $l$ -th <code>BatchNorm</code> layer of  ${\cal T},$  and  $\mu_l$  and  $\sigma_l^2$  are the running mean and variance of the  $l$ -th BatchNorm layer in  $T$ .

Non-Critical Region. A naive approach to refining  $\tilde{x}$ would involve optimizing all pixels in  $\tilde{x}$  using the gradient of  $\mathcal{L}_C$ . However, this would lead to significant changes in the images, resulting in the loss of fine-grained, instancespecific features, as shown in Figure [1.](#page-1-0) Instead, we propose the *Non-Critical Region Refinement Dataset Distillation* (NRR-DD) method, which preserves instance-specific and fine-grained regions in the synthetic data while enriching non-critical regions with more class-general information. This approach enables our models to utilize all pixel information to capture both types of features, thereby enhancing overall performance.

<span id="page-3-2"></span>Given a synthetic image  $\tilde{x}$  with label  $\tilde{y}$ , we use CAM to create a *non-critical mask*  $M$  of the same size as  $\tilde{x}$ . This mask assigns low or zero values to high-importance pixels in  $\tilde{x}$  and higher values to less important pixels. The objective is to control pixel updates in  $\tilde{x}$  by limiting updates to important pixels in order to preserve instance-specific features, while allowing less important pixels to update more significantly, thus enhancing the learning of class-general features. The process is detailed as follows.

We first generate the CAM matrix C of  $(\tilde{x}, \tilde{y})$  using Eq. [1.](#page-2-3) Subsequently, we calculate the pixel-wise non-

<span id="page-4-2"></span><span id="page-4-1"></span>Image /page/4/Figure/0 description: This image displays a grid of images, organized into three rows labeled "SRe
2L", "RDED", and "NRR-DD". Each row contains six columns of images, with the first two columns showing English Springer Spaniels and the remaining four columns showing people holding fish. The "SRe
2L" row features stylized, almost painterly depictions of dogs and people with fish. The "RDED" row shows clear, realistic photographs of dogs and people with fish. The "NRR-DD" row presents images that appear to be generated or reconstructed, with some distortion and pixelation, showing dogs and people with fish.

Figure 3. Visualization of images from the 'tench' and 'English springer' classes synthesized using various dataset distillation methods, including  $SRe^{2}L$  [\[27\]](#page-8-3), RDED [\[21\]](#page-8-1), and our NRR-DD. For additional visualizations, please refer to the **Supplementary Material**.

critical mask  $M$  matrix using the following formula:

$$
M = \max\{0, \epsilon - C\}.
$$
 (4)

Here,  $\epsilon$  serves as the upper threshold for the method. For any value c in C that exceeds  $\epsilon$ , its corresponding value in M will be set to 0; otherwise, the value will be  $\epsilon - c$ . Since higher class activation values correspond to lower non-critical scores, the matrix  $M$  will be used to weight the gradient update for each pixel in  $\tilde{x}$ . With each gradient update, the image  $\tilde{x}$  is updated as follows:

$$
\tilde{\boldsymbol{x}} = \tilde{\boldsymbol{x}} - M \times \eta \nabla_{\tilde{\boldsymbol{x}}} \mathcal{L}_{\mathcal{C}},\tag{5}
$$

where  $\nabla_{\tilde{x}} \mathcal{L}_{\mathcal{C}}$  represents the gradient of the loss function  $\mathcal{L}_{\mathcal{C}}$ with respect to the image  $\tilde{x}$ .

<span id="page-4-0"></span>

### 3.4. Knowledge Transfer via Relabeling

Soft-Label Knowledge Transfer. Previous methods [\[3,](#page-8-12)[21,](#page-8-1) [27\]](#page-8-3) store soft labels generated by a pretrained teacher model  $T$  to train a student model S by minimizing the Kullback-Leibler (KL) divergence between the student model's predictions and the teacher's soft labels. These soft labels are generated through augmentation techniques such as Cut-Mix  $[28]$  and Mixup  $[30]$ .

Given a pair of original image  $(\tilde{x}_{org}, \tilde{y}_{org})$  and augmented image  $(\tilde{x}_{\text{aug}}, \tilde{y}_{\text{aug}})$  in the synthetic dataset  $\tilde{\mathcal{D}}$ , we apply CutMix or Mixup to create the mixed image and compute its soft label as:

$$
\tilde{\boldsymbol{x}}_{\text{mix}} = A(\tilde{\boldsymbol{x}}_{\text{org}}, \tilde{\boldsymbol{x}}_{\text{aug}}) \tag{6}
$$

$$
\tilde{\mathbf{y}}_{\text{soft}} = \mathcal{T}(\tilde{\mathbf{x}}_{\text{mix}}). \tag{7}
$$

Here, A represents an augmentation method such as Cut-Mix [\[28\]](#page-8-21) or Mixup [\[30\]](#page-9-4), and  $\tilde{y}_{soft}$  denotes the soft labels produced by the mixed image  $\tilde{x}_{mix}$ .

Therefore, for each pair of original image  $(\tilde{x}_{org}, \tilde{y}_{org})$ and augmented image  $(\tilde{x}_{\text{aug}}, \tilde{y}_{\text{aug}})$ , we need to store the indices of the two images in the synthetic dataset, the specification of the mixing method  $A$  used to construct the mixed image  $\tilde{x}_{mix}$ , and the soft label  $\tilde{y}_{soft}$ . However, this approach requires substantial memory to store the soft labels, especially when large datasets and numerous augmentations are involved.

Distance-Based Representative Knowledge Transfer. To address the memory limitations of storing soft labels, we propose a more memory-efficient approach. Instead of storing the soft labels  $\tilde{\mathbf{y}}_{\text{soft}}$ , each of which consists of 1,000 real numbers for ImageNet1K, we store only two real numbers representing the cross-entropy (CE) divergences between the soft label and the one-hot vectors of  $\tilde{y}_{\text{aug}}$  and  $\tilde{y}_{\text{org}}$ .

Specifically, for each pair of an original image  $(\tilde{x}_{org}, \tilde{y}_{org})$  and an augmented image  $(\tilde{x}_{aug}, \tilde{y}_{aug})$  in the synthetic dataset  $\tilde{\mathcal{D}}$ , we apply CutMix or Mixup to create the mixed image  $\tilde{x}_{mix}$  and compute its soft label  $\tilde{y}_{soft}$  using the teacher model  $T$ . We then calculate and store the crossentropy (CE) divergences between the soft label and the one-hot vectors of  $\tilde{y}_{\text{aug}}$  and  $\tilde{y}_{\text{org}}$  as follows:

$$
d_{\text{org}}^{T} = \mathcal{L}_{ce}(\tilde{\mathbf{y}}_{\text{soft}}, \tilde{\mathbf{y}}_{\text{org}}),
$$
  
\n
$$
d_{\text{aug}}^{T} = \mathcal{L}_{ce}(\tilde{\mathbf{y}}_{\text{soft}}, \tilde{\mathbf{y}}_{\text{aug}}).
$$
\n(8)

Moreover, for each pair of original and augmented images, we store their indices in the synthetic dataset, the details of

<span id="page-5-4"></span><span id="page-5-2"></span>

|               |     |                          |                          | ConvNet                  |                |                |                    | Resnet18       |                |                | ResNet-101     |                |
|---------------|-----|--------------------------|--------------------------|--------------------------|----------------|----------------|--------------------|----------------|----------------|----------------|----------------|----------------|
|               | IPC | <b>MTT</b>               | <b>IDM</b>               | <b>TESLA</b>             | <b>RDED</b>    | NRR-DD         | SRe <sup>2</sup> L | <b>RDED</b>    | NRR-DD         | $SRe^2L$       | <b>RDED</b>    | NRR-DD         |
| CIFAR10       |     | $46.3 \pm 0.8$           | $45.6 \pm 0.7$           | $48.5 \pm 0.8$           | $23.5 \pm 0.3$ | $48.4 \pm 0.4$ | $16.6 \pm 0.9$     | $22.9 \pm 0.4$ | $30.3 \pm 0.4$ | $13.7 \pm 0.2$ | $18.7 \pm 0.1$ | $25.7 \pm 0.3$ |
|               | 10  | $65.3 \pm 0.7$           | $65.3 \pm 0.7$           | $66.4 \pm 0.8$           | $50.2 \pm 0.3$ | $66.7 \pm 0.4$ | $29.3 \pm 0.5$     | $37.1 \pm 0.3$ | $72.2 \pm 0.4$ | $24.3 \pm 0.6$ | $33.7 \pm 0.3$ | $65.1 \pm 0.3$ |
|               | 50  | $71.6 \pm 0.2$           | $67.5 \pm 0.1$           | $72.6 \pm 0.7$           | $68.4 \pm 0.1$ | $73.1 \pm 0.1$ | $45.0 \pm 0.7$     | $62.1 \pm 0.1$ | $84.1 \pm 0.1$ | $34.9 \pm 0.1$ | $51.6 \pm 0.4$ | $78.2 \pm 0.4$ |
| CIFAR100      |     | $24.3 \pm 0.3$           | $20.1 \pm 0.3$           | $24.8 \pm 0.5$           | $19.6 \pm 0.3$ | $27.3 \pm 0.3$ | $6.6 \pm 0.2$      | $11.0 \pm 0.3$ | $33.3 \pm 0.3$ | $6.2 \pm 0.0$  | $10.8 \pm 0.1$ | $32.9 \pm 0.3$ |
|               | 10  | $40.1 \pm 0.4$           | $45.1 \pm 0.1$           | $41.7 \pm 0.3$           | $48.1 \pm 0.3$ | $55.7 \pm 0.2$ | $27.0 \pm 0.4$     | $42.6 \pm 0.2$ | $62.7 \pm 0.2$ | $30.7 \pm 0.3$ | $41.1 \pm 0.2$ | $58.3 \pm 0.2$ |
|               | 50  | $47.7 \pm 0.2$           | $50.0 \pm 0.2$           | $47.9 \pm 0.3$           | $57.0 \pm 0.1$ | $61.1 \pm 0.1$ | $50.2 \pm 0.4$     | $62.6 \pm 0.1$ | $67.1 \pm 0.1$ | $56.9 \pm 0.1$ | $63.4 \pm 0.3$ | $65.1 \pm 0.3$ |
| Tiny-ImageNet |     | $8.8 \pm 0.3$            | $10.1 \pm 0.2$           | ÷                        | $12.0 \pm 0.1$ | $20.4 \pm 0.2$ | $2.62 \pm 0.1$     | $9.7 \pm 0.4$  | $13.5 \pm 0.2$ | $1.9 \pm 0.1$  | $3.8 \pm 0.1$  | $10.1 \pm 0.1$ |
|               | 10  | $23.2 \pm 0.2$           | $21.9 \pm 0.3$           | $\sim$                   | $39.6 \pm 0.1$ | $44.3 \pm 0.2$ | $16.1 \pm 0.2$     | $41.9 \pm 0.2$ | $45.2 \pm 0.2$ | $14.6 \pm 1.1$ | $22.9 \pm 3.3$ | $26.1 \pm 3.3$ |
|               | 50  | $28.0 \pm 0.3$           | $27.7 \pm 0.3$           | $\sim$                   | $47.6 \pm 0.2$ | $50.2 \pm 0.1$ | $41.1 \pm 0.4$     | $58.2 \pm 0.1$ | $61.2 \pm 0.1$ | $42.5 \pm 0.2$ | $41.2 \pm 0.4$ | $46.2 \pm 0.4$ |
| ImageNette    |     | $47.7 \pm 0.9$           |                          | $\sim$                   | $33.8 \pm 0.8$ | $39.3 \pm 0.9$ | $19.1 \pm 1.1$     | $35.8 \pm 1.0$ | $40.1 \pm 0.9$ | $15.8 \pm 0.6$ | $25.1 \pm 2.7$ | $28.1 \pm 2.7$ |
|               | 10  | $63.0 \pm 1.3$           | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $63.2 \pm 0.7$ | $68.3 \pm 0.6$ | $29.4 \pm 3.0$     | $61.4 \pm 0.4$ | $66.2 \pm 0.6$ | $23.4 \pm 0.8$ | $54.0 \pm 0.4$ | $56.0 \pm 0.4$ |
|               | 50  | $\overline{\phantom{a}}$ |                          | $\overline{\phantom{a}}$ | $83.8 \pm 0.2$ | $86.5 \pm 0.3$ | $40.9 \pm 0.3$     | $80.4 \pm 0.4$ | $85.6 \pm 0.3$ | $36.5 \pm 0.7$ | $75.0 \pm 1.2$ | $78.0 \pm 1.2$ |
| ImageNet1k    |     |                          | $\overline{\phantom{a}}$ | $7.7 \pm 0.2$            | $6.4 \pm 0.1$  | $11.2 \pm 0.2$ | $0.1 \pm 0.1$      | $6.6 \pm 0.2$  | $11.6 \pm 0.2$ | $0.6 \pm 0.1$  | $5.9 \pm 0.4$  | $12.2 \pm 0.4$ |
|               | 10  |                          | $\sim$                   | $17.8 \pm 1.3$           | $20.4 \pm 0.1$ | $25.6 \pm 0.2$ | $21.3 \pm 0.6$     | $42.0 \pm 0.1$ | $46.1 \pm 0.2$ | $30.9 \pm 0.1$ | $48.3 \pm 1.0$ | $51.3 \pm 1.0$ |
|               | 50  |                          |                          | $27.9 \pm 1.2$           | $38.4 \pm 0.2$ | $42.1 \pm 0.1$ | $46.8 \pm 0.2$     | $56.5 \pm 0.1$ | $60.2 \pm 0.1$ | $60.8 \pm 0.5$ | $61.2 \pm 0.4$ | $64.3 \pm 0.4$ |

Table 1. Comparison with state-of-the-art (SOTA) dataset distillation baselines. Identical neural networks are used for both dataset distillation and evaluation. Following [\[21,](#page-8-1) [27\]](#page-8-3), ConvNets used for distillation are Conv-3 for CIFAR10 and CIFAR100, Conv-4 for Tiny-ImageNet and ImageNet-1K, Conv-5 for ImageNette and ImageWoof, and Conv-6 for ImageNet-100. MTT and TESLA use down-sampled images for distillation to  $224 \times 224$  images. SRe2L and RDED use ResNet-18 for distillation and retrieval, and are evaluated on ResNet-18 and ResNet-101. Entries marked with "-" indicate scalability issues. See Supplementary Material for further details.

the data mixing, and the two divergences  $d_{\text{org}}^T$  and  $d_{\text{aug}}^T$  (see Figure  $2(c)$  $2(c)$  for visualization). Subsequently, we train the student model  $S$  by minimizing:

$$
LS=Lsce+αdbrLdbr,(9)
$$

$$
Lsce=max⁡{0,dorgS-r}+max⁡{0,daugS-r},
$$

$$
Ldbr=|dorgS-dorgT|+|daugS-daugT|,
$$

where r is a threshold and  $d^S$  is calculated as follows:

$$
d_{\text{org}}^{S} = \mathcal{L}_{ce}(\mathcal{S}(\tilde{\boldsymbol{x}}_{\text{mix}}), \tilde{\boldsymbol{y}}_{\text{org}})
$$
  
\n
$$
d_{\text{aug}}^{S} = \mathcal{L}_{ce}(\mathcal{S}(\tilde{\boldsymbol{x}}_{\text{mix}}), \tilde{\boldsymbol{y}}_{\text{aug}}).
$$
\n(10)

In Eq. [9,](#page-5-1)  $\mathcal{L}_{\text{see}}$  denotes the soft cross-entropy loss function, which encourages the student model to predict the mixed instance  $\tilde{x}_{mix}$  as a blend of the two labels,  $\tilde{y}_{org}$  and  $\tilde{y}_{\text{aug}}$ . This helps prevent excessive confidence by imposing a threshold  $r$ , which is especially crucial when using techniques such as CutMix or MixUp, where the soft label often blends multiple classes. This mechanism reduces the risk of the model becoming overly confident in any single class. Additionally,  $\mathcal{L}_{\text{dbr}}$  represents the distance-based representative loss, which allows the student model  $S$  to replicate the teacher model's divergences from its predictions on the mixed image  $\tilde{x}_{mix}$  to  $\tilde{y}_{org}$  and  $\tilde{y}_{aug}$ . This loss ensures consistency between the teacher and student models, both in terms of learned feature representations and decision boundaries. Memory Reduction. It is important to note that, unlike previous models that require storing augmentation information to generate  $\tilde{x}_{mix}$ , along with the indices and labels for both  $\tilde{x}_{org}$  and  $\tilde{x}_{aug}$ , our approach only necessitates storing two additional distances,  $d_{\text{org}}^T$  and  $d_{\text{aug}}^T$ , for the loss function. This significantly reduces memory requirements.

<span id="page-5-1"></span>Label Refinement. To further enhance the benefits of Distance-Based Representative (DBR), we propose refining the images with an additional term. Instead of training  $\tilde{x}_{org}$ using the formula from Eq. [2,](#page-3-2) we train  $\tilde{x}_{org}$  with the following formulation:

<span id="page-5-0"></span>
$$
\mathcal{L}_C = \mathcal{L}_{\text{org}} + \alpha_{lr} \mathcal{L}_{lr},\tag{11}
$$

$$
\mathcal{L}_{org} = \mathcal{L}_{ce}(\mathcal{T}(\tilde{\mathbf{x}}_{org}), \tilde{\mathbf{y}}_{org}) + \alpha_{bn} \mathcal{L}_{bn}(\mathcal{T}(\tilde{\mathbf{x}}_{org}))
$$
$$
\mathcal{L}_{1} = \max\{0, d^T - r\} + \max\{0, d^T - r\} \tag{12}
$$

<span id="page-5-3"></span>
$$
\mathcal{L}_{lr} = \max\{0, d_{\text{org}}^{I} - r\} + \max\{0, d_{\text{aug}}^{I} - r\} \qquad (12)
$$

Minimizing  $\mathcal{L}_{lr}$  ensures that the prediction of  $\mathcal{T}$  on  $\tilde{x}_{mix}$ , which will be used for training the student model, is focused solely on the two designated classes, facilitating alignment with the DBR function.

In Figure [3,](#page-4-1) we visualize the images generated by our NRR-DD method alongside several state-of-the-art approaches. It is clear that our method captures both instancespecific and class-general features, leading to improved performance.

## 4. Experiment

This section evaluates the effectiveness of our proposed method compared to state-of-the-art techniques across various datasets and neural architectures, accompanied by comprehensive ablation studies.

### 4.1. Experimental Setting

For large-scale datasets, we assessed our method using two popular pairs of backbones: ResNet34/ResNet18 [\[8\]](#page-8-5) and ResNet50/MobileNetV2 [\[18\]](#page-8-22), applied to three wellknown benchmarks: Tiny-ImageNet [\[15\]](#page-8-23), which consists of

<span id="page-6-2"></span><span id="page-6-1"></span>

|            |              |     |                | Using Soft Label | Using One-Hot  |                | <b>Using Compact Label</b> |                 |         |
|------------|--------------|-----|----------------|------------------|----------------|----------------|----------------------------|-----------------|---------|
| Dataset    | Architecture | IPC | RDED (SL)      | $NRR-DD(SL)$     | RDED (OH)      | RDED (CL)      | NRR-DD (DBR)               | NRR-DD (DBR+LR) | Recover |
|            |              |     | $33.8 \pm 0.8$ | $39.3 \pm 0.9$   | $16.3 \pm 0.3$ | $23.2 \pm 0.5$ | $32.3 \pm 0.4$             | $34.5 \pm 0.5$  | 79%     |
|            | ConvNet      | 10  | $63.2 \pm 0.7$ | $68.3 \pm 0.6$   | $27.3 \pm 0.2$ | $37.3 \pm 0.4$ | $52.2 \pm 0.4$             | $55.1 \pm 0.4$  | 68%     |
| ImageNette |              | 50  | $83.8 \pm 0.2$ | $86.5 \pm 0.3$   | $41.2 \pm 0.3$ | $54.6 \pm 0.3$ | $66.9 \pm 0.2$             | $69.2 \pm 0.2$  | 62%     |
|            |              | л.  | $35.8 \pm 1.0$ | $40.1 \pm 0.9$   | $16.2 \pm 0.4$ | $22.2 \pm 0.4$ | $34.6 \pm 0.6$             | $36.2 \pm 0.4$  | 84%     |
|            | Resnet18     | 10  | $61.4 \pm 0.4$ | $66.2 \pm 0.6$   | $25.4 \pm 0.3$ | $34.1 \pm 0.4$ | $54.7 \pm 0.4$             | $57.1 \pm 0.4$  | 78%     |
|            |              | 50  | $80.4 \pm 0.4$ | $85.6 \pm 0.3$   | $41.3 \pm 0.3$ | $52.1 \pm 0.2$ | $69.4 \pm 0.2$             | $72.2 \pm 0.3$  | 70%     |
|            |              |     | $6.4 \pm 0.1$  | $11.2 \pm 0.2$   | $2.4 \pm 0.5$  | $3.1 \pm 0.5$  | $6.2 \pm 0.5$              | $8.5 \pm 0.5$   | 69%     |
|            | ConvNet      | 10  | $20.4 \pm 0.1$ | $25.6 \pm 0.2$   | $8.3 \pm 0.4$  | $12.5 \pm 0.3$ | $16.1 \pm 0.3$             | $19.2 \pm 0.5$  | 63%     |
| ImageNet1k |              | 50  | $38.4 \pm 0.2$ | $42.1 \pm 0.1$   | $14.1 \pm 0.3$ | $22.3 \pm 0.3$ | $26.1 \pm 0.1$             | $28.1 \pm 0.3$  | 50%     |
|            |              |     | $6.6 \pm 0.2$  | $11.6 \pm 0.2$   | $2.3 \pm 0.5$  | $3.2 \pm 0.5$  | $7.5 \pm 0.3$              | $8.9 \pm 0.5$   | 71%     |
|            | Resnet18     | 10  | $42.0 \pm 0.1$ | $46.1 \pm 0.2$   | $16.3 \pm 0.7$ | $22.2 \pm 0.4$ | $34.3 \pm 0.5$             | $37.2 \pm 0.4$  | 70%     |
|            |              | 50  | $56.5 \pm 0.1$ | $60.2 \pm 0.1$   | $32.4 \pm 0.5$ | $39.3 \pm 0.2$ | $45.1 \pm 0.3$             | $49.2 \pm 0.5$  | 60%     |

Table 2. Comparison of various relabeling methods on large-scale datasets, including ImageNette and ImageNet1k. Bold values indicate the highest scores, while underlined values indicate the second-highest scores. 'SL' denotes the use of soft labels, 'OH' represents onehot vector labels, and 'CL' signifies compact labels (with 2 classes for fair comparison to our DBR). 'DBR' refers to our method using distance-based representation without label refinement, and 'DBR+LR' indicates our method with distance-based representation combined with label refinement. The Recover rate is calculate by using (DBR - One-hot)/(Soft-label - One-hot)

200 object categories, with 500 training images, 50 validation images, and 50 test images per category, all resized to  $64 \times 64$  pixels; ImageNet1k [\[4\]](#page-8-24), containing 1,000 object categories and over 1.2 million labeled training images, along with its subset Imagenette, which includes 10 sub-classes. For small-scale datasets, we ran experiments with ResNet  $[8]$ , VGG  $[20]$ , and WideResNet (WRN)  $[29]$ across CIFAR-10 and CIFAR-100 [\[11\]](#page-8-26). Both CIFAR-10 and CIFAR-100 consist of 60,000 images (50,000 for training and 10,000 for testing), with 10 and 100 categories, respectively, and all images have a resolution of  $32 \times 32$ pixels. Consistent with previous research, we set the IPC to 1, 10, and 50. All experiments were run on a single NVIDIA A100 40 GB GPU. The details of the model architectures, parameters, and additional experimental results are provided in the Supplementary Material.

Compared Baselines. We focus on comparing our method to state-of-the-art dataset distillation methods:

- MTT [\[2\]](#page-8-0), the first to propose trajectory matching;
- IDM [\[33\]](#page-9-2), the first to introduce distribution matching;
- TESLA [\[3\]](#page-8-12), the first method to scale up to full ImageNet1k;
- $SRe^{2}L$  [\[27\]](#page-8-3), a method that efficiently scales to ImageNet-1k and significantly outperforms existing approaches on large, high-resolution datasets;
- RDED  $[21]$ , a recent paper that uses instance-specific features with fine-grained details to improve largescale dataset distillation, which we consider the closest baseline.

### 4.2. Main Result

Large-scale Dataset. To demonstrate the effectiveness of our methods in real-world applications, we first compared them with various baselines on large-scale datasets

<span id="page-6-0"></span>

| Teacher\Student |                    | Resnet18       | MobileNetV2    | EfficientNet-B0 |
|-----------------|--------------------|----------------|----------------|-----------------|
| Resnet18        | SRe <sup>2</sup> L | $21.7 \pm 0.6$ | $15.4 \pm 0.2$ | $11.7 \pm 0.2$  |
|                 | <b>RDED</b>        | $42.3 \pm 0.6$ | $40.4 \pm 0.1$ | $31.0 \pm 0.1$  |
|                 | NRR-DD             | $46.1 \pm 0.2$ | $45.0 \pm 0.2$ | $34.2 \pm 0.1$  |
| MobileNetV2     | SRe <sup>2</sup> L | $19.7 \pm 0.1$ | $10.2 \pm 2.6$ | $9.8 \pm 0.4$   |
|                 | <b>RDED</b>        | $34.4 \pm 0.2$ | $33.8 \pm 0.6$ | $24.1 \pm 0.8$  |
|                 | NRR-DD             | $36.2 \pm 0.2$ | $37.2 \pm 0.1$ | $27.3 \pm 0.7$  |
| EfficientNet-B0 | SRe <sup>2</sup> L | $25.2 \pm 0.2$ | $20.5 \pm 0.2$ | $11.4 \pm 2.5$  |
|                 | <b>RDED</b>        | $42.8 \pm 0.5$ | $43.6 \pm 0.2$ | $33.3 \pm 0.9$  |
|                 | NRR-DD             | $47.2 \pm 0.2$ | $45.6 \pm 0.3$ | $35.1 \pm 0.2$  |

Table 3. Evaluation of ImageNet-1K top-1 accuracy for crossarchitecture generalization. Datasets are distilled using ResNet-18, EfficientNet-B0, and MobileNet-V2, and transferred across different architectures. Note that experiments for  $SRe<sup>2</sup>L$  could not be conducted when the distillation model lacks batch normalization  $[27]$ . All methods are evaluated with IPC = 10.

such as ImageNet1k, Tiny ImageNet, and ImageNette. The results in Table [1](#page-5-2) show that our methods outperform all the compared baselines in every scenario. For instance, our method achieves 60.2% accuracy on ImageNet1k using ResNet18, which is 4% higher than RDED and 14% higher than SRe<sup>2</sup>L. These improvements highlight the superiority of our approach in handling large-scale datasets and its ability to significantly outperform existing methods.

Small-scale Dataset. To validate the robustness of our NRR-DD, we compared it with various baselines on smallscale datasets, including CIFAR-10 and CIFAR-100. As shown in Table [1,](#page-5-2) our NRR-DD demonstrates significant improvements over the baselines. For example, with 10 IPC, our method achieves 72.2% accuracy on CIFAR-10 using ResNet18, which is 35% higher than the current stateof-the-art, RDED, which achieves 37.1%. Similarly, on CIFAR-100, our method shows a 20% improvement with 10 IPC. These results highlight the effectiveness and generalization ability of our method on small-scale datasets.

<span id="page-7-1"></span><span id="page-7-0"></span>

|                 | ConvNet        |                |                | Resnet18       |                |                |
|-----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                 | CIFAR10        | CIFAR100       | ImageNet1k     | CIFAR10        | CIFAR100       | ImageNet1k     |
| RDED (baseline) | $50.2 \pm 0.3$ | $48.1 \pm 0.3$ | $20.4 \pm 0.1$ | $37.1 \pm 0.3$ | $42.6 \pm 0.2$ | $42.0 \pm 0.1$ |
| <b>CIDD</b>     | $51.3 \pm 0.4$ | $50.2 \pm 0.3$ | $21.2 \pm 0.3$ | $50.4 \pm 0.5$ | $51.3 \pm 0.2$ | $43.2 \pm 0.2$ |
| <b>CIDD+NRR</b> | $66.7 \pm 0.4$ | $55.7 \pm 0.2$ | $25.6 \pm 0.2$ | $65.1 \pm 0.3$ | $58.3 \pm 0.2$ | $51.3 \pm 1.0$ |

Table 4. Comparison between the baseline RDED [\[21\]](#page-8-1) and our framework using only the CIDD modules (CIDD) and with Non-Critical Region Refinement (NRR) (CIDD+NRR). All methods are evaluated with  $IPC = 10$ .

Different Label Compression Comparison. To demonstrate the benefits of our distance-based representation (DBR), we conducted experiments to compare it with various label compression methods. The results show that, while requiring storage of only two distance values per instance (achieving 5× compression on ImageNette and 500× on ImageNet1k compared to soft-labels), our method provides comparable performance. Notably, in all cases with IPC set to 1, our DBR method outperforms the state-ofthe-art RDED with soft-labels. When compared to compact labels (which use only two elements of soft-labels), our method achieves significant improvements across all scenarios. These findings highlight the effectiveness of distance-based representation in this task.

Cross-architecture Generalization. To ensure the generalization capability of our distilled datasets, it is crucial to evaluate their performance across a range of neural architectures not involved in the dataset distillation process. The results in Table [3](#page-6-0) demonstrate the robustness and generalization of our method across all cross-comparisons. This can be attributed to the advantage of selecting the critical key patches in CIDD and the effectiveness of non-critical image refinement.

### 4.3. Ablation Study

Effectiveness of Critical-based Initial Data Discovery. To verify the benefits of our Critical-based Initial Data Discovery (CIDD) in Section [3.2,](#page-2-1) we conducted experiments using only the data generated by CIDD to train the student model, following the same settings as RDED [\[21\]](#page-8-1). The results in Table [4](#page-7-0) demonstrate that our CIDD achieves better performance than RDED in all cases, indicating the effectiveness of the module.

Effectiveness of Non-Critical Region Refinement. Table [4](#page-7-0) shows the results of our model with NRR (CIDD+NRR) and without NRR (CIDD). The results demonstrate that the NRR method significantly improves model performance. For example, on CIFAR-10 using ResNet18, the addition of NRR leads to an approximate 15% performance increase compared to when NRR is not used. This clearly indicates the effectiveness of incorporating the Non-Critical Region Refinement module.

**Effectiveness of Label Refinement**  $(\mathcal{L}_{lr})$ . To further validate the benefits of  $\mathcal{L}_{lr}$  in Eq. [12,](#page-5-3) we conducted experiments to determine whether this term enhances model learning without relying on soft-label information. As shown in Table [2,](#page-6-1) incorporating Label Refinement NRR-DD (DBR) consistently improves performance over without using Label Refinement NRR-DD (DBR) across all comparisons, demonstrating the effectiveness of this term.

#### 5. Limitation and Future Works

A possible limitation of our work is its reliance on the quality of Class Activation Mapping (CAM) for identifying critical and non-critical regions. While CAM provides useful insights, its performance can be sensitive to the model's initial training, potentially affecting the accuracy of the identified regions. Therefore, a promising direction for future work is exploring how to connect the training of pretrained models to generate better CAMs.

Additionally, while the Distance-Based Representative (DBR) technique reduces memory requirements, it may not fully address the trade-off between memory efficiency and the preservation of fine-grained details for certain complex tasks. This emphasizes the need for further research to develop enhanced solutions for this issue, which we plan to explore in our future work.

## 6. Conclusion

In this paper, we introduced the Non-Critical Region Refinement Dataset Distillation (NRR-DD) method to address the limitations of current dataset distillation techniques. Our approach comprises three key stages: Criticalbased Initial Data Discovery (CIDD) to capture instancespecific details, Non-Critical Region Refinement (NRR) to balance critical and non-critical regions using Class Activation Mapping, and relabeling to transfer knowledge effectively. Additionally, we proposed the Distance-Based Representative (DBR) technique, which eliminates the need for memory-intensive soft label storage by using a distancebased measure, significantly reducing memory requirements. By combining DBR with NRR, our method generates compact and efficient datasets, achieving state-of-theart performance across small- and large-scale datasets. Experimental results confirm that our approach not only enhances dataset representativeness but also minimizes training complexity, making it highly suitable for various training environments. This work advances dataset distillation by capturing critical features and optimizing storage, paving the way for efficient, scalable data solutions.

#### Acknowledgements

This work was supported by ARC DP23 grant DP230101176 and by the Air Force Office of Scientific Research under award number FA2386-23-1-4044.

## References

- <span id="page-8-7"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. *Advances in neural information processing systems*, 32, 2019. [1](#page-0-0)
- <span id="page-8-0"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, and Charless Fowlkes. Dataset distillation by matching training trajectories. *arXiv preprint arXiv:2203.11938*, 2022. [1,](#page-0-0) [2,](#page-1-1) [7](#page-6-2)
- <span id="page-8-12"></span>[3] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-4) [5,](#page-4-2) [7](#page-6-2)
- <span id="page-8-24"></span>[4] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2009. [7](#page-6-2)
- <span id="page-8-4"></span>[5] Alexey Dosovitskiy. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-0)
- <span id="page-8-11"></span>[6] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-8-13"></span>[7] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 15793–15803, 2024. [1,](#page-0-0) [2,](#page-1-1) [3](#page-2-4)
- <span id="page-8-5"></span>[8] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1,](#page-0-0) [6,](#page-5-4) [7](#page-6-2)
- <span id="page-8-8"></span>[9] Ahmed Imteaj, Urmish Thakker, Shiqiang Wang, Jian Li, and M Hadi Amini. A survey on federated learning for resource-constrained iot devices. *IEEE Internet of Things Journal*, 9(1):1–24, 2021. [1](#page-0-0)
- <span id="page-8-10"></span>[10] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. In *2023 IEEE International Conference on Omni-layer Intelligent Systems (COINS)*, pages 1–6. IEEE, 2023. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-8-26"></span>[11] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [7](#page-6-2)
- <span id="page-8-16"></span>[12] Xuan-May Le, Ling Luo, Uwe Aickelin, and Minh-Tuan Tran. Shapeformer: Shapelet transformer for multivariate time series classification. In *Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, pages 1484–1494, 2024. [3](#page-2-4)
- [13] Xuan-May Le, Minh-Tuan Tran, and Van-Nam Huynh. Learning perceptual position-aware shapelets for time series classification. In *Joint European Conference on Machine Learning and Knowledge Discovery in Databases*, pages 53– 69. Springer, 2022. [3](#page-2-4)
- <span id="page-8-17"></span>[14] Xuan-May Thi Le, Tuan Minh Tran, and Hien T Nguyen. An improvement of sax representation for time series by using complexity invariance. *Intelligent Data Analysis*, 24(3):625– 641, 2020. [3](#page-2-4)

- <span id="page-8-23"></span>[15] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6](#page-5-4)
- <span id="page-8-15"></span>[16] Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. Diversified semantic distribution matching for dataset distillation. In *Proceedings of the 32nd ACM International Conference on Multimedia*, pages 7542–7550, 2024. [2](#page-1-1)
- <span id="page-8-6"></span>[17] Nils Reimers and Iryna Gurevych. Sentence-bert: Sentence embeddings using siamese bert-networks. *arXiv preprint arXiv:1908.10084*, 2019. [1](#page-0-0)
- <span id="page-8-22"></span>[18] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 4510–4520, 2018. [6](#page-5-4)
- <span id="page-8-14"></span>[19] Ramprasaath R Selvaraju, Michael Cogswell, Abhishek Das, Ramakrishna Vedantam, Devi Parikh, and Dhruv Batra. Grad-cam: Visual explanations from deep networks via gradient-based localization. In *Proceedings of the IEEE international conference on computer vision*, pages 618–626, 2017. [1,](#page-0-0) [3](#page-2-4)
- <span id="page-8-25"></span>[20] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [7](#page-6-2)
- <span id="page-8-1"></span>[21] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390– 9399, 2024. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-4) [4,](#page-3-3) [5,](#page-4-2) [6,](#page-5-4) [7,](#page-6-2) [8,](#page-7-1) [10](#page-9-6)
- <span id="page-8-19"></span>[22] Minh-Tuan Tran, Trung Le, Xuan-May Le, Jianfei Cai, Mehrtash Harandi, and Dinh Phung. Large-scale data-free knowledge distillation for imagenet via multi-resolution data generation. *arXiv preprint arXiv:2411.17046*, 2024. [4](#page-3-3)
- <span id="page-8-9"></span>[23] Minh-Tuan Tran, Trung Le, Xuan-May Le, Mehrtash Harandi, and Dinh Phung. Text-enhanced data-free approach for federated class-incremental learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 23870–23880, 2024. [1](#page-0-0)
- <span id="page-8-20"></span>[24] Minh-Tuan Tran, Trung Le, Xuan-May Le, Mehrtash Harandi, Quan Hung Tran, and Dinh Phung. Nayer: Noisy layer data generation for efficient and effective data-free knowledge distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 23860–23869, 2024. [4](#page-3-3)
- <span id="page-8-18"></span>[25] Minh-Tuan Tran, Xuan-May Le, Van-Nam Huynh, and Sung-Eui Yoon. Pisd: A linear complexity distance beats dynamic time warping on time series classification and clustering. *Engineering Applications of Artificial Intelligence*, 138:109222, 2024. [3](#page-2-4)
- <span id="page-8-2"></span>[26] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and William T Freeman. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1](#page-0-0)
- <span id="page-8-3"></span>[27] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-4) [4,](#page-3-3) [5,](#page-4-2) [6,](#page-5-4) [7,](#page-6-2) [10](#page-9-6)
- <span id="page-8-21"></span>[28] Sangdoo Yun, Dongyoon Han, Seong Joon Oh, Sanghyuk Chun, Junsuk Choe, and Youngjoon Yoo. Cutmix: Regularization strategy to train strong classifiers with localizable

<span id="page-9-6"></span>features. In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 6023–6032, 2019. [5](#page-4-2)

- <span id="page-9-5"></span>[29] Sergey Zagoruyko and Nikos Komodakis. Wide residual networks. *arXiv preprint arXiv:1605.07146*, 2016. [7](#page-6-2)
- <span id="page-9-4"></span>[30] Hongyi Zhang. mixup: Beyond empirical risk minimization. *arXiv preprint arXiv:1710.09412*, 2017. [5](#page-4-2)
- <span id="page-9-1"></span>[31] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 38, pages 9314–9322, 2024. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-0"></span>[32] Bo Zhao, Krishna Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 708–717, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-2"></span>[33] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [1,](#page-0-0) [2,](#page-1-1) [7](#page-6-2)
- <span id="page-9-3"></span>[34] Bolei Zhou, Aditya Khosla, Agata Lapedriza, Aude Oliva, and Antonio Torralba. Learning deep features for discriminative localization. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2921–2929, 2016. [1,](#page-0-0) [3,](#page-2-4) [4](#page-3-3)

# A. Training Details

For all experiments conducted in this study, we fixed the following hyperparameters:

- Scale factor for batch normalization  $\alpha_{bn} = 10$  (as follows to  $[27]$
- CAM matrix's upper threshold  $\epsilon = 0.5$
- Embedding radius  $r = 0.4$
- Scale factors for distance-based representation  $\alpha_{dbr} =$ 1 and  $\alpha_{lr} = 1$

These values were selected through careful tuning based on prior work and were maintained consistently across all experiments to ensure comparability and minimize the impact of hyperparameter variability. By fixing these hyperparameters, we aim to provide a fair and reproducible assessment of our methods across different datasets and experimental conditions.

## A.1. Non-Critical Region Refinement Phase.

During this phase, we utilized the following settings:

- Optimizer: Adam
- Learning rate: 0.05
- Betas:  $\beta_1 = 0.5, \beta_2 = 0.9$

- Batch size: 100
- Iterations: 2000

This phase is designed to refine the synthetic data, particularly in non-critical regions of the feature space, enhancing the model's ability to generalize for subsequent tasks.

### A.2. Knowledge Transfer and Post-Evaluation Phase.

In this phase, we applied the following parameters:

- Optimizer: AdamW (incorporating weight decay for better generalization)
- Learning rate: 1e-3
- Batch size: 100
- Training epochs: 300
- Learning rate scheduler: Smoothing LR

To augment the synthetic data and improve robustness, we employed the following data augmentation techniques:

- Two RandAugment transformations
- RandomResizeCrop
- RandomHorizontalFlip

These augmentations, as detailed in [\[21\]](#page-8-1) and [\[27\]](#page-8-3), help prevent overfitting by introducing variability into the data, thereby enhancing the model's generalization capabilities.

All of these settings were consistently applied across all datasets to ensure fair comparison and reliable evaluation of the model's performance under varying experimental conditions.

## B. Parameter Sensitivity Analysis

### B.1. Threshold $\epsilon$ .

In this section, we examine the impact of different threshold values  $(\epsilon)$  on model performance across two datasets, ImageNette and CIFAR100, for both IPC 10 and IPC 50 classification tasks. The results presented in Table [5](#page-10-0) show that a threshold of 0.5 consistently delivers the best performance across all settings. Specifically, for ImageNette (IPC 10), the accuracy reaches 66.2%, and for ImageNette (IPC 50), it peaks at 85.6%. Similarly, for CI-FAR100 (IPC 10), the highest accuracy is 62.7%, while CIFAR100 (IPC 50) achieves 67.1%. This optimal performance at  $\epsilon = 0.5$  can be attributed to a balanced trade-off. where this threshold maintains an effective level of value retention and updates without excessive loss or staleness.

<span id="page-10-0"></span>

| Thresold $\epsilon$                                       | 0.1   | 0.3   | 0.4   | 0.5         | 0.6   | 0.7   | 0.9   |
|-----------------------------------------------------------|-------|-------|-------|-------------|-------|-------|-------|
| ImageNette (IPC 10)                                       | 63.15 | 65.35 | 65.59 | <b>66.2</b> | 65.87 | 65.4  | 65.35 |
| ImageNette (IPC 50)                                       | 82.89 | 84.66 | 85.01 | <b>85.6</b> | 85.08 | 84.7  | 84.31 |
| <b>CIFAR100 (IPC 10)</b>                                  | 60.14 | 61.56 | 62.28 | <b>62.7</b> | 61.29 | 61.69 | 61.14 |
| <b>CIFAR100 (IPC 50)</b>                                  | 66.58 | 65.5  | 66.46 | <b>67.1</b> | 66.26 | 66.7  | 66.15 |
| Table 5. Comparison of model performance across different |       |       |       |             |       |       |       |

threshold values  $(\epsilon)$  on the ImageNette and CIFAR100 datasets for IPC 10 and IPC 50 classification tasks. The best performance is achieved at  $\epsilon = 0.5$  for both datasets and tasks.

### B.2. Radius r.

In this section, we investigate the impact of different radius values on model performance across the ImageNette and CIFAR10 datasets for both IPC 10 and IPC 50 classification tasks. The results in Table [6](#page-10-1) reveal that a radius of 0.4 consistently provides the best performance across all settings. Specifically, for ImageNette (IPC 10), the accuracy peaks at 66.2%, and for ImageNette (IPC 50), it reaches 85.6%. Similarly, for CIFAR10 (IPC 10), the highest accuracy is 62.7%, while CIFAR10 (IPC 50) achieves 67.1%. This suggests that a radius of 0.4 strikes an optimal balance, yielding the highest accuracy without causing overfitting or excessive loss.

<span id="page-10-1"></span>

| radius                                                                                                                        | 0.1   | 0.2   | 0.3   | 0.4         | 0.5   | 0.6   | 0.7   |
|-------------------------------------------------------------------------------------------------------------------------------|-------|-------|-------|-------------|-------|-------|-------|
| ImageNette (IPC 10)                                                                                                           | 64.29 | 65.24 | 65.24 | <b>66.2</b> | 64.92 | 65.6  | 64.91 |
| ImageNette (IPC 50)                                                                                                           | 84.17 | 83.41 | 84.21 | <b>85.6</b> | 84.14 | 85.24 | 85.05 |
| CIFAR10 (IPC 10)                                                                                                              | 61.02 | 61.21 | 61.97 | <b>62.7</b> | 61.18 | 61.16 | 59.56 |
| CIFAR10 (IPC 50)                                                                                                              | 65    | 65.1  | 66.02 | <b>67.1</b> | 65.74 | 66.57 | 65.74 |
| Table 6. Comparison of model performance across different radius values on the ImageNette and CIFAR10 datasets for IPC 10 and |       |       |       |             |       |       |       |

IPC 50 classification tasks. A radius of 0.4 consistently yields the best performance across all settings.

### B.3. Scale Factor $\alpha_{dbr}$ and $\alpha_{lr}$ .

In this section, we analyze the effect of two hyperparameters,  $\alpha_{dbr}$  and  $\alpha_{lr}$ , on model performance across different configurations. The results, as shown in Table [7,](#page-10-2) indicate that the best performance is achieved when  $\alpha_{dbr} = 1$ and  $\alpha_{lr} = 1$ , yielding an accuracy of 66.2%. This combination outperforms other configurations, particularly for higher values of  $\alpha_{lr}$ , where the accuracy tends to decrease. These findings suggest that the balance between these two parameters plays a critical role in optimizing the model's performance.

<span id="page-10-2"></span>

| $\alpha_{lr}$<br>$\alpha_{dbr}$ | 0.2  | 0.5  |      | 2    | 5    |
|---------------------------------|------|------|------|------|------|
| 0.2                             | 64.2 | 64.5 | 64.4 | 64.4 | 62.7 |
| 0.5                             | 63.7 | 65.0 | 65.6 | 65.0 | 62.6 |
|                                 | 63.8 | 65.8 | 66.2 | 65.9 | 63.2 |
| $\mathcal{D}_{\mathcal{L}}$     | 64.7 | 66.1 | 65.6 | 64.3 | 62.8 |
| $\overline{\mathbf{5}}$         | 64.5 | 63.6 | 63.8 | 63.9 | 63.0 |

Table 7. Performance analysis of the model with varying values of  $\alpha_{\text{dbr}}$  and  $\alpha_{\text{lr}}$ . The highest accuracy of 66.2% is achieved when  $\alpha_{dbr} = 1$  and  $\alpha_{lr} = 1$ .

### B.4. Ablation Study on $L_{bn}$ :

Inspired by your recommendations, we conducted additional experiments in the Table  $8$ . The results show that: (1) With or without  $L_{bn}$ , our method outperforms RDED; (2)  $L_{bn}$  is important and helps improve model performance.

<span id="page-10-3"></span>

|                         | CIFAR <sub>10</sub> | CIFAR100       |                  | ImageNette ImageNet1k |
|-------------------------|---------------------|----------------|------------------|-----------------------|
| RDED                    | $37.1 \pm 0.3$      | $42.6 \pm 0.2$ | $61.4 \pm 0.4$   | $42.0 \pm 0.1$        |
| NRR-DD Without $L_{bn}$ | $68.3 \pm 0.4$      | $60.1 \pm 0.2$ | $65.1 \pm 0.5$   | $44.8 \pm 0.2$        |
| NRR-DD With $L_{bn}$    | $72.2 \pm 0.4$      | $62.7 \pm 0.2$ | $66.2 \pm 0.6$   | $46.1 \pm 0.2$        |
|                         | $T11011'$ $T$       |                | $\cdot$ 1.10 IDC |                       |

Table 8. Ablation Study for  $L_{bn}$  with 10 IPC.

# C. Choosing Lowest Confident Score or Highest Confident Score.

To further highlight the benefits of using the Lowest Confident Score (LCS) over the Highest Confident Score (HCS) for selecting initial patches, we compare two experimental scenarios: (1) training the combined images of these patches directly (referred to as Direct) to evaluate the raw image quality, and (2) refining the images through our Non-Critical Refinement (Referred to as Refining) to assess the improved versions. The results, presented in Table [9,](#page-11-0) clearly indicate that the Lowest Confident Score consistently yields better performance than the Highest Confident Score across all evaluated settings. These observations can be attributed to two main factors:

- Hard-to-Learn Samples: The Lowest Confident Score tends to identify harder-to-learn, more challenging samples that are often neglected by simpler approaches. These samples provide critical information that enhances the model's ability to generalize, which leads to improved performance across different datasets. Additionally, by selecting these harder samples, the model still focus on instance-specific features, which is the most important features in largescale dataset distillation.
- Optimization Flexibility with CE Loss: When using the teacher's classification loss (CE Loss) on synthetic data, the Lowest Confident Score offers more flexibility for parameter updates compared to the Highest Confident Score. This is because patches with the Highest Confident Score typically have a CE Loss close to zero in many cases, indicating that these samples are already well-classified and do not require significant adjustments. In contrast, the Lowest Confident Score corresponds to harder-to-learn patches, which are more likely to lead to meaningful updates during training. By focusing on these difficult samples, the model has more opportunity for optimization, thus facilitating better overall performance.

Thus, the results demonstrate that using the Lowest Confident Score for selecting patches not only improves the

<span id="page-11-0"></span>

|                          |                     | ConvNet              |                    |                | Resnet18       |                |
|--------------------------|---------------------|----------------------|--------------------|----------------|----------------|----------------|
|                          | CIFAR <sub>10</sub> | CIFAR <sub>100</sub> | ImageNet1k CIFAR10 |                | CIFAR100       | ImageNet1k     |
| Highest-Score (Direct)   | $50.2 \pm 0.3$      | $48.1 \pm 0.3$       | $20.4 \pm 0.1$     | $37.1 \pm 0.3$ | $42.6 \pm 0.2$ | $42.0 \pm 0.1$ |
| Lowest-Score (Direct)    | $51.3 \pm 0.4$      | $50.2 \pm 0.3$       | $21.2 \pm 0.3$     | $50.4 \pm 0.5$ | $51.3 \pm 0.2$ | $43.2 \pm 0.2$ |
| Highest-Score (Refining) | $64.8 \pm 0.4$      | $53.1 \pm 0.3$       | $24.1 \pm 0.3$     | $63.7 \pm 0.2$ | $57.1 \pm 0.3$ | $49.9 \pm 1.1$ |
| Lowest-Score (Refining)  | $66.7 \pm 0.4$      | $55.7 \pm 0.2$       | $25.6 \pm 0.2$     | $65.1 \pm 0.3$ | $58.3 \pm 0.2$ | $51.3 \pm 1.0$ |

Table 9. Comparison of the performance using Lowest Confident Score (LCS) versus Highest Confident Score (HCS) for selecting initial patches. The results show that LCS outperforms HCS in all scenarios, both in direct training and after applying Non-Critical Refinement (Refining), highlighting the advantages of LCS in improving model performance.

model's performance but also makes the training process more effective. By focusing on hard-to-learn samples, LCS enables the model to update parameters more significantly, yielding better performance both in direct training and after refinement. The effectiveness of LCS in this context underscores its importance as a selection criterion for initial patches.

# D. Further Discussion

*Balancing instance-level vs class-level features:* Thank you for your comment. We've added a discussion in the revised paper, explaining the effect of instance- and classlevel information by using different value of  $\epsilon$  in Eq. 4 (higher  $\epsilon$  emphasizes lower instance-level and higher classlevel information). The table below shows optimal performance at  $\epsilon = 0.5$ , balancing both levels. Qualitative images with different  $\epsilon$  values are also included for illustration.

Image /page/11/Picture/5 description: The image displays a series of five images, each labeled with a value of epsilon (ε) ranging from 0.1 to 0.9. Above the images, text indicates that a higher epsilon leads to more class-level and less instance-level information. The images themselves show a split view: the top half features close-ups of a dog's face, while the bottom half shows multiple dogs in a grassy area. Arrows on the left side of the first image point to the top section, labeling it 'Class-level information' in red, and to the bottom section, labeling it 'Instance-level information' in green. The bottom sections of the images also feature green outlines, which appear to highlight individual dogs, with the outlines becoming less defined and encompassing larger areas as the epsilon value increases.

Figure 4. Visualization with various value of  $\epsilon$ .

| Threshold \$\epsilon\$ | 0.1   | 0.3   | 0.4   | 0.5  | 0.6   | 0.7   | 0.9   |
|------------------------|-------|-------|-------|------|-------|-------|-------|
| CIFAR100 (IPC 10)      | 60.14 | 61.56 | 62.28 | 62.7 | 61.29 | 61.69 | 61.14 |
| CIFAR100 (IPC 50)      | 66.58 | 65.5  | 66.46 | 67.1 | 66.26 | 66.7  | 66.15 |

Table 10. Accuracies with various value of  $\epsilon$ .

*Efficiency test:* The experiment in Table [11](#page-11-1) shows our method has lower time cost and similar memory to SRe2L. However, due to image refinement, both time and peak memory are higher than RDED.

<span id="page-11-1"></span>

| Architecture     | Resnet18 |       |        | MobileNet-V2 |       |        |
|------------------|----------|-------|--------|--------------|-------|--------|
|                  | SRe2L    | RDED  | Our    | SRe2L        | RDED  | Our    |
| Time Cost (ms)   | 2113.23  | 39.89 | 520.65 | 3783.16      | 64.97 | 989.46 |
| Peak Memory (GB) | 9.14     | 1.57  | 9.14   | 12.93        | 2.35  | 12.93  |

Table 11. Efficiency test in generating 100 images in ImageNet1k.

*Neural architecture search (NAS):* Due to time constraints, we follow the setting in the DM paper and run experiement in Table [12.](#page-11-2) Results show that our synthetic data achieves better performance than previous methods.

<span id="page-11-2"></span>

|                 | Random | DSA   | DM    | RDED  | Our   | Whole dataset |
|-----------------|--------|-------|-------|-------|-------|---------------|
| Performance (%) | 84.0   | 82.6  | 84.3  | 84.6  | 84.9  | 85.9          |
| Correlation     | -0.04  | 0.68  | 0.76  | 0.78  | 0.80  | 1.00          |
| Time cost (min) | 142.6  | 142.6 | 142.6 | 142.6 | 142.6 | 3580.2        |

le 12. NAS on CIFAR-10 (50 IPC) searching for 720 ConvNets.

*MMT's Initialization:* Table [13](#page-11-3) shows results with MMTinitialized images. Our method only yields slight improvements, as these images already reside in high-confidence regions and contain class-general information. The nearzero  $L_C$  leads to minimal refinement, and their limited finegrained details keep performance relatively low.

*Real Data Initialization:* On the other hand, real data initialization performs well, achieving SOTA results, though slightly lower than our method. This is due to the strong instance-specific information in real images, with our refining method adding class-general features.

<span id="page-11-3"></span>

|                   | MMT (Baseline) | MMT's Init     | Real Image     | RDED's Init    | NRR-DD's Init  |
|-------------------|----------------|----------------|----------------|----------------|----------------|
| CIFAR100 (10 IPC) | $40.1 pm 0.4$ | $40.8 pm 0.6$ | $53.5 pm 0.3$ | $53.8 pm 0.3$ | $55.7 pm 0.2$ |
| CIFAR100 (50 IPC) | $47.7 pm 0.2$ | $48.0 pm 0.3$ | $59.5 pm 0.1$ | $59.6 pm 0.1$ | $61.1 pm 0.1$ |

Table 13. Comparing various initializations.