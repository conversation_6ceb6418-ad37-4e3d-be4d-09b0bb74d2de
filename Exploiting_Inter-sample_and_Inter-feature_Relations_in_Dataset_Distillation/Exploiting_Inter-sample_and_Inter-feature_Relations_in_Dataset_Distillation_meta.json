{"table_of_contents": [{"title": "Exploiting Inter-sample and Inter-feature Relations in Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[69.75, 106.5], [524.25, 106.5], [524.25, 119.109375], [69.75, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.1845703125, 235.5], [191.25, 235.5], [191.25, 247.11328125], [144.1845703125, 247.11328125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 606.0], [127.5, 606.0], [127.5, 617.203125], [48.75, 617.203125]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[307.494140625, 72.0], [397.5, 72.0], [397.5, 83.77294921875], [307.494140625, 83.77294921875]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[48.0, 72.0], [102.75, 72.0], [102.75, 83.6279296875], [48.0, 83.6279296875]]}, {"title": "3.1. Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[48.0, 159.0], [133.5, 159.0], [133.5, 170.349609375], [48.0, 170.349609375]]}, {"title": "3.2. Class centralization constraint (inter-sample)", "heading_level": null, "page_id": 2, "polygon": [[48.75, 449.25], [282.0, 449.25], [282.0, 459.421875], [48.75, 459.421875]]}, {"title": "3.3. Covariance matching constraint (inter-feature)", "heading_level": null, "page_id": 2, "polygon": [[307.5, 502.5], [546.0, 502.5], [546.0, 512.40234375], [307.5, 512.40234375]]}, {"title": "3.4. The objective function", "heading_level": null, "page_id": 3, "polygon": [[48.0, 353.25], [174.75, 353.25], [174.75, 363.515625], [48.0, 363.515625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 3, "polygon": [[48.75, 557.25], [128.25, 557.25], [128.25, 568.08984375], [48.75, 568.08984375]]}, {"title": "4.1. Experimental setup", "heading_level": null, "page_id": 3, "polygon": [[48.75, 576.75], [162.0, 576.75], [162.0, 587.8125], [48.75, 587.8125]]}, {"title": "4.2. Comparison with state-of-the-art methods", "heading_level": null, "page_id": 3, "polygon": [[307.5, 480.69140625], [525.75, 480.69140625], [525.75, 489.97265625], [307.5, 489.97265625]]}, {"title": "4.3. Cross-architecture generalization", "heading_level": null, "page_id": 4, "polygon": [[48.75, 612.75], [226.5, 612.75], [226.5, 623.390625], [48.75, 623.390625]]}, {"title": "4.4. Ablation study", "heading_level": null, "page_id": 5, "polygon": [[48.75, 576.0], [138.75, 576.0], [138.75, 586.65234375], [48.75, 586.65234375]]}, {"title": "4.5. Applications", "heading_level": null, "page_id": 6, "polygon": [[307.5, 636.0], [387.75, 636.0], [387.75, 646.20703125], [307.5, 646.20703125]]}, {"title": "4.6. Visualization", "heading_level": null, "page_id": 7, "polygon": [[48.75, 528.75], [130.5, 528.75], [130.5, 539.859375], [48.75, 539.859375]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 376.5], [378.75, 376.5], [378.75, 387.685546875], [307.5, 387.685546875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 84.15966796875], [48.75, 84.15966796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 73], ["Text", 8], ["SectionHeader", 3], ["Reference", 2], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5141, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 103], ["Text", 6], ["ListItem", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 671], ["Line", 153], ["Text", 8], ["Reference", 5], ["SectionHeader", 4], ["Equation", 4], ["TextInlineMath", 3], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 702], ["Line", 127], ["TextInlineMath", 6], ["Text", 6], ["Equation", 5], ["SectionHeader", 4], ["Reference", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 562], ["Span", 531], ["Line", 72], ["Text", 5], ["Reference", 3], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15382, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["TableCell", 135], ["Line", 99], ["Text", 6], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 85], ["TableCell", 30], ["Caption", 6], ["Reference", 6], ["Figure", 4], ["FigureGroup", 4], ["Text", 2], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 5354, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["TableCell", 147], ["Line", 99], ["Text", 4], ["Reference", 4], ["Caption", 3], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["Picture", 1], ["TableGroup", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4134, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 115], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 66], ["ListItem", 15], ["Reference", 15], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Exploiting_Inter-sample_and_Inter-feature_Relations_in_Dataset_Distillation"}