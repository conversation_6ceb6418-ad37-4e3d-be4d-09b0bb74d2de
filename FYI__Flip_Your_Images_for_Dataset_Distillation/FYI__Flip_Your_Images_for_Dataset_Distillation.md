# FYI: Flip Your Images for Dataset Distillation

Byunggwan Son<sup>o</sup>[,](https://orcid.org/0009-0006-5568-2127) <PERSON><PERSON><sup>o</sup>, <PERSON><PERSON><PERSON>[k](https://orcid.org/0009-0003-2470-1469)<sup>o</sup>, and B<PERSON>ub Ham<sup>\*</sup> <https://cvlab.yonsei.ac.kr/projects/FYI>

Yonsei University

Abstract. Dataset distillation synthesizes a small set of images from a large-scale real dataset such that synthetic and real images share similar behavioral properties (e.g, distributions of gradients or features) during a training process. Through extensive analyses on current methods and real datasets, together with empirical observations, we provide in this paper two important things to share for dataset distillation. First, object parts that appear on one side of a real image are highly likely to appear on the opposite side of another image within a dataset, which we call the bilateral equivalence. Second, the bilateral equivalence enforces synthetic images to duplicate discriminative parts of objects on both the left and right sides of the images, limiting the recognition of subtle differences between objects. To address this problem, we introduce a surprisingly simple yet effective technique for dataset distillation, dubbed FYI, that enables distilling rich semantics of real images into synthetic ones. To this end, FYI embeds a horizontal flipping technique into distillation processes, mitigating the influence of the bilateral equivalence, while capturing more details of objects. Experiments on CIFAR-10/100, Tiny-ImageNet, and ImageNet demonstrate that FYI can be seamlessly integrated into several state-of-the-art methods, without modifying training objectives and network architectures, and it improves the performance remarkably.

Keywords: Dataset distillation · Bilateral equivalence

## 1 Introduction

Training neural networks [\[7,](#page-14-0)[13,](#page-14-1)[33,](#page-15-0)[36\]](#page-15-1) with large-scale datasets [\[4,](#page-14-2)[28,](#page-15-2)[37\]](#page-15-3) is computationally expensive, and also requires lots of memory for storing training samples. Dataset distillation [\[39\]](#page-15-4) addresses this problem by condensing entire training samples into a small set of synthetic images and training networks with the synthetic ones. This facilitates many applications, including continual learning [\[2,](#page-14-3) [34,](#page-15-5) [41\]](#page-15-6), neural architecture search (NAS) [\[12,](#page-14-4) [25,](#page-15-7) [32,](#page-15-8) [50\]](#page-16-0), and federated learning [\[23,](#page-15-9) [24,](#page-15-10) [29\]](#page-15-11). For example, it is important in NAS to predict the performance of an arbitrary architecture efficiently. We can use synthetic images obtained from dataset distillation methods as proxies for original training samples. The networks trained with the synthetic images can then be used to predict the performance, instead of training networks with the original samples.

<sup>⋆</sup> Corresponding author.

<span id="page-1-0"></span>Image /page/1/Picture/1 description: The image displays a comparison of real images and generated images. Row (a) shows real images of camels, chairs, and lawnmowers. Row (b) shows generated images using MTT [1] and DSA [44], also featuring camels, chairs, and lawnmowers. The generated images appear to be lower resolution and less detailed than the real images.

(c) MTT+FYI and DSA+FYI

Fig. 1: Comparisons of existing dataset distillation methods and our approach with the 1 IPC setting on CIFAR-100 [\[17\]](#page-14-6): Camel, chair, and lawn mower classes. (a) Objects in natural images are oriented diversely, and (b) current dataset distillation methods ((left) MTT [\[1\]](#page-14-5) and (right) DSA [\[44\]](#page-16-1)) synthesize symmetric images with repeated patterns in the left and right halves, neglecting fine-grained details of objects. (c) Applying FYI to MTT and DSA avoids this problem, while capturing the fine-grained details.

The seminal work of [\[39\]](#page-15-4) formulates the dataset distillation task as a bilevel optimization problem. Specifically, it trains neural networks with synthetic images, while optimizing the synthetic images with the trained networks alternately. This approach, however, requires numerous updates to train the networks using synthetic images [\[5\]](#page-14-7). Recent works avoid the iterative updates by approximating the training process with ridge regression using the neural tangent kernel (NTK) [\[30,](#page-15-12)[31\]](#page-15-13) or exploiting surrogate objectives encouraging real and synthetic images to have similar properties  $(e,q,$  gradients [\[16,](#page-14-8) [22,](#page-15-14) [27,](#page-15-15) [44,](#page-16-1) [45\]](#page-16-2), network trajectories [\[1,](#page-14-5) [8\]](#page-14-9), or feature distributions [\[35,](#page-15-16) [38,](#page-15-17) [46\]](#page-16-3)) during the training process. Although these methods achieve better results in terms of efficiency and accuracy, we have observed that they produce similar patterns in the left and right halves across a synthetic dataset, failing to distill various semantics of real datasets into synthetic ones. For example, Fig. [1\(](#page-1-0)b) shows a single image per class (IPC) synthesized using current dataset distillation methods [\[1,](#page-14-5) [44\]](#page-16-1). We can see that the synthetic images are highly likely to be symmetric. Particularly, both halves of the synthetic images contain discriminative parts of objects (e.g., the back support of a chair), which rather prevent the synthetic images from capturing fine-grained details. The reason behind this is that similar object parts are present on the left and right sides equivalently in a real dataset (Fig.  $1(a)$ ); a phenomenon we call the bilateral equivalence. One potential solution to consider the bilateral equivalence of real datasets is to align images in the dataset before applying dataset distillation methods. However, aligning several images is nontrivial, especially when there are many objects in the images and/or objects are occluded.

In this paper, we propose a surprisingly simple yet effective method, dubbed FYI, that embeds a horizontal flipping technique into a dataset distillation process. Exploiting synthetic images with horizontally flipped counterparts reduces duplicated patterns remarkably, preventing a discriminative part synthesized on one side of a specific image from being duplicated on the other side of the image, as well as on any side of other images. For example, the lawn mower in Fig. [1\(](#page-1-0)c) contains fine-grained details with distinguishable front and back parts, compared to that in Fig. [1\(](#page-1-0)b). FYI can easily be integrated with existing dataset distillation methods to boost the performance, and it encourages them to transfer rich semantics from real to synthetic images, providing more clues when training networks with synthetic images. Extensive experiments on standard benchmarks [\[1,](#page-14-5)[14,](#page-14-10)[17,](#page-14-6)[19\]](#page-14-11) demonstrate that FYI improves the performance of existing dataset distillation methods significantly, especially for fine-grained classification [\[1\]](#page-14-5). We summarize our contributions in the following:

- We provide in-depth analyses on the bilateral equivalence for dataset distillation, and show that existing methods fail to encode diverse semantics of objects.
- In order to consider the bilateral equivalence for dataset distillation, we introduce a generic approach, dubbed FYI, that can be applied to any dataset distillation methods to prevent parts of objects synthesized within one side of an image from being duplicated on the other side of the image and on different images.
- We demonstrate the effectiveness of FYI through comprehensive experiments across various combinations of dataset distillation methods [\[1,](#page-14-5)[44–](#page-16-1)[46\]](#page-16-3), datasets [\[1,](#page-14-5) [14,](#page-14-10) [17,](#page-14-6) [19\]](#page-14-11), and compression ratios.

## 2 Related work

Dataset distillation condenses a set of natural images into a few synthetic ones, which can be categorized into two groups, regression-based and matching-based approaches. The first approach synthesizes images using a kernel ridge regression method. Specifically, it tries to regress real images from synthetic ones in a feature space. For example, KIP [\[30,](#page-15-12) [31\]](#page-15-13) performs regression using NTKs [\[15\]](#page-14-12) that represent training dynamics of neural networks [\[21\]](#page-15-18). FRePo [\[49\]](#page-16-4) instead uses convolutional features to avoid the expensive calculation for NTKs. Kernel ridge regression exploits all synthetic images at each training step, which is computationally expensive, and thus it would be not adequate for large-scale datasets [\[3\]](#page-14-13). To overcome the scalability issue, the second approach optimizes synthetic images, such that real and synthetic images share similar behavioral properties during a training process. DC [\[45\]](#page-16-2) enforces synthetic and real images to have similar gradients at every training step. Extending the single-step approach of DC [\[45\]](#page-16-2), MTT [\[1\]](#page-14-5) proposes to imitate long-range trajectories of optimization steps for real images, in order for synthetic images to better mimic the training dynamics of real images. The works of [\[38,](#page-15-17) [43,](#page-16-5) [46\]](#page-16-3) enforce real and synthetic images to have similar feature statistics, by minimizing the maximum mean discrepancy [\[11\]](#page-14-14) between intermediate features of these images, which is 4 B. Son et al.

more efficient compared to other methods [\[47\]](#page-16-6). We have observed that all the aforementioned methods encode similar semantics repeatedly on one side of an image and the other side of the same or a different image, regardless of the training objectives, which distracts from distilling rich semantics into the synthetic images.

Other approaches attempt to adjust real and/or synthetic images before applying dataset distillation methods. DREAM [\[27\]](#page-15-15) uses the K-means clustering technique [\[9\]](#page-14-15) to sample real images representing entire training samples. Although this method accelerates the training speed, and shows a satisfactory distillation performance, the representative images still contain objects with diverse orientations, providing the bilateral equivalence. DSA [\[44\]](#page-16-1) proposes to apply a data augmentation technique to both real and synthetic images in order to consider the effect of the augmentation for training networks with synthetic images. Our approach also exploits a data augmentation technique  $(i.e.,$  horizontal flipping), but differs in that we focus on distilling rich semantics from real images into synthetic ones, rather than learning how the real images respond to the augmentation technique for dataset distillation. Recently, the works of [\[5,](#page-14-7) [16,](#page-14-8) [26,](#page-15-19) [40,](#page-15-20) [47\]](#page-16-6) propose to parameterize synthetic images in order to encode rich semantics from a set of natural images more efficiently within limited storage. Specifically, HaBa [\[26\]](#page-15-19) feeds class-specific latent codes into lightweight networks to form synthetic images. IDC [\[16\]](#page-14-8) synthesizes low-resolution images which are then up-sampled using bilinear interpolation, assuming that nearby pixels are similar. Our approach also transfers rich semantics from real to synthetic images, but for the purpose of mitigating the influence of the bilateral equivalence, which has not been addressed by the previous methods.

## 3 Method

In this section, we describe dataset distillation briefly (Sec. [3.1\)](#page-3-0) and analyze the bilateral equivalence (Sec. [3.2\)](#page-4-0). We then present a detailed description of our approach (Sec. [3.3\)](#page-6-0).

<span id="page-3-0"></span>

### 3.1 Problem statement

Let us denote by  $\mathcal{T}_c$  and  $\mathcal{S}_c$  sets of real and synthetic images for the class  $c$ , respectively, defined as follows:

$$
\mathcal{T}_c = \{ t_i \mid i = 1, ..., N_c \},
$$
  
\n
$$
\mathcal{S}_c = \{ s_j \mid j = 1, ..., M_c \},
$$
\n(1)

where  $t_i$  and  $s_j$  indicate real and synthetic images, respectively. Note that the number of real images is much larger than that of synthetic images (*i.e.*,  $N_c \gg$  $M<sub>c</sub>$ ). The goal of dataset distillation methods is to estimate a small set of synthetic images such that networks trained on the set provide results similar to those trained on the real dataset in terms of accuracy. To this end, current methods [\[1,](#page-14-5)[45,](#page-16-2)[46\]](#page-16-3) imitate the training process of real images. Specifically, they define

<span id="page-4-2"></span>Image /page/4/Picture/1 description: Five square images are displayed in a row. Each image contains a central, diamond-shaped area of bright colors, transitioning from red in the center to yellow, green, and finally blue at the edges. The diamond shape is oriented with its points facing up, down, left, and right. The intensity and spread of the colors vary slightly across the five images, suggesting a progression or comparison.

Fig. 2: Distributions of discriminative object parts for the class of (from left to right) tench, goldfish, white shark, tiger shark, and hammerhead, on ImageNet [\[4\]](#page-14-2). We count how many times each pixel belongs to the top-10% of attention values obtained from class activation maps [\[48\]](#page-16-7) using a pre-trained ResNet-18 [\[13\]](#page-14-1). Red: high, Blue: low.

a distance metric  $D_{\theta}$  quantifying the difference between two datasets in terms of gradients [\[1,](#page-14-5) [45\]](#page-16-2) or convolutional features [\[46\]](#page-16-3) for the network parameterized by  $\theta$ , and minimize an objective function over various networks as follows:

<span id="page-4-3"></span>
$$
\mathcal{L} = \mathbb{E}_{\theta \sim P_{\theta}} \Big[ \sum_{c} D_{\theta}(\mathcal{T}_c, \mathcal{S}_c) \Big], \tag{2}
$$

where we denote by  $P_{\theta}$  a distribution of network parameters. For example, DM  $[46]$  exploits the following distance metric<sup>[1](#page-4-1)</sup>:

$$
D_{\theta}(\mathcal{T}_c, \mathcal{S}_c) = \Big\|\frac{1}{N_c} \sum_i C_{\theta}(t_i) - \frac{1}{M_c} \sum_j C_{\theta}(s_j)\Big\|^2,
$$
\n(3)

where  $\|\cdot\|$  is the Euclidean distance, and  $C_{\theta}$  computes convolutional features using a network parameterized by  $\theta$ . The synthetic images for the class c are then optimized as follows:

$$
S_c \leftarrow S_c - \eta \frac{\partial D_{\theta}(\mathcal{T}_c, \mathcal{S}_c)}{\partial \mathcal{S}_c},\tag{4}
$$

where  $\eta$  is a learning rate. In this way, DM encourages synthetic images to imitate an average feature of real images. However, we have found that current methods [\[1,](#page-14-5) [45,](#page-16-2) [46\]](#page-16-3) fail to capture fine-grained details of real images, distilling a few discriminative patterns into the synthetic images only. In the following, we describe this problem in detail.

<span id="page-4-0"></span>

### 3.2 The bilateral equivalence

It is unlikely that a specific part of objects consistently appears on either the left or right halves of natural images. That is, particular patterns (e.g., a head of an animal) could be on the left or right side of images with an equal possibility in a balanced manner, which we call the bilateral equivalence. We show in Fig. [2](#page-4-2) distributions of positions for the top-10% of the attention values obtained using class activation maps [\[48\]](#page-16-7) for real images of different object classes. We can

<span id="page-4-1"></span><sup>&</sup>lt;sup>1</sup> Here we mainly describe our approach based on DM. Detailed descriptions for other methods, including DC [\[45\]](#page-16-2) and MTT [\[1\]](#page-14-5), can be found in the supplementary material.

<span id="page-5-1"></span>6 B. Son et al.

Image /page/5/Figure/1 description: This image displays three line graphs, labeled (a) DC [45], (b) DSA [44], and (c) DM [46]. Each graph plots the "Number of images" on the x-axis, ranging from 0 to 1000, against a value on the y-axis, ranging from 0 to 300. All three graphs show a similar trend: a steep initial decline from a value close to 300 at 0 images, followed by a rapid decrease and then a slower, asymptotic approach towards 0 as the number of images increases. The curves represent some form of performance metric or error that decreases with more data.

Fig. 3: The bilateral equivalence of a real-world dataset. We compute the unequalness score with a set of image(s) for each object class, where we randomly sample the images from CIFAR-10 [\[17\]](#page-14-6), using DC [\[45\]](#page-16-2), DSA [\[44\]](#page-16-1) and DM [\[46\]](#page-16-3) as distance metrics in Eq. [\(5\)](#page-5-0), and show the scores averaged over the classes.

<span id="page-5-2"></span>Image /page/5/Figure/3 description: This diagram illustrates a machine learning process involving synthetic and real images. Synthetic images are processed through a 'flip' operation, concatenated with themselves, and then fed into a neural network via forward propagation. Real images are also fed into a separate neural network via forward propagation. Both networks' outputs are then used to calculate a 'Matching Loss'. Backward propagation arrows indicate that the loss influences the processing of both synthetic and real images. The diagram includes a legend explaining that the circle with a plus sign represents concatenation, solid arrows represent forward propagation, and pink arrows represent backward propagation.

Fig. 4: FYI augments synthetic images with the flipped counterparts to avoid the influence of the bilateral equivalence for dataset distillation.

observe that the distributions are highly symmetric, since discriminative parts of objects tend to distribute equally to the left and right sides. To concretely analyze the bilateral equivalence, we define an unequalness score of an arbitrary set of images  $\mathcal R$  using the distance metric in Eq. [\(2\)](#page-4-3) as follows:

<span id="page-5-0"></span>
$$
Score(\mathcal{R}) = D_{\theta} \left( \mathcal{R}, \text{Flip}(\mathcal{R}) \right), \tag{5}
$$

where Flip is a function that flips an image or all the images in a set horizontally. Note that the score is zero if a set  $\mathcal R$  is flip-invariant, *i.e.*,  $\text{Flip}(\mathcal R) = \mathcal R$ . A set is flip-invariant if the set contains a flipped counterpart for every image, *i.e.*,  $\forall r \in \mathcal{R}$ , Flip(r)  $\in \mathcal{R}$ . Thus, the unequalness score approaches to zero, as more similar patterns appear evenly on different sides of images across  $\mathcal{R}$ . More analyses on the effectiveness of the unequalness score can be found in the supplementary material. We plot in Fig. [3](#page-5-1) the unequalness scores according to the number of real images on CIFAR-10 [\[17\]](#page-14-6). We can see from this figure that the unequalness score of real images decreases rapidly, confirming the bilateral equivalence. Although the bilateral equivalence is an inherent characteristic of real datasets, we conjecture that it would prevent distilling fine-grained details into a small set of synthetic images. In particular, we have found that a synthetic dataset is highly likely to encode discriminative parts of objects on both the left and right halves of its images. This is because the discriminative parts could appear on both sides in a real dataset, and they provide strong supervisory signals at training time. Distilling discriminative parts only into the synthetic

<span id="page-6-3"></span>Image /page/6/Figure/1 description: The image displays four line graphs comparing the performance of different algorithms over iterations. Graph (a) shows 'DSA [44], 1 IPC' with two lines: 'DSA + FYI' starting at approximately 300 and increasing to about 350, and 'DSA' starting at 300 and decreasing to about 150 over 1000 iterations. Graph (b) shows 'DSA, 10 IPC' with 'DSA + FYI' starting at approximately 160 and staying around 155, and 'DSA' starting at 160 and decreasing to about 75 over 1000 iterations. Graph (c) shows 'DM [46], 1 IPC' with 'DM + FYI' starting at approximately 450 and staying around 480, and 'DM' starting at 450 and decreasing to about 50 over 20,000 iterations. Graph (d) shows 'DM, 10 IPC' with 'DM + FYI' starting at approximately 40 and staying around 42, and 'DM' starting at 40 and decreasing to about 2 over 20,000 iterations.

Fig. 5: The bilateral equivalence of synthetic datasets with and without FYI on CIFAR-100 [\[17\]](#page-14-6). We compute the unequalness score of synthetic images during training for (a-b) DSA [\[44\]](#page-16-1) and (c-d) DM [\[46\]](#page-16-3). FYI achieves a higher unequalness score compared to the vanilla methods during training, implying that it enables encoding different semantics on different halves of images. More experiments for different datasets, methods, and compression ratios can be found in the supplementary material.

<span id="page-6-4"></span>Image /page/6/Figure/3 description: This image contains four line graphs comparing the performance of different algorithms over iterations. Graph (a) shows DSA and DSA + FYI, with DSA starting at approximately 310 and decreasing to around 180 after 1000 iterations, while DSA + FYI starts at approximately 260 and decreases to around 145. Graph (b) shows DSA and DSA + FYI over 1000 iterations, with DSA starting at approximately 150 and decreasing to around 75, and DSA + FYI starting at approximately 120 and decreasing to around 50. Graph (c) shows DM and DM + FYI over 20,000 iterations, with DM starting at approximately 210 and stabilizing around 70, and DM + FYI starting at approximately 120 and stabilizing around 20. Graph (d) shows DM and DM + FYI over 20,000 iterations, with DM starting at approximately 22 and stabilizing around 5, and DM + FYI starting at approximately 10 and stabilizing around 3.

Fig. 6: Plots of training losses in Eq. [\(2\)](#page-4-3) or Eq. [\(7\)](#page-6-1), computed using (a-b) DSA [\[44\]](#page-16-1) and (c-d) DM [\[46\]](#page-16-3), on CIFAR-100 [\[17\]](#page-14-6). FYI provides lower training losses compared to the vanilla methods consistently, which indicates that the distance between synthetic and real datasets is minimized more effectively by incorporating flipped counterparts of synthetic images into the dataset distillation process. More experiments can be found in the supplementary material.

dataset however degrades performance, since fine-grained details provide more clues for recognizing subtle differences between objects.

<span id="page-6-0"></span>

## 3.3 FYI

We propose a surprisingly simple yet effective approach, dubbed FYI, that allows synthetic images to encode both discriminative parts and fine-grained details of objects (Fig. [4\)](#page-5-2). To be specific, we propose to optimize synthetic images along with their flipped counterparts. Concretely, FYI first concatenates synthetic images with the flipped counterparts as follows:

<span id="page-6-2"></span><span id="page-6-1"></span>
$$
\mathcal{A}_c = \mathcal{S}_c \cup \text{Flip}(\mathcal{S}_c),\tag{6}
$$

where we denote by ∪ a batch-wise concatenation. It then exploits the augmented set of synthetic images to compute the following objective:

$$
\mathcal{L}_{\text{FYI}} = \mathbb{E}_{\theta \sim P_{\theta}} \Big[ \sum_{c} D_{\theta}(\mathcal{T}_c, \mathcal{A}_c) \Big]. \tag{7}
$$

<span id="page-7-1"></span>Algorithm 1 Learning synthetic images using DM with FYI.

**Require:** Number of outer loop iterations  $K$ , number of classes  $C$ , parameter distribution  $P_{\theta}$ **Input:** Real dataset  $\mathcal{T} = \bigcup_c \mathcal{T}_c$ . 1: Initialize a synthetic dataset  $S = \bigcup_c S_c$ . 2: for  $k = 0$  to  $K - 1$  do 3: Sample network parameters  $\theta$  from  $P_{\theta}$ . 4: for  $c = 0$  to  $C - 1$  do 5: FYI: flip-and-concatenate the synthetic images using Eq. [\(6\)](#page-6-2). 6: Calculate  $D_{\theta}(\mathcal{T}_c, \mathcal{A}_c)$ 7: Update  $S_c$  using Eq. [\(8\)](#page-7-0). 8: end for 9: end for

Since the flipping operation and the batch-wise concatenation are differentiable, we can update synthetic images as follows:

<span id="page-7-0"></span>
$$
\mathcal{S}_c \leftarrow \mathcal{S}_c - \eta \frac{\partial D_{\theta}(\mathcal{T}_c, \mathcal{A}_c)}{\partial \mathcal{A}_c} \frac{\partial \mathcal{A}_c}{\partial \mathcal{S}_c}.
$$
 (8)

Note that FYI can be applied to any existing dataset distillation methods, since it does not modify network architectures and training objectives. We show in Fig. [5](#page-6-3) that the unequalness scores of synthetic datasets with and without using FYI on CIFAR-100 [\[17\]](#page-14-6). We can see that leveraging flipped images for the synthesis is very effective to avoid encoding duplicated patterns. Specifically, DSA [\[44\]](#page-16-1) and DM [\[46\]](#page-16-3) without FYI show a rapid decrease of the unequalness score during training, indicating that both left and right halves of the synthetic images contain similar patterns (See Fig.  $1(b)$ ). On the other hand, the score does not decrease for FYI, since it helps to capture discriminative parts of objects and fine-grained details (See Fig.  $1(c)$ ). It is worth noting that the score gap is more significant if we synthesize a single image for dataset distillation  $(i.e., 1 IPC)$ . This suggests that FYI would be even more effective when a very limited number of synthetic images are affordable. To further demonstrate how FYI works, we show in Fig. [6](#page-6-4) the training loss of dataset distillation. We can see that FYI provides much lower training losses during image synthesis. This indicates that synthesized images using FYI better capture diverse semantics of real images, compared to the vanilla methods. Note that synthesizing an image with FYI is conditioned on both other synthetic images and flipped counterparts of all the images. In this context, FYI enables encoding different semantics on the left and right sides of synthetic images. We summarize in Algorithm [1](#page-7-1) the overall dataset distillation process using FYI on top of DM.

## 4 Experiments

In this section, we describe our implementation details (Sec. [4.1\)](#page-8-0) and provide quantitative and qualitative comparisons of our approach with state-of-the-art

<span id="page-8-1"></span>Table 1: Quantitative comparison on the test set of CIFAR-10/100 [\[17\]](#page-14-6) and the val-idation split of Tiny-ImageNet [\[19\]](#page-14-11). We report the average top-1 accuracy  $(\%)$  with standard deviations.

| Dataset                                                                                                          | CIFAR 10 |                                                                                                                                      |    |     | CIFAR 100      |    | Tiny-ImageNet |                |    |
|------------------------------------------------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------------------------------------------|----|-----|----------------|----|---------------|----------------|----|
| <b>IPC</b>                                                                                                       | 1        | 10                                                                                                                                   | 50 |     | 10             | 50 |               | 10             | 50 |
| Ratio $(\%)$                                                                                                     | 0.02     | 0.2                                                                                                                                  |    | 0.2 | $\overline{2}$ | 10 | 0.2           | $\overline{2}$ | 10 |
| <b>CAFE</b> [38]                                                                                                 |          | $ 30.3 (1.1) 46.3 (0.6) 55.5 (0.6) 12.9 (0.3) 27.8 (0.3) 37.9 (0.3)$                                                                 |    |     |                |    |               |                |    |
| $CAFE+DSA$                                                                                                       |          | $ 31.6(0.8) 50.9(0.5) 62.3(0.4) 14.0(0.3) 31.5(0.2) 42.9(0.2) $                                                                      |    |     |                |    |               |                |    |
| DataDAM $[35]$ 32.0 (1.2) 54.2 (0.8) 67.0 (0.4) 14.5 (0.5) 34.8 (0.5) 49.4 (0.3) 8.3 (0.4) 18.7 (0.3) 28.7 (0.3) |          |                                                                                                                                      |    |     |                |    |               |                |    |
| DC [45]                                                                                                          |          | $ 28.3(0.5) 44.9(0.5) 53.9(0.5) 12.8(0.3) 25.2(0.3)$                                                                                 |    |     |                |    |               |                |    |
| $DC + FYI$                                                                                                       |          | $ 30.0(0.5) 49.5(0.5) 54.6(0.6) 14.4(0.3) 29.2(0.3)$                                                                                 |    |     |                |    |               |                |    |
| $DSA$ [44]                                                                                                       |          | $[28.8 (0.7) 52.1 (0.5) 60.6 (0.5)] 13.9 (0.3) 32.3 (0.3) 42.8 (0.4)$                                                                |    |     |                |    |               |                |    |
| $DSA + FYI$                                                                                                      |          | $ 30.6(0.7) 54.7(0.5) 63.7(0.5) 16.0(0.3) 35.0(0.3) 45.4(0.4)$                                                                       |    |     |                |    |               |                |    |
| IDC[16]                                                                                                          |          | $[50.1 (0.4) 67.5 (0.5) 74.5 (0.1) 28.1 (0.2) 45.0 (0.3)]$                                                                           |    |     |                |    |               |                |    |
| $IDC + FYI$                                                                                                      |          | $ 52.5(0.4) 68.1(0.3) 75.2(0.0) 28.9(0.1) 45.8(0.2)$                                                                                 |    |     |                |    |               |                |    |
| DM [46]                                                                                                          |          | $[26.0 (0.8) 48.9 (0.6) 63.0 (0.4)]$ 11.4 (0.3) 29.7 (0.3) 43.6 (0.4) 3.9 (0.2) 12.9 (0.4) 24.1 (0.3)                                |    |     |                |    |               |                |    |
| $DM + FYI$                                                                                                       |          | $28.7(1.2)$ 53.1 (0.6) 64.2 (0.4) 13.3 (0.3) 32.3 (0.4) 45.6 (0.4) 4.6 (0.2) 16.6 (0.3) 25.6 (0.4)                                   |    |     |                |    |               |                |    |
| MTT <sub>1</sub>                                                                                                 |          | $\left[46.3\ (0.8)\ 65.3\ (0.7)\ 71.6\ (0.2)\right]24.3\ (0.3)\ 40.1\ (0.4)\ 47.7\ (0.2)\right]9.0\ (0.3)\ 24.5\ (0.4)\ 29.7\ (0.4)$ |    |     |                |    |               |                |    |
| $MTT + FYI$                                                                                                      |          | $47.2$ (0.4) 68.2 (0.3) 74.0 (0.3) 28.2 (0.3) 41.6 (0.2) 48.2 (0.2) 10.4 (0.3) 25.2 (0.5) 30.1 (0.3)                                 |    |     |                |    |               |                |    |
| FTD [8]                                                                                                          |          | $46.8(0.3)66.6(0.3)73.8(0.2)25.2(0.2)43.4(0.3)50.7(0.3)10.4(0.3)26.4(0.1)$                                                           |    |     |                |    |               |                |    |
| $FTD + FYI$                                                                                                      |          | $(49.5\ (1.0)\ 68.1\ (0.5)\ 74.5\ (0.3)\ 28.7\ (0.7)\ 44.7\ (0.3)\ 50.8\ (0.4)\ 11.6\ (0.1)\ 26.8\ (0.3)$                            |    |     |                |    |               |                |    |
| Full dataset                                                                                                     |          | 84.8(0.1)                                                                                                                            |    |     | 56.2(0.3)      |    |               | 37.6(0.4)      |    |

<span id="page-8-2"></span>Table 2: Quantitative comparison on the validation split of ImageNet subsets [\[14\]](#page-14-10) for 1 and 10 IPC settings. The numbers in the brackets indicate the standard deviations.

| Dataset      | IPC | ImageNette |            | ImageWoof  |            | ImageFruit |            | ImageMeow  |            | ImageSquawk |            | ImageYellow |            |
|--------------|-----|------------|------------|------------|------------|------------|------------|------------|------------|-------------|------------|-------------|------------|
|              |     | 1          | 10         | 1          | 10         | 1          | 10         | 1          | 10         | 1           | 10         | 1           | 10         |
| FTD [8]      |     | 52.2 (1.0) | 67.7 (0.7) | 30.1 (1.0) | 38.8 (1.4) | 29.1 (0.9) | 44.9 (1.5) | 33.8 (1.5) | 43.3 (0.6) | -           | -          | -           | -          |
| MTT [1]      |     | 47.7 (0.9) | 63.0 (1.3) | 28.6 (0.8) | 35.8 (1.8) | 26.6 (0.8) | 40.3 (1.3) | 30.7 (1.6) | 40.4 (2.2) | 39.4 (1.5)  | 52.3 (1.0) | 45.2 (0.8)  | 60.0 (1.5) |
| MTT + FYI    |     | 52.4 (2.6) | 68.4 (1.6) | 30.6 (1.1) | 40.3 (0.7) | 30.1 (1.4) | 46.0 (1.1) | 33.5 (1.6) | 46.8 (0.8) | 42.6 (0.6)  | 61.6 (1.6) | 48.4 (1.0)  | 66.4 (1.9) |
| Full dataset |     | 87.4 (1.0) |            | 67.0 (1.3) |            | 63.9 (2.0) |            | 66.7 (1.1) |            | 87.5 (0.3)  |            | 84.4 (0.6)  |            |

methods (Sec. [4.2\)](#page-9-0). We also present extensive analyses on our approach (Sec. [4.3\)](#page-11-0). Please refer to the supplementary material for more results including applications to continual learning and NAS.

<span id="page-8-0"></span>

### 4.1 Implementation details

Datasets. We perform experiments on standard benchmarks: CIFAR-10/100 [\[17\]](#page-14-6), Tiny-ImageNet [\[19\]](#page-14-11), and ImageNet [\[4\]](#page-14-2). The CIFAR-10/100 datasets consist of 50K training and 10K test images of size  $32\times32$  for 10 and 100 object classes, respectively. The Tiny-ImageNet dataset provides 100K training and 10K validation images of size  $64\times64$  for 200 classes. Following [\[1\]](#page-14-5), we use six subsets of ImageNet, where all images are resized to the size of  $128 \times 128$ . Each subset contains approximately 12K training and 500 validation images for 10 classes. For evaluation, we use the validation splits for Tiny-ImageNet and ImageNet, following the experimental protocol in [\[1\]](#page-14-5).

Training and evaluation. We apply FYI to several state-of-the-art methods: DC [\[45\]](#page-16-2), DSA [\[44\]](#page-16-1), IDC [\[16\]](#page-14-8), DM [\[46\]](#page-16-3), MTT [\[1\]](#page-14-5), and FTD [\[8\]](#page-14-9). We follow the

<span id="page-9-1"></span>Image /page/9/Picture/1 description: The image displays a grid of 12 images, arranged in two rows of six. Each image appears to be a generated or stylized representation of various subjects. The top row features images that resemble a bald eagle, a dog, a cat, a bird, and two abstract or cityscape scenes. The bottom row mirrors the top row with similar subjects: a bald eagle, a dog, a cat, a bird, and two abstract or cityscape scenes. The overall aesthetic is somewhat abstract and impressionistic, with vibrant colors and blurred details, suggesting a generative art or image synthesis context.

**Fig. 7:** Qualitative comparison between MTT [\[1\]](#page-14-5) (top) and MTT+FYI (bottom) on ImageNet [\[4\]](#page-14-2): Bald eagle, English springer, tabby cat, French horn, chainsaw, tiger, parachute, and garbage truck classes. We observe that FYI improves MTT to encode fine-grained details of objects.

<span id="page-9-2"></span>Image /page/9/Picture/3 description: The image displays a grid of 16 smaller images, arranged in two rows of eight. Each smaller image shows a close-up, abstract pattern of dots and lines. The top row features patterns that appear to be rows of illuminated dots against a textured, grayish background, with some images showing a slight diagonal distortion or blur. The bottom row presents a different pattern, resembling a hexagonal or diamond-shaped grid of dark dots against a similar textured background, with some variations in lighting and focus. The overall impression is of a series of abstract, possibly digital or microscopic, patterns.

**Fig. 8:** Qualitative comparison of DM [\[46\]](#page-16-3) (top) and  $DM + FYI$  (bottom) on the first 10 classes of Tiny-ImageNet [\[19\]](#page-14-11). FYI synthesizes images in various patterns, whereas the vanilla method duplicates patterns in the left and right halves of images.

training details of each method. To be specific, we use a ConvNet [\[10\]](#page-14-16) architecture for both distillation and retraining processes. ConvNet consists of 3, 4, and 5 blocks on CIFAR-10/100 [\[17\]](#page-14-6), Tiny-ImageNet [\[19\]](#page-14-11), and ImageNet [\[4\]](#page-14-2), respectively, where each block contains a  $3\times3$  convolutional layer with 128 channels followed by a ReLU [\[18\]](#page-14-17) activation and a  $2\times 2$  average pooling layer. We halve the batch size of synthetic images for MTT and FTD before applying FYI in order to maintain computational costs of original methods. For evaluation, we retrain ConvNet with the synthesized images for 1K epochs using the SGD optimizer with a learning rate of 0.01, a momentum of 0.9, and a weight decay of 5e-4. The learning rate is adjusted by the step schedule. We use 6 operations for data augmentation, namely, crop, color jitters [\[18\]](#page-14-17), cutout [\[6\]](#page-14-18), flip, scale, and rotate. For IDC, we also apply CutMix [\[42\]](#page-15-21) following the original work. Note that we do not concatenate flipped images as in Eq. [\(6\)](#page-6-2) during retraining for a fair comparison. We measure the classification accuracy on the test or validation splits of each dataset, and report average accuracies using 100, 100, 3, 25, and 5 different random seeds for DC, DSA, IDC, DM, and MTT, respectively. We provide more details in the supplementary material.

<span id="page-9-0"></span>

## 4.2 Results

Quantitative results. We compare in Table [1](#page-8-1) results of state-of-the-art methods on CIFAR-10/100 [\[17\]](#page-14-6) and Tiny-ImageNet [\[19\]](#page-14-11) with varying numbers of synthetic images. From this table, we have three findings: (1) FYI gives remarkable

<span id="page-10-0"></span>Image /page/10/Picture/1 description: The image displays a grid of ten small, pixelated images arranged in two rows of five. The images appear to be generated by a machine learning model, possibly showing variations of a few distinct subjects. The first two images in each row are similar, depicting a round, textured object with reddish-brown and white speckles. The third image in each row shows a darker, more defined object with red and brown elements against a green background, resembling a stylized animal or creature. The last two images in each row are very similar, showing a blurry, blueish background with scattered white and dark specks, possibly representing abstract patterns or a different type of object.

Fig. 9: Qualitative comparison of synthetic images trained on CIFAR-10 [\[17\]](#page-14-6). We visualize synthetic images from the following object categories: dog, frog, horse, ship, and truck. Top: Synthesized images using DSA contain discriminative parts repeatedly (e.g., heads of horses). Bottom: Applying FYI to DSA helps to capture different parts of objects (e.g., the tail of a horse).

Table 3: Comparison of the top-1 accuracy (%) for different network architectures. We synthesize images using ConvNet [\[10\]](#page-14-16) on CIFAR-10 [\[17\]](#page-14-6) with 50 IPC, and use them to train ConvNet [\[10\]](#page-14-16), VGG-11 [\[36\]](#page-15-1), and ResNet-18 [\[13\]](#page-14-1). We use DC [\[45\]](#page-16-2), DSA [\[44\]](#page-16-1), DM [\[46\]](#page-16-3), and MTT [\[1\]](#page-14-5) for image synthesis. We report the standard deviations in the brackets.

|          | ConvNet [10] | VGG-11 [36] | ResNet-18 [13] |
|----------|--------------|-------------|----------------|
| DC [45]  | 53.9 (0.5)   | 38.8 (1.1)  | 20.9 (1.0)     |
| DC+FYI   | 54.6 (0.6)   | 40.8 (0.7)  | 25.8 (0.8)     |
| DSA [44] | 60.6 (0.5)   | 51.4 (1.0)  | 47.8 (0.9)     |
| DSA+FYI  | 63.7 (0.5)   | 56.2 (0.6)  | 52.9 (0.8)     |
| DM [46]  | 63.0 (0.4)   | 57.4 (0.8)  | 52.9 (0.4)     |
| DM+FYI   | 64.2 (0.4)   | 59.6 (0.5)  | 56.1 (0.8)     |
| MTT [1]  | 71.6 (0.2)   | 61.5 (0.5)  | 58.7 (0.2)     |
| MTT+FYI  | 74.0 (0.3)   | 65.7 (0.5)  | 61.1 (0.6)     |

gains over DC [\[45\]](#page-16-2), DM [\[46\]](#page-16-3), and MTT [\[1\]](#page-14-5) consistently. This demonstrates that FYI can be easily applied to different types of training objectives (i.e., distribution [\[46\]](#page-16-3), gradient [\[45\]](#page-16-2), and trajectory matching [\[1\]](#page-14-5)) to improve the distillation performance. (2) All methods using FYI provide better results, especially in challenging scenarios (e.g., 1 IPC). This suggests that the problem caused by the bilateral equivalence becomes severe, as the number of synthetic images becomes smaller. Our FYI mitigates the problem effectively, achieving the accuracy gains of  $2.7\%$ ,  $3.5\%$ , and  $1.2\%$  over FTD [\[8\]](#page-14-9) for the 1 IPC case on CIFAR-10, CIFAR-100 and Tiny-ImageNet, respectively. (3) FYI brings large improvements over DSA [\[44\]](#page-16-1) and IDC [\[16\]](#page-14-8). This shows that FYI improves the performance of dataset distillation in a complementary manner to existing methods using data augmentation techniques. DSA applies the same data augmentation technique  $(e,q)$ , rotate 10 degrees) to real and synthetic images before feeding them into networks. IDC enlarges the number of synthetic images by resizing low-resolution images, keeping the total storage budget. For example, IDC synthesizes 40 images for the 10 IPC setting but stores the same number of pixels as 10 real images. While FYI also exploits a data augmentation technique  $(i.e.,$  horizontal flipping), it mitigates a different problem caused by the bilateral equivalence. Note that both DSA and IDC suffer from this problem, and FYI further improves the performance consistently.

Qualitative results. We show in Figs. [7](#page-9-1) to [9](#page-10-0) qualitative results obtained without (top) and with (bottom) FYI. Compared to the original methods [\[1,](#page-14-5)[44,](#page-16-1)[46\]](#page-16-3), we can see that FYI provides synthetic images containing rich semantics, including discriminative parts of objects and fine-grained details. For example, Fig. [7](#page-9-1) shows that MTT [\[1\]](#page-14-5) using FYI produces synthetic images containing fine-grained details such as the beak of a bald eagle (the first column) and the blade of a chainsaw (the fifth column). We can see from Fig. [8](#page-9-2) that DM [\[46\]](#page-16-3) without FYI produces duplicated shapes (top), while using FYI avoids duplicating patterns

#### 12 B. Son et al.

<span id="page-11-1"></span>**Table 4:** Quantitative comparison of the top-1 accuracy  $(\%)$  of FYI and its variants using different data augmentation techniques. We synthesize images on CIFAR-10 [\[17\]](#page-14-6) with 50 IPC. We report the standard deviations in the brackets.

<span id="page-11-2"></span>Image /page/11/Figure/2 description: The image contains a table and two line graphs. The table shows performance metrics for different methods (DC, DSA, DM) under various augmentation strategies (w/o augmentation, Horizontal Flip, Rotate, Scale, Vertical Flip). The values are presented as percentages with standard deviations in parentheses. For example, DC with Horizontal Flip has a score of 54.6 (0.6). The left graph plots the number of images on the x-axis against a metric (likely error or loss) on the y-axis, showing four lines representing Flip, Rotate, Scale, and Vertical flip. The right graph is similar but includes a zoomed-in inset showing the performance of Flip, Rotate, and Scale at higher values. The inset highlights that Scale has the highest performance, followed by Rotate, and then Flip.

Fig. 10: The unequalness score and its variants on CIFAR-10 [\[17\]](#page-14-6). For the variants, we replace Flip in Eq. [\(5\)](#page-5-0) with different data augmentation techniques. We use (left) DC [\[45\]](#page-16-2) and (right) DM [\[46\]](#page-16-3) as a distance metric  $D_{\theta}$ . We report the scores averaged over object classes, similar to the results in Fig. [3.](#page-5-1)

on both the left and right sides of images (bottom). We can also observe in Fig. [9](#page-10-0) that FYI can also be effective in distilling low-resolution images.

<span id="page-11-0"></span>

### 4.3 Discussion

Fine-grained classification. We provide in Table [2](#page-8-2) results of our method on subsets of ImageNet [\[4\]](#page-14-2) for two IPC cases. We can see that MTT [\[1\]](#page-14-5) using FYI outperforms state-of-the-art methods [\[1,](#page-14-5) [8\]](#page-14-9) significantly on all subsets for all IPC settings, validating once again the effectiveness of the proposed FYI. In particular, the accuracy gains from FYI are 3.2% and 9.3% for 1 and 10 IPC cases on ImageSquawk. FYI removes duplicated patterns, while capturing fine-grained details (See Fig. [7\)](#page-9-1), which is crucial for recognizing such an object.

Cross-architecture generalization. We report in Table [3](#page-10-0) the top-1 accuracy of network architectures that are unseen during the image synthesis. Specifically, we train synthetic images with ConvNet [\[10\]](#page-14-16) and use them to train VGG-11 [\[36\]](#page-15-1) and ResNet-18 [\[13\]](#page-14-1) for evaluation. We can see that FYI again provides remarkable improvements over the original methods consistently. This indicates that FYI helps to synthesize images encoding rich semantics robust to various network architectures effectively.

Data augmentation. We compare in Table [4](#page-11-1) the top-1 accuracy of FYI and its variants with different data augmentation techniques. Specifically, we rotate images by 15 degrees, scale them by a factor of 1.2, or flip them vertically followed by a batch-wise concatenation. We can see that 1) FYI outperforms all

<span id="page-12-0"></span>Image /page/12/Figure/1 description: The image displays four sets of generated digits, arranged in a 2x2 grid. Each set contains two rows of digits. Set (a) shows digits generated by DSA with 1 IPC, with the digits 0, 1, 8, 8, 4, 8, 8, 9 highlighted with red boxes. Set (b) shows digits generated by DSA + FYI with 1 IPC, displaying digits 0, 1, 2, 3, 4, 8, 8, 9. Set (c) shows digits generated by DSA with 2 IPC, with the top row displaying 0, 1, 8, 4, 8, 8, 6, 7 and the bottom row displaying 0, 1, 8, 4, 8, 6, 7, 9. Set (d) shows digits generated by DSA + FYI with 2 IPC, with the top row displaying 0, 1, 2, 3, 4, 8, 8, 9 and the bottom row displaying 0, 1, 2, 3, 4, 8, 8, 9.

<span id="page-12-1"></span>Fig. 11: Qualitative comparison of synthetic images trained on the extended MNIST [\[20\]](#page-15-22) dataset. (a) Images synthesized using DSA [\[44\]](#page-16-1) for 1 IPC. We observe all synthesized images are symmetric. (b) Applying FYI to DSA provides asymmetric images, with identifiable digits. (c) The vanilla DSA with 2 IPC still encodes similar semantics in the left and right halves of images. (d) DSA using FYI with 2 IPC shows that two synthetic images for the same digit capture different semantics effectively, while being asymmetric.

Image /page/12/Figure/3 description: The image is a line graph showing the feature distance over iterations. The x-axis is labeled "Iterations" and ranges from 0 to 1000. The y-axis is labeled "Feature distance" and ranges from 4 to 15. There are five lines plotted on the graph, each representing a different method: "Real Imgs" (pink line, approximately 14.8), "DSA (1 IPC)" (gray line, starting at 14.8 and decreasing to approximately 4.5), "DSA+FYI (1 IPC)" (blue line, starting at 14.8 and stabilizing around 13), "DSA (2 IPC)" (green line, starting at 12.5 and stabilizing around 11.8), and "DSA+FYI (2 IPC)" (purple line, starting at 14.8 and stabilizing around 14.5). The legend is located on the right side of the graph.

Fig. 12: The average distances between synthetic images and corresponding flipped counterparts in a feature space on the extended MNIST [\[20\]](#page-15-22) dataset. We use ConvNet [\[10\]](#page-14-16) pre-trained on the real dataset to embed images into the feature space.

the variants, and 2) the variants mostly degrade or marginally improve the performance of the vanilla methods. To analyze the reason behind this result, we show in Fig. [10](#page-11-2) the unequalness score and its variants on real images to further verify our interpretation. In detail, we replace the horizontal flipping in Eq. [\(5\)](#page-5-0) with other augmentation techniques and measure the scores with varying numbers of real images. We can see that the unequalness score converges to zero, while the variants do not. This is because these augmentation techniques other than horizontal flipping can generate samples that are out of the distribution of the original dataset. For example, as most of the objects are upright in images, synthesized images, if vertically flipped, correspond to samples from out of the distribution. Augmenting synthetic images using such techniques can prevent them from learning the semantics of the original dataset effectively. Note that real datasets are likely to be invariant under horizontal flipping. That is, similar patterns are highly likely to appear in different horizontal directions within a dataset, indicating that augmented samples from horizontal flipping belong to the in-distribution of the original dataset.

Bilateral equivalence. To further verify that FYI removes duplicated patterns and encodes rich semantics, we perform experiments with a dataset satisfying

#### 14 B. Son et al.

a perfect bilateral equivalence. Specifically, we apply a horizontal flipping to all images of MNIST [\[20\]](#page-15-22) and construct an extended version consisting of an equal number of original and flipped images. We adopt DSA [\[44\]](#page-16-1) to distill the augmented dataset into synthetic images. We can see in Fig. [11\(](#page-12-0)a) that the synthetic image from DSA is highly symmetric, making it difficult to recognize digits. In particular, the synthesized images of '3' and '8' become very similar, since DSA enforces the synthetic image of '3' to imitate both original and flipped images of '3'. On the contrary, we show in Fig. [11\(](#page-12-0)b) that DSA with FYI encodes different semantics on the left and right sides of images, leading to recognizable digits. Additionally, we show in Fig. [11\(](#page-12-0)c) the synthetic images using DSA for a 2 IPC case. Although we have more synthetic images, compared to Fig. [11\(](#page-12-0)b), the synthesized images are still symmetric (e.g., the number '2'). This implies that existing methods struggle to handle the bilateral equivalence, even with more number of synthetic images. Also, two images with the same class look very similar except for the directions, whereas those synthesized using  $FYI$  in Fig. [11\(](#page-12-0)d) look different in shapes. This indicates that our method further encodes rich semantics with more storage. We plot in Fig. [12](#page-12-1) the average Euclidean distance between an image and its flipped counterpart in a feature space during training. We can see that DSA using FYI preserves the distances comparable to those of the real images during training, suggesting that FYI mitigates the negative effects of bilateral equivalence, especially for the 1 IPC setting. On the contrary, the feature distances using DSA only decrease rapidly, indicating that both sides of synthetic images tend to contain duplicated patterns. The distance increases with the 2 IPC setting, as two images with the same class category are optimized together to capture different semantics. We can see that our method provides more asymmetric images even with the 1 IPC case, and its feature distances are almost the same as those for real images with the 2 IPC setting.

Limitation. Our method focuses on natural images that contain objects with arbitrary orientations, which could limit an application of our method to the dataset, where the orientation is important for recognition, typically containing numbers or characters.

## 5 Conclusion

We have presented a novel plug-and-play technique for dataset distillation, dubbed FYI, that enables better distilling rich semantics of real images into synthetic images. Specifically, we have found that object parts that appear on one side of a real image are highly likely to appear on the opposite side of another image within a dataset, making synthetic images of current methods fail to encode finegrained details of objects. We have proposed a simple yet effective strategy that uses a horizontal flipping technique to encourage synthetic images to capture diverse information. Finally, we have shown that the proposed method can be easily integrated into state-of-the-art methods, demonstrating its effectiveness on standard benchmarks.

Acknowledgements. This work was partly supported by the NRF and IITP grants funded by the Korea government (MSIT) (No.2023R1A2C2004306, No.RS-2022-00143524, Development of Fundamental Technology and Integrated Solution for Next-Generation Automatic Artificial Intelligence System, No.2022- 0-00124, Development of Artificial Intelligence Technology for Self-Improving Competency-Aware Learning Capabilities), and the Yonsei Signature Research Cluster Program of 2024 (2024-22-0161).

## References

- <span id="page-14-5"></span>1. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: CVPR (2022)
- <span id="page-14-3"></span>2. Cha, H., Lee, J., Shin, J.:  $Co<sup>2</sup>L$ : Contrastive continual learning. In: ICCV (2021)
- <span id="page-14-13"></span>3. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. In: ICML (2023)
- <span id="page-14-2"></span>4. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: ImageNet: A large-scale hierarchical image database. In: CVPR (2009)
- <span id="page-14-7"></span>5. Deng, Z., Russakovsky, O.: Remember the past: Distilling datasets into addressable memories for neural networks. In: NeurIPS (2022)
- <span id="page-14-18"></span>6. DeVries, T., Taylor, G.W.: Improved regularization of convolutional neural networks with cutout. arXiv preprint arXiv:1708.04552 (2017)
- <span id="page-14-0"></span>7. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., Uszkoreit, J., Houlsby, N.: An image is worth 16x16 words: Transformers for image recognition at scale. In: ICLR (2021)
- <span id="page-14-9"></span>8. Du, J., Jiang, Y., Tan, V.T.F., Zhou, J.T., Li, H.: Minimizing the accumulated trajectory error to improve dataset distillation. In: CVPR (2023)
- <span id="page-14-15"></span>9. Forgy, E.W.: Cluster analysis of multivariate data: efficiency versus interpretability of classifications. biometrics (1965)
- <span id="page-14-16"></span>10. Gidaris, S., Komodakis, N.: Dynamic few-shot visual learning without forgetting. In: CVPR (2018)
- <span id="page-14-14"></span>11. Gretton, A., Borgwardt, K.M., Rasch, M.J., Schölkopf, B., Smola, A.: A kernel two-sample test. The Journal of Machine Learning Research (2012)
- <span id="page-14-4"></span>12. Guo, Z., Zhang, X., Mu, H., Heng, W., Liu, Z., Wei, Y., Sun, J.: Single path one-shot neural architecture search with uniform sampling. In: ECCV (2020)
- <span id="page-14-1"></span>13. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: CVPR (2016)
- <span id="page-14-10"></span>14. Howard, J.: A smaller subset of 10 easily classified classes from imagenet, and a little more french. URL https://github.com/fastai/imagenette (2019)
- <span id="page-14-12"></span>15. Jacot, A., Gabriel, F., Hongler, C.: Neural tangent kernel: Convergence and generalization in neural networks. In: NeurIPS (2018)
- <span id="page-14-8"></span>16. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: ICML (2022)
- <span id="page-14-6"></span>17. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images. Technical report (2009)
- <span id="page-14-17"></span>18. Krizhevsky, A., Sutskever, I., Hinton, G.E.: ImageNet classification with deep convolutional neural networks. NeurIPS (2012)
- <span id="page-14-11"></span>19. Le, Y., Yang, X.: Tiny ImageNet visual recognition challenge. CS 231N (2015)

- 16 B. Son et al.
- <span id="page-15-22"></span>20. LeCun, Y., Bottou, L., Bengio, Y., Haffner, P.: Gradient-based learning applied to document recognition. Proceedings of the IEEE (1998)
- <span id="page-15-18"></span>21. Lee, J., Xiao, L., Schoenholz, S., Bahri, Y., Novak, R., Sohl-Dickstein, J., Pennington, J.: Wide neural networks of any depth evolve as linear models under gradient descent. In: NeurIPS (2019)
- <span id="page-15-14"></span>22. Lee, S., Chun, S., Jung, S., Yun, S., Yoon, S.: Dataset condensation with contrastive signals. In: ICML (2022)
- <span id="page-15-9"></span>23. Li, T., Sahu, A.K., Zaheer, M., Sanjabi, M., Talwalkar, A., Smith, V.: Federated optimization in heterogeneous networks. In: MLSys (2020)
- <span id="page-15-10"></span>24. Li, X., Huang, K., Yang, W., Wang, S., Zhang, Z.: On the convergence of fedavg on non-iid data. In: ICLR (2020)
- <span id="page-15-7"></span>25. Liu, H., Simonyan, K., Yang, Y.: Darts: Differentiable architecture search. In: ICLR (2019)
- <span id="page-15-19"></span>26. Liu, S., Wang, K., Yang, X., Ye, J., Wang, X.: Dataset distillation via factorization. In: NeurIPS (2022)
- <span id="page-15-15"></span>27. Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., You, Y.: DREAM: Efficient dataset distillation by representative matching. In: ICCV (2023)
- <span id="page-15-2"></span>28. Mahajan, D., Girshick, R., Ramanathan, V., He, K., Paluri, M., Li, Y., Bharambe, A., van der Maaten, L.: Exploring the limits of weakly supervised pretraining. In: ECCV (2018)
- <span id="page-15-11"></span>29. McMahan, B., Moore, E., Ramage, D., Hampson, S., y Arcas, B.A.: Communication-efficient learning of deep networks from decentralized data. In: AISTATS (2017)
- <span id="page-15-12"></span>30. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression. In: ICLR (2021)
- <span id="page-15-13"></span>31. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. In: NeurIPS (2021)
- <span id="page-15-8"></span>32. Pham, H., Guan, M., Zoph, B., Le, Q., Dean, J.: Efficient neural architecture search via parameters sharing. In: ICML (2018)
- <span id="page-15-0"></span>33. Radford, A., Kim, J.W., Hallacy, C., Ramesh, A., Goh, G., Agarwal, S., Sastry, G., Askell, A., Mishkin, P., Clark, J., Krueger, G., et al.: Learning transferable visual models from natural language supervision. In: NeurIPS (2021)
- <span id="page-15-5"></span>34. Rebuffi, S.A., Kolesnikov, A., Sperl, G., Lampert, C.H.: iCaRL: Incremental classifier and representation learning. In: CVPR (2017)
- <span id="page-15-16"></span>35. Sajedi, A., Khaki, S., Amjadian, E., Liu, L.Z., Lawryshyn, Y.A., Plataniotis, K.N.: DataDAM: Efficient dataset distillation with attention matching. In: ICCV (2023)
- <span id="page-15-1"></span>36. Simonyan, K., Zisserman, A.: Very deep convolutional networks for large-scale image recognition. In: ICLR (2015)
- <span id="page-15-3"></span>37. Sun, C., Shrivastava, A., Singh, S., Gupta, A.: Revisiting unreasonable effectiveness of data in deep learning era. In: ECCV (2016)
- <span id="page-15-17"></span>38. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: CAFE: Learning to condense dataset by aligning features. In: CVPR (2022)
- <span id="page-15-4"></span>39. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-15-20"></span>40. Wei, X., Cao, A., Yang, F., Ma, Z.: Sparse parameterization for epitomic dataset distillation. In: NeurIPS (2023)
- <span id="page-15-6"></span>41. Yan, S., Xie, J., He, X.: DER: Dynamically expandable representation for class incremental learning. In: CVPR (2021)
- <span id="page-15-21"></span>42. Yun, S., Han, D., Oh, S.J., Chun, S., Choe, J., Yoo, Y.: Cutmix: Regularization strategy to train strong classifiers with localizable features. In: CVPR (2019)

- <span id="page-16-5"></span>43. Zhang, H., Li, S., Wang, P., Zeng, D., Ge, S.: M3d: Dataset condensation by minimizing maximum mean discrepancy. In: AAAI (2024)
- <span id="page-16-1"></span>44. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: ICML (2021)
- <span id="page-16-2"></span>45. Zhao, B., Bilen, H.: Dataset condensation with gradient matching. In: ICLR (2021)
- <span id="page-16-3"></span>46. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: WACV (2023)
- <span id="page-16-6"></span>47. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation. In: CVPR (2023)
- <span id="page-16-7"></span>48. Zhou, B., Khosla, A., Lapedriza, A., Oliva, A., Torralba, A.: Learning deep features for discriminative localization. In: CVPR (2016)
- <span id="page-16-4"></span>49. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. In: NeurIPS (2022)
- <span id="page-16-0"></span>50. Zoph, B., Vasudevan, V., Shlens, J., Le, Q.V.: Learning transferable architectures for scalable image recognition. In: CVPR (2018)

# FYI: Flip Your Images for Dataset Distillation Supplement

Byunggwan Son<sup>o</sup>, Youngmin Oh<sup>o</sup>, Donghyeon Baek<sup> $\text{D}$ </sup>, and Bumsub Ham<sup>\*</sup> $\text{D}$ 

Yonsei University

In this supplementary material, we first provide more detailed explanations of existing dataset distillation methods (Sec. S1). We then describe more details for the experimental settings (Sec. S2). Finally, we present more analysis of our approach including results for continual learning and NAS (Sec. S3).

## S1 Current methods

We provide in the main paper a description of DM [19]. Here we review DC [18] and MTT [2] additionally.

DC [18]. Several methods [7, 11, 12, 17], including DC, attempt to match onestep gradients through network parameters between real and synthetic datasets. Specifically, they use images for the same class to compute the distance metric, defined as follows:

$$
D_{\theta}(\mathcal{T}_c, \mathcal{S}_c) = \sum_i \text{Cos}\Big[\mathcal{G}_i(\mathcal{S}_c; \theta), \mathcal{G}_i(\mathcal{T}_c; \theta)\Big],\tag{i}
$$

where Cos indicates the cosine distance between two vectors. We denote by  $\mathcal{G}_i$ the vectorized gradients w.r.t. the parameters of the i-th output node. Note that an output node is defined as a filter in a convolutional layer and a vector to compute a neuron in a fully connected layer.

MTT [2]. Extending the idea of matching one-step gradients, MTT matches training trajectories of real and synthetic datasets. Instead of minimizing a distance metric between images for the same class, MTT computes training trajectories using all images in the dataset and minimizes the distance between them. The overall objective function over different network parameters is defined as follows:

$$
\mathcal{L} = \mathbb{E}_{\theta \sim P_{\theta}} \Big[ D_{\theta}(\mathcal{T}, \mathcal{S}) \Big], \tag{ii}
$$

where  $\mathcal{T} = \bigcup_c \mathcal{T}_c$  and  $\mathcal{S} = \bigcup_c \mathcal{S}_c$  are the real and synthetic datasets, respectively. In particular, MTT defines the distance metric  $D_{\theta}$  as follows:

$$
D_{\theta}(\mathcal{T}, \mathcal{S}) = \frac{\|\theta^{\mathcal{S}} - \theta^{\mathcal{T}}\|^2}{\|\theta - \theta^{\mathcal{T}}\|^2},
$$
(iii)

<sup>⋆</sup> Corresponding author.

Image /page/18/Figure/1 description: The image displays a grid of 20 plots, arranged in 4 rows and 5 columns. Each plot is a line graph showing the performance of different methods over iterations. The first row shows plots for 'DC + FYI' and 'DC' with varying IPC (Iterations Per Class) values of 1, 10, 50, 100, and 100. The second row shows plots for 'DSA + FYI' and 'DSA' with similar IPC values. The third row shows plots for 'DM + FYI' and 'DM' with IPC values of 1, 10, 50, 100, and 100. The fourth row shows plots for 'MTT + FYI' and 'MTT' with IPC values of 1, 10, 50, 100, and 100. The x-axis for the first two rows is labeled 'Iterations' and ranges from 0 to 1000. The x-axis for the third and fourth rows is labeled 'Iterations' and ranges from 0K to 20K or 0K to 10K. The y-axis scales vary across the plots, indicating different performance metrics.

Fig. A: We show the unequalness scores of the synthesized datasets during training for DC [18], DSA [17], DM [19], and MTT [2] with and without FYI. We train synthetic datasets on CIFAR-10/100 [8] using 1, 10, or 50 IPC. We denote CIFAR-10 and CIFAR-100 by C-10 and C-100, respectively. FYI achieves higher unequalness scores throughout the training.

where  $\theta'$  and  $\theta^s$  are network parameters updated from  $\theta$  using  $\mathcal T$  and  $\mathcal S$  for a few iterations, respectively. Our FYI augments images in a mini-batch at each iteration to obtain the network parameters  $\theta^S$ .

## S2 More details

Hyperparameters. Although FYI may achieve better results by adjusting various hyperparameters (e.g., learning rate for images and networks), we follow the hyperparameters of the original papers. This shows that FYI does not require an additional search for hyperparameters. Exceptionally, we reproduce the results for MTT [2] and FTD [5] on Tiny-ImageNet [9] and FTD on CIFAR-100 under the 50 IPC setting using 2 times longer iterations to compute trajectories from synthetic images. We use efficient implementation of [4] to reduce memory caused by this change. This modification improves the performance regardless of the application of FYI.

Image /page/19/Figure/1 description: The image displays a grid of 20 line graphs, organized into 4 rows and 5 columns. Each graph plots 'Iterations' on the x-axis against a numerical value on the y-axis. The graphs are categorized by 'C-10' and 'C-100' along with '1 IPC', '10 IPC', and '50 IPC'. Within each category, there are variations labeled 'DC', 'DSA', 'DM', and 'MTT', each with a corresponding ' + FYI' version. The y-axis scales vary across the graphs, ranging from approximately 30 to 300 for the top two rows, and from 0.6 to 1.4 for the bottom two rows. The x-axis scales also vary, with some graphs showing up to 1000 iterations and others showing up to 20K or 10K iterations.

Fig. B: Plots of training losses computed using DC [18], DSA [17], DM [19], and MTT [2] with and without FYI. We train synthetic datasets on CIFAR-10/100 [8] with 1, 10, or 50 IPC settings. We denote CIFAR-10 and CIFAR-100 by C-10 and C-100, respectively. Using FYI lowers the training losses.

Data preprocessing. Data preprocessing technique used in the previous methods [2,17–19] differs from each other. We use the same technique as the original papers to compare the performance with and without FYI. Specifically, we use standard channel-wise normalization for all the methods but also used ZCA transformation for MTT [2] for CIFAR-10 [8] with 1 and 10 IPC, and CIFAR-100 [8] with 1 and 50 IPC.

## S3 More results and discussions

The unequalness score. We show in Fig. A more results for the unequalness score using various methods [2, 17–19], compression ratios, and datasets [8]. We can see that FYI provides synthetic datasets with higher unequalness scores, and the score gaps are larger for higher compression ratios. This suggests the behavioral properties of the synthetic datasets and their flipped counterparts become more different using FYI and FYI encodes different semantics on different sides effectively.

Image /page/20/Picture/1 description: Four square images are displayed side-by-side. Each image is a heatmap with a color gradient from blue to red, indicating varying intensity. The first image shows a circular pattern with the highest intensity in the center, fading outwards. The second image displays an oval-shaped pattern, elongated horizontally, with the highest intensity in the center. The third image presents a rectangular pattern with two distinct areas of high intensity at the bottom corners, and lower intensity in the middle and top. The fourth image shows a pattern with the highest intensity concentrated in the top center, fading downwards and outwards.

Fig. C: Distributions of objects on COCO. We show person, zebra, chair, and umbrella categories from left to right. Red: high, Blue: low.

| Dataset      | CIFAR 10   |            |            | CIFAR 100  |            |    |
|--------------|------------|------------|------------|------------|------------|----|
|              | 1          | 10         | 50         | 1          | 10         | 50 |
| IPC          |            |            |            |            |            |    |
| Ratio (%)    | 0.02       | 0.2        | 1          | 0.2        | 2          |    |
| DC [18]      | 28.3 (0.5) | 44.9 (0.5) | 53.9 (0.5) | 12.8 (0.3) | 25.2 (0.3) |    |
| DC + Flip    | 27.9 (0.6) | 45.4 (0.5) | 54.0 (0.5) | 12.6 (0.4) | 27.5 (0.3) |    |
| DC + FYI     | 30.0 (0.5) | 49.5 (0.5) | 54.6 (0.6) | 14.4 (0.3) | 29.2 (0.3) |    |
| DSA [17]     | 28.8 (0.7) | 52.1 (0.5) | 60.6 (0.5) | 13.9 (0.3) | 32.3 (0.3) |    |
| DSA + Flip   | 28.2 (0.6) | 52.0 (0.6) | 60.4 (0.4) | 14.1 (0.3) | 31.7 (0.3) |    |
| DSA + FYI    | 30.6 (0.7) | 54.7 (0.5) | 63.7 (0.5) | 16.0 (0.3) | 35.0 (0.3) |    |
| DM [19]      | 26.0 (0.8) | 48.9 (0.6) | 63.0 (0.4) | 11.4 (0.3) | 29.7 (0.3) |    |
| DM + Flip    | 25.8 (0.6) | 49.3 (0.8) | 63.4 (0.4) | 11.1 (0.3) | 30.1 (0.3) |    |
| DM + FYI     | 28.7 (1.2) | 53.1 (0.6) | 64.2 (0.4) | 13.3 (0.3) | 32.3 (0.4) |    |
| MTT [2]      | 46.3 (0.8) | 65.3 (0.7) | 71.6 (0.2) | 24.3 (0.3) | 40.1 (0.4) |    |
| MTT + Flip   | 47.0 (0.5) | 67.7 (0.4) | 72.8 (0.2) | 27.5 (0.7) | 41.1 (0.2) |    |
| MTT + FYI    | 47.2 (0.4) | 68.2 (0.3) | 74.0 (0.3) | 28.2 (0.3) | 41.6 (0.2) |    |
| Full dataset |            | 84.8 (0.1) |            | 56.2 (0.3) |            |    |

Table A: Quantitative comparison between applying random horizontal flipping for each synthetic image (Flip) and flip-and-concatenate strategy to all the synthetic images (FYI). We report the average top-1 accuracy (%) with standard deviations in the brackets on CIFAR-10/100 [8]. FYI is consistently better than Flip and Flip sometimes degrades the performance of the original methods.

Training losses. We plot in Fig. B the training losses during image synthesis over various settings. We observe that using FYI minimizes the training losses more effectively for all the settings. Minimizing the loss is achieved by capturing the rich semantics of real datasets into synthetic ones, and FYI achieves this by encoding diverse semantics across different sides in addition to different images.

Distributions of objects. We show in Sec. S2 distributions of locations for several objects on COCO. Each plot is obtained by counting how many times each location falls into a specific category using ground-truth masks. We can see that the distributions are also horizontally symmetric, implying that the bilateral equivalence exists in scene-centric datasets as well.

Flipping without concatenation. We compare in Tab. A our FYI, which flips and concatenates all the synthetic images, and the horizontal flipping technique for augmenting synthetic images during image synthesis. We can see that

Image /page/21/Figure/1 description: This image contains four line graphs, labeled (a) DC [18], (b) DM [19], (c) DC, and (d) DM. Each graph plots the 'Number of images' on the x-axis, ranging from 0 to 1000, and a value on the y-axis, ranging from 0 to 200. All four graphs show a steep initial drop in the plotted value as the number of images increases, followed by a plateau at a lower value. Specifically, graph (a) starts at approximately 210 and plateaus around 100. Graph (b) starts at approximately 190 and plateaus around 40. Graph (c) starts at approximately 230 and plateaus around 10. Graph (d) starts at approximately 210 and plateaus around 5.

Fig. D: The bilateral equivalence measured using the unequalness score. We measure the unequalness score of real images sampled from MNIST [10] varying the number of samples. We use DC [18] and DM [19] to compute distance metrics. Before measuring the unequalness score, we apply horizontal flipping by 50% for each image in (c) and (d) whereas we do not apply any data augmentations for (a) and (b). The unequalness score consistently decreases when the bilateral equivalence is satisfied.

methods with FYI consistently outperform vanilla methods or those with horizontal flipping, confirming the effectiveness of our approach. This indicates that conditioning flipped images is important for dataset distillation. Note that the horizontal flipping does not improve vanilla methods in most of the settings. For MTT, where multiple updates are involved, using horizontal flipping consistently improves the performance as it would condition flipped images for updating synthetic images. Nonetheless, FYI still outperforms the horizontal flipping for MTT as well by conditioning flipped images with equal importance.

The bilateral equivalence. To show the effectiveness of the unequalness score as a measurement for the bilateral equivalence, we show in Fig. D (a, b) the unequalness score of real images sampled from MNIST [10] according to the number of images. We can see that the unequalness score does not decrease even when the number of images are increased for MNIST dataset since the images are aligned in a specific direction. On the contrary, when we apply random horizontal flipping before measuring the unequalness score, the score consistently decreases as the number of images increases Fig.  $D$  (c, d). It is noteworthy that we sample images without replacement and all the images are distinct. The results suggest that the unequalness score is a good measurement for the bilateral equivalence and the score decreases when similar patterns are observed across different sides within a group of images.

Continual learning. In class-incremental continual learning [3, 13, 16], images belonging to certain class categories are given at each training step and networks are evaluated to classify among all the class categories that are seen at the past and current training steps. To let networks predict class categories in the past steps accurately, many methods [1, 13, 14] store images in a memory buffer and use them to train networks together with the images from the current step. To test the effectiveness of dataset distillation in constructing memory buffers, previous works [15, 18, 19] store synthetic images in a memory buffer and train networks from scratch using the images in the memory buffer only. We note that

6 B. Son et al.

Image /page/22/Figure/1 description: The image contains two line graphs side-by-side, labeled (a) 5-step and (b) 10-step. Both graphs plot "Top-1 accuracy (%)" on the y-axis against "Number of classes" on the x-axis. Graph (a) shows four lines: "DM + FYI" (blue solid), "DM" (blue dashed), "DSA + FYI" (pink solid), and "DSA" (pink dashed). The x-axis ranges from 20 to 100 in increments of 20. The y-axis ranges from 30 to 60 in increments of 10. Graph (b) also shows four lines with the same labels and line styles. The x-axis ranges from 10 to 100 in increments of 10. The y-axis ranges from 30 to 60 in increments of 10. In both graphs, accuracy generally decreases as the number of classes increases.

Fig. E: Quantitative comparison between current methods [17, 19] with and without FYI for continual learning on CIFAR-100 [8]. We plot the average top-1 accuracy (%) of the networks trained on 10 IPC synthesized in 5- and 10-step class-incremental settings.

|            | Top 50%      | All          | Time (min) | Images No. |
|------------|--------------|--------------|------------|------------|
| Real       | 1.000        | 1.000        | 6,804      | 50,000     |
| <b>MTT</b> | 0.312        | 0.670        | 360        | 500        |
| MTT + FYI  | <b>0.382</b> | <b>0.699</b> | 360        | 500        |

Table B: Quantitative comparison between MTT [2] with and without FYI for NAS on CIFAR-10 [8] with 50 IPC. We report Spearman's ranking correlation to the results from the whole dataset.

the size of the memory buffer grows linearly to the number of object categories to classify. We divide 100 classes of CIFAR-100 into 5 steps or 10 steps with the same number of classes for each step and synthesize 10 IPC, which belong to the class categories of the current step. We randomly select class categories without replacement, and train randomly initialized ConvNets [6]. We plot in Fig. E the average top-1 accuracy  $(\%)$  of the networks across different random seeds using DM [19] and DSA [17], with and without FYI. From the results, we can see that FYI improves the performance of vanilla methods for both 5-step and 10-step class-incremental learning. We argue that encoding fine-grained details into the images in the memory buffer is important for continual learning, and FYI can provide such details.

NAS. As an application of dataset distillation, previous works [5, 18, 19] train various networks in a predefined search space using synthetic datasets and rank the architectures based on the validation accuracy. When the rankings align with those predicted from real datasets, synthetic datasets can act as a proxy for the real datasets for NAS reducing considerable computational burden and memory requirements. We construct a search space consisting of 720 ConvNets using different combinations of width, depth, normalization, activation function, and pooling operations, following the previous works. We compare in Table B Spearman's ranking correlation between the rankings of the synthetic and the real dataset using MTT [2] with and without FYI. Specifically, we synthesize 50 IPC on CIFAR-10 [8] and compare the correlation with top-performing 50% architectures searched by the real dataset or all the architectures. We can see that MTT with FYI provides higher correlations compared to the vanilla MTT in both settings. This demonstrates that FYI is effective in searching for architectures that perform well on real datasets because FYI incorporates diverse semantics into the synthetic datasets.

## References

- 1. Aljundi, R., Lin, M., Goujaud, B., Bengio, Y.: Gradient based sample selection for online continual learning. NeurIPS (2019)
- 2. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: CVPR (2022)
- 3. Cha, H., Lee, J., Shin, J.:  $Co^2L$ : Contrastive continual learning. In: ICCV (2021)
- 4. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. In: ICML (2023)
- 5. Du, J., Jiang, Y., Tan, V.T.F., Zhou, J.T., Li, H.: Minimizing the accumulated trajectory error to improve dataset distillation. In: CVPR (2023)
- 6. Gidaris, S., Komodakis, N.: Dynamic few-shot visual learning without forgetting. In: CVPR (2018)
- 7. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: ICML (2022)
- 8. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images. Technical report (2009)
- 9. Le, Y., Yang, X.: Tiny ImageNet visual recognition challenge. CS 231N (2015)
- 10. LeCun, Y., Bottou, L., Bengio, Y., Haffner, P.: Gradient-based learning applied to document recognition. Proceedings of the IEEE (1998)
- 11. Lee, S., Chun, S., Jung, S., Yun, S., Yoon, S.: Dataset condensation with contrastive signals. In: ICML (2022)
- 12. Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., You, Y.: DREAM: Efficient dataset distillation by representative matching. In: ICCV (2023)
- 13. Rebuffi, S.A., Kolesnikov, A., Sperl, G., Lampert, C.H.: iCaRL: Incremental classifier and representation learning. In: CVPR (2017)
- 14. Riemer, M., Cases, I., Ajemian, R., Liu, M., Rish, I., Tu, Y., Tesauro, G.: Learning to learn without forgetting by maximizing transfer and minimizing interference. arXiv preprint arXiv:1810.11910 (2018)
- 15. Sajedi, A., Khaki, S., Amjadian, E., Liu, L.Z., Lawryshyn, Y.A., Plataniotis, K.N.: DataDAM: Efficient dataset distillation with attention matching. In: ICCV (2023)
- 16. Yan, S., Xie, J., He, X.: DER: Dynamically expandable representation for class incremental learning. In: CVPR (2021)
- 17. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: ICML (2021)
- 18. Zhao, B., Bilen, H.: Dataset condensation with gradient matching. In: ICLR (2021)
- 19. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: WACV (2023)