{"table_of_contents": [{"title": "Fair Graph Distillation", "heading_level": null, "page_id": 0, "polygon": [[217.6962890625, 99.75], [392.66015625, 99.75], [392.66015625, 116.982421875], [217.6962890625, 116.982421875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 223.716796875], [329.25, 223.716796875], [329.25, 234.544921875], [282.75, 234.544921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 445.5], [192.5947265625, 445.5], [192.5947265625, 457.875], [107.25, 457.875]]}, {"title": "2 Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[107.05517578125, 333.73828125], [196.62890625, 333.73828125], [196.62890625, 344.56640625], [107.05517578125, 344.56640625]]}, {"title": "2.1 Notations", "heading_level": null, "page_id": 1, "polygon": [[107.25, 358.5], [171.75, 358.5], [171.75, 367.962890625], [107.25, 367.962890625]]}, {"title": "2.2 Graph Distillation via Gradient Matching", "heading_level": null, "page_id": 1, "polygon": [[107.25, 591.75], [309.0, 591.75], [309.0, 601.34765625], [107.25, 601.34765625]]}, {"title": "3 Bias Measurement for Distilled Graph", "heading_level": null, "page_id": 2, "polygon": [[107.25, 210.0], [322.5, 210.0], [322.5, 221.203125], [107.25, 221.203125]]}, {"title": "3.1 Is Graph Distillation Really Fair?", "heading_level": null, "page_id": 2, "polygon": [[106.5, 301.5], [273.75, 301.5], [273.75, 311.501953125], [106.5, 311.501953125]]}, {"title": "3.2 Geometric Connections in Data Distillation", "heading_level": null, "page_id": 2, "polygon": [[107.25, 454.5], [315.0, 454.5], [315.0, 464.8359375], [107.25, 464.8359375]]}, {"title": "3.3 Sensitive Attribute Estimation", "heading_level": null, "page_id": 3, "polygon": [[106.5, 74.25], [259.5, 74.25], [259.5, 83.48291015625], [106.5, 83.48291015625]]}, {"title": "3.4 Bias Measurement", "heading_level": null, "page_id": 3, "polygon": [[107.25, 456.75], [210.75, 456.75], [210.75, 466.76953125], [107.25, 466.76953125]]}, {"title": "4 Methodology", "heading_level": null, "page_id": 4, "polygon": [[107.25, 330.75], [193.5, 330.75], [193.5, 341.859375], [107.25, 341.859375]]}, {"title": "4.1 Problem Statement", "heading_level": null, "page_id": 4, "polygon": [[107.1298828125, 354.0], [213.2138671875, 354.0], [213.2138671875, 364.095703125], [107.1298828125, 364.095703125]]}, {"title": "4.2 Fair Graph Distillation Loss", "heading_level": null, "page_id": 4, "polygon": [[106.5, 497.25], [251.25, 497.25], [251.25, 507.375], [106.5, 507.375]]}, {"title": "4.3 Final Objective and Training Algorithm", "heading_level": null, "page_id": 5, "polygon": [[106.5, 554.25], [301.5, 554.25], [301.5, 564.22265625], [106.5, 564.22265625]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 6, "polygon": [[106.5, 336.75], [192.0, 336.75], [192.0, 348.046875], [106.5, 348.046875]]}, {"title": "5.1 Experimental setting", "heading_level": null, "page_id": 6, "polygon": [[106.5, 446.25], [220.9833984375, 446.25], [220.9833984375, 456.71484375], [106.5, 456.71484375]]}, {"title": "5.2 Debiasing distilled Graph", "heading_level": null, "page_id": 7, "polygon": [[106.5, 242.25], [240.556640625, 242.25], [240.556640625, 252.333984375], [106.5, 252.333984375]]}, {"title": "5.3 Trade-Off Comparison", "heading_level": null, "page_id": 7, "polygon": [[106.5, 390.75], [229.5, 390.75], [229.5, 400.640625], [106.5, 400.640625]]}, {"title": "5.4 Add-on Module", "heading_level": null, "page_id": 7, "polygon": [[106.5, 681.0], [198.5712890625, 681.0], [198.5712890625, 690.6796875], [106.5, 690.6796875]]}, {"title": "6 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.1298828125, 221.25], [198.0, 221.25], [198.0, 232.224609375], [107.1298828125, 232.224609375]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 524.77734375], [183.0, 524.77734375], [183.0, 536.37890625], [106.5, 536.37890625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[106.98046875, 675.0], [207.685546875, 675.0], [207.685546875, 686.0390625], [106.98046875, 686.0390625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.0], [164.953125, 72.0], [164.953125, 83.6279296875], [106.5, 83.6279296875]]}, {"title": "A Training Algorithm", "heading_level": null, "page_id": 12, "polygon": [[106.5, 72.75], [229.6494140625, 72.75], [229.6494140625, 84.2080078125], [106.5, 84.2080078125]]}, {"title": "B Proof of Theorem 3.4", "heading_level": null, "page_id": 12, "polygon": [[106.5, 304.5], [238.46484375, 304.5], [238.46484375, 316.72265625], [106.5, 316.72265625]]}, {"title": "C Proof of Ridge Regression", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 385.5], [262.2216796875, 385.5], [262.2216796875, 396.38671875], [107.1298828125, 396.38671875]]}, {"title": "D More Results on Consistent Span Space", "heading_level": null, "page_id": 13, "polygon": [[106.2333984375, 516.75], [331.5, 516.75], [331.5, 527.87109375], [106.2333984375, 527.87109375]]}, {"title": "E Preliminary Motivation", "heading_level": null, "page_id": 14, "polygon": [[106.5, 438.75], [249.0, 438.75], [249.0, 450.9140625], [106.5, 450.9140625]]}, {"title": "F Dataset Statistics", "heading_level": null, "page_id": 14, "polygon": [[106.5, 654.0], [216.3515625, 654.0], [216.3515625, 665.54296875], [106.5, 665.54296875]]}, {"title": "G More Experimental Details", "heading_level": null, "page_id": 15, "polygon": [[106.5, 510.85546875], [269.25, 510.85546875], [269.25, 523.23046875], [106.5, 523.23046875]]}, {"title": "G.1 Parameter Study", "heading_level": null, "page_id": 15, "polygon": [[106.5, 535.21875], [208.283203125, 535.21875], [208.283203125, 546.046875], [106.5, 546.046875]]}, {"title": "G.2 Implementation Details", "heading_level": null, "page_id": 15, "polygon": [[106.5, 612.5625], [235.6259765625, 612.5625], [235.6259765625, 623.390625], [106.5, 623.390625]]}, {"title": "G.3 More Visualization", "heading_level": null, "page_id": 16, "polygon": [[106.8310546875, 331.611328125], [215.9033203125, 331.611328125], [215.9033203125, 343.212890625], [106.8310546875, 343.212890625]]}, {"title": "H Limitations and Future Work", "heading_level": null, "page_id": 16, "polygon": [[106.5, 401.4140625], [281.6455078125, 401.4140625], [281.6455078125, 413.7890625], [106.5, 413.7890625]]}, {"title": "H.1 Non-binary Sensitive Attribute", "heading_level": null, "page_id": 16, "polygon": [[106.5, 426.1640625], [265.95703125, 426.1640625], [265.95703125, 436.9921875], [106.5, 436.9921875]]}, {"title": "H.2 Individual Fairness", "heading_level": null, "page_id": 16, "polygon": [[107.25, 558.0], [217.3974609375, 558.0], [217.3974609375, 568.08984375], [107.25, 568.08984375]]}, {"title": "H.3 Other Tasks", "heading_level": null, "page_id": 16, "polygon": [[106.5, 634.5], [186.46875, 634.5], [186.46875, 645.8203125], [106.5, 645.8203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 44], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 73], ["Text", 3], ["ListItem", 3], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 725, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 74], ["TextInlineMath", 7], ["Reference", 6], ["Text", 5], ["SectionHeader", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 927], ["Line", 115], ["TextInlineMath", 6], ["Equation", 5], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 571], ["Line", 88], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 3], ["Figure", 2], ["Reference", 2], ["Caption", 1], ["Text", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1352, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 744], ["Line", 102], ["Text", 6], ["Equation", 6], ["TextInlineMath", 4], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1173], ["TableCell", 294], ["Line", 64], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 54], ["TableCell", 36], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3366, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 68], ["Text", 6], ["SectionHeader", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1023, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 46], ["ListItem", 19], ["Reference", 19], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 47], ["ListItem", 20], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 30], ["ListItem", 12], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 816], ["Line", 122], ["Text", 5], ["Equation", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1369, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 780], ["Line", 131], ["Equation", 6], ["TextInlineMath", 5], ["Reference", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2214, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["TableCell", 84], ["Line", 77], ["TextInlineMath", 3], ["ListItem", 2], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["Equation", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["TableCell", 234], ["Line", 56], ["Text", 6], ["SectionHeader", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2055, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 63], ["SectionHeader", 5], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 719, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Fair Graph Distillation"}