{"table_of_contents": [{"title": "Feature Extraction by Non-Parametric Mutual Information\nMaximization", "heading_level": null, "page_id": 0, "polygon": [[121.5, 101.70703125], [491.2734375, 101.70703125], [491.2734375, 132.451171875], [121.5, 132.451171875]]}, {"title": "", "heading_level": null, "page_id": 0, "polygon": [[90.17138671875, 154.7841796875], [158.080078125, 154.7841796875], [158.080078125, 165.4189453125], [90.17138671875, 165.4189453125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 267.75], [329.30859375, 267.75], [329.30859375, 278.82421875], [282.75, 278.82421875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[89.25, 414.75], [169.5, 414.75], [169.5, 426.55078125], [89.25, 426.55078125]]}, {"title": "TORKKOLA", "heading_level": null, "page_id": 1, "polygon": [[279.0, 39.75], [329.009765625, 39.75], [329.009765625, 50.1767578125], [279.0, 50.1767578125]]}, {"title": "2. Mutual Information", "heading_level": null, "page_id": 3, "polygon": [[88.5, 205.5], [207.087890625, 205.5], [207.087890625, 216.369140625], [88.5, 216.369140625]]}, {"title": "2.1 Shannon's Definition", "heading_level": null, "page_id": 3, "polygon": [[88.5, 281.25], [208.5, 281.25], [208.5, 292.166015625], [88.5, 292.166015625]]}, {"title": "2.2 Bounds Relating Bayes Error Rate to Mutual Information", "heading_level": null, "page_id": 4, "polygon": [[89.25, 135.0], [380.25, 135.0], [380.25, 145.3095703125], [89.25, 145.3095703125]]}, {"title": "2.3 Objective of Learning Feature Transforms", "heading_level": null, "page_id": 4, "polygon": [[89.25, 263.25], [308.25, 263.25], [308.25, 274.18359375], [89.25, 274.18359375]]}, {"title": "3. <PERSON>yi Entropy Reduces to Pairwise Interactions", "heading_level": null, "page_id": 4, "polygon": [[89.2001953125, 631.5], [348.75, 631.5], [348.75, 643.5], [89.2001953125, 643.5]]}, {"title": "3.1 Renyi Entropy", "heading_level": null, "page_id": 5, "polygon": [[89.05078125, 147.75], [179.25, 147.75], [179.25, 159.0380859375], [89.05078125, 159.0380859375]]}, {"title": "3.2 Parzen Density Estimation", "heading_level": null, "page_id": 5, "polygon": [[88.5, 404.25], [234.75, 404.25], [234.75, 414.94921875], [88.5, 414.94921875]]}, {"title": "4. Quadratic Mutual Information", "heading_level": null, "page_id": 6, "polygon": [[89.25, 284.25], [262.5, 284.25], [262.5, 295.06640625], [89.25, 295.06640625]]}, {"title": "4.1 Quadratic Divergence Measures", "heading_level": null, "page_id": 6, "polygon": [[89.25, 348.75], [261.0, 348.75], [261.0, 358.681640625], [89.25, 358.681640625]]}, {"title": "TORKKOLA", "heading_level": null, "page_id": 7, "polygon": [[279.0, 39.75], [328.5, 39.75], [328.5, 50.32177734375], [279.0, 50.32177734375]]}, {"title": "4.2 Quadratic Mutual Information in the Discrete Case", "heading_level": null, "page_id": 7, "polygon": [[89.05078125, 295.5], [350.25, 295.5], [350.25, 305.701171875], [89.05078125, 305.701171875]]}, {"title": "4.3 Information Potentials", "heading_level": null, "page_id": 8, "polygon": [[89.2001953125, 93.0], [216.0, 93.0], [216.0, 103.833984375], [89.2001953125, 103.833984375]]}, {"title": "4.4 Information Forces", "heading_level": null, "page_id": 9, "polygon": [[89.05078125, 92.25], [200.25, 92.25], [200.25, 104.02734375], [89.05078125, 104.02734375]]}, {"title": "5. Linear Feature Transforms", "heading_level": null, "page_id": 9, "polygon": [[89.25, 541.5], [243.75, 541.5], [243.75, 553.0078125], [89.25, 553.0078125]]}, {"title": "5.1 Matrix Parametrization", "heading_level": null, "page_id": 9, "polygon": [[88.82666015625, 591.0], [222.75, 591.0], [222.75, 602.5078125], [88.82666015625, 602.5078125]]}, {"title": "5.2 Visualization of Class Separation", "heading_level": null, "page_id": 10, "polygon": [[88.5, 230.25], [264.0, 230.25], [264.0, 241.3125], [88.5, 241.3125]]}, {"title": "TORKKOLA", "heading_level": null, "page_id": 11, "polygon": [[279.0, 39.75], [329.30859375, 39.75], [329.30859375, 50.41845703125], [279.0, 50.41845703125]]}, {"title": "6. Learning Nonlinear Feature Transforms", "heading_level": null, "page_id": 12, "polygon": [[89.25, 416.49609375], [310.5, 416.49609375], [310.5, 427.32421875], [89.25, 427.32421875]]}, {"title": "6.1 Radial Basis Function Networks", "heading_level": null, "page_id": 12, "polygon": [[88.5, 549.0], [260.25, 549.0], [260.25, 559.96875], [88.5, 559.96875]]}, {"title": "6.2 Classification Experiments with Linear and Non-Linear Transforms and Feature\nSelection", "heading_level": null, "page_id": 13, "polygon": [[89.2001953125, 363.0], [486.75, 363.0], [486.75, 387.4921875], [89.2001953125, 387.4921875]]}, {"title": "TORKKOLA", "heading_level": null, "page_id": 15, "polygon": [[279.0, 39.75], [329.009765625, 39.75], [329.009765625, 50.2734375], [279.0, 50.2734375]]}, {"title": "7. Reducing Computation", "heading_level": null, "page_id": 15, "polygon": [[89.25, 562.5], [223.5, 562.5], [223.5, 573.50390625], [89.25, 573.50390625]]}, {"title": "8. Some Limitations of Feature Transform and Feature Selection Methods", "heading_level": null, "page_id": 16, "polygon": [[89.25, 285.75], [468.75, 285.75], [468.75, 297.38671875], [89.25, 297.38671875]]}, {"title": "8.1 Concatenating Noise to Data", "heading_level": null, "page_id": 16, "polygon": [[88.5, 526.5], [243.0, 526.5], [243.0, 536.765625], [88.5, 536.765625]]}, {"title": "8.2 Embedding Data in Noise", "heading_level": null, "page_id": 17, "polygon": [[88.5, 441.75], [228.75, 441.75], [228.75, 452.4609375], [88.5, 452.4609375]]}, {"title": "9. Previous Work", "heading_level": null, "page_id": 18, "polygon": [[88.5, 402.0], [180.75, 402.0], [180.75, 413.7890625], [88.5, 413.7890625]]}, {"title": "10. Conclusion", "heading_level": null, "page_id": 19, "polygon": [[89.25, 594.75], [167.25, 594.75], [167.25, 606.375], [89.25, 606.375]]}, {"title": "Appendix A: Reducing Computation by <PERSON> Sampling", "heading_level": null, "page_id": 20, "polygon": [[89.2001953125, 360.0], [393.0, 360.0], [393.0, 371.63671875], [89.2001953125, 371.63671875]]}, {"title": "Appendix B: Reducing Computation by Gaussian Mixture Model Mappings", "heading_level": null, "page_id": 21, "polygon": [[89.25, 376.5], [477.75, 376.5], [477.75, 388.072265625], [89.25, 388.072265625]]}, {"title": "References", "heading_level": null, "page_id": 22, "polygon": [[88.5, 208.5], [146.35107421875, 208.5], [146.35107421875, 220.236328125], [88.5, 220.236328125]]}, {"title": "TORKKOLA", "heading_level": null, "page_id": 23, "polygon": [[279.0, 39.75], [329.607421875, 39.75], [329.607421875, 50.22509765625], [279.0, 50.22509765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 39], ["Text", 7], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 51], ["Text", 4], ["Footnote", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 46], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 52], ["Text", 5], ["TextInlineMath", 4], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 52], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 722, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 342], ["Line", 62], ["Text", 7], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 70], ["Text", 8], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1579, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 68], ["Text", 7], ["Equation", 6], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 665], ["Line", 109], ["Equation", 6], ["Text", 4], ["TextInlineMath", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 71], ["Text", 9], ["Equation", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 67], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 50], ["Text", 5], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 799, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 30], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 668, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["TableCell", 60], ["Line", 44], ["Text", 5], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1786, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 740], ["Span", 281], ["Line", 73], ["Table", 5], ["Caption", 5], ["TableGroup", 5], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 14994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 44], ["Text", 7], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 43], ["Text", 8], ["ListItem", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["TableCell", 139], ["Line", 61], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 10974, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["TableCell", 123], ["Line", 65], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 9682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 360], ["Line", 53], ["Text", 9], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 43], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 44], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 50], ["ListItem", 20], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 37], ["ListItem", 17], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Feature Extraction by Non-Parametric Mutual Information Maximization"}