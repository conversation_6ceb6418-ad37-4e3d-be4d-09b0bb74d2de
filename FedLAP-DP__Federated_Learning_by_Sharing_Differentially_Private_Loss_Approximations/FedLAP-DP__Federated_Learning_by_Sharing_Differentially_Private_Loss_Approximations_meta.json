{"table_of_contents": [{"title": "FedLAP-DP: Federated Learning by Sharing Differentially Private\nLoss Approximations", "heading_level": null, "page_id": 0, "polygon": [[52.5, 83.25], [558.75, 83.25], [558.75, 119.8828125], [52.5, 119.8828125]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[52.5, 244.5], [112.5, 244.5], [112.5, 256.39453125], [52.5, 256.39453125]]}, {"title": "KEYWORDS", "heading_level": null, "page_id": 0, "polygon": [[52.257568359375, 522.0], [116.25, 522.0], [116.25, 532.125], [52.257568359375, 532.125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[52.5, 560.25], [156.75, 560.25], [156.75, 570.796875], [52.5, 570.796875]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[52.5, 633.0], [159.0, 633.0], [159.0, 645.046875], [52.5, 645.046875]]}, {"title": "3 BACKGROUND", "heading_level": null, "page_id": 2, "polygon": [[316.458984375, 636.75], [412.5, 636.75], [412.5, 646.98046875], [316.458984375, 646.98046875]]}, {"title": "3.1 Federated Learning", "heading_level": null, "page_id": 2, "polygon": [[316.5, 652.5], [438.75, 652.5], [438.75, 663.609375], [316.5, 663.609375]]}, {"title": "3.2 Non-IID Challenges", "heading_level": null, "page_id": 3, "polygon": [[52.5, 597.75], [177.75, 597.75], [177.75, 608.30859375], [52.5, 608.30859375]]}, {"title": "3.3 Differential Privacy", "heading_level": null, "page_id": 3, "polygon": [[316.458984375, 285.75], [441.0, 285.75], [441.0, 295.83984375], [316.458984375, 295.83984375]]}, {"title": "Algorithm 1 FedLAP: Local Approximation", "heading_level": null, "page_id": 4, "polygon": [[52.5, 84.75], [216.75, 84.75], [216.75, 95.18115234375], [52.5, 95.18115234375]]}, {"title": "4 FEDLAP-DP", "heading_level": null, "page_id": 4, "polygon": [[52.5, 570.75], [130.5, 570.75], [130.5, 580.46484375], [52.5, 580.46484375]]}, {"title": "4.1 Overview", "heading_level": null, "page_id": 4, "polygon": [[52.5, 586.5], [124.5, 586.5], [124.5, 596.3203125], [52.5, 596.3203125]]}, {"title": "4.2 Local Approximation", "heading_level": null, "page_id": 4, "polygon": [[317.056640625, 620.25], [449.25, 620.25], [449.25, 630.3515625], [317.056640625, 630.3515625]]}, {"title": "4.3 Global Optimization", "heading_level": null, "page_id": 5, "polygon": [[316.5, 516.75], [444.0, 516.75], [444.0, 527.09765625], [316.5, 527.09765625]]}, {"title": "Algorithm 2 FedLAP: Global Optimization", "heading_level": null, "page_id": 6, "polygon": [[52.5, 84.75], [213.75, 84.75], [213.75, 95.18115234375], [52.5, 95.18115234375]]}, {"title": "4.4 Record-level DP", "heading_level": null, "page_id": 6, "polygon": [[52.5, 315.0], [158.5283203125, 315.0], [158.5283203125, 326.390625], [52.5, 326.390625]]}, {"title": "5 PRIVACY ANALYSIS", "heading_level": null, "page_id": 7, "polygon": [[52.5, 248.25], [173.021484375, 248.25], [173.021484375, 259.294921875], [52.5, 259.294921875]]}, {"title": "5.1 Definitions", "heading_level": null, "page_id": 7, "polygon": [[52.5, 264.75], [134.25, 264.75], [134.25, 275.150390625], [52.5, 275.150390625]]}, {"title": "5.2 Analysis", "heading_level": null, "page_id": 7, "polygon": [[52.5, 642.0], [122.25, 642.0], [122.25, 651.62109375], [52.5, 651.62109375]]}, {"title": "6 EXPERIMENTS", "heading_level": null, "page_id": 8, "polygon": [[52.5, 618.0], [148.9658203125, 618.0], [148.9658203125, 628.03125], [52.5, 628.03125]]}, {"title": "6.1 Setup", "heading_level": null, "page_id": 8, "polygon": [[52.5, 634.5], [106.681640625, 634.5], [106.681640625, 644.2734375], [52.5, 644.2734375]]}, {"title": "6.2 Data Heterogeneity", "heading_level": null, "page_id": 9, "polygon": [[52.5, 444.0], [175.5615234375, 444.0], [175.5615234375, 454.39453125], [52.5, 454.39453125]]}, {"title": "6.3 Privacy Protection", "heading_level": null, "page_id": 9, "polygon": [[316.16015625, 384.75], [435.0, 384.75], [435.0, 396.0], [316.16015625, 396.0]]}, {"title": "6.4 Ablation Study", "heading_level": null, "page_id": 9, "polygon": [[316.5, 633.75], [417.1640625, 633.75], [417.1640625, 644.66015625], [316.5, 644.66015625]]}, {"title": "6.5 Qualitative Results", "heading_level": null, "page_id": 10, "polygon": [[52.5, 652.5], [173.619140625, 652.5], [173.619140625, 662.8359375], [52.5, 662.8359375]]}, {"title": "6.6 Privacy Auditing", "heading_level": null, "page_id": 10, "polygon": [[316.5, 276.0], [426.75, 276.0], [426.75, 286.9453125], [316.5, 286.9453125]]}, {"title": "7 DISCUSSION", "heading_level": null, "page_id": 10, "polygon": [[316.5, 611.25], [399.0, 611.25], [399.0, 622.23046875], [316.5, 622.23046875]]}, {"title": "8 CONCLUSION", "heading_level": null, "page_id": 12, "polygon": [[52.5, 531.0], [141.75, 531.0], [141.75, 541.79296875], [52.5, 541.79296875]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 12, "polygon": [[317.25, 401.25], [435.75, 401.25], [435.75, 412.2421875], [317.25, 412.2421875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 12, "polygon": [[316.5, 600.75], [387.75, 600.75], [387.75, 611.015625], [316.5, 611.015625]]}, {"title": "APPENDICES", "heading_level": null, "page_id": 15, "polygon": [[52.5, 84.75], [122.25, 84.75], [122.25, 96.099609375], [52.5, 96.099609375]]}, {"title": "A ADDITIONAL ANALYSIS", "heading_level": null, "page_id": 15, "polygon": [[52.5, 108.75], [197.25, 108.75], [197.25, 121.04296875], [52.5, 121.04296875]]}, {"title": "A.1 Matching Criteria", "heading_level": null, "page_id": 15, "polygon": [[52.5, 126.0], [170.25, 126.0], [170.25, 136.9951171875], [52.5, 136.9951171875]]}, {"title": "A.2 Radius Selection", "heading_level": null, "page_id": 15, "polygon": [[316.5, 310.5], [427.623046875, 310.5], [427.623046875, 320.783203125], [316.5, 320.783203125]]}, {"title": "A.3 Qualitative Results", "heading_level": null, "page_id": 15, "polygon": [[316.5, 642.0], [440.25, 642.0], [440.25, 651.62109375], [316.5, 651.62109375]]}, {"title": "Figure 10: Architecture of ConvNets used in the federated experiments.", "heading_level": null, "page_id": 16, "polygon": [[160.5, 270.75], [450.75, 272.25], [450.75, 281.25], [160.5, 281.14453125]]}, {"title": "B COMPUTATION COMPLEXITY", "heading_level": null, "page_id": 16, "polygon": [[316.5, 652.5], [492.46875, 652.5], [492.46875, 662.8359375], [316.5, 664.3828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 105], ["Text", 12], ["SectionHeader", 4], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7681, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 110], ["Text", 8], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 72], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 879], ["Line", 169], ["Text", 11], ["TextInlineMath", 9], ["Equation", 5], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1061, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 794], ["Line", 128], ["Text", 9], ["Reference", 7], ["TextInlineMath", 6], ["SectionHeader", 4], ["Equation", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1297, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 869], ["Line", 193], ["Equation", 7], ["TextInlineMath", 7], ["Reference", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6193, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1102], ["Line", 144], ["Text", 6], ["TextInlineMath", 4], ["Reference", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 585, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1106], ["Line", 155], ["TextInlineMath", 17], ["Reference", 9], ["Text", 6], ["Equation", 6], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["Line", 129], ["TableCell", 32], ["Text", 6], ["Reference", 4], ["Caption", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 895, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 113], ["TableCell", 40], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 105], ["TableCell", 42], ["Text", 9], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 82], ["Figure", 4], ["Caption", 3], ["Text", 3], ["FigureGroup", 3], ["Reference", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2513, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Line", 182], ["Span", 178], ["Reference", 6], ["Text", 4], ["ListItem", 4], ["Caption", 3], ["SectionHeader", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1532, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 157], ["ListItem", 49], ["Reference", 49], ["ListGroup", 2], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 36], ["ListItem", 11], ["Reference", 11], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["Line", 115], ["TableCell", 14], ["Text", 11], ["SectionHeader", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1035, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Line", 192], ["Span", 129], ["Text", 4], ["Figure", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Code", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1448, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 112], ["Text", 5], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["Reference", 4], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 6, "llm_error_count": 0, "llm_tokens_used": 4403, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Line", 66], ["Span", 10], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1323, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/FedLAP-DP__Federated_Learning_by_Sharing_Differentially_Private_Loss_Approximations"}