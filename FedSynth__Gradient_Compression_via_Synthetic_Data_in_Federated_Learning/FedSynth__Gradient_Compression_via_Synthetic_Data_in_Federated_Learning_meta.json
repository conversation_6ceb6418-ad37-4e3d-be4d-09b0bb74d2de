{"table_of_contents": [{"title": "FedSynth: Gradient Compression via Synthetic Data in Federated\nLearning", "heading_level": null, "page_id": 0, "polygon": [[69.7763671875, 111.0], [539.68359375, 111.0], [539.68359375, 148.4033203125], [69.7763671875, 148.4033203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[284.484375, 298.5], [326.25, 298.5], [326.25, 308.408203125], [284.484375, 308.408203125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.187255859375, 438.0], [183.0322265625, 438.0], [183.0322265625, 452.07421875], [70.187255859375, 452.07421875]]}, {"title": "2 Background and Related Work", "heading_level": null, "page_id": 1, "polygon": [[70.5, 157.6845703125], [310.5, 157.6845703125], [310.5, 171.0263671875], [70.5, 171.0263671875]]}, {"title": "3 Communication via Synthetic Data", "heading_level": null, "page_id": 1, "polygon": [[70.5, 460.58203125], [340.5, 460.58203125], [340.5, 473.73046875], [70.5, 473.73046875]]}, {"title": "3.1 Formulation", "heading_level": null, "page_id": 1, "polygon": [[70.5, 491.25], [174.0, 491.25], [174.0, 502.34765625], [70.5, 502.34765625]]}, {"title": "3.2 Algorithm", "heading_level": null, "page_id": 2, "polygon": [[70.5, 480.75], [163.5, 480.75], [163.5, 491.90625], [70.5, 491.90625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 4, "polygon": [[70.5, 278.25], [183.0, 278.25], [183.0, 291.97265625], [70.5, 291.97265625]]}, {"title": "4.1 Experimental setup details", "heading_level": null, "page_id": 4, "polygon": [[70.5, 376.470703125], [261.0, 376.470703125], [261.0, 388.072265625], [70.5, 388.072265625]]}, {"title": "4.2 Comparison between FedSynth and Random Masking", "heading_level": null, "page_id": 4, "polygon": [[70.5, 527.87109375], [418.5, 527.87109375], [418.5, 539.47265625], [70.5, 539.47265625]]}, {"title": "4.3 Number of synthetic batches vs. batch size", "heading_level": null, "page_id": 5, "polygon": [[70.5, 224.25], [357.0, 224.25], [357.0, 235.318359375], [70.5, 235.318359375]]}, {"title": "5 Conclusion and Future works", "heading_level": null, "page_id": 5, "polygon": [[70.5, 379.5], [298.23046875, 379.5], [298.23046875, 392.326171875], [70.5, 392.326171875]]}, {"title": "References", "heading_level": null, "page_id": 6, "polygon": [[70.5, 70.5], [145.9775390625, 70.5], [145.9775390625, 84.3046875], [70.5, 84.3046875]]}, {"title": "A Appendix", "heading_level": null, "page_id": 7, "polygon": [[69.75, 158.25], [168.5390625, 158.25], [168.5390625, 174.75], [69.75, 174.75]]}, {"title": "A.1 Datasets and Models", "heading_level": null, "page_id": 7, "polygon": [[69.75, 189.75], [231.0, 189.75], [231.0, 201.8671875], [69.75, 201.8671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 51], ["Text", 7], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3009, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 45], ["Text", 4], ["SectionHeader", 3], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 760], ["Line", 77], ["TextInlineMath", 5], ["Equation", 3], ["Reference", 3], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 743], ["Line", 80], ["Text", 8], ["TextInlineMath", 8], ["ListItem", 6], ["Equation", 5], ["ListGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["TableCell", 120], ["Line", 39], ["SectionHeader", 3], ["Text", 3], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["TableCell", 70], ["Line", 27], ["SectionHeader", 2], ["Text", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1488, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 26], ["ListItem", 13], ["Reference", 12], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 34], ["Span", 31], ["Line", 13], ["SectionHeader", 2], ["TableOfContents", 1], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3139, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/FedSynth__Gradient_Compression_via_Synthetic_Data_in_Federated_Learning"}