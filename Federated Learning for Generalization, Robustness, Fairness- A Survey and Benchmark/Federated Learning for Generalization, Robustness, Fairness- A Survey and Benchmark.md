## Federated Learning for Generalization, Robustness, Fairness: A Survey and Benchmark

<PERSON><PERSON>, <PERSON><PERSON>, *Senior Member, IEEE* , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, *Senior Member, IEEE*, <PERSON><PERSON>, *Fellow, IEEE*

**Abstract**—Federated learning has emerged as a promising paradigm for privacy-preserving collaboration among different parties. Recently, with the popularity of federated learning, an influx of approaches have delivered towards different realistic challenges. In this survey, we provide a systematic overview of the important and recent developments of research on federated learning. Firstly, we introduce the study history and terminology definition of this area. Then, we comprehensively review three basic lines of research: generalization, robustness, and fairness, by introducing their respective background concepts, task settings, and main challenges. We also offer a detailed overview of representative literature on both methods and datasets. We further benchmark the reviewed methods on several well-known datasets. Finally, we point out several open issues in this field and suggest opportunities for further research. We also provide a public website to continuously track developments in this fast advancing field: [https://github.com/WenkeHuang/MarsFL.](https://github.com/WenkeHuang/MarsFL)

✦

**Index Terms**—Federated Learning, Generalization, Robustness, Fairness

<span id="page-0-0"></span>

# 1 INTRODUCTION

**C** URRENT development of deep learning has caused significant changes in numerous research fields, and had profound impacts on every nook and cranny of societal URRENT development of deep learning has caused significant changes in numerous research fields, and and industrial sectors, including computer vision [\[1\]](#page-15-0), [\[2\]](#page-15-1), [\[3\]](#page-15-2), [\[4\]](#page-15-3), [\[5\]](#page-15-4), [\[6\]](#page-15-5), [\[7\]](#page-15-6), natural language processing  $[8]$ , [\[9\]](#page-15-8), multimodal learning  $[10]$ ,  $[11]$ ,  $[12]$ , medical analysis  $[13]$ , and more. However, the success of deep learning heavily relies on large-scale data and there has been increasing awareness in the public and scientific communities for data privacy. Specifically, in the real world, data is commonly distributed among different entities (*e.g*., edge devices and companies). Due to the increasing emphasis on data sensitivity, strict legislations  $[14]$ ,  $[15]$ ,  $[16]$ ,  $[17]$  have been proposed to govern data collection and utilization. Thus, the traditional centralized training paradigm, which requires to aggregate data, fails to deploy in the practical setting. Driven by such realistic challenges, federated learning (FL) [\[18\]](#page-15-17), [\[19\]](#page-15-18), [\[20\]](#page-15-19),  $[21]$ ,  $[22]$ ,  $[23]$ ,  $[24]$  has emerged as a popular research field because it can train a global model for different participants without centralizing data owned by the distributed parties. Roughly speaking, the classical federated paradigm can be abstractly divided into the following two steps: server-side collaboration and client-side optimization. The former could be regarded as that a central server aggregates parameters from participants and then distributes the global model (averaged parameters) back. The latter represents that the client optimizes the distributed model on the local private data. Therefore, FL achieves the privacy-preserving collaboration to learn a shared model without data consolidation. Despite great advancements in federated learning [\[25\]](#page-15-24), [\[26\]](#page-15-25), [\[27\]](#page-15-26),

[\[28\]](#page-15-27), [\[29\]](#page-15-28), [\[30\]](#page-15-29), [\[31\]](#page-15-30), [\[32\]](#page-16-0), current federation has three major challenges as the following:

• **Generalization**. The distributed data is normally collected from different sources with diverse preferences and naturally brings the non-independent and identically distributed (Non-IID) characteristics [\[33\]](#page-16-1), [\[34\]](#page-16-2), [\[35\]](#page-16-3), [\[36\]](#page-16-4), [\[37\]](#page-16-5), [\[38\]](#page-16-6), [\[39\]](#page-16-7). Owing to this distribution discrepancy, there exists two major distribution shift types. **i)** Cross-Client Shift: Distributed data from different clients is inevitable to appear a different underlying distribution, incurring large heterogeneity among client data. Under this circumstance, each client optimizes toward its distinct local empirical minimum. resulting in divergent local optimization directions [\[40\]](#page-16-8), [\[41\]](#page-16-9), [\[42\]](#page-16-10), [\[43\]](#page-16-11), [\[44\]](#page-16-12). Thus, the federation presents slow convergence and unsatisfying performance. **ii)** Out-Client Shift: The federated system only incorporates knowledge from participating clients and optimizes toward fitting online client distribution. Therefore, it would be vulnerable under the Out-Client Shift [\[45\]](#page-16-13), [\[46\]](#page-16-14), [\[47\]](#page-16-15), [\[48\]](#page-16-16), [\[49\]](#page-16-17). Specifically, when deploying the federated model to outer client distribution (*i.e*., unseen testing domains), the federation fails to adapt to the target distribution and inevitable performance degradation is commonly observed. All in all, achieving a generalizable federated model acts as a foundational requirement to boost the convergence speed and performance accuracy for the real-world data distribution.

• **Robustness**. As a privacy-aware collaborative paradigm, ensuring federated robustness plays a crucial role in guaranteeing federated effectiveness. In practice, due to its distributed nature, federated learning fails to ensure the client trustworthiness and is highly vulnerable to different malicious behaviors. A small set of malicious clients can easily manipulate the training process by uploading poisoned local models to the server **i)** Byzantine Attack: malicious clients endeavor to falsify real network updates to attack the federated convergence and performance, [\[50\]](#page-16-18), [\[51\]](#page-16-19), [\[52\]](#page-16-20), [\[53\]](#page-16-21). Existing attacks mainly focus on poisoning client local

<sup>•</sup> *Wenke Huang, Mang Ye, Zekun Shi, Guancheng Wan, He Li, Bo Du, are with the School of Computer Science, Wuhan University, Wuhan, China. E-mail:*{*wenkehuang, yemang*}*@whu.edu.cn*

<sup>•</sup> *Qiang Yang is with the Department of Computer Science and Engineering, Hong Kong University of Science and Technology, Hong Kong, China. Email: <EMAIL>.*

Image /page/1/Figure/1 description: The image displays a table titled "TABLE 1 Overview of related surveys. See details in § 1.1." The table has columns for "Work", "Generalization", "Robustness", "Fairness", and "Benchmark". The "Work" column lists various research papers with their corresponding citation numbers, such as [arXiv'18] [77], [TIST'19] [21], and [WS4'20] [78]. The entries in the table indicate the type of shift addressed (e.g., "Cross-Client Shift", "Out-Client Shift") and whether the work covers "Generalization", "Robustness", "Fairness", or "Benchmark" aspects, often marked with a checkmark (✓) or specific notes like "Byzantine" or "Performance". The last row is labeled "Ours" and shows checkmarks across "Generalization", "Robustness", "Fairness", and "Benchmark".

training data, *i.e*., Data-Based Byzantine Attack [\[54\]](#page-16-33), [\[55\]](#page-16-34) and directly manipulating the model parameters, Model-Based Byzantine Attack [\[56\]](#page-16-35), [\[57\]](#page-16-36), [\[58\]](#page-16-37) **i)** Backdoor Attack: evils inject the backdoor trigger to induce the global model to learn misleading information, so as to predict attackerchosen labels for adversarial examples while maintaining stable performance on the main task [\[59\]](#page-16-38), [\[60\]](#page-16-39), [\[61\]](#page-16-40), [\[62\]](#page-16-41). Furthermore, backdoor attacks utilize the distributed paradigm to consider more stealthy trigger patterns via decomposing the global trigger pattern into separate local patterns to easily dodge various defensive mechanisms while successfully launching distributed backdoor attacks [\[63\]](#page-16-42), [\[64\]](#page-16-43).

• **Fairness**. Federated learning functions as a collaborative paradigm. The crucial cooperation pre-requirement is to satisfy the multi-party interest allocation for its sustained development viability [\[65\]](#page-16-44), [\[66\]](#page-16-31), [\[67\]](#page-16-32). Within this context, two primary interest conflicts groups emerge out. **i)** Collaboration Fairness: in the absence of equitable credit assignment and just reward allocation, clients lack the incentive to actively participate in the federated collaboration [\[68\]](#page-16-45), [\[69\]](#page-16-46), [\[70\]](#page-16-47). **ii)** Performance Fairness: the federated model may result in biased predictions, thereby yielding sub-optimal performance for clients characterized by limited data scale or distinct data distributions from the rest [\[71\]](#page-16-48), [\[72\]](#page-16-49), [\[73\]](#page-16-50). Consequently, the maintenance of both collaboration fairness and performance fairness significantly determines whether federated learning can function in a robust and enduring manner. These shortcomings severely hinder the adoption of federated learning in realistic applications, such as medical diagnosis [\[74\]](#page-16-30), autonomous driving [\[75\]](#page-16-51), and market finance analysis [\[76\]](#page-16-52), which lead to the increasing concern on the contemporary federated learning techniques.

<span id="page-1-0"></span>

### 1.1 Prior Surveys

As FL research has become a prominent research field in recent years, a large amount federated survey papers have emerged. Pioneering works [\[21\]](#page-15-20), [\[79\]](#page-16-24), [\[94\]](#page-17-9), [\[84\]](#page-16-29), [\[82\]](#page-16-27), [\[95\]](#page-17-10) basically focus on the conceptual framework and macro guidance and neglect in-depth exploration of specific challenges and problems. The majority focuses on the data heterogeneity problem [\[77\]](#page-16-22), [\[78\]](#page-16-23), [\[85\]](#page-17-0), [\[81\]](#page-16-26), [\[96\]](#page-17-11), [\[87\]](#page-17-2), [\[91\]](#page-17-6), [\[93\]](#page-17-8), [\[97\]](#page-17-12),  $[88]$  from different angles. For example  $[77]$ ,  $[87]$ ,  $[93]$  tackle the non-IID issue in FL and provide detailed discussion on different specific data heterogeneity forms with respective federated solutions. A few attempts respectively investigate the robustness [\[21\]](#page-15-20), [\[79\]](#page-16-24), [\[80\]](#page-16-25), [\[83\]](#page-16-28), [\[86\]](#page-17-1), [\[92\]](#page-17-7), [\[66\]](#page-16-31) and fairness [\[67\]](#page-16-32), [\[84\]](#page-16-29) issues. Some surveys discuss specific real-world applications such as medical contexts [\[98\]](#page-17-13) and smart healthcare [\[74\]](#page-16-30) but lack the universality for different scenarios. For instance, [\[74\]](#page-16-30) concentrates on the federated healthcare topic, which mainly discusses scalable smart healthcare networks and applications, including healthcare management, remote health monitoring, and so on. However, over-focusing on specific areas weakens their universality in other federated scenarios. Importantly, although there are few surveys such as  $[88]$ ,  $[87]$ ,  $[86]$ ,  $[92]$  with empirical experiments analysis, their framework appears a strong entanglement with the specific problems and thus lacks the high flexibility towards realistic federated problems. Besides, we provide fruitful SOTA methods implements, which largely benefit relative researchers. All in all, with the rapid advance of this field, **Generalization**, **Robustness**, and **Fairness** have been crucial aspects in federated learning. Generalization ensures federated performance on heterogeneous distribution. Robustness guarantees the federation to defend against malicious attacks. Fairness provides reasonable interest allocation to maintain sustainable multiple-party collaboration. Although there is a huge body of new literature, most existing surveys focus on the **narrow view** with **fragmented results**. In contrast, we argue that these three pieces interact with each other to jointly enhance the practical federation deployment and this is the first work to **simultaneously investigate** the related research development and **uniformly benchmark** multi-view experimental analysis on the Generalization, Robustness, and Fairness realms.

<span id="page-1-1"></span>

### 1.2 Contribution

In this paper, we introduce a comprehensive survey on federated learning and mainly focus on generalization, robustness, and fairness. Compared with existing surveys, this paper makes the following contributions:

- We delve into an in-depth exploration of federated learning and provide the first state-of-the-art and symmetric survey on the generalization for heterogeneous distribution, robustness for malicious attacks, and fairness for multiple-party interest in federated learning, including hundreds of papers in this fast-growing field.
- We select influential works published in prestigious journals and conferences and classify the existing federated methods, based on different task settings: Cross-Client Shift and Out-Client Shift in Generalizable Federated Learning, Byzantine Attack and Backdoor Attack in Robust Federated Learning, Reward Conflict and Prediction Biases in Fair Federated Learning. Except for the taxonomies, in-depth analysis about the pros and cons of these methods is also provided.

Image /page/2/Figure/1 description: This is a flowchart outlining a survey. The flowchart is organized into several main sections: Introduction (§ 1), Background (§ 2), Generalizations (§ 3), Robustness (§ 4), Fairness (§ 5), Setup (§ 6), Benchmark (§ 7), and Outlook (§ 8). Within each section, there are sub-topics with corresponding section numbers. For example, Introduction (§ 1) includes Prior Surveys (§ 1.1) and Contributions (§ 1.2). Generalizations (§ 3) includes Cross-Client Shift (§ 2.2.1), Out-Client Shift (§ 2.2.1), Cross Calibration (§ 3.2), and Unknown Generalization (§ 3.3). Robustness (§ 4) includes Byzantine Tolerance (§ 4.2) and Backdoor Defense (§ 4.3), and is associated with an icon of a devil. Fairness (§ 5) includes Reward Conflict (§ 2.2.3) and Prediction Biases (§ 2.2.3), and is associated with an icon of scales. There are arrows indicating the flow between some sections, such as from Robustness (§ 4) to Fairness (§ 5), and from Fairness (§ 5) to Collaboration Fairness (§ 5.2) and Performance Fairness (§ 5.3). A central circular element is labeled 'Close the Reliable Gap' and contains a graph. The Benchmark (§ 7) section includes Generalization Benchmark (§ 7.1), Robustness Benchmark (§ 7.2), and Fairness Benchmark (§ 7.3). The Outlook (§ 8) section includes Future Direction (§ 8.1) and Conclusion (§ 8.2).

<span id="page-2-2"></span>Fig. 1. **Overview of the survey**. Best viewed in color.

- We conduct a plentiful benchmark analysis on various federated scenarios with different solutions. Alongside a set of evaluation metrics: generalizable performance, defensive degree, and fair level, we comprehensively investigate the methods effectiveness.
- We discuss future research directions, which will assist the community to rethink and improve their current designs toward federated learning in real practicality. Meanwhile, in order to promote the development of this field.

The remainder of this paper is organized as follows. Fig. [1](#page-2-2) shows the structure of this survey. § [2](#page-2-0) gives some brief introduction on the history, terminology, and problem definition. We review representative papers on federated algorithms from three aspects: Generalizable Federated Learning in  $\S 3$ , Robust Federated Learning in § [4,](#page-7-0) and Fair Federated Learning in § [4,](#page-7-0) respectively. With respect to the experimental setting § [6,](#page-10-0) we introduce the federated datasets, implementation details, and thoroughgoing evaluation metrics. § [7](#page-11-0) conducts performance evaluation and analysis. While § [8.1](#page-14-1) raises open questions and potential directions. Finally, we make concluding remarks in § [8.2.](#page-15-31)

<span id="page-2-0"></span>

## 2 BACKGROUND

<span id="page-2-1"></span>

### 2.1 History and Terminology

Federated learning typically aims to enable systems or devices to collaboratively construct a global model while maintaining all private data locally, without the need for explicit data exchange among clients. This concept was introduced in milestone work such as [\[19\]](#page-15-18), [\[18\]](#page-15-17), [\[20\]](#page-15-19). Based on distribution characteristics, existing federated learning can be categorized into the following types [\[81\]](#page-16-26), [\[95\]](#page-17-10): Horizontal Federated Learning (HFL): Clients own the same feature space while having different sample spaces, and collaboratively optimize a shared global model [\[20\]](#page-15-19), [\[99\]](#page-17-14). Vertical Federated Learning (VFL): Each device has a dataset with distinct feature characteristics derived from the same sample identification space [\[100\]](#page-17-15), [\[101\]](#page-17-16). Thus, Vertical Federated Learning can be seen as performing featurelevel privacy-preserving computations. Federated Transfer Learning (FTL): When both sample spaces and feature

spaces are different among clients, FTL basically leverages the transfer learning technique to achieve knowledge communication  $[102]$ ,  $[103]$ ,  $[103]$ . In this paper, we focus on three crucial characteristics in federated learning and we conduct the uniform benchmark evaluation on the Horizontal Federated Learning [1](#page-2-3) (HFL) scenario. **I) Generalizable Federated Learning** (GFL): As a slang goes that when you hear hoofbeats, think of horses, not zebras. During federated learning, the decentralized data poses non-independent and identically distribution (called data heterogeneity) [\[104\]](#page-17-19),  $[105]$ ,  $[46]$ ,  $[91]$ . Then, there naturally exist two types of shift. First, Cross-Client Shift denotes the difference between empirical risk loss among participating clients [\[106\]](#page-17-21), [\[107\]](#page-17-22), [\[108\]](#page-17-23). Second, Out-Client Shift means the difference in expected risk between participating and non-participating clients [\[46\]](#page-16-14), [\[109\]](#page-17-24), [\[110\]](#page-17-25). **II) Robust Federated Learning** (RFL): Recent works show that standard federated learning is vulnerable to malicious attacks. One stream, Byzantine Attack, investigates polluting the local data or the uploaded model to jeopardize the discrimination and convergence of the federated model  $[50]$ ,  $[111]$ ,  $[112]$ . The other group, Backdoor Attack aims to induce the local model to incorporate misdefined information and activate the backdoor trigger to achieve malicious targeted prediction [\[113\]](#page-17-28), [\[62\]](#page-16-41), [\[114\]](#page-17-29), [\[115\]](#page-17-30). **III) Fair Federated Learning** (FFL): Fairness in federated learning mainly encompasses two aspects [\[116\]](#page-17-31), [\[66\]](#page-16-31), [\[89\]](#page-17-4). During the training stage, clients need to optimize the distributed model on their local data. The local updating process incurs computation cost to train the model and the training effectiveness largely depends on the data quality [\[68\]](#page-16-45), [\[117\]](#page-17-32). Consequently, the computation cost and data value should be carefully considered to ensure the Collaboration Fairness. As the saying goes *No pain, no gain*, federated learning needs to estimate reward to incentivize the client to contribute to the federated system. Besides, given the distributed data distribution, fitting different client data poses varying difficulty degrees. Naturally, during the inference period, the shared model would manifest the biased performance of minority or marginalized groups [\[71\]](#page-16-48), [\[118\]](#page-17-33),

<span id="page-2-3"></span>1. We omit the word horizontal for brevity.

[\[119\]](#page-17-34). Thus, maintaining Performance Fairness becomes an essential objective to mitigate performance disparities.

<span id="page-3-0"></span>

### 2.2 Problem Definition

Following the standard federated learning setup [\[20\]](#page-15-19), [\[36\]](#page-16-4), [\[40\]](#page-16-8), [\[106\]](#page-17-21), [\[120\]](#page-17-35), suppose there are  $M$  clients, (indexed by  $i$ ) with respective private data  $D_i$ .  $N_i \!=\! |D_i|$  means the private data scale for the  $i^{th}$  client private dataset. We further assume each data sample  $(x, y)$ , where x is the input attribute and  $y$  is the label. The local data follows the distribution  $\mathbb{P}_i(x, y)$ . We denote the global model parameter as  $w = f \circ g$ , where  $f$  means the network backbone and  $g$  represents the uniform classifier. For the query sample  $\xi = (x, y)$ , The f maps  $x$  into the  $d$  dimensional feature vector  $h=f(x)\in\mathbb{R}^d.$ g maps feature h into logits output  $z = g(h) \in \mathbb{R}^{|\mathcal{C}|}$ , where  $C$  denotes classification set. Formally, federated solutions generally seek to learn an *ideal* global model, w ∗ to minimize the weighted empirical loss among clients:

<span id="page-3-3"></span>
$$
w^* = \min_w \sum_{i=1}^M \alpha_i F_i(w, D_i), \qquad (1)
$$

where  $\alpha_i$  denotes the pre-allocated aggregation weight  $(\sum_{i=1}^{N} \alpha_i = 1)$  and is normally based on the data scale:  $\alpha_i = \frac{N_i}{\sum_i N_i}$ , or client scale:  $\alpha_i = \frac{1}{M}$ .  $F_i(w, D_i)$  represents the empirical loss. We define the  $L_i$  as the client-specific loss function and further disassemble the federated process into the following three steps:

<span id="page-3-4"></span>
$$
w_i \leftarrow w \quad \text{Distribute}
$$

$$
\min_{w_i} \mathbb{E}_{\xi \in D_i} [L_i(w_i, \xi)] \quad \text{Optimize} \hfill (2)
$$

$$
w = \sum_{i=1}^{M} \alpha_i w_i \quad \text{Aggregate}
$$

<span id="page-3-2"></span>

#### 2.2.1 Distributed Shift in FL

The isolated data distributions are often non-independent and non-identically distributed (non-IID), reflecting local distribution differences among clients [\[36\]](#page-16-4), [\[121\]](#page-17-36), [\[122\]](#page-17-37), [\[47\]](#page-16-15), [\[123\]](#page-17-38). The local distribution  $\mathbb{P}_i(x, y)$  can be considered as  $\mathbb{P}_i(x|y)\mathbb{P}_i(y)$ . According to how the distribution  $\mathbb{P}_i(x, y)$  is defined, distributed shift can be broadly categorized into two classes: Cross-Client Shift and Out-Client Shift. • **Cross-Client Shift**. In the distributed manner, different clients normally hold the local data with different distributions. Thus, there exists the cross-client shift, which brings the distinct local empirical risk minima. Thus, the divergent client updating direction hinders the federated convergence speed and weakens federated effectiveness.

**i)** Label Skew: The label distributions  $\mathbb{P}_i(y)$  on the clients are distinct, and the conditional feature distribution  $\mathbb{P}_i(x|y)$ is shared across clients. For instance, different hospitals specialize in distinct diseases and would hold inconsistent medical records ratio. Experiments normally leverage the Dirchlet sampling [\[124\]](#page-17-39) to mimic the Label Skew. Furthermore, the Label Skew is formulated as:

$$
\mathbb{P}_i(y) \neq \mathbb{P}_j(y) \n\mathbb{P}_i(x|y) = \mathbb{P}_j(x|y),
$$
\n(3)

<span id="page-3-6"></span>where  $i$  and  $j$  denotes arbitrary clients in federation.

**ii)** Domain Skew: As for the same label space, distinct feature distribution exists among different participants [\[37\]](#page-16-5), [\[120\]](#page-17-35), [\[125\]](#page-17-40). For example, cats may vary in coat colors

and patterns in different body parts and we provide the mathematical definition in the following form:

$$
\mathbb{P}_i(x|y) \neq \mathbb{P}_j(x|y) \n\mathbb{P}_i(y) = \mathbb{P}_j(y).
$$
\n(4)

<span id="page-3-5"></span>• **Out-Client Shift**. As shown in Eq. [\(1\)](#page-3-3), the federated system fits and promotes performance in cross-client distribution. But when deploying the federated model to outer client distribution (*i.e*., unseen domains), denoted as o, nonparticipating client data exhibits the distributional shift compared to the participating client private data. Therefore, the performance drop is commonly observed [\[45\]](#page-16-13) because it fails to consider the underlying domain shift on the unseen distribution, an issue named Out-Client Shift. In our work, we conduct experiments based on the feature-level distribution skew and provide the following definition:

$$
\overline{\mathbb{P}}_i(x|y) \neq \mathbb{P}_o(x|y) \n\mathbb{P}_i(y) = \mathbb{P}_o(y).
$$
\n(5)

<span id="page-3-1"></span>

#### 2.2.2 Malicious Behavior in Federated Learning

Participants are uncontrollable in the distributed setting, some of them are malicious and aim to destroy the federated paradigm. We broadly divide adversarial behavior into two types, based on their destroying goal. As for Byzantine Attack [\[126\]](#page-17-41), [\[53\]](#page-16-21), [\[111\]](#page-17-26), it is untargeted attack and the adversary objective is to hinder the model from achieving a near-optimal performance on the major task. As for Backdoor Attack  $[59]$ ,  $[60]$ ,  $[61]$ , it could be assumed as the targeted attack, which ensures the optimized model presents differently on certain targeted sub-tasks while maintaining good overall performance on the primary task.

• **Byzantine Attack**. Baleful client uploads the modified information to inhibit federated convergence and performance from the data-based and parameter-based angles.

**i)** Data-Based Byzantine Attack. As for the data-based attacks [\[50\]](#page-16-18), [\[51\]](#page-16-19), malicious clients would intentionally pollute the local data to corrupt the learned model. We mainly consider the following two kinds and  $\epsilon$  denotes the noise rate that the label is flipped from the clean to the noisy class: • Symmetry Flipping (SymF) [\[54\]](#page-16-33): The original label will be

flipped to any wrong classes with the equal ratio.

<span id="page-3-8"></span>
$$
\text{SymF} = \left[\begin{array}{cccc} 1 - \epsilon & \frac{\epsilon}{|C|-1} & \cdots & \frac{\epsilon}{|C|-1} \\ \frac{\epsilon}{|C|-1} & 1 - \epsilon & \cdots & \frac{\epsilon}{|C|-1} \\ \vdots & \vdots & \ddots & \frac{\epsilon}{|C|-1} \\ \frac{\epsilon}{|C|-1} & \frac{\epsilon}{|C|-1} & \frac{\epsilon}{|C|-1} & 1 - \epsilon \end{array}\right].\tag{6}
$$

|C|−1 |C|−1 |C|−1 • Pair Flipping (PairF) [\[55\]](#page-16-34): The original class label would only be flipped to a similar wrong semantic.

<span id="page-3-7"></span>
$$
\text{PairF} = \left[ \begin{array}{cccc} 1 - \epsilon & \epsilon & \cdots & 0 \\ 0 & 1 - \epsilon & \cdots & 0 \\ \vdots & \vdots & \ddots & \epsilon \\ \epsilon & 0 & 0 & 1 - \epsilon \end{array} \right]. \tag{7}
$$

**ii)** Model-Based Byzantine Attack. With respect to parameter-based [\[50\]](#page-16-18), [\[113\]](#page-17-28), [\[127\]](#page-17-42), [\[63\]](#page-16-42), [\[62\]](#page-16-41), [\[57\]](#page-16-36), [\[114\]](#page-17-29), [\[57\]](#page-16-36), evil participants manipulate uploading model parameters before sharing with the server. We utilize the uploading gradient of the  $k$  participant as an example. For kind clients, they faithfully upload the gradient change as  $\nabla_k$ . But malicious clients deliberately send distorted signals. We majorly consider the following attacks types:

• Random Noise (RanN): Straightforwardly modify the neural network via random sampling values as:

<span id="page-4-3"></span>
$$
\nabla_k = *.\t\t(8)
$$

∗ denotes the arbitrary values and normally leverages Gaussian Distribution or default initialization function to generate the parameter distortion.

- Little Is Enough (LIE) [\[56\]](#page-16-35): Assume the complete knowledge of the gradients of benign clients. Add a very limited amount of noise to aggregation. This kind of distortion is significant enough to negatively affect the global model but subtle enough to evade detection by the Byzantine Tolerance solutions. Specifically, it calculates the average value  $\mu$  and standard deviation  $\sigma$  on the benign ones. Then, measure the coefficient *z* on the overall clients. The malicious updates is formed as  $\mu + z\sigma$ .
- Fang [\[58\]](#page-16-37): Formulate the attacks as optimization-based model poisoning tailored to different defensive methods. Specifically, Fang crafts the local models on the compromised clients such that the global model deviates the most towards the inverse of the the direction along which the previous normal global model would change.
- Min-Max (MiMa) [\[128\]](#page-17-43): Ensure that the evil gradients lie close to the benign gradient group. We calibrate the malicious gradient to ensure that its maximum distance from any other gradient is limited by the maximum distance between benign gradients as the following form:

$$
\arg\max_{\gamma} \max_{i \in [n]} ||\nabla_k - \nabla_i||_2 \le \max_{i,j \in [n]} ||\nabla_i - \nabla_j||_2,
$$
  
$$
\nabla_k = AVG(\nabla_{\{i \in [n]\}}) + \gamma \nabla^p,
$$
\n(9)

where  $\nabla^p$  means the perturbation vector,  $\gamma$  is the learnable scaling coefficient and  $[n]$  is the benign client clique.

• Min-Sum (MiSu)  $[128]$ : The objective is to ensure that the sum of squared distances between the malicious gradient and all benign gradients remains below an upper bound, smaller than the sum of squared distances between any benign gradient and the other benign gradients as:

<span id="page-4-4"></span>
$$
\arg \max_{\gamma} \sum_{i \in [n]} ||\nabla_k - \nabla_i||_2 \le \max_{i \in [n]} \sum_{j \in [n]} ||\nabla_i - \nabla_j||_2,
$$
  
$$
\nabla_k = AVG(\nabla_{\{i \in [n]\}}) + \gamma \nabla_p.
$$
 (10)

• **Backdoor Attack**. Compared with direct performance degradation, Backdoor Attack focuses on injecting a backdoor trigger pattern into the existing model while retaining the major task accuracy  $[113]$ ,  $[129]$ ,  $[130]$ . Specifically, we define the trigger pattern as the  $\Phi$  and the modified sample as  $\tilde{x} = (1 - \mathbf{m}) \odot x + \mathbf{m} \odot \mathbf{\Phi}$ . Thus, the original local optimization direction in Eq. [\(2\)](#page-3-4) would be reformulated into:

<span id="page-4-2"></span>
$$
\min_{w_i} \mathbb{E}_{(x,\widetilde{x},y)\in D_i}[L_i(w_i,x,y) + \varpi \underbrace{L(w_i,\widetilde{x},\widetilde{y})}_{Backdoor}].
$$
\n(11)

The  $\widetilde{y}$  means the preset attack target predictions. The **m** means the trigger location mask. The hyper-parameter  $\varpi$ is utilized to control the trade-off between the original common empirical risk minimization and the backdoor injection loss function. Prior backdoor attacks are normally motivated by centralized backdoor attacks and assume that each malicious attacker independently trains their local models, without any collusion among them  $[62]$ ,  $[114]$ . Therefore, they utilize the same backdoor trigger, the poisoned local models tend to appear similar updating trend, largely deviated from the benign optimizing direction [\[131\]](#page-17-46). Recently, advanced attacks focus on the distributed backdoor paradigm [\[63\]](#page-16-42),

[\[64\]](#page-16-43), [\[132\]](#page-17-47), [\[132\]](#page-17-47) and dynamic backdoor solution [\[133\]](#page-17-48), [\[134\]](#page-17-49) to evade the secure detection strategies.

<span id="page-4-0"></span>

#### 2.2.3 Interest Conflict in Federated Learning

Unlike the traditional centralized training paradigm, which optimizes on local data, federated learning involves multiple clients collaboration. Thus, as a saying goes *No rules, no justice*. The federation needs to carefully deal with the client interest conflict to satisfy the expected performance improvement and reasonable benefit allocation [\[135\]](#page-17-50), [\[67\]](#page-16-32). We mainly discuss several types of interest conflict:

• **Reward Conflict**. Existing federated literature is normally under the assumption that data owners have an inherent willingness to participate in the federated system [\[20\]](#page-15-19), [\[36\]](#page-16-4), [\[136\]](#page-17-51). However, during the federated process, the contributions of various clients are not uniform due to differences in local data value and computational costs [\[137\]](#page-18-0). Consequently, it becomes necessary to ensure that the client reward is proportionate to its contribution to the federation. Specifically, a participant contributing significantly should receive a higher reward than one with a lower contribution. Normally, existing research leverages the Game theory [\[138\]](#page-18-1), which investigates the strategic interactions among rational agents. In our work, we utilize the Shapley Value [\[139\]](#page-18-2), which is a marginal contribution-based scheme from cooperative game theory  $[140]$ ,  $[141]$  to evaluate the client contribution. To be precise, we consider the standard test dataset  $u$  for performance evaluation. We denote the online client set  $S \subseteq M$  and  $D_S$  as the union dataset set. The overall accuracy on testing set  $u$  accuracy of federated model w, optimized on  $D<sub>S</sub>$ , is defined as  $A(w, D<sub>S</sub>, u)$ , abbreviated as  $\tilde{A}_S^u$ . The Shapley value for the client i,  $\nu_i(w,D_S,D_i,u)$ , abbreviated as  $\nu_i$ , is formulated as the following form:

<span id="page-4-1"></span>
$$
\nu_i = \frac{\rho}{|M|} \sum_{S \subseteq M \setminus \{i\}} \frac{A_{S \cup \{i\}}^u - A_S^u}{\binom{|M| - 1}{|S|}},\tag{12}
$$

where  $\rho$  means the constant parameter to rescale the shapely value. Via the Eq.  $(12)$ , we assess the importance of client contribution via evaluating its marginal effect for the respective final model accuracy.

• **Prediction Biases**. Due to the data heterogeneity in the federated paradigm, it is possible that the federated performance would vary significantly with uneven performance on different distributions [\[71\]](#page-16-48), [\[72\]](#page-16-49). For instance, under the Domain Skew (Eq.  $(4)$ ), private data is normally derived from different domains with diverse distribution, *e.g*., MNIST [\[142\]](#page-18-5) and SVHN [\[143\]](#page-18-6) in the Digits scenario. Regrettably, solving the optimization in Eq. [\(1\)](#page-3-3) does not inherently promote the shared global model to produce uniform predictive accuracy across multiple domains, referred to as Prediction Biases. Specifically, due to the fitting difficulty and similarity correlation  $[144]$ , the federated model would be biased towards specific domains and thus present inferior performance on those domains with hard or distinct distri-bution with others [\[145\]](#page-18-8), [\[119\]](#page-17-34). We mathematically define the metric to evalutate the Prediction Biases degree as:

$$
\zeta = \sigma \{ \mathcal{A}^u \}_{u \in \mathcal{U}} \tag{13}
$$

The  $\sigma$  means the standard deviation and  $\mathcal{U}$  denotes the testing dataset collection. We assume that all clients join the federation and omit the  $A_M^T$  as  $A^T$ . The larger the  $\zeta$  is, the more biased the predictive results are.

<span id="page-5-0"></span>

# 3 GENERALIZABLE FEDERATED LEARNING

<span id="page-5-2"></span>

### 3.1 Generalization Metrics

To achieve the generalization property during federation, existing methodologies for Cross-Client Shift and Out-Client Shift problems, are divided into two major streams: Cross Calibration (§ [3.2\)](#page-5-1) and Unknown Generalization (§ [3.3\)](#page-6-0).  $\bullet$  Cross-Client Accuracy:  $\mathcal{A}^{U}$ . As for the Cross-Client Shift methods § [3.2,](#page-5-1) it mainly considers that the client distribution is sampled from the single domain distribution, Label Skew or multiple domain distributions, Domain Skew. Thus, when it comes to the evaluation stage, we abstract it as a test dataset collection with an uncertain scale,  $U$ . We default assume that clients are sampled from several source domains and there is a testing dataset collection  $\mathcal{U} = \{u\}$ and the unknown testing dataset  $O$ . We acquire the logits output  $(z=w(x))$  from the global model on the query testing sample,  $\xi = (x, y)$ . We measure the standard Top-1 accuracy metric on each specific testing dataset  $u \in U$ . We further utilize the average value to represent the performance in the following formulation:

$$
\mathcal{A}^{u} = \frac{\sum (\max(z) = y)}{|u|},
$$
  
$$
\mathcal{A}^{u} = \frac{1}{|u|} \sum_{u \in u} \mathcal{A}^{u}.
$$
 (14)

• **Out-Client Accuracy:**  $A^O$ . Similarly, with respect to the Out-Client Shift solutions § [3.3,](#page-6-0) it focuses on evaluating the unknown distribution. Thus, we directly measure the Top-1 accuracy metric on the unseen domain distribution  $O$  as the following formulation:

<span id="page-5-3"></span>
$$
\mathcal{A}^O = \frac{\sum (\max(z) = y)}{|O|}.
$$
 (15)

<span id="page-5-1"></span>

### 3.2 Cross Calibration

As for the Cross-Client Shift problem, each local distribution is highly heterogeneous with others, and the local objective of each party is inconsistent among clients. Thus, different client optimizes towards distinct local empirical minimization, bringing the divergent optimization direction Related methods focus on calibrating the client optimization direction via three major angles.

#### 3.2.1 Client Regularization

• **Global Neural Network.** The most intuitive method is to directly use a shared global model for local optimization guidance because it literally aggregates clients knowledge and naturally acts as the global knowledge representative. Some works focus on directly regulating the local network based on the parameter stiff  $[36]$ ,  $[33]$ ,  $[146]$  and parameter variance [\[147\]](#page-18-10), [\[43\]](#page-16-11), [\[148\]](#page-18-11), [\[149\]](#page-18-12), [\[150\]](#page-18-13). A handful of methods utilize the global model output on the private data to penalize the local direction  $[151]$ ,  $[40]$ ,  $[42]$ ,  $[152]$ , [\[99\]](#page-17-14), [\[153\]](#page-18-16). However, involving a global model in the local optimization process deeply enlarges the local computation cost and linearly increases with the parameter scale.

• **Global Statistic Information.** Although the global model represents the overall federated knowledge, it fails to provide more fine-grained information. A group of federated methods has developed to construct the class-wise representative, *e.g*. prototype [\[154\]](#page-18-17), [\[120\]](#page-17-35) and Gaussian distribution

TABLE 2 **Summary of essential characteristics for reviewed methods in Cross Calibration** (§ [3.2\)](#page-5-1).

| Methods                                 | Venue        | Highlight                                                      |               |
|-----------------------------------------|--------------|----------------------------------------------------------------|---------------|
| Global Neural Network                   |              |                                                                |               |
|                                         |              | Limitation: Linearly increase the local computation cost       |               |
| FedProx[36]                             | [MLSys'20]   | Parameter $\ell_2$ normalization                               |               |
| SCAFFOLD[147]                           | [ICML'20]    | Control variate                                                |               |
| MOON[40]                                | [CVPR'21]    | Feature-level contrastive learning                             |               |
| FedNTD[152]                             | [NeurIPS'22] | Decoupled distillation                                         |               |
| FedSeg[99]                              | [CVPR'23]    | Pixel-level contrastive learning                               |               |
| Global Statistic Information            |              |                                                                |               |
|                                         |              | Limitation: Reliance on data richness for ideal initialization |               |
| FedProc[154]                            | [arXiv'21]   | Prototypical contrast                                          |               |
| HarmoFL [155]                           | [AAAI'22]    | Normalized amplitude                                           |               |
| FedFA [156]                             | [ICLR'23]    | Gaussian augmentation                                          |               |
| FPL[120]                                | [CVPR'23]    | Cluseter and unbiased prototypes                               |               |
| Extra Network Architecture              |              |                                                                |               |
|                                         |              | Limitation: Compatibility difficulty and communication burden  |               |
| FedMLB[157]                             | [ICML'22]    | Multiple auxiliary branches                                    |               |
| FedCGAN[158]                            | [IJCAI'22]   | Conditional GAN                                                |               |
| ADCOL[159]                              | [ICML'23]    | Representation generator                                       |               |
| DaFKD[160]                              | [CVPR'23]    | Discriminator module                                           |               |
| Self-Driven Regularization              |              |                                                                |               |
|                                         |              | Limitation: Forgetting with unstable hyper-parameter           |               |
| FedRS[161]                              | [KDD'21]     | Restrict Softmax function                                      |               |
| FedAlign [162]                          | [CVPR'22]    | Final block Lipschitz constant                                 |               |
| FedSAM[44]                              | [ICML'22]    | Sharpness aware minimization                                   |               |
| FedLC [106]                             | [ICML'22]    | Calibrate logit via class probability                          |               |
| FedDecorr[163]                          | [ICLR'23]    | Dimensional decorrelation                                      |               |
| Federated Data Sharing                  |              |                                                                |               |
|                                         |              | Limitation: Pre-defined relevant data is not always available  |               |
| DC-Adam[164]                            | [CS'21]      | Pre-shared data for warm-up strategy                           |               |
| FEDAUX [165]                            | [TNNLS'21]   | Pretrain and distill on auxiliary data                         |               |
| Federated Data Enhancement              |              |                                                                |               |
|                                         |              | Limitation: Limited diversity and potential privacy leakage    |               |
| FedMix[166]                             | [ICLR'21]    | Close global Mixup via averaged data                           |               |
| FEDGEN[167]                             | [ICML'21]    | Data generator ensemble learning                               |               |
| Federated Data Selection                |              |                                                                |               |
|                                         |              | Limitation: Data and client-level unfairness                   |               |
| FedACS[168]                             | [IWQOS'21]   | Filter out poisoned data via clustering                        |               |
| Safe[169]                               | [TII'22]     | Thompson sampling low skew clients                             |               |
| Server Aggregation Reweighting          |              |                                                                |               |
|                                         |              | Limitation: Require qualified datasets and element evaluation  |               |
| FEDBE[170]                              | [ICLR'21]    | Bayesian ensemble inference                                    |               |
| Elastic [171]                           | [CVPR'23]    | Interpolate parameter via sensitivity                          |               |
| Server Adaptive Optimization            |              |                                                                |               |
|                                         |              | Limitation: Demand proxy dataset and elaborate joint-goal      |               |
| FedMD [172]                             | [NeurIPS'19] | Additional classifier for data distillation                    |               |
| FedDF [173]                             | [NeurIPS'20] | Ensemble distillation on related data                          |               |
| FedGKT[174]                             | [NeurIPS'20] | Server-side group knowledge transfer                           |               |
| FedOPT[175]                             | [ICLR'21]    | Federated adaptive server optimizer                            |               |
| FCCL [176]                              | [CVPR'22]    | Cross-Correlation on unrelated data                            |               |
| Methods                                 | Venue        | Highlight                                                      | Limitation    |
| Federated Domain Adaptation § 3.3.1     |              |                                                                |               |
| FADA [46]                               | [ICLR'20]    | Dynamic adversarial alignment                                  | ○: GAN [178]  |
| COPA [219]                              | [ICCV'21]    | Shared extractor & group heads                                 | ○: IBN [220]  |
| AEGR [221]                              | [ICME'23]    | Pseudo-label refinement                                        | ★: PGD [222]  |
| Federated Domain Generalization § 3.3.2 |              |                                                                |               |
| FedDG [47]                              | [CVPR'21]    | Sharing amplitude spectrum                                     | ★: Amplitude  |
| CCST [109]                              | [WACV'23]    | Cross-client style transfer                                    | ★: Style info |
| CSAC[223]                               | [TKDE'23]    | Layer semantic aggregation                                     | ○: Attention  |

 $[41]$ ,  $[156]$ . Some approaches make use of spectrum  $[155]$ and feature maps  $[177]$ . However, it is vital to note that generating reliable global statistical knowledge necessitates data enrichment. Without this preliminary, initial signals may introduce bias and result in biased federated models.

• **Extra Network Architecture.** A plethora of works switch to utilizing additional network structures such as generative adversarial networks  $[178]$ ,  $[167]$ ,  $[158]$ ,  $[160]$  and auxiliary global branch  $[174]$ ,  $[157]$ ,  $[151]$ . Nevertheless, they

largely restrict the participating network architecture selection scope, which needs to be compatible with the extra network architecture and increase the communication cost. • **Self-Driven Regularization.** A stream calibrates the client drift by enhancing the local learning generality in a selfdriven manner. Generally, it adopts the self-distillation paradigm [\[179\]](#page-18-42), [\[162\]](#page-18-25) or modified Cross Entropy term [\[180\]](#page-18-43), [\[161\]](#page-18-24), [\[106\]](#page-17-21), [\[163\]](#page-18-26). Although these methods get rid of the additional shared signals cost, they are normally hyperparameter sensitive and presents fragile under serious data heterogeneous scenarios.

#### 3.2.2 Client Augmentation

• **Federated Data Sharing.** To alleviate the damage of heterogeneous client data, directly sharing relevant datasets acts as a naive and simple solution during federation. Current solutions normally leverage the labeled samples for the warm-up operation [\[77\]](#page-16-22), [\[164\]](#page-18-27) and unlabeled instances for distillation [\[181\]](#page-18-44) and pretraining [\[165\]](#page-18-28) strategies. However, in scenarios characterized by data scarcity, it may be infeasible to select suitable related datasets for sharing.

• **Federated Data Enhancement.** The client shift problem originates from the inconsistency between the ideal global and local client distribution. A surge of efforts supplements local data to mimic the ideal data distribution via the batch normalization layer [\[182\]](#page-18-45), [\[183\]](#page-18-46), MixUp [\[166\]](#page-18-29), [\[184\]](#page-18-47), [\[185\]](#page-18-48), [\[186\]](#page-18-49), [\[187\]](#page-18-50), data generative generator [\[188\]](#page-18-51), [\[182\]](#page-18-45). However, federated data enhancement largely depends on the local data diversity and exists the privacy leakage potential.

• **Federated Data Selection.** Another direction proposes dynamic data selection without impacting the data collection and client optimization. Generally speaking, this approach either employs local data filtering strategies [\[169\]](#page-18-32), [\[189\]](#page-18-52) to select meaningful data while rejecting potentially tainted samples, or it establishes client selection paradigms [\[190\]](#page-18-53), [\[191\]](#page-19-0), [\[168\]](#page-18-31), [\[192\]](#page-19-1), [\[193\]](#page-19-2), [\[194\]](#page-19-3) that prioritize those with lower degrees of data heterogeneity. However, it is worth noting that while federated data selection can significantly boost convergence speed, it unavoidably introduces unfairness at both the data and client levels since it tends to favor the selection of mainstream data or clients while potentially ignoring other diversified samples.

#### 3.2.3 Server Operation

• **Server Aggregation Reweighting.** Compared to modifying local training paradigms for calibrating client shift, existing research has confirmed that default fixed parameter aggregation ( $\alpha_i$  in Eq. [\(1\)](#page-3-3)) also leads to slow convergence and accuracy drop because it fails to effectively calibrate client divergence  $[35]$ . Thus, a dynamic parameter aggregation framework is helpful to improve federated convergence and performance. Related solutions can be mainly divided into several types. Some techniques utilize different post-processing methods to mitigate the model drift, *e.g*., Bayesian non-parametric theory [\[195\]](#page-19-4), [\[196\]](#page-19-5), [\[197\]](#page-19-6), [\[170\]](#page-18-33), MCMC [\[198\]](#page-19-7), optimal transport [\[199\]](#page-19-8), gradient-based optimization  $[200]$ ,  $[201]$ , and shapley-value  $[202]$ . Another big family is built upon network characteristics [\[203\]](#page-19-12), [\[204\]](#page-19-13), [\[171\]](#page-18-34), [\[205\]](#page-19-14). However, they either require unlabeled auxiliary datasets in the server or involve element-wise parameter evaluation, which is time-consuming and laborious.

TABLE 3

**Summary of essential characteristics for reviewed solutions for Unknown Generalization (§ [3.3\)](#page-6-0).** ★ and ◦ respectively represent the **potential privacy leakage** and **network architecture modification**.

• **Server Adaptive Optimization.** Notably, adaptive optimization has proven to be an effective solution for tailoring models to specific tasks. One approach focuses on server-side fine-tuning by synthesizing relevant information to facilitate empirical risk minimization [\[206\]](#page-19-20), [\[207\]](#page-19-21), [\[208\]](#page-19-22), [\[209\]](#page-19-23), [\[210\]](#page-19-24), [\[174\]](#page-18-37) or conducting the knowledge distillation on public dataset for knowledge transferring [\[211\]](#page-19-25), [\[212\]](#page-19-26), [\[213\]](#page-19-27), [\[214\]](#page-19-28), [\[172\]](#page-18-35), [\[173\]](#page-18-36), [\[215\]](#page-19-29), [\[176\]](#page-18-39). Another stream investigates the server-side optimizing objective, including gradient-biased server optimizer [\[175\]](#page-18-38) and global local joint objectives [\[216\]](#page-19-30), [\[217\]](#page-19-31), [\[218\]](#page-19-32). Clearly, such implicit targetappearance modeling strategy hurts flexibility and adaptivity due to the requirement for the proxy dataset or elaborate optimization definition.

<span id="page-6-0"></span>

### 3.3 Unknown Generalization

Previous research has demonstrated that deep neural networks tend to empirically overfit training data, leading to over-confident predictions [\[224\]](#page-19-33), [\[225\]](#page-19-34). This phenomenon can be problematic in real-world scenarios [\[226\]](#page-19-35), where even minor variations,*i.e*. domain shift, in the characteristics of deployment examples can result in significant performance drop [\[227\]](#page-19-36), [\[228\]](#page-19-37). With respect to the federated system, most existing works only focus on improving model performance among clients, while ignoring model generalizability onto unseen domains outside the federation [\[229\]](#page-19-38), [\[46\]](#page-16-14), [\[47\]](#page-16-15), [\[45\]](#page-16-13). Existing efforts can be mainly divided into two groups based on the accessible stage of the unseen domain: Federated Domain Adaptation (FDA § [3.3.1\)](#page-6-1) and Federated Domain Generalization (FDG § [3.3.2\)](#page-7-2).

<span id="page-6-1"></span>

#### 3.3.1 Federated Domain Adaptation

There has been an explosive interest in the Federated Domain Adaptation (FDA) paradigm. FDA normally assumes to incorporate unlabeled data from target domains during the federated learning process. Closely related methods mainly divide into two factions.

• **Federated Domain Alignment.** The naive solution for handling domain shift with the target domain is to directly align and harmonize the multiple domain distributions. Relevant methods typically incorporate the contrastive learning [\[230\]](#page-19-39), [\[231\]](#page-19-40), knowledge distillation [\[232\]](#page-19-41), [\[233\]](#page-19-42), [\[234\]](#page-19-43), adversarial learning [\[221\]](#page-19-17), gradient matching [\[235\]](#page-19-44), [\[236\]](#page-19-45). In essence, this stream helps to align the representations of different domains, reducing the domain shift and thus enhancing the model generalizable ability across domains. However, a significant challenge stems from the problem

setup, wherein the target domain is used during optimization but may not be accessible in many real-world scenarios.

• **Federated Domain Disentanglement.** This line of methods focuses on separating knowledge into domain-invariant and domain-specific components. Specifically, [\[46\]](#page-16-14), [\[50\]](#page-16-18) explore the extension of adversarial adaptation techniques to the federated setting. [\[219\]](#page-19-15) explicitly disentangle domaininvariant feature extractor and the ensemble multiple domain-specific classifiers. [\[151\]](#page-18-14) utilize the Mixture of Experts theory [\[237\]](#page-19-46), [\[238\]](#page-19-47) to adjust the domain expert and domain generalist via gating mechanism. Nevertheless, it is important to note that these methods often rely on additional networks or the preservation of specialized modules to achieve the decoupling objective.

<span id="page-7-2"></span>

#### 3.3.2 Federated Domain Generalization

Federated Domain Generalization investigates deploying the models trained on sites of heterogeneous distributions, directly generalizing to unknown target clients with domain shift. Several pioneering works attempt to incorporate domain generalization problems into federated learning and can be generally categorized into two classes:

• **Federated Invariant Optimization.** A major stream of methods exploits calibrating the local training objective to alleviate the domain shift influence. Closely related solutions normally leverage the shared information, *e.g*., amplitude spectrum  $[47]$ , style distribution  $[109]$  and Iterative Naive Barycenter [\[110\]](#page-17-25) to learn domain invariant representation. Obviously, sharing data could be regarded as a breach of privacy [\[239\]](#page-19-48), [\[109\]](#page-17-24) and leads to a significant communication burden. Besides, a cluster of methods harness the additional modules or replace original network architecture to enhance the network generality and alleviate the domain biased trend, such as GAN [\[178\]](#page-18-41) in FedADG [\[240\]](#page-19-49), [\[241\]](#page-19-50), Adain [\[242\]](#page-19-51) in CCST [\[109\]](#page-17-24), and IBN [\[220\]](#page-19-16) in COPA [\[219\]](#page-19-15). However, implementing these approaches can be complicated because of adapting to various structures.

• **Federated Invariant Aggregation.** Yet another complementary line of work tries to modify the aggregation strategy to enhance the domain invariance ability. Some take inspiration from performance fairness to achieve multipdomain performance fairness in order to mitigate the generalization gap via the empirical variance balance [\[243\]](#page-20-0), agnostic distribution fusion [\[244\]](#page-20-1). A crowd of works delves into parameter aggregation from a fine-grained perspective, such as layer-wise semantic calibration [\[223\]](#page-19-19).

<span id="page-7-0"></span>

# 4 ROBUST FEDERATED LEARNING

### 4.1 Robustness Metrics

While federated learning has emerged as a promising solution for many realistic settings, it often assumes that all clients are trustworthy and provide reliable information for the federation. However, due to its distributed nature, FL is vulnerable to various malicious manipulations by rogue clients. Existing Robust Federated Learning strategies can be broadly categorized into two categories respectively towards Byzantine Attack and Backdoor Attack: Byzantine Tolerance  $(\S$  [4.2\)](#page-7-1) and Backdoor Defense  $(\S$  [4.3\)](#page-8-0). We provide the corresponding evaluation in the following.

<span id="page-7-5"></span>TABLE 4 **Summary of essential characteristics for reviewed Byzantine Tolerance solutions** (§ [4.2\)](#page-7-1).

| Methods                                                          | Venue        | Highlight                                  |
|------------------------------------------------------------------|--------------|--------------------------------------------|
| <i>Distance Base Tolerance</i> § 4.2.1                           |              |                                            |
| Limitation: Fail in data heterogeneous federated learning        |              |                                            |
| Multi Krum [126]                                                 | [NeurIPS'17] | Average candidate gradient via Krum        |
| FoolsGold [131]                                                  | [arXiv'18]   | Filter sybils via contribution similarity  |
| DnC [128]                                                        | [NDSS'21]    | SVD based spectral for outliers detection  |
| <i>Statistics Distribution Tolerance</i> § 4.2.2                 |              |                                            |
| Limitation: Require relative mathematical assumption             |              |                                            |
| Trim Median [245]                                                | [ICML'18]    | Remove via coordinatewise trimmed mean     |
| Bulyan [53]                                                      | [ICML'18]    | Agree each coordinate by major vectors     |
| RFA [246]                                                        | [TSP'22]     | Geometric median and smoothed Weiszfeld    |
| <i>Proxy Dataset Tolerance</i> § 4.2.3                           |              |                                            |
| Limitation: Require qualified dataset and homogeneity assumption |              |                                            |
| FLTrust [247]                                                    | [NDSS'21]    | Allocate score via ReLU-clipped similarity |

Sageflow [\[248\]](#page-20-5) [NeurIPS'21] Entropy filtering and loss reweighting

• Accuracy Decline Impact: *I*. The malicious attack goal for the Byzantine Attack  $\S$  [4.2](#page-7-1) is to hinder the federated performance compared with the accuracy of the global model  $(A^u)$ , optimized in the benign setting without any attack. We denote the  ${\cal A}_{Byz}^u$  as the performance under the Byzantine Attack. We define the attack impact,  $\mathcal{I}$ , to depict the accuracy drop as the following formulation:

<span id="page-7-6"></span>
$$
\mathcal{I} = \mathcal{A}^u - \mathcal{A}_{Byz}^u.
$$
 (16)

• Attack Success Rate:  $\mathcal{R}^u$ . For the Backdoor Attack § [4.3,](#page-8-0) we estimate the effectiveness by calculating the proportion of triggered samples classified as target labels. As illustrated in Eq. [\(11\)](#page-4-2), we construct the trigger testing sample as  $(\tilde{x}, \tilde{y})$ . Thus,  $R$  on the selected testing domain  $u$  is formed as:

<span id="page-7-7"></span>
$$
\mathcal{R}^{u} = \frac{\sum(\arg \max(\widetilde{z}) = \widetilde{y})}{|\widetilde{T}|}.
$$
 (17)

<span id="page-7-1"></span>

#### 4.2 Byzantine Tolerance

To combat byzantine attackers, designing robust aggregation has become an effective paradigm. Existing solutions can be generally categorized into three classes:

<span id="page-7-3"></span>

##### 4.2.1 Distance Base Tolerance

A group of robust aggregation strategy models normally compare the participating clients update differences and regard those significantly far from the overall direction as malicious clients and then exclude them from the aggregation process [\[126\]](#page-17-41), [\[131\]](#page-17-46), [\[249\]](#page-20-6), [\[112\]](#page-17-27), [\[128\]](#page-17-43), [\[250\]](#page-20-7). For example, Multi Krum  $[126]$  selects the candidate gradient that is the closest to its neighboring clients. FoolsGold [\[131\]](#page-17-46) leverages cosine similarity to identify malicious clients and allocate low weight. FABA  $[249]$  removes the outliers far from the mean value of the uploaded gradient. However, they basically rely on the data homogeneity hypothesis, where each client shares the independent and identically distribution data distribution. Therefore, these methods are not applicable under data heterogeneous federated learning.

<span id="page-7-4"></span>

# 4.2.2 Statistics Distribution Tolerance

A handful of robust aggregation schemes focus on constructing diverse statistical criteria to select the circumvent the evil clients  $[245]$ ,  $[53]$ ,  $[251]$ ,  $[246]$ . For instance, RFA [\[246\]](#page-20-3) calculates the geometric median with an alternating

minimization function, which which empirically appears rapid convergence and can be interpreted as a numerically stable version of the Weiszfeld algorithm. Notably, RFA gets rid of collecting the client individual contribution and is agnostic to the corruption level. Bulyan [\[53\]](#page-16-21) cooperates trimmed median [\[245\]](#page-20-2) to conduct a two-step metaaggregation algorithm. Despite the certain advantages, they are also sensitive to the degree of data heterogeneity. They normally hypothesize that distributed data is constrained into a certain range. Besides, these methods are generally based on the statistics assumption, which presents a strong prior limitation for the realistic application.

<span id="page-8-2"></span>

#### 4.2.3 Proxy Dataset Tolerance

One recent line of robust aggregation solutions [\[248\]](#page-20-5), [\[247\]](#page-20-4), [\[201\]](#page-19-10) leverages the proxy data to conduct the additional performance evaluation. The motivation behind this is that the proxy dataset normally shares a similar or constant semantic space with the private data and is assumed to be clean. Therefore, utilizing the proxy dataset for related evaluation modules is a reliable and convincing approach to effectively discriminate the benign and malicious ones. For example, Sageflow [\[248\]](#page-20-5) proposes an entropy-based filter and reweights aggregation based on empirical loss. FLTrust [\[247\]](#page-20-4) introduces ReLU-clipped cosine similarity and allocates high trust scores for those reliable clients. Notably, they depend on the auxiliary related data for examination, which hampers their practicability. Therefore, existing methods acquire strong assumptions for the data homogeneity or qualified related proxy dataset, as illustrated in Tab. [4.](#page-7-5)

<span id="page-8-0"></span>

### 4.3 Backdoor Defense

The potential backdoor attacks act as a serious threat to the federated learning system and have attracted a large number of interest in diverse defensive solutions to mitigate different backdoor attacks. Based on the distinct defensive kernel, the federated backdoor defenses can be classified into three major categories:

#### 4.3.1 Model Refinement Defense

This type of efforts focuses on refining the aggregated global model to erase the possible backdoor attacks such as finetuning [\[252\]](#page-20-9), logits distillation [\[172\]](#page-18-35), [\[173\]](#page-18-36), [\[253\]](#page-20-10), [\[176\]](#page-18-39), and Bayesian learning [\[195\]](#page-19-4), [\[196\]](#page-19-5), [\[197\]](#page-19-6), [\[170\]](#page-18-33). However, these strategies require a high-scale proxy dataset to tune the global model, which presents realistic deployment for the federated learning setting [\[201\]](#page-19-10). Besides, Model Refinement Defense fails to guarantee the backdoor erasing degree. Moreover, without the additional optimization regularization, it needs careful and complicated hyper-parameter configuration to optimize towards a satisfying condition to avoid the potential severe overfitting problem.

# 4.3.2 Robust Aggregation Defense

A group of solutions addresses this issue during the aggregation stage by excluding the malicious signals such as weights and gradients, from suspicious clients through anomaly behavior detection and dynamic weight allocation [\[126\]](#page-17-41), [\[245\]](#page-20-2), [\[53\]](#page-16-21), [\[254\]](#page-20-11), [\[255\]](#page-20-12). Importantly, a part of these solutions are initially designed to defend against Byzantine

attacks (as discussed in  $\S$  [4.2\)](#page-7-1) but have also proven to be effective in countering backdoor attacks. For example, Dim-Krum [\[255\]](#page-20-12) identifies abnormal and malicious client updates on a small fraction of dimensions with higher backdoor strengths, building on the Krum [\[126\]](#page-17-41). RLR [\[254\]](#page-20-11) adjusts the server learning rate through the sign information of client updates in terms of the dimension and round aspects. However, these solutions typically assume that each client distribution obeys the independent and identically distributed property, which does not hold for the heterogeneous federated learning setting [\[77\]](#page-16-22), [\[104\]](#page-17-19), [\[35\]](#page-16-3), [\[256\]](#page-20-13).

#### 4.3.3 Certified Robustness Defense

This family of methods aims to provide the certified robustness guarantee for each testing example, *i.e*., the prediction would not change even if some features in local training data of malicious clients have been modified within a certain constraint [\[257\]](#page-20-14), [\[258\]](#page-20-15), [\[259\]](#page-20-16), [\[260\]](#page-20-17), [\[261\]](#page-20-18), [\[262\]](#page-20-19), [\[263\]](#page-20-20). Specifically, ProvableFL [\[261\]](#page-20-18) acquires multiple global models via optimizing on a randomly selected client subset. When inferring the testing example label, we take the majority vote results among the global models. CRFL [\[260\]](#page-20-17) controls the global smoothness via the clipping and smoothing parameter operations. FLIP [\[263\]](#page-20-20) combines trigger inversion techniques during the federated training process. Yet, it either requires the storage cost for a large number of models or brings performance degradation due to heavy parameter interpretation operation. Besides, some pre-define possible attack types and thus are not suitable for the open world.

<span id="page-8-1"></span>

## 5 FAIR FEDERATED LEARNING

### 5.1 Fairness Metrics

Federated learning is not a *utopia*, which describes an imaginary community or society that holds highly desirable or near-perfect qualities for its members [\[264\]](#page-20-21). Federated learning unavoidably brings the interest conflict problem. Specifically, the client spends both computation and communication costs to optimize and upload the federated models [\[18\]](#page-15-17), [\[265\]](#page-20-22), [\[266\]](#page-20-23), [\[267\]](#page-20-24), [\[268\]](#page-20-25). Besides, with respect to the quality and quantity of the data, the data value varies among different clients [\[269\]](#page-20-26), [\[270\]](#page-20-27). Their contributions to the federated system would not be suitable to regard equally. This federated learning requires incentive mechanisms to distinctly measure contribution and reward benefits for each client [\[66\]](#page-16-31), [\[67\]](#page-16-32). Existing Fair Federated Learning strategies mainly deal with two types of unfairness: Reward Conflict and Prediction Biases, and thus naturally group into two methodology objective streams: Collaboration Fairness  $(S 5.2)$  $(S 5.2)$  and Performance Fairness  $(S 5.3)$  $(S 5.3)$ . We provide the respective evaluation metrics as the following form.

**• Contribution Match Degree:**  $\mathcal{E}$ **.** Evaluating the client importance acts as an important step to reasonably allocate the profit [\[70\]](#page-16-47), [\[271\]](#page-20-28). We argue that the reward mechanism is normally based on the pre-defined aggregation weight  $(\alpha$  in Eq. [\(1\)](#page-3-3)) to allocate reward. However, the realistic the client contribution could be could be directly reflected in the performance change. Specifically, we conduct the leave-oneout experiment and define the federated model without  $i$ 

client as  $w^{-i} = \frac{w - \alpha_i w_i}{1 - \alpha_i}$ . We measure the performance drop,  $\Gamma_i$ , if we remove the query client *i*. Then, we calculate  $\mathcal E$  as:

<span id="page-9-2"></span>
$$
\Gamma_{i} = \overline{\mathcal{A}} - \frac{1}{|\mathcal{U}|} \sum_{u \in \mathcal{U}} \mathcal{A}_{-i}^{u},
$$

$$
\Gamma = [\dots, \frac{\Gamma_{i}}{\sum_{i \in k} \Gamma_{i}}, \dots] \in \mathbb{R}^{|K|}, (18)
$$

$$
\mathcal{E} = \frac{\Gamma \cdot \alpha}{||\Gamma||_{2}||\alpha||_{2}}.
$$

The Eq. [\(18\)](#page-9-2) reveals that a Collaboration Fairness methodology would achieve a higher  $\mathcal E$  to reasonably allocate interest. • **Performance Deviation:** V. Performance Fairness aims to maintain both high average accuracy and uniform accuracy distribution. Following [\[72\]](#page-16-49), [\[272\]](#page-20-29), [\[145\]](#page-18-8), we utilize the standard deviation to measure the performance inconsistency of the selected algorithm across different testing distributions.

<span id="page-9-3"></span>
$$
\mathcal{V} = \sqrt{\frac{1}{|\mathcal{U}|} \sum_{u \in \mathcal{U}} (\mathcal{A}^u - \overline{\mathcal{A}})^2} \%, \tag{19}
$$

where  $A$  denotes the average performance on the overall testing distributions. The larger the  $\mathcal V$  is, the more biased the fedeated performance is.

<span id="page-9-0"></span>

### 5.2 Collaboration Fairness

Contribution evaluation plays a crucial role in federated learning by assessing each client's contribution in a privacyfriendly manner  $[273]$ ,  $[65]$ . Specifically, when one party contributes more, it may feel unfairly treated if all parties receive the same compensation regardless of their differing contributions. Hence, a fair contribution evaluation mechanism motivates clients to join the collaboration. Existing approaches basically follow the following ideologies.

#### 5.2.1 Individual Contribution Evaluation

The individualized assessment paradigm focuses on measuring the client contribution based on individual contribution via the locally relevant information and the specific task performance. Specifically, the client-representative information normally builds on data collection cost [\[274\]](#page-20-31), contract theory [\[275\]](#page-20-32), [\[276\]](#page-20-33), Stackelberg Game [\[277\]](#page-20-34), [\[278\]](#page-20-35), and computation bids [\[279\]](#page-20-36). As for the specific task performance, some methods are based on the reputation mechanism to record the clients contributions [\[70\]](#page-16-47), [\[280\]](#page-20-37), [\[275\]](#page-20-32), [\[281\]](#page-20-38), [\[281\]](#page-20-38), [\[282\]](#page-20-39), [\[279\]](#page-20-36). For example, CFFL [\[70\]](#page-16-47) evaluates the fairness by evaluating the validation performance on each client to represent the reputation metric. RRFL [\[280\]](#page-20-37) considers the updates divergence between each local and shared global model to represent the reputation value. Besides, several papers introduce different contribution metrics such as client bid and resource quality score [\[279\]](#page-20-36) and pair-wise mutual evaluation mechanism [\[65\]](#page-16-44). However, Individual Contribution Evaluation often assumes that both servers and clients are trustworthy, which can yield unreliable client contribution results in a semi-honest setting. Moreover, it may be less effective in dealing with significant data heterogeneity because clients with vastly different data distributions may exhibit noticeably different performances compared to others, resulting in the low reputation scores.

# 5.2.2 Marginal Contribution Evaluation

As federated learning is a multip-party collaboration paradigm, some work evaluates the client marginal contribution to the federated performance. Notably, existing solutions normally take inspiration from the cooperative game theory  $[138]$ ,  $[141]$ ,  $[140]$ , especially shapely value (SV) [\[139\]](#page-18-2) to measure the player value. Related solutions focus on efficient shapely value measurement under the federated scenarios [\[283\]](#page-20-40), [\[284\]](#page-20-41), [\[271\]](#page-20-28), [\[285\]](#page-20-42), [\[116\]](#page-17-31). For example, CGSV [\[271\]](#page-20-28) introduces the cosine gradient Shapley value to approximate the SV between the client and global updates. FedCE [\[116\]](#page-17-31) estimates client contribution in both gradient and data space for medical image segmentation on the local evaluation. However, these methods still face a heavy computation burden because the computational complexity of calculating SV is  $\mathcal{O}(2^n)$ . Furthermore, some require auxiliary dataset for performance validation, posing the additional data collection requirement.

<span id="page-9-1"></span>

### 5.3 Performance Fairness

The performance disparity means that the federated model presents a biased prediction preference, which appears in certain protected groups classification failure because of overfitting certain clients at the expense of other clients. Existing Performance Fairness methods mainly focus on achieving uniform performance from the local optimization and parameter aggregation aspects  $[71]$ ,  $[72]$ ,  $[147]$ ,  $[286]$ .

#### 5.3.1 Performance Debias Optimization

A set of solutions proposes to mitigate the biased global performance by modifying the local objective function to satisfy the specific fairness constraints. One type focuses on the single worst client  $[71]$ ,  $[72]$ ,  $[73]$ ,  $[287]$ . For example, AFL [\[71\]](#page-16-48) introduces the min-max approach to avoid overfitting to the particular client. However, naively focusing on the single worst objective can drag on the overall model utility. qFFL [\[72\]](#page-16-49) calculates the min-max performance of all clients and penalizes those with larger empirical loss via the hyper-parameter. Another group leverages the multiobjective optimization [\[272\]](#page-20-29), [\[118\]](#page-17-33), [\[115\]](#page-17-30), [\[288\]](#page-20-45), [\[289\]](#page-20-46). For instance, FedMGDA+ [\[272\]](#page-20-29) designs a constraint multiple gradient descent for a common descent direction. FCFL [\[118\]](#page-17-33) utilizes the surrogate maximum function to consider the multi-objective optimization to achieve Pareto optimality by controlling the gradient direction. However, these algorithms often assume that clients are benign and do not engage in malicious behavior against the federation. If a client maliciously reports its local empirical loss, it can lead to misleading updating directions [\[67\]](#page-16-32) and brings the federated optimization failure.

#### 5.3.2 Performance Debias Rewighting

A mainstream of solutions has been developed to reweight the parameter aggregation to alleviate biased performance. These methods primarily rely on various parameter variance signals, such as gradients  $[145]$ , predictive risk  $[290]$ , [\[243\]](#page-20-0), [\[116\]](#page-17-31), [\[119\]](#page-17-34). In particular, FedFV  $[145]$  utilizes the cosine similarity to measure gradient conflict and then modifies both magnitude and direction to avoid client con-flict. FedCE [\[116\]](#page-17-31) reduces the generalization gaps variance among different source domains to encourage optimization flatness and fairness. However, gradient estimation is based on the previous rounds and fails to synchronize with the

latest updates. Additionally, prediction evaluation naturally involves additional validation datasets, which poses a serious data requirement for the method application.

<span id="page-10-0"></span>

# 6 SETUP

<span id="page-10-1"></span>

### 6.1 Experimental Datasets

According to the different data heterogeneity, we divide the existing benchmark datasets into the following two groups.

<span id="page-10-5"></span>

#### 6.1.1 Label Skew Datasets

Existing research mainly utilizes the Dirichlet distribution:  $Dir(\beta)$  to simulate the Label Skew distribution (Eq. [\(3\)](#page-3-6)) for experimental analysis [\[36\]](#page-16-4), [\[40\]](#page-16-8), [\[154\]](#page-18-17), where  $\beta > 0$  is the concentration hyper-parameter to adjust the skewed level (imbalance degree of class composition). If  $\beta$  is set to a smaller value, the local distribution appears more imbalanced from the global distribution.

• **Cifar-10** [\[291\]](#page-20-48) contains 50, 000 images for training and 10,000 images for the validation. Its image size is  $32 \times 32$ within 10 categories.

• **Cifar-100** [\[291\]](#page-20-48) is a famous image classification dataset, containing  $32 \times 32$  images of 100 categories. Training and validating sets are composed of 50,000 and 10,000 images.

• **Tiny-ImageNet** [\[1\]](#page-15-0) is the subset of ImageNet with 100K images of size  $64 \times 64$  with 200 classes scale.

• **Fashion-MNIST** [\[292\]](#page-20-49) includes 70,000 28  $\times$  28 grayscale fashion products pictures with with ten categories.

<span id="page-10-4"></span>

#### 6.1.2 Domain Skew & Out-Client Shift Datasets

Both Domain Skew and Out-Client Shift settings consider the tasks that each distributed dataset is from a different domain with feature shift (Eq.  $(4)$ ). The crucial difference is that in the Out-Client Shift, the evaluation solution is the leave-one-domain-out evaluation for all benchmarks, which means that randomly selecting one domain as the unseen client and all the left domains are used as source clients for collaboration. Furthermore, Fig. [2](#page-10-3) shows example cases from relatively federated datasets.

• **Office Caltech** [\[293\]](#page-20-50) is built upon the Office dataset and Caltech256 [\[294\]](#page-20-51) datasets with 10 overlapping categories. It includes four diverse domains: Amazon (Am), Caltech (Ca), DSLR (D), and Webcam (W).

• **Digits** is a numeral classification task and includes four different domains: MNIST (M) [\[142\]](#page-18-5), USPS (U)[\[295\]](#page-20-52), SVHN (Sv)  $[143]$ , and SYN (Sy) $[296]$  with ten categories.

• **Office31** [\[297\]](#page-21-1) has 31 classification number in three domains: Amazon, DSLR, and Webcam. The 31 categories in the dataset consist of objects commonly encountered in office scenarios, such as laptops, keyboards, and, file cabinets. • **PACS** [\[298\]](#page-21-2) includes four domains: Photo (P) with 1, 670 images, Art Painting (AP) with 2,048 images, Cartoon (Ct) with 2,344 images and Sketch (Sk) with 3,929 images. Each domain holds seven categories.

#### 6.1.3 Data Augmentation

We follow the wide-used data augmentation strategy in [\[154\]](#page-18-17), [\[40\]](#page-16-8), [\[120\]](#page-17-35) to construct the local data augmentation strategy. We list these data augmentation introductions following the PyTorch notations:

Image /page/10/Figure/18 description: The image displays a grid of various datasets, categorized by type. The top row shows MNIST(M) and USPS(U) datasets, both featuring grids of handwritten digits. To their right are the Amazon(Am) and Caltech(Ca) datasets, showcasing everyday objects like projectors, cameras, mugs, and computer peripherals. The second row presents SVHN(SV) and SYN(SY) datasets, which also contain digits, followed by Dslr(D) and Webcam(W) datasets, displaying objects such as cameras, bicycles, and headphones. Below these are the Office-Caltech and Digits categories. The third row features Photo(Ph) and Art Painting(AP) datasets, with images of animals, musical instruments, and landscapes. Next to these are Amazon(AM) and Dslr(D) datasets, similar to the ones above. The bottom row includes Cartoon(C) and Sketch(S) datasets, with simple drawings of animals, houses, and musical instruments. Finally, the PACS dataset is shown, followed by Webcam(W) and Office-31 datasets, which contain a mix of objects and scenes.

<span id="page-10-3"></span>Fig. 2. Visualization for Digits [\[142\]](#page-18-5), [\[295\]](#page-20-52), [\[143\]](#page-18-6), [\[296\]](#page-21-0), Office Caltech [\[293\]](#page-20-50), PACS [\[298\]](#page-21-2), and Office31 [\[297\]](#page-21-1). Refer to § [6.1.2.](#page-10-4)

TABLE 5

<span id="page-10-6"></span>Hyper-parameters chosen for different methods. Hyper-parameters in different methodologies may share the same notation but represent **distinct** meanings. See details in § [6.2.](#page-10-2)

| Methods                              | Hyper-Parameter                                                |
|--------------------------------------|----------------------------------------------------------------|
| Generalizable Federated Learning § 3 |                                                                |
| FedProx [36]                         | Proximal weight $\mu$ : 0.01                                   |
| SCAFFOLD [147]                       | Global learning rate: $lr$ : 0.25                              |
| FedProc [154]                        | Contrastive temp $\tau$ : 1.0                                  |
| MOON [40]                            | Contrastive temp $\tau$ : 0.5; Proximal weight $\mu$ : 1.0     |
| FedRS [161]                          | Scaling factors $\alpha$ : 0.5                                 |
| FedDyn [154]                         | Proximal weight $\alpha$ : 0.5                                 |
| FedOPT [175]                         | Global Learning rate $\eta_g$ : 0.5                            |
| FedProto [107]                       | Proximal weight $\lambda$ : 2                                  |
| FedLC [106]                          | Scaling factor $\tau$ : 0.5                                    |
| FedDC [148]                          | Penalized factor $\alpha$ : 0.1                                |
| FedNTD [152]                         | Distill temp $\tau$ : 1; Reg weight $\beta$ : 1                |
| FPL [120]                            | Contrastive temp $\tau$ : 0.02                                 |
| KD3A [232]                           | Confidence gate $g$ : [0.9, 0.95]                              |
| Robust Federated Learning § 4        |                                                                |
| Multi Krum [126]                     | Evil ratio: $\Upsilon$ < 50%; Top-K: 5                         |
| Bulyan [53]                          | Evil ratio: $\Upsilon$ < 50%                                   |
| Trim Median [245]                    | Evil ratio: $\Upsilon$ < 50%                                   |
| FoolsGold [131]                      | Stability value $\epsilon$ = 1e-5                              |
| DnC [128]                            | Sub dimension $b$ :1000 Filter ratio: $c$ :1.0                 |
| FLTrust [247]                        | Public epoch $E$ : 20                                          |
| Sageflow [248]                       | Threshold value $E_{th}$ : 2.2; Loss exponent $\delta$ : 5     |
| RFA [246]                            | Iteration $E$ :3                                               |
| RLR [254]                            | Global learning rate $lr$ : 1.0; Robust threshold $\tau$ : 4.0 |
| CRFL [260]                           | Norm threshold $\rho$ : 15 Smooth level $\sigma$ : 0.01        |
| Fair Federated Learning § 5          |                                                                |

AFL [\[71\]](#page-16-48) Regularization parameter  $\gamma$  : 0.01

- RandomCrop: The images are randomly cropped out with:  $32 \times 32$ ,  $224 \times 224$  for respective federated scenarios.
- RandomHorizontalFlip: The sample is horizontally flipped randomly with the probability  $p = 0.5$ .
- Normalize: Normalize the image with the mean value and standard deviation.

<span id="page-10-2"></span>

### 6.2 Implementation Details

• **Training Setting**. As for the uniform comparison evaluation, we follow [\[154\]](#page-18-17), [\[40\]](#page-16-8), [\[120\]](#page-17-35), [\[128\]](#page-17-43), [\[248\]](#page-20-5), [\[116\]](#page-17-31), [\[72\]](#page-16-49), [\[271\]](#page-20-28) and conduct the local updating round  $U = 10$ . We use

Image /page/11/Figure/1 description: This figure displays two line graphs comparing the performance of different federated learning algorithms on CIFAR-10 and CIFAR-100 datasets. The x-axis for both graphs represents the communication epoch, and the y-axis represents accuracy, ranging from 40 to 70 for CIFAR-10 and 40 to 75 for CIFAR-100. The CIFAR-10 graph shows that FedAVG reaches approximately 68.07% accuracy, while FedProto reaches around 65.05%. The CIFAR-100 graph shows FedAVG reaching approximately 72.76% accuracy and FedProto reaching around 68.52%. Several other algorithms like FedNova, FedProx, FedRS, MOON, FedDyn, FedOpt, and Scaffold are also plotted, showing varying performance trends across communication epochs.

Fig. 3. Visualization of training curves of the test accuracy with Communication Epochs 100 with Cifar-10 and Cifar-100 datasets ( $\beta = 0.5$ ).

Image /page/11/Figure/3 description: This image contains two line graphs side-by-side, both plotting accuracy against communication epochs. The left graph is titled "MNIST" and shows accuracy values ranging from 98 to 99.4. The right graph is titled "Fashion-MNIST" and displays accuracy values from 83 to 91. Both graphs feature multiple colored lines representing different federated learning algorithms: FedProto (yellow), FedNova (green), FedAVG (red), FedProx (purple), FedRS (pink), MOON (dark blue), FedDyn (light blue), and FedOpt (orange). The MNIST graph shows accuracies generally above 98.5, with several lines reaching close to 99.4. The Fashion-MNIST graph shows accuracies starting around 83 and increasing to over 90 for most algorithms, with some fluctuations. Specific peak values noted are 99.41 and 99.11 on the MNIST graph, and 90.11 and 89.40 on the Fashion-MNIST graph.

Fig. 4. Visualization of training curves of the test accuracy with Communication Epochs 100 with MNIST and Fashion-MNIST datasets ( $\beta = 0.5$ ).

<span id="page-11-2"></span>Image /page/11/Figure/5 description: TABLE 6 is a title for a table that describes the configuration of different federated scenarios. The text explains that Image Size is operated after the resize operation. |C| denotes the classification scale, |K| denotes the clients number, E is the communication epochs for federation, and B means the training batch size. The table has columns for Scenario, Size, |C|, Network, Rate, |K|, E, and B.

| Scenario                                        | Size | C   | Network $w$ | Rate $\eta$ | K   | E   | B  |
|-------------------------------------------------|------|-----|-------------|-------------|-----|-----|----|
| Label Skew Setting § 6.1.1                      |      |     |             |             |     |     |    |
| Cifar-10                                        | 32   | 10  | SimpleCNN   | $1e-2$      | 10  | 100 | 64 |
| Fashion-MNIST                                   | 32   | 10  | SimpleCNN   | $1e-2$      | 10  | 100 | 64 |
| MNIST                                           | 32   | 10  | SimpleCNN   | $1e-2$      | 10  | 100 | 64 |
| Cifar-100                                       | 32   | 100 | ResNet-50   | $1e-1$      | 10  | 100 | 64 |
| Tiny-ImageNet                                   | 32   | 200 | ResNet-50   | $1e-2$      | 10  | 100 | 64 |
| Domain Skew / Out-Client Shift Settings § 6.1.2 |      |     |             |             |     |     |    |
| Digits                                          | 32   | 10  | ResNet-18   | $1e-2$      | 4/3 | 50  | 16 |
| PACS                                            | 224  | 7   | ResNet-34   | $1e-3$      | 4/3 | 50  | 16 |
| Office Caltech                                  | 224  | 10  | ResNet-34   | $1e-3$      | 4/3 | 50  | 16 |
| Office-Home                                     | 224  | 65  | ResNet-34   | $1e-3$      | 4/3 | 50  | 16 |

the SGD [\[299\]](#page-21-3) optimizer for all local updating optimization. The corresponding weight decay is  $1e - 5$  and momentum is 0.9. The learning rate  $\eta$  and communication epoch E are different in various scenarios, as shown in Tab. [6.](#page-11-2) Notably, the communication epoch is set according to when all federated approaches have little or no accuracy gain with more communication epochs. The local training batch size is  $B = 64$ . Furthermore, the Tab. [5](#page-10-6) plots the chosen hyperparameter for different methods. We fix the random seed to ensure the experimental reproduction. Different methods are implemented on the PyTorch framework and trained on the NVIDIA GeForce RTX 3090.

• **Network Setting**. Following [\[40\]](#page-16-8), [\[154\]](#page-18-17), [\[120\]](#page-17-35), we utilize the CNN as the backbone for different scenarios, which has two  $5 \times 5$  convolution layers with  $2 \times 2$  max pooling

and name is as SimpleCNN. Besides, we utilize the ResNet network [\[4\]](#page-15-3) family to support several large-scale dataset experiments. We summarize the essential experiments configuration of different federated scenarios in Tab. [6.](#page-11-2)

• **Malicious Setting**. For both byzantine and backdoor attacker scales, we set the malicious ratio  $\Upsilon \in \{0.2, 0.4\}$  to represent the evils scale in the overall clients. Besides, for the Data-Based Byzantine Attack, the noise rate,  $\epsilon$ , is default set as 0.5 for Pair Flipping and Symmetry Flipping attacks.

<span id="page-11-0"></span>

# 7 BENCHMARK

Next, we tabulate the performance of previously discussed solutions. For each of the reviewed fields, the most widely used dataset is selected for performance benchmarking. The performance scores are gathered from the reproduced results. As different methods are with different code bases and levels of optimization, it is hard to make completely fair comparison. Besides, for a small set of methods whose implementations are not well organized or publicly available, we directly borrow them from the unofficial code repository.

<span id="page-11-1"></span>

### 7.1 Generalization Benchmark

#### 7.1.1 Evaluation Metrics

Cross-Client Accuracy  $\mathcal{A}^{\mathcal{U}}$  is widely adopted in the Cross-Client Shift: Label Skew and Domain Skew settings. We further denote Out-Client Accuracy  $A^{\mathcal{O}}$  under the Out-Client Shift for generalizable performance evaluation.

# 7.1.2 Results

Cifar-10 [\[291\]](#page-20-48), Cifar-100 [\[291\]](#page-20-48), MNIST [\[142\]](#page-18-5), and Fashion-MNIST [\[292\]](#page-20-49) are arguably popular datasets for the Label TABLE 7

<span id="page-12-1"></span>**Quantitative Label Skew results** in term of  $A^U$ ,  $A^u$ , and  $\mathcal E$  (Eq. [\(18\)](#page-9-2)), on Cifar-10, Cifar-100, MNIST and Fashion-MNIST scenarios.  $\mathcal E$  is evaluated under the  $\beta = 0.5$ . Best in bold and second with underline. / denotes that these methods are not available for these metrics, *i.e.*, the value is zero or NaN. These notes also apply to the other tables. Please refer to § [3.1](#page-5-2) for metrics definition and § [7.1](#page-11-1) for experimental analysis.

|                                                                                                                                                      |     | $C$ ifar-10         |               |     | $Cifar-100$ |                           | <b>MNIST</b>                  |  |  |  |  |                   | Fashion-MNIST     |  |  |               |  |  |
|------------------------------------------------------------------------------------------------------------------------------------------------------|-----|---------------------|---------------|-----|-------------|---------------------------|-------------------------------|--|--|--|--|-------------------|-------------------|--|--|---------------|--|--|
| Methods <sup>1</sup>                                                                                                                                 | 1.0 | $0.5 \t 0.3 \t 0.1$ | $\varepsilon$ | 1.0 |             | 0.5 0.3 0.1 $\mathcal{E}$ | 1.0 0.5 0.3 0.1 $\mathcal{E}$ |  |  |  |  | $\vert 1.0 \vert$ | $0.5$ $0.3$ $0.1$ |  |  | $\varepsilon$ |  |  |
| FedAvg [20]   70.64 66.96 63.92 60.43 0.354 68.47 69.72 69.21 68.92 0.213 99.44 99.37 99.13 98.76 0.602 89.94 89.87 83.82 90.15 0.462                |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedProx [36]   71.22 67.16 64.88 61.03   0.423   72.37 70.19 63.48 67.4   0.773   99.15 99.41 99.32 98.73   0.114   89.87 89.97 88.69 83.57   0.524  |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| SCAFFOLD [147]   70.77 68.33 68.34 60.83 / 7 71.91 72.76 69.82 68.24 / 99.41 99.12 98.95 96.95 / 89.83 89.73 88.32 81.27                             |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedNova [121]   70.94 67.06 66.42 64.05       70.12 67.11 63.86 27.91         99.42 99.29 99.22 99.88         90.20 89.81 89.03 84.39                |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| MOON [40]   69.73 68.07 66.48 61.71   0.063   71.47 69.51 69.09 65.53   0.412   99.51 99.36 99.17 98.02   0.324   90.52 90.11 88.95 82.92   0.614    |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedRS [161]   70.14 66.036 63.89 59.47   0.184   69.81 68.53 67.32 67.16   0.637   99.34 99.33 99.23 98.93   0.333   90.01 89.40 88.47 77.54   0.579 |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedDyn [146]   70.59 67.80 64.39 60.52   0.488   71.48 71.25 70.28 66.81   0.583   99.48 99.31 99.10 98.71   0.059   90.24 89.97 88.59 82.92   0.533 |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedOPT [175]   70.44 66.70 65.95 63.10       69.40 68.52 67.57 67.26       99.32 99.11 98.92 98.13       90.06 89.65 88.79 83.41                     |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedProto [107]   69.75 65.05 56.45 48.74 0.319 70.07 70.83 68.32 67.36 0.759   99.44 99.26 99.12 98.69 0.323   90.17 90.07 88.73 83.26 0.444         |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |
| FedNTD [152]   51.43 35.06 37.37 22.18 0.647 32.48 28.92 24.36 21.21 0.492 85.47 31.41 78.87 30.18 0.930 83.67 79.23 70.12 52.04 0.782               |     |                     |               |     |             |                           |                               |  |  |  |  |                   |                   |  |  |               |  |  |

<span id="page-12-2"></span>TABLE 8 **Quantitative Domain Skew results** in term of  $A^{\mathcal{U}}$ ,  $A^u$ ,  $\mathcal{E}$  (Eq. [\(18\)](#page-9-2)), and  $\mathcal{V}$  (Eq. [\(19\)](#page-9-3)) on Digits, Office Caltech, and PACS. Refer to § [7.1.](#page-11-1)

| Methods        | Digits |       |       |       |                             | Office Caltech |               |       | PACS  |       |       |                             |               |               |       |       |       |       |                             |               |               |
|----------------|--------|-------|-------|-------|-----------------------------|----------------|---------------|-------|-------|-------|-------|-----------------------------|---------------|---------------|-------|-------|-------|-------|-----------------------------|---------------|---------------|
|                | M      | U     | Sv    | Sy    | $\mathcal{A}^{\mathcal{U}}$ | $\mathcal{E}$  | $\mathcal{V}$ | Am    | Ca    | D     | W     | $\mathcal{A}^{\mathcal{U}}$ | $\mathcal{E}$ | $\mathcal{V}$ | P     | AP    | Ct    | Sk    | $\mathcal{A}^{\mathcal{U}}$ | $\mathcal{E}$ | $\mathcal{V}$ |
| FedAvg [20]    | 90.40  | 60.30 | 34.68 | 46.99 | 58.09                       | 0.024          | 4.35          | 81.99 | 73.21 | 79.37 | 67.93 | 75.62                       | 0.653         | 0.379         | 76.09 | 64.19 | 83.50 | 89.40 | 78.30                       | 0.279         | 0.911         |
| FedProx [36]   | 95.03  | 63.25 | 34.50 | 44.60 | 59.34                       | 0.059          | 5.44          | 85.26 | 75.08 | 84.67 | 75.17 | 80.23                       | 0.717         | 0.273         | 79.26 | 69.86 | 80.51 | 90.82 | 80.19                       | 0.170         | 0.612         |
| SCAFFOLD [147] | 97.79  | 94.45 | 26.64 | 90.69 | 77.39                       | /              | 8.93          | 39.79 | 42.50 | 78.02 | 70.69 | 57.75                       | /             | 0.281         | 61.95 | 45.44 | 58.87 | 54.64 | 55.25                       | /             | 0.383         |
| MOON [40]      | 92.78  | 68.11 | 33.36 | 39.28 | 58.36                       | 0.287          | 5.72          | 84.42 | 75.98 | 84.67 | 68.97 | 78.51                       | 0.678         | 0.539         | 74.44 | 64.19 | 83.92 | 89.17 | 77.93                       | 0.321         | 0.924         |
| FedDyn [146]   | 88.91  | 60.34 | 34.57 | 50.72 | 58.65                       | 0.161          | 4.06          | 84.02 | 72.59 | 77.34 | 68.97 | 75.72                       | 0.824         | 0.430         | 78.17 | 64.29 | 82.27 | 89.93 | 78.66                       | 0.129         | 0.881         |
| FedOPT [175]   | 92.71  | 87.62 | 31.32 | 87.92 | 74.89                       | /              | 6.37          | 79.05 | 71.96 | 89.34 | 74.48 | 78.71                       | /             | 0.480         | 78.66 | 67.66 | 82.41 | 83.68 | 78.12                       | /             | 0.410         |
| FedProto [107] | 90.54  | 89.54 | 34.61 | 58.00 | 68.18                       | 0.558          | 5.47          | 87.79 | 75.98 | 90.00 | 79.31 | 83.27                       | 0.556         | 0.410         | 85.63 | 73.69 | 83.57 | 91.14 | 83.51                       | 0.540         | 0.411         |
| FedNTD [152]   | 52.31  | 58.07 | 18.03 | 97.29 | 56.43                       | 0.800          | 7.90          | 10.95 | 10.89 | 14.67 | 10.34 | 11.71                       | 0.911         | 0.601         | 16.77 | 18.23 | 28.47 | 93.18 | 39.16                       | 0.642         | 9.932         |

Design for Performance Fairness setting § [5.3](#page-9-1)

AFL [\[71\]](#page-16-48) 96.58 90.72 32.90 87.56 76.94 0.64 6.57 85.33 73.79 80.21 68.93 77.06 0.775 0.517 85.76 72.92 83.16 87.08 82.23 0.90 0.329

TABLE 9

**Quantitative Out-Client Shift results** in term of  $A^O$  (Eq. [\(15\)](#page-5-3)) on Office Caltech, Digits, PACS, Office31 scenarios. See details in § [7.1.](#page-11-1)

| Methods                                                    | Office Caltech   |                  |                 |                 |       | Digits          |                 |                  |                  |       | PACS            |                  |                  |                  |       | Office31        |                  |                 |       |
|------------------------------------------------------------|------------------|------------------|-----------------|-----------------|-------|-----------------|-----------------|------------------|------------------|-------|-----------------|------------------|------------------|------------------|-------|-----------------|------------------|-----------------|-------|
|                                                            | $\rightarrow$ Ca | $\rightarrow$ Am | $\rightarrow$ W | $\rightarrow$ D | AVG   | $\rightarrow$ M | $\rightarrow$ U | $\rightarrow$ Sv | $\rightarrow$ Sy | AVG   | $\rightarrow$ P | $\rightarrow$ AP | $\rightarrow$ Ct | $\rightarrow$ Sk | AVG   | $\rightarrow$ D | $\rightarrow$ Am | $\rightarrow$ W | AVG   |
| FedAvg [20]                                                | 58.12            | 67.47            | 43.10           | 80.00           | 62.17 | 32.60           | 47.20           | 13.91            | 13.54            | 26.81 | 52.28           | 46.16            | 60.74            | 51.12            | 52.57 | 14.28           | 8.93             | 21.51           | 14.90 |
| FedProx [36]                                               | 56.60            | 69.26            | 42.41           | 85.33           | 63.40 | 23.54           | 60.28           | 15.83            | 13.78            | 28.35 | 54.45           | 49.61            | 56.91            | 56.17            | 54.28 | 15.92           | 6.01             | 19.36           | 13.76 |
| SCAFFOLD [147]                                             | 36.07            | 47.36            | 45.86           | 59.33           | 47.15 | 67.61           | 82.39           | 7.79             | 14.52            | 43.07 | 43.85           | 23.81            | 45.07            | 39.79            | 38.12 | 12.44           | 5.58             | 10.88           | 9.63  |
| FedProc [154]                                              | 47.41            | 60.84            | 42.41           | 66.66           | 54.33 | 24.34           | 43.37           | 10.15            | 13.09            | 22.73 | 56.94           | 30.95            | 56.02            | 49.94            | 48.46 | 19.39           | 4.91             | 10.38           | 11.56 |
| MOON [40]                                                  | 55.53            | 68.63            | 44.83           | 79.33           | 62.08 | 31.28           | 31.75           | 14.30            | 14.45            | 22.94 | 54.01           | 45.10            | 60.42            | 58.10            | 54.40 | 14.08           | 7.04             | 21.39           | 14.17 |
| FedDyn [146]                                               | 59.99            | 66.42            | 40.34           | 81.99           | 62.18 | 28.74           | 56.08           | 14.36            | 11.88            | 27.76 | 51.40           | 43.19            | 60.57            | 50.71            | 51.46 | 14.08           | 7.86             | 17.85           | 13.26 |
| FedOPT [175]                                               | 52.67            | 55.68            | 60.34           | 69.33           | 59.50 | 59.35           | 62.62           | 17.59            | 15.22            | 38.69 | 57.64           | 39.19            | 45.92            | 49.50            | 48.06 | 19.38           | 6.90             | 18.73           | 15.00 |
| FedProto [107]                                             | 60.35            | 66.94            | 58.62           | 76.00           | 65.47 | 43.67           | 58.08           | 13.49            | 13.73            | 32.24 | 65.07           | 36.56            | 56.98            | 57.87            | 54.12 | 31.01           | 7.08             | 23.54           | 20.54 |
| FedNTD [152]                                               | 58.66            | 69.47            | 44.83           | 84.00           | 64.23 | 24.15           | 58.56           | 18.44            | 13.68            | 28.70 | 64.50           | 47.47            | 58.52            | 53.43            | 55.98 | 17.75           | 7.12             | 27.97           | 17.61 |
| Design for Federated Domain Adaptation setting § 3.3.1     |                  |                  |                 |                 |       |                 |                 |                  |                  |       |                 |                  |                  |                  |       |                 |                  |                 |       |
| COPA [219]                                                 | 55.17            | 67.05            | 56.55           | 78.33           | 64.27 | 58.93           | 92.20           | 10.49            | 14.90            | 44.13 | 71.61           | 53.74            | 63.12            | 56.60            | 61.26 | 43.06           | 6.69             | 31.26           | 27.00 |
| KD3A [232]                                                 | 54.73            | 70.00            | 68.61           | 75.33           | 67.16 | 83.91           | 97.46           | 14.33            | 34.03            | 57.43 | 76.99           | 56.91            | 67.63            | 55.70            | 64.30 | 44.28           | 8.04             | 37.08           | 29.80 |
| Design for Federated Domain Generalization setting § 3.3.2 |                  |                  |                 |                 |       |                 |                 |                  |                  |       |                 |                  |                  |                  |       |                 |                  |                 |       |
| COPA [219]                                                 | 57.32            | 66.31            | 48.27           | 70.00           | 60.47 | 33.76           | 47.32           | 13.26            | 15.16            | 27.37 | 59.54           | 35.33            | 56.67            | 57.93            | 52.36 | 21.22           | 5.48             | 19.49           | 15.39 |

COPA [\[219\]](#page-19-15) 57.32 66.31 48.27 70.00 **60.47** 33.76 47.32 13.26 15.16 27.37 59.54 35.33 56.67 57.93 **52.36** 21.22 5.48 19.49 **15.39** FedGA [\[243\]](#page-20-0) 44.28 54.10 51.72 71.33 55.35 58.74 86.92 9.16 14.81 **42.40** 59.00 35.01 43.20 53.60 47.70 22.24 5.15 10.63 12.67

Skew setting. The Tab. [7](#page-12-1) summarizes the results of ten methods on these four datasets. It reveals the first method FedAvg [\[20\]](#page-15-19) proposed in 2017, to recent complicated solutions [\[152\]](#page-18-15). For further comparison, we visualize the training curves of the testing evaluation accuracy along with the training process under the  $\beta = 0.5$ . Furthermore, as for the Domain Skew scenario, we adopt the widely federated scenarios,*i.e*., Digits [\[142\]](#page-18-5), [\[295\]](#page-20-52), [\[143\]](#page-18-6), [\[296\]](#page-21-0), Office Caltech [\[293\]](#page-20-50), and PACS [\[298\]](#page-21-2). As seen in Tab. [8,](#page-12-2) SCAF-FOLD [\[147\]](#page-18-10) and FedProto [\[107\]](#page-17-22) present relative competitive performance. Regarding the Out-Client Shift task, we take into account the specialized Federated Domain Adaptation § [3.3.1](#page-6-1) and Federated Domain Generalization § [3.3.2](#page-7-2) paradigms. Notably, Federated Domain Adaptation utilizes the unlabeled the unknown domain distribution during the training process and achieves the clear Out-Client Accuracyimprovement. For example, KD3A achieves the 67.16 accuracy on the Office Caltech scenario.

<span id="page-12-0"></span>

### 7.2 Robustness Benchmark

#### 7.2.1 Evaluation Metrics

 ${\cal A}_{Byz}^u$  means the testing accuracy under the Byzantine Attack. And thus, Accuracy Decline Impact  $\mathcal I$  denotes the decreased accuracy, compared with the benign federation results. Similarly, Attack Success Rate  $\mathcal{R}^u$  evals the performance on the backdoor polluted datasets.

#### FEDERATED LEARNING FOR GENERALIZATION, ROBUSTNESS, FAIRNESS: A SURVEY AND BENCHMARK 14

TABLE 10

<span id="page-13-1"></span>

| <b>Quantitative Byzantine Attack results</b> in term of $A^u$ , $A^u_{Byz}$ , and $\mathcal I$ (Eq. (16)) on Cifar-10, MNIST, and Fashion-MNIST scenarios. FLTrust and |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Sageflow utilizes SVHN as the proxy. The local optimization is FedProx [36] with $\mu$ = 0.01. See Byzantine Tolerance comparison in § 7.2.                            |

|                                         | Cifar-10           |                     |              |                    |                                                |            |                                    |                                                    |                            | Fashion-MNIST      |                                           |                            | <b>MNIST</b>       |                                                        |                            |                                             |                  |              | <b>USPS</b>        |                                                           |                            |                    |                            |             |  |
|-----------------------------------------|--------------------|---------------------|--------------|--------------------|------------------------------------------------|------------|------------------------------------|----------------------------------------------------|----------------------------|--------------------|-------------------------------------------|----------------------------|--------------------|--------------------------------------------------------|----------------------------|---------------------------------------------|------------------|--------------|--------------------|-----------------------------------------------------------|----------------------------|--------------------|----------------------------|-------------|--|
|                                         |                    | $\beta = 0.5$       |              |                    | $\beta = 0.3$                                  |            |                                    | $\beta = 0.5$                                      |                            |                    | $\beta = 0.3$                             |                            |                    | $\beta = 0.5$                                          |                            |                                             | $\beta = 0.3$    |              |                    | $\beta = 0.5$                                             |                            |                    | $\beta = 0.3$              |             |  |
| Methods                                 | $\Upsilon = 0.2$   | $\Upsilon = 0.4$    |              | $\Upsilon = 0.2$   |                                                |            | $\Upsilon = 0.2$                   | $\Upsilon = 0.4$                                   |                            | $\Upsilon = 0.2$   | $\Upsilon = 0.4$                          |                            | $\Upsilon = 0.2$   | $\Upsilon = 0.4$                                       |                            | $\Upsilon = 0.2$                            | $\Upsilon = 0.4$ |              | $\Upsilon = 0.2$   | $\Upsilon = 0.4$                                          |                            | $\Upsilon = 0.2$   | $\Upsilon = 0.4$           |             |  |
|                                         | ${\cal A}^u_{Buz}$ |                     | $\mathcal I$ | ${\cal A}^u_{Buz}$ | $\Upsilon = 0.4$<br>$\boldsymbol{\mathcal{A}}$ | Ι          | $\boldsymbol{\mathcal{A}}^u_{Byz}$ |                                                    | $\boldsymbol{\mathcal{I}}$ | ${\cal A}^u_{Buz}$ |                                           | $\boldsymbol{\mathcal{I}}$ | ${\cal A}^u_{Buz}$ |                                                        | $\boldsymbol{\mathcal{I}}$ | $\mathcal{A}^u_{Buz}$ $\mathcal{A}^u_{Buz}$ |                  | $\mathcal I$ | ${\cal A}^u_{Buz}$ |                                                           | $\boldsymbol{\mathcal{I}}$ | ${\cal A}^u_{Buz}$ | $\mathcal{A}^u_{B \, u z}$ | $\tau$      |  |
|                                         |                    | Byz<br>$A^u$ :67.16 |              |                    | Buz<br>$\overline{\mathcal{A}^u:}64.88$        |            |                                    | $\boldsymbol{\mathcal{A}}_{Byz}^u$<br>$A^u$ :89.97 |                            |                    | $\bm{\mathcal{A}}^u_{Byz}$<br>$A^u:88.69$ |                            |                    | $\bm{\mathcal{A}}^u_{Byz}$<br>$\overline{A^{u}$ :99.41 |                            |                                             | $A^u$ :99.32     |              |                    | $ {\cal A}_{Byz}^u $<br>$\overline{\mathcal{A}^u}$ :96.70 |                            |                    | $A^u$ :96.69               |             |  |
| FedProx $[36]$<br>Pair Flipping Eq. (7) |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            |                    |                                                        |                            |                                             |                  |              |                    |                                                           |                            |                    |                            |             |  |
| Multi Krum [126]                        |                    | 46.85 20.31         |              | 46.99              | 43.91                                          | 20.82      | 82.20                              | 47.59                                              | 42.38                      | 80.79              | 82.51                                     |                            | 10.18              | 11.35 88.06                                            |                            | 10.43                                       | 11.35 87.97      |              | 50.83              | 93.52                                                     | 3.18                       | 93.41              |                            | 51.11 45.58 |  |
| Bulyan <sup>[53]</sup>                  | 50.21<br>46.88     | 44.06 20.68         |              | 10.00              | 10.00                                          | 54.88      | 82.62                              | 80.76                                              | 9.21                       | 78.00              | 73.57 15.12                               | 6.18                       | 97.01              | 98.18                                                  | 1.23                       | 93.21                                       | 92.13 7.19       |              | 93.21              | 92.13                                                     | 4.57                       | 86.04              | 87.20                      | 9.49        |  |
| Trim Median [245]                       | 51.70              | 45.77 21.39         |              | 19.94              | 10.67                                          | 54.21      | 84.18                              | 78.09                                              | 11.88                      | 81.76              | 77.89                                     | 10.8                       | 98.57              | 94.62                                                  | 4.79                       | 93.25                                       | 92.90            | 6.42         | 94.85              | 94.33 2.37                                                |                            | 91.72              | 92.05 0.64                 |             |  |
| FoolsGold [131]                         | 60.09              | 56.80 10.36         |              | 50.81              | 57.98                                          | 6.90       | 86.97                              | 86.07                                              | 3.90                       | 85.65              | 81.50                                     | 7.19                       | 97.25              | 97.80                                                  | 1.61                       | 98.05                                       | 97.22            | 2.10         | 77.69              | 91.77                                                     | 4.93                       | 87.90              |                            | 77.23 19.46 |  |
| DnC [128]                               | 62.67              | 58.38               | 8.78         | 60.41              | 59.96                                          | 4.92       | 87.54                              | 87.76                                              | 2.21                       | 87.22              | 88.24                                     | 0.45                       | 99.33              | 99.07                                                  | 0.34                       | 98.85                                       | 98.70            | 0.62         | 95.94              | 95.16                                                     | 1.54                       | 95.07              | 95.08                      | 1.61        |  |
| FLTrust <sup>[247]</sup>                | $\prime$           | $\prime$            |              |                    |                                                | $\sqrt{2}$ | $\prime$                           |                                                    |                            | $\sqrt{2}$         |                                           | $\prime$                   | 11.35              | 11.35                                                  | 88.06                      | 11.35                                       |                  | 78.68 20.64  | 13.15              | 13.15                                                     | 83.55                      | 13.15              |                            | 13.15 83.54 |  |
| Sageflow [248]                          |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            | 99.28              | 99.03                                                  | 0.38                       | 99.02                                       | 98.73            | 0.59         | 95.36              | 94.34                                                     | 2.36                       | 96.15              | 95.37 1.32                 |             |  |
| RFA [246]                               | 66.84              | 66.31               | 0.85         | 62.28              | 61.54 3.34                                     |            | 89.67                              | 89.73                                              | 0.24                       | 88.18              | 88.73                                     | $-0.04$                    | 99.12              | 99.10 0.31                                             |                            | 98.97                                       | 98.91 0.41       |              | 96.12              | 95.56 1.14                                                |                            | 96.30              | 96.08 0.61                 |             |  |
| Symmetry Flipping Eq. (6)               |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            |                    |                                                        |                            |                                             |                  |              |                    |                                                           |                            |                    |                            |             |  |
| Multi Krum [126]                        | 52.18              | 46.48 20.68         |              | 49.03              | 50.56 14.32                                    |            | 81.87                              | 85.52                                              | 4.45                       | 82.14              | 81.76                                     | 6.93                       | 10.02              | 91.76                                                  | 7.65                       | 11.35                                       | 92.72            | 6.60         | 81.20              | 93.06                                                     | 3.64                       | 84.12              | 93.79                      | 2.90        |  |
| Bulyan [53]                             | 50.73              | 38.38 28.78         |              | 14.55              | 27.01 37.87                                    |            | 84.15                              | 82.15                                              | 7.82                       | 79.51              |                                           | 74.93 13.76                | 97.16              | 97.52                                                  | 1.89                       | 87.10                                       | 91.66            | 7.66         | 91.46              | 89.71                                                     | 6.99                       | 89.94              | 87.93                      | 8.76        |  |
| Trim Median [245]                       | 53.24              | 49.82 17.34         |              | 34.46              | 39.24 25.64                                    |            | 84.61                              | 84.39                                              | 5.58                       | 80.49              | 81.48                                     | 7.21                       | 98.50              | 98.08                                                  | 1.33                       | 92.16                                       | 96.25            | 3.07         | 93.46              | 92.23                                                     | 4.47                       | 93.32              | 93.70                      | 2.99        |  |
| FoolsGold [131]                         | 61.37              | 59.34               | 7.82         | 58.35              | 54.97                                          | 9.91       | 69.15                              | 86.30                                              | 3.67                       | 82.34              | 84.27                                     | 4.42                       | 98.46              | 97.77                                                  | 1.64                       | 95.90                                       | 90.45            | 8.87         | 83.02              | 78.07                                                     | 18.63                      | 75.72              | 73.92                      | 22.77       |  |
| DnC [128]                               | 62.57              | 58.12               | 9.04         | 61.94              | 59.51                                          | 5.37       | 88.15                              | 87.23                                              | 12.74                      | 86.33              | 87.83                                     | 0.86                       | 99.31              | 98.99                                                  | 0.42                       | 98.63                                       | 98.63            | 0.69         | 95.86              | 94.70                                                     | 2.00                       | 94.98              | 93.64 3.05                 |             |  |
| FLTrust <sup>[247]</sup>                |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            | 11.35              | 70.09                                                  | 29.32                      | 11.35                                       |                  | 67.29 32.03  | 60.41              | 52.83                                                     | 43.87                      | 59.31              |                            | 13.15 83.54 |  |
| Sageflow [248]                          |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            | 98.86              | 98.75                                                  | 0.66                       | 98.51                                       | 98.31 1.01       |              | 94.08              | 92.32                                                     | 4.38                       | 95.33              | 92.93 3.76                 |             |  |
| RFA [246]                               | 63.43              | 61.67               | 5.49         | 62.78              | 60.13 4.75                                     |            | 89.44                              |                                                    | 88.30 11.67                | 87.73              | 87.49                                     | 1.20                       | 99.00              | 99.06 0.35                                             |                            | 98.78                                       | 98.65 0.67       |              | 95.80              | 94.57                                                     | 2.13                       | 95.98              | 95.47 1.22                 |             |  |
| Random Noise Eq. (8)                    |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            |                    |                                                        |                            |                                             |                  |              |                    |                                                           |                            |                    |                            |             |  |
| Multi Krum [126]                        | 10.00              | 13.06               | 54.1         | 29.25              | 14.11                                          | 50.77      | 10.00                              | 21.71                                              | 68.26                      | 75.55              | 25.60 63.09                               |                            | 11.35              | 13.42 85.99                                            |                            | 11.35                                       |                  | 21.04 78.28  | 89.25              | 15.07                                                     | 81.63                      | 13.15              |                            | 26.79 69.90 |  |
| Bulyan [53]                             | 51.04              | 51.34 15.82         |              | 42.09              | 49.29 15.59                                    |            | 82.70                              | 87.24                                              | 2.73                       | 81.70              | 86.43                                     | 2.26                       | 98.74              | 98.63 0.78                                             |                            | 91.95                                       | 98.32            | 1.00         | 94.27              | 94.51                                                     | 2.19                       | 92.59              | 95.34                      | 1.35        |  |
| Trim Median [245]                       | 53.87              | 51.92 15.24         |              | 50.24              | 50.21                                          | 14.67      | 85.94                              | 85.66                                              | 4.31                       | 82.32              | 85.61                                     | 3.08                       | 98.86              | 98.85                                                  | 0.56                       | 94.36                                       | 98.18 1.14       |              | 94.80              | 13.15                                                     | 83.55                      | 95.66              | 95.59 1.10                 |             |  |
| FoolsGold [131]                         | 50.01              | 32.85 34.31         |              | 49.60              | 27.45                                          | 37.43      | 85.98                              | 35.82                                              | 54.15                      | 76.86              | 83.58                                     | 5.11                       | 98.46              | 37.62 61.79                                            |                            | 87.91                                       |                  | 78.90 20.42  | 85.36              | 22.55                                                     | 74.15                      | 54.10              | 55.92 40.77                |             |  |
| DnC [128]                               | 59.64              | 56.95 10.21         |              | 60.00              | 56.45 8.43                                     |            | 87.81                              | 87.72                                              | 2.25                       | 87.26              | 87.66                                     | 1.03                       | 99.31              | 98.97                                                  | 0.44                       | 98.78                                       | 98.85 0.47       |              | 95.73              | 94.60                                                     | 2.10                       | 95.31              | 94.28 2.41                 |             |  |
| FLTrust [247]                           |                    |                     |              |                    |                                                |            |                                    |                                                    |                            | $\prime$           |                                           |                            | 11.35              | 11.35 88.06                                            |                            | 11.35                                       |                  | 11.35 87.97  | 36.53              | 13.15 83.55                                               |                            | 13.15              |                            | 13.15 83.54 |  |
| Sageflow [248]                          |                    |                     |              |                    |                                                |            | $\prime$                           |                                                    |                            |                    |                                           |                            | 98.76              | 96.75                                                  | 2.66                       | 93.14                                       | 89.85            | 9.47         | 92.40              | 78.20                                                     | 18.50                      | 86.02              |                            | 75.63 21.06 |  |
| RFA [246]                               | 56.37              | 10.64 56.52         |              | 55.88              | 15.45 49.43                                    |            | 87.11                              |                                                    | 64.10 25.87                | 85.32              |                                           | 72.30 16.39                | 99.15              | 95.40 4.01                                             |                            | 98.26                                       | 94.01 5.31       |              | 94.67              | 67.49 29.21                                               |                            | 95.35              | 53.08 43.61                |             |  |
| Min-Sum Eq. $(10)$                      |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            |                    |                                                        |                            |                                             |                  |              |                    |                                                           |                            |                    |                            |             |  |
| Multi Krum [126]                        | 10.00              | 10.90 56.26         |              | 42.20              | 10.02 54.86                                    |            | 10.00                              | 11.02                                              | 78.95                      | 80.78              | 10.00 78.69                               |                            | 11.35              |                                                        | 23.17 76.24                | 10.43                                       | 11.35 87.97      |              | 13.15              | 15.96 80.74                                               |                            | 13.15              |                            | 13.15 83.54 |  |
| Bulyan [53]                             | 51.49              | 51.00 16.16         |              | 42.99              | 40.07 24.81                                    |            | 84.64                              | 85.84                                              | 4.13                       | 80.23              | 84.21                                     | 4.48                       | 98.60              | 94.38                                                  | 5.03                       | 92.40                                       | 90.14 9.18       |              | 94.88              | 85.91 10.79                                               |                            | 92.91              | 93.36 3.33                 |             |  |
| Trim Median [245]                       | 53.62              | 53.71 13.45         |              | 49.58              | 51.76 13.12                                    |            | 84.64                              | 85.71                                              | 4.26                       | 83.24              | 85.41                                     | 3.28                       | 98.77              | 98.76                                                  | 0.65                       | 96.80                                       | 92.90            | 6.42         | 95.12              | 95.75                                                     | 0.95                       | 94.22              | 95.45 1.24                 |             |  |
| FoolsGold [131]                         | 52.26              | 10.00 57.16         |              | 47.83              | 10.00                                          | 54.88      | 80.58                              | 14.80                                              | 75.17                      | 80.20              | 19.36 69.33                               |                            | 97.18              | 16.87                                                  | 82.54                      | 98.71                                       | 97.22            | 2.10         | 69.49              | 15.04                                                     | 81.66                      | 64.16              |                            | 13.12 83.57 |  |
| DnC [128]                               | 61.11              | 55.52 11.84         |              | 60.29              | 55.83                                          | 9.05       | 87.63                              | 87.80                                              | 2.17                       | 87.25              | 88.01                                     | 0.68                       | 99.19              | 99.20                                                  | 0.21                       | 98.80                                       | 98.70            | 0.62         | 95.34              | 94.51                                                     | 2.19                       | 94.93              | 95.35                      | 1.34        |  |
| FLTrust <sup>[247]</sup>                |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            | 61.57              | 12.99                                                  | 86.42                      | 11.35                                       | 11.35 87.97      |              | 13.15              | 15.04                                                     | 81.66                      | 13.15              | 14.09                      | 82.60       |  |
| Sageflow [248]                          |                    |                     |              |                    |                                                |            |                                    |                                                    |                            |                    |                                           |                            | 98.59              | 92.85                                                  | 6.56                       | 92.30                                       | 85.01 14.31      |              | 87.07              | 14.09                                                     | 82.61                      | 81.95              | 50.59                      | 46.1        |  |
| $RFA$ $[246]$                           | 51.90              | 11.40 55.76         |              | 60.29              | 14.22 50.66                                    |            | 87.40                              |                                                    | 22.83 67.14                | 85.71              | 61.18 27.51                               |                            | 99.05              | 94.39                                                  | 5.02                       | 98.80                                       | 98.91            | 0.41         | 94.65              | 71.23                                                     | 25.47                      | 94.93              |                            | 57.83 38.86 |  |

# 7.2.2 Results

The Tab. [11](#page-14-2) gathers the Byzantine Attack results for existing Byzantine Tolerance methodologies. We conduct the comparison under four widely adopted datasets, *i.e*., Cifar-10, Fashion-MNIST, MNIST, and USPS datasets with both Data-Based Byzantine Attack: Pair Flipping and Symmetry Flipping, and Model-Based Byzantine Attack: Random Noise and Min-Sum. We select the popular Byzantine Tolerance methods from three types: Distance Base Tolerance, Statistics Distribution Tolerance, Proxy Dataset Tolerance. The results show that DnC reaches a relatively satisfying performance under different Byzantine Attack. Proxy Dataset Tolerance appears the obvious disadvantage, additional related proxy dataset. The Tab. [10](#page-13-1) evaluates the Backdoor Attack results under two popular forms: Bac, and Sem Bac. We further evaluate two Backdoor Defense: RLR [\[254\]](#page-20-11) and CRFL [\[260\]](#page-20-17). It shows that RFA and CRFL achieves satisfying defensive performance.

<span id="page-13-0"></span>

### 7.3 Fairness Benchmark

#### 7.3.1 Evaluation Metrics

Contribution Match Degree:  $\mathcal E$  and Performance Deviation  $\nu$  respectively evaluates the Collaboration Fairness and Performance Fairness.

#### 7.3.2 Results

As shown in Tab. [7](#page-12-1) and Tab. [8,](#page-12-2) few of the existing federated optimization takes the Collaboration Fairness into federated objective account. Besides, Collaboration Fairness also is largely impended under large local data distribution diversity such as the Domain Skew. Regarding the Performance Fairness, existing methods basically focus on minimizing the weighted empirical loss and thus bring the imbalanced performance. Notably, global network utilization and server adaptive optimization seem to roundly alleviate the imbalanced performance on the multiple domains.

### 7.4 Discussion

From experiments, we draw several crucial conclusions.

• **Reproducible Dilemma**. Across different federated fields, many methods do not describe the setup for the experimentation. Some of them even do not release the source code for implementation. Moreover, different methods use various datasets and distinct backbone models. These make fair comparison impossible and hurt reproducibility.

• **Computation Cost**. Another important fact discovered thanks to this study is the lack of information about execution time and memory cost. Federation can be basically divided into cross-device [\[25\]](#page-15-24) and cross-silo [\[98\]](#page-17-13) settings [\[21\]](#page-15-20). Specifically, many methods claim that they greatly improve federated performance improvement. However, they TABLE 11

<span id="page-14-2"></span>**Quantitative Backdoor Attack results** in term of  $A^u$  and  $\mathcal{R}^u$  (Eq. [\(17\)](#page-7-7)) on Cifar-10, MNIST, and USPS. The local optimization algorithm is FedAvg [\[20\]](#page-15-19). We consider two types of backdoor attacks and abbreviate them as Bac [\[59\]](#page-16-38) and Sem Bac [\[62\]](#page-16-41). - means that these solutions are not applicable to these evaluations. Refer to § [7.2](#page-12-0) for Backdoor Defense discussion.

|                                                                                                                                                               |                 |                 |                 |                 | $Cifar-10$      |                 |                 |                 |                 |                 |         |                               | <b>MNIST</b>    |                 |                               |                 |                 |     | <b>USPS</b> |                                                                                                          |     |  |                               |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|---------|-------------------------------|-----------------|-----------------|-------------------------------|-----------------|-----------------|-----|-------------|----------------------------------------------------------------------------------------------------------|-----|--|-------------------------------|
| Methods                                                                                                                                                       |                 |                 | 0.5             |                 |                 |                 | 0.3             |                 |                 |                 | 0.5     |                               |                 | 0.3             |                               |                 |                 | 0.5 |             |                                                                                                          | 0.3 |  |                               |
|                                                                                                                                                               | Bac             |                 |                 | Sem Bac         |                 | Bac             |                 | Sem Bac         |                 | Bac             | Sem Bac |                               |                 | Bac             | Sem Bac                       |                 | Bac             |     | Sem Bac     | Bac                                                                                                      |     |  | Sem Bac                       |
|                                                                                                                                                               | $\mathcal{A}^u$ | $\mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ |         | $\mathcal{A}^u \mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ | $\mathcal{A}^u \mathcal{R}^u$ | $\mathcal{A}^u$ | $\mathcal{R}^u$ |     | $A^u R^u$   | $A^u R$                                                                                                  |     |  | $\mathcal{A}^u \mathcal{R}^u$ |
| Focus on Byzantine Tolerance § 4.2                                                                                                                            |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             |                                                                                                          |     |  |                               |
| Bulyan [53]   47.61 28.73 44.61 17.12                                                                                                                         |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             | 11.12 19.56 96.95 14.77 92.13 0.45 87.70 11.13 87.86 0.10 93.32 10.95 93.52 11.32 87.79 10.83 85.14 1.56 |     |  |                               |
| Trim Median [245] [51.34 22.49 52.21 13.70]                                                                                                                   |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             | 14.78 51.66 98.07 99.18 98.44 0.16 96.65 89.42 96.72 0.61 94.62 71.52 94.24 4.82 92.05 84.17 94.77 2.40  |     |  |                               |
| FoolsGold [131] [60.69 62.54 60.50 13.06 58.58 56.85 59.84 12.56 82.20 91.61 98.45 0.59 92.88 98.06 97.00 1.52 89.66 90.24 83.21 10.11 76.56 86.14 94.77 2.40 |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             |                                                                                                          |     |  |                               |
| DnC [128] 59.30 23.07 61.40 12.88 60.03 42.79 59.80 9.76 99.26 10.39 99.13 0.20 98.53 10.46 98.79 0.29 95.75 9.62 95.11 2.89 96.14 16.89 94.86 1.81           |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             |                                                                                                          |     |  |                               |
| FLTrust [247]                                                                                                                                                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             | 95.31 8.71 97.84 0.00 92.55 10.03 97.43 0.30 71.67 17.69 59.83 20.96 63.20 5.29 63.20 5.29               |     |  |                               |
| Sageflow [248]                                                                                                                                                |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             | 99.17 98.70 99.21 0.53 99.03 98.05 98.83 1.27 96.07 73.63 96.20 3.61 96.83 86.39 96.02 2.65              |     |  |                               |
| RFA [246] 64.90 74.31 63.90 11.54 60.36 75.57 62.75 14.76 99.09 99.09 99.12 0.32 99.11 98.88 98.84 0.39 95.89 2.28 95.75 3.13 97.04 39.59 95.89 2.28          |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             |                                                                                                          |     |  |                               |
| Focus on Backdoor Defense § 4.3                                                                                                                               |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             |                                                                                                          |     |  |                               |
| RLR [254] 51.65 28.83 50.37 10.60                                                                                                                             |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |         |                               |                 |                 |                               |                 |                 |     |             | 44.80 20.74 94.77 10.54 93.11 0.40 91.11 22.69 92.94 0.35 89.20 10.78 92.00 12.65 87.00 10.27 82.15 1.44 |     |  |                               |

CRFL [\[260\]](#page-20-17) 59.27 63.29 58.59 **9.52** 52.27 59.50 52.62 11.66 98.93 33.86 98.89 0.43 98.44 26.28 98.08 0.91 94.96 49.77 95.31 3.61 95.38 62.98 **94.36 1.32**

properly utilize the heavy memory cost or tedious execution time. This void is due to the fact that most solutions focus only on accuracy metrics without any concern about running time efficiency or memory requirements. However, computation-efficient acts as an important role in the federated realistic setting and should be given high-level priority. • **Thinking Everything Failure**. Existing federated methods primarily focus on addressing separate problems, such as

data heterogeneity [\[90\]](#page-17-5), [\[87\]](#page-17-2), malicious attacks [\[83\]](#page-16-28), [\[86\]](#page-17-1), and others. Although these methods often achieve satisfactory performance on specific evaluation metrics, we are curious whether a feasible federated paradigm exists to simultaneously handle various aspects, like a unified federation.

<span id="page-14-0"></span>

# 8 OUTLOOK

<span id="page-14-1"></span>

### 8.1 Future Direction

Based on the reviewed research, we list several future research directions that we believe should be pursued.

• **Generalization and Robustness Dilemma**. Distributed data inherently exhibits the heterogeneous property and generalization aims to incorporate diverse client knowledge. Concurrently, Robustness aims to identify and mitigate the malicious clients effect on the federated system [\[300\]](#page-21-4), [\[301\]](#page-21-5). However, data heterogeneity in federated learning brings that certain local distributions deviate significantly from the primary trend and may be erroneously categorized as malicious ones. This misclassification restricts the effectiveness of federated performance because some benign clients are rejected from the federation.

• **Generalization and Fairness Trade-Off**. Generalization primarily focuses on fitting the distributed data distribution to enhance the mean accuracy  $\mathcal{A}^{\mathcal{U}}$  on the test distribution collection  $U$ . However, Fair Federated Learning, especially Performance Fairness, places emphasis on the client-wise or group-wise uniform performance and requires less performance variance on different testing scenarios. Thus, considering both generalization and robustness, it naturally involves a multi-task optimization: striving for both higher overall performance and uniform individual testing distribution performance. Another Prisoner dilemma is that generalization highlights the federated convergence speed,

which normally achieves via fitting on the major client distribution at the cost of ignoring some minority groups. However, this optimization objective presents a contradictory direction with fairness, which requires uniform performance on different testing distributions.

• **Robustness and Fairness Cooperation**. Fairness aims to ensure long-term and stable multi-party federated cooperation. Robustness focuses on eliminating the malicious influence on the federation. Thus, to some extent, they promote each other. Specifically, achieving Collaboration Fairness requires accurate client contribution measurement, which helps detect those malicious ones with meaningless contributions. On the flip side, with reliable robustness, only benign ones are enrolled in the federated system, which boosts the better interest allocation.

• **Vertical FL meets Generalization, Robustness, Fairness**. Vertical Federated Learning (VFL) has attracted attention in federated learning, and thus the generalization, robustness, and fairness problems in VFL have become burgeoning research directions  $[100]$ ,  $[101]$ . With regard to the generalization aspect, a primary challenge in VFL stems from the need to align and integrate diverse feature sets pertaining to identical entities across varied domains [\[302\]](#page-21-6), [\[303\]](#page-21-7). Moreover, several domains may have missing features for certain entities, which complicates the learning process as it is not straightforward to handle such incomplete information without biasing the model. The questions for aligning features and conducting de-bias training are yet to be resolved. As for the robustness, moving beyond the Byzantine and Backdoor attacks in robust FL, VFL should address data inference assaults, wherein malicious clients endeavor to deduce privacy information from the model updates or the final model. Such attempts encompass label inference and feature inference attacks [\[304\]](#page-21-8), [\[305\]](#page-21-9), [\[306\]](#page-21-10), [\[307\]](#page-21-11), [\[303\]](#page-21-7). There is a need for the development of more defense strategies against them, *e.g*., secure aggregation protocols, robust model architectures, and decentralized trust frameworks) which should not only aim to safeguard data but also strive to maintain a balance between model performance and computational efficiency. Regarding the fairness concept, the imbalance in contribution fairness happens when one client data contributes more significantly to the

predictive power of the model than others [\[308\]](#page-21-12), [\[309\]](#page-21-13), [\[310\]](#page-21-14), [\[311\]](#page-21-15). Identifying fairness-sensitive features among participants is crucial, and the development of collaborative, biasmitigating algorithms is necessary for fair representation.

• **Federated Learning with Large Language Model**. Recently, the Large Language Model (LLM) represents a significant breakthrough, *e.g*., GPT family [\[312\]](#page-21-16), [\[313\]](#page-21-17), [\[314\]](#page-21-18), PaLM series [\[315\]](#page-21-19), [\[316\]](#page-21-20). However, LLM requires the high-quality dataset to improve performance, which incurs expensive data collection cost. Notably, federated learning presents a viable solution by establishing collaboration to eliminate the large-scale data scrape requirement [\[317\]](#page-21-21). However, a major hindrance derives from the communication cost. Specifically, LLM normally contains the enormous parameters scale and presents the strict challenge on the federation communication burden, which relies on the parameter exchange for multiple-party collaboration. Besides, directly sharing overall parameters largely threatens the company interests. Therefore, considering both the communication cost and intellectual property[\[14\]](#page-15-13), achieving controllable and privacy-preserving communication media is important for the federated Large Language Model deployment.

<span id="page-15-31"></span>

### 8.2 Conclusion

To our knowledge, this is the first survey to comprehensively review recent progress in federated learning from generalization, robustness, and fairness aspects. We provided the reader with the necessary background knowledge and summarized more than 100 federated methods according to various criteria, including task settings, learning strategies, and technique contributions. We also present benchmarking results of 8 widely used federated datasets. We discuss the results and provide insight into the shape of future research directions and open problems in the field. In conclusion, federated learning has achieved notable progress attributed to the striking research development, but several challenges still lie ahead.

## REFERENCES

- <span id="page-15-0"></span>[1] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei, "Imagenet: A large-scale hierarchical image database," in *CVPR*. Ieee, 2009, pp. 248–255.
- <span id="page-15-1"></span>[2] O. Russakovsky, J. Deng, H. Su, J. Krause, S. Satheesh, S. Ma, Z. Huang, A. Karpathy, A. Khosla, M. Bernstein, A. C. Berg, and L. Fei-Fei, "Imagenet large scale visual recognition challenge," *IJCV*, pp. 211–252, 2015.
- <span id="page-15-2"></span>[3] A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly *et al.*, "An image is worth 16x16 words: Transformers for image recognition at scale," in *ICLR*, 2021.
- <span id="page-15-3"></span>[4] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *CVPR*, 2016, pp. 770–778.
- <span id="page-15-4"></span>[5] S. Xie, R. Girshick, P. Dollár, Z. Tu, and K. He, "Aggregated residual transformations for deep neural networks," in *CVPR*, 2017, pp. 1492–1500.
- <span id="page-15-5"></span>[6] G. Huang, Z. Liu, G. Pleiss, L. Van Der Maaten, and K. Weinberger, "Convolutional networks with dense connectivity," *IEEE PAMI*, 2019.
- <span id="page-15-6"></span>[7] G. Yenduri, G. Srivastava, P. K. R. Maddikunta, R. H. Jhaveri, W. Wang, A. V. Vasilakos, T. R. Gadekallu *et al.*, "Generative pre-trained transformer: A comprehensive review on enabling technologies, potential applications, emerging challenges, and future directions," *arXiv preprint arXiv:2305.10435*, 2023.
- <span id="page-15-7"></span>[8] A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, Ł. Kaiser, and I. Polosukhin, "Attention is all you need," in *NeurIPS*, 2017.

- <span id="page-15-8"></span>[9] J. Devlin, M.-W. Chang, K. Lee, and K. Toutanova, "Bert: Pretraining of deep bidirectional transformers for language understanding," *arXiv preprint arXiv:1810.04805*, 2018.
- <span id="page-15-9"></span>[10] A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark *et al.*, "Learning transferable visual models from natural language supervision, in *ICML*, 2021, pp. 8748–8763.
- <span id="page-15-10"></span>[11] Y. Li, F. Liang, L. Zhao, Y. Cui, W. Ouyang, J. Shao, F. Yu, and J. Yan, "Supervision exists everywhere: A data efficient contrastive language-image pre-training paradigm," in *ICLR*, 2022.
- <span id="page-15-11"></span>[12] R. Zhang, X. Hu, B. Li, S. Huang, H. Deng, Y. Qiao, P. Gao, and H. Li, "Prompt, generate, then cache: Cascade of foundation models makes strong few-shot learners," in *CVPR*, 2023, pp. 15 211–15 222.
- <span id="page-15-12"></span>[13] T. Chen, C. Gong, D. J. Diaz, X. Chen, J. T. Wells, qiang liu, Z. Wang, A. Ellington, A. Dimakis, and A. Klivans, "Hotprotein: A novel framework for protein thermostability prediction and editing," in *ICLR*, 2023.
- <span id="page-15-13"></span>[14] C. May and S. K. Sell, *Intellectual property rights: A critical history*. Lynne Rienner Publishers Boulder, 2006.
- <span id="page-15-14"></span>[15] I. C. of Investigators for Fairness in Trial Data Sharing, "Toward fairness in data sharing," *New England Journal of Medicine*, vol. 375, no. 5, pp. 405–407, 2016.
- <span id="page-15-15"></span>[16] P. Voigt and A. Von dem Bussche, "The eu general data protection regulation (gdpr)," *A Practical Guide, 1st Ed., Cham: Springer International Publishing*, p. 3152676, 2017.
- <span id="page-15-16"></span>[17] S. L. Pardau, "The california consumer privacy act: Towards a european-style privacy regime in the united states," *J. Tech. L. & Pol'y*, vol. 23, p. 68, 2018.
- <span id="page-15-17"></span>[18] J. Konečnỳ, H. B. McMahan, F. X. Yu, P. Richtárik, A. T. Suresh, and D. Bacon, "Federated learning: Strategies for improving communication efficiency," *arXiv preprint arXiv:1610.05492*, 2016.
- <span id="page-15-18"></span>[19] J. Konečnỳ, H. B. McMahan, D. Ramage, and P. Richtárik, "Federated optimization: Distributed machine learning for on-device intelligence," *arXiv preprint arXiv:1610.02527*, 2016.
- <span id="page-15-19"></span>[20] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication-efficient learning of deep networks from decentralized data," in *AISTATS*, 2017, pp. 1273–1282.
- <span id="page-15-20"></span>[21] Q. Yang, Y. Liu, T. Chen, and Y. Tong, "Federated machine learning: Concept and applications," *ACM TIST*, pp. 1–19, 2019.
- <span id="page-15-21"></span>[22] J. Sun, T. Chen, G. B. Giannakis, Q. Yang, and Z. Yang, "Lazily aggregated quantized gradient innovation for communicationefficient federated learning," *IEEE PAMI*, vol. 44, no. 4, pp. 2031– 2044, 2020.
- <span id="page-15-22"></span>[23] S. Hong and J. Chae, "Communication-efficient randomized algorithm for multi-kernel online federated learning," *IEEE PAMI*, vol. 44, no. 12, pp. 9872–9886, 2021.
- <span id="page-15-23"></span>[24] H. Yang, J. Yuan, C. Li, G. Zhao, Z. Sun, Q. Yao, B. Bao, A. V. Vasilakos, and J. Zhang, "Brainiot: Brain-like productive services provisioning with federated learning in industrial iot," *IEEE IoT-J*, vol. 9, no. 3, pp. 2014–2024, 2021.
- <span id="page-15-24"></span>[25] A. Hard, K. Rao, R. Mathews, S. Ramaswamy, F. Beaufays, S. Augenstein, H. Eichner, C. Kiddon, and D. Ramage, "Federated learning for mobile keyboard prediction," *arXiv preprint arXiv:1811.03604*, 2018.
- <span id="page-15-25"></span>[26] C. Ju, R. Zhao, J. Sun, X. Wei, B. Zhao, Y. Liu, H. Li, T. Chen, X. Zhang, D. Gao *et al.*, "Privacy-preserving technology to help millions of people: Federated prediction model for stroke prevention," *arXiv preprint arXiv:2006.10517*, 2020.
- <span id="page-15-26"></span>[27] W. Zhuang, Y. Wen, X. Zhang, X. Gan, D. Yin, D. Zhou, S. Zhang, and S. Yi, "Performance optimization of federated person reidentification via benchmark analysis," in *ACM MM*, 2020, pp. 955–963.
- <span id="page-15-27"></span>[28] X. Guo, P. Xing, S. Feng, B. Li, and C. Miao, "Federated learning with diversified preference for humor recognition," in *IJCAI Workshop*, 2020.
- <span id="page-15-28"></span>[29] Y. Liu, A. Huang, Y. Luo, H. Huang, Y. Liu, Y. Chen, L. Feng, T. Chen, H. Yu, and Q. Yang, "Fedvision: An online visual object detection platform powered by federated learning," in *AAAI*, 2020, pp. 13 172–13 179.
- <span id="page-15-29"></span>[30] G. Wu and S. Gong, "Decentralised learning from independent multi-domain labels for person re-identification," in *AAAI*, 2021, pp. 2898–2906.
- <span id="page-15-30"></span>[31] S. Pati, U. Baid, B. Edwards, M. Sheller, S.-H. Wang, G. A. Reina, P. Foley, A. Gruzdev, D. Karkada, C. Davatzikos *et al.*, "Federated learning enables big data for rare cancer boundary detection," *Nature communications*, vol. 13, no. 1, p. 7346, 2022.

- <span id="page-16-0"></span>[32] S. Chen, L. Zhang, and L. Zhang, "Msdformer: Multiscale deformable transformer for hyperspectral image super-resolution," *IEEE TGRS*, 2023.
- <span id="page-16-1"></span>[33] N. Shoham, T. Avidor, A. Keren, N. Israel, D. Benditkis, L. Mor-Yosef, and I. Zeitak, "Overcoming forgetting in federated learning on non-iid data," in *NeurIPS Workshop*, 2019.
- <span id="page-16-2"></span>[34] N. Liu, Z. Liang, J. Lin, and Y. Liu, "Patient clustering improves efficiency of federated machine learning to predict mortality and hospital stay time using distributed electronic medical records," *Journal of biomedical informatics*, vol. 99, p. 103291, 2019.
- <span id="page-16-3"></span>K. Hsieh, A. Phanishayee, O. Mutlu, and P. Gibbons, "The noniid data quagmire of decentralized machine learning," in *ICML*, 2020, pp. 4387–4398.
- <span id="page-16-4"></span>[36] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," in *MLSys*, 2020.
- <span id="page-16-5"></span>[37] X. Li, M. Jiang, X. Zhang, M. Kamp, and Q. Dou, "Fed{bn}: Federated learning on non-{iid} features via local batch normalization," in *ICLR*, 2021.
- <span id="page-16-6"></span>C. Wu, F. Wu, L. Lyu, T. Qi, Y. Huang, and X. Xie, "A federated graph neural network framework for privacy-preserving personalization," *Nature Communications*, vol. 13, no. 1, p. 3091, 2022.
- <span id="page-16-7"></span>[39] Y. Tan, Y. Liu, G. Long, J. Jiang, Q. Lu, and C. Zhang, "Federated learning on non-iid graphs via structural knowledge sharing," in *AAAI*, 2023.
- <span id="page-16-8"></span>[40] O. Li, B. He, and D. Song, "Model-contrastive federated learning," in *CVPR*, 2021, pp. 10 713–10 722.
- <span id="page-16-9"></span>[41] M. Luo, F. Chen, D. Hu, Y. Zhang, J. Liang, and J. Feng, "No fear of heterogeneity: Classifier calibration for federated learning with non-iid data," in *NeurIPS*, 2021.
- <span id="page-16-10"></span>[42] L. Zhang, Y. Luo, Y. Bai, B. Du, and L.-Y. Duan, "Federated learning for non-iid data via unified feature learning and optimization objective alignment," in *ICCV*, 2021, pp. 4420–4428.
- <span id="page-16-11"></span>[43] Y. Dandi, L. Barba, and M. Jaggi, "Implicit gradient alignment in distributed and federated learning," in *AAAI*, 2022, pp. 6454– 6462.
- <span id="page-16-12"></span>[44] Z. Qu, X. Li, R. Duan, Y. Liu, B. Tang, and Z. Lu, "Generalized federated learning via sharpness aware minimization," in *ICML*, 2022.
- <span id="page-16-13"></span>[45] H. Yuan, W. Morningstar, L. Ning, and K. Singhal, "What do we mean by generalization in federated learning?" in *ICLR*, 2022.
- <span id="page-16-14"></span>[46] X. Peng, Z. Huang, Y. Zhu, and K. Saenko, "Federated adversarial domain adaptation," in *ICLR*, 2020.
- <span id="page-16-15"></span>[47] Q. Liu, C. Chen, J. Qin, Q. Dou, and P.-A. Heng, "Feddg: Federated domain generalization on medical image segmentation via episodic learning in continuous frequency space," in *CVPR*, 2021, pp. 1013–1023.
- <span id="page-16-16"></span>[48] M. Jiang, H. Yang, C. Cheng, and Q. Dou, "Iop-fl: Inside-outside personalization for federated medical image segmentation," *IEEE TMI*, 2023.
- <span id="page-16-17"></span>[49] L. Jiang and T. Lin, "Test-time robust personalization for federated learning," in *ICLR*, 2023.
- <span id="page-16-18"></span>[50] L. Huang, A. D. Joseph, B. Nelson, B. I. Rubinstein, and J. D. Tygar, "Adversarial machine learning," in *ACM workshop on Security and artificial intelligence*, 2011, pp. 43–58.
- <span id="page-16-19"></span>[51] B. Biggio, B. Nelson, and P. Laskov, "Poisoning attacks against support vector machines," in *ICML*, 2012.
- <span id="page-16-20"></span>[52] T. Gu, B. Dolan-Gavitt, and S. Garg, "Badnets: Identifying vulnerabilities in the machine learning model supply chain," *arXiv preprint arXiv:1708.06733*, 2017.
- <span id="page-16-21"></span>[53] R. Guerraoui, S. Rouault *et al.*, "The hidden vulnerability of distributed learning in byzantium," in *ICML*, 2018, pp. 3521– 3530.
- <span id="page-16-33"></span>[54] B. Van Rooyen, A. Menon, and R. C. Williamson, "Learning with symmetric label noise: The importance of being unhinged," in *NeurIPS*, vol. 28, 2015.
- <span id="page-16-34"></span>[55] B. Han, Q. Yao, X. Yu, G. Niu, M. Xu, W. Hu, I. Tsang, and M. Sugiyama, "Co-teaching: Robust training of deep neural networks with extremely noisy labels," in *NeurIPS*, vol. 31, 2018.
- <span id="page-16-35"></span>[56] G. Baruch, M. Baruch, and Y. Goldberg, "A little is enough: Circumventing defenses for distributed learning," in *NeurIPS*, vol. 32, 2019.
- <span id="page-16-36"></span>[57] C. Xie, O. Koyejo, and I. Gupta, "Fall of empires: Breaking byzantine-tolerant sgd by inner product manipulation," in *UAI*, 2020, pp. 261–270.

- <span id="page-16-37"></span>[58] M. Fang, X. Cao, J. Jia, and N. Z. Gong, "Local model poisoning attacks to byzantine-robust federated learning," in *USENIX*, 2020, pp. 1623–1640.
- <span id="page-16-38"></span>[59] X. Chen, C. Liu, B. Li, K. Lu, and D. Song, "Targeted backdoor attacks on deep learning systems using data poisoning," *arXiv preprint arXiv:1712.05526*, 2017.
- <span id="page-16-39"></span>[60] C. Liao, H. Zhong, A. Squicciarini, S. Zhu, and D. Miller, "Backdoor embedding in convolutional neural network models via invisible perturbation," *arXiv preprint arXiv:1808.10307*, 2018.
- <span id="page-16-40"></span>[61] T. Gu, K. Liu, B. Dolan-Gavitt, and S. Garg, "Badnets: Evaluating backdooring attacks on deep neural networks," *IEEE Access*, vol. 7, pp. 47 230–47 244, 2019.
- <span id="page-16-41"></span>[62] E. Bagdasaryan, A. Veit, Y. Hua, D. Estrin, and V. Shmatikov, "How to backdoor federated learning," in *AISTATS*, 2020, pp. 2938–2948.
- <span id="page-16-42"></span>[63] C. Xie, K. Huang, P.-Y. Chen, and B. Li, "Dba: Distributed backdoor attacks against federated learning," in *ICLR*, 2020.
- <span id="page-16-43"></span>[64] X. Lyu, Y. Han, W. Wang, J. Liu, B. Wang, J. Liu, and X. Zhang, "Poisoning with cerberus: stealthy and colluded backdoor attack against federated learning," in *AAAI*, 2023.
- <span id="page-16-44"></span>[65] L. Lyu, J. Yu, K. Nandakumar, Y. Li, X. Ma, J. Jin, H. Yu, and K. S. Ng, "Towards fair and privacy-preserving federated deep models," *IEEE Transactions on Parallel and Distributed Systems*, vol. 31, no. 11, pp. 2524–2541, 2020.
- <span id="page-16-31"></span>[66] T. H. Rafi, F. A. Noor, T. Hussain, and D.-K. Chae, "Fairness and privacy-preserving in federated learning: A survey," *arXiv preprint arXiv:2306.08402*, 2023.
- <span id="page-16-32"></span>[67] Y. Shi, H. Yu, and C. Leung, "Towards fairness-aware federated learning," IEEE TNNLS, 2023.
- <span id="page-16-45"></span>[68] T. Song, Y. Tong, and S. Wei, "Profit allocation for federated learning," in *IEEE Big Data*. IEEE, 2019, pp. 2577–2586.
- <span id="page-16-46"></span>[69] J. Kang, Z. Xiong, D. Niyato, S. Xie, and J. Zhang, "Incentive mechanism for reliable federated learning: A joint optimization approach to combining reputation and contract theory," *IEEE TOJ*, vol. 6, no. 6, pp. 10 700–10 714, 2019.
- <span id="page-16-47"></span>[70] L. Lyu, X. Xu, Q. Wang, and H. Yu, "Collaborative fairness in federated learning," *Federated Learning: Privacy and Incentive*, pp. 189–204, 2020.
- <span id="page-16-48"></span>[71] M. Mohri, G. Sivek, and A. T. Suresh, "Agnostic federated learning," in *ICML*. PMLR, 2019, pp. 4615–4625.
- <span id="page-16-49"></span>[72] T. Li, M. Sanjabi, A. Beirami, and V. Smith, "Fair resource allocation in federated learning," in *ICLR*, 2020.
- <span id="page-16-50"></span>[73] J. Hong, Z. Zhu, S. Yu, Z. Wang, H. H. Dodge, and J. Zhou, "Federated adversarial debiasing for fair and transferable representations," in *ACM SIGKDD*, 2021, pp. 617–627.
- <span id="page-16-30"></span>[74] D. C. Nguyen, Q.-V. Pham, P. N. Pathirana, M. Ding, A. Seneviratne, Z. Lin, O. Dobre, and W.-J. Hwang, "Federated learning for smart healthcare: A survey," *CSUR*, pp. 1–37, 2022.
- <span id="page-16-51"></span>[75] A. Nguyen, T. Do, M. Tran, B. X. Nguyen, C. Duong, T. Phan, E. Tjiputra, and Q. D. Tran, "Deep federated learning for autonomous driving," in *IEEE IV*. IEEE, 2022, pp. 1824–1830.
- <span id="page-16-52"></span>[76] W. Zheng, L. Yan, C. Gou, and F.-Y. Wang, "Federated metalearning for fraudulent credit card detection," in *IJCAI*, 2021, pp. 4654–4660.
- <span id="page-16-22"></span>[77] Y. Zhao, M. Li, L. Lai, N. Suda, D. Civin, and V. Chandra, "Federated learning with non-iid data," *arXiv preprint arXiv:1806.00582*, 2018.
- <span id="page-16-23"></span>[78] V. Kulkarni, M. Kulkarni, and A. Pant, "Survey of personalization techniques for federated learning," in *WorldS4*, 2020, pp. 794-797.
- <span id="page-16-24"></span>[79] T. Li, A. K. Sahu, A. Talwalkar, and V. Smith, "Federated learning: Challenges, methods, and future directions," *IEEE SPM*, pp. 50– 60, 2020.
- <span id="page-16-25"></span>[80] L. Lyu, H. Yu, and Q. Yang, "Threats to federated learning: A survey," *arXiv preprint arXiv:2003.02133*, 2020.
- <span id="page-16-26"></span>[81] H. Zhu, J. Xu, S. Liu, and Y. Jin, "Federated learning on non-IID data: A survey," *NC*, pp. 371–390, 2021.
- <span id="page-16-27"></span>[82] P. Kairouz, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. Bonawitz, Z. Charles, G. Cormode, R. Cummings et al., "Advances and open problems in federated learning," *Found. Trends Mach. Learn.*, pp. 1–210, 2021.
- <span id="page-16-28"></span>[83] V. Mothukuri, R. M. Parizi, S. Pouriyeh, Y. Huang, A. Dehghantanha, and G. Srivastava, "A survey on security and privacy of federated learning," *Future Generation Computer Systems*, pp. 619– 640, 2021.
- <span id="page-16-29"></span>[84] Q. Li, Z. Wen, Z. Wu, S. Hu, N. Wang, Y. Li, X. Liu, and B. He, "A survey on federated learning systems: vision, hype and reality for data privacy and protection," *IEEE TKDE*, 2021.

- <span id="page-17-0"></span>[85] C. Xu, Y. Qu, Y. Xiang, and L. Gao, "Asynchronous federated learning on heterogeneous devices: A survey," *arXiv preprint arXiv:2109.04269*, 2021.
- <span id="page-17-1"></span>[86] J. Shi, W. Wan, S. Hu, J. Lu, and L. Y. Zhang, "Challenges and approaches for mitigating byzantine attacks in federated learning," in *IEEE TrustCom*. IEEE, 2022, pp. 139–146.
- <span id="page-17-2"></span>[87] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non-iid data silos: An experimental study," *IEEE TKDE*, 2022.
- <span id="page-17-3"></span>[88] X. Liu, T. Shi, C. Xie, Q. Li, K. Hu, H. Kim, X. Xu, B. Li, and D. Song, "Unifed: A benchmark for federated learning frameworks," *arXiv preprint arXiv:2207.10308*, 2022.
- <span id="page-17-4"></span>[89] L. Lyu, H. Yu, X. Ma, C. Chen, L. Sun, J. Zhao, Q. Yang, and S. Y. Philip, "Privacy and robustness in federated learning: Attacks and defenses," *IEEE TNNLS*, 2022.
- <span id="page-17-5"></span>[90] X. Ma, J. Zhu, Z. Lin, S. Chen, and Y. Qin, "A state-of-the-art survey on solving non-iid data in federated learning," *Future Generation Computer Systems*, vol. 135, pp. 244–258, 2022.
- <span id="page-17-6"></span>[91] Y. Li, X. Wang, R. Zeng, P. K. Donta, I. Murturi, M. Huang, and S. Dustdar, "Federated domain generalization: A survey," *arXiv preprint arXiv:2306.01334*, 2023.
- <span id="page-17-7"></span>[92] J. Shao, Z. Li, W. Sun, T. Zhou, Y. Sun, L. Liu, Z. Lin, and J. Zhang, "A survey of what to share in federated learning: Perspectives on model utility, privacy leakage, and communication efficiency," *arXiv preprint arXiv:2307.10655*, 2023.
- <span id="page-17-8"></span>[93] M. Ye, X. Fang, B. Du, P. C. Yuen, and D. Tao, "Heterogeneous federated learning: State-of-the-art and research challenges," *CSUR*, 2023.
- <span id="page-17-9"></span>[94] O. A. Wahab, A. Mourad, H. Otrok, and T. Taleb, "Federated machine learning: Survey, multi-level classification, desirable criteria and future directions in communication and networking systems," *IEEE CST*, pp. 1342–1397, 2021.
- <span id="page-17-10"></span>N. Rodríguez-Barroso, D. Jiménez-López, M. V. Luzón, F. Herrera, and E. Martínez-Cámara, "Survey on federated learning threats: Concepts, taxonomy on attacks and defences, experimental study and challenges," *Information Fusion*, vol. 90, pp. 148–173, 2023.
- <span id="page-17-11"></span>[96] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non-iid data silos: An experimental study," in *ICDE*, 2022, pp. 965–978.
- <span id="page-17-12"></span>[97] Y. Jin, Y. Liu, K. Chen, and Q. Yang, "Federated learning without full labels: A survey," *arXiv preprint arXiv:2303.14453*, 2023.
- <span id="page-17-13"></span>[98] J. H. Yoo, H. Jeong, J. Lee, and T.-M. Chung, "Federated learning: Issues in medical application," in *FDSE*, 2021, pp. 3–22.
- <span id="page-17-14"></span>[99] J. Miao, Z. Yang, L. Fan, and Y. Yang, "Fedseg: Classheterogeneous federated learning for semantic segmentation," in *CVPR*, 2023, pp. 8042–8052.
- <span id="page-17-15"></span>[100] Y. Liu, Y. Kang, T. Zou, Y. Pu, Y. He, X. Ye, Y. Ouyang, Y.-Q. Zhang, and Q. Yang, "Vertical federated learning," *arXiv preprint arXiv:2211.12814*, 2022.
- <span id="page-17-16"></span>[101] K. Wei, J. Li, C. Ma, M. Ding, S. Wei, F. Wu, G. Chen, and T. Ranbaduge, "Vertical federated learning: Challenges, methodologies and experiments," *arXiv preprint arXiv:2202.04309*, 2022.
- <span id="page-17-17"></span>[102] Y. Liu, Y. Kang, C. Xing, T. Chen, and Q. Yang, "A secure federated transfer learning framework," *IEEE TS*, vol. 35, no. 4, pp. 70–82, 2020.
- <span id="page-17-18"></span>[103] S. Saha and T. Ahmad, "Federated transfer learning: Concept and applications," *IA*, vol. 15, no. 1, pp. 35–44, 2021.
- <span id="page-17-19"></span>[104] X. Li, K. Huang, W. Yang, S. Wang, and Z. Zhang, "On the convergence of fedavg on non-iid data," *arXiv preprint arXiv:1907.02189*, 2019.
- <span id="page-17-20"></span>[105] H. Yang, M. Fang, and J. Liu, "Achieving linear speedup with partial worker participation in non-iid federated learning," in *ICLR*, 2021.
- <span id="page-17-21"></span>[106] J. Zhang, Z. Li, B. Li, J. Xu, S. Wu, S. Ding, and C. Wu, "Federated learning with label distribution skew via logits calibration," in *ICML*, 2022, pp. 26 311–26 329.
- <span id="page-17-22"></span>[107] Y. Tan, G. Long, L. Liu, T. Zhou, Q. Lu, J. Jiang, and C. Zhang, "Fedproto: Federated prototype learning across heterogeneous clients," in *AAAI*, 2022.
- <span id="page-17-23"></span>[108] Y. Tan, G. Long, J. Ma, L. Liu, T. Zhou, and J. Jiang, "Federated learning from pre-trained models: A contrastive learning approach," in *NeurIPS*, 2022.
- <span id="page-17-24"></span>[109] J. Chen, M. Jiang, Q. Dou, and Q. Chen, "Federated domain generalization for image recognition via cross-client style transfer," in *WACV*, 2023, pp. 361–370.
- <span id="page-17-25"></span>[110] Z. Zhou, S. S. Azam, C. Brinton, and D. I. Inouye, "Efficient federated domain translation," in *ICLR*, 2023.

- <span id="page-17-26"></span>[111] G. Damaskinos, R. Guerraoui, R. Patra, M. Taziki *et al.*, "Asynchronous byzantine machine learning (the case of sgd)," in *ICML*. PMLR, 2018, pp. 1145–1154.
- <span id="page-17-27"></span>[112] L. Muñoz-González, K. T. Co, and E. C. Lupu, "Byzantine-robust" federated machine learning through adaptive model averaging," *arXiv preprint arXiv:1909.05125*, 2019.
- <span id="page-17-28"></span>[113] Z. Sun, P. Kairouz, A. T. Suresh, and H. B. McMahan, "Can you really backdoor federated learning?" in *NeurIPS*, 2019.
- <span id="page-17-29"></span>[114] H. Wang, K. Sreenivasan, S. Rajput, H. Vishwakarma, S. Agarwal, J.-y. Sohn, K. Lee, and D. Papailiopoulos, "Attack of the tails: Yes, you really can backdoor federated learning," in *NeurIPS*, 2020, pp. 16 070–16 084.
- <span id="page-17-30"></span>[115] T. Li, S. Hu, A. Beirami, and V. Smith, "Ditto: Fair and robust federated learning through personalization," in *ICML*, 2021, pp. 6357–6368.
- <span id="page-17-31"></span>[116] M. Jiang, H. R. Roth, W. Li, D. Yang, C. Zhao, V. Nath, D. Xu, Q. Dou, and Z. Xu, "Fair federated medical image segmentation via client contribution estimation," in *CVPR*, 2023.
- <span id="page-17-32"></span>[117] Q. P. Nguyen, B. K. H. Low, and P. Jaillet, "Trade-off between payoff and model rewards in shapley-fair collaborative machine learning," in *NeurIPS*, 2022, pp. 30 542–30 553.
- <span id="page-17-33"></span>[118] S. Cui, W. Pan, J. Liang, C. Zhang, and F. Wang, "Addressing algorithmic disparity and performance inconsistency in federated learning," in *NeurIPS*, 2021, pp. 26 091–26 102.
- <span id="page-17-34"></span>[119] Y. H. Ezzeldin, S. Yan, C. He, E. Ferrara, and A. S. Avestimehr, "Fairfed: Enabling group fairness in federated learning," in *AAAI*, 2023, pp. 7494–7502.
- <span id="page-17-35"></span>[120] W. Huang, M. Ye, Z. Shi, H. Li, and B. Du, "Rethinking federated learning with domain shift: A prototype view," in *CVPR*, 2023, pp. 16 312–16 322.
- <span id="page-17-36"></span>[121] J. Wang, Q. Liu, H. Liang, G. Joshi, and H. V. Poor, "Tackling the objective inconsistency problem in heterogeneous federated optimization," in *NeurIPS*, 2020, pp. 7611–7623.
- <span id="page-17-37"></span>[122] Z. Qu, K. Lin, J. Kalagnanam, Z. Li, J. Zhou, and Z. Zhou, "Federated learning's blessing: Fedavg has linear speedup," in *ICLR*, 2021.
- <span id="page-17-38"></span>[123] M. Glasgow, H. Yuan, and T. Ma, "Sharp bounds for federated averaging (local sgd) and continuous perspective," in *AISTATS*, 2022.
- <span id="page-17-39"></span>[124] S. Kotz, N. Balakrishnan, and N. L. Johnson, *Continuous multivariate distributions, Volume 1: Models and applications*. John Wiley & Sons, 2004, vol. 1.
- <span id="page-17-40"></span>[125] W. Huang, M. Ye, B. Du, and X. Gao, "Few-shot model agnostic federated learning," in *ACM MM*, 2022, pp. 7309–7316.
- <span id="page-17-41"></span>[126] P. Blanchard, E. M. El Mhamdi, R. Guerraoui, and J. Stainer, "Machine learning with adversaries: Byzantine tolerant gradient descent," in *NeurIPS*, 2017.
- <span id="page-17-42"></span>[127] A. N. Bhagoji, S. Chakraborty, P. Mittal, and S. Calo, "Analyzing federated learning through an adversarial lens," in *ICML*, 2019, pp. 634–643.
- <span id="page-17-43"></span>[128] V. Shejwalkar and A. Houmansadr, "Manipulating the byzantine: Optimizing model poisoning attacks and defenses for federated learning," in *NDSS*, 2021.
- <span id="page-17-44"></span>[129] A. N. Bhagoji, S. Chakraborty, P. Mittal, and S. Calo, "Analyzing federated learning through an adversarial lens," in *ICML*. PMLR, 2019, pp. 634–643.
- <span id="page-17-45"></span>[130] Z. Zhang, Q. Liu, Z. Wang, Z. Lu, and Q. Hu, "Backdoor defense via deconfounded representation learning," in *CVPR*, 2023, pp. 12 228–12 238.
- <span id="page-17-46"></span>[131] C. Fung, C. J. Yoon, and I. Beschastnikh, "Mitigating sybils in federated learning poisoning," *arXiv preprint arXiv:1808.04866*, 2018.
- <span id="page-17-47"></span>[132] P. Fang and J. Chen, "On the vulnerability of backdoor defenses for federated learning," in *AAAI*, 2023.
- <span id="page-17-48"></span>[133] A. Huang, "Dynamic backdoor attacks against federated learning," *arXiv preprint arXiv:2011.07429*, 2020.
- <span id="page-17-49"></span>[134] H. Li, C. Wu, S. Zhu, and Z. Zheng, "Learning to backdoor federated learning," in *ICLR Workshop*, 2023.
- <span id="page-17-50"></span>[135] X. Zhang, F. Li, Z. Zhang, Q. Li, C. Wang, and J. Wu, "Enabling execution assurance of federated learning at untrusted participants," in *INFOCOM*. IEEE, 2020, pp. 1877–1886.
- <span id="page-17-51"></span>[136] P. P. Liang, T. Liu, L. Ziyin, N. B. Allen, R. P. Auerbach, D. Brent, R. Salakhutdinov, and L.-P. Morency, "Think locally, act globally: Federated learning with local and global representations," in *NeurIPS Workshop*, 2020.

- <span id="page-18-0"></span>[137] S. S. Tay, X. Xu, C. S. Foo, and B. K. H. Low, "Incentivizing collaboration in machine learning via synthetic data rewards, in *AAAI*, 2022, pp. 9448–9456.
- <span id="page-18-1"></span>[138] R. B. Myerson, *Game theory: analysis of conflict*. Harvard university press, 1997.
- <span id="page-18-2"></span>[139] L. S. Shapley, "A value for n-person games," *Classics in game theory*, vol. 69, 1997.
- <span id="page-18-3"></span>[140] J. M. Bilbao, *Cooperative games on combinatorial structures*. Springer Science & Business Media, 2012, vol. 26.
- <span id="page-18-4"></span>[141] M. Davis and M. Maschler, "The kernel of a cooperative game," *Naval Research Logistics Quarterly*, vol. 12, no. 3, pp. 223–259, 1965.
- <span id="page-18-5"></span>[142] Y. LeCun, L. Bottou, Y. Bengio, and P. Haffner, "Gradient-based learning applied to document recognition," *Proceedings of the IEEE*, pp. 2278–2324, 1998.
- <span id="page-18-6"></span>[143] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," in *NeurIPS Workshop*, 2011.
- <span id="page-18-7"></span>[144] D. Caldarola, M. Mancini, F. Galasso, M. Ciccone, E. Rodola, ` and B. Caputo, "Cluster-driven graph federated learning over multiple domains," in *CVPRW*, 2021, pp. 2749–2758.
- <span id="page-18-8"></span>[145] Z. Wang, X. Fan, J. Qi, C. Wen, C. Wang, and R. Yu, "Federated learning with fair averaging," in *IJCAI*, 2021.
- <span id="page-18-9"></span>[146] D. A. E. Acar, Y. Zhao, R. Matas, M. Mattina, P. Whatmough, and V. Saligrama, "Federated learning based on dynamic regularization," in *ICLR*, 2021.
- <span id="page-18-10"></span>[147] S. P. Karimireddy, S. Kale, M. Mohri, S. J. Reddi, S. U. Stich, and A. T. Suresh, "Scaffold: Stochastic controlled averaging for ondevice federated learning," in *ICML*, 2020.
- <span id="page-18-11"></span>[148] L. Gao, H. Fu, L. Li, Y. Chen, M. Xu, and C.-Z. Xu, "Feddc: Federated learning with non-iid data via local drift decoupling and correction," in *CVPR*, 2022.
- <span id="page-18-12"></span>[149] B. Li, M. N. Schmidt, T. S. Alstrøm, and S. U. Stich, "Partial variance reduction improves non-convex federated learning on heterogeneous data," in *CVPR*, 2023.
- <span id="page-18-13"></span>[150] K. Luo, X. Li, Y. Lan, and M. Gao, "Gradma: A gradient-memorybased accelerated federated learning with alleviated catastrophic forgetting," in *CVPR*, 2023, pp. 3708–3717.
- <span id="page-18-14"></span>[151] E. L. Zec, J. Martinsson, O. Mogren, L. R. Sütfeld, and D. Gillblad, "Specialized federated learning using mixture of experts," *arXiv preprint arXiv:2010.02056*, 2020.
- <span id="page-18-15"></span>[152] G. Lee, M. Jeong, Y. Shin, S. Bae, and S.-Y. Yun, "Preservation of the global knowledge by not-true distillation in federated learning," in *NeurIPS*, 2022.
- <span id="page-18-16"></span>[153] N. Wu, L. Yu, X. Jiang, K.-T. Cheng, and Z. Yan, "FedNoRo: Towards noise-robust federated learning by addressing class imbalance and label noise heterogeneity," in *IJCAI*, 2023.
- <span id="page-18-17"></span>[154] X. Mu, Y. Shen, K. Cheng, X. Geng, J. Fu, T. Zhang, and Z. Zhang, "Fedproc: Prototypical contrastive federated learning on non-iid data," *arXiv preprint arXiv:2109.12273*, 2021.
- <span id="page-18-18"></span>[155] M. Jiang, Z. Wang, and Q. Dou, "Harmofl: Harmonizing local and global drifts in federated learning on heterogeneous medical images," in *AAAI*, 2022, pp. 1087–1095.
- <span id="page-18-19"></span>[156] T. Zhou and E. Konukoglu, "FedFA: Federated feature augmentation," in *ICLR*, 2023.
- <span id="page-18-20"></span>[157] J. Kim, G. Kim, and B. Han, "Multi-level branched regularization for federated learning," in *ICML*, 2022, pp. 11 058–11 073.
- <span id="page-18-21"></span>[158] Y. Wu, Y. Kang, J. Luo, Y. He, and Q. Yang, "Fedcg: Leverage conditional gan for protecting privacy and maintaining competitive performance in federated learning," in *IJCAI*, 2022, pp. 2334– 2340.
- <span id="page-18-22"></span>[159] Q. Li, B. He, and D. Song, "Adversarial collaborative learning on non-iid features," in *ICML*, 2023.
- <span id="page-18-23"></span>[160] H. Wang, Y. Li, W. Xu, R. Li, Y. Zhan, and Z. Zeng, "Dafkd: Domain-aware federated knowledge distillation," in *CVPR*, 2023.
- <span id="page-18-24"></span>[161] X.-C. Li and D.-C. Zhan, "Fedrs: Federated learning with restricted softmax for label distribution non-iid data," in *ACM SIGKDD*, 2021, pp. 995–1005.
- <span id="page-18-25"></span>[162] M. Mendieta, T. Yang, P. Wang, M. Lee, Z. Ding, and C. Chen, "Local learning matters: Rethinking data heterogeneity in federated learning," in *CVPR*, 2022, pp. 8397–8406.
- <span id="page-18-26"></span>[163] Y. Shi, J. Liang, W. Zhang, V. Y. Tan, and S. Bai, "Towards understanding and mitigating dimensional collapse in heterogeneous federated learning," in *ICLR*, 2023.
- <span id="page-18-27"></span>[164] P. Tian, Z. Chen, W. Yu, and W. Liao, "Towards asynchronous federated learning based threat detection: A dc-adam approach," *Computers & Security*, vol. 108, p. 102344, 2021.

- <span id="page-18-28"></span>[165] F. Sattler, T. Korjakow, R. Rischke, and W. Samek, "Fedaux: Leveraging unlabeled auxiliary data in federated learning," *IEEE TNNLS*, 2021.
- <span id="page-18-29"></span>[166] T. Yoon, S. Shin, S. J. Hwang, and E. Yang, "Fedmix: Approximation of mixup under mean augmented federated learning," in *ICLR*, 2021.
- <span id="page-18-30"></span>[167] Z. Zhu, J. Hong, and J. Zhou, "Data-free knowledge distillation for heterogeneous federated learning," in *ICML*, 2021, pp. 12 878– 12 889.
- <span id="page-18-31"></span>[168] Z. Wang, Y. Zhu, D. Wang, and Z. Han, "Fedacs: Federated skewness analytics in heterogeneous decentralized data environments," in *IWQOS*. IEEE, 2021, pp. 1–10.
- <span id="page-18-32"></span>[169] X. Xu, H. Li, Z. Li, and X. Zhou, "Safe: Synergic data filtering for federated learning in cloud-edge computing," *IEEE TII*, vol. 19, no. 2, pp. 1655–1665, 2022.
- <span id="page-18-33"></span>[170] H.-Y. Chen and W.-L. Chao, "Fedbe: Making bayesian model ensemble applicable to federated learning," in *ICLR*, 2021.
- <span id="page-18-34"></span>[171] C. Dengsheng, J. Hu, V. J. K. Tan, and E. Wu, "Elastic aggregation for federated optimization," in *CVPR*, 2023.
- <span id="page-18-35"></span>[172] D. Li and J. Wang, "Fedmd: Heterogenous federated learning via model distillation," in *NeurIPS Workshop*, 2019.
- <span id="page-18-36"></span>[173] T. Lin, L. Kong, S. U. Stich, and M. Jaggi, "Ensemble distillation for robust model fusion in federated learning," in *NeurIPS*, 2020, pp. 2351–2363.
- <span id="page-18-37"></span>[174] C. He, M. Annavaram, and S. Avestimehr, "Group knowledge transfer: Federated learning of large cnns at the edge," in *NeurIPS*, 2020, pp. 14 068–14 080.
- <span id="page-18-38"></span>[175] S. Reddi, Z. Charles, M. Zaheer, Z. Garrett, K. Rush, J. Konečnỳ, S. Kumar, and H. B. McMahan, "Adaptive federated optimization," in *ICLR*, 2021.
- <span id="page-18-39"></span>[176] W. Huang, M. Ye, and B. Du, "Learn from others and be yourself in heterogeneous federated learning," in *CVPR*, 2022.
- <span id="page-18-40"></span>[177] B. Peng, M. Chi, and C. Liu, "Non-iid federated learning via random exchange of local feature maps for textile iiot secure computing," *SCIS*, vol. 65, no. 7, p. 170302, 2022.
- <span id="page-18-41"></span>[178] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial nets," in *NeurIPS*, 2014.
- <span id="page-18-42"></span>[179] F. Yu, W. Zhang, Z. Qin, Z. Xu, D. Wang, C. Liu, Z. Tian, and X. Chen, "Fed2: Feature-aligned federated learning," in *ACM SIGKDD*, 2021, pp. 2066–2074.
- <span id="page-18-43"></span>[180] P.-T. De Boer, D. P. Kroese, S. Mannor, and R. Y. Rubinstein, "A tutorial on the cross-entropy method," *Ann. Oper. Res.*, pp. 19–67, 2005.
- <span id="page-18-44"></span>[181] S. Itahara, T. Nishio, Y. Koda, M. Morikura, and K. Yamamoto, "Distillation-based semi-supervised federated learning for communication-efficient collaborative training with non-iid private data," *IEEE TMC*, vol. 22, no. 1, pp. 191–205, 2021.
- <span id="page-18-45"></span>[182] W. Hao, M. El-Khamy, J. Lee, J. Zhang, K. J. Liang, C. Chen, and L. C. Duke, "Towards fair federated learning with zero-shot data augmentation," in *CVPRW*, 2021, pp. 3310–3319.
- <span id="page-18-46"></span>[183] S. Ioffe and C. Szegedy, "Batch normalization: Accelerating deep network training by reducing internal covariate shift," in *ICML*, 2015, pp. 448–456.
- <span id="page-18-47"></span>[184] H. Zhang, M. Cisse, Y. N. Dauphin, and D. Lopez-Paz, "mixup: Beyond empirical risk minimization," in *ICLR*, 2018.
- <span id="page-18-48"></span>[185] S. Thulasidasan, G. Chennupati, J. A. Bilmes, T. Bhattacharya, and S. Michalak, "On mixup training: Improved calibration and predictive uncertainty for deep neural networks," in *NeurIPS*, 2019.
- <span id="page-18-49"></span>[186] M. Duan, D. Liu, X. Chen, Y. Tan, J. Ren, L. Qiao, and L. Liang, "Astraea: Self-balancing federated learning for improving classification accuracy of mobile deep learning applications," in *ICCD*. IEEE, 2019, pp. 246–254.
- <span id="page-18-50"></span>[187] M. Shin, C. Hwang, J. Kim, J. Park, M. Bennis, and S.-L. Kim, "Xor mixup: Privacy-preserving data augmentation for one-shot federated learning," *arXiv preprint arXiv:2006.05148*, 2020.
- <span id="page-18-51"></span>[188] E. Jeong, S. Oh, H. Kim, J. Park, M. Bennis, and S.-L. Kim, "Communication-efficient on-device machine learning: Federated distillation and augmentation under non-iid private data," *arXiv preprint arXiv:1811.11479*, 2018.
- <span id="page-18-52"></span>[189] L. Cai, D. Lin, J. Zhang, and S. Yu, "Dynamic sample selection for federated learning with heterogeneous data in fog computing," in *ICC*. IEEE, 2020, pp. 1–6.
- <span id="page-18-53"></span>[190] C. Wang, Y. Yang, and P. Zhou, "Towards efficient scheduling of federated mobile devices under computational and statistical heterogeneity," *IEEE TPDS*, vol. 32, no. 2, pp. 394–410, 2020.

- <span id="page-19-0"></span>[191] H. Wang, Z. Kaplan, D. Niu, and B. Li, "Optimizing federated learning on non-iid data with reinforcement learning," in *INFO-COM*. IEEE, 2020, pp. 1698–1707.
- <span id="page-19-1"></span>[192] X. Xu, S. Duan, J. Zhang, Y. Luo, and D. Zhang, "Optimizing federated learning on device heterogeneity with a sampling strategy," in *IWQOS*. IEEE, 2021, pp. 1–10.
- <span id="page-19-2"></span>[193] M. Tang, X. Ning, Y. Wang, J. Sun, Y. Wang, H. Li, and Y. Chen, "Fedcor: Correlation-based active client selection strategy for heterogeneous federated learning," in *CVPR*, 2022, pp. 10 102– 10 111.
- <span id="page-19-3"></span>[194] Y. J. Cho, J. Wang, and G. Joshi, "Towards understanding biased client selection in federated learning," in *AISTATS*, 2022, pp. 10 351–10 375.
- <span id="page-19-4"></span>[195] M. Yurochkin, M. Agarwal, S. Ghosh, K. Greenewald, N. Hoang, and Y. Khazaeni, "Bayesian nonparametric federated learning of neural networks," in *ICML*, 2019.
- <span id="page-19-5"></span>[196] M. Yurochkin, M. Agarwal, S. Ghosh, K. Greenewald, and N. Hoang, "Statistical model aggregation via parameter matching," in *NeurIPS*, 2019.
- <span id="page-19-6"></span>[197] H. Wang, M. Yurochkin, Y. Sun, D. Papailiopoulos, and Y. Khazaeni, "Federated learning with matched averaging," in *ICLR*, 2020.
- <span id="page-19-7"></span>[198] M. Al-Shedivat, J. Gillenwater, E. Xing, and A. Rostamizadeh, "Federated learning via posterior averaging: A new perspective and practical algorithms," in *ICLR*, 2021.
- <span id="page-19-8"></span>[199] S. P. Singh and M. Jaggi, "Model fusion via optimal transport," in *NeurIPS*, 2020.
- <span id="page-19-9"></span>[200] Y. Xia, D. Yang, W. Li, A. Myronenko, D. Xu, H. Obinata, H. Mori, P. An, S. Harmon, E. Turkbey *et al.*, "Auto-fedavg: learnable federated averaging for multi-institutional medical image segmentation," *arXiv preprint arXiv:2104.10195*, 2021.
- <span id="page-19-10"></span>[201] Y. Xie, W. Zhang, R. Pi, F. Wu, Q. Chen, X. Xie, and S. Kim, "Optimizing server-side aggregation for robust federated learning via subspace training," *arXiv preprint arXiv:2211.05554*, 2022.
- <span id="page-19-11"></span>[202] Q. Sun, X. Li, J. Zhang, L. Xiong, W. Liu, J. Liu, Z. Qin, and K. Ren, "Shapleyfl: Robust federated learning based on shapley value," in *ACM SIGKDD*, 2023, pp. 2096–2108.
- <span id="page-19-12"></span>[203] R. Balakrishnan, T. Li, T. Zhou, N. Himayat, V. Smith, and J. Bilmes, "Diverse client selection for federated learning via submodular maximization," in *ICLR*, 2021.
- <span id="page-19-13"></span>[204] C. Palihawadana, N. Wiratunga, A. Wijekoon, and H. Kalutarage, "Fedsim: Similarity guided model aggregation for federated learning," *NC*, vol. 483, pp. 432–445, 2022.
- <span id="page-19-14"></span>[205] Z. Li, T. Lin, X. Shang, and C. Wu, "Revisiting weighted aggregation in federated learning with neural networks," in *ICML*, 2023.
- <span id="page-19-20"></span>[206] T.-M. H. Hsu, H. Qi, and M. Brown, "Measuring the effects of non-identical data distribution for federated visual classification," *arXiv preprint arXiv:1909.06335*, 2019.
- <span id="page-19-21"></span>[207] L. Zhang, L. Shen, L. Ding, D. Tao, and L.-Y. Duan, "Fine-tuning global model via data-free knowledge distillation for non-iid federated learning," in *CVPR*, 2022, pp. 10 174–10 183.
- <span id="page-19-22"></span>[208] X. Shang, Y. Lu, G. Huang, and H. Wang, "Federated learning on heterogeneous and long-tailed data via classifier re-training with federated features," in *IJCAI*, 2022.
- <span id="page-19-23"></span>[209] Y. Xiong, R. Wang, M. Cheng, F. Yu, and C.-J. Hsieh, "Feddm: Iterative distribution matching for communication-efficient federated learning," in *CVPR*, 2023.
- <span id="page-19-24"></span>[210] R. Pi, W. Zhang, Y. Xie, J. Gao, X. Wang, S. Kim, and Q. Chen, "Dynafed: Tackling client data heterogeneity with global dynamics," in *CVPR*, 2023.
- <span id="page-19-25"></span>[211] C. Buciluă, R. Caruana, and A. Niculescu-Mizil, "Model compression," in *ACM SIGKDD*, 2006, pp. 535–541.
- <span id="page-19-26"></span>[212] V. Vapnik, R. Izmailov *et al.*, "Learning using privileged information: similarity control and knowledge transfer." *JMLR*, pp. 2023–2049, 2015.
- <span id="page-19-27"></span>[213] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-19-28"></span>[214] D. Lopez-Paz, L. Bottou, B. Schölkopf, and V. Vapnik, "Unifying distillation and privileged information," in *ICLR*, 2016.
- <span id="page-19-29"></span>[215] L. Sun and L. Lyu, "Federated model distillation with noise-free differential privacy," in *IJCAI*, 2021.
- <span id="page-19-30"></span>[216] Y. Yeganeh, A. Farshad, N. Navab, and S. Albarqouni, "Inverse distance aggregation for federated learning with non-iid data," in *MICCAI Workshop*. Springer, 2020, pp. 150–159.
- <span id="page-19-31"></span>[217] L. Li, M. Duan, D. Liu, Y. Zhang, A. Ren, X. Chen, Y. Tan, and C. Wang, "Fedsae: A novel self-adaptive federated learning framework in heterogeneous systems," in *IJCNN*. IEEE, 2021, pp. 1–10.

- <span id="page-19-32"></span>[218] J. Zhang, X. Cheng, C. Wang, Y. Wang, Z. Shi, J. Jin, A. Song, W. Zhao, L. Wen, and T. Zhang, "Fedada: Fast-convergent adaptive federated learning in heterogeneous mobile edge computing environment," *WWW*, vol. 25, no. 5, pp. 1971–1998, 2022.
- <span id="page-19-15"></span>[219] G. Wu and S. Gong, "Collaborative optimization and aggregation for decentralized domain generalization and adaptation," in *ICCV*, 2021, pp. 6484–6493.
- <span id="page-19-16"></span>[220] X. Pan, P. Luo, J. Shi, and X. Tang, "Two at once: Enhancing learning and generalization capacities via ibn-net," in *Proceedings of the European Conference on Computer Vision (ECCV)*, 2018, pp. 464–479.
- <span id="page-19-17"></span>[221] G. Li, Q. Zhang, P. Wang, J. Zhang, and C. Wu, "Federated domain adaptation via pseudo-label refinement," in *ICME*. IEEE, 2023, pp. 1829–1834.
- <span id="page-19-18"></span>[222] A. Madry, A. Makelov, L. Schmidt, D. Tsipras, and A. Vladu, "Towards deep learning models resistant to adversarial attacks," *arXiv preprint arXiv:1706.06083*, 2017.
- <span id="page-19-19"></span>[223] J. Yuan, X. Ma, D. Chen, F. Wu, L. Lin, and K. Kuang, "Collaborative semantic aggregation and calibration for federated domain generalization," *IEEE TKDE*, 2023.
- <span id="page-19-33"></span>[224] C. Guo, G. Pleiss, Y. Sun, and K. Q. Weinberger, "On calibration of modern neural networks," in *ICML*, 2017, pp. 1321–1330.
- <span id="page-19-34"></span>[225] B. Lakshminarayanan, A. Pritzel, and C. Blundell, "Simple and scalable predictive uncertainty estimation using deep ensembles," in *NeurIPS*, 2017.
- <span id="page-19-35"></span>[226] D. Amodei, C. Olah, J. Steinhardt, P. Christiano, J. Schulman, and D. Mané, "Concrete problems in ai safety," arXiv preprint *arXiv:1606.06565*, 2016.
- <span id="page-19-36"></span>[227] B. Lakshminarayanan, A. Pritzel, and C. Blundell, "Simple and scalable predictive uncertainty estimation using deep ensembles," in *NeurIPS*, vol. 30, 2017.
- <span id="page-19-37"></span>[228] Y. Ovadia, E. Fertig, J. Ren, Z. Nado, D. Sculley, S. Nowozin, J. Dillon, B. Lakshminarayanan, and J. Snoek, "Can you trust your model's uncertainty? evaluating predictive uncertainty under dataset shift," in *NeurIPS*, vol. 32, 2019.
- <span id="page-19-38"></span>[229] D. Peterson, P. Kanani, and V. J. Marathe, "Private federated learning with domain adaptation," in *NeurIPS*, 2019.
- <span id="page-19-39"></span>[230] Y. Wei, L. Yang, Y. Han, and Q. Hu, "Multi-source collaborative contrastive learning for decentralized domain adaptation," *IEEE Transactions on Circuits and Systems for Video Technology*, 2022.
- <span id="page-19-40"></span>[231] Y. Wei and Y. Han, "Exploring instance relation for decentralized multi-source domain adaptation," in *ICASSP*. IEEE, 2023, pp. 1–5.
- <span id="page-19-41"></span>[232] H. Feng, Z. You, M. Chen, T. Zhang, M. Zhu, F. Wu, C. Wu, and W. Chen, "Kd3a: Unsupervised multi-source decentralized domain adaptation via knowledge distillation." in *ICML*, 2021, pp. 3274–3283.
- <span id="page-19-42"></span>[233] Z. Niu, H. Wang, H. Sun, S. Ouyang, Y.-w. Chen, and L. Lin, "Mckd: Mutually collaborative knowledge distillation for federated domain adaptation and generalization," in *ICASSP*. IEEE, 2023, pp. 1–5.
- <span id="page-19-43"></span>[234] X. Liu, W. Xi, W. Li, D. Xu, G. Bai, and J. Zhao, "Co-mda: Federated multi-source domain adaptation on black-box models," *IEEE TCSVT*, 2023.
- <span id="page-19-44"></span>[235] M. H. Zhu, L. N. Ezzine, D. Liu, and Y. Bengio, "Fedilc: Weighted geometric mean and invariant gradient covariance for federated learning on non-iid data," *arXiv preprint arXiv:2205.09305*, 2022.
- <span id="page-19-45"></span>[236] L.-L. Zeng, Z. Fan, J. Su, M. Gan, L. Peng, H. Shen, and D. Hu, "Gradient matching federated domain adaptation for brain image classification," *IEEE TNNLS*, 2022.
- <span id="page-19-46"></span>[237] S. Nowlan and G. E. Hinton, "Evaluation of adaptive mixtures of competing experts," in *NeurIPS*, 1990.
- <span id="page-19-47"></span>[238] S. Masoudnia and R. Ebrahimpour, "Mixture of experts: a literature survey," *Artificial Intelligence Review*, vol. 42, pp. 275–293, 2014.
- <span id="page-19-48"></span>[239] A. T. Nguyen, P. Torr, and S. N. Lim, "Fedsr: A simple and effective domain generalization method for federated learning, in *NeurIPS*, vol. 35, 2022, pp. 38 831–38 843.
- <span id="page-19-49"></span>[240] L. Zhang, X. Lei, Y. Shi, H. Huang, and C. Chen, "Federated learning with domain generalization," *arXiv preprint arXiv:2111.10487*, 2021.
- <span id="page-19-50"></span>[241] - federated learning for iot devices with domain generalization," *IEEE IoT-J*, 2023.
- <span id="page-19-51"></span>[242] X. Huang and S. Belongie, "Arbitrary style transfer in real-time with adaptive instance normalization," in *CVPR*, 2017, pp. 1501– 1510.

- <span id="page-20-0"></span>[243] R. Zhang, Q. Xu, J. Yao, Y. Zhang, Q. Tian, and Y. Wang, "Federated domain generalization with generalization adjustment," in *CVPR*, June 2023, pp. 3954–3963.
- <span id="page-20-1"></span>[244] J. h. Duan, W. Li, D. Zou, R. Li, and S. Lu, "Federated learning with data-agnostic distribution fusion," in *CVPR*, 2023.
- <span id="page-20-2"></span>[245] D. Yin, Y. Chen, R. Kannan, and P. Bartlett, "Byzantine-robust distributed learning: Towards optimal statistical rates," in *ICML*, 2018, pp. 5650–5659.
- <span id="page-20-3"></span>[246] K. Pillutla, S. M. Kakade, and Z. Harchaoui, "Robust aggregation for federated learning," *IEEE TSP*, vol. 70, pp. 1142–1154, 2022.
- <span id="page-20-4"></span>[247] X. Cao, M. Fang, J. Liu, and N. Z. Gong, "Fltrust: Byzantinerobust federated learning via trust bootstrapping," in *NDSS*, 2021.
- <span id="page-20-5"></span>[248] J. Park, D.-J. Han, M. Choi, and J. Moon, "Sageflow: Robust federated learning against both stragglers and adversaries," in *NeurIPS*, 2021, pp. 840–851.
- <span id="page-20-6"></span>[249] Q. Xia, Z. Tao, Z. Hao, and Q. Li, "Faba: an algorithm for fast aggregation against byzantine attacks in distributed neural networks," in *IJCAI*, 2019.
- <span id="page-20-7"></span>[250] W. Wan, S. Hu, J. Lu, L. Y. Zhang, H. Jin, and Y. He, "Shielding federated learning: Robust aggregation with adaptive client selection," in *IJCAI*, 2022.
- <span id="page-20-8"></span>[251] C. P. Wan and Q. Chen, "Robust federated learning with attackadaptive aggregation," in *IJCAI Workshop*, 2021.
- <span id="page-20-9"></span>[252] C. Wu, X. Yang, S. Zhu, and P. Mitra, "Mitigating backdoor attacks in federated learning," *arXiv preprint arXiv:2011.01767*, 2020.
- <span id="page-20-10"></span>[253] S. P. Sturluson, S. Trew, L. Muñoz-González, M. Grama, J. Passerat-Palmbach, D. Rueckert, and A. Alansary, "Fedrad: Federated robust adaptive distillation," *arXiv preprint arXiv:2112.01405*, 2021.
- <span id="page-20-11"></span>[254] M. S. Ozdayi, M. Kantarcioglu, and Y. R. Gel, "Defending against backdoors in federated learning with robust learning rate," in *AAAI*, 2021, pp. 9268–9276.
- <span id="page-20-12"></span>[255] Z. Zhang, Q. Su, and X. Sun, "Dim-krum: Backdoor-resistant federated learning for nlp with dimension-wise krum-based aggregation," in *EMNLP*, 2022.
- <span id="page-20-13"></span>[256] L. Qu, Y. Zhou, P. P. Liang, Y. Xia, F. Wang, E. Adeli, L. Fei-Fei, and D. Rubin, "Rethinking architecture design for tackling data heterogeneity in federated learning," in *CVPR*, 2022, pp. 10 061– 10 071.
- <span id="page-20-14"></span>[257] C. Xiang, A. N. Bhagoji, V. Sehwag, and P. Mittal, "{PatchGuard}: A provably robust defense against adversarial patches via small receptive fields and masking," in *USENIX*, 2021, pp. 2237–2254.
- <span id="page-20-15"></span>[258] J. Cohen, E. Rosenfeld, and Z. Kolter, "Certified adversarial robustness via randomized smoothing," in *ICML*. PMLR, 2019, pp. 1310–1320.
- <span id="page-20-16"></span>[259] A. Levine and S. Feizi, "Deep partition aggregation: Provable defense against general poisoning attacks," in *ICLR*, 2021.
- <span id="page-20-17"></span>[260] C. Xie, M. Chen, P.-Y. Chen, and B. Li, "Crfl: Certifiably robust federated learning against backdoor attacks," in *ICML*. PMLR, 2021, pp. 11 372–11 382.
- <span id="page-20-18"></span>[261] X. Cao, J. Jia, and N. Z. Gong, "Provably secure federated learning against malicious clients," in *AAAI*, 2021, pp. 6885–6893.
- <span id="page-20-19"></span>[262] A. Panda, S. Mahloujifar, A. N. Bhagoji, S. Chakraborty, and P. Mittal, "Sparsefed: Mitigating model poisoning attacks in federated learning with sparsification," in *ICML*. PMLR, 2022, pp. 7587–7624.
- <span id="page-20-20"></span>[263] K. Zhang, G. Tao, Q. Xu, S. Cheng, S. An, Y. Liu, S. Feng, G. Shen, P.-Y. Chen, S. Ma *et al.*, "Flip: A provable defense framework for backdoor mitigation in federated learning," in *ICLR*, 2023.
- <span id="page-20-21"></span>[264] H. Giroux, "Utopian thinking under the sign of neoliberalism: Towards a critical pedagogy of educated hope," *Democracy & Nature*, vol. 9, no. 1, pp. 91–105, 2003.
- <span id="page-20-22"></span>[265] V. Smith, C.-K. Chiang, M. Sanjabi, and A. S. Talwalkar, "Federated multi-task learning," in *NeurIPS*, 2017.
- <span id="page-20-23"></span>[266] A. Li, J. Sun, B. Wang, L. Duan, S. Li, Y. Chen, and H. Li, "Lotteryfl: Personalized and communication-efficient federated learning with lottery ticket hypothesis on non-iid datasets," *arXiv preprint arXiv:2008.03371*, 2020.
- <span id="page-20-24"></span>[267] O. Marfoq, C. Xu, G. Neglia, and R. Vidal, "Throughput-optimal topology design for cross-silo federated learning," in *NeurIPS*, 2020, pp. 19 478–19 487.
- <span id="page-20-25"></span>[268] N. Hyeon-Woo, M. Ye-Bin, and T.-H. Oh, "Fedpara: Low-rank hadamard product for communication-efficient federated learning," in *ICLR*, 2022.

- <span id="page-20-26"></span>[269] Z. Wu, Y. Shu, and B. K. H. Low, "Davinz: Data valuation using deep neural networks at initialization," in *ICML*. PMLR, 2022, pp. 24 150–24 176.
- <span id="page-20-27"></span>[270] J. Xu, N. Hong, Z. Xu, Z. Zhao, C. Wu, K. Kuang, J. Wang, M. Zhu, J. Zhou, K. Ren *et al.*, "Data-driven learning for data rights, data pricing, and privacy computing," *Engineering*, 2023.
- <span id="page-20-28"></span>[271] X. Xu, L. Lyu, X. Ma, C. Miao, C. S. Foo, and B. K. H. Low, "Gradient driven rewards to guarantee fairness in collaborative machine learning," in *NeurIPS*, vol. 34, 2021, pp. 16 104–16 117.
- <span id="page-20-29"></span>[272] Z. Hu, K. Shaloudegi, G. Zhang, and Y. Yu, "Fedmgda+: Federated learning meets multi-objective optimization. corr abs/2006.11489 (2020)," *IEEE TNSE*, 2020.
- <span id="page-20-30"></span>[273] L. Lyu, Y. Li, K. Nandakumar, J. Yu, and X. Ma, "How to democratise and protect ai: Fair and differentially private decentralised deep learning," *IEEE TDSC*, vol. 19, no. 2, pp. 1003–1017, 2020.
- <span id="page-20-31"></span>[274] J. Zhang, C. Li, A. Robles-Kelly, and M. Kankanhalli, "Hierarchically fair federated learning," *arXiv*, 2020.
- <span id="page-20-32"></span>[275] J. Kang, Z. Xiong, D. Niyato, H. Yu, Y. Liang, and D. I. Kim, "Incentive design for efficient federated learning in mobile networks: A contract theory approach," in *APWCS*, 2019, pp. 1–5.
- <span id="page-20-33"></span>[276] D. Ye, R. Yu, M. Pan, and Z. Han, "Federated learning in vehicular edge computing: A selective model aggregation approach," *IEEE Access*, vol. 8, pp. 23 920–23 935, 2020.
- <span id="page-20-34"></span>[277] M. Simaan and J. B. J. Cruz, "On the stackelberg strategy in nonzero-sum games," *JOTA*, vol. 11, pp. 533–555, 1973.
- <span id="page-20-35"></span>[278] Y. Sarikaya and O. Ercetin, "Motivating workers in federated learning: A stackelberg game perspective," *IEEE Networking Letters*, vol. 2, no. 1, pp. 23–27, 2020.
- <span id="page-20-36"></span>[279] T. H. T. Le, N. H. Tran, Y. K. Tun, M. N. Nguyen, S. R. Pandey, Z. Han, and C. S. Hong, "An incentive mechanism for federated learning in wireless cellular networks: An auction approach," *IEEE TWC*, vol. 20, no. 8, pp. 4874–4887, 2021.
- <span id="page-20-37"></span>[280] X. Xu and L. Lyu, "A reputation mechanism is all you need: Collaborative fairness and adversarial robustness in federated learning," in *ICML Workshop*, 2021.
- <span id="page-20-38"></span>[281] J. Kang, Z. Xiong, D. Niyato, Y. Zou, Y. Zhang, and M. Guizani, "Reliable federated learning for mobile networks," *IEEE WC*, vol. 27, no. 2, pp. 72–80, 2020.
- <span id="page-20-39"></span>[282] Z. Song, H. Sun, H. H. Yang, X. Wang, Y. Zhang, and T. Q. S. Quek, "Reputation-based federated learning for secure wireless networks," *IEEE IoT-J*, vol. 9, no. 2, pp. 1212–1226, 2022.
- <span id="page-20-40"></span>[283] G. Wang, C. X. Dang, and Z. Zhou, "Measure contribution of participants in federated learning," in *IEEE Big Data*, 2019, pp. 2597–2604.
- <span id="page-20-41"></span>[284] S. Wei, Y. Tong *et al.*, "Efficient and fair data valuation for horizontal federated learning," in *Federated Learning*. Springer, 2020, pp. 139–152.
- <span id="page-20-42"></span>[285] Z. Liu, Y. Chen, H. Yu *et al.*, "Gtg-shapley: Efficient and accurate participant contribution evaluation in federated learning," *ACM TIST*, vol. 13, no. 4, pp. 1–21, 2022.
- <span id="page-20-43"></span>[286] Y. Yu, S. P. Karimireddy, Y. Ma, and M. I. Jordan, "Scaff-pd: Communication efficient fair and robust federated learning," *arXiv preprint arXiv:2307.13381*, 2023.
- <span id="page-20-44"></span>[287] X. Yue, M. Nouiehed, and R. Al Kontar, "Gifair-fl: A framework for group and individual fairness in federated learning," *INFORMS Journal on Data Science*, vol. 2, no. 1, pp. 10–23, 2023.
- <span id="page-20-45"></span>[288] Y.-Y. Xu, C.-S. Lin, and Y.-C. F. Wang, "Bias-eliminating augmentation learning for debiased federated learning," in *CVPR*, 2023, pp. 20 442–20 452.
- <span id="page-20-46"></span>[289] Z. Pan, S. Wang, C. Li, H. Wang, X. Tang, and J. Zhao, "Fedmdfg: Federated learning with multi-gradient descent and fair guidance," in *AAAI*, 2023, pp. 9364–9371.
- <span id="page-20-47"></span>[290] T. Li, A. Beirami, M. Sanjabi, and V. Smith, "Tilted empirical risk minimization," in *ICLR*, 2021.
- <span id="page-20-48"></span>[291] A. Krizhevsky and G. Hinton, "Learning multiple layers of features from tiny images," *Master's thesis, Department of Computer Science, University of Toronto*, 2009.
- <span id="page-20-49"></span>[292] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: A novel image dataset for benchmarking machine learning algorithms," *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-20-50"></span>[293] B. Gong, Y. Shi, F. Sha, and K. Grauman, "Geodesic flow kernel for unsupervised domain adaptation," in *CVPR*, 2012, pp. 2066– 2073.
- <span id="page-20-51"></span>[294] G. Griffin, A. Holub, and P. Perona, "Caltech-256 object category dataset," 2007.
- <span id="page-20-52"></span>[295] J. J. Hull, "A database for handwritten text recognition research," *IEEE PAMI*, pp. 550–554, 1994.

- <span id="page-21-0"></span>[296] P. Roy, S. Ghosh, S. Bhattacharya, and U. Pal, "Effects of degradations on deep neural network architectures," *arXiv preprint arXiv:1807.10108*, 2018.
- <span id="page-21-1"></span>[297] K. Saenko, B. Kulis, M. Fritz, and T. Darrell, "Adapting visual category models to new domains," in *ECCV*, 2010, pp. 213–226.
- <span id="page-21-2"></span>[298] D. Li, Y. Yang, Y.-Z. Song, and T. M. Hospedales, "Deeper, broader and artier domain generalization," in *ICCV*, 2017, pp. 5542–5550.
- <span id="page-21-3"></span>[299] H. Robbins and S. Monro, "A stochastic approximation method," *AoMS*, pp. 400–407, 1951.
- <span id="page-21-4"></span>[300] K. Zhu, X. Hu, J. Wang, X. Xie, and G. Yang, "Improving generalization of adversarial training via robust critical fine-tuning," in *ICCV*, 2023.
- <span id="page-21-5"></span>[301] B. Li, L. Fan, H. Gu, J. Li, and Q. Yang, "Fedipr: Ownership verification for federated deep neural network models," *IEEE PAMI*, vol. 45, no. 4, pp. 4521–4536, 2022.
- <span id="page-21-6"></span>[302] D. Cha, M. Sung, Y.-R. Park *et al.*, "Implementing vertical federated learning using autoencoders: Practical application, generalizability, and utility study," *JMIR Med Info*, vol. 9, no. 6, p. e26598, 2021.
- <span id="page-21-7"></span>[303] Z. Wu, Q. Li, and B. He, "Practical vertical federated learning with unsupervised representation learning," *IEE TBD*, 2022.
- <span id="page-21-8"></span>[304] T. Zou, Y. Liu, Y. Kang, W. Liu, Y. He, Z. Yi, Q. Yang, and Y.-Q. Zhang, "Defending batch-level label inference and replacement attacks in vertical federated learning," *IEE TBD*, 2022.
- <span id="page-21-9"></span>[305] C. Fu, X. Zhang, S. Ji, J. Chen, J. Wu, S. Guo, J. Zhou, A. X. Liu, and T. Wang, "Label inference attacks against vertical federated learning," in *USENIX*, 2022, pp. 1397–1414.
- <span id="page-21-10"></span>[306] X. Luo, Y. Wu, X. Xiao, and B. C. Ooi, "Feature inference attack on model predictions in vertical federated learning," in *ICDE*. IEEE, 2021, pp. 181–192.
- <span id="page-21-11"></span>[307] X. Jin, P.-Y. Chen, C.-Y. Hsu, C.-M. Yu, and T. Chen, "Cafe: Catastrophic data leakage in vertical federated learning," in *NeurIPS*, vol. 34, 2021, pp. 994–1006.
- <span id="page-21-12"></span>[308] C. Liu, Z. Fan, Z. Zhou, Y. Shi, J. Pei, L. Chu, and Y. Zhang, "Achieving model fairness in vertical federated learning," *arXiv preprint arXiv:2109.08344*, 2021.
- <span id="page-21-13"></span>[309] T. Qi, F. Wu, C. Wu, L. Lyu, T. Xu, H. Liao, Z. Yang, Y. Huang, and X. Xie, "Fairvfl: A fair vertical federated learning framework with contrastive adversarial learning," in *NeurIPS*, vol. 35, 2022, pp. 7852–7865.
- <span id="page-21-14"></span>[310] J. Jiang, L. Burkhalter, F. Fu, B. Ding, B. Du, A. Hithnawi, B. Li, and C. Zhang, "Vf-ps: How to select important participants in vertical federated learning, efficiently and securely?" in *NeurIPS*, vol. 35, 2022, pp. 2088–2101.
- <span id="page-21-15"></span>[311] T. Castiglia, Y. Zhou, S. Wang, S. Kadhe, N. Baracaldo, and S. Patterson, "Less-vfl: Communication-efficient feature selection for vertical federated learning," in *ICML*, 2023.
- <span id="page-21-16"></span>[312] A. Radford, J. Wu, R. Child, D. Luan, D. Amodei, I. Sutskever *et al.*, "Language models are unsupervised multitask learners," *OpenAI blog*, vol. 1, no. 8, p. 9, 2019.
- <span id="page-21-17"></span>[313] T. Brown, B. Mann, N. Ryder, M. Subbiah, J. D. Kaplan, P. Dhariwal, A. Neelakantan, P. Shyam, G. Sastry, A. Askell *et al.*, "Language models are few-shot learners," in *NeurIPS*, 2020, pp. 1877– 1901.
- <span id="page-21-18"></span>[314] OpenAI, "Gpt-4 technical report," *arXiv preprint arXiv:2303.08774*, 2023.
- <span id="page-21-19"></span>[315] A. Chowdhery, S. Narang, J. Devlin, M. Bosma, G. Mishra, A. Roberts, P. Barham, H. W. Chung, C. Sutton, S. Gehrmann *et al.*, "Palm: Scaling language modeling with pathways," *arXiv preprint arXiv:2204.02311*, 2022.
- <span id="page-21-20"></span>[316] R. Anil, A. M. Dai, O. Firat, M. Johnson, D. Lepikhin, A. Passos, S. Shakeri, E. Taropa, P. Bailey, Z. Chen *et al.*, "Palm 2 technical report," *arXiv preprint arXiv:2305.10403*, 2023.
- <span id="page-21-21"></span>[317] W. Zhuang, C. Chen, and L. Lyu, "When foundation model meets federated learning: Motivations, challenges, and future directions," *arXiv preprint arXiv:2306.15546*, 2023.