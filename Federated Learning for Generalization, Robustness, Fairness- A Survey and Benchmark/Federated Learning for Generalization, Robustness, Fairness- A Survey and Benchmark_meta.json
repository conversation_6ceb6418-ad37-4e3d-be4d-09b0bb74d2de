{"table_of_contents": [{"title": "Federated Learning for Generalization,\nRobustness, Fairness: A Survey and Benchmark", "heading_level": null, "page_id": 0, "polygon": [[47.25, 55.5], [565.5, 55.5], [565.5, 106.734375], [47.25, 106.734375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[47.25, 299.25], [141.046875, 299.25], [141.046875, 310.728515625], [47.25, 310.728515625]]}, {"title": "1.1 Prior Surveys", "heading_level": null, "page_id": 1, "polygon": [[46.5, 710.25], [132.75, 710.25], [132.75, 720.0703125], [46.5, 720.0703125]]}, {"title": "1.2 Contribution", "heading_level": null, "page_id": 1, "polygon": [[311.25, 512.25], [391.5, 512.25], [391.5, 522.45703125], [311.25, 522.45703125]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 2, "polygon": [[47.14013671875, 532.5], [136.5, 532.5], [136.5, 542.953125], [47.14013671875, 542.953125]]}, {"title": "2.1 History and Terminology", "heading_level": null, "page_id": 2, "polygon": [[46.5, 549.0], [182.25, 549.0], [182.25, 558.421875], [46.5, 558.421875]]}, {"title": "2.2 Problem Definition", "heading_level": null, "page_id": 3, "polygon": [[46.5, 84.75], [155.25, 84.75], [155.25, 94.40771484375], [46.5, 94.40771484375]]}, {"title": "2.2.1 Distributed Shift in FL", "heading_level": null, "page_id": 3, "polygon": [[46.5, 432.73828125], [170.25, 432.73828125], [170.25, 442.01953125], [46.5, 442.01953125]]}, {"title": "2.2.2 Malicious Behavior in Federated Learning", "heading_level": null, "page_id": 3, "polygon": [[311.25, 261.75], [518.25, 261.75], [518.25, 271.283203125], [311.25, 271.283203125]]}, {"title": "2.2.3 Interest Conflict in Federated Learning", "heading_level": null, "page_id": 4, "polygon": [[311.25, 79.5], [505.5, 79.5], [505.5, 89.28369140625], [311.25, 89.28369140625]]}, {"title": "3 GENERALIZABLE FEDERATED LEARNING", "heading_level": null, "page_id": 5, "polygon": [[47.25, 42.75], [266.25, 43.5], [266.25, 53.31884765625], [47.25, 53.31884765625]]}, {"title": "3.1 Generalization Metrics", "heading_level": null, "page_id": 5, "polygon": [[45.75, 57.75], [173.25, 57.75], [173.25, 67.1923828125], [45.75, 67.1923828125]]}, {"title": "3.2 Cross Calibration", "heading_level": null, "page_id": 5, "polygon": [[46.5, 433.5], [150.0, 433.5], [150.0, 443.1796875], [46.5, 443.1796875]]}, {"title": "3.2.1 Client Regularization", "heading_level": null, "page_id": 5, "polygon": [[47.25, 538.5], [167.25, 538.5], [167.25, 547.59375], [47.25, 547.59375]]}, {"title": "3.2.2 Client Augmentation", "heading_level": null, "page_id": 6, "polygon": [[47.25, 180.75], [165.75, 180.75], [165.75, 190.458984375], [47.25, 190.458984375]]}, {"title": "3.2.3 Server Operation", "heading_level": null, "page_id": 6, "polygon": [[47.25, 537.75], [152.25, 537.75], [152.25, 547.59375], [47.25, 547.59375]]}, {"title": "3.3 Unknown Generalization", "heading_level": null, "page_id": 6, "polygon": [[311.080078125, 373.5], [446.25, 373.5], [446.25, 383.431640625], [311.080078125, 383.431640625]]}, {"title": "3.3.1 Federated Domain Adaptation", "heading_level": null, "page_id": 6, "polygon": [[311.25, 561.0], [471.0, 561.0], [471.0, 571.18359375], [311.25, 571.18359375]]}, {"title": "3.3.2 Federated Domain Generalization", "heading_level": null, "page_id": 7, "polygon": [[47.25, 219.0], [221.25, 219.0], [221.25, 227.970703125], [47.25, 227.970703125]]}, {"title": "4 ROBUST FEDERATED LEARNING", "heading_level": null, "page_id": 7, "polygon": [[47.25, 599.25], [224.419921875, 599.25], [224.419921875, 609.85546875], [47.25, 609.85546875]]}, {"title": "4.1 Robustness Metrics", "heading_level": null, "page_id": 7, "polygon": [[46.5, 616.5], [161.25, 616.5], [161.25, 626.484375], [46.5, 626.484375]]}, {"title": "4.2 Byzantine Tolerance", "heading_level": null, "page_id": 7, "polygon": [[309.884765625, 436.9921875], [427.025390625, 436.9921875], [427.025390625, 446.2734375], [309.884765625, 446.2734375]]}, {"title": "4.2.1 Distance Base Tolerance", "heading_level": null, "page_id": 7, "polygon": [[311.25, 499.25390625], [448.5, 499.25390625], [448.5, 508.53515625], [311.25, 508.53515625]]}, {"title": "4.2.2 Statistics Distribution Tolerance", "heading_level": null, "page_id": 7, "polygon": [[311.25, 687.5859375], [475.5, 686.25], [475.5, 696.8671875], [311.25, 696.8671875]]}, {"title": "4.2.3 Proxy Dataset Tolerance", "heading_level": null, "page_id": 8, "polygon": [[47.25, 192.75], [183.0, 192.75], [183.0, 202.640625], [47.25, 202.640625]]}, {"title": "4.3 Backdoor Defense", "heading_level": null, "page_id": 8, "polygon": [[46.5, 406.5], [154.5, 406.5], [154.5, 416.49609375], [46.5, 416.49609375]]}, {"title": "4.3.1 Model Refinement Defense", "heading_level": null, "page_id": 8, "polygon": [[47.25, 501.75], [194.25, 501.75], [194.25, 511.2421875], [47.25, 511.2421875]]}, {"title": "4.3.2 Robust Aggregation Defense", "heading_level": null, "page_id": 8, "polygon": [[47.25, 664.5], [201.75, 664.5], [201.75, 675.2109375], [47.25, 675.2109375]]}, {"title": "4.3.3 Certified Robustness Defense", "heading_level": null, "page_id": 8, "polygon": [[311.25, 186.0], [469.5, 186.0], [469.5, 195.29296875], [311.25, 195.29296875]]}, {"title": "5 FAIR FEDERATED LEARNING", "heading_level": null, "page_id": 8, "polygon": [[311.25, 411.0], [469.5, 411.0], [469.5, 421.13671875], [311.25, 421.13671875]]}, {"title": "5.1 Fairness Metrics", "heading_level": null, "page_id": 8, "polygon": [[311.25, 430.5], [410.25, 430.5], [410.25, 440.0859375], [311.25, 440.0859375]]}, {"title": "5.2 Collaboration Fairness", "heading_level": null, "page_id": 9, "polygon": [[46.5, 297.0], [173.25, 297.0], [173.25, 307.248046875], [46.5, 307.248046875]]}, {"title": "5.2.1 Individual Contribution Evaluation", "heading_level": null, "page_id": 9, "polygon": [[47.25, 412.5], [221.25, 412.5], [221.25, 422.68359375], [47.25, 422.68359375]]}, {"title": "5.2.2 Marginal Contribution Evaluation", "heading_level": null, "page_id": 9, "polygon": [[47.25, 711.0], [216.75, 711.0], [216.75, 721.6171875], [47.25, 721.6171875]]}, {"title": "5.3 Performance Fairness", "heading_level": null, "page_id": 9, "polygon": [[310.5, 232.5], [434.25, 232.5], [434.25, 242.47265625], [310.5, 242.47265625]]}, {"title": "5.3.1 Performance Debias Optimization", "heading_level": null, "page_id": 9, "polygon": [[311.25, 339.75], [486.0, 339.75], [486.0, 349.400390625], [311.25, 349.400390625]]}, {"title": "5.3.2 <PERSON> <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 9, "polygon": [[311.25, 607.5], [479.25, 607.5], [479.25, 617.203125], [311.25, 617.203125]]}, {"title": "6 SETUP", "heading_level": null, "page_id": 10, "polygon": [[47.25, 96.0], [99.75, 96.0], [99.75, 107.4111328125], [47.25, 107.4111328125]]}, {"title": "6.1 Experimental Datasets", "heading_level": null, "page_id": 10, "polygon": [[46.5, 111.75], [173.25, 111.75], [173.25, 122.396484375], [46.5, 122.396484375]]}, {"title": "6.1.1 Label Skew Datasets", "heading_level": null, "page_id": 10, "polygon": [[47.25, 162.75], [168.75, 162.75], [168.75, 172.669921875], [47.25, 172.669921875]]}, {"title": "6.1.2 Domain Skew & Out-Client Shift Datasets", "heading_level": null, "page_id": 10, "polygon": [[46.5, 384.0], [255.0, 384.0], [255.0, 393.873046875], [46.5, 393.873046875]]}, {"title": "6.1.3 Data Augmentation", "heading_level": null, "page_id": 10, "polygon": [[46.5, 687.0], [161.25, 687.0], [161.25, 696.75], [46.5, 696.75]]}, {"title": "6.2 Implementation Details", "heading_level": null, "page_id": 10, "polygon": [[311.25, 697.5], [439.5, 697.5], [439.5, 707.25], [311.25, 707.25]]}, {"title": "7 BENCHMARK", "heading_level": null, "page_id": 11, "polygon": [[311.25, 488.25], [393.75, 488.25], [393.75, 499.25390625], [311.25, 499.25390625]]}, {"title": "7.1 Generalization Benchmark", "heading_level": null, "page_id": 11, "polygon": [[311.25, 624.0], [455.25, 624.0], [455.25, 633.4453125], [311.25, 633.4453125]]}, {"title": "7.1.1 Evaluation Metrics", "heading_level": null, "page_id": 11, "polygon": [[311.080078125, 639.0], [422.244140625, 639.0], [422.244140625, 648.9140625], [311.080078125, 648.9140625]]}, {"title": "7.1.2 Results", "heading_level": null, "page_id": 11, "polygon": [[311.25, 710.25], [375.75, 710.25], [375.75, 721.23046875], [311.25, 721.23046875]]}, {"title": "7.2 Robustness Benchmark", "heading_level": null, "page_id": 12, "polygon": [[310.482421875, 662.8359375], [444.0, 662.8359375], [444.0, 672.1171875], [310.482421875, 672.1171875]]}, {"title": "7.2.1 Evaluation Metrics", "heading_level": null, "page_id": 12, "polygon": [[311.080078125, 676.5], [420.75, 676.5], [420.75, 686.42578125], [311.080078125, 686.42578125]]}, {"title": "FEDERATED LEARNING FOR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ROBUSTNE<PERSON>, FAIRNESS: A SURVEY AND B<PERSON>CHMARK 14", "heading_level": null, "page_id": 13, "polygon": [[46.5, 26.25], [564.0, 26.25], [564.0, 35.360595703125], [46.5, 35.360595703125]]}, {"title": "7.2.2 Results", "heading_level": null, "page_id": 13, "polygon": [[47.25, 431.25], [111.0, 432.75], [111.0, 442.40625], [47.25, 442.40625]]}, {"title": "7.3 Fairness Benchmark", "heading_level": null, "page_id": 13, "polygon": [[46.5, 674.25], [163.5, 674.25], [163.5, 684.4921875], [46.5, 684.4921875]]}, {"title": "7.3.1 Evaluation Metrics", "heading_level": null, "page_id": 13, "polygon": [[47.849853515625, 694.5], [157.5, 694.5], [157.5, 704.6015625], [47.849853515625, 704.6015625]]}, {"title": "7.3.2 Results", "heading_level": null, "page_id": 13, "polygon": [[311.25, 432.0], [375.92578125, 432.0], [375.92578125, 442.01953125], [311.25, 442.01953125]]}, {"title": "7.4 Discussion", "heading_level": null, "page_id": 13, "polygon": [[311.25, 582.0], [385.5, 582.0], [385.5, 591.29296875], [311.25, 591.29296875]]}, {"title": "8 OUTLOOK", "heading_level": null, "page_id": 14, "polygon": [[46.5, 425.25], [116.25, 425.25], [116.25, 435.83203125], [46.5, 435.83203125]]}, {"title": "8.1 Future Direction", "heading_level": null, "page_id": 14, "polygon": [[45.75, 443.25], [145.0810546875, 443.25], [145.0810546875, 452.84765625], [45.75, 452.84765625]]}, {"title": "8.2 Conclusion", "heading_level": null, "page_id": 15, "polygon": [[45.75, 312.0], [123.0, 312.0], [123.0, 321.556640625], [45.75, 321.556640625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 15, "polygon": [[47.25, 487.5], [116.25, 487.5], [116.25, 497.70703125], [47.25, 497.70703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 91], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["Footnote", 2], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12208, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["Line", 120], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["Figure", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 147], ["Reference", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1186, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 987], ["Line", 183], ["Reference", 9], ["Text", 8], ["TextInlineMath", 7], ["Equation", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7855, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 154], ["Text", 7], ["Equation", 6], ["ListItem", 5], ["TextInlineMath", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 673], ["TableCell", 303], ["Line", 134], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["Line", 118], ["TableCell", 71], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8319, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 120], ["TableCell", 72], ["Text", 9], ["Reference", 8], ["SectionHeader", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1677, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 382], ["Line", 113], ["SectionHeader", 7], ["TextInlineMath", 5], ["Text", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 135], ["Text", 6], ["SectionHeader", 6], ["TextInlineMath", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1113, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 774], ["Line", 120], ["TableCell", 110], ["Text", 12], ["Reference", 7], ["SectionHeader", 6], ["TextInlineMath", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 631], ["TableCell", 192], ["Line", 143], ["Text", 6], ["SectionHeader", 4], ["Figure", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["FigureGroup", 2], ["Table", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5574, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1111], ["TableCell", 1094], ["Line", 73], ["Text", 8], ["Table", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 17157, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1632], ["TableCell", 1020], ["Line", 111], ["Text", 7], ["SectionHeader", 6], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2989, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["TableCell", 353], ["Line", 103], ["Text", 10], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 142], ["Reference", 32], ["ListItem", 31], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 565], ["Line", 153], ["ListItem", 53], ["Reference", 53], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 152], ["ListItem", 52], ["Reference", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 577], ["Line", 154], ["ListItem", 54], ["Reference", 54], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 154], ["ListItem", 52], ["Reference", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 154], ["ListItem", 53], ["Reference", 53], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 67], ["ListItem", 22], ["Reference", 22], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Federated Learning for Generalization, Robustness, Fairness- A Survey and Benchmark"}