{"table_of_contents": [{"title": "Federated Learning via Decentralized Dataset\nDistillation in Resource-Constrained Edge\nEnvironments", "heading_level": null, "page_id": 0, "polygon": [[71.298828125, 53.23419412288513], [523.828125, 53.23419412288513], [523.828125, 130.53466796875], [71.298828125, 130.53466796875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[125.71875, 516.5966162065895], [206.42317380352642, 516.5966162065895], [206.42317380352642, 525.427734375], [125.71875, 525.427734375]]}, {"title": "II. <PERSON><PERSON><PERSON><PERSON>OUND AND RELATED WORK", "heading_level": null, "page_id": 2, "polygon": [[78.816120906801, 152.95458593054317], [253.0380859375, 152.95458593054317], [253.0380859375, 163.01416015625], [78.816120906801, 163.01416015625]]}, {"title": "III. FEDD3: FEDERATED LEARNING FROM\nDECENTRALIZED DISTILLED DATASETS", "heading_level": null, "page_id": 2, "polygon": [[335.541015625, 609.5690115761354], [520.9370277078085, 609.5690115761354], [520.9370277078085, 630.2666015625], [335.541015625, 630.2666015625]]}, {"title": "A. Coreset-based Methods", "heading_level": null, "page_id": 3, "polygon": [[39.032745591939545, 573.5796972395369], [150.74609375, 572.8299198575245], [150.74609375, 584.2197265625], [39.032745591939545, 584.2197265625]]}, {"title": "B. KIP-based Methods", "heading_level": null, "page_id": 3, "polygon": [[302.947265625, 50.984861976847725], [397.8337531486146, 50.984861976847725], [397.8337531486146, 61.567138671875], [302.947265625, 61.567138671875]]}, {"title": "C. Aggregation and Learning", "heading_level": null, "page_id": 3, "polygon": [[303.2544080604534, 232.43098842386465], [425.6070528967254, 232.43098842386465], [425.6070528967254, 243.18505859375], [303.2544080604534, 243.18505859375]]}, {"title": "D. Gamma Communication Efficiency", "heading_level": null, "page_id": 3, "polygon": [[302.50377833753146, 430.37221727515583], [460.88664987405537, 430.37221727515583], [460.88664987405537, 440.3232421875], [302.50377833753146, 440.3232421875]]}, {"title": "IV. EXPERIMENT", "heading_level": null, "page_id": 4, "polygon": [[127.60705289672543, 220.43455031166516], [204.7294921875, 220.43455031166516], [204.7294921875, 230.43994140625], [127.60705289672543, 230.43994140625]]}, {"title": "<PERSON><PERSON> Settings", "heading_level": null, "page_id": 4, "polygon": [[39.032745591939545, 236.92965271593943], [143.54345703125, 236.92965271593943], [143.54345703125, 246.6796875], [39.032745591939545, 246.6796875]]}, {"title": "<PERSON><PERSON> Training on Heterogeneous Data", "heading_level": null, "page_id": 4, "polygon": [[39.032745591939545, 524.8441674087265], [220.4443359375, 524.8441674087265], [220.4443359375, 534.5912733748887], [39.032745591939545, 534.5912733748887]]}, {"title": "C. <PERSON>alable Communication Efficiency", "heading_level": null, "page_id": 4, "polygon": [[303.2544080604534, 166.45057880676757], [463.13853904282115, 166.45057880676757], [463.13853904282115, 176.787109375], [303.2544080604534, 176.787109375]]}, {"title": "D. Evaluation with System Parameters", "heading_level": null, "page_id": 4, "polygon": [[302.50377833753146, 536.840605520926], [463.8891687657431, 536.840605520926], [463.8891687657431, 546.5877114870881], [302.50377833753146, 546.5877114870881]]}, {"title": "V. DISCUSSION", "heading_level": null, "page_id": 5, "polygon": [[394.8312342569269, 641.059661620659], [463.8891687657431, 641.059661620659], [463.8891687657431, 651.6455078125], [394.8312342569269, 651.6455078125]]}, {"title": "TABLE III", "heading_level": null, "page_id": 6, "polygon": [[141.36083984375, 52.48441674087266], [188.4326171875, 52.48441674087266], [188.4326171875, 61.2073974609375], [141.36083984375, 61.2073974609375]]}, {"title": "VI. CONCLUSION", "heading_level": null, "page_id": 6, "polygon": [[387.923828125, 497.8521816562778], [469.990234375, 497.8521816562778], [469.990234375, 507.337890625], [387.923828125, 507.337890625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 6, "polygon": [[399.85546875, 686.0463045414069], [458.63476070528964, 686.0463045414069], [458.63476070528964, 694.814453125], [399.85546875, 694.814453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 88], ["Text", 9], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3909, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 85], ["Text", 3], ["ListItem", 3], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 536], ["Line", 112], ["Text", 11], ["TextInlineMath", 4], ["SectionHeader", 2], ["Equation", 2], ["ListItem", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1211], ["Line", 123], ["TextInlineMath", 9], ["SectionHeader", 4], ["Equation", 3], ["Caption", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 440], ["Line", 109], ["TableCell", 16], ["Text", 11], ["SectionHeader", 5], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 526], ["TableCell", 158], ["Line", 150], ["Text", 5], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 6024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 576], ["Line", 129], ["TableCell", 80], ["Text", 9], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["ListItem", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4378, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Line", 211], ["Span", 160], ["ListItem", 9], ["Reference", 6], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 512], ["Line", 147], ["ListItem", 40], ["Reference", 25], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 39], ["ListItem", 12], ["Reference", 12], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Federated_Learning_via_Decentralized_Dataset_Distillation_in_Resource-Constrained_Edge_Environments"}