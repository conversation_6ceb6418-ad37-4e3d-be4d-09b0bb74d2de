{"table_of_contents": [{"title": "Federated Learning via Synthetic Data", "heading_level": null, "page_id": 0, "polygon": [[164.5048828125, 113.9853515625], [444.0, 113.9853515625], [444.0, 130.8076171875], [164.5048828125, 130.8076171875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[283.5, 232.41796875], [327.75, 232.41796875], [327.75, 241.69921875], [283.5, 241.69921875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.0751953125, 359.25], [182.583984375, 359.25], [182.583984375, 372.796875], [70.0751953125, 372.796875]]}, {"title": "2 Connection to Data Poisoning and Beyond", "heading_level": null, "page_id": 0, "polygon": [[70.5, 648.75], [391.5, 648.75], [391.5, 661.67578125], [70.5, 661.67578125]]}, {"title": "2.1 Motivation from data poisoning", "heading_level": null, "page_id": 0, "polygon": [[70.5, 676.5], [290.25, 676.5], [290.25, 687.97265625], [70.5, 687.97265625]]}, {"title": "2.2 Improvements", "heading_level": null, "page_id": 2, "polygon": [[70.5, 74.25], [186.169921875, 74.25], [186.169921875, 85.46484375], [70.5, 85.46484375]]}, {"title": "2.2.1 Approximating standard federated learning", "heading_level": null, "page_id": 2, "polygon": [[70.5, 137.25], [347.25, 137.25], [347.25, 148.4033203125], [70.5, 148.4033203125]]}, {"title": "2.2.2 Multiple steps of gradient descent", "heading_level": null, "page_id": 2, "polygon": [[70.5, 480.69140625], [295.5, 480.69140625], [295.5, 490.74609375], [70.5, 490.74609375]]}, {"title": "2.2.3 Normalizing intermediate and final updates", "heading_level": null, "page_id": 3, "polygon": [[70.5, 116.015625], [348.732421875, 116.015625], [348.732421875, 127.6171875], [70.5, 127.6171875]]}, {"title": "2.2.4 Trainable Y in synthetic data", "heading_level": null, "page_id": 3, "polygon": [[70.5, 274.5], [271.3359375, 274.5], [271.3359375, 285.78515625], [70.5, 285.78515625]]}, {"title": "3 Synthetic data generating procedure", "heading_level": null, "page_id": 3, "polygon": [[70.5, 544.5], [349.03125, 544.5], [349.03125, 557.6484375], [70.5, 557.6484375]]}, {"title": "Algorithm 2: clientUpdate", "heading_level": null, "page_id": 4, "polygon": [[75.864990234375, 273.75], [214.5, 273.75], [214.5, 285.3984375], [75.864990234375, 285.3984375]]}, {"title": "Algorithm 3: updateFromSynthetic", "heading_level": null, "page_id": 5, "polygon": [[75.005859375, 76.5], [257.25, 76.5], [257.25, 87.78515625], [75.005859375, 87.78515625]]}, {"title": "3.1 Tracking the best approximation", "heading_level": null, "page_id": 5, "polygon": [[70.5, 532.5], [295.5, 532.5], [295.5, 543.33984375], [70.5, 543.33984375]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 6, "polygon": [[70.5, 188.8154296875], [182.4345703125, 188.8154296875], [182.4345703125, 201.287109375], [70.5, 201.287109375]]}, {"title": "4.1 Quality of procedure on IID and non-IID clients", "heading_level": null, "page_id": 6, "polygon": [[70.5, 528.0], [387.75, 528.0], [387.75, 540.24609375], [70.5, 540.24609375]]}, {"title": "Figure 2: Comparing synthetic data FL to full gradient transmission FL", "heading_level": null, "page_id": 7, "polygon": [[145.3798828125, 70.81787109375], [464.25, 70.81787109375], [464.25, 82.5], [145.3798828125, 82.5]]}, {"title": "4.2 Robustness to distillation learning rate", "heading_level": null, "page_id": 7, "polygon": [[70.5, 604.828125], [334.986328125, 604.828125], [334.986328125, 617.203125], [70.5, 617.203125]]}, {"title": "4.3 Trade off between communication, compute and approximation quality", "heading_level": null, "page_id": 8, "polygon": [[70.5, 347.66015625], [521.15625, 347.66015625], [521.15625, 358.875], [70.5, 358.875]]}, {"title": "4.4 Adapt for server to client transmission", "heading_level": null, "page_id": 8, "polygon": [[70.5, 560.25], [330.75, 560.25], [330.75, 571.18359375], [70.5, 571.18359375]]}, {"title": "5 Possible Future Work", "heading_level": null, "page_id": 10, "polygon": [[70.5, 72.123046875], [247.2802734375, 72.123046875], [247.2802734375, 87.1083984375], [70.5, 87.1083984375]]}, {"title": "5.1 Async updates", "heading_level": null, "page_id": 10, "polygon": [[70.5, 154.107421875], [191.84765625, 154.107421875], [191.84765625, 167.642578125], [70.5, 167.642578125]]}, {"title": "5.2 Privacy concerns", "heading_level": null, "page_id": 10, "polygon": [[70.5, 352.880859375], [205.1455078125, 352.880859375], [205.1455078125, 366.416015625], [70.5, 366.416015625]]}, {"title": "5.3 Experiments with heterogeneous client resources", "heading_level": null, "page_id": 10, "polygon": [[70.5, 511.2421875], [392.66015625, 511.2421875], [392.66015625, 524.390625], [70.5, 524.390625]]}, {"title": "5.4 Remove needing localUpdate", "heading_level": null, "page_id": 10, "polygon": [[70.5, 642.33984375], [265.95703125, 642.33984375], [265.95703125, 656.26171875], [70.5, 656.26171875]]}, {"title": "5.5 Analysis from a compression view", "heading_level": null, "page_id": 11, "polygon": [[70.5, 74.25], [302.712890625, 74.25], [302.712890625, 85.51318359375], [70.5, 85.51318359375]]}, {"title": "6 Acknowledgements", "heading_level": null, "page_id": 11, "polygon": [[70.5, 222.0], [226.6611328125, 222.0], [226.6611328125, 235.125], [70.5, 235.125]]}, {"title": "References", "heading_level": null, "page_id": 11, "polygon": [[70.5, 294.6796875], [147.322265625, 294.6796875], [147.322265625, 307.44140625], [70.5, 307.44140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 39], ["Text", 7], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 381], ["Line", 43], ["Equation", 3], ["Text", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1055, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 322], ["Line", 44], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 3], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 37], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 45], ["TextInlineMath", 5], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["Line", 55], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 41], ["Text", 6], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 295], ["Line", 82], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1498, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 65], ["Text", 3], ["Figure", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 61], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 40], ["SectionHeader", 5], ["Text", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 33], ["ListItem", 8], ["Reference", 8], ["SectionHeader", 3], ["Text", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Federated_Learning_via_Synthetic_Data"}