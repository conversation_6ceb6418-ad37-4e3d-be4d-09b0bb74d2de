{"table_of_contents": [{"title": "Federated Learning on Virtual Heterogeneous Data with\nLocal-Global Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[70.5, 81.75], [540.28125, 81.75], [540.28125, 119.689453125], [70.5, 119.689453125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.0, 387.0], [330.0, 387.0], [330.0, 397.93359375], [282.0, 397.93359375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.5, 648.0], [159.0, 648.0], [159.0, 658.96875], [70.5, 658.96875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[70.5, 423.45703125], [168.240234375, 423.45703125], [168.240234375, 436.60546875], [70.5, 436.60546875]]}, {"title": "2.1 Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[70.5, 448.59375], [186.6181640625, 448.59375], [186.6181640625, 460.96875], [70.5, 460.96875]]}, {"title": "2.2 Heterogeneous Federated Learning", "heading_level": null, "page_id": 2, "polygon": [[70.5, 650.84765625], [259.083984375, 650.84765625], [259.083984375, 663.22265625], [70.5, 663.22265625]]}, {"title": "2.3 Datasets Distillation for FL", "heading_level": null, "page_id": 3, "polygon": [[70.5, 369.75], [222.4775390625, 369.75], [222.4775390625, 379.951171875], [70.5, 379.951171875]]}, {"title": "3 Method", "heading_level": null, "page_id": 3, "polygon": [[70.5, 551.07421875], [135.0, 551.07421875], [135.0, 561.90234375], [70.5, 561.90234375]]}, {"title": "3.1 Setup for Federated Virtual Learning", "heading_level": null, "page_id": 3, "polygon": [[70.5, 582.0], [265.658203125, 582.0], [265.658203125, 591.29296875], [70.5, 591.29296875]]}, {"title": "3.2 Overall Pipeline", "heading_level": null, "page_id": 4, "polygon": [[70.5, 440.47265625], [171.52734375, 440.47265625], [171.52734375, 452.84765625], [70.5, 452.84765625]]}, {"title": "3.3 FL with Local-Global Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[70.5, 586.65234375], [287.6220703125, 586.65234375], [287.6220703125, 598.25390625], [70.5, 598.25390625]]}, {"title": "3.3.1 Local Data Distillation for Federated Virtual Learning", "heading_level": null, "page_id": 4, "polygon": [[70.5, 609.46875], [352.01953125, 609.46875], [352.01953125, 621.0703125], [70.5, 621.0703125]]}, {"title": "3.3.2 Global Data Distillation for Heterogeneity Harmonization", "heading_level": null, "page_id": 6, "polygon": [[70.5, 441.0], [366.75, 441.0], [366.75, 450.52734375], [70.5, 450.52734375]]}, {"title": "4 Theoretical Analysis", "heading_level": null, "page_id": 7, "polygon": [[70.5, 254.25], [201.75, 254.25], [201.75, 265.095703125], [70.5, 265.095703125]]}, {"title": "5 Experiment", "heading_level": null, "page_id": 8, "polygon": [[70.5, 602.89453125], [154.5, 602.89453125], [154.5, 614.49609375], [70.5, 614.49609375]]}, {"title": "5.1 Training and Evaluation Setup", "heading_level": null, "page_id": 9, "polygon": [[70.5, 160.875], [235.6259765625, 160.875], [235.6259765625, 171.703125], [70.5, 171.703125]]}, {"title": "5.2 DIGITS Experiment", "heading_level": null, "page_id": 9, "polygon": [[70.5, 453.234375], [190.0546875, 453.234375], [190.0546875, 464.8359375], [70.5, 464.8359375]]}, {"title": "5.3 CIFAR10C Experiment", "heading_level": null, "page_id": 10, "polygon": [[70.5, 83.25], [201.41015625, 83.25], [201.41015625, 93.82763671875], [70.5, 93.82763671875]]}, {"title": "5.4 RETINA Experiment", "heading_level": null, "page_id": 10, "polygon": [[70.5, 285.75], [189.3076171875, 285.75], [189.3076171875, 295.83984375], [70.5, 295.83984375]]}, {"title": "5.5 Ablation studies for FedLGD", "heading_level": null, "page_id": 10, "polygon": [[70.5, 613.5], [228.4541015625, 613.5], [228.4541015625, 623.77734375], [70.5, 623.77734375]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[70.5, 122.396484375], [151.43115234375, 122.396484375], [151.43115234375, 133.998046875], [70.5, 133.998046875]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 13, "polygon": [[70.5, 330.0], [172.125, 330.0], [172.125, 341.0859375], [70.5, 341.0859375]]}, {"title": "References", "heading_level": null, "page_id": 13, "polygon": [[70.5, 405.75], [131.6337890625, 405.75], [131.6337890625, 416.8828125], [70.5, 416.8828125]]}, {"title": "A Notation Table", "heading_level": null, "page_id": 18, "polygon": [[70.5, 171.75], [177.0, 171.75], [177.0, 183.3046875], [70.5, 183.3046875]]}, {"title": "B Additional Results and Ablation Studies for FedLGD", "heading_level": null, "page_id": 18, "polygon": [[70.5, 462.75], [380.25, 462.75], [380.25, 474.1171875], [70.5, 474.1171875]]}, {"title": "B.2 Different random seeds", "heading_level": null, "page_id": 19, "polygon": [[70.5, 120.75], [206.19140625, 120.75], [206.19140625, 130.7109375], [70.5, 130.7109375]]}, {"title": "B.3 Different heterogeneity levels of label shift", "heading_level": null, "page_id": 19, "polygon": [[70.5, 628.5], [293.25, 628.5], [293.25, 638.47265625], [70.5, 638.47265625]]}, {"title": "B.4 Analysis of batch size", "heading_level": null, "page_id": 20, "polygon": [[70.5, 287.912109375], [197.25, 287.912109375], [197.25, 298.740234375], [70.5, 299.25]]}, {"title": "B.5 Analysis of Local Epoch", "heading_level": null, "page_id": 20, "polygon": [[70.5, 502.34765625], [209.6279296875, 502.34765625], [209.6279296875, 513.17578125], [70.5, 513.17578125]]}, {"title": "B.6 Different Initialization for Virtual Images", "heading_level": null, "page_id": 20, "polygon": [[70.5, 626.87109375], [285.75, 626.87109375], [285.75, 637.69921875], [70.5, 637.69921875]]}, {"title": "C Experimental details", "heading_level": null, "page_id": 21, "polygon": [[70.5, 425.00390625], [205.59375, 425.00390625], [205.59375, 437.37890625], [70.5, 437.37890625]]}, {"title": "C.1 Visualization of the original images", "heading_level": null, "page_id": 21, "polygon": [[70.5, 452.07421875], [259.5, 452.07421875], [259.5, 463.67578125], [70.5, 463.67578125]]}, {"title": "C.2 Visualization of our distilled global and local images", "heading_level": null, "page_id": 21, "polygon": [[70.5, 686.0390625], [336.0, 686.0390625], [336.0, 697.640625], [70.5, 697.640625]]}, {"title": "C.3 Visualization of the heterogeneity of the datasets", "heading_level": null, "page_id": 22, "polygon": [[69.75, 479.91796875], [324.826171875, 479.91796875], [324.826171875, 492.29296875], [69.75, 492.29296875]]}, {"title": "C.4 Model architecture", "heading_level": null, "page_id": 22, "polygon": [[69.75, 539.47265625], [186.46875, 539.47265625], [186.46875, 551.07421875], [69.75, 551.07421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 44], ["Text", 18], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 7, "llm_error_count": 0, "llm_tokens_used": 11042, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 78], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 663, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 48], ["Text", 5], ["ListItem", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 49], ["SectionHeader", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 104], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 977, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 839], ["Line", 84], ["TextInlineMath", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 101], ["Equation", 5], ["Text", 4], ["Reference", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["Line", 70], ["TextInlineMath", 10], ["Equation", 3], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2187, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 730], ["TableCell", 250], ["Line", 47], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["TableCell", 96], ["Line", 52], ["Text", 4], ["Table", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 7100, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 64], ["TableCell", 63], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["Line", 117], ["Reference", 5], ["Figure", 2], ["Caption", 2], ["Text", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 116], ["Text", 7], ["Caption", 4], ["Figure", 3], ["FigureGroup", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2325, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 46], ["ListItem", 8], ["Reference", 8], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 44], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 44], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 44], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 63], ["Line", 20], ["ListItem", 8], ["Reference", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["TableCell", 68], ["Line", 67], ["Text", 4], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["ListItem", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 113], ["Text", 3], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 42], ["TableCell", 40], ["Text", 5], ["SectionHeader", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2468, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["TableCell", 56], ["Line", 40], ["Reference", 4], ["SectionHeader", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Table", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 13], ["Reference", 4], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1471, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 42], ["Line", 11], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1493, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Line", 42], ["Span", 40], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1562, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Line", 104], ["Span", 41], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1454, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["TableCell", 124], ["Line", 41], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15861, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Federated_Virtual_Learning_on_Heterogeneous_Data_with_Local-global_Distillation"}