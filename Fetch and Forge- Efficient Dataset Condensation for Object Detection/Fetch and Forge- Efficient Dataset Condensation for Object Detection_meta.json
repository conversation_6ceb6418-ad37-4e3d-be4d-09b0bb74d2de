{"table_of_contents": [{"title": "Fetch and Forge: Efficient Dataset\nCondensation for Object Detection", "heading_level": null, "page_id": 0, "polygon": [[177.0, 99.75], [435.392578125, 99.75], [435.392578125, 136.0283203125], [177.0, 136.0283203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 267.0], [329.25, 267.0], [329.25, 278.244140625], [282.75, 278.244140625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 516.0], [191.84765625, 516.0], [191.84765625, 527.09765625], [107.25, 527.09765625]]}, {"title": "2 Related work", "heading_level": null, "page_id": 2, "polygon": [[106.5, 310.921875], [196.62890625, 310.921875], [196.62890625, 323.68359375], [106.5, 323.68359375]]}, {"title": "3 Dataset Condensation for Object Detection", "heading_level": null, "page_id": 3, "polygon": [[106.98046875, 348.75], [345.4453125, 348.75], [345.4453125, 360.228515625], [106.98046875, 360.228515625]]}, {"title": "3.1 Preliminary", "heading_level": null, "page_id": 3, "polygon": [[106.5, 374.25], [181.5, 374.25], [181.5, 384.205078125], [106.5, 384.205078125]]}, {"title": "3.2 Fetch and Forge: DCOD Framework", "heading_level": null, "page_id": 4, "polygon": [[106.5, 74.25], [288.0, 74.25], [288.0, 83.4345703125], [106.5, 83.4345703125]]}, {"title": "Algorithm 1 Dataset Condensation for Object Detection", "heading_level": null, "page_id": 5, "polygon": [[107.25, 73.5], [334.5, 73.5], [334.5, 83.57958984375], [107.25, 83.57958984375]]}, {"title": "Input: Training set T", "heading_level": null, "page_id": 5, "polygon": [[107.25, 87.0], [199.6171875, 87.0], [199.6171875, 97.06640625], [107.25, 97.06640625]]}, {"title": "4 Experiment", "heading_level": null, "page_id": 6, "polygon": [[107.25, 313.5], [187.5, 313.5], [187.5, 325.423828125], [107.25, 325.423828125]]}, {"title": "4.1 Experiment Setup", "heading_level": null, "page_id": 6, "polygon": [[106.75634765625, 338.25], [208.5, 338.25], [208.5, 348.43359375], [106.75634765625, 348.43359375]]}, {"title": "4.2 Experimental Results", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 615.0], [223.0751953125, 615.0], [223.0751953125, 624.9375], [107.1298828125, 624.9375]]}, {"title": "4.3 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[106.5, 479.91796875], [195.75, 479.91796875], [195.75, 489.97265625], [106.5, 489.97265625]]}, {"title": "4.4 Visualization", "heading_level": null, "page_id": 8, "polygon": [[106.5, 658.1953125], [187.06640625, 658.1953125], [187.06640625, 669.0234375], [106.5, 669.0234375]]}, {"title": "5 Conclusion, Limitations and Future Work", "heading_level": null, "page_id": 9, "polygon": [[106.5, 405.75], [341.25, 405.75], [341.25, 418.04296875], [106.5, 418.04296875]]}, {"title": "Acknowledgments and Disclosure of Funding", "heading_level": null, "page_id": 9, "polygon": [[107.25, 582.75], [340.5, 582.75], [340.5, 594.7734375], [107.25, 594.7734375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 72.0], [165.0, 72.0], [165.0, 83.53125], [107.25, 83.53125]]}, {"title": "NeurIPS Paper Checklist", "heading_level": null, "page_id": 12, "polygon": [[106.5, 72.0], [237.8671875, 72.0], [237.8671875, 84.35302734375], [106.5, 84.35302734375]]}, {"title": "1. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 12, "polygon": [[130.5, 92.25], [174.814453125, 92.25], [174.814453125, 103.640625], [130.5, 103.640625]]}, {"title": "2. Limitations", "heading_level": null, "page_id": 12, "polygon": [[129.75, 296.25], [194.25, 296.25], [194.25, 307.634765625], [129.75, 307.634765625]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 12, "polygon": [[141.75, 326.25], [206.6396484375, 326.25], [206.6396484375, 336.83203125], [141.75, 336.83203125]]}, {"title": "3. Theory Assumptions and Proofs", "heading_level": null, "page_id": 12, "polygon": [[129.75, 670.5], [283.2890625, 670.5], [283.2890625, 681.78515625], [129.75, 681.78515625]]}, {"title": "Justification:", "heading_level": null, "page_id": 13, "polygon": [[141.75, 73.5], [196.330078125, 73.5], [196.330078125, 84.2080078125], [141.75, 84.2080078125]]}, {"title": "4. Experimental Result Reproducibility", "heading_level": null, "page_id": 13, "polygon": [[129.75, 225.0], [302.25, 225.0], [302.25, 236.091796875], [129.75, 236.091796875]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 13, "polygon": [[142.5, 276.75], [205.294921875, 276.75], [205.294921875, 287.138671875], [142.5, 287.138671875]]}, {"title": "5. Open access to data and code", "heading_level": null, "page_id": 13, "polygon": [[129.2431640625, 675.0], [269.3935546875, 675.0], [269.3935546875, 685.265625], [129.2431640625, 685.265625]]}, {"title": "Answer: [No]", "heading_level": null, "page_id": 14, "polygon": [[141.75, 73.4765625], [202.1572265625, 73.4765625], [202.1572265625, 84.4013671875], [141.75, 84.4013671875]]}, {"title": "6. Experimental Setting/Details", "heading_level": null, "page_id": 14, "polygon": [[129.75, 340.5], [268.048828125, 340.5], [268.048828125, 351.9140625], [129.75, 351.9140625]]}, {"title": "7. Experiment Statistical Significance", "heading_level": null, "page_id": 14, "polygon": [[129.75, 498.48046875], [294.0, 498.48046875], [294.0, 509.30859375], [129.75, 509.30859375]]}, {"title": "8. Experiments Compute Resources", "heading_level": null, "page_id": 15, "polygon": [[130.5, 171.6064453125], [286.1279296875, 171.6064453125], [286.1279296875, 181.6611328125], [130.5, 181.6611328125]]}, {"title": "9. Code Of Ethics", "heading_level": null, "page_id": 15, "polygon": [[129.75, 377.25], [210.5244140625, 377.25], [210.5244140625, 387.685546875], [129.75, 387.685546875]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 15, "polygon": [[142.5, 422.25], [203.3525390625, 422.25], [203.3525390625, 431.96484375], [142.5, 431.96484375]]}, {"title": "10. <PERSON><PERSON>s", "heading_level": null, "page_id": 15, "polygon": [[125.25, 536.25], [217.845703125, 536.25], [217.845703125, 546.43359375], [125.25, 546.43359375]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 15, "polygon": [[142.5, 581.25], [203.**********, 581.25], [203.**********, 590.90625], [142.5, 590.90625]]}, {"title": "11. Safeguards", "heading_level": null, "page_id": 16, "polygon": [[125.25, 246.533203125], [193.**********, 246.533203125], [193.**********, 256.974609375], [125.25, 256.974609375]]}, {"title": "12. Licenses for existing assets", "heading_level": null, "page_id": 16, "polygon": [[125.25, 467.25], [259.681640625, 467.25], [259.681640625, 477.59765625], [125.25, 477.59765625]]}, {"title": "13. <PERSON>sets", "heading_level": null, "page_id": 17, "polygon": [[125.25, 99.75], [193.341796875, 99.75], [193.341796875, 110.6015625], [125.25, 110.6015625]]}, {"title": "14. Crowdsourcing and Research with Human Subjects", "heading_level": null, "page_id": 17, "polygon": [[125.25, 281.25], [365.466796875, 281.25], [365.466796875, 292.359375], [125.25, 292.359375]]}, {"title": "15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human\nSubjects", "heading_level": null, "page_id": 17, "polygon": [[125.25, 474.0], [506.25, 474.0], [506.25, 495.38671875], [125.25, 495.38671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 42], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8026, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 67], ["Text", 3], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 43], ["Text", 7], ["Picture", 2], ["Caption", 1], ["ListItem", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1363, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 63], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 713, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 52], ["TextInlineMath", 5], ["Text", 4], ["Equation", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 691], ["Line", 67], ["Text", 8], ["Equation", 5], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["TableCell", 247], ["Line", 83], ["Text", 7], ["SectionHeader", 3], ["Caption", 2], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["TableCell", 113], ["Line", 65], ["Caption", 3], ["Table", 3], ["Text", 3], ["TextInlineMath", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 1, "llm_tokens_used": 13058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 26], ["Text", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1323, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Line", 62], ["Span", 50], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 52], ["ListItem", 17], ["Reference", 17], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 44], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 53], ["ListItem", 12], ["Text", 9], ["SectionHeader", 5], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 55], ["ListItem", 15], ["Text", 5], ["SectionHeader", 4], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 53], ["ListItem", 17], ["Text", 9], ["SectionHeader", 3], ["ListGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 49], ["ListItem", 13], ["Text", 10], ["SectionHeader", 5], ["ListGroup", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 53], ["ListItem", 14], ["Text", 8], ["ListGroup", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 51], ["ListItem", 12], ["Text", 12], ["SectionHeader", 3], ["ListGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Fetch and Forge- Efficient Dataset Condensation for Object Detection"}