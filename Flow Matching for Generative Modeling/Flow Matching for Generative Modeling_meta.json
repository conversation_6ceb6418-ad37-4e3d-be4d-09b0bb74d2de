{"table_of_contents": [{"title": "FLOW MATCHING FOR GENERATIVE MODELING", "heading_level": null, "page_id": 0, "polygon": [[106.2333984375, 81.0], [462.5859375, 81.0], [462.5859375, 96.6796875], [106.2333984375, 96.6796875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 168.75], [334.6875, 168.75], [334.6875, 179.1474609375], [276.75, 179.1474609375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 393.75], [207.0, 393.75], [207.0, 403.734375], [107.25, 403.734375]]}, {"title": "2 PRELIMINARIES: CONTINUOUS NORMALIZING FLOWS", "heading_level": null, "page_id": 1, "polygon": [[106.75634765625, 324.0], [404.25, 325.5], [404.25, 336.638671875], [106.75634765625, 336.638671875]]}, {"title": "3 FLOW MATCHING", "heading_level": null, "page_id": 1, "polygon": [[107.25, 624.75], [218.25, 624.75], [218.25, 635.37890625], [107.25, 635.37890625]]}, {"title": "3.1 CONSTRUCTING p_t, u_t FROM CONDITIONAL PROBABILITY PATHS AND VECTOR FIELDS", "heading_level": null, "page_id": 2, "polygon": [[106.5, 266.25], [494.25, 266.25], [494.25, 275.73046875], [106.5, 275.73046875]]}, {"title": "3.2 CONDITIONAL FLOW MATCHING", "heading_level": null, "page_id": 3, "polygon": [[106.5322265625, 83.25], [272.25, 83.25], [272.25, 93.392578125], [106.5322265625, 93.392578125]]}, {"title": "4 CONDITIONAL PROBABILITY PATHS AND VECTOR FIELDS", "heading_level": null, "page_id": 3, "polygon": [[105.78515625, 356.25], [422.25, 356.25], [422.25, 368.349609375], [105.78515625, 368.349609375]]}, {"title": "4.1 SPECIAL INSTANCES OF GAUSSIAN CONDITIONAL PROBABILITY PATHS", "heading_level": null, "page_id": 4, "polygon": [[106.5, 164.25], [432.10546875, 164.25], [432.10546875, 174.5068359375], [106.5, 174.5068359375]]}, {"title": "5 RELATED WORK", "heading_level": null, "page_id": 5, "polygon": [[107.25, 588.0], [211.5, 588.0], [211.5, 599.02734375], [107.25, 599.02734375]]}, {"title": "6 EXPERIMENTS", "heading_level": null, "page_id": 6, "polygon": [[107.25, 606.0], [200.25, 606.0], [200.25, 617.203125], [107.25, 617.203125]]}, {"title": "6.1 DENSITY MODELING AND <PERSON><PERSON><PERSON> QUALITY ON IMAGENET", "heading_level": null, "page_id": 7, "polygon": [[106.5, 315.0], [390.0, 315.0], [390.0, 325.23046875], [106.5, 325.23046875]]}, {"title": "6.2 SAMPLING EFFICIENCY", "heading_level": null, "page_id": 7, "polygon": [[106.98046875, 694.5], [231.75, 694.5], [231.75, 704.98828125], [106.98046875, 704.98828125]]}, {"title": "6.3 CONDITIONAL SAMPLING FROM LOW-RESOLUTION IMAGES", "heading_level": null, "page_id": 8, "polygon": [[106.5, 453.75], [383.25, 453.75], [383.25, 462.90234375], [106.5, 462.90234375]]}, {"title": "7 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 610.5], [195.75, 610.5], [195.75, 622.6171875], [107.25, 622.6171875]]}, {"title": "8 SOCIAL RESPONSIBILITY", "heading_level": null, "page_id": 9, "polygon": [[107.25, 82.5], [254.6015625, 82.5], [254.6015625, 93.53759765625], [107.25, 93.53759765625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 179.25], [176.25, 179.25], [176.25, 189.7822265625], [107.25, 189.7822265625]]}, {"title": "A THEOREM PROOFS", "heading_level": null, "page_id": 13, "polygon": [[107.05517578125, 81.75], [225.0, 81.75], [225.0, 93.4892578125], [107.05517578125, 93.4892578125]]}, {"title": "B THE CONTINUITY EQUATION", "heading_level": null, "page_id": 14, "polygon": [[107.25, 324.0], [274.025390625, 324.0], [274.025390625, 335.28515625], [107.25, 335.28515625]]}, {"title": "C COMPUTING PROBA<PERSON>LITIES OF THE CNF MODEL", "heading_level": null, "page_id": 14, "polygon": [[106.5, 456.75], [381.75, 456.75], [381.75, 468.31640625], [106.5, 468.31640625]]}, {"title": "D DIFFUSION CONDITIONAL VECTOR FIELDS", "heading_level": null, "page_id": 16, "polygon": [[107.25, 477.0], [344.25, 477.0], [344.25, 488.0390625], [107.25, 488.0390625]]}, {"title": "Variance Preserving (VP) path The SDE for the VP path is", "heading_level": null, "page_id": 17, "polygon": [[106.90576171875, 494.25], [359.25, 494.25], [359.25, 504.66796875], [106.90576171875, 504.66796875]]}, {"title": "E IMPLEMENTATION DETAILS", "heading_level": null, "page_id": 18, "polygon": [[106.5, 321.0], [267.0, 321.0], [267.0, 332.19140625], [106.5, 332.19140625]]}, {"title": "E.1 DIFFUSION BASELINES", "heading_level": null, "page_id": 18, "polygon": [[106.5, 425.25], [229.5, 425.25], [229.5, 435.05859375], [106.5, 435.05859375]]}, {"title": "E.2 TRAINING & EVALUATION DETAILS", "heading_level": null, "page_id": 19, "polygon": [[106.5, 486.75], [283.5, 486.75], [283.5, 496.93359375], [106.5, 496.93359375]]}, {"title": "F ADDITIONAL TABLES AND FIGURES", "heading_level": null, "page_id": 20, "polygon": [[106.5, 474.75], [309.0, 474.75], [309.0, 486.87890625], [106.5, 486.87890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 57], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 599], ["Line", 58], ["Reference", 6], ["TextInlineMath", 4], ["Equation", 4], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 728], ["Line", 53], ["TextInlineMath", 7], ["Text", 5], ["Equation", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 70], ["TextInlineMath", 7], ["Equation", 6], ["Reference", 6], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 796], ["Line", 91], ["Equation", 7], ["TextInlineMath", 7], ["Reference", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 64], ["TextInlineMath", 4], ["Text", 4], ["Equation", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1385, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 51], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["TableCell", 111], ["Line", 73], ["Text", 4], ["Reference", 3], ["Caption", 2], ["Figure", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1437, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 97], ["TableCell", 35], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 951, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 49], ["ListItem", 14], ["Reference", 14], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 48], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 49], ["Reference", 17], ["ListItem", 16], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 10], ["ListItem", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 997], ["Line", 91], ["TextInlineMath", 7], ["Text", 5], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10823, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 630], ["Line", 86], ["Equation", 10], ["TextInlineMath", 7], ["Text", 7], ["Reference", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 758], ["Line", 87], ["TextInlineMath", 8], ["Equation", 8], ["Reference", 6], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1478, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 734], ["Line", 94], ["Equation", 9], ["Text", 9], ["TableCell", 8], ["TextInlineMath", 7], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Form", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1849, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 815], ["Line", 147], ["Equation", 14], ["Text", 13], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2176, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 587], ["TableCell", 91], ["Line", 85], ["Equation", 6], ["Text", 5], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2021, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["TableCell", 150], ["Line", 61], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3918, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 176], ["Span", 149], ["Line", 29], ["Reference", 4], ["Text", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5439, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 624, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1044, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 885, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 23], ["Line", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Flow Matching for Generative Modeling"}