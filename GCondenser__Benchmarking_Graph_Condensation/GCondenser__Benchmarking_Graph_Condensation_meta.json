{"table_of_contents": [{"title": "GCondenser: Benchmarking Graph Condensation", "heading_level": null, "page_id": 0, "polygon": [[118.5, 98.25], [492.767578125, 98.25], [492.767578125, 116.982421875], [118.5, 116.982421875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.2431640625, 222.75], [329.25, 222.75], [329.25, 233.96484375], [282.2431640625, 233.96484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 422.25], [191.6982421875, 422.25], [191.6982421875, 434.28515625], [107.25, 434.28515625]]}, {"title": "2 Preliminary", "heading_level": null, "page_id": 1, "polygon": [[106.5, 269.25], [187.5, 269.25], [187.5, 280.951171875], [106.5, 280.951171875]]}, {"title": "3 Benchmark Design", "heading_level": null, "page_id": 2, "polygon": [[106.90576171875, 599.25], [224.25, 599.25], [224.25, 610.2421875], [106.90576171875, 610.2421875]]}, {"title": "3.1 Graph Condensation Modules", "heading_level": null, "page_id": 2, "polygon": [[106.5, 659.25], [259.5, 659.25], [259.5, 669.41015625], [106.5, 669.41015625]]}, {"title": "3.2 Evaluations", "heading_level": null, "page_id": 3, "polygon": [[106.5, 191.25], [181.5380859375, 191.25], [181.5380859375, 201.48046875], [106.5, 201.48046875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 3, "polygon": [[107.25, 610.5], [192.0, 610.5], [192.0, 622.6171875], [107.25, 622.6171875]]}, {"title": "4.1 Baselines", "heading_level": null, "page_id": 4, "polygon": [[107.25, 110.8916015625], [171.0, 110.8916015625], [171.0, 121.1396484375], [107.25, 121.1396484375]]}, {"title": "4.2 Implementations", "heading_level": null, "page_id": 4, "polygon": [[106.5, 398.3203125], [204.0, 398.3203125], [204.0, 408.375], [106.5, 408.375]]}, {"title": "4.3 Overall Results (RQ1)", "heading_level": null, "page_id": 4, "polygon": [[107.25, 565.5], [227.109375, 565.5], [227.109375, 575.82421875], [107.25, 575.82421875]]}, {"title": "4.4 Condensation Efficiency (RQ2)", "heading_level": null, "page_id": 5, "polygon": [[106.5, 619.91015625], [264.76171875, 619.91015625], [264.76171875, 630.73828125], [106.5, 630.73828125]]}, {"title": "4.6 Impact of Different Validators (RQ4)", "heading_level": null, "page_id": 6, "polygon": [[107.25, 669.75], [289.5, 669.75], [289.5, 679.8515625], [107.25, 679.8515625]]}, {"title": "4.7 Cross-architecture Transferability (RQ5)", "heading_level": null, "page_id": 7, "polygon": [[106.90576171875, 272.25], [306.0, 272.25], [306.0, 282.498046875], [106.90576171875, 282.498046875]]}, {"title": "4.8 Continual Graph Learning (RQ6)", "heading_level": null, "page_id": 7, "polygon": [[106.45751953125, 561.0], [275.818359375, 561.0], [275.818359375, 571.18359375], [106.45751953125, 571.18359375]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 8, "polygon": [[106.5, 516.0], [197.3759765625, 516.0], [197.3759765625, 527.484375], [106.5, 527.484375]]}, {"title": "6 Conclusion and Future Work", "heading_level": null, "page_id": 9, "polygon": [[106.5, 112.5], [276.1171875, 112.5], [276.1171875, 123.556640625], [106.5, 123.556640625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 351.0], [165.0, 351.0], [165.0, 361.1953125], [107.25, 361.1953125]]}, {"title": "Checklist", "heading_level": null, "page_id": 12, "polygon": [[107.25, 72.0], [157.5, 72.0], [157.5, 83.724609375], [107.25, 83.724609375]]}, {"title": "A Implementation Details", "heading_level": null, "page_id": 13, "polygon": [[106.5322265625, 72.26806640625], [249.6708984375, 72.26806640625], [249.6708984375, 83.86962890625], [106.5322265625, 83.86962890625]]}, {"title": "A.1 Graph Condensation Settings", "heading_level": null, "page_id": 13, "polygon": [[106.60693359375, 96.72802734375], [259.5, 96.72802734375], [259.5, 106.6376953125], [106.60693359375, 106.6376953125]]}, {"title": "A.2 Label Distribution", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 368.25], [212.3173828125, 368.25], [212.3173828125, 378.017578125], [107.1298828125, 378.017578125]]}, {"title": "A.3 Reproducibility", "heading_level": null, "page_id": 13, "polygon": [[107.25, 626.25], [200.25, 626.25], [200.25, 636.5390625], [107.25, 636.5390625]]}, {"title": "A.4 Backbone Models", "heading_level": null, "page_id": 14, "polygon": [[106.98046875, 649.5], [209.1796875, 649.5], [209.1796875, 660.90234375], [106.98046875, 660.90234375]]}, {"title": "A.5 Continual Graph Learning", "heading_level": null, "page_id": 15, "polygon": [[106.5, 187.5], [248.4755859375, 187.5], [248.4755859375, 198.966796875], [106.5, 198.966796875]]}, {"title": "B More Experiments", "heading_level": null, "page_id": 15, "polygon": [[106.5, 341.25], [225.615234375, 341.25], [225.615234375, 353.07421875], [106.5, 353.07421875]]}, {"title": "B.1 Condensation Efficiency", "heading_level": null, "page_id": 15, "polygon": [[106.5, 402.0], [237.26953125, 402.0], [237.26953125, 413.015625], [106.5, 413.015625]]}, {"title": "B.2 Cross-architecture Transferability", "heading_level": null, "page_id": 16, "polygon": [[106.5, 628.5], [278.25, 628.5], [278.25, 638.47265625], [106.5, 638.47265625]]}, {"title": "C Assets License", "heading_level": null, "page_id": 17, "polygon": [[106.5, 541.5], [202.5, 541.5], [202.5, 553.39453125], [106.5, 553.39453125]]}, {"title": "D Ethic Statement", "heading_level": null, "page_id": 17, "polygon": [[106.5, 614.49609375], [212.25, 614.49609375], [212.25, 626.09765625], [106.5, 626.09765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 46], ["Text", 6], ["SectionHeader", 3], ["ListItem", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6315, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 770], ["Line", 69], ["TextInlineMath", 8], ["ListItem", 5], ["Equation", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 595], ["Line", 107], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["Caption", 2], ["SectionHeader", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 710, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 59], ["TableCell", 55], ["Text", 16], ["SectionHeader", 2], ["Table", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 49], ["Text", 13], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 2416], ["TableCell", 630], ["Line", 94], ["Text", 6], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["TableCell", 120], ["Line", 80], ["Text", 7], ["Reference", 5], ["Table", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 12386, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 486], ["Line", 79], ["TableCell", 66], ["Reference", 4], ["Text", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4558, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 56], ["Text", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1039, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 46], ["Reference", 13], ["ListItem", 12], ["Text", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 44], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 5], ["ListItem", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 36], ["ListItem", 23], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 47], ["Reference", 5], ["SectionHeader", 4], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["TableCell", 90], ["Line", 54], ["Text", 6], ["Caption", 3], ["ListItem", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7646, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 60], ["TableCell", 24], ["ListItem", 4], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4447, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 169], ["Line", 63], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1876, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 76], ["Footnote", 4], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2131, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 790], ["TableCell", 745], ["Line", 105], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 13348, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 1469], ["Span", 1451], ["Line", 158], ["Table", 4], ["Caption", 4], ["TableGroup", 4], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 33591, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/GCondenser__Benchmarking_Graph_Condensation"}