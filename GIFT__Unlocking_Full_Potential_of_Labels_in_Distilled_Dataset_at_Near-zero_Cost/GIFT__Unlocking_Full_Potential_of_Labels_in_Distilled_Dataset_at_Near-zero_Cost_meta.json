{"table_of_contents": [{"title": "GIFT: <PERSON><PERSON><PERSON><PERSON><PERSON> FULL POTENTIAL OF <PERSON><PERSON><PERSON>\nIN DISTILLED DATASET AT NEAR-ZERO COST", "heading_level": null, "page_id": 0, "polygon": [[105.486328125, 79.5], [472.1484375, 81.0], [472.1484375, 116.208984375], [105.486328125, 116.208984375]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 198.75], [335.28515625, 198.75], [335.28515625, 209.98828125], [276.75, 209.98828125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 417.0], [206.25, 417.0], [206.25, 427.32421875], [107.25, 427.32421875]]}, {"title": "In summary, our contributions are fourfold:", "heading_level": null, "page_id": 1, "polygon": [[106.5, 611.25], [296.5869140625, 611.25], [296.5869140625, 621.45703125], [106.5, 621.45703125]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 2, "polygon": [[107.25, 211.5], [212.765625, 211.5], [212.765625, 222.75], [107.25, 222.75]]}, {"title": "3 MOTIVATION", "heading_level": null, "page_id": 2, "polygon": [[107.25, 424.6171875], [195.2841796875, 424.6171875], [195.2841796875, 435.4453125], [107.25, 435.4453125]]}, {"title": "3.1 PRELIMINARY", "heading_level": null, "page_id": 2, "polygon": [[107.25, 444.33984375], [192.8935546875, 444.33984375], [192.8935546875, 454.39453125], [107.25, 454.39453125]]}, {"title": "3.2 ARE LOSS FUNCTIONS PULLING THE STRINGS IN SYNTHETIC DATASET PERFORMANCE?", "heading_level": null, "page_id": 2, "polygon": [[107.25, 624.9375], [505.6171875, 624.9375], [505.6171875, 634.9921875], [107.25, 634.9921875]]}, {"title": "4 METHOD", "heading_level": null, "page_id": 3, "polygon": [[107.25, 272.830078125], [174.515625, 272.830078125], [174.515625, 284.818359375], [107.25, 284.818359375]]}, {"title": "5 EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[107.25, 308.25], [201.111328125, 308.25], [201.111328125, 319.623046875], [107.25, 319.623046875]]}, {"title": "5.1 EXPERIMENT SETUP", "heading_level": null, "page_id": 4, "polygon": [[107.25, 447.43359375], [219.75, 447.43359375], [219.75, 456.75], [107.25, 456.75]]}, {"title": "5.2 CAN GIFT IMPROVE PERFORMANCE OF DATASET DISTILLATION?", "heading_level": null, "page_id": 5, "polygon": [[107.25, 575.82421875], [413.578125, 575.82421875], [413.578125, 585.87890625], [107.25, 585.87890625]]}, {"title": "5.3 CAN KNOWLEDGE DISTILLATION WORK?", "heading_level": null, "page_id": 6, "polygon": [[106.90576171875, 558.80859375], [309.5859375, 558.80859375], [309.5859375, 568.86328125], [106.90576171875, 568.86328125]]}, {"title": "5.4 CAN GIFT ACHIEVE NEAR-ZERO COST?", "heading_level": null, "page_id": 7, "polygon": [[106.30810546875, 418.4296875], [304.20703125, 418.4296875], [304.20703125, 428.484375], [106.30810546875, 428.484375]]}, {"title": "5.5 CAN GIFT IMPROVE GENERALIZATION?", "heading_level": null, "page_id": 7, "polygon": [[106.75634765625, 516.0], [305.25, 516.0], [305.25, 526.32421875], [106.75634765625, 526.32421875]]}, {"title": "5.6 ABLATION STUDY", "heading_level": null, "page_id": 8, "polygon": [[107.20458984375, 485.71875], [210.375, 485.71875], [210.375, 495.7734375], [107.20458984375, 495.7734375]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 599.25], [195.75, 599.25], [195.75, 610.2421875], [107.25, 610.2421875]]}, {"title": "ACKNOWLEDGMENT", "heading_level": null, "page_id": 10, "polygon": [[106.98046875, 82.5], [213.36328125, 82.5], [213.36328125, 93.4892578125], [106.98046875, 93.4892578125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.05517578125, 169.5], [176.009765625, 169.5], [176.009765625, 179.82421875], [107.05517578125, 179.82421875]]}, {"title": "A PROOF OF THEOREM 1", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 81.75], [244.5, 81.75], [244.5, 93.53759765625], [107.1298828125, 93.53759765625]]}, {"title": "B RELATED WORK", "heading_level": null, "page_id": 13, "polygon": [[107.25, 561.75], [214.5, 561.75], [214.5, 572.73046875], [107.25, 572.73046875]]}, {"title": "B.1 KNOWLEDGE DISTILLATION", "heading_level": null, "page_id": 13, "polygon": [[106.5, 586.5], [254.25, 586.5], [254.25, 595.93359375], [106.5, 595.93359375]]}, {"title": "C EXPERIMENT DETAILS", "heading_level": null, "page_id": 14, "polygon": [[107.05517578125, 81.75], [245.25, 81.75], [245.25, 93.63427734375], [107.05517578125, 93.63427734375]]}, {"title": "D EXPERIMENT RESULTS", "heading_level": null, "page_id": 15, "polygon": [[107.25, 630.0], [247.5791015625, 630.0], [247.5791015625, 641.953125], [107.25, 641.953125]]}, {"title": "E EMPERICAL AND THEORETICAL ANALYSIS OF CROSS-<PERSON>P<PERSON>MIZ<PERSON>\nGENERALIZATION", "heading_level": null, "page_id": 21, "polygon": [[106.5322265625, 81.59765625], [467.3671875, 81.59765625], [467.3671875, 107.4111328125], [106.5322265625, 107.4111328125]]}, {"title": "E.1 EMPERICAL ANALYSIS", "heading_level": null, "page_id": 21, "polygon": [[106.5, 328.130859375], [232.48828125, 328.130859375], [232.48828125, 338.958984375], [106.5, 338.958984375]]}, {"title": "E.2 THEORETICAL ANALYSIS", "heading_level": null, "page_id": 21, "polygon": [[106.5, 495.38671875], [242.6484375, 495.38671875], [242.6484375, 506.21484375], [106.5, 506.21484375]]}, {"title": "E.2.1 STOCHASTIC GRADIENT DESCENT (SGD)", "heading_level": null, "page_id": 21, "polygon": [[106.5, 515.8828125], [322.435546875, 515.8828125], [322.435546875, 526.7109375], [106.5, 526.7109375]]}, {"title": "SGD Update Rules. Define the following:", "heading_level": null, "page_id": 21, "polygon": [[106.5, 592.06640625], [287.3232421875, 592.06640625], [287.3232421875, 602.89453125], [106.5, 602.89453125]]}, {"title": "E.2.2 ADAM OPTIMIZER", "heading_level": null, "page_id": 22, "polygon": [[106.5, 118.5], [221.73046875, 118.5], [221.73046875, 128.77734375], [106.5, 128.77734375]]}, {"title": "E.2.3 ADAMW OPTIMIZER", "heading_level": null, "page_id": 22, "polygon": [[106.5, 680.25], [230.25, 680.25], [230.25, 690.6796875], [106.5, 690.6796875]]}, {"title": "E.2.4 WHY EXCESSIVE WEIGHT DECAY IN ADAM IMPEDES UPDATES WHEN LOSS IS\nSMALL?", "heading_level": null, "page_id": 23, "polygon": [[106.5, 450.0], [477.75, 450.0], [477.75, 471.41015625], [106.5, 471.41015625]]}, {"title": "F PYTORCH IMPLEMENTATION CODE", "heading_level": null, "page_id": 24, "polygon": [[106.5, 168.416015625], [307.5, 168.416015625], [307.5, 180.59765625], [106.5, 180.59765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 52], ["Text", 5], ["Footnote", 5], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 70], ["Text", 3], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 977, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 51], ["Text", 6], ["SectionHeader", 4], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 541], ["Line", 71], ["Text", 7], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2278, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 535], ["Line", 68], ["Text", 6], ["TextInlineMath", 3], ["Reference", 3], ["Equation", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1307], ["TableCell", 207], ["Line", 64], ["Footnote", 5], ["Text", 4], ["Reference", 4], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 633], ["TableCell", 95], ["Line", 51], ["Text", 6], ["Reference", 5], ["Table", 3], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 3, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 958], ["TableCell", 165], ["Line", 59], ["Text", 5], ["Reference", 5], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4376, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 968], ["TableCell", 130], ["Line", 59], ["Text", 7], ["Table", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 3, "llm_tokens_used": 994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 823], ["Line", 79], ["TableCell", 36], ["Text", 4], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 46], ["Reference", 15], ["ListItem", 13], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 48], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 37], ["Reference", 14], ["ListItem", 13], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 622], ["Line", 152], ["Text", 7], ["Equation", 7], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5512, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["TableCell", 96], ["Line", 45], ["Text", 5], ["ListItem", 5], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2549, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["TableCell", 97], ["Line", 49], ["Text", 5], ["ListItem", 4], ["Reference", 4], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["TableCell", 144], ["Line", 66], ["Reference", 4], ["Table", 3], ["Caption", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 16555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Line", 70], ["Span", 55], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 95], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 1045], ["TableCell", 122], ["Line", 116], ["Caption", 3], ["Text", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 16661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["TableCell", 102], ["Line", 77], ["Caption", 4], ["Reference", 4], ["Table", 2], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 16210, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 46], ["Text", 7], ["ListItem", 6], ["SectionHeader", 5], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 62], ["Text", 10], ["ListItem", 7], ["Equation", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1210, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 70], ["Text", 6], ["Equation", 6], ["TextInlineMath", 6], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 24], ["ListItem", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Code", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/GIFT__Unlocking_Full_Potential_of_Labels_in_Distilled_Dataset_at_Near-zero_Cost"}