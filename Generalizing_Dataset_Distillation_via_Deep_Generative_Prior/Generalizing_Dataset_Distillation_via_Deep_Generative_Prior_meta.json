{"table_of_contents": [{"title": "Generalizing Dataset Distillation via Deep Generative Prior", "heading_level": null, "page_id": 0, "polygon": [[114.75, 106.0576171875], [480.75, 106.0576171875], [480.75, 119.0126953125], [114.75, 119.0126953125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[143.138671875, 417.0], [191.25, 417.0], [191.25, 427.32421875], [143.138671875, 427.32421875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 646.5], [127.5, 646.5], [127.5, 657.03515625], [48.75, 657.03515625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 2, "polygon": [[48.0, 192.0], [134.25, 192.0], [134.25, 203.02734375], [48.0, 203.02734375]]}, {"title": "3. Generative Latent Distillation (GLaD)", "heading_level": null, "page_id": 2, "polygon": [[307.5, 244.5], [513.75, 244.5], [513.75, 255.814453125], [307.5, 255.814453125]]}, {"title": "3.1. Preliminaries on Dataset Distillation Methods", "heading_level": null, "page_id": 2, "polygon": [[307.5, 381.0], [543.0, 381.0], [543.0, 391.74609375], [307.5, 391.74609375]]}, {"title": "3.2. GLaD: Adding a Deep Generative Prior", "heading_level": null, "page_id": 3, "polygon": [[48.0, 672.50390625], [251.25, 672.50390625], [251.25, 682.55859375], [48.0, 682.55859375]]}, {"title": "3.3. Choosing a Generative Model and Latent Space", "heading_level": null, "page_id": 3, "polygon": [[307.5, 600.75], [545.25, 600.75], [545.25, 610.5], [307.5, 610.5]]}, {"title": "3.4. Memory Saving via Checkpointing", "heading_level": null, "page_id": 4, "polygon": [[307.5, 492.75], [492.0, 492.75], [492.0, 503.12109375], [307.5, 503.12109375]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 5, "polygon": [[48.75, 477.0], [128.25, 477.0], [128.25, 488.0390625], [48.75, 488.0390625]]}, {"title": "4.1. Finding a Suitable Latent Space", "heading_level": null, "page_id": 6, "polygon": [[48.75, 612.75], [219.75, 612.75], [219.75, 623.00390625], [48.75, 623.00390625]]}, {"title": "4.2. Improving Cross-Architecture Generalization", "heading_level": null, "page_id": 6, "polygon": [[307.5, 306.75], [543.0, 306.75], [543.0, 316.72265625], [307.5, 316.72265625]]}, {"title": "4.3. Latent Initialization and Generator Choices", "heading_level": null, "page_id": 6, "polygon": [[307.5, 587.25], [534.0, 587.25], [534.0, 597.8671875], [307.5, 597.8671875]]}, {"title": "5. Discussion and Limitations", "heading_level": null, "page_id": 7, "polygon": [[307.5, 288.0], [459.75, 288.0], [459.75, 298.546875], [307.5, 298.546875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.91796875], [48.75, 83.91796875]]}, {"title": "<PERSON><PERSON> More Visualizations", "heading_level": null, "page_id": 11, "polygon": [[48.75, 290.25], [167.25, 290.25], [167.25, 301.640625], [48.75, 301.640625]]}, {"title": "<PERSON>. StyleGAN Latent Spaces", "heading_level": null, "page_id": 11, "polygon": [[48.0, 344.25], [191.25, 344.25], [191.25, 355.78125], [48.0, 355.78125]]}, {"title": "C. Dataset Specifications", "heading_level": null, "page_id": 11, "polygon": [[48.75, 648.0], [176.90625, 648.0], [176.90625, 658.1953125], [48.75, 658.1953125]]}, {"title": "<PERSON><PERSON> Experimental Results", "heading_level": null, "page_id": 11, "polygon": [[307.5, 387.75], [465.0, 387.75], [465.0, 398.3203125], [307.5, 398.3203125]]}, {"title": "E. Hyper-Parameters and Experimental De-\ntails", "heading_level": null, "page_id": 11, "polygon": [[307.5, 597.0], [546.75, 597.0], [546.75, 621.45703125], [307.5, 621.45703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 64], ["Text", 7], ["SectionHeader", 3], ["Picture", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5443, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 82], ["Text", 7], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 625], ["Line", 129], ["TableCell", 52], ["Text", 9], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2813, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 478], ["Line", 85], ["TextInlineMath", 6], ["Equation", 2], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 730, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1054], ["TableCell", 351], ["Line", 102], ["Reference", 5], ["Table", 4], ["Caption", 4], ["TableGroup", 4], ["Text", 3], ["TextInlineMath", 2], ["Footnote", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 30077, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 62], ["Text", 8], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2207, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 74], ["Text", 6], ["Caption", 4], ["TextInlineMath", 3], ["SectionHeader", 3], ["Figure", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1348, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 60], ["Text", 9], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1183, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 120], ["ListItem", 38], ["Reference", 38], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 120], ["ListItem", 37], ["Reference", 37], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 4], ["ListItem", 1], ["<PERSON>Footer", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["TableCell", 242], ["Line", 103], ["Text", 12], ["SectionHeader", 5], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4313, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 635], ["TableCell", 247], ["Line", 71], ["Reference", 5], ["Caption", 4], ["Table", 3], ["TableGroup", 3], ["Text", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 13563, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 16], ["Text", 4], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Generalizing_Dataset_Distillation_via_Deep_Generative_Prior"}