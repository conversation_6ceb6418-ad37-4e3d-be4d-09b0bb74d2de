{"table_of_contents": [{"title": "GSDD: Generative Space Dataset Distillation for Image Super-resolution", "heading_level": null, "page_id": 0, "polygon": [[81.1318359375, 97.69482421875], [530.25, 97.69482421875], [530.25, 110.98828125], [81.1318359375, 110.98828125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[153.75, 217.5], [193.939453125, 217.5], [193.939453125, 227.390625], [153.75, 227.390625]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[138.75, 500.25], [207.0, 500.25], [207.0, 511.2421875], [138.75, 511.2421875]]}, {"title": "Related Works", "heading_level": null, "page_id": 1, "polygon": [[133.5, 494.61328125], [212.16796875, 494.61328125], [212.16796875, 506.21484375], [133.5, 506.21484375]]}, {"title": "Dataset Distillation (DD)", "heading_level": null, "page_id": 1, "polygon": [[52.5, 512.7890625], [170.25, 512.7890625], [170.25, 523.6171875], [52.5, 523.6171875]]}, {"title": "Single Image Super-Resolution (SISR)", "heading_level": null, "page_id": 1, "polygon": [[318.0, 96.63134765625], [498.75, 96.63134765625], [498.75, 107.89453125], [318.0, 107.89453125]]}, {"title": "Remarks", "heading_level": null, "page_id": 1, "polygon": [[318.0, 348.626953125], [363.0, 348.626953125], [363.0, 359.841796875], [318.0, 359.841796875]]}, {"title": "Proposed Method", "heading_level": null, "page_id": 1, "polygon": [[392.25, 579.3046875], [485.25, 579.3046875], [485.25, 591.75], [392.25, 591.75]]}, {"title": "Problem Formulation", "heading_level": null, "page_id": 2, "polygon": [[52.5, 236.25], [156.0, 236.25], [156.0, 247.306640625], [52.5, 247.306640625]]}, {"title": "GAN-Inversion", "heading_level": null, "page_id": 2, "polygon": [[52.5, 385.5], [126.75, 385.5], [126.75, 396.38671875], [52.5, 396.38671875]]}, {"title": "Pre-trained SISR Generator G", "heading_level": null, "page_id": 2, "polygon": [[317.35546875, 334.5], [463.5, 334.5], [463.5, 344.56640625], [317.35546875, 344.56640625]]}, {"title": "Distribution Matching Optimization", "heading_level": null, "page_id": 2, "polygon": [[318.0, 525.0], [489.0, 525.0], [489.0, 534.83203125], [318.0, 534.83203125]]}, {"title": "Training Data Distillation", "heading_level": null, "page_id": 3, "polygon": [[52.5, 369.0], [174.0, 369.0], [174.0, 379.37109375], [52.5, 379.37109375]]}, {"title": "Regularization Term R", "heading_level": null, "page_id": 3, "polygon": [[318.0, 354.75], [428.25, 354.75], [428.25, 364.869140625], [318.0, 364.869140625]]}, {"title": "Experiments", "heading_level": null, "page_id": 3, "polygon": [[403.41796875, 585.75], [471.75, 585.75], [471.75, 597.09375], [403.41796875, 597.09375]]}, {"title": "Dataset & Evaluation", "heading_level": null, "page_id": 3, "polygon": [[318.0, 602.25], [422.244140625, 602.25], [422.244140625, 611.7890625], [318.0, 611.7890625]]}, {"title": "Training Details", "heading_level": null, "page_id": 4, "polygon": [[52.5, 343.79296875], [130.5, 343.79296875], [130.5, 355.0078125], [52.5, 355.0078125]]}, {"title": "Comparison to the State-of-the-art", "heading_level": null, "page_id": 4, "polygon": [[52.5, 487.65234375], [216.75, 487.65234375], [216.75, 498.48046875], [52.5, 498.48046875]]}, {"title": "Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[52.5, 422.25], [125.25, 422.25], [125.25, 433.125], [52.5, 433.125]]}, {"title": "Conclusion", "heading_level": null, "page_id": 6, "polygon": [[409.095703125, 537.0], [468.0, 537.0], [468.0, 546.43359375], [409.095703125, 546.43359375]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 7, "polygon": [[125.25, 54.09228515625], [221.73046875, 54.09228515625], [221.73046875, 65.59716796875], [125.25, 65.59716796875]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[144.0, 123.0], [204.099609375, 123.0], [204.099609375, 134.0947265625], [144.0, 134.0947265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 90], ["Text", 7], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 113], ["Text", 7], ["SectionHeader", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 111], ["Text", 6], ["Equation", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 628], ["TableCell", 332], ["Line", 97], ["Equation", 4], ["SectionHeader", 4], ["Text", 4], ["TextInlineMath", 3], ["Caption", 2], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 311], ["Line", 102], ["TableCell", 100], ["Text", 8], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8293, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 59], ["TableCell", 54], ["Caption", 3], ["Text", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4367, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["Line", 161], ["Text", 4], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 113], ["Text", 26], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 112], ["Text", 27], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Generative Space Dataset Distillation for Image Super-resolution"}