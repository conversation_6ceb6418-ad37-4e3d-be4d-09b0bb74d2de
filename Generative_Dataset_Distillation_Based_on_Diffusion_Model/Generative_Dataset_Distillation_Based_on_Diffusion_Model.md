<span id="page-0-0"></span>

# Generative Dataset Distillation Based on Diffusion Model

Duo Su<sup>†1</sup>©[,](https://orcid.org/0000-0001-7359-1081) <PERSON><PERSON><PERSON><sup>†2</sup><PERSON>, <PERSON><PERSON><sup>\*3</sup><PERSON>, <PERSON><sup>3</sup>◎, <PERSON><PERSON><sup>4,5</sup>◎, <PERSON><PERSON><PERSON><sup>3</sup><sup>®</sup>[,](https://orcid.org/0000-0001-5332-8112) and <PERSON><PERSON><sup>[3](https://orcid.org/0000-0003-1496-1761)</sup>

> $1$ <NAME_EMAIL> <sup>2</sup> Hong Kong University of Science <NAME_EMAIL> <sup>3</sup> Hokkaido University {guang,togo,ogawa,mhaseyama}@lmd.ist.hokudai.ac.jp  $^4\,$  <NAME_EMAIL> <sup>5</sup> Technical University <NAME_EMAIL>

Abstract. This paper presents our method for the generative track of [The First Dataset Distillation Challenge at ECCV 2024.](https://www.dd-challenge.com) Since the diffusion model has become the mainstay of generative models because of its high-quality generative effects, we focus on distillation methods based on the diffusion model. Considering that the track can only generate a fixed number of images in 10 minutes using a generative model for CIFAR-100 and Tiny-ImageNet datasets, we need to use a generative model that can generate images at high speed. In this study, we proposed a novel generative dataset distillation method based on Stable Diffusion. Specifically, we use the SDXL-Turbo model which can generate images at high speed and quality. Compared to other diffusion models that can only generate images per class  $(IPC) = 1$ , our method can achieve an IPC  $= 10$  for Tiny-ImageNet and an IPC  $= 20$  for CIFAR-100, respectively. Additionally, to generate high-quality distilled datasets for CIFAR-100 and Tiny-ImageNet, we use the class information as text prompts and post data augmentation for the SDXL-Turbo model. Experimental results show the effectiveness of the proposed method, and we achieved third place in the generative track of the ECCV 2024 DD Challenge. Codes are available at <https://github.com/Guang000/BANKO> .

Keywords: Dataset Distillation · Generative Model · Stable Diffusion

# 1 Background

Deep learning has achieved remarkable success, driven by advancements in powerful computational resources. Particularly since the rise of Transformers  $[2, 3, 3]$  $[2, 3, 3]$  $[2, 3, 3]$  $[2, 3, 3]$ 

<sup>†</sup> Equal Contribution

<sup>⋆</sup> Team Lead, Corresponding Author

<span id="page-1-0"></span>

### 2 D. Su, J. Hou and G. Li et al.

[28,](#page-9-0) [49\]](#page-10-0), the scale of models and the amount of data required for their training has increased rapidly. However, this rapid growth has led to a bottleneck in deep learning, where the ever-increasing data volume outpaces the available computational resources. To address this issue, the development of data-efficient learning techniques [\[31,](#page-9-1) [45\]](#page-10-1) has become increasingly important. Among these, dataset distillation (DD) has emerged as a promising approach to tackle the issue of large-scale data. Dataset distillation is a method that synthesizes a small, highly informative dataset by summarizing a large amount of real data [\[52\]](#page-10-2). Models trained on this distilled dataset can achieve generalization performance comparable to those trained on the full real dataset. This approach offers an efficient solution, particularly in managing and training large datasets [\[8,](#page-8-0) [47,](#page-10-3) [57\]](#page-11-0).

The significant advancements in generative models have led to the development of new approaches in dataset distillation  $[6, 12, 47]$  $[6, 12, 47]$  $[6, 12, 47]$  $[6, 12, 47]$  $[6, 12, 47]$ . Generative models can encode critical information from the dataset and use it to create synthetic datasets. This approach is noteworthy due to its flexibility, allowing for various manipulations of the synthetic dataset [\[59\]](#page-11-1), and is considered a promising direction in the evolution of dataset distillation techniques. One critical factor affecting the test accuracy of distilled datasets is the distillation budget. This budget is defined by the concept of images per class (IPC), which constrains the size of the distilled dataset. Typically, the distilled dataset is designed to have the same number of classes as the target dataset [\[35,](#page-9-2)[61\]](#page-11-2). Compared with other traditional dataset distillation methods, generative dataset distillation offers superior flexibility in manipulating the IPC for distilled image generation  $[33, 47]$  $[33, 47]$  $[33, 47]$ . This flexibility suggests that the synthetic datasets produced through this method could adapt to a broader range of tasks and conditions.

In this paper, we propose a new generative dataset distillation based on Stable Diffusion, developed for the generative track of The First Dataset Distillation Challenge at ECCV 2024. Our method leverages the SDXL-Turbo diffusion model [\[44\]](#page-10-4), which was selected for its superior speed and quality in image generation. Our method significantly improves efficiency in response to the challenge's time constraints, achieving an IPC of 10 for Tiny-ImageNet and 20 for CIFAR-100, compared to the typical IPC of 1 in other diffusion models. The method also employs class-specific text prompts and post data augmentation techniques to enhance the quality of the distilled datasets. Our experimental results demonstrate the effectiveness of this approach, which earned third place in the competition.

## 2 Related Work

### 2.1 Dataset Distillation

Dataset distillation was initially proposed by [\[52\]](#page-10-2). It aims to synthesize a smaller dataset based on the original dataset while ensuring that the results of model training on the synthetic data are similar to those trained on the original dataset [\[25\]](#page-9-4). Existing dataset distillation algorithms mainly include kernel-based and matchingbased methods [\[20,](#page-8-3) [39,](#page-10-5) [58\]](#page-11-3).

<span id="page-2-2"></span>Kernel-based methods, which utilize ridge regression as an optimization objective, were first introduced by [\[34,](#page-9-5)[35\]](#page-9-2). These methods employ the Neural Tangent Kernel (NTK) [\[15\]](#page-8-4) to generate synthetic datasets. In contrast, RFAD [\[29\]](#page-9-6) leverages the Empirical Neural Network Gaussian Process (NNGP) kernel for dataset distillation, resulting in more efficient performance in classification tasks. FRePo [\[64\]](#page-11-4) enhances this approach by replacing NTKs with network features, leading to a more effective data generation method.

Matching-based methods for generating synthetic data encompass approaches based on parameter matching, performance matching, and distribution matching. Matching gradients by training on both synthetic and original data is a widely used and highly effective method in dataset distillation. For example, approaches like DC [\[61\]](#page-11-2), DSA [\[60\]](#page-11-5), IDC [\[16\]](#page-8-5), and DCC [\[18\]](#page-8-6) employ this technique. Similar to gradient matching, methods like MTT [\[4\]](#page-7-2), IADD [\[24\]](#page-9-7), SelMatch [\[19\]](#page-8-7), and ATT [\[27\]](#page-9-8) match parameters by minimizing the loss over the training trajec-tory on synthetic and original data. CAFE [\[51\]](#page-10-6), IDM [\[63\]](#page-11-6), and the method in [\[9\]](#page-8-8) utilize feature matching, ensuring that the model produces similar deep features on both generated and original data to achieve comparable performance. Similarly, [\[43\]](#page-10-7) matches the attention between two datasets, applying attention-based methods for dataset distillation. In recent years, the development of generative models has introduced new paradigms for dataset distillation.

Due to its potential applications [\[10\]](#page-8-9), especially in training large models, dataset distillation has been successfully explored across various domains, including healthcare  $[21-23]$  $[21-23]$ , fashion  $[5,7,13]$  $[5,7,13]$  $[5,7,13]$ , and trustworthy AI  $[46,48,53,54,65]$  $[46,48,53,54,65]$  $[46,48,53,54,65]$  $[46,48,53,54,65]$  $[46,48,53,54,65]$ . In computer vision, dataset distillation also provides particularly significant advantages, prompting the organization of multiple workshops and challenges on the topic, such as those at CVPR 2024  $\frac{1}{3}$  and ECCV 2024  $\frac{1}{3}$ .

### 2.2 Diffusion Models

Generative models have experienced rapid advancements in recent years, leading to their successful application in various industries, with prominent examples including Imagen [\[41\]](#page-10-12), DALL·E 2 [\[37\]](#page-9-10), Stable Diffusion [\[38\]](#page-9-11), and Adobe Firefly. Compared to other generative approaches like GANs [\[11\]](#page-8-13) or VAEs [\[17\]](#page-8-14), diffusion models often demonstrate more stable and versatile generative capabilities. These models operate by gradually adding random noise to real data and then learning to reverse this process, thereby recovering the original data distribution.

Particularly in the field of computer vision, diffusion models have been successfully applied to a wide range of tasks [\[55\]](#page-11-8). For instance, SR3 [\[42\]](#page-10-13) and CDM [\[14\]](#page-8-15) leverage diffusion models to enhance image resolution. RePaint [\[30\]](#page-9-12) and Palette [\[40\]](#page-10-14) utilize denoising techniques for image restoration. Additionally, RVD [\[56\]](#page-11-9) employs diffusion models for future frame prediction, thereby improving video compression performance, and DDPM-CD [\[1\]](#page-7-4) uses them for anomaly detection in remote sensing images. Unlike these applications, our approach aims

<span id="page-2-0"></span><sup>§</sup> <https://sites.google.com/view/dd-cvpr2024/home>

<span id="page-2-1"></span><sup>¶</sup> <https://www.dd-challenge.com>

<span id="page-3-0"></span>to harness the power of diffusion models to achieve better performance in dataset distillation.

### 2.3 Generative Dataset Distillation

A few studies have already begun exploring the potential integration of generative models in dataset distillation, with examples such as KFS [\[18\]](#page-8-6), IT-GAN [\[62\]](#page-11-10) and DiM [\[26,](#page-9-13) [50\]](#page-10-15). The inherent ability of generative models to create new data aligns well with the concept of synthesizing datasets in dataset distillation. KFS improves training efficiency by encoding features in the latent space and using multiple decoders to generate additional new samples. IT-GAN, on the other hand, generates more informative training samples through a GAN model, enabling faster and more effective model training, and thereby addressing the challenges faced in dataset distillation. DiM stores the information of target images into a generative model, enabling the synthesis of new datasets. In parallel with recent works in [\[12,](#page-8-2) [33,](#page-9-3) [47\]](#page-10-3), our approach also integrates an effective variant of the Stable Diffusion model into the dataset distillation framework, resulting in a more efficient method for distilled image synthesis.

## 3 Methods

Our goal is to distill the dataset with limited time and resources. We leverage the prior knowledge and controllability of the generative models to develop a dataset distillation method, which does not require any parameter optimization. The method is capable of distilling a large number of images in a short period while maintaining high sampling fidelity. Theoretically, the method is general as it can distill datasets with arbitrary sizes. The core of our approach lies in the use of SDXL-Turbo, a diffusion model trained with adversarial diffusion distillation (ADD) strategy. SDXL-Turbo synthesizes high-fidelity images with only 1-4 steps during the inference time.

### 3.1 Overall Procedure

As illustrated in Fig. [1,](#page-4-0) we employ the Text2Image (T2I) pipeline within SDXL-Turbo, where the category labels from the original dataset are formatted as the prompts. All of the distilled images are obtained after one-step sampling.

### 3.2 Fast and High-Fidelity Dataset Distillation

The real-time sampling of the SDXL-Turbo benefits from its ADD training strategy, which includes two objectives: (1) the adversarial loss involving images synthesized by the student model and the original images  $(x_0)$ , and  $(2)$  distillation loss compares the teacher-student outputs. Adversarial training ensures the generated images align with the original manifolds, mitigating the blurriness and artifacts inherent in traditional distillation methods. Distillation training

<span id="page-4-1"></span><span id="page-4-0"></span>Image /page/4/Figure/1 description: This is a diagram illustrating a process involving noise, text labels, SDXL-Turbo, synthetic images, and post-data augmentation. The process begins with 'Noise' input, which is then processed by 'SDXL-Turbo' along with 'Text Labels'. The output of this stage is a collection of 'Synthetic Images', including a goldfish, a salamander, a frog, a beaver, a dolphin, and a seal. Finally, these synthetic images undergo 'Post Data Augmentation', represented by icons for rotation, cropping, and other transformations.

Fig. 1: The pipeline of the proposed method.

effectively leverages the substantial prior knowledge embedded in the pretrained diffusion model while maintaining compositionality. Unlike other one-step synthesis methods (e.g., GANs, Latent Consistency Models), SDXL-Turbo retains the iterative optimization capability of diffusion models.

In adversarial training, ADD incorporates the learnable discriminator heads  $\mathcal{D}_{\phi,k}$  with a pretrained Vision Transformer (ViT) feature extractor  $F_k$ . For a generated sample  $\hat{x}_{\theta}(x_s, s)$  and discriminator heads  $\mathcal{D}_{\phi,k}$ , the loss is determined by

$$
\mathcal{L}_{\text{adv}}^{\text{G}}\left(\hat{x}_{\theta}\left(x_{s}, s\right), \phi\right) = -\mathbb{E}_{s, \epsilon, x_{0}}\left[\sum_{k} \mathcal{D}_{\phi, k}\left(F_{k}\left(\hat{x}_{\theta}\left(x_{s}, s\right)\right)\right)\right],\tag{1}
$$

whereas the timestep  $s \in T_{student} = \{\tau_1, \ldots, \tau_n\}$ . The discriminator is defined according to the following hinge loss:

$$
LadvD(x^θ(xs,s),φ)=Ex0[∑kmax(0,1-Dφ,k(Fk(x0)))+γR1(φ)]+Ex^θ[∑kmax(0,1+Dφ,k(Fk(x^θ)))],
\mathcal{L}_{adv}^{D} \left( \hat{x}_{\theta} \left( x_{s}, s \right), \phi \right) = \mathbb{E}_{x_{0}} \left[ \sum_{k} \max \left( 0, 1 - \mathcal{D}_{\phi,k} \left( F_{k} \left( x_{0} \right) \right) \right) + \gamma R1(\phi) \right] + \mathbb{E}_{\hat{x}_{\theta}} \left[ \sum_{k} \max \left( 0, 1 + \mathcal{D}_{\phi,k} \left( F_{k} \left( \hat{x}_{\theta} \right) \right) \right) \right],
$$
 $(2)$ 

where  $R1$  represents the R1 gradient penalty [\[32\]](#page-9-14).

The distillation training mainly focuses on the weighted L2 distance between the outputs of the teacher  $(\hat{x}_{\psi})$  and student  $(\hat{x}_{\theta})$  models. Thus, the loss is calculated by

$$
\mathcal{L}_{distill}(\hat{x}_{\theta}(x_s, s), \psi) = \mathbb{E}_{t, \epsilon'}[c(t) d(\hat{x}_{\theta}, \hat{x}_{\psi}(s g(\hat{x}_{\theta, t}); t))],
$$
\n(3)

where sg stands for stop-gradient. The weighting function  $c(t)$  has two choices, exponential weighting or score distillation sampling (SDS) weighting [\[36\]](#page-9-15).

### 3.3 T2I Dataset Distillation

Considering the dataset, category labels provide crucial information that should not be overlooked. These labels can be embedded as conditions in diffusion models to guide the image generation process. During training, the discriminator of SDXL-Turbo employs a projector to extract such conditional information. Consequently, the trained diffusion model can leverage text labels as prompt information.

### 3.4 Post Data Augmentation

Since the Dataset Distillation challenge uses a trivial ConvNet classifier for evaluation, we aim to enhance the information richness of the distilled images without increasing computational complexity. Post data augmentation (PDA) is an effective approach, as it only introduces marginal overhead. Specifically, after synthesizing images with the diffusion model, we directly apply several augmentation methods such as image cropping, rotation, or flipping to these images. Please refer to Section [4.1](#page-5-0) for detailed PDA settings.

### 4 Experiments

<span id="page-5-0"></span>

#### 4.1 Implementation Details

The proposed method is based on SDXL-Turbo, an effective variant of the Stable Diffusion model. The pretrained checkpoints come from the official Huggingface repository. For boosting the generation of more high-fidelity images, the model utilizes half-precision (float16) tensors. The prompts consist of the corresponding category labels as mentioned. These labels are derived from the ordered training datasets and saved in the corresponding lists. The hyper-parameters are set to num\_inference\_steps=1 and guidance\_scale=0. To maximize the diversity of the distilled images, Post Data Augmentation is applied after the sampling process. The specific parameters used for augmentations are as follows:

- RandomCrop(width=size, height=size)
- HorizontalFlip(p=0.8)
- VerticalFlip(p=0.8)
- RandomBrightnessContrast(p=0.5)
- Rotate(limit=60, p=0.8)
- RandomGamma(p=0.5)

Considering the generation speed along with the time required for model loading and image saving, the final IPC is set to 50 for Tiny-ImageNet and 100 for CIFAR-100. The environmental requirements of our experiments are listed as follows:

- Python  $\geq 3.9$
- $-$  Pytorch  $\geq 1.12.1$
- Torchvision  $\geq 0.13.1$
- $-$  Diffusers  $== 0.29.2$

We follow the [official repository](https://github.com/DataDistillation/ECCV2024-Dataset-Distillation-Challenge) for evaluation. The evaluation network, ConvNetD3-W128, will be trained for 1000 epochs. The learning rate, momentum, and weight decay are set to 0.01, 0.9, and 0.0005 respectively. For assessing the robustness of the method, each dataset is evaluated three times.

| Model            | Tiny-ImageNet |                     | CIFAR-100 |                     |
|------------------|---------------|---------------------|-----------|---------------------|
|                  | IPC           | Accuracy            | IPC       | Accuracy            |
| SDXL-Turbo       | 10            | 0.0437 $\pm$ 0.0012 | 20        | 0.0097 $\pm$ 0.0010 |
| SDXL-Turbo + PDA | 50            | 0.0478 $\pm$ 0.0018 | 100       | 0.0137 $\pm$ 0.0005 |

<span id="page-6-0"></span>Table 1: The model performance on the validation set with different components.

<span id="page-6-1"></span>Image /page/6/Picture/3 description: The image displays two grids of small square images, each grid representing a different dataset. The left grid is labeled "CIFAR-100" and contains images of animals such as beavers, dolphins, lions, seals, sharks, and fish. The right grid is labeled "Tiny-ImageNet" and contains images of various creatures including fish, salamanders, frogs, snails, crabs, ants, and spiders. Both grids are arranged in a 5x6 format, showcasing a variety of animal subjects.

Fig. 2: The generated images of the top-10 classes in Tiny-ImageNet and CIFAR-100. The first row displays the images produced by the diffusion model, and the subsequent four rows represent the PDA images.

#### 4.2 Experimental Results

According to Table [1,](#page-6-0) PDA directly increases the number of IPC and significantly improves the distilled performance. The Accuracy increased by 0.0041 on Tiny-ImageNet, from 0.0437 to 0.0478, while in CIFAR-100, it increased by 0.0040. The top-10 category visualization results are displayed in Fig. [2.](#page-6-1) The first row features images generated by the diffusion model, noted for their high fidelity and realism The remaining four rows illustrate the results of applying data augmentation to these images, which increases their diversity and improves the quality of the distilled dataset.

#### 5 Discussion

#### 5.1 Distribution Discrepancy

The matching strategies in traditional dataset distillation methods ensure that the distribution of the distilled dataset aligns with the original dataset. However, when it comes to the generative models, significant discrepancies may arise between their distributions. Some recent works indicate that images generated by the diffusion model perform well on the large-scale ImageNet-1K. This success may be attributed to two factors: (1) the images closely resemble the real-world scenes and (2) the resolution of ImageNet-1K is similar to the generative model. In other words, ImageNet-1K has a resolution of  $224 \times 224$  and the diffusion model generates images at  $256 \times 256$ .

8 D. Su, J. Hou and G. Li et al.

When the dataset is set to Tiny-ImageNet, the effectiveness of dataset distillation begins to decline. Tiny-ImageNet is recognized as a subset of ImageNet-1K, thus, their data distributions are similar. However, the resolution decreases to  $64 \times 64$ , resulting in significant information loss. When the dataset is further changed to CIFAR-100, the performance of generative model dataset distillation becomes unsatisfactory. This is due to a substantial distribution discrepancy between CIFAR-100 and the generated models, as well as a considerable gap in resolution. The Common methods, such as fine-tuning or modifying text prompts, have not effectively addressed this issue.

##### 6 Conclusion

We have proposed a novel generative dataset distillation method based on diffusion model. Specifically, we use the SDXL-Turbo model which can generate images at high speed and quality. As a result, our method can generate distilled datasets with large IPC. Furthermore, to obtain high-quality distilled CIFAR-100 and Tiny-ImageNet datasets, we utilize the class information as text prompts and post-augmentation for the proposed method. The top performance during the challenge shows the superiority of our method. In the future, we want to develop more effective distillation techniques with generative models across different datasets.

###### Acknowledgements

This study was supported by JSPS KAKENHI Grant Numbers JP23K21676, JP23K11141, and JP24K23849.

###### References

- <span id="page-7-4"></span>1. Bandara, W., Nair, N.G., Patel, V.: Ddpm-cd: Denoising diffusion probabilistic models as feature extractors for change detection. arXiv preprint arXiv:2206.11892 (2022) [3](#page-2-2)
- <span id="page-7-0"></span>2. Brown, T.B.: Language models are few-shot learners. arXiv preprint ArXiv:2005.14165 (2020) [1](#page-0-0)
- <span id="page-7-1"></span>3. Carion, N., Massa, F., Synnaeve, G., Usunier, N., Kirillov, A., Zagoruyko, S.: End-to-end object detection with transformers. In: Proceedings of the European Conference on Computer Vision (ECCV). pp. 213–229 (2020) [1](#page-0-0)
- <span id="page-7-2"></span>4. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 10718–10727 (2022) [3](#page-2-2)
- <span id="page-7-3"></span>5. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Wearable ImageNet: Synthesizing tileable textures via dataset distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), Workshop. pp. 2278–2282 (2022) [3](#page-2-2)

- <span id="page-8-1"></span>6. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Generalizing dataset distillation via deep generative prior. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 3739–3748 (2023) [2](#page-1-0)
- <span id="page-8-11"></span>7. Chen, Y., Wu, Z., Shen, Z., Jia, J.: Learning from designers: Fashion compatibility analysis via dataset distillation. In: Proceedings of the IEEE International Conference on Image Processing (ICIP). pp. 856–860 (2022) [3](#page-2-2)
- <span id="page-8-0"></span>8. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. In: Proceedings of the International Conference on Machine Learning (ICML). pp. 6565–6590 (2023) [2](#page-1-0)
- <span id="page-8-8"></span>9. Deng, W., Li, W., Ding, T., Wang, L., Zhang, H., Huang, K., Huo, J., Gao, Y.: Exploiting inter-sample and inter-feature relations in dataset distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 17057–17066 (2024) [3](#page-2-2)
- <span id="page-8-9"></span>10. Geng, Jiahui andg Chen, Z., Wang, Y., Woisetschlaeger, H., Schimmler, S., Mayer, R., Zhao, Z., Rong, C.: A survey on dataset distillation: Approaches, applications and future directions. In: Proceedings of the International Joint Conference on Artificial Intelligence (IJCAI) (2023) [3](#page-2-2)
- <span id="page-8-13"></span>11. Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., Bengio, Y.: Generative adversarial nets. Advances in Neural Information Processing Systems 27 (2014) [3](#page-2-2)
- <span id="page-8-2"></span>12. Gu, J., Vahidian, S., Kungurtsev, V., Wang, H., Jiang, W., You, Y., Chen, Y.: Efficient dataset distillation via minimax diffusion. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 15793– 15803 (2024) [2,](#page-1-0) [4](#page-3-0)
- <span id="page-8-12"></span>13. Guan, H., Zhao, X., Wang, Z., Li, Z., Kempe, J.: Discovering galaxy features via dataset distillation. Advances in Neural Information Processing Systems (NeurIPS), Workshop (2023) [3](#page-2-2)
- <span id="page-8-15"></span>14. Ho, J., Saharia, C., Chan, W., Fleet, D.J., Norouzi, M., Salimans, T.: Cascaded diffusion models for high fidelity image generation. Journal of Machine Learning Research 23(47), 1–33 (2022) [3](#page-2-2)
- <span id="page-8-4"></span>15. Jacot, A., Gabriel, F., Hongler, C.: Neural tangent kernel: Convergence and generalization in neural networks. Advances in Neural Information Processing Systems 31 (2018) [3](#page-2-2)
- <span id="page-8-5"></span>16. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: Proceedings of the International Conference on Machine Learning (ICML). pp. 11102–11118 (2022) [3](#page-2-2)
- <span id="page-8-14"></span>17. Kingma, D.P., Welling, M.: Auto-encoding variational bayes. arXiv preprint arXiv:1312.6114 (2013) [3](#page-2-2)
- <span id="page-8-6"></span>18. Lee, H.B., Lee, D.B., Hwang, S.J.: Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494 (2022) [3,](#page-2-2) [4](#page-3-0)
- <span id="page-8-7"></span>19. Lee, Y., Chung, H.W.: SelMatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In: Proceedings of the International Conference on Machine Learning (ICML) (2024) [3](#page-2-2)
- <span id="page-8-3"></span>20. Lei, S., Tao, D.: A comprehensive survey to dataset distillation. IEEE Transactions on Pattern Analysis and Machine Intelligence 46(1), 17–32 (2023) [2](#page-1-0)
- <span id="page-8-10"></span>21. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Soft-label anonymous gastric x-ray image distillation. In: Proceedings of the IEEE International Conference on Image Processing (ICIP). pp. 305–309 (2020) [3](#page-2-2)

- 10 D. Su, J. Hou and G. Li et al.
- 22. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. Computer Methods and Programs in Biomedicine 227, 107189 (2022) [3](#page-2-2)
- <span id="page-9-9"></span>23. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Dataset distillation for medical dataset sharing. In: Proceedings of the AAAI Conference on Artificial Intelligence (AAAI), Workshop. pp. 1–6 (2023) [3](#page-2-2)
- <span id="page-9-7"></span>24. Li, G., Togo, R., Ogawa, T., Haseyama, M.: Importance-aware adaptive dataset distillation. Neural Networks 172 (2024) [3](#page-2-2)
- <span id="page-9-4"></span>25. Li, G., Zhao, B., Wang, T.: Awesome dataset distillation. [https://github.com/](https://github.com/Guang000/Awesome-Dataset-Distillation) [Guang000/Awesome-Dataset-Distillation](https://github.com/Guang000/Awesome-Dataset-Distillation) (2022) [2](#page-1-0)
- <span id="page-9-13"></span>26. Li, L., Li, G., Togo, R., Maeda, K., Ogawa, T., Haseyama, M.: Generative dataset distillation: Balancing global structure and local details. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), Workshop. pp. 7664–7671 (2024) [4](#page-3-0)
- <span id="page-9-8"></span>27. Liu, D., Gu, J., Cao, H., Trinitis, C., Schulz, M.: Dataset distillation by automatic training trajectories. In: Proceedings of the European Conference on Computer Vision (ECCV) (2024) [3](#page-2-2)
- <span id="page-9-0"></span>28. Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., Guo, B.: Swin transformer: Hierarchical vision transformer using shifted windows. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 10012– 10022 (2021) [1](#page-0-0)
- <span id="page-9-6"></span>29. Loo, N., Hasani, R., Amini, A., Rus, D.: Efficient dataset distillation using random feature approximation. Advances in Neural Information Processing Systems (NeurIPS) (2022) [3](#page-2-2)
- <span id="page-9-12"></span>30. Lugmayr, A., Danelljan, M., Romero, A., Yu, F., Timofte, R., Van Gool, L.: Repaint: Inpainting using denoising diffusion probabilistic models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 11461–11471 (2022) [3](#page-2-2)
- <span id="page-9-1"></span>31. Maclaurin, D., Duvenaud, D., Adams, R.: Gradient-based hyperparameter optimization through reversible learning. In: Proceedings of the International Conference on Machine Learning (ICML). pp. 2113–2122 (2015) [2](#page-1-0)
- <span id="page-9-14"></span>32. Mescheder, L., Geiger, A., Nowozin, S.: Which training methods for GANs do actually converge? In: Proceedings of the International Conference on Machine Learning (ICML). pp. 3481–3490 (2018) [5](#page-4-1)
- <span id="page-9-3"></span>33. Moser, B.B., Raue, F., Palacio, S., Frolov, S., Dengel, A.: Latent dataset distillation with diffusion models. arXiv preprint arXiv:2403.03881 (2024) [2,](#page-1-0) [4](#page-3-0)
- <span id="page-9-5"></span>34. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression. In: Proceedings of the International Conference on Learning Representations (ICLR) (2021) [3](#page-2-2)
- <span id="page-9-2"></span>35. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34 (2021) [2,](#page-1-0) [3](#page-2-2)
- <span id="page-9-15"></span>36. Poole, B., Jain, A., Barron, J.T., Mildenhall, B.: Dreamfusion: Text-to-3d using 2d diffusion. arXiv (2022) [5](#page-4-1)
- <span id="page-9-10"></span>37. Ramesh, A., Dhariwal, P., Nichol, A., Chu, C., Chen, M.: Hierarchical textconditional image generation with clip latents. arXiv preprint arXiv:2204.06125  $1(2)$ , [3](#page-2-2) (2022) 3
- <span id="page-9-11"></span>38. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 10684– 10695 (2022) [3](#page-2-2)

- <span id="page-10-5"></span>39. Sachdeva, N., McAuley, J.: Data distillation: A survey. Transactions on Machine Learning Research (2023) [2](#page-1-0)
- <span id="page-10-14"></span>40. Saharia, C., Chan, W., Chang, H., Lee, C., Ho, J., Salimans, T., Fleet, D., Norouzi, M.: Palette: Image-to-image diffusion models. In: Proceedings of the ACM SIG-GRAPH. pp. 1–10 (2022) [3](#page-2-2)
- <span id="page-10-12"></span>41. Saharia, C., Chan, W., Saxena, S., Li, L., Whang, J., Denton, E.L., Ghasemipour, K., Gontijo Lopes, R., Karagol Ayan, B., Salimans, T., et al.: Photorealistic textto-image diffusion models with deep language understanding. Advances in Neural Information Processing Systems 35, 36479–36494 (2022) [3](#page-2-2)
- <span id="page-10-13"></span>42. Saharia, C., Ho, J., Chan, W., Salimans, T., Fleet, D.J., Norouzi, M.: Image superresolution via iterative refinement. IEEE Transactions on Pattern Analysis and Machine Intelligence 45(4), 4713–4726 (2022) [3](#page-2-2)
- <span id="page-10-7"></span>43. Sajedi, A., Khaki, S., Amjadian, E., Liu, L.Z., Lawryshyn, Y.A., Plataniotis, K.N.: Datadam: Efficient dataset distillation with attention matching. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 17097– 17107 (2023) [3](#page-2-2)
- <span id="page-10-4"></span>44. Sauer, A., Lorenz, D., Blattmann, A., Rombach, R.: Adversarial diffusion distillation. arXiv preprint arXiv:2311.17042 (2023) [2](#page-1-0)
- <span id="page-10-1"></span>45. Sinha, S., Roth, K., Goyal, A., Ghassemi, M., Akata, Z., Larochelle, H., Garg, A.: Uniform priors for data-efficient learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4017–4028 (2022) [2](#page-1-0)
- <span id="page-10-8"></span>46. Song, R., Liu, D., Chen, D.Z., Festag, A., Trinitis, C., Schulz, M., Knoll, A.: Federated learning via decentralized dataset distillation in resource-constrained edge environments. In: Proceedings of the International Joint Conference on Neural Networks (IJCNN). pp. 1–10 (2023) [3](#page-2-2)
- <span id="page-10-3"></span>47. Su, D., Hou, J., Gao, W., Tian, Y., Tang, B.: D4M: Dataset distillation via disentangled diffusion model. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 5809–5818 (2024) [2,](#page-1-0) [4](#page-3-0)
- <span id="page-10-9"></span>48. Tsilivis, N., Su, J., Kempe, J.: Can we achieve robustness from data alone? In: Proceedings of the International Conference on Machine Learning (ICML), Workshop (2022) [3](#page-2-2)
- <span id="page-10-0"></span>49. Vaswani, A.: Attention is all you need. arXiv preprint arXiv:1706.03762 (2017) [1](#page-0-0)
- <span id="page-10-15"></span>50. Wang, K., Gu, J., Zhou, D., Zhu, Z., Jiang, W., You, Y.: DiM: Distilling dataset into generative model. arXiv preprint arXiv:2303.04707 (2023) [4](#page-3-0)
- <span id="page-10-6"></span>51. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: CAFE: Learning to condense dataset by aligning features. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 12196–12205 (2022) [3](#page-2-2)
- <span id="page-10-2"></span>52. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018) [2](#page-1-0)
- <span id="page-10-10"></span>53. Wang, Y., Fu, H., Kanagavelu, R., Wei, Q., Liu, Y., Goh, R.S.M.: An aggregationfree federated learning for tackling data heterogeneity. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 26233–26242 (2024) [3](#page-2-2)
- <span id="page-10-11"></span>54. Xiong, Y., Wang, R., Cheng, M., Yu, F., Hsieh, C.J.: FedDM: Iterative distribution matching for communication-efficient federated learning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 16323–16332 (2023) [3](#page-2-2)

- 12 D. Su, J. Hou and G. Li et al.
- <span id="page-11-8"></span>55. Yang, L., Zhang, Z., Song, Y., Hong, S., Xu, R., Zhao, Y., Zhang, W., Cui, B., Yang, M.H.: Diffusion models: A comprehensive survey of methods and applications. ACM Computing Surveys 56(4), 1–39 (2023) [3](#page-2-2)
- <span id="page-11-9"></span>56. Yang, R., Srivastava, P., Mandt, S.: Diffusion probabilistic modeling for video generation. Entropy 25(10), 1469 (2023) [3](#page-2-2)
- <span id="page-11-0"></span>57. Yin, Z., Xing, E., Shen, Z.: Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. Advances in Neural Information Processing Systems 36 (2024) [2](#page-1-0)
- <span id="page-11-3"></span>58. Yu, R., Liu, S., Wang, X.: A comprehensive survey to dataset distillation. IEEE Transactions on Pattern Analysis and Machine Intelligence 46(1), 150–170 (2023) [2](#page-1-0)
- <span id="page-11-1"></span>59. Zhang, D.J., Wang, H., Xue, C., Yan, R., Zhang, W., Bai, S., Shou, M.Z.: Dataset condensation via generative model. arXiv preprint arXiv:2309.07698 (2023) [2](#page-1-0)
- <span id="page-11-5"></span>60. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: Proceedings of the International Conference on Machine Learning (ICML). pp. 12674–12685 (2021) [3](#page-2-2)
- <span id="page-11-2"></span>61. Zhao, B., Bilen, H.: Dataset condensation with gradient matching. In: Proceedings of the International Conference on Learning Representations (ICLR) (2021) [2,](#page-1-0) [3](#page-2-2)
- <span id="page-11-10"></span>62. Zhao, B., Bilen, H.: Synthesizing informative training samples with gan. arXiv preprint arXiv:2204.07513 (2022) [4](#page-3-0)
- <span id="page-11-6"></span>63. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 7856–7865 (2023) [3](#page-2-2)
- <span id="page-11-4"></span>64. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35, 9813–9827 (2022) [3](#page-2-2)
- <span id="page-11-7"></span>65. Zhu, D., Lei, B., Zhang, J., Fang, Y., Zhang, R., Xie, Y., Xu, D.: Rethinking data distillation: Do not overlook calibration. In: Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV). pp. 4935–4945 (2023) [3](#page-2-2)