{"table_of_contents": [{"title": "Generative Dataset Distillation Based on\nDiffusion Model", "heading_level": null, "page_id": 0, "polygon": [[163.3095703125, 114.0], [450.03515625, 114.0], [450.03515625, 146.759765625], [163.3095703125, 146.759765625]]}, {"title": "1 Background", "heading_level": null, "page_id": 0, "polygon": [[133.5, 589.359375], [225.75, 589.359375], [225.75, 600.1875], [133.5, 600.1875]]}, {"title": "2 <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> et al.", "heading_level": null, "page_id": 1, "polygon": [[133.5, 92.25], [289.5, 92.25], [289.5, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[133.5, 565.5], [237.0, 565.5], [237.0, 576.59765625], [133.5, 576.59765625]]}, {"title": "2.1 Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[133.5, 588.75], [258.75, 588.75], [258.75, 599.02734375], [133.5, 599.02734375]]}, {"title": "2.2 Diffusion Models", "heading_level": null, "page_id": 2, "polygon": [[133.5, 447.75], [245.4873046875, 447.75], [245.4873046875, 457.875], [133.5, 457.875]]}, {"title": "2.3 Generative Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[133.5, 159.0], [318.0, 159.0], [318.0, 169.189453125], [133.5, 169.189453125]]}, {"title": "3 Methods", "heading_level": null, "page_id": 3, "polygon": [[133.5, 353.07421875], [207.984375, 353.07421875], [207.984375, 363.90234375], [133.5, 363.90234375]]}, {"title": "3.1 Overall Procedure", "heading_level": null, "page_id": 3, "polygon": [[133.5, 502.5], [253.107421875, 502.5], [253.107421875, 513.17578125], [133.5, 513.17578125]]}, {"title": "3.2 Fast and High-Fidelity Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[133.5, 575.82421875], [376.5234375, 575.82421875], [376.5234375, 585.87890625], [133.5, 585.87890625]]}, {"title": "3.3 T2I Dataset Distillation", "heading_level": null, "page_id": 4, "polygon": [[133.5, 576.75], [281.25, 576.75], [281.25, 586.65234375], [133.5, 586.65234375]]}, {"title": "3.4 Post Data Augmentation", "heading_level": null, "page_id": 5, "polygon": [[133.5, 117.75], [286.5, 117.75], [286.5, 127.9072265625], [133.5, 127.9072265625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[133.5, 246.75], [229.5, 246.75], [229.5, 257.94140625], [133.5, 257.94140625]]}, {"title": "4.1 Implementation Details", "heading_level": null, "page_id": 5, "polygon": [[133.5, 274.5], [279.0, 274.5], [279.0, 285.01171875], [133.5, 285.01171875]]}, {"title": "4.2 Experimental Results", "heading_level": null, "page_id": 6, "polygon": [[133.5, 377.82421875], [269.25, 377.82421875], [269.25, 387.87890625], [133.5, 387.87890625]]}, {"title": "5 Discussion", "heading_level": null, "page_id": 6, "polygon": [[133.5, 513.75], [216.75, 513.75], [216.75, 524.390625], [133.5, 524.390625]]}, {"title": "5.1 Distribution Discrepancy", "heading_level": null, "page_id": 6, "polygon": [[133.5, 538.5], [287.25, 538.5], [287.25, 548.75390625], [133.5, 548.75390625]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[133.5, 244.5], [219.75, 244.5], [219.75, 255.814453125], [133.5, 255.814453125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 7, "polygon": [[133.5, 397.5], [246.9814453125, 397.5], [246.9814453125, 408.76171875], [133.5, 408.76171875]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[133.5, 465.75], [197.82421875, 465.75], [197.82421875, 476.82421875], [133.5, 476.82421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4583, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 44], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 44], ["TextInlineMath", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 38], ["Text", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 38], ["Text", 4], ["TextInlineMath", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 38], ["ListItem", 10], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["TableCell", 35], ["Line", 31], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3283, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 40], ["ListItem", 5], ["Reference", 5], ["Text", 3], ["SectionHeader", 3], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 51], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 52], ["ListItem", 18], ["Reference", 16], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 50], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 30], ["ListItem", 12], ["Reference", 11], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Generative_Dataset_Distillation_Based_on_Diffusion_Model"}