{"table_of_contents": [{"title": "Generative Dataset Distillation: Balancing Global Structure and Local Details", "heading_level": null, "page_id": 0, "polygon": [[58.5, 106.5], [537.0, 106.5], [537.0, 119.0126953125], [58.5, 119.0126953125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[143.736328125, 213.75], [191.25, 213.75], [191.25, 225.84375], [143.736328125, 225.84375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 491.25], [127.5, 491.25], [127.5, 502.34765625], [48.75, 502.34765625]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[48.75, 621.0], [138.75, 621.0], [138.75, 633.83203125], [48.75, 633.83203125]]}, {"title": "2.1. Dataset Distillation Using Performance Match-\ning", "heading_level": null, "page_id": 1, "polygon": [[307.5, 73.5], [545.25, 73.5], [545.25, 96.72802734375], [307.5, 96.72802734375]]}, {"title": "2.2. Dataset Distillation Using Gradient Matching", "heading_level": null, "page_id": 1, "polygon": [[307.5, 516.75], [541.5, 517.5], [541.5, 528.2578125], [307.5, 528.2578125]]}, {"title": "2.3. Dataset Distillation Using Distribution Match-\ning", "heading_level": null, "page_id": 2, "polygon": [[48.75, 115.5], [286.5, 115.5], [286.5, 138.4453125], [48.75, 138.4453125]]}, {"title": "3. Methodology", "heading_level": null, "page_id": 2, "polygon": [[48.0, 395.25], [129.75, 395.25], [129.75, 406.828125], [48.0, 406.828125]]}, {"title": "3.1. Conditional GAN Training", "heading_level": null, "page_id": 2, "polygon": [[48.0, 480.75], [195.75, 480.75], [195.75, 491.90625], [48.0, 491.90625]]}, {"title": "3.2. Dataset Distillation via Balancing Global Struc-\nture and Local Details", "heading_level": null, "page_id": 2, "polygon": [[307.5, 600.75], [544.5, 600.75], [544.5, 623.00390625], [307.5, 623.00390625]]}, {"title": "3.3. Deployment Stage", "heading_level": null, "page_id": 4, "polygon": [[48.75, 276.75], [155.25, 276.75], [155.25, 287.138671875], [48.75, 287.138671875]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[48.75, 604.5], [127.5, 604.5], [127.5, 616.04296875], [48.75, 616.04296875]]}, {"title": "4.1. <PERSON> Settings", "heading_level": null, "page_id": 4, "polygon": [[48.0, 624.75], [173.25, 624.75], [173.25, 635.37890625], [48.0, 635.37890625]]}, {"title": "4.2. <PERSON><PERSON><PERSON> Comparison", "heading_level": null, "page_id": 5, "polygon": [[48.75, 564.609375], [185.25, 564.609375], [185.25, 575.4375], [48.75, 575.4375]]}, {"title": "4.3. Cross-architecture Generalization", "heading_level": null, "page_id": 5, "polygon": [[307.1953125, 612.0], [488.25, 612.0], [488.25, 623.390625], [307.1953125, 623.390625]]}, {"title": "4.4. Hyperparameter Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[48.75, 526.5], [222.0, 526.5], [222.0, 536.765625], [48.75, 536.765625]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 6, "polygon": [[306.896484375, 72.0], [378.75, 72.0], [378.75, 83.28955078125], [306.896484375, 83.28955078125]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 6, "polygon": [[307.5, 198.0], [396.0, 198.0], [396.0, 208.248046875], [307.5, 208.248046875]]}, {"title": "References", "heading_level": null, "page_id": 6, "polygon": [[307.5, 264.75], [366.662109375, 264.75], [366.662109375, 275.34375], [307.5, 275.34375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 85], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 188], ["Line", 103], ["Text", 6], ["ListItem", 3], ["SectionHeader", 3], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 90], ["Text", 10], ["Equation", 5], ["SectionHeader", 4], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 86], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 96], ["ListItem", 23], ["Text", 7], ["SectionHeader", 3], ["Equation", 2], ["Reference", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["TableCell", 174], ["Line", 60], ["Text", 4], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["Line", 100], ["TableCell", 70], ["Reference", 14], ["ListItem", 11], ["SectionHeader", 4], ["Text", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7845, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 114], ["ListItem", 33], ["Reference", 33], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Generative_Dataset_Distillation__Balancing_Global_Structure_and_Local_Details"}