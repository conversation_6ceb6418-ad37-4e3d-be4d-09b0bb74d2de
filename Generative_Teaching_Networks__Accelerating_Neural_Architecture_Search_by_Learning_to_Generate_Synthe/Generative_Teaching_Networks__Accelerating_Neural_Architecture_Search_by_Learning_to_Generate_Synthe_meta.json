{"table_of_contents": [{"title": "GENERATIVE TEACHING NETWORKS: ACCELERATING\nNEURAL ARCHITECTURE SEARCH BY LEARNING TO\nGENERATE SYNTHETIC TRAINING DATA", "heading_level": null, "page_id": 0, "polygon": [[106.5, 81.0], [504.421875, 80.25], [504.421875, 137.1884765625], [106.5, 137.1884765625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 198.0], [334.5, 198.0], [334.5, 209.408203125], [276.75, 209.408203125]]}, {"title": "1 INTRODUCTION AND RELATED WORK", "heading_level": null, "page_id": 0, "polygon": [[107.25, 504.75], [320.25, 504.75], [320.25, 517.04296875], [107.25, 517.04296875]]}, {"title": "2 METHODS", "heading_level": null, "page_id": 1, "polygon": [[107.05517578125, 687.97265625], [180.0, 687.97265625], [180.0, 699.57421875], [107.05517578125, 699.57421875]]}, {"title": "3 RESULTS", "heading_level": null, "page_id": 3, "polygon": [[107.25, 676.7578125], [174.3662109375, 676.7578125], [174.3662109375, 688.359375], [107.25, 688.359375]]}, {"title": "3.1 IMPROVING STABILITY WITH WEIGHT NORMALIZATION", "heading_level": null, "page_id": 4, "polygon": [[106.5, 243.0], [371.7421875, 243.0], [371.7421875, 253.494140625], [106.5, 253.494140625]]}, {"title": "3.2 IMPROVING GTNS WITH A CURRICULUM", "heading_level": null, "page_id": 4, "polygon": [[107.05517578125, 336.83203125], [309.5859375, 336.83203125], [309.5859375, 347.66015625], [107.05517578125, 347.66015625]]}, {"title": "3.3 GTNS FOR SUPERVISED LEARNING", "heading_level": null, "page_id": 4, "polygon": [[106.5, 634.5], [285.380859375, 634.5], [285.380859375, 644.66015625], [106.5, 644.66015625]]}, {"title": "3.4 ARCHITECTURE SEARCH WITH GTNS", "heading_level": null, "page_id": 5, "polygon": [[106.8310546875, 586.265625], [293.25, 586.265625], [293.25, 595.546875], [106.8310546875, 595.546875]]}, {"title": "4 DISCUSSION, FUTURE WORK, AND CONCLUSION", "heading_level": null, "page_id": 6, "polygon": [[106.90576171875, 621.75], [372.75, 621.75], [372.75, 633.05859375], [106.90576171875, 633.05859375]]}, {"title": "5 ACKNOWLEDGEMENTS", "heading_level": null, "page_id": 8, "polygon": [[107.25, 134.25], [243.84375, 134.25], [243.84375, 145.40625], [107.25, 145.40625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[107.25, 198.0], [176.009765625, 198.0], [176.009765625, 208.634765625], [107.25, 208.634765625]]}, {"title": "APPENDIX A ADDITIONAL EXPERIMENTAL DETAILS", "heading_level": null, "page_id": 11, "polygon": [[107.25, 82.5], [385.5, 82.5], [385.5, 93.5859375], [107.25, 93.5859375]]}, {"title": "A.1 MNIST EXPERIMENTS:", "heading_level": null, "page_id": 11, "polygon": [[106.5, 306.75], [236.25, 306.75], [236.25, 316.529296875], [106.5, 316.529296875]]}, {"title": "A.2 CIFAR10 EXPERIMENTS:", "heading_level": null, "page_id": 11, "polygon": [[106.2333984375, 645.75], [244.5, 645.75], [244.5, 656.26171875], [106.2333984375, 656.26171875]]}, {"title": "APPENDIX B REASONS GTNS ARE NOT EXPECTED TO PRODUCE SOTA\nACCURACY VS. ASYMPTOTIC PERFORMANCE WHEN TRAINING\nON REAL DATA", "heading_level": null, "page_id": 12, "polygon": [[107.25, 299.900390625], [502.62890625, 299.900390625], [502.62890625, 339.345703125], [107.25, 339.345703125]]}, {"title": "APPENDIX C CELL SEARCH SPACE", "heading_level": null, "page_id": 12, "polygon": [[107.25, 435.75], [296.25, 435.75], [296.25, 447.43359375], [107.25, 447.43359375]]}, {"title": "APPENDIX D COMPUTATION AND MEMORY COMPLEXITY", "heading_level": null, "page_id": 12, "polygon": [[107.05517578125, 663.75], [412.5, 663.75], [412.5, 674.82421875], [107.05517578125, 674.82421875]]}, {"title": "APPENDIX E EXTENDED NAS RESULTS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 209.25], [318.75, 209.25], [318.75, 221.396484375], [107.25, 221.396484375]]}, {"title": "APPENDIX F CONDITIONED GENERATOR VS. XY-GENERATOR", "heading_level": null, "page_id": 13, "polygon": [[107.25, 642.7265625], [434.197265625, 642.7265625], [434.197265625, 653.5546875], [107.25, 653.5546875]]}, {"title": "APPENDIX G GTN GENERATES (SEEMINGLY) ENDLESS DATA", "heading_level": null, "page_id": 14, "polygon": [[106.98046875, 366.99609375], [426.75, 366.99609375], [426.75, 378.2109375], [106.98046875, 378.2109375]]}, {"title": "APPENDIX H GTN FOR RL", "heading_level": null, "page_id": 15, "polygon": [[107.25, 618.75], [255.75, 618.75], [255.75, 629.96484375], [107.25, 629.96484375]]}, {"title": "APPENDIX I SOLVING MODE COLLAPSE IN GANS WITH GTNS", "heading_level": null, "page_id": 16, "polygon": [[107.25, 575.25], [435.09375, 575.25], [435.09375, 586.265625], [107.25, 586.265625]]}, {"title": "APPENDIX J ADDITIONAL MOTIVATION", "heading_level": null, "page_id": 17, "polygon": [[107.25, 641.56640625], [318.849609375, 641.56640625], [318.849609375, 653.16796875], [107.25, 653.16796875]]}, {"title": "APPENDIX K ON THE REALISM OF IMAGES", "heading_level": null, "page_id": 18, "polygon": [[107.25, 403.5], [335.28515625, 404.89453125], [335.28515625, 416.49609375], [107.25, 416.49609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 51], ["Text", 4], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5310, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 55], ["Text", 7], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 79], ["Text", 5], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 55], ["Text", 6], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 54], ["Text", 6], ["SectionHeader", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 69], ["Text", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 901, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 67], ["Text", 4], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 818, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["TableCell", 72], ["Line", 55], ["Text", 6], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 44], ["ListItem", 15], ["Reference", 15], ["Text", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 48], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 39], ["ListItem", 16], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 58], ["TableCell", 36], ["Reference", 4], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 3], ["Equation", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 44], ["Line", 43], ["ListItem", 11], ["Text", 4], ["Reference", 4], ["SectionHeader", 3], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2373, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["TableCell", 180], ["Line", 52], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 55], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 53], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["Picture", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1455, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 56], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 830, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["TableCell", 104], ["Line", 44], ["Text", 3], ["Reference", 2], ["Figure", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 44], ["Text", 6], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 581, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 58], ["Text", 4], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 58], ["Text", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 7], ["Text", 3], ["Picture", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 10], ["Line", 5], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 576, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Line", 3], ["Span", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 53], ["Line", 24], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 850, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 7], ["Line", 5], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 586, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Generative_Teaching_Networks__Accelerating_Neural_Architecture_Search_by_Learning_to_Generate_Synthe"}