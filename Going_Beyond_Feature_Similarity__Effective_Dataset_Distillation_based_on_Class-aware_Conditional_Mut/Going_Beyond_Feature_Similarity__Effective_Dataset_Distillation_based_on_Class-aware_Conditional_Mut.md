# GOING BEYOND FEATURE SIMILARITY: EFFECTIVE DATASET DISTILLATION BASED ON CLASS-AWARE CONDITIONAL MUTUAL INFORMATION

Xi<PERSON><PERSON><sup>1</sup> <PERSON><sup>1,2</sup>\* <PERSON><PERSON><sup>3</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>4</sup><sup>1</sup>Harbin Institute of Technology, Shenzhen <sup>2</sup>Peng Cheng Laboratory <sup>1</sup>Harbin Institute of Technology, Shenzhen  $3$ Tsinghua Shenzhen International Graduate School, Tsinghua University  $4$ University <NAME_EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>,

<EMAIL>, <EMAIL>;

# ABSTRACT

Dataset distillation (DD) aims to minimize the time and memory consumption needed for training deep neural networks on large datasets, by creating a smaller synthetic dataset that has similar performance to that of the full real dataset. However, current dataset distillation methods often result in synthetic datasets that are excessively difficult for networks to learn from, due to the compression of a substantial amount of information from the original data through metrics measuring feature similarity, e,g., distribution matching (DM). In this work, we introduce conditional mutual information (CMI) to assess the class-aware complexity of a dataset and propose a novel method by minimizing CMI. Specifically, we minimize the distillation loss while constraining the class-aware complexity of the synthetic dataset by minimizing its empirical CMI from the feature space of pre-trained networks, simultaneously. Conducting on a thorough set of experiments, we show that our method can serve as a general regularization method to existing DD methods and improve the performance and training efficiency. Our code is available at <https://github.com/ndhg1213/CMIDD>.

## 1 INTRODUCTION

Dataset distillation (DD) [\(Wang et al., 2018;](#page-11-0) [Nguyen et al., 2021a;](#page-10-0)[b;](#page-10-1) [Zhou et al., 2022\)](#page-12-0) has received tremendous attention from both academia and industry in recent years as a highly effective data compression technique, and has been deployed in different fields, ranging from continual learning [\(Gu et al., 2024b\)](#page-9-0), federated learning [\(Wang et al., 2024b\)](#page-11-1) to privacy-preserving [\(Dong et al.,](#page-9-1) [2022\)](#page-9-1). Dataset distillation aims to generate a smaller synthetic dataset from a large real dataset, which encapsulates a higher level of concentration of task-specific information than the large real counterpart and which the deep neural networks (DNNs) trained on can attain performance comparable to that of those trained on large real datasets, but with benefits of significant savings in both training time and memory consumption.

Existing dataset distillation methods optimize the smaller synthetic dataset through stochastic gradient descent by matching gradient [\(Zhao et al., 2021\)](#page-11-2), feature [\(Zhao & Bilen, 2021a\)](#page-11-3), or statistic information [Yin et al.](#page-11-4) [\(2024\)](#page-11-4), aiming to compress task-relevant information sensitive to the backbone model into the generated images. Building on this, some recent methods propose using feature clustering [\(Deng et al., 2024\)](#page-9-2), maximizing mutual information [\(Shang et al., 2024\)](#page-10-2), or progressive optimization [\(Chen et al., 2024\)](#page-9-3) to enhance performance. However, these plug-and-play methods focus on improving the alignment of the synthetic dataset with the real dataset in a specific information space, e.g., distribution matching, while neglecting the properties of different classes inherent in the synthetic dataset. This limitation restricts their applicability to specific distillation methods and results in relatively modest performance improvements.

<sup>∗</sup>Corresponding Author.

A promising group of methods [\(Paul et al., 2021;](#page-10-3) [Zheng et al., 2022\)](#page-11-5) on dataset evaluation indicate that in few-shot learning, simpler datasets are often more beneficial for model training. This suggests that while synthetic datasets aim to distillate all the information from the real dataset during optimization, the complexity of distilled information can make it more challenging for models to learn, and the overly complex samples often introduce biases during model training. This limitation has been further supported by recent studies [\(He et al., 2024;](#page-10-4) [Yang et al., 2024\)](#page-11-6), which show harder samples can not support the entire training and often lead to performance drops and fluctuations.

<span id="page-1-0"></span>Image /page/1/Figure/2 description: The image displays two sets of scatter plots, each comparing a "Feature Space" and a "Probability Space". The left side, labeled (a) "Synthetic dataset w.o. CMI constraint.", shows two plots. The "Feature Space" plot on the top left has points scattered across a wide range of x and y values, with no clear clusters. The "Probability Space" plot on the top right shows similar scattered points, also without distinct clusters. The right side, labeled (b) "Synthetic dataset w. CMI constraint.", also has two plots. The "Feature Space" plot on the bottom left shows distinct clusters of points, with several groups appearing separated. The "Probability Space" plot on the bottom right also displays well-defined clusters of points, indicating a clearer separation of data points compared to the dataset without the CMI constraint.

Figure 1: Visualization of the synthetic dataset generated by DM with (a) high CMI value, and (b) low CMI value.

To achieve a more granular measurement of the information involved in the dataset distillation process, especially regarding the varying difficulty levels across different classes, this paper introduces conditional mutual information (CMI) from information theory [\(Yang et al., 2023\)](#page-11-7) as a class-aware complexity metric for measuring fine-grained condensed information. Specifically, given a pre-trained neural network  $f_{\theta^*}(\cdot)$  parameterized by  $\theta^*$ , we define the CMI  $I(S; \hat{Y} | Y)$ , where S, Y and  $\hat{Y}$  are three random variables representing the input synthetic dataset, the ground truth label, and the output of  $f_{\theta^*}(\cdot)$ , respectively. Building on this,  $I(S; \hat{Y} | Y)$  quantifies the amount of information about S contained in  $\hat{Y}$  given the class condition Y, indirectly measuring the class-aware complexity of the synthetic dataset by demonstrating how the output of a pre-trained network is influenced by the synthetic dataset. Through empirical verification, we demonstrate that minimizing CMI in the feature space of  $f_{\theta^*}(\cdot)$  invariably leads to the distilled data becoming more focused around the center of each class, thereby enhancing the generalization of the distilled data across different network architectures as shown in Figure [1.](#page-1-0)

Based on this intuition, we propose a novel regularization method to existing DD methods by simultaneously minimizing the distillation loss of the synthetic datasets and its CMI. First, we employ an efficient CMI estimation method to measuring the class-aware inherent properties of the synthetic dataset. Under the guidance of this metric, we treat CMI as a universal regularization method and combine it with existing dataset distillation techniques with diverse optimization objectives. Experiments demonstrate that the CMI enhanced losses significantly outperform various state-ofthe-art methods in most cases. Even huge improvements of over 5% can be obtained under some conditions.

In summary, the contributions of the paper are as follows:

- We provide an insight into the properties of different classes inherent in the synthetic dataset and point out that the generalization of the distilled data can be improved by optimizing the class-aware complexity of synthetic dataset quantified via CMI empirically and theoretically.
- Building on this perspective, we propose the CMI enhanced loss that simultaneously minimizes the distillation loss and CMI of the synthetic dataset in the feature space. This enables the class-aware complexity of synthetic dataset could be efficiently reduced, while distilled data becoming more focused around their class centers.
- Experimental results show that our method can effectively improve the performance of existing dataset distillation methods by up to 5.5%. Importantly, our method can be deployed as an plug-and-play module for all the existing DD methods with different optimization objectives.

### 2 RELATED WORKS

Dataset Distillation (DD) [\(Wang et al., 2018\)](#page-11-0) aims to generate a synthetic dataset that, when used for training, can achieve performance similar to training on the full real dataset. To achieve this, DD adopted a meta-learning process comprising two nested loops. The method minimizes the loss function of the synthetic dataset in the outer loop, using a model trained on the synthetic dataset in the inner loop. To address the issue of unrolled computational graphs, recent studies propose matching proxy information. DC [\(Zhao et al., 2021\)](#page-11-2) and DCC [\(Lee et al., 2022\)](#page-10-5) minimize the distance between the gradients of the synthetic and original data on the network being trained with synthetic data. DM [\(Zhao & Bilen, 2021a\)](#page-11-3) and CAFE [Wang et al.](#page-11-8) [\(2022\)](#page-11-8) matche the extracted features between the synthetic dataset and the real dataset. MTT [\(Cazenavette et al., 2022\)](#page-9-4) and TESLA [\(Cui et al., 2023\)](#page-9-5) match the training trajectories of the network parameters obtained from training on the complete and synthetic datasets, respectively.  $SRe^{2}L$  [\(Yin et al., 2024\)](#page-11-4) successfully scales up dataset distillation to larger datasets and deeper network architectures by utilizing model inversion loss and matching the statistics in batch-normalization layers.

Subsequently, various techniques have been proposed to enhance the performance of the synthetic dataset. DSA [\(Zhao & Bilen, 2021b\)](#page-11-9) applied differentiable siamese augmentations to both the real and synthetic data while training. IDC [\(Kim et al., 2022\)](#page-10-6) proposed a multi formulation framework to generate more augmented examples under the same memory budget, successfully achieved a more efficient utilization of the limited pixel space by reducing the resolution of generated images. Since then, incorporating differentiable data augmentation and multi formulation have been adopted by almost all subsequent studies. DREAM [\(Liu et al., 2023\)](#page-10-7) utilized clustering to select more representative original images. MIM4DD [\(Shang et al., 2024\)](#page-10-2) proposed leveraging a contrastive learning framework to maximize the mutual information between the synthetic dataset and the real dataset. Several methods have been improved based on the characteristics of the matching objectives. PDD [\(Chen et al., 2024\)](#page-9-3) and SeqMatch [\(Du et al., 2024\)](#page-9-6) propose multi-stage distillation based on the characteristics of network parameter variations during the stochastic gradient descent process. IID [\(Deng et al., 2024\)](#page-9-2) further aligned the inter-class and intra-class relationships when using feature matching.

Existing methods primarily focus on achieving more precise estimation and utilization of surrogate information or compressing additional beneficial information into the synthetic dataset, while neglecting the impact of synthetic dataset complexity. In contrast, we show that properly constraining the complexity of the synthetic dataset yields superior performance.

## 3 NOTATION AND PRELIMINARIES

### 3.1 NOTATION

For a positive integer K, we denote  $[K] \triangleq \{1, \ldots, K\}$ . Let  $\mathcal{P}([C])$  be the set of all possible probability distributions over the C-dimensional probability simplex indexed by  $[C]$ . For any two probability distributions  $P_1, P_2 \in \mathcal{P}([C])$ , the cross-entropy between  $P_1$  and  $P_2$  is defined as  $H(P_1, P_2) = -\sum_{i=1}^{C} P_1(i) \log P_2(i)$ , and the Kullback–Leibler (KL) divergence between  $P_1$  and  $P_2$  is defined as  $D(P_1 \| P_2) = \sum_{i=1}^{C} P_1(i) \log \frac{P_1(i)}{P_2(i)}$ .

For a random variable X, let  $\mathbb{P}_X$  denote its probability distribution, and  $\mathbb{E}_X[\cdot]$  represent the expected value with respect to X. For two random variables X and Y, let  $\mathbb{P}_{(X,Y)}$  denote the joint distribution of X and Y. The mutual information between the random variables X and Y is defined as  $I(X; Y) =$  $H(X) - H(X | Y)$ , where  $H(X)$  is the entropy of X. The conditional mutual information of X and Z given a third random variable Y is defined as  $I(X;Z | Y) = H(X | Y) - H(X | Z, Y)$ .

Given a deep neural network  $f_{\theta}(\cdot)$  parameterized by  $\theta$ , we can view it as a mapping from  $\mathbf{x} \in \mathbb{R}^d$  to  $P_x$ . When there is no ambiguity, we denote  $P_x$  as  $P_{x,\theta}$ , and  $P_x(y)$  as  $P(y | x, \theta)$  for any  $y \in [C]$ . Let  $\hat{Y}$  denote the output predicted by the DNN, with the probability  $P_X(\hat{Y})$  in response to the input X. Specifically, for any input  $\mathbf{x} \in \mathbb{R}^d$ , we have  $P(\hat{Y} = \hat{y} \mid X = \mathbf{x}) = P_{\mathbf{x}}(\hat{y}) = P(\hat{y} \mid \mathbf{x}, \theta)$ . Since  $Y \to X \to \hat{Y}$  forms a Markov chain, we can infer that Y and  $\hat{Y}$  are independent conditioned on X.

### 3.2 DATASET DISTILLATION

Given a large-scale dataset  $\mathcal{T} = \{(\mathbf{x}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ , dataset distillation aims to generate a synthetic dataset  $S = \{(\mathbf{s}_i, y_i)\}_{i=1}^{|\mathcal{S}|}$  that retains as much class-relevant information as possible, where  $|\mathcal{S}| \ll |\mathcal{T}|$ . The key motivation for dataset distillation is to create an informative  $S$  that allows models to achieve performance within an acceptable deviation  $\epsilon$  from those trained on T. This can be formulated as:

$$
\sup_{(\mathbf{x},y)\in\mathcal{T}}|l(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}),y)-l(\phi_{\theta_{\mathcal{S}}}(\mathbf{x}),y)|\leq\epsilon,
$$
\n(1)

where  $l(\cdot, \cdot)$  represents the loss function,  $\theta_{\tau}$  is the parameter of the neural network  $\phi$  trained on  $\tau$ , and a similar definition applies to  $\theta_{\mathcal{S}}$ :

$$
\theta_{\mathcal{T}} = \underset{\theta}{\arg\min} \mathbb{E}_{(\mathbf{x},y)\in\mathcal{T}} \left( l(\phi_{\theta}(\mathbf{x}), y) \right). \tag{2}
$$

To transform this metric into a computable optimization method, previous optimization-based approaches utilize various  $\phi(\cdot)$  to extract informative guidance from T and S in a specific information space while alternately optimizing  $S$ , formulated as:

$$
S^* = \underset{S}{\arg\min} \mathcal{M}(\phi(S), \phi(\mathcal{T})),\tag{3}
$$

where  $\mathcal{M}(\cdot, \cdot)$  denotes various matching metrics, such as neural network gradients [\(Zhao et al., 2021\)](#page-11-2), extracted features [\(Zhao & Bilen, 2021a\)](#page-11-3), and training trajectories [\(Cazenavette et al., 2022\)](#page-9-4).

## 4 METHODOLOGY

### 4.1 CLASS-AWARE CMI AS A VALID MEASURE OF SYNTHETIC DATASET

Prior data evaluation studies [\(Paul et al., 2021;](#page-10-3) [Zheng et al., 2022\)](#page-11-5) indicate that when  $|S|$  is small, the model's ability to learn complex representations is constrained. To enable the model to effectively capture dominant patterns across different classes in a limited dataset, the samples in  $S$  must represent the most prevalent patterns. By focusing on learning from these representative samples, the model can capture essential discriminative information across classes. Additionally, easier samples of certain classes in  $S$  tend to yield lower loss function values, facilitating faster convergence and enhancing performance within a limited number of training iterations. Therefore, it becomes essential to introduce a measure for the class-aware complexity of the synthetic dataset  $S$  itself.

Recognizing that feature representation contains more effective information than probabilistic representation  $Y$  [\(Gou et al., 2021\)](#page-9-7), we impose constraints on the synthetic dataset in the feature space. To be specific, for a pre-trained network  $f_{\theta^*}(\cdot)$  trained on the original large-scale dataset  $\tau$ , we can decompose it as  $f_{\theta^*}(\cdot) = l_{\theta_2^*}(\cdot) \circ h_{\theta_1^*}(\cdot)$   $(\theta^* = {\theta_1^*, \theta_2^*} \},$  where  $h_{\theta_1^*}(\cdot)$  :  $s \mapsto z$  denotes a feature extractor that maps an input sample  $\mathbf{s} \in \mathcal{S} \subseteq \mathbb{R}^d$  into an M-dimensional feature vector  $\mathbf{z} \in \mathcal{Z} \subseteq \mathbb{R}^M$ (e.g., the 512-dimensions penultimate features of ResNet-18), and  $l_{\theta_2^*}(\cdot) : \mathbf{z} \mapsto \hat{y}$  is a parametric classifier that takes **z** as input and produces a class prediction  $\hat{y}$ .

For a given input  $s \in S$ , the output feature z is a deterministic feature vector. We apply the softmax function to the feature vector  $\mathbf{z} = (z^1, z^2, \dots, z^M)$  for an input sample  $\mathbf{s} \in \mathcal{S}$ , which maps it to a one-dimensional random variable  $\hat{Z}$ , whose probability distribution  $P_{s}[\hat{Z}]$  is a random point in the probability simplex  $\mathcal{P}([M])$  indexed by  $[M]$ , where

$$
P_{\mathbf{s}}[i] = P(\hat{Z} = i \mid \mathbf{s}) = \frac{\exp(z^i)}{\sum_{j=1}^M \exp(z^j)}, \quad \text{for any } i \in [M] = \{1, 2, \dots, M\}.
$$
 (4)

Based on the above analysis, we can see that  $Y \to S \to \mathbb{Z}$  forms a Markov chain. To quantify the class-aware complexity of the synthetic dataset, we introduce the class-aware conditional mutual information (CMI)  $I(S; \bar{Z} | Y)$  as a novel measure of the complexity of the synthetic dataset and further explain its validity below.

Note that the non-linear relationship between the input S and the output  $\hat{Z}$  can be quantified by the conditional mutual information  $I(S; \hat{Z} | Y)$ . This can also be expressed as  $I(S; \hat{Z} | Y) = H(S | Y)$   $Y$ ) –  $H(S | \hat{Z}, Y)$ , representing the difference between the uncertainty of S given both  $\hat{Z}$  and Y and that of S given Y. When a relatively diverse and large dataset (e.g.,  $\mathcal{T} \sim \mathbb{P}_X$ ) is used as the input to  $f_{\theta^*}(\cdot)$ , the corresponding output  $\hat{Z}$  follows a more certain probability distribution produced by  $f_{\theta^*}(\cdot)$ . In contrast, since S is more challenging for randomly initialized networks to learn, its output  $\hat{Z}$  often contains excessive confused information related to it, leading to a significant reduction in  $H(S \mid \hat{Z}, Y)$ . Thus, minimizing the class-aware CMI value constraints the uncertainty brought to  $\hat{Z}$  with S as the input of  $f_{\theta^*}(\cdot)$ , preventing S from overly complicating the prediction process of  $f_{\theta^*}(\cdot)$ . Additionally, minimizing CMI leads to a reduction in  $H(S | Y)$  implicitly, which represents the complexity of  $S$  conditioned on  $Y$  alone.

### 4.2 ESTIMATING THE CLASS-AWARE CMI FOR SYNTHETIC DATASET

Given  $Y = y$  and  $y \in [C]$ , the input S is conditionally distributed according to  $P_{S|Y}(\cdot|y)$  and then mapped into  $P_S \in \mathcal{P}([M])$ . The conditional distribution  $P_{\hat{Z}|y}$ , i,e., the centroid of this cluster, is exactly the average of input distribution  $P_S$  with respect to  $P_{S|Y}(\cdot|y)$ , which is formulated as:

<span id="page-4-0"></span>
$$
P_{\hat{Z}|y} = \mathbb{E}[P_S \mid Y = y].\tag{5}
$$

Similar to the calculation in [\(Ye et al., 2024\)](#page-11-10), we employ the Kullback-Leibler (KL) divergence  $D(P_S||P_{\hat{Z}|y})$  to quantify the distance between  $P_S$  and the conditional distribution  $P_{\hat{Z}|y}$ .

Then we can derive the class-aware conditional mutual information as follows:

$$
I(S; \hat{Z} \mid Y = y) = \sum_{\mathbf{s} \in \mathcal{S}} P_{S|Y}(S = \mathbf{s} \mid y) \left[ \sum_{i=1}^{M} P(\hat{Z} = i \mid \mathbf{s}) \times \ln \frac{P(\hat{Z} = i \mid \mathbf{s})}{P_{\hat{Z}|y}(\hat{Z} = i \mid Y = y)} \right] (6)
$$

$$
= \mathbb{E}_{S|Y} \left[ \left( \sum_{i=1}^{M} P_S[i] \ln \frac{P_S[i]}{P_{\hat{Z}|y}(\hat{Z} = i | Y = y)} \right) \middle| Y = y \right]
$$
(7)

$$
= \mathbb{E}_{S|Y} \left[ D \left( P_S \| P_{\hat{Z}|y} \right) \mid Y = y \right], \tag{8}
$$

Averaging  $I(S; \hat{Z}|y)$  with respect to the distribution  $P_Y(y)$  of Y, we can obtain the conditional mutual information between S and  $\hat{Z}$  given Y as follow:

$$
\text{CMI}(\mathcal{S}) \triangleq I(S; \hat{Z} \mid Y) = \sum_{y \in [C]} P_Y(y)I(S; \hat{Z} \mid y). \tag{9}
$$

In practice, the joint distribution  $P(s, y)$  of  $(S, Y)$  may be unknown. To compute the CMI(S) in this case, we approximate  $P(s, y)$  by the empirical distribution of synthetic dataset  $S_y$  =  $\{(\mathbf{s}_1, y), (\mathbf{s}_2, y), \cdots, (\mathbf{s}_n, y)\}\$ for any  $y \in [C]$ . Then the CMI<sub>emp</sub>(S) can be calculated as:

<span id="page-4-1"></span>
$$
\text{CMI_\text{emp}}(\mathcal{S}) = \frac{1}{|\mathcal{S}|} \sum_{y \in [C]} \sum_{\mathbf{s}_j \in \mathcal{S}_y} \text{KL} \left( P_{\mathbf{s}_j} || Q_\text{emp}^y \right), \quad (10)
$$

$$
\text{where} \quad Q_\text{emp}^y = \frac{1}{|\mathcal{S}_y|} \sum_{\mathbf{s}_j \in \mathcal{S}_y} P_{\mathbf{s}_j}, \text{ for } y \in [C].
$$

### 4.3 DATASET DISTILLATION WITH CMI ENHANCED LOSS

According to the above calculation of CMI, we propose the CMI enhanced Loss  $\mathcal{L}$ . Overall, it includes two parts:

<span id="page-4-2"></span>
$$
\mathcal{L} = \mathcal{L}_{DD} + \lambda \, \text{CMI}_{\text{emp}}(\mathcal{S}).\tag{11}
$$

The first term  $\mathcal{L}_{DD}$  represents any loss function in previous DD methods, e.g., DM, DSA and MTT,  $\lambda > 0$  is a weighting hyperparameter.

The proposed CMI enhanced loss is universal since it provides a plug-and-play solution to minimize the class-conditional complexity of the synthetic dataset, making it adaptable to all previous dataset distillation methods that focus on better aligning the synthetic dataset with the real one just based on

| Method                                                                                                             |                                                                           | <b>SVHN</b>                                                               |                                                                                             | CIFAR10                                                                                         |                                                                                             | CIFAR100                                                                  |
|--------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|---------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|
| <b>IPC</b>                                                                                                         | 10                                                                        | 50                                                                        | 10                                                                                          | 50                                                                                              | 10                                                                                          | 50                                                                        |
| Ratio $(\%)$                                                                                                       | 0.14                                                                      | 0.7                                                                       | 0.2                                                                                         | 1                                                                                               | $\overline{c}$                                                                              | 10                                                                        |
| Random                                                                                                             | $35.1 \pm 4.1$                                                            | $70.9 \pm 0.9$                                                            | $26.0 + 1.2$                                                                                | $43.4 + 1.0$                                                                                    | $14.6 \pm 0.5$                                                                              | $30.0 \pm 0.4$                                                            |
| Herding(Welling, 2009)                                                                                             | $50.5 \pm 3.3$                                                            | $72.6 \pm 0.8$                                                            | $31.6 \pm 0.7$                                                                              | $40.4 \pm 0.6$                                                                                  | $17.3 \pm 0.3$                                                                              | $33.7 \pm 0.5$                                                            |
| K-Center(Sener & Savarese, 2017)                                                                                   | $14.0 \pm 1.3$                                                            | $20.1 \pm 1.4$                                                            | $14.7 \pm 0.9$                                                                              | $27.0 \pm 1.4$                                                                                  | $17.3 \pm 0.2$                                                                              | $30.5 \pm 0.3$                                                            |
| Forgetting (Toneva et al., 2019)                                                                                   | $16.8 \pm 1.2$                                                            | $27.2 \pm 1.5$                                                            | $23.3 \pm 1.0$                                                                              | $23.3 \pm 1.1$                                                                                  | $15.1 \pm 0.2$                                                                              | $30.5 \pm 0.4$                                                            |
| MTT (Cazenavette et al., 2022)<br>MIM4DD (Shang et al., 2024)<br>SeqMatch (Du et al., 2024)<br><b>MTT+CMI</b><br>Δ | 79.9±0.1<br>$80.2 \pm 0.6$<br>$80.8 \pm 0.2$<br>(0.9 <sup>†</sup> )       | $87.7 \pm 0.3$<br>$88.5 \pm 0.2$<br>$88.8 \pm 0.1$<br>(1.1 <sup>†</sup> ) | $65.3 \pm 0.4$<br>$66.4 + 0.2$<br>$66.2 \pm 0.6$<br>$66.7 \pm 0.3$<br>(1.4 <sup>†</sup> )   | $71.6 \pm 0.2$<br>$71.4 \pm 0.3$<br>$74.4 \pm 0.5$<br>$72.4 \pm 0.3$<br>(0.8 <sup>†</sup> )     | $39.7 \pm 0.4$<br>$41.5 \pm 0.2$<br>$41.9 \pm 0.5$<br>$41.9 \pm 0.4$<br>(2.2 <sup>†</sup> ) | $47.7 \pm 0.2$<br>$51.2 \pm 0.3$<br>$48.8 \pm 0.2$<br>(1.1 <sup>†</sup> ) |
| DM (Zhao & Bilen, 2023)                                                                                            | $72.8 \pm 0.3$                                                            | $82.6 \pm 0.5$                                                            | $48.9 \pm 0.6$                                                                              | $63.0 \pm 0.4$                                                                                  | $29.7 \pm 0.3$                                                                              | $43.6 \pm 0.4$                                                            |
| IID-DM (Deng et al., 2024)                                                                                         | $75.7 \pm 0.3$                                                            | $85.3 \pm 0.2$                                                            | $55.1 \pm 0.1$                                                                              | $65.1 \pm 0.2$                                                                                  | $32.2 \pm 0.5$                                                                              | $43.6 \pm 0.3$                                                            |
| $DM+CMI$                                                                                                           | $77.9 \pm 0.4$                                                            | $84.9 \pm 0.4$                                                            | $52.9 \pm 0.3$                                                                              | $65.8 \pm 0.3$                                                                                  | $32.5 \pm 0.4$                                                                              | $44.9 \pm 0.2$                                                            |
| Δ                                                                                                                  | (5.1 <sup>†</sup> )                                                       | (2.3 <sup>†</sup> )                                                       | (4.0 <sup>†</sup> )                                                                         | (2.8 <sup>†</sup> )                                                                             | (2.8 <sup>†</sup> )                                                                         | (1.3 <sup>†</sup> )                                                       |
| IDM (Zhao et al., 2023)                                                                                            | $81.0 \pm 0.1$                                                            | $84.1 \pm 0.1$                                                            | $58.6 \pm 0.1$                                                                              | $67.5 \pm 0.1$                                                                                  | $45.1 \pm 0.1$                                                                              | $50.0 \pm 0.2$                                                            |
| IID-IDM (Deng et al., 2024)                                                                                        | $82.1 \pm 0.3$                                                            | $85.1 \pm 0.5$                                                            | 59.9±0.2                                                                                    | $69.0 \pm 0.3$                                                                                  | $45.7 \pm 0.4$                                                                              | $51.3 \pm 0.4$                                                            |
| <b>IDM+CMI</b>                                                                                                     | $84.3 \pm 0.2$                                                            | $88.9 \pm 0.2$                                                            | $62.2 \pm 0.3$                                                                              | $71.3 \pm 0.2$                                                                                  | $47.2 \pm 0.4$                                                                              | $51.9 \pm 0.3$                                                            |
| Δ                                                                                                                  | (3.3 <sup>†</sup> )                                                       | (4.8 <sup>†</sup> )                                                       | (3.6 <sup>†</sup> )                                                                         | (3.8 <sup>†</sup> )                                                                             | (2.1 <sup>†</sup> )                                                                         | (1.9 <sup>†</sup> )                                                       |
| DSA (Zhao et al., 2021)                                                                                            | $79.2 \pm 0.5$                                                            | $84.4 \pm 0.4$                                                            | $52.1 \pm 0.5$                                                                              | $60.6 \pm 0.5$                                                                                  | $32.3 \pm 0.3$                                                                              | $42.8 \pm 0.4$                                                            |
| DSA+CMI                                                                                                            | $80.5 \pm 0.2$                                                            | $85.5 \pm 0.3$                                                            | $54.7 \pm 0.4$                                                                              | $66.1 \pm 0.1$                                                                                  | $35.0 \pm 0.4$                                                                              | $45.9 \pm 0.3$                                                            |
| Δ                                                                                                                  | (1.3 <sup>†</sup> )                                                       | (1.1 <sup>†</sup> )                                                       | (2.6 <sup>†</sup> )                                                                         | (5.5 <sup>†</sup> )                                                                             | (2.7 <sup>†</sup> )                                                                         | (3.1 <sup>†</sup> )                                                       |
| IDC (Kim et al., $2022$ )<br>DREAM (Liu et al., 2023)<br>PDD (Chen et al., 2024)<br><b>IDC+CMI</b><br>Δ            | $87.5 \pm 0.3$<br>$87.9 \pm 0.4$<br>$88.5 \pm 0.2$<br>(1.0 <sup>†</sup> ) | $90.1 \pm 0.1$<br>$90.5 \pm 0.1$<br>$92.2 \pm 0.1$<br>(2.1 <sup>†</sup> ) | $67.5 \pm 0.5$<br>$69.4 \pm 0.4$<br>$67.9 \pm 0.2$<br>$70.0 \pm 0.3$<br>(2.5 <sup>†</sup> ) | $74.5 \pm 0.1$<br>$74.8 \pm 0.1$<br>$76.5 \pm 0.4$<br>$76.6 \!\pm\! 0.2$<br>(2.1 <sup>†</sup> ) | $45.1 \pm 0.4$<br>$46.8 \pm 0.7$<br>$45.8 \pm 0.5$<br>$46.6 \pm 0.3$<br>(1.5 <sup>†</sup> ) | $52.6 \pm 0.4$<br>$53.1 \pm 0.4$<br>$53.8 \pm 0.2$                        |
| <b>Whole Dataset</b>                                                                                               |                                                                           | $95.4 \pm 0.2$                                                            |                                                                                             | $84.8 \pm 0.1$                                                                                  |                                                                                             | $56.2 \pm 0.3$                                                            |

<span id="page-5-0"></span>Table 1: Comparative analysis of dataset distillation methods. ∆: the improvement magnitude of CMI as a plugin to the base distillation methods. Ratio  $(\%)$ : the proportion of condensed images relative to the number of entire training set. Whole Dataset: the accuracy of training on the entire original dataset. The best results are highlighted.

feature similarity. In addition, minimizing CMI as a complexity constraint poses certain challenges, as the optimization target is a general synthetic dataset. To address this issue, we compute CMI in the feature space rather than the probability space as demonstrated in [\(Ye et al., 2024\)](#page-11-10), thereby establishing a stronger constraint. Addistionally, during the optimization process, we randomly sample pre-trained networks with varying initializations to compute CMI, resulting in a dataset-specific optimization process that is independent of the network parameters.

## 5 EXPERIMENTS

In this section, we conduct comprehensive experiments to evaluate our proposed method across various datasets and network architectures. We begin by detailing the implementation, followed by an analysis of the performance improvements achieved with different distillation techniques. Finally, we assess the effectiveness of the the CMI enhanced loss through a series of ablation studies.

### 5.1 EXPERIMENTAL SETTINGS

Datasets. We conduct our experiments on five standard datasets with varying scales and resolutions: SVHN[\(Sermanet et al., 2012\)](#page-10-10) CIFAR-10/100 [\(Krizhevsky, 2009\)](#page-10-11), Tiny-ImageNet [\(Le & Yang, 2015\)](#page-10-12) and ImageNet-1K[\(Deng et al., 2009\)](#page-9-8). SVHN contains over 600, 000 images of house numbers from around the world. CIFAR-10 and CIFAR-100 consist of 50, 000 training images, with 10 and 100 classes, respectively. The image size for CIFAR is  $32 \times 32$ . For larger datasets, Tiny-ImageNet contains 100, 000 training images from 200 categories, with the image size of  $64 \times 64$ . ImageNet-1K consists of over 1, 200, 000 training images with various resolutions from 1, 000 classes.

Baselines. We consider state-of-the-art (SOTA) plug-and-play techniques for enhancing dataset distillation performance as baselines, each with different optimization objectives:

- MIM4DD [\(Shang et al., 2024\)](#page-10-2) introduces a contrastive learning framework to estimate and maximize the mutual information between the synthetic dataset and the real dataset.
- SeqMatch [\(Du et al., 2024\)](#page-9-6) implements dynamic adjustment of the optimization process by splitting the synthetic dataset in gradient matching methods.
- IID [\(Deng et al., 2024\)](#page-9-2) better aligns the intra-class and inter-class relationships between the original dataset and the synthetic dataset in feature matching methods.
- DREAM [\(Liu et al., 2023\)](#page-10-7) universally accelerates dataset distillation by selecting representative original images through clustering.
- PDD [\(Chen et al., 2024\)](#page-9-3) generates a considerably larger synthetic dataset by aligning the parameter variations throughout the gradient matching process.

Architectures. Our experimental settings follow that of [Cazenavette et al.](#page-9-4) [\(2022\)](#page-9-4) and  $SRe<sup>2</sup>L$  [Yin](#page-11-4) [et al.](#page-11-4) [\(2024\)](#page-11-4): we employ a ConvNet for distillation, with three convolutional blocks for CIFAR-10 and CIFAR-100 and four convolutional blocks for Tiny-ImageNet respectively, each containing a 128-kernel convolutional layer, an instance normalization layer [\(Ulyanov et al., 2016\)](#page-10-13), a ReLU activation function [\(Nair & Hinton, 2010\)](#page-10-14) and an average pooling layer. For larger datasets (i.e., ImageNet-1K), we employ ResNet18 [\(He et al., 2016\)](#page-10-15) as the backbone of  $SRe<sup>2</sup>L$ . To compute CMI, we employ multiple pre-trained ResNet18 trained on each corresponding dataset. AlexNet, VGG11 and ResNet18 are used to assess the cross-architecture performance.

**Evaluation.** Following previous works, we generate synthetic datasets with  $IPC=1$ , 10, 50, and 100, then train multiple randomly initialized networks to evaluate their performance. All the training configurations follow the corresponding base distillation methods. We compute the Top-1 accuracy on the test set of the real dataset, repeating each experiment five times to calculate the mean.

utilize multi-formulation introduced by IDC.

<span id="page-6-0"></span>Table 2: Synthetic dataset performance(%) under Table 3: Performance of the CMI enhanced loss IPC=1. CMI can only be applied to methods that on higher resolution datasets. †: the reported error range is reproduced by us.

|                                                                        |                                                         |                                                         |                                                    | Method                                         |                                                                 | TinyImageNet                                                       |                                                                   |
|------------------------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|----------------------------------------------------|------------------------------------------------|-----------------------------------------------------------------|--------------------------------------------------------------------|-------------------------------------------------------------------|
| Method                                                                 | <b>SVHN</b>                                             | CIFAR10                                                 | CIFAR100                                           | <b>IPC</b>                                     |                                                                 | 10                                                                 | 50                                                                |
| IDM Zhao et al. (2023)<br>IID-IDM Deng et al. (2024)<br><b>IDM+CMI</b> | $65.3 \pm 0.3$<br>$66.3 \pm 0.1$<br>$66.4 \pm 0.3$      | $45.6 \pm 0.7$<br>$47.1 \pm 0.1$<br>$47.5 \pm 0.2$      | $20.1 \pm 0.3$<br>$24.6 \pm 0.1$<br>$24.7 \pm 0.5$ | MTT Cazenavette et al. (2022)<br>MTT+CMI<br>Δ  | $8.8 \pm 0.3$<br>۰<br>٠                                         | $23.2 \pm 0.2$<br>$24.1 \pm 0.3$<br>(1.9 <sup>†</sup> )            | $28.0 \pm 0.3$<br>$28.8 \pm 0.3$<br>(0.8 <sup>†</sup> )           |
| Δ<br>IDC Kim et al. $(2022)$<br>DREAM Liu et al. (2023)                | (1.1 <sup>†</sup> )<br>$68.5 \pm 0.9$<br>$69.8 \pm 0.8$ | (1.9 <sup>†</sup> )<br>$50.6 \pm 0.4$<br>$51.1 \pm 0.3$ | (4.6 <sup>†</sup> )<br>٠<br>$29.5 \pm 0.3$         | IDM Zhao et al. (2023)<br><b>IDM+CMI</b><br>Δ  | $9.8 + 0.2$<br>$10.4 \pm 0.3$<br>(0.6 <sup>†</sup> )            | $21.9 \pm 0.2$<br>$23.7 \pm 0.3$<br>(1.8 <sup>†</sup> )            | $26.2 \pm 0.3$<br>$27.7 \pm 0.1$<br>(1.5 <sup>†</sup> )           |
| $IDC+CMI$                                                              | $70.0 \pm 0.5$<br>(1.5 <sup>†</sup> )                   | $51.6 + 0.3$<br>(1.0 <sup>†</sup> )                     | $30.1 \pm 0.1$                                     | IDC Kim et al. $(2022)$<br><b>IDC+CMI</b><br>Δ | $9.5 + 0.3$ <sup>†</sup><br>$10.4 + 0.3$<br>(0.9 <sup>†</sup> ) | $24.5 \pm 0.4$ <sup>†</sup><br>$25.7 + 0.3$<br>(1.2 <sup>†</sup> ) | $29.0 \pm 0.2^{\dagger}$<br>$30.1 \pm 0.1$<br>(1.1 <sup>†</sup> ) |

### 5.2 PERFORMANCE IMPROVEMENT

We demonstrate the effectiveness of our proposed method by applying the CMI-enhanced loss to previous optimization-based distillation methods on CIFAR-10/100, Tiny-ImageNet, and ImageNet-1K. Our approach is compared with state-of-the-art baselines for various distillation methods on low-resolution datasets, with IPC set to 10 and 50 for all benchmarks, as using only one distilled image per class makes empirical computation of CMI infeasible. Additionally, we evaluate methods that employ multi-formulation techniques, such as IDM. As shown in Table [1](#page-5-0) and Table [2,](#page-6-0) our method consistently yields significant performance improvements across different datasets and compression ratios, with modest gains in trajectory matching and substantial gains in feature and gradient matching methods. Notably, CMI + DSA surpasses DSA by 5.5% on CIFAR-10 with IPC=50.

periments on representative optimization-based dis- $SRe^{2}L$  on ImageNet-1K. tillation methods with various objectives on Tiny-ImageNet. As shown in Table [3,](#page-6-0) noticeable improvements were achieved under different settings. For ImageNet-1K, we applied the CMI enhanced loss to  $SRe<sup>2</sup>L$ , which decouples the distillation process by using a pre-trained model and optimizes the synthetic

For higher-resolution datasets, we conducted ex- Table 4: Performance improvement with

<span id="page-6-1"></span>

| Method                  | ImageNet-1K     |                 |                 |
|-------------------------|-----------------|-----------------|-----------------|
|                         | IPC             | 10              | 50              |
| SRe2L Yin et al. (2024) | 21.3±0.6        | 46.8±0.2        | 52.8±0.3        |
| <b>SRe2L+CMI</b>        | <b>24.2±0.3</b> | <b>49.1±0.1</b> | <b>54.6±0.2</b> |
| Δ                       | (2.9↑)          | (2.3↑)          | (1.8↑)          |

| Method                  | AlexNet                     | VGG11                     | ResNet18               | Method                        | <b>SVHN</b>    | CIFAR <sub>10</sub> | CIFAR100       |
|-------------------------|-----------------------------|---------------------------|------------------------|-------------------------------|----------------|---------------------|----------------|
| MTT Zhao et al. (2023)  | $34.2 \pm 2.6$              | $50.3 \pm 0.8$            | $46.4 \pm 0.6$         | MTT Cazenavette et al. (2022) | $79.9 \pm 0.1$ | $65.3 \pm 0.4$      | $39.7 \pm 0.4$ |
| <b>MTT+CMI</b>          | $34.9 \pm 1.1$              | $51.3 \pm 0.6$            | $47.7 \pm 0.7$         | SeqMatch Du et al. (2024)     | $80.2 + 0.6$   | $66.2 \pm 0.6$      | $41.9 \pm 0.5$ |
| Δ                       | (0.7 <sup>†</sup> )         | (1.0 <sup>†</sup> )       | (1.3 <sup>†</sup> )    | SeqMatch+CMI                  | $81.2 \pm 0.4$ | $67.3 \pm 0.5$      | $43.1 \pm 0.3$ |
| IDM Zhao et al. (2023)  | $44.6 \pm 0.8$              | $47.8 + 1.1$              | $44.6 \pm 0.4$         | IDM Zhao et al. (2023)        | $81.0 + 0.1$   | $58.6 \pm 0.1$      | $45.1 + 0.1$   |
| <b>IDM+CMI</b>          | $54.0 \pm 0.4$              | $57.0 \pm 0.2$            | $53.7 \pm 0.2$         | $IID$ Deng et al. $(2024)$    | $82.1 \pm 0.3$ | $59.9 \pm 0.2$      | $45.7 \pm 0.4$ |
| Δ                       | (9.4 <sup>†</sup> )         | (9.2 <sup>†</sup> )       | (9.1 <sup>†</sup> )    | $IID+CMI$                     | $85.1 + 0.3$   | $63.0 + 0.3$        | $47.9 + 0.3$   |
| IDC Kim et al. $(2022)$ | $63.5 \pm 0.4$ <sup>†</sup> | $64.4 + 0.4$ <sup>†</sup> | $61.2 + 0.2^{\dagger}$ | IDC Kim et al. $(2022)$       | $87.5 + 0.3$   | $67.5 \pm 0.5$      | $45.1 \pm 0.4$ |
| <b>IDC+CMI</b>          | $64.4 \pm 0.3$              | $67.4 \pm 0.2$            | $66.0 \pm 0.4$         | DREAM Liu et al. (2023)       | $87.9 + 0.4$   | $69.4 \pm 0.4$      | $46.8 \pm 0.7$ |
| Δ                       | (0.9 <sup>†</sup> )         | (3.0 <sup>†</sup> )       | (4.8 <sup>†</sup> )    | <b>DREAM+CMI</b>              | $88.2 + 0.1$   | $71.5 + 0.1$        | $47.9 + 0.3$   |

<span id="page-7-0"></span>Table 5: Cross-architecture performance on CI-Table 6: Performance obtained simultaneously ap-FAR10 under IPC=10 with differnent distilla-plying previous techniques and CMI enhance loss on tion methods. different distillation methods.

dataset through cross-entropy loss and batch normalization statistics. This enables it to handle large datasets and achieve reliable performance. However,  $SRe<sup>2</sup>L$  faces the same challenge of excessive dataset complexity as other optimization-based distillation methods. Following the original settings, we validate the effectiveness of our proposed method with the results shown in Table [4](#page-6-1) for larger synthetic datasets (e.g., IPC=100).

#### 5.3 CROSS-ARCHITECTURE GENERALIZATION

We evaluated the performance of architectures different from the backbone used to distill CIFAR-10. Following the settings in previous works, our experiments included widely used models such as AlexNet, VGG11, and ResNet18, with consistent optimizers and hyperparameters. Following the same pipeline used to evaluate the same-architecture performance, each experiment is repeated five times and the mean values are reported as shown in Table [5.](#page-7-0)

We can see that the considerable cross-architecture generalization performance improvement achieved by our method, especially when using IDM and IDC as the basic distillation method. By leveraging pre-trained models with diverse architectures in the distillation phase, reducing the CMI value effectively constrains the complexity of the synthetic dataset. The results indicate that our synthetic dataset is robust to changes in network architectures.

### 5.4 COMBINATION WITH DIFFERENT TECHNIQUES

Unlike existing dataset distillation techniques, our method achieves stable performance improvements by reducing the complexity of the synthetic dataset itself. Furthermore, Our approach can serve as a unified technique added to existing methods, and experiments have demonstrated its generality. Table [6](#page-7-0) demonstrates the effectiveness of combining existing techniques with CMI enhanced loss on different basic distillation methods on CIFAR-10 with IPC=10. It can be observed that different optimization directions enable our method to be integrated with other techniques, leading to considerable performance improvements.

<span id="page-7-1"></span>Image /page/7/Figure/9 description: The image displays three plots. The first plot, labeled "(a) IDC", shows the relationship between the weighting parameter \lambda and accuracy. The x-axis ranges from 0 to 500, and the y-axis ranges from 66 to 71. The plot shows an increasing trend in accuracy as \lambda increases from 0 to 100, reaching a peak of approximately 70 at \lambda=100, and then decreasing as \lambda increases further. The second plot, labeled "(b) IDM", illustrates the relationship between \lambda and accuracy. The x-axis spans from 0 to 5000, and the y-axis ranges from 58 to 63. This plot also shows an initial increase in accuracy with \lambda, peaking around 62.2 at \lambda=2000, followed by a slight decrease. The third plot, labeled "Figure 3: Accuracy curve w. and w.o. CMI constraint", presents accuracy over epochs for four different methods: IDC, IDC+CMI, IDM, and IDM+CMI. The x-axis represents epochs from 0 to 1000, and the y-axis shows accuracy from 46 to 70. The IDC+CMI and IDC curves show higher accuracy compared to IDM and IDM+CMI, with IDC+CMI achieving the highest accuracy. The figure caption indicates that Figure 2 is an ablation study on the weighting parameter \lambda.

CMI constraint.

5.5 ABLATION STUDIES AND ANALYSIS

We conducted additional experiments to verify the effectiveness of CMI enhanced loss. Unless otherwise specified, the experiments were conducted on CIFAR-10 with IPC=10.

Sample distribution. As defined in Equation [\(5\)](#page-4-0), the centroid of the DNN's outputs from a specific class  $Y = y$ , where  $y \in [C]$ , is the average of  $P_X$  with respect to the conditional distribution  $\mathbb{P}_{(X|Y)}(\cdot|Y=y)$ . Referring to Section [4.2,](#page-4-1) fixing the label  $Y=y$ , the CMI( $f, Y=y$ ) measures the distance between the centroid  $Q^y$  and the output  $P_X$  for class y. Thus, CMI( $f, Y = y$ ) indicates how concentrated the output distributions  $P_X$  are around the centroid  $Q^y$  for class y.

To more intuitively demonstrate how the strategy of minimizing CMI values affects the complexity of the synthetic dataset, we visualize the t-SNE graphs of the synthetic dataset in both feature space and probability space using a pre-trained ConvNet. As shown in Figure [1,](#page-1-0) even when evaluating the dataset using different network architectures, the clusters for the synthetic dataset with lower CMI values are more concentrated. Furthermore, reducing CMI values in the feature space also effectively concentrates the dataset clusters around the class center in probability space.

**Evaluation of hyper-parameter**  $\lambda$ **.** We conducted a sensitivity analysis focusing on the weighting parameter  $\lambda$  in Section [4.3.](#page-4-2) The results shown in Figure [2](#page-7-1) indicate that the CMI value is too small compared to  $\mathcal{L}_{DD}$  in practical applications, requiring a larger  $\lambda$  to achieve the desired constraint effect. To achieve this, the value of  $\lambda$  must be adjusted according to  $\mathcal{L}_{DD}$ . However, a too large  $\lambda$  often shifts the optimization objective towards clustering the synthetic dataset around the centroids, leading to degraded performance. Varying  $\lambda$  between 500 and 5000 resulted in only a 1.5% performance difference on IDM, indicating moderate sensitivity.

Training Efficiency. In addition to the stable performance improvements shown in Table [1,](#page-5-0) we visualized the accuracy curve during training by applying the CMI enhanced loss with different distillation methods. As shown in Figure [3,](#page-7-1) unlike previous methods that typically require extensive training iterations to converge, our proposed method achieves comparable performance with significantly fewer iterations (e.g., one-fifth and one-tenth of the epochs required on IDC and IDM respectively) and further improve the performance with additional iterations, more visualizations can be seen in Appendix [A.3.](#page-13-0)

CMI Constraint Performance under different Settings. We explore multiple settings for computing the empirical CMI value to more comprehensively assess the effectiveness of CMI enhanced loss: (1) *Pre-trained*: we investigate whether it is necessary to pre-train the surrogate model for computing CMI value on the corresponding dataset; (2) *Architecture*: we compute CMI using surrogate models with different network architectures; (3) *Space*: whether computing CMI in probability space or feature space leads to better constraint effects.

<span id="page-8-0"></span>Table 7: Ablation study on different settings of computing CMI value. PT indicates pretrained or not.

| Space       | PT | Arch            |                 |
|-------------|----|-----------------|-----------------|
|             |    | ConvNet         | ResNet18        |
| Probability | -  | 67.7±0.1        | 67.0±0.4        |
|             | ✓  | <b>68.1±0.2</b> | <b>67.8±0.3</b> |
| Feature     | -  | 67.3±0.2        | 67.9±0.4        |
|             | ✓  | <b>68.7±0.3</b> | <b>70.0±0.3</b> |

Table [7](#page-8-0) illustrates the results of computing and minimizing the CMI value under different settings. We observe that selecting an effective surrogate network architecture for CMI calculation and pre-training the model is essential, as it allows the model to generate more accurate class centroids for computing CMI values. Additionally, better results are achieved by computing CMI in feature space rather than in probability space. We hypothesize that computing CMI in probability space makes the constraint more dependent on the certain model architecture. Note that to align with the baseline, all settings related to pre-training are consistent with IID [\(Deng et al., 2024\)](#page-9-2).

#### 5.6 VISUALIZATIONS

We compare the distillation results with and without the proposed CMI enhanced loss in Figure [4.](#page-8-1) Unlike the t-SNE results in Figure [1,](#page-1-0) the constraint has minimal impact on the generated images, with differences nearly imperceptible to the human eye. This intriguing phenomenon suggests that CMI enhances performance without making substantial changes to the pixel space, offering a method that differs from existing approaches by having minimal impact on the images. Additional visualizations comparison of the distilled datasets with different distillation methods are provided in Appendix [A.8.](#page-15-0)

<span id="page-8-1"></span>Image /page/8/Picture/11 description: The image displays two grids of nine images each. The left grid contains nine blurry, abstract images. The middle row of the left grid shows two images of cars, one from the front and one from the side. The right grid also contains nine blurry, abstract images. The middle row of the right grid also shows two images of cars, similar to the left grid, one from the front and one from the side.

Figure 4: Visualization comparison between DSA (Left column) and DSA with CMI constraint (Right column).

## 6 CONCLUSION

In this paper, we present a novel conditional mutual information (CMI) enhanced loss for dataset distillation by analyzing and reducing the class-aware complexity of synthetic datasets. The proposed method computes and minimizes the empirical conditional mutual information of a pre-trained model, effectively addressing the challenges faced by previous dataset distillation approaches. Extensive experiments demonstrate the effectiveness of the CMI constraint in enhancing the performance of existing dataset distillation methods across various benchmark datasets. This novel approach emphasizes the intrinsic properties of the dataset itself, contributing to an advanced methodology for dataset distillation.

Acknowledgement. This work is supported in part by the National Natural Science Foundation of China under grant 62171248, 62301189, Peng Cheng Laboratory (PCL2023A08), and Shenzhen Science and Technology Program under Grant KJZD20240903103702004, JCYJ20220818101012025, RCBS20221008093124061, GXWD20220811172936001.

## REFERENCES

- <span id="page-9-4"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-9-10"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. *arXiv preprint arXiv:2305.01649*, 2023.
- <span id="page-9-3"></span>Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-9-5"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pp. 6565–6590. PMLR, 2023.
- <span id="page-9-8"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pp. 248–255. Ieee, 2009.
- <span id="page-9-2"></span>Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 17057–17066, 2024.
- <span id="page-9-1"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pp. 5378–5396. PMLR, 2022.
- <span id="page-9-6"></span>Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-9-9"></span>Jiawei Du, Juncheng Hu, Wenxin Huang, Joey Tianyi Zhou, et al. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. *Advances in neural information processing systems*, 37:119443–119465, 2025.
- <span id="page-9-7"></span>Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021.
- <span id="page-9-11"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 15793–15803, 2024a.
- <span id="page-9-0"></span>Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-restricted online continual learning. In *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*, 2024b.

- <span id="page-10-15"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-10-4"></span>Yang He, Lingao Xiao, and Joey Tianyi Zhou. You only condense once: Two rules for pruning condensed datasets. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-10-6"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102–11118. PMLR, 2022.
- <span id="page-10-11"></span>A Krizhevsky. Learning multiple layers of features from tiny images. *Master's thesis, University of Tront*, 2009.
- <span id="page-10-12"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-10-5"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pp. 12352–12364. PMLR, 2022.
- <span id="page-10-7"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 17314–17324, 2023.
- <span id="page-10-14"></span>Vinod Nair and Geoffrey E Hinton. Rectified linear units improve restricted boltzmann machines. In *Proceedings of the 27th international conference on machine learning (ICML-10)*, pp. 807–814, 2010.
- <span id="page-10-0"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *International Conference on Learning Representations*, 2021a. URL [https:](https://openreview.net/forum?id=l-PrrQrK0QR) [//openreview.net/forum?id=l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR).
- <span id="page-10-1"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021b.
- <span id="page-10-3"></span>Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in Neural Information Processing Systems*, 34: 20596–20607, 2021.
- <span id="page-10-8"></span>Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-10-10"></span>Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *Proceedings of the 21st international conference on pattern recognition (ICPR2012)*, pp. 3288–3291. IEEE, 2012.
- <span id="page-10-2"></span>Yuzhang Shang, Zhihang Yuan, and Yan Yan. Mim4dd: Mutual information maximization for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-10-16"></span>Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. Dˆ 4: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 5809–5818, 2024.
- <span id="page-10-9"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*, 2019. URL [https://openreview.](https://openreview.net/forum?id=BJlxm30cKm) [net/forum?id=BJlxm30cKm](https://openreview.net/forum?id=BJlxm30cKm).
- <span id="page-10-13"></span>Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016.

- <span id="page-11-8"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-11-14"></span>Shaobo Wang, Yantai Yang, Qilong Wang, Kaixin Li, Linfeng Zhang, and Junchi Yan. Not all samples should be utilized equally: Towards understanding and improving dataset distillation. *arXiv preprint arXiv:2408.12483*, 2024a.
- <span id="page-11-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-1"></span>Yuan Wang, Huazhu Fu, Renuga Kanagavelu, Qingsong Wei, Yong Liu, and Rick Siow Mong Goh. An aggregation-free federated learning for tackling data heterogeneity. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 26233–26242, 2024b.
- <span id="page-11-11"></span>Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, pp. 1121–1128, 2009.
- <span id="page-11-7"></span>En-Hui Yang, Shayan Mohajer Hamidi, Linfeng Ye, Renhao Tan, and Beverly Yang. Conditional mutual information constrained deep learning for classification. *arXiv preprint arXiv:2309.09123*, 2023.
- <span id="page-11-6"></span>William Yang, Ye Zhu, Zhiwei Deng, and Olga Russakovsky. What is dataset distillation learning? *arXiv preprint arXiv:2406.04284*, 2024.
- <span id="page-11-10"></span>Linfeng Ye, Shayan Mohajer Hamidi, Renhao Tan, and En-Hui Yang. Bayes conditional distribution estimation for knowledge distillation based on conditional mutual information. *arXiv preprint arXiv:2401.08732*, 2024.
- <span id="page-11-4"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-11-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021a.
- <span id="page-11-9"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021b.
- <span id="page-11-12"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-11-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021.
- <span id="page-11-13"></span>Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 7856–7865, 2023.
- <span id="page-11-5"></span>Haizhong Zheng, Rui Liu, Fan Lai, and Atul Prakash. Coverage-centric coreset selection for high pruning rates. *arXiv preprint arXiv:2210.15809*, 2022.
- <span id="page-11-15"></span>Xinhao Zhong, Hao Fang, Bin Chen, Xulin Gu, Tao Dai, Meikang Qiu, and Shu-Tao Xia. Hierarchical features matter: A deep exploration of gan priors for improved dataset distillation. *arXiv preprint arXiv:2406.05704*, 2024a.
- <span id="page-11-16"></span>Xinhao Zhong, Shuoyang Sun, Xulin Gu, Zhaoyang Xu, Yaowei Wang, Jianlong Wu, and Bin Chen. Efficient dataset distillation via diffusion-driven patch selection for improved generalization. *arXiv preprint arXiv:2412.09959*, 2024b.

- <span id="page-12-0"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho (eds.), *Advances in Neural Information Processing Systems*, 2022. URL [https://openreview.net/forum?](https://openreview.net/forum?id=2clwrA2tfik) [id=2clwrA2tfik](https://openreview.net/forum?id=2clwrA2tfik).
- <span id="page-12-2"></span>Chenyang Zhu, Kai Li, Yue Ma, Chunming He, and Li Xiu. Multibooth: Towards generating all your concepts in an image from text. *arXiv preprint arXiv:2404.14239*, 2024a.
- <span id="page-12-1"></span>Chenyang Zhu, Kai Li, Yue Ma, Longxiang Tang, Chengyu Fang, Chubin Chen, Qifeng Chen, and Xiu Li. Instantswap: Fast customized concept swapping across sharp shape differences. *arXiv preprint arXiv:2412.01197*, 2024b.

## A APPENDIX

### A.1 COMPARISON WITH RELATED WORKS

Several recent works also focus on optimizing the dataset distillation process from the perspective of the synthetic dataset itself. IID [\(Deng](#page-9-2) [et al., 2024\)](#page-9-2) proposes using the L2 norm to aggregate the features of the synthetic dataset, thereby constraining its classification boundary. SDC [\(Wang et al., 2024a\)](#page-11-14) suggests reducing the complexity of the synthetic dataset using

<span id="page-13-1"></span>

|                      | Table 8: Performance comparison with SDC under |  |
|----------------------|------------------------------------------------|--|
| differnent settings. |                                                |  |

| Dataset                          | SVHN                                                                                                                     |                                                                                                                          | TinyImageNet                                                                                                             |                                                                                                                          |
|----------------------------------|--------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|
|                                  | DSA                                                                                                                      |                                                                                                                          | MTT                                                                                                                      |                                                                                                                          |
| Method<br>IPC                    | 10                                                                                                                       | 50                                                                                                                       | 10                                                                                                                       | 50                                                                                                                       |
| SDC (Wang et al., 2024a)<br>+CMI | 79.4 <span style="vertical-align:sub;">±</span> 0.4<br><b>80.5</b> <span style="vertical-align:sub;">±</span> <b>0.2</b> | 85.3 <span style="vertical-align:sub;">±</span> 0.4<br><b>85.5</b> <span style="vertical-align:sub;">±</span> <b>0.3</b> | 20.7 <span style="vertical-align:sub;">±</span> 0.2<br><b>24.1</b> <span style="vertical-align:sub;">±</span> <b>0.3</b> | 28.0 <span style="vertical-align:sub;">±</span> 0.2<br><b>28.8</b> <span style="vertical-align:sub;">±</span> <b>0.3</b> |

GraDN-Score [\(Paul et al., 2021\)](#page-10-3) constraints. However, IID restricts its method to distribution matching, and the L2 norm constraints fail to provide stable improvements due to the problem of dimensionality explosion. Notably, the performance comparison in Table [1](#page-5-0) is based on IID, which additionally optimizes the variance matching between the synthetic and original datasets.

Although SDC shares a similar starting point with our approach, it merely uses the gradient of the distillation loss as a constraint and restricts its method to gradient matching. This approach is constrained by the proxy backbone, leading to weaker constraint effects and limited performance improvements due to the same optimization objective with the distillation loss. A comparison of experimental results is shown in Table [8.](#page-13-1)

### A.2 REGULARIZATION PROPRIETY

Here, we analyze the trends of dataset distillation loss  $\mathcal{L}_{DD}$  during training as shown in Section [4.3.](#page-4-2)  $\mathcal{L}_{DD}$  refers to any dataset distillation loss. By incorporating CMI as a regularization term during dataset distillation, we successfully constrained the complexity of the synthetic dataset. As shown in Figure [5,](#page-13-2) we present the  $\mathcal{L}_{DD}$ curves throughout the training process. It can be observed that adding CMI as a constraint decreased the main function losses, demonstrating that our proposed method effectively reduces the complexity of the synthetic dataset.

<span id="page-13-2"></span>Image /page/13/Figure/10 description: The image displays a line graph illustrating the accuracy of two models, IDC and IDC+CMI, over 1000 epochs. The x-axis represents the epochs, ranging from 0 to 1000, and is labeled "Epoch". The y-axis represents the accuracy, ranging from 4 to 13, and is labeled "Accuracy". The IDC model is represented by a dashed blue line, and the IDC+CMI model is represented by a solid orange line. Both lines show a sharp decrease in accuracy in the initial epochs, followed by a gradual stabilization. The IDC+CMI model generally shows slightly lower accuracy than the IDC model throughout the observed epochs, with some fluctuations.

Figure 5: The  $\mathcal{L}_{IDC}$  curves while training on CIFAR10 under IPC=10.

<span id="page-13-3"></span>Image /page/13/Figure/12 description: The image displays three line graphs side-by-side, each plotting accuracy against epochs. The x-axis for all graphs represents epochs, ranging from 0 to 1000. The y-axis represents accuracy, with scales varying slightly across the graphs. Graph (a) is titled "The accuracy curve of adding CMI constraint to DM." It shows two lines: a teal line labeled "DM" and a brown line labeled "DM+CMI." The brown line consistently shows higher accuracy than the teal line, reaching approximately 50.5% accuracy at 150 epochs and plateauing around 51% thereafter. The teal line plateaus around 48.5%. Graph (b) is titled "The accuracy curve of adding CMI constraint to DSA." It also features a teal line for "DSA" and a brown line for "DSA+CMI." Similar to graph (a), the brown line is above the teal line, reaching about 51.5% at 150 epochs and plateauing around 52%. The teal line plateaus around 49.5%. Graph (c) is titled "The accuracy curve of adding CMI constraint to MTT." This graph has a y-axis ranging from 26 to 62. It shows a teal line for "DM" and a brown line for "DM+CMI." The brown line starts at approximately 27% accuracy at epoch 0, rises sharply to about 54% at 100 epochs, and continues to increase, reaching around 62% by 400 epochs and staying there. The teal line starts similarly but plateaus earlier and at a lower accuracy, around 58%.

Figure 6: Applying CMI constraint brings stable performance and efficiency improvements with different optimization objectives.

<span id="page-13-0"></span>

#### A.3 EFFICIENT TRAINING PROCESS

By deploying the CMI constraint, we not only achieve performance improvements but also significant computational acceleration. As shown in Figure [6,](#page-13-3) we present the accuracy curves after incorporating the CMI constraint on DSA [\(Zhao & Bilen, 2021b\)](#page-11-9), DM [\(Zhao & Bilen, 2021a\)](#page-11-3), and MTT [\(Cazenavette et al., 2022\)](#page-9-4). It can be seen that our method achieves considerable computational acceleration with the same initialization settings. These results indicate that actively constraining the

complexity of the synthetic dataset during training benefits both new network training and reduces gradient bias, leading to faster convergence.

Algorithm 1 Disentangled computation of applying CMI constraint

**Input**: L: distillation loss;  $f_{\theta^*}$ : pre-trained classifier; T: real dataset;  $\phi(\cdot)$ : proxy information extractor;  $M$ : distance metric;  $K$ : training iteration number  $C$ : class number;

1:  $\mathcal{L} = 0$ 2: Randomly initialize  $S$ 3: for i  $\leftarrow$  0 to  $K - 1$  do 4: Randomly sample  $f_{\theta^*}$ 5:  $\mathcal{L} = \mathcal{L} + \mathcal{M}(\phi(\mathcal{S}), \phi(\mathcal{T}))$ 6: for  $\mathbf{i} \leftarrow 0$  to  $C - 1$  do 7:  $\text{CMI}_{\text{emp.c}}(\mathcal{S}) = \sum_{\mathbf{s} \in \mathcal{S}_j} \text{KL} \left( P_{\mathbf{s}} \| Q_{\text{emp}}^j \right)$ 8:  $\mathcal{L} = \mathcal{L} + \lambda \cdot \text{CMI}_{\text{emp.c}}(\mathcal{S})$ <br>9: end for end for 10:  $S \leftarrow SGD(S; \mathcal{L})$ 11: end for **Output:** Synthetic dataset  $S$ 

<span id="page-14-0"></span>

#### A.4 DISTILLED DATASET VISUALIZATION

Simultaneously computing and minimizing the CMI value for the entire synthetic dataset during distillation can lead to significant memory consumption as IPC and the number of categories increase. To address this issue, we propose decomposing the CMI computation shown in Section [4.2](#page-4-1) by category and embedding it into the original distillation process, as shown in Algorithm [1.](#page-14-0) Since the computation of the empirical CMI value is category-independent, the decoupled calculation can equivalently represent the original equation without any loss of fidelity. This approach greatly alleviates the memory overhead while achieving consistent results.

#### A.5 ADDITIONAL TRAINING COST

We further analyze the extra time consumption caused by applying CMI constraint in Table [9](#page-14-1) on a single NVIDIA GeForce RTX 3090. For IDM, adding the CMI constraint nearly doubles the time consumption; however, considering that we need only one-tenth to one-quarter of the distillation iterations to achieve the original performance, using the CMI constraint can save up to 50% of the time. In contrast, for IDC, the additional time cost introduced by the CMI constraint is negligible compared to the method

<span id="page-14-1"></span>Table 9: Additional time consumption (s) per iteration of adding CMI constraint.

| Method                             | CIFAR10      |              | CIFAR100       |                |
|------------------------------------|--------------|--------------|----------------|----------------|
|                                    | 10           | 50           | 10             | 50             |
| IPC                                | 10           | 50           | 10             | 50             |
| IDM (Zhao et al., 2023)<br>IDM+CMI | 0.5<br>1.0   | 0.6<br>1.2   | 5.3<br>10.2    | 7.1<br>11.2    |
| IDC (Kim et al., 2022)<br>IDC+CMI  | 18.2<br>22.6 | 22.4<br>24.2 | 190.5<br>222.5 | 215.7<br>236.8 |

itself. Moreover, since applying CMI enhanced loss can reduce the number of distillation iterations to one-fifth of the iterations to achieve the original performance, deploying CMI is justified. Notably, we calculate and optimize CMI at each iteration. However, to save computational resources, we could reduce CMI calculations to once every five iterations while maintaining performance, thus speeding up distillation.

### A.6 ABLATION STUDY ON MODEL ARCHITECTURES

To further investigate the impact of teacher model architecture, we conduct more comprehensive ablation experiments. Under the CIFAR10 with IPC=10, we utilized simply pre-trained ConvNet, AlexNet, ResNet18, and VGG11 as proxy models to compute CMI for both DM and DSA.

We sequentially report the following metrics: the depth (i.e., number of convolution layer), the dimensionality  $M$  of the feature space, and the accuracy of the simply pre-trained proxy model; the start CMI value of the synthetic dataset, the CMI value at the end of the optimization process w.o. and

| Method             | ConvNet | AlexNet | VGG11  | ResNet18    | Method             | ConvNet | AlexNet | VGG11       | ResNet18 |
|--------------------|---------|---------|--------|-------------|--------------------|---------|---------|-------------|----------|
| Depth              | 3       | 5       | 8      | 17          | Depth              | 3       | 5       | 8           | 17       |
| Dimension          | 2048    | 4096    | 512    | 512         | Dimension          | 2048    | 4096    | 512         | 512      |
| Model Acc $(%)$    | 79.8    | 80.2    | 79.4   | 79.5        | Model Acc $(%)$    | 79.8    | 80.2    | 79.4        | 79.5     |
| Start value        | 0.1045  | 1.9338  | 0.0021 | 0.0016      | Start value        | 0.1045  | 1.9338  | 0.0021      | 0.0016   |
| End value w.o. CMI | 0.0866  | 1.6217  | 0.0059 | 0.0066      | End value w.o. CMI | 0.0825  | 1.8755  | 0.0076      | 0.0051   |
| End value w. CMI   | 0.0326  | 0.6642  | 0.0006 | 0.0004      | End value w. CMI   | 0.0455  | 0.8875  | 0.0005      | 0.0004   |
| Performance $(%)$  | 51.2    | 50.8    | 52.4   | <b>52.9</b> | Performance $(%)$  | 53.2    | 53.4    | <b>54.8</b> | 54.7     |

Table 10: Ablation study of model architecture on Table 11: Ablation study of model architecture on DM. DSA.

w. CMI constraint of the synthetic dataset, and the accuracy of the model trained using the synthetic dataset. As shown in table below, it can be observed that the dimensionality of the feature space has a greater impact on CMI values than network depth, and CMI effectively reflects the degree of clustering in  $\hat{Z}$  under the same dimensional conditions. Nonetheless, the proposed CMI constraint effectively regulates the complexity of the synthetic dataset, demonstrating the strong generalization capability of our method.

### A.7 LIMITATION AND FUTURE WORK

**IPC=1.** Minimizing CMI is equal to reduce the Kullback-Leibler (KL) divergence  $D(\cdot||\cdot)$  between  $P_S$  and  $P_{\hat{Z}|y}$ , hence, when the IPC is 1, the CMI value is 0.

**Proxy Model.** Computing CMI requires choosing an appropriate proxy model architecture to provide  $\ddot{Z}$  when  $S$  is used as the input, and selecting an architecture often relies on heuristic methods. Besides, similar to IID [\(Deng et al., 2024\)](#page-9-2), constraining the synthetic datasets with CMI needs multiple pre-trained teacher networks, which leads to a substantial consumption overhead.

Here, we suggest a future direction to better utilize the proxy model; mixing the proxy model could potentially provide a more informative CMI constraint. And One can try to perturb a single model to obtain multiple teacher models which is similar to what DWA [\(Du et al., 2025\)](#page-9-9) did.

Recent years, some dataset distillation methods have introduced GANs as a parameterization technique [\(Cazenavette et al., 2023;](#page-9-10) [Zhong et al., 2024a\)](#page-11-15). Moreover, with the successful application of diffusion models in various fields [\(Zhu et al., 2024b](#page-12-1)[;a\)](#page-12-2), some dataset distillation methods have begun using diffusion models to directly generate images [\(Gu et al., 2024a;](#page-9-11) [Su et al., 2024;](#page-10-16) [Zhong et al.,](#page-11-16) [2024b\)](#page-11-16). In future work, we will explore the application of CMI to these types of dataset distillation methods.

<span id="page-15-0"></span>

#### A.8 DISTILLED DATASET VISUALIZATION

To demonstrate the effectiveness of our method, we present the t-SNE visualization of the synthetic dataset with the CMI constraint on DSA, as shown in Figure [7.](#page-15-1) It can be observed that the CMI constraint provides strong regularization effects in both feature space and probability space, resulting in clearer class boundaries. Additionally, we visualize the effects of synthetic datasets produced using different datasets and distillation methods. As shown in Figures [8](#page-16-0) to [11,](#page-17-0) the CMI constraint not only significantly enhances performance but also introduces minimal perceptual distortion.

<span id="page-15-1"></span>Image /page/15/Figure/11 description: The image displays two sets of scatter plots, each comparing a 'Feature Space' and a 'Probability Space'. The left set, labeled (a) Synthetic dataset vs CMI constraint, shows two plots. The first plot, in the 'Feature Space', contains numerous points scattered across the graph, colored with various distinct colors, suggesting different categories or clusters that are not clearly separated. The second plot, in the 'Probability Space', shows a similar distribution of points, also colored distinctly, but with some tendency towards grouping. The right set, labeled (b) Synthetic dataset vs CMI constraint, also presents two plots. The 'Feature Space' plot here shows points that appear more clustered than in (a), with several distinct groups of colors. The 'Probability Space' plot for (b) exhibits even clearer separation of points into distinct clusters, with each cluster represented by a different color, indicating a more organized or separable data structure in this space compared to the feature space.

(a) Synthetic dataset *w.o.* CMI constraint.

(b) Synthetic dataset *w.* CMI constraint.

Figure 7: Visualization of the synthetic dataset generated by DSA with (a) high CMI value, and (b) low CMI value.

<span id="page-16-0"></span>Image /page/16/Picture/1 description: This image displays two grids of numbers, labeled (a) IDC and (b) IDC+CMI. Each grid is composed of smaller square cells, each containing a number. The numbers appear to be digits from 0 to 9, arranged in rows and columns. The grids are presented side-by-side, suggesting a comparison between the two methods, IDC and IDC+CMI, in displaying or generating these numbers.

(b) IDC+CMI

Figure 8: Visualization of the synthetic dataset generated by IDC on SVHN.

Image /page/16/Picture/5 description: The image displays two grids of generated images, labeled (a) DSA and (b) DSA+CMI. Each grid contains 10 rows and 10 columns of small, square images. The images in the top rows of both grids appear to be cars, while the subsequent rows show various animals, including dogs, cats, and horses, as well as some boats and abstract patterns. The images in grid (b) seem to exhibit slightly more detail and clarity compared to grid (a).

Figure 9: Visualization of the synthetic dataset generated by DSA on CIFAR10.

Image /page/17/Figure/1 description: The image displays two grids of smaller images, labeled (a) IDM and (b) IDM+CMI. Both grids contain numerous colorful images, with the top rows predominantly featuring apples of various colors. Below the apples, the grids show a variety of other images, including what appear to be insects, fish, and bottles. The overall impression is a comparison of image generation or representation techniques, with the two grids likely illustrating different results or methods.

Figure 10: Partial visualization of the synthetic dataset generated by IDM on CIFAR100.

<span id="page-17-0"></span>Image /page/17/Picture/3 description: The image displays two grids of small, square images, side-by-side. The left grid is labeled "(a) DM" and the right grid is labeled "(b) DM+CMI". Both grids contain approximately 64 small images arranged in an 8x8 pattern. The individual images within the grids are abstract and colorful, with a variety of textures and patterns, appearing to be generated images rather than photographs of real-world objects. Some images show hints of recognizable forms like fish or insects, but they are highly stylized and pixelated. The overall impression is a comparison of image generation results from two different methods, DM and DM+CMI.

Figure 11: Partial visualization of the synthetic dataset generated by DM on TinyImageNet.