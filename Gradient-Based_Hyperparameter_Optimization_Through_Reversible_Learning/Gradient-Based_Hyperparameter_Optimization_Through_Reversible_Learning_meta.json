{"table_of_contents": [{"title": "Gradient-based Hyperparameter Optimization through Reversible Learning", "heading_level": null, "page_id": 0, "polygon": [[62.25, 89.25], [533.70703125, 89.25], [533.70703125, 103.833984375], [62.25, 103.833984375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 198.75], [195.75, 198.75], [195.75, 210.375], [148.5, 210.375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 399.75], [132.60498046875, 399.75], [132.60498046875, 411.46875], [54.0, 411.46875]]}, {"title": "1.1. Contributions", "heading_level": null, "page_id": 0, "polygon": [[306.75, 677.25], [385.5, 677.25], [385.5, 687.97265625], [306.75, 687.97265625]]}, {"title": "2. Hypergradients", "heading_level": null, "page_id": 1, "polygon": [[54.0, 292.5], [148.5, 292.5], [148.5, 304.154296875], [54.0, 304.154296875]]}, {"title": "2.1. Reversible learning with exact arithmetic", "heading_level": null, "page_id": 1, "polygon": [[305.40234375, 141.75], [501.0, 141.75], [501.0, 152.0771484375], [305.40234375, 152.0771484375]]}, {"title": "2.2. Reversible learning with finite precision arithmetic", "heading_level": null, "page_id": 2, "polygon": [[54.0, 69.0], [289.5, 69.0], [289.5, 79.13232421875], [54.0, 79.13232421875]]}, {"title": "2.3. Optimal storage of discarded entropy", "heading_level": null, "page_id": 2, "polygon": [[54.0, 489.0], [232.6376953125, 489.0], [232.6376953125, 499.25390625], [54.0, 499.25390625]]}, {"title": "3. Experiments", "heading_level": null, "page_id": 3, "polygon": [[54.0, 68.25], [132.75, 68.25], [132.75, 79.857421875], [54.0, 79.857421875]]}, {"title": "3.1. Gradient-based optimization of gradient-based\noptimization", "heading_level": null, "page_id": 3, "polygon": [[54.0, 274.5], [273.75, 274.5], [273.75, 296.419921875], [54.0, 296.419921875]]}, {"title": "3.2. Optimizing regularization parameters", "heading_level": null, "page_id": 4, "polygon": [[54.0, 514.5], [236.25, 514.5], [236.25, 524.390625], [54.0, 524.390625]]}, {"title": "3.3. Optimizing training data", "heading_level": null, "page_id": 4, "polygon": [[306.0, 342.0], [432.0, 342.0], [432.0, 352.30078125], [306.0, 352.30078125]]}, {"title": "3.4. Optimizing initial parameters", "heading_level": null, "page_id": 5, "polygon": [[54.0, 69.0], [201.41015625, 69.0], [201.41015625, 79.6640625], [54.0, 79.6640625]]}, {"title": "3.5. Learning continuously parameterized architetures", "heading_level": null, "page_id": 5, "polygon": [[54.0, 196.5], [288.0, 196.5], [288.0, 206.701171875], [54.0, 206.701171875]]}, {"title": "3.6. Implementation Details", "heading_level": null, "page_id": 5, "polygon": [[306.0, 616.5], [426.0, 616.5], [426.0, 626.87109375], [306.0, 626.87109375]]}, {"title": "4. Limitations", "heading_level": null, "page_id": 6, "polygon": [[54.0, 162.0], [127.5, 162.0], [127.5, 173.63671875], [54.0, 173.63671875]]}, {"title": "5. Related work", "heading_level": null, "page_id": 6, "polygon": [[306.0, 399.0], [388.5, 399.0], [388.5, 410.30859375], [306.0, 410.30859375]]}, {"title": "6. Extensions and future work", "heading_level": null, "page_id": 7, "polygon": [[54.0, 324.75], [210.0, 324.75], [210.0, 336.83203125], [54.0, 336.83203125]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[54.0, 675.0], [125.25, 675.0], [125.25, 686.0390625], [54.0, 686.0390625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 7, "polygon": [[306.0, 234.0], [402.0, 234.0], [402.0, 245.56640625], [306.0, 245.56640625]]}, {"title": "Appendix: Forward vs. reverse-mode\ndifferentiation", "heading_level": null, "page_id": 7, "polygon": [[305.25, 342.0], [499.5, 342.0], [499.5, 365.8359375], [305.25, 365.8359375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 281.337890625], [111.75, 281.337890625], [111.75, 293.326171875], [54.0, 293.326171875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 83], ["Text", 9], ["SectionHeader", 4], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5819, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 755], ["Line", 101], ["TableCell", 43], ["Text", 7], ["ListItem", 3], ["SectionHeader", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 7838, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 641], ["Line", 100], ["TableCell", 31], ["Text", 8], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4517, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 117], ["Text", 10], ["Caption", 3], ["SectionHeader", 2], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1370, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 100], ["Text", 5], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["TableCell", 89], ["Line", 86], ["Text", 8], ["SectionHeader", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 87], ["Text", 12], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 130], ["Text", 13], ["SectionHeader", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 94], ["ListItem", 20], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 70], ["ListItem", 20], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Gradient-Based_Hyperparameter_Optimization_Through_Reversible_Learning"}