<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>

*Abstract*—The rapid growth of graph data poses significant challenges in storage, transmission, and particularly the training of graph neural networks (GNNs). To address these challenges, graph condensation (GC) has emerged as an innovative solution. GC focuses on synthesizing a compact yet highly representative graph, enabling GNNs trained on it to achieve performance comparable to those trained on the original large graph. The notable efficacy of GC and its broad prospects have garnered significant attention and spurred extensive research. This survey paper provides an up-to-date and systematic overview of GC, organizing existing research into five categories aligned with critical GC evaluation criteria: effectiveness, generalization, efficiency, fairness, and robustness. To facilitate an in-depth and comprehensive understanding of GC, this paper examines various methods under each category and thoroughly discusses two essential components within GC: optimization strategies and condensed graph generation. We also empirically compare and analyze representative GC methods with diverse optimization strategies based on the five proposed GC evaluation criteria. Finally, we explore the applications of GC in various fields, outline the related open-source libraries, and highlight the present challenges and novel insights, with the aim of promoting advancements in future research. The related resources can be found at [https://github.com/XYGaoG/Graph-Condensation-](https://github.com/XYGaoG/Graph-Condensation-Papers)[Papers.](https://github.com/XYGaoG/Graph-Condensation-Papers)

*Index Terms*—Graph Condensation, Data-Centric AI, Graph Representation Learning, Graph Neural Network

# I. INTRODUCTION

GRAPH data is extensively utilized across a diverse range<br>of domains, owing to its capability to represent complex<br>structural relationships arrangements artifice in the real number RAPH data is extensively utilized across a diverse range structural relationships among various entities in the real world [\[1\]](#page-16-0), [\[2\]](#page-16-1), [\[3\]](#page-16-2). Notable applications include, but are not limited to, social networks [\[4\]](#page-16-3), [\[5\]](#page-16-4), chemical molecular structures [\[6\]](#page-16-5), transportation systems [\[7\]](#page-16-6), and recommender systems [\[8\]](#page-16-7), [\[9\]](#page-16-8). However, the exponential growth of data volume in these applications makes the management and processing of largescale graph data a complex and challenging task. A particularly demanding aspect is the computational requirements associated with training graph neural networks (GNNs) on large-scale graph datasets  $[10]$ . These challenges are more pronounced in scenarios that require training multiple GNNs, such as hyper-parameter optimization [\[11\]](#page-16-10), continual learning [\[12\]](#page-16-11) and neural architecture search [\[13\]](#page-16-12), etc. This underscores

Corresponding author: Hongzhi Yin (e-mail: <EMAIL>).

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image depicts a flowchart illustrating the process of graph condensation and model training. It starts with an 'Original graph T' which undergoes 'Graph condensation' to produce a 'Condensed graph S'. Both graphs are then used for 'Model training'. The training process involves 'Downstream tasks' categorized into 'Node-level', 'Edge-level', and 'Graph-level', and 'GNN architectures' represented by various neural network diagrams. The outcome is a 'GNN trained on graph T' and a 'GNN trained on graph S', with a note indicating 'Comparable performance' between the two. Below the main flowchart, a section details 'Methods' such as 'Effective GC', 'Generalized GC', 'Efficient GC', 'Fair GC', and 'Robust GC', each linked to corresponding 'Criteria' including 'Effectiveness', 'Generalization', 'Efficiency', 'Fairness', and 'Robustness'.

Fig. 1: An overview for graph condensation. Graph condensation aims to generate small informative graphs such that the models trained on these graphs have similar downstream task performance to those trained on the original graphs. GC methods can be categorised into five classes aligned with critical GC evaluation criteria.

the growing importance of developing efficient and effective methodologies for processing large-scale graph data.

Early attempts to accelerate GNN training predominantly focused on model-centric methods, which aim to design advanced model structures to alleviate the heavy computational burden associated with the message-passing paradigm [\[14\]](#page-16-13) in GNNs. Specifically, sampling-based methods [\[10\]](#page-16-9), [\[15\]](#page-16-14), [\[16\]](#page-16-15) attempt to minimize the number of nodes involved in message-passing by selectively sampling node neighbors. Linear aggregation methods [\[17\]](#page-16-16), [\[18\]](#page-16-17), [\[19\]](#page-16-18) propagate messages as a pre-processing step to reduce the online training computation. Despite achieving more elaborate model architectures, these model-centric methods suffer from poor generalization and fail to ensure optimal performance across diverse graph datasets and downstream tasks [\[20\]](#page-16-19). In light of these limitations, research attention has shifted towards data-centric methods  $[21]$ , and graph reduction techniques have been proposed to accelerate model training by reducing graph size. Appropriate simplification of graphs can not only expedite graph algorithms but also enhance the efficiency of storage, transformation, and retrieval processes for diverse graph analysis tasks. Early explorations into graph reduction primarily focused on graph sparsification [\[22\]](#page-16-21) and coarsening [\[23\]](#page-17-0) techniques. These methods aim to reduce graph size by eliminating redundant edges and merging similar nodes. While effective in maintaining the essential characteristics of large graphs, these approaches have notable limitations. They predominantly rely on heuristic methods, such as the largest principal eigenvalues [\[23\]](#page-17-0) or pairwise distances [\[24\]](#page-17-1), which still result in poor generalization across different downstream tasks [\[25\]](#page-17-2). Consequently, the emphasis of current research is

This work is supported by Australian Research Council under the streams of Future Fellowship (Grant No. FT210100624), Linkage Project (Grant No. LP230200892), Discovery Early Career Researcher Award (Grants No. DE230101033 and No. DE250100613), and Discovery Project (Grants No. DP240101108 and No. DP240101814).

Xinyi Gao, Junliang Yu, Tong Chen and Hongzhi Yin are affiliated with the School of Electrical Engineering and Computer Science, the University of Queensland, Brisbane, Australia. Guanhua Ye is with the School of Computer Science, Beijing University of Posts and Telecommunications, Beijing, China. Wentao Zhang is with the Center for Machine Learning Research, Peking University, Beijing, China.

on developing more generalized and adaptable graph reduction techniques, and the leading development is graph condensation (GC) (a.k.a. graph distillation). As shown in Figure [1,](#page-0-0) GC  $[26]$  aims to synthesize a small but highly informative graph dataset, encompassing both node feature and topology structure, to effectively represent the large original graph. Through task-driven optimization, models trained on these small condensed datasets can achieve performance comparable to those trained on the large original graphs. For instance, GCond [\[26\]](#page-17-3) can condense the Flickr graph dataset to just 0.1% of its original size, while some certain GNNs models trained on this small graph manage to retain 99.8% of the original test accuracy. Given the notable efficacy of GC, it has soon been applied to a variety of applications [\[27\]](#page-17-4), [\[28\]](#page-17-5), [\[11\]](#page-16-10) and has sparked a proliferation of follow-up research [\[29\]](#page-17-6), [\[30\]](#page-17-7), further broadening the reach and deepening the impact of GC.

Although significant strides have been made to enhance the effectiveness of GC methods, their evaluation extends beyond mere effectiveness due to the data-centric focus of these methods. This necessitates a broader exploration of additional criteria for assessing the performance of GC. One critical aspect is the condensed graph's ability to generalize across multiple GNN architectures and various downstream tasks [\[26\]](#page-17-3). A GC method with superior generalization is particularly beneficial in scenarios that demand the training of heterogeneous models or the handling of diverse tasks [\[31\]](#page-17-8). In addition, it is essential for the condensed graph to maintain unbiased representations compared to the original graph [\[32\]](#page-17-9), [\[33\]](#page-17-10), which is particularly crucial in fields sensitive to bias, such as human resources [\[34\]](#page-17-11), criminal justice [\[35\]](#page-17-12), and financial services [\[36\]](#page-17-13). Moreover, a robust GC procedure can enhance the resilience of condensed graphs to noisy graph structures or features [\[37\]](#page-17-14) in real-world systems. Beyond these considerations, the scalability of GC also emerges as a key factor in practical applications [\[2\]](#page-16-1). An efficient GC method enables rapid adaption in life-long graph learning scenarios, effectively handling the continuous growth and dynamic changes of graph data. These criteria, effectiveness, generalization, efficiency, fairness, and robustness, play a pivotal role in GC evaluation and simultaneously serve as guiding principles for research motivations. Thus, we categorize existing GC methods based on these evaluation criteria, to provide a structured taxonomy for a comprehensive field survey. This taxonomy can help researchers comprehensively understand the diverse motivations driving GC methods, thereby facilitating a clearer path toward achieving their specific objectives.

While recent surveys on related topics including dataset distillation [\[38\]](#page-17-15), [\[39\]](#page-17-16), [\[40\]](#page-17-17), [\[41\]](#page-17-18), data-centric graph learning  $[21]$ ,  $[42]$ ,  $[43]$ , as well as graph neural network acceleration  $[44]$ ,  $[20]$  have touched upon GC, they summarize GC as a peripheral application or future direction, lacking systematic updates on the latest research and a holistic view. Moreover, concurrent GC surveys [\[25\]](#page-17-2), [\[45\]](#page-17-22) and subsequent GC benchmarks [\[46\]](#page-17-23), [\[47\]](#page-17-24), [\[48\]](#page-17-25) focus on detailed optimization strategies, missing high-level perspectives and analysis of design objectives and rationals. This hinders researchers from understanding the novel GC for varying contexts. Recognizing the rapid evolution and growing significance of GC, there is

<span id="page-1-1"></span>Image /page/1/Figure/5 description: This is a diagram illustrating a machine learning process. On the left, there are two graph-like structures, labeled T and S. The structure labeled T is composed of white nodes and black edges, representing a graph. The structure labeled S is similar but uses gray nodes and black edges, with an arrow indicating an 'Update' process. From T, an arrow points to a box labeled 'f\_theta'. From S, an arrow also points to another box labeled 'f\_theta'. A dashed arrow labeled 'Share weights' connects the two 'f\_theta' boxes, indicating that they share the same parameters. The outputs of both 'f\_theta' boxes are fed into a final output labeled 'L\_cond', representing a conditional loss.

Fig. 2: Graph condensation procedure. The original graph and condensed graph are encoded by the relay model  $f_{\theta}$  and the condensed graph is optimized according to  $\mathcal{L}_{cond}$ .

an urgent need for a systematic survey that encapsulates the latest advancements and motivations behind methodological designs.

The contributions of this survey are summarized as follows:

- We are the first<sup>[1](#page-1-0)</sup> to survey the literature on GC and introduce a systematic taxonomy for GC, categorizing existing methods into five distinct groups based on key criteria: effectiveness, generalization, efficiency, fairness and robustness. This taxonomy structures existing knowledge and captures the underlying motivations driving GC research.
- We provide a comprehensive and up-to-date review of the latest advancements in GC. Additionally, we delve into two fundamental aspects: optimization strategies and condensed graph generation, facilitating an in-depth comprehension of GC techniques.
- Through rigorous experiments, we systematically compare and analyze diverse optimization strategies in GC based on the five proposed GC evaluation criteria, offering comprehensive guidelines and insights for the design and development of advanced GC methods.
- We explore practical applications and available resources in GC, while also shedding light on current challenges and future directions in the field. This discussion aims to guide researchers who are seeking to advance GC further.

The structure of this survey is outlined as follows: Section 2 delves into the definition, criteria, and taxonomy of GC. Following this, Section 3 discusses the optimization strategies employed in GC methods. Section 4 provides a thorough investigation of each category of GC, as aligned with our taxonomy. In Section 5, we extensively discuss the methods involved in condensed graph generation, and Section 6 empirically compares the representative GC methods across diverse evaluation criteria. Section 7 is dedicated to summarizing the various applications and resources of GC. Finally, we present the challenges and future directions in this field in Section 8.

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>The initial version was released on January 22, 2024, and is available at [https://arxiv.org/abs/2401.11720.](https://arxiv.org/abs/2401.11720)

<span id="page-2-1"></span>Image /page/2/Figure/2 description: The image is a flowchart illustrating the taxonomy of graph condensation. The main categories are Optimization Strategy (Sec. III), GC Methods (Sec. IV), and Condensed Graph Generation (Sec. V). Under Optimization Strategy, there are Gradient Matching, Trajectory Matching, Kernel Ridge Regression, and Distribution Matching, each with associated methods and references. GC Methods are further divided into Effective GC (Sec. IV-A), Generalized GC (Sec. IV-B), Efficient GC (Sec. IV-C), Fair GC (Sec. IV-D), and Robust GC (Sec. IV-E). Each of these has sub-categories like Optimization-Driven Methods, Augmentation-Based Methods, Spectral Methods, Spatial Methods, Efficient Graph Encoding, Efficient Optimization, and Efficient Graph Generation, also with associated methods and references. Condensed Graph Generation includes Feature Generation (Sec. V-A) and Structure Generation (Sec. V-B). Feature Generation is broken down into Initialization, Closed-Form Solution, and Modeling, with sub-categories like Random, Coreset, Cluster, Generative Model, and Parameterization. Structure Generation includes Modeling and Sparsification, with sub-categories like Pre-defined Structure. The figure is titled 'Fig. 3: The taxonomy of graph condensation'.

Fig. 3: The taxonomy of graph condensation.

## II. DEFINITION AND TAXONOMY

<span id="page-2-2"></span>

### *A. Preliminaries*

Graph dataset. A large-scale graph dataset can be represented as  $\mathcal{T} = (\mathcal{V}, \mathcal{E})$ , which contains  $|\mathcal{V}| = N$  nodes and  $|\mathcal{E}| =$  $M$  edges  $^2$  $^2$ . Its *d*-dimensional node feature matrix, adjacency matrix, and task specific label are denoted as  $X \in \mathbb{R}^{N \times d}$ ,  $A \in \mathbb{R}^{N \times N}$  and Y, respectively. The entry in the adjacency matrix  $A_{i,j} > 0$  denotes an observed edge from node i to j, and  $A_{i,j} = 0$  otherwise.

<span id="page-2-0"></span><sup>2</sup>We unify definitions for node-level and graph-level graph datasets.

Graph neural network. GNNs leverage the graph topological information  $A$  and node features  $X$  to learn node representation via message-passing [\[14\]](#page-16-13), formulating GNN layers by aggregation and transformation functions. The  $l^{th}$  layer of GNNs is formulated as:

$$
\mathbf{m}_v^{(l)} \leftarrow \text{aggregate}\left(\mathbf{h}_v^{(l-1)}, \left\{\mathbf{h}_u^{(l-1)} \mid u \in \mathcal{N}(v)\right\}\right),
$$
  
$$
\mathbf{h}_v^{(l)} \leftarrow \text{transform}\left(\mathbf{m}_v^{(l)}, \mathbf{h}_v^{(l-1)}\right),
$$
 (1)

where  $h_v^{(l)}$  represents the representation of node v in the  $l^{th}$  layer, while  $\mathbf{m}_v^{(l)}$  denotes the message for node v. The function aggregate $(\cdot, \cdot)$  aggregates the neighboring nodes

of node v (i.e.,  $\mathcal{N}(v)$ ) to compute the message  $\mathbf{m}_v^{(l)}$ , which is subsequently updated by the transformation function  $transform(\cdot, \cdot)$ . Node representations are initialized as node features, and the final representations after L GNN layers are utilized for various downstream tasks.

Graph condensation. Graph condensation aims to find a small condensed graph dataset  $S = (\mathcal{V}', \mathcal{E}')$  with  $|\mathcal{V}'| = N'$ and  $N' \ll N$ , enabling more efficient GNN training while maintaining performance comparable to GNNs trained on the original graph  $\mathcal T$ . The adjacency matrix, node feature matrix, and task-specific label of S are denoted as  $A'$ ,  $X'$  and  $Y'$ , respectively. The compression rate (a.k.a. condensation ratio)  $r$ is defined as  $\frac{N'}{N}$ . To facilitate the connection between original graph  $\mathcal T$  and condensed graph  $\mathcal S$ , a relay graph model  $f_\theta(\cdot)$ , parameterized by  $\theta$ , is employed in the optimization process for encoding both graphs as shown in Figure [2.](#page-1-1) Then, the graph condensation is formulated as an optimization problem:

<span id="page-3-2"></span>
$$
S = \arg\min_{S} \mathcal{L}_{cond}\left(f_{\theta}\left(\mathcal{T}\right), f_{\theta}\left(\mathcal{S}\right)\right),\tag{2}
$$

where  $\mathcal{L}_{cond}$  is the optimization objective for graph condensation, which varies in format and will be elaborated upon in Section [III.](#page-4-0)

### *B. Criteria for Graph Condensation*

The primary metric for assessing GC methods involves evaluating the accuracy of GNN models trained on condensed graphs as compared to those trained on the original graphs, providing a direct measure of information preservation. To achieve a comprehensive evaluation and gain a holistic understanding of method performance, it is essential to incorporate additional criteria. Specifically, we summarize the key criteria of GC as follows:

- Effectiveness assesses the accuracy of a specified GNN architecture trained on the condensed graph under various compression rates  $[26]$ . It measures the extent to which the condensed graph preserves the essential characteristics of the original graph.
- Generalization measures the overall accuracy of different GNN architectures trained on the condensed graph across various downstream tasks [\[31\]](#page-17-8). It reflects the condensed graph's adaptability and utility in diverse contexts.
- Efficiency assesses the time required to condense a graph [\[54\]](#page-17-31), focusing on the speed of condensed graph generation and the method's feasibility for time-sensitive scenarios.
- Fairness evaluates the disparity in model performance between GNNs trained on condensed graphs and those trained on original graphs across different demographic groups[\[51\]](#page-17-28), aiming to prevent bias amplification in the condensed graph and ensure equitable model performance.
- Robustness measures the quality of condensed graphs generated from the noisy original graph. It assesses the ability of GC methods to identify and preserve the vital characteristics of the original graph despite the presence of noise in structures or features.

Image /page/3/Figure/12 description: A close-up, high-angle shot shows a single, small, gray, fuzzy caterpillar crawling on a white surface. The caterpillar is positioned in the upper right corner of the frame, with its body curved slightly. The background is entirely white, creating a stark contrast with the dark gray caterpillar.

<span id="page-3-0"></span>Image /page/3/Figure/13 description: The image displays two donut charts. Chart (a), labeled "Split by GC criteria," shows the following percentages for different GC criteria: Efficient GC (30.0%), Effective GC (26.7%), Application (23.3%), Generalized GC (10.0%), Fair GC (6.7%), and Robust GC (3.3%). Chart (b), labeled "Split by optimization objectives," shows the following percentages for different optimization objectives: Gradient Matching (55.2%), Distribution Matching (24.1%), Kernel Ridge Regression (10.3%), and Trajectory Matching (10.3%).

Fig. 4: The research focuses of graph condensation literature.

Given these criteria, an ideal GC method should generate a high-fidelity condensed graph dataset at low cost, enabling models with different GNN architectures to achieve results comparable and unbiased to those obtained on the original graph on a wide range of downstream tasks.

#### *C. Taxonomy*

The above criteria not only serve as indicators of utility for GC but also act as the objectives that steer the extensive research efforts in this field. To streamline the understanding of GC, we class GC methods into five categories that align with the above criteria: effective GC, generalized GC, efficient GC, fair GC, and robust GC, as shown in Figure [3.](#page-2-1) The paper distribution for each category is show in Figure [4](#page-3-0) (a).

*1) Effective Graph Condensation:* The primary objective of GC focuses on optimizing the task-specific quality of the condensed graph. As illustrated in Figure [5,](#page-3-1) the condensed graph produced by effective GC methods enables GNNs to achieve performance that closely approximates those trained on the original graph, especially in targeted downstream tasks. To systematically study these methods, we further divide this category into two distinct classes: optimization-driven methods and augmentation-based methods, facilitating an indepth understanding of techniques in effective GC.

<span id="page-3-1"></span>Image /page/3/Figure/19 description: This is a flowchart illustrating a process. On the left, there are two inputs labeled 'T' and 'S', originating from a box labeled 'Effective GC'. The 'T' input connects to a box labeled 'Downstream task', and the 'S' input connects to a box labeled 'Downstream model'. Both of these boxes are contained within a larger, rounded rectangle. Arrows from 'Downstream task' and 'Downstream model' point to outputs labeled 'Performance'. A dashed arrow labeled 'Close' points upwards from the 'Performance' output of 'Downstream model' to the 'Performance' output of 'Downstream task'.

Fig. 5: Effective graph condensation (GC). The condensed graph enables GNNs to be trained with performance comparable to GNNs trained on the original graph.

*2) Generalized Graph Condensation:* The objective of GC extends beyond merely training specific models for designated tasks. It also involves enabling the application of various models across a spectrum of tasks, as depicted in Figure [6.](#page-4-1) A condensed graph with good generalization capabilities avoids the repeated generation of task-specific or modelspecific condensed graphs, thereby significantly enhancing its utility in various applications, such as open-world graph learning [\[72\]](#page-17-49), neural architecture search [\[13\]](#page-16-12) and hyper-parameter optimization [\[73\]](#page-17-50). The primary challenge of generalization is to capture the key information in both the structural and feature representations during condensation. Consequently, we study existing generalized GC from two primary aspects: the spectral and spatial techniques for better information preservation during condensation.

<span id="page-4-1"></span>Image /page/4/Figure/2 description: This is a flowchart illustrating a generalized graph convolutional network (GC) framework. The GC module, labeled with 'T' and 'S', feeds into a central box containing two sub-boxes: 'Downstream task' and 'Downstream model'. This central box is influenced by 'Multiple downstream tasks' from above and 'Multiple GNN architectures' from below. Arrows indicate that the 'Downstream task' receives input from 'Multiple downstream tasks', and the 'Downstream model' receives input from 'Multiple GNN architectures'. Both 'Downstream task' and 'Downstream model' interact with each other. An output arrow from the central box points to 'Comparable performance', suggesting that the framework aims to achieve comparable performance across different tasks and architectures.

Fig. 6: Generalized graph condensation (GC). The condensed graph facilitates the training of various GNN architectures and supports various downstream tasks.

*3) Efficient Graph Condensation:* An efficient condensing procedure is essential for rapid adaptation in life-long graph learning scenarios. However, GC often suffers from sophisticated optimization processes and slow convergence, resulting in a time-consuming procedure. This particularly deteriorates in life-long learning contexts, where regular updates to the condensed graph dataset are a common requirement, as depicted in Figure [7.](#page-4-2) To address these challenges, efficient GC methods have been developed to accelerate the condensation process across all components within GC. To thoroughly explore the mechanisms underlying efficient GC, we break down the conventional GC procedure into three stages: graph encoding, optimization, and graph generation. These components align with the input, processing, and output stages of GC, respectively. By enhancing the efficiency of each stage, efficient GC methods significantly reduce the time required for the entire GC process.

<span id="page-4-2"></span>Image /page/4/Figure/5 description: The image displays a comparison between a conventional graph convolutional network (GC) and an efficient GC. The conventional GC is shown to require multiple updates to produce an output S from an input T. In contrast, the efficient GC requires minimal updates to produce the same output S from input T. Below the efficient GC, three components are listed: 'Efficient graph encoding', 'Efficient optimization', and 'Efficient graph generation'.

Fig. 7: Efficient graph condensation (GC) contributes to accelerating the condensation procedure.

*4) Fair Graph Condensation:* Balancing information during the condensation process represents a pivotal challenge in the domain of GC. The intensive compression of GC often leads to an amplification of data biases within the condensed graphs. Consequently, GNNs trained on such condensed graphs exhibit more pronounced fairness issues compared to those trained on original graphs. In response to this, fair GC methods constrain the condensation procedure to mitigate bias in the condensed graph as shown in Figure [8.](#page-4-3) The approaches to improving fairness in GC encompass two key aspects: the first integrates the fairness-enhancing regularization into the GC objective function, effectively guiding the optimization process toward fairer outcomes. The second aspect is the development of advanced relay model, promoting unbiased representations in the condensed graphs.

<span id="page-4-3"></span>Image /page/4/Figure/9 description: The image depicts a flowchart illustrating a process involving a 'Fair GC' component. The 'Fair GC' component takes an input labeled 'T' and produces an output labeled 'S'. Both 'T' and 'S' are then fed into a shaded box containing two sub-boxes. The top sub-box is labeled 'Downstream task', and the bottom sub-box is labeled 'Downstream model'. From this shaded box, two arrows point outwards, each labeled 'Distribution'. Between these two output arrows, a dashed arrow points upwards, labeled 'Unbiased'.

Fig. 8: Fair graph condensation (GC) ensures that GNNs trained on condensed graphs produce results unbiased relative to those from GNNs trained on the original graph.

*5) Robust Graph Condensation:* A robust condensation procedure is significant for unskewed condensed graph generation and deployment in noisy real-world scenarios. Conventional GC indiscriminately emulates the original graph distributions by condensed graph, disregarding the intrinsic quality of the graph. As a result, the condensed graph inherits the noisy distribution from the original graph, which is carried over to GNNs and compromises the model prediction accuracy. To address this issue, Robust GC aims to filter out the noise within the original graph during the condensation procedure and extracts only the core, causal information to the condensed graph for effective GNN training, as depicted in Figure [9.](#page-4-4)

<span id="page-4-4"></span>Image /page/4/Figure/12 description: This is a flowchart illustrating a process. On the left, there are two boxes labeled 'Robust GC' and 'Denoise', connected by arrows. 'Robust GC' has an arrow pointing to 'S', and 'Denoise' is connected to 'T'. Both 'T' and 'S' feed into a larger shaded box containing two sub-boxes: 'Downstream task' and 'Downstream model'. An arrow originates from the 'Downstream task' and 'Downstream model' box and points upwards to 'Comparable performance', with another arrow pointing downwards from 'Comparable performance' back to the larger shaded box, indicating a feedback loop or comparison.

Fig. 9: Robust graph condensation (GC) ensures that GNNs trained on condensed graphs produce comparable results relative to those from GNNs trained on the clean original graph.

## III. OPTIMIZATION STRATEGIES

<span id="page-4-0"></span>As we delve deeper into the intricacies of GC, it is crucial to examine the optimization strategies that form a core module within the GC framework. We hereby introduce the various optimization strategies employed in GC, offering a high-level perspective on the condensation procedure. Our discussion commences with the foundational principle of optimization objective (Eq. [\(4\)](#page-5-0)), then moves onto detailed optimization strategies (Eq.  $(5)-(9)$  $(5)-(9)$  $(5)-(9)$ ).

Algorithm 1: The bi-level optimization workflow.

| 1 | <b>Input:</b> Original graph $\mathcal{T}$ , relay model $f_{\theta}$ , iterations ${T_1, T_2}$ . |
|---|---------------------------------------------------------------------------------------------------|
| 2 | <b>Output:</b> Condensed graph $S$ .                                                              |
| 3 | Initialise $S$ and $f_{\theta}$ .                                                                 |
| 4 | <b>for</b> $t_1 = 1$ to $T_1$ <b>do</b> $\triangleright$ Outer loop                               |
| 5 | Update $S$ according to $\mathcal{L}^{\mathcal{T}}$ .                                             |
| 6 | <b>for</b> $t_2 = 1$ to $T_2$ <b>do</b> $\triangleright$ Inner loop                               |
| 7 | Optimize $f_{\theta}$ according to $\mathcal{L}^{\mathcal{S}}$ .                                  |
| 8 | <b>Return:</b> Condensed graph $S$ .                                                              |

<span id="page-5-2"></span>The foundational principle of the optimization strategies posits that the performance of a model trained on the condensed data should align with that of a model trained on the original data. To this end, the losses for data  $\mathcal T$  and  $\mathcal S$  in relation to the parameter  $\theta$  of the relay model are initially defined as:

$$
\mathcal{L}^{\mathcal{T}}(\theta) = \ell(f_{\theta}(\mathcal{T}), \mathbf{Y}),
$$
  
\n
$$
\mathcal{L}^{S}(\theta) = \ell(f_{\theta}(\mathcal{S}), \mathbf{Y}'),
$$
\n(3)

where  $\ell$  is the task-specific objective such as cross-entropy. Then the objective of GC can be formulated as the following bi-level problem:

<span id="page-5-0"></span>
$$
\min_{S} \mathcal{L}^{\mathcal{T}}(\theta^{\mathcal{S}}) \quad \text{s.t.} \quad \theta^{\mathcal{S}} = \arg\min_{\theta} \mathcal{L}^{\mathcal{S}}(\theta). \tag{4}
$$

Addressing the objective outlined in Eq. [\(4\)](#page-5-0) necessitates the incorporation of the task-specific objective  $\ell$  and entails resolving a nested loop optimization, which includes unrolling the entire training trajectory of the relay model optimization (i.e., inner loop) and then updates the condensed graph in the outer loop. A general workflow for this bi-level optimization can be formulated as Algorithm [1](#page-5-2) and this process can be prohibitively expensive [\[74\]](#page-17-51). To alleviate the complexity of the optimization process, various approximation methods have been proposed as shown in Figure [4](#page-3-0) (b), including gradient matching, trajectory matching, kernel ridge regression, and distribution matching.

Gradient matching. Gradient matching is initially proposed by Zhao et al. [\[74\]](#page-17-51) and has become the dominant optimization strategy in GC. Specifically, it formulates the condensation objective in Eq. [\(4\)](#page-5-0) as matching the optimized parameters of the models trained on two datasets:

<span id="page-5-1"></span>
$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim \Theta}[\mathcal{D}\left(\theta^{\mathcal{T}}, \theta^{\mathcal{S}}\right)],\tag{5}
$$

where  $\theta_0$  is the initialization of  $\theta^{\mathcal{T}}$  and  $\theta^{\mathcal{S}}$ .  $\Theta$  is a specific distribution for relay model initialization. The expectation on  $\theta_0$  aims to improve the robustness of S to different parameter initialization [\[41\]](#page-17-18).  $\mathcal{D}(\cdot, \cdot)$  is the distance measurement. The bi-level objective in Eq. [\(4\)](#page-5-0) is approximated by matching the model gradients at each training step  $t$ . In this way, the training trajectory on condensed data can mimic that on the original data, i.e., the models trained on these two datasets converge to similar solutions. The optimization objective  $\mathcal{L}_{cond}$  in Eq. [\(2\)](#page-3-2) is defined as:

$$
\mathcal{L}_{cond} = \mathbb{E}_{\theta_0 \sim \Theta} \left[ \sum_{t=1}^T \mathcal{D} \left( \nabla_{\theta} \mathcal{L}^{\mathcal{T}} \left( \theta_t \right), \nabla_{\theta} \mathcal{L}^{\mathcal{S}} \left( \theta_t \right) \right) \right] \\ \text{s.t. } \theta_{t+1} = \text{opt} \left( \mathcal{L}^{\mathcal{S}} \left( \theta_t \right) \right) \quad (6)
$$

where  $opt(\cdot)$  is the model parameter optimizer and the parameter of relay model is updated only on S.

Trajectory matching. Gradient matching primarily aligns with single-step gradients, yet it may accumulate errors when the relay model is iteratively updated using condensed data across multiple steps. To mitigate this problem, Cazenavette et al. [\[75\]](#page-17-52) propose a multi-step matching approach to approx-imate Eq. [\(5\)](#page-5-1) known as trajectory matching.

Trajectory matching trains two separate relay models on condensed data and original data, respectively. These two relay models are trained for distinct steps starting from the same initialization  $\theta'_t$ , which is sampled from the original data training checkpoints Θ′ . Then, trajectory matching aims to minimize the discrepancy between the final points of these two training trajectories:

$$
\mathcal{L}_{cond} = \mathbb{E}_{\theta_t' \sim \Theta'} \left[ \mathcal{D} \left( \theta_{t+k}^{\mathcal{T}}, \theta_{t+l}^{\mathcal{S}} \right) \right]
$$
\ns.t.  $\theta_{t+1}^{\mathcal{T}} = \text{opt} \left( \mathcal{L}^{\mathcal{T}} \left( \theta_t^{\mathcal{T}} \right) \right),$ \n
$$
\theta_{t+1}^{\mathcal{S}} = \text{opt} \left( \mathcal{L}^{\mathcal{S}} \left( \theta_t^{\mathcal{S}} \right) \right),
$$
\n(7)

where  $k$  and  $l$  are hyper-parameters controlling the update steps.  $\theta_t^S$  and  $\theta_t^T$  denote the parameters of relay models trained on  $\mathcal T$  and  $\mathcal S$  at step  $t$  respectively. Although trajectory matching introduces a tri-level optimization process, the training phase on the original data can be pre-processed. Consequently, the online condensation procedure remains a bi-level optimization process.

Kernel ridge regression. Gradient matching and trajectory matching approximately optimize the objective Eq. [\(5\)](#page-5-1) by matching one-step gradient or multi-step trajectory. However, the complex bi-level optimization and inexact solution inevitably lead to slow convergence and performance drop. In light of these challenges, Nguyen et al. [\[76\]](#page-17-53) transform the original task into a regression problem by substituting the neural network with kernel ridge regression (KRR). This approach enables the direct resolution of the inner loop optimization in Eq. [\(4\)](#page-5-0) using a closed-form solution. Consequently, the complex nested optimization is simplified into a first-order process by considering the condensed data as the support set and the original data as the target set. The optimization problem described in Eq. [\(4\)](#page-5-0) is reformulated into a regression task:

$$
\mathcal{L}_{cond} = \frac{1}{2} ||\mathbf{Y} - K_{\mathcal{TS}} (K_{\mathcal{SS}} + \lambda I)^{-1} \mathbf{Y}' ||^2, \qquad (8)
$$

where  $K_{\mathcal{TS}}$  is the kernel matrix of  $\mathcal T$  and  $\mathcal S$ , and  $K_{\mathcal{SS}}$  is the kernel matrix of  $S$ . It is important to note that matrix inversion is solely dependent on the number of condensed samples, making it computationally efficient.

Distribution matching. All aforementioned optimization strategies stem from the objective of aligning model perfor-mance as outlined in Eq. [\(4\)](#page-5-0), inevitably introducing the relay

<span id="page-6-3"></span>TABLE I: The comparison of the time complexity for diverse optimization objectives. The process in the condensing procedure varies for different GC objectives. Gradient matching includes the gradient calculation. Kernel ridge regression includes the computation of regression solution. Distribution matching includes class representation calculation.

| <b>Strategy</b>         | <b>Pre-processing</b>       | <b>Condensing procedure</b>          |                          |                   |                    |                          |
|-------------------------|-----------------------------|--------------------------------------|--------------------------|-------------------|--------------------|--------------------------|
|                         |                             | <b>Forward</b>                       | <b>Process</b>           | <b>Loss</b>       | <b>Update S</b>    | <b>Update f_{\theta}</b> |
| Gradient matching       | $\mathcal{O}(LMd)$          | $\mathcal{O}\left(\(N+N'\)dh\right)$ | $\mathcal{O}(dh)$        | $\mathcal{O}(dh)$ | $\mathcal{O}(N'd)$ | $\mathcal{O}(qdh)$       |
| Trajectory matching     | $\mathcal{O}(Z(LMd + Ndh))$ | $\mathcal{O}(qN'dh)$                 | N/A                      | $\mathcal{O}(dh)$ | $\mathcal{O}(N'd)$ | $\mathcal{O}(qdh)$       |
| Kernel ridge regression | $\mathcal{O}(LMd)$          | $\mathcal{O}((N+N')dh)$              | $\mathcal{O}(N^3 + NCh)$ | $\mathcal{O}(Ch)$ | $\mathcal{O}(N'd)$ | N/A                      |
| Distribution matching   | $\mathcal{O}(LMd)$          | $\mathcal{O}((N+N')dh)$              | $\mathcal{O}((N+N')h)$   | $\mathcal{O}(Ch)$ | $\mathcal{O}(N'd)$ | N/A                      |

model training procedure within the optimization process. To avoid this issue, Zhao et al. [\[77\]](#page-17-54) propose an alternative optimization principle known as distribution matching, which focuses on matching the feature distributions of condensed and original data. The objective of distribution matching is to generate condensed data with a feature distribution closely approximating that of the original data. Accordingly, the optimization objective of distribution matching is formulated as:

<span id="page-6-2"></span>
$$
\mathcal{L}_{cond} = \mathbb{E}_{\theta_0 \sim \Theta} \left[ \mathcal{D} \left( f_{\theta} \left( \mathcal{T} \right), f_{\theta} \left( \mathcal{S} \right) \right) \right]. \tag{9}
$$

To address the discrepancy in sample quantities between the original and condensed dataset, the distance measurement  $\mathcal{D}(\cdot, \cdot)$  is specifically designed for class-wise comparison, necessitating the use of class labels in distribution matching. Discussion. The gradient matching approach focuses on aligning short-range model parameters, while trajectory matching enhances data quality by matching long-range model trajectories. Both methods employ the bi-level optimization framework, requiring multiple forward and backward computations and thus incurring high costs and time consumption. In contrast, KRR and distribution matching adopt the singlelevel optimization framework, leading to more efficient optimization processes. Although distribution matching uses the feature space as a matching proxy and bypasses relay model optimization, this strategy inherently confines it to class-wise comparisons. Consequently, this design restricts its adaptability to diverse task-specific objectives and its applicability to data lacking class labels.

For an intuitive comparison, we present the detailed time complexity of various optimization strategies in Table [I.](#page-6-3) Time complexities for both the pre-processing and condensing phases are assessed separately and the condensing procedure is further divided into forward propagation, process execution, loss calculation, condensed graph updating, and relay model updating. Following GCond [\[26\]](#page-17-3), the relay model  $f_\theta$  for all strategies is a  $L$  layer SGC  $[17]$  incorporating a linear model with the hidden dimension denoted by  $h$ . For the original graph,  $N$ ,  $M$ ,  $d$ , and  $C$  are the number of nodes, edges, feature dimensions, and classes, respectively. The number of nodes in the condensed graph is represented by  $N'$ . For a fair comparison, the pre-defined adjacency matrix [\[29\]](#page-17-6) is utilized across all methods. The pre-processing stage for gradient matching  $[26]$ , kernel ridge regression  $[64]$  and distribution matching [\[48\]](#page-17-25) incorporates non-parametric graph convolution. Trajectory matching [\[29\]](#page-17-6) entails the pre-training of teacher models and the model quantity is denoted by  $Z$ . The process execution stage involves different operations specific to each

strategy. Specifically, gradient matching entails calculating the gradient w.r.t the relay model parameters twice. Kernel ridge regression calculates the closed-form solution, and distribution matching involves computing the class representation. As for the update of the relay model, gradient matching and trajectory matching necessitate updating the relay model on the condensed graph  $q$  times to generate model parameters and trajectories in the inner loop, respectively. Conversely, kernel ridge regression and distribution matching simplify the process by eliminating bi-level optimization, thereby obviating the need to update the relay model.

## IV. GRAPH CONDENSATION METHODS

<span id="page-6-0"></span>In this section, we dive into the specifics of GC and conduct a systematic analysis of five distinct GC methodologies: effective GC, generalized GC, efficient GC, fair GC, and robust GC, exploring the techniques employed to achieve their respective objectives. In addition to our proposed taxonomy, we also provide a detailed summary of GC methods, highlighting their distinct characteristics, in Table [II.](#page-7-0) This table offers a comprehensive comparison across several dimensions, including category, optimization strategy, condensed graph generation method, and downstream tasks.

<span id="page-6-1"></span>

### *A. Effective Graph Condensation*

As mentioned in Section  $II$ , we further divide effective GC methods into two categories according to the enhanced components within the GC framework, including optimizationdriven methods and augmentation-based methods.

*1) Optimization-Driven Methods:* Designing more advanced optimization strategies is often considered the critical driving force to achieve effective GC, thus steering many studies in this field. The first GC method, **GCond** [\[26\]](#page-17-3), employs gradient matching as the optimization strategy. However, the limitations associated with single-step gradient alignment constrain the overall performance of GC. Therefore, follow-up studies are proposed to explore and enhance the optimization process.

Gradient matching. CTRL [\[53\]](#page-17-30) argues that the distance measurement in gradient matching is coarse, which relies on cosine similarity and solely focuses on the directional aspect of gradients. This fails to adequately capture the vector characteristics of gradients, thereby introducing biases in the gradient matching process. Therefore, CTRL proposes to refine the matching objective by adding the magnitudes distance. Furthermore, CTRL empirical discovers that gradient magnitude matching not only explicitly accounts for the

<span id="page-7-0"></span>TABLE II: Summary of existing graph condensation research, organized chronologically by publication date. In optimization strategy: "GM" represents gradient matching, "DM" refers to distribution matching, "TM" is short for trajectory matching, "KRR" is kernel ridge regression, and "CTP" denotes computation tree preservation. In relay model: "GNN" is graph neural network, "GNTK" refers to graph neural tangent kernel and "SD" is short for spectral decomposition. In downstream task: "NC" represents node classification, "GC" refers to graph classification, "AD" is anomaly detection and "LP" is short for link prediction. The "Enhanced Module" identifies the specific target for improvements within the GC framework.

| Method         | Category       | Input        |            | <b>Optimization Strategy</b> |              | Output                 |                | Downstream  | <b>Enhanced</b> |
|----------------|----------------|--------------|------------|------------------------------|--------------|------------------------|----------------|-------------|-----------------|
|                |                | Preproc.     | Objective  | Model                        | Bi-level     | Structure construction | Sparsification | <b>Task</b> | Module          |
| $GCond$ [26]   | Effectiveness  |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | N/A             |
| DosCond [54]   | Efficiency     |              | <b>GM</b>  | <b>GNN</b>                   |              | Parameterization       | Gumbel softmax | GC          | Optimization    |
| $GCDM$ [65]    | Efficiency     |              | DM         | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Objective       |
| $HCDC$ [11]    | Application    |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Condensed graph |
| $MSGC$ [50]    | Effectiveness  | $\checkmark$ | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Pre-defined structure  | N/A            | NC          | Condensed graph |
| SGDD [31]      | Generalization | $\checkmark$ | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Regularization | NC, AD, LP  | Objective       |
| SFGC [29]      | Effectiveness  | $\checkmark$ | <b>TM</b>  | <b>GNN</b>                   |              | Pre-defined structure  | N/A            | NC          | Objective       |
| FGD [51]       | Fairness       |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Objective       |
| $GCARe$ [52]   | Fairness       |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Relay model     |
| $MCond$ [27]   | Application    |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Objective       |
| KiDD [30]      | Efficiency     | $\checkmark$ | <b>KRR</b> | <b>GNTK</b>                  |              | Parameterization       | Gumbel softmax | GC          | Relay model     |
| CaT [28]       | Application    |              | DM         | <b>GNN</b>                   |              | Pre-defined structure  | N/A            | NC          | Optimization    |
| CTRL [53]      | Effectiveness  |              | GM         | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC, GC      | Objective       |
| FedGKD [58]    | Application    |              | <b>GM</b>  | <b>GNN</b>                   |              | Generative model       | Gumbel softmax | NC          | Condensed graph |
| GDEM [66]      | Generalization | $\checkmark$ | DM         | <b>SD</b>                    |              | Parameterization       | N/A            | NC          | Relay model     |
| $GC-SNTK$ [63] | Efficiency     |              | <b>KRR</b> | <b>GNTK</b>                  |              | Pre-defined structure  | N/A            | NC          | Objective       |
| Mirage [71]    | Efficiency     | $\checkmark$ | <b>CTP</b> | Free                         |              | Parameterization       | N/A            | GC          | Optimization    |
| GroC [49]      | Effectiveness  |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC, GC      | Objective       |
| PUMA [67]      | Application    | $\checkmark$ | <b>DM</b>  | <b>GNN</b>                   |              | Pre-defined structure  | N/A            | NC          | Objective       |
| DisCo [69]     | Efficiency     | $\checkmark$ | DM         | <b>MLP</b>                   |              | Generative model       | N/A            | NC          | Objective       |
| $GEOM$ [61]    | Effectiveness  | $\checkmark$ | <b>TM</b>  | <b>GNN</b>                   |              | Generative model       | Threshold      | NC          | Objective       |
| $HGCond$ [55]  | Application    |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | N/A            | NC          | Condensed graph |
| EXGC [56]      | Efficiency     |              | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC, GC      | Optimization    |
| OpenGC [64]    | Generalization | $\checkmark$ | <b>KRR</b> | <b>GNN</b>                   |              | Pre-defined structure  | N/A            | NC          | Objective       |
| GCSR [62]      | Effectiveness  |              | TM         | <b>GNN</b>                   | $\checkmark$ | Parameterization       | N/A            | NC          | Objective       |
| $SimGC$ [70]   | Efficiency     | $\checkmark$ | DM         | <b>GNN</b>                   |              | Generative model       | Threshold      | NC          | Optimization    |
| FedGC [57]     | Application    |              | GM         | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Objective       |
| CGC [68]       | Efficiency     | $\checkmark$ | DM         | <b>GNN</b>                   |              | Parameterization       | Threshold      | NC          | Optimization    |
| $RobGC$ [60]   | Robustness     | $\checkmark$ | <b>GM</b>  | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Condensed graph |
| TinyGraph [59] | Effectiveness  |              | GM         | <b>GNN</b>                   | $\checkmark$ | Generative model       | Threshold      | NC          | Condensed graph |

vector properties of gradients, but also implicitly benefits the alignment of frequency distributions between condensed and original graphs. This provides another compelling rationale for the performance improvement.

Trajectory matching. Despite the improvements from finegrained gradient matching, CTRL still focuses on short-range matching. This leads to the short-sight issue and fails to capture the holistic GNN learning behaviors, potentially compromising the quality of condensed graphs. To address this limitation, SFGC [\[29\]](#page-17-6) introduces trajectory matching in GC and proposes to align the long-term GNN learning behaviors between the original graph and condensed graph. As a result, SFGC enables a more comprehensive knowledge transfer to the condensed graph. Furthermore, GEOM [\[61\]](#page-17-38) identifies the restricted supervision signals in trajectory matching and argues that difficult nodes are key to the performance gap of GNNs trained on the condensed graph. To address this, GEOM evaluates difficult nodes through the principle of homophily [\[78\]](#page-17-55) and utilizes curriculum learning to adjust the size of the matching window for trajectories. This approach allows GEOM to systematically structure the condensation procedure to progress from simpler to more challenging trajectories, thereby incorporating more diverse supervision signals from the original graph.

Kernel ridge regression. Beyond aligning gradients or trajectories during model training, kernel ridge regression is employed for precise solutions in bi-level optimization objectives. For instance, GC-SNTK [\[63\]](#page-17-40) discards the GNN and employs the kernel ridge regression, incorporating graph neural tangent kernels [\[79\]](#page-18-0) as the relay model. This modification not only facilitates the first-order optimization of the condensed graph but also eliminates the requirement for multiple initializations of relay GNN, resulting in a more stable condensation procedure. KiDD [\[30\]](#page-17-7) employs kernel ridge regression for graph classification tasks and further improves the method by removing the non-linear activation in graph neural tangent kernels. This approach simplifies the matrix multiplications in the iterative updates of condensed graphs and leads to substantial improvements in model accuracy compared to the gradient matching method.

*2) Augmentation-Based Methods:* Optimization-driven methods enhance the effectiveness of GC by emphasizing the optimization procedure. In contrast, other studies improve the performance by augmenting different modules in the GC framework.

**Structure augmentation. MSGC** [\[50\]](#page-17-27) and **GCSR** [\[62\]](#page-17-39) improve the GC from graph structure construction. Unlike many GC approaches which synthesize a single dense adjacency matrix, MSGC instead pre-defines multiple small-scale sparse graphs. These pre-defined structures allow each condensed node to encompass distinct neighborhoods. The variation in structure enables GNNs to capture broader information, leading to diverse node embeddings and enhanced performance. Additionally, GCSR explicitly models the inter-class correlations within the original graph structure and leverages them to reconstruct an interpretable condensed graph structure through the self-expressive property [\[80\]](#page-18-1). This approach transfers structural correlations from the original graph to the condensed graph, thereby facilitating better information preservation and enhancing the graph structure.

Label augmentation. PUMA  $[67]$  and CGC  $[68]$  emphasize the significance of label quantity in GC and improve the condensation quality by incorporating pseudo labels from the original graph. The inclusion of additional supervision signals enhances class representation and ensures more accurate information preservation in the condensed graph.

Feature augmentation. GroC [\[49\]](#page-17-26) designs a perturbation module in the process of generating condensed graphs. This module maximizes the distance between the condensed graph and the original graph by selectively perturbing the condensed node features that are insufficiently informative. By utilizing the adversarial training technique, these perturbations are capable of exploring the neighborhood areas within the parameter space of the condensed graph and significantly enhancing the gradient matching process. TinyGraph [\[59\]](#page-17-36) aims to further compact the condensed graph by reducing the dimension of node features. It employs a graph attention network to compress the original graph features, with the compressed node features replacing the original ones as the condensation target during the condensation process. By simultaneously optimizing the graph attention network and the condensed graph with a unified GC objective, TinyGraph effectively reduces the size of condensed graphs while preserving the downstream GNN performances.

<span id="page-8-0"></span>

### *B. Generalized Graph Condensation*

The essence of generalization lies in preserving crucial task-related information while mitigating bias injection into the condensed graph. Nevertheless, some GNN relay models, employed for encoding both structure and feature information, inevitably result in a loss of structural information and create an entanglement between the condensed graph and the relay model. These issues significantly affect the generalization ability to unseen models and tasks. To explore the cause, GDEM [\[66\]](#page-17-43) conducts an investigation across various GNN architectures, including different spatial and spectral GNNs. They find that the performance gap increases when the relay model and downstream model have different architectures. This is due to that distinct spectral preferences of GNNs have varying impacts on the significance of different graph eigenvectors [\[81\]](#page-18-2), [\[82\]](#page-18-3), consequently leading to alterations in node features. For instance, if a GNN functions primarily as a low-pass filter, it will prioritize low-frequency information in node features, overlooking the full spectrum. The loss of highfrequency information will injure the high-pass filter GNNs' performance.

To address this issue, some generalized GC methods are developed from the spectral perspective. For example, GDEM [\[66\]](#page-17-43) eschews the conventional relay GNN and directly generates the eigenbasis for the condensed graph, thereby eliminating the spectrum bias inherent in relay GNNs. To address the inconsistencies in eigenvector quantity and dimension, GDEM aligns the condensed eigenvectors with the most important eigenvectors of the original graph and matches the class features in the subspace defined by these eigenvectors. Apart from leveraging the advanced relay model, **SGDD** [\[31\]](#page-17-8) employs the Laplacian energy distribution (LED) to analyze the structural properties of the graph, quantifying the spectral shift between condensed and original graphs. A substantial LED shift suggests that crucial structural information from the original graph is lost, diminishing the generalization capabilities of the condensed graph. Therefore, SGDD leverages the LED as a structural shift metric and designs an additional regularization to enhance the structure of the condensed graph. This regularization aligns the LED between the original graph and the condensed graph, facilitating the broadcast of structural information. By preserving crucial structural information, SGDD effectively reduces the performance discrepancies observed across different GNN architectures and various tasks, e.g. node classification, anomaly detection and link prediction.

Besides these spectral methods, OpenGC [\[64\]](#page-17-41) emphasizes the temporal generalization capacity of condensed graphs, particularly in dynamic open-world scenarios [\[72\]](#page-17-49). In this context, new nodes and classes are continuously integrated into the existing graph structure, resulting in a distribution shift compared to the original graph being condensed. To adapt the condensed graph to these dynamic distribution shifts and manage graph data over an extended period, OpenGC employs temporal data augmentation to simulate evolving graph patterns and create multiple structure-aware environments. Furthermore, it incorporates invariance learning regularization in GC to preserve the invariant patterns across different temporal environments in the condensed graph. By this means, OpenGC significantly enhances the adaptability of downstream GNNs by training them on the temporally generalized condensed graph, eliminating the need to laboriously design specific

generalization modules for each GNN.

<span id="page-9-0"></span>

#### *C. Efficient Graph Condensation*

The GC process requires significant computational resources. To comprehend the root causes of heavy computation, we break down the GC process into three fundamental stages, including efficient graph encoding, efficient optimization, and efficient graph generation, and examine acceleration methods for each component.

*1) Efficient Graph Encoding:* In GC, the graph encoding procedure is executed by the relay model, and it requires intensive computation primarily due to the neighbor explosion problem in the large original graph. Generally, relay models used in GC methods adopt either the message-passing framework or the graph neural tangent kernel framework. All these frameworks stack multiple propagation and transformation operations to aggregate and update information according to the graph structure. However, as multiple propagation processes are executed, the number of aggregated neighboring nodes increases exponentially, leading to significant computational demands.

To mitigate the expensive computation resulting from propagation, some GC methods borrow the idea from SGC [\[17\]](#page-16-16) and remove the non-linear transformation layers between aggregation layers in the relay model. For example, GCond  $[26]$  and PUMA  $[67]$  utilize SGC to encode the original graph. KiDD  $[30]$  simplifies the graph neural tangent kernel by removing non-linear activation at certain layers. By this means, the propagation of these methods can be pre-computed in the pre-processing procedure and only needs to be executed once. Instead of performing propagation during each training epoch, the online time complexity of GC is significantly reduced than utilizing other relay models.

*2) Efficient Optimization:* In GC, the optimization procedure requires numerous iterations to update the relay model before optimizing the condensed graph, leading to considerable computation.

Gradient matching. To mitigate this problem, DosCond [\[54\]](#page-17-31) proposes the one-step gradient matching strategy which discards the iterated relay model update and only performs gradient matching for one single step. The theoretical analysis posits that minimizing the one-step matching loss serves a dual purpose: it not only directs the learning process of condensed graphs towards lower losses on original graphs but also delineates the trajectory for updating the condensed data. Moreover, one-step matching eliminates the requirement of hyper-parameter tuning, such as the number of iterations of bi-level optimization, further simplifying the condensation process. Besides simplifying the relay model updates in the inner loop optimization, EXGC [\[56\]](#page-17-33) aims to expedite the convergence of condensed graph generation in the outer loop. It begins by framing the bi-level optimization within an Expectation Maximization framework, then employs the Mean-Field variational approximation to update a subset of node features at once while maintaining complementary features fixed. Furthermore, EXGC observes that the condensation procedure follows a long-tail distribution, where a minority of condensed node features retain the majority of performance capabilities. However, this distribution leads to significant variability in the convergence speeds across different node sets. To address this, EXGC introduces post-hoc explanation methods to pinpoint the key subgraphs that hold most of the essential information for further convergence speedup.

Kernel ridge regression. To avoid the bi-level optimization in GC, another research line involves kernel ridge regressionbased methods [\[30\]](#page-17-7), [\[63\]](#page-17-40), [\[64\]](#page-17-41). These methods reformulate the classification task as a kernel ridge regression task and incorporate the graph neural tangent kernel within kernel ridge regression to encode the original graph. Kernel ridge regression provides an efficient closed-form solution, dispensing with the necessity for iterative computations in updating the relay model and resulting in faster convergence speeds.

Distribution matching. Besides the gradient matching and kernel ridge regression, distribution matching changes the optimization principle towards approximating the original graph in the feature space, attracting significant attention in efficient GC studies. This approach leads to a shift in the role of the relay model within GC. It transitions from aligning task performances to transforming both original and condensed graphs into a unified space for distribution alignment. Consequently, distribution matching obviates the need to compute the gradients and trajectories w.r.t. parameters of the relay model in each training iteration, leading to a substantial reduction in computational complexity. Specifically, GCDM [\[65\]](#page-17-42), CaT [\[28\]](#page-17-5) and PUMA [\[67\]](#page-17-44) compute node distributions for both original and condensed graphs in the same feature space and employ the maximum mean discrepancy [\[83\]](#page-18-4) between each pair of class distributions as the optimization objective. GDEM [\[66\]](#page-17-43) decomposes the graph and matches the class distributions within sub-spaces formed by eigenvectors. To enhance the fidelity of the condensation procedure, DisCo [\[69\]](#page-17-46) and SimGC [\[70\]](#page-17-47) introduce the pre-trained GNN in distribution matching and further align the node variations for performance enhancement.

Clustering. Instead of updating the condensed graph via gradients, CGC [\[68\]](#page-17-45) simplifies the distribution matching objective by recasting it as a class partition problem, which can be effectively resolved using any clustering method. By integrating this strategy with a predefined graph structure, CGC facilitates a training-free solution for generating condensed graphs. This innovation significantly accelerates the condensation process, enhancing the efficiency and scalability of GC.

Computation tree preservation. Mirage [\[71\]](#page-17-48) proposes a training-free solution for graph-level datasets. The underlying concept is that message-passing GNNs decompose the input graph into a multiset of computation trees according to the neighborhood aggregation. These computation trees capture the fundamental connectivity information in the graph and explicitly model the message-passing flows for each node. By preserving the most frequently occurring computation trees for each class, Mirage identifies the most crucial graph structures, thereby achieving a relay model-agnostic condensation method.

*3) Efficient Graph Generation:* Condensed graph generation targets two components: the feature matrix and the adjacency matrix. Modeling these matrices typically requires two distinct optimization processes. For example, GCond [\[26\]](#page-17-3) models the condensed structure as a function of the condensed nodes, requiring the optimization of an additional adjacency matrix generator. To simplify the optimization procedure, MSGC [\[50\]](#page-17-27) and CGC [\[68\]](#page-17-45) leverage the pre-defined sparse graphs to eliminate the condensed graph optimization. GCSR [\[62\]](#page-17-39) incorporates the self-expressive property to construct the graph, enabling a closed-form solution for the calculation of the adjacency matrix. Furthermore, several studies [\[26\]](#page-17-3),  $[29]$ ,  $[65]$ ,  $[28]$ ,  $[67]$  discard the adjacency matrix directly and instead use an identity matrix to represent condensed structure. These structure-free GC methods implicitly encode the original graph's topological structure into discriminative node features via relay GNNs and these enriched embeddings are informative to train different downstream GNNs.

<span id="page-10-0"></span>

#### *D. Fair Graph Condensation*

GC can effectively capture the essential task-specific characteristics of the original graph by exclusively optimizing for task performance. However, this task-centric optimization approach may introduce fairness concerns, primarily attributed to the indiscriminate transmission of information into the condensed graph. Especially in scenarios with intensive compression rates, GC tends to amplify biases as it prioritizes the preservation of predominant and representative information w.r.t the tasks. Consequently, GNNs trained on condensed graphs exhibit more severe fairness issues than those trained on original graphs  $[51]$ ,  $[52]$  and challenge the applicability of GC in high-stake applications [\[84\]](#page-18-5), [\[51\]](#page-17-28).

To mitigate bias in condensed graphs, FGD [\[51\]](#page-17-28) initially investigates the relationship between the feature spaces of the original graph and condensed graph, and constructs an estimator for sensitive features. Following this, the variance of the estimated sensitive group membership is employed as the bias measurement and integrated as the regularization to facilitate the unbiased condensed graph. Moreover, GCARe [\[52\]](#page-17-29) directly regularizes the condensation process with adversarial training. It additionally incorporates a discriminator into the relay model, which is designed to predict the sensitive groups of node representations. The relay model operates as a generator, generating fair data to deceive the discriminator. Consequently, the condensation process is formulated as a min-max optimization problem, striking a balance between data generation and debiasing efforts.

While the two aforementioned methods address group fairness from distinct perspectives, their focus has primarily been on node classification tasks, leaving fairness concerns in other tasks largely unexplored. Furthermore, an empirical study by FGD has revealed that datasets also experience more pronounced issues with individual fairness when GNNs are trained on condensed graphs [\[51\]](#page-17-28). This underscores the need to broaden the scope of fairness research to include additional tasks and alternative fairness metrics, such as individual fairness [\[85\]](#page-18-6) and rank fairness [\[86\]](#page-18-7).

<span id="page-10-1"></span>

### *E. Robust Graph Condensation*

GC typically assumes the availability of the clean original graph and emulates its distribution indiscriminately by the condensed graph. However, this idealized assumption contradicts the complexities of data processing in practical scenarios. Real-world graphs are often marred by various forms of noise—ranging from data collection errors [\[87\]](#page-18-8) and outdated information [\[88\]](#page-18-9) to inherent variability in dynamic environments [\[89\]](#page-18-10), [\[90\]](#page-18-11). Additionally, recent research [\[91\]](#page-18-12) has highlighted vulnerabilities of GC to backdoor attacks, where malicious attackers can subtly manipulate the condensed graph while maintaining its apparent quality. These unexpected noises or deliberate attacks all significantly impact the representation of the condensed graph and carry over to GNNs, compromising the model prediction accuracy and the resilience of GC in real-world applications.

Despite its significant relevance, robust GC has not been extensively studied, with only a limited number of studies addressing this vital field. RobGC [\[60\]](#page-17-37) emerges as a pioneer by specifically targeting structural noises and incorporates a denoising procedure into the graph condensation process. It employs the condensed graph as a denoising signal to provide an overview for the structure and node features of the original graph, facilitating the correction of inherent noises. By alternately executing graph condensation and denoising procedures, the quality of both the original and condensed graphs is mutually improved, ensuring the condensed graph retains only the essential information from the original graph. Moreover, the unified optimization in RobGC enables the denoising of unseen test graphs using the noise-free condensed graph, leading to more accurate inference results.

As a preliminary exploration, RobGC effectively demonstrates the feasibility and crucial need for developing robust GC methods. However, its application is currently limited to addressing structural noises. Expanding the research scope to include challenges such as node feature noise and label noise is imperative for enhancing the utility of GC across a broader spectrum of practical applications.

## V. CONDENSED GRAPH GENERATION

<span id="page-10-2"></span>Different from traditional graph reduction methods that trim the original graph, GC focuses on creating an entirely new condensed graph. An effective graph generation method not only accelerates optimization convergence but also enhances overall performance. In this section, we discuss the condensed graph generation methods from two aspects: feature and structure generation.

<span id="page-10-3"></span>

### *A. Feature Generation*

The feature matrix  $X'$  dominates parameter quantity in the condensed graph and the most of methods update it by gradients. As a result, the feature matrix is significantly influenced by diverse initializations, potentially leading to varied convergence speeds. To expedite the optimization process for the feature matrix, researchers have investigated a range of initialization methods as substitutes for conventional random initialization. For instance, GCond [\[26\]](#page-17-3) initiates the

<span id="page-11-1"></span>TABLE III: Summary of structure modeling and sparsification methods in existing graph condensation research, with representative literature listed.

|                          | <b>Methods</b>        | <b>Principles</b>                                             | Literature                                |
|--------------------------|-----------------------|---------------------------------------------------------------|-------------------------------------------|
|                          | Generative model      | Node similarity                                               | GCond $[26]$ , SGDD $[31]$ , DisCo $[69]$ |
| Structure modeling       | Parameterization      | Node self-expression $&$<br>Inter-class structure correlation | GCSR $[62]$                               |
|                          |                       | Low rank decomposition                                        | KiDD $[30]$                               |
|                          |                       | Spectral decomposition                                        | <b>GDEM</b> [66]                          |
|                          | Pre-defined structure | Node similarity                                               | MSGC [50], CGC [68]                       |
|                          |                       | Structure-free method                                         | GCond [26], SFGC [29]                     |
|                          | Threshold             |                                                               | GCond $[26]$                              |
| Structure sparsification | Regularization        |                                                               | <b>SGDD</b> [31]                          |
|                          | Gumbel softmax        | ۰                                                             | DosCond $[54]$ , KiDD $[30]$              |

feature matrix by randomly selecting original nodes with corresponding labels. SFGC [\[29\]](#page-17-6) leverages the coreset method [\[92\]](#page-18-13) and chooses the representative nodes for initialization. On the other hand, MCond [\[27\]](#page-17-4) and CTRL [\[53\]](#page-17-30) demonstrate that employing the representative embeddings can accelerate the convergence of optimization. Accordingly, these methods first cluster original graph nodes based on the pre-defined compression rate, and then utilize the cluster centroids as the initialization of the feature matrix.

In addition to gradient-based optimization, which relies on different initializations, CGC [\[68\]](#page-17-45) proposes generating the feature matrix through a closed-form solution. This approach eliminates the need for iterative updates of the feature matrix, thus ensuring a more precise and efficient feature generation process.

<span id="page-11-0"></span>

### *B. Structure Generation*

The adjacency matrix  $A'$  of the condensed graph reflects relationships among condensed nodes and preserves the topological information of the original graph. The construction method of the adjacency matrix affects its characteristics, such as homophily, spectral properties, and sparsity, which in turn impact overall performance. As shown in Table [III,](#page-11-1) current studies employ three kinds of strategies for generating adjacency matrices: generative model, parameterization, and predefined structure.

Generative model. A straightforward generation method involves employing the generative model to construct an adjacency matrix explicitly. For example, GCond [\[26\]](#page-17-3) constructs the adjacency matrix based on homophily and generates the adjacency matrix by node similarities. The generative model is designed to investigate the correlations between pairs of condensed nodes. SGDD [\[31\]](#page-17-8) utilizes noise, along with the features and labels of the condensed graph, as inputs to the generative model for creating the adjacency matrix. DisCo [\[69\]](#page-17-46) transfers the link prediction model of the original graph to the condensed nodes, generating the corresponding edges for the condensed graph. Although this method offers greater flexibility in controlling the generated structures, it suffers from substantial modeling complexity: the number of parameters in A′ grows quadratically with the increase of condensed nodes. Parameterization. To mitigate the modeling complexity of graph structure, several GC methods propose to parameterize the adjacency matrix. GCSR [\[62\]](#page-17-39) explores the self-expressive

property among condensed nodes and further enhances the condensed graph by transferring the inter-class correlations from the original graph structure. Besides, KiDD [\[30\]](#page-17-7) directly optimizes the low-rank decomposed matrices of the adjacency matrix to speed up the convergence. To preserve the spectral characteristics, GDEM [\[66\]](#page-17-43) decomposes the adjacency matrix into an eigenbasis and constructs the condensed structure using the eigenvectors matched with the most significant eigenvectors of the original graph.

Pre-defined structure. To further simplify the modeling of condensed graphs, several studies leverage pre-defined struc-tures for the condensed graph. For instance, MSGC [\[50\]](#page-17-27) pre-defines multiple sparse adjacency matrices for condensed nodes. By this means, MSGC reduces the complexity of the adjacency matrix while capturing a more diverse range of structural information compared to the single dense matrix. Moreover, several GC methods have incorporated the structure-free strategy  $[26]$ ,  $[29]$ ,  $[65]$ ,  $[28]$ ,  $[67]$  by employing a fixed identity matrix as the adjacency matrix. This strategy leads to a significant simplification of the computational requirements for the adjacency matrix.

Condensed graph sparsification. The structure construction methods typically yield dense adjacency matrices with continuous values. These dense matrices often contain small but storage-intensive values that have minimal impact on GNN aggregation performance. Moreover, certain application scenarios, such as molecular graphs, require binary and discrete adjacency matrices for downstream tasks. Hence, it becomes essential to sparsify the generated adjacency matrices, and numerous sparsification methods have been proposed to tackle these challenges. For instance, GCond [\[26\]](#page-17-3) utilizes the threshold to remove the entries with small values and justifies that suitable choices of threshold do not degrade performance but increase the scalability of the condensed graph. SGDD [\[31\]](#page-17-8) employs the L2 norm of the adjacency matrix as a regularization term for sparsity control. To construct binary and discrete adjacency matrices, DosCond [\[54\]](#page-17-31) and KiDD [\[30\]](#page-17-7) model each pair of nodes as an independent Bernoulli variable. Then, the Gumbel-Max reparametrization trick [\[93\]](#page-18-14) is utilized to address the non-differentiability challenges in the optimization procedure.

## VI. EMPIRICAL STUDIES

Considering the diversity of optimization strategies and GC criteria, it is crucial to understand the characteristics of

<span id="page-12-0"></span>TABLE IV: The accuracy (%) comparison between GC methods under different compression rates. "OOM" means out-of-memory. The highest accuracies are highlighted in bold, and runners-up are underlined. "GM" represents gradient matching. "DM" denotes distribution matching. "KRR" is kernel ridge regression, and "TM" represents trajectory matching. "Original graph" denotes the GNN performance which is trained on the original graph.

| Dataset    |          | GM<br>$\boldsymbol{r}$ |                |                | DM             |                |                | <b>KRR</b>     |                | <b>TM</b>      |                |                |                |
|------------|----------|------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|            |          | GCond                  | GCond-X        | DosCond        | SGDD           | <b>GCDM</b>    | GCDM-X         | SimGC          | <b>GC-SNTK</b> | GC-SNTK-X      | <b>SFGC</b>    | <b>GCSR</b>    | graph          |
|            | 1.30%    | $79.8 \pm 1.3$         | $75.9 \pm 1.2$ | $80.5 \pm 0.1$ | $80.1 \pm 0.7$ | $69.4 \pm 1.3$ | $81.3 \pm 0.4$ | $80.8 \pm 2.3$ | $81.7 \pm 0.7$ | $82.2 \pm 0.3$ | $80.1 \pm 0.4$ | 79.9±0.7       |                |
| Cora       | 2.60%    | $80.1 \pm 0.6$         | $75.7 \pm 0.9$ | $80.1 \pm 0.5$ | $80.6 \pm 0.8$ | $77.2 \pm 0.4$ | $81.4 \pm 0.1$ | $80.9 \pm 2.6$ | $81.5 \pm 0.7$ | $82.4 \pm 0.5$ | $81.7 \pm 0.5$ | $80.6 \pm 0.8$ | $81.2 \pm 0.2$ |
|            | 5.20%    | $79.3 \pm 0.3$         | $76.0 \pm 0.9$ | $80.3 \pm 0.4$ | $80.4 \pm 1.6$ | $79.4 \pm 0.1$ | $82.5 \pm 0.3$ | $82.1 \pm 1.3$ | $81.3 \pm 0.2$ | $82.1 \pm 0.1$ | $81.6 \pm 0.8$ | $81.2 \pm 0.9$ |                |
|            | $0.90\%$ | $70.5 \pm 1.2$         | $71.4 \pm 0.8$ | $71.0 \pm 0.2$ | $69.5 \pm 0.4$ | $62.0 \pm 0.1$ | $69.0 \pm 0.5$ | $73.8 \pm 2.5$ | $66.4 \pm 1.0$ | $69.9 \pm 0.4$ | $71.4 \pm 0.5$ | $70.2 \pm 1.1$ |                |
| Citeseer   | 1.80%    | $70.6 \pm 0.9$         | $69.8 \pm 1.1$ | $71.2 \pm 0.2$ | $70.2 \pm 0.8$ | $69.5 \pm 1.1$ | $71.9 \pm 0.5$ | $72.2 \pm 0.5$ | $68.4 \pm 1.1$ | $69.9 \pm 0.5$ | $72.4 \pm 0.4$ | $71.7 \pm 0.9$ | $71.7 \pm 0.1$ |
|            | 3.60%    | $69.8 \pm 1.4$         | $69.4 \pm 1.4$ | $70.7 \pm 0.1$ | $70.3 \pm 1.7$ | $69.8 \pm 0.2$ | $72.8 \pm 0.6$ | $71.1 \pm 2.8$ | $69.8 \pm 0.8$ | $69.1 \pm 0.4$ | $70.6 \pm 0.7$ | $74.0{\pm}0.4$ |                |
|            | 0.05%    | $59.2 \pm 1.1$         | $61.3 \pm 0.5$ | $62.1 \pm 0.3$ | $60.8 \pm 1.3$ | $59.3 \pm 0.3$ | $61.0 \pm 0.1$ | $63.6 \pm 0.8$ | $64.4 \pm 0.2$ | $63.9 \pm 0.3$ | $65.5 \pm 0.7$ | $60.6 \pm 1.1$ |                |
| Ogbn-arxiv | 0.25%    | $63.2 \pm 0.3$         | $64.2 \pm 0.4$ | $63.5 \pm 0.1$ | $65.8 \pm 1.2$ | $59.6 \pm 0.4$ | $61.2 \pm 0.1$ | $66.4 \pm 0.3$ | $65.1 \pm 0.8$ | $65.5 \pm 0.1$ | $66.1 \pm 0.4$ | $65.4 \pm 0.8$ | $71.4 \pm 0.1$ |
|            | 0.50%    | $64.0 \pm 0.4$         | $63.1 \pm 0.5$ | $63.7 \pm 0.2$ | $66.3 \pm 0.7$ | $62.4 \pm 0.1$ | $62.5 \pm 0.1$ | $66.8 \pm 0.4$ | $65.4 \pm 0.5$ | $65.7 \pm 0.4$ | $66.8 \pm 0.4$ | $65.9 \pm 0.6$ |                |
|            | 0.10%    | $46.5 \pm 0.4$         | $45.9 \pm 0.1$ | $46.0 \pm 0.3$ | $46.9 \pm 0.1$ | $46.1 \pm 0.1$ | $46.0 \pm 0.1$ | $45.3 \pm 0.7$ | $46.7 \pm 0.1$ | $46.6 \pm 0.3$ | $46.6 \pm 0.2$ | $46.6 \pm 0.3$ |                |
| Flickr     | 0.50%    | $47.1 \pm 0.1$         | $45.0 \pm 0.2$ | $46.2 \pm 0.2$ | $47.1 \pm 0.3$ | $46.8 \pm 0.1$ | $45.6 \pm 0.1$ | $45.6 \pm 0.4$ | $46.8 \pm 0.1$ | $46.7 \pm 0.1$ | $47.0 \pm 0.1$ | $46.6 \pm 0.2$ | $47.2 \pm 0.1$ |
|            | $1.00\%$ | $47.1 \pm 0.1$         | $45.0 \pm 0.1$ | $46.1 \pm 0.1$ | $47.1 \pm 0.1$ | $46.7 \pm 0.1$ | $45.4 \pm 0.3$ | $43.8 \pm 1.5$ | $46.5 \pm 0.2$ | $46.6 \pm 0.2$ | $47.1 \pm 0.1$ | $46.8 \pm 0.2$ |                |
|            | $0.05\%$ | $88.0 \pm 1.8$         | $88.4 \pm 0.4$ | $89.8 \pm 0.1$ | $90.5 \pm 2.1$ | $89.3 \pm 0.1$ | $86.5 \pm 0.2$ | $89.6 \pm 0.6$ | <b>OOM</b>     | <b>OOM</b>     | $89.7 \pm 0.2$ | $90.5 \pm 0.2$ |                |
| Reddit     | 0.10%    | $89.6 \pm 0.7$         | $89.3 \pm 0.1$ | $90.5 \pm 0.1$ | $91.8 \pm 1.9$ | $89.7 \pm 0.2$ | $87.2 \pm 0.1$ | $90.6 \pm 0.3$ | <b>OOM</b>     | <b>OOM</b>     | $90.0 \pm 0.3$ | $91.2 \pm 0.2$ | $93.9 \pm 0.0$ |
|            | 0.20%    | $90.1 \pm 0.5$         | $88.8 \pm 0.4$ | $91.1 \pm 0.1$ | $91.6 \pm 1.8$ | $90.2 \pm 0.4$ | $88.8 \pm 0.1$ | $91.4 \pm 0.2$ | <b>OOM</b>     | <b>OOM</b>     | $89.9 \pm 0.4$ | $92.2 \pm 0.1$ |                |

different GC methods and design appropriate modules for enhancing specific task performance. Although this is not a paper centered on experiments and analysis, in this section we empirically analyze existing GC methods with diverse optimization strategies in terms of the five proposed criteria to provide the guidelines for both researchers and practitioners.

#### *A. Evaluation Metric*

Each criterion for the GC method is evaluated by a specific metric.

Effectiveness is assessed by comparing the accuracy of GNNs trained on condensed graphs at varying compression rates to those trained on the original graph. Methods that closely match the accuracy of the original setup are considered more effective.

Generalization is measured by comparing the average performance of models trained on condensed graphs across various GNN architectures and a range of downstream tasks, such as node classification, link prediction, anomaly detection, and temporal prediction. For a specific task  $t$ , the average performance of  $n$  GNN architectures is defined as:

$$
p_t = \frac{1}{n} \sum_{i=1}^{n} acc_i,
$$
 (10)

where  $acc_i$  represents the accuracy of the  $i^{th}$  GNN architecture. A higher value indicates superior generalization ability for task  $t$ .

Efficiency is evaluated by the total time required for the condensation procedure, with shorter times indicating better practical usability.

Fairness is assessed by comparing the bias performance of GNNs trained on condensed graphs to those trained on the original graph. For the sensitive feature  $s \in \{0, 1\}$ , it is desired that GNN predictions remain independent of the sensitive feature  $[51]$ ,  $[94]$ . With binary labels denoted as  $y \in \{0, 1\}$ , and model predictions as  $\hat{y} \in \{0, 1\}$ , model bias

<span id="page-12-1"></span>TABLE V: The generalizability comparison of GC methods on Ogbnarxiv dataset. "Opt." and "Avg." indicate the optimization strategy and average value, respectively. The highest accuracies are highlighted in bold, and runners-up are underlined.  $r$  is set as 0.25%.

| Opt.       | Method           | SGC  | <b>GCN</b> | <b>SAGE</b> | <b>APPNP</b> | Cheby | <b>GAT</b> | Avg. |
|------------|------------------|------|------------|-------------|--------------|-------|------------|------|
|            | GCond            | 63.7 | 63.2       | 62.6        | 63.4         | 54.9  | 60.0       | 61.3 |
| <b>GM</b>  | $GCond-X$        | 64.7 | 64.2       | 64.4        | 61.5         | 59.5  | 60.1       | 62.4 |
|            | DosCond          | 63.3 | 63.5       | 62.1        | 63.5         | 55.1  | 60.4       | 61.3 |
|            | SGDD             | 64.3 | 65.8       | 63.4        | 63.3         | 55.9  | 61.4       | 62.4 |
|            | <b>GCDM</b>      | 61.2 | 59.6       | 61.1        | 62.8         | 55.4  | 61.2       | 60.2 |
| DM         | GCDM-X           | 64.4 | 61.2       | 63.4        | 60.5         | 60.2  | 60.0       | 61.6 |
|            | SimGC            | 64.3 | 66.4       | 60.4        | 61.5         | 54.7  | 61.1       | 61.4 |
| <b>KRR</b> | <b>GC-SNTK</b>   | 62.7 | 65.1       | 62.9        | 62.6         | 55.1  | 61.8       | 61.7 |
|            | <b>GC-SNTK-X</b> | 64.0 | 65.5       | 62.4        | 61.8         | 58.7  | 60.9       | 62.2 |
| TM         | <b>SFGC</b>      | 64.8 | 66.1       | 64.8        | 63.9         | 60.7  | 65.7       | 64.3 |
|            | <b>GCSR</b>      | 65.6 | 65.4       | 65.4        | 64.4         | 58.9  | 63.5       | 63.9 |

is quantified by demographic parity  $(\Delta_{DP})$  [\[95\]](#page-18-16) and equal opportunity  $(\Delta_{EO})$  [\[96\]](#page-18-17):

$$
\Delta_{DP} = |P(\hat{y} = 1|s = 0) - P(\hat{y} = 1|s = 1)|, \quad (11)
$$

$$
\Delta_{EO} = |P(\hat{y} = 1|y = 1, s = 0) - P(\hat{y} = 1|y = 1, s = 1)|. (12)
$$

GC methods that effectively mitigate the bias of GNNs trained on the original graph are considered fairer.

Robustness is evaluated by the accuracy of GNNs trained on condensed graphs derived from original graphs with varying noise levels. Methods that achieve higher accuracy are considered to preserve core information more effectively and demonstrate greater robustness against noise in the original graph.

#### *B. Experimental Settings*

Methods. To assess the efficacy of various optimization strategies, we evaluate 11 representative GC methods categorized under 4 distinct optimization strategies: (1) gradient matching (GM)-based method: GCond, GCond-X [\[26\]](#page-17-3), DosCond  $[54]$  and SGDD  $[31]$ ; (2) distribution matching (DM)-based method: GCDM, GCDM-X [\[65\]](#page-17-42) and SimGC [\[70\]](#page-17-47); (3) KRR-based method: GC-SNTK and GC-SNTK-X [\[63\]](#page-17-40); (4) trajectory matching (TM)-based method: SFGC [\[29\]](#page-17-6) and GCSR [\[62\]](#page-17-39). Notice that the suffix "-X" represents the graphless variant.

Datasets. Given the diversity of evaluation criteria, GC methods are evaluated across various datasets with distinct characteristics, including three transductive datasets (Cora, Citeseer [\[97\]](#page-18-18) and Ogbn-arxiv [\[98\]](#page-18-19)), two inductive datasets (Flickr and Reddit  $[16]$ ) and a dataset with sensitive feature (Pokec-n [\[94\]](#page-18-15)).

**Implementations.** Following GCond  $[26]$ , we evaluate three compression rates  $(r)$  for each dataset to evaluate the effectiveness. In the transductive setting,  $N$  represents the original graph size, while in the inductive setting,  $N$  indicates the subgraph size observed in the training stage. Two-layer GNNs with 256 hidden units are used for evaluation.

Hyper-parameters. The hyper-parameters are determined through the grid search on the validation set. We use the ADAM optimization algorithm to train all the models. The learning rate for the condensation process is determined through a search over the set {0.01, 0.001, 0.0001}. The weight decay is 5e-4. Dropout is searched from [0, 1).

Computing Infrastructure. The codes are written in Python 3.9 and Pytorch 1.12.1. The operating system is Ubuntu 18.0, and all models are trained on GPUs. All experiments are conducted on a server with Intel(R) Xeon(R) CPUs (Gold 6128 @ 3.40GHz) and NVIDIA GeForce RTX 2080 Ti 11GB GPUs.

#### *C. Experimental Results*

Effectiveness. The condensed graphs generated by GC methods are evaluated to train the same 2-layer GCN [\[97\]](#page-18-18) for node classification and the test accuracies with standard deviation are reported in Table [IV.](#page-12-0) In the table, "Original graph" refers to the GCN performance which is trained on the original graph and we make the following observations. (1) GC methods are not sensitive to the compression rate and a larger condensed graph size does not strictly indicate better performance. Even with extremely small compression rates, GC methods can achieve promising performance. (2) The graphless variants demonstrate comparable performance to the method with the structure modeling, indicating the graph structure information can be effectively encoded into the condensed nodes. (3) When comparing different optimization strategies, trajectory matching-based methods consistently show superior performance, outperforming other strategies across various datasets. While KRR-based methods achieve competitive performance, the graph kernel in KRR is memory-intensive and does not scale well with large-scale graphs. Distribution matchingbased methods perform well on smaller datasets, whereas gradient matching-based methods are more effective on larger datasets. compression rates (c) Ior care dataset to exalte the trace-<br>SAGE results are the simulate the simulate effective entries and to exalte the simulate effective string paper is which in the inductive setting N indicates the s

Generalization. We adhere to the settings commonly used in most GC studies and assess the generalization of GC methods across various GNN models for the node classification task. The GNN models evaluated include GCN [\[97\]](#page-18-18), SGC [\[17\]](#page-16-16),

<span id="page-13-0"></span>Image /page/13/Figure/9 description: This is a scatter plot showing the relationship between condensation time (in seconds) on the x-axis and accuracy (in percentage) on the y-axis. The x-axis is on a logarithmic scale, ranging from approximately 10^2 to 10^5. The y-axis ranges from 58% to 68%. Several data points are plotted, each representing a different method with its corresponding condensation time and accuracy. For example, SimGC has an accuracy of approximately 66.5% and a condensation time of 1.0x (likely a normalized value). GCSR has an accuracy of about 65% and a condensation time of 20.9x. SGDD has an accuracy of about 66% and a condensation time of 87.8x. SFGC has an accuracy of about 66.5% and a condensation time of 341.8x. GCond-X has an accuracy of about 65% and a condensation time of 53.5x. GCond has an accuracy of about 64.5% and a condensation time of 58.9x. DosCond has an accuracy of about 64.5% and a condensation time of 49.5x. GC-SNTK has an accuracy of about 64.5% and a condensation time of 37.1x. GC-SNTK-X has an accuracy of about 65.5% and a condensation time of 36.0x. GCDM has an accuracy of about 60.5% and a condensation time of 2.7x. GCDM-X has an accuracy of about 62% and a condensation time of 1.9x. The plot uses different markers for each data point, including hexagons, circles, triangles, squares, and diamonds.

Fig. 10: The condensation time (seconds) comparison of different GC methods on Ogbn-arxiv dataset.  $r$  is set as 0.25%.

detailed accuracies are shown in Table [V.](#page-12-1) Across various GNN architectures, most GC methods consistently achieve the best performance on GCN and SGC. This superior performance is attributed to the same convolution kernels [\[26\]](#page-17-3) in the relay models of these GC methods, which share the same spectral characteristics [\[66\]](#page-17-43) as those in GCN and SGC. Among different optimization strategies, trajectory matchingbased methods demonstrate a significant improvement over other methods, underscoring the effectiveness of trajectory matching in capturing essential information from the original graph and its superior generalization capabilities.

Efficiency. We report the condensation times of various GC methods in Fig. [10](#page-13-0) and make the following observations. (1) Distribution matching achieves the most efficient condensation process among the optimization strategies, primarily due to its elimination of the need for gradient calculations. In contrast, trajectory matching and gradient matching are computational intensive. (2) Without graph structure modeling, graphless variant methods expedite the condensation process. (3) The one-step matching strategy used in DosCond eliminates the intensive model updates required in the inner loop while maintaining competitive classification performance.

Fairness. Following FairGNN [\[94\]](#page-18-15) and FGD [\[51\]](#page-17-28), we evaluate the representative GC method in each optimization strategy on the Pokec-n dataset, and assess group fairness issues within a binary sensitive feature setting. As shown in Table [VI,](#page-14-0) GC methods amplify the graph data bias to varying degrees. Gradient matching and distribution matching not only achieve the lowest node classification accuracy but also exhibit the most pronounced bias. More advanced optimization strategies, i.e., KRR and trajectory matching, can effectively reduce this bias while preserving predictive performance.

Robustness. We evaluate the robustness of GC methods to structural noise, which involves randomly adding and removing edges. The noise level is set at 100%, defined by the proportion of modified edges in the original graph [\[102\]](#page-18-23). As illustrated in Table [VII,](#page-15-0) the noisy graph structure significantly impacts the classification performance of models trained on condensed graphs. The performance disparity between noisy condensed graphs and noisy original graphs is greater than that observed with clean graphs, suggesting that structural

| Opt.                     | GM             |                | DM             |                | KRR            | TM             |                | Origianl<br>graph |                |
|--------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|-------------------|----------------|
|                          | GCond          | SGDD           | GCDM           | SimGC          |                | GC-SNTK        | SFGC           |                   | GCSR           |
| Method                   | AUC $\uparrow$ | $52.2 \pm 1.9$ | $55.1 \pm 0.9$ | $53.0 \pm 1.6$ | $55.3 \pm 1.3$ | $54.4 \pm 1.1$ | $55.5 \pm 1.6$ | $54.9 \pm 0.6$    | $72.9 \pm 0.2$ |
| $\Delta_{DP} \downarrow$ | $3.0 \pm 1.2$  | $2.5 \pm 1.0$  | $2.3 \pm 1.4$  | $2.4 \pm 1.1$  | $2.0 \pm 1.7$  | $2.1 \pm 1.2$  | $1.9 \pm 0.9$  | $0.2 \pm 0.1$     |                |
| $\Delta_{EO} \downarrow$ | $3.2 \pm 1.0$  | $3.0 \pm 1.1$  | $2.7 \pm 1.5$  | $2.8 \pm 1.2$  | $2.6 \pm 1.6$  | $2.2 \pm 1.1$  | $2.0 \pm 1.2$  | $1.4 \pm 1.6$     |                |

<span id="page-14-0"></span>TABLE VI: The comparison of AUC (%), demographic parity ( $\Delta_{DP}$ , %) and equal opportunity ( $\Delta_{EO}$ , %) for GC methods with various optimization strategies.

noise severely affects the quality of condensed graphs. Among the GC methods, distribution matching is most sensitive to random noises, while trajectory matching demonstrates robust performances.

#### VII. APPLICATIONS AND RESOURCES

Due to the efficiency in training GNN models and the capacity to conserve storage costs, GC contains vast application prospects and is increasingly being utilized across a variety of fields. In this section, we first enumerate the diverse applications of GC, including hyperparameter/neural architecture search, graph continual learning, federated learning, inference acceleration, recommender systems, and heterogeneous graphs. Consequently, this section details the opensource libraries for GC methods, facilitating the advancement of future research in this domain.

### *A. Practical Applications*

*1) Hyper-Parameter/Neural Architecture Search:* The hyper-parameter/neural architecture search [\[103\]](#page-18-24) involves systematic experimentation with a range of hyper-parameters or architectures to identify the optimal combination for downstream tasks. This process typically requires the training of multiple models with varying hyper-parameters/architectures on the same dataset, incurring substantial computational costs. To mitigate the computational burden,  $HCDC$  [\[11\]](#page-16-10) introduces GC in this problem and additionally generates the synthetic validation data by matching the hyper-parameter gradients. This ensures that the performance ranking of hyperparameters/architectures on condensed datasets is consistent with that on the original datasets. By integrating GC, HCDC significantly reduces the time required for the search process.

*2) Graph Continual Learning:* Graph continual learning [\[104\]](#page-18-25) is the approach that focuses on the continuous acquisition of knowledge from dynamic and evolving graph data. In this realm, a critical challenge is the catastrophic forgetting problem, where a model's performance on previously learned data deteriorates significantly when trained on new data. To mitigate this issue,  $CaT$  [\[28\]](#page-17-5) and PUMA [\[67\]](#page-17-44) incorporate GC into the process of graph continual learning, leveraging the condensed graphs to preserve historical information within a memory bank. To maintain a balance between historical and newly incoming data, CaT strategically trains the model exclusively on the condensed graph. This method not only addresses the challenge of data imbalance in graph continual learning but also significantly enhances learning efficiency.

*3) Federated Learning:* Federated learning [\[105\]](#page-18-26) is a distributed framework that safeguards private data, involving multiple clients with exclusive data and a central server. In this setting, clients train local models with their unique data and then transmit the parameters to the server, which aggregates and redistributes the updated knowledge. A key challenge in federated learning is the non-i.i.d nature of client data, leading to biased local models and slow convergence, increasing the communication load between the server and clients. To address this, FedGKD [\[58\]](#page-17-35) integrates GC at the client level to efficiently extract the local task features, enhancing the similarity computation and ensuring better incorporation of local information. Combined with global aggregation, FedGKD significantly improves collaboration results in federated learning environments. Besides integrating GC at the client level, FedGC [\[57\]](#page-17-34) explores the federated graph condensation task, which aims to learn a unified condensed graph at the server level, encompassing knowledge from various clients. By matching the aggregated gradients from clients, FedGC tackles the data heterogeneity problem and generates a global condensed graph, facilitating efficient model training in the cross-silo scenarios.

*4) Inference Acceleration:* In practical graph systems, new present nodes will be connected with existing nodes in the original graph, then the message-passing process is executed to aggregate information and generate representations for inference. However, the extensive size of large original graphs poses a considerable challenge for real-time inference [\[2\]](#page-16-1). This difficulty primarily stems from the "neighbor explosion" problem, where the number of aggregated nodes exponentially increases with the propagation depth. To reduce the high computational demands and latency inherent in GNNs, MCond [\[27\]](#page-17-4) adopts GC and proposes to learn a mapping from original nodes to condensed nodes. This approach allows direct information propagation on the small condensed graph, resulting in a significant acceleration of the inference process.

*5) Recommender Systems:* Recommender systems are widely used in various industries such as e-commerce and social media, aiming to suggest relevant items or content to users. In a typical recommender system, the user-item interactions can be represented as a bipartite graph. As the number of users and items grows, this graph becomes increasingly large and complex, posing significant computational challenges. To handle this issue, **Distill-CF** [\[106\]](#page-18-27) applies GC to recommender systems. It adopts a multi-step Gumbel sampling trick to condense the original user base into a limited number of representative users, which are then iteratively updated through distilling knowledge from the original graph. By replacing the original user-item graph with the synthesized small graph for

| Method      | GМ             |                | DМ             |                | <b>KRR</b>     | TМ             |                | Origianl       |
|-------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|             | GCond          | SGDD           | <b>GCDM</b>    | SimGC          | <b>GC-SNTK</b> | <b>SFGC</b>    | <b>GCSR</b>    | graph          |
| Clean graph | $63.2 \pm 0.3$ | $65.8 \pm 1.2$ | $59.6 \pm 0.4$ | $66.4 \pm 0.3$ | $65.1 \pm 0.8$ | $66.1 \pm 0.4$ | $65.4 \pm 0.8$ | $71.4 \pm 0.1$ |
| Noisy graph | $50.1 \pm 0.5$ | $52.9 \pm 0.9$ | $49.4 \pm 0.7$ | $51.7 \pm 0.4$ | $52.7 \pm 0.7$ | $54.6 \pm 0.7$ | $55.5 \pm 0.5$ | $61.1 \pm 0.1$ |

<span id="page-15-0"></span>TABLE VII: The performance of GC methods against random structure noise on Ogbn-arxiv. Noise level is set as 100%. "Original graph" indicates that GNNs are trained on the noisy original graph. In contrast, other methods involve training GNNs on the condensed graph.

training and inference, this method leads to more scalable and efficient recommender systems.

*6) Heterogeneous Graphs:* Heterogeneous graphs [\[107\]](#page-18-28), [\[3\]](#page-16-2) are adept at modeling complex systems characterized by multi-typed entities and relations, thereby providing a broad range of practical applications and significant potential for generalization. However, their complex structure and inherent heterogeneity present significant challenges in data management and in training heterogeneous GNNs. In light of these challenges, HGCond [\[55\]](#page-17-32) extends GCond [\[26\]](#page-17-3) from simple graphs to heterogeneous graphs, generating a condensed heterogeneous graph for efficient model training. To address the heterogeneity problem, HGCond initializes condensed nodes by clustering same-type original nodes and further enhances the gradient matching optimization by constraining the parameter space of the relay model with orthogonal parameter sequences. This approach significantly enhances the scalability and generalization of heterogeneous GNNs on heterogeneous graphs, thereby broadening their applications in managing complex, large-scale systems.

### *B. Open-Source Libraries*

Several notable PyTorch-based GC libraries have been developed based on the widely-used GNN libraries PyTorch Geometric  $(PyG)^3$  $(PyG)^3$  and Deep Graph Library  $(DGL)^4$  $(DGL)^4$ . These libraries provide essential tools and benchmarks to advance research and applications in graph-based systems.

- GCondenser<sup>[5](#page-15-3)</sup> [\[46\]](#page-17-23), compatible with both PyG and DGL, supports 6 GC methods evaluated across 6 diverse graph datasets for node classification tasks. It also includes implementations for applying GC methods to graph continual learning and hyperparamter sweeping by Optuna [\[108\]](#page-18-29).
- GC-Bench<sup>[6](#page-15-4)</sup> [\[48\]](#page-17-25) is a unified GC benchmark based on PyG. It incorporates 9 GC methods assessed over 12 graph datasets for node- and graph-level tasks, including node/graph classification, link prediction, node clustering, and anomaly detection.
- GraphSlim<sup>[7](#page-15-5)</sup> [\[47\]](#page-17-24) is a PyG-based library for graph reduction, including sparsification, coarsening, and condensation. It evaluates 7 GC methods on 5 graph datasets for node classification and extends the utility of GC to neural architecture search. Additionally, it assesses the robustness of GC methods by integrating the DeepRobust [\[109\]](#page-18-30) library.

# VIII. CHALLENGES AND FUTURE DIRECTIONS

Although current methodologies in GC have shown remarkable performance across various applications, the field still grapples with inherent limitations and unresolved challenges. In this section, we summarize the current challenges and identify several future research directions worth exploring.

#### *A. Condensation for Diverse Graphs*

As an innovative approach for graph reduction, current GC methods have predominantly focused on simple graphs. However, the graph data in practical applications is far more complex, showcasing a wide spectrum of characteristics including heterophilic graphs [\[110\]](#page-18-31), digraphs [\[111\]](#page-18-32), and dynamic graphs  $[112]$ , etc. Each type of graph brings forth its own unique features, necessitating a deep understanding of their structures and a requirement for diverse GC strategies. Such a comprehensive grasp is not only critical for effectively managing the increasing volumes of data but also plays a key role in widening the horizons of their applications.

#### *B. Task-Agnostic Graph Condensation*

The optimization strategies currently employed in GC are specifically designed for the classification task, heavily relying on class labels. However, the entanglement of the GC optimization process with task-related labels can potentially hinder performance in other downstream tasks. The recent study, CTGC [\[113\]](#page-18-34), investigates self-supervised learning to enhance the task adaptability of GC for link prediction, and clustering tasks. This confirms the feasibility of extending GC to a wider spectrum of practical scenarios and delineates promising directions for future research, including anomaly detection [\[114\]](#page-18-35), graph reconstruction [\[115\]](#page-18-36), and graph generation [\[116\]](#page-18-37).

#### *C. Efficient Evaluation for Condensed Graphs*

In the realm of GC, the primary objective is to create high-quality condensed graphs. However, a systematic and well-established methodology for assessing the condensed graph quality is notably absent in the field [\[29\]](#page-17-6). This gap is particularly critical given the complex optimization involved in GC. The assessment of the condensed graph quality is essential not only for monitoring the condensation progress but also for potentially streamlining the optimization through early-stop strategies. Currently, GC practices leverage the downstream models, i.e., GNN [\[26\]](#page-17-3) or graph neural tangent kernel [\[29\]](#page-17-6), as the indicator for assessment. However, this method adds considerable computational overhead to the GC process. Moreover, the concept of quality in condensed graphs is inherently complex, varying significantly across different applications

<span id="page-15-1"></span><sup>3</sup><https://www.pyg.org/>

<span id="page-15-2"></span><sup>4</sup><https://www.dgl.ai/>

<span id="page-15-3"></span><sup>5</sup><https://github.com/superallen13/GCondenser>

<span id="page-15-5"></span><span id="page-15-4"></span><sup>6</sup><https://github.com/RingBDStack/GC-Bench> <sup>7</sup><https://github.com/Emory-Melody/GraphSlim>

and necessitating a multifaceted approach to evaluation. Considering these challenges, there is a pressing requirement to develop comprehensive and efficient evaluation metrics. Such metrics would not only accelerate the condensation process but also broaden the range of GC applications.

#### *D. Secure Graph Condensation*

The condensation of graphs not only facilitates efficient training of GNNs but also contributes significantly to reducing the storage overhead and accelerating data transmission. However, the security of highly compressed graph datasets to various privacy threats [\[117\]](#page-18-38) remains under-explored. The recent study [\[91\]](#page-18-12) has exposed vulnerabilities of GC and injected malicious information into condensed graphs to backdoor the GNNs trained on them. This highlights concerns over additional threats, such as adversarial attacks [\[60\]](#page-17-37) and inference breaches  $[118]$ , which pose significant challenges for deploying GC in security-sensitive domains, including confidential communications, financial systems, and industrial controls. In light of these challenges, the development of secure, privacy-preserving GC algorithms becomes imperative.

#### *E. Explainable Graph Condensation*

Another promising avenue for GC research is the development of explainable GC methods. A relevant work [\[56\]](#page-17-33) utilizes explainable algorithms to assess the importance of condensed nodes and prune the condensation redundancy. However, this method still provides limited insight into how condensed graphs represent the original data [\[119\]](#page-18-40). An explainable condensation can illuminate the intricate patterns within the graph, thereby enhancing user trust and acceptance. This is particularly important in sectors like healthcare [\[120\]](#page-18-41) and finance [\[36\]](#page-17-13), where the interpretability and justification of decision-making processes are critical. Consequently, by elucidating the logic behind data transformations, explainable GC has the potential to open up new avenues for its application, establishing GC as a more reliable and transparent tool in data-centric technology fields.

# IX. CONCLUSION

This survey presents an up-to-date and comprehensive overview of GC and classifies the existing research into five distinct categories aligned with the proposed essential GC evaluation criteria. It further explores two critical components of GC: optimization strategies and condensed graph generation, facilitating a deep understanding of GC techniques. Additionally, it highlights the current challenges and emerging perspectives in GC, with the objective of inspiring and guiding future research in this evolving field. Overall, this survey seeks to serve as a beacon for researchers and practitioners, guiding them through the evolving landscape of GC and fostering advancements that could reshape the use of graph data in various practical applications.

### REFERENCES

- <span id="page-16-0"></span>[1] S. Wu, F. Sun, W. Zhang, X. Xie, and B. Cui, "Graph neural networks in recommender systems: a survey," *ACM Computing Surveys*, vol. 55, no. 5, pp. 1–37, 2022.
- <span id="page-16-1"></span>[2] X. Gao, W. Zhang, J. Yu, Y. Shao, Q. V. H. Nguyen, B. Cui, and H. Yin, "Accelerating scalable graph neural network inference with node-adaptive propagation," *ICDE*, 2024.
- <span id="page-16-2"></span>[3] X. Gao, W. Zhang, T. Chen, J. Yu, H. Q. V. Nguyen, and H. Yin, "Semantic-aware node synthesis for imbalanced heterogeneous information networks," in *Proceedings of the 32nd ACM International Conference on Information and Knowledge Management*, 2023, pp. 545–555.
- <span id="page-16-3"></span>[4] Y. Li, J. Fan, Y. Wang, and K.-L. Tan, "Influence maximization on social graphs: A survey," *IEEE Transactions on Knowledge and Data Engineering*, vol. 30, no. 10, pp. 1852–1872, 2018.
- <span id="page-16-4"></span>[5] K. Jung, W. Heo, and W. Chen, "Irie: Scalable and robust influence maximization in social networks," in *2012 IEEE 12th International Conference on Data Mining*. IEEE, 2012, pp. 918–923.
- <span id="page-16-5"></span>[6] Z. Guo, K. Guo, B. Nan, Y. Tian, R. G. Iyer, Y. Ma, O. Wiest, X. Zhang, W. Wang, C. Zhang *et al.*, "Graph-based molecular representation learning," *arXiv preprint arXiv:2207.04869*, 2022.
- <span id="page-16-6"></span>[7] S. Rahmani, A. Baghbani, N. Bouguila, and Z. Patterson, "Graph neural networks for intelligent transportation systems: A survey," *IEEE Transactions on Intelligent Transportation Systems*, 2023.
- <span id="page-16-7"></span>[8] R. Zheng, L. Qu, B. Cui, Y. Shi, and H. Yin, "Automl for deep recommender systems: A survey," *ACM Transactions on Information Systems*, vol. 41, no. 4, pp. 1–38, 2023.
- <span id="page-16-8"></span>[9] J. Yu, H. Yin, X. Xia, T. Chen, J. Li, and Z. Huang, "Self-supervised learning for recommender systems: A survey," *IEEE Transactions on Knowledge and Data Engineering*, 2023.
- <span id="page-16-9"></span>[10] W. L. Hamilton, Z. Ying, and J. Leskovec, "Inductive representation learning on large graphs," in *Advances in Neural Information Processing Systems 30: Annual Conference on Neural Information Processing Systems 2017, December 4-9, 2017, Long Beach, CA, USA*, 2017, pp. 1024–1034.
- <span id="page-16-10"></span>[11] M. Ding, X. Liu, T. Rabbani, T. Ranadive, T.-C. Tuan, and F. Huang, "Faster hyperparameter search for GNNs via calibrated dataset condensation," *arXiv*, 2022.
- <span id="page-16-11"></span>[12] Q. Yuan, S.-U. Guan, P. Ni, T. Luo, K. L. Man, P. Wong, and V. Chang, "Continual graph learning: A survey," *arXiv preprint arXiv:2301.12230*, 2023.
- <span id="page-16-12"></span>[13] B. M. Oloulade, J. Gao, J. Chen, T. Lyu, and R. Al-Sabri, "Graph neural architecture search: A survey," *Tsinghua Science and Technology*, vol. 27, no. 4, pp. 692–708, 2021.
- <span id="page-16-13"></span>[14] J. Gilmer, S. S. Schoenholz, P. F. Riley, O. Vinyals, and G. E. Dahl, "Neural message passing for quantum chemistry," in *ICML*, 2017.
- <span id="page-16-14"></span>[15] J. Chen, T. Ma, and C. Xiao, "Fastgcn: Fast learning with graph convolutional networks via importance sampling," in *6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 - May 3, 2018, Conference Track Proceedings*. OpenReview.net, 2018.
- <span id="page-16-15"></span>[16] H. Zeng, H. Zhou, A. Srivastava, R. Kannan, and V. K. Prasanna, "Graphsaint: Graph sampling based inductive learning method," in *8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia, April 26-30, 2020*. OpenReview.net, 2020.
- <span id="page-16-16"></span>[17] F. Wu, A. H. S. Jr., T. Zhang, C. Fifty, T. Yu, and K. Q. Weinberger, "Simplifying graph convolutional networks," in *ICML*, 2019.
- <span id="page-16-17"></span>[18] W. Zhang, Z. Sheng, M. Yang, Y. Li, Y. Shen, Z. Yang, and B. Cui, "Nafs: A simple yet tough-to-beat baseline for graph representation learning," in *International Conference on Machine Learning*. PMLR, 2022, pp. 26 467–26 483.
- <span id="page-16-18"></span>[19] W. Zhang, Z. Yin, Z. Sheng, Y. Li, W. Ouyang, X. Li, Y. Tao, Z. Yang, and B. Cui, "Graph attention multi-layer perceptron," in *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2022, pp. 4560–4570.
- <span id="page-16-19"></span>[20] L. Ma, Z. Sheng, X. Li, X. Gao, Z. Hao, L. Yang, W. Zhang, and B. Cui, "Acceleration algorithms in gnns: A survey," *arXiv preprint arXiv:2405.04114*, 2024.
- <span id="page-16-20"></span>[21] C. Yang, D. Bo, J. Liu, Y. Peng, B. Chen, H. Dai, A. Sun, Y. Yu, Y. Xiao, Q. Zhang *et al.*, "Data-centric graph learning: A survey," *arXiv*, 2023.
- <span id="page-16-21"></span>[22] D. A. Spielman and N. Srivastava, "Graph sparsification by effective resistances," in *Proceedings of the fortieth annual ACM symposium on Theory of computing*, 2008, pp. 563–568.

- <span id="page-17-0"></span>[23] A. Loukas and P. Vandergheynst, "Spectrally approximating large graphs with smaller graphs," in *Proceedings of the 35th International Conference on Machine Learning, ICML 2018*, 2018.
- <span id="page-17-1"></span>[24] G. Bravo Hermsdorff and L. Gunderson, "A unifying framework for spectrum-preserving graph sparsification and coarsening," *Advances in Neural Information Processing Systems*, vol. 32, 2019.
- <span id="page-17-2"></span>[25] M. Hashemi, S. Gong, J. Ni, W. Fan, B. A. Prakash, and W. Jin, "A comprehensive survey on graph reduction: Sparsification, coarsening, and condensation," *International Joint Conference on Artificial Intelligence (IJCAI)*, 2024.
- <span id="page-17-3"></span>[26] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah, "Graph condensation for graph neural networks," in *ICLR*, 2022.
- <span id="page-17-4"></span>[27] X. Gao, T. Chen, Y. Zang, W. Zhang, Q. V. H. Nguyen, K. Zheng, and H. Yin, "Graph condensation for inductive node representation learning," in *ICDE*, 2024.
- <span id="page-17-5"></span>[28] Y. Liu, R. Qiu, and Z. Huang, "CaT: Balanced continual graph learning with graph condensation," in *ICDM*, 2023.
- <span id="page-17-6"></span>[29] X. Zheng, M. Zhang, C. Chen, Q. V. H. Nguyen, X. Zhu, and S. Pan, "Structure-free graph condensation: From large-scale graphs to condensed graph-free data," in *NeurIPS*, 2023.
- <span id="page-17-7"></span>[30] Z. Xu, Y. Chen, M. Pan, H. Chen, M. Das, H. Yang, and H. Tong, "Kernel ridge regression-based graph dataset distillation," in *SIGKDD*, 2023.
- <span id="page-17-8"></span>[31] B. Yang, K. Wang, Q. Sun, C. Ji, X. Fu, H. Tang, Y. You, and J. Li, "Does graph distillation see like vision dataset counterpart?" in *NeurIPS*, 2023.
- <span id="page-17-9"></span>[32] Y. Liu, "Fairgraph: Automated graph debiasing with gradient matching," in *Proceedings of the 32nd ACM International Conference on Information and Knowledge Management*, 2023, pp. 4135–4139.
- <span id="page-17-10"></span>[33] Y. Dong, B. Zhang, Y. Yuan, N. Zou, Q. Wang, and J. Li, "Reliant: Fair knowledge distillation for graph neural networks," in *Proceedings of the 2023 SIAM International Conference on Data Mining (SDM)*. SIAM, 2023, pp. 154–162.
- <span id="page-17-11"></span>[34] S. Bourmpoulias, D. Zeginis, and K. Tarabanis, "An entity event knowledge graph for human resources management in public administration: the case of education personnel," in *2023 IEEE 25th Conference on Business Informatics (CBI)*. IEEE, 2023, pp. 1–8.
- <span id="page-17-12"></span>[35] N. Lettieri, A. Guarino, D. Malandrino, and R. Zaccagnino, "Knowledge mining and social dangerousness assessment in criminal justice: metaheuristic integration of machine learning and graph-based inference," *Artificial Intelligence and Law*, vol. 31, no. 4, pp. 653–702, 2023.
- <span id="page-17-13"></span>[36] J. Wang, S. Zhang, Y. Xiao, and R. Song, "A review on graph neural network methods in financial applications," *arXiv preprint arXiv:2111.15367*, 2021.
- <span id="page-17-14"></span>[37] Y. Zhu, W. Xu, J. Zhang, Q. Liu, S. Wu, and L. Wang, "Deep graph structure learning for robust representations: A survey," *arXiv preprint arXiv:2103.03036*, vol. 14, pp. 1–1, 2021.
- <span id="page-17-15"></span>[38] J. Geng, Z. Chen, Y. Wang, H. Woisetschlaeger, S. Schimmler, R. Mayer, Z. Zhao, and C. Rong, "A survey on dataset distillation: Approaches, applications and future directions," in *IJCAI*, 2023.
- <span id="page-17-16"></span>[39] R. Yu, S. Liu, and X. Wang, "Dataset distillation: A comprehensive review," *TPAMI*, 2023.
- <span id="page-17-17"></span>[40] N. Sachdeva and J. McAuley, "Data distillation: A survey," *TMLR*, 2023.
- <span id="page-17-18"></span>[41] S. Lei and D. Tao, "A comprehensive survey of dataset distillation," *TPAMI*, 2024.
- <span id="page-17-19"></span>[42] X. Zheng, Y. Liu, Z. Bao, M. Fang, X. Hu, A. W.-C. Liew, and S. Pan, "Towards data-centric graph machine learning: Review and outlook," *arXiv*, 2023.
- <span id="page-17-20"></span>[43] N. Shabani, J. Wu, A. Beheshti, Q. Z. Sheng, J. Foo, V. Haghighi, A. Hanif, and M. Shahabikargar, "A comprehensive survey on graph summarization with graph neural networks," *IEEE Transactions on Artificial Intelligence*, 2024.
- <span id="page-17-21"></span>[44] S. Zhang, A. Sohrabizadeh, C. Wan, Z. Huang, Z. Hu, Y. Wang, J. Cong, Y. Sun *et al.*, "A survey on graph neural network acceleration: Algorithms, systems, and customized hardware," *arXiv*, 2023.
- <span id="page-17-22"></span>[45] H. Xu, L. Zhang, Y. Ma, S. Zhou, Z. Zheng, and B. Jiajun, "A survey on graph condensation," *arXiv preprint arXiv:2402.02000*, 2024.
- <span id="page-17-23"></span>[46] Y. Liu, R. Qiu, and Z. Huang, "Gcondenser: Benchmarking graph condensation," *arXiv preprint arXiv:2405.14246*, 2024.
- <span id="page-17-24"></span>[47] S. Gong, J. Ni, N. Sachdeva, C. Yang, and W. Jin, "Gc-bench: A benchmark framework for graph condensation with new insights," *arXiv preprint arXiv:2406.16715*, 2024.
- <span id="page-17-25"></span>[48] Q. Sun, Z. Chen, B. Yang, C. Ji, X. Fu, S. Zhou, H. Peng, J. Li, and P. S. Yu, "Gc-bench: An open and unified benchmark for graph condensation," *arXiv preprint arXiv:2407.00615*, 2024.

- <span id="page-17-26"></span>[49] X. Li, K. Wang, H. Deng, Y. Liang, and D. Wu, "Attend who is weak: Enhancing graph condensation via cross-free adversarial training," *arXiv*, 2023.
- <span id="page-17-27"></span>[50] J. Gao and J. Wu, "Multiple sparse graphs condensation," *Knowledge-Based Systems*, 2023.
- <span id="page-17-28"></span>[51] Q. Feng, Z. Jiang, R. Li, Y. Wang, N. Zou, J. Bian, and X. Hu, "Fair graph distillation," in *NeurIPS*, 2023.
- <span id="page-17-29"></span>[52] R. Mao, W. Fan, and Q. Li, "GCARe: Mitigating subgroup unfairness in graph condensation through adversarial regularization," *Applied Sciences*, 2023.
- <span id="page-17-30"></span>[53] T. Zhang, Y. Zhang, K. Wang, K. Wang, B. Yang, K. Zhang, W. Shao, P. Liu, J. T. Zhou, and Y. You, "Two trades is not baffled: Condense graph via crafting rational gradient matching," *arXiv preprint arXiv:2402.04924*, 2024.
- <span id="page-17-31"></span>[54] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin, "Condensing graphs via one-step gradient matching," in *SIGKDD*, 2022
- <span id="page-17-32"></span>[55] J. Gao, J. Wu, and J. Ding, "Heterogeneous graph condensation," *IEEE Transactions on Knowledge and Data Engineering*, 2024.
- <span id="page-17-33"></span>[56] J. Fang, X. Li, Y. Sui, Y. Gao, G. Zhang, K. Wang, X. Wang, and X. He, "Exgc: Bridging efficiency and explainability in graph condensation," *WWW*, 2024.
- <span id="page-17-34"></span>[57] B. Yan, "Federated graph condensation with information bottleneck principles," *arXiv preprint arXiv:2405.03911*, 2024.
- <span id="page-17-35"></span>[58] Q. Pan, R. Wu, T. Liu, T. Zhang, Y. Zhu, and W. Wang, "FedGKD: Unleashing the power of collaboration in federated graph neural networks," *arXiv*, 2023.
- <span id="page-17-36"></span>[59] Y. Liu and Y. Shen, "Tinygraph: Joint feature and node condensation for graph neural networks," *arXiv preprint arXiv:2407.08064*, 2024.
- <span id="page-17-37"></span>[60] X. Gao, H. Yin, T. Chen, G. Ye, W. Zhang, and B. Cui, "Robgc: Towards robust graph condensation," *arXiv*, 2024.
- <span id="page-17-38"></span>[61] Y. Zhang, T. Zhang, K. Wang, Z. Guo, Y. Liang, X. Bresson, W. Jin, and Y. You, "Navigating complexity: Toward lossless graph condensation via expanding window matching," *ICML 2024*, 2024.
- <span id="page-17-39"></span>[62] Z. Liu, C. Zeng, and G. Zheng, "Graph data condensation via selfexpressive graph structure reconstruction," *KDD*, 2024.
- <span id="page-17-40"></span>[63] L. Wang, W. Fan, J. Li, Y. Ma, and Q. Li, "Fast graph condensation with structure-based neural tangent kernel," *arXiv*, 2023.
- <span id="page-17-41"></span>[64] X. Gao, T. Chen, W. Zhang, Y. Li, X. Sun, and H. Yin, "Graph condensation for open-world graph learning," *KDD*, 2024.
- <span id="page-17-42"></span>[65] M. Liu, S. Li, X. Chen, and L. Song, "Graph condensation via receptive field distribution matching," *arXiv*, 2022.
- <span id="page-17-43"></span>[66] Y. Liu, D. Bo, and C. Shi, "Graph distillation via eigenbasis matching," *ICML*, 2024.
- <span id="page-17-44"></span>[67] Y. Liu, R. Qiu, Y. Tang, H. Yin, and Z. Huang, "PUMA: Efficient continual graph learning with graph condensation," *arXiv*, 2023.
- <span id="page-17-45"></span>[68] X. Gao, T. Chen, W. Zhang, J. Yu, G. Ye, Q. V. H. Nguyen, and H. Yin, "Rethinking and accelerating graph condensation: A trainingfree approach with class partition," *arXiv preprint arXiv:2405.13707*, 2024.
- <span id="page-17-46"></span>[69] Z. Xiao, S. Liu, Y. Wang, T. Zheng, and M. Song, "Disentangled condensation for large-scale graphs," *arXiv preprint arXiv:2401.12231*, 2024.
- <span id="page-17-47"></span>[70] Z. Xiao, Y. Wang, S. Liu, H. Wang, M. Song, and T. Zheng, "Simple graph condensation," *arXiv preprint arXiv:2403.14951*, 2024.
- <span id="page-17-48"></span>[71] M. Gupta, S. Manchanda, S. Ranu, and H. Kodamana, "Mirage: Modelagnostic graph distillation for graph classification," *ICLR*, 2024.
- <span id="page-17-49"></span>[72] M. Wu, S. Pan, and X. Zhu, "Openwgl: Open-world graph learning," in *2020 IEEE international conference on data mining (icdm)*. IEEE, 2020, pp. 681–690.
- <span id="page-17-50"></span>[73] J. Bergstra, R. Bardenet, Y. Bengio, and B. Kégl, "Algorithms for hyper-parameter optimization," *Advances in neural information processing systems*, vol. 24, 2011.
- <span id="page-17-51"></span>[74] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *ICLR*, 2020.
- <span id="page-17-52"></span>[75] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *CVPR*, 2022, pp. 4750–4759.
- <span id="page-17-53"></span>[76] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," in *ICLR*, 2020.
- <span id="page-17-54"></span>[77] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *WACV*, 2023.
- <span id="page-17-55"></span>[78] X. Wei, X. Gong, Y. Zhan, B. Du, Y. Luo, and W. Hu, "Clnode: Curriculum learning for node classification," in *Proceedings of the Sixteenth ACM International Conference on Web Search and Data Mining*, 2023, pp. 670–678.

- <span id="page-18-0"></span>[79] S. S. Du, K. Hou, R. R. Salakhutdinov, B. Poczos, R. Wang, and K. Xu, "Graph neural tangent kernel: Fusing graph neural networks with graph kernels," in *NeurIPS*, 2019.
- <span id="page-18-1"></span>[80] C.-Y. Lu, H. Min, Z.-Q. Zhao, L. Zhu, D.-S. Huang, and S. Yan, "Robust and efficient subspace segmentation via least squares regression," in *Computer Vision–ECCV 2012: 12th European Conference on Computer Vision, Florence, Italy, October 7-13, 2012, Proceedings, Part VII 12*. Springer, 2012, pp. 347–360.
- <span id="page-18-2"></span>[81] D. Bo, X. Wang, Y. Liu, Y. Fang, Y. Li, and C. Shi, "A survey on spectral graph neural networks," *arXiv preprint arXiv:2302.05631*, 2023.
- <span id="page-18-3"></span>[82] M. Zhu, X. Wang, C. Shi, H. Ji, and P. Cui, "Interpreting and unifying graph neural networks with an optimization framework," in *Proceedings of the Web Conference 2021*, 2021, pp. 1215–1226.
- <span id="page-18-4"></span>[83] A. Gretton, K. M. Borgwardt, M. J. Rasch, B. Schölkopf, and A. Smola, "A kernel two-sample test," *JMLR*, 2012.
- <span id="page-18-5"></span>[84] N. Mehrabi, F. Morstatter, N. Saxena, K. Lerman, and A. Galstyan, "A survey on bias and fairness in machine learning," *ACM computing surveys (CSUR)*, 2021.
- <span id="page-18-6"></span>[85] W. Song, Y. Dong, N. Liu, and J. Li, "Guide: Group equality informed individual fairness in graph neural networks," in *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2022, pp. 1625–1634.
- <span id="page-18-7"></span>[86] A. Chen, R. A. Rossi, N. Park, P. Trivedi, Y. Wang, T. Yu, S. Kim, F. Dernoncourt, and N. K. Ahmed, "Fairness-aware graph neural networks: A survey," *ACM Transactions on Knowledge Discovery from Data*, vol. 18, no. 6, pp. 1–23, 2024.
- <span id="page-18-8"></span>[87] S. M. Randall, J. H. Boyd, A. M. Ferrante, J. K. Bauer, and J. B. Semmens, "Use of graph theory measures to identify errors in record linkage," *Computer methods and programs in biomedicine*, vol. 115, no. 2, pp. 55–63, 2014.
- <span id="page-18-9"></span>[88] H. Tu, S. Yu, V. Saikrishna, F. Xia, and K. Verspoor, "Deep outdated fact detection in knowledge graphs," in *2023 IEEE International Conference on Data Mining Workshops (ICDMW)*. IEEE, 2023, pp. 1443–1452.
- <span id="page-18-10"></span>[89] Z. Kang, H. Pan, S. C. Hoi, and Z. Xu, "Robust graph learning from noisy data," *IEEE transactions on cybernetics*, vol. 50, no. 5, pp. 1833– 1843, 2019.
- <span id="page-18-11"></span>[90] Z. Gao, S. Bhattacharya, L. Zhang, R. S. Blum, A. Ribeiro, and B. M. Sadler, "Training robust graph neural networks with topology adaptive edge dropping," *arXiv preprint arXiv:2106.02892*, 2021.
- <span id="page-18-12"></span>[91] J. Wu, N. Lu, Z. Dai, W. Fan, S. Liu, Q. Li, and K. Tang, "Backdoor graph condensation," *arXiv preprint arXiv:2407.11025*, 2024.
- <span id="page-18-13"></span>[92] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-18-14"></span>[93] C. J. Maddison, A. Mnih, and Y. W. Teh, "The concrete distribution: A continuous relaxation of discrete random variables," in *ICLR*, 2016.
- <span id="page-18-15"></span>[94] E. Dai and S. Wang, "Say no to the discrimination: Learning fair graph neural networks with limited sensitive attribute information," in *Proceedings of the 14th ACM International Conference on Web Search and Data Mining*, 2021, pp. 680–688.
- <span id="page-18-16"></span>[95] A. Beutel, J. Chen, Z. Zhao, and E. H. Chi, "Data decisions and theoretical implications when adversarially learning fair representations," *arXiv preprint arXiv:1707.00075*, 2017.
- <span id="page-18-17"></span>[96] C. Louizos, K. Swersky, Y. Li, M. Welling, and R. Zemel, "The variational fair autoencoder," *arXiv preprint arXiv:1511.00830*, 2015.
- <span id="page-18-18"></span>[97] T. N. Kipf and M. Welling, "Semi-supervised classification with graph convolutional networks," in *5th International Conference on Learning Representations, ICLR 2017, Toulon, France, April 24-26, 2017, Conference Track Proceedings*, 2017.
- <span id="page-18-19"></span>[98] W. Hu, M. Fey, M. Zitnik, Y. Dong, H. Ren, B. Liu, M. Catasta, and J. Leskovec, "Open graph benchmark: Datasets for machine learning on graphs," *arXiv preprint arXiv:2005.00687*, 2020.
- <span id="page-18-20"></span>[99] J. Gasteiger, A. Bojchevski, and S. Günnemann, "Predict then propagate: Graph neural networks meet personalized pagerank," in *International Conference on Learning Representations (ICLR)*, 2019.
- <span id="page-18-21"></span>[100] M. Defferrard, X. Bresson, and P. Vandergheynst, "Convolutional neural networks on graphs with fast localized spectral filtering," in *Advances in Neural Information Processing Systems 29: Annual Conference on Neural Information Processing Systems 2016, December 5-10, 2016, Barcelona, Spain*, 2016.
- <span id="page-18-22"></span>[101] P. Velickovic, G. Cucurull, A. Casanova, A. Romero, P. Liò, and Y. Bengio, "Graph attention networks," in *6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada*, 2018.

- <span id="page-18-23"></span>[102] W. Jin, Y. Ma, X. Liu, X. Tang, S. Wang, and J. Tang, "Graph structure learning for robust graph neural networks," in *Proceedings of the 26th ACM SIGKDD international conference on knowledge discovery & data mining*, 2020, pp. 66–74.
- <span id="page-18-24"></span>[103] Y. Gao, H. Yang, P. Zhang, C. Zhou, and Y. Hu, "Graph neural architecture search," in *International joint conference on artificial intelligence*. International Joint Conference on Artificial Intelligence, 2021.
- <span id="page-18-25"></span>[104] F. G. Febrinanto, F. Xia, K. Moore, C. Thapa, and C. Aggarwal, "Graph lifelong learning: A survey," *IEEE Computational Intelligence Magazine*, vol. 18, no. 1, pp. 32–51, 2023.
- <span id="page-18-26"></span>[105] X. Fu, B. Zhang, Y. Dong, C. Chen, and J. Li, "Federated graph machine learning: A survey of concepts, techniques, and applications," *ACM SIGKDD Explorations Newsletter*, vol. 24, no. 2, pp. 32–47, 2022.
- <span id="page-18-27"></span>[106] N. Sachdeva, M. Dhaliwal, C.-J. Wu, and J. McAuley, "Infinite recommendation networks: A data-centric approach," *Advances in Neural Information Processing Systems*, vol. 35, pp. 31 292–31 305, 2022.
- <span id="page-18-28"></span>[107] X. Wang, D. Bo, C. Shi, S. Fan, Y. Ye, and S. Y. Philip, "A survey on heterogeneous graph embedding: methods, techniques, applications and sources," *IEEE Transactions on Big Data*, vol. 9, no. 2, pp. 415–436, 2022.
- <span id="page-18-29"></span>[108] T. Akiba, S. Sano, T. Yanase, T. Ohta, and M. Koyama, "Optuna: A next-generation hyperparameter optimization framework," in *The 25th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*, 2019, pp. 2623–2631.
- <span id="page-18-30"></span>[109] Y. Li, W. Jin, H. Xu, and J. Tang, "Deeprobust: A pytorch library for adversarial attacks and defenses," *arXiv preprint arXiv:2005.06149*, 2020.
- <span id="page-18-31"></span>[110] X. Zheng, Y. Liu, S. Pan, M. Zhang, D. Jin, and P. S. Yu, "Graph neural networks for graphs with heterophily: A survey," *arXiv preprint arXiv:2202.07082*, 2022.
- <span id="page-18-32"></span>[111] J. Bang-Jensen and G. Z. Gutin, *Digraphs: theory, algorithms and applications*. Springer Science & Business Media, 2008.
- <span id="page-18-33"></span>[112] S. M. Kazemi, R. Goel, K. Jain, I. Kobyzev, A. Sethi, P. Forsyth, and P. Poupart, "Representation learning for dynamic graphs: A survey," *The Journal of Machine Learning Research*, vol. 21, no. 1, pp. 2648– 2720, 2020.
- <span id="page-18-34"></span>[113] X. Gao, Y. Li, T. Chen, G. Ye, W. Zhang, and H. Yin, "Contrastive graph condensation: Advancing data versatility through self-supervised learning," *arXiv preprint arXiv:2411.17063*, 2024.
- <span id="page-18-35"></span>[114] L. Akoglu, H. Tong, and D. Koutra, "Graph based anomaly detection and description: a survey," *Data mining and knowledge discovery*, vol. 29, pp. 626–688, 2015.
- <span id="page-18-36"></span>[115] F. Xia, K. Sun, S. Yu, A. Aziz, L. Wan, S. Pan, and H. Liu, "Graph learning: A survey," *IEEE Transactions on Artificial Intelligence*, vol. 2, no. 2, pp. 109–127, 2021.
- <span id="page-18-37"></span>[116] Y. Zhu, Y. Du, Y. Wang, Y. Xu, J. Zhang, Q. Liu, and S. Wu, "A survey on deep graph generation: Methods and applications," in *Learning on Graphs Conference*. PMLR, 2022, pp. 47–1.
- <span id="page-18-38"></span>[117] L. Sun, Y. Dou, C. Yang, K. Zhang, J. Wang, S. Y. Philip, L. He, and B. Li, "Adversarial attack and defense on graph data: A survey," *IEEE Transactions on Knowledge and Data Engineering*, 2022.
- <span id="page-18-39"></span>[118] H. Hu, Z. Salcic, L. Sun, G. Dobbie, P. S. Yu, and X. Zhang, "Membership inference attacks on machine learning: A survey," *ACM Computing Surveys (CSUR)*, vol. 54, no. 11s, pp. 1–37, 2022.
- <span id="page-18-40"></span>[119] H. Yuan, H. Yu, S. Gui, and S. Ji, "Explainability in graph neural networks: A taxonomic survey," *IEEE transactions on pattern analysis and machine intelligence*, vol. 45, no. 5, pp. 5782–5799, 2022.
- <span id="page-18-41"></span>[120] F. Wang, P. Cui, J. Pei, Y. Song, and C. Zang, "Recent advances on graph analytics and its applications in healthcare," in *Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*, 2020, pp. 3545–3546.