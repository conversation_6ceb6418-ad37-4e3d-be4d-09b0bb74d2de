{"table_of_contents": [{"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 435.0], [214.5, 435.0], [214.5, 445.11328125], [133.5, 445.11328125]]}, {"title": "II. DEFINITION AND TAXONOMY", "heading_level": null, "page_id": 2, "polygon": [[102.75, 596.3203125], [245.25, 596.3203125], [245.25, 605.6015625], [102.75, 605.6015625]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[46.5, 618.75], [117.75, 618.75], [117.75, 628.8046875], [46.5, 628.8046875]]}, {"title": "<PERSON><PERSON> for Graph Condensation", "heading_level": null, "page_id": 3, "polygon": [[48.0, 347.25], [198.0, 347.25], [198.0, 357.328125], [48.0, 357.328125]]}, {"title": "C. Taxonomy", "heading_level": null, "page_id": 3, "polygon": [[310.5, 289.5], [367.5, 289.5], [367.5, 298.*********], [310.5, 298.*********]]}, {"title": "III. OPTIMIZATION STRATEGIES", "heading_level": null, "page_id": 4, "polygon": [[366.75, 636.75], [506.25, 636.75], [506.25, 646.59375], [366.75, 646.59375]]}, {"title": "IV. GRAPH CONDENSATION METHODS", "heading_level": null, "page_id": 6, "polygon": [[351.421875, 321.0], [521.25, 321.0], [521.25, 330.*********], [351.421875, 330.*********]]}, {"title": "A. Effective Graph Condensation", "heading_level": null, "page_id": 6, "polygon": [[309.75, 472.18359375], [449.25, 472.18359375], [449.25, 481.5], [309.75, 481.5]]}, {"title": "B. Generalized Graph Condensation", "heading_level": null, "page_id": 8, "polygon": [[48.0, 711.0], [200.25, 711.0], [200.25, 721.23046875], [48.0, 721.23046875]]}, {"title": "C. Efficient Graph Condensation", "heading_level": null, "page_id": 9, "polygon": [[48.0, 87.0], [185.25, 87.0], [185.25, 95.95458984375], [48.0, 95.95458984375]]}, {"title": "D<PERSON> Fair Graph Condensation", "heading_level": null, "page_id": 10, "polygon": [[47.25, 280.5], [169.734375, 280.5], [169.734375, 290.42578125], [47.25, 290.42578125]]}, {"title": "<PERSON><PERSON> Graph Condensation", "heading_level": null, "page_id": 10, "polygon": [[310.5, 56.25], [442.5, 56.25], [442.5, 66.66064453125], [310.5, 66.66064453125]]}, {"title": "V. CONDENSED GRAPH GENERATION", "heading_level": null, "page_id": 10, "polygon": [[355.904296875, 525.75], [518.25, 525.75], [518.25, 535.60546875], [355.904296875, 535.60546875]]}, {"title": "A. Feature Generation", "heading_level": null, "page_id": 10, "polygon": [[309.0, 639.0], [405.0, 639.0], [405.0, 649.30078125], [309.0, 649.30078125]]}, {"title": "B. Structure Generation", "heading_level": null, "page_id": 11, "polygon": [[47.8125, 409.5], [147.75, 409.5], [147.75, 419.203125], [47.8125, 419.203125]]}, {"title": "VI. EMPIRICAL STUDIES", "heading_level": null, "page_id": 11, "polygon": [[382.5, 711.0], [492.0, 711.0], [492.0, 720.84375], [382.5, 720.84375]]}, {"title": "A. Evaluation Metric", "heading_level": null, "page_id": 12, "polygon": [[47.25, 387.75], [136.5, 387.75], [136.5, 396.7734375], [47.25, 396.7734375]]}, {"title": "B. Experimental Settings", "heading_level": null, "page_id": 12, "polygon": [[310.5, 663.0], [414.0, 663.0], [414.0, 672.1171875], [310.5, 672.1171875]]}, {"title": "<PERSON>. Experimental Results", "heading_level": null, "page_id": 13, "polygon": [[47.999267578125, 411.0], [149.25, 411.0], [149.25, 419.9765625], [47.999267578125, 419.9765625]]}, {"title": "VII. APPLICATIONS AND RESOURCES", "heading_level": null, "page_id": 14, "polygon": [[93.0, 229.5], [255.75, 229.5], [255.75, 238.60546875], [93.0, 238.60546875]]}, {"title": "A. Practical Applications", "heading_level": null, "page_id": 14, "polygon": [[46.5, 395.25], [154.5, 395.25], [154.5, 404.5078125], [46.5, 404.5078125]]}, {"title": "B. Open-Source Libraries", "heading_level": null, "page_id": 15, "polygon": [[47.8125, 401.25], [156.0, 401.25], [156.0, 411.46875], [47.8125, 411.46875]]}, {"title": "VIII. CHALLENGES AND FUTURE DIRECTIONS", "heading_level": null, "page_id": 15, "polygon": [[334.6875, 148.5], [538.5, 148.5], [538.5, 158.6513671875], [334.6875, 158.6513671875]]}, {"title": "<PERSON><PERSON>den<PERSON> for Diverse Graphs", "heading_level": null, "page_id": 15, "polygon": [[309.0, 240.75], [465.0, 240.75], [465.0, 250.013671875], [309.0, 250.013671875]]}, {"title": "B. Task-Agnostic Graph Condensation", "heading_level": null, "page_id": 15, "polygon": [[310.5, 404.25], [471.0, 404.25], [471.0, 413.7890625], [310.5, 413.7890625]]}, {"title": "C. Efficient Evaluation for Condensed Graphs", "heading_level": null, "page_id": 15, "polygon": [[311.25, 567.703125], [503.25, 567.703125], [503.25, 576.984375], [311.25, 576.984375]]}, {"title": "D. Secure Graph Condensation", "heading_level": null, "page_id": 16, "polygon": [[47.25, 141.75], [180.0, 141.75], [180.0, 151.1103515625], [47.25, 151.1103515625]]}, {"title": "E. Explainable Graph Condensation", "heading_level": null, "page_id": 16, "polygon": [[48.0, 351.75], [199.5, 351.75], [199.5, 361.58203125], [48.0, 361.58203125]]}, {"title": "IX. CONCLUSION", "heading_level": null, "page_id": 16, "polygon": [[134.25, 573.0], [213.75, 573.0], [213.75, 583.171875], [134.25, 583.171875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 16, "polygon": [[407.900390625, 57.0], [465.75, 57.0], [465.75, 66.515625], [407.900390625, 66.515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 126], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 4], ["TextInlineMath", 2], ["Footnote", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 107], ["Text", 6], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 683, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 678], ["Line", 111], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 121], ["Text", 6], ["ListItem", 5], ["Figure", 3], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2015, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 108], ["Text", 6], ["Reference", 5], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 186], ["TableCell", 32], ["Text", 7], ["Equation", 6], ["TextInlineMath", 6], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10096, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 574], ["Line", 109], ["TableCell", 76], ["Text", 8], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 640], ["Span", 568], ["Line", 72], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8100, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 116], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 317], ["Line", 115], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 318], ["Line", 110], ["Text", 9], ["SectionHeader", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 104], ["TableCell", 40], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 684], ["TableCell", 339], ["Line", 106], ["Text", 9], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 131], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 363], ["Line", 101], ["TableCell", 83], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10765, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 101], ["TableCell", 33], ["Text", 8], ["Reference", 6], ["SectionHeader", 5], ["Footnote", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 125], ["ListItem", 22], ["Reference", 22], ["Text", 4], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 588], ["Line", 154], ["ListItem", 56], ["Reference", 56], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 501], ["Line", 140], ["ListItem", 42], ["Reference", 42], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Graph_Condensation__A_Survey"}