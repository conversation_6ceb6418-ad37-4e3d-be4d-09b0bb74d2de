# GRAPH CONDENSATION FOR GRAPH NEURAL NET-WORKS

<PERSON> <sup>∗</sup> Michigan <NAME_EMAIL>

<PERSON><PERSON><PERSON> Zhao Carnegie <NAME_EMAIL>

<PERSON>zen Liu Snap Inc. <EMAIL> Jiliang Tang Michigan <NAME_EMAIL>

<PERSON><PERSON> UCLA <EMAIL>

<PERSON> Shah Snap Inc. <EMAIL>

# ABSTRACT

Given the prevalence of large-scale graphs in real-world applications, the storage and time for training neural models have raised increasing concerns. To alleviate the concerns, we propose and study the problem of *graph condensation* for graph neural networks (GNNs). Specifically, we aim to condense the large, original graph into a small, synthetic and highly-informative graph, such that GNNs trained on the small graph and large graph have comparable performance. We approach the condensation problem by imitating the GNN training trajectory on the original graph through the optimization of a gradient matching loss and design a strategy to condense node features and structural information simultaneously. Extensive experiments have demonstrated the effectiveness of the proposed framework in condensing different graph datasets into informative smaller graphs. In particular, we are able to approximate the original test accuracy by 95.3% on Reddit, 99.8% on Flickr and 99.0% on Citeseer, while reducing their graph size by more than 99.9%, and the condensed graphs can be used to train various GNN architectures. Code is released at <https://github.com/ChandlerBang/GCond>.

# 1 INTRODUCTION

Many real-world data can be naturally represented as graphs such as social networks, chemical molecules, transportation networks, and recommender systems [\(Battaglia et al.,](#page-9-0) [2018;](#page-9-0) [Wu et al.,](#page-12-0) [2019b;](#page-12-0) [Zhou et al.,](#page-12-1) [2018\)](#page-12-1). As a generalization of deep neural networks for graph-structured data, graph neural networks (GNNs) have achieved great success in capturing the abundant information residing in graphs and tackle various graph-related applications [\(Wu et al.,](#page-12-0) [2019b;](#page-12-0) [Zhou et al.,](#page-12-1) [2018\)](#page-12-1).

However, the prevalence of large-scale graphs in real-world scenarios, often on the scale of millions of nodes and edges, poses significant computational challenges for training GNNs. More dramatically, the computational cost continues to increase when we need to retrain the models multiple times, e.g., under incremental learning settings, hyperparameter and neural architecture search. To address this challenge, a natural idea is to properly simplify, or reduce the graph so that we can not only speed up graph algorithms (including GNNs) but also facilitate storage, visualization and retrieval for associated graph data analysis tasks.

There are two main strategies to simplify graphs: graph sparsification (Peleg & Schäffer, [1989;](#page-11-0) [Spielman & Teng,](#page-11-1) [2011\)](#page-11-1) and graph coarsening [\(Loukas & Vandergheynst,](#page-11-2) [2018;](#page-11-2) [Loukas,](#page-11-3) [2019\)](#page-11-3). Graph sparsification approximates a graph with a sparse graph by reducing the number of edges, while graph coarsening directly reduces the number of nodes by replacing the original node set with its subset. However, these methods have some shortcomings: (1) sparsification becomes much less promising in simplifying graphs when nodes are also associated with attributes as sparsification does not reduce the node attributes; (2) the goal of sparsification and coarsening is to preserve some graph

<sup>∗</sup>Work done while author was on internship at Snap Inc.

<span id="page-1-1"></span>Image /page/1/Figure/1 description: The image depicts a process of graph condensation. On the left, a large graph labeled (A, X, Y) with 153,932 training nodes is shown, along with test accuracies for four models: GCN (93.9%), SGC (93.5%), APPNP (94.3%), and GraphSAGE (93.0%). An arrow labeled "Condense" points to a smaller, condensed graph on the right, labeled (A', X', Y'), with only 154 training nodes. The test accuracies for the same four models on this condensed graph are GCN (89.4%), SGC (89.6%), APPNP (87.8%), and GraphSAGE (89.1%). The graphs are visualized as networks of colored nodes (blue, green, yellow, and brown) connected by black lines.

Figure 1: We study the graph condensation problem, which seeks to learn a small, synthetic graph, features and labels  $\{A', X', Y'\}$  from a large, original dataset  $\{A, X, Y\}$ , which can be used to train GNN models that generalize comparably to the original. **Shown:** An illustration of our proposed GCOND graph condensation approach's empirical performance, which exhibits *95.3%* of original graph test performance with *99.9%* data reduction.

properties such as principle eigenvalues (Loukas  $\&$  Vandergheynst, [2018\)](#page-11-2) that could be not optimal for the downstream performance of GNNs. In this work, we ask if it is possible to significantly reduce the graph size while providing sufficient information to well train GNN models.

Motivated by dataset distillation [\(Wang et al.,](#page-11-4) [2018\)](#page-11-4) and dataset condensation [\(Zhao et al.,](#page-12-2) [2021\)](#page-12-2) which generate a small set of images to train deep neural networks on the downstream task, we aim to condense a given graph through learning a synthetic graph structure and node attributes. Correspondingly, we propose the task of *graph condensation*<sup>[1](#page-1-0)</sup>. It aims to minimize the performance gap between GNN models trained on a synthetic, simplified graph and the original training graph. In this work, we focus on attributed graphs and the node classification task. We show that we are able to reduce the number of graph nodes to as low as 0.1% while training various GNN architectures to reach surprisingly good performance on the synthetic graph. For example, in Figure [1,](#page-1-1) we condense the graph of the Reddit dataset with 153,932 training nodes into only 154 synthetic nodes together with their connections. In essence, we face two challenges for graph condensation: (1) how to formulate the objective for graph condensation tractable for learning; and (2) how to parameterize the to-be-learned node features and graph structure. To address the above challenges, we adapt the gradient matching scheme in [\(Zhao et al.,](#page-12-2) [2021\)](#page-12-2) and match the gradients of GNN parameters w.r.t. the condensed graph and original graph. In this way, the GNN trained on condensed graph can mimic the training trajectory of that on real data. Further, we carefully design the strategy for parametrizations for the condensed graph. In particular, we introduce the strategy of parameterizing the condensed features as free parameters and model the synthetic graph structure as a function of features, which takes advantage of the implicit relationship between structure and node features, consumes less number of parameters and offers better performance.

Our contributions can be summarized as follows:

- 1. We make the first attempt to condense a large-real graph into a small-synthetic graph, such that the GNN models trained on the large graph and small graph have comparable performance. We introduce a proposed framework for graph condensation (GCOND) which parameterizes the condensed graph structure as a function  $\overline{of}$  condensed node features, and leverages a gradient matching loss as the condensation objective.
- 2. Through extensive experimentation, we show that GCOND is able to condense different graph datasets and achieve comparable performance to their larger counterparts. For instance, GCOND approximates the original test accuracy by 95.3% on Reddit, 99.8% on Flickr and 99.0% on Citeseer, while reducing their graph size by more than 99.9%. Our approach consistently outperforms coarsening, coreset and dataset condensation baselines.
- 3. We show that the condensed graphs can generalize well to different GNN test models. Additionally, we observed reliable correlation of performances between condensed dataset training and whole-dataset training in the neural architecture search (NAS) experiments.

# 2 RELATED WORK

Dataset Distillation & Condensation. Dataset distillation (DD) [\(Wang et al.,](#page-11-4) [2018;](#page-11-4) [Bohdal et al.,](#page-9-1) [2020;](#page-9-1) [Nguyen et al.,](#page-11-5) [2021\)](#page-11-5) aims to distill knowledge of a large training dataset into a small synthetic

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>We aim to condense both graph structure and node attributes. A formal definition is given in Section 3.

dataset, such that a model trained on the synthetic set is able to obtain the comparable performance to that of a model trained on the original dataset. To improve the efficiency of DD, dataset condensation (DC) [\(Zhao et al.,](#page-12-2) [2021;](#page-12-2) [Zhao & Bilen,](#page-12-3) [2021\)](#page-12-3) is proposed to learn the small synthetic dataset by matching the gradients of the network parameters w.r.t. large-real and small-synthetic training data. However, these methods are designed exclusively for image data and are not applicable to non-Euclidean graph-structured data where samples (nodes) are interdependent. In this work, we generalize the problem of dataset condensation to graph domain and we seek to jointly learn the synthetic node features as well as graph structure. Additionally, our work relates to coreset methods [\(Welling,](#page-11-6) [2009;](#page-11-6) [Sener & Savarese,](#page-11-7) [2018;](#page-11-7) [Rebuffi et al.,](#page-11-8) [2017\)](#page-11-8), which seek to find informative samples from the original datasets. However, they rely on the presence of representative samples, and tend to give suboptimal performance.

Graph Sparsification & Coarsening. Graph sparsification and coarsening are two means of reducing the size of a graph. Sparsification reduces the number of edges while approximating pairwise distances (Peleg & Schäffer, [1989\)](#page-11-0), cuts [\(Karger,](#page-10-0) [1999\)](#page-10-0) or eigenvalues [\(Spielman & Teng,](#page-11-1) [2011\)](#page-11-1) while coarsening reduces the number of nodes with similar constraints [\(Loukas & Vandergheynst,](#page-11-2) [2018;](#page-11-2) [Loukas,](#page-11-3) [2019;](#page-11-3) [Deng et al.,](#page-9-2) [2020\)](#page-9-2), typically by grouping original nodes into super-nodes, and defining their connections. [Cai et al.](#page-9-3)  $(2021)$  proposes a GNN-based framework to learn these connections to improve coarsening quality. [Huang et al.](#page-10-1) [\(2021b\)](#page-10-1) adopts coarsening as a preprocessing method to help scale up GNNs. Graph condensation also aims to reduce the number of nodes, but aims to learn synthetic nodes and connections in a supervised way, rather than unsupervised grouping as in these prior works. Graph pooling is also related to our work, but it targets at improving graph-level representation learning (see Appendix [D\)](#page-18-0).

Graph Neural Networks. Graph neural networks (GNNs) are a modern way to capture the intuition that inferences for individual samples (nodes) can be enhanced by utilizing graph-based information from neighboring nodes [\(Kipf & Welling,](#page-10-2) [2017;](#page-10-2) [Hamilton et al.,](#page-10-3) [2017;](#page-10-3) [Klicpera et al.,](#page-10-4) [2019;](#page-10-4) [Velick](#page-11-9)[ovic et al.,](#page-11-9) [2018;](#page-11-9) [Wu et al.,](#page-12-0) [2019b](#page-12-0)[;a;](#page-11-10) [Liu et al.,](#page-11-11) [2020;](#page-11-11) [2021;](#page-11-12) [You et al.,](#page-12-4) [2021;](#page-12-4) [Zhou et al.,](#page-12-5) [2021;](#page-12-5) [Zhao](#page-12-6) [et al.,](#page-12-6) [2022\)](#page-12-6). Due to their prevalence, various real-world applications have been tremendously facilitated including recommender systems [\(Ying et al.,](#page-12-7) [2018a;](#page-12-7) [Fan et al.,](#page-10-5) [2019\)](#page-10-5), computer vision [\(Li](#page-10-6) [et al.,](#page-10-6) [2019\)](#page-10-6) and drug discovery [\(Duvenaud et al.,](#page-9-4) [2015\)](#page-9-4).

Graph Structure Learning. Our work is also related to graph structure learning, which explores methods to learn graphs from data. One line of work [\(Dong et al.,](#page-9-5) [2016;](#page-9-5) [Egilmez et al.,](#page-10-7) [2017\)](#page-10-7) learns graphs under certain structural constraints (e.g. sparsity) based on graph signal processing. Recent efforts aim to learn graphs by leveraging GNNs [\(Franceschi et al.,](#page-10-8) [2019;](#page-10-8) [Jin et al.,](#page-10-9) [2020;](#page-10-9) [Chen et al.,](#page-9-6) [2020\)](#page-9-6). However, these methods are incapable of learning graphs with smaller size, and are thus not applicable for graph condensation. For more related works on graph structure learning and graph generation, we kindly refer the reader to recent surveys [\(Ding et al.,](#page-9-7) [2022;](#page-9-7) [Zhu et al.,](#page-12-8) [2021b\)](#page-12-8).

# 3 METHODOLOGY

In this section, we present our proposed *graph condensation* framework, GCOND. Consider that we have a graph dataset  $\mathcal{T} = \{ \mathbf{A}, \mathbf{X}, \mathbf{Y} \}$ , where  $\mathbf{A} \in \mathbb{R}^{N \times N}$  is the adjacency matrix, N is the number of nodes,  $\mathbf{X} \in \mathbb{R}^{N \times d}$  is the d-dimensional node feature matrix and  $\mathbf{Y} \in \{0, \ldots, C-1\}^N$  denotes the node labels over  $C$  classes. Graph condensation aims to learn a small, synthetic graph dataset  $S = {\mathbf{A}', \mathbf{X}', \mathbf{Y}'}$  with  $\mathbf{A}' \in \mathbb{R}^{N' \times N'}$ ,  $\mathbf{X}' \in \mathbb{R}^{N' \times D}$ ,  $\mathbf{Y}' \in \{0, \dots, C - 1\}^{N'}$  and  $N' \ll N$ , such that a GNN trained on S can achieve comparable performance to one trained on the much larger  $\mathcal T$ . Thus, the objective can be formulated as the following bi-level problem,

$$
\min_{\mathcal{S}} \mathcal{L}(\text{GNN}_{\theta_{\mathcal{S}}}(\mathbf{A}, \mathbf{X}), \mathbf{Y}) \quad \text{s.t} \quad \theta_{\mathcal{S}} = \arg\min_{\theta} \mathcal{L}(\text{GNN}_{\theta}(\mathbf{A}', \mathbf{X}'), \mathbf{Y}'), \tag{1}
$$

where GNN $_{\theta}$  denotes the GNN model parameterized with  $\theta$ ,  $\theta_{\mathcal{S}}$  denotes the parameters of the model trained on  $S$ , and  $\mathcal L$  denotes the loss function used to measure the difference between model predictions and ground truth, i.e. cross-entropy loss. However, optimizing the above objective can lead to overfitting on a specific model initialization. To generate condensed data that generalizes to a distribution of random initializations  $P_{\theta_0}$ , we rewrite the objective as follows:

<span id="page-2-0"></span>
$$
\min_{\mathcal{S}} \mathrm{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \left[ \mathcal{L} \left( \mathrm{GNN}_{\boldsymbol{\theta}_{\mathcal{S}}} (\mathbf{A}, \mathbf{X}), \mathbf{Y} \right) \right] \quad \text{s.t.} \quad \boldsymbol{\theta}_{\mathcal{S}} = \underset{\boldsymbol{\theta}}{\arg \min} \ \mathcal{L} \left( \mathrm{GNN}_{\boldsymbol{\theta}(\boldsymbol{\theta}_0)} (\mathbf{A}', \mathbf{X}'), \mathbf{Y}' \right). \tag{2}
$$

where  $\theta(\theta_0)$  indicates that  $\theta$  is a function acting on  $\theta_0$ . Note that the setting discussed above is for inductive learning where all the nodes are labeled and test nodes are unseen during training. We can easily generalize graph condensation to transductive setting by assuming  $\bf{Y}$  is partially labeled.

## 3.1 GRAPH CONDENSATION VIA GRADIENT MATCHING

To tackle the optimization problem in Eq. [\(2\)](#page-2-0), one strategy is to compute the gradient of  $\mathcal L$  w.r.t  $\mathcal S$  and optimize  $S$  via gradient descent, as in dataset distillation [\(Wang et al.,](#page-11-4) [2018\)](#page-11-4). However, this requires solving a nested loop optimization and unrolling the whole training trajectory of the inner problem, which can be prohibitively expensive. To bypass the bi-level optimization, we follow the gradient matching method proposed in  $(Zhao et al., 2021)$  $(Zhao et al., 2021)$  $(Zhao et al., 2021)$  which aims to match the network parameters w.r.t. large-real and small-synthetic training data by matching their gradients at each training step. In this way, the training trajectory on small-synthetic data  $S$  can mimic that on the large-real data  $\mathcal{T}$ , i.e., the models trained on these two datasets converge to similar solutions (parameters). Concretely, the parameter matching process for GNNs can be modeled as follows:

$$
\min_{\mathcal{S}} \mathcal{E}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D\left(\theta_t^{\mathcal{S}}, \theta_t^{\mathcal{T}}\right) \right] \quad \text{with}
$$
\n
$$
\theta_{t+1}^{\mathcal{S}} = \text{opt}_{\theta} \left( \mathcal{L} \left( \text{GNN}_{\theta_t^{\mathcal{S}}}(\mathbf{A}', \mathbf{X}'), \mathbf{Y}' \right) \right) \text{ and } \theta_{t+1}^{\mathcal{T}} = \text{opt}_{\theta} \left( \mathcal{L} \left( \text{GNN}_{\theta_t^{\mathcal{T}}}(\mathbf{A}, \mathbf{X}), \mathbf{Y} \right) \right) \tag{3}
$$

where  $D(\cdot, \cdot)$  is a distance function, T is the number of steps of the whole training trajectory, opt<sub> $\theta$ </sub> is the update rule for model parameters, and  $\theta_t^S$ ,  $\theta_t^\mathcal{T}$  denote the model parameters trained on S and T at time step  $t$ , respectively. Since our goal is to match the parameters step by step, we then consider one-step gradient descent for the update rule opt $_{\theta}$ :

$$
\boldsymbol{\theta}_{t+1}^{\mathcal{S}} \leftarrow \boldsymbol{\theta}_{t}^{\mathcal{S}} - \eta \nabla_{\boldsymbol{\theta}} \mathcal{L} \left( \text{GNN}_{\boldsymbol{\theta}_{t}^{\mathcal{S}}}(\mathbf{A}', \mathbf{X}'), \mathbf{Y}' \right) \quad \text{ and } \quad \boldsymbol{\theta}_{t+1}^{\mathcal{T}} \leftarrow \boldsymbol{\theta}_{t}^{\mathcal{T}} - \eta \nabla_{\boldsymbol{\theta}} \mathcal{L} \left( \text{GNN}_{\boldsymbol{\theta}_{t}^{\mathcal{T}}}(\mathbf{A}, \mathbf{X}), \mathbf{Y} \right) \tag{4}
$$

where  $\eta$  is the learning rate for the gradient descent. Based on the observation made in [Zhao et al.](#page-12-2) [\(2021\)](#page-12-2) that the distance between  $\theta_t^S$  and  $\theta_t^T$  is typically small, we can simplify the objective as a gradient matching process as follows,

$$
\min_{\mathcal{S}} \mathcal{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \left[ \sum_{t=0}^{T-1} D\left( \nabla_{\boldsymbol{\theta}} \mathcal{L} \left( \text{GNN}_{\boldsymbol{\theta}_t}(\mathbf{A}', \mathbf{X}') , \mathbf{Y}' \right), \nabla_{\boldsymbol{\theta}} \mathcal{L} \left( \text{GNN}_{\boldsymbol{\theta}_t}(\mathbf{A}, \mathbf{X}), \mathbf{Y} \right) \right) \right]
$$
(5)

where  $\theta_t^S$  and  $\theta_t^T$  are replaced by  $\theta_t$ , which is trained on the small-synthetic graph. The distance D is further defined as the sum of the distance dis at each layer. Given two gradients  $G^S \in \mathbb{R}^{d_1 \times d_2}$  and  $G^{\mathcal{T}} \in \mathbb{R}^{d_1 \times d_2}$  at a specific layer, the distance  $dis(\cdot, \cdot)$  used for condensation is defined as follows,

$$
dis(\mathbf{G}^{\mathcal{S}}, \mathbf{G}^{\mathcal{T}}) = \sum_{i=1}^{d_2} \left( 1 - \frac{\mathbf{G}_i^{\mathcal{S}} \cdot \mathbf{G}_i^{\mathcal{T}}}{\|\mathbf{G}_i^{\mathcal{S}}\| \|\mathbf{G}_i^{\mathcal{T}}\|} \right)
$$
(6)

where  $G_i^S$ ,  $G_i^T$  are the *i*-th column vectors of the gradient matrices. With the above formulations, we are able to achieve parameter matching through an efficient strategy of gradient matching.

We note that jointly learning the three variables  $A', X'$  and  $Y'$  is highly challenging, as they are interdependent. Hence, to simplify the problem, we fix the node labels  $Y'$  while keeping the class distribution the same as the original labels Y.

**Graph Sampling.** GNNs are often trained in a full-batch manner [\(Kipf & Welling,](#page-10-2) [2017;](#page-10-2) [Wu](#page-12-0) [et al.,](#page-12-0) [2019b\)](#page-12-0). However, as suggested by previous works that reconstruct data from gradients [\(Zhu](#page-12-9) [et al.,](#page-12-9) [2019\)](#page-12-9), large batch size tends to make reconstruction more difficult because more variables are involved during optimization. To make things worse, the computation cost of GNNs gets expensive on large graphs as the forward pass of GNNs involves the aggregation of enormous neighboring nodes. To address the above issues, we sample a fixed-size set of neighbors on the original graph in each aggregation layer of GNNs and adopt a mini-batch training strategy. To further reduce memory usage and ease optimization, we calculate the gradient matching loss for nodes from different classes separately, as matching the gradients w.r.t. the data from a single class is easier than that from all classes. Specifically, for a given class  $c$ , we sample a batch of nodes of class  $c$  together with a portion of their neighbors from large-real data T. We denote the process as  $(\mathbf{A}_c, \mathbf{X}_c, \mathbf{Y}_c) \sim \mathcal{T}$ . For the condensed graph  $A'$ , we sample a batch of synthetic nodes of class c but do not sample their neighbors. In other words, we use all of their neighbors, i.e., all other nodes, during the aggregation process, since we need to learn the connections with other nodes. We denote the process as  $(\mathbf{A}_c^{\prime}, \mathbf{X}_c^{\prime}, \mathbf{\hat{Y}}_c^{\prime}) \sim S$ .

## 3.2 MODELING CONDENSED GRAPH DATA

One essential challenge in the graph condensation problem is how to model the condensed graph data and resolve dependency among nodes. The most straightforward way is to treat both  $A'$  and  $X'$ 

as free parameters. However, the number of parameters in  $A'$  grows quadratically as  $N'$  increases. The increased model complexity can pose challenges in optimizing the framework and increase the risk of overfitting. Therefore, it is desired to parametrize the condensed adjacency matrix in a way where the number of parameters does not grow too fast. On the other hand, treating  $A'$  and  $X'$ as independent parameters overlooks the implicit correlations between graph structure and features, which have been widely acknowledged in the literature [\(III et al.,](#page-10-10) [2014;](#page-10-10) [Shalizi & Thomas,](#page-11-13) [2011\)](#page-11-13); e.g., in social networks, users interact with others based on their interests, while in e-commerce, users purchase products due to certain product attributes. Hence, we propose to model the condensed graph structure as a function of the condensed node features:

<span id="page-4-0"></span>
$$
\mathbf{A}' = g_{\Phi}(\mathbf{X}'), \quad \text{with } \mathbf{A}'_{ij} = \text{Sigmoid}\left(\frac{\text{MLP}_{\Phi}([\mathbf{x}'_i; \mathbf{x}'_j]) + \text{MLP}_{\Phi}([\mathbf{x}'_j; \mathbf{x}'_i])}{2}\right) \tag{7}
$$

where MLP<sub>Φ</sub> is a multi-layer neural network parameterized with  $\Phi$  and  $[\cdot; \cdot]$  denotes concatenation. In Eq. [\(7\)](#page-4-0), we intentionally control  $A'_{ij} = \hat{A}'_{ji}$  to make the condensed graph structure symmetric since we are mostly dealing with symmetric graphs. It can also adjust to asymmetric graphs by setting  $\mathbf{A}'_{ij} = \text{Sigmoid}(\text{MLP}_{\Phi}([\mathbf{x}_i; \mathbf{x}'_j]).$  Then we rewrite our objective as

<span id="page-4-1"></span>
$$
\min_{\mathbf{X}',\Phi} \mathrm{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \left[ \sum_{t=0}^{T-1} D\left(\nabla_{\boldsymbol{\theta}} \mathcal{L}\left(\mathrm{GNN}_{\boldsymbol{\theta}_t}(\mathbf{X}'), \mathbf{X}'\right), \mathbf{Y}'\right), \nabla_{\boldsymbol{\theta}} \mathcal{L}\left(\mathrm{GNN}_{\boldsymbol{\theta}_t}(\mathbf{A}, \mathbf{X}), \mathbf{Y}\right) \right] \tag{8}
$$

Note that there are two clear benefits of the above formulation over the naïve one (free parameters). Firstly, the number of parameters for modeling graph structure no longer depends on the number of nodes, hence avoiding jointly learning  $O(N^2)$  parameters; as a result, when N' gets larger, GCOND suffers less risk of overfitting. Secondly, if we want to grow the synthetic graph by adding more synthetic nodes condensed from real graph, the trained  $MLP_{\Phi}$  can be employed to infer the connections of new synthetic nodes, and hence we only need to learn their features.

Alternating Optimization Schema. Jointly optimizing  $X'$  and  $\Phi$  is often challenging as they are directly affecting each other. Instead, we propose to alternatively optimize  $X'$  and  $\Phi$ : we update  $\Phi$ for the first  $\tau_1$  epochs and then update  $X^f$  for  $\tau_2$  epochs; the process is repeated until the stopping condition is met – we find empirically that this does better as shown in Appendix  $C$ .

Sparsification. In the learned condensed adjacency matrix  $A'$ , there can exist some small values which have little effect on the aggregation process in GNNs but still take up a certain amount of storage (e.g. 4 bytes per float). Thus, we remove the entries whose values are smaller than a given threshold  $\delta$  to promote sparsity of the learned A'. We further justify that suitable choices of  $\delta$  for sparsification do not degrade performance a lot in Appendix [C.](#page-14-0)

The detailed algorithm can be found in Algorithm 1 in Appendix  $\overline{B}$ . In detail, we first set the condensed label set  $Y'$  to fixed values and initialize  $X'$  as node features randomly selected from each class. In each outer loop, we sample a GNN model initialization  $\theta$  from a distribution  $P_{\theta}$ . Then, for each class we sample the corresponding node batches from  $\mathcal T$  and  $\mathcal S$ , and calculate the gradient matching loss within each class. The sum of losses from different classes are used to update  $X'$  or  $\Phi$ . After that we update the GNN parameters for  $\tau_{\theta}$  epochs. When finishing the updating of condensed graph parameters, we filter edge weights smaller than  $\delta$  to obtain the final sparsified graph structure.

A "Graphless" Model Variant. We now explore another parameterization for the condensed graph data. We provide a model variant named GCOND-X that only learns the condensed node features  $X'$  without learning the condensed structure  $A'$ . In other words, we use a fixed identity matrix I as the condensed graph structure. Specifically, this model variant aims to match the gradients of GNN parameters on the large-real data  $(A, X)$  and small-synthetic data  $(I, X')$ . Although GCOND-X is unable to learn the condensed graph structure which can be highly useful for downstream data analysis, it still shows competitive performance in Table [2](#page-6-0) in the experiments because the features are learned to incorporate relevant information from the graph via the matching loss.

# 4 EXPERIMENTS

In this section, we design experiments to validate the effectiveness of the proposed framework GCOND. We first introduce experimental settings, then compare GCOND against representative baselines with discussions and finally show some advantages of GCOND.

## 4.1 EXPERIMENTAL SETUP

Datasets. We evaluate the condensation performance of the proposed framework on three transduc-tive datasets, i.e., Cora, Citeseer [\(Kipf & Welling,](#page-10-2) [2017\)](#page-10-2) and Ogbn-arxiv [\(Hu et al.,](#page-10-11) [2020\)](#page-10-11), and two inductive datasets, i.e., Flickr [\(Zeng et al.,](#page-12-10)  $2020$ ) and Reddit [\(Hamilton et al.,](#page-10-3)  $2017$ ). We use the public splits for all the datasets. For the inductive setting, we follow the setup in [\(Hamilton et al.,](#page-10-3) [2017\)](#page-10-3) where the test graph is not available during training. Dataset statistics are shown in Appendix [A.](#page-13-0)

Baselines. We compare our proposed methods to five baselines: (i) one *graph coarsening* method [\(Loukas,](#page-11-3) [2019;](#page-11-3) [Huang et al.,](#page-10-1) [2021b\)](#page-10-1), (ii-iv) three coreset methods (*Random*, *Herding* [\(Welling,](#page-11-6) [2009\)](#page-11-6) and *K-Center* [\(Farahani & Hekmatfar,](#page-10-12) [2009;](#page-10-12) [Sener & Savarese,](#page-11-7) [2018\)](#page-11-7)), and (v) *dataset condensation* (DC). For the graph coarsening method, we adopt the variation neighbor-hoods method implemented by [Huang et al.](#page-10-1) [\(2021b\)](#page-10-1). For coreset methods, we first use them to select nodes from the original dataset and induce a subgraph from the selected nodes to serve as the reduced graph. In Random, the nodes are randomly selected. The Herding method, which is often used in continual learning [\(Rebuffi et al.,](#page-11-8) [2017;](#page-11-8) [Castro et al.,](#page-9-8) [2018\)](#page-9-8), picks samples that are closest to the cluster center. K-Center selects the center samples to minimize the largest distance between a sample and its nearest center. We use the implementations provided by  $Z$ hao et al.  $(2021)$  for Herding, K-Center and DC. As vanilla DC cannot leverage any structure information, we develop a variant named DC-Graph, which additionally leverages graph structure during test stage, to replace DC for the following experiments. A comparison between DC, DC-Graph, GCOND and GCOND-X is shown in Table [1](#page-6-1) and their training details can be found in Appendix [A.3.](#page-13-1)

Evaluation. We first use the aforementioned baselines to obtain condensed graphs and then evaluate them on GNNs for both transductive and inductive node classification tasks. For transductive datasets, we condense the full graph with N nodes into a synthetic graph with  $rN$  ( $0 < r < 1$ ) nodes, where  $r$  is the ratio of synthetic nodes to original nodes. For inductive datasets, we only condense the training graph since the rest of the full graph is not available during training. The choices of  $r^2$  $r^2$  are listed in Table [2.](#page-6-0) For each r, we generate 5 condensed graphs with different seeds. To evaluate the effectiveness of condensed graphs, we have two stages: (1) a training stage, where we train a GNN model on the condensed graph, and (2) a test stage, where the trained GNN uses the test graph (or full graph in transductive setting) to infer the labels for test nodes. The resulting test performance is compared with that obtained when training on original datasets. All experiments are repeated 10 times, and we report average performance and variance.

Hyperparameter settings. As our goal is to generate highly informative synthetic graphs which can benefit GNNs, we choose one representative model, GCN [\(Kipf & Welling,](#page-10-2) [2017\)](#page-10-2), for performance evaluation. For the GNN used in condensation, i.e., the  $GNN_{\theta}(\cdot)$  in Eq. [\(8\)](#page-4-1), we adopt SGC [\(Wu](#page-11-10) [et al.,](#page-11-10) [2019a\)](#page-11-10) which decouples the propagation and transformation process but still shares similar graph filtering behavior as GCN. Unless otherwise stated, we use 2-layer models with 256 hidden units. The weight decay and dropout for the models are set to 0 in condensation process. More details for hyper-parameter tuning can be found in Appendix [A.](#page-13-0)

## 4.2 COMPARISON WITH BASELINES

In this subsection, we test the performance of a 2-layer GCN on the condensed graphs, and compare the proposed GCOND and GCOND-X with baselines. Notably, all methods produce both structure and node features, i.e.  $A'$  and  $X'$ , except DC-Graph and GCOND-X. Since DC-Graph and GCOND-X do not produce any structure, we simply use an identity matrix as the adjacency matrix when training GNNs solely on condensed features. However, during inference, we use the full graph (transductive setting) or test graph (inductive setting) to propagate information based on the trained GNNs. This training paradigm is similar to the C&S model [\(Huang et al.,](#page-10-13)  $2021a$ ) which trains an MLP without the graph information and performs label propagation based on MLP predictions. Table [2](#page-6-0) reports node classification performance; we make the following observations:

Obs 1. Condensation methods achieve promising performance even with extremely large reduction rates. Condensation methods, i.e., GCOND, GCOND-X and DC-Graph, outperform coreset methods and graph coarsening significantly at the lowest ratio  $r$  for each dataset. This shows the importance of learning synthetic data using the guidance from downstream tasks. Notably, GCOND

<span id="page-5-0"></span><sup>&</sup>lt;sup>2</sup>We determine r based on original graph size and labeling rate – see [A](#page-13-0)ppendix **A** for details.

|                  | DC                        | DC-Graph                                   | GCOND-X                                    | GCOND                                          |
|------------------|---------------------------|--------------------------------------------|--------------------------------------------|------------------------------------------------|
| Condensation     | $X_{\text{train}}$        | $X_{\text{train}}$                         | $A_{\text{train}}, X_{\text{train}}$       | $A_{\text{train}}, X_{\text{train}}$           |
| Training<br>Test | $X'$<br>$X_{\text{test}}$ | $X'$<br>$A_{\text{test}}, X_{\text{test}}$ | $X'$<br>$A_{\text{test}}, X_{\text{test}}$ | $A', X'$<br>$A_{\text{test}}, X_{\text{test}}$ |

<span id="page-6-1"></span>Table 1: Information comparison used during condensation, training and test for reduction methods.  $A', X'$  and  $A, X$  are condensed (original) graph and features, respectively.

<span id="page-6-0"></span>Table 2: GCOND and GCOND-X achieves promising performance in comparison to baselines even with extremely large reduction rates. We report transductive performance on Citeseer, Cora, Ogbnarxiv; inductive performance on Flickr, Reddit. Performance is reported as test accuracy (%).

|            |                             |                                                    |                                                    | <b>Baselines</b>                                   |                                                    |                                                    |                                                    | Proposed                                           |                  |
|------------|-----------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|------------------|
| Dataset    | Ratio $(r)$                 | Random<br>$(\mathbf{A}', \mathbf{X}')$             | Herding<br>$(\mathbf{A}', \mathbf{X}')$            | K-Center<br>$(\mathbf{A}', \mathbf{X}')$           | Coarsening<br>$(\mathbf{A}', \mathbf{X}')$         | DC-Graph<br>(X')                                   | GCOND-X<br>(X')                                    | <b>GCOND</b><br>$(\mathbf{A}', \mathbf{X}')$       | Whole<br>Dataset |
| Citeseer   | $0.9\%$<br>1.8%<br>3.6%     | $54.4 \pm 4.4$<br>$64.2 \pm 1.7$<br>$69.1 \pm 0.1$ | $57.1 \pm 1.5$<br>$66.7 \pm 1.0$<br>$69.0 \pm 0.1$ | $52.4 \pm 2.8$<br>$64.3 \pm 1.0$<br>$69.1 \pm 0.1$ | $52.2 \pm 0.4$<br>$59.0 \pm 0.5$<br>$65.3 \pm 0.5$ | $66.8 \pm 1.5$<br>$66.9 \pm 0.9$<br>$66.3 \pm 1.5$ | $71.4 \pm 0.8$<br>$69.8 \pm 1.1$<br>$69.4 \pm 1.4$ | $70.5 \pm 1.2$<br>$70.6 \pm 0.9$<br>$69.8 \pm 1.4$ | $71.7 \pm 0.1$   |
| Cora       | $1.3\%$<br>2.6%<br>5.2%     | $63.6 \pm 3.7$<br>$72.8 \pm 1.1$<br>$76.8 \pm 0.1$ | $67.0 \pm 1.3$<br>$73.4 \pm 1.0$<br>$76.8 \pm 0.1$ | $64.0 \pm 2.3$<br>$73.2 \pm 1.2$<br>$76.7 \pm 0.1$ | $31.2 \pm 0.2$<br>$65.2 \pm 0.6$<br>$70.6 \pm 0.1$ | $67.3 \pm 1.9$<br>$67.6 \pm 3.5$<br>$67.7 \pm 2.2$ | $75.9 \pm 1.2$<br>$75.7 \pm 0.9$<br>$76.0 \pm 0.9$ | $79.8 \pm 1.3$<br>$80.1 \pm 0.6$<br>$79.3 \pm 0.3$ | $81.2 \pm 0.2$   |
| Ogbn-arxiv | 0.05%<br>0.25%<br>$0.5\%$   | $47.1 \pm 3.9$<br>$57.3 \pm 1.1$<br>$60.0 \pm 0.9$ | $52.4 \pm 1.8$<br>$58.6 \pm 1.2$<br>$60.4 \pm 0.8$ | $47.2 \pm 3.0$<br>$56.8 \pm 0.8$<br>$60.3 \pm 0.4$ | $35.4 \pm 0.3$<br>$43.5 \pm 0.2$<br>$50.4 \pm 0.1$ | $58.6 \pm 0.4$<br>$59.9 \pm 0.3$<br>$59.5 \pm 0.3$ | 61.3 $\pm$ 0.5<br>$64.2 \pm 0.4$<br>$63.1 \pm 0.5$ | $59.2 \pm 1.1$<br>$63.2 \pm 0.3$<br>64.0 $\pm$ 0.4 | $71.4 \pm 0.1$   |
| Flickr     | $0.1\%$<br>$0.5\%$<br>$1\%$ | $41.8 \pm 2.0$<br>$44.0 \pm 0.4$<br>$44.6 \pm 0.2$ | $42.5 \pm 1.8$<br>$43.9 \pm 0.9$<br>$44.4 \pm 0.6$ | $42.0 \pm 0.7$<br>$43.2 \pm 0.1$<br>$44.1 \pm 0.4$ | $41.9 \pm 0.2$<br>$44.5 \pm 0.1$<br>$44.6 \pm 0.1$ | $46.3 \pm 0.2$<br>$45.9 \pm 0.1$<br>$45.8 \pm 0.1$ | $45.9 \pm 0.1$<br>$45.0 \pm 0.2$<br>$45.0 \pm 0.1$ | $46.5 \pm 0.4$<br>$47.1 \pm 0.1$<br>$47.1 \pm 0.1$ | $47.2 \pm 0.1$   |
| Reddit     | 0.05%<br>0.1%<br>0.2%       | $46.1 \pm 4.4$<br>$58.0 \pm 2.2$<br>$66.3 \pm 1.9$ | $53.1 \pm 2.5$<br>$62.7 \pm 1.0$<br>$71.0 \pm 1.6$ | $46.6 \pm 2.3$<br>$53.0 \pm 3.3$<br>$58.5 \pm 2.1$ | $40.9 \pm 0.5$<br>$42.8 \pm 0.8$<br>$47.4 \pm 0.9$ | $88.2 \pm 0.2$<br>$89.5 \pm 0.1$<br>$90.5 \pm 1.2$ | $88.4 \pm 0.4$<br>$89.3 \pm 0.1$<br>$88.8 \pm 0.4$ | $88.0 \pm 1.8$<br>$89.6 \pm 0.7$<br>$90.1 \pm 0.5$ | $93.9 \pm 0.0$   |

achieves 79.8%, 80.1% and 79.3% at 1.3%, 2.6% and 5.2% condensation ratios at Cora, while the whole dataset performance is 81.2%. The GCOND variants also show promising performance on Cora, Flickr and Reddit at all coarsening ratios. Although the gap between whole-dataset Ogbn-arxiv and our methods is larger, they still outperform baselines by a large margin.

Obs 2. Learning X' instead of  $(A', X')$  as the condensed graph can also lead to good results. GCOND-X achieves close performance to GCOND on 11 of 15 cases. Since our objective in graph condensation is to achieve parameter matching through gradient matching, training a GNN on the learned features  $X'$  with identity adjacency matrix is also able to mimic the training trajectory of GNN parameters. One reason could be that  $X'$  has already encoded node features and structural information of the original graph during the condensation process. However, there are many scenarios where the graph structure is essential such as the generalization to other GNN architectures (e.g., GAT) and visualizing the patterns in the data. More details are given in the following subsections.

Obs 3. Condensing node features and structural information simultaneously can lead to better performance. In most cases, GCOND and GCOND-X obtain much better performance than DC-Graph. One key reason is that GCOND and GCOND-X can take advantage of both node features and structural information in the condensation process. We notice that DC-Graph achieves a highly comparable result (90.5%) on Reddit at 0.2% condensation ratio to the whole dataset performance (93.9%). This may indicate that the original training graph structure might not be useful. To verify this assumption, we train a GCN on the original Reddit dataset without using graph structure (i.e., setting  $A_{\text{train}} = I$ ), but allow using the test graph structure for inference using the trained model. The obtained performance is 92.5%, which is very close to the original performance 93.9%, indicating that training without graph structure can still achieve comparable performance. We also note that learning  $X^{\gamma}$ , A' simultaneously creates opportunities to absorb information from graph structure directly into learned features, lessening reliance on distilling graph properties reliably while still achieving good generalization performance from features.

Obs 4. Larger condensed graph size does not strictly indicate better performance. Although larger condensed graph sizes allow for more parameters which can potentially encapsulate more information from original graph, it simultaneously becomes harder to optimize due to the increased

|                            | Methods                                    | Data                                                                     | <b>MLP</b>           | <b>GAT</b>             | <b>APPNP</b>         | Cheby                | <b>GCN</b>           | SAGE                 | SGC                  | Avg.                 |
|----------------------------|--------------------------------------------|--------------------------------------------------------------------------|----------------------|------------------------|----------------------|----------------------|----------------------|----------------------|----------------------|----------------------|
| Citeseer<br>$r = 1.8\%$    | DC-Graph<br>GCOND-X<br><b>GCOND</b>        | $\mathbf{X}^\prime$<br>$\mathbf{X}^\prime$<br>$\mathbf{A}', \mathbf{X}'$ | 66.2<br>69.6<br>63.9 | ÷.<br>55.4             | 66.4<br>69.7<br>69.6 | 64.9<br>70.6<br>68.3 | 66.2<br>69.7<br>70.5 | 65.9<br>69.2<br>66.2 | 69.6<br>71.6<br>70.3 | 66.6<br>70.2<br>69.0 |
| Cora<br>$r = 2.6\%$        | DC-Graph<br>GCOND-X<br><b>GCOND</b>        | $\mathbf{X}'$<br>$\mathbf{X}'$<br>$\mathbf{A}', \mathbf{X}'$             | 67.2<br>76.0<br>73.1 | 66.2                   | 67.1<br>77.0<br>78.5 | 67.7<br>74.1<br>76.0 | 67.9<br>75.3<br>80.1 | 66.2<br>76.0<br>78.2 | 72.8<br>76.1<br>79.3 | 68.3<br>75.7<br>78.4 |
| Ogbn-arxiv<br>$r = 0.25\%$ | DC-Graph<br>GCOND-X<br><b>GCOND</b>        | $\mathbf{X}'$<br>$\mathbf{X}'$<br>$\mathbf{A}', \mathbf{X}'$             | 59.9<br>64.1<br>62.2 | 60.0                   | 60.0<br>61.5<br>63.4 | 55.7<br>59.5<br>54.9 | 59.8<br>64.2<br>63.2 | 60.0<br>64.4<br>62.6 | 60.4<br>64.7<br>63.7 | 59.2<br>62.9<br>61.6 |
| Flickr<br>$r = 0.5\%$      | DC-Graph<br><b>GCOND-X</b><br><b>GCOND</b> | $\mathbf{X}'$<br>$\mathbf{X}'$<br>$\mathbf{A}', \mathbf{X}'$             | 43.1<br>42.1<br>44.8 | $\blacksquare$<br>40.1 | 45.7<br>44.6<br>45.9 | 43.8<br>42.3<br>42.8 | 45.9<br>45.0<br>47.1 | 45.8<br>44.7<br>46.2 | 45.6<br>44.4<br>46.1 | 45.4<br>44.2<br>45.6 |
| Reddit<br>$r = 0.1\%$      | DC-Graph<br>GCOND-X<br>GCOND               | $\mathbf{X}^\prime$<br>$\mathbf{X}'$<br>$\mathbf{A}', \mathbf{X}'$       | 50.3<br>40.1<br>42.5 | 60.2                   | 81.2<br>78.7<br>87.8 | 77.5<br>74.0<br>75.5 | 89.5<br>89.3<br>89.4 | 89.7<br>89.3<br>89.1 | 90.5<br>91.0<br>89.6 | 85.7<br>84.5<br>86.3 |

<span id="page-7-0"></span>Table 3: Graph condensation can work well with different architectures. Avg. stands for the average test accuracy of APPNP, Cheby, GCN, GraphSAGE and SGC. SAGE stands for GraphSAGE.

model complexity. We observe that once the condensation ratio reaches a certain threshold, the performance becomes stable. However, the performance of coreset methods and graph coarsening is much more sensitive to the reduction ratio. Coreset methods only select existing samples while graph coarsening groups existing nodes into super nodes. When the reduction ratio is too low, it becomes extremely difficult to select informative nodes or form representative super nodes by grouping.

### 4.3 GENERALIZABILITY OF CONDENSED GRAPHS

Next, we illustrate the generalizability of condensed graphs from the following three perspectives.

Different Architectures. Next, we show the generalizability of the graph condensation procedure. Specifically, we show test performance when using a graph condensed by one GNN model to train different GNN architectures. Specifically, we choose APPNP [\(Klicpera et al.,](#page-10-4) [2019\)](#page-10-4), GCN, SGC [\(Wu et al.,](#page-11-10) [2019a\)](#page-11-10), GraphSAGE [\(Hamilton et al.,](#page-10-3) [2017\)](#page-10-3), Cheby [\(Defferrard et al.,](#page-9-9) [2016\)](#page-9-9) and GAT [\(Velickovic et al.,](#page-11-9) [2018\)](#page-11-9). We also include MLP and report the results in Table [3.](#page-7-0) From the table, we find that the condensed graphs generated by GCOND show good generalization on different architectures. We may attribute such transferability across different architectures to similar filtering behaviors of those GNN models, which have been studied in [Ma et al.](#page-11-14) [\(2020\)](#page-11-14); [Zhu et al.](#page-12-11) [\(2021a\)](#page-12-11).

Versatility of GCOND. The proposed GCOND is highly composable in that we can adopt various GNNs inside the condensation network. We investigate the performances of various GNNs when using different GNN models in the condensation process, i.e.,  $GNN_{\theta}(\cdot)$  in Eq. [\(8\)](#page-4-1). We choose APPNP, Cheby, GCN, GraphSAGE and SGC to serve as the models used in condensation and evaluation. Note that we omit GAT due to its deterioration under large neighborhood sizes [\(Ma et al.,](#page-11-15) [2021\)](#page-11-15). We choose Cora and Ogbn-arxiv to report the performance in Table [4](#page-8-0) where C and T denote condensation and test models, respectively. The graphs condensed by different GNNs all show strong transfer performance on other architectures.

Neural Architecture Search. We also perform experiments on neural architecture search, detailed in Appendix [C.2.](#page-15-0) We search 480 architectures of APPNP and perform the search process on Cora, Citeseer and Ogbn-arxiv. Specifically, we train each architecture on the reduced graph for epochs on as the model converges faster on the smaller graph. We observe reliable correlation of performances between condensed dataset training and whole-dataset training as shown in Table [9:](#page-15-1) 0.76/0.79/0.64 for Cora/Citeseer/Ogbn-arxiv.

## 4.4 ANALYSIS ON CONDENSED DATA

Statistics of Condensed Graphs. In Table [5,](#page-8-1) we compare several properties between condensed graphs and original graphs. Note that a widely used homophily measure is defined in [\(Zhu et al.,](#page-12-12) [2020\)](#page-12-12) but it does not apply to weighted graphs. Hence, when computing homophily, we binarize the graphs by removing edges whose weights are smaller than 0.5. We make the following observations. First, while achieving similar performance for downstream tasks, the condensed graphs contain fewer nodes and take much less storage. Second, the condensed graphs are less sparse than their

| (a) Cora, $r=2.6%$ |                                       |                                       |                                       |                                       |                                       | (b) Ogbn-arxiv, $r=0.05%$ |                                       |                                       |                                       |                                       |                                       |
|--------------------|---------------------------------------|---------------------------------------|---------------------------------------|---------------------------------------|---------------------------------------|---------------------------|---------------------------------------|---------------------------------------|---------------------------------------|---------------------------------------|---------------------------------------|
| $C \setminus T$    | APPNP                                 | Cheby                                 | GCN                                   | SAGE                                  | SGC                                   | $C \setminus T$           | APPNP                                 | Cheby                                 | GCN                                   | SAGE                                  | SGC                                   |
| APPNP              | 72.1 $	hinmuskull$ $	hickmuskull$ 2.6 | 60.8 $	hinmuskull$ $	hickmuskull$ 6.4 | 73.5 $	hinmuskull$ $	hickmuskull$ 2.4 | 72.3 $	hinmuskull$ $	hickmuskull$ 3.5 | 73.1 $	hinmuskull$ $	hickmuskull$ 3.1 | APPNP                     | 60.3 $	hinmuskull$ $	hickmuskull$ 0.2 | 51.8 $	hinmuskull$ $	hickmuskull$ 0.5 | 59.9 $	hinmuskull$ $	hickmuskull$ 0.4 | 59.0 $	hinmuskull$ $	hickmuskull$ 1.1 | 61.2 $	hinmuskull$ $	hickmuskull$ 0.4 |
| Cheby              | 75.3 $	hinmuskull$ $	hickmuskull$ 2.9 | 71.8 $	hinmuskull$ $	hickmuskull$ 1.1 | 76.8 $	hinmuskull$ $	hickmuskull$ 2.1 | 76.4 $	hinmuskull$ $	hickmuskull$ 2.0 | 75.5 $	hinmuskull$ $	hickmuskull$ 3.5 | Cheby                     | 57.4 $	hinmuskull$ $	hickmuskull$ 0.4 | 53.5 $	hinmuskull$ $	hickmuskull$ 0.5 | 57.4 $	hinmuskull$ $	hickmuskull$ 0.8 | 57.1 $	hinmuskull$ $	hickmuskull$ 0.8 | 58.2 $	hinmuskull$ $	hickmuskull$ 0.6 |
| GCN                | 69.8 $	hinmuskull$ $	hickmuskull$ 4.0 | 53.2 $	hinmuskull$ $	hickmuskull$ 3.4 | 70.6 $	hinmuskull$ $	hickmuskull$ 3.7 | 60.2 $	hinmuskull$ $	hickmuskull$ 1.9 | 68.7 $	hinmuskull$ $	hickmuskull$ 5.4 | GCN                       | 59.3 $	hinmuskull$ $	hickmuskull$ 0.4 | 51.8 $	hinmuskull$ $	hickmuskull$ 0.7 | 60.3 $	hinmuskull$ $	hickmuskull$ 0.3 | 60.2 $	hinmuskull$ $	hickmuskull$ 0.4 | 59.2 $	hinmuskull$ $	hickmuskull$ 0.7 |
| SAGE               | 77.1 $	hinmuskull$ $	hickmuskull$ 1.1 | 69.3 $	hinmuskull$ $	hickmuskull$ 1.7 | 77.0 $	hinmuskull$ $	hickmuskull$ 0.7 | 76.1 $	hinmuskull$ $	hickmuskull$ 0.7 | 77.7 $	hinmuskull$ $	hickmuskull$ 1.8 | SAGE                      | 57.6 $	hinmuskull$ $	hickmuskull$ 0.8 | 53.9 $	hinmuskull$ $	hickmuskull$ 0.6 | 58.1 $	hinmuskull$ $	hickmuskull$ 0.6 | 57.8 $	hinmuskull$ $	hickmuskull$ 0.7 | 59.0 $	hinmuskull$ $	hickmuskull$ 1.1 |
| SGC                | 78.5 $	hinmuskull$ $	hickmuskull$ 1.0 | 76.0 $	hinmuskull$ $	hickmuskull$ 1.1 | 80.1 $	hinmuskull$ $	hickmuskull$ 0.6 | 78.2 $	hinmuskull$ $	hickmuskull$ 0.9 | 79.3 $	hinmuskull$ $	hickmuskull$ 0.7 | SGC                       | 59.7 $	hinmuskull$ $	hickmuskull$ 0.5 | 49.5 $	hinmuskull$ $	hickmuskull$ 0.8 | 59.2 $	hinmuskull$ $	hickmuskull$ 1.1 | 58.9 $	hinmuskull$ $	hickmuskull$ 1.6 | 60.5 $	hinmuskull$ $	hickmuskull$ 0.6 |

 $\theta$  Ogbits  $\theta$  Of  $\alpha$ 

<span id="page-8-0"></span>Table 4: Cross-architecture performance is shown in test accuracy (%). SAGE: GraphSAGE. Graphs condensed by different GNNs all show strong transfer performance on other architectures.

<span id="page-8-1"></span>Table 5: Comparison between condensed graphs and original graphs. The condensed graphs have fewer nodes and are more dense.

|           | Citeseer, r=0.9% |        | Cora, r=1.3% |        | Ogbn-arxiv, r=0.25% |        | Flickr, r=0.5% |        | Reddit, r=0.1% |        |
|-----------|------------------|--------|--------------|--------|---------------------|--------|----------------|--------|----------------|--------|
|           | Whole            | GCOND  | Whole        | GCOND  | Whole               | GCOND  | Whole          | GCOND  | Whole          | GCOND  |
| Accuracy  | 70.7             | 70.5   | 81.5         | 79.8   | 71.4                | 63.2   | 47.1           | 47.1   | 94.1           | 89.4   |
| #Nodes    | 3,327            | 60     | 2,708        | 70     | 169,343             | 454    | 44,625         | 223    | 153,932        | 153    |
| #Edges    | 4,732            | 1,454  | 5,429        | 2,128  | 1,166,243           | 3,354  | 218,140        | 3,788  | 10,753,238     | 301    |
| Sparsity  | 0.09%            | 80.78% | 0.15%        | 86.86% | 0.01%               | 3.25%  | 0.02%          | 15.23% | 0.09%          | 2.57%  |
| Homophily | 0.74             | 0.65   | 0.81         | 0.79   | 0.65                | 0.07   | 0.33           | 0.28   | 0.78           | 0.04   |
| Storage   | 47.1 MB          | 0.9 MB | 14.9 MB      | 0.4 MB | 100.4 MB            | 0.3 MB | 86.8 MB 0.5 MB |        | 435.5 MB       | 0.4 MB |

Image /page/8/Figure/5 description: The image displays five network graphs, labeled (a) through (e). Graph (a) is labeled "Cora, r=2.5%", graph (b) is labeled "Citeseer, r=1.8%", graph (c) is labeled "Arxiv, r=0.05%", graph (d) is labeled "Flickr, r=0.1%", and graph (e) is labeled "Reddit, r=0.1%". Each graph consists of nodes represented by colored circles and edges represented by black lines connecting the nodes. The nodes in each graph are colored with a variety of colors, including red, orange, yellow, green, blue, and purple. The graphs vary in structure, with some appearing more clustered and others more spread out or star-like.

Figure 2: Condensed graphs sometimes exhibit structure mimicking the original (a, b, d). Other times (c, e), learned features absorb graph properties and create less explicit graph reliance.

larger counterparts. Since the condensed graph is on extremely small scale, there would be almost no connections between nodes if the condensed graph maintains the original sparsity. Third, for Citeseer, Cora and Flickr, the homophily information are well preserved in the condensed graphs.

Visualization. We present the visualization results for all datasets in Figure [4,](#page-18-1) where nodes with the same color are from the same class. Notably, as the learned condensed graphs are weighted graphs, we use black lines to denote the edges with weights larger than 0.5 and gray lines to denote the edges with weights smaller than 0.5. From Figure [4,](#page-18-1) we can observe some patterns in the condensed graphs, e.g., the homophily patterns on Cora and Citeseer are well preserved. Interestingly, the learned graph for Reddit is very close to a star graph where almost all the nodes only have connections with very few center nodes. Such a structure can be meaningless for GNNs because almost all the nodes receive the information from their neighbors. In this case, the learned features  $X'$  play a major role in training GNN parameters, indicating that the original training graph of Reddit is not very informative, aligning with our observations in Section 4.2.

# 5 CONCLUSION

The prevalence of large-scale graphs poses great challenges in training graph neural networks. Thus, we study a novel problem of *graph condensation* which targets at condensing a large-real graph into a small-synthetic one while maintaining the performances of GNNs. Through our proposed framework, we are able to significantly reduce the graph size while approximating the original performance. The condensed graphs take much less space of storage and can be used to efficiently train various GNN architectures. Future work can be done on (1) improving the transferability of condensed graphs for different GNNs, (2) studying graph condensation for other tasks such as graph classification and (3) designing condensation framework for multi-label datasets.

# ACKNOLWEDGEMENT

Wei Jin and Jiliang Tang are supported by the National Science Foundation (NSF) under grant numbers IIS1714741, CNS1815636, IIS1845081, IIS1907704, IIS1928278, IIS1955285, IOS2107215, and IOS2035472, the Army Research Office (ARO) under grant number W911NF-21-1-0198, the Home Depot, Cisco Systems Inc. and SNAP Inc.

# ETHICS STATEMENT

To the best of our knowledge, there are no ethical issues with this paper.

# REPRODUCIBILITY STATEMENT

To ensure reproducibility of our experiments, we provide our source code at  $https://github.$ [com/ChandlerBang/GCond](https://github.com/ChandlerBang/GCond). The hyper-parameters are described in details in the appendix. We also provide a pseudo-code implementation of our framework in the appendix.

## REFERENCES

- <span id="page-9-0"></span>Peter W Battaglia, Jessica B Hamrick, Victor Bapst, Alvaro Sanchez-Gonzalez, Vinicius Zambaldi, Mateusz Malinowski, Andrea Tacchetti, David Raposo, Adam Santoro, Ryan Faulkner, et al. Relational inductive biases, deep learning, and graph networks. *ArXiv preprint*, 2018.
- <span id="page-9-1"></span>Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *ArXiv preprint*, 2020.
- <span id="page-9-3"></span>Chen Cai, Dingkang Wang, and Yusu Wang. Graph coarsening with neural networks. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021.
- <span id="page-9-8"></span>Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *Proceedings of the European conference on computer vision (ECCV)*, 2018.
- <span id="page-9-6"></span>Yu Chen, Lingfei Wu, and Mohammed J. Zaki. Iterative deep graph learning for graph neural networks: Better and robust node embeddings. In *Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual*, 2020.
- <span id="page-9-9"></span>Michael Defferrard, Xavier Bresson, and Pierre Vandergheynst. Convolutional neural networks ¨ on graphs with fast localized spectral filtering. In *Advances in Neural Information Processing Systems 29: Annual Conference on Neural Information Processing Systems 2016, December 5- 10, 2016, Barcelona, Spain*, 2016.
- <span id="page-9-2"></span>Chenhui Deng, Zhiqiang Zhao, Yongyu Wang, Zhiru Zhang, and Zhuo Feng. Graphzoom: A multilevel spectral approach for accurate and scalable graph embedding. In *8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia, April 26-30, 2020*, 2020.
- <span id="page-9-7"></span>Kaize Ding, Zhe Xu, Hanghang Tong, and Huan Liu. Data augmentation for deep graph learning: A survey. *arXiv preprint arXiv:2202.08235*, 2022.
- <span id="page-9-5"></span>Xiaowen Dong, Dorina Thanou, Pascal Frossard, and Pierre Vandergheynst. Learning laplacian matrix in smooth graph signal representations. *IEEE Transactions on Signal Processing*, (23), 2016.
- <span id="page-9-4"></span>David Duvenaud, Dougal Maclaurin, Jorge Aguilera-Iparraguirre, Rafael Gómez-Bombarelli, Timothy Hirzel, Alan Aspuru-Guzik, and Ryan P. Adams. Convolutional networks on graphs for learn- ´ ing molecular fingerprints. In *Advances in Neural Information Processing Systems 28: Annual Conference on Neural Information Processing Systems 2015, December 7-12, 2015, Montreal, Quebec, Canada*, 2015.

- <span id="page-10-7"></span>Hilmi E Egilmez, Eduardo Pavez, and Antonio Ortega. Graph learning from data under laplacian and structural constraints. *IEEE Journal of Selected Topics in Signal Processing*, (6), 2017.
- <span id="page-10-5"></span>Wenqi Fan, Yao Ma, Qing Li, Yuan He, Yihong Eric Zhao, Jiliang Tang, and Dawei Yin. Graph neural networks for social recommendation. In *The World Wide Web Conference, WWW 2019, San Francisco, CA, USA, May 13-17, 2019*, 2019.
- <span id="page-10-12"></span>Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. 2009.
- <span id="page-10-14"></span>Matthias Fey and Jan Eric Lenssen. Fast graph representation learning with pytorch geometric. *ArXiv preprint*, 2019.
- <span id="page-10-8"></span>Luca Franceschi, Mathias Niepert, Massimiliano Pontil, and Xiao He. Learning discrete structures for graph neural networks. In *Proceedings of the 36th International Conference on Machine Learning, ICML 2019, 9-15 June 2019, Long Beach, California, USA*, Proceedings of Machine Learning Research, 2019.
- <span id="page-10-15"></span>Hongyang Gao and Shuiwang Ji. Graph u-nets. In *Proceedings of the 36th International Conference on Machine Learning, ICML 2019, 9-15 June 2019, Long Beach, California, USA*, Proceedings of Machine Learning Research, 2019.
- <span id="page-10-3"></span>William L. Hamilton, Zhitao Ying, and Jure Leskovec. Inductive representation learning on large graphs. In *Advances in Neural Information Processing Systems 30: Annual Conference on Neural Information Processing Systems 2017, December 4-9, 2017, Long Beach, CA, USA*, 2017.
- <span id="page-10-11"></span>Weihua Hu, Matthias Fey, Marinka Zitnik, Yuxiao Dong, Hongyu Ren, Bowen Liu, Michele Catasta, and Jure Leskovec. Open graph benchmark: Datasets for machine learning on graphs. In *Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual*, 2020.
- <span id="page-10-13"></span>Qian Huang, Horace He, Abhay Singh, Ser-Nam Lim, and Austin R. Benson. Combining label propagation and simple models out-performs graph neural networks. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021a.
- <span id="page-10-1"></span>Zengfeng Huang, Shengzhong Zhang, Chong Xi, Tang Liu, and Min Zhou. Scaling up graph neural networks via graph coarsening. In *In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '21)*, 2021b.
- <span id="page-10-10"></span>Joseph J. Pfeiffer III, Sebastian Moreno, Timothy La Fond, Jennifer Neville, and Brian Gallagher. ´ Attributed graph models: modeling network structure with correlated attributes. In *23rd International World Wide Web Conference, WWW '14, Seoul, Republic of Korea, April 7-11, 2014*, 2014.
- <span id="page-10-9"></span>Wei Jin, Yao Ma, Xiaorui Liu, Xianfeng Tang, Suhang Wang, and Jiliang Tang. Graph structure learning for robust graph neural networks. In *KDD '20: The 26th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, Virtual Event, CA, USA, August 23-27, 2020*, 2020.
- <span id="page-10-0"></span>David R Karger. Random sampling in cut, flow, and network design problems. *Mathematics of Operations Research*, (2), 1999.
- <span id="page-10-2"></span>Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks. In *5th International Conference on Learning Representations, ICLR 2017, Toulon, France, April 24-26, 2017, Conference Track Proceedings*, 2017.
- <span id="page-10-4"></span>Johannes Klicpera, Aleksandar Bojchevski, and Stephan Gunnemann. Predict then propagate: ¨ Graph neural networks meet personalized pagerank. In *7th International Conference on Learning Representations, ICLR 2019, New Orleans, LA, USA, May 6-9, 2019*, 2019.
- <span id="page-10-6"></span>Guohao Li, Matthias Müller, Ali K. Thabet, and Bernard Ghanem. Deepgens: Can gens go as deep as cnns? In *2019 IEEE/CVF International Conference on Computer Vision, ICCV 2019, Seoul, Korea (South), October 27 - November 2, 2019*, 2019.

- <span id="page-11-11"></span>Meng Liu, Hongyang Gao, and Shuiwang Ji. Towards deeper graph neural networks. In *Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*. ACM, 2020.
- <span id="page-11-12"></span>Xiaorui Liu, Wei Jin, Yao Ma, Yaxin Li, Hua Liu, Yiqi Wang, Ming Yan, and Jiliang Tang. Elastic graph neural networks. In *Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18-24 July 2021, Virtual Event*, Proceedings of Machine Learning Research, 2021.
- <span id="page-11-3"></span>Andreas Loukas. Graph reduction with spectral and cut guarantees. *J. Mach. Learn. Res.*, (116), 2019.
- <span id="page-11-2"></span>Andreas Loukas and Pierre Vandergheynst. Spectrally approximating large graphs with smaller graphs. In *Proceedings of the 35th International Conference on Machine Learning, ICML 2018, Stockholmsmässan, Stockholm, Sweden, July 10-15, 2018, Proceedings of Machine Learning Re*search, 2018.
- <span id="page-11-15"></span>Xiaojun Ma, Ziyao Li, Lingjun Xu, Guojie Song, Yi Li, and Chuan Shi. Learning discrete adaptive receptive fields for graph convolutional networks, 2021.
- <span id="page-11-14"></span>Yao Ma, Xiaorui Liu, Tong Zhao, Yozen Liu, Jiliang Tang, and Neil Shah. A unified view on graph neural networks as graph signal denoising. *ArXiv preprint*, 2020.
- <span id="page-11-5"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021.
- <span id="page-11-0"></span>David Peleg and Alejandro A Schäffer. Graph spanners. *Journal of graph theory*, (1), 1989.
- <span id="page-11-8"></span>Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H. Lampert. icarl: Incremental classifier and representation learning. In *2017 IEEE Conference on Computer Vision and Pattern Recognition, CVPR 2017, Honolulu, HI, USA, July 21-26, 2017*, 2017.
- <span id="page-11-7"></span>Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 - May 3, 2018, Conference Track Proceedings*, 2018.
- <span id="page-11-13"></span>Cosma Rohilla Shalizi and Andrew C Thomas. Homophily and contagion are generically confounded in observational social network studies. *Sociological methods & research*, (2), 2011.
- <span id="page-11-1"></span>Daniel A Spielman and Shang-Hua Teng. Spectral sparsification of graphs. *SIAM Journal on Computing*, (4), 2011.
- <span id="page-11-16"></span>Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, (11), 2008.
- <span id="page-11-9"></span>Petar Velickovic, Guillem Cucurull, Arantxa Casanova, Adriana Romero, Pietro Lio, and Yoshua ` Bengio. Graph attention networks. In *6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 - May 3, 2018, Conference Track Proceedings*, 2018.
- <span id="page-11-4"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *ArXiv preprint*, 2018.
- <span id="page-11-6"></span>Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning, ICML 2009, Montreal, Quebec, Canada, June 14-18, 2009*, ACM International Conference Proceeding Series, 2009.
- <span id="page-11-10"></span>Felix Wu, Amauri H. Souza Jr., Tianyi Zhang, Christopher Fifty, Tao Yu, and Kilian Q. Weinberger. Simplifying graph convolutional networks. In *Proceedings of the 36th International Conference on Machine Learning, ICML 2019, 9-15 June 2019, Long Beach, California, USA*, Proceedings of Machine Learning Research, 2019a.

- <span id="page-12-0"></span>Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, and Philip S Yu. A comprehensive survey on graph neural networks. *ArXiv preprint*, 2019b.
- <span id="page-12-7"></span>Rex Ying, Ruining He, Kaifeng Chen, Pong Eksombatchai, William L. Hamilton, and Jure Leskovec. Graph convolutional neural networks for web-scale recommender systems. In *Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, KDD 2018, London, UK, August 19-23, 2018*, 2018a.
- <span id="page-12-14"></span>Zhitao Ying, Jiaxuan You, Christopher Morris, Xiang Ren, William L. Hamilton, and Jure Leskovec. Hierarchical graph representation learning with differentiable pooling. In *Advances in Neural Information Processing Systems 31: Annual Conference on Neural Information Processing Systems 2018, NeurIPS 2018, December 3-8, 2018, Montreal, Canada ´* , 2018b.
- <span id="page-12-4"></span>Yuning You, Tianlong Chen, Yang Shen, and Zhangyang Wang. Graph contrastive learning automated. In *Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18-24 July 2021, Virtual Event*, Proceedings of Machine Learning Research, 2021.
- <span id="page-12-10"></span>Hanqing Zeng, Hongkuan Zhou, Ajitesh Srivastava, Rajgopal Kannan, and Viktor K. Prasanna. Graphsaint: Graph sampling based inductive learning method. In *8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia, April 26-30, 2020*, 2020.
- <span id="page-12-13"></span>Muhan Zhang, Zhicheng Cui, Marion Neumann, and Yixin Chen. An end-to-end deep learning architecture for graph classification. In *Proceedings of the Thirty-Second AAAI Conference on Artificial Intelligence, (AAAI-18), the 30th innovative Applications of Artificial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational Advances in Artificial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7, 2018*, 2018.
- <span id="page-12-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18-24 July 2021, Virtual Event*, Proceedings of Machine Learning Research, 2021.
- <span id="page-12-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021.
- <span id="page-12-6"></span>Lingxiao Zhao, Wei Jin, Leman Akoglu, and Neil Shah. From stars to subgraphs: Uplifting any GNN with local structure awareness. In *International Conference on Learning Representations*, 2022. URL [https://openreview.net/forum?id=Mspk\\_WYKoEH](https://openreview.net/forum?id=Mspk_WYKoEH).
- <span id="page-12-1"></span>Jie Zhou, Ganqu Cui, Zhengyan Zhang, Cheng Yang, Zhiyuan Liu, Lifeng Wang, Changcheng Li, and Maosong Sun. Graph neural networks: A review of methods and applications. *ArXiv preprint*, 2018.
- <span id="page-12-5"></span>Kaixiong Zhou, Ninghao Liu, Fan Yang, Zirui Liu, Rui Chen, Li Li, Soo-Hyun Choi, and Xia Hu. Adaptive label smoothing to regularize large-scale graph training. *arXiv preprint arXiv:2108.13555*, 2021.
- <span id="page-12-12"></span>Jiong Zhu, Yujun Yan, Lingxiao Zhao, Mark Heimann, Leman Akoglu, and Danai Koutra. Beyond homophily in graph neural networks: Current limitations and effective designs. In *Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual*, 2020.
- <span id="page-12-9"></span>Ligeng Zhu, Zhijian Liu, and Song Han. Deep leakage from gradients. In *Advances in Neural Information Processing Systems 32: Annual Conference on Neural Information Processing Systems 2019, NeurIPS 2019, December 8-14, 2019, Vancouver, BC, Canada*, 2019.
- <span id="page-12-11"></span>Meiqi Zhu, Xiao Wang, Chuan Shi, Houye Ji, and Peng Cui. Interpreting and unifying graph neural networks with an optimization framework. In *Proceedings of the Web Conference 2021*, 2021a.
- <span id="page-12-8"></span>Yanqiao Zhu, Weizhi Xu, Jinghao Zhang, Qiang Liu, Shu Wu, and Liang Wang. Deep graph structure learning for robust representations: A survey. *arXiv preprint arXiv:2103.03036*, 2021b.

<span id="page-13-0"></span>

## A DATASETS AND HYPER-PARAMETERS

### A.1 DATASETS

We evaluate the proposed framework on three transductive datasets, i.e., Cora, Citeseer (Kipf  $\&$ [Welling,](#page-10-2) [2017\)](#page-10-2) and Ogbn-arxiv [\(Hu et al.,](#page-10-11) [2020\)](#page-10-11), and two inductive datasets, i.e., Flickr [\(Zeng et al.,](#page-12-10) [2020\)](#page-12-10) and Reddit [\(Hamilton et al.,](#page-10-3) [2017\)](#page-10-3). Since all the datasets have public splits, we download them from PyTorch Geometric (Fey  $\&$  Lenssen, [2019\)](#page-10-14) and use those splits throughout the experiments. Dataset statistics are shown in Table [6.](#page-13-2)

<span id="page-13-2"></span>Table 6: Dataset statistics. The first three are transductive datasets and the last two are inductive datasets.

| <b>Dataset</b> | #Nodes  | #Edges     | #Classes | <b>#Features</b> | <b>Training/Validation/Test</b> |
|----------------|---------|------------|----------|------------------|---------------------------------|
| Cora           | 2,708   | 5,429      | 7        | 1,433            | 140/500/1000                    |
| Citeseer       | 3,327   | 4,732      | 6        | 3,703            | 120/500/1000                    |
| Ogbn-arxiv     | 169,343 | 1,166,243  | 40       | 128              | 90,941/29,799/48,603            |
| Flickr         | 89,250  | 899,756    | 7        | 500              | 44,625/22312/22313              |
| Reddit         | 232,965 | 57,307,946 | 210      | 602              | 15,3932/23,699/55,334           |

### A.2 HYPER-PARAMETER SETTING

**Condensation Process.** For DC, we tune the number of hidden layers in a range of  $\{1, 2, 3\}$  and fix the number of hidden units to 256. We further tune the number of epochs for training DC in a range of  $\{100, 200, 400\}$ . For GCOND, without specific mention, we adopt a 2-layer SGC [\(Wu](#page-11-10) [et al.,](#page-11-10) [2019a\)](#page-11-10) with 256 hidden units as the GNN used for gradient matching. The function  $g_{\Phi}$  that models the relationship between  $A'$  and  $X'$  is implemented as a multi-layer perceptron (MLP). Specifically, we adopt a 3-layer MLP with 128 hidden units for small graphs (Cora and Citeseer) and 256 hidden units for large graphs (Flickr, Reddit and Ogbn-arxiv). We tune the training epoch for GCOND in a range of {400, 600, 1000}. For GCOND-X, we tune the number of hidden layers in a range of  $\{1, 2, 3\}$  and fix the number of hidden units to 256. We further tune the number of epochs for training GCOND-X in a range of  $\{100, 200, 400\}$ . We tune the learning rate for all the methods in a range of  $\{0.1, 0.01, 0.001, 0.0001\}$ . Furthermore, we set  $\delta$  to be  $0.05, 0.05, 0.01, 0.01, 0.01$  for Citeseer, Cora, Ogbn-arxiv, Flickr and Reddit, respectively.

For the choices of condensation ratio  $r$ , we divide the discussion into two parts. The first part is about transductive datasets. For Cora and Citeseer, since their labeling rates are very small (5.2% and 3.6%, respectively), we choose r to be  $\{25\%, 50\%, 100\%\}$  of the labeling rate. Thus, we finally choose  $\{1.3\%, 2.6\%, 5.2\%\}$  for Cora and  $\{0.9\%, 1.8\%, 3.6\%\}$  for Citeseer. For Ogbn-arxiv, we choose r to be  $\{0.1\%, 0.5\%, 1\%\}$  of its labeling rate (53%), thus being  $\{0.05\%, 0.25\%, 0.5\%\}$ . The second part is about inductive datasets. As the nodes in the training graphs are all labeled in inductive datasets, we simply choose  $\{0.1\%, 0.5\%, 0.1\%\}$  for Flickr and  $0.05\%, 0.1\%, 0.2\%$  for Reddit.

Evaluation Process. During the evaluation process, we set dropout rate to be 0 and weight decay to be 0.0005 when training various GNNs. The number of epochs is set to 3000 for GAT while it is set to 600 for other models. The initial learning rate is set to 0.01.

Settings for Table 3 and Table 4. In both condensation stage and evaluation stage, we set the depth of GNNs to 2. During condensation stage, we set weight decay to 0, dropout to 0 and training epochs to 1000. During evaluation stage, we set weight decay to 0.0005, dropout to 0 and training epochs to 600.

<span id="page-13-1"></span>A.3 TRAINING DETAILS OF DC-GRAPH, GCOND-X AND GCOND

DC-Graph: During the condensation stage, DC-Graph only leverages the node features to produce condensed node features  $X'$ . During the training stage of evaluation, DC-Graph takes the condensed features  $X'$  together with an identity matrix as the graph structure to train a GNN. In the later test stage of evaluation, the GNN takes both test node features and test graph structure as input to make predictions for test nodes.

GCOND-X: During the condensation stage, GCOND-X leverages both the graph structure and node features to produce condensed node features  $X'$ . During the training stage of evaluation, GCOND-X takes the condensed features  $X'$  together with an identity matrix as the graph structure to train a GNN. In the later test stage of evaluation, the GNN takes both test node features and test graph structure as input to make predictions for test nodes.

GCOND: During the condensation stage, GCOND leverages both the graph structure and node features to produce condensed graph data  $(A', X')$ . During the training stage of evaluation, GCOND takes the condensed data  $(A', X')$  to train a GNN. In the later test stage of evaluation, the GNN takes both test node features and test graph structure as input to make predictions for test nodes.

<span id="page-14-1"></span>

## B ALGORITHM

We show the detailed algorithm of GCOND in Algorithm 1. In detail, we first set the condensed label set  $Y'$  to fixed values and initialize  $X'$  as node features randomly selected from each class. In each outer loop, we sample a GNN model initialization  $\theta$  from a distribution  $P_{\theta}$ . Then, for each class we sample the corresponding node batches from  $\mathcal T$  and  $\mathcal S$ , and calculate the gradient matching loss within each class. The sum of losses from different classes are used to update  $X'$  or  $\Phi$ . After that we update the GNN parameters for  $\tau_{\theta}$  epochs. When finishing the updating of condensed graph parameters, we use  $\mathbf{A}' = \text{ReLU}(g_{\Phi}(\mathbf{X}') - \delta)$  to obtain the final sparsified graph structure.

### Algorithm 1: GCOND for Graph Condensation

1 Input: Training data  $\mathcal{T} = (\mathbf{A}, \mathbf{X}, \mathbf{Y})$ , pre-defined condensed labels  $\mathbf{Y}'$ 2 Initialize  $X'$  as node features randomly selected from each class 3 for  $k = 0, ..., K - 1$  do 4 | Initialize  $θ_0 \sim P_{θ_0}$ 5  $\int$  for  $t = 0, ..., T - 1$  do 6 |  $D' = 0$ 7 | for  $c = 0, ..., C - 1$  do 8 Compute  $\mathbf{A}' = g_{\Phi}(\mathbf{X}')$ ; then  $\mathcal{S} = {\mathbf{A}', \mathbf{X}', \mathbf{Y}' }$ 9 │ │ Sample  $(\mathbf{A}_c,\mathbf{X}_c,\mathbf{Y}_c) \sim \mathcal{T}$  and  $(\mathbf{A}'_c,\mathbf{X}'_c,\mathbf{Y}'_c)$ ⊳ detailed in Section 3.1 10 Compute  $\mathcal{L}^{\mathcal{T}} = \mathcal{L}(\text{GNN}_{\theta_t}(\mathbf{A}_c, \mathbf{X}_c), \mathbf{Y}_c)$  and  $\mathcal{L}^{\mathcal{S}} = \mathcal{L}(\text{GNN}_{\theta_t}(\mathbf{A}_c', \mathbf{X}_c'), \mathbf{Y}_c')$ 11  $\parallel D' \leftarrow D' + D(\nabla_{\theta_t} \mathcal{L}^{\mathcal{T}}, \nabla_{\theta_t} \mathcal{L}^{\mathcal{S}})$ 12 **if**  $t\%(\tau_1 + \tau_2) < \tau_1$  then 13 | | Update  $\mathbf{X}' \leftarrow \mathbf{X}' - \eta_1 \nabla_{\mathbf{X}'} D'$ 14 else 15 | | Update  $\Phi \leftarrow \Phi - \eta_2 \nabla_{\Phi} D'$ 16 | Update  $\theta_{t+1} \leftarrow \text{opt}_{\theta}(\theta_t, \mathcal{S}, \tau_{\theta})$  $\triangleright \tau_{\theta}$  is the number of steps for updating  $\theta$ 17  ${\bf A}^{\prime}=g_{\Phi}({\bf X}^{\prime})$ 18  $\mathbf{A}'_{ij} = \mathbf{A}'_{ij}$  if  $\mathbf{A}'_{ij} > \delta$ , otherwise 0 19 Return:  $(\mathbf{A}', \mathbf{X}', \mathbf{Y}')$ 

<span id="page-14-0"></span>

## C MORE EXPERIMENTS

### C.1 ABLATION STUDY

**Different Parameterization.** We study the effect of different parameterizations for modeling  $A'$ and compare GCOND with modeling  $A'$  as free parameters in Table [7.](#page-15-2) From the table, we observe a significant improvement by taking into account the relationship between  $A'$  and  $X'$ . This suggests that directly modeling the structure as a function of features can ease the optimization and lead to better condensed graph data.

Joint optimization versus alternate optimization. We perform the ablation study on joint optimization and alternate optimization when updating  $\Phi$  and  $X'$ . The results are shown in Table [8.](#page-15-3) From the table, we can observe that joint optimization always gives worse performance and the standard deviation is much higher than alternate optimization.

<span id="page-15-2"></span>

| Parameters                                        | Citeseer, $r=1.8%$                         | Cora, $r=2.6%$                             | Ogbn-arxiv, $r=0.25%$                      |
|---------------------------------------------------|--------------------------------------------|--------------------------------------------|--------------------------------------------|
| $oldsymbol{A}', oldsymbol{X}'$                  | $62.2 oldsymbol{oldsymbol{	ext{±}}} 4.8$ | $75.5 oldsymbol{oldsymbol{	ext{±}}} 0.6$ | $63.0 oldsymbol{oldsymbol{	ext{±}}} 0.5$ |
| $oldsymbol{oldsymbol{	ext{Φ}}}, oldsymbol{X}'$ | $70.6 oldsymbol{oldsymbol{	ext{±}}} 0.9$ | $80.1 oldsymbol{oldsymbol{	ext{±}}} 0.6$ | $63.2 oldsymbol{oldsymbol{	ext{±}}} 0.3$ |

Table 7: Ablation study on different parametrizations.

Table 8: Ablation study on different optimization strategies.

<span id="page-15-3"></span>

|           | Citeseer. $r=1.8\%$ |              | Cora, $r = 2.6\%$ Ogbn-arxiv, $r = 0.25\%$ | Flickr. $r = 0.5\%$ Reddit. $r = 0.1\%$ |              |
|-----------|---------------------|--------------|--------------------------------------------|-----------------------------------------|--------------|
| Joint     | $68.2 + 3.0$        | $79.9 + 1.6$ | $62.8 + 1.1$                               | $45.4 + 0.4$                            | $87.5 + 1.8$ |
| Alternate | $70.6 + 0.9$        | $80.1 + 0.6$ | $63.2 + 0.3$                               | $47.1 + 0.1$                            | $89.5 + 0.8$ |

<span id="page-15-0"></span>

### C.2 NEURAL ARCHITECTURE SEARCH

We focus on APPNP instead of GCN since the architecture of APPNP involves more hyperparameters regarding its architecture setup. The detailed search space is shown as follows:

- (a) **Number of propagation**  $K$ : we search the number of propagation  $K$  in the range of  $\{2, 4, 6, 8, 10\}.$
- (b) Residual coefficient  $\alpha$ : for the residual coefficient in APPNP, we search it in the range of  $\{0.1, 0.2\}.$
- (c) Hidden dimension: We collect the set of dimensions that are widely adopted by existing work as the candidates, i.e., {16,32,64,128,256,512}.
- (d) Activation function: The set of available activation functions is listed as follows: {Sigmoid, Tanh, ReLU, Linear, Softplus, LeakyReLU, ReLU6, ELU}

In total, for each dataset we search 480 architectures of APPNP and we perform the search process on Cora, Citeseer and Ogbn-arxiv. Specifically, we train each architecture on the reduced graph for epochs on as the model converges faster on the smaller graph. We use the best validation accuracy to choose the final architecture. We report (1) the Pearson correlation between validation accuracies obtained by architectures trained on condensed graphs and those trained on original graphs, and (2) the average test accuracy of the searched architecture over 20 runs.

<span id="page-15-1"></span>Table 9: Neural Architecture Search. Methods are compared in validation accuracy correlation and test accuracy obtained by searched architecture. Whole means the architecture is searched using whole dataset.

|            | Pearson Correlation |         |             | Test Accuracy |         |       |       |
|------------|---------------------|---------|-------------|---------------|---------|-------|-------|
|            | Random              | Herding | GCOND       | Random        | Herding | GCOND | Whole |
| Cora       | 0.40                | 0.21    | <b>0.76</b> | 82.9          | 82.9    | 83.1  | 82.6  |
| Citeseer   | 0.56                | 0.29    | <b>0.79</b> | 71.4          | 71.3    | 71.3  | 71.6  |
| Ogbn-arxiv | 0.63                | 0.60    | <b>0.64</b> | 71.1          | 71.2    | 71.2  | 71.9  |

### C.3 TIME COMPLEXITY AND RUNNING TIME

Time Complexity. We start from analyzing the time complexity of calculating gradient matching loss, i.e., line 8 to line 11 in Algorithm 1. Let the number of MLP layers in  $g_{\Phi}$  be L and r be the number of sampled neighbors per node. For simplicity, we assume all the hidden units are  $d$  for all layers and we use L-layer GCN for the analysis. The forward process of  $g_{\Phi}$  has a complexity of  $O(N'^2d^2)$ . The forward process of GCN on the original graph has a complexity of  $O(r^LNd^2)$  and that on condensed graph has a complexity of  $O(LN^2d+LN^{\prime}d)$ . The complexity of calculating the second-order derivatives in backward propagation has an additional factor of  $O(|\theta_t| |\mathbf{A}'| + |\theta_t| |\mathbf{X}'|)$ , which can be reduced to  $O(|\theta_t| + |A'| + |\mathbf{X}'|)$  with finite difference approximation. Although there are  $C$  iterations in line 7-11, we note that the process is easily parallelizable. Furthermore, the process of updating  $\theta_t$  in line 16 has a complexity of  $\tau_{\theta}(LN^2d + LN^{\prime}d)$ . Considering there are  $\overline{T}$  iterations and  $\overline{K}$  different initializations, we multiply the aforementioned complexity by  $\overline{KT}$ . To sum up, we can see that the time complexity linearly increases with number of nodes in the original graph.

Running Time. We now report the running time of the proposed GCOND for different condensation rates. Specifically, we vary the condensation rates in the range of  $\{0.1\%, 0.5\%, 1\%\}$  on Ogbn-arxiv and  $\{1\%, 5\%, 10\%\}\$  on Cora. The running time of 50 epochs on one single A100-SXM4 GPU is reported in Table [10.](#page-16-0)The whole condensation process (1000 epochs) for generating 0.5% condensed graph of Ogbn-arxiv takes around 2.4 hours, which is an acceptable cost given the huge benefits of the condensed graph.

<span id="page-16-0"></span>

|                                                          |  | $r = 0.1\%$ 0.5% 1%   $r = 1\%$ 5% 10% |  |  |
|----------------------------------------------------------|--|----------------------------------------|--|--|
| Ogbn-arxiv 348.6s 428.2s 609.8s   Cora 37.4s 43.9s 64.8s |  |                                        |  |  |

Table 10: Running time of GCOND for 50 epochs.

### C.4 SPARSIFICATION

In this subsection, we investigate the effect of threshold  $\delta$  on the test accuracy and sparsity. In detail, we vary the values of the threshold  $\delta$  used for truncating adjacency matrix in a range of  $\{0.01, 0.05, 0.1, 0.2, 0.4, 0.6, 0.8\}$ , and report the corresponding test accuracy and sparsity in Fig-ure [3.](#page-16-1) From the figure, we can see that increasing  $\delta$  can effectively increase the sparsity of the obtained adjacency matrix without affecting the performance too much.

<span id="page-16-1"></span>Image /page/16/Figure/7 description: The image displays five line graphs, each plotting accuracy and sparsity against a threshold delta. The graphs are labeled (a) Cora, r=2.5%, (b) Citeseer, r=1.8%, (c) Ogbn-arxiv, r=0.05%, (d) Flickr, r=0.1%, and (e) Reddit, r=0.1%. Each graph has a y-axis labeled "Accuracy/Sparsity (%)" ranging from 0 to 100, and an x-axis labeled "Threshold \delta" ranging from 0.0 to 0.8. The legend in each graph indicates a blue line with diamond markers for "acc" (accuracy) and a red line with triangle markers for "sparsity". In graph (a), accuracy starts at approximately 80% and decreases slightly to about 55% at threshold 0.8, while sparsity starts at 100% and drops sharply to around 10% at threshold 0.1, remaining low thereafter. Graph (b) shows accuracy starting at about 70% and decreasing to about 55%, with sparsity starting at 100% and dropping to about 20% at threshold 0.2, then continuing to decrease. Graph (c) shows accuracy around 65% and sparsity starting at 80% and dropping to near 0% at threshold 0.1. Graph (d) shows accuracy around 45% and sparsity starting at 90% and dropping to around 15% at threshold 0.1, remaining low thereafter. Graph (e) shows accuracy around 85% and sparsity starting at 25% and dropping to near 0% at threshold 0.1, remaining low thereafter. A caption below the graphs reads "Figure 2: Test accuracy and sparsity under different values of \delta."

Figure 3: Test accuracy and sparsity under different values of  $\delta$ .

C.5 DIFFERENT DEPTH AND HIDDEN UNITS.

**Depth Versus Hidden Units.** We vary the number of model layers (GCN) in a range of  $\{1, 2, 3, 4\}$ and the number of hidden units in a range of  $\{16, 32, 64, 128, 256\}$ , and test them on the condensed graphs of Cora and Citeseer. The results are reported in Table [11.](#page-17-0) From the table, we can observe that changing the number of layers impacts the model performance a lot while changing the number of units does not.

<span id="page-17-2"></span>

|                  |                          | (a) Cora, $r=2.6\%$ |                |                |                  |                 | (b) Citeseer, $r=1.8\%$ |                 |                |
|------------------|--------------------------|---------------------|----------------|----------------|------------------|-----------------|-------------------------|-----------------|----------------|
| $H \backslash L$ |                          |                     |                | 4              | $H \backslash L$ |                 |                         |                 |                |
| 16               | $74.8 \pm 0.5$           | $76.8 + 1.0$        | $68.0 \pm 3.0$ | $50.9 \pm 9.5$ | 16               | $58.6 \pm 12.1$ | $69.2 \pm 1.3$          | $56.9 \pm 8.4$  | $40.4 \pm 1.2$ |
| 32               | $\overline{\phantom{a}}$ | $79.2 \pm 0.7$      | $70.4 \pm 3.2$ | $61.1 \pm 7.2$ | 32               | ۰               | $69.4 \pm 1.3$          | $59.9 \pm 10.2$ | $42.6 \pm 3.6$ |
| 64               | $\overline{\phantom{a}}$ | $79.2 \pm 1.0$      | $72.0 \pm 3.3$ | $64.5 \pm 2.2$ | 64               | ۰               | $69.7 \pm 1.5$          | $62.3 \pm 10.3$ | $43.6 \pm 3.7$ |
| 128              | $\sim$                   | $79.9 \pm 0.3$      | $76.6 \pm 1.8$ | $61.8 \pm 3.8$ | 128              | ٠               | $70.2 \pm 1.4$          | $63.3 \pm 9.7$  | $51.6 \pm 1.8$ |
| 256              | $\sim$                   | $80.1 \pm 0.6$      | $75.9 \pm 1.6$ | $65.6 \pm 2.9$ | 256              | -               | $70.6 \pm 0.9$          | $63.5 \pm 10.0$ | $52.9 \pm 5.5$ |

<span id="page-17-0"></span>Table 11: Test accuracy on different numbers of hidden units (H) and layers (L). When L=1, there is no hidden layer so the number of hidden units is meaningless.

Table 12: Cross-depth accuracy on Cora,  $r=2.6\%$ 

| C\T | 2     | 3     | 4     | 5     | 6     |
|-----|-------|-------|-------|-------|-------|
| 2   | 80.30 | 80.70 | 79.46 | 76.06 | 71.23 |
| 3   | 40.62 | 72.37 | 40.14 | 67.19 | 35.02 |
| 4   | 74.24 | 72.56 | 76.26 | 71.70 | 65.12 |
| 5   | 71.31 | 75.73 | 70.95 | 73.13 | 67.12 |
| 6   | 75.20 | 75.18 | 75.67 | 76.16 | 75.00 |

Propagation Versus Transformation. We further study the effect of propagation and transformation on the condensed graph. We use Cora as an example and use SGC as the test model due to its decoupled architecture. Specifically, we vary both the propagation layers and transformation layers of SGC in the range of {1, 2, 3, 4, 5}, and report the performance in Table [13.](#page-17-1) As can be seen, the condensed graph still achieves good performance with 3 and 4 layers of propagation. Although the condensed graph is generated under 2-layer SGC, it is able to generalize to 3-layer and 4-layer SGC. When increasing the propagation to 5, the performance degrades a lot which could be the cause of the oversmoothing issue. On the other hand, stacking more transformation layers can first help boost the performance but then hurt. Given the small scale of the graph, SGC suffers the overfitting issue in this case.

Cross-depth Performance. We show the cross-depth performance in Table [12.](#page-17-2) Specifically, we use SGC of different depth in the condensation to generate condensed graphs and then use them to test on SGC of different depth. Note that in this table, we set weight decay to 0 and dropout to 0.5. We can observe that usgin a deeper GNN is not always helpful. Stacking more layers do not necessarily mean we can learn better condensed graphs since more nodes are involved during the optimization, and this makes optimization more difficult.

| Trans\Prop | 1              | 2              | 3              | 4              | 5              |
|------------|----------------|----------------|----------------|----------------|----------------|
| 1          | 77.09 $±$ 0.43 | 79.02 $±$ 1.17 | 78.12 $±$ 2.13 | 74.04 $±$ 3.60 | 61.19 $±$ 7.73 |
| 2          | 76.94 $±$ 0.50 | 79.01 $±$ 0.57 | 79.11 $±$ 1.15 | 77.57 $±$ 1.03 | 72.37 $±$ 4.25 |
| 3          | 75.28 $±$ 0.58 | 77.95 $±$ 0.67 | 74.16 $±$ 1.50 | 70.58 $±$ 3.71 | 58.28 $±$ 8.90 |
| 4          | 66.87 $±$ 0.73 | 66.54 $±$ 0.82 | 59.24 $±$ 1.60 | 43.94 $±$ 6.33 | 30.45 $±$ 9.67 |
| 5          | 46.44 $±$ 0.91 | 37.29 $±$ 3.23 | 16.05 $±$ 2.74 | 15.33 $±$ 2.79 | 15.33 $±$ 2.79 |

<span id="page-17-1"></span>Table 13: Test accuracy of SGC on different transformations and propagations for Cora,  $r=2.6\%$ 

### C.6 VISUALIZATION OF NODE FEATURES.

In addition, we provide the t-SNE [\(Van der Maaten & Hinton,](#page-11-16) [2008\)](#page-11-16) plots of condensed node features to further understand the condensed graphs. In Cora and Citeseer, the condensed node features are well clustered. For Ogbn-arxiv and Reddit, we also observe some clustered pattern for the nodes within the same class. In contrast, the condensed features are less discriminative in Flickr, which indicates that the condensed structure information can be essential in training GNN.

<span id="page-18-1"></span>Image /page/18/Figure/1 description: The image displays five scatter plots, each containing multiple colored dots. The dots in each plot are clustered into distinct groups, with each group represented by a different color. The first plot shows about seven distinct clusters of dots. The second plot also shows several clusters, with colors like blue, red, orange, and brown. The third plot features a larger number of dots spread out, with visible clusters of green, blue, purple, and brown dots. The fourth plot contains a more dispersed arrangement of dots in various colors, including yellow, purple, blue, and green. The fifth plot shows a dense collection of dots, with several overlapping clusters in colors such as blue, red, yellow, green, and brown.

(a) Cora,  $r=2.5\%$  (b) Citeseer,  $r=1.8\%$  (c) Arxiv,  $r=0.05\%$  (d) Flickr,  $r=0.1\%$  (e) Reddit,  $r=0.1\%$ 

Figure 4: The t-SNE plots of node features in condensed graphs.

### C.7 PERFORMANCES ON ORIGINAL GRAPHS

<span id="page-18-2"></span>We show the performances of various GNNs on original graphs in Table [14](#page-18-2) to serve as references.

| GAT        | Cheby | SAGE | SGC  | APPNP | GCN  |      |
|------------|-------|------|------|-------|------|------|
| Cora       | 83.1  | 81.4 | 81.2 | 81.4  | 83.1 | 81.2 |
| Citeseer   | 70.8  | 70.2 | 70.1 | 71.3  | 71.8 | 71.7 |
| Ogbn-arxiv | 71.5  | 71.4 | 71.5 | 71.4  | 71.2 | 71.7 |
| Flickr     | 44.3  | 47.0 | 46.1 | 46.2  | 47.3 | 47.1 |
| Reddit     | 91.0  | 93.1 | 93.0 | 93.5  | 94.3 | 93.9 |

Table 14: Performances of various GNNs on original graphs. SAGE: GraphSAGE.

### C.8 EXPERIMENTS ON PUBMED.

We also show the experiments for Pubmed with condensation ratio of 0.3% in Table [15.](#page-18-3) From the table, we can observe that GCOND approximates the original performance very well (77.92% vs. 79.32% on GCN). It also generalizes well to different architectures and outperforms GCOND-X and DC-Graph, indicating that it is important to leverage the graph structure information and learn a condensed structure.

Table 15: Performance of different GNNs on Pubmed ( $r=0.3\%$ ).

<span id="page-18-3"></span>

|              | <b>APPNP</b>     | Cheby          | <b>GCN</b>                                         | GraphSage        | SGC.           |
|--------------|------------------|----------------|----------------------------------------------------|------------------|----------------|
| DC-Graph     | 72.76±1.39       | 72.66±0.59     | 72.44+2.90                                         | $71.96 \pm 2.50$ | $75.43 + 0.65$ |
| $GConv-X$    | $73.91 + 0.41$   | $74.57 + 1.00$ | $71.81 + 0.94$                                     | $73.10 + 2.08$   | $76.72 + 0.65$ |
| <b>GCOND</b> | $76.77 \pm 1.17$ |                | $75.48 \pm 0.82$ $77.92 \pm 0.42$ $71.12 \pm 3.10$ |                  | $75.91 + 1.38$ |

<span id="page-18-0"></span>

## D MORE RELATED WORK

Graph pooling. Graph pooling [\(Zhang et al.,](#page-12-13) [2018;](#page-12-13) [Ying et al.,](#page-12-14) [2018b;](#page-12-14) [Gao & Ji,](#page-10-15) [2019\)](#page-10-15) also generates a coarsened graph with smaller size. [Zhang et al.](#page-12-13) [\(2018\)](#page-12-13) is one the first to propose an end-to-end architecture for graph classification by incorporating graph pooling. Later, DiffPool [\(Ying et al.,](#page-12-14) [2018b\)](#page-12-14) proposes to use GNNs to parameterize the process of node grouping. However, those methods are majorly tailored for the graph classification task and the coarsened graphs are a byproduct graph during the representation learning process.