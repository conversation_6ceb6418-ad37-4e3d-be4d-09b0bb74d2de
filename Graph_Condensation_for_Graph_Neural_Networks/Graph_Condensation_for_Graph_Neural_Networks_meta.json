{"table_of_contents": [{"title": "GRAPH CONDENSATION FOR GRAPH NEURAL NET-\nWORKS", "heading_level": null, "page_id": 0, "polygon": [[107.25, 81.75], [503.82421875, 81.75], [503.82421875, 117.6591796875], [107.25, 117.6591796875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 249.0], [334.5, 249.0], [334.5, 260.068359375], [276.75, 260.068359375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 471.0], [206.9384765625, 471.0], [206.9384765625, 481.8515625], [107.25, 481.8515625]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[107.25, 669.75], [212.466796875, 669.75], [212.466796875, 681.01171875], [107.25, 681.01171875]]}, {"title": "3 METHODOLOGY", "heading_level": null, "page_id": 2, "polygon": [[107.25, 512.25], [210.0, 512.25], [210.0, 523.23046875], [107.25, 523.23046875]]}, {"title": "3.1 GRAPH CONDENSATION VIA GRADIENT MATCHING", "heading_level": null, "page_id": 3, "polygon": [[107.25, 83.25], [351.0, 83.25], [351.0, 93.2958984375], [107.25, 93.2958984375]]}, {"title": "3.2 MODELING CONDENSED GRAPH DATA", "heading_level": null, "page_id": 3, "polygon": [[106.5, 699.0], [297.0, 699.0], [297.0, 708.08203125], [106.5, 708.08203125]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[106.681640625, 674.25], [200.513671875, 674.25], [200.513671875, 686.0390625], [106.681640625, 686.0390625]]}, {"title": "4.1 EXPERIMENTAL SETUP", "heading_level": null, "page_id": 5, "polygon": [[106.5, 82.8544921875], [229.6494140625, 82.8544921875], [229.6494140625, 93.6826171875], [106.5, 93.6826171875]]}, {"title": "4.2 COMPARISON WITH BASELINES", "heading_level": null, "page_id": 5, "polygon": [[106.5, 542.56640625], [268.34765625, 542.56640625], [268.34765625, 553.39453125], [106.5, 553.39453125]]}, {"title": "4.3 GENERALIZABILITY OF CONDENSED GRAPHS", "heading_level": null, "page_id": 7, "polygon": [[106.5, 348.75], [327.515625, 348.75], [327.515625, 358.48828125], [106.5, 358.48828125]]}, {"title": "4.4 ANALYSIS ON CONDENSED DATA", "heading_level": null, "page_id": 7, "polygon": [[107.20458984375, 650.25], [273.75, 650.25], [273.75, 660.90234375], [107.20458984375, 660.90234375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 619.91015625], [196.330078125, 619.91015625], [196.330078125, 631.51171875], [107.25, 631.51171875]]}, {"title": "ACKNOLWEDGEMENT", "heading_level": null, "page_id": 9, "polygon": [[107.05517578125, 82.5], [219.9375, 82.5], [219.9375, 93.2958984375], [107.05517578125, 93.2958984375]]}, {"title": "ETHICS STATEMENT", "heading_level": null, "page_id": 9, "polygon": [[107.25, 168.0], [212.16796875, 168.0], [212.16796875, 179.05078125], [107.25, 179.05078125]]}, {"title": "REPRODUCIBILITY STATEMENT", "heading_level": null, "page_id": 9, "polygon": [[107.25, 220.5], [269.25, 220.5], [269.25, 231.451171875], [107.25, 231.451171875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 295.5], [176.30859375, 295.5], [176.30859375, 305.701171875], [107.25, 305.701171875]]}, {"title": "A DATASETS AND HYPER-PARAMETERS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 82.2744140625], [321.240234375, 82.2744140625], [321.240234375, 93.5859375], [107.25, 93.5859375]]}, {"title": "A.1 DATASETS", "heading_level": null, "page_id": 13, "polygon": [[107.25, 107.701171875], [179.7451171875, 107.701171875], [179.7451171875, 117.755859375], [107.25, 117.755859375]]}, {"title": "A.2 HYPER-PARAMETER SETTING", "heading_level": null, "page_id": 13, "polygon": [[107.25, 319.5], [261.7734375, 319.5], [261.7734375, 330.2578125], [107.25, 330.2578125]]}, {"title": "B ALGORITHM", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 205.541015625], [193.5, 205.541015625], [193.5, 216.369140625], [107.1298828125, 216.369140625]]}, {"title": "Algorithm 1: GCOND for Graph Condensation", "heading_level": null, "page_id": 14, "polygon": [[107.25, 321.556640625], [298.5, 321.556640625], [298.5, 331.224609375], [107.25, 331.224609375]]}, {"title": "C MORE EXPERIMENTS", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 583.5], [238.3154296875, 583.5], [238.3154296875, 594.7734375], [107.1298828125, 594.7734375]]}, {"title": "C.1 ABLATION STUDY", "heading_level": null, "page_id": 14, "polygon": [[106.5, 607.5], [212.6162109375, 607.5], [212.6162109375, 617.9765625], [106.5, 617.9765625]]}, {"title": "C.2 NEURAL ARCHITECTURE SEARCH", "heading_level": null, "page_id": 15, "polygon": [[106.5, 237.75], [279.5537109375, 237.75], [279.5537109375, 248.2734375], [106.5, 248.2734375]]}, {"title": "C.3 TIME COMPLEXITY AND RUNNING TIME", "heading_level": null, "page_id": 15, "polygon": [[106.5, 589.5], [307.5, 589.5], [307.5, 600.1875], [106.5, 600.1875]]}, {"title": "C.4 SPARSIFICATION", "heading_level": null, "page_id": 16, "polygon": [[106.5, 267.0], [204.0, 267.0], [204.0, 276.50390625], [106.5, 276.50390625]]}, {"title": "C.6 VISUALIZATION OF NODE FEATURES.", "heading_level": null, "page_id": 17, "polygon": [[106.5, 655.5], [289.5, 655.5], [289.5, 665.9296875], [106.5, 665.9296875]]}, {"title": "C.7 PERFORMANCES ON ORIGINAL GRAPHS", "heading_level": null, "page_id": 18, "polygon": [[106.5, 208.5], [304.3564453125, 208.5], [304.3564453125, 218.689453125], [106.5, 218.689453125]]}, {"title": "C.8 EXPERIMENTS ON PUBMED.", "heading_level": null, "page_id": 18, "polygon": [[106.5, 369.75], [255.75, 369.75], [255.75, 380.14453125], [106.5, 380.14453125]]}, {"title": "D MORE RELATED WORK", "heading_level": null, "page_id": 18, "polygon": [[106.5, 555.0], [250.5, 555.0], [250.5, 566.9296875], [106.5, 566.9296875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 62], ["Text", 10], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 61], ["ListItem", 3], ["TextInlineMath", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 831, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 633], ["Line", 73], ["Text", 6], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 847], ["Line", 140], ["TextInlineMath", 6], ["Equation", 4], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 617], ["Line", 82], ["TextInlineMath", 5], ["Text", 3], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 55], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1163], ["TableCell", 105], ["Line", 90], ["Text", 4], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1483, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 676], ["Line", 86], ["TableCell", 66], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 579], ["TableCell", 282], ["Line", 53], ["Text", 4], ["Caption", 3], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 12573, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 46], ["ListItem", 10], ["Reference", 10], ["SectionHeader", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 49], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 49], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 600], ["TableCell", 54], ["Line", 50], ["Text", 8], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 788], ["Line", 52], ["SectionHeader", 4], ["Text", 3], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 487], ["TableCell", 116], ["Line", 50], ["Text", 4], ["ListItem", 4], ["Reference", 4], ["Table", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 7355, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 96], ["TableCell", 15], ["Text", 5], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 570], ["TableCell", 214], ["Line", 54], ["Table", 3], ["Caption", 3], ["Text", 3], ["TableGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4678, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["TableCell", 72], ["Line", 31], ["Caption", 4], ["Reference", 4], ["SectionHeader", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 3779, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Graph_Condensation_for_Graph_Neural_Networks"}