# GRAPH CONDENSATION VIA RECEPTIVE FIELD DISTRIBUTION MATCHING

## A PREPRINT

Mengyang Liu Georgia Institute of Technology

Xinshi Chen Georgia Institute of Technology

Shanchuan Li Georgia Institute of Technology

Le Song Georgia Institute of Technology

June 29, 2022

## ABSTRACT

Graph neural networks (GNNs) enable the analysis of graphs using deep learning, with promising results in capturing structured information in graphs. This paper focuses on creating a small graph to represent the original graph, so that GNNs trained on the size-reduced graph can make accurate predictions. We view the original graph as a distribution of receptive fields and aim to synthesize a small graph whose receptive fields share a similar distribution. Thus, we propose Graph Condesation via Rceptive Field Distribution Matching (GCDM), which is accomplished by optimizing the synthetic graph through the use of a distribution matching loss quantified by maximum mean discrepancy (MMD). Additionally, we demonstrate that the synthetic graph generated by GCDM is highly generalizable to a variety of models in evaluation phase and that the condensing speed is significantly improved using this framework.

# 1 Introduction

Many real-world datasets, including social networks, molecular interactions, and recommendation systems are graphstructured. Recently, a substantial line of research on graph neural networks (GNNs) enables the analysis of graphs using deep learning, with promising results in capturing structured information in graphs.

Numerous real-world graphs are large-scale, containing millions of nodes and trillions of edges. Due to the noneuclidean nature of the network and the complicated dependencies between nodes, training a GNN over a large graph can be very expensive. More specifically, we often need to train a GNN multiple times on the same graph to validate the design choices made when tuning the hyperparameters, searching the architecture, and so on, or when considering the continual learning scenario. As a consequence, there is a considerable interest in techniques that reduce the computational cost of training multiple GNNs on the same graph without losing the performance.

To address these issues, this paper focuses on creating a small but informative graph to represent the original graph, so that GNNs trained on the size-reduced graph can make accurate predictions on the original graph.

Graph sparsification and graph coarsening are two well-known techniques for graph simplification. By reducing the number of edges, graph sparsification attempts to mimic a graph with a more sparse graph, whereas graph coarsening directly reduces the number of nodes (using a subset of the original node set). While the motive for the two techniques is self-evident, the disadvantage is equally self-evident: (1) sparsification has a diminishing effect when the graph's complexity is primarily derived from node features. (2) Both methods seek to maintain a spectral attribute of the original graph, such as the principal eigenvalues of the Laplacian Matrix, although this may not be the ideal option due to the possibility of preserving significant noise.

[\[1\]](#page-10-0) recently proposed a deep learning based method called GCOND. Its central concept is *synthesizing* a small graph, by minimizing the *gradient matching* loss between the gradients of training losses w.r.t. GNN parameters given by

the original graph and the synthetic graph. While GCond has demonstrated advantages over traditional approaches, it has two downsides. Firstly, while minimizing the gradient matching loss, the condensation procedure is expensive due to the need of computing the second-order derivative w.r.t. GNN parameters. For example, it takes about 100 minutes (running on a single RTX8000 GPU) to condense the Ogbn-arxiv dataset to 1% by 50 epochs. Secondly, since the gradient matching loss is architecture-dependent, the condensed graph may not generalize well to new GNN architectures.

To address the aforementioned difficulty, we present Graph Condensation through receptive field Distribution Matching (GCDM). More precisely, we view the original graph as *a distribution of receptive fields* and aim to synthesize a small graph whose receptive fields share a similar distribution as the original graph (see Figure [1\)](#page-2-0). This is accomplished by optimizing the synthetic graph through the use of a distribution matching loss quantified by maximum mean discrepancy (MMD). In contrast to GCOND, our condensation loss does not rely on the training loss of optimizing parameters of a specific GNN. Therefore, the condensed graph can be used for training various GNN models. Furthermore, it avoids computing the second-order gradient w.r.t. the GNN parameters, which reduces the cost of the condensation process.

The use of distribution matching method for dataset condensation has been proposed in a recent Arxiv paper by [\[2\]](#page-10-1), which is applied to image dataset condensation. However, applying the distribution matching method to condense graph-structured datasets is more challenging due to the complex dependencies between the nodes. As an example, it is straightforward to define a data distribution given an image dataset since each image can be viewed as an independent sample. However, in a graph-structured dataset, the nodes and edges are no longer independent. We need to carefully utilize the concept of receptive field to define the distribution that we aim to match. We will introduce the details in Sec [3.](#page-3-0)

To summarize, the main contributions of our work are:

- *Methodology:* We present a novel method for graph condensation, in which we define a distribution matching problem based on the concept of receptive fields.
- *Accuracy:* Experimentally, we demonstrate the effectiveness of GCDM on a variety of graph datasets. GNNs trained on small graphs produced by GCDM can achieve comparable performances to those trained on the original graphs. For instance, GCDM can approximate the original test accuracy by 97.6% on Filckr, 90.6% on Ogbn-arxiv with a 99% graph size reduction, and it can approximate 99% test accuracy on Cora, CiteSeer and PubMed with a 99% graph size reduction.
- *Generalization ability:* Our approach is broadly applicable to all GNN models, including generative invertible networks (GINs) [\[3\]](#page-10-2), which excel in discovering pathophysiologic information. For example, we achieved accuracy of 42.2% (GCDM-X) and 42.5% (GCDM) on the Flickr dataset with GIN, compared to the benchmark approach GCOND's [\[1\]](#page-10-0) 29.5% (GCOND-X) and 38.8% (GCOND).
- *Efficiency:* Our method is more efficient than the present graph condensation counterpart. For instance, in order to generate a 1% synthetic graph for 50 epochs, GCDM takes an RTX8000 instance 1,782 GPU seconds while GCOND takes the same instance 5,960 GPU seconds.

Image /page/2/Figure/1 description: This figure illustrates a comparison between a large graph T with 44,625 training nodes and a synthetic graph S with 210 training nodes. For graph T, test accuracies for GCN, SGC, APPNP, and GIN are 51.6%, 51.3%, 47.8%, and 47.2% respectively. For synthetic graph S, test accuracies for GCN, SGC, APPNP, and GIN are 48.2%, 49.4%, 45.9%, and 42.5% respectively. Both graphs are processed to show the distribution of their receptive fields. The receptive fields from graph T are shown in the top right, and the receptive fields from synthetic graph S are shown in the bottom right. A "Distribution Matching" process is indicated between these two sets of receptive fields.

<span id="page-2-0"></span>Figure 1: The overall framework of graph condensation via receptive field distribution matching (GCDM) and the test performance on Flickr dataset with 99.5% size reduction.

# 2 Related Work

### 2.1 Graph Neural Networks.

Graph neural networks (GNNs) have gained increasing popularity in both research and applications due its powerful representability on graph-structured data, which enables the use of neighbor information to generate more expressive representations. [\[4,](#page-10-3) [5,](#page-10-4) [3,](#page-10-2) [6,](#page-10-5) [7,](#page-10-6) [8\]](#page-10-7). The methodology has been implemented in various real-world applications such as recommender systems [\[9\]](#page-11-0), computer vision [\[10\]](#page-11-1), and natural language processing [\[11\]](#page-11-2).

### 2.2 Coreset & Data Condensation

Coresets are small and highly informative weighted subsets of the original dataset that can be used to approximate its performance. Numerous works have studied the selection strategy to facilitate the efficient training of deep learning models in supervised learning scenarios. Methods include maximizing the diversity of selected sample using parameters gradients as the feature [\[12\]](#page-11-3), approximation of the coreset and the original data distributions [\[13,](#page-11-4) [14,](#page-11-5) [15,](#page-11-6) [16\]](#page-11-7), incrementally choosing data point with the largest negative implicit gradient [\[17\]](#page-11-8). However, the coreset selection is upper-bounded by the existing features, which could potentially constrain its expressiveness. Data condensation was proposed to address the aforementioned issue since it seeks to generate new samples that incorporate more information. Although a recently proposed gradient matching solution to data condensation seems to achieve promising performance [\[18\]](#page-11-9), it may provide difficulties since it was designed for image data.

### 2.3 Graph Coarsening & Graph Sparsification.

Similar to coreset, graph coarsening is the idea to reduce the size of graph data while keeping its basic properties.[\[19\]](#page-11-10) considered the graph reduction problem from the perspective of restricted spectral approximation while modifying the measure utilized for the construction of graph sparsifiers. Graph sparsification [\[20\]](#page-11-11), on the other hand, tries to make a graph sparser while only removing edges. While coreset, graph coarsening, and graph sparsification methods are generally computationally efficient, they do come with their limitations. All three methods conduct greedy and incremental searches. Such searches don't guarantee global optimal. Additionally, coreset and graph coarsening both depend on the assumption that there exist highly representative samples in the original data; graph sparsification, on the

other hand, makes the assumption that a large number of insignificant edges that can be removed. Both of the preceding are not always true.

### 2.4 Data Condensation & Graph Condensation.

In contract to data selection methods such as coreset, the goal of data condensation [\[21,](#page-11-12) [18\]](#page-11-9) is to synthesize informative samples rather than selecting from existing samples. Recently, [\[22\]](#page-11-13) proposed matching the gradients w.r.t a neural network weights between the original data and the synthetic data. Then, [\[23\]](#page-11-14) further reduced the computational cost by matching the distribution of the two datasets. Similar to data condensation, graph condensation seeks to generate a synthetic graph that resembles the original data. [\[1\]](#page-10-0) recently attempted to match the network gradients of GNNs and generate an adjacency matrix for the synthetic graph using a trained multilayer perceptron. It achieved similar performance to the original graph. However, this method has limitations in terms of generalizability and computational speed.

# <span id="page-3-0"></span>3 Methodology

This section introduces our *graph condensation via receptive field distribution matching* framework, GCDM. We begin with a brief review of the graph condensation problem and existing approaches in Sec [3.1,](#page-3-1) and then discuss our methodology in greater detail in the followed sub-sections.

<span id="page-3-1"></span>

### 3.1 Graph Condensation Problem

Consider a graph dataset  $\mathcal{T} = \{A, X, Y\}$  where  $A \in \mathbb{R}^{N \times N}$  is an adjacency matrix,  $X \in \mathbb{R}^{N \times d}$  is a matrix of node features, and  $\hat{Y} \in \{0, ..., C-1\}^N$  denotes the label of nodes. The constants N, d, C are the number of nodes, the dimension of node features, and the number of classes respectively. Graph condensation aims to find a small graph  $S = \{A', X', Y'\}$  with  $A' \in \mathbb{R}^{N' \times N'}$ ,  $X' \in \mathbb{R}^{N' \times d}$ ,  $Y' \in \{0, ..., C-1\}^{N'}$  and  $N' \ll N$  such that GNNs trained on S can achieve similar performances to GNNs trained on the original graph  $\mathcal T$ . Therefore, this problem can be formulated as the following bi-level optimization problem,

$$
\min_{\mathcal{S}} \mathcal{L}(\Psi_{\theta^{\mathcal{S}}}(A, X), Y) s.t. \ \theta^{\mathcal{S}} = \arg\min_{\theta} \mathcal{L}(\Psi_{\theta}(A', X'), Y') \tag{1}
$$

<span id="page-3-2"></span>where  $\Psi_\theta$  denotes a GNN parameterized by  $\theta$ ,  $\mathcal L$  denotes the training loss function (i.e cross-entropy), and  $\theta^S$  denotes the set of parameters obtained by minimizing the training loss on the small graph  $S$ .

Existing Solutions. Previous work on data condensation attempted to solve Eq. [1](#page-3-2) by matching the gradient of the neural network [\[22\]](#page-11-13), and [\[1\]](#page-10-0) further extended this method to graph neural networks. The gradient matching objective in [\[1\]](#page-10-0) is

<span id="page-3-3"></span>
$$
\min_{\mathcal{S}} E_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\theta} \mathcal{L}(\Psi_{\theta_t}(A', X'), Y'), \nabla_{\theta} \mathcal{L}(\Psi_{\theta_t}(A, X), Y)) \right]
$$
(2)

where  $P_{\theta_0}$  is a distribution of parameter initialization of  $\Psi$ , and  $D$  is a distance measurement.

Challenges. Optimizing Eq. [2](#page-3-3) can result in a condensed graph that produces similar gradients as the original graph during training. However, the gradients are only guaranteed to be similar on the particular model structure  $\Psi_{\theta}$  used during the condensation. The generalization ability of training other GNN architectures on the condensed graph is not guaranteed by the objective in Eq. [2.](#page-3-3) Additionally, optimizing Eq. [2](#page-3-3) could be very expensive because it involves computing the second order derivative w.r.t. the parameters in Ψ. In the subsequent experiment part, we will also compare the efficiency of our method to that of the gradient matching method.

Our Solution: Distribution Matching between Graphs. To overcome the above-mentioned challenges, we propose Graph Condensation via Distribution Matching (GCDM). This method is inspired by [\[2\]](#page-10-1) which proposed to condense image datasets via distribution matching. The main idea in [\[2\]](#page-10-1) is to synthesize a small set of images  $S_{IMG}$  that can accurately approximate the data distribution of real images  $\mathcal{T}_{IMG}$ , by minimizing the maximum mean discrepancy (MMD) between them:

$$
\min_{S_{IMG}} \text{MMD}(\mathcal{S}_{IMG}, \mathcal{T}_{IMG}). \tag{3}
$$

The method of distribution matching can be more advantages because it *only compares the data distributions induced by the two datasets*, without relying on the configurations in the training process. However, migrating the idea of distribution matching to condense graph-structured dataset  $\tau$  is not straightfoward. It is unclear how to view a graph  $\tau$  as a data distribution since the nodes in the graph are connected by edges, unlike images which are viewed as independent samples. In the following, we will introduce how we formulate the graph condensation problem as a distribution matching problem.

### 3.2 Viewing a Graph as a Distribution

Before going into the details of our proposed GCDM, we first introduce *how a graph* T *is converted to a distribution* under the context of node classification tasks.

#### 3.2.1 Node classification by GNN

For a node classification dataset  $\mathcal{T} = \{A, X, Y\}$ , training a GNN  $\Psi_\theta$  is accomplished by optimizing the classification loss over the nodes

$$
\mathcal{L}(\Psi_{\theta}(A, X), Y) = \frac{1}{N} \sum_{i=1}^{N} \ell(\psi_{\theta}(A, X, i), Y_i)
$$
\n(4)

<span id="page-4-1"></span><span id="page-4-0"></span>
$$
= \mathbb{E}_{((A,X,i),Y_i)\sim P_{\mathcal{T}}} \left[ \ell \left( \psi_{\theta}(A,X,i),Y_i \right) \right], \tag{5}
$$

where  $\ell$  is a loss function,  $\psi_{\theta}(A, X, i)$  is the class predicted by the GNN  $\Psi_{\theta}$  for the *i*-th node, and  $Y_i$  is the *i*-th label. According to Eq. [4](#page-4-0) and Eq. [5,](#page-4-1) we can view each pair  $((A, X, i), Y_i)$  as an independent data point, and view the training graph as an empirical distribution  $P_{\tau}$  induced by these data points:

$$
\{((A, X, 1), Y_1), ((A, X, 2), Y_2), \cdots, ((A, X, N), Y_N)\}.
$$
\n(6)

However, representing and matching such a distribution seem to be complicated, because each data point involves a large adjacency matrix A. Therefore, we make use of the concept of receptive field to simplify this distribution.

### 3.2.2 Receptive Fields of GNN

In Eq. [4,](#page-4-0) the label of node i is predicted by  $\psi_{\theta}(A, X, i)$ . In fact, most GNNs only makes use of a *local graph* around node *i* to compute  $\psi_{\theta}(A, X, i)$ . As an example, for a L-layer Graph Convolutional Neural Network (GCN), only the information of the target node's L-hop neighbors contributes to the representation and prediction, so as other message-passing based GNNs regardless of the specific architecture. In the literature of GNN researches, a target node's L-hop local graph is called the *receptive field* of a L-layer GNN (See Figure [2\)](#page-4-2).

Image /page/4/Figure/13 description: The image displays three network graphs, labeled Layer 0, Layer 1, and Layer 2. Each graph has a central dark red node connected to several teal nodes, which are in turn connected to light blue nodes. A brown circle surrounds the central nodes and the first layer of teal nodes in Layer 0 and Layer 1, and it encompasses all nodes in Layer 2. The number of nodes increases with each layer, with Layer 0 having one central node, five teal nodes, and five light blue nodes. Layer 1 has one central node, five teal nodes, and eight light blue nodes. Layer 2 has one central node, five teal nodes, and ten light blue nodes, with one light blue node extending beyond the circle.

<span id="page-4-2"></span>Figure 2: Receptive field  $R(i, L)$  of GNNs for a node i (red-colored) with  $L = 0, 1, 2$ .

In this paper, we use the notation  $R(i, L)$  to represent the receptive field of a L-layer GNN for node i. Clearly, if L is larger the the diameter of graph, then  $R(i, L)$  contains the whole graph.

### 3.2.3 Viewing a Graph as a Distribution of Receptive Fields

Given the concept of receptive fields  $R(i, L)$ , we can rewrite the loss in Eq. [4](#page-4-0) as

$$
\mathcal{L}(\Psi_{\theta}(A, X), Y) = \frac{1}{N} \sum_{i=1}^{N} \ell(\psi_{\theta}\left(R(i, L)\right), Y_i)
$$
\n(7)

$$
= \mathbb{E}_{(R(i,L),Y_i)\sim P_{\mathcal{T}}^L} \left[ \ell \left( \psi_{\theta}(R(i,L)), Y_i \right) \right], \tag{8}
$$

where each pair  $(R(i, L), Y_i)$  can be viewed as an independent data point, and  $P^L_\mathcal{T}$  is the empirical distribution induced by these data points:

$$
\{(R(1, L), Y_1), (R(2, L), Y_2), \cdots, (R(N, L), Y_N)\}.
$$
\n(9)

In such a way, we convert a graph  $T$  to a distribution  $P^L_T$ . Based on this conversion, we can then make use of the distribution matching loss to synthesize another S whose induced distribution  $P_S^L$  is similar to  $P_T^L$ .

### 3.2.4 Some Remarks.

The hop number L has an effect on the size of the receptive field since it determines how many neighbours are included. Ideally, we want L to be larger than the diameter of  $\mathcal T$ , enabling the local graph to contain information of the entire graph. However, doing so will result in the following: 1) a large local graph and significant computational cost; 2) the introduction of noises 3) make the local graph indistinguishable, which is analogous to the over-smoothing problem in deep graph convolutional neural network [\[24,](#page-11-15) [25,](#page-11-16) [26\]](#page-11-17). In many real-world datasets, using a larger number of layers does not increase the prediction accuracy even when training on the original large graph. In our studies, we will use a fixed L during the condensation process, and then train the condensed graph with a  $L^f$  layer model where  $L' \leq L$ .

In this paper, we present GCDM under the context of node classification tasks. However, it is trivial to generalize the method to link prediction tasks.

### 3.3 GCDM: Graph Condensation via Receptive Field Distribution Matching

With a graph dataset  $T$  viewed as a distribution of receptive fields  $P_T^L$ , we aim to synthesize a small graph  $S$  whose distribution of receptive fields  $P_S^L$  is similar to  $P_T^L$ .

## 3.3.1 Synthesize the Labels $Y'$

Recall that the small graph  $S = \{A', X', Y'\}$  contains  $A' \in \mathbb{R}^{N' \times N'}$ ,  $X' \in \mathbb{R}^{N' \times d}$ , and  $Y' \in \{0, ..., C-1\}^{N'}$  with  $N' \ll N$ . To keep the class distribution of Y' similar to that of Y, our method first determines the synthetic labels Y' by sampling according to the class distribution in  $Y$ .

More precisely, let

<span id="page-5-0"></span>
$$
V_c := \{i : Y_i = c\}
$$
 (10)

be the set of nodes that are in class c in the original graph  $T$ . We sample the value of each synthetic label  $Y'_j$ independently from the following categorical distribution:

$$
P(Y'_{j} = c) = r_{c} := \frac{|V_{c}|}{\sum_{c'=0}^{C-1} |V_{c'}|}, \quad \text{for } c = 1, \cdots, C-1,
$$
\n(11)

where  $r_c$  denotes the class ratio. After the synthetic labels  $Y'$  are sampled, we can define

$$
V'_{c} := \{ i : Y'_{i} = c \}
$$
\n<sup>(12)</sup>

as the set of nodes that are in class c in the synethetic graph  $S$ .

## 3.3.2 Synthesize $A'$ and $X'$ .

Given the sampled labels Y', we further optimize A' and X' to minimize the distance between distributions  $P_S^L$  and  $P^L_{\mathcal{T}}.$ 

To achieve the goal, within each class  $c$ , we optimize the maximum mean discrepancy (MMD) [\[27\]](#page-11-18) between the distributions of receptive fields according to the two graphs:

$$
\text{MMD}_c(\mathcal{T}, \mathcal{S}) := \sup_{\phi \in \mathcal{H}} \left| \frac{1}{|V_c|} \sum_{i \in V_c} \phi(R_{\mathcal{T}}(i, L)) - \frac{1}{|V_c'|} \sum_{j \in V_c'} \phi(R_{\mathcal{S}}(j, L)) \right|,
$$

where H is a family of functions, and we use the notation  $R<sub>T</sub>$  and  $R<sub>S</sub>$  to distinguish the receptive fields in different graphs  $\tau$  and S. Note that when H is a reproducing kernel Hilbert space, theoretical guarantees are available in the literature. By aggregating the MMD losses across all classes, the overall condensation loss is the following weighted sum:

<span id="page-6-0"></span>
$$
\min_{A', X'} \sum_{c=1}^{C-1} r_c \cdot \text{MMD}_c(\mathcal{T}, \mathcal{S}).
$$
\n(13)

### 3.4 Parameterization and Algorithm

In this section, we introduce how to optimize the condensation loss introduced in Eq. [13.](#page-6-0) There are two main challenges. Firstly, we need to define the family of parametric function  $H$  so that the MMD loss can be computed efficiently. Secondly, we need to parameterize the adjacency matrix  $A'$  in a continuous way to avoid optimizing over a combinational solution space.

#### 3.4.1 Defining $H$ by GNNs

Due to the fact that (GNNs) inherently aggregate the information from each node's receptive field, they are ideal parametric functions  $\phi(R(i, L))$  for computing the statistics of receptive fields. Therefore, in GCDM, we define the function space  $H$  via GNN models.

#### 3.4.2 Parametrize $A'$ by MLP

As discussed in [\[1\]](#page-10-0), treating  $A'$  and  $X'$  as independent parameters ignores the inherent relationships between graph structure and node features, which have been well accepted in the literature. Therefore, we parameterize  $A'$  as a function of the synthetic node features  $X'$ :

$$
A' = g_{\vartheta}(X') \text{ with } A'_{ij} = \text{Sigmoid}\left(\text{MLP}_{\vartheta}([X'_{i}; X'_{j}])\right),\tag{14}
$$

where MLP<sub>i</sub> is a multilayer perceptron, and  $[\cdot; \cdot]$  denotes concatenation. With this parameterizaton, we optimize the parameters  $\vartheta$  to find the synthetic  $A'$ .

#### 3.4.3 Condensation Loss in GCDM

With the function space  $H$  defined by GNNs and the matrix  $A'$  parameterized as a function of node features, we can now present the actual condensation loss used in GCDM. Let  $\Phi_{\theta}$  be an L-layer GNN model parametrized by  $\theta$ , we solve the following optimization problem to generate the synthetic  $X'$  and  $A'$ :

$$
\min_{\vartheta, X'} \sum_{c=1}^{C-1} r_c \cdot \max_{\theta_c} \left\| \frac{1}{|V_c|} \sum_{i \in V_c} \mathsf{emb}_i^c - \frac{1}{|V_c'|} \sum_{j \in V_c'} \mathsf{emb}_j^{c'} \right\|_2^2 \tag{15}
$$

where

<span id="page-6-1"></span>
$$
\{\mathbf{emb}_i^c\}_{i=1}^N \leftarrow \Phi_{\theta_c}(A, X) \tag{16}
$$

$$
\{\text{emb}_{j}^{c\prime}\}_{j=1}^{N'} \leftarrow \Phi_{\theta_c}(A' = g_{\vartheta}(X'), X')\tag{17}
$$

are node embeddings given by the GNN model  $\Phi_{\theta}$ .

### 3.4.4 Algorithm

To solve the optimization problem defined in Eq. [15,](#page-6-1) we adopt and algorithm that alternatively update the parameters  $\vartheta$ ,  $X'$  and  $\theta_c$ . The algorithm steps are lined out in Algorithm [1.](#page-7-0) When implementing this algorithm, the gradient descent step can be replaced by other optimizers.

### Algorithm 1: GCDM for Graph Condensation

**Input:** Training data  $\mathcal{T} = (A, X, Y)$ 1 Obtain  $Y'$  by sampling from the distribution in Eq. [11;](#page-5-0) 2 Initialize  $X<sup>i</sup>$  by randomly sample N' node features from X; 3 Initialize  $\vartheta$ ,  $\{\theta_c\}_{c=0}^{C-1}$  randomly; 4 for  $i = 1, \cdots, \tilde{M}$  do 5 | for  $e = 1, \dots, K_1$  do 6 **for**  $c = 0, ..., C - 1$  do  $\begin{array}{cc} \pi & | & | & A' \leftarrow g_{\vartheta}(X') ; \end{array}$  $\quad \begin{array}{|c|c|c|c|}\ \text{}} & \text{ $\big\{} \in \text{emb}_i^c \big\}_{i=1}^N \leftarrow \Phi_{\theta_c}(A,X); \ \end{array}$  $\mathsf{9} \quad \Big| \qquad \Big| \quad \{\mathsf{emb}_{j}^{c\,\prime}\}_{j=1}^{N^\prime} \leftarrow \Phi_{\theta_c}(A^\prime,X^\prime);$ 10  $\left|\left|\left|\left|\right|\right|\right|\leq\left|\mathcal{L}\leftarrow r_{c}\right|\right|$  $\frac{1}{|V_c|}\sum_{i\in V_c}\mathsf{emb}_i^c - \frac{1}{|V_c'|}\sum_{j\in V_c'}\mathsf{emb}_j^c{}'\bigg\|$ 2  $\frac{1}{2}$ 11 **if**  $e\%(\tau_1 + \tau_2) < \tau_1$  then 12 | | Update  $X' \leftarrow X' - \eta_1 \nabla_{X'} \mathcal{L}$ ;  $13$  else 14 | | Update  $\vartheta \leftarrow \vartheta - \eta_2 \nabla_{\vartheta} \mathcal{L};$ 15 **for**  $e = 1, \dots, K_2$  **do** 16  $A' \leftarrow g_{\vartheta}(X')$ ; 17 **for**  $c = 0, ..., C - 1$  do  $\quad \quad \texttt{18} \quad | \quad \quad \big| \quad \{ \texttt{emb}_i^c \}_{i=1}^N \leftarrow \Phi_{\theta_c}(A,X);$  $\quad \ \ \, \texttt{19} \quad \quad \ \ \bigg| \qquad \big\{ \texttt{emb}_j^c{'} \}_{j=1}^{N'} \leftarrow \Phi_{\theta_c}(A',X');$ 20  $\left|\left|\left|\left|\right|\right|\right|\leq C$   $\left|\left|\left|\right|\right|\right|$  $\frac{1}{|V_c|}\sum_{i\in V_c}\texttt{emb}_i^c - \frac{1}{|V_c'|}\sum_{j\in V_c'}\texttt{emb}_j^c{}' \bigg\|$ 2 ;<br>2 21 | | Update  $\theta_c \leftarrow \theta_c + \eta_3 \nabla_{\theta_c} \mathcal{L}$ ; 22  $A' \leftarrow g_{\vartheta}(X')$ ; 23  $A'_{ij} \leftarrow 0$  if  $A'_{ij} < 0.5$ ; Output:  $S = (A', X', Y')$ ;

<span id="page-7-0"></span>

### 3.4.5 A "Graphless" Variant: GCDM-X

Inspired by the work in [\[1\]](#page-10-0), we provide a model variant named GCDM-X that only producing synthetic node features  $X'$  and fix the structure  $A'$  to be an identity matrix I. Despite the fact that it does not learn the synthetic structure, this variation appears to be competitive in experiments. The potential reason could be that the node features are very informative, and the condensed  $X<sup>'</sup>$  have incorporated relevant information from the graph.

<span id="page-7-1"></span>Table 1: Information used during condensation, training, and testing.  $X'$  and  $A'$  refer to the condensed graph.

|                          | GCOND-X                        | GCOND                              | GCDM-X                         | GCDM                               |
|--------------------------|--------------------------------|------------------------------------|--------------------------------|------------------------------------|
| Condensation<br>Training | $A_{train}, X_{train}$<br>$X'$ | $A_{train}, X_{train}$<br>$A', X'$ | $A_{train}, X_{train}$<br>$X'$ | $A_{train}, X_{train}$<br>$A', X'$ |
| Test                     | $A_{test}, X_{test}$           | $A_{test}, X_{test}$               | $A_{test}, X_{test}$           | $A_{test}, X_{test}$               |

# 4 EXPERIMENTS

We conducted experiments on a range of graph datasets to evaluate the performance of GCDM. We were able to demonstrate the benefits of GCDM by comparing its performances to that of alternative baseline approaches, testing the generalizability of the condensed graphs on a variety of model architectures, running a speed comparison with the benchmark, and visualizing the condensed graphs. The information used in condensation, training and testing phase are demonstrated in Table. [1.](#page-7-1)

### 4.1 EXPERIMENTAL SETTINGS

Datasets. We evaluate the performance of GCDM on five graph datasets: Cora [\[28\]](#page-11-19), PubMed [\[29\]](#page-11-20), Citeseer [\[30\]](#page-11-21), Ogbn-arxiv [\[31\]](#page-11-22), and Flikcr [\[32\]](#page-11-23). For all these datasets, we use public splits to split them into train, validation, and test sets. Dataset statistics are summarized in Table. [2.](#page-8-0)

Baselines. We compare our proposed approach to six baseline methods: (i) one graph coarsening method [\[33\]](#page-11-24), (ii-iv) three coreset methods (Random, Herding [\[34\]](#page-11-25)) and K-Center [\[35\]](#page-12-0), (v) dataset condensation (DC) [\[22\]](#page-11-13), and (vi) the recent advance: Graph Condensation for Graph Neural Networks (GCOND) [\[1\]](#page-10-0). It should be noted that PubMed's benchmark coarsening results and the corresponding codes are not provided. Thus, we implement coarsening according to [\[36\]](#page-12-1).

Experiment setting. The experiment procedure consists of 3 steps: (1) given the training graph dataset, the condensation algorithm produces the condensed graph dataset; (2) train a GNN model using the condensed graph dataset and select the model using the original validation set; (3) evaluate the trained model on the original test dataset. Only the step (1) will be different for different condensation methods. After obtaining the condensed graph, the followed steps (2) and (3) are the same for all methods.

<span id="page-8-0"></span>

| Table 2: Dataset statistics. |               |               |                 |                  |                                 |
|------------------------------|---------------|---------------|-----------------|------------------|---------------------------------|
| <b>Dataset</b>               | <b>#Nodes</b> | <b>#Edges</b> | <b>#Classes</b> | <b>#Features</b> | <b>Training/Validation/Test</b> |
| Cora                         | 2,708         | 5,429         | 7               | 1,433            | 140/500/1,000                   |
| Pubmed                       | 19,717        | 88,648        | 3               | 500              | 60/500/1,000                    |
| Citeseer                     | 3,327         | 4,732         | 6               | 3,703            | 120/500/1,000                   |
| Flickr                       | 89,250        | 899,756       | 7               | 500              | 44,625/22,312/22,313            |
| Ogbn-arxiv                   | 169,343       | 1,166,243     | 40              | 128              | 90,941/29,799/48,603            |

<span id="page-8-1"></span>

|            |                             |  | <b>Baselines</b>                                                                                                                                                                                                                                                                             |                                                                                                                            |                                                                    | Proposed |                                                                                                                     |
|------------|-----------------------------|--|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|----------|---------------------------------------------------------------------------------------------------------------------|
| Dataset    |                             |  | Ratio (r) Random Herding K-Center Coarsening GCOND-X GCOND GCDM-X GCDM<br>$(A', X') (A', X') (A', X') (A', X') (A', X') (A') (A', X')$                                                                                                                                                       |                                                                                                                            | (A')                                                               | (A', X') | Whole<br>Dataset                                                                                                    |
| Cora       | $1.3\%$<br>$2.6\%$<br>5.2%  |  | $63.6\pm3.767.0\pm1.364.0\pm2.3$ $31.2\pm0.2$ $75.9\pm1.2$ $79.8\pm1.3$ <b>81.3</b> $\pm$ 0.4 69.4 $\pm$ 1.3<br>$72.8 \pm 1.173.4 \pm 1.073.2 \pm 1.2$ 65.2 + 0.6 $75.7 \pm 0.9$ 80.1 + 0.6 <b>81.4</b> + 0.1 77.2 + 0.482.5 $\pm$ 1.2<br>$76.8 \pm 0.167.0 \pm 1.376.7 \pm 0.170.6 \pm 0.1$ | $76.0+0.9$ $79.3+0.3$ <b>82.5+0.3</b> $79.4+0.1$                                                                           |                                                                    |          |                                                                                                                     |
| Pubmed     | $0.3\%$                     |  | $0.08\%$ 69.4 $\pm$ 0.276.7 $\pm$ 0.764.5 $\pm$ 2.718.1 $\pm$ 0.1* 69.1 $\pm$ 1.1 76.5 $\pm$ 0.275.5 $\pm$ 0.375.7 $\pm$ 0.3<br>$77.8 \pm 0.378.0 \pm 0.578.2 \pm 0.442.8 \pm 4.1^*$ $71.7 \pm 0.9$ $77.9 \pm 0.4$ $77.2 \pm 0.2$ <b>78.3</b> $\pm$ 0.3                                      |                                                                                                                            |                                                                    |          | $0.15\%$ $73.3\pm0.776.2\pm0.569.4\pm0.728.7\pm4.1^*$ $73.2\pm0.7$ $77.1\pm0.5$ $75.7\pm0.3$ $77.3\pm0.179.3\pm0.2$ |
| Citeseer   | $0.9\%$<br>$1.8\%$<br>3.6%  |  | $54.4+4.457.1+1.552.4+2.8$ $52.2+0.4$<br>$64.2 \pm 1.766.7 \pm 1.064.3 \pm 1.059.0 \pm 0.5$<br>$69.1 \pm 0.169.0 \pm 0.169.1 \pm 0.165.3 \pm 0.5$                                                                                                                                            | <b>71.4</b> $\pm$ <b>0.8</b> 70.5 $\pm$ 1.2 69.0 $\pm$ 0.5 62.0 $\pm$ 0.1                                                  | $69.4 \pm 1.4$ $69.8 \pm 1.4$ <b>72.8</b> $\pm$ 0.6 69.8 $\pm$ 0.2 |          | $69.8 \pm 1.1$ $70.6 \pm 0.9$ $71.9 \pm 0.5$ $69.5 \pm 1.1$ $73.0 \pm 0.1$                                          |
| Flickr     | $0.1\%$<br>$0.5\%$<br>$1\%$ |  | $41.8 \pm 2.042.5 \pm 1.842.0 \pm 0.7$ $41.9 \pm 0.2$<br>$44.0 \pm 0.443.9 \pm 0.943.2 \pm 0.1$ $44.5 \pm 0.1$<br>$44.6 \pm 0.244.4 \pm 0.644.1 \pm 0.4$ $44.6 \pm 0.1$                                                                                                                      | $45.9 \pm 0.1$ $46.5 \pm 0.4$ $46.0 \pm 0.1$ $46.8 \pm 0.2$<br>$45.0 \pm 0.1$ $47.1 \pm 0.1$ $47.1 \pm 0.2$ $47.5 \pm 0.1$ |                                                                    |          | $45.0 \pm 0.2$ $47.1 \pm 0.1$ $47.4 \pm 0.3$ $47.9 \pm 0.35$ $0.2 \pm 0.3$                                          |
| Ogbn-arxiv | $0.5\%$                     |  | $0.25\%$ 57.3 $\pm$ 1.158.6 $\pm$ 1.256.8 $\pm$ 0.8 43.5 $\pm$ 0.2<br>$60.0+0.960.4+0.860.3+0.4$ 50.4+0.1                                                                                                                                                                                    | 63.1±0.5 64.0±0.4 62.5±0.1 62.4±0.1                                                                                        |                                                                    |          | 64.2±0.4 63.2±0.3 61.2±0.1 59.6±0.4 71.4 ± 0.1                                                                      |

Table 3: Comparison of GCDM Performance with Baselines (Non-Empirical MMD)

### 4.2 Test Accuracy Comparison

To evaluate whether the GNN trained on the condensed graph can perform well on the test set in the original graph, we report the test accuracy for each method in Table. [3.](#page-8-1) The results in this table are achieved by using a two-layer GCN to perform training and testing after the condensation procedure. For the GCOND benchmark [\[1\]](#page-10-0), the GNNs utlized during condensation vary according to the datasets, with the authors making the decisions; For GCDM, we use a two-layer GCN while condensing under the GCDM-X framework and a two-layer SGC while condensing under the GCDM framework. The latter SGC was chosen to conserve GPU memory and improve the speed of condensing.

Table. [3](#page-8-1) summarizes the node classification performance, from which the following observations are made:

|                        | Methods | Data  | GIN  | MLP  | APPNP | Cheby | GCN  | SAGE | SGC  | Avg. |
|------------------------|---------|-------|------|------|-------|-------|------|------|------|------|
| Flickr<br>r = 0.5%     | GCOND-X | X'    | 29.5 | 41.5 | 44.1  | 31.9  | 47.6 | 32.6 | 47.7 | 39.3 |
|                        | GCOND   | A',X' | 38.8 | 42.2 | 45.3  | 34.9  | 45.8 | 43.5 | 46.2 | 42.4 |
|                        | GCDM-X  | X'    | 42.2 | 42.3 | 42.4  | 42.4  | 47.4 | 42.6 | 45.8 | 43.6 |
|                        | GCDM    | A',X' | 42.5 | 43.9 | 45.9  | 43.2  | 47.8 | 42.9 | 49.4 | 45.1 |
| Ogbn-arxiv<br>r = 0.5% | GCOND-X | X'    | 59.9 | 47.6 | 55.2  | 47.4  | 62.9 | 59.3 | 64.7 | 56.7 |
|                        | GCOND   | A',X' | 7.2* | 46.4 | 55.1  | 44.7  | 64.7 | 37.8 | 64.8 | 47.8 |
|                        | GCDM-X  | X'    | 58.6 | 44.5 | 52.8  | 45.1  | 62.5 | 57.4 | 64.1 | 55.0 |
|                        | GCDM    | A',X' | 58.1 | 45.0 | 52.6  | 45.1  | 62.0 | 56.8 | 62.2 | 54.5 |

<span id="page-9-0"></span>Table 4: Graph generated by Receptive Field Distribution Matching using a single condense architecture could generalize to other architectures in evaluation, while the performance is comparable or superior to GCOND.

Obs 1. GCDM and GCDM-X achieve promising performance with high reduction rates. Deep learning based condensation methods like GCOND and GCDM outperform coreset selection methods by a large margin. Our methods achieve 81.3%, 81.4%, and 82.5% accuracies at 1.3%, 2.6% and 5.2% condensation ratios on Cora dataset, while the model trained on the whole graph achieves an accuracy of 82.5%. Our methods also show promising performance on the other four datasets. Additionally, for most settings, our framework outperforms the current SOTA framework: GCOND.

Obs 2. Learning  $X'$  solely can also result in good performance. Similar to what was reported in GCOND, we observed that GCDM-X often achieves close performance to GCDM. In some cases, GCDM-X even works better. The reason could be that  $X<sup>'</sup>$  has already encoded node features and structural information of the original graph during the condensation process, or that the node features are very informative in real-world datasets.

Obs 3. Larger sample size leads to improved performance. The authors of GCOND [\[1\]](#page-10-0) reported that increasing the size of the condensed graph doesn't necessarily improve performance. It is stated that once the condensation ratio reaches a certain value, the performance stops improving. In our experiments, we find the performance improves as the size of the condensed graph increases. This may attribute to the inherent property of distribution matching. Larger graph sizes allow for more sample points to mimic the distribution of the original graph. When the distribution of the original graph becomes more complicated, additional sample points may be required to approximate the distribution. It enables the synthetic graph generated by our method to encapsulate additional information from the original graph. Notably, our method under-performs GCOND on Ogbn-arxiv with the same condensation ratio. However, when we run a experiment with a 1% condensation rate, we obtain a test accuracy of 64.9%.

## 4.3 GENERALIZED PERFORMANCE

In this section, we determine whether or not a graph condensed using a particular GNN architecture can generalize to other GNN models during the evaluation phase. For the experiments in this section, we use GCN during the condensation procedure for both GCOND and GCDM. After the condensed graphs are obtained, we perform training and testing using various models including: GIN [\[3\]](#page-10-2), MLP [\[37\]](#page-12-2), APPNP [\[6\]](#page-10-5), Cheby [\[8\]](#page-10-7), GCN [\[30\]](#page-11-21), SAGE [\[7\]](#page-10-6), and SGC [\[38\]](#page-12-3). The test accuracies on the two largest datasets, Flickr and ogbn-Arxiv, are summarized in Table. [4.](#page-9-0) We can observe consistent improvement in comparison to GCOND, most notably when SAGE and GIN are used. One reason that GCDM outperforms GCOND in terms of generalization ability is that GCOND utilizes gradients during a single model training process, which may not translate well to another training process. It should be noted that the abnormal GIN performance in the table was obtained by execution of the original GCOND program.

## 4.4 SPEED PERFORMANCE

This section examines and compares the condensing speeds of GCOND and GCDM. The overall condensation algorithm of GCOND (see Algorithm 1 in [\[1\]](#page-10-0)) has a very similar structure to GCDM (Algorithm [1\)](#page-7-0) except that their condensation losses are different. Therefore, we report the time required to run 50 epochs ( $M = 50$  in Algorithm [1\)](#page-7-0) on the ogbn-arxiv dataset for both GCDM and GCOND in Table. [7.](#page-10-8) Note that to achieve the performances reported in Table. [3,](#page-8-1) GCOND needs to run for around 200 epochs, and GCDM runs for less than 150 epochs. As the size of the condensed graph increases, the time required for GCOND increases considerably faster than that of GCDM.

Table 5: Some condensed graph could produce more visually clustered results compared to GCOND. NPC: Node Per Class.

Image /page/10/Figure/2 description: The image displays a series of five scatter plots arranged horizontally. Each scatter plot contains multiple colored dots, with the density and distribution of dots varying across the plots. The first plot shows a few distinct clusters of dots in purple, teal, and yellow. The second plot also shows distinct clusters, but with more dots and slightly more spread. The third plot features a larger, more dispersed collection of dots in purple, teal, and yellow, with some appearing to form loose groupings. The fourth plot shows a very sparse distribution with only a few dots in purple and yellow. The fifth plot presents a dense, somewhat amorphous cloud of dots in purple, teal, and yellow, indicating a high degree of overlap and mixing.

(a) Cora 10 NPC (b) Citeseer 10 NPC (c) Flickr 30 NPC (d) Pubmed 10 NPC (e) ogbn-Arxiv 20 NPC

Table 6: Some condensed graph could produce more visually clustered results compared to GCOND. NPC: Node Per Class.

<span id="page-10-8"></span>Table 7: Running time comparison between GCDM and GCOND on a single RTX8000 GPU (50 epochs).

<span id="page-10-9"></span>

| <b>Dataset</b> | Methods                               | $0.1\%$ | $0.5\%$                                    | $1\%$ |  |
|----------------|---------------------------------------|---------|--------------------------------------------|-------|--|
| Ogbn-arxiv     | GCOND (50 epochs)<br>GCDM (50 epochs) | 981.8s  | 1083.3s 2028.5s 5960.4s<br>1125.6s 1781.8s |       |  |

### 4.5 VISUALIZATION OF CONDENSED GRAPHS

We utilize T-SNE [\[39\]](#page-12-4) to embed the the condensed graph's node features in two dimensions and present the resulting visualizations in Table. [6.](#page-10-9) As shown, the condensed node features occasionally exhibit a more clustered pattern compared to the visualizations of GCOND [\[1\]](#page-10-0), as demonstrated by the condensed nodes for Cora, CiteSeer, and Pubmed. While the patterns of other datasets such as Flickr and Ogbn-arxiv are not as immediately discernible, similar to the visualizations presented by GCOND.

# 5 CONCLUSION

In this paper, we investigate a novel methodology called GCDM for generating a small synthetic graph from a large and complex graph. The GCDM framework is implemented by optimizing the synthetic graph using a distribution matching loss measured by maximum mean discrepancy (MMD). Through the proposed framework, we are able to obtain comparable evaluation performance to the original graph. The synthetic graph is also generalizable to a variety of downstream models. This enables neural architecture search and hyperparameter tuning to be done in a highly efficient way. Additionally, the improvement in condensation speed is demonstrated, allowing greater flexibility in a model-retraining setting.

# References

- <span id="page-10-0"></span>[1] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks, 2021.
- <span id="page-10-1"></span>[2] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching, 2021.
- <span id="page-10-2"></span>[3] Jialei Chen, Yujia Xie, Kan Wang, Zih Huei Wang, Geet Lahoti, Chuck Zhang, Mani A. Vannan, Ben Wang, and Zhen Qian. Generative invertible networks (gin): Pathophysiology-interpretable feature mapping and virtual patient generation. *Lecture Notes in Computer Science*, page 537–545, 2018.
- <span id="page-10-3"></span>[4] Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, and Philip S. Yu. A comprehensive survey on graph neural networks. *IEEE Transactions on Neural Networks and Learning Systems*, 32(1):4–24, Jan 2021.
- <span id="page-10-4"></span>[5] Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks, 2017.
- <span id="page-10-5"></span>[6] Johannes Klicpera, Aleksandar Bojchevski, and Stephan Günnemann. Predict then propagate: Graph neural networks meet personalized pagerank, 2019.
- <span id="page-10-6"></span>[7] William L. Hamilton, Rex Ying, and Jure Leskovec. Inductive representation learning on large graphs, 2018.
- <span id="page-10-7"></span>[8] Shanshan Tang, Bo Li, and Haijun Yu. Chebnet: Efficient and stable constructions of deep neural networks with rectified power units using chebyshev approximations, 2019.

- <span id="page-11-0"></span>[9] Shoujin Wang, Liang Hu, Yan Wang, Xiangnan He, Quan Z. Sheng, Mehmet A. Orgun, Longbing Cao, Francesco Ricci, and Philip S. Yu. Graph learning based recommender systems: A review, 2021.
- <span id="page-11-1"></span>[10] Usman Nazir, He Wang, and Murtaza Taj. Survey of image based graph neural networks, 2021.
- <span id="page-11-2"></span>[11] Lingfei Wu, Yu Chen, Kai Shen, Xiaojie Guo, Hanning Gao, Shucheng Li, Jian Pei, and Bo Long. Graph neural networks for natural language processing: A survey, 2021.
- <span id="page-11-3"></span>[12] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning, 2019.
- <span id="page-11-4"></span>[13] Yutian Chen, Max Welling, and Alexander J. Smola. Super-samples from kernel herding. *CoRR*, abs/1203.3472, 2012.
- <span id="page-11-5"></span>[14] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H. Lampert. icarl: Incremental classifier and representation learning, 2017.
- <span id="page-11-6"></span>[15] Eden Belouadah and Adrian Popescu. Scail: Classifier weights scaling for class incremental learning, 2020.
- <span id="page-11-7"></span>[16] Trevor Campbell and Tamara Broderick. Automated scalable bayesian inference via hilbert coresets, 2019.
- <span id="page-11-8"></span>[17] Zalán Borsos, Mojmír Mutný, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming, 2020.
- <span id="page-11-9"></span>[18] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In Marina Meila and Tong Zhang, editors, *Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18-24 July 2021, Virtual Event*, volume 139 of *Proceedings of Machine Learning Research*, pages 12674–12685. PMLR, 2021.
- <span id="page-11-10"></span>[19] Andreas Loukas. Graph reduction with spectral and cut guarantees, 2018.
- <span id="page-11-11"></span>[20] Daniel A. Spielman and Shang-Hua Teng. Spectral sparsification of graphs, 2010.
- <span id="page-11-12"></span>[21] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation, 2020.
- <span id="page-11-13"></span>[22] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-11-14"></span>[23] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching, 2021.
- <span id="page-11-15"></span>[24] Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks. In *5th International Conference on Learning Representations, ICLR 2017, Toulon, France, April 24-26, 2017, Conference Track Proceedings*. OpenReview.net, 2017.
- <span id="page-11-16"></span>[25] Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, and Philip S. Yu. A comprehensive survey on graph neural networks. *CoRR*, abs/1901.00596, 2019.
- <span id="page-11-17"></span>[26] Qimai Li, Zhichao Han, and Xiao-Ming Wu. Deeper insights into graph convolutional networks for semisupervised learning. In Sheila A. McIlraith and Kilian Q. Weinberger, editors, *Proceedings of the Thirty-Second AAAI Conference on Artificial Intelligence, (AAAI-18), the 30th innovative Applications of Artificial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational Advances in Artificial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7, 2018*, pages 3538–3545. AAAI Press, 2018.
- <span id="page-11-18"></span>[27] Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-11-19"></span>[28] Guolei Sun and Xiangliang Zhang. Graph embedding with rich information through heterogeneous network, 2018.
- <span id="page-11-20"></span>[29] Jian Xu, Sunkyu Kim, Min Song, Minbyul Jeong, Donghyeon Kim, Jaewoo Kang, Justin F. Rousseau, Xin Li, Weijia Xu, Vetle I. Torvik, Yi Bu, Chongyan Chen, Islam Akef Ebeid, Daifeng Li, and Ying Ding. Building a pubmed knowledge graph, 2020.
- <span id="page-11-21"></span>[30] Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks. *CoRR*, abs/1609.02907, 2016.
- <span id="page-11-22"></span>[31] Weihua Hu, Matthias Fey, Marinka Zitnik, Yuxiao Dong, Hongyu Ren, Bowen Liu, Michele Catasta, and Jure Leskovec. Open graph benchmark: Datasets for machine learning on graphs, 2021.
- <span id="page-11-23"></span>[32] Hanqing Zeng, Hongkuan Zhou, Ajitesh Srivastava, Rajgopal Kannan, and Viktor Prasanna. Graphsaint: Graph sampling based inductive learning method, 2020.
- <span id="page-11-24"></span>[33] Zengfeng Huang, Shengzhong Zhang, Chong Xi, Tang Liu, and Min Zhou. Scaling up graph neural networks via graph coarsening, 2021.
- <span id="page-11-25"></span>[34] Max Welling. Herding dynamical weights to learn: Proceedings of the 26th annual international conference on machine learning, Jun 2009.

- <span id="page-12-0"></span>[35] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach, 2018.
- <span id="page-12-1"></span>[36] Zengfeng Huang, Shengzhong Zhang, Chong Xi, Tang Liu, and Min Zhou. Scaling up graph neural networks via graph coarsening. *CoRR*, abs/2106.05150, 2021.
- <span id="page-12-2"></span>[37] Yang Hu, Haoxuan You, Zhecan Wang, Zhicheng Wang, Erjin Zhou, and Yue Gao. Graph-mlp: Node classification without message passing in graph, 2021.
- <span id="page-12-3"></span>[38] Felix Wu, Tianyi Zhang, Amauri Holanda de Souza Jr. au2, Christopher Fifty, Tao Yu, and Kilian Q. Weinberger. Simplifying graph convolutional networks, 2019.
- <span id="page-12-4"></span>[39] Laurens van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of Machine Learning Research*, 9(86):2579–2605, 2008.