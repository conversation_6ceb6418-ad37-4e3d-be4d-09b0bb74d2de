{"table_of_contents": [{"title": "GRAPH CONDENSATION VIA RECEPTIVE FIELD DISTRIBUTION\nMATCHING", "heading_level": null, "page_id": 0, "polygon": [[73.6611328125, 97.5], [536.09765625, 97.5], [536.09765625, 135.3515625], [73.6611328125, 135.3515625]]}, {"title": "A PREPRINT", "heading_level": null, "page_id": 0, "polygon": [[278.25, 159.0], [332.25, 159.0], [332.25, 169.2861328125], [278.25, 169.2861328125]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 306.75], [334.986328125, 306.75], [334.986328125, 317.8828125], [276.75, 317.8828125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.336669921875, 456.75], [156.0, 456.75], [156.0, 468.703125], [70.336669921875, 468.703125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[70.5, 392.326171875], [162.263671875, 392.326171875], [162.263671875, 404.89453125], [70.5, 404.89453125]]}, {"title": "2.1 Graph Neural Networks.", "heading_level": null, "page_id": 2, "polygon": [[70.5, 416.8828125], [201.0, 416.8828125], [201.0, 428.484375], [70.5, 428.484375]]}, {"title": "2.2 Coreset & Data Condensation", "heading_level": null, "page_id": 2, "polygon": [[70.5, 494.25], [222.75, 494.25], [222.75, 505.44140625], [70.5, 505.44140625]]}, {"title": "2.3 Graph Coarsening & Graph Sparsification.", "heading_level": null, "page_id": 2, "polygon": [[70.5, 625.7109375], [280.5, 625.7109375], [280.5, 636.5390625], [70.5, 636.5390625]]}, {"title": "2.4 Data Condensation & Graph Condensation.", "heading_level": null, "page_id": 3, "polygon": [[70.5, 108.0], [282.0, 108.0], [282.0, 118.142578125], [70.5, 118.142578125]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 3, "polygon": [[70.5, 231.75], [157.5, 231.75], [157.5, 243.24609375], [70.5, 243.24609375]]}, {"title": "3.1 Graph Condensation Problem", "heading_level": null, "page_id": 3, "polygon": [[70.5, 300.0], [222.92578125, 300.0], [222.92578125, 310.1484375], [70.5, 310.1484375]]}, {"title": "3.2 Viewing a Graph as a Distribution", "heading_level": null, "page_id": 4, "polygon": [[70.5, 153.0], [241.453125, 153.0], [241.453125, 163.1953125], [70.5, 163.1953125]]}, {"title": "3.2.1 Node classification by GNN", "heading_level": null, "page_id": 4, "polygon": [[70.5, 207.75], [219.0, 207.75], [219.0, 217.916015625], [70.5, 217.916015625]]}, {"title": "3.2.2 Receptive Fields of GNN", "heading_level": null, "page_id": 4, "polygon": [[70.5, 400.5], [207.5361328125, 400.5], [207.5361328125, 411.08203125], [70.5, 411.08203125]]}, {"title": "3.2.3 Viewing a Graph as a Distribution of Receptive Fields", "heading_level": null, "page_id": 5, "polygon": [[70.5, 73.7666015625], [331.400390625, 73.7666015625], [331.400390625, 83.2412109375], [70.5, 83.2412109375]]}, {"title": "3.2.4 Some Remarks.", "heading_level": null, "page_id": 5, "polygon": [[70.5, 248.25], [169.5, 248.25], [169.5, 258.71484375], [70.5, 258.71484375]]}, {"title": "3.3 GCDM: Graph Condensation via Receptive Field Distribution Matching", "heading_level": null, "page_id": 5, "polygon": [[70.5, 384.75], [402.75, 384.75], [402.75, 394.646484375], [70.5, 394.646484375]]}, {"title": "3.3.1 Synthesize the Labels Y'", "heading_level": null, "page_id": 5, "polygon": [[71.34521484375, 439.06939697265625], [205.99737548828125, 439.06939697265625], [205.99737548828125, 450.5421142578125], [71.34521484375, 450.5421142578125]]}, {"title": "3.3.2 Synthesize A' and X'.", "heading_level": null, "page_id": 5, "polygon": [[70.89697265625, 652.7953796386719], [194.83961486816406, 652.7953796386719], [194.83961486816406, 664.3828125], [70.89697265625, 664.3828125]]}, {"title": "3.4 Parameterization and Algorithm", "heading_level": null, "page_id": 6, "polygon": [[70.5, 228.75], [234.75, 228.75], [234.75, 238.9921875], [70.5, 238.9921875]]}, {"title": "3.4.1 Defining H by GNNs", "heading_level": null, "page_id": 6, "polygon": [[70.5, 306.0], [192.0, 306.0], [192.0, 316.142578125], [70.5, 316.142578125]]}, {"title": "3.4.2 Parametrize A' by MLP", "heading_level": null, "page_id": 6, "polygon": [[70.5, 371.25], [206.3408203125, 371.25], [206.3408203125, 381.111328125], [70.5, 381.111328125]]}, {"title": "3.4.3 Condensation Loss in GCDM", "heading_level": null, "page_id": 6, "polygon": [[70.5, 486.0], [228.005859375, 486.0], [228.005859375, 495.7734375], [70.5, 495.7734375]]}, {"title": "3.4.4 Algorithm", "heading_level": null, "page_id": 6, "polygon": [[70.5, 670.5], [147.4716796875, 670.5], [147.4716796875, 681.3984375], [70.5, 681.3984375]]}, {"title": "Algorithm 1: GCDM for Graph Condensation", "heading_level": null, "page_id": 7, "polygon": [[70.5, 75.75], [258.78515625, 76.5], [258.78515625, 87.01171875], [70.5, 87.01171875]]}, {"title": "3.4.5 A \"Graphless\" Variant: GCDM-X", "heading_level": null, "page_id": 7, "polygon": [[70.5, 455.25], [249.0, 455.25], [249.0, 465.99609375], [70.5, 465.99609375]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 7, "polygon": [[70.5, 641.1796875], [178.998046875, 641.1796875], [178.998046875, 652.78125], [70.5, 652.78125]]}, {"title": "4.1 EXPERIMENTAL SETTINGS", "heading_level": null, "page_id": 8, "polygon": [[70.5, 72.65478515625], [228.7529296875, 72.65478515625], [228.7529296875, 83.77294921875], [70.5, 83.77294921875]]}, {"title": "4.2 Test Accuracy Comparison", "heading_level": null, "page_id": 8, "polygon": [[70.5, 620.25], [211.7197265625, 620.25], [211.7197265625, 631.125], [70.5, 631.125]]}, {"title": "4.3 GENERALIZED PERFORMANCE", "heading_level": null, "page_id": 9, "polygon": [[70.5, 486.4921875], [250.716796875, 486.4921875], [250.716796875, 498.8671875], [70.5, 498.8671875]]}, {"title": "4.4 SPEED PERFORMANCE", "heading_level": null, "page_id": 9, "polygon": [[70.5, 631.8984375], [210.076171875, 631.8984375], [210.076171875, 643.5], [70.5, 643.5]]}, {"title": "4.5 VISUALIZATION OF CONDENSED GRAPHS", "heading_level": null, "page_id": 10, "polygon": [[70.5, 298.5], [300.919921875, 298.5], [300.919921875, 309.375], [70.5, 309.375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 10, "polygon": [[70.5, 390.5859375], [171.75, 390.5859375], [171.75, 402.9609375], [70.5, 402.9609375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[70.5, 508.53515625], [129.0, 508.53515625], [129.0, 520.13671875], [70.5, 520.13671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 46], ["Text", 11], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 37], ["Text", 4], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 50], ["SectionHeader", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 709, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 483], ["Line", 68], ["Text", 5], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 40], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 676], ["Line", 61], ["Text", 9], ["Equation", 6], ["SectionHeader", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 635], ["Line", 128], ["Equation", 6], ["Text", 5], ["SectionHeader", 5], ["TextInlineMath", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 807], ["Line", 55], ["TableCell", 30], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["TableCell", 130], ["Line", 54], ["Text", 5], ["SectionHeader", 2], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1760, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 191], ["Span", 182], ["Line", 53], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2146, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 44], ["TableCell", 10], ["Reference", 10], ["ListItem", 8], ["Text", 4], ["SectionHeader", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 664, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 53], ["ListItem", 26], ["Reference", 26], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 44], ["Line", 11], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Graph_Condensation_via_Receptive_Field_Distribution_Matching"}