{"table_of_contents": [{"title": "Graph Data Condensation via Self-expressive Graph Structure\nReconstruction", "heading_level": null, "page_id": 0, "polygon": [[61.5, 82.5], [549.24609375, 81.0], [549.24609375, 118.916015625], [61.5, 118.916015625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[52.5, 185.25], [96.22265625, 185.25], [96.22265625, 196.83984375], [52.5, 196.83984375]]}, {"title": "CCS Concepts", "heading_level": null, "page_id": 0, "polygon": [[52.5, 440.25], [123.75, 440.25], [123.75, 451.30078125], [52.5, 451.30078125]]}, {"title": "Keywords", "heading_level": null, "page_id": 0, "polygon": [[52.5, 477.0], [103.5, 477.0], [103.5, 487.65234375], [52.5, 487.65234375]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[52.5, 517.5], [141.75, 517.5], [141.75, 527.484375], [52.5, 527.484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[314.666015625, 185.25], [398.25, 185.25], [398.25, 198.0], [314.666015625, 198.0]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[52.5, 641.25], [139.5, 641.25], [139.5, 651.62109375], [52.5, 651.62109375]]}, {"title": "3 Preliminary", "heading_level": null, "page_id": 1, "polygon": [[316.5, 564.22265625], [395.6484375, 564.22265625], [395.6484375, 574.27734375], [316.5, 574.27734375]]}, {"title": "4 Method", "heading_level": null, "page_id": 2, "polygon": [[52.5, 163.5], [108.75, 163.5], [108.75, 173.3466796875], [52.5, 173.3466796875]]}, {"title": "4.1 Initialization", "heading_level": null, "page_id": 2, "polygon": [[52.5, 319.5], [144.10986328125, 319.5], [144.10986328125, 330.2578125], [52.5, 330.2578125]]}, {"title": "4.1.1 Node Initialization.", "heading_level": null, "page_id": 2, "polygon": [[52.5, 334.5], [146.25, 334.5], [146.25, 343.986328125], [52.5, 343.986328125]]}, {"title": "4.1.2 Regularizer Initialization.", "heading_level": null, "page_id": 2, "polygon": [[316.7578125, 207.0], [434.25, 207.0], [434.25, 216.369140625], [316.7578125, 216.369140625]]}, {"title": "4.2 Self-expressive Reconstruction", "heading_level": null, "page_id": 2, "polygon": [[317.25, 610.5], [495.75, 610.5], [495.75, 620.68359375], [317.25, 620.68359375]]}, {"title": "4.3 Update", "heading_level": null, "page_id": 4, "polygon": [[52.5, 410.25], [114.75, 410.25], [114.75, 420.75], [52.5, 420.75]]}, {"title": "5 Experiment", "heading_level": null, "page_id": 4, "polygon": [[317.25, 457.5], [394.5, 457.5], [394.5, 467.54296875], [317.25, 467.54296875]]}, {"title": "5.1 Experimental Settings", "heading_level": null, "page_id": 4, "polygon": [[316.7578125, 472.5], [453.0234375, 472.5], [453.0234375, 483.78515625], [316.7578125, 483.78515625]]}, {"title": "5.2 Overall Performance", "heading_level": null, "page_id": 5, "polygon": [[52.5, 531.0], [182.25, 531.0], [182.25, 542.1796875], [52.5, 542.1796875]]}, {"title": "5.3 Cross Architecture Performance", "heading_level": null, "page_id": 5, "polygon": [[317.25, 552.0], [503.25, 552.0], [503.25, 562.2890625], [317.25, 562.2890625]]}, {"title": "5.4 Learned Graph Structure Visualization", "heading_level": null, "page_id": 6, "polygon": [[317.25, 258.75], [534.0, 258.75], [534.0, 270.31640625], [317.25, 270.31640625]]}, {"title": "5.5 Learned Node Feature Visualization", "heading_level": null, "page_id": 6, "polygon": [[316.5, 609.46875], [519.75, 608.25], [519.75, 619.5234375], [316.5, 619.5234375]]}, {"title": "5.6 Node Initialization", "heading_level": null, "page_id": 7, "polygon": [[317.25, 238.5], [436.5, 238.5], [436.5, 249.8203125], [317.25, 249.8203125]]}, {"title": "5.7 Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[316.458984375, 473.25], [417.0, 473.25], [417.0, 484.171875], [316.458984375, 484.171875]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[316.5, 674.25], [391.5, 674.25], [391.5, 684.4921875], [316.5, 684.4921875]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 8, "polygon": [[52.5, 193.5], [147.75, 193.5], [147.75, 204.9609375], [52.5, 204.9609375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[52.5, 295.453125], [108.25048828125, 295.453125], [108.25048828125, 304.734375], [52.5, 304.734375]]}, {"title": "A The Effectiveness of Message Passing\nInitialization", "heading_level": null, "page_id": 10, "polygon": [[316.16015625, 84.75], [522.3515625, 84.75], [522.3515625, 107.89453125], [316.16015625, 107.89453125]]}, {"title": "B Graph Regularizer Update Analysis", "heading_level": null, "page_id": 10, "polygon": [[316.5, 295.5], [511.5, 295.5], [511.5, 306.087890625], [316.5, 306.087890625]]}, {"title": "C Graph Reconstruction Analysis", "heading_level": null, "page_id": 10, "polygon": [[316.5, 494.25], [492.0, 494.25], [492.0, 505.44140625], [316.5, 505.44140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 398], ["Line", 115], ["Text", 14], ["SectionHeader", 6], ["Footnote", 2], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 118], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 744], ["Line", 146], ["TextInlineMath", 8], ["Equation", 7], ["Text", 6], ["SectionHeader", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1018], ["Line", 142], ["ListItem", 18], ["Text", 7], ["TextInlineMath", 5], ["Equation", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 824], ["Line", 123], ["TableCell", 84], ["Text", 11], ["Equation", 5], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["Caption", 2], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 812], ["TableCell", 145], ["Line", 85], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 6193, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["TableCell", 373], ["Line", 225], ["Text", 7], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11511, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 111], ["TableCell", 49], ["Text", 5], ["Caption", 4], ["Reference", 4], ["Figure", 3], ["SectionHeader", 3], ["FigureGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 6492, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 497], ["Line", 147], ["ListItem", 41], ["Reference", 40], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 474], ["Line", 121], ["ListItem", 41], ["Reference", 41], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 596, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 615], ["TableCell", 140], ["Line", 74], ["Reference", 8], ["Caption", 7], ["Table", 7], ["TableGroup", 6], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 2, "llm_tokens_used": 13694, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Graph_Data_Condensation_via_Self-expressive_Graph_Structure_Reconstruction"}