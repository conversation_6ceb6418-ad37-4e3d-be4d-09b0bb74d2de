{"table_of_contents": [{"title": "Graph Distillation with Eigenbasis Matching", "heading_level": null, "page_id": 0, "polygon": [[159.75, 89.25], [435.990234375, 89.25], [435.990234375, 104.02734375], [159.75, 104.02734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 176.25], [195.75, 176.25], [195.75, 187.9453125], [148.5, 187.9453125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 584.25], [132.75, 584.25], [132.75, 595.93359375], [54.0, 595.93359375]]}, {"title": "2. Preliminary", "heading_level": null, "page_id": 1, "polygon": [[305.8505859375, 216.0], [381.75, 216.0], [381.75, 227.00390625], [305.8505859375, 227.00390625]]}, {"title": "3. <PERSON> in Gradient Matching", "heading_level": null, "page_id": 1, "polygon": [[306.0, 613.5], [510.0, 613.5], [510.0, 624.1640625], [306.0, 624.1640625]]}, {"title": "4. The Proposed Method: GDEM", "heading_level": null, "page_id": 2, "polygon": [[54.0, 603.0], [226.5, 603.0], [226.5, 613.72265625], [54.0, 613.72265625]]}, {"title": "4.1. <PERSON><PERSON><PERSON><PERSON><PERSON> Matching", "heading_level": null, "page_id": 2, "polygon": [[306.0, 354.75], [414.7734375, 354.75], [414.7734375, 365.0625], [306.0, 365.0625]]}, {"title": "4.2. Discrimination Constraint", "heading_level": null, "page_id": 3, "polygon": [[54.0, 150.0], [186.0, 150.0], [186.0, 160.1982421875], [54.0, 160.1982421875]]}, {"title": "4.3. Final Objective and Synthetic Graph Construction", "heading_level": null, "page_id": 3, "polygon": [[54.0, 593.2265625], [289.5, 593.2265625], [289.5, 602.5078125], [54.0, 602.5078125]]}, {"title": "4.4. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 3, "polygon": [[306.0, 425.25], [370.845703125, 425.25], [370.845703125, 435.4453125], [306.0, 435.4453125]]}, {"title": "Algorithm 1 GDEM for Graph Distillation", "heading_level": null, "page_id": 3, "polygon": [[306.0, 68.25], [479.25, 68.25], [479.25, 77.77880859375], [306.0, 77.77880859375]]}, {"title": "5. Theoretical Analysis", "heading_level": null, "page_id": 4, "polygon": [[54.0, 67.5], [172.5, 67.5], [172.5, 79.3740234375], [54.0, 79.3740234375]]}, {"title": "6. Experiments", "heading_level": null, "page_id": 4, "polygon": [[305.25, 136.5], [385.5, 136.5], [385.5, 147.0498046875], [305.25, 147.0498046875]]}, {"title": "6.1. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[306.0, 201.0], [410.58984375, 201.0], [410.58984375, 210.955078125], [306.0, 210.955078125]]}, {"title": "6.2. Node Classification", "heading_level": null, "page_id": 5, "polygon": [[54.0, 414.5625], [155.25, 414.5625], [155.25, 424.6171875], [54.0, 424.6171875]]}, {"title": "6.3. Cross-architecture Generalization", "heading_level": null, "page_id": 5, "polygon": [[305.25, 476.25], [471.0, 476.25], [471.0, 486.10546875], [305.25, 486.10546875]]}, {"title": "6.4. Optimal Performance and Time Overhead", "heading_level": null, "page_id": 6, "polygon": [[54.0, 562.5], [254.25, 562.5], [254.25, 572.34375], [54.0, 572.34375]]}, {"title": "6.5. Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[54.0, 295.5], [137.25, 295.5], [137.25, 305.12109375], [54.0, 305.12109375]]}, {"title": "6.6. Visualization", "heading_level": null, "page_id": 7, "polygon": [[54.0, 604.5], [129.0, 604.5], [129.0, 614.49609375], [54.0, 614.49609375]]}, {"title": "7. Related Work", "heading_level": null, "page_id": 7, "polygon": [[306.0, 405.75], [391.5, 405.75], [391.5, 416.8828125], [306.0, 416.8828125]]}, {"title": "8. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[54.0, 288.0], [125.25, 288.0], [125.25, 299.126953125], [54.0, 299.126953125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[54.0, 443.953125], [155.25, 443.953125], [155.25, 454.78125], [54.0, 454.78125]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 515.8828125], [147.75, 515.8828125], [147.75, 526.7109375], [54.0, 526.7109375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 611.25], [111.75, 611.25], [111.75, 622.23046875], [54.0, 622.23046875]]}, {"title": "A. Experimental Details", "heading_level": null, "page_id": 10, "polygon": [[54.0, 68.25], [180.193359375, 68.25], [180.193359375, 78.84228515625], [54.0, 78.84228515625]]}, {"title": "A.1. Visualization of Synthetic Graphs", "heading_level": null, "page_id": 10, "polygon": [[54.0, 87.75], [219.33984375, 87.75], [219.33984375, 98.95166015625], [54.0, 98.95166015625]]}, {"title": "A.2. Cross-architecture Performance of GCOND and SGDD", "heading_level": null, "page_id": 10, "polygon": [[54.0, 320.25], [310.5, 320.25], [310.5, 331.03125], [54.0, 331.03125]]}, {"title": "A.3. Implementation Details of GDEM", "heading_level": null, "page_id": 10, "polygon": [[54.0, 652.5], [219.75, 652.5], [219.75, 662.8359375], [54.0, 662.8359375]]}, {"title": "A.4. Complexity of Different Methods", "heading_level": null, "page_id": 11, "polygon": [[54.0, 222.0], [216.75, 222.0], [216.75, 232.03125], [54.0, 232.03125]]}, {"title": "Complexity of GDEM.", "heading_level": null, "page_id": 11, "polygon": [[54.0, 303.0], [152.701171875, 303.0], [152.701171875, 312.85546875], [54.0, 312.85546875]]}, {"title": "Complexity of GCOND.", "heading_level": null, "page_id": 11, "polygon": [[54.0, 432.0], [159.0, 432.0], [159.0, 442.40625], [54.0, 442.40625]]}, {"title": "Complexity of SGDD.", "heading_level": null, "page_id": 11, "polygon": [[54.0, 524.25], [149.5634765625, 524.25], [149.5634765625, 534.4453125], [54.0, 534.4453125]]}, {"title": "Complexity of SFGC.", "heading_level": null, "page_id": 11, "polygon": [[54.0, 628.5], [148.6669921875, 628.5], [148.6669921875, 639.24609375], [54.0, 639.24609375]]}, {"title": "A.5. Statistics of Datasets", "heading_level": null, "page_id": 12, "polygon": [[54.0, 187.5], [165.251953125, 187.5], [165.251953125, 198.7734375], [54.0, 198.7734375]]}, {"title": "A.6. Baselines", "heading_level": null, "page_id": 12, "polygon": [[54.0, 420.75], [115.1982421875, 420.75], [115.1982421875, 431.578125], [54.0, 431.578125]]}, {"title": "A.7. Evaluation Details", "heading_level": null, "page_id": 12, "polygon": [[54.0, 512.25], [154.0458984375, 512.25], [154.0458984375, 523.6171875], [54.0, 523.6171875]]}, {"title": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 12, "polygon": [[54.0, 664.3828125], [147.322265625, 664.3828125], [147.322265625, 675.2109375], [54.0, 675.2109375]]}, {"title": "A.9. Analysis of the Worse Performance on Obgn-arxiv", "heading_level": null, "page_id": 13, "polygon": [[54.0, 507.75], [289.5, 507.75], [289.5, 518.25], [54.0, 518.25]]}, {"title": "B. Theoretical Analysis of RSS for Gradient Matching", "heading_level": null, "page_id": 14, "polygon": [[54.0, 67.5], [331.69921875, 67.5], [331.69921875, 79.3740234375], [54.0, 79.3740234375]]}, {"title": "C. G<PERSON> Distiilation", "heading_level": null, "page_id": 14, "polygon": [[54.0, 355.78125], [163.5, 355.78125], [163.5, 366.609375], [54.0, 366.609375]]}, {"title": "<PERSON>. General <PERSON>s", "heading_level": null, "page_id": 14, "polygon": [[54.0, 686.25], [155.25, 686.25], [155.25, 698.02734375], [54.0, 698.02734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["Line", 91], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8389, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 703], ["Line", 111], ["TableCell", 84], ["Text", 4], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4088, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 921], ["Line", 157], ["TextInlineMath", 9], ["Reference", 7], ["Text", 5], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 937], ["Line", 158], ["Text", 10], ["Equation", 5], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1017], ["Line", 212], ["Text", 8], ["TextInlineMath", 7], ["Equation", 4], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 111], ["TableCell", 99], ["Text", 7], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 427], ["Span", 328], ["Line", 85], ["Table", 3], ["Caption", 3], ["Text", 3], ["TableGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4511, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 663], ["Line", 155], ["TableCell", 26], ["Text", 6], ["Reference", 5], ["Caption", 4], ["Figure", 3], ["SectionHeader", 3], ["FigureGroup", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 2787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 92], ["ListItem", 16], ["Reference", 16], ["Text", 5], ["SectionHeader", 4], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 91], ["ListItem", 30], ["Reference", 30], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["TableCell", 232], ["Line", 69], ["Text", 7], ["SectionHeader", 4], ["Reference", 4], ["TextInlineMath", 2], ["Equation", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5889, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 755], ["Line", 63], ["TextInlineMath", 12], ["Text", 6], ["SectionHeader", 5], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["TableCell", 158], ["Line", 52], ["Text", 7], ["Reference", 5], ["SectionHeader", 4], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9593, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 539], ["Span", 233], ["Line", 61], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["SectionHeader", 1], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 13884, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 931], ["Line", 223], ["Text", 6], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6511, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 55], ["Line", 15], ["ListItem", 11], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Graph_Distillation_with_Eigenbasis_Matching"}