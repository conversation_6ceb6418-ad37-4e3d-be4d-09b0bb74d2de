{"table_of_contents": [{"title": "GROUP DISTRIBUTIONALLY ROBUST DATASET DISTIL-\nLATION WITH RISK MINIMIZATION", "heading_level": null, "page_id": 0, "polygon": [[106.3828125, 81.0], [506.25, 81.0], [506.25, 116.015625], [106.3828125, 116.015625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 222.0], [334.5, 222.0], [334.5, 232.8046875], [276.75, 232.8046875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 476.25], [206.3408203125, 476.25], [206.3408203125, 487.65234375], [107.25, 487.65234375]]}, {"title": "2 ALGORITHM", "heading_level": null, "page_id": 2, "polygon": [[107.25, 165.75], [191.84765625, 165.75], [191.84765625, 176.8271484375], [107.25, 176.8271484375]]}, {"title": "Algorithm 1 Robust Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[106.8310546875, 83.25], [270.2900390625, 83.25], [270.2900390625, 93.1025390625], [106.8310546875, 93.1025390625]]}, {"title": "3 ANALYSIS", "heading_level": null, "page_id": 3, "polygon": [[107.25, 470.25], [179.25, 470.25], [179.25, 479.91796875], [107.25, 479.91796875]]}, {"title": "3.1 <PERSON><PERSON><PERSON> DEVIATIONS AND SOLVING THE BILEVEL DRO", "heading_level": null, "page_id": 4, "polygon": [[106.5, 690.0], [365.25, 690.0], [365.25, 700.34765625], [106.5, 700.34765625]]}, {"title": "3.2 LARGE DEVIATIONS AND DISTRIBUTIONALLY ROBUST OPTIMIZATION", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 279.75], [432.0, 279.75], [432.0, 289.072265625], [106.98046875, 289.072265625]]}, {"title": "3.3 DISTRIBUT<PERSON>ALLY ROBUST OPTI<PERSON><PERSON><PERSON><PERSON> AND SUBGROUP COVERAGE", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 501.75], [444.0, 501.75], [444.0, 511.62890625], [106.98046875, 511.62890625]]}, {"title": "4 NUMERICAL RESULTS", "heading_level": null, "page_id": 6, "polygon": [[107.20458984375, 446.25], [240.75, 446.25], [240.75, 458.26171875], [107.20458984375, 458.26171875]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 471.41015625], [196.1806640625, 471.41015625], [196.1806640625, 483.78515625], [107.25, 483.78515625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 681.0], [176.25, 681.0], [176.25, 691.83984375], [107.1298828125, 691.83984375]]}, {"title": "APPENDIX", "heading_level": null, "page_id": 14, "polygon": [[106.90576171875, 82.5], [162.861328125, 82.5], [162.861328125, 93.24755859375], [106.90576171875, 93.24755859375]]}, {"title": "A THEORETICAL DETAILS", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 179.9208984375], [251.7626953125, 179.9208984375], [251.7626953125, 190.3623046875], [107.1298828125, 190.3623046875]]}, {"title": "A.1 DRO AND GENERALIZATION", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 203.25], [257.1416015625, 203.25], [257.1416015625, 213.46875], [107.1298828125, 213.46875]]}, {"title": "A.2 LARGE DEVIATIONS AND DATA DRIVEN DRO", "heading_level": null, "page_id": 14, "polygon": [[107.25, 385.5], [329.90625, 385.5], [329.90625, 395.419921875], [107.25, 395.419921875]]}, {"title": "B GENERALIZE TO BROADER DATASET DISTILLATION SCENARIOS", "heading_level": null, "page_id": 15, "polygon": [[107.25, 314.25], [455.25, 314.25], [455.25, 324.84375], [107.25, 324.84375]]}, {"title": "C PROOF OF PROPOSITION 3.4", "heading_level": null, "page_id": 15, "polygon": [[107.25, 636.75], [271.5, 636.75], [271.5, 647.75390625], [107.25, 647.75390625]]}, {"title": "D MORE EXPERIMENTS AND ANALYSIS", "heading_level": null, "page_id": 16, "polygon": [[107.20458984375, 454.5], [319.1484375, 454.5], [319.1484375, 464.8359375], [107.20458984375, 464.8359375]]}, {"title": "D.1 MORE CHALLENGING TESTING SCENARIOS", "heading_level": null, "page_id": 16, "polygon": [[107.25, 478.5], [316.5, 478.5], [316.5, 488.42578125], [107.25, 488.42578125]]}, {"title": "D.2 APPLICATION ON MORE DD METHODS", "heading_level": null, "page_id": 17, "polygon": [[106.5, 407.98828125], [301.0693359375, 407.98828125], [301.0693359375, 418.81640625], [106.5, 418.81640625]]}, {"title": "D.3 EFFICIENCY EVALUATION", "heading_level": null, "page_id": 17, "polygon": [[106.5, 510.08203125], [244.2919921875, 510.08203125], [244.2919921875, 520.91015625], [106.5, 520.91015625]]}, {"title": "D.4 CHOICE OF THE NUMBER OF CLUSTERS", "heading_level": null, "page_id": 17, "polygon": [[106.5, 611.7890625], [302.4140625, 611.7890625], [302.4140625, 623.390625], [106.5, 623.390625]]}, {"title": "D.5 CHOICE OF MINI-BATCH SIZE", "heading_level": null, "page_id": 18, "polygon": [[106.5, 410.30859375], [264.1640625, 410.30859375], [264.1640625, 421.13671875], [106.5, 421.13671875]]}, {"title": "D.6 CHOICE OF INITIALIZATION", "heading_level": null, "page_id": 18, "polygon": [[106.5, 512.015625], [254.6015625, 512.015625], [254.6015625, 522.84375], [106.5, 522.84375]]}, {"title": "D.7 EVALUATION ON DOMAIN GENERALIZATION BASELINES", "heading_level": null, "page_id": 18, "polygon": [[106.5, 656.6484375], [376.822265625, 656.6484375], [376.822265625, 667.4765625], [106.5, 667.4765625]]}, {"title": "D.8 RESULTS ON MORE IMAGENET SUB-SETS", "heading_level": null, "page_id": 19, "polygon": [[106.5, 414.75], [313.76953125, 414.75], [313.76953125, 425.00390625], [106.5, 425.00390625]]}, {"title": "E RELATED WORK", "heading_level": null, "page_id": 19, "polygon": [[107.25, 507.0], [213.75, 507.0], [213.75, 518.9765625], [107.25, 518.9765625]]}, {"title": "F VISUALIZATION OF SYNTHESIZED SAMPLES", "heading_level": null, "page_id": 20, "polygon": [[106.5322265625, 326.390625], [353.25, 326.390625], [353.25, 339.15234375], [106.5322265625, 339.15234375]]}, {"title": "G DATASET STATISTICS", "heading_level": null, "page_id": 20, "polygon": [[107.25, 621.0703125], [238.5, 621.0703125], [238.5, 633.4453125], [107.25, 633.4453125]]}, {"title": "H IMPLEMENTATION DETAILS", "heading_level": null, "page_id": 21, "polygon": [[106.5, 663.609375], [271.3359375, 663.609375], [271.3359375, 675.984375], [106.5, 675.984375]]}, {"title": "H.1 IDC DETAILS", "heading_level": null, "page_id": 22, "polygon": [[106.5, 648.0], [193.5, 648.0], [193.5, 658.96875], [106.5, 658.96875]]}, {"title": "H.2 GLAD DETAILS", "heading_level": null, "page_id": 23, "polygon": [[106.5, 118.6259765625], [203.9501953125, 118.6259765625], [203.9501953125, 130.2275390625], [106.5, 130.2275390625]]}, {"title": "H.3 DISTILLING ITERATION", "heading_level": null, "page_id": 23, "polygon": [[106.5, 257.748046875], [235.9248046875, 257.748046875], [235.9248046875, 268.576171875], [106.5, 268.576171875]]}, {"title": "I BROADER IMPACTS", "heading_level": null, "page_id": 23, "polygon": [[106.5, 383.044921875], [226.212890625, 383.044921875], [226.212890625, 395.033203125], [106.5, 395.033203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 51], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7215, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 212], ["Line", 57], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["Line", 75], ["Text", 7], ["Reference", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 58], ["Text", 5], ["TextInlineMath", 4], ["Reference", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 71], ["Text", 14], ["Equation", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 90], ["Text", 12], ["Equation", 7], ["TextInlineMath", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 896], ["TableCell", 194], ["Line", 80], ["Table", 3], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 3, "llm_tokens_used": 1125, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["TableCell", 70], ["Line", 60], ["Text", 5], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 15631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 62], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 49], ["Text", 6], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1352, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 47], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 27], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 556], ["Line", 88], ["Text", 9], ["Equation", 5], ["SectionHeader", 4], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 72], ["Text", 7], ["TextInlineMath", 4], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 62], ["TableCell", 21], ["Text", 7], ["Reference", 3], ["Table", 2], ["Equation", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4913, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 365], ["TableCell", 88], ["Line", 48], ["Text", 4], ["Table", 3], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 2972, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["TableCell", 60], ["Line", 48], ["Text", 5], ["Table", 3], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 6878, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["TableCell", 123], ["Line", 49], ["Text", 4], ["Table", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 14603, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["TableCell", 46], ["Line", 31], ["Text", 5], ["Reference", 4], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4127, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 18], ["Picture", 2], ["Caption", 2], ["ListItem", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1303, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 34], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 33], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Group_Distributionally_Robust_Dataset_Distillation_with_Risk_Minimization"}