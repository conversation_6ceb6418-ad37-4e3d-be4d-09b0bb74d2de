# <span id="page-0-1"></span>Hierarchical Features Matter: A Deep Exploration of Progressive Parameterization Method for Dataset Distillation

Xinh<PERSON>hong<sup>1,∗</sup> <PERSON><PERSON><sup>3,∗</sup> <PERSON><sup>1,2†</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>4</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>2,3</sup> <sup>1</sup>Harbin Institute of Technology, Shenzhen  $\frac{2P}{P}$ eng Cheng Laboratory

<sup>3</sup>Tsinghua Shenzhen International Graduate School, Tsinghua University <sup>4</sup>Augusta University

<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>;

## Abstract

*Dataset distillation is an emerging dataset reduction method, which condenses large-scale datasets while maintaining task accuracy. Current parameterization methods achieve enhanced performance under extremely high compression ratio by optimizing determined synthetic dataset in informative feature domain. However, they limit themselves to a fixed optimization space for distillation, neglecting the diverse guidance across different informative latent spaces. To overcome this limitation, we propose a novel parameterization method dubbed Hierarchical Parameterization Distillation (H-PD), to systematically explore hierarchical feature within provided feature space (e.g., layers within pretrained generative adversarial networks). We verify the correctness of our insights by applying the hierarchical optimization strategy on GAN-based parameterization method. In addition, we introduce a novel class-relevant feature distance metric to alleviate the computational burden associated with synthetic dataset evaluation, bridging the gap between synthetic and original datasets. Experimental results demonstrate that the proposed H-PD achieves a significant performance improvement under various settings with equivalent time consumption, and even surpasses current generative distillation using diffusion models under extreme compression ratios IPC=1 and IPC=10. Our code is available at* <https://github.com/ndhg1213/H-PD>

# 1. Introduction

In recent years, deep learning has made significant strides in various research fields, encompassing computer vision [\[13,](#page-8-0) [19\]](#page-8-1) and natural language processing [\[3,](#page-8-2) [12\]](#page-8-3). These advancements have been facilitated by utilizing larger and more intricate deep neural networks (DNNs) in conjunction with

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image is a line graph showing the accuracy of two different architectures, "Same-architecture" and "Cross-architecture", across different feature layers. The x-axis is labeled "Index of Feature Layer" and includes categories F0, F4, F8, F12, F16, F20, F24, and Pixel. The y-axis is labeled "Accuracy" and ranges from 25 to 37. The "Same-architecture" line, plotted in blue with circular markers, starts at approximately 35, dips to around 33.5, rises to about 34.5, then gradually decreases to around 32.5. The "Cross-architecture" line, plotted in orange with circular markers, starts at approximately 26.8, rises to about 27.5, then to 27.8, peaks at around 29.2 at F16, and then decreases to about 28.5 at F20, 27.5 at F24, and 28 at Pixel. Red boxes highlight the data point at F0 for "Same-architecture" (around 35) and the data point at F16 for "Cross-architecture" (around 29.2).

Figure 1. Performance of synthetic datasets condensed from various feature domains provided by GAN under the same settings (DSA on ImageNet-Birds).

numerous datasets tailored for diverse application fields. However, as the complexity of various learning tasks increases, neural networks have grown both deeper and wider, resulting in an exponential surge in the size of datasets required for training these models. This has presented a substantial challenge to data storage and processing efficiency [\[26\]](#page-8-4), further exacerbating the bottleneck in deep learning due to the mismatch between the enormous data volume and limited computing resources.

Dataset distillation (DD) [\[44\]](#page-9-0) has emerged as a promising solution to the aforementioned issues. It allows for the generation of a more compact synthetic dataset, where each data point encapsulates a higher concentration of task-specific information than its real counterparts. When trained on the synthetic dataset, the network can achieve performance comparable to its counterpart using the original dataset. By significantly reducing the size of the training data, dataset distillation offers a substantial reduction in training costs and memory consumption. Various methods have been proposed to enhance the performance of the condensed dataset.

Subsequently, synthetic dataset parameterization methods [\[5,](#page-8-5) [27,](#page-8-6) [38,](#page-9-1) [49\]](#page-9-2) employ differentiable operations to pro-

<sup>\*</sup>Equal Contribution.

<sup>†</sup>Corresponding Author.

<span id="page-1-0"></span>cess synthetic images, shifting the optimization space from pixels to feature domains. These methods benefit from the efficient guidance of hidden features, thus achieving better performance. However, existing parameterization methods focus on one fixed optimization space, overlooking the informative guidance across multiple corresponding feature domains. For example, FreD [\[38\]](#page-9-1) optimizes the synthetic dataset in the low-frequency space using discrete cosine transform (DCT), while ignoring informative guidance in the high-frequency domain. In recent years, several recent studies have exploited the rich semantic information encoded in the generators to enhance dataset distillation. With the rapid advancement achieved by generative models, one category of distillation methods utilizes diffusion models [\[34\]](#page-9-3) to generative informative samples [\[18,](#page-8-7) [40\]](#page-9-4). However, under high compression rates (i.e., IPC=1/10), these methods can degrade into coreset selection methods. Another category of distillation methods employs GANs in an optimization-based manner [\[5,](#page-8-5) [49\]](#page-9-2) to parameterize the synthetic datasets and achieve reliable results.

In contrast to the aforementioned methods, GAN-based parameterization distillation methods possess an optimization space with richer semantic information. ITGAN[\[49\]](#page-9-2) directly optimizes the initial latent space of GAN and achieves significant performance improvements on lowresolution datasets. To fully utilize the GAN prior, GLaD [\[5\]](#page-8-5) decomposes the GAN structure and manually selects the intermediate layer, greatly enhancing the crossarchitecture performance of the synthetic dataset. However, existing method exhibit a performance decrease in the same-architecture settings when coupled with certain dataset distillation methods as suggested in Figure [1.](#page-0-0) In this manner, even though synthetic datasets are condensed from the optimally selected intermediate layer through preliminary experiments by manual picking, the diverse model architectures still lead to changes in the optimal performance. As aforementioned parameterization methods, current GAN-based approaches limit the optimization space to a specific feature domain and necessitate extensive computing time and resources to manually select the optimal feature domain for different settings. This naturally raises a question: *Does a fixed optimization space meet the demands of dynamic data distribution and model architectures during parameterization dataset distillation?*

To address this question, we propose a straightforward and efficient approach based on the parameterization prior, Hierarchical Parameterization Distillation (H-PD), which explores the significance of hierarchical features. To verify our intuition, we design a well-design framework on GANbased parameterization method. The proposed H-PD embraces adaptive exploration across all hierarchical feature domains within GAN models. Specifically, we decompose the GAN structure, undertaking a greedy search that spans different hierarchical feature domains. During the distillation process, we optimize these hierarchical latents within the GAN model, guided by the loss from the dataset distillation task. Throughout this optimization, we track the best hierarchical latents at the current layer, feeding them into the next layer. This iterative process continues until the optimizer traverses the hierarchical layers and reaches the pixel domain. To mitigate the time-consuming nature of performance evaluation, we introduce a class-relevant feature distance metric between the synthetic and real datasets to search for the optimal latent feature. This metric serves as a performance estimation for the synthetic datasets, encapsulating the significance of hierarchical features. Crucially, our method explores hierarchical features more comprehensively than previous approaches, which only rely on a single fixed feature domain as image priors.

Our main contributions can be summarized as follows:

- We revisited the shortcomings of existing parameterization methods, and provided a novel parametrization framework to enhance their efficiency by leveraging information across various feature domains.
- Through a well-designed framework, we effectively enhanced the performance of GAN-based parameterization methods, demonstrating the validity of our insights.
- To mitigate the computational demands associated with searching feature domains, we introduce a novel classrelevant feature distance metric, saving valuable computational time by approximating the real performance of the synthetic dataset.

# 2. Related Work

## 2.1. Dataset Distillation

Dataset distillation was initially regarded as a meta-learning problem [\[44\]](#page-9-0). It involves minimizing the loss function of the synthetic dataset using a model trained on the synthetic dataset. Since then, several approaches have been proposed to enhance the performance of dataset distillation. One category of methods utilizes ridge regression models to approximate the inner loop optimization [\[1,](#page-8-8) [31,](#page-9-5) [32,](#page-9-6) [54\]](#page-9-7). Another category of methods selects an informative space as a proxy target to address the unrolled optimization problem. DC [\[51\]](#page-9-8), DSA [\[48\]](#page-9-9) and DCC [\[25\]](#page-8-9) match the weight gradients of models trained on the real and synthetic dataset, while DM [\[50\]](#page-9-10), CAFE [\[43\]](#page-9-11) and DataDAM [\[35\]](#page-9-12) use feature distance between the real and synthetic dataset as matching targets. MTT [\[4\]](#page-8-10) and TESLA [\[10\]](#page-8-11) match the model parameter trajectories obtained from training on the real dataset and the synthetic dataset. In recent years, some studies have argued that the bi-level optimization structure required by traditional dataset distillation is redundant and computationally expensive.

Other studies suggest that the pixel domain, where im-

<span id="page-2-0"></span>ages reside, is considered a high-dimensional space. Therefore, performance improvement can be achieved by parameterizing the synthetic dataset and transferring the optimization space. IDC [\[21\]](#page-8-12) and HaBa [\[27\]](#page-8-6) perform optimization in a low-dimensional space using differentiable operations, while GLaD [\[5\]](#page-8-5) and ITGAN [\[49\]](#page-9-2) utilize the feature domain provided by GANs as the optimization space, both of them employ pre-trained GANs as priors. FreD [\[38\]](#page-9-1) employs traditional compression methods (e.g., DCT) to provide a lowfrequency space as the optimization space. However, existing parameterization methods fix the optimization space thus neglecting the guidance from multi-feature domains.

The proposed H-PD introduces an innovative approach to parameterizing synthetic datasets and be verified on GAN-based parameterization methods, representing a broader and more encompassing enhancement compared to previous approaches utilizing fixed optimization space.

## 2.2. GAN for parameterization distillation

GAN [\[8\]](#page-8-13) is a deep generative model trained adversarially to learn the distribution of real images. Recent studies have shown that GANs can tackle inverse problems by mapping images into their latent space [\[6,](#page-8-14) [14–](#page-8-15)[16,](#page-8-16) [33,](#page-9-13) [45\]](#page-9-14), enabling tasks like image editing [\[2,](#page-8-17) [41\]](#page-9-15). Incorporating image distribution information into GAN enhances the performance of dataset distillation by utilizing GAN to parameterize the synthetic dataset. GLaD employs GAN (e.g., StyleGAN-XL [\[36\]](#page-9-16)) as a prior and significantly improves the crossarchitecture performance of the synthetic dataset by selecting the feature domain of GAN's intermediate layers as the optimization space. However, it overlooks the fact that the optimal optimization space may vary when dealing with different datasets, even with the same dataset distillation method. Additionally, it ignores the guidance offered by GAN's earlier layers.

Compared with the current dataset distillation methods [\[18,](#page-8-7) [40\]](#page-9-4) based on diffusion models, which could be more suitable for generative methods. GLaD can achieve significantly better performance under extreme compression ratios of IPC=1 and IPC=10 by enhancing the optimizationbased methods as an efficient plugin. With the observation, the proposed H-PD further explores hierarchical feature domains of pre-trained GANs to address the limitation of GLaD, resulting in a novel parameterization method that successfully leveraging informative guidance within unfixed optimization space.

# 3. Method

In this section, we first present the problem definition of dataset distillation and discuss existing methods that parameterize synthetic datasets using GANs. Subsequently, we delve into the specifics of our method, aiming to improve upon previous works by exploring the feature do-

mains provided by GANs. Finally, we propose an alternative evaluation scheme that assesses the synthetic dataset's performance by measuring the layer-wise feature distance between it and the real one.

## 3.1. Preliminaries

Dataset distillation necessitates a real large-scale dataset  $\mathcal{T} = \{(\mathbf{x}_t^i, y_t^i)\}_{i=1}^{\mathcal{T}}$  and aims to create a smaller synthetic dataset  $S = \{(\mathbf{x}_s^i, y_s^i)\}_{i=1}^S$  ( $|S| \ll |\mathcal{T}|$ ), minimizing the performance gap between models trained on the two datasets. To achieve this, a well-designed matching objective  $\phi(\cdot)$  is employed to extract feature distances in a specific informative space, representing the performance gap between the real and synthetic datasets. The optimization process involves initializing the synthetic dataset from the real dataset and iteratively updating it by minimizing the feature distance, which can be formulated below:

$$
S^* = \underset{S}{\arg\min} \mathcal{M}(\phi(S), \phi(\mathcal{T})), \tag{1}
$$

where  $\mathcal{M}(\cdot, \cdot)$  denotes some matching metric, e.g., neural network gradients [\[51\]](#page-9-8), exacted features[\[50\]](#page-9-10), and training trajectories[\[4\]](#page-8-10).

Building upon these findings, methods that parameterize synthetic datasets shift the optimization space from the pixel domain to the feature domain by employing differentiable operations. For instance, GAN priors-based methods [\[5,](#page-8-5) [49\]](#page-9-2) can be formulated uniformly below:

$$
\mathbf{z}^* = \underset{\mathbf{z} \in \mathcal{Z}}{\arg \min} \mathcal{M}(\phi(G_w(\mathbf{z})), \phi(\mathcal{T})), \tag{2}
$$

where  $z \in \mathcal{Z}$  represents the latent in a specific feature domain of a pre-trained generative model  $G_w(\cdot)$ . Guided by GAN priors, these methods demonstrate substantial performance improvements.

## 3.2. Theoretical Insights of Unfixed Optimization Space and Fixed Optimization Space

Previous parameterization methods distill knowledge within a fixed optimization space, where the starting point in a given feature domain is typically randomly initialized. In contrast, our proposed H-PD framework performs progressive optimization on the latents. Figure [2](#page-3-0) provides a detailed comparison of the optimization processes in fixed versus flexible feature spaces. Beyond the limited guidance across various feature domains, the fixed optimization space encounters a critical bottleneck: it restricts further enhancement by limiting the selection of superior synthetic datasets based on explicit or implicit criteria. In particular, the optimization within a fixed space can be viewed as a continuous process. If a temporarily optimal result, denoted as  $S<sup>i</sup>$ , is chosen before convergence, and the corresponding optimization epoch does not mark the end of the optimization trajectory, a fundamental issue arises: *should* S i *serve*

<span id="page-3-2"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The image displays two diagrams illustrating optimization spaces. Diagram (a), labeled "Fixed optimization space," shows an initial state S1^0 entering "Feature Domain 1." Within this domain, an optimizer processes the input, leading to an intermediate state S1^i and ultimately to an optimized state S1^\*. An arrow from S1^\* points to S1^N, with a question mark and an arrow pointing back to S1^\*, suggesting an iterative or uncertain process. Diagram (b), labeled "Unfixed optimization space," presents a sequential process across two feature domains. S1^0 enters "Feature Domain 1," is processed by an optimizer to S1^i and then S1^\*. This S1^\* then enters "Feature Domain 2," where it is processed by another optimizer to S2^j and finally to S2^\*. Both diagrams show an optimizer within their respective feature domains, with arrows indicating the flow of states.

Figure 2. The comparison between fixed optimization space and unfixed optimization space.  $S^i$  is the synthetic dataset at optimization steps i,  $S^*$  is the optimal synthetic dataset selected during the optimization path,  $S_j$  is the synthetic dataset optimized in feature domain j.

*as the starting point for the next phase of the optimization, or should the suboptimal result* S <sup>N</sup> *be used instead?* Opting for the latter can trap the optimization in a local minimum due to the absence of robust criterion-driven guidance, while choosing the former risks creating an optimization loop, effectively discarding the progress between  $S<sup>i</sup>$ and  $S<sup>N</sup>$  and forcing a reset.

Next, we provide the theoretical insights of its effectiveness. Specifically, we denote the random latent and optimized latent as  $z_{rand}$  and  $z_{opt}$ , respectively. For an effective dataset distillation method, we assume that the distilled data distribution  $P(Z_{\text{lic}})$  fits the original dataset distribution in a lossless manner. Let the coupling latent  $(z_{rand}, z_{opt})$  be the observed values of random variables  $Z_{\text{rand}}$  and  $Z_{\text{opt}}$  respectively. Let  $c(\cdot)$  be a cost function defined in optimal trans-port theory [\[28\]](#page-8-18), which satisfies  $\mathbb{E}[c(X-Y)] \propto 1/I(X;Y)$ . Then we can obtain:

<span id="page-3-1"></span>
$$
\mathbb{E}[c(Z_{\text{lic}} - Z_{\text{opt}})]
$$
\n
$$
= k/D_{\text{KL}}(P(Z_{\text{lic}}, Z_{\text{opt}})||P(Z_{\text{lic}}) \cdot P(Z_{\text{opt}}))
$$
\n
$$
= k/[H(Z_{\text{lic}}) - H(Z_{\text{lic}}|Z_{\text{opt}})]
$$
\n
$$
\leq k/[H(Z_{\text{lic}}) - H(Z_{\text{lic}}|Z_{\text{rand}})]
$$
\n
$$
= k/D_{\text{KL}}(P(Z_{\text{lic}}, Z_{\text{rand}})||P(Z_{\text{lic}}) \cdot P(Z_{\text{rand}}))
$$
\n
$$
= \mathbb{E}[c(Z_{\text{lic}} - Z_{\text{rand}})],
$$
\n(3)

where k denotes a constant,  $D_{KL}(\cdot||\cdot)$  and  $H(\cdot)$  stand for Kullback-Leibler divergence and entropy, respectively. The Inequality [\(3\)](#page-3-1) follows since the conditional entropy  $H(Z_{\text{lic}}|Z_{\text{opt}})$  is smaller than  $H(Z_{\text{lic}}|Z_{\text{rand}})$ .

The theoretical analysis indicates that the proposed H-PD method of selecting partially optimized latents, rather than random initialization, reduces optimization cost across different spaces. By leveraging multiple feature domains, it accelerates convergence and, with its implicit evaluation criterion, effectively avoids local optima—an issue common in fixed optimization spaces.

## 3.3. Progressive Optimization with Hierarchical Feature Domains

To verify our insights about the utilization of unfixed parameterization optimization space, we apply it with GANbased parameterization method (i.e., GLaD [\[5\]](#page-8-5)). As depicted in Algorithm [1,](#page-4-0) our approach diverges from restricting the optimization space to a specific feature domain of the GANs. Instead, we aim to explore the hierarchical layers of the GAN, striving to enhance the effective utilization of the prior information.

To sufficiently utilize the informative guidance from the hierarchical feature domains, we decompose a pre-trained GAN  $G_w(\cdot)$  for hierarchical layer searching, i.e.,

$$
G_w(\cdot) = G_{K-1} \circ G_{K-2} \circ \cdots \circ G_1 \circ G_0(\cdot). \tag{4}
$$

For each hierarchical layer  $G_i$  provided by GAN, we repeat the following steps. Firstly, we generate images from  $z_i$ only using the remaining synthesizing network  $G_{k-1}(\cdot) \circ$  $G_{k-2}(\cdot) \circ \cdots \circ G_i(\cdot)$ . Then, we employ the distillation method (e.g., MTT [\[4\]](#page-8-10)) to calculate  $\mathcal L$  based on the synthetic dataset composed of generated images and the real dataset to optimize  $z_i$  with an SGD optimizer, the optimization process lasts for a pre-determined and fixed  $N$  steps. After completing the optimization process for a specific layer, we implicitly evaluate the latents synthesized during the SGD optimization process and record  $z_i^*$  as the optimal latents for the current layer. Finally, we pass  $z_i^*$  into the next layer to obtain  $z_{i+1}^0$  as the initial latent for the next layer.

When the optimization space reaches the ultimate pixel domain, we choose the optimal latent  $z^*$  from the recorded latents  $z_i^*$  based on the real performance of the synthetic dataset  $S$  generated by the corresponding remaining synthesizing network  $G_{k-1}(\cdot) \circ G_{k-2}(\cdot) \circ \cdots \circ G_i(\cdot)$ . In this way, we fully explore the feature domain of the GAN, leveraging its rich information.

<span id="page-4-2"></span>Algorithm 1 Pseudocode of H-PD with GAN-based parameterization method

**Input:**  $G_w(\cdot)$ : a pre-trained generative model; K: the number of hierarchical layers;  $N$ : distillation steps;  $T$ : real training dataset;  $P_z$ : distribution of latent initializations;  $\mathcal{L}$ : distillation loss;  $Acc(\cdot)$ : evaluate real performance of synthetic dataset;

1: Initial average latent  $z \sim P_z$ 2: Dissemble  $G_w(\cdot)$  into  $G_{k-1} \circ G_{k-2} \circ \cdots \circ G_0(\cdot)$ 3:  $acc_{max} = 0$ 4: for i  $\leftarrow$  0 to  $K - 1$  do 5:  $d_{min} = \mathcal{D}(G_{k-1} \circ \cdots \circ G_i(\mathbf{z}_i^0), \mathcal{T})$ 6: **for**  $j \leftarrow 0$  to  $N - 1$  **do** 7:  $\tilde{S}_i^j = G_{k-1} \circ \cdots \circ G_i(\mathbf{z}_i^j).$ 8:  $\mathcal{L} = \mathcal{M}(\phi(\mathcal{S}_i^j), \phi(\mathcal{T}))$ 9:  $\mathbf{z}_i^{j+1} \leftarrow SGD(\mathbf{z}_i^j; \mathcal{L})$ 10: **if**  $\mathcal{D}(\mathcal{S}_i^j, \mathcal{T}) \leq d_{min}$  then  $11:$  $\mathbf{z}_i^*=\mathbf{z}_i^j, \mathcal{S}_i^*=\mathcal{S}_i^j$  $12:$  end if 13: end for 14: if  $Acc(S_i^*) > acc_{max}$  then 15:  $acc_{max} = Acc(S_i^*)$ 16:  $S = S_i^*$ 17: end if  $18:$  $_{i+1}^0 = G_i(\mathbf{z}_i^*)$ 19: end for **Output:** Synthetic dataset  $S$ 

<span id="page-4-0"></span>

# 3.4. Enhancing Performance with Efficient Searching Strategy

Ensemble-Averaging Latent Initialization To mitigate the undesirable time overhead brought by existing methods [\[29\]](#page-9-17) using clustering or GAN inversion [\[45\]](#page-9-14), we propose an inactive searching initialization by calculating the average value of multiple noises, and passing it through the GAN's mapping network to obtain the initial latent  $z_0$ with reduced bias. our method showcases simplicity without compromising effectiveness, as confirmed by sufficient experimental results.

Class-relevant Feature Distance To search for optimal latent as the optimization starting point of the subsequent feature domain, an efficient implicit evaluation metric is needed to replace the time-consuming evaluation of the synthetic dataset's real performance. We first attempt to use the loss value as a substitute evaluation metric. However, it fails to yield desired results and, in some cases, performs even worse than not searching at all.

To utilize gradient information while maintaining diversity, we adopt the class activation map (CAM) [\[30\]](#page-9-18) by utilizing the gradients of the corresponding class with respect

to the feature maps to localize the class-specific features. With the output logits  $q = f^d(w^d; \mathbf{z})$  from the classifier  $w<sup>d</sup>$ , the CAM is defined as the gradients of output logits  $q<sup>y</sup>$ of class  $y$  with respect to features  $z$  as follows:

$$
g_{\mathbf{z}} = \frac{\partial q^y}{\partial \mathbf{z}}.\tag{5}
$$

To focus attention on the class-relevant region, we propose a novel class-relevant feature distance  $\mathcal{D}(\mathcal{S}, \mathcal{T})$  between the real dataset S and the synthetic dataset  $\mathcal T$ . i.e.,

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}) = ||\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} w^e (\mathbf{x}_t^i) \cdot \text{ReLU}(g_{\mathbf{z}}^t)
$$
$$
-\frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} w^e (\mathbf{x}_s^j) \cdot \text{ReLU}(g_{\mathbf{z}}^s) ||^2, \qquad (6)
$$

where  $w^e(\cdot)$  represents the feature extractor of a pre-trained network, and  $ReLU(·)$  is the rectified linear unit function.

## 4. Experiments

To verify the efficiency of our proposed method, we conduct experiments using code derived from the open-source  $GLaD<sup>1</sup>$  $GLaD<sup>1</sup>$  $GLaD<sup>1</sup>$ . We utilize ImageNet-1K [\[11\]](#page-8-19) subsets and CIFAR-10 [\[22\]](#page-8-20) to generate high-resolution and low-resolution distilled datasets respectively, with StyleGAN-XL as the deep generative network. To ensure a fair comparison, we maintain consistency by adopting the same network architecture and employing identical hyperparameters. Our code is availabel at https://github.com/ndhg1213/H-PD.

## 4.1. Settings and Implementation Details

Datasets and Network Architectures In this study, we build upon previous research by utilizing CIFAR10 and Tiny-ImageNet [\[24\]](#page-8-21) as low-resolution dataset, For highresolution experiments, we choose ImageNet-1K and ten subsets from it. These subsets, each consisting of ten categories, are divided into the training and validation sets. The detailed categories combination can be found in Appendix.

For the surrogate model for dataset distillation, we choose ConvNet-5 [\[17\]](#page-8-22) as the backbone network for DM, DSA and TESLA. For ImageNet-1K and Tiny-Imagenet, we conduct experiments on  $SRe^{2}L$  [\[46\]](#page-9-19) and adopt ResNet-18 [\[19\]](#page-8-1) as backbone. To evaluate the performance of the synthetic dataset, we employ various models, including ConveNet, AlexNet [\[23\]](#page-8-23), VGG-11 [\[39\]](#page-9-20), ResNet-18, and a Vision Transformer model [\[13\]](#page-8-0) from the DC-BENCH [\[9\]](#page-8-24) resource. It is important to note that all of these evaluation models are versions specifically tailored for corresponding resolution datasets.

<span id="page-4-1"></span><sup>1</sup>https://georgecazenavette.github.io/glad

<span id="page-5-5"></span><span id="page-5-0"></span>

| Alg.         | Method      | ImNet-A      | ImNet-B      | $ImNet-C$      | ImNet-D        | ImNet-E        | <b>ImNette</b> | ImWoof       | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------------|-------------|--------------|--------------|----------------|----------------|----------------|----------------|--------------|----------------|----------------|----------------|
|              | Pixel       | $51.7 + 0.2$ | $53.3 + 1.0$ | $48.0 \pm 0.7$ | $43.0 \pm 0.6$ | $39.5 + 0.9$   | $41.8 \pm 0.6$ | $22.6 + 0.6$ | $37.3 \pm 0.8$ | $22.4 + 1.1$   | $22.6 \pm 0.4$ |
| <b>TESLA</b> | <b>GLaD</b> | $50.7 + 0.4$ | $51.9 + 1.3$ | $44.9 \pm 0.4$ | $39.9 + 1.7$   | $37.6 + 0.7$   | $38.7 + 1.6$   | $23.4 + 1.1$ | $35.8 \pm 1.4$ | $23.1 \pm 0.4$ | $26.0 + 1.1$   |
|              | $H-PD$      | $55.1 + 0.6$ | $57.4 + 0.3$ | 49.5 $\pm 0.6$ | $46.3 + 0.9$   | $43.0 + 0.6$   | $45.4 + 1.1$   | $28.3 + 0.2$ | $39.7 \pm 0.8$ | $25.6 \pm 0.7$ | $29.6 \pm 1.0$ |
|              | Pixel       | $43.2 + 0.6$ | $47.2 + 0.7$ | $41.3 \pm 0.7$ | $34.3 + 1.5$   | $34.9 + 1.5$   | $34.2 + 1.7$   | $22.5 + 1.0$ | $32.0 \pm 1.5$ | $21.0 \pm 0.9$ | $22.0 \pm 0.6$ |
| <b>DSA</b>   | <b>GLaD</b> | $44.1 + 2.4$ | $49.2 + 1.1$ | $42.0 \pm 0.6$ | $35.6 + 0.9$   | $35.8 \pm 0.9$ | $35.4 + 1.2$   | $22.3 + 1.1$ | $33.8 \pm 0.9$ | $20.7 + 1.1$   | $22.6 \pm 0.8$ |
|              | $H-PD$      | $46.9 + 0.8$ | $50.7 + 0.9$ | $43.9 + 0.7$   | $37.4 + 0.4$   | $37.2 + 0.3$   | $36.9 + 0.8$   | $24.0 + 0.8$ | $35.3 \pm 1.0$ | $22.4 + 1.1$   | $24.1 \pm 0.9$ |
|              | Pixel       | $394+18$     | $40.9 + 1.7$ | $39.0 + 1.3$   | $30.8 + 0.9$   | $27.0 + 0.8$   | $30.4 + 2.7$   | $20.7 + 1.0$ | $26.6 \pm 2.6$ | $20.4 + 1.9$   | $20.1 + 1.2$   |
| DM           | <b>GLaD</b> | $41.0 + 1.5$ | $42.9 + 1.9$ | $39.4 + 1.7$   | $33.2 + 1.4$   | $30.3 + 1.3$   | $32.2 + 1.7$   | $21.2 + 1.5$ | $27.6 + 1.9$   | $21.8 + 1.8$   | $22.3 + 1.6$   |
|              | $H-PD$      | $42.8 + 1.2$ | $44.7 + 1.3$ | 41.1 $\pm$ 1.3 | $34.8 \pm 1.5$ | $31.9 + 0.9$   | $34.8 + 1.0$   | $23.9 + 1.9$ | $29.5 \pm 1.5$ | $24.4 \pm 2.1$ | $24.2 + 1.1$   |

Table 1. Synthetic dataset same-architecture performance (%) on ImageNet-Subset (128×128) under IPC=1. "Pixel" refers to not deploying GAN.

<span id="page-5-1"></span>

| Alg.  | Method | Tiny-ImageNet |                | ImageNet-1K   |                |
|-------|--------|---------------|----------------|---------------|----------------|
|       |        | IPC-1         | IPC-10         | IPC-1         | IPC-10         |
|       | Pixel  | $2.6 \pm 0.1$ | $16.1 \pm 0.2$ | $0.1 \pm 0.1$ | $21.3 \pm 0.6$ |
| SRe2L | GLD    | $3.1 \pm 0.3$ | $15.7 \pm 0.2$ | $1.2 \pm 0.1$ | $21.9 \pm 0.8$ |
|       | $H-PD$ | $4.5 \pm 1.0$ | $18.3 \pm 0.5$ | $2.6 \pm 0.2$ | $23.5 \pm 0.4$ |

Table 2. Synthetic dataset performance comparison with  $SRe^{2}L$ on Tiny-ImageNet (64x64) and ImageNet-1K (224x224) evaluted by ResNet-18.

### 4.2. Performance Improvements

The performance comparison of our method with previous works GLaD is shown in Table [1](#page-5-0) and Table [2.](#page-5-1) We report the same-architecture performance of the synthetic dataset. Since GLaD did not conduct experiments on  $SRe<sup>2</sup>L$ , we chose to adopt the same layers settings (i.e., 12) as TESLA to ensure fairness. compared to optimizing only in a fixed feature space, it can be observed that our method achieves consistent and significant improvements with all the optimization-based methods. This indicates that our method successfully leverages the guidance information provided by all feature domains. The visualized images of synthetic datasets are depicted in Figure [3.](#page-6-0)

<span id="page-5-2"></span>

| Alg.       | Method         | ImNet-A ImNet-B ImNet-C      |                              |                                | ImNet-D                          | ImNet-E                        | Alg.         | Method | ConvNet        | AlexNet        | ResNet-18      | VGG-11         | ViT            |
|------------|----------------|------------------------------|------------------------------|--------------------------------|----------------------------------|--------------------------------|--------------|--------|----------------|----------------|----------------|----------------|----------------|
| <b>DSA</b> | Pixel<br>GLaD  | $52.3 + 0.7$<br>$53.1 + 1.4$ | $45.1 + 8.3$<br>$50.1 + 0.6$ | $40.1 + 7.6$<br>$48.9 \pm 1.1$ | $36.1 \pm 0.4$<br>$38.9 \pm 1.0$ | $38.1 \pm 0.4$<br>$38.4 + 0.7$ | <b>TESLA</b> | Pixel  | $46.3 	pm 0.8$ | $26.8 	pm 0.6$ | $23.4 	pm 1.3$ | $24.9 	pm 0.8$ | $21.2 	pm 0.4$ |
|            | $H-PD$         | $54.1 + 1.2$                 | $52.0 + 1.1$                 | $49.5 + 0.8$                   | $39.8 + 0.7$                     | 40.1 $\pm$ 0.7                 |              | GLaD   | $35.5 	pm 0.6$ | $27.9 	pm 0.6$ | $30.2 	pm 0.6$ | $31.3 	pm 0.7$ | $22.7 	pm 0.4$ |
| DM         | Pixel          | $52.6 \pm 0.4$               | $50.6 + 0.5$                 | $47.5 + 0.7$                   | $35.4 \pm 0.4$                   | $36.0 \pm 0.5$                 |              | H-PD   | $37.2 	pm 0.4$ | $28.5 	pm 0.3$ | $31.4 	pm 0.4$ | $32.2 	pm 0.2$ | $24.1 	pm 0.4$ |
|            | GLaD           | $52.8 + 1.0$                 | $51.3 + 0.6$                 | $49.7 \pm 0.4$                 | $36.4 \pm 0.4$                   | $38.6 \pm 0.7$                 | <b>DSA</b>   | Pixel  | $28.3 	pm 0.3$ | $25.9 	pm 0.2$ | $27.3 	pm 0.5$ | $28.0 	pm 0.5$ | $22.9 	pm 0.3$ |
|            | $H-PD$         | $55.1 + 0.5$                 | $54.2 + 0.5$                 | $50.8 + 0.4$                   | $37.6 + 0.6$                     | $39.9 + 0.7$                   |              | GLaD   | $29.2 	pm 0.8$ | $26.0 	pm 0.7$ | $27.6 	pm 0.6$ | $28.2 	pm 0.6$ | $23.4 	pm 0.2$ |
| H-PD       | $30.2 	pm 0.5$ | $26.6 	pm 0.4$               | $28.2 	pm 0.4$               | $28.0 	pm 0.6$                 | $24.4 	pm 0.5$                   |                                |              |        |                |                |                |                |                |
| <b>DM</b>  | Pixel          | $26.0 	pm 0.6$               | $22.9 	pm 0.2$               | $22.2 	pm 0.7$                 | $23.8 	pm 0.5$                   | $21.3 	pm 0.5$                 |              |        |                |                |                |                |                |
|            | GLaD           | $27.1 	pm 0.7$               | $25.1 	pm 0.5$               | $22.5 	pm 0.7$                 | $24.8 	pm 0.8$                   | $23.0 	pm 0.1$                 |              |        |                |                |                |                |                |
|            | H-PD           | $27.6 	pm 0.7$               | $27.5 	pm 0.6$               | $25.6 	pm 0.6$                 | $25.4 	pm 0.8$                   | $23.6 	pm 0.5$                 |              |        |                |                |                |                |                |

Table 3. Synthetic dataset cross-architecture performance (%) on ImageNet-Subset under IPC=10.

### 4.3. More Comparisons with GLaD

To align the proposed H-PD with GLaD on higher IPC, i.e. only  $IPC = 10$  under DSA and DM is reported from original paper, our current confirmatory trials achieves a performance improvement of 1% to 3% compared to GLaD with

 $IPC = 10$  under DSA and DM as shown in Table [3,](#page-5-2) respectively, which demonstrates the effectiveness of H-PD.

<span id="page-5-3"></span>

Table 4. Performance(%) across different models on CIFAR-10.

The cross-architecture performance on CIFAR-10 [\[22\]](#page-8-20) is shown in Table [4.](#page-5-3) The results demonstrate that using a shallower StyleGAN-XL structure on the lower-resolution dataset CIFAR-10, H-PD still improves the performance of synthetic datasets distilled by different distillation methods. Please note that the released code of GLaD does not include the data augmentation and hyperparameter settings used by TESLA on CIFAR10, which leads to a poor performance on ConvNet.

To present a more comprehensive comparison, we assess the performance of the synthetic dataset across different architectures as shown in Table [5.](#page-5-4) The cross-architecture accuracy is calculated by averaging the performance of the remaining four models, excluding the backbone model. The results of previous studies are acquired directly from the original papers.

<span id="page-5-4"></span>

| Alg.         | Method | ImNet-A           | ImNet-B           | ImNet-C           | ImNet-D           | ImNet-E           |
|--------------|--------|-------------------|-------------------|-------------------|-------------------|-------------------|
| <b>TESLA</b> | Pixel  | 33.4 ± 1.5        | 34.0 ± 3.4        | 31.4 ± 3.4        | 27.7 ± 2.7        | 24.9 ± 1.8        |
|              | GLaD   | 39.9 ± 1.2        | 39.4 ± 1.3        | 34.9 ± 1.1        | 30.4 ± 1.5        | 29.0 ± 1.1        |
|              | H-PD   | <b>40.2 ± 0.3</b> | <b>39.8 ± 0.8</b> | <b>35.8 ± 0.7</b> | <b>31.2 ± 1.0</b> | <b>29.5 ± 0.7</b> |
| <b>DSA</b>   | Pixel  | 38.7 ± 4.2        | 38.7 ± 1.0        | 33.3 ± 1.9        | 26.4 ± 1.1        | 27.4 ± 0.9        |
|              | GLaD   | 41.8 ± 1.7        | 42.1 ± 1.2        | 35.8 ± 1.4        | 28.0 ± 0.8        | 29.3 ± 1.3        |
|              | H-PD   | <b>42.4 ± 1.2</b> | <b>42.6 ± 1.1</b> | <b>36.1 ± 1.1</b> | <b>28.7 ± 1.1</b> | <b>29.6 ± 0.9</b> |
| <b>DM</b>    | Pixel  | 27.2 ± 1.2        | 24.4 ± 1.1        | 23.0 ± 1.4        | 18.4 ± 0.7        | 17.7 ± 0.9        |
|              | GLaD   | 31.6 ± 1.4        | 31.3 ± 3.9        | 26.9 ± 1.2        | 21.5 ± 1.0        | 20.4 ± 0.8        |
|              | H-PD   | <b>34.9 ± 2.1</b> | <b>33.8 ± 2.0</b> | <b>27.8 ± 1.7</b> | <b>23.6 ± 1.5</b> | <b>22.5 ± 1.3</b> |

Table 5. Synthetic dataset cross-architecture performance (%) on ImageNet-Subset.

<span id="page-6-4"></span><span id="page-6-0"></span>Image /page/6/Picture/0 description: The image displays a grid of images, organized into two rows labeled "GLaD" and "H-PD". The top row is titled "ImNet-A with TESLA", "ImNet-B with DSA", and "ImNet-C with DM". Each column within these rows contains four images. The "GLaD" row shows images of dogs, a proboscis monkey, spoonbills, a parrot, and a textured pattern. The "H-PD" row shows similar subjects but with a darker, more abstract aesthetic, including dogs, a proboscis monkey, spoonbills, a parrot, and the textured pattern.

Figure 3. Visualization comparison of the synthetic datasets with different distillation methods.

## 4.4. Comparison with Diffusion Model Based Methods

Other generative distillation methods consider diffusion as an image generator rather than parameterization methods. Minimax  $[18]$  and  $D^4M$   $[40]$  fine-tune the diffusion model and corresponding latent respectively to generate entirely new samples. However, these methods only achieve random sampling outcomes under a high compression ratio (e.g., IPC=1/10), Contrary to these diffusion-based methods, our method in conjunction with GLAD belongs to an optimization-based method, which can effectively accomplish distillation in a more efficient way.

For a fair comparison, we adhere to GLaD's experimental setting (e.g., a 128x128 resolution image corresponds to a one-hot label), disregarding training time matching [\[37\]](#page-9-21) and mixiup [\[47\]](#page-9-22) strategy. As shown in Table [6,](#page-6-1) our method demonstrates significant advantages at a mechanism compression ratio of IPC=1 without requiring fine-tuning of the pre-trained model.

<span id="page-6-1"></span>

| Method  | ImNette      | ImWoof       | ImNet-Birds  | ImNet-Fruits | ImNet-Cats | Metric     | Method | TESLA        | DSA          | DM           |
|---------|--------------|--------------|--------------|--------------|------------|------------|--------|--------------|--------------|--------------|
| Minimax | $22.8±0.5$   | $17.8±0.3$   | $23.2±0.7$   | $17.5±0.5$   | $19.8±0.3$ | Time       | GLaD   | 75           | 69           | 64           |
| $D4M$   | $15.2±0.5$   | $17.4±0.6$   | $18.2±0.4$   | $17.6±0.6$   | $23.4±0.7$ |            | H-PD   | 70           | 73           | 15           |
| $H-PD$  | $45.4±1.1$   | $28.3±0.2$   | $39.7±0.8$   | $25.6±0.7$   | $29.6±1.0$ | perfomance | GLaD   | 45.0 $±$ 0.9 | 41.3 $±$ 1.2 | 37.4 $±$ 1.6 |
| H-PD    | 50.3 $±$ 0.6 | 43.2 $±$ 0.6 | 39.1 $±$ 1.2 |              |            |            |        |              |              |              |

Table 6. Comparison with generative dataset distillation methods on ImageNet-Subsets under IPC=1.

## 4.5. Evaluation Protocol

The approach to assessing the performance of a synthetic dataset is as follows: firstly, a set of models is trained using the synthetic dataset. Once the training is complete, the trained models are validated using the corresponding validation set from the real dataset. For a specific model architecture, this process is repeated five times, and the average performance is calculated based on these repetitions. In previous studies, the evaluation method involved continuously optimizing the entire distillation process for 1000 epochs, with sampling the synthetic dataset every 100 epochs. The

<span id="page-6-2"></span>

Table 7. Time complexity (min) and performance (%) averaged on ImageNet-[A, B, C, D, E].

best performance among all sampled examples would then be selected. To ensure a fair comparison, we decompose StyleGAN-XL into  $G_{11} \circ \cdots \circ G_1 \circ G_0(\cdot)$  and apply the same optimizer and learning rate for each layer, optimizing for 100 steps. This ensures that the total number of optimization epochs remains consistent, thereby preventing performance improvements solely due to a higher number of optimization epochs. The comparison of time complexities and corresponding performance is shown in Table [7.](#page-6-2) Additionally, we adopt the same setup in our evaluation and sample the synthetic dataset after optimizing for 100 epochs in all of these different feature domains (i.e.,  $\mathbf{z}_{i}^{K-1}$ ). This approach prevents performance improvements obtained from implicitly selecting a dataset with a higher quality.

<span id="page-6-3"></span>

| Commponent               | ImNet-B                  | ImNet-C                  | <b>ImWoof</b>            | <b>ImNet-Fruits</b>      |
|--------------------------|--------------------------|--------------------------|--------------------------|--------------------------|
| <b>GLaD-TESLA</b>        | $51.9 \pm 1.3$           | $44.9 \pm 0.4$           | $23.4 \pm 1.1$           | $23.1 \pm 0.4$           |
| + Average Initialization | $53.5 \pm 0.7$           | $46.1 \pm 0.9$           | $24.8 \pm 1.1$           | $22.7 \pm 1.2\downarrow$ |
| + Hierarchical Layers    | $56.2 \pm 0.7$           | $48.1 \pm 0.9$           | $28.1 \pm 1.0$           | $24.1 \pm 0.5$           |
| + Distance Metric        | $57.4 \pm 0.3$           | $49.5 \pm 0.6$           | $28.3 \pm 0.2$           | $25.6 \pm 0.7$           |
| GLaD-DSA                 | $49.2 \pm 1.1$           | $42.0 \pm 0.6$           | $22.3 \pm 1.1$           | $20.7 \pm 1.1$           |
| + Average Initialization | $48.9 \pm 0.8\downarrow$ | $40.6 \pm 0.7\downarrow$ | $22.8 \pm 1.4$           | $21.3 \pm 1.0$           |
| + Hierarchical Layers    | $50.1 \pm 1.1$           | $43.1 \pm 1.4$           | $23.6 \pm 0.8$           | $21.9 \pm 0.8$           |
| + Distance Metric        | $50.7 \pm 0.9$           | $43.9 \pm 0.7$           | $24.0 \pm 0.8$           | $22.4 \pm 1.1$           |
| <b>GLaD-DM</b>           | $42.9 \pm 1.9$           | $39.4 \pm 1.7$           | $21.2 \pm 1.5$           | $21.8 \pm 1.8$           |
| + Average Initialization | $43.2 \pm 1.6$           | $39.9 \pm 1.7$           | $21.1 \pm 1.9\downarrow$ | $22.3 \pm 1.3$           |
| + Hierarchical Layers    | $44.2 \pm 2.1$           | $41.0 \pm 1.2$           | $23.1 \pm 0.9$           | $24.1 \pm 1.4$           |
| + Distance Metric        | $44.7 \pm 1.3$           | $41.1 \pm 1.3$           | $23.9 \pm 1.9$           | $24.4 \pm 2.1$           |

Table 8. Ablation study of each component with different distillation method across various ImageNet-Subset.

### 4.6. Ablation Studies

Effectiveness of Each Component As Table [8](#page-6-3) shows, the two major components of our method, i.e., hierarchical feature domains and class-relevant feature distance both improve the performance across various ImageNet-Subsets with all distillation methods, especially on TESLA. Optimizing in an unfixed feature space can bring significant gains, and on this basis, using class-relevant feature distance for implicit evaluation can yield a slight additional improvement. Please note that using class-relevant feature distance is infeasible without unfixed optimization spaces. Despite Initialization with averaged noise improves the performance of TESLA and DM to some degree, it cannot achieve stable improvement in the performance of the DSA method. We attribute this discrepancy to the inherent inclination towards noise and edge samples in DSA.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: A bar chart displays the accuracy of two methods, GLaD and H-PD, across different sampling steps. The x-axis is labeled "Sampling steps" and shows values of 50, 100, 150, 200, and 250. The y-axis is labeled "Accuracy" and ranges from 32 to 40. For each sampling step, there are two bars: a gray bar representing GLaD and a red bar representing H-PD. At 50 steps, GLaD has an accuracy of approximately 31.8, and H-PD has an accuracy of 36. At 100 steps, GLaD is at 34.5, and H-PD is at 37.8. At 150 steps, GLaD is at 35.5, and H-PD is at 38.3. At 200 steps, GLaD is at 36.3, and H-PD is at 39.3. Finally, at 250 steps, GLaD is at 37, and H-PD is at 38.2.

Figure 4. The comparison of performance  $(\%)$  at the same optimization epoch.

<span id="page-7-0"></span>

| <b>Steps</b> | <b>TESLA</b> | <b>DSA</b>   | DМ           |
|--------------|--------------|--------------|--------------|
| 20           | $46.9 + 1.2$ | $39.8 + 1.1$ | $39.1 + 1.2$ |
| 50           | $47.2 + 0.8$ | $41.6 + 0.8$ | $37.0 + 1.7$ |
| 100          | $50.3 + 0.6$ | $43.2 + 0.6$ | $36.5 + 1.4$ |
| 200          | $50.5 + 0.4$ | $43.0 + 0.6$ | $35.8 + 1.1$ |

Table 9. Ablation results of optimization steps per optimization space averaged on ImageNet-[A, B, C, D, E].

**Optimization Steps** We perform 100 optimization steps in each layer to align with the sampling method in previous works. We conduct more experiments across different optimization steps to explore the correct optimization steps. By observing the results shown in Table [9,](#page-7-0) we find that optimization steps beyond 100 do not yield significant performance improvements for TESLA and DSA methods. Meanwhile, optimization steps below 100 result in performance degradation. Considering the trade-off between effects and costs, we set the steps at 100 for both TESLA and DSA. For DM, however, optimal performance is attained at the least number of steps per layer, i.e., 20 steps. We compare the performance of GLaD and H-PD at the same epoch as

shown in Figure [4,](#page-7-1) our method outperforms and converges faster, demonstrating the superiority of the approach in utilizing the hierarchical features.

<span id="page-7-2"></span>Image /page/7/Figure/8 description: The image is a line graph showing the relationship between a normalized value on the x-axis and training epochs on the y-axis. There are three lines plotted: Accuracy (blue dashed line), Distance\_norm (orange line), and Loss\_norm (green line). The normalized values range from 40 to 220, and the training epochs range from 0.33 to 0.54. The Loss\_norm line shows a general upward trend, starting at approximately 0.35 and ending at around 0.52. The Distance\_norm line also shows an upward trend, starting at about 0.39 and ending at approximately 0.42. The Accuracy line fluctuates, starting at about 0.36, peaking around 0.38, and ending at approximately 0.39.

Figure 5. The relationship between searching basis and performance. Note that higher loss-norm values indicate lower loss values and the same applies to feature distances.

Searching Basis To avoid the time-consuming task of directly evaluating the synthetic dataset, we opt for classrelevant feature distance for implicit searching. Specifically, we evaluate the synthetic dataset at specific epochs during the optimization process and subsequently record its ground-truth performance, loss value, and feature distance. Figure [5](#page-7-2) demonstrates the recorded values during the optimization process. We normalize the loss values and feature distance to range [0, 1] for clear clarity and comparison. Our observation indicates that compared with the loss value, the feature distance consistently exhibits a stronger negative correlation with the performance.

## 5. Conclusion

In this paper, we present a novel approach to dataset distillation by exploring hierarchical parameterization space and successfully enhance the GAN-based parameterization method. Our method transforms the optimization space from a specific GAN feature domain to a broader feature space, addressing challenges seen in previous GAN-based parameterization methods. An advantage is that our approach provides a new insight for parameterization methods. Additionally, we anticipate that further improvements can be achieved through detailed optimization steps and optimization space combinations. The proposed H-PD optimization space combinations. re-explores and showcases the potential of hierarchical features in parameterization distillation for enhancing the performance under extreme compression ratios, contributing to an advanced dataset distillation approach.

Acknowledgement. This work is supported in part by the National Natural Science Foundation of China under grant 62171248, 62301189, Peng Cheng Laboratory (PCL2023A08), and Shenzhen Science and Technology Program under Grant KJZD20240903103702004, JCYJ20220818101012025, RCBS20221008093124061, GXWD20220811172936001.

# References

- <span id="page-8-8"></span>[1] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [2](#page-1-0)
- <span id="page-8-17"></span>[2] Andrew Brock, Theodore Lim, James M Ritchie, and Nick Weston. Neural photo editing with introspective adversarial networks. *arXiv preprint arXiv:1609.07093*, 2016. [3](#page-2-0)
- <span id="page-8-2"></span>[3] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020. [1](#page-0-1)
- <span id="page-8-10"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [2,](#page-1-0) [3,](#page-2-0) [4,](#page-3-2) [11,](#page-10-0) [17](#page-16-0)
- <span id="page-8-5"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-0) [4,](#page-3-2) [12,](#page-11-0) [17](#page-16-0)
- <span id="page-8-14"></span>[6] Lucy Chai, Jonas Wulff, and Phillip Isola. Using latent space regression to analyze and leverage compositionality in gans. *arXiv preprint arXiv:2103.10426*, 2021. [3](#page-2-0)
- <span id="page-8-25"></span>[7] Mingyang Chen, Jiawei Du, Bo Huang, Yi Wang, Xiaobo Zhang, and Wei Wang. Influence-guided diffusion for dataset distillation. In *The Thirteenth International Conference on Learning Representations*, 2025. [12](#page-11-0)
- <span id="page-8-13"></span>[8] Antonia Creswell, Tom White, Vincent Dumoulin, Kai Arulkumaran, Biswa Sengupta, and Anil A Bharath. Generative adversarial networks: An overview. *IEEE signal processing magazine*, 35(1):53–65, 2018. [3](#page-2-0)
- <span id="page-8-24"></span>[9] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. *Advances in Neural Information Processing Systems*, 35:810–822, 2022. [5,](#page-4-2) [17](#page-16-0)
- <span id="page-8-11"></span>[10] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [2](#page-1-0)
- <span id="page-8-19"></span>[11] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [5,](#page-4-2) [17](#page-16-0)
- <span id="page-8-3"></span>[12] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-1)
- <span id="page-8-0"></span>[13] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1,](#page-0-1) [5,](#page-4-2) [17](#page-16-0)
- <span id="page-8-15"></span>[14] Hao Fang, Bin Chen, Xuan Wang, Zhi Wang, and Shu-Tao Xia. Gifd: A generative gradient inversion method with fea-

ture domain optimization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 4967– 4976, 2023. [3](#page-2-0)

- [15] Hao Fang, Jiawei Kong, Wenbo Yu, Bin Chen, Jiawei Li, Shutao Xia, and Ke Xu. One perturbation is enough: On generating universal adversarial perturbations against vision-language pre-training models. *arXiv preprint arXiv:2406.05491*, 2024.
- <span id="page-8-16"></span>[16] Hao Fang, Yixiang Qiu, Hongyao Yu, Wenbo Yu, Jiawei Kong, Baoli Chong, Bin Chen, Xuan Wang, and Shu-Tao Xia. Privacy leakage on dnns: A survey of model inversion attacks and defenses. *arXiv preprint arXiv:2402.04013*, 2024. [3](#page-2-0)
- <span id="page-8-22"></span>[17] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [5](#page-4-2)
- <span id="page-8-7"></span>[18] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 15793–15803, 2024. [2,](#page-1-0) [3,](#page-2-0) [7,](#page-6-4) [12](#page-11-0)
- <span id="page-8-1"></span>[19] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1,](#page-0-1) [5,](#page-4-2) [17](#page-16-0)
- <span id="page-8-26"></span>[20] Jeremy Howard. A smaller subset of 10 easily classified classes from imagenet, and a little more french. *URL https://github. com/fastai/imagenette*, 2019. [17](#page-16-0)
- <span id="page-8-12"></span>[21] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [3,](#page-2-0) [11](#page-10-0)
- <span id="page-8-20"></span>[22] A Krizhevsky. Learning multiple layers of features from tiny images. *Master's thesis, University of Tront*, 2009. [5,](#page-4-2) [6,](#page-5-5) [17](#page-16-0)
- <span id="page-8-23"></span>[23] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [5,](#page-4-2) [17](#page-16-0)
- <span id="page-8-21"></span>[24] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [5](#page-4-2)
- <span id="page-8-9"></span>[25] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [2](#page-1-0)
- <span id="page-8-4"></span>[26] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023. [1](#page-0-1)
- <span id="page-8-6"></span>[27] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022. [1,](#page-0-1) [3,](#page-2-0) [11](#page-10-0)
- <span id="page-8-18"></span>[28] Yanbin Liu, Makoto Yamada, Yao-Hung Hubert Tsai, Tam Le, Ruslan Salakhutdinov, and Yi Yang. Lsmi-sinkhorn: Semi-supervised mutual information estimation with optimal transport. In *Machine Learning and Knowledge Discovery in*

*Databases. Research Track: European Conference, ECML PKDD 2021, Bilbao, Spain, September 13–17, 2021, Proceedings, Part I 21*, pages 655–670. Springer, 2021. [4](#page-3-2)

- <span id="page-9-17"></span>[29] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023. [5](#page-4-2)
- <span id="page-9-18"></span>[30] Mohammed Bany Muhammad and Mohammed Yeasin. Eigen-cam: Class activation map using principal components. In *2020 international joint conference on neural networks (IJCNN)*, pages 1–7. IEEE, 2020. [5,](#page-4-2) [13](#page-12-0)
- <span id="page-9-5"></span>[31] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [2](#page-1-0)
- <span id="page-9-6"></span>[32] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [2](#page-1-0)
- <span id="page-9-13"></span>[33] Yixiang Qiu, Hao Fang, Hongyao Yu, Bin Chen, MeiKang Qiu, and Shu-Tao Xia. A closer look at gan priors: Exploiting intermediate features for enhanced model inversion attacks. In *European Conference on Computer Vision*, pages 109–126. Springer, 2024. [3](#page-2-0)
- <span id="page-9-3"></span>[34] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10684–10695, 2022. [2](#page-1-0)
- <span id="page-9-12"></span>[35] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17097–17107, 2023. [2](#page-1-0)
- <span id="page-9-16"></span>[36] Axel Sauer, Katja Schwarz, and Andreas Geiger. Styleganxl: Scaling stylegan to large diverse datasets. In *ACM SIG-GRAPH 2022 conference proceedings*, pages 1–10, 2022. [3](#page-2-0)
- <span id="page-9-21"></span>[37] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European conference on computer vision*, pages 673–690. Springer, 2022. [7](#page-6-4)
- <span id="page-9-1"></span>[38] DongHyeok Shin, Seungjae Shin, and Il-chul Moon. Frequency domain-based dataset distillation. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-0) [12](#page-11-0)
- <span id="page-9-20"></span>[39] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [5,](#page-4-2) [17](#page-16-0)
- <span id="page-9-4"></span>[40] Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. D<sup>o</sup> 4: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 5809– 5818, 2024. [2,](#page-1-0) [3,](#page-2-0) [7,](#page-6-4) [12](#page-11-0)
- <span id="page-9-15"></span>[41] Ayush Tewari, Mohamed Elgharib, Florian Bernard, Hans-Peter Seidel, Patrick Pérez, Michael Zollhöfer, and Christian Theobalt. Pie: Portrait image embedding for semantic control. *ACM Transactions on Graphics (TOG)*, 39(6):1–14, 2020. [3](#page-2-0)

- <span id="page-9-27"></span>[42] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016. [17](#page-16-0)
- <span id="page-9-11"></span>[43] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [2](#page-1-0)
- <span id="page-9-0"></span>[44] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-0) [11](#page-10-0)
- <span id="page-9-14"></span>[45] Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. Gan inversion: A survey. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 45(3):3121–3138, 2022. [3,](#page-2-0) [5](#page-4-2)
- <span id="page-9-19"></span>[46] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024. [5](#page-4-2)
- <span id="page-9-22"></span>[47] Hongyi Zhang, Moustapha Cisse, Yann N Dauphin, and David Lopez-Paz. mixup: Beyond empirical risk minimization. *arXiv preprint arXiv:1710.09412*, 2017. [7](#page-6-4)
- <span id="page-9-9"></span>[48] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [2,](#page-1-0) [17](#page-16-0)
- <span id="page-9-2"></span>[49] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022. [1,](#page-0-1) [2,](#page-1-0) [3](#page-2-0)
- <span id="page-9-10"></span>[50] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [2,](#page-1-0) [3,](#page-2-0) [11](#page-10-0)
- <span id="page-9-8"></span>[51] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [2,](#page-1-0) [3,](#page-2-0) [11](#page-10-0)
- <span id="page-9-26"></span>[52] Xinhao Zhong, Bin Chen, Hao Fang, Xulin Gu, Shu-Tao Xia, and En-Hui Yang. Going beyond feature similarity: Effective dataset distillation based on class-aware conditional mutual information. *arXiv preprint arXiv:2412.09945*, 2024. [13](#page-12-0)
- <span id="page-9-23"></span>[53] Xinhao Zhong, Shuoyang Sun, Xulin Gu, Zhaoyang Xu, Yaowei Wang, Jianlong Wu, and Bin Chen. Efficient dataset distillation via diffusion-driven patch selection for improved generalization. *arXiv preprint arXiv:2412.09959*, 2024. [12](#page-11-0)
- <span id="page-9-7"></span>[54] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [2](#page-1-0)
- <span id="page-9-24"></span>[55] Chenyang Zhu, Kai Li, Yue Ma, Chunming He, and Xiu Li. Multibooth: Towards generating all your concepts in an image from text. *arXiv preprint arXiv:2404.14239*, 2024. [12](#page-11-0)
- <span id="page-9-25"></span>[56] Chenyang Zhu, Kai Li, Yue Ma, Longxiang Tang, Chengyu Fang, Chubin Chen, Qifeng Chen, and Xiu Li. Instantswap: Fast customized concept swapping across sharp shape differences. *arXiv preprint arXiv:2412.01197*, 2024. [12](#page-11-0)

# <span id="page-10-0"></span>Hierarchical Features Matter: A Deep Exploration of Progressive Parameterization Method for Dataset Distillation

Supplementary Material

# A. Literature Reviews on Dataset Distillation

## A.1. Dataset Distillation in Pixel Space

In this section, we review the methodology of optimizing synthetic dataset  $S$  with the surrogate objective in pixel space, which provides the basic optimization objective for all parameterization dataset distillation methods.

### A.1.1 DC [\[51\]](#page-9-8).

Dataset Distillation (DD) [\[44\]](#page-9-0) aims at optimizing the synthetic dataset  $S$  with a bi-level optimization. The main idea of bi-level optimization is that a network with parameter  $\theta_{\mathcal{S}}$ , which is trained on  $S$ , should minimize the risk of the real dataset  $\mathcal T$ . However, due to the need to pass through an unrolled computation graph, DD brings about a significant amount of time overhead. Based on this, DC introduces a surrogate objective, which aims at matching the gradients of a network during the optimization. For a network with parameters  $\theta_S$  trained on the synthetic data for some number of iterations, the matching loss is

$$
\mathcal{L}_{\text{DC}} = 1 - \frac{\nabla_{\theta} \ell^{\mathcal{S}}(\theta) \cdot \nabla_{\theta} \ell^{\mathcal{T}}(\theta)}{\|\nabla_{\theta} \ell^{\mathcal{S}}(\theta)\| \|\nabla_{\theta} \ell^{\mathcal{T}}(\theta)\|},\tag{7}
$$

where  $\ell^{\mathcal{T}}(\cdot)$  represents the loss function (e.g., CE loss) calculated on real dataset  $\mathcal T$ , and  $\ell^{\mathcal S}(\cdot)$  is the same loss function calculated on synthetic dataset  $\mathcal{T}$ .

### A.1.2 DM [\[50\]](#page-9-10).

Despite DC significantly reducing time consumption through surrogate, bi-level optimization still introduces a substantial amount of time overhead, especially when dealing with high-resolution and large-scale datasets. DM achieves this by using only the features extracted from networks  $\psi$  with random initialization as the matching target, the matching loss is

$$
\mathcal{L}_{\rm DM} = \sum_{c} \left\| \frac{1}{|\mathcal{T}_c|} \sum_{\mathbf{x} \in \mathcal{T}_c} \psi(\mathbf{x}) - \frac{1}{|\mathcal{S}_c|} \sum_{\mathbf{s} \in \mathcal{S}_c} \psi(\mathbf{s}) \right\|^2, \quad (8)
$$

where  $\mathcal{T}_c$  and  $\mathcal{S}_c$  represents the real and synthetic images from class c respectively.

### A.1.3 MTT [\[4\]](#page-8-10).

Distinct from the short-range optimization introduced from DC, MTT utilizes many expert trajectories  $\{\theta^*_t\}_0^T$  which are

obtained by training networks from scratch on the full real dataset and choose the parameter distance the matching objective. During the distillation process, a student network is initialized with parameters  $\theta_t^*$  by sample expert trajectory at timestamp  $t$  and then trained on the synthetic data for some number of iterations  $N$ , the matching loss is

$$
\mathcal{L}_{\text{MTT}} = \frac{\left\| \hat{\theta}_{t+N} - \theta_{t+M}^* \right\|^2}{\left\| \theta_t^* - \theta_{t+M}^* \right\|^2},\tag{9}
$$

where  $\theta_{t+M}^*$  represents the expert trajectory at timestamp  $t + M$ .

## A.2. Dataset Distillation in Feature Domain

In this section, we review the methodology of parameterization dataset distillation built upon the aforementioned dataset distillation methods, achieving better performance by employing a differentiable operation  $\mathcal{F}(\cdot)$  to shift the optimization space from pixel space to various feature domain, which can be formulated as

$$
\mathcal{S} = \{ \mathcal{F}(\mathbf{z}) \}. \tag{10}
$$

where z represents latent code in the feature domain corresponding to  $\mathcal{F}(\cdot)$ .

### A.2.1 HaBa [\[27\]](#page-8-6).

HaBa breaks the synthetic dataset into bases and a small neural network called hallucinator which is utilized to produce additional synthetic images. By leveraging this technique, the resulting model could be regarded as a differentiable operation and produce more diverse samples. However, HaBa simultaneously optimizes the bases and the hallucinator, neglecting the relationship between the two feature domains. This leads to unstable optimization during the training process.

### A.2.2 IDC [\[21\]](#page-8-12).

IDC proposes a principle that small-sized synthetic images often carry more effective information under the same spatial budget and utilize an upsampling module as the differentiable operation. Despite employing a differentiable operation, the optimization of IDC is still the pixel space, which resulted in the loss of effective information gain obtained from other feature domains.

<span id="page-11-0"></span>

### A.2.3 FreD [\[38\]](#page-9-1).

FreD suggests that optimizing for the main subject in the synthetic image is more instructive than optimizing for all the details. Therefore, FreD employs discrete cosine transform (DCT) as the differentiable operation and uses a learnable mask matrix to remove high-frequency information, ensuring that the optimization process only occurs in the low-frequency domain. This allows the synthetic dataset to achieve higher performance and generalization. However, FreD overlooks the effective guiding information within the high-frequency domain and fails to connect the two feature domains produced by DCT, leading to potential incomplete optimization.

### A.2.4 GLaD [\[5\]](#page-8-5).

Different from existing methods [\[7,](#page-8-25) [18,](#page-8-7) [40,](#page-9-4) [53\]](#page-9-23) utilizing diffusion models [\[55,](#page-9-24) [56\]](#page-9-25), GLaD employs a pre-trained generative model (i.e., GAN) and distills the synthetic dataset in the corresponding latent space. By leveraging the capability of a generative model to map latent noise to image patterns, GLaD achieves better generalization to unseen architecture and scale to high-dimensional datasets. However, for StyleGAN, the earlier layers tend to provide the information about the main subject in an image while the later layers often contribute to the details. However, GLaD attempts to balance the low-frequency information with the high-frequency information by selecting an intermediate layer as a fixed optimization space, discarding the guiding information from the earlier layers can lead to incomplete optimization. Another limitation of GLaD is the need for a large number of preliminary experiments. GLaD selects a specific intermediate layer suitable for all datasets for different distillation methods, However, under the same distillation method, the optimal intermediate layer corresponding to different datasets is not the same, especially when the manifold of the datasets varies greatly, which suggests that GLaD cannot spontaneously adapt to different datasets, distillation methods, and GANs.

## B. Additional Experimental Results

## B.1. More Comparisons with GLaD

To expand the optimization space, the method we proposed utilizes hierarchical feature domains composed of intermediate layers from GAN. To investigate whether optimization across multiple feature domains is superior to optimization within a single fixed feature domain, we evaluate the performance by simply expanding the optimization space based on the baseline. As shown in Table [10,](#page-12-1) compared to GLaD, which only selects a single yet optimal intermediate layer of the GAN as the optimization space, H-PD has successfully achieved considerable improvement, validating our viewpoint that the optimization result from the previous feature domain can serve as better starting point for subsequent feature domain. Please note the result is obtained by not selecting  $S^*$ .

<span id="page-11-1"></span>Image /page/11/Figure/8 description: The image displays a grid of four images in the top row labeled "GLaD" and four images in the bottom row labeled "H-PD". The top row shows a group of flamingos, a fluffy white cat, a close-up of red berries, and sliced starfruit. The bottom row shows a similar group of flamingos, a fluffy white cat from a different angle, a close-up of a pomegranate, and a group of apricots or peaches.

Figure 6. The comparison of visualization.

To present a more comprehensive comparison, we evaluate the cross-architecture performance of a high-resolution synthetic dataset under the same setting (i.e., DSA on ImageNet-[A, B, C, D, E] under IPC=1). As shown in Table [11,](#page-12-2) our proposed H-PD still achieves considerable improvements, demonstrating the stability of our proposed method. Figure [6](#page-11-1) illustrates the comparison of synthetic dataset visualization generated by H-PD and GLaD using the same initial image. The images produced by H-PD achieve a good balance between content and style. On one hand, H-PD tends to preserve more main subject information by optimizing in the earlier layers of the GAN. On the other hand, since H-PD also undergoes optimization in the later layers, the synthetic images tend to be sharper and rarely produce the kaleidoscope-like patterns that are common in the GLaD method.

## B.2. Visualizing Morphological Transition of Synthetic Images

As shown in Figure [7a,](#page-12-3) we demonstrate the visualization changes of the synthetic image throughout the optimization process. Layer 0 represents the initial image produced by StyleGAN-XL using averaged noise, and Layer  $i$  indicates the image when the optimization space reaches layer  $i$ . In the early stage of optimization, since the optimization space is located in the earlier layer of the GAN, the optimization object primarily focus on the main subject of the synthetic image. Meanwhile, GAN still maintains a high degree of integrity which leads to a strong constraint on the slight changes in the latent produced during the optimization process, which can be transformed into patterns resembling real images instead of noises. Thus the tendency in the early stage of optimization is to generate images that better conform to the constraint of distillation loss yet appear more realistic, leading to produce synthetic images that can be regarded as a better starting point for the subsequent

<span id="page-12-1"></span><span id="page-12-0"></span>

| Alg.         | <b>Opimization Space</b> | ImNet-A        | ImNet-B      | ImNet-C        | ImNet-D        | ImNet-E        | ImNette        | <b>ImWoof</b> | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------------|--------------------------|----------------|--------------|----------------|----------------|----------------|----------------|---------------|----------------|----------------|----------------|
|              | Fixed (Pixel)            | $51.7 \pm 0.2$ | $53.3 + 1.0$ | $48.0 \pm 0.7$ | $43.0 \pm 0.6$ | $39.5 \pm 0.9$ | $41.8 \pm 0.6$ | $22.6 + 0.6$  | $37.3 \pm 0.8$ | $22.4 \pm 1.1$ | $22.6 \pm 0.4$ |
| <b>TESLA</b> | Fixed (GAN)              | $50.7 + 0.4$   | $51.9 + 1.3$ | $44.9 \pm 0.4$ | $39.9 + 1.7$   | $37.6 + 0.7$   | $38.7 + 1.6$   | $23.4 + 1.1$  | $35.8 + 1.4$   | $23.1 \pm 0.4$ | $26.0 \pm 1.1$ |
|              | Unfixed                  | $53.1 + 0.8$   | $55.4 + 0.7$ | $47.5 + 0.9$   | $44.1 + 0.6$   | $40.8 + 0.7$   | $42.8 + 1.0$   | $27.0 + 0.6$  | $37.6 \pm 0.9$ | $24.7 \pm 0.7$ | $28.3 \pm 0.8$ |
|              | Fixed (Pixel)            | $43.2 + 0.6$   | $47.2 + 0.7$ | $41.3 + 0.7$   | $34.3 + 1.5$   | $34.9 + 1.5$   | $34.2 + 1.7$   | $22.5 + 1.0$  | $32.0 \pm 1.5$ | $21.0 \pm 0.9$ | $22.0 \pm 0.6$ |
| <b>DSA</b>   | Fixed (GAN)              | $44.1 \pm 2.4$ | $49.2 + 1.1$ | $42.0 + 0.6$   | $35.6 \pm 0.9$ | $35.8 + 0.9$   | $35.4 + 1.2$   | $22.3 + 1.1$  | $33.8 \pm 0.9$ | $20.7 \pm 1.1$ | $22.6 \pm 0.8$ |
|              | Unfixed                  | $46.1 \pm 0.7$ | $50.0 + 0.9$ | $43.8 + 1.4$   | $37.1 \pm 0.9$ | $36.6 \pm 0.6$ | $36.2 + 0.5$   | $22.7 + 0.3$  | $34.9 \pm 1.5$ | $21.2 \pm 0.8$ | $23.1 \pm 0.3$ |
|              | Fixed (Pixel)            | $39.4 + 1.8$   | $40.9 + 1.7$ | $39.0 + 1.3$   | $30.8 + 0.9$   | $27.0 + 0.8$   | $30.4 + 2.7$   | $20.7 + 1.0$  | $26.6 + 2.6$   | $20.4 \pm 1.9$ | $20.1 + 1.2$   |
| DM           | Fixed (GAN)              | $41.0 + 1.5$   | $42.9 + 1.9$ | $39.4 + 1.7$   | $33.2 + 1.4$   | $30.3 \pm 1.3$ | $32.2 + 1.7$   | $212+15$      | $27.6 \pm 1.9$ | $21.8 \pm 1.8$ | $22.3 \pm 1.6$ |
|              | Unfixed                  | $42.3 + 1.5$   | $44.1 + 1.5$ | $41.3 + 1.7$   | $33.7 + 1.1$   | $31.5 + 1.1$   | $34.0 + 1.2$   | $23.1 + 1.3$  | $28.9 + 1.4$   | $24.3 \pm 1.3$ | $22.8 \pm 0.8$ |

Table 10. Abltion study on optimization space comparison. "Fixed (Pixel)" refers to optimize in pixel space and "Fixed (GAN)" refers to GLaD, while Unfixed refers to optimize in multiple feature domains.

<span id="page-12-3"></span>Image /page/12/Figure/2 description: The image displays a comparison of synthetic images and their corresponding class activation maps (CAMs) across different layers (0, 6, 12, 18, 24) for three models: TESLA, DSA, and DM. The top row shows TESLA, with synthetic images of pelicans and their CAMs. The middle row shows DSA, with synthetic images of skydivers and their CAMs. The bottom row shows DM, with synthetic images of a ship and its CAMs. The synthetic images progress from clear representations at Layer 0 to more abstract or distorted versions at later layers. The CAMs are overlaid on the synthetic images, indicating the regions of interest for each model's prediction at different stages of processing.

(a) Visualization of synthetic images.

(b) Visualization of corresponding CAM.

Figure 7. The visualization change of synthetic images and corresponding CAM during the optimization process using different distillation methods. "Layer" refers to the index of intermediate layers provided by StyleGAN-XL.

<span id="page-12-2"></span>

|  | Method ImNet-A ImNet-B ImNet-C ImNet-D ImNet-D                                   |  |
|--|----------------------------------------------------------------------------------|--|
|  | Pixel $38.3 \pm 4.7$ $32.8 \pm 4.1$ $27.6 \pm 3.3$ $25.5 \pm 1.2$ $23.5 \pm 2.4$ |  |
|  | GLaD $37.4 \pm 5.5$ $41.5 \pm 1.2$ $35.7 \pm 4.0$ $27.9 \pm 1.0$ $29.3 \pm 1.2$  |  |
|  | H-PD $40.7 \pm 2.1$ $42.9 \pm 1.8$ $37.2 \pm 2.2$ $30.1 \pm 1.7$ $29.7 \pm 1.8$  |  |

Table 11. Higher-resolution (256×256) synthetic dataset (using DSA) cross-architecture performance (%).

optimization process.

In the later stage of optimization, the main subject of the synthetic image no longer undergoes significant changes, and the optimization objective shifts along with the movement of the optimization space, focusing more on the details of the synthetic images. As shown in Figure [7a,](#page-12-3) due to the weakened generative constraint of the incomplete GAN, the final synthetic image becomes similar to the indistinguishable and distorted image produced by existing distillation methods. Building upon the better synthetic image obtained through the optimization process in the earlier layers, different distillation methods gradually incorporate more guidance-oriented customized patterns into the synthetic image, achieving further performance improvement, which has also been proved by recent work [\[52\]](#page-9-26).

# B.3. Qualitative Interpretation using CAM

We additionally introduce CAM [\[30\]](#page-9-18) to visualize the heatmap of class-relevant information in the synthetic images as shown in Figure [7b,](#page-12-3) which also demonstrates our perspective from another aspect. The blue areas represent

<span id="page-13-0"></span>

| Layers         | Optimization | ImNet-A      | ImNet-B        | $ImNet-C$      | ImNet-D        | ImNet-E        | ImNette        | <b>ImWoof</b>  | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|----------------|--------------|--------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                | 50           | $536+02$     | $55.2 + 1.5$   | $47.3 + 0.5$   | $44.1 \pm 0.7$ | $40.5 \pm 1.1$ | $43.8 + 0.4$   | $26.6 + 0.7$   | $37.1 \pm 0.6$ | $22.9 \pm 0.5$ | $27.8 \pm 1.0$ |
|                | 100          | $55.3 + 0.8$ | $57.1 \pm 0.7$ | $49.1 \pm 0.9$ | $46.6 \pm 0.4$ | $42.2 \pm 1.5$ | $44.9 \pm 1.2$ | $28.6 \pm 0.6$ | $39.4 \pm 0.8$ | $25.9 \pm 0.7$ | $30.1 \pm 1.2$ |
|                | 200          | $55.4 + 0.7$ | $57.5 + 1.1$   | $48.6 + 0.8$   | $46.2 \pm 0.9$ | 43.6 $\pm 0.6$ | $45.7 \pm 0.5$ | $28.7 + 0.4$   | $39.4 \pm 0.6$ | $25.5 \pm 0.5$ | $29.8 \pm 0.2$ |
|                | 50           | $51.3 + 0.9$ | $54.2 \pm 1.1$ | $46.3 + 0.8$   | $44.1 \pm 1.2$ | $40.3 + 1.2$   | $41.8 + 1.4$   | $27.1 \pm 0.6$ | $36.5 \pm 1.1$ | $23.0 \pm 1.2$ | $28.1 \pm 1.3$ |
| 2              | 100          | $55.1 + 0.6$ | $57.4 \pm 0.3$ | 49.5 $\pm 0.6$ | $46.3 \pm 0.9$ | $43.0 \pm 0.6$ | $45.4 + 1.1$   | $28.3 \pm 0.2$ | $39.7 \pm 0.8$ | $25.6 \pm 0.7$ | $29.6 \pm 1.0$ |
|                | 200          | $55.6 + 0.9$ | $57.9 + 0.5$   | $49.4 \pm 0.3$ | $46.0 \pm 0.1$ | $43.5 \pm 0.4$ | $45.1 + 0.7$   | $28.6 \pm 0.2$ | $39.3 \pm 0.8$ | $25.9 + 1.1$   | $29.9 \pm 0.6$ |
|                | 50           | $51.8 + 0.7$ | $52.9 + 1.2$   | $46.1 + 1.5$   | $42.3 + 0.5$   | $39.8 + 0.5$   | $40.9 + 1.3$   | $24.7 + 1.1$   | $35.9 \pm 0.5$ | $21.2 + 1.7$   | $25.3 + 1.1$   |
| $\overline{4}$ | 100          | $53.3 + 0.8$ | $54.2 \pm 1.1$ | $47.3 + 1.2$   | $41.8 + 1.7$   | $42.7 + 0.6$   | $27.7 + 0.5$   | $27.1 + 1.0$   | $27.0 \pm 0.9$ | $22.5 \pm 1.4$ | $26.4 \pm 1.2$ |
|                | 200          | $55.0 + 1.0$ | $57.0 + 1.3$   | $48.1 + 1.6$   | $45.2 \pm 0.5$ | $42.1 \pm 1.4$ | $45.0 \pm 0.5$ | $27.2 + 0.9$   | $38.8 \pm 1.1$ | $24.6 \pm 0.5$ | $28.4 \pm 0.8$ |

Table 12. Abltion study on layers combination and optimization allocation using TESLA. "Layers" refers to the number of layers per optimization space, "Optimization" refers to the number of SGD steps allocated in each optimization space.

<span id="page-13-1"></span>

| Layers | Optimization | ImNet-A        | ImNet-B        | ImNet-C        | ImNet-D        | ImNet-E        | ImNette        | ImWoof         | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------|--------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| 1      | 50           | $45.2 pm 1.2$ | $48.3 pm 1.3$ | $42.0 pm 0.4$ | $36.2 pm 0.7$ | $35.0 pm 0.8$ | $35.8 pm 1.1$ | $22.7 pm 1.0$ | $33.5 pm 0.5$ | $21.1 pm 1.5$ | $22.7 pm 0.8$ |
|        | 100          | $46.2 pm 0.7$ | $51.1 pm 0.4$ | $43.3 pm 1.1$ | $37.2 pm 0.5$ | $36.6 pm 0.9$ | $36.7 pm 1.3$ | $22.9 pm 0.8$ | $35.6 pm 1.1$ | $22.1 pm 1.5$ | $23.8 pm 0.7$ |
|        | 200          | $46.5 pm 0.9$ | $50.7 pm 1.1$ | $43.8 pm 0.2$ | $37.3 pm 0.7$ | $37.6 pm 0.7$ | $36.9 pm 1.3$ | $24.3 pm 0.5$ | $34.9 pm 0.3$ | $22.6 pm 1.3$ | $23.6 pm 0.7$ |
| 2      | 50           | $44.8 pm 0.4$ | $48.9 pm 0.9$ | $42.1 pm 1.1$ | $35.6 pm 1.0$ | $36.6 pm 0.6$ | $34.2 pm 1.1$ | $22.1 pm 0.6$ | $33.3 pm 1.6$ | $20.0 pm 1.3$ | $22.7 pm 0.8$ |
|        | 100          | $46.9 pm 0.8$ | $50.7 pm 0.9$ | $43.9 pm 0.7$ | $37.4 pm 0.4$ | $37.2 pm 0.3$ | $36.9 pm 0.8$ | $24.0 pm 0.8$ | $35.3 pm 1.0$ | $22.4 pm 1.1$ | $24.1 pm 0.9$ |
|        | 200          | $46.8 pm 0.5$ | $50.8 pm 0.3$ | $43.4 pm 0.6$ | $37.0 pm 1.3$ | $37.3 pm 0.5$ | $37.1 pm 0.7$ | $23.8 pm 1.3$ | $35.6 pm 1.1$ | $22.1 pm 1.2$ | $24.6 pm 1.3$ |
| 4      | 50           | $43.6 pm 0.7$ | $47.8 pm 0.7$ | $40.4 pm 0.6$ | $34.6 pm 0.5$ | $34.2 pm 0.8$ | $33.4 pm 1.2$ | $21.3 pm 0.9$ | $32.7 pm 1.4$ | $19.9 pm 0.5$ | $21.6 pm 0.6$ |
|        | 100          | $45.7 pm 0.7$ | $49.4 pm 0.9$ | $43.1 pm 1.1$ | $36.1 pm 1.3$ | $36.4 pm 0.8$ | $35.2 pm 0.6$ | $23.4 pm 1.1$ | $34.7 pm 0.5$ | $21.3 pm 1.1$ | $23.5 pm 1.3$ |
|        | 200          | $46.3 pm 0.8$ | $50.1 pm 0.9$ | $43.2 pm 0.7$ | $37.0 pm 0.4$ | $36.8 pm 1.6$ | $36.2 pm 1.0$ | $23.3 pm 1.3$ | $34.4 pm 1.4$ | $21.6 pm 0.8$ | $23.7 pm 0.5$ |

Table 13. Abltion study on layers combination and optimization allocation using DSA.

regions of class-relevant information, which can produce the largest gradient during the training process. Conversely, the red areas indicate regions of class-irrelevant information, with deeper colors signifying higher degrees of corresponding information. In the early stage of optimization, the class-relevant information of the main subject in the synthetic image produced by various distillation methods is compressed.

Interestingly, for the gradient matching methods TESLA and DSA, which rely on long-range and short-range gradient matching respectively, the class-relevant information of the main subject remains unchanged when optimization space changes to later layers, while the gradient that can be produced by the image background (e.g., corners) are further decreased, as indicated by the deeper red color, even though the changes in the background are hardly observable by the naked eye during the optimization process. However, for the feature matching method DM, compared to the visualized kaleidoscope-like pattern, the visualization of corresponding CAM shows an unbalanced distribution and focuses on areas not typically observed by humans. We believe this phenomenon also explains the poorer performance of DM compared with gradient matching methods. Compared to the synthetic images with a centralized concen-

tration of class-relevant information produced by TESLA and DSA, the images generated by DM are too diverse due to fitting all the features of the entire dataset including the class-irrelevant features, which is disadvantageous for training neural networks on tiny distilled datasets.

## B.4. Layers Combination and Optimization Allocation

As discussed, we adopt a uniform sampling method that evaluates the synthetic dataset per 100 optimization epochs (even less when using DM) to align with the evaluation method of the baseline (i.e., GLaD). Additionally, we decompose StylGAN-XL into  $G_{11} \circ \cdots \circ G_1 \circ G_0(\cdot)$  to align with the time complexity of the baseline. We present an ablation study on the allocation of optimization epochs per optimization space. Building on this, we further explore the impact of combining different numbers of intermediate layers into a single optimization space and allocating different numbers of optimization epochs to each optimization space on the performance of the synthetic dataset. For all distillation methods, we explore the impact of varying optimization spaces by using combinations of 1, 2, and 4 intermediate layers within each optimization space. Under the same optimization space setting, for TESLA and DSA, we

investigated the effects of different numbers of optimization epochs allocated to each optimization space by using 50, 100, and 200. For DM, due to the overfitting issue caused by feature matching, we used 10, 20, and 50 as the number of optimization epochs per optimization space.

The results for TESLA and DSA are shown in Table [12](#page-13-0) and Table [13.](#page-13-1) Combining 1 or 2 intermediate layers as a single optimization space does not produce a significant impact on the performance, indicating that existing redundant feature spaces provided by GAN contribute little to the distillation tasks and may even lead to a negative effect. Under this setting, allocating 50 optimization epochs per optimization space produces a clear phenomenon of optimization not converging. However, when the number of optimization epochs comes to 100 or 200, the optimization converges without significant performance differences. Achieved by implicitly selecting the optimal synthetic dataset through the proposed class-relevant feature distance metric, allowing us to avoid overfitting issues to some extent through a certain level of optimization path withdrawal. Therefore, we choose 100 epochs as the basic setting to reduce time complexity in the actual training process. When using 4 intermediate layers as an optimization space, the performance is decreased even when setting optimization epochs to 200, indicating that too few feature domains could not provide sufficiently rich guiding information, forcing the optimization process to require more epochs to converge, demonstrating the superiority of our proposed H-PD in utilizing multiple feature domains.

The results for DM are shown in Table [14.](#page-15-0) Similar to TESLA and DSA, Combining 1 or 2 intermediate layers as a single optimization space results in similar performance, while combining 4 intermediate layers as optimization space leads to a significant performance drop. However, under the same optimization space settings, an excessive number of optimization epochs often leads to a severe decline in performance when using DM as the distillation method. As aforementioned, DM is unable to focus on class-relevant information, which causes an irreversible loss of the main subject information in the synthetic image after deploying a large number of optimization epochs in a specific feature domain, which in turn leads to a situation where the informative guidance provided by subsequent feature domains could not be effectively incorporated into the synthetic image, resulting in performance degradation. In this case, even the proposed class-relevant feature distance could not effectively select a superior synthetic dataset. To align with the approach of decomposing GAN used in TESLA and DSA, we ultimately combine 2 intermediate layers as an optimization space and deploy 20 optimization epochs as the experimental setting for DM.

<span id="page-14-0"></span>Image /page/14/Figure/3 description: A line graph displays three lines representing MTT\_norm (blue), DC\_norm (orange), and DM\_norm (green). The x-axis ranges from 1 to 11, and the y-axis ranges from 0.48 to 0.63. The MTT\_norm line starts at approximately 0.63, fluctuates between 0.60 and 0.63, and ends around 0.625. The DC\_norm line begins at about 0.63, generally decreases to around 0.54 by x=10, and then rises slightly to about 0.57. The DM\_norm line shows a steep decline from approximately 0.63 at x=1 to around 0.48 at x=10, with a slight increase to about 0.49 at x=11.

Figure 8. Quantitative results of loss function value using different distillation methods. Note that we normalize all the values for clear comparison.

### B.5. Ablation Study on Searching Strategy

To better utilize the informative guidance provided by multiple feature domains, we propose class-relevant feature distance as an evaluation metric for implicitly selecting the optimal synthetic dataset. We demonstrate the ablation study using different implicit evaluation metrics, as shown in Table [15,](#page-15-1) the metric we proposed outperforms the use of loss function value corresponding to the distillation methods as the metric under all settings. It is worth noting that, although the accuracy of the model trained on the synthetic dataset can be used as an explicit evaluation metric for the data distillation task, the evaluation process incurred much greater time overhead than the distillation task itself, rendering it impractical for actual training processes.

To explore the principle of the superiority of classrelevant feature distance, we first discussed the respective limitations of directly using existing distillation loss function value as the evaluation metric. The tendency of different distillation loss functions is shown in Figure [8.](#page-14-0) For TESLA, the loss function is obtained by calculating the distance between the student network parameters and the teacher network parameters. However, in order to consider diversity, TESLA selects a random initialization method when initializing the student network parameters, and the expert trajectory also comes from the training process of models with different initialization, leading to a significant fluctuation caused by utilizing different initialization parameters. For DSA, the loss function utilizes neural network gradients as guidance. However, when IPC=1, the proxy neural network used in each optimization process is randomly initialized, causing DSA to face the same issue as TESLA, where the loss function is affected by network parameter initialization. As for DM, the loss function is obtained from the feature distance between the dataset features extracted by randomly initialized networks, resulting in the same impact of network initialization parameters on this loss function. Additionally, DM suffers from severe overfitting in the later stages of optimization due to fitting

<span id="page-15-0"></span>

| Layers | Optimization | ImNet-A      | ImNet-B      | ImNet-C        | ImNet-D        | ImNet-E        | ImNette        | <b>ImWoof</b>  | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------|--------------|--------------|--------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|        | 10           | $42.1 + 2.2$ | $44.1 + 1.6$ | $41.7 + 1.7$   | $33.9 + 1.2$   | $31.3 + 1.9$   | $34.2 \pm 2.1$ | $24.1 + 1.4$   | $29.7 \pm 0.7$ | $24.1 \pm 1.6$ | $22.6 \pm 1.3$ |
|        | 20           | $41.6 + 1.6$ | $44.8 + 1.8$ | $41.3 + 1.4$   | $34.1 \pm 2.1$ | $31.2 \pm 0.5$ | $33.7 \pm 0.6$ | $24.0 + 1.3$   | $29.6 \pm 1.7$ | $23.4 \pm 0.8$ | $23.7 \pm 1.9$ |
|        | 50           | $40.2 + 1.6$ | $43.4 + 1.7$ | $40.2 + 2.0$   | $33.1 + 1.3$   | $29.7 + 1.8$   | $32.6 + 1.9$   | $23.1 + 2.1$   | $28.2 \pm 1.6$ | $22.1 \pm 0.8$ | $21.0 \pm 0.5$ |
|        | 10           | $414+17$     | $43.5 + 1.3$ | $40.4 + 0.9$   | $34.1 + 1.3$   | $31.3 + 1.8$   | $33.6 + 1.7$   | $22.4 + 1.6$   | $28.3 \pm 2.1$ | $23.1 \pm 1.7$ | $22.9 + 1.5$   |
| 2      | 20           | $42.8 + 1.2$ | $44.7 + 1.3$ | $41.1 \pm 1.3$ | $34.8 \pm 1.5$ | $31.9 \pm 0.9$ | $34.8 \pm 1.0$ | $23.9 + 1.9$   | $29.5 \pm 1.5$ | $24.4 \pm 2.1$ | $24.2 \pm 1.1$ |
|        | 50           | $40.1 + 1.8$ | $42.6 + 2.0$ | $40.2 + 1.6$   | $32.6 + 1.7$   | $29.7 + 1.3$   | $33.1 + 0.6$   | $21.6 + 0.7$   | $27.7 \pm 1.6$ | $22.2 \pm 1.3$ | $22.4 \pm 1.9$ |
|        | 10           | $39.9 + 1.4$ | $42.5 + 1.0$ | $40.4 \pm 1.8$ | $32.4 \pm 1.6$ | $30.1 \pm 2.4$ | $32.7 + 2.3$   | $20.9 + 1.6$   | $27.5 + 2.2$   | $22.5 + 1.7$   | $21.8 + 1.2$   |
| 4      | 20           | $40.6 + 1.3$ | $42.5 + 1.6$ | $39.6 + 2.1$   | $32.2 \pm 1.5$ | $30.1 \pm 1.3$ | $32.9 + 1.8$   | $21.6 + 1.5$   | $27.3 \pm 1.2$ | $21.7 \pm 2.3$ | $22.3 + 1.6$   |
|        | 50           | $40.4 + 1.7$ | $42.7 + 1.3$ | $39.9 \pm 1.2$ | $32.0 \pm 1.4$ | $30.3 \pm 1.9$ | $32.6 + 1.6$   | $22.0 \pm 1.1$ | $27.8 \pm 0.9$ | $21.1 \pm 1.7$ | $22.6 \pm 1.4$ |

Table 14. Abltion study on layers combination and optimization allocation using DM.

<span id="page-15-1"></span>

| Alg.         | Searching Basis          | ImNet-A        | ImNet-B      | $ImNet-C$      | ImNet-D        | ImNet-E        | ImNette        | <b>ImWoof</b>  | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------------|--------------------------|----------------|--------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|              | $\overline{\phantom{a}}$ | $54.7 + 0.8$   | $56.2 + 0.7$ | $48.1 \pm 0.9$ | $45.4 + 0.9$   | $41.8 + 0.6$   | $43.8 \pm 0.8$ | $28.1 \pm 1.0$ | $38.5 \pm 1.2$ | $24.1 \pm 0.5$ | $28.7 \pm 0.9$ |
| <b>TESLA</b> | Loss Value               | $53.6 \pm 0.9$ | $56.9 + 0.7$ | $48.3 \pm 0.8$ | $45.0 + 0.6$   | $41.0 + 1.2$   | $44.5 \pm 0.8$ | $27.5 + 1.4$   | $37.8 \pm 0.7$ | $25.1 \pm 0.9$ | $27.6 \pm 1.0$ |
|              | <b>Feature Distance</b>  | $55.1 + 0.6$   | $57.4 + 0.3$ | $49.5 + 0.6$   | $46.3 \pm 0.9$ | $43.0 + 0.6$   | $45.4 + 1.1$   | $28.3 + 0.2$   | $39.7 \pm 0.8$ | $25.6 \pm 0.7$ | $29.6 \pm 1.0$ |
|              | $\overline{\phantom{a}}$ | $45.9 + 0.7$   | $50.1 + 1.1$ | $43.1 \pm 1.4$ | $36.9 \pm 0.8$ | $36.8 \pm 0.6$ | $36.0 + 0.9$   | $23.6 \pm 0.8$ | $34.5 \pm 0.4$ | $21.9 \pm 0.8$ | $23.2 \pm 0.9$ |
| <b>DSA</b>   | Loss Value               | $46.6 + 1.3$   | $48.9 + 1.7$ | $43.6 + 1.1$   | $36.1 + 1.2$   | $36.6 + 0.5$   | $36.2 + 0.9$   | $23.1 + 0.6$   | $33.6 \pm 0.7$ | $21.3 + 1.1$   | $22.8 \pm 1.0$ |
|              | <b>Feature Distance</b>  | $46.9 + 0.8$   | $50.7 + 0.9$ | 43.9 $\pm$ 0.7 | $37.4 + 0.4$   | $37.2 + 0.3$   | $36.9 + 0.8$   | $24.0 + 0.8$   | $35.3 + 1.0$   | $22.4 + 1.1$   | $24.1 \pm 0.9$ |
|              | $\overline{\phantom{a}}$ | $42.4 + 1.6$   | $44.2 + 2.1$ | $41.0 \pm 1.2$ | $34.0 + 1.2$   | $31.1 + 1.0$   | $34.5 + 2.1$   | $23.1 + 0.9$   | $29.0 \pm 1.5$ | $24.1 \pm 1.4$ | $22.6 \pm 1.5$ |
| DM           | Loss Value               | $41.6 + 1.8$   | $44.4 + 1.4$ | $40.7 + 2.1$   | $34.6 + 1.7$   | $30.1 + 1.3$   | $34.5 + 1.3$   | $23.6 + 1.2$   | $28.7 + 1.3$   | $24.4 + 1.3$   | $21.2 + 1.2$   |
|              | <b>Feature Distance</b>  | $42.8 + 1.2$   | $44.7 + 1.3$ | $41.1 + 1.3$   | $34.8 + 1.5$   | $31.9 + 0.9$   | $34.8 + 1.0$   | $23.9 + 1.9$   | $29.5 + 1.5$   | $24.4 + 2.1$   | $24.2 + 1.1$   |

Table 15. Quantitative results on searching basis. "-" refers to not employing a searching strategy, "Loss Value" refers to directly using corresponding loss function value as the searching basis, "Feature Distance" refers to the use of proposed class-relevant distance as a searching basis

to the useless features. In summary, the loss functions corresponding to the three distillation methods could not serve as effective evaluation metrics due to the excessive diversity.

<span id="page-15-2"></span>Image /page/15/Picture/5 description: Two images are displayed side-by-side, each featuring a heatmap overlay on a photograph. The underlying photograph appears to show a robotic arm or a complex mechanical structure with multiple components. The heatmap uses a gradient of colors, including red, yellow, green, blue, and purple, to highlight areas of interest or intensity within the scene. The left image shows a broader view with the heatmap concentrated on the upper and central parts of the structure, with a gradient from red at the bottom to blue/purple at the top. The right image is a slightly closer view, with the heatmap also covering the central and upper portions, showing a similar color distribution but with some variations in the intensity and spread of the colors, particularly in the central area where blues and greens are more prominent.

Figure 9. The visualization comparison of CAM between pretrained model and random model using DM.

Distinguished from existing distillation methods, where the loss function is influenced by the need to fit diversity, our proposed class-relevant feature distance effectively addresses this issue by using CAM, which is calculated by utilizing a pre-trained neural network, and we utilize a ResNet-18 trained on ImageNet-1k as a proxy model for computing CAM. As shown in Figure [9,](#page-15-2) we demonstrate the difference between the visualization obtained using the pretrained model and those obtained using a randomly initialized model. The observation indicates that there is a significant difference in the regions of interest for the two, by utilizing a pre-trained model with fixed parameters, we can better identify the feature regions that are beneficial for the classification task (i.e., larger gradients). Therefore, our proposed metric successfully leverages this strong supervisory signal to achieve data selection while eliminating the strong correlation between the loss function and the proxy model parameters.

<span id="page-15-3"></span>

| Method                   | ImNet-A        | ImNet-B        | $ImNet-C$      | ImNet-D        | ImNet-E        |
|--------------------------|----------------|----------------|----------------|----------------|----------------|
| <b>GLaD-TESLA</b>        | $50.7 \pm 0.4$ | $51.9 + 1.3$   | $44.9 + 0.4$   | $39.9 + 1.7$   | $37.6 \pm 0.7$ |
| + Average Initialization | $51.9 \pm 1.0$ | $53.5 \pm 0.7$ | $46.1 \pm 0.9$ | 41.0 $\pm$ 0.7 | $39.1 \pm 1.0$ |
| <b>GLaD-DSA</b>          | $44.1 + 2.4$   | $49.2 + 1.1$   | $42.0 \pm 0.6$ | $35.6 + 0.9$   | $35.8 \pm 0.9$ |
| + Average Initialization | $45.4 \pm 0.6$ | $48.9 \pm 0.8$ | $40.6 \pm 0.7$ | $36.4 \pm 0.5$ | $34.8 \pm 0.3$ |
| GLaD-DM                  | $41.0 \pm 1.5$ | $42.9 + 1.9$   | $39.4 + 1.7$   | $33.2 + 1.4$   | $30.3 + 1.3$   |
| + Average Initialization | $41.5 + 1.2$   | $43.2 + 1.6$   | $39.9 + 1.7$   | $32.2 \pm 0.9$ | $30.8 \pm 1.3$ |

Table 16. Ablation study of average noise initialization on GLaD.

### B.6. Ablation Study on Average Noise Initialization

To investigate the effect of using averaged noise as initialization, we conduct ablation experiments on both GLaD and H-PD respectively. As shown in Table [16,](#page-15-3) averaged noise often provides a significant gain for GLaD. Indicating that <span id="page-16-0"></span>using averaged noise as input tends to produce images with reduced bias that conform to the statistical characteristics of the real dataset, implying that images generated from averaged noise are usually centered within the real dataset. As aforementioned, since GLaD neglects the informative guidance from the earlier layers, leading to a lack of optimization for the main subject of the synthetic image, averaged noise can to some extent replace this operation.

<span id="page-16-1"></span>

| Method                   | ImNet-A        | ImNet-B        | $ImNet-C$      | ImNet-D      | ImNet-E        |
|--------------------------|----------------|----------------|----------------|--------------|----------------|
| H-PD-TESLA               | $54.1 \pm 0.5$ | $56.8 + 0.4$   | $48.9 + 1.3$   | $45.0 + 0.7$ | $42.1 + 0.6$   |
| + Average Initialization | $55.1 \pm 0.6$ | $57.4 \pm 0.3$ | $49.5 + 0.6$   | $46.3 + 0.9$ | $43.0 \pm 0.6$ |
| H-PD-DSA                 | $46.5 + 1.0$   | $50.4 + 0.4$   | $44.5 \pm 0.6$ | $37.7 + 1.1$ | $36.9 + 0.7$   |
| + Average Initialization | $46.9 \pm 0.8$ | $50.7 \pm 0.9$ | $43.9 + 0.7$   | $37.4 + 0.4$ | $37.2 \pm 0.3$ |
| H-PD-DM                  | $42.6 \pm 1.6$ | $44.5 \pm 0.9$ | $42.3 + 1.4$   | $34.5 + 1.1$ | $32.3 + 1.3$   |
| + Average Initialization | $42.8 \pm 1.2$ | $44.7 \pm 1.3$ | $41.1 + 1.3$   | $34.8 + 1.5$ | $31.9 + 0.9$   |

Table 17. Ablation study of average noise initialization on H-PD.

As shown in Table [17,](#page-16-1) average noise initialization provides only a limited improvement for H-PD on TESLA, while using DSA and DM, averaged noise is closer to random initialization. The observation aligns with our perspective that H-PD requires optimization through all layers of the GAN, which has already led to optimization for the main subject information that conforms to the constraints of the loss function during the early stages of training. The role of averaged noise is then reduced to merely providing samples that better conform to statistical characteristics, which is also why we still employ averaged noise for H-PD to obtain a training-free optimization starting point.

Additionally, since DSA tends to optimize towards classification boundary samples or noisy samples, and DM tends to substantially modify synthetic datasets to achieve feature maximum mean discrepancy optimization, neither GLaD nor H-PD with average noise initialization can effectively improve the performance on DSA and DM. Nevertheless, TESLA is most effective in preserving the primary subject information in the synthetic images, which allows for the averaging of noise and the achievement of a relatively stable improvement.

## C. Experimental Details

## C.1. Dataset

We evaluate H-PD on various datasets, including a lowresolution dataset CIFAR10[\[22\]](#page-8-20) and a large number of highresolution datasets ImageNet-Subset.

- CIFAR-10 consists of  $32 \times 32$  RGB images with 50,000 images for training and 10,000 images for testing. It has 10 classes in total and each class contains 5,000 images for training and 1,000 images for testing.
- ImageNet-Subset is a small dataset that is divided out from the ImageNet[\[11\]](#page-8-19) based on certain characteristics. By aligning with the previous work, we use the same

types of subsets: ImageNette (various objects)[\[20\]](#page-8-26), ImageWoof (dogs)[\[20\]](#page-8-26), ImageFruit (fruits) [\[4\]](#page-8-10), ImageMeow (cats) [\[4\]](#page-8-10), ImageSquawk (birds) [\[4\]](#page-8-10), and ImageNet-[A, B, C, D, E] (based on ResNet50 performance) [\[5\]](#page-8-5). Each subset has 10 classes. The specific class name in each Imagenet-Subset is shown in Table [18.](#page-17-0)

### C.2. Network Architecture

For the comparison of same-architecture performance, we employ a convolutional neural network ConvNet-3 as the backbone network as well as the test network. For lowresolution datasets, we employ a 3-depth convolutional neural network ConvNet-3 as the backbone network, consisting of three basic blocks and one fully connected layer. Each block includes a  $3 \times 3$  convolutional layer, instance nor-malization [\[42\]](#page-9-27), ReLU non-linear activation, and a  $2 \times 2$ average pooling layer with a stride of 2. After the convolution blocks, a linear classifier outputs the logits. For high-resolution datasets, we employ a 5-depth convolutional neural network ConvNet-5 as the backbone network for  $128 \times 128$  resolution, ConvNet-5 has five duplicate blocks, which is as the same as that in ConvNet-3. For  $256 \times 256$  resolution, we employ ConvNet-6 as the backbone network.

For the comparison of cross-architecture performance, we also follow the previous work: ResNet-18 [\[19\]](#page-8-1), VGG-11 [\[39\]](#page-9-20), AlexNet [\[23\]](#page-8-23), and ViT  $[13]$  from the DC-BENCH [\[9\]](#page-8-24) resource.

### C.3. Implementation details

The implementation of our proposed H-PD is based on the open-source code for GLaD [\[5\]](#page-8-5), which is conducted on NVIDIA GeForce RTX 3090.

To ensure fairness, we utilize identical hyperparameters and optimization settings as GLaD. In our experiments, we also adopt the same suite of differentiable augmentations (originally from the DSA codebase [\[48\]](#page-9-9)), including color, crop, cutout, flip, scale, and rotate. We use an SGD optimizer with momentum,  $\ell_2$  decay. The entire distillation process continues for 1200 epochs. We evaluate the performance of the synthetic dataset by training 5 randomly initialized networks on it.

To obtain the expert trajectories used in MTT, we train a backbone model from scratch on the real dataset for 15 epochs of SGD with a learning rate of  $10^{-2}$ , a batch size of 256, and no momentum or regularization. To maintain the integration of different distillation methods, we do not use the ZCA whitening on both high-resolution datasets and low-resolution datasets different from previous work[\[4\]](#page-8-10), which leads to a same-architecture performance drop, please note that our proposed H-PD still outperforms under the same setting. Different from GLaD which records 1000 expert trajectories for the MTT method,

<span id="page-17-0"></span>

| Dataset      | 0                     | 1                   | 2                  | 3                    | 4                 | 5                        | 6                      | 7                     | 8                    | 9                     |
|--------------|-----------------------|---------------------|--------------------|----------------------|-------------------|--------------------------|------------------------|-----------------------|----------------------|-----------------------|
| ImNet-A      | Leonberg              | Probiscis<br>Monkey | Rapeseed           | Three-Toed<br>Sloth  | Cliff<br>Dwelling | Yellow<br>Lady's Slipper | Hamster                | Gondola               | Orca                 | Limpkin               |
| ImNet-B      | Spoonbill             | Website             | Lorikeet           | Hyena                | Earthstar         | Trollybus                | Echidna                | Pomeranian            | Odometer             | Ruddy<br>Turnstone    |
| ImNet-C      | Freight<br>Car        | Hummingbird         | Fireboat           | Disk<br>Brak         | Bee<br>Eater      | Rock<br>Beauty           | Lion                   | European<br>Gallinule | Cabbage<br>Butterfly | Goldfinch             |
| ImNet-D      | Ostrich               | Samoyed             | Snowbird           | Brabancon<br>Griffon | Chickadee         | Sorrel                   | Admiral                | Great<br>Gray Owl     | Hornbill             | Ringlet               |
| ImNet-E      | Spindle               | Toucan              | Black<br>Swan      | King<br>Penguin      | Potter's<br>Wheel | Photocopier              | Screw                  | Tarantula             | Oscilloscope         | Lycaenid              |
| ImNet-te     | Tench                 | English<br>Springer | Cassette<br>Player | Chainsaw             | Church            | French Horn              | Garbage<br>Truck       | Gas<br>Pump           | Golf<br>Ball         | Parachute             |
| ImWoof       | Australian<br>Terrier | Border<br>Terrier   | Samoyed            | Beagle               | Shih-Tzu          | English<br>Foxhound      | Rhodesian<br>Ridgeback | Dingo                 | Golden<br>Retriever  | English<br>Sheepdog   |
| ImNet-Birds  | Peacock               | Flamingo            | Macaw              | Pelican              | King<br>Penguin   | Bald<br>Eagle            | Toucan                 | Ostrich               | Black<br>Swan        | Cockatoo              |
| ImNet-Fruits | Pineapple             | Banana              | Strawberry         | Orange               | Lemon             | Pomegranate              | Fig                    | Bell Pepper           | Cucumber             | Granny<br>Smith Apple |
| ImNet-Cats   | Tabby<br>Cat          | Bengal<br>Cat       | Persian<br>Cat     | Siamese<br>Cat       | Egyptian<br>Cat   | Lion                     | Tiger                  | Jaguar                | Snow<br>Leopard      | Lynx                  |

Table 18. Corresponding class names in each ImageNet-Subsets. The visualizations follow the same order.

<span id="page-17-3"></span>

| Dataset         | IPC | Synthetic<br>steps | Expert<br>epochs | Max expert<br>epoch | Trajectory<br>number | Learning rate<br>(Learning rate) | Learning rate<br>(Teacher) | Learning rate<br>(Latent w) | Learning rate<br>(Latent f) | Steps<br>per space |
|-----------------|-----|--------------------|------------------|---------------------|----------------------|----------------------------------|----------------------------|-----------------------------|-----------------------------|--------------------|
| CIFAR-10        | 1   | 20                 | 3                | 50                  | 100                  | $10^{-6}$                        | $10^{-2}$                  | $10^{1}$                    | $10^{4}$                    | 100                |
|                 | 10  | 20                 | 3                | 50                  | 100                  | $10^{-6}$                        | $10^{-2}$                  | $10^{1}$                    | $10^{4}$                    | 100                |
| ImageNet-Subset | 1   | 20                 | 3                | 15                  | 200                  | $10^{-6}$                        | $10^{-2}$                  | $10^{1}$                    | $10^{4}$                    | 100                |

Table 19. TESLA hyper-parameters

<span id="page-17-1"></span>

| Dataset         | IPC | Learning rate<br>(Latent w) | Learning rate<br>(Latent f) | Steps<br>per space |
|-----------------|-----|-----------------------------|-----------------------------|--------------------|
| CIFAR-10        | 1   | $10^{-2}$                   | $10^{1}$                    | 20                 |
|                 | 10  | $10^{-2}$                   | $10^{1}$                    | 20                 |
| ImageNet-Subset | 1   | $10^{-2}$                   | $10^{1}$                    | 20                 |
|                 | 10  | $10^{-2}$                   | $10^{1}$                    | 20                 |

<span id="page-17-2"></span>

| Dataset         | IPC | inner loop | outer loop | Learning rate<br>(Latent w) | Learning rate<br>(Latent f) | Steps<br>per space |
|-----------------|-----|------------|------------|-----------------------------|-----------------------------|--------------------|
| CIFAR-10        | 1   | 1          | 1          | $10^{-3}$                   | $10^{0}$                    | 100                |
|                 | 10  | 50         | 10         | $10^{-3}$                   | $10^{0}$                    | 100                |
| ImageNet-Subset | 1   | 1          | 1          | $10^{-3}$                   | $10^{0}$                    | 100                |
|                 | 10  | 50         | 10         | $10^{-3}$                   | $10^{0}$                    | 100                |

Table 20. DM hyper-parameters

Table 21. DSA hyper-parameters

we only record 200 expert trajectories and thus largely reduce the computational costs. Additionally, while GLaD performs 5k optimization epochs on the synthetic dataset using MTT as the distillation method, we only perform 1k optimization epochs and achieve better performance both on same-architecture and cross-architecture settings, further proving the superiority of our H-PD. The detailed hyperparameters are shown in Table [20,](#page-17-1) Table [21](#page-17-2) and Table [19.](#page-17-3)

# D. More Visualizations

We provide additional visualizations of synthetic datasets generated by H-PD using diverse distillation methods, as shown in Figure [10,](#page-18-0) Figure [11,](#page-19-0) and Figure [12.](#page-20-0)

<span id="page-18-0"></span>Image /page/18/Picture/0 description: This is a grid of 60 images, arranged in 6 rows and 10 columns. The images display a variety of subjects, including animals such as cows, dogs, cats, birds, penguins, flamingos, and fish. There are also images of plants, flowers, fruits like strawberries and oranges, and some abstract or patterned images. Some images appear to be computer-generated or digitally manipulated, with bright colors and unusual textures. The overall impression is a diverse collection of visual content, possibly representing a dataset or a visual experiment.

Figure 10. More visualization of the synthetic datasets using TESLA.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image displays a grid of 70 small images, arranged in 7 rows and 10 columns. Each small image appears to be a synthetic or generated image, with many of them featuring abstract or distorted representations of objects, animals, or scenes. Some images show what look like stylized flowers or plants, while others contain more recognizable elements like a dashboard, a group of people, or animals such as penguins and cows. The overall impression is a diverse collection of AI-generated visuals, with varying levels of clarity and subject matter.

Figure 11. More visualization of the synthetic datasets using DSA.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image is a grid of 6x6 smaller images, each showing a different pattern or object. The overall background of the grid is black, with white circular or oval shapes scattered throughout. Some of the smaller images contain colorful elements, such as yellow flowers, blue water, red and yellow lights, and green lights. Other images show geometric patterns, white grids, or abstract textures. The bottom row of the grid features a sequence of colorful lights, transitioning from yellow to red to green. The image is labeled as "Figure 12. More visualization of the synthetic datasets using DM."

Figure 12. More visualization of the synthetic datasets using DM.