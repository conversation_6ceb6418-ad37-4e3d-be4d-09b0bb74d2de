{"table_of_contents": [{"title": "arXiv:2401.15863v1 [cs.CV] 29 Jan 2024\narXiv:2401.15863v1 [cs.CV] 29 Jan 2024", "heading_level": null, "page_id": 0, "polygon": [[14.261964735516372, 258.19140625], [35.394775390625, 258.19140625], [35.394775390625, 610.12109375], [14.261964735516372, 610.12109375]]}, {"title": "Importance-Aware Adaptive Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[150.455078125, 89.524169921875], [437.396484375, 89.524169921875], [437.396484375, 103.39990234375], [150.455078125, 103.39990234375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[36.7808564231738, 239.17898486197683], [75.44580078125, 239.17898486197683], [75.44580078125, 250.58544921875], [36.7808564231738, 250.58544921875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[36.03022670025189, 466.36153161175423], [106.00244140625, 466.36153161175423], [106.00244140625, 476.091796875], [36.03022670025189, 476.091796875]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 2, "polygon": [[36.03022670025189, 309.6580587711487], [114.09571788413098, 309.6580587711487], [114.09571788413098, 321.0947265625], [36.03022670025189, 321.0947265625]]}, {"title": "2.1. Dataset Distillation Based on Performance Matching", "heading_level": null, "page_id": 2, "polygon": [[36.03022670025189, 337.39982190560994], [270.22670025188916, 337.39982190560994], [270.22670025188916, 347.4072265625], [36.03022670025189, 347.4072265625]]}, {"title": "2.2. Dataset Distillation Based on Parameter Matching", "heading_level": null, "page_id": 2, "polygon": [[305.5062972292191, 131.21104185218164], [530.6952141057934, 131.21104185218164], [530.6952141057934, 140.95814781834372], [305.5062972292191, 140.95814781834372]]}, {"title": "2.3. Dataset Distillation Based on Distribution Matching", "heading_level": null, "page_id": 2, "polygon": [[305.5062972292191, 410.12822796081923], [536.7002518891687, 410.12822796081923], [536.7002518891687, 420.177734375], [305.5062972292191, 420.177734375]]}, {"title": "2.4. Dataset Distillation for Medical Task", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 629.8130008904719], [476.6498740554156, 629.8130008904719], [476.6498740554156, 639.72265625], [304.7556675062972, 639.72265625]]}, {"title": "3. Methodology", "heading_level": null, "page_id": 3, "polygon": [[36.03022670025189, 502.3508459483526], [106.58942065491183, 502.3508459483526], [106.58942065491183, 513.09375], [36.03022670025189, 513.09375]]}, {"title": "3.1. Teacher and Student Network Training", "heading_level": null, "page_id": 3, "polygon": [[36.03022670025189, 618.5663401602849], [212.42821158690174, 618.5663401602849], [212.42821158690174, 628.**********], [36.03022670025189, 628.**********]]}, {"title": "3.2. Teacher and Student Parameters Matching", "heading_level": null, "page_id": 3, "polygon": [[304.7556675062972, 725.7845057880677], [496.91687657430725, 725.7845057880677], [496.91687657430725, 735.927734375], [304.7556675062972, 735.927734375]]}, {"title": "3.3. Optimized Distilled Dataset Generation", "heading_level": null, "page_id": 4, "polygon": [[305.5062972292191, 317.90560997328583], [486.4080604534005, 317.90560997328583], [486.4080604534005, 327.6728515625], [305.5062972292191, 327.6728515625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 5, "polygon": [[36.03022670025189, 222.68388245770257], [105.088161209068, 222.68388245770257], [105.088161209068, 233.1123046875], [36.03022670025189, 233.1123046875]]}, {"title": "4.1. <PERSON> Settings", "heading_level": null, "page_id": 5, "polygon": [[36.03022670025189, 333.63427734375], [146.37279596977328, 333.63427734375], [146.37279596977328, 343.09033203125], [36.03022670025189, 343.09033203125]]}, {"title": "4.2. <PERSON><PERSON><PERSON> Comparison", "heading_level": null, "page_id": 5, "polygon": [[304.7556675062972, 650.412109375], [424.8564231738035, 650.412109375], [424.8564231738035, 660.279296875], [304.7556675062972, 660.279296875]]}, {"title": "4.3. Analysis of Self-Adaptive Weights", "heading_level": null, "page_id": 7, "polygon": [[304.7556675062972, 399.6313446126447], [461.6372795969773, 399.6313446126447], [461.6372795969773, 409.**********], [304.7556675062972, 409.**********]]}, {"title": "4.4. Cross-Architecture Generalization", "heading_level": null, "page_id": 10, "polygon": [[36.03022670025189, 431.1219946571683], [195.16372795969772, 431.1219946571683], [195.16372795969772, 441.**********], [36.03022670025189, 441.**********]]}, {"title": "4.5. Real-World Medical Application", "heading_level": null, "page_id": 10, "polygon": [[36.03022670025189, 700.2920747996437], [188.40806045340048, 700.2920747996437], [188.40806045340048, 710.4375], [36.03022670025189, 710.4375]]}, {"title": "Real CXR images", "heading_level": null, "page_id": 11, "polygon": [[265.7229219143577, 83.22528940338378], [326.51953125, 83.22528940338378], [326.51953125, 92.6590576171875], [265.7229219143577, 92.6590576171875]]}, {"title": "5. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 11, "polygon": [[36.03022670025189, 541.3392698130009], [95.32997481108312, 541.3392698130009], [95.32997481108312, 551.740234375], [36.03022670025189, 551.740234375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 12, "polygon": [[36.03022670025189, 217.43544078361532], [99.08312342569269, 217.43544078361532], [99.08312342569269, 227.**********], [36.03022670025189, 227.**********]]}, {"title": "Ethical Approval", "heading_level": null, "page_id": 12, "polygon": [[36.03022670025189, 584.826357969724], [111.09319899244332, 584.826357969724], [111.09319899244332, 594.9091796875], [36.03022670025189, 594.9091796875]]}, {"title": "Declaration of Competing Interest", "heading_level": null, "page_id": 12, "polygon": [[36.03022670025189, 629.8130008904719], [184.65491183879092, 629.8130008904719], [184.65491183879092, 640.1337890625], [36.03022670025189, 640.1337890625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 12, "polygon": [[36.7808564231738, 674.7996438112199], [116.8427734375, 674.7996438112199], [116.8427734375, 684.947265625], [36.7808564231738, 684.947265625]]}, {"title": "References", "heading_level": null, "page_id": 12, "polygon": [[305.5062972292191, 83.97506678539625], [353.583984375, 83.97506678539625], [353.583984375, 93.7896728515625], [305.5062972292191, 93.7896728515625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 72], ["Text", 11], ["SectionHeader", 4], ["<PERSON>Footer", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5789, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 102], ["Text", 5], ["ListItem", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1355, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 107], ["Text", 9], ["SectionHeader", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 89], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 767, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1336], ["Line", 170], ["ListItem", 15], ["Equation", 12], ["Text", 8], ["TextInlineMath", 3], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 107], ["Text", 8], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["TableCell", 210], ["Line", 84], ["Caption", 4], ["Text", 4], ["Reference", 3], ["Table", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 6671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 66], ["Text", 6], ["Caption", 3], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Line", 100], ["Span", 63], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Line", 96], ["Span", 53], ["Text", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 947, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["TableCell", 184], ["Line", 107], ["Text", 8], ["SectionHeader", 2], ["Caption", 2], ["Table", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 72], ["Text", 5], ["SectionHeader", 2], ["Caption", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 121], ["ListItem", 21], ["Reference", 21], ["Text", 7], ["SectionHeader", 5], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 143], ["ListItem", 45], ["Reference", 45], ["Text", 2], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 97], ["ListItem", 29], ["Reference", 29], ["Text", 2], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Importance-Aware_Adaptive_Dataset_Distillation"}