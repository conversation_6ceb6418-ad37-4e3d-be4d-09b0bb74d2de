{"table_of_contents": [{"title": "Improved Distribution Matching for Dataset Condensation", "heading_level": null, "page_id": 0, "polygon": [[114.8994140625, 106.5], [478.5, 106.5], [478.5, 119.109375], [114.8994140625, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 228.75], [191.25, 228.75], [191.25, 240.15234375], [144.75, 240.15234375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[49.5, 554.25], [127.5, 554.25], [127.5, 564.99609375], [49.5, 564.99609375]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[307.5, 257.25], [396.75, 257.25], [396.75, 267.802734375], [307.5, 267.802734375]]}, {"title": "3. Problem Definition", "heading_level": null, "page_id": 2, "polygon": [[48.0, 156.0], [159.75, 156.0], [159.75, 167.3525390625], [48.0, 167.3525390625]]}, {"title": "4. Methodology", "heading_level": null, "page_id": 2, "polygon": [[48.75, 558.75], [129.0, 558.75], [129.0, 569.25], [48.75, 569.25]]}, {"title": "4.1. Partitioning and Expansion Augmentation", "heading_level": null, "page_id": 2, "polygon": [[307.5, 337.5], [526.5, 337.5], [526.5, 347.853515625], [307.5, 347.853515625]]}, {"title": "4.2. Efficient and Enriched Model Sampling", "heading_level": null, "page_id": 2, "polygon": [[307.5, 672.75], [514.5, 672.75], [514.5, 682.9453125], [307.5, 682.9453125]]}, {"title": "4.3. Class-aware Distribution Regularization", "heading_level": null, "page_id": 3, "polygon": [[307.5, 624.75], [517.5, 624.75], [517.5, 634.9921875], [307.5, 634.9921875]]}, {"title": "4.4. Overall Loss Function and Pseudocode", "heading_level": null, "page_id": 4, "polygon": [[48.0, 464.25], [252.75, 464.25], [252.75, 474.1171875], [48.0, 474.1171875]]}, {"title": "5. <PERSON>", "heading_level": null, "page_id": 4, "polygon": [[48.75, 622.5], [123.75, 622.5], [123.75, 634.21875], [48.75, 634.21875]]}, {"title": "5.1. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[48.0, 642.0], [163.5, 642.0], [163.5, 652.78125], [48.0, 652.78125]]}, {"title": "5.2. <PERSON><PERSON><PERSON><PERSON> with Previous Methods", "heading_level": null, "page_id": 5, "polygon": [[48.0, 323.25], [238.5, 323.25], [238.5, 333.931640625], [48.0, 333.931640625]]}, {"title": "5.3. Justification of Algorithmic Motivation", "heading_level": null, "page_id": 5, "polygon": [[307.5, 449.25], [511.5, 449.25], [511.5, 459.80859375], [307.5, 459.80859375]]}, {"title": "5.4. Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[48.0, 470.25], [141.0, 470.25], [141.0, 480.69140625], [48.0, 480.69140625]]}, {"title": "5.5. Ablation Study on CIFAR-10 Architectural\nGeneralization", "heading_level": null, "page_id": 6, "polygon": [[307.5, 624.0], [546.0, 624.0], [546.0, 646.59375], [307.5, 646.59375]]}, {"title": "5.6. Continual Learning", "heading_level": null, "page_id": 7, "polygon": [[48.0, 576.0], [163.5, 576.0], [163.5, 586.65234375], [48.0, 586.65234375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[307.5, 431.25], [378.75, 431.25], [378.75, 442.01953125], [307.5, 442.01953125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.8212890625], [48.75, 83.8212890625]]}, {"title": "1. Implementation Details", "heading_level": null, "page_id": 10, "polygon": [[48.75, 72.75], [183.0, 72.75], [183.0, 83.6279296875], [48.75, 83.6279296875]]}, {"title": "2. Cross-architectural Experiments", "heading_level": null, "page_id": 10, "polygon": [[48.0, 494.25], [230.25, 494.25], [230.25, 505.0546875], [48.0, 505.0546875]]}, {"title": "3. Start with Pre-trained Models", "heading_level": null, "page_id": 10, "polygon": [[48.0, 587.25], [216.0, 587.25], [216.0, 597.8671875], [48.0, 597.8671875]]}, {"title": "4. Visualization of Condensed Synthetic Sets", "heading_level": null, "page_id": 10, "polygon": [[307.5, 599.25], [534.75, 599.25], [534.75, 610.5], [307.5, 610.5]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 97], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 2], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 103], ["Text", 9], ["ListItem", 4], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 784], ["Line", 123], ["TextInlineMath", 7], ["Text", 5], ["Reference", 5], ["SectionHeader", 4], ["Equation", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1169, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 681], ["Line", 112], ["TextInlineMath", 7], ["ListItem", 4], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 104], ["TableCell", 21], ["Reference", 6], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 3], ["Equation", 2], ["Footnote", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 108], ["Text", 7], ["Reference", 4], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 522], ["TableCell", 392], ["Line", 81], ["Text", 4], ["Reference", 4], ["Caption", 3], ["Table", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 7005, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 143], ["Caption", 6], ["Text", 5], ["Figure", 3], ["FigureGroup", 3], ["Reference", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 115], ["ListItem", 32], ["Reference", 30], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 104], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 488], ["Line", 102], ["TableCell", 54], ["Text", 6], ["SectionHeader", 4], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 21], ["Line", 5], ["Picture", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 577, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 4], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1220, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1249, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Improved_Distribution_Matching_for_Dataset_Condensation"}