{"table_of_contents": [{"title": "Infinite Recommendation Networks: A Data-Centric\nApproach", "heading_level": null, "page_id": 0, "polygon": [[110.7158203125, 99.0], [500.25, 99.0], [500.25, 136.4150390625], [110.7158203125, 136.4150390625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 269.25], [328.5, 269.25], [328.5, 280.564453125], [282.75, 280.564453125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 518.25], [191.25, 518.25], [191.25, 529.41796875], [107.25, 529.41796875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[106.5, 610.5], [198.0, 610.5], [198.0, 622.23046875], [106.5, 622.23046875]]}, {"title": "3 \\quad \\infty-AE: Infinite-width Autoencoders for Recommendation", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 442.40625], [422.841796875, 442.40625], [422.841796875, 453.234375], [106.98046875, 453.234375]]}, {"title": "4 DISTILL-CF", "heading_level": null, "page_id": 3, "polygon": [[107.25, 390.0], [192.75, 390.0], [192.75, 401.80078125], [107.25, 401.80078125]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 400.5], [192.146484375, 400.5], [192.146484375, 413.015625], [106.98046875, 413.015625]]}, {"title": "6 Conclusion & Future Work", "heading_level": null, "page_id": 8, "polygon": [[107.05517578125, 423.75], [267.0, 423.75], [267.0, 435.4453125], [107.05517578125, 435.4453125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 72.75], [164.5048828125, 72.75], [164.5048828125, 83.724609375], [107.25, 83.724609375]]}, {"title": "Checklist", "heading_level": null, "page_id": 12, "polygon": [[106.681640625, 520.5], [157.5, 520.5], [157.5, 530.578125], [106.681640625, 530.578125]]}, {"title": "A Appendix: Pseudo-codes", "heading_level": null, "page_id": 14, "polygon": [[106.5, 72.75], [255.0, 72.75], [255.0, 84.15966796875], [106.5, 84.15966796875]]}, {"title": "B Appendix: Experiments", "heading_level": null, "page_id": 14, "polygon": [[106.5, 523.5], [251.25, 523.5], [251.25, 534.05859375], [106.5, 534.05859375]]}, {"title": "B.1 Baselines & Competitor Methods", "heading_level": null, "page_id": 14, "polygon": [[107.25, 546.75], [273.75, 546.75], [273.75, 556.1015625], [107.25, 556.1015625]]}, {"title": "B.2 Sampling strategies", "heading_level": null, "page_id": 15, "polygon": [[106.5, 479.25], [216.75, 479.25], [216.75, 489.5859375], [106.5, 489.5859375]]}, {"title": "B.3 Data statistics & hyper-parameter configurations", "heading_level": null, "page_id": 16, "polygon": [[106.5, 428.25], [342.75, 428.25], [342.75, 438.92578125], [106.5, 438.92578125]]}, {"title": "B.4 Additional training details", "heading_level": null, "page_id": 16, "polygon": [[106.5, 485.71875], [245.337890625, 485.71875], [245.337890625, 495.7734375], [106.5, 495.7734375]]}, {"title": "B.5 Evaluation metrics", "heading_level": null, "page_id": 17, "polygon": [[107.25, 208.5], [213.36328125, 208.5], [213.36328125, 218.49609375], [107.25, 218.49609375]]}, {"title": "B.6 Additional experiments", "heading_level": null, "page_id": 18, "polygon": [[106.5, 73.5], [232.6376953125, 73.5], [232.6376953125, 83.2412109375], [106.5, 83.2412109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 44], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5453, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 54], ["Text", 6], ["ListItem", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 55], ["TextInlineMath", 3], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 76], ["TextInlineMath", 3], ["Reference", 3], ["Equation", 2], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1338, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 675], ["Line", 66], ["TextInlineMath", 4], ["Text", 3], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5454, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["TableCell", 110], ["Line", 66], ["Reference", 4], ["Text", 3], ["Footnote", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9832, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 490], ["Line", 114], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 71], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1923, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 308], ["Line", 68], ["Text", 4], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 831, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 50], ["ListItem", 20], ["Reference", 20], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 50], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 52], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 47], ["ListItem", 21], ["Reference", 13], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 29], ["ListItem", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 980], ["Line", 76], ["TableCell", 28], ["Reference", 5], ["SectionHeader", 3], ["ListItem", 3], ["Text", 2], ["TextInlineMath", 2], ["Table", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2342, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 483], ["Line", 46], ["TableCell", 45], ["ListItem", 8], ["Reference", 3], ["TextInlineMath", 2], ["ListGroup", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2860, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 88], ["Line", 56], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7345, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["Line", 122], ["Text", 8], ["Equation", 6], ["ListItem", 4], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 498], ["Line", 100], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Text", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2267, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 483], ["Line", 69], ["Text", 5], ["Figure", 2], ["Caption", 2], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1617, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 83], ["Text", 8], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1774, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 165], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1700, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 457], ["Line", 77], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1360, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 60], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1350, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Infinite_Recommendation_Networks__A_Data-Centric_Approach"}