{"table_of_contents": [{"title": "Information Compensation:\nA Fix for Any-scale Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[166.5, 101.9970703125], [445.25390625, 101.9970703125], [445.25390625, 133.3212890625], [166.5, 133.3212890625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[279.0, 294.75], [331.5, 294.75], [331.5, 305.89453125], [279.0, 305.89453125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[89.25, 457.5], [177.0, 457.5], [177.0, 468.703125], [89.25, 468.703125]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 1, "polygon": [[89.25, 429.64453125], [180.0, 429.64453125], [180.0, 441.24609375], [89.25, 441.24609375]]}, {"title": "2.1 Selecting Key Samples", "heading_level": null, "page_id": 1, "polygon": [[89.2001953125, 573.50390625], [235.5, 573.50390625], [235.5, 584.33203125], [89.2001953125, 584.33203125]]}, {"title": "2.2 Information Compensation for Compressed Data", "heading_level": null, "page_id": 2, "polygon": [[89.25, 292.5], [378.75, 292.5], [378.75, 302.607421875], [89.25, 302.607421875]]}, {"title": "2.3 Relabel with Observer Model", "heading_level": null, "page_id": 4, "polygon": [[89.25, 223.5], [282.0, 223.5], [282.0, 233.96484375], [89.25, 233.96484375]]}, {"title": "3 Experiment", "heading_level": null, "page_id": 4, "polygon": [[88.5, 449.3671875], [171.75, 449.3671875], [171.75, 460.1953125], [88.5, 460.1953125]]}, {"title": "3.1 Experimental Setting", "heading_level": null, "page_id": 4, "polygon": [[88.5, 549.75], [228.75, 549.75], [228.75, 561.12890625], [88.5, 561.12890625]]}, {"title": "3.2 Comparison with the SOTA Methods", "heading_level": null, "page_id": 5, "polygon": [[89.2001953125, 537.75], [316.5, 537.75], [316.5, 548.3671875], [89.2001953125, 548.3671875]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[89.25, 638.25], [195.75, 638.25], [195.75, 649.30078125], [89.25, 649.30078125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[89.25, 597.0], [167.34375, 597.0], [167.34375, 608.30859375], [89.25, 608.30859375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[88.5, 92.25], [153.0, 92.25], [153.0, 104.5107421875], [88.5, 104.5107421875]]}, {"title": "Appendix <PERSON>", "heading_level": null, "page_id": 11, "polygon": [[89.25, 93.0], [253.5, 93.0], [253.5, 104.5107421875], [89.25, 104.5107421875]]}, {"title": "Appendix B. Motivation and Intuitive Exploration", "heading_level": null, "page_id": 12, "polygon": [[89.25, 93.0], [390.0, 93.0], [390.0, 104.02734375], [89.25, 104.02734375]]}, {"title": "B.1 Preliminary", "heading_level": null, "page_id": 12, "polygon": [[89.2001953125, 219.75], [180.0, 219.75], [180.0, 231.2578125], [89.2001953125, 231.2578125]]}, {"title": "B.2 Exploring Image Distillation with RELABEL", "heading_level": null, "page_id": 13, "polygon": [[89.25, 416.8828125], [352.01953125, 416.8828125], [352.01953125, 427.7109375], [89.25, 427.7109375]]}, {"title": "B.3 Examining Squeezing Strategy with RELABEL", "heading_level": null, "page_id": 14, "polygon": [[89.12548828125, 551.84765625], [363.076171875, 551.84765625], [363.076171875, 561.90234375], [89.12548828125, 561.90234375]]}, {"title": "Appendix C. Framework", "heading_level": null, "page_id": 15, "polygon": [[88.5, 216.75], [237.0, 216.75], [237.0, 228.55078125], [88.5, 228.55078125]]}, {"title": "Appendix D. Experiment Details", "heading_level": null, "page_id": 15, "polygon": [[89.25, 539.25], [286.5, 539.25], [286.5, 550.6875], [89.25, 550.6875]]}, {"title": "Appendix E. Experiment Results", "heading_level": null, "page_id": 17, "polygon": [[89.25, 499.5], [287.25, 499.5], [287.25, 511.2421875], [89.25, 511.2421875]]}, {"title": "E.1 Efficiency Comparison", "heading_level": null, "page_id": 17, "polygon": [[89.25, 636.75], [237.0, 636.75], [237.0, 648.52734375], [89.25, 648.52734375]]}, {"title": "Appendix F. Ablation", "heading_level": null, "page_id": 18, "polygon": [[89.25, 522.0], [221.25, 522.0], [221.25, 534.4453125], [89.25, 534.4453125]]}, {"title": "Appendix G. Continual Learning", "heading_level": null, "page_id": 19, "polygon": [[89.25, 394.5], [286.5, 394.5], [286.5, 406.44140625], [89.25, 406.44140625]]}, {"title": "Appendix H. Visualization", "heading_level": null, "page_id": 19, "polygon": [[88.5, 494.25], [249.0, 494.25], [249.0, 506.21484375], [88.5, 506.21484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 36], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4477, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 40], ["Text", 3], ["ListItem", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 466], ["Line", 51], ["TextInlineMath", 5], ["Reference", 4], ["Footnote", 3], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 546], ["Line", 51], ["TextInlineMath", 5], ["Equation", 5], ["Reference", 5], ["Text", 4], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 48], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["ListItem", 2], ["Reference", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 714], ["TableCell", 282], ["Line", 44], ["Text", 5], ["Reference", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4422, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 75], ["TableCell", 45], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8233, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["TableCell", 48], ["Line", 39], ["Text", 3], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3929, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 38], ["ListItem", 12], ["Reference", 12], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 39], ["ListItem", 14], ["Reference", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 97], ["Line", 36], ["ListItem", 13], ["Reference", 13], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 38], ["Text", 3], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 44], ["ListItem", 4], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 42], ["Text", 5], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 315], ["Line", 55], ["Text", 5], ["Reference", 4], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1122, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 39], ["Text", 6], ["Reference", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 764, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["Line", 43], ["ListItem", 7], ["Text", 3], ["Code", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["TableCell", 48], ["Line", 42], ["Text", 7], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2533, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 705], ["TableCell", 298], ["Line", 43], ["Reference", 3], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1426, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 63], ["TableCell", 50], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7408, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 45], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2393, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 604, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 685, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 4], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 651, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 4], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 4], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 11], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 620, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 651, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 606, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Line", 5], ["Span", 5], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Line", 7], ["Span", 5], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 579, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Information Compensation- A Fix for Any-scale Dataset Distillation"}