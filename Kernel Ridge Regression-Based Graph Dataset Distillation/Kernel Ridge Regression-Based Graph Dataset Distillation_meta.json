{"table_of_contents": [{"title": "Kernel Ridge Regression-Based Graph Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[77.8447265625, 82.2744140625], [533.25, 82.2744140625], [533.25, 100.4501953125], [77.8447265625, 100.4501953125]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[52.5, 247.5], [112.5, 247.5], [112.5, 259.681640625], [52.5, 259.681640625]]}, {"title": "CCS CONCEPTS", "heading_level": null, "page_id": 0, "polygon": [[316.5, 247.5], [399.75, 247.5], [399.75, 258.908203125], [316.5, 258.908203125]]}, {"title": "KEYWORDS", "heading_level": null, "page_id": 0, "polygon": [[316.5, 306.66796875], [380.25, 306.66796875], [380.25, 317.49609375], [316.5, 317.49609375]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[316.5, 338.185546875], [405.0, 338.185546875], [405.0, 347.080078125], [316.5, 347.080078125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[317.25, 422.25], [420.75, 422.25], [420.75, 433.125], [317.25, 433.125]]}, {"title": "2 PROBLEM DEFINITION", "heading_level": null, "page_id": 1, "polygon": [[316.16015625, 141.75], [454.21875, 141.75], [454.21875, 151.98046875], [316.16015625, 151.98046875]]}, {"title": "2.1 Notations", "heading_level": null, "page_id": 1, "polygon": [[316.16015625, 212.25], [391.5, 212.25], [391.5, 221.58984375], [316.16015625, 221.58984375]]}, {"title": "2.2 Graph Neural Network", "heading_level": null, "page_id": 1, "polygon": [[316.5, 359.25], [456.75, 359.25], [456.75, 369.31640625], [316.5, 369.31640625]]}, {"title": "2.3 Graph Neural Tang<PERSON>", "heading_level": null, "page_id": 1, "polygon": [[316.16015625, 563.25], [490.5, 563.25], [490.5, 573.1171875], [316.16015625, 573.1171875]]}, {"title": "2.4 Graph Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[52.5, 623.25], [210.0, 623.25], [210.0, 633.83203125], [52.5, 633.83203125]]}, {"title": "3 PROPOSED METHOD", "heading_level": null, "page_id": 2, "polygon": [[316.5, 222.75], [442.5, 222.75], [442.5, 233.19140625], [316.5, 233.19140625]]}, {"title": "3.1 Optimization Objective", "heading_level": null, "page_id": 2, "polygon": [[317.25, 268.5], [456.75, 268.5], [456.75, 278.82421875], [317.25, 278.82421875]]}, {"title": "3.2 Graph Kernel Ridge Regression", "heading_level": null, "page_id": 3, "polygon": [[52.5, 140.25], [234.75, 140.25], [234.75, 150.5302734375], [52.5, 150.5302734375]]}, {"title": "3.3 Model Enhancements", "heading_level": null, "page_id": 3, "polygon": [[316.458984375, 209.25], [449.736328125, 209.25], [449.736328125, 219.65625], [316.458984375, 219.65625]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[52.5, 647.25], [148.59228515625, 647.25], [148.59228515625, 657.421875], [52.5, 657.421875]]}, {"title": "4.1 Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[316.5, 86.23828125], [441.0, 86.23828125], [441.0, 96.1962890625], [316.5, 96.1962890625]]}, {"title": "4.2 Efficacy Study", "heading_level": null, "page_id": 5, "polygon": [[316.5, 534.0], [412.5, 534.0], [412.5, 544.88671875], [316.5, 544.88671875]]}, {"title": "4.3 Auxiliary Experiments", "heading_level": null, "page_id": 7, "polygon": [[316.16015625, 483.0], [456.0, 483.0], [456.0, 493.453125], [316.16015625, 493.453125]]}, {"title": "5 RELATED WORK", "heading_level": null, "page_id": 8, "polygon": [[52.5, 405.75], [157.78125, 405.75], [157.78125, 416.8828125], [52.5, 416.8828125]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[316.16015625, 503.25], [405.75, 503.25], [405.75, 513.5625], [316.16015625, 513.5625]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 8, "polygon": [[316.5, 648.0], [436.5, 648.0], [436.5, 658.96875], [316.5, 658.96875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[52.5, 85.078125], [123.78955078125, 85.078125], [123.78955078125, 95.8095703125], [52.5, 95.8095703125]]}, {"title": "A EXACT COMPUTATION OF THE UPDATE\nFUNCTION", "heading_level": null, "page_id": 10, "polygon": [[52.5, 370.5], [285.380859375, 370.5], [285.380859375, 393.29296875], [52.5, 393.29296875]]}, {"title": "B PROOF OF LEMMA 1", "heading_level": null, "page_id": 10, "polygon": [[52.5, 604.5], [177.205078125, 606.375], [177.205078125, 616.4296875], [51.75, 616.4296875]]}, {"title": "C PROOF OF LEMMA 2", "heading_level": null, "page_id": 10, "polygon": [[316.5, 372.75], [441.0, 372.75], [441.0, 383.818359375], [316.5, 383.818359375]]}, {"title": "Algorithm 1: KiDD-LR", "heading_level": null, "page_id": 11, "polygon": [[56.067626953125, 87.0], [148.21875, 87.0], [148.21875, 96.24462890625], [56.067626953125, 96.24462890625]]}, {"title": "Algorithm 2: KiDD-D", "heading_level": null, "page_id": 11, "polygon": [[56.3291015625, 282.69140625], [143.5869140625, 282.69140625], [143.5869140625, 292.74609375], [56.3291015625, 292.74609375]]}, {"title": "X, and \\Theta to special cases but we keep this form for generality. \\Box", "heading_level": null, "page_id": 11, "polygon": [[52.5, 538.5], [294.0, 538.5], [294.0, 554.16796875], [52.5, 554.16796875]]}, {"title": "D DATASET STATISTICS", "heading_level": null, "page_id": 11, "polygon": [[52.5, 558.75], [185.25, 558.75], [185.25, 570.41015625], [52.5, 570.41015625]]}, {"title": "E REPRODUCIBILITY", "heading_level": null, "page_id": 11, "polygon": [[317.25, 208.5], [437.484375, 208.5], [437.484375, 219.849609375], [317.25, 219.849609375]]}, {"title": "F DETAILED ALGORITHMS", "heading_level": null, "page_id": 11, "polygon": [[316.5, 388.845703125], [465.75, 388.845703125], [465.75, 399.8671875], [316.5, 399.8671875]]}, {"title": "G LIMITATION AND FUTURE WORK", "heading_level": null, "page_id": 11, "polygon": [[317.25, 435.0], [515.1796875, 435.0], [515.1796875, 445.88671875], [317.25, 445.88671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 300], ["Line", 111], ["Text", 16], ["SectionHeader", 6], ["Picture", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7469, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 609], ["Line", 119], ["Text", 11], ["SectionHeader", 4], ["TextInlineMath", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1250], ["Line", 182], ["TableCell", 48], ["Equation", 14], ["Reference", 12], ["Text", 11], ["TextInlineMath", 6], ["SectionHeader", 3], ["Caption", 1], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 982], ["Line", 133], ["Text", 8], ["Reference", 8], ["Equation", 7], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1267], ["Line", 163], ["Text", 10], ["TextInlineMath", 8], ["Equation", 7], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 708], ["Line", 107], ["Text", 8], ["Reference", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 861], ["TableCell", 205], ["Line", 96], ["ListItem", 4], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["ListGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 14587, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["TableCell", 154], ["Line", 134], ["Text", 6], ["ListItem", 4], ["Reference", 4], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 8371, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 109], ["Text", 9], ["SectionHeader", 3], ["Caption", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 726, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["Line", 158], ["ListItem", 50], ["Reference", 50], ["ListGroup", 2], ["Text", 1], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1643], ["Line", 198], ["TextInlineMath", 13], ["ListItem", 11], ["Reference", 11], ["Equation", 8], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1165, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 988], ["TableCell", 96], ["Line", 78], ["ListItem", 12], ["SectionHeader", 7], ["TextInlineMath", 7], ["Text", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Caption", 1], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1414, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Kernel Ridge Regression-Based Graph Dataset Distillation"}