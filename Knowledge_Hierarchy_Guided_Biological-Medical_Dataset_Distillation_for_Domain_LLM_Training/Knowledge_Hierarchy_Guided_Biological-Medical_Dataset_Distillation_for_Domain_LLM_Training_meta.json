{"table_of_contents": [{"title": "Knowledge Hierarchy Guided Biological-Medical\nDataset Distillation for Domain LLM Training", "heading_level": null, "page_id": 0, "polygon": [[138.0, 114.75], [477.52734375, 114.75], [477.52734375, 146.1796875], [138.0, 146.1796875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 551.25], [228.75, 551.25], [228.75, 562.2890625], [133.5, 562.2890625]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 2, "polygon": [[133.5, 391.5], [232.787109375, 391.5], [232.787109375, 404.12109375], [133.5, 404.12109375]]}, {"title": "3 Experiment", "heading_level": null, "page_id": 6, "polygon": [[132.978515625, 117.0], [224.25, 117.0], [224.25, 127.6171875], [132.978515625, 127.6171875]]}, {"title": "3.1 Experimental Setups", "heading_level": null, "page_id": 6, "polygon": [[133.20263671875, 137.25], [266.255859375, 137.25], [266.255859375, 147.**********], [133.20263671875, 147.**********]]}, {"title": "3.2 Main Results and Analysis", "heading_level": null, "page_id": 6, "polygon": [[133.5, 371.25], [296.25, 371.25], [296.25, 381.3046875], [133.5, 381.3046875]]}, {"title": "8 <PERSON><PERSON><PERSON> et al.", "heading_level": null, "page_id": 7, "polygon": [[133.27734375, 91.5], [238.46484375, 92.25], [238.46484375, 101.900390625], [133.27734375, 101.900390625]]}, {"title": "3.3 Robustness Checking", "heading_level": null, "page_id": 7, "polygon": [[133.4267578125, 396.0], [267.75, 396.0], [267.75, 406.44140625], [133.4267578125, 406.44140625]]}, {"title": "3.4 Scaling Law of Dataset Distillation", "heading_level": null, "page_id": 8, "polygon": [[133.5, 623.25], [336.0, 623.25], [336.0, 633.83203125], [133.5, 633.83203125]]}, {"title": "3.5 Ablation Study and Case Study", "heading_level": null, "page_id": 9, "polygon": [[133.20263671875, 498.0], [320.25, 498.0], [320.25, 508.53515625], [133.20263671875, 508.53515625]]}, {"title": "3.6 Hyperparameter Experiment", "heading_level": null, "page_id": 11, "polygon": [[133.5, 117.75], [306.0, 117.75], [306.0, 128.1005859375], [133.5, 128.1005859375]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 11, "polygon": [[133.5, 392.25], [237.4189453125, 392.25], [237.4189453125, 402.9609375], [133.5, 402.9609375]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 12, "polygon": [[133.5, 279.75], [219.75, 279.75], [219.75, 290.619140625], [133.5, 290.619140625]]}, {"title": "6 Ackownledgement", "heading_level": null, "page_id": 12, "polygon": [[133.5, 430.5], [261.0, 430.5], [261.0, 441.6328125], [133.5, 441.6328125]]}, {"title": "References", "heading_level": null, "page_id": 12, "polygon": [[133.5, 522.0], [198.0, 522.0], [198.0, 532.8984375], [133.5, 532.8984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3592, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 71], ["Text", 4], ["ListItem", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 826, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 66], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 879, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 613], ["Line", 43], ["TextInlineMath", 4], ["ListItem", 4], ["Text", 2], ["Equation", 1], ["Footnote", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 585], ["Line", 54], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 525], ["Line", 94], ["TableCell", 36], ["Text", 8], ["Reference", 3], ["ListItem", 2], ["Table", 1], ["Caption", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 73], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1377, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 58], ["Text", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1230, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 84], ["Caption", 3], ["Figure", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2080, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 44], ["TableCell", 30], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 45], ["Text", 5], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 40], ["Text", 4], ["ListItem", 4], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 51], ["ListItem", 16], ["Reference", 15], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 52], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 51], ["ListItem", 18], ["Reference", 17], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Knowledge_Hierarchy_Guided_Biological-Medical_Dataset_Distillation_for_Domain_LLM_Training"}