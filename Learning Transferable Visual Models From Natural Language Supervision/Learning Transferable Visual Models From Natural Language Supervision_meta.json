{"table_of_contents": [{"title": "Learning Transferable Visual Models From Natural Language Supervision", "heading_level": null, "page_id": 0, "polygon": [[66.75, 89.25], [528.328125, 89.25], [528.328125, 103.640625], [66.75, 103.640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.44287109375, 187.5], [196.03125, 187.5], [196.03125, 199.353515625], [148.44287109375, 199.353515625]]}, {"title": "1. Introduction and Motivating Work", "heading_level": null, "page_id": 0, "polygon": [[54.0, 602.25], [246.75, 602.25], [246.75, 613.72265625], [54.0, 613.72265625]]}, {"title": "2. Approach", "heading_level": null, "page_id": 2, "polygon": [[54.0, 495.75], [118.5, 495.75], [118.5, 506.6015625], [54.0, 506.6015625]]}, {"title": "2.1. Natural Language Supervision", "heading_level": null, "page_id": 2, "polygon": [[54.0, 515.25], [204.0, 515.25], [204.0, 525.1640625], [54.0, 525.1640625]]}, {"title": "2.2. Creating a Sufficiently Large Dataset", "heading_level": null, "page_id": 2, "polygon": [[306.0, 339.75], [484.5, 339.75], [484.5, 349.400390625], [306.0, 349.400390625]]}, {"title": "2.3. Selecting an Efficient Pre-Training Method", "heading_level": null, "page_id": 3, "polygon": [[54.0, 129.75], [256.5, 129.75], [256.5, 139.8955078125], [54.0, 139.8955078125]]}, {"title": "2.4. <PERSON><PERSON><PERSON> and <PERSON><PERSON> a Model", "heading_level": null, "page_id": 3, "polygon": [[305.25, 566.25], [455.25, 566.25], [455.25, 576.2109375], [305.25, 576.2109375]]}, {"title": "2.5. Training", "heading_level": null, "page_id": 4, "polygon": [[306.0, 190.5], [363.0, 190.5], [363.0, 200.3203125], [306.0, 200.3203125]]}, {"title": "3. Experiments", "heading_level": null, "page_id": 5, "polygon": [[54.0, 68.9326171875], [132.75, 68.9326171875], [132.75, 78.890625], [54.0, 78.890625]]}, {"title": "3.1. Zero-Shot Transfer", "heading_level": null, "page_id": 5, "polygon": [[54.0, 87.75], [156.0, 87.75], [156.0, 98.61328125], [54.0, 98.61328125]]}, {"title": "3.1.1. MOTIVATION", "heading_level": null, "page_id": 5, "polygon": [[54.0, 106.5], [138.0, 106.5], [138.0, 116.6923828125], [54.0, 116.6923828125]]}, {"title": "3.1.2. USING CLIP FOR ZERO-SHOT TRANSFER", "heading_level": null, "page_id": 5, "polygon": [[306.59765625, 176.25], [511.5, 176.25], [511.5, 186.4951171875], [306.59765625, 186.4951171875]]}, {"title": "3.1.3. <PERSON><PERSON><PERSON><PERSON> COMPARISON TO VISUAL N-GRAMS", "heading_level": null, "page_id": 5, "polygon": [[306.75, 577.5], [525.75, 577.5], [525.75, 587.8125], [306.75, 587.8125]]}, {"title": "3.1.4. PROMPT ENGINEERING AND ENSEMBLING", "heading_level": null, "page_id": 6, "polygon": [[54.0, 554.25], [263.25, 554.25], [263.25, 563.8359375], [54.0, 563.8359375]]}, {"title": "3.1.5. ANALYSIS OF ZERO-SHOT CLIP PERFORMANCE", "heading_level": null, "page_id": 7, "polygon": [[54.0, 528.75], [289.5, 528.75], [289.5, 538.69921875], [54.0, 538.69921875]]}, {"title": "3.2. Representation Learning", "heading_level": null, "page_id": 10, "polygon": [[54.0, 401.25], [180.0, 401.25], [180.0, 411.46875], [54.0, 411.46875]]}, {"title": "3.3. <PERSON><PERSON><PERSON> to Natural Distribution Shift", "heading_level": null, "page_id": 12, "polygon": [[54.0, 419.25], [245.25, 419.25], [245.25, 428.87109375], [54.0, 428.87109375]]}, {"title": "4. <PERSON><PERSON><PERSON><PERSON> to Human Performance", "heading_level": null, "page_id": 15, "polygon": [[54.0, 656.25], [255.0, 656.25], [255.0, 667.4765625], [54.0, 667.4765625]]}, {"title": "5. Data Overlap Analysis", "heading_level": null, "page_id": 16, "polygon": [[306.0, 411.75], [435.75, 411.75], [435.75, 423.0703125], [306.0, 423.0703125]]}, {"title": "6. Lim<PERSON>s", "heading_level": null, "page_id": 17, "polygon": [[305.25, 622.5], [378.75, 622.5], [378.75, 633.83203125], [305.25, 633.83203125]]}, {"title": "7. <PERSON><PERSON>", "heading_level": null, "page_id": 19, "polygon": [[306.0, 69.0], [406.5, 69.0], [406.5, 79.6640625], [306.0, 79.6640625]]}, {"title": "7.1. <PERSON><PERSON>", "heading_level": null, "page_id": 20, "polygon": [[54.0, 420.75], [92.25, 420.75], [92.25, 430.8046875], [54.0, 430.8046875]]}, {"title": "7.2. Surveillance", "heading_level": null, "page_id": 22, "polygon": [[305.25, 608.25], [377.25, 608.25], [377.25, 617.9765625], [305.25, 617.9765625]]}, {"title": "7.3. Future Work", "heading_level": null, "page_id": 24, "polygon": [[54.0, 664.5], [129.0, 664.5], [129.0, 674.82421875], [54.0, 674.82421875]]}, {"title": "8. Related Work", "heading_level": null, "page_id": 24, "polygon": [[306.0, 480.75], [391.5, 480.75], [391.5, 491.90625], [306.0, 491.90625]]}, {"title": "9. Conclusion", "heading_level": null, "page_id": 26, "polygon": [[54.0, 300.0], [124.5, 300.0], [124.5, 311.30859375], [54.0, 311.30859375]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 26, "polygon": [[54.0, 476.25], [148.5, 476.25], [148.5, 486.4921875], [54.0, 486.4921875]]}, {"title": "References", "heading_level": null, "page_id": 26, "polygon": [[306.298828125, 68.5458984375], [363.076171875, 68.5458984375], [363.076171875, 78.50390625], [306.298828125, 78.50390625]]}, {"title": "<PERSON><PERSON>-probe evaluation", "heading_level": null, "page_id": 36, "polygon": [[54.0, 69.0], [194.3876953125, 69.0], [194.3876953125, 79.27734375], [54.0, 79.27734375]]}, {"title": "A.1. Datasets", "heading_level": null, "page_id": 36, "polygon": [[54.0, 137.25], [112.5, 137.25], [112.5, 147.4365234375], [54.0, 147.4365234375]]}, {"title": "A.2. Models", "heading_level": null, "page_id": 36, "polygon": [[54.0, 635.25], [106.5322265625, 635.25], [106.5322265625, 645.8203125], [54.0, 645.8203125]]}, {"title": "A.3. Evaluation", "heading_level": null, "page_id": 37, "polygon": [[54.0, 497.25], [122.25, 497.25], [122.25, 507.375], [54.0, 507.375]]}, {"title": "A.4. Results", "heading_level": null, "page_id": 37, "polygon": [[306.0, 406.5], [358.59375, 406.5], [358.59375, 416.49609375], [306.0, 416.49609375]]}, {"title": "<PERSON>. Zero-Shot Prediction", "heading_level": null, "page_id": 43, "polygon": [[54.0, 69.0], [177.75, 69.0], [177.75, 79.42236328125], [54.0, 79.42236328125]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 43, "polygon": [[54.0, 164.25], [167.25, 164.25], [167.25, 174.8935546875], [54.0, 174.8935546875]]}, {"title": "D. Dataset Ablation on YFCC100M", "heading_level": null, "page_id": 43, "polygon": [[305.25, 333.75], [489.75, 333.75], [489.75, 344.953125], [305.25, 344.953125]]}, {"title": "E. Selected Task and Dataset Results", "heading_level": null, "page_id": 44, "polygon": [[54.0, 180.0], [243.0, 180.0], [243.0, 191.232421875], [54.0, 191.232421875]]}, {"title": "E.1. Image and Text Retrieval", "heading_level": null, "page_id": 44, "polygon": [[54.0, 273.75], [183.0, 273.75], [183.0, 283.658203125], [54.0, 283.658203125]]}, {"title": "E.2. Optical Character Recognition", "heading_level": null, "page_id": 44, "polygon": [[54.0, 556.875], [206.25, 556.875], [206.25, 566.9296875], [54.0, 566.9296875]]}, {"title": "E.3. Action Recognition in Videos", "heading_level": null, "page_id": 45, "polygon": [[54.0, 550.5], [198.2724609375, 550.5], [198.2724609375, 560.35546875], [54.0, 560.35546875]]}, {"title": "E.4. Geolocalization", "heading_level": null, "page_id": 46, "polygon": [[305.25, 190.5], [393.0, 190.5], [393.0, 199.740234375], [305.25, 199.740234375]]}, {"title": "E.5. <PERSON><PERSON><PERSON> to Distribution Shift", "heading_level": null, "page_id": 46, "polygon": [[305.25, 401.25], [463.5, 401.25], [463.5, 411.08203125], [305.25, 411.08203125]]}, {"title": "F. Model Hyperparameters", "heading_level": null, "page_id": 47, "polygon": [[53.25, 66.0], [195.0, 66.0], [195.0, 80.25], [53.25, 80.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 87], ["Text", 7], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8156, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 127], ["Text", 6], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1002, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 104], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 103], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 101], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 103], ["Text", 6], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 100], ["TableCell", 24], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["SectionHeader", 1], ["Footnote", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2982, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 111], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Caption", 1], ["Figure", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1143, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 105], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 404], ["Line", 150], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1964, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 103], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 120], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 111], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 94], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 964, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 114], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1268, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 108], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 124], ["TableCell", 50], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3498, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 91], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 865, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 102], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 974, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 101], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 289], ["Span", 241], ["Line", 76], ["Text", 6], ["Table", 3], ["Caption", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 2], ["TableGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7059, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["TableCell", 98], ["Line", 79], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2944, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 229], ["Line", 102], ["Text", 13], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 109], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1012, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 94], ["TableCell", 44], ["Text", 10], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1], ["Footnote", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1147, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 105], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 94], ["ListItem", 11], ["Text", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 90], ["ListItem", 26], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 94], ["ListItem", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 95], ["ListItem", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 92], ["ListItem", 26], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 94], ["ListItem", 25], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 95], ["ListItem", 24], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 96], ["ListItem", 24], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 96], ["ListItem", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Line", 22], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 95], ["Text", 15], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 58], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 280], ["Span", 65], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 809], ["Span", 733], ["Line", 96], ["Table", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 23076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 819], ["Line", 282], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2104, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 969], ["Line", 303], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4125, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 691], ["TableCell", 561], ["Line", 230], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6343, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["TableCell", 149], ["Line", 99], ["Text", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2852, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 100], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["TableCell", 408], ["Line", 114], ["Text", 4], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 26406, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["TableCell", 202], ["Line", 83], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9471, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 203], ["Span", 196], ["Line", 43], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4493, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Learning Transferable Visual Models From Natural Language Supervision"}