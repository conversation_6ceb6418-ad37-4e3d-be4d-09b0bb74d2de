{"table_of_contents": [{"title": "Learning to Generate Synthetic Training Data using\nGradient Matching and Implicit Differentiation", "heading_level": null, "page_id": 0, "polygon": [[48.75, 54.0], [561.19921875, 54.0], [561.19921875, 102.9638671875], [48.75, 102.9638671875]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 342.0], [213.75, 342.0], [213.75, 351.9140625], [133.5, 351.9140625]]}, {"title": "II. RELATED WORK", "heading_level": null, "page_id": 0, "polygon": [[393.0, 579.75], [480.75, 579.75], [480.75, 588.97265625], [393.0, 588.97265625]]}, {"title": "III. GENERAL FORMULATION", "heading_level": null, "page_id": 1, "polygon": [[109.5, 297.0], [237.0, 297.0], [237.0, 307.0546875], [109.5, 307.0546875]]}, {"title": "IV. BA<PERSON>KPROPAGATION THROUGH THE STUDENT'S\nLEARNING PROCESS", "heading_level": null, "page_id": 1, "polygon": [[64.5, 657.75], [284.3349609375, 657.75], [284.3349609375, 678.69140625], [64.5, 678.69140625]]}, {"title": "V. IMPLICIT DIFFERENTIATION", "heading_level": null, "page_id": 2, "polygon": [[106.5, 357.75], [241.5, 358.5], [241.5, 367.576171875], [106.5, 367.576171875]]}, {"title": "VI. GRADIENT MATCHING", "heading_level": null, "page_id": 2, "polygon": [[378.75, 660.75], [495.75, 660.75], [495.75, 671.34375], [378.75, 671.34375]]}, {"title": "VII. GENERATIVE TEACHING NETWORK", "heading_level": null, "page_id": 3, "polygon": [[350.25, 331.5], [525.0, 331.5], [525.0, 341.859375], [350.25, 341.859375]]}, {"title": "VIII. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[127.97314453125, 323.25], [219.75, 323.25], [219.75, 333.931640625], [127.97314453125, 333.931640625]]}, {"title": "<PERSON><PERSON> Distillation with time limit", "heading_level": null, "page_id": 4, "polygon": [[46.5, 339.75], [171.0, 339.75], [171.0, 349.59375], [46.5, 349.59375]]}, {"title": "B. Training generator with gradient matching", "heading_level": null, "page_id": 4, "polygon": [[310.5, 354.0], [500.25, 354.0], [500.25, 363.12890625], [310.5, 363.12890625]]}, {"title": "<PERSON><PERSON> Distillation with implicit differentiation", "heading_level": null, "page_id": 5, "polygon": [[310.78125, 345.75], [487.08984375, 345.75], [487.08984375, 355.974609375], [310.78125, 355.974609375]]}, {"title": "<PERSON>. Distillation with augmentation", "heading_level": null, "page_id": 6, "polygon": [[48.0, 557.25], [188.560546875, 557.25], [188.560546875, 566.54296875], [48.0, 566.54296875]]}, {"title": "E. Generalizability", "heading_level": null, "page_id": 6, "polygon": [[310.5, 372.0], [391.5, 372.0], [391.5, 381.498046875], [310.5, 381.498046875]]}, {"title": "IX. CONCLUSION", "heading_level": null, "page_id": 6, "polygon": [[396.75, 561.0], [476.33203125, 561.0], [476.33203125, 571.18359375], [396.75, 571.18359375]]}, {"title": "ACKNOWLEDGMENT", "heading_level": null, "page_id": 7, "polygon": [[129.75, 129.75], [219.638671875, 129.75], [219.638671875, 139.025390625], [129.75, 139.025390625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 7, "polygon": [[144.75, 198.75], [202.5, 198.75], [202.5, 208.634765625], [144.75, 208.634765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 95], ["Text", 10], ["SectionHeader", 3], ["Reference", 2], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4002, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 1075], ["Line", 138], ["ListItem", 12], ["Text", 9], ["TextInlineMath", 6], ["Equation", 6], ["Reference", 6], ["SectionHeader", 2], ["ListGroup", 2], ["Caption", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1099, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 989], ["Line", 154], ["Text", 12], ["Reference", 7], ["Equation", 6], ["TextInlineMath", 5], ["ListItem", 3], ["SectionHeader", 2], ["Figure", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 892], ["Line", 150], ["Text", 8], ["Equation", 6], ["TextInlineMath", 5], ["Reference", 3], ["ListItem", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4297, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 592], ["Line", 104], ["TableCell", 68], ["Text", 7], ["Reference", 7], ["SectionHeader", 3], ["Caption", 2], ["TextInlineMath", 2], ["Footnote", 2], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 797], ["Line", 157], ["TableCell", 72], ["Text", 5], ["Reference", 5], ["Caption", 4], ["Figure", 2], ["Table", 2], ["FigureGroup", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 2451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 872], ["TableCell", 137], ["Line", 121], ["Reference", 7], ["Table", 4], ["Text", 4], ["Caption", 3], ["SectionHeader", 3], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 2, "llm_tokens_used": 5165, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 66], ["ListItem", 20], ["Reference", 18], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Learning_to_Generate_Synthetic_Training_Data_using_Gradient_Matching_and_Implicit_Differentiation"}