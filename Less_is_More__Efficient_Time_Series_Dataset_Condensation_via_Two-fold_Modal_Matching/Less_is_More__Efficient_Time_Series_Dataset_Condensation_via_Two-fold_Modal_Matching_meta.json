{"table_of_contents": [{"title": "Less is More: Efficient Time Series Dataset Condensation via\nTwo-fold Modal Matching–Extended Version", "heading_level": null, "page_id": 0, "polygon": [[69.75, 82.5], [542.07421875, 82.5], [542.07421875, 118.8193359375], [69.75, 118.8193359375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[317.25, 388.5], [421.34765625, 388.5], [421.34765625, 399.8671875], [317.25, 399.8671875]]}, {"title": "PVLDB Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[52.5, 526.5], [149.712890625, 526.5], [149.712890625, 535.9921875], [52.5, 535.9921875]]}, {"title": "PVLDB Artifact Availability:", "heading_level": null, "page_id": 0, "polygon": [[52.5, 595.5], [157.5, 595.5], [157.5, 605.21484375], [52.5, 605.21484375]]}, {"title": "2 PRELIMINARIES", "heading_level": null, "page_id": 2, "polygon": [[52.5, 391.5], [156.0, 391.5], [156.0, 402.1875], [52.5, 402.1875]]}, {"title": "2.1 Dataset Condensation over Time Series", "heading_level": null, "page_id": 2, "polygon": [[52.5, 532.5], [272.25, 532.5], [272.25, 542.56640625], [52.5, 542.56640625]]}, {"title": "3 METHODOLOGY", "heading_level": null, "page_id": 2, "polygon": [[317.056640625, 430.5], [420.0, 430.5], [420.0, 440.859375], [317.056640625, 440.859375]]}, {"title": "3.1 Framework Overview", "heading_level": null, "page_id": 2, "polygon": [[316.458984375, 488.25], [449.25, 488.25], [449.25, 498.48046875], [316.458984375, 498.48046875]]}, {"title": "3.2 Time Series Feature Extraction", "heading_level": null, "page_id": 3, "polygon": [[315.861328125, 391.5], [495.0, 391.5], [495.0, 401.80078125], [315.861328125, 401.80078125]]}, {"title": "3.3 Decomposition-Driven Frequency Matching", "heading_level": null, "page_id": 4, "polygon": [[317.25, 306.75], [558.75, 306.75], [558.75, 316.142578125], [317.25, 316.142578125]]}, {"title": "3.4 Curriculum Training Trajectory Matching", "heading_level": null, "page_id": 5, "polygon": [[52.5, 279.0], [288.0, 279.0], [288.0, 290.0390625], [52.5, 290.0390625]]}, {"title": "3.5 Overall Objective Function", "heading_level": null, "page_id": 6, "polygon": [[316.7578125, 119.109375], [475.5, 119.109375], [475.5, 129.9375], [316.7578125, 129.9375]]}, {"title": "4 EXPERIMENTAL EVALUATION", "heading_level": null, "page_id": 6, "polygon": [[317.25, 279.017578125], [492.46875, 279.017578125], [492.46875, 289.845703125], [317.25, 289.845703125]]}, {"title": "4.1 Experimental Setup", "heading_level": null, "page_id": 6, "polygon": [[316.7578125, 295.259765625], [441.0, 295.259765625], [441.0, 305.701171875], [316.7578125, 305.701171875]]}, {"title": "4.2 Experimental Results", "heading_level": null, "page_id": 7, "polygon": [[316.5, 368.15625], [450.0, 368.15625], [450.0, 378.59765625], [316.5, 378.59765625]]}, {"title": "Table 3: Dynamic Tensor Memory Cost on Four Datasets", "heading_level": null, "page_id": 9, "polygon": [[58.4208984375, 312.0], [288.0, 312.0], [288.0, 321.556640625], [58.4208984375, 321.556640625]]}, {"title": "4.3 Application on Streaming Time Series", "heading_level": null, "page_id": 10, "polygon": [[52.5, 641.25], [266.25, 641.25], [266.25, 652.0078125], [52.5, 652.0078125]]}, {"title": "4.4 Performance on Time Series Classification", "heading_level": null, "page_id": 11, "polygon": [[52.5, 248.080078125], [288.75, 248.080078125], [288.75, 258.521484375], [52.5, 258.521484375]]}, {"title": "5 RELATED WORK", "heading_level": null, "page_id": 11, "polygon": [[52.5, 469.5], [158.25, 469.5], [158.25, 480.3046875], [52.5, 480.3046875]]}, {"title": "5.1 Time Series Modeling", "heading_level": null, "page_id": 11, "polygon": [[52.5, 486.0], [186.46875, 486.0], [186.46875, 496.16015625], [52.5, 496.16015625]]}, {"title": "5.2 Coreset and Dataset Condensation", "heading_level": null, "page_id": 11, "polygon": [[317.056640625, 85.5], [513.087890625, 85.5], [513.087890625, 95.90625], [317.056640625, 95.90625]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 11, "polygon": [[316.5, 417.75], [405.75, 417.75], [405.75, 428.87109375], [316.5, 428.87109375]]}, {"title": "7 ACKNOWLEDGMENTS", "heading_level": null, "page_id": 11, "polygon": [[316.5, 597.75], [452.25, 597.75], [452.25, 608.30859375], [316.5, 608.30859375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 12, "polygon": [[52.5, 85.5], [123.75, 85.5], [123.75, 95.8095703125], [52.5, 95.8095703125]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 13, "polygon": [[52.5, 84.75], [129.0, 84.75], [129.0, 96.72802734375], [52.5, 96.72802734375]]}, {"title": "A.1 Preliminaries", "heading_level": null, "page_id": 13, "polygon": [[52.5, 270.896484375], [148.5, 270.896484375], [148.5, 281.724609375], [52.5, 281.724609375]]}, {"title": "A.2 Methodology", "heading_level": null, "page_id": 13, "polygon": [[52.5, 306.28125], [146.25, 306.28125], [146.25, 317.109375], [52.5, 317.109375]]}, {"title": "A.3 Experiment", "heading_level": null, "page_id": 13, "polygon": [[52.5, 624.9375], [140.25, 624.9375], [140.25, 635.765625], [52.5, 635.765625]]}, {"title": "Table 10: Statistics of Classification Datasets", "heading_level": null, "page_id": 13, "polygon": [[342.755859375, 174.75], [528.029296875, 174.75], [528.029296875, 184.5615234375], [342.755859375, 184.5615234375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 108], ["Text", 15], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 114], ["Text", 9]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 748], ["Line", 120], ["Text", 11], ["TextInlineMath", 6], ["ListItem", 5], ["SectionHeader", 4], ["Reference", 3], ["Equation", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 134], ["TextInlineMath", 4], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["ListItem", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1504, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 902], ["Line", 148], ["Reference", 8], ["TextInlineMath", 7], ["Text", 5], ["Equation", 4], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 879], ["Line", 208], ["Text", 9], ["TextInlineMath", 7], ["Equation", 5], ["Reference", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 848], ["Line", 151], ["Text", 11], ["ListItem", 10], ["Reference", 9], ["Equation", 8], ["TextInlineMath", 6], ["SectionHeader", 3], ["ListGroup", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 714], ["Span", 489], ["Line", 97], ["ListItem", 5], ["Text", 4], ["ListGroup", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5861, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 142], ["Text", 8], ["Reference", 6], ["ListItem", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2192, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 418], ["Span", 386], ["Line", 106], ["Reference", 7], ["Text", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 2], ["SectionHeader", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7853, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 395], ["TableCell", 221], ["Line", 105], ["Text", 7], ["Table", 4], ["Reference", 4], ["Caption", 3], ["TableGroup", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 7139, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 119], ["Text", 7], ["SectionHeader", 6], ["Reference", 5], ["Figure", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 820, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 593], ["Line", 151], ["ListItem", 55], ["Reference", 55], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["TableCell", 190], ["Line", 118], ["Reference", 15], ["Text", 8], ["SectionHeader", 5], ["Table", 5], ["ListItem", 5], ["Code", 3], ["Caption", 2], ["ListGroup", 2], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 8009, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 157], ["TableCell", 48], ["Text", 7], ["Reference", 6], ["Caption", 2], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4234, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["TableCell", 122], ["Line", 106], ["Text", 6], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 7493, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 136], ["Span", 127], ["Line", 47], ["Text", 5], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4594, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Less_is_More__Efficient_Time_Series_Dataset_Condensation_via_Two-fold_Modal_Matching"}