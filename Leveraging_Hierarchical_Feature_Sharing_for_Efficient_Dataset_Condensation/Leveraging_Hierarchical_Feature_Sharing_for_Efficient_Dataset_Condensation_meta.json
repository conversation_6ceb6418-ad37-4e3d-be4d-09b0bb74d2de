{"table_of_contents": [{"title": "Leveraging Hierarchical Feature Sharing for\nEfficient Dataset Condensation", "heading_level": null, "page_id": 0, "polygon": [[153.298828125, 114.75], [459.0, 114.75], [459.0, 146.0830078125], [153.298828125, 146.0830078125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 515.109375], [229.201171875, 515.109375], [229.201171875, 525.9375], [133.5, 525.9375]]}, {"title": "2 Background and Related Work", "heading_level": null, "page_id": 2, "polygon": [[133.5, 516.0], [338.25, 516.0], [338.25, 528.2578125], [133.5, 528.2578125]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 4, "polygon": [[133.5, 117.0], [232.5, 117.0], [232.5, 128.1005859375], [133.5, 128.1005859375]]}, {"title": "3.1 Hierarchical Memory Network (HMN)", "heading_level": null, "page_id": 4, "polygon": [[133.5, 236.25], [356.203125, 236.25], [356.203125, 246.7265625], [133.5, 246.7265625]]}, {"title": "3.2 Post-Condensation Pruning of an HMN", "heading_level": null, "page_id": 6, "polygon": [[133.5, 117.75], [361.5, 117.75], [361.5, 127.810546875], [133.5, 127.810546875]]}, {"title": "Algorithm 1 Over-budget HMN Double-end Pruning", "heading_level": null, "page_id": 7, "polygon": [[133.5, 327.75], [373.5, 327.75], [373.5, 338.37890625], [133.5, 338.37890625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 8, "polygon": [[133.5, 166.2890625], [229.5, 166.2890625], [229.5, 177.1171875], [133.5, 177.1171875]]}, {"title": "4.1 Experimental Settings", "heading_level": null, "page_id": 8, "polygon": [[133.5, 420.0], [273.0, 420.0], [273.0, 429.64453125], [133.5, 429.64453125]]}, {"title": "10 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 9, "polygon": [[133.5, 92.25], [228.4541015625, 92.25], [228.4541015625, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "4.2 Data Condensation Performance Comparison", "heading_level": null, "page_id": 9, "polygon": [[133.5, 626.25], [387.87890625, 626.25], [387.87890625, 635.765625], [133.5, 635.765625]]}, {"title": "4.3 Cross-architecture Transferability", "heading_level": null, "page_id": 10, "polygon": [[133.5, 521.25], [330.802734375, 521.25], [330.802734375, 531.73828125], [133.5, 531.73828125]]}, {"title": "12 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 11, "polygon": [[133.5, 92.25], [229.201171875, 92.25], [229.201171875, 101.900390625], [133.5, 101.900390625]]}, {"title": "4.4 GPU Memory Comparison", "heading_level": null, "page_id": 11, "polygon": [[133.5, 282.0], [297.0, 282.0], [297.0, 292.552734375], [133.5, 292.552734375]]}, {"title": "4.5 Ablation Studies", "heading_level": null, "page_id": 12, "polygon": [[133.5, 365.25], [245.25, 365.25], [245.25, 375.697265625], [133.5, 375.697265625]]}, {"title": "14 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 13, "polygon": [[133.5, 92.25], [230.2470703125, 92.25], [230.2470703125, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "4.6 Continual Learning Performance Comparison", "heading_level": null, "page_id": 13, "polygon": [[133.5, 403.5], [389.970703125, 403.5], [389.970703125, 413.40234375], [133.5, 413.40234375]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[133.5, 549.75], [219.75, 549.75], [219.75, 561.515625], [133.5, 561.515625]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 14, "polygon": [[133.5, 117.0], [241.154296875, 117.0], [241.154296875, 128.1005859375], [133.5, 128.1005859375]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[133.5, 253.5], [198.0, 253.5], [198.0, 264.515625], [133.5, 264.515625]]}, {"title": "A Appendix Overview", "heading_level": null, "page_id": 17, "polygon": [[133.5, 117.0], [275.25, 117.0], [275.25, 128.77734375], [133.5, 128.77734375]]}, {"title": "B Discussion", "heading_level": null, "page_id": 17, "polygon": [[133.5, 247.5], [219.75, 247.5], [219.75, 259.1015625], [133.5, 259.1015625]]}, {"title": "C Experiment Setting and Implementation Details", "heading_level": null, "page_id": 17, "polygon": [[133.5, 546.75], [442.265625, 546.75], [442.265625, 558.03515625], [133.5, 558.03515625]]}, {"title": "C.1 HMN architecture design.", "heading_level": null, "page_id": 17, "polygon": [[133.5, 579.0], [294.345703125, 579.0], [294.345703125, 589.74609375], [133.5, 589.74609375]]}, {"title": "C.2 Training settings", "heading_level": null, "page_id": 19, "polygon": [[133.5, 193.5], [248.25, 193.5], [248.25, 203.607421875], [133.5, 203.607421875]]}, {"title": "D Additional Evaluation Results", "heading_level": null, "page_id": 20, "polygon": [[133.5, 408.75], [335.28515625, 408.75], [335.28515625, 419.9765625], [133.5, 419.9765625]]}, {"title": "D.1 Pruning Rate v.s. Accuracy", "heading_level": null, "page_id": 20, "polygon": [[133.5, 554.16796875], [303.01171875, 554.16796875], [303.01171875, 564.22265625], [133.5, 564.22265625]]}, {"title": "22 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 21, "polygon": [[132.6796875, 92.25], [228.005859375, 92.25], [228.005859375, 101.900390625], [132.6796875, 101.900390625]]}, {"title": "D.2 Training Time Comparison with Condensed Datasets", "heading_level": null, "page_id": 21, "polygon": [[133.1279296875, 191.25], [431.25, 191.25], [431.25, 201.673828125], [133.1279296875, 201.673828125]]}, {"title": "D.3 Ablation Study on Learning Rate Scheduler", "heading_level": null, "page_id": 21, "polygon": [[133.5, 543.7265625], [384.75, 543.7265625], [384.75, 553.78125], [133.5, 553.78125]]}, {"title": "D.4 Data Profiling on SOTA Methods", "heading_level": null, "page_id": 22, "polygon": [[133.5, 240.5390625], [334.5, 240.5390625], [334.5, 250.59375], [133.5, 250.59375]]}, {"title": "D.5 Visualization", "heading_level": null, "page_id": 23, "polygon": [[133.5, 618.75], [229.3505859375, 618.75], [229.3505859375, 629.19140625], [133.5, 629.19140625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6727, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 61], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 637, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 78], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 917, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 46], ["Text", 7]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 226], ["Line", 43], ["Text", 5], ["ListItem", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 41], ["Text", 4], ["TextInlineMath", 2], ["Equation", 2], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 63], ["TableCell", 8], ["Text", 6], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["SectionHeader", 1], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2606, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 45], ["ListItem", 11], ["Text", 5], ["TextInlineMath", 4], ["Footnote", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 43], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 1010], ["TableCell", 349], ["Line", 226], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 57], ["TableCell", 48], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1994, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["TableCell", 158], ["Line", 63], ["Text", 5], ["Reference", 3], ["SectionHeader", 2], ["Caption", 2], ["Table", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 6575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 97], ["Text", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1092, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 55], ["TableCell", 40], ["Text", 4], ["SectionHeader", 3], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 47], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 52], ["ListItem", 20], ["Reference", 19], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 50], ["ListItem", 17], ["Reference", 17], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 36], ["Text", 5], ["SectionHeader", 4], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["TableCell", 133], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5890, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 45], ["Text", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 43], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 54], ["TableCell", 21], ["Reference", 4], ["SectionHeader", 3], ["Text", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1936, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 68], ["Text", 4], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 54], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1548, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 26], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 653, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 39], ["Line", 11], ["Caption", 6], ["Text", 3], ["Picture", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 3201, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 15], ["Text", 8], ["Caption", 5], ["Figure", 2], ["FigureGroup", 2], ["Picture", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 7, "llm_error_count": 0, "llm_tokens_used": 4297, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 25], ["Line", 12], ["Caption", 3], ["Picture", 2], ["Text", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1923, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Leveraging_Hierarchical_Feature_Sharing_for_Efficient_Dataset_Condensation"}