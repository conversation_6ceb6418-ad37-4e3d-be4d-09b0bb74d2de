{"table_of_contents": [{"title": "Loss-Curvature Matching for Dataset Selection and Condensation", "heading_level": null, "page_id": 0, "polygon": [[102.75, 92.25], [510.3984375, 92.25], [510.3984375, 106.2509765625], [102.75, 106.2509765625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[156.0, 204.75], [204.0, 204.75], [204.0, 215.595703125], [156.0, 215.595703125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[62.25, 553.0078125], [177.0, 553.0078125], [177.0, 565.3828125], [62.25, 565.3828125]]}, {"title": "2 PRELIMINARY", "heading_level": null, "page_id": 1, "polygon": [[61.5, 363.75], [167.34375, 363.75], [167.34375, 375.50390625], [61.5, 375.50390625]]}, {"title": "2.1 Notations", "heading_level": null, "page_id": 1, "polygon": [[61.5, 390.0], [127.5, 390.0], [127.5, 400.25390625], [61.5, 400.25390625]]}, {"title": "2.2 Previous Researches on Dataset Reduction", "heading_level": null, "page_id": 1, "polygon": [[62.25, 599.25], [267.0, 599.25], [267.0, 609.46875], [62.25, 609.46875]]}, {"title": "2.3 Generalization on Parameter Space", "heading_level": null, "page_id": 2, "polygon": [[61.5, 323.25], [237.0, 323.25], [237.0, 332.96484375], [61.5, 332.96484375]]}, {"title": "3 METHOD", "heading_level": null, "page_id": 2, "polygon": [[61.5, 667.5], [136.5, 667.5], [136.5, 678.69140625], [61.5, 678.69140625]]}, {"title": "3.1 Parameter Generalization in Dataset Reduction", "heading_level": null, "page_id": 2, "polygon": [[313.470703125, 358.5], [541.5, 358.5], [541.5, 368.54296875], [313.470703125, 368.54296875]]}, {"title": "3.2 Loss-Curvature Matching (LCMat)", "heading_level": null, "page_id": 2, "polygon": [[313.5, 695.25], [489.75, 695.25], [489.75, 705.375], [313.5, 705.375]]}, {"title": "3.3 LCMat for Selection-based method", "heading_level": null, "page_id": 3, "polygon": [[313.5, 568.5], [487.6875, 568.5], [487.6875, 578.14453125], [313.5, 578.14453125]]}, {"title": "3.4 LCMat for Condensation-based method", "heading_level": null, "page_id": 4, "polygon": [[313.5, 285.0], [507.75, 285.0], [507.75, 295.259765625], [313.5, 295.259765625]]}, {"title": "3.5 Theoretical Understanding of LCMat", "heading_level": null, "page_id": 5, "polygon": [[62.23095703125, 372.75], [246.0, 372.75], [246.0, 382.078125], [62.23095703125, 382.078125]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[313.5, 450.75], [420.75, 450.75], [420.75, 462.12890625], [313.5, 462.12890625]]}, {"title": "4.1 Coreset Selection Evaluation", "heading_level": null, "page_id": 5, "polygon": [[313.5, 564.609375], [459.75, 564.609375], [459.75, 573.890625], [313.5, 573.890625]]}, {"title": "4.2 Dataset Condensation Evaluation", "heading_level": null, "page_id": 7, "polygon": [[313.5, 467.25], [480.75, 467.25], [480.75, 477.984375], [313.5, 477.984375]]}, {"title": "4.3 Application : Continual Learning with Memory\nReplay", "heading_level": null, "page_id": 8, "polygon": [[313.5, 74.25], [541.4765625, 74.25], [541.4765625, 95.95458984375], [313.5, 95.95458984375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[311.9765625, 501.75], [414.17578125, 501.75], [414.17578125, 513.94921875], [311.9765625, 513.94921875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[313.5, 697.5], [398.25, 697.5], [398.25, 707.30859375], [313.5, 707.30859375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[62.25, 135.544921875], [109.5, 135.544921875], [109.5, 145.212890625], [62.25, 145.212890625]]}, {"title": "A Proofs", "heading_level": null, "page_id": 12, "polygon": [[61.5, 72.0], [117.0, 72.0], [117.0, 83.86962890625], [61.5, 83.86962890625]]}, {"title": "A.1 Proof of Proposition 1", "heading_level": null, "page_id": 12, "polygon": [[61.5, 98.25], [181.5, 98.25], [181.5, 108.66796875], [61.5, 108.66796875]]}, {"title": "A.2 Proof of Eq (7)", "heading_level": null, "page_id": 12, "polygon": [[61.5, 615.0], [151.0576171875, 615.0], [151.0576171875, 625.7109375], [61.5, 625.7109375]]}, {"title": "A.3 Proof of Eq (10)", "heading_level": null, "page_id": 13, "polygon": [[61.5, 73.5], [156.0, 73.5], [156.0, 83.57958984375], [61.5, 83.57958984375]]}, {"title": "A.4 Proof of Theorem 3.4", "heading_level": null, "page_id": 14, "polygon": [[61.5, 263.25], [178.8486328125, 263.25], [178.8486328125, 273.41015625], [61.5, 273.41015625]]}, {"title": "A.5 Proof of Corollary 3.5", "heading_level": null, "page_id": 17, "polygon": [[61.5, 140.25], [181.5, 140.25], [181.5, 150.1435546875], [61.5, 150.1435546875]]}, {"title": "B Further Analyses of LCMat", "heading_level": null, "page_id": 17, "polygon": [[61.5, 484.5], [226.5, 484.5], [226.5, 495.7734375], [61.5, 495.7734375]]}, {"title": "B.1 Analyses on L_{abs}", "heading_level": null, "page_id": 17, "polygon": [[61.5, 510.75], [159.0, 510.75], [159.0, 520.5234375], [61.5, 520.5234375]]}, {"title": "B.2 Analyses on Sub-Dimension Selection", "heading_level": null, "page_id": 18, "polygon": [[61.5, 505.5], [246.75, 504.75], [246.75, 515.49609375], [62.25, 515.49609375]]}, {"title": "B.3 Discussion on the Limitations and Social Impacts of LCMat", "heading_level": null, "page_id": 18, "polygon": [[61.5, 672.0], [340.5, 672.0], [340.5, 682.171875], [61.5, 682.171875]]}, {"title": "C Technical Survey of Methods for Dataset Reduction", "heading_level": null, "page_id": 19, "polygon": [[61.5, 149.466796875], [347.25, 149.466796875], [347.25, 161.26171875], [61.5, 161.26171875]]}, {"title": "C.1 Selection-based Methods", "heading_level": null, "page_id": 19, "polygon": [[61.5, 175.18359375], [194.25, 174.75], [194.25, 185.431640625], [61.5, 185.431640625]]}, {"title": "C.2 Condensation-based Methods", "heading_level": null, "page_id": 20, "polygon": [[61.5, 134.19140625], [214.7080078125, 134.19140625], [214.7080078125, 146.373046875], [61.5, 146.373046875]]}, {"title": "D Experimental Details and Further Results", "heading_level": null, "page_id": 20, "polygon": [[61.5, 369.31640625], [299.42578125, 369.31640625], [299.42578125, 382.8515625], [61.5, 382.8515625]]}, {"title": "D.1 Experimental Details", "heading_level": null, "page_id": 20, "polygon": [[61.5, 394.83984375], [178.998046875, 394.83984375], [178.998046875, 407.21484375], [61.5, 407.21484375]]}, {"title": "D.2 Wall-Clock Time Analyses of Selection-based Methods", "heading_level": null, "page_id": 20, "polygon": [[61.5, 682.9453125], [321.240234375, 682.9453125], [321.240234375, 694.546875], [61.5, 694.546875]]}, {"title": "D.3 Results with Inception-v3", "heading_level": null, "page_id": 21, "polygon": [[61.5, 215.25], [196.5, 215.25], [196.5, 225.45703125], [61.5, 225.45703125]]}, {"title": "D.4 Selected Images for All Class in Cifar-10", "heading_level": null, "page_id": 22, "polygon": [[61.5, 72.509765625], [261.0, 72.509765625], [261.0, 83.91796875], [61.5, 83.91796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 88], ["Text", 11], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 10097, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 854], ["Line", 128], ["TextInlineMath", 6], ["Reference", 5], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 665], ["Line", 89], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1532, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1308], ["Line", 221], ["TextInlineMath", 12], ["Reference", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8452, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1461], ["Line", 204], ["TextInlineMath", 12], ["Equation", 7], ["Reference", 7], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1206, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1069], ["Line", 134], ["TextInlineMath", 9], ["Reference", 5], ["Text", 4], ["Equation", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1233], ["TableCell", 535], ["Line", 99], ["Text", 5], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 8689, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 673], ["Line", 141], ["TableCell", 126], ["Text", 5], ["Caption", 3], ["TextInlineMath", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 12899, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 107], ["TableCell", 89], ["Text", 6], ["Caption", 3], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 103], ["ListItem", 24], ["Reference", 24], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 259], ["Line", 103], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 8], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1413], ["Line", 263], ["Equation", 8], ["TextInlineMath", 6], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 5, "llm_error_count": 0, "llm_tokens_used": 6243, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1168], ["Line", 292], ["Equation", 7], ["Text", 4], ["TextInlineMath", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 914], ["Line", 71], ["Text", 6], ["TextInlineMath", 5], ["Equation", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 1081], ["Line", 160], ["Equation", 9], ["TextInlineMath", 8], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 1321], ["Line", 215], ["Equation", 12], ["TextInlineMath", 6], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5232, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 924], ["Line", 97], ["Equation", 7], ["Text", 5], ["TextInlineMath", 4], ["Reference", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 693], ["Line", 81], ["TextInlineMath", 4], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 59], ["Text", 12], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1068, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 47], ["Text", 8], ["SectionHeader", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["TableCell", 223], ["Line", 40], ["Text", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3472, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 87], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 945, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Loss-Curvature_Matching_for_Dataset_Selection_and_Condensation"}