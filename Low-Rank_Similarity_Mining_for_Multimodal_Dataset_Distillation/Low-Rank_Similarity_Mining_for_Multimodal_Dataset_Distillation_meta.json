{"table_of_contents": [{"title": "Low-Rank Similarity Mining for Multimodal Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[97.5, 89.25], [498.0, 89.25], [498.0, 103.833984375], [97.5, 103.833984375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 187.4619140625], [148.5, 187.4619140625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 462.75], [132.75, 462.75], [132.75, 474.1171875], [54.0, 474.1171875]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[306.0, 132.75], [391.5, 132.75], [391.5, 143.47265625], [306.0, 143.47265625]]}, {"title": "2.1. Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[305.701171875, 152.8505859375], [406.705078125, 152.8505859375], [406.705078125, 162.3251953125], [305.701171875, 162.3251953125]]}, {"title": "2.2. Image-text Contrastive Learning", "heading_level": null, "page_id": 2, "polygon": [[54.0, 190.5], [213.75, 190.5], [213.75, 199.93359375], [54.0, 199.93359375]]}, {"title": "3. Methodology", "heading_level": null, "page_id": 2, "polygon": [[54.0, 451.5], [135.0, 451.5], [135.0, 462.515625], [54.0, 462.515625]]}, {"title": "3.1. Preliminary", "heading_level": null, "page_id": 2, "polygon": [[54.0, 471.0], [124.5, 471.0], [124.5, 481.46484375], [54.0, 481.46484375]]}, {"title": "3.2. Similarity Mining for Image-Text Distillation", "heading_level": null, "page_id": 3, "polygon": [[54.0, 192.0], [265.5, 192.0], [265.5, 202.25390625], [54.0, 202.25390625]]}, {"title": "3.3. Justification of Similarity Mining", "heading_level": null, "page_id": 3, "polygon": [[306.0, 562.5], [466.5, 562.5], [466.5, 572.73046875], [306.0, 572.73046875]]}, {"title": "3.4. Low Rank Similarity Mining", "heading_level": null, "page_id": 4, "polygon": [[306.0, 491.25], [449.25, 491.25], [449.25, 501.57421875], [306.0, 501.57421875]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 5, "polygon": [[305.8505859375, 408.0], [384.75, 408.0], [384.75, 419.9765625], [305.8505859375, 419.9765625]]}, {"title": "4.1. Dataset and Metrics", "heading_level": null, "page_id": 5, "polygon": [[306.0, 429.0], [411.75, 429.0], [411.75, 439.3125], [306.0, 439.3125]]}, {"title": "4.2. <PERSON><PERSON> and Proposed Method", "heading_level": null, "page_id": 5, "polygon": [[305.8505859375, 593.25], [461.25, 593.25], [461.25, 602.89453125], [305.8505859375, 602.89453125]]}, {"title": "4.3. Implementation Details", "heading_level": null, "page_id": 6, "polygon": [[54.0, 581.25], [173.25, 581.25], [173.25, 590.90625], [54.0, 590.90625]]}, {"title": "4.4. Results", "heading_level": null, "page_id": 6, "polygon": [[306.0, 543.75], [357.0, 543.75], [357.0, 554.94140625], [306.0, 554.94140625]]}, {"title": "4.5. Cross Architecture Generalization", "heading_level": null, "page_id": 7, "polygon": [[54.0, 465.0], [219.75, 465.0], [219.75, 475.27734375], [54.0, 475.27734375]]}, {"title": "4.6. Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[54.0, 664.5], [138.0, 664.5], [138.0, 674.25], [54.0, 674.25]]}, {"title": "perform above random model.", "heading_level": null, "page_id": 8, "polygon": [[54.0, 477.75], [176.25, 477.75], [176.25, 488.0390625], [54.0, 488.0390625]]}, {"title": "4.7. Visualization", "heading_level": null, "page_id": 8, "polygon": [[54.0, 598.5], [129.0, 598.5], [129.0, 608.6953125], [54.0, 608.6953125]]}, {"title": "5. Conclusions", "heading_level": null, "page_id": 8, "polygon": [[306.0, 602.89453125], [382.5, 602.89453125], [382.5, 613.72265625], [306.0, 613.72265625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [149.5634765625, 68.25], [149.5634765625, 79.6640625], [54.0, 79.6640625]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 9, "polygon": [[54.0, 164.0654296875], [147.75, 164.0654296875], [147.75, 174.7001953125], [54.0, 174.7001953125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 247.5], [111.75, 247.5], [111.75, 258.71484375], [54.0, 258.71484375]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 12, "polygon": [[54.0, 66.75], [157.9306640625, 66.75], [157.9306640625, 79.42236328125], [54.0, 79.42236328125]]}, {"title": "B. Derivation of Loss Gradients", "heading_level": null, "page_id": 12, "polygon": [[54.0, 348.75], [218.25, 348.75], [218.25, 360.421875], [54.0, 360.421875]]}, {"title": "B.1. Proposition 3.1 and 3.2", "heading_level": null, "page_id": 12, "polygon": [[54.0, 369.75], [173.25, 369.75], [173.25, 380.14453125], [54.0, 380.14453125]]}, {"title": "B.2. Proposition 3.3", "heading_level": null, "page_id": 13, "polygon": [[54.0, 426.75], [140.25, 426.75], [140.25, 437.765625], [54.0, 437.765625]]}, {"title": "C. Justification of Low-Rank Technique", "heading_level": null, "page_id": 14, "polygon": [[54.0, 281.724609375], [258.78515625, 281.724609375], [258.78515625, 293.712890625], [54.0, 293.712890625]]}, {"title": "C.1. Low-Rank Nature of the Ideal Similarity Matrix", "heading_level": null, "page_id": 14, "polygon": [[54.0, 302.80078125], [281.25, 302.80078125], [281.25, 313.2421875], [54.0, 313.2421875]]}, {"title": "C.2. Eigenvalue Analysis of Similarity Mining", "heading_level": null, "page_id": 14, "polygon": [[54.0, 394.453125], [250.5, 394.453125], [250.5, 405.28125], [54.0, 405.28125]]}, {"title": "D. Extended Ablation Study", "heading_level": null, "page_id": 14, "polygon": [[54.0, 476.4375], [199.318359375, 476.4375], [199.318359375, 488.0390625], [54.0, 488.0390625]]}, {"title": "D.1. Data Filtering", "heading_level": null, "page_id": 14, "polygon": [[54.0, 497.25], [135.966796875, 497.25], [135.966796875, 508.1484375], [54.0, 508.1484375]]}, {"title": "D.2. Network Architectures", "heading_level": null, "page_id": 15, "polygon": [[54.0, 453.234375], [173.25, 453.234375], [173.25, 464.0625], [54.0, 464.0625]]}, {"title": "D.3. Efficiency Analysis", "heading_level": null, "page_id": 15, "polygon": [[54.0, 601.5], [156.884765625, 601.5], [156.884765625, 611.7890625], [54.0, 611.7890625]]}, {"title": "Low-Rank Similarity Mining", "heading_level": null, "page_id": 16, "polygon": [[239.8095703125, 46.1162109375], [354.75, 46.1162109375], [354.75, 55.6875], [239.8095703125, 55.6875]]}, {"title": "<PERSON><PERSON> More Visualizations of Synthetic Dataset", "heading_level": null, "page_id": 16, "polygon": [[54.0, 626.25], [278.25, 626.25], [278.25, 638.47265625], [54.0, 638.47265625]]}, {"title": "F. Hyper-parameters", "heading_level": null, "page_id": 16, "polygon": [[54.0, 675.0], [163.5, 675.0], [163.5, 686.0390625], [54.0, 686.0390625]]}, {"title": "G. <PERSON>", "heading_level": null, "page_id": 17, "polygon": [[54.0, 66.6123046875], [126.75, 66.6123046875], [126.75, 79.470703125], [54.0, 79.470703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 94], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8957, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 105], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 781], ["Line", 132], ["TableCell", 32], ["Text", 4], ["TextInlineMath", 4], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Table", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4566, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 672], ["Line", 132], ["Text", 8], ["TextInlineMath", 7], ["Equation", 5], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1824, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 655], ["Line", 139], ["Text", 8], ["TextInlineMath", 5], ["Reference", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 846], ["Line", 114], ["ListItem", 13], ["Text", 7], ["TableCell", 5], ["Reference", 4], ["TextInlineMath", 3], ["SectionHeader", 3], ["ListGroup", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1708, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 577], ["TableCell", 196], ["Line", 92], ["Text", 5], ["Caption", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["TableCell", 258], ["Line", 82], ["Text", 6], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 9095, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 520], ["Span", 448], ["Line", 74], ["Text", 5], ["Caption", 3], ["SectionHeader", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 13692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 227], ["Line", 93], ["ListItem", 20], ["Reference", 20], ["SectionHeader", 3], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 95], ["ListItem", 26], ["Reference", 26], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 82], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 784], ["Line", 185], ["TableCell", 138], ["SectionHeader", 3], ["Reference", 3], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5595, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1048], ["Line", 281], ["Equation", 7], ["Text", 4], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6704, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 47], ["TableCell", 27], ["SectionHeader", 5], ["Text", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3493, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["TableCell", 277], ["Line", 58], ["Text", 6], ["Reference", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11157, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["TableCell", 181], ["Line", 94], ["Text", 12], ["Picture", 11], ["Caption", 6], ["PictureGroup", 5], ["Reference", 4], ["SectionHeader", 3], ["Table", 2], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 13, "llm_error_count": 1, "llm_tokens_used": 15064, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 34], ["Line", 10], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Low-Rank_Similarity_Mining_for_Multimodal_Dataset_Distillation"}