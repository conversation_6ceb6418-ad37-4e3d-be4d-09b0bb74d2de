{"table_of_contents": [{"title": "M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy", "heading_level": null, "page_id": 0, "polygon": [[78.4423828125, 98.25], [531.9140625, 98.25], [531.9140625, 110.6015625], [78.4423828125, 110.6015625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[153.0, 217.5], [193.640625, 217.5], [193.640625, 227.197265625], [153.0, 227.197265625]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[139.5, 542.25], [206.6396484375, 542.25], [206.6396484375, 552.62109375], [139.5, 552.62109375]]}, {"title": "Background", "heading_level": null, "page_id": 1, "polygon": [[140.25, 538.5], [205.5, 538.5], [205.5, 549.0], [140.25, 549.0]]}, {"title": "Methodology", "heading_level": null, "page_id": 2, "polygon": [[138.0, 351.0], [207.0, 351.0], [207.0, 361.775390625], [138.0, 361.775390625]]}, {"title": "Importance of the Higher-Order Alignment", "heading_level": null, "page_id": 2, "polygon": [[52.5, 456.75], [258.75, 456.75], [258.75, 467.9296875], [52.5, 467.9296875]]}, {"title": "Minimizing Maximum Mean Discrepancy", "heading_level": null, "page_id": 2, "polygon": [[317.953125, 55.5], [514.5, 55.5], [514.5, 65.7421875], [317.953125, 65.7421875]]}, {"title": "Training Algorithm of M3D", "heading_level": null, "page_id": 3, "polygon": [[52.5, 657.75], [184.5, 657.75], [184.5, 667.4765625], [52.5, 667.4765625]]}, {"title": "Experiments", "heading_level": null, "page_id": 3, "polygon": [[404.015625, 647.25], [471.75, 647.25], [471.75, 657.80859375], [404.015625, 657.80859375]]}, {"title": "Experimental Setups", "heading_level": null, "page_id": 4, "polygon": [[52.5, 504.75], [152.25, 504.75], [152.25, 515.8828125], [52.5, 515.8828125]]}, {"title": "Comparison to the SOTA Methods", "heading_level": null, "page_id": 5, "polygon": [[52.5, 666.0], [216.75, 666.0], [216.75, 677.53125], [52.5, 677.53125]]}, {"title": "Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[318.0, 250.400390625], [391.5, 250.400390625], [391.5, 260.841796875], [318.0, 260.841796875]]}, {"title": "Conclusion", "heading_level": null, "page_id": 6, "polygon": [[409.5, 525.75], [468.0, 525.75], [468.0, 535.60546875], [409.5, 535.60546875]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 7, "polygon": [[125.25, 54.0], [221.431640625, 54.0], [221.431640625, 65.88720703125], [125.25, 65.88720703125]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[144.0, 156.0], [203.0537109375, 156.0], [203.0537109375, 167.642578125], [144.0, 167.642578125]]}, {"title": "Pseudo-Code of M3D", "heading_level": null, "page_id": 9, "polygon": [[249.22265625, 53.12548828125], [361.283203125, 53.12548828125], [361.283203125, 65.69384765625], [249.22265625, 65.69384765625]]}, {"title": "Related Works", "heading_level": null, "page_id": 9, "polygon": [[266.25, 307.634765625], [345.0, 307.634765625], [345.0, 320.396484375], [266.25, 320.396484375]]}, {"title": "Optimization-Oriented Methods", "heading_level": null, "page_id": 9, "polygon": [[52.5, 375.1171875], [205.59375, 375.1171875], [205.59375, 387.10546875], [52.5, 387.10546875]]}, {"title": "Distribution-Matching-based Methods", "heading_level": null, "page_id": 10, "polygon": [[52.5, 198.38671875], [234.0, 198.38671875], [234.0, 209.6015625], [52.5, 209.6015625]]}, {"title": "Coreset Selection", "heading_level": null, "page_id": 10, "polygon": [[52.5, 425.25], [135.5185546875, 425.25], [135.5185546875, 436.21875], [52.5, 436.21875]]}, {"title": "Derivation of MMD", "heading_level": null, "page_id": 10, "polygon": [[254.25, 532.5], [357.0, 532.5], [357.0, 543.33984375], [254.25, 543.33984375]]}, {"title": "Dataset Description", "heading_level": null, "page_id": 10, "polygon": [[254.25, 675.75], [357.0, 675.75], [357.0, 687.5859375], [254.25, 687.5859375]]}, {"title": "Low-Resolution Datasets", "heading_level": null, "page_id": 11, "polygon": [[52.5, 54.140625], [171.0, 54.140625], [171.0, 66.2255859375], [52.5, 66.2255859375]]}, {"title": "ImageNet-Subsets", "heading_level": null, "page_id": 11, "polygon": [[52.5, 214.435546875], [139.47802734375, 214.435546875], [139.47802734375, 226.423828125], [52.5, 226.423828125]]}, {"title": "Baseline Description", "heading_level": null, "page_id": 11, "polygon": [[252.0, 294.6796875], [359.25, 294.6796875], [359.25, 307.44140625], [252.0, 307.44140625]]}, {"title": "Coreset-Selection", "heading_level": null, "page_id": 11, "polygon": [[53.25, 310.53515625], [135.75, 310.53515625], [135.75, 322.5234375], [53.25, 322.5234375]]}, {"title": "Dataset-Condensation", "heading_level": null, "page_id": 11, "polygon": [[52.5, 383.431640625], [158.37890625, 383.431640625], [158.37890625, 395.033203125], [52.5, 395.033203125]]}, {"title": "Optimization-oriented.", "heading_level": null, "page_id": 11, "polygon": [[52.5, 401.02734375], [152.25, 401.02734375], [152.25, 412.62890625], [52.5, 412.62890625]]}, {"title": "Distribution-Matching-based.", "heading_level": null, "page_id": 11, "polygon": [[52.5, 483.78515625], [181.5, 483.78515625], [181.5, 495.38671875], [52.5, 495.38671875]]}, {"title": "More Ablation Studies", "heading_level": null, "page_id": 11, "polygon": [[247.5, 582.78515625], [365.765625, 582.78515625], [365.765625, 595.16015625], [247.5, 595.16015625]]}, {"title": "More Visualization Results", "heading_level": null, "page_id": 11, "polygon": [[235.5, 655.875], [377.12109375, 655.875], [377.12109375, 668.25], [235.5, 668.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 86], ["Text", 8], ["SectionHeader", 3], ["Footnote", 3], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 513], ["Line", 131], ["Text", 8], ["TextInlineMath", 4], ["Equation", 4], ["ListItem", 3], ["SectionHeader", 1], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 783], ["Line", 102], ["TableCell", 50], ["Text", 8], ["Equation", 6], ["TextInlineMath", 6], ["SectionHeader", 3], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1412, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 716], ["Line", 118], ["TextInlineMath", 5], ["Text", 4], ["Equation", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1902, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1140], ["Line", 109], ["TableCell", 78], ["Text", 4], ["Caption", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 933, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["TableCell", 146], ["Line", 72], ["Text", 9], ["Caption", 2], ["Picture", 1], ["Table", 1], ["SectionHeader", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3046, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 106], ["TableCell", 67], ["Text", 4], ["Caption", 3], ["SectionHeader", 2], ["Picture", 1], ["Table", 1], ["Figure", 1], ["PictureGroup", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 110], ["Text", 35], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 109], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 358], ["Line", 53], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 446], ["Line", 59], ["Text", 6], ["SectionHeader", 4], ["Equation", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2027, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 47], ["ListItem", 13], ["SectionHeader", 9], ["ListGroup", 4], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["TableCell", 28], ["Line", 10], ["Caption", 2], ["Table", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2079, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 11], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1261, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 7], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1275, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/M3D__Dataset_Condensation_by_Minimizing_Maximum_Mean_Discrepancy"}