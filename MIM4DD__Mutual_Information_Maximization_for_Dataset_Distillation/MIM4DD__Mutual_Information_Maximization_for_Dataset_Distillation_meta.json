{"table_of_contents": [{"title": "MIM4DD: Mutual Information Maximization for\nDataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[122.25, 99.0], [489.0, 99.0], [489.0, 136.2216796875], [122.25, 136.2216796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 251.25], [329.25, 251.25], [329.25, 262.001953125], [282.75, 262.001953125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.05517578125, 504.0], [191.25, 504.0], [191.25, 515.49609375], [107.05517578125, 515.49609375]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 1, "polygon": [[107.25, 554.25], [193.5, 554.25], [193.5, 566.54296875], [107.25, 566.54296875]]}, {"title": "2.1 Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 679.8515625], [189.0, 679.8515625], [189.0, 690.6796875], [106.98046875, 690.6796875]]}, {"title": "2.2 MIM4DD: Mutual Information Maximization for Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[107.25, 576.0], [426.0, 576.0], [426.0, 585.4921875], [107.25, 585.4921875]]}, {"title": "2.3 Discussion on MIM4DD", "heading_level": null, "page_id": 5, "polygon": [[107.25, 626.25], [233.384765625, 626.25], [233.384765625, 636.15234375], [107.25, 636.15234375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 6, "polygon": [[107.25, 231.75], [192.0, 231.75], [192.0, 242.859375], [107.25, 242.859375]]}, {"title": "3.1 Datasets and Implementation Details", "heading_level": null, "page_id": 6, "polygon": [[106.5, 329.25], [288.0, 329.25], [288.0, 339.345703125], [106.5, 339.345703125]]}, {"title": "3.2 <PERSON>mp<PERSON>on with SoTA", "heading_level": null, "page_id": 6, "polygon": [[106.5, 559.5], [231.0, 559.5], [231.0, 568.86328125], [106.5, 568.86328125]]}, {"title": "3.3 Ablation Studies", "heading_level": null, "page_id": 6, "polygon": [[106.5, 706.5], [201.75, 706.5], [201.75, 716.58984375], [106.5, 716.58984375]]}, {"title": "3.4 Regularization Propriety", "heading_level": null, "page_id": 7, "polygon": [[106.8310546875, 612.75], [237.0, 612.75], [237.0, 623.00390625], [106.8310546875, 623.00390625]]}, {"title": "3.5 CKA analysis", "heading_level": null, "page_id": 8, "polygon": [[106.5, 108.75], [189.3076171875, 108.0], [189.75, 119.109375], [106.5, 119.25]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.05517578125, 378.0], [197.25, 378.0], [197.25, 389.0390625], [107.05517578125, 389.0390625]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[107.25, 554.25], [183.75, 554.25], [183.75, 566.15625], [107.25, 566.15625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[106.5, 650.25], [207.0, 650.25], [207.0, 660.90234375], [106.5, 660.90234375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 72.75], [163.7578125, 72.75], [163.7578125, 83.53125], [107.25, 83.53125]]}, {"title": "A Appendix", "heading_level": null, "page_id": 11, "polygon": [[106.5, 72.75], [179.25, 72.75], [179.25, 83.57958984375], [106.5, 83.57958984375]]}, {"title": "A.1 In-variance of Mutual Information", "heading_level": null, "page_id": 11, "polygon": [[106.3828125, 97.5], [282.0, 97.5], [282.0, 107.9912109375], [106.3828125, 107.9912109375]]}, {"title": "Discussion on Theorem 1.", "heading_level": null, "page_id": 11, "polygon": [[107.25, 356.25], [216.75, 356.25], [216.75, 367.576171875], [107.25, 367.576171875]]}, {"title": "A.2 Datasets and Implementation Details", "heading_level": null, "page_id": 11, "polygon": [[107.25, 466.5], [290.25, 466.5], [290.25, 476.82421875], [107.25, 476.82421875]]}, {"title": "A.2.1 Datasets", "heading_level": null, "page_id": 11, "polygon": [[107.25, 488.25], [176.30859375, 488.25], [176.30859375, 498.09375], [107.25, 498.09375]]}, {"title": "A.2.2 Implementation Details.", "heading_level": null, "page_id": 11, "polygon": [[107.25, 594.0], [243.0, 594.0], [243.0, 604.0546875], [107.25, 604.0546875]]}, {"title": "A.3 Synthetic Samples Visualization.", "heading_level": null, "page_id": 12, "polygon": [[106.5, 470.25], [271.93359375, 470.25], [271.93359375, 481.8515625], [106.5, 481.8515625]]}, {"title": "B Related Work", "heading_level": null, "page_id": 12, "polygon": [[106.5, 494.2265625], [200.25, 494.2265625], [200.25, 505.828125], [106.5, 505.828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 67], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5610, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 54], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 65], ["TextInlineMath", 6], ["Equation", 3], ["Reference", 3], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 835], ["Line", 80], ["TextInlineMath", 10], ["Equation", 6], ["Reference", 4], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1063], ["Line", 98], ["Equation", 4], ["Reference", 4], ["TextInlineMath", 3], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 723, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 847], ["Line", 123], ["TextInlineMath", 9], ["Reference", 5], ["Equation", 4], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1008, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 49], ["SectionHeader", 4], ["TextInlineMath", 3], ["Text", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 600], ["TableCell", 220], ["Line", 67], ["Text", 4], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 6384, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 332], ["Line", 57], ["Text", 6], ["SectionHeader", 4], ["TextInlineMath", 3], ["Figure", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 702, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 47], ["ListItem", 24], ["Reference", 24], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 45], ["ListItem", 23], ["Reference", 23], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["Line", 76], ["SectionHeader", 6], ["TextInlineMath", 4], ["Text", 4], ["Equation", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3496, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 24], ["SectionHeader", 2], ["Picture", 1], ["Caption", 1], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 645, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 27], ["Text", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/MIM4DD__Mutual_Information_Maximization_for_Dataset_Distillation"}