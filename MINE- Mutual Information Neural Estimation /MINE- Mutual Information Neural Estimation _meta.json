{"table_of_contents": [{"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 186.0], [196.330078125, 186.0], [196.330078125, 195.7763671875], [148.5, 195.7763671875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 403.5], [132.75, 403.5], [132.75, 414.94921875], [54.0, 414.94921875]]}, {"title": "2. Background", "heading_level": null, "page_id": 1, "polygon": [[54.0, 322.5], [130.5, 322.5], [130.5, 333.544921875], [54.0, 333.544921875]]}, {"title": "2.1. Mutual Information", "heading_level": null, "page_id": 1, "polygon": [[54.0, 342.75], [159.0, 342.75], [159.0, 353.07421875], [54.0, 353.07421875]]}, {"title": "2.2. Dual representations of the KL-divergence.", "heading_level": null, "page_id": 1, "polygon": [[305.8505859375, 69.0], [508.5, 69.0], [508.5, 79.27734375], [305.8505859375, 79.27734375]]}, {"title": "3. The Mutual Information Neural Estimator", "heading_level": null, "page_id": 2, "polygon": [[54.0, 67.5], [285.75, 67.5], [285.75, 79.13232421875], [54.0, 79.13232421875]]}, {"title": "3.1. Method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 149.25], [107.05517578125, 149.25], [107.05517578125, 159.908203125], [54.0, 159.908203125]]}, {"title": "3.2. Correcting the bias from the stochastic gradients", "heading_level": null, "page_id": 2, "polygon": [[306.0, 271.5], [532.5, 271.5], [532.5, 281.337890625], [306.0, 281.337890625]]}, {"title": "3.3. Theoretical properties", "heading_level": null, "page_id": 2, "polygon": [[306.0, 479.25], [420.75, 479.25], [420.75, 489.97265625], [306.0, 489.97265625]]}, {"title": "3.3.1. CONSISTENCY", "heading_level": null, "page_id": 2, "polygon": [[306.0, 545.25], [397.44140625, 545.25], [397.44140625, 555.71484375], [306.0, 555.71484375]]}, {"title": "Theorem 2. MINE is strongly consistent.", "heading_level": null, "page_id": 3, "polygon": [[54.0, 443.1796875], [222.0, 443.1796875], [222.0, 452.4609375], [54.0, 452.4609375]]}, {"title": "3.3.2. SAMPLE COMPLEXITY", "heading_level": null, "page_id": 3, "polygon": [[54.0, 466.5], [177.75, 466.5], [177.75, 476.05078125], [54.0, 476.05078125]]}, {"title": "4. Empirical comparisons", "heading_level": null, "page_id": 3, "polygon": [[305.8505859375, 138.75], [438.75, 138.75], [438.75, 150.046875], [305.8505859375, 150.046875]]}, {"title": "4.1. Comparing MINE to non-parametric estimation", "heading_level": null, "page_id": 3, "polygon": [[306.0, 219.75], [530.25, 219.75], [530.25, 229.517578125], [306.0, 229.517578125]]}, {"title": "4.2. Capturing non-linear dependencies", "heading_level": null, "page_id": 3, "polygon": [[306.75, 616.5], [475.5, 616.5], [475.5, 626.484375], [306.75, 626.484375]]}, {"title": "5. Applications", "heading_level": null, "page_id": 4, "polygon": [[54.0, 242.25], [132.0, 242.25], [132.0, 253.880859375], [54.0, 253.880859375]]}, {"title": "5.1. Maximizing mutual information to improve GANs", "heading_level": null, "page_id": 4, "polygon": [[54.0, 371.25], [288.0, 371.25], [288.0, 381.498046875], [54.0, 381.498046875]]}, {"title": "5.2. Maximizing mutual information to improve\ninference in bi-directional adversarial models", "heading_level": null, "page_id": 5, "polygon": [[54.0, 593.25], [266.25, 593.25], [266.25, 614.109375], [54.0, 614.109375]]}, {"title": "5.3. <PERSON>", "heading_level": null, "page_id": 6, "polygon": [[54.0, 675.75], [173.25, 675.75], [173.25, 686.42578125], [54.0, 686.42578125]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[54.0, 663.0], [125.25, 663.0], [125.25, 674.4375], [54.0, 674.4375]]}, {"title": "7. Acknowledgements", "heading_level": null, "page_id": 7, "polygon": [[306.0, 651.0], [418.5, 651.0], [418.5, 662.0625], [306.0, 662.0625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.3740234375], [54.0, 79.3740234375]]}, {"title": "8. <PERSON><PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 10, "polygon": [[54.0, 67.5], [117.73828125, 67.5], [117.73828125, 79.61572265625], [54.0, 79.61572265625]]}, {"title": "8.1. Experimental Details", "heading_level": null, "page_id": 10, "polygon": [[54.0, 111.0], [163.5, 111.0], [163.5, 120.9462890625], [54.0, 120.9462890625]]}, {"title": "8.1.1. ADAPTIVE CLIPPING", "heading_level": null, "page_id": 10, "polygon": [[54.0, 130.5], [172.72265625, 130.5], [172.72265625, 139.7021484375], [54.0, 139.7021484375]]}, {"title": "8.1.2. GAN+MINE: SPIRAL AND 25-GAUSSIANS", "heading_level": null, "page_id": 10, "polygon": [[54.0, 363.75], [264.75, 363.75], [264.75, 373.95703125], [54.0, 373.95703125]]}, {"title": "8.1.3. GAN+MINE: STACKED-MNIST", "heading_level": null, "page_id": 10, "polygon": [[54.0, 454.5], [223.5, 454.5], [223.5, 463.2890625], [54.0, 463.2890625]]}, {"title": "Mutual Information Neural Estimation", "heading_level": null, "page_id": 11, "polygon": [[221.25, 45.75], [374.25, 45.75], [374.25, 55.7841796875], [221.25, 55.7841796875]]}, {"title": "8.1.4. ALI+MINE: MNIST AND CELEBA", "heading_level": null, "page_id": 11, "polygon": [[54.0, 366.029296875], [235.5, 366.029296875], [235.5, 377.244140625], [54.0, 377.244140625]]}, {"title": "8.1.5. INFORMATION BOTTLENECK WITH MINE", "heading_level": null, "page_id": 13, "polygon": [[54.0, 404.12109375], [260.25, 404.12109375], [260.25, 414.17578125], [54.0, 414.17578125]]}, {"title": "8.2. <PERSON><PERSON>s", "heading_level": null, "page_id": 13, "polygon": [[54.0, 620.25], [101.25, 620.25], [101.25, 630.73828125], [54.0, 630.73828125]]}, {"title": "8.2.1. DONSKER-<PERSON><PERSON>D<PERSON><PERSON> REPRESENTATION", "heading_level": null, "page_id": 13, "polygon": [[54.0, 639.0], [259.5, 639.0], [259.5, 649.6875], [54.0, 649.6875]]}, {"title": "8.2.2. CONSISTENCY PROOFS", "heading_level": null, "page_id": 14, "polygon": [[54.0, 276.0], [181.5, 277.5], [181.5, 286.5], [54.0, 286.171875]]}, {"title": "Theorem 5 (<PERSON><PERSON> 2 restated). MINE is strongly consistent.", "heading_level": null, "page_id": 15, "polygon": [[54.0, 578.25], [283.5, 578.25], [283.5, 587.25], [54.0, 587.25]]}, {"title": "8.2.3. SAMPLE COMPLEXITY PROOF", "heading_level": null, "page_id": 16, "polygon": [[54.0, 69.0], [208.5, 69.0], [208.5, 78.6005859375], [54.0, 78.6005859375]]}, {"title": "8.2.4. BOUND ON THE RECONSTRUCTION ERROR", "heading_level": null, "page_id": 16, "polygon": [[54.0, 636.75], [262.5, 636.75], [262.5, 645.43359375], [54.0, 645.43359375]]}, {"title": "8.3. Embeddings for bi-direction 25 Gaussians experiments", "heading_level": null, "page_id": 17, "polygon": [[54.0, 423.0], [306.75, 423.0], [306.75, 433.8984375], [54.0, 433.8984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 91], ["Text", 6], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 2], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 764], ["Line", 112], ["Text", 14], ["Equation", 7], ["ListItem", 4], ["SectionHeader", 3], ["Footnote", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 798], ["Line", 135], ["TableCell", 32], ["Text", 8], ["TextInlineMath", 8], ["SectionHeader", 5], ["Equation", 5], ["Footnote", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5016, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 667], ["Line", 101], ["Text", 7], ["TextInlineMath", 7], ["SectionHeader", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["Line", 111], ["TextInlineMath", 6], ["Text", 5], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 607, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 99], ["TableCell", 23], ["Text", 5], ["Caption", 3], ["TextInlineMath", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Picture", 1], ["SectionHeader", 1], ["Table", 1], ["Footnote", 1], ["FigureGroup", 1], ["PictureGroup", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4321, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 91], ["TableCell", 84], ["Text", 7], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Table", 1], ["TextInlineMath", 1], ["Equation", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 86], ["TableCell", 40], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3349, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 324], ["Line", 104], ["ListItem", 34], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 81], ["ListItem", 27], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["TableCell", 72], ["Line", 42], ["Text", 6], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1416, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 413], ["TableCell", 299], ["Line", 49], ["Table", 4], ["Caption", 3], ["Text", 3], ["TableGroup", 3], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 18660, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["TableCell", 290], ["Line", 43], ["Table", 4], ["Caption", 3], ["TableGroup", 3], ["Text", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 4421, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["TableCell", 237], ["Line", 52], ["Text", 6], ["Table", 3], ["SectionHeader", 3], ["Caption", 2], ["Equation", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10802, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 918], ["Line", 96], ["TextInlineMath", 12], ["Equation", 11], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 1179], ["Line", 145], ["Equation", 11], ["Text", 9], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1329, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 862], ["Line", 114], ["Equation", 11], ["Text", 8], ["TextInlineMath", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1253, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 405], ["Line", 22], ["Equation", 5], ["Text", 4], ["TextInlineMath", 3], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/MINE- Mutual Information Neural Estimation "}