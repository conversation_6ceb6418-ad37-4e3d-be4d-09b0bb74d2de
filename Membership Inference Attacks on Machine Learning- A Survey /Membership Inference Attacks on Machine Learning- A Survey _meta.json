{"table_of_contents": [{"title": "Membership Inference Attacks on Machine Learning: A\nSurvey", "heading_level": null, "page_id": 0, "polygon": [[44.25, 81.9140625], [406.5, 81.9140625], [406.5, 114.78515625], [44.25, 114.78515625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[45.0, 454.5], [139.5, 454.5], [139.5, 465.46875], [45.0, 465.46875]]}, {"title": "2 PRELIMINARIES ABOUT MACHINE LEARNING", "heading_level": null, "page_id": 3, "polygon": [[44.25, 86.25], [277.5, 86.25], [277.5, 96.240234375], [44.25, 96.240234375]]}, {"title": "3 MEMBERSHIP INFERENCE ATTACKS ON MACHINE LEARNING MODELS", "heading_level": null, "page_id": 4, "polygon": [[44.73193359375, 429.0], [397.72265625, 429.0], [397.72265625, 439.453125], [44.73193359375, 439.453125]]}, {"title": "3.1 Definition of Membership Inference Attacks", "heading_level": null, "page_id": 4, "polygon": [[44.25, 477.75], [269.25, 477.75], [269.25, 487.6171875], [44.25, 487.6171875]]}, {"title": "3.2 Adversarial Knowledge", "heading_level": null, "page_id": 4, "polygon": [[44.25, 574.5], [174.75, 574.5], [174.75, 583.9453125], [44.25, 583.9453125]]}, {"title": "3.3 Membership Inference Attack Approaches", "heading_level": null, "page_id": 6, "polygon": [[44.25, 278.25], [260.25, 278.25], [260.25, 289.16015625], [44.25, 289.16015625]]}, {"title": "3.4 Membership Inference Attacks on Different ML models", "heading_level": null, "page_id": 10, "polygon": [[44.25, 478.5], [318.75, 478.5], [318.75, 487.96875], [44.25, 487.96875]]}, {"title": "3.5 Membership Inference Attacks against Federated Learning", "heading_level": null, "page_id": 14, "polygon": [[44.25, 321.75], [335.25, 321.75], [335.25, 332.578125], [44.25, 332.578125]]}, {"title": "3.6 Taxonomies of Membership Inference Attacks", "heading_level": null, "page_id": 15, "polygon": [[44.25, 513.75], [276.75, 513.75], [276.75, 524.1796875], [44.25, 524.1796875]]}, {"title": "Table 2. Summary of membership inference attacks work on machine learning models (time ascending).", "heading_level": null, "page_id": 17, "polygon": [[48.0, 82.5], [436.5, 82.5], [436.5, 95.25], [48.0, 95.25]]}, {"title": "4 WHY MEMBERSHIP INFERENCE ATTACKS WORK", "heading_level": null, "page_id": 18, "polygon": [[44.25, 298.5], [294.75, 298.5], [294.75, 309.375], [44.25, 309.375]]}, {"title": "5 MEMBERSHIP INFERENCE DEFENSE ON MACHINE LEARNING MODELS", "heading_level": null, "page_id": 19, "polygon": [[44.25, 476.25], [396.7734375, 476.25], [396.7734375, 487.96875], [44.25, 487.96875]]}, {"title": "5.1 Confidence Score Masking", "heading_level": null, "page_id": 19, "polygon": [[44.25, 537.75], [189.75, 537.75], [189.75, 548.7890625], [44.25, 548.7890625]]}, {"title": "5.2 Regularization", "heading_level": null, "page_id": 20, "polygon": [[44.25, 429.75], [135.75, 429.75], [135.75, 440.5078125], [44.25, 440.5078125]]}, {"title": "5.3 Knowledge Distillation", "heading_level": null, "page_id": 21, "polygon": [[44.25, 322.5], [173.25, 322.5], [173.25, 332.9296875], [44.25, 332.9296875]]}, {"title": "5.4 Differential Privacy", "heading_level": null, "page_id": 21, "polygon": [[44.25, 549.75], [158.25, 549.75], [158.25, 560.390625], [44.25, 560.390625]]}, {"title": "5.5 Taxonomies of Membership Inference Defenses", "heading_level": null, "page_id": 22, "polygon": [[44.25, 609.75], [284.25, 609.75], [284.25, 620.5078125], [44.25, 620.5078125]]}, {"title": "6 METRICS, DATASETS, AND OPEN-SOURCE IMPLEMENTATIONS", "heading_level": null, "page_id": 23, "polygon": [[44.25, 594.84375], [357.1435546875, 594.84375], [357.1435546875, 606.796875], [44.25, 606.796875]]}, {"title": "6.1 Metrics", "heading_level": null, "page_id": 26, "polygon": [[44.25, 86.25], [103.7021484375, 86.25], [103.7021484375, 96.*********], [44.25, 96.*********]]}, {"title": "6.2 Evaluation Datasets and Open-Source Implementations", "heading_level": null, "page_id": 27, "polygon": [[44.25, 155.25], [319.5, 155.25], [319.5, 165.5859375], [44.25, 165.5859375]]}, {"title": "7 FUTURE DIRECTIONS", "heading_level": null, "page_id": 27, "polygon": [[44.25, 298.5], [164.25, 298.5], [164.25, 309.0234375], [44.25, 309.0234375]]}, {"title": "7.1 Membership Inference Attacks", "heading_level": null, "page_id": 27, "polygon": [[44.25, 359.25], [208.5, 359.25], [208.5, 369.140625], [44.25, 369.140625]]}, {"title": "7.2 Membership Inference Defenses", "heading_level": null, "page_id": 29, "polygon": [[45.0, 131.25], [215.25, 131.25], [215.25, 141.85546875], [45.0, 141.85546875]]}, {"title": "8 CONCLUSION", "heading_level": null, "page_id": 30, "polygon": [[44.25, 181.5], [128.26318359375, 181.5], [128.26318359375, 191.953125], [44.25, 191.953125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 30, "polygon": [[44.8505859375, 314.25], [108.75, 314.25], [108.75, 325.72265625], [44.8505859375, 325.72265625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 47], ["Text", 5], ["SectionHeader", 2], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 49], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 46], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Footnote", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 70], ["Text", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 70], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 42], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1467, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 48], ["TableCell", 19], ["Text", 5], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 76], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 462], ["Line", 83], ["Text", 5], ["Equation", 5], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1298, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 396], ["Line", 68], ["Equation", 7], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 48], ["Text", 5], ["Equation", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 49], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 45], ["Text", 6], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 61], ["Text", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 48], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 563], ["Line", 92], ["Text", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2464, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 813], ["Span", 409], ["Line", 127], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 48], ["Text", 5], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 48], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 48], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 47], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 48], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1196, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 602], ["Span", 394], ["Line", 152], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 705], ["Span", 667], ["Line", 83], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15526, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 518], ["Line", 49], ["Text", 10], ["ListItem", 6], ["Equation", 5], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 46], ["ListItem", 4], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 49], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 48], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 52], ["Reference", 15], ["ListItem", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 57], ["ListItem", 23], ["Reference", 23], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 57], ["ListItem", 24], ["Reference", 24], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 58], ["ListItem", 24], ["Reference", 24], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 58], ["ListItem", 28], ["Reference", 28], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 57], ["ListItem", 26], ["Reference", 26], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 57], ["ListItem", 24], ["Reference", 24], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 58], ["ListItem", 27], ["Reference", 27], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 58], ["ListItem", 25], ["Reference", 25], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 58], ["ListItem", 27], ["Reference", 27], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 45], ["Line", 11], ["ListItem", 4], ["Reference", 4], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Membership Inference Attacks on Machine Learning- A Survey "}