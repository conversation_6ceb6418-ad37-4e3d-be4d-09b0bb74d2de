{"table_of_contents": [{"title": "META KNOWLEDGE CONDENSATION FOR\nFEDERATED LEARNING", "heading_level": null, "page_id": 0, "polygon": [[105.0380859375, 81.0], [411.0, 81.0], [411.0, 116.208984375], [105.0380859375, 116.208984375]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[331.1015625, 136.318359375], [363.75, 136.318359375], [363.75, 146.373046875], [331.1015625, 146.373046875]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 293.25], [334.5, 293.25], [334.5, 303.767578125], [276.75, 303.767578125]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 570.75], [206.25, 570.75], [206.25, 582.3984375], [107.25, 582.3984375]]}, {"title": "2 METHODOLOGY", "heading_level": null, "page_id": 2, "polygon": [[107.25, 171.0], [210.375, 171.0], [210.375, 181.8544921875], [107.25, 181.8544921875]]}, {"title": "2.1 PROBLEM DEFINITION", "heading_level": null, "page_id": 2, "polygon": [[106.5, 195.486328125], [228.3046875, 195.486328125], [228.3046875, 204.767578125], [106.5, 204.767578125]]}, {"title": "2.2 FEDERATED LEARNING VIA META KNOWLEDGE", "heading_level": null, "page_id": 2, "polygon": [[106.681640625, 444.7265625], [336.779296875, 444.7265625], [336.779296875, 454.0078125], [106.681640625, 454.0078125]]}, {"title": "2.2.1 FEDERATED META KNOWLEDGE EXTRACTION ON CLIENTS", "heading_level": null, "page_id": 2, "polygon": [[107.25, 543.75], [393.2578125, 543.75], [393.2578125, 554.16796875], [107.25, 554.16796875]]}, {"title": "2.2.2 SERVER-SIDE CENTRAL MODEL TRAINING", "heading_level": null, "page_id": 4, "polygon": [[107.20458984375, 147.75], [324.228515625, 147.75], [324.228515625, 157.2978515625], [107.20458984375, 157.2978515625]]}, {"title": "3 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[107.25, 265.095703125], [201.111328125, 265.095703125], [201.111328125, 276.310546875], [107.25, 276.310546875]]}, {"title": "3.1 DATASETS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 290.232421875], [177.6533203125, 290.232421875], [177.6533203125, 300.287109375], [106.5, 300.287109375]]}, {"title": "3.2 IMPLEMENTATION DETAILS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 467.9296875], [250.119140625, 467.9296875], [250.119140625, 477.984375], [106.5, 477.984375]]}, {"title": "3.3 COMPARATIVE STUDIES", "heading_level": null, "page_id": 5, "polygon": [[106.5, 645.43359375], [236.373046875, 645.43359375], [236.373046875, 655.48828125], [106.5, 655.48828125]]}, {"title": "4 RELATED WORK", "heading_level": null, "page_id": 8, "polygon": [[107.25, 399.48046875], [213.064453125, 399.48046875], [213.064453125, 411.08203125], [107.25, 411.08203125]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 123.0], [196.330078125, 123.0], [196.330078125, 134.19140625], [107.25, 134.19140625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 223.5], [176.25, 223.5], [176.25, 233.771484375], [107.1298828125, 233.771484375]]}, {"title": "A APPENDIX", "heading_level": null, "page_id": 11, "polygon": [[106.90576171875, 82.03271484375], [183.779296875, 82.03271484375], [183.779296875, 93.53759765625], [106.90576171875, 93.53759765625]]}, {"title": "A.1 ADDITIONAL EXPERIMENTAL RESULTS", "heading_level": null, "page_id": 11, "polygon": [[107.05517578125, 109.5], [301.81640625, 109.5], [301.81640625, 119.49609375], [107.05517578125, 119.49609375]]}, {"title": "A.1.1 DATASETS", "heading_level": null, "page_id": 11, "polygon": [[107.25, 131.9677734375], [187.5, 131.9677734375], [187.5, 141.2490234375], [107.25, 141.2490234375]]}, {"title": "A.1.2 COMPARATIVE STUDIES", "heading_level": null, "page_id": 11, "polygon": [[107.25, 188.25], [245.25, 188.25], [245.25, 198.580078125], [107.25, 198.580078125]]}, {"title": "A.2 DISCUSSIONS", "heading_level": null, "page_id": 11, "polygon": [[107.1298828125, 332.771484375], [193.04296875, 332.771484375], [193.04296875, 342.052734375], [107.1298828125, 342.052734375]]}, {"title": "Table 4: Results with 10 rounds.", "heading_level": null, "page_id": 12, "polygon": [[239.25, 81.0], [371.25, 81.0], [371.25, 91.458984375], [239.25, 91.458984375]]}, {"title": "A.3 ALGORITHM", "heading_level": null, "page_id": 12, "polygon": [[106.5, 587.25], [188.25, 587.25], [188.25, 597.48046875], [106.5, 597.48046875]]}, {"title": "Algorithm 1: FedMK", "heading_level": null, "page_id": 13, "polygon": [[105.11279296875, 409.5], [200.513671875, 409.5], [200.513671875, 421.13671875], [105.11279296875, 421.13671875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 55], ["Text", 7], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4624, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 65], ["Text", 4], ["ListItem", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 625, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["Line", 68], ["Text", 6], ["SectionHeader", 4], ["TextInlineMath", 3], ["Equation", 2], ["Reference", 2], ["ListItem", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 749], ["Line", 92], ["TextInlineMath", 8], ["Equation", 5], ["Text", 4], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 629], ["Line", 50], ["TextInlineMath", 7], ["Equation", 3], ["Text", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 452], ["TableCell", 209], ["Line", 55], ["SectionHeader", 4], ["Text", 2], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 10319, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 117], ["TableCell", 36], ["Text", 5], ["Reference", 4], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5093, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 77], ["TableCell", 28], ["Text", 6], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4248, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 49], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 46], ["Reference", 19], ["ListItem", 17], ["Text", 5], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 41], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 52], ["Text", 9], ["SectionHeader", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 277], ["Span", 215], ["Line", 49], ["Text", 5], ["Table", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 12658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 48], ["TableCell", 46], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Meta_Knowledge_Condensation_for_Federated_Learning"}