{"table_of_contents": [{"title": "Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[57.0, 106.5], [538.5, 106.5], [538.5, 119.302734375], [57.0, 119.302734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.0, 270.75], [191.25, 270.75], [191.25, 280.564453125], [144.0, 280.564453125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 651.75], [127.5, 651.75], [127.5, 662.8359375], [48.75, 662.8359375]]}, {"title": "2. Preliminaries and Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.5, 497.25], [486.75, 497.25], [486.75, 508.1484375], [307.5, 508.1484375]]}, {"title": "3. Methodology", "heading_level": null, "page_id": 2, "polygon": [[48.0, 623.25], [129.0, 623.25], [129.0, 634.60546875], [48.0, 634.60546875]]}, {"title": "3.1. Matching Training Trajectories (MTT)", "heading_level": null, "page_id": 2, "polygon": [[307.5, 444.0], [510.75, 444.0], [510.75, 454.78125], [307.5, 454.78125]]}, {"title": "3.2. Accumulated Trajectory Error", "heading_level": null, "page_id": 3, "polygon": [[48.75, 554.25], [214.5, 554.25], [214.5, 564.99609375], [48.75, 564.99609375]]}, {"title": "3.3. Flat Trajectory helps reduce the accumulated\ntrajectory error", "heading_level": null, "page_id": 3, "polygon": [[307.5, 504.0], [545.25, 504.0], [545.25, 525.9375], [307.5, 525.9375]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 4, "polygon": [[307.5, 356.16796875], [386.25, 356.16796875], [386.25, 366.609375], [307.5, 366.609375]]}, {"title": "4.1. Experimental Setup", "heading_level": null, "page_id": 4, "polygon": [[307.5, 504.75], [422.25, 504.75], [422.25, 515.109375], [307.5, 515.109375]]}, {"title": "4.2. Results", "heading_level": null, "page_id": 5, "polygon": [[307.494140625, 515.109375], [363.0, 515.109375], [363.0, 525.1640625], [307.494140625, 525.1640625]]}, {"title": "4.3. Ablation and Parameter Studies", "heading_level": null, "page_id": 6, "polygon": [[48.75, 589.5], [220.5, 589.5], [220.5, 600.1875], [48.75, 600.1875]]}, {"title": "4.4. Neural Architecture Search (NAS)", "heading_level": null, "page_id": 6, "polygon": [[307.494140625, 624.75], [489.75, 624.75], [489.75, 634.60546875], [307.494140625, 634.60546875]]}, {"title": "5. Conclusion and Future Work", "heading_level": null, "page_id": 7, "polygon": [[48.0, 670.5], [212.25, 670.5], [212.25, 682.171875], [48.0, 682.171875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 7, "polygon": [[307.5, 477.0], [408.0, 477.0], [408.0, 488.0390625], [307.5, 488.0390625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.0], [106.5, 72.0], [106.5, 83.96630859375], [48.75, 83.96630859375]]}, {"title": "Author Contributions", "heading_level": null, "page_id": 10, "polygon": [[48.0, 72.75], [162.0, 72.75], [162.0, 83.96630859375], [48.0, 83.96630859375]]}, {"title": "<PERSON><PERSON> Discussions and Experiments", "heading_level": null, "page_id": 10, "polygon": [[48.0, 315.0], [245.25, 315.0], [245.25, 325.810546875], [48.0, 325.810546875]]}, {"title": "A.1. Exploring the Accumulated Trajectory Error", "heading_level": null, "page_id": 10, "polygon": [[48.75, 334.5], [283.5, 334.5], [283.5, 344.953125], [48.75, 344.953125]]}, {"title": "A.2. Exploring the Flat Trajectory", "heading_level": null, "page_id": 10, "polygon": [[307.5, 433.5], [469.5, 433.5], [469.5, 444.7265625], [307.5, 444.7265625]]}, {"title": "A.3. Implementation Details", "heading_level": null, "page_id": 11, "polygon": [[48.75, 556.5], [182.25, 556.5], [182.25, 566.54296875], [48.75, 566.54296875]]}, {"title": "A.3.1 Parameter Study", "heading_level": null, "page_id": 11, "polygon": [[48.75, 576.0], [155.25, 576.0], [155.25, 585.4921875], [48.75, 585.4921875]]}, {"title": "A.3.2 Optimizing of the Flat Trajectory", "heading_level": null, "page_id": 11, "polygon": [[307.5, 233.25], [483.75, 233.25], [483.75, 243.24609375], [307.5, 243.24609375]]}, {"title": "A.3.3 Hyperparameter Details", "heading_level": null, "page_id": 12, "polygon": [[48.0, 280.5], [186.0, 280.5], [186.0, 290.619140625], [48.0, 290.619140625]]}, {"title": "A.3.4 Neural Architecture Search.", "heading_level": null, "page_id": 12, "polygon": [[48.0, 396.75], [203.25, 396.75], [203.25, 407.21484375], [48.0, 407.21484375]]}, {"title": "A.3.5 Visualizations", "heading_level": null, "page_id": 12, "polygon": [[48.0, 621.0], [142.5, 621.0], [142.5, 631.8984375], [48.0, 631.8984375]]}, {"title": "<PERSON><PERSON> More Related Work", "heading_level": null, "page_id": 12, "polygon": [[307.5, 72.0], [425.25, 72.0], [425.25, 83.337890625], [307.5, 83.337890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 94], ["Text", 10], ["SectionHeader", 3], ["Reference", 2], ["Footnote", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6674, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["Line", 104], ["Text", 5], ["TextInlineMath", 4], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 917], ["Line", 138], ["Equation", 7], ["Reference", 7], ["TextInlineMath", 6], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 846, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1439], ["Line", 202], ["TextInlineMath", 8], ["Equation", 7], ["Reference", 6], ["Text", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8623, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 973], ["Line", 182], ["TextInlineMath", 6], ["Text", 5], ["Reference", 5], ["Equation", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2256, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 665], ["TableCell", 141], ["Line", 71], ["TextInlineMath", 5], ["Reference", 3], ["Caption", 2], ["Table", 2], ["Text", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 528], ["Line", 100], ["TableCell", 61], ["Text", 6], ["Reference", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 83], ["TableCell", 34], ["Text", 8], ["Reference", 4], ["Caption", 3], ["SectionHeader", 2], ["Picture", 1], ["Figure", 1], ["Table", 1], ["PictureGroup", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 114], ["ListItem", 30], ["Reference", 30], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["Line", 81], ["ListItem", 21], ["Reference", 20], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 614], ["Line", 97], ["TableCell", 56], ["SectionHeader", 4], ["ListItem", 4], ["Reference", 4], ["Text", 3], ["TextInlineMath", 3], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 1088], ["Line", 190], ["Equation", 7], ["Reference", 7], ["TextInlineMath", 6], ["Text", 4], ["SectionHeader", 3], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4987, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 656], ["Line", 81], ["ListItem", 9], ["Reference", 7], ["Text", 6], ["SectionHeader", 4], ["TextInlineMath", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 188], ["Span", 103], ["Line", 13], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Picture", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 39], ["Line", 3], ["Caption", 2], ["Reference", 2], ["Picture", 1], ["Figure", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1253, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Picture", 1], ["Caption", 1], ["Line", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Minimizing_the_Accumulated_Trajectory_Error_to_Improve_Dataset_Distillation"}