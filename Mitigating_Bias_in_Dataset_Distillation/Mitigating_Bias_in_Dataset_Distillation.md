# Mitigating Bias in Dataset Distillation

<PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><PERSON><sup>1</sup>

# Abstract

Dataset Distillation has emerged as a technique for compressing large datasets into smaller synthetic counterparts, facilitating downstream training tasks. In this paper, we study the impact of bias inside the original dataset on the performance of dataset distillation. With a comprehensive empirical evaluation on canonical datasets with color, corruption and background biases, we found that color and background biases in the original dataset will be amplified through the distillation process, resulting in a notable decline in the performance of models trained on the distilled dataset, while corruption bias is suppressed through the distillation process. To reduce bias amplification in dataset distillation, we introduce a simple yet highly effective approach based on a sample reweighting scheme utilizing kernel density estimation. Empirical results on multiple realworld and synthetic datasets demonstrate the effectiveness of the proposed method. Notably, on CMNIST with 5% bias-conflict ratio and IPC 50, our method achieves 91.5% test accuracy compared to 23.8% from vanilla DM, boosting the performance by 67.7%, whereas applying state-ofthe-art debiasing method on the same dataset only achieves 53.7% accuracy. Our findings highlight the importance of addressing biases in dataset distillation and provide a promising avenue to address bias amplification in the process.

# 1. Introduction

Dataset plays a central role in machine learning model performances. The rapidly growing size of contemporary datasets not only poses challenges to data storage and preprocessing, but also makes it increasingly expensive to train machine learning models and design new methods, such

as architectures, hyperparameters, and loss functions. As a result, dataset distillation (also known as dataset condensation [\(Wang et al.,](#page-10-0) [2018\)](#page-10-0)) emerges as a promising direction for solving this issue. Dataset Distillation aims at compressing the original large-scale dataset into a small subset of information-rich examples, enabling models trained on them to achieve competitive performance compared with training on the whole dataset. The distilled dataset with a significantly reduced size can therefore be used to accelerate model training and reduce data storage. Dataset distillation has been shown to benefit a wide range of machine learning tasks, such as Neural Architecture Search [\(Wang et al.,](#page-10-1) [2021\)](#page-10-1), Federated Learning [\(Xiong et al.,](#page-10-2) [2023\)](#page-10-2), Continual Learning [\(Yang et al.,](#page-10-3) [2023;](#page-10-3) [Gu et al.,](#page-8-0) [2023\)](#page-8-0), Graph Compression [\(Jin et al.,](#page-8-1) [2021;](#page-8-1) [Feng et al.,](#page-8-2) [2023a\)](#page-8-2), and Multimodality [\(Wu et al.,](#page-10-4) [2023\)](#page-10-4).

Recent dataset distillation methods mainly focus on performance enhancements on standard datasets [\(Loo et al.,](#page-9-0) [2023;](#page-9-0) [Zhao et al.,](#page-10-5) [2023;](#page-10-5) [Du et al.,](#page-8-3) [2023;](#page-8-3) [Cui et al.,](#page-8-4) [2023\)](#page-8-4). However, this emphasis often overlooks dataset bias, a critical issue in machine learning. Dataset bias [\(Tommasi et al.,](#page-9-1) [2017\)](#page-9-1) occurs when collected data unintentionally reflects existing biases, leading to skewed predictions and potential ethical concerns. Despite extensive research on bias detection and mitigation strategies in recent years [\(Sagawa et al.,](#page-9-2) [2019;](#page-9-2) [Li & Vasconcelos,](#page-9-3) [2019;](#page-9-3) [Nam et al.,](#page-9-4) [2020;](#page-9-4) [Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5), the impact of dataset bias on data distillation remains unexplored. Given that a biased synthetic set can lead to inaccurate or unfair decisions, understanding the role of bias in dataset distillation and developing effective mitigation strategies is crucial.

This paper provides the first study of how biases in the original training set affect dataset distillation process. Specifically, we are interested in the following questions: *1). How does bias propagate from the original dataset to the distilled dataset? 2). How do we mitigate biases present in the distilled dataset?*

To answer the first question, we assess existing dataset distillation algorithms across several benchmark datasets. Our findings reveal that the distillation process is significantly influenced by the type of bias, with color and background biases being amplified and noise bias suppressed. For datasets exhibiting amplified biases, we found that even state-of-the-

<sup>1</sup>Department of Computer Science, University of California, Los Angeles. Correspondence to: Justin Cui <<EMAIL>>, Cho-Jui Hsieh <<EMAIL>>.

*Proceedings of the*  $41^{st}$  *International Conference on Machine Learning*, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).

<span id="page-1-0"></span>Image /page/1/Figure/1 description: This is a flowchart illustrating a method for bias mitigation in machine learning. The process starts with a 'biased real dataset' containing images of digits 0-4, with a clear overrepresentation of certain digits. This dataset is fed into a 'surrogate model' represented by a diamond shape labeled psi. The output of this model is used in a 'matching loss' calculation. Simultaneously, an 'initial synthetic set with random noise' is also processed. This synthetic data is updated and then fed into another 'surrogate model' (also labeled psi). The output of this second surrogate model is also used in the 'matching loss'. The 'matching loss' is then backpropagated to update the second surrogate model. The flowchart also shows a 'SupCon' process feeding into a diamond labeled Phi, which then leads to a 't-SNE visualization'. This visualization displays clusters of data points, colored differently, representing the learned representations. A Kernel Density Estimation (KDE) process connects the 'matching loss' output to the 't-SNE visualization'.

Figure 1. Workflow of our method that utilizes Supervised Contrastive model and Kernel Density Estimation to mitigate bias in the dataset distillation process.

art de-biasing training methods are insufficient in restoring the original performance. This highlights the urgency of developing a bias-mitigating dataset distillation algorithm.

To counter bias in distilled datasets, we propose a simple yet effective debiasing algorithm based on data point reweighting. Leveraging the insight that biased data points cluster in the model's embedding space, we down-weight samples within such clusters using kernel density estimation. This re-weighting rebalances the significance of biased and unbiased samples, ameliorating biases in the distillation process. Empirical results on diverse bias-injected datasets demonstrate that the proposed reweighting scheme significantly reduces bias in the distilled datasets. For example, on Colored MNIST with a 5% bias in conflicting samples and 50 images per class, the original Distribution Matching (DM) method leads to a biased synthetic set. A model trained on such a distilled set achieves only 23.8% accuracy. In contrast, our reweighting method produces a more balanced dataset, resulting in 91.5% accuracy, representing a 67.7% gain over DM. In summary, our contributions are:

- 1. We provide the first study on the impact of biases in dataset distillation process.
- 2. We propose a simple yet effective re-weighting scheme to mitigate biases in two canonical types of dataset distillation methods.
- 3. Through extensive experiments and ablation studies on both real-world and synthesized datasets, we demonstrate the effectiveness of our method.

# 2. Related Work

### 2.1. Dataset Distillation

Dataset Distillation (DD) or Condensation (DC) [\(Wang](#page-10-0) [et al.,](#page-10-0) [2018;](#page-10-0) [Li et al.,](#page-9-6) [2022\)](#page-9-6) aims to compress a large dataset into a small but informative one to achieve competitive performances compared to the whole dataset. Naturally, it's

framed as a bi-level optimization problem using surrogate objectives such as model gradients [\(Zhao & Bilen,](#page-10-6) [2021b;](#page-10-6) [Feng et al.,](#page-8-6) [2023b;](#page-8-6) [Du et al.,](#page-8-7) [2024\)](#page-8-7) or training trajectories [\(Cazenavette et al.,](#page-8-8) [2022;](#page-8-8) [Du et al.,](#page-8-3) [2023;](#page-8-3) [Cui et al.,](#page-8-4) [2023;](#page-8-4) [Guo et al.,](#page-8-9) [2023\)](#page-8-9). The inner loop of the bi-level optimization usually optimizes a surrogate model using the distilled dataset and the outer loop tries to optimize the resulting distilled dataset itself.

Due to the heavy computation involved in the bi-level optimization process, one line of work [\(Zhao & Bilen,](#page-10-7) [2021a;](#page-10-7) [Wang et al.,](#page-9-7) [2022;](#page-9-7) [Zhao et al.,](#page-10-5) [2023;](#page-10-5) [Sajedi et al.,](#page-9-8) [2023;](#page-9-8) [Zhang et al.,](#page-10-8) [2024a\)](#page-10-8) tries to directly match the latent feature space. While for other works [\(Zhou et al.,](#page-10-9) [2022;](#page-10-9) [Loo et al.,](#page-9-9) [2022\)](#page-9-9), they draw inspiration from Kernel Ridge Regression (KRR) [\(Nguyen et al.,](#page-9-10) [2020\)](#page-9-10) and try to approximate the surrogate model using kernel methods to tackle the high computation cost. Orthogonally, some methods [\(Kim et al.,](#page-9-11) [2022;](#page-9-11) [Deng & Russakovsky,](#page-8-10) [2022;](#page-8-10) [Wei et al.,](#page-10-10) [2024;](#page-10-10) [Shin](#page-9-12) [et al.,](#page-9-12) [2024;](#page-9-12) [Liu & Wang,](#page-9-13) [2023\)](#page-9-13) propose to compress the dataset into even more compact spaces through parametrization. In order to boost the applicability of the resulting dataset, recent works [\(Yin et al.,](#page-10-11) [2024;](#page-10-11) [Sun et al.,](#page-9-14) [2023\)](#page-9-14) try to scale DD to large scale datasets such as ImageNet-1K using different approaches such as a 3-stage process or an efficient paradigm that enables both diversity and realism. Pre-trained generative models are also used in recent works [\(Cazenavette et al.,](#page-8-11) [2023;](#page-8-11) [Zhang et al.,](#page-10-12) [2023;](#page-10-12) [Wang et al.,](#page-10-13) [2023\)](#page-10-13) to enhance the distilled datasets.

Because of the highly compressed dataset size and competitive performance, the distilled dataset can be used for many downstream tasks such as Continual Learning [\(Yang](#page-10-3) [et al.,](#page-10-3) [2023;](#page-10-3) [Gu et al.,](#page-8-0) [2023\)](#page-8-0), Federated Learning [\(Xiong](#page-10-2) [et al.,](#page-10-2) [2023;](#page-10-2) [Huang et al.,](#page-8-12) [2023\)](#page-8-12), Graph Neural Networks [\(Zhang et al.,](#page-10-14) [2024b;](#page-10-14) [Liu et al.,](#page-9-15) [2024\)](#page-9-15), Neural Architecture Search [\(Such et al.,](#page-9-16) [2020;](#page-9-16) [Medvedev & D'yakonov,](#page-9-17) [2021\)](#page-9-17), etc. Since most of the parameterization methods can be used as an add-on module to non-parameterization methods, we focus on non-parameterization methods to study the effect of bias in this paper.

### 2.2. Dataset and Model Bias

Deep Neural Networks (DNNs) have achieved remarkable performance in discovering correlations present within datasets. However, when applied to datasets where simple and spurious correlations coexist with complex and intrinsic correlations, DNNs may inadvertently lean towards the shortcuts. The generalization ability of DNNs will be greatly hindered when these spurious relations are learned instead of the intrinsic ones. Following previous works [\(Hwang](#page-8-5) [et al.,](#page-8-5) [2022\)](#page-8-5), we refer to samples strongly correlates with bias feature as bias-aligned and samples that don't align with bias features as bias-conflicting. The most commonly

studied bias types include color, background, noise, texture etc [\(Nam et al.,](#page-9-4) [2020;](#page-9-4) [Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5). In this paper, we study how the bias in the original data affects the small synthetic set through the dataset distillation process and try to mitigate this phenomena. The evaluations are done by testing whether models trained on synthetic datasets can effectively generalize to unbiased test datasets.

### 2.3. De-biasing Methods

While prior research has explored de-biasing methods on entire datasets, our work is the first, to the best of our knowledge, to investigate the impact of biases on dataset distillation results in this emerging field. Below, we discuss several state-of-the-art de-biasing methods designed to train de-biased models on entire datasets.

LfF [\(Nam et al.,](#page-9-4) [2020\)](#page-9-4) debiases by training a biased and debiased model together. It uses generalized cross entropy (GCE) [\(Zhang & Sabuncu,](#page-10-15) [2018\)](#page-10-15) loss to amplify bias and computes a difficulty score for debiased model loss weighting. DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) disentangles bias and intrinsic attributes, creating unbiased samples by swapping bias embeddings among training samples to train a debiased model. SelectMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) shares the same idea of creating more unbiased samples by using mixup augmentation on contradicting pairs selected by a biased model.

Note that, although some prior de-biasing methods involve reweighting [\(Sagawa et al.,](#page-9-2) [2019;](#page-9-2) [Li & Vasconcelos,](#page-9-3) [2019;](#page-9-3) [Nam et al.,](#page-9-4) [2020\)](#page-9-4), our approach differs for the following reasons: 1) Previous methods [\(Li & Vasconcelos,](#page-9-3) [2019;](#page-9-3) [Nam et al.,](#page-9-4) [2020\)](#page-9-4) integrate the reweighting scheme into the final de-biased model's training, either as a loss or using an auxiliary model, optimizing it alongside the de-biased model. This process does not apply to dataset distillation methods since a well-trained model is not required in the process (e.g., DM uses randomly initialized models, and MTT only matches part of the training trajectories). 2) Methods like [\(Sagawa et al.,](#page-9-2) [2019\)](#page-9-2) require explicit bias supervision, which may be challenging or infeasible, while our method has no such requirement.

# <span id="page-2-0"></span>3. The Impact of Bias in Dataset Distillation

In this section, we conduct comprehensive experiments to answer the following question: *how does a biased training set influence the distilled data? Will the bias be amplified or suppressed through the distillation process?*

In line with prior studies [\(Nam et al.,](#page-9-4) [2020;](#page-9-4) [Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5), we explore three datasets: Colored MNIST (CMNIST), Background Fashion-MNIST (BG FM-NIST), and Corrupted CIFAR-10. CMNIST introduces a color bias, causing classes to share specific colors, potentially confusing training. BG FMNIST combines Mini-

Places (Zhou et al., 2017) with Fashion-MNIST, creating background biases, e.g. T-shirts in bamboo forests. Corrupted CIFAR-10 introduces perturbations and image distortions like Gaussian noise, blur, brightness, contrast changes, and occlusions. See Figure [7](#page-15-0) for examples and Section [5.1](#page-4-0) for detailed descriptions.

For each dataset, we use  $D$  to denote the unbiased set (e.g., randomly distributed colors in CMNIST), while  $D_b$  represents the bias-injected dataset. In  $D<sub>b</sub>$ , 95% of the samples are aligned with the bias, meaning, for example, 95% of the digit '0' may be red, while the remaining 5% of the digit '0' possess random colors. Let  $\mathcal F$  be a dataset distillation algorithm that maps the original dataset into a distilled synthetic set, and let  $M$  denote the model training procedure that maps a dataset to a model. By comparing  $\mathcal{M}(\mathcal{F}(D))$  (model trained with distilled unbiased dataset) and  $\mathcal{M}(\mathcal{F}(D_b))$  (model trained with distilled biased dataset), we evaluate these two models' performance on unbiased test samples and compute the following measurement to reveal how the bias in the source dataset degrades the performance of the model trained on distilled synthetic samples:

$$
Acc(\mathcal{M}(\mathcal{F}(D))) - Acc(\mathcal{M}(\mathcal{F}(D_b))).
$$

We conduct experiments on three representitive dataset distillation algorithms: DSA [\(Zhao & Bilen,](#page-10-6) [2021b\)](#page-10-6), DM [\(Zhao & Bilen,](#page-10-7) [2021a\)](#page-10-7) and MTT [\(Cazenavette et al.,](#page-8-8) [2022\)](#page-8-8) as lots of SOTA methods can be used as an add-on module to these methods [\(Wang et al.,](#page-9-7) [2022;](#page-9-7) [Kim et al.,](#page-9-11) [2022;](#page-9-11) [Liu et al.,](#page-9-18) [2022;](#page-9-18) [Lee et al.,](#page-9-19) [2022\)](#page-9-19). Additionally, we include a baseline approach without dataset distillation, which is equivalent to the case when  $F$  represents an identity transformation. The visualization of bias impacts in dataset distillation can be found in Figure [2.](#page-3-0)

For CMNIST, the results presented on the left panel of Figure [2](#page-3-0) reveal a strong bias amplification effect – while the bias injected into the original dataset leads to a mere 4% performance drop in regular training, it results in over 50% performance decline when employing any of the three dataset distillation methods. This observation can be attributed to the following factors. Since the original set comprises 95% biased samples, with a selection of IPC 10, it is highly possible that all of the chosen images are biased. As a result, the unbiased signal totally diminishes through the distillation process. Another critical factor is that since color is a discriminative feature that can be easily learned by neural networks, dataset distillation algorithms will focus on distilling this color feature into the synthetic images in order to achieve good performance, leading to bias amplification. Similar impacts are also seen on the BG FMNIST dataset.

Interestingly, results from Corrupted CIFAR-10 show a reverse trend. The right panel of Figure [2](#page-3-0) shows that the

<span id="page-3-0"></span>Image /page/3/Figure/1 description: This image contains three bar charts comparing the performance of 'unbiased' and 'biased' models across different datasets and methods. The first chart, labeled 'CMNIST', shows 'unbiased' models performing at 100% for 'No distillation', 'DSA', 'DM', and 'MTT'. 'Biased' models show 96% for 'No distillation', 26% for 'DSA' with a 70% difference, 22% for 'DM' with a 78% difference, and 50% for 'MTT' with a 50% difference. The second chart, labeled 'BG FMNIST', shows 'unbiased' models at 90% for 'No distillation', 82% for 'DSA', 88% for 'DM', and 90% for 'MTT'. 'Biased' models show 73% for 'No distillation' with a 17% difference, 50% for 'DSA' with a 32% difference, 46% for 'DM' with a 42% difference, and 48% for 'MTT' with a 42% difference. The third chart, labeled 'Corrupted CIFAR-10', shows 'unbiased' models at 80% for 'No distillation', 52% for 'DSA', 48% for 'DM', and 68% for 'MTT'. 'Biased' models show 37% for 'No distillation' with a 43% difference, 32% for 'DSA' with a 20% difference, 32% for 'DM' with a 16% difference, and 42% for 'MTT' with a 26% difference. Each chart has a legend indicating 'unbiased' in blue and 'biased' in red.

Figure 2. The left most 2 bars indicate the model performance on full dataset with no distillation. For DSA/DM/MTT, the blue bar shows the model performance on the unbiased dataset and the red bar shows the performance of the corresponding dataset distillation method on that biased dataset with 5% bias-conflicting samples. The distillation performances are measured under IPC 10.

performance degradation is actually more substantial in the traditional training pipeline compared to the ones incorporating dataset distillation. This observation indicates the bias suppression effect for corruption biases, and dataset distillation is helpful for mitigating bias in this setting. Since corruption biases include several different perturbation effects such as Gaussian noise and blurring, we assume that the distillation process naturally blends information from multiple images, and the resulting images are already blurred in nature where noisy effects tend to cancel out, so it is harder to capture the corruption bias. Sampled distilled images are visualized in Figure [7.](#page-15-0)

For datasets exhibiting amplified biases (CMNIST and BG FMNIST), we found that even state-of-the-art de-biasing training methods such as SelectMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) and DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) are not able to obtain an unbiased model from the biased synthetic set. See more details in Section [6.](#page-6-0) This finding also highlights the urgency of developing a bias-mitigating DC algorithm to obtain unbiased synthetic sets.

# <span id="page-3-4"></span>4. Ameliorate Bias in Dataset Distillation

### 4.1. Bias Amelioration Through Re-weighting

In dataset distillation methods such as DM [\(Zhao & Bilen,](#page-10-7) [2021a;](#page-10-7) [Zhao et al.,](#page-10-5) [2023\)](#page-10-5), the objective is to match the embeddings generated by the synthetic dataset  $(S)$  with the ones generated by the real images  $(T)$ . The objective function is formulated as below

<span id="page-3-1"></span>
$$
\min_{\mathcal{S}} \mathop{\mathbb{E}}_{\substack{v \sim P_v \\ \omega \sim \Omega}} \| \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_v(\mathcal{A}(x_i, \omega)) - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \psi_v(\mathcal{A}(s_j, \omega)) \|^{2},
$$
\n(1)

where  $\psi$  is usually a surrogate model that maps the data into an embedding space and  $A$  is a differentiable augmentation function. By solving [\(1\)](#page-3-1), DM learns the synthetic set to match the mean embedding of the original set. If the dataset is highly biased, the first term  $\frac{1}{|T|}$ .  $\sum_{i=1}^{\lvert\mathcal{T}\rvert}$  $\sum_{i=1} \psi_v(\mathcal{A}(x_i,\omega))$ 

will be dominated by bias-aligned samples, thus causing distribution matching based methods to synthesize more bias-aligned images.

To mitigate bias, we propose to compute a weighted sum of the real image embeddings instead of simply using the mean of all data points. For data points that exhibit a strong correlation with the spurious (bias) feature, they should be assigned lower weights. Conversely, data points that have limited association with the bias feature should be afforded higher weights. This adjustment ensures that the distillation process effectively captures the intrinsic features. Let  $W(\mathcal{T}) = [w_0, w_1, ..., w_n]$  with n equals  $|\mathcal{T}|$  in Equation [\(1\)](#page-3-1) be the normalized weight of each training sample, the weighted loss can be written as

<span id="page-3-2"></span>
$$
\min_{\mathcal{S}} \mathop{\mathbb{E}}_{\substack{v \sim P_v \\ \omega \sim \Omega}} \|W(\mathcal{T}) \cdot \psi_v(\mathcal{A}(\mathcal{T}, \omega)) - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \psi_v(\mathcal{A}(s_j, \omega)) \|^{2},\tag{2}
$$

where  $W(\mathcal{T}) \cdot \psi_v(\mathcal{A}(\mathcal{T}, \omega))$  is the re-weighted embeddings where the bias feature has been balanced.

#### 4.2. Bias Estimation Using Kernel Density Estimation

The key problem of this reweighting scheme is how to compute  $W(X)$  which is unknown. In order to compute it, we propose to use Kernel Density Estimation (KDE), which is a non-parametric technique used to estimate the probability density function (PDF) of a random variable based on observed data points. Mathematically, given a set of n data points  $x_1, x_2, ..., x_n$ , the kernel density estimate  $\hat{f}(x)$  at any point x is given by:

<span id="page-3-3"></span>
$$
\hat{f}(x) = \frac{1}{n} \sum_{i=1}^{n} K(||\Phi(x) - \Phi(x_i)||), \tag{3}
$$

where  $K(\cdot)$  is the kernel function that determines the shape and width of the kernel placed on each data point. The most commonly used kernel function is the Gaussian kernel:  $K(u) = \frac{1}{\sigma\sqrt{2\pi}}e^{-\frac{1}{2}\cdot(\frac{u}{\sigma})^2}$ .  $\Phi$  is a function that maps the data points into an embedding space for distance computation. By summing the contributions of the kernel functions centered at each data point, KDE provides an estimate of the PDF at any given point x. The estimate  $\hat{f}(x)$  represents the density of the underlying distribution at that point. Since a biased dataset is usually dominated by bias-aligned samples which should be given a lower weight, we propose to use the normalized inverse of the kernel density function  $\mathbb{N}(\frac{1}{\hat{f}(x)})$ as the new weights, and  $N$  is a normalization function such as softmax so that  $\sum_{i=1}^{|\mathcal{T}|} \mathbb{N}(\frac{1}{\hat{f}(x_i)}) = 1$ . Eventually we have  $W(\mathcal{T}) = [\mathbb{N}(\frac{1}{\hat{f}(x_1)}),..\mathbb{N}(\frac{1}{\hat{f}(x_n)})].$ 

#### 4.3. Distance Computation in KDE

The aforementioned KDE reweighting scheme requires a feature mapping  $\Phi(\cdot)$  to map images from raw pixel space to a more meaningful hidden space. Moreover, an optimal mapping for  $\Phi(\cdot)$  would be one that transforms images into biased features, enabling KDE to accurately represent the density of bias. Consequently, our reweighting scheme can effectively mitigate bias. To obtain bias features without external knowledge, DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) and LfF [\(Nam](#page-9-4) [et al.,](#page-9-4) [2020\)](#page-9-4) both utilize the generalized cross entropy (GCE) loss to train an auxiliary bias model. However, neither of them directly work with latent spaces that can be utilized in KDE. Following the recent SOTA de-biasing method SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5), we utilize a supervised constrastive learning [\(Chen et al.,](#page-8-13) [2020;](#page-8-13) [Khosla et al.,](#page-9-20) [2020\)](#page-9-20) model, which is trained with generalized supervised contrastive (GSC) loss to produce image embeddings that can be used to measure distances between data points. The model is proven to produce high quality similarity matrix regarding bias features [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5). See Figure [1](#page-1-0) for the visualization of our overall workflow.

### 4.4. Apply to Other Dataset Distillation Methods

In addition to DM and its follow up works such as DREAM [\(Liu et al.,](#page-9-21) [2023\)](#page-9-21) and IDM [\(Zhao et al.,](#page-10-5) [2023\)](#page-10-5), our method can also be easily applied to other dataset distillation methods. Here we use DSA [\(Zhao & Bilen,](#page-10-6) [2021b;](#page-10-6) [2023;](#page-10-16) [Kim et al.,](#page-9-11) [2022\)](#page-9-11) as an example which synthesizes data by matching the gradients between surrogate models trained with real data and synthetic data. Generally, gradient matching based methods can be formulated as:

<span id="page-4-1"></span>
$$
\min_{\mathcal{S}} \ D(\nabla_{\theta} \mathcal{L}_{c}^{\mathcal{T}}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}}), \theta_{t}), \nabla_{\theta} \mathcal{L}_{c}^{\mathcal{S}}(\mathcal{A}(\mathcal{S}, \omega^{\mathcal{S}}), \theta_{t})),
$$
\n(4)

where  $\mathcal{L}_{c}^{\mathcal{T}} = \frac{1}{|\mathcal{T}|} \sum_{x,y} \ell(\phi_{\theta_t}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}})))$ . We denote the re-computed weights as  $W(\mathcal{T})$ , then the first term in Equa-tion [\(4\)](#page-4-1) can be replaced with  $W(\mathcal{T}) \cdot \nabla_{\theta} \mathcal{L}_{c}^{\mathcal{T}}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}}), \theta_t)$ . We demonstrate that the proposed method works well with both DSA and DM in the following sections.

### 5. Experiments

#### <span id="page-4-0"></span>5.1. Evaluation Datasets

Following previous works [\(Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5), we first evaluate our method on 3 canonical biased datasets including two synthesized datasets Colored MNIST (CMNIST) and Corrupted CIFAR-10 and one real-world dataset BFFHQ. CMNIST originates from MNIST [\(Deng,](#page-8-14) [2012\)](#page-8-14) dataset with added color correlation such as most digit 0 being red and digit 4 being green. Corrupted CIFAR10 applies different corruptions to the images in CIFAR-10 so that one class are associated with one type of corruption such as GaussianNoise or MotionBlur. BFFHQ [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) is constructed from FFHQ [\(Kar](#page-9-22)[ras et al.,](#page-9-22) [2019\)](#page-9-22) which contains human face images paired with their corresponding facial attribute annotations. Age is selected as the label and gender is selected as the biased feature. We also contribute one novel dataset Background Fashion-MNIST (BG FMNIST) to evaluate background biases which is constructed by using Fashion-MNIST [\(Xiao](#page-10-17) [et al.,](#page-10-17) [2017\)](#page-10-17) as foregrounds and MiniPlaces [\(Zhou et al.,](#page-10-18) [2017\)](#page-10-18) as backgrounds. Similar to SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) and DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5), we evaluate our methods under 3 settings with  $1\%, 2\%, 5\%$  $1\%, 2\%, 5\%$  bias conflicting samples<sup>1</sup> for CMNIST, Corrupted CIFAR-10 and BG FMNIST and  $0.5\%$  bias conflicting ratios for BFFHQ<sup>[2](#page-4-3)</sup>. More detailed description regarding datasets can be found in Appendix [A.5.](#page-12-0)

### 5.2. Experimental Results

We evaluate the performance of 4 representative dataset distillation methods: DM [\(Zhao & Bilen,](#page-10-7) [2021a\)](#page-10-7) , DSA [\(Zhao](#page-10-6) [& Bilen,](#page-10-6) [2021b\)](#page-10-6), DREAM [\(Liu et al.,](#page-9-21) [2023\)](#page-9-21) and IDM [\(Zhao](#page-10-5) [et al.,](#page-10-5) [2023\)](#page-10-5). We then use the proposed algorithm to improve DM and DSA algorithms in the experiments. Note that our algorithm can also be applied to other existing distillation methods, as discussed in Appendix [A.1.](#page-11-0) Also, see Appendix [A.7](#page-13-0) for a qualitative analysis and Appendix [A.2](#page-11-1) for specific hyper-parameter settings.

### 5.2.1. PERFORMANCE BOOST ON DM

We first investigate if the proposed method can mitigate biases in distribution matching-based methods such as DM. The evaluation results are shown in Table [1.](#page-5-0) It can be seen that as the number of IPCs increases, the vanilla DM has little to no performance gain on bias amplifying datasets CMNIST and BG FMNIST. When IPC increases from 1 to 10 on CMNIST with 1, 2 and 5 percent bias-conflicting

<span id="page-4-2"></span> $1B$ ias-conflicting samples are samples in a class that have different bias properties from the majority of the samples in that class. For example, a yellow 0 is a bias conflicting sample when the majority of 0s are red.

<span id="page-4-3"></span> ${}^{2}$ BFFHQ only comes with 0.5% bias conflicting ratio [\(Lee](#page-9-5) [et al.,](#page-9-5) [2021\)](#page-9-5).

<span id="page-5-0"></span>

| Dataset            | Method       |                | Bias-conflict Ratio (1.0%) |                |                | Bias-conflict Ratio (2.0%) |                | Bias-conflict Ratio (5.0%) |                |                |
|--------------------|--------------|----------------|----------------------------|----------------|----------------|----------------------------|----------------|----------------------------|----------------|----------------|
|                    |              | 1              | 10                         | 50             | 1              | 10                         | 50             | 1                          | 10             | 50             |
|                    | Random       | $22.6 \pm 1.0$ | $13.5 \pm 0.3$             | $16.0 \pm 0.1$ | $22.8 \pm 1.0$ | $16.2 \pm 0.1$             | $20.7 \pm 0.1$ | $19.9 \pm 0.5$             | $17.4 \pm 0.4$ | $27.2 \pm 0.2$ |
|                    | <b>MTT</b>   | $24.2 \pm 0.3$ | $27.6 \pm 0.4$             | $18.1 \pm 0.6$ | $28.3 \pm 0.3$ | $42.0 \pm 0.4$             | $26.0 \pm 0.5$ | $29.2 \pm 0.9$             | $47.7 \pm 0.8$ | $33.9 \pm 1.2$ |
|                    | <b>IDM</b>   | $20.1 \pm 0.9$ | $18.8 \pm 1.2$             | $17.6 \pm 1.6$ | $20.2 \pm 1.0$ | $20.1 \pm 0.5$             | $19.1 \pm 1.3$ | $20.8 \pm 0.9$             | $22.3 \pm 1.1$ | $26.5 \pm 1.5$ |
| <b>CMNIST</b>      | <b>DREAM</b> | $21.3 \pm 0.4$ | $15.9 \pm 1.2$             | $17.9 \pm 0.8$ | $26.2 \pm 0.5$ | $17.1 \pm 1.3$             | $17.5 \pm 1.5$ | $23.7 \pm 1.2$             | $30.8 \pm 1.5$ | $50.4 \pm 1.0$ |
|                    | DM           | $25.4 \pm 0.1$ | $18.6 \pm 0.2$             | $22.6 \pm 0.5$ | $24.8 \pm 0.4$ | $18.5 \pm 0.6$             | $23.6 \pm 0.8$ | $25.3 \pm 0.3$             | $19.6 \pm 0.9$ | $23.8 \pm 1.3$ |
|                    | $DM+Ours$    | $28.0 \pm 0.5$ | $64.9 \pm 0.3$             | $75.4 \pm 1.1$ | $26.4 \pm 0.7$ | $50.6 \pm 1.2$             | $75.7 \pm 1.0$ | $32.2 \pm 1.0$             | $86.5 \pm 1.2$ | $91.5 \pm 0.9$ |
|                    | <b>DSA</b>   | $26.1 \pm 0.3$ | $16.5 \pm 0.2$             | $14.5 \pm 0.2$ | $25.2 \pm 0.3$ | $16.8 \pm 0.3$             | $30.9 \pm 0.4$ | $25.9 \pm 0.5$             | $27.3 \pm 0.4$ | $68.5 \pm 1.2$ |
|                    | DSA+Ours     | $27.9 \pm 0.4$ | $76.7 \pm 1.1$             | $81.4 \pm 0.8$ | $26.4 \pm 0.2$ | $75.3 \pm 0.3$             | $83.0 \pm 1.2$ | $32.6 \pm 0.1$             | $91.9 \pm 0.7$ | $94.0 \pm 0.8$ |
|                    | Random       | $40.0 \pm 0.2$ | $40.4 \pm 1.6$             | $35.2 \pm 0.4$ | $30.2 \pm 0.6$ | $43.2 \pm 0.2$             | $33.5 \pm 1.0$ | $36.2 \pm 1.3$             | $44.6 \pm 1.2$ | $41.2 \pm 1.1$ |
|                    | <b>MTT</b>   | $39.0 \pm 1.2$ | $48.0 \pm 1.5$             | $45.3 \pm 0.9$ | $38.9 \pm 1.4$ | $59.2 \pm 1.1$             | $59.3 \pm 0.8$ | $48.1 \pm 1.4$             | $45.2 \pm 1.3$ | $62.3 \pm 0.8$ |
|                    | <b>IDM</b>   | $40.7 \pm 1.0$ | $42.3 \pm 0.9$             | $38.4 \pm 1.2$ | $41.1 \pm 0.8$ | $37.2 \pm 1.3$             | $40.5 \pm 0.9$ | $43.4 \pm 0.4$             | $46.6 \pm 0.8$ | $42.0 \pm 1.2$ |
| <b>BG FMNIST</b>   | <b>DREAM</b> | 39.7±0.9       | $46.4 \pm 1.2$             | $46.1 \pm 1.5$ | $45.0 \pm 1.0$ | $46.0 \pm 0.8$             | $44.5 \pm 0.8$ | $43.5 \pm 0.9$             | $52.4 \pm 1.5$ | $53.2 \pm 1.2$ |
|                    | <b>DM</b>    | $41.0 \pm 0.3$ | $42.2 \pm 0.8$             | $43.9 \pm 0.4$ | $40.1 \pm 0.6$ | $40.1 \pm 0.9$             | $44.4 \pm 0.5$ | $41.7 \pm 0.5$             | $42.0 \pm 1.2$ | $44.6 \pm 0.9$ |
|                    | DM+Ours      | $44.6 \pm 0.5$ | $50.6 \pm 0.2$             | $57.2 \pm 0.6$ | $51.4 \pm 0.7$ | $62.3 \pm 0.4$             | $63.0 \pm 1.0$ | $49.4 \pm 0.2$             | $61.8 \pm 0.6$ | $65.0 \pm 0.8$ |
|                    | <b>DSA</b>   | $43.4 \pm 0.4$ | $45.8 \pm 0.5$             | $40.7 \pm 0.9$ | $43.7 \pm 0.5$ | $47.6 \pm 0.3$             | $48.4 \pm 0.8$ | $44.7 \pm 0.6$             | $52.8 \pm 0.5$ | $59.3 \pm 0.6$ |
|                    | DSA+Ours     | $44.4 \pm 0.6$ | $57.0 \pm 1.0$             | $58.3 \pm 0.8$ | $48.5 \pm 1.2$ | $64.4 \pm 0.9$             | $65.1 \pm 0.8$ | $46.2 \pm 0.6$             | $66.4 \pm 0.6$ | $71.2 \pm 1.1$ |
|                    | Random       | $16.4 \pm 0.6$ | $26.9 \pm 0.2$             | $32.7 \pm 0.3$ | $19.1 \pm 0.1$ | $23.2 \pm 0.2$             | $33.5 \pm 0.1$ | $11.8 \pm 0.1$             | $26.4 \pm 0.3$ | $34.2 \pm 0.4$ |
|                    | MTT          | $23.5 \pm 0.4$ | $25.4 \pm 1.5$             | $33.3 \pm 0.5$ | $24.1 \pm 0.3$ | $36.3 \pm 0.4$             | $35.7 \pm 0.2$ | $24.2 \pm 0.8$             | $39.0 \pm 0.3$ | $39.5 \pm 0.4$ |
|                    | <b>IDM</b>   | $26.3 \pm 1.0$ | $30.2 \pm 0.4$             | $36.6 \pm 0.8$ | $26.2 \pm 0.8$ | $29.5 \pm 0.2$             | $35.0 \pm 0.7$ | $26.0 \pm 0.8$             | $31.4 \pm 1.1$ | $36.5 \pm 0.9$ |
| Corrupted CIFAR-10 | <b>DREAM</b> | $23.7 \pm 0.9$ | $25.0 \pm 1.2$             | $33.6 \pm 0.9$ | $25.9 \pm 0.8$ | $25.2 \pm 0.6$             | $32.1 \pm 1.3$ | $25.6 \pm 0.9$             | $24.5 \pm 0.8$ | $33.7 \pm 1.2$ |
|                    | DM           | $25.1 \pm 0.4$ | $32.9 \pm 0.3$             | $37.6 \pm 0.8$ | $25.0 \pm 0.1$ | $32.9 \pm 0.1$             | $37.7 \pm 0.2$ | $24.6 \pm 0.4$             | $33.5 \pm 0.8$ | $38.7 \pm 0.4$ |
|                    | DM+Ours      | $24.2 \pm 1.2$ | $33.4 \pm 0.9$             | $39.4 \pm 0.8$ | $25.3 \pm 0.5$ | $34.2 \pm 0.5$             | $39.7 \pm 0.4$ | $26.6 \pm 0.5$             | $33.5 \pm 0.6$ | $40.2 \pm 0.4$ |
|                    | <b>DSA</b>   | $25.5 \pm 0.3$ | $31.9 \pm 0.8$             | $34.1 \pm 0.5$ | $25.1 \pm 0.2$ | $32.0 \pm 0.1$             | $34.2 \pm 0.3$ | $25.7 \pm 0.5$             | $32.8 \pm 0.6$ | $35.6 \pm 0.5$ |
|                    | DSA+Ours     | $26.0 \pm 0.1$ | $32.6 \pm 0.8$             | $35.0 \pm 0.6$ | $25.2 \pm 0.8$ | $33.2 \pm 0.2$             | $35.8 \pm 0.6$ | $26.0 \pm 0.3$             | $32.5 \pm 0.7$ | $36.6 \pm 0.3$ |

Table 1. Test accuracy of baseline methods and our methods on distilled datasets with varying bias-conflict ratios.

1. Results in bold show the best performance. Results with underline show the second best performance.

2. See Appendix [A.1](#page-11-0) for extending our method to DREAM [\(Liu et al.,](#page-9-21) [2023\)](#page-10-5) and IDM [\(Zhao et al.,](#page-10-5) 2023) which achieves SOTA results based on DSA and DM.

3. See Appendix [A.3](#page-11-2) for the results under extremely low bias-conflict ratio (0.5%) where our method also achieves strong performances.

samples, there is even a performance degradation. After reweighting the samples according to Equation [\(2\)](#page-3-2), we are able to mitigate the biases effectively. Under the settings of IPC 50, we are able to boost the performance from 22.6% to 75.4% on CMNIST dataset with 1% bias conflicting samples. With 2% and 5% bias conflicting samples, the accuracy also increased from 23.6% to 75.7% and 23.8% to 91.5% respectively. The complete results can be found in Tables [1](#page-5-0) and [2.](#page-5-1) Similar performance boosts are also observed on BG FMNIST, e.g. with IPC 10, the performance gains are 8.4%, 22.2% and 19.8% with 1, 2 and 5 percent bias conflicting samples. As shown in previous works [\(Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5), de-biasing becomes more challenging on real-world dataset BFFHQ which only comes with 0.5% bias conflict samples. However, we still see an overall improvement of up to 6.2%. On Corrupted CIFAR-10, we only observe a slight performance boost which aligns with the intuition described in Section [3.](#page-2-0)

### 5.2.2. PERFORMANCE BOOST ON DSA

Next, we investigate whether the proposed method can improve gradient matching based methods and choose DSA as the representative algorithm. Similar to DM, we also observe a strong performance boost on CMNIST. Under the settings of IPC 50, with 1, 2 and 5 percent bias conflicting samples, the performance increases from 14.5% to 81.4%, <span id="page-5-1"></span>Table 2. Test accuracy on real-world dataset BFFHQ with varying IPCs. BFFHQ dataset only comes with 0.5% bias-conflict samples which makes bias mitigation challenging. Our method is still able to boost the performance by up to 6.2%.

| IPC.     | Random       | MTT          | <b>DREAM</b>                                            | DM | $DM+Ours$                                                                  | <b>DSA</b> | $DSA+Ours$     |
|----------|--------------|--------------|---------------------------------------------------------|----|----------------------------------------------------------------------------|------------|----------------|
| -1       | $50.8 + 1.5$ |              |                                                         |    | $52.5 \pm 1.2$ $54.0 \pm 1.2$ $58.3 \pm 0.3$ $64.5 \pm 1.5$ $61.1 \pm 1.0$ |            | $62.3 + 0.2$   |
| 10       | $50.2 + 0.8$ | $58.6 + 1.3$ |                                                         |    | $61.2 \pm 1.9$ $63.6 \pm 1.0$ $65.2 \pm 1.7$ $62.6 \pm 1.6$                |            | $64.0 \pm 1.5$ |
| 50       | $50.5 + 1.2$ | $\sim$       |                                                         |    | $64.7 \pm 1.2$ $63.9 \pm 1.2$ $65.6 \pm 0.9$ $58.7 \pm 1.4$                |            | $61.5 + 0.7$   |
| $x = -1$ | .            | _____        | $\sim$ $\sim$ $\sim$ $\sim$ $\sim$ $\sim$ $\sim$ $\sim$ |    | _______________________________________                                    |            |                |

We aren't able to get IDM's performance because it's OOM on all settings for BFFHQ dataset

30.9% to 83.0% and 68.5% to 94.0%. On BG FMNIST, the performances increase from 40.7% to 58.3%, 48.4% to 65.1% and 59.3% to 71.2% for 1, 2 and 5% bias conflicting samples with IPC 50. On BFFHQ, we see a performance increase of up to 2.8% even with the extremely low biasconflict ratio. Complete results are shown in Tables [1](#page-5-0) and [2.](#page-5-1)

### 5.2.3. COMPARE TO OTHER SOTA METHODS

The performance of 3 other representative methods, DREAM, IDM and MTT, are also included in Tables [1](#page-5-0) and [2.](#page-5-1) We observe that MTT outperforms vanilla DM and DSA, achieving better results on CMNIST and BG FMNIST. For instance, on BG FMNIST with 5% bias-conflicting samples and IPC 50, MTT achieves 62.3% accuracy compared to DM's 44.6% under the same settings. We think the reason is that MTT doesn't use real images during distillation phase

but the trajectories from teacher models. Thus its performance is determined by both the teacher model trajectories (directly through trajectory matching) and biased original dataset (indirectly through teacher models trained using the biased original dataset). However, it also struggles with biases on general, whereas our proposed method is able to mitigate biases effectively on CMNIST, BG FMNIST and BFFHQ. DREAM and IDM, based on DSA and DM, tend to perform worse on biased datasets. This is partly due to their attempt to extract more information from the original dataset, potentially introducing additional biases. For instance, DREAM's use of Kmeans for selecting representative samples can lead to exclusion of unbiased samples in a biased dataset. See Appendix [A.1](#page-11-0) for results of applying our method to mitigate biases in DREAM and IDM.

### 5.3. Runtime Analysis

Our method has a similar runtime overhead to the state-ofthe-art de-biasing method SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5). It consists of two parts: 1) training a ResNet18 [\(He et al.,](#page-8-15) [2016a\)](#page-8-15) with a 128-dimensional MLP projection head, typically converging within 30 minutes which is around 5% overhead on DSA which usually requires more than 10 hours to finish with IPC 50 on the hardware, and 2) computing pairwise distances and KDE within each training batch, with a time complexity of  $O(B<sup>2</sup>d)$ , where B is the batch size (usually 256) and  $d$  is the projection dimension (128 in our case). With DSA and IPC 50, it takes 55 seconds for 10 iterations without KDE and 65 seconds with KDE, resulting in an 18.2% overhead. Note that, unlike SelecMix which updates the SupCon model during training and repeatedly recomputes pairwise distances, our pretrained SupCon model allows us to cache and reuse pairwise distances throughout the entire distillation process to mitigate this overhead. More details can be found in Appendix [A.12.](#page-15-1)

# <span id="page-6-0"></span>6. Ablation Study

1. Apply de-biasing methods to synthetic dataset. Do we really need to mitigate bias in the DD process? Even though vanilla dataset distillation methods result in the biased synthetic set, can we still obtain an unbiased model from such a biased set using existing de-biasing training algorithms? In order to answer this question, we apply two SOTA de-biasing methods, SelectMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) and DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) on the synthetic dataset generated by DM and present the results in Table [3.](#page-6-1) The experiments are conducted on CMNIST and BG FMNIST with 5% bias-conflicting samples and IPC 10 and 50. We have the following observations from the experiments: 1) DFA can slightly improve the performance on CMNIST but suffers from severe performance degradation on BG FMNIST. We think the reason is that DFA relies on a bias

<span id="page-6-1"></span>Table 3. Ablation study test accuracy (%) on applying de-biasing method to train with synthetic datasets from DM, assessed under 5% bias-conflicting samples and IPC 10 and 50.

|              |                | <b>CMNIST</b>  | <b>BG FMNIST</b> |                |  |
|--------------|----------------|----------------|------------------|----------------|--|
| <b>IPC</b>   | 10             | 50             | 10               | 50             |  |
| DМ           | $19.6 + 0.9$   | $23.8 + 1.3$   | $42.0 + 1.2$     | $44.6 + 0.9$   |  |
| <b>DFA</b>   | $25.8 + 1.0$   | $31.2 + 1.3$   | $11.0 + 2.1$     | $17.6 + 1.9$   |  |
| $SelecMix^*$ | $43.3 \pm 1.3$ | $53.7 \pm 1.5$ | $57.2 + 1.1$     | $58.7 \pm 0.9$ |  |
| $DM+Ours$    | $86.5 + 1.2$   | $91.5 + 0.9$   | $61.8 + 0.6$     | $65.0 + 0.8$   |  |

SelecMix has two versions; we choose the LfF-based [\(Nam et al.,](#page-9-4) [2020\)](#page-9-4) version for its superior performance

model to split embeddings, which suffers from performance degradation when the dataset becomes more complex, thus causing the overal performance drop. This aligns with the observations in [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5). 2) While SelectMix consistently mitigates bias to some extent, it cannot fully rectify biases in synthetic datasets. For instance, SelectMix improves performance from 23.8% to 53.7% on CMNIST IPC 50. However, under the same bias conditions, standard training without dataset distillation achieves approximately >85% accuracy. This indicates that even state-of-the-art methods struggle to recover from biases amplified synthetic sets. This observation aligns with Figure [4,](#page-13-1) which illustrates the severe bias amplification to the point where there isn't a single unbiased sample (in this case, a digit with a different color) in the synthetic set. Consequently, de-biasing becomes impossible in such a scenario. In contrast, our method achieves 91.5% accuracy in this case, demonstrating the critical importance of debiasing during the dataset distillation procedure.

*Remark 1 While applying a state-of-the-art debiasing method typically enhances performance with a biased synthetic dataset, it still doesn't match up to the effectiveness of our KDE-based approach.*

2. Applying de-biasing methods to surrogate models. Many dataset distillation methods use surrogate models to distill information into synthetic sets. To mitigate bias, applying existing de-biasing methods to these surrogate models is another straightforward idea. Here, we explore the impact of applying de-biasing methods to surrogate models in DM and MTT. The experiments are conducted on CMNIST with 5% bias conflicting sample. Since DM tries to match the distribution in the embedding space, we first apply DFA [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) to separate the embedding into intrinsic (shape of the digits) and bias (color of the digits) parts. Then we have DM match only the intrinsic part. For MTT, we first generate the de-biased expert training trajectories using SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5). Then we have MTT match the de-biased training trajectories. The results are shown in Table Table [4.](#page-7-0) We observe that there is a slight performance increase for DM+DFA compared to the

<span id="page-7-0"></span>Table 4. Ablation study test accuracy (%) on applying de-biasing methods to surrogate models on CMNIST with 5% bias-conflict samples and IPC 1, 10 and 50.

|                 |                | <b>IPC</b>      |                |  |
|-----------------|----------------|-----------------|----------------|--|
| Method          | 1              | 10              | 50             |  |
| DM              | $25.3 + 0.3$   | $19.6 + 0.9$    | $23.8 + 1.3$   |  |
| $DM+DFA$        | $26.1 + 0.3$   | $20.5 \pm 0.5$  | $25.4 + 0.4$   |  |
| <b>MTT</b>      | $29.2 \pm 0.9$ | $47.7 + 0.8$    | $33.9 + 1.2$   |  |
| MTT+SelecMix    | $18.1 \pm 0.5$ | $29.9 \pm 0.8$  | $52.1 \pm 0.3$ |  |
| $DM+Ours$       | $32.2 + 1.0$   | $86.5 + 1.2$    | $91.5 \pm 0.9$ |  |
|                 |                |                 |                |  |
| 90              |                | Kernel variance |                |  |
| 80              |                | Temperature     |                |  |
| curacy(%)<br>70 |                |                 |                |  |
| 60              |                |                 |                |  |
|                 |                |                 |                |  |

<span id="page-7-1"></span> $\frac{8}{6}$  50 Testing  $\frac{1}{8}$ 20  $0.6$  $0.8$  $0.0$  $0.2$  $0.4$  $1.0$ 

Temperature/Kernel variance

Figure 3. Ablation study on Kernel variance and temperature on CMNIST with 5% bias-conflicting samples and IPC 10.

vanilla DM which validates that the embeddings matched includes less biases. However, as SelecMix points out, fully separating intrinsic and bias parts is challenging. Thus biases will still be distilled into the synthetic dataset even if we only perform DM on the intrinsic embeddings. For MTT+SelecMix, we see mixed results such as a 17.8% drop on IPC 10 and an 18.2% increase on IPC 50. We think the reason is that the de-biased expert training trajectories are not stable due to the use of auxiliary models. Although the final de-biased teacher model performs well, the intermediate training trajectories are hard for MTT to match.

*Remark 2 Our experiments reveal that biased features will still dominate even when using de-biased surrogate models during dataset distillation. This may be due to sub-optimal bias feature disentanglement and less stable matching trajectories.*

3. Ablation study on hyper-parameters We assess two hyper-parameters in our method, the kernel variance and temperature of N, on CMNIST with 5% bias-conflicting samples and IPC 10 and present the results in Figure [3.](#page-7-1) We use DM as the base method which achieves similar results but runs much faster than DSA. In our observation, a very small variance introduces noise to the estimation, while a large value prevents the algorithm from assigning more weight to bias samples, leading to degraded performances. In general, choosing  $\sigma^2$  to be 0.1 works well, so we fix it in all of our experiments.

<span id="page-7-2"></span>Table 5. Ablation study test accuracy (%) on creating de-biased dataset first using SelecMix and then applying dataset distillation method on it. The experiment is run on CMNIST with IPC 10.

|             | Bias-conflict Ratio |                |                |  |  |  |  |
|-------------|---------------------|----------------|----------------|--|--|--|--|
| Method      | $1\%$               | 2%             | 5%             |  |  |  |  |
| DМ          | $18.6 \pm 0.2$      | $18.5 \pm 0.6$ | $19.6 \pm 0.9$ |  |  |  |  |
| SelecMix+DM | $44.6 \pm 1.0$      | $25.6 \pm 0.9$ | $65.6 \pm 0.8$ |  |  |  |  |
| $DM+Ours$   | $64.9 + 0.3$        | $50.6 + 1.2$   | $86.5 + 1.2$   |  |  |  |  |

We also study the impact of softmax temperature when normalizing scores in Equation [\(3\)](#page-3-3). When the temperature goes up, the weights are more evenly distributed among all samples. When the temperature goes down, more weights are given to samples that are further away from the rest of the samples. As shown in the figure, best performances are achieved around 0.1 which is the default setting.

*Remark 3 It can be seen that our method is robust to different hyper-parameter settings.*

4. Ablation study on learning from de-biased dataset Recent state-of-the-art de-biasing methods such as DFA [\(Lee](#page-9-5) [et al.,](#page-9-5) [2021\)](#page-9-5) and SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) try to train the de-biased model by creating a less biased data representation. This leads us to wonder: Can we follow the same procedure and have DD methods train on de-biased representations generated by these methods? In this ablation study, we run DM on de-biased dataset generated by one of the recent SOTA methods SelecMix and show the results in Table [5.](#page-7-2) The experiment is conducted on CMNIST with IPC 10 and varying bias-conflict ratios. As we can see from the table that the performances are greatly boosted after creating a less biased representation and have DM learn from it. This aligns with our earlier observation that dataset distillation methods can effectively learn from unbiased datasets. However, its performance still falls short compared to our KDE based method. We think the reason is that KDE estimates a smoothed distribution of the biased and unbiased samples while SelecMix combines two data points with lowest similarity (e.g. intra-class) which depends heavily on the auxiliary model quality.

*Remark 4 Creating an unbiased dataset first and then applying dataset distillation methods on it also greatly boost the model performance. However it still falls short by a large margin compared to our method.*

# 7. Conclusion

This paper conducts the first analysis of dataset bias in dataset distillation. Our findings show that bias type greatly influences distillation behavior (amplification vs. suppression). Then we introduce a debiasing method using reweight and kernel density estimation which substantially reduces retained bias in distilled datasets. We assess our debiasing method on various benchmark datasets with different bias ratios and IPC values and empirically verify the effectiveness of our method. In summary, our study offers insights into bias in dataset distillation, presents a practical algorithm for better performance and paves the way for future research on bias mitigation.

Limitations. Although many dataset distillation methods rely on the matching of real and synthetic dataset through carefully designed objective functions, there are methods such as MTT where our method cannot be easily applied due to the use of expert training trajectories instead of real images directly. This will be the focus of our future research.

# Impact Statement

Our paper will have the following positive social impacts: 1) We are the first revealing bias amplification effect in data distillation processes, which can prevent the misuse of such technique. 2) We propose a simple yet effective way to mitigate bias when data distillation is used, which can lead to unbiased decisions when employing those models.

# Acknowledgements

The work is partially supported by NSF 2048280, 2331966, 2325121, 2244760 and ONR N00014-23-1-2300.

# References

- <span id="page-8-8"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750– 4759, 2022.
- <span id="page-8-11"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 3739– 3748, 2023.
- <span id="page-8-13"></span>Chen, T., Kornblith, S., Norouzi, M., and Hinton, G. A simple framework for contrastive learning of visual representations. In *International conference on machine learning*, pp. 1597–1607. PMLR, 2020.
- <span id="page-8-4"></span>Cui, J., Wang, R., Si, S., and Hsieh, C.-J. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pp. 6565– 6590. PMLR, 2023.
- <span id="page-8-14"></span>Deng, L. The mnist database of handwritten digit images for machine learning research. *IEEE Signal Processing Magazine*, 29(6):141–142, 2012.

- <span id="page-8-10"></span>Deng, Z. and Russakovsky, O. Remember the past: Distilling datasets into addressable memories for neural networks. *Advances in Neural Information Processing Systems*, 35:34391–34404, 2022.
- <span id="page-8-3"></span>Du, J., Jiang, Y., Tan, V. Y., Zhou, J. T., and Li, H. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 3749–3758, 2023.
- <span id="page-8-7"></span>Du, J., Shi, Q., and Zhou, J. T. Sequential subset matching for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-8-2"></span>Feng, Q., Jiang, Z., Li, R., Wang, Y., Zou, N., Bian, J., and Hu, X. Fair graph distillation. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023a.
- <span id="page-8-6"></span>Feng, Y., Vedantam, S. R., and Kempe, J. Embarrassingly simple dataset distillation. In *The Twelfth International Conference on Learning Representations*, 2023b.
- <span id="page-8-0"></span>Gu, J., Wang, K., Jiang, W., and You, Y. Summarizing stream data for memory-restricted online continual learning. *arXiv preprint arXiv:2305.16645*, 2023.
- <span id="page-8-9"></span>Guo, Z., Wang, K., Cazenavette, G., Li, H., Zhang, K., and You, Y. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-8-15"></span>He, K., Zhang, X., Ren, S., and Sun, J. Deep residual learning for image recognition. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, June 2016a.
- <span id="page-8-16"></span>He, K., Zhang, X., Ren, S., and Sun, J. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016b.
- <span id="page-8-17"></span>Hendrycks, D. and Dietterich, T. Benchmarking neural network robustness to common corruptions and perturbations, Mar 2019.
- <span id="page-8-12"></span>Huang, C.-Y., Jin, R., Zhao, C., Xu, D., and Li, X. Federated virtual learning on heterogeneous data with local-global distillation. *arXiv preprint arXiv:2303.02278*, 2023.
- <span id="page-8-5"></span>Hwang, I., Lee, S., Kwak, Y., Oh, S. J., Teney, D., Kim, J.-H., and Zhang, B.-T. Selecmix: Debiased learning by contradicting-pair sampling. *Advances in Neural Information Processing Systems*, 35:14345–14357, 2022.
- <span id="page-8-1"></span>Jin, W., Zhao, L., Zhang, S., Liu, Y., Tang, J., and Shah, N. Graph condensation for graph neural networks. *arXiv preprint arXiv:2110.07580*, 2021.

- <span id="page-9-22"></span>Karras, T., Laine, S., and Aila, T. A style-based generator architecture for generative adversarial networks. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 4401–4410, 2019.
- <span id="page-9-20"></span>Khosla, P., Teterwak, P., Wang, C., Sarna, A., Tian, Y., Isola, P., Maschinot, A., Liu, C., and Krishnan, D. Supervised contrastive learning. *Advances in neural information processing systems*, 33:18661–18673, 2020.
- <span id="page-9-11"></span>Kim, J.-H., Kim, J., Oh, S. J., Yun, S., Song, H., Jeong, J., Ha, J.-W., and Song, H. O. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102– 11118. PMLR, 2022.
- <span id="page-9-24"></span>Krizhevsky, A., Hinton, G., et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-9-19"></span>Lee, H. B., Lee, D. B., and Hwang, S. J. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022.
- <span id="page-9-5"></span>Lee, J., Kim, E., Lee, J., Lee, J., and Choo, J. Learning debiased representation via disentangled feature augmentation. *Advances in Neural Information Processing Systems*, 34:25123–25133, 2021.
- <span id="page-9-6"></span>Li, G., Zhao, B., and Wang, T. Awesome dataset distillation. [https://github.com/Guang000/](https://github.com/Guang000/Awesome-Dataset-Distillation) [Awesome-Dataset-Distillation](https://github.com/Guang000/Awesome-Dataset-Distillation), 2022.
- <span id="page-9-3"></span>Li, Y. and Vasconcelos, N. Repair: Removing representation bias by dataset resampling. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 9572–9581, 2019.
- <span id="page-9-13"></span>Liu, S. and Wang, X. Few-shot dataset distillation via translative pre-training. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 18654– 18664, 2023.
- <span id="page-9-18"></span>Liu, S., Wang, K., Yang, X., Ye, J., and Wang, X. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022.
- <span id="page-9-21"></span>Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., and You, Y. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-9-15"></span>Liu, Z., Zeng, C., and Zheng, G. Graph data condensation via self-expressive graph structure reconstruction. *arXiv preprint arXiv:2403.07294*, 2024.
- <span id="page-9-23"></span>Lloyd, S. Least squares quantization in pcm. *IEEE transactions on information theory*, 28(2):129–137, 1982.

- <span id="page-9-9"></span>Loo, N., Hasani, R., Amini, A., and Rus, D. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35: 13877–13891, 2022.
- <span id="page-9-0"></span>Loo, N., Hasani, R., Lechner, M., and Rus, D. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023.
- <span id="page-9-17"></span>Medvedev, D. and D'yakonov, A. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *International Conference on Analysis of Images, Social Networks and Texts*, pp. 138–150. Springer, 2021.
- <span id="page-9-4"></span>Nam, J., Cha, H., Ahn, S., Lee, J., and Shin, J. Learning from failure: De-biasing classifier from biased classifier. *Advances in Neural Information Processing Systems*, 33: 20673–20684, 2020.
- <span id="page-9-10"></span>Nguyen, T., Chen, Z., and Lee, J. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2020.
- <span id="page-9-2"></span>Sagawa, S., Koh, P. W., Hashimoto, T. B., and Liang, P. Distributionally robust neural networks for group shifts: On the importance of regularization for worst-case generalization. *arXiv preprint arXiv:1911.08731*, 2019.
- <span id="page-9-8"></span>Sajedi, A., Khaki, S., Amjadian, E., Liu, L. Z., Lawryshyn, Y. A., and Plataniotis, K. N. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 17097–17107, 2023.
- <span id="page-9-12"></span>Shin, D., Shin, S., and Moon, I.-C. Frequency domainbased dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-9-16"></span>Such, F. P., Rawal, A., Lehman, J., Stanley, K., and Clune, J. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pp. 9206–9216. PMLR, 2020.
- <span id="page-9-14"></span>Sun, P., Shi, B., Yu, D., and Lin, T. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. *arXiv preprint arXiv:2312.03526*, 2023.
- <span id="page-9-1"></span>Tommasi, T., Patricia, N., Caputo, B., and Tuytelaars, T. A deeper look at dataset bias. *Domain adaptation in computer vision applications*, pp. 37–55, 2017.
- <span id="page-9-7"></span>Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., and You, Y. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.

- <span id="page-10-13"></span>Wang, K., Gu, J., Zhou, D., Zhu, Z., Jiang, W., and You, Y. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-10-1"></span>Wang, R., Cheng, M., Chen, X., Tang, X., and Hsieh, C.-J. Rethinking architecture selection in differentiable nas. In *International Conference on Learning Representation*, 2021.
- <span id="page-10-0"></span>Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-10"></span>Wei, X., Cao, A., Yang, F., and Ma, Z. Sparse parameterization for epitomic dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-10-4"></span>Wu, X., Deng, Z., and Russakovsky, O. Multimodal dataset distillation for image-text retrieval. *arXiv preprint arXiv:2308.07545*, 2023.
- <span id="page-10-17"></span>Xiao, H., Rasul, K., and Vollgraf, R. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms, 2017.
- <span id="page-10-2"></span>Xiong, Y., Wang, R., Cheng, M., Yu, F., and Hsieh, C.-J. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 16323–16332, 2023.
- <span id="page-10-3"></span>Yang, E., Shen, L., Wang, Z., Liu, T., and Guo, G. An efficient dataset condensation plugin and its application to continual learning. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023.
- <span id="page-10-19"></span>Yang, M. and Kim, B. Benchmarking Attribution Methods with Relative Feature Importance. *CoRR*, abs/1907.09701, 2019.
- <span id="page-10-11"></span>Yin, Z., Xing, E., and Shen, Z. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-10-12"></span>Zhang, D. J., Wang, H., Xue, C., Yan, R., Zhang, W., Bai, S., and Shou, M. Z. Dataset condensation via generative model. *arXiv preprint arXiv:2309.07698*, 2023.
- <span id="page-10-8"></span>Zhang, H., Li, S., Wang, P., Zeng, D., and Ge, S. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 38, pp. 9314–9322, 2024a.
- <span id="page-10-14"></span>Zhang, Y., Zhang, T., Wang, K., Guo, Z., Liang, Y., Bresson, X., Jin, W., and You, Y. Navigating complexity: Toward lossless graph condensation via expanding window matching. *arXiv preprint arXiv:2402.05011*, 2024b.

- <span id="page-10-15"></span>Zhang, Z. and Sabuncu, M. Generalized cross entropy loss for training deep neural networks with noisy labels. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-10-7"></span>Zhao, B. and Bilen, H. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021a.
- <span id="page-10-6"></span>Zhao, B. and Bilen, H. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021b.
- <span id="page-10-16"></span>Zhao, B. and Bilen, H. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-10-5"></span>Zhao, G., Li, G., Qin, Y., and Yu, Y. Improved distribution matching for dataset condensation. 2023.
- <span id="page-10-18"></span>Zhou, B., Lapedriza, A., Khosla, A., Oliva, A., and Torralba, A. Places: A 10 million image database for scene recognition. *IEEE transactions on pattern analysis and machine intelligence*, 40(6):1452–1464, 2017.
- <span id="page-10-9"></span>Zhou, Y., Nezhadarya, E., and Ba, J. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.

## A. Appendix

<span id="page-11-0"></span>

## A.1. Extension to Other SOTA methods

Here we show that our method can be extended to other SOTA methods such as DREAM [\(Liu et al.,](#page-9-21) [2023\)](#page-9-21) and IDM [\(Zhao](#page-10-5) [et al.,](#page-10-5) [2023\)](#page-10-5) that's based on either DM or DSA. In DREAM, instead of sampling and matching random batches of real images, it applies Kmeans [\(Lloyd,](#page-9-23) [1982\)](#page-9-23) algorithm to select representative samples. However, if a dataset is dominated by biased samples, this will cause the bias to be amplified, thus making de-biasing harder. Same for IDM which applies extra operations on top of DM. Both DREAM and IDM shares similar loss with DSA and DM with extra loss to capture the information shared between the condensed dataset and the real dataset. Therefore, our method can naturally extend to these methods as well. See Table [6](#page-11-3) for the detailed performance boost.

<span id="page-11-3"></span>Table 6. Extend our method to recent SOTA methods DREAM and IDM. Results are run on CMNIST dataset with various bias-conflict ratios.

| Method          | Bias-conflict Ratio $(1.0\%)$ |                                                                   |    | Bias-conflict Ratio $(2.0\%)$                          |    |    | Bias-conflict Ratio $(5.0\%)$ |                           |              |
|-----------------|-------------------------------|-------------------------------------------------------------------|----|--------------------------------------------------------|----|----|-------------------------------|---------------------------|--------------|
|                 |                               | 10                                                                | 50 |                                                        | 10 | 50 |                               | 10                        | 50           |
| <b>IDM</b>      | $20.1 + 0.9$                  |                                                                   |    | $18.8+1.2$ $17.6+1.6$ $20.2+1.0$ $20.1+0.5$ $19.1+1.3$ |    |    | $20.8+0.9$ $22.3+1.1$         |                           | $26.5 + 1.5$ |
| <b>IDM+Ours</b> |                               | $26.1+1.0$ $54.1+0.3$ $72.2+0.1$ $27.8+1.0$ $35.2+0.1$ $55.7+0.1$ |    |                                                        |    |    | $30.9 + 0.5$                  | $40.4 + 0.4$              | 77.2+0.2     |
| <b>DREAM</b>    | $213+04$                      |                                                                   |    | $15.9+1.2$ $17.9+0.8$ $26.2+0.5$ $17.1+1.3$ $17.5+1.5$ |    |    | $23.7 + 1.2$                  | $30.8 + 1.5$              | $50.4 + 1.0$ |
| DREAM+Ours      | $270+12$                      |                                                                   |    | $56.6+0.8$ $73.8+1.1$ $27.4+1.1$ $38.3+1.1$ $58.5+0.1$ |    |    |                               | $40.1 + 0.5$ $50.0 + 1.2$ | $80.1 + 0.6$ |

<span id="page-11-1"></span>

## A.2. Experimental Setup

Following previous dataset distillation methods [\(Zhao & Bilen,](#page-10-7) [2021a;](#page-10-7) [Cazenavette et al.,](#page-8-8) [2022\)](#page-8-8), we use ConvNet as the model architecture. It has 128 filters with kernel size of  $3\times3$ . Then it's followed by instance normalization, RELU activation, and an average pooling layer. We use SGD as the optimizer with 0.01 learning rate. For the supervised contrastive model, we use ResNet18 [\(He et al.,](#page-8-16) [2016b\)](#page-8-16) following [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) with a projection head of 128 dimensions. Same as previous de-biasing works [\(Nam et al.,](#page-9-4) [2020;](#page-9-4) [Lee et al.,](#page-9-5) [2021;](#page-9-5) [Hwang et al.,](#page-8-5) [2022\)](#page-8-5), we evaluate our results by training a DNN model (same as distillation) on the synthetic dataset and measure its accuracy using the unbiased test set. The results are evaluated with IPC 1, 10 and 50. See Appendix [A.2](#page-11-1) in Appendix for more experiment details.

To avoid introducing biases during initialization, all synthetic images are initialized with random noise instead of randomly selected real images. For KDE, we fix kernel variance and temperature to be 0.1 across all datasets. All experiments are run on a single 48GB NVIDIA RTX A6000 GPU.

<span id="page-11-2"></span>

## A.3. Extremely low bias-conflict setting

<span id="page-11-4"></span>It's possible that in some datasets that bias-conflict samples are extremely rare such as 0.5% used in SelecMix [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5). Here we show that our method performs well under this setting as well. See Table [7.](#page-11-4)

| Dataset <b>IPC</b> |  | MTT DM DM+Ours DSA DSA+Ours                                                                        |              |
|--------------------|--|----------------------------------------------------------------------------------------------------|--------------|
|                    |  | $1 \quad 22.9 \pm 1.2 \quad 25.1 \pm 0.9 \quad 27.4 \pm 0.7 \quad 25.9 \pm 0.7 \quad 26.0 \pm 1.0$ |              |
| <b>CMNIST</b>      |  | $10\quad 26.8\pm1.8\quad 18.8\pm0.9\quad 40.0\pm1.2\quad 15.7\pm0.8$                               | $42.0 + 1.2$ |
|                    |  | $50\quad 20.7 \pm 1.0\quad 23.8 \pm 0.9\quad 49.0 \pm 1.0\quad 11.6 \pm 2.0$                       | $500+15$     |

Table 7. Test accuracy under extremely low bias-conflict ratios (0.5%).

## A.4. Experiment results on the original unbiased dataset

Here we show the experiment results of our method on the original unbiased dataset in Table [8.](#page-12-1)

# Mitigating Bias in Dataset Distillation

<span id="page-12-1"></span>

| Table 0. I chromainee comparison on original Datasets |         |                |                |                |  |  |  |  |
|-------------------------------------------------------|---------|----------------|----------------|----------------|--|--|--|--|
| Dataset                                               | Method  | IPC 1          | IPC 10         | $IPC$ 50       |  |  |  |  |
| <b>CMNIST</b>                                         | DM      | $87.3 \pm 0.4$ | $95.6 \pm 0.5$ | $97.2 \pm 0.7$ |  |  |  |  |
|                                                       | DM+Ours | $86.0 \pm 0.5$ | $95.8 \pm 0.8$ | $97.2 \pm 1.0$ |  |  |  |  |
| FashionMNIST                                          | DМ      | $72.5 \pm 0.5$ | $83.1 \pm 0.4$ | $86.2 \pm 1.0$ |  |  |  |  |
|                                                       | DM+Ours | $70.4 \pm 0.9$ | $82.4 \pm 0.7$ | $84.5 \pm 1.2$ |  |  |  |  |
| CIFAR-10                                              | DM      | $27.2 \pm 0.3$ | $49.5 \pm 0.6$ | $60.2 \pm 0.8$ |  |  |  |  |
|                                                       | DM+Ours | $27.1 \pm 0.2$ | $49.0 \pm 0.8$ | $60.8 \pm 0.9$ |  |  |  |  |

Table 8. Performance Comparison on original Datasets

<span id="page-12-0"></span>

## A.5. Datasets

We conduct experiments on 3 datasets, two of which are widely used in SOTA de-biasing methods to assess color and noise biases, while also introducing a novel dataset to evaluate background biases.

Colored MNIST (CMNIST): [\(Nam et al.,](#page-9-4) [2020\)](#page-9-4) introduces the Colored MNIST dataset by injecting color with random perturbation into the MNIST dataset [\(Deng,](#page-8-14) [2012\)](#page-8-14). Each digit will be associated with a specific color as its bias such as digit 0 being red and digit 4 being green. We evaluate our method under 3 bias conflicting ratios with  $1\%$  (54,509 bias aligned, 491 bias conflicting), 2% (54,014 bias aligned, 986 bias conflicting) and 5% (52,551 bias aligned, 2,449 bias conflicting).

Corrupted CIFAR-10: Generated from the regular CIFAR-10 dataset [\(Krizhevsky et al.,](#page-9-24) [2009\)](#page-9-24), Corrupted CIFAR-10 applies different corruptions [\(Hendrycks & Dietterich,](#page-8-17) [2019\)](#page-8-17) to the images in CIFAR-10 so that images from one class are associated with one type of corruption such as GaussianNoise or MotionBlur. We also evaluate our method under 3 bias conflicting ratios with 1% (44,527 bias aligned, 442 bias conflicting), 2% (44,145 bias aligned, 887 bias conflicting) and 5% (42,820 bias aligned, 2,242 bias conflicting).

Background Fashion-MNIST (BG FMNIST): Background bias, which results in an over-reliance on the background for predicting foreground objects, has been employed to assess interpretability methods in various prior studies [\(Yang &](#page-10-19) [Kim,](#page-10-19) [2019;](#page-10-19) [Zhou et al.,](#page-10-18) [2017\)](#page-10-18). Following this idea, we construct a new dataset biased in backgrounds by using Fashion-MNIST [\(Xiao et al.,](#page-10-17) [2017\)](#page-10-17) as foregrounds which include a training set of 60,000 examples and a test set of 10,000 examples. And MiniPlaces [\(Zhou et al.,](#page-10-18) [2017\)](#page-10-18) is used as backgrounds to introduce background biases such as T-shirt is associated with bamboo forest background, trouser is associated with livingroom background, etc. Similar to other biased datasets, we also conduct experiments in 3 settings including 1%, 2% and 5% bias conflicting samples.

BFFHQ: It is introduced in [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5) by curating a biased dataset from the FFHQ dataset [\(Karras et al.,](#page-9-22) [2019\)](#page-9-22). In this dataset, they focus on age and gender as key facial attributes, with age being the intrinsic attribute and gender as the bias attribute. The dataset is curated to exhibit a strong correlation between these two attributes. The dataset predominantly features females categorized as 'young' (aged between 10 to 29 years) and males as 'old' (aged between 40 to 59 years). Consequently, the majority of the samples in the dataset are composed of young women and old men, aligning with the intended bias. The dataset only comes with 0.5% bias-conflict samples. For more detailed info regarding the dataset, please refer to [\(Lee et al.,](#page-9-5) [2021\)](#page-9-5).

### A.6. Matching Training Trajectories

The objective function of MTT can be described as below

$$
\mathcal{L} = \|\hat{\theta}_{t+T} - \theta_{t+M}^*\|_2^2 / \|\theta_t^* - \theta_{t+M}^*\|_2^2.
$$
\n(5)

Where T is the number of synthetic data training steps and M is the expert model training steps using the whole dataset. As MTT tries to match the training trajectory of expert models and models trained using synthetic data, the real dataset is not needed during matching phase. Therefore, our reweighting scheme doesn't work directly in MTT. However, as shown in Table [4,](#page-7-0) we should still be able to mitigate bias for MTT through fixing the expert models. We leave this to future work.

<span id="page-13-1"></span>Image /page/13/Picture/1 description: The image displays two grids of handwritten digits, each grid containing 10 rows and 6 columns. The digits range from 0 to 9, with each row dedicated to a specific digit. The digits are presented in various colors, including red, orange, yellow, green, cyan, blue, purple, and pink. The left grid shows the digits in a more uniform color progression, while the right grid exhibits a greater variety of colors for each digit. The title above the grids reads "Mitigating Bias in Dataset Distillation."

Figure 4. Synthetic images from vanilla DM (left) vs Ours (right) distilled from CMNIST with 5% bias-conflict samples. The one synthesized by vanilla DM is dominated by the bias feature while ours includes a rich set of features from both biased and bias-conflict samples.

<span id="page-13-0"></span>

## A.7. Qualitative Analysis

Here we visualize the synthetic datasets produced by vanilla DM and DM+Ours on CMNIST with 5% bias-conflicting samples and IPC 10 in Figure [4.](#page-13-1) As shown, images synthesized by the vanilla DM completely ignores the unbiased samples due to the reasons explained in Section [4.1,](#page-3-4) causing the bias to be even more amplified than the original dataset (the original dataset has 5% unbiased samples such as green 0s or yellow 1s, the distilled dataset has 0%). When combined with our method, DM is able to identify and synthesize unbiased samples into the final synthetic dataset. Similar results can also be seen with the vanilla DSA and DSA+Ours in Appendix Appendix [A.14.](#page-15-2)

## A.8. Visualization of KDE

See Figure Figure [5](#page-14-0) for a visualization of KDE applied on a normal distribution. The dotted line is a true normal distribution. The histogram represents the observed data points and the red curve shows the density function estimated using KDE.

## A.9. Ablation study on cutting-off score

Here we conduct an ablation study on the cutoff score used when computing the sample weights, the results are shown in Figure [6.](#page-14-1) It can be seen that choosing a cutoff score that's too large or too small will hinder the de-biasing performance. Also our algorithm is not very sensitive to cutoff scores and the optimal performances can be achived with a wide range of cutoff scores.

### A.10. Implementation details of applying de-biasing methods to synthetic dataset

As mentioned before, one natural idea is to directly apply de-biasing methods on distilled synthetic dataset. Here we describe the implementation details. For the results of DM, it's acquired by directly running the vanilla DM on CMNIST dataset. For DM+DFA, we apply the DFA de-biasing methods on the distilled synthetic dataset from the original DM. For DM+SelecMix, we test the two methods proposed in [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5) which corresponds to directly applying SelecMix methods and appling it on top of LfF [\(Nam et al.,](#page-9-4) [2020\)](#page-9-4).

<span id="page-14-0"></span>Image /page/14/Figure/1 description: This is a histogram showing the distribution of data. The x-axis is labeled "X-axis" and ranges from -4 to 4. The y-axis is labeled "Density" and ranges from 0.00 to 0.40. The histogram bars are light blue. A red line represents the Kernel Density Estimation, and a dashed green line represents the True Distribution. Both the Kernel Density Estimation and the True Distribution closely follow the shape of the histogram, indicating a good fit.

Figure 5. KDE applied on a normal distribution.

<span id="page-14-1"></span>Image /page/14/Figure/3 description: The image displays a line graph plotting testing accuracy (%) against cutoff score. The x-axis is labeled "Cutoff score" and ranges from 0 to 250. The y-axis is labeled "Testing accuracy(%)" and ranges from 20 to 90. The line starts at approximately 36% accuracy at a cutoff score of 0, rises sharply to a peak of about 90% accuracy around a cutoff score of 80, and then gradually decreases to about 85% accuracy at a cutoff score of 200. After 200, the accuracy drops significantly, reaching approximately 17% at a cutoff score of 250.

Figure 6. Ablation study on cutoff score. The experiment is conducted on CMNIST with 5% bias-conflicting samples and IPC 10.

<span id="page-15-0"></span>Image /page/15/Picture/1 description: The image displays two rows of images. The first row shows the digits 0, 1, 2, 3, and 4, colored red, orange, yellow, green, and lime green, respectively. Following these digits are several images of objects, some of which are partially obscured by black pixels. The second row mirrors the first row with the same colored digits 0 through 4. The subsequent images in the second row are pixelated and abstract representations of objects, with some sections appearing as black voids.

Figure 7. Examples of original images (top) and synthetic images (bottom) from biased dataset.

### A.11. Implementation details of applying de-biasing method to surrogate model

For applying de-biasing methods to surrogate models, we mainly test two combinations. The first one is DM plus DFA. The reason this can potentially work is that DM tries to match the distribution of real data and synthetic data in the embedding space. DFA is a perfect fit for DM because DFA tries to separate the embeddings into the intrinsic parts and the bias parts. DM can choose to match only the intrinsic parts, thus getting rid of biases. For MTT, since it doesn't rely on real data during matching phase but the expert training trajectories. Therefore, the best way to mitigate bias in MTT synthesized datasets is to debias the expert training trajectories. Thus we choose to apply the most recent SOTA model de-biasing method to acquire the expert trajectories first. Then we have MTT match these de-biased trajectories.

<span id="page-15-1"></span>

## A.12. Computation time for KDE

Similar to [\(Hwang et al.,](#page-8-5) [2022\)](#page-8-5), computing the embedding distance between samples adds extra computational cost. This applies to our method as well. For distribution based method such as DM, since it doesn't compute second order gradients, the whole distillation process is very fast. The time used to compute 10 matching iterations on CMNIST for the vanilla DM is 1.0 second while the time used to compute 10 matching iterations of DM+KDE is 1.9 seconds. However, note that DM usually takes less than 10,000 iterations to converge which is around 16.7 minutes. Compared to other dataset distillation based methods such as DSA that takes hours to finish on the same dataset, DM+KDE can converge quickly even with the added computation. For DSA, sicne it computes second order gradients, it takes around 55 seconds for each 10 iterations. With KDE, it takes 65 seconds. Therefore, computing KDE adds around 18.2% computation time. We usually observe significant performance improvement at the early training epochs when combining our method with the vanilla method. Note that generating the Supervised Contrastive model is fast, e.g. it takes less than 30 minutes to train it on CMNIST. All the data are measured on the same hardware as our experiments.

The computation of KDE can be greatly reduced by caching or grouping the data points. Based on our findings mentioned in the main paper, we observe that most of the biased samples tend to be close to each other in the SupCon embedding space and most of the unbiased samples are far away from this majority group. Therefore, there is no need to compute their distance within these biased samples and their distance with unbiased samples repeatedly. Thus, by pre-computing an estimated grouping information and reuse the intra-group and inter-group distance, the whole KDE process can be greatly reduced. We leave this to our future work.

### A.13. More qualitative results

First of all, we show the results under IPC 1 in Appendix [A.13.](#page-16-0) It can be seen that images synthesized by the vanilla DM is dominated by the biased samples such as red 0s and green 4s. On the contrary, the ones produced by our method is a fusion of both biased samples and unbiased samples, thus mitigating the biases in synthetic datasets. Similar results can also be seen from gradient based method in Appendix [A.13](#page-16-0) where the single image synthesized by DSA is also a fusion of both biased and unbiased samples.

### <span id="page-15-2"></span>A.14. Example Synthetic Images

<span id="page-16-0"></span>Image /page/16/Picture/1 description: The image displays two rows of digits from 0 to 9. The top row shows each digit in a different vibrant color against a black background: 0 in red, 1 in orange, 2 in yellow, 3 in green, 4 in bright green, 5 in cyan, 6 in blue, 7 in purple, 8 in magenta, and 9 in pink. The bottom row shows the same digits, but they appear to be reconstructions or variations, with each digit composed of multiple colors and a slightly more pixelated or textured appearance, also against a black background.

Figure 8. Synthetic images from original DM (top left) vs DM+Ours (bottom left) and original DSA (top right) vs DSA+Ours (bottom right). Results are generated from CMNIST with 5% bias-conflicting samples and IPC 1.

Image /page/16/Figure/3 description: A grid of synthesized digits from 0 to 9, with each row dedicated to a specific digit. The digits are displayed in various colors, including red, orange, yellow, green, cyan, blue, purple, and pink, against a black background. Each row contains multiple instances of the same digit, showcasing variations in color and slight distortions. The overall arrangement suggests a dataset of colorful, generated numerical characters.

Figure 9. Synthesized dataset by DM on CMNIST with 5% bias-conflicting samples.

Image /page/17/Picture/1 description: A grid of 50 images, each displaying a handwritten digit from 0 to 9. The digits are arranged in 10 rows and 5 columns. Each digit is rendered in a vibrant, multi-colored style against a black background. The colors vary for each digit and across the grid, with hues of red, orange, yellow, green, blue, and purple visible. The digits themselves are clearly recognizable, with variations in style and color saturation. The overall impression is a colorful and diverse collection of synthesized digits.

Figure 10. Synthesized dataset by DSA on CMNIST with 5% bias-conflicting samples.

Image /page/18/Picture/1 description: A grid of 60 images, each displaying a handwritten digit from 0 to 9. The digits are arranged in rows, with each row dedicated to a specific digit. The first row shows variations of the digit 0, the second row shows variations of the digit 1, and so on, up to the tenth row showing variations of the digit 9. Each digit is presented in multiple slightly different forms, suggesting a dataset of synthesized or augmented examples. The colors of the digits vary, with some appearing in red, yellow, green, blue, and pink, often with a colorful, almost iridescent quality. The background of each image is black, and the digits are centered within their respective squares. The overall impression is a collection of diverse representations of digits.

Figure 11. Synthesized dataset by MTT on CMNIST with 5% bias-conflicting samples.

Image /page/19/Picture/1 description: A grid of colorful digits from 0 to 9 is displayed against a black background. Each digit is rendered in a vibrant, multi-colored style, with different colors like red, orange, yellow, green, blue, and purple appearing within each digit. The digits are arranged in rows and columns, with each row dedicated to a specific digit. For example, the first row shows multiple instances of the digit '0', the second row shows '1's, and so on, down to the tenth row displaying '9's. The overall impression is a visually striking representation of numerical data, likely generated or synthesized, with each digit exhibiting a unique color gradient.

Figure 12. Synthesized dataset by DM+Ours on CMNIST with 5% bias-conflicting samples.

Image /page/20/Picture/1 description: A grid of colorful handwritten digits from 0 to 9 is displayed. Each row is dedicated to a specific digit, with multiple instances of that digit shown across the row. The digits are rendered in a variety of vibrant colors, including green, blue, purple, red, orange, and yellow, against a black background. The overall impression is a visually striking collection of stylized numerical characters.

Figure 13. Synthesized dataset by DSA+Ours on CMNIST with 5% bias-conflicting samples.

Image /page/21/Picture/1 description: The image displays a grid of 60 images, arranged in 6 rows and 10 columns. Each image is a silhouette of an article of clothing or an accessory against a background. The top row features silhouettes of t-shirts with varied backgrounds, some green and leafy, others more abstract. The second row shows silhouettes of pants, with indoor backgrounds visible. The third and fourth rows display silhouettes of jackets or sweaters, with diverse backgrounds including some with yellow and blue hues. The fifth row presents silhouettes of shoes, with backgrounds that appear to be outdoor scenes, including roads and pavements. The bottom row also shows silhouettes of shoes, with similar outdoor backgrounds. The overall impression is a dataset of synthesized clothing items with different backgrounds.

Figure 14. Synthesized dataset by DM on BG FMNIST with 5% bias-conflicting samples.

Image /page/22/Picture/1 description: The image displays a grid of synthesized fashion items, likely generated by a machine learning model. The grid is organized into rows and columns, with each cell containing a small image of an article of clothing or footwear. The items appear to be primarily black and are presented against varied backgrounds, some of which are abstract or blurred. The items include t-shirts, jackets, pants, shoes, and handbags. The overall impression is a collection of generated fashion samples, possibly for a dataset or a generative model demonstration.

Figure 15. Synthesized dataset by DSA on BG FMNIST with 5% bias-conflicting samples.

Image /page/23/Picture/1 description: A grid of 80 images, each 10x10 pixels, displays abstract, colorful patterns. The images are arranged in 8 rows and 10 columns, with black borders separating each individual image. The overall impression is a mosaic of diverse, somewhat blurry, and often symmetrical visual elements, with a prevalence of pastel colors like pink, blue, green, and yellow, interspersed with darker tones. Some images show more defined shapes, resembling distorted human figures or architectural elements, while others are purely abstract textures. The figure is captioned 'Figure 16. Synthesized dataset by MTT on BG-EMNIST with 5% bias, conflicting samples'.

Figure 16. Synthesized dataset by MTT on BG FMNIST with 5% bias-conflicting samples.

Image /page/24/Picture/1 description: The image displays a grid of 60 small images, arranged in 6 rows and 10 columns. Each small image appears to be a synthesized fashion item, possibly generated by a machine learning model. The items include tops, bottoms, dresses, shoes, and bags. The background of many of the images is a blurry green, suggesting an outdoor or natural setting. Some images are darker and more abstract, while others are clearer and show more detail of the clothing or accessory. The overall impression is a diverse collection of fashion items presented in a grid format.

Figure 17. Synthesized dataset by DM+Ours on BG FMNIST with 5% bias-conflicting samples.

Image /page/25/Picture/1 description: The image displays a grid of 80 synthesized fashion items, arranged in 8 rows and 10 columns. The items appear to be clothing and shoes, predominantly in dark colors, set against a background with hints of green and blue, suggesting an outdoor or natural setting. The top four rows showcase various tops and outfits, including t-shirts, sweaters, and pants. The bottom four rows feature footwear, such as sneakers and boots. The overall impression is a collection of generated fashion samples, likely from a machine learning model, with some variations in clarity and detail across the grid.

Figure 18. Synthesized dataset by DSA+Ours on BG FMNIST with 5% bias-conflicting samples.