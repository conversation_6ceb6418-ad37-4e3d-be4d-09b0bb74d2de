# <span id="page-0-0"></span>MULTI-SOURCE DOMAIN ADAPTATION MEETS DATASET DISTILLATION THROUGH DATASET DICTIONARY LEARNING

A PREPRINT

<PERSON>A, List Université Paris-Saclay F-91120 <PERSON>eau, France

<PERSON>, List Université Paris-Saclay F-91120 Palaiseau, France

Antoine Souloumiac CEA, List Université Paris-Saclay F-91120 Palaiseau, France

# ABSTRACT

In this paper, we consider the intersection of two problems in machine learning: Multi-Source Domain Adaptation (MSDA) and Dataset Distillation (DD). On the one hand, the first considers adapting multiple heterogeneous labeled source domains to an unlabeled target domain. On the other hand, the second attacks the problem of synthesizing a small summary containing all the information about the datasets. We thus consider a new problem called MSDA-DD. To solve it, we adapt previous works in the MSDA literature, such as Wasserstein Barycenter Transport and Dataset Dictionary Learning, as well as DD method Distribution Matching. We thoroughly experiment with this novel problem on four benchmarks (Caltech-Office 10, Tennessee-Eastman Process, Continuous Stirred Tank Reactor, and Case Western Reserve University), where we show that, even with as little as 1 sample per class, one achieves state-of-the-art adaptation performance.

*Keywords* Optimal Transport · Dataset Distillation · Domain Adaptation · Dictionary Learning

# 1 Introduction

In modern Machine Learning (ML) practice, researchers face the challenge of reasoning about large-scale, heterogeneous datasets. This situation is challenging, as intuitive geometric concepts lose sense in high dimensions, and the computational cost of processing large amounts of data is often prohibitive. As such, [Wang et al.](#page-6-0) [\(2018\)](#page-6-0) proposed Dataset Distillation (DD), a novel field of ML that seeks to synthesize a small dataset summary while retaining as much information as possible.

Nonetheless, current works in DD still need to consider the heterogeneity present in datasets. An example of such a phenomenon occurs in Multi-Source Domain Adaptation (MSDA) [Crammer et al.](#page-6-1) [\(2008\)](#page-6-1), where datasets contain multiple domains that follow different but related probability distributions. In this context, previous algorithms [Montesuma and](#page-6-2) [Mboula](#page-6-2) [\(2021b,](#page-6-2)[a\)](#page-6-3); [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4) leverage Wasserstein barycenters [Agueh and Carlier](#page-6-5) [\(2011\)](#page-6-5) for performing MSDA. As we argue in this paper, this mechanism can be used for distillation.

In this paper, we propose to bridge MSDA and DD, i.e., performing MSDA while summarizing the target domain. We call this new problem MSDA-DD. To this end, we adapt previous MSDA methods, such as Wasserstein Barycenter Transport (WBT) [Montesuma and Mboula](#page-6-2) [\(2021b](#page-6-2)[,a\)](#page-6-3) and Dataset Dictionary Learning (DaDiL) [Fernandes Montesuma](#page-6-4) [et al.](#page-6-4) [\(2023a\)](#page-6-4), and DD method Distribution Matching (DM) [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6) to our setting. To the best of our knowledge, this is the first paper considering MSDA and DD simultaneously.

In the following, section [2](#page-1-0) discusses previous work on DD and MSDA. Section [3](#page-1-1) presents our methodology. Section [4](#page-3-0) shows and discusses our experiments. Finally, section [5](#page-5-0) concludes this paper.

## <span id="page-1-0"></span>2 Related Work

[DD](#page-0-0) is a novel problem in [ML,](#page-0-0) founded by [Wang et al.](#page-6-0) [\(2018\)](#page-6-0), whose goal is to synthesize a small set of samples, which retain the information of the whole original dataset. So far, most techniques focus on distilling single datasets to train neural networks [Zhao and Bilen](#page-6-7) [\(2021\)](#page-6-7); [Lee et al.](#page-6-8) [\(2022\)](#page-6-8). In this sense, [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6) proposed a method where data summaries are synthesized by *matching probability distributions*. Nonetheless, none of these works consider the case of distillation of *closely related datasets* characterized by a shift in their probability distributions. In the following, we explore how to leverage the similarities in similar datasets to enhance the power of data summaries, i.e., being capable of synthesizing a few effective samples for [Domain Adaptation \(DA\).](#page-0-0)

In parallel, [MSDA](#page-0-0) [Crammer et al.](#page-6-1) [\(2008\)](#page-6-1) proposes adapting multiple domains with heterogeneous but related probability distributions towards an unlabeled target domain. This problem was previously explored by [Montesuma and Mboula](#page-6-2) [\(2021b](#page-6-2)[,a\)](#page-6-3) and [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4) through *Wasserstein barycenters* [Agueh and Carlier](#page-6-5) [\(2011\)](#page-6-5), i.e., calculating an equidistant distribution of sources in a Wasserstein space. An essential feature of empirical, free-support Wasserstein barycenters, as proposed by [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4), is that the number of samples in their support is a hyper-parameter. As we explore in this work, we can leverage this property for [MSDA](#page-0-0) and [DD.](#page-0-0)

<span id="page-1-1"></span>

## 3 Methodology

### 3.1 Classification and Domain Adaptation

Classification is a sub-problem in supervised learning, formalized through the [Empirical Risk Minimization \(ERM\)](#page-0-0) principle. For a distribution Q, a ground-truth  $h_0 : \mathbb{R}^d \to \{1, \dots, n_c\}$  and a loss function  $\mathcal{L}$ , let  $\mathbf{x}_i^{(Q)} \stackrel{i.i.d.}{\sim} Q$  and  $y_i^{(Q)} = h_0(\mathbf{x}_i^{(Q)})$ . A classifier  $h \in \mathcal{H}$  is said to fit the data, if,

$$
\hat{h} = \mathop{\rm argmin}_{h \in \mathcal{H}} \hat{\mathcal{R}}_Q(h) = \frac{1}{n} \sum_{i=1}^n \mathcal{L}(h(\mathbf{x}_i^{(Q)}), y_i^{(Q)}).
$$

where  $\hat{R}_Q$  is the empirical approximation for the *true risk*  $R_Q$ . This framework assumes that train and test data follow a single distribution Q. [DA](#page-0-0) relaxes this assumption, by defining a domain as a pair  $(\mathcal{X}, Q(X))$  of a feature space and a distribution. Assuming  $\mathcal{X} = \mathbb{R}^d$ , domains differ when there is a shift  $Q_S(X) \neq Q_T(X)$  in the distributions. As a consequence, classifiers fit with data from  $Q_S$  may not be appropriate for data from  $Q_T$ .

**Problem Description.** [MSDA](#page-0-0) presupposes  $Q_S = \{Q_{S_\ell}\}_{\ell=1}^{N_S}$  of source distributions s.t.  $Q_{S_i} \neq Q_{S_j}$ ,  $\forall i \neq j$ , and  $Q_T$  s.t.  $Q_T \neq Q_{S_i}$ ,  $\forall i$ . Even though  $Q_{S_\ell}$  and  $Q_T$  are not available, we have access to  $(\mathbf{x}_i^{(Q_{S_\ell})})$  $\binom{(Q_{S_\ell})}{i}, y_i^{(Q_\ell)}) \stackrel{i.i.d.}{\sim} Q_{S_\ell}$  and  $\mathbf{x}_{j}^{(Q_T)} \stackrel{i.i.d.}{\sim} Q_T$ , i.e., the target domain is *unlabeled*. We use labeled samples from sources and unlabeled samples from the target for creating a summary of labeled samples in the target domain.

#### 3.2 Metric and Barycenters of Probability Distributions

In this section we introduce the Wasserstein [Villani et al.](#page-6-9) [\(2009\)](#page-6-9) and the [Maximum Mean Discrepancy \(MMD\)](#page-0-0) [Gretton](#page-6-10) [et al.](#page-6-10) [\(2012\)](#page-6-10) distances, an [Optimal Transport \(OT\)-](#page-0-0)based and a kernel-based metric, respectively. We refer readers to [Peyré et al.](#page-6-11) [\(2019\)](#page-6-11); [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4) for a broader context of [OT,](#page-0-0) and [Sriperumbudur et al.](#page-6-12) [\(2012\)](#page-6-12) for a review on metrics between distributions.

On the one hand, under a linear kernel, the [MMD](#page-0-0) is defined by,

<span id="page-1-3"></span>
$$
MMD(P, Q) = \|\mu^{(P)} - \mu^{(Q)}\|_2,
$$
\n(1)

where  $\mu^{(P)} = \int x dP$ . On the other hand, let  $\Pi(P, Q)$  denote the set of distributions with marginals P and Q. Then,

<span id="page-1-4"></span>
$$
W_c(P,Q) = \underset{\pi \in \Pi(P,Q)}{\text{arginf}} \int c(\mathbf{x}^{(P)}, \mathbf{x}^{(Q)}) d\pi,
$$
\n(2)

where c is called *ground-cost* (e.g.,  $W_2$ , where c is the Euclidean distance). In many [ML](#page-0-0) applications, P and Q are unknown. One then resorts to *empirical approximations* via samples  $\mathbf{x}_i^{(P)} \stackrel{i.i.d.}{\sim} P$  and  $\mathbf{x}_j^{(Q)} \stackrel{i.i.d.}{\sim} Q$ , so that,

<span id="page-1-2"></span>
$$
\hat{P}(\mathbf{x}) = \frac{1}{n} \sum_{i=1}^{n} \delta(\mathbf{x} - \mathbf{x}_i^{(P)}),
$$
\n(3)

where  $\delta$  is the Dirac delta function and  $\mathbf{X}^{(P)} \in \mathbb{R}^{n \times d}$  is the support of  $\hat{P}$ . Under such approximation, one can calculate MMD( $\hat{P}$ ,  $\hat{Q}$ ) and  $W_c(\hat{P}, \hat{Q})$  by plugging-in eq. [3](#page-1-2) into [1](#page-1-3) and [2.](#page-1-4)

A particularly useful application of [OT](#page-0-0) is defining barycenters of distributions. This concept allows for the aggregation of a family of distributions, and was first introduced by [Agueh and Carlier](#page-6-5) [\(2011\)](#page-6-5),

**Definition 1.** For distributions  $\mathcal{P} = \{P_k\}_{k=1}^K$  and weights  $\alpha \in \Delta_K$ , the Wasserstein barycenter is a solution to,

$$
B^* = \mathcal{B}(\alpha; \mathcal{P}) = \inf_B \sum_{k=1}^K \alpha_k W_c(P_k, B). \tag{4}
$$

Wasserstein barycenters can be calculated for empirical distributions [Cuturi and Doucet](#page-6-13) [\(2014\)](#page-6-13); [Montesuma and Mboula](#page-6-2) [\(2021b](#page-6-2)[,a\)](#page-6-3), in which case it is empirical as well. Henceforth we use [\(Fernandes Montesuma et al., 2023a,](#page-6-4) Algorithm 1) for calculating  $\mathcal{B}(\alpha;\mathcal{P})$ . Wasserstein barycenters are interesting for synthesizing small summaries of distributions, since they have a free number of samples in their support.

#### 3.3 Dataset Distillation through Distribution Matching

[DD](#page-0-0) [Wang et al.](#page-6-0) [\(2018\)](#page-6-0) seeks to represent a dataset  $\{x_i^{(Q)}, y_i^{(Q)}\}_{i=1}^n$  through a new set of *synthetic* samples  $\{{\bf x}_j^{(P)}, y_j^{(P)}\}_{j=1}^m$ , also called data summary of Q, such that  $m \ll n$ . In mathematical terms, [DD](#page-0-0) seeks for a summary  $\hat{P}$ s.t. for  $\epsilon > 0$ , and  $h_0$  [Sachdeva and McAuley](#page-6-14) [\(2023\)](#page-6-14),

$$
\sup_{\mathbf{x}\sim Q} |\mathcal{L}(h_P(\mathbf{x}), h_0(\mathbf{x})) - \mathcal{L}(h_Q(\mathbf{x}), h_0(\mathbf{x}))| \le \epsilon,
$$

that is, when trained on the summary  $\hat{P}$ , a classifier has similar generalization performance (on Q) than when trained with  $\hat{Q}$ . Among the existing methods for [DD,](#page-0-0) [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6) proposed building  $\hat{P}$  through [DM.](#page-0-0) Let d denote a notion of discrepancy between probability distributions (e.g.  $W_2$ ). [DM](#page-0-0) seeks the summary  $\hat{P}$  closest to  $\hat{Q}$  *in distribution*,

$$
\hat{P}^* = \underset{\mathbf{x}_1^{(P)}, \cdots, \mathbf{x}_m^{(P)}}{\operatorname{argmin}} d(\hat{P}, \hat{Q}),\tag{5}
$$

where  $m =$  SPC  $\times n_c$ , for [Samples Per Class \(SPC\)](#page-0-0) and a number of classes  $n_c$ . In [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6), the authors use a [MMD-](#page-0-0)like metric between feature-label joint distributions,

$$
\text{MMD}_c(\hat{P}, \hat{Q}) = \sum_{c=1}^{n_c} ||\mu_c^{(P)} - \mu_c^{(Q)}||_2^2,
$$
\n(6)

where  $\mu_c^{(P)} \in \mathbb{R}^d$  (resp. *Q*) is the mean vector of samples  $\mathbf{x}_i^{(P)}$  belonging to class *c*.

#### 3.4 MSDA through Dataset Distillation

An advantage of free-support Wasserstein barycenters [\(Cuturi and Doucet, 2014,](#page-6-13) Algorithm 2), is that the support  ${\bf X}^{(B)}$  of  $\hat{B}$  has a free number of samples. This property was previously treated as a hyper-parameter in [MSDA](#page-0-0) works [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4). In this work, we investigate this feature through the lens of [DD,](#page-0-0) as it can be straightforwardly used to compress domains in [MSDA.](#page-0-0) In what follows, we describe 3 adaptations of previously proposed algorithms for [MSDA-DM.](#page-0-0)

Wasserstein Barycenter Transport. [Montesuma and Mboula](#page-6-2) [\(2021b](#page-6-2)[,a\)](#page-6-3) previously proposed to use Wasserstein barycenters for [MSDA.](#page-0-0) Their approach minimizes the following objective,

$$
\hat{P} = \underset{\{\mathbf{x}_{i}^{(P)}, y_{i}^{(P)}\}_{i=1}^{m}}{\text{argmin}} W_{2}(\hat{P}, \hat{Q}_{T}) + \sum_{\ell=1}^{N_{S}} W_{c}(\hat{P}, \hat{Q}_{S_{\ell}}),
$$

where  $W_2$  and  $W_c$  refer to Wasserstein distances with Euclidean ground-cost and  $C_{ij} = ||\mathbf{x}_i^{(P)} - \mathbf{x}_j^{(Q)}||_2^2 + \beta ||\mathbf{y}_i^{(P)} - \mathbf{y}_j^{(Q)}||_2^2$  $\mathbf{y}_{j}^{(Q)}||_{2}^{2}$ , for  $\beta \gg \max_{i,j} \|\mathbf{x}_{i}^{(P)} - \mathbf{x}_{j}^{(Q)}\|_{2}^{2}$ , respectively. As such, [WBT](#page-0-0) first calculates a Wasserstein barycenter of sources,  $\hat{B} = \mathcal{B}(\alpha; \mathcal{Q}_S)$ , with uniform weights  $\alpha_k = N_S^{-1}$ , then transports the barycenter to the target domain through a barycentric mapping [\(Courty et al., 2017,](#page-6-15) Eq. 14). Hence, the target domain is compressed through  $\hat{B}$ .

<span id="page-3-1"></span>Image /page/3/Figure/2 description: The image displays two diagrams illustrating different methods. Diagram (a), labeled "MSDA-DM and WBT," shows a central point labeled "P" connected by dashed lines to four other points. Three of these points are labeled "Q̂S1," "Q̂S2," and "Q̂S3," each represented by a stack of blue horizontal lines. The fourth point is labeled "Q̂T" and is represented by a stack of red horizontal lines. Diagram (b), labeled "DaDiL," depicts a triangular arrangement of three points labeled "P̂1," "P̂2," and "P̂3," connected by dashed lines. Within the triangle, there are several square shapes labeled "B̂1," "B̂2," "B̂3," and "B̂T," all colored dark blue. Also within the triangle, a stack of red horizontal lines labeled "Q̂T" is positioned near the center. Several stacks of blue horizontal lines labeled "Q̂S1," "Q̂S2," and "Q̂S3" are placed along the dashed lines connecting the P̂ points.

Figure 1: Conceptual illustration of [MSDA-DM](#page-0-0) methods, where sources  $\hat{Q}_{S_\ell}$  are labeled, and the target  $\hat{Q}_T$  is unlabeled. The distillation is done from true datasets  $\circledg$  towards data summaries (blue circles). We denote [DaDiL'](#page-0-0)s atoms by triangles. While [MSDA-DM](#page-0-0) and [WBT](#page-0-0) move the barycenter of true datasets towards the target domain, [DaDiL](#page-0-0) learns to express datasets as Wasserstein barycenters of atoms.

Dataset Condensation. We adapt the framework of [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6) for [MSDA.](#page-0-0) Instead of minimizing the distance between the summary  $\hat{P}$  and a single dataset  $\hat{Q}$ , the goal is to minimize,

$$
\hat{P} = \underset{\{\mathbf{x}_i^{(P)}\}_{i=1}^m}{\text{argmin}} \text{ MMD}(\hat{P}, \hat{Q}_T) + \sum_{\ell=1}^{N_S} \text{MMD}_c(\hat{P}, \hat{Q}_{S_{\ell}}).
$$

As we show conceptually in Fig. [1a,](#page-3-1) [MSDA-DM](#page-0-0) and [WBT](#page-0-0) are conceptually close, in which they move the barycenter of labeled distributions towards the target, using the MMD, and  $W_2$  respectively.

Dataset Dictionary Learning. In [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4), authors proposed doing [MSDA](#page-0-0) through [Dictionary Learning \(DiL\).](#page-0-0) As such, [DaDiL](#page-0-0) learns a set of atoms  $\mathcal{P} = \{\hat{P}_k\}_{k=1}^K$  and barycentric coordinates  $\mathcal{A} =$  $\{\alpha_\ell\}_{\ell=1}^{N_S+1}$ ,  $\alpha_\ell \in \Delta_K$  and  $\alpha_T := \alpha_{N_S+1}$ . Let  $\hat{B}_\ell = \mathcal{B}(\alpha_\ell; \mathcal{P})$ ,

$$
(\mathcal{P}^\star, \mathcal{A}^\star) = \underset{\mathcal{P}, \mathcal{A}}{\text{argmin}} \ W_2(\hat{Q}_T, \hat{B}_T) + \sum_{\ell=1}^{N_S} W_c(\hat{Q}_\ell, \hat{B}_\ell).
$$

We illustrate [DaDiL](#page-0-0) conceptually in Fig. [1b.](#page-3-1) Effectively, DaDiL learns how to express each dataset  $\hat{Q}_\ell$  as a Wasserstein barycenter of atoms, i.e.,  $\mathcal{B}(\alpha_\ell; \mathcal{P})$ . As a consequence, one can directly compress  $\hat{Q}_T$  by calculating  $\hat{B}_T = \mathcal{B}(\alpha_T; \mathcal{P})$ with  $n = SPC \times n_c$  points in its support.

<span id="page-3-0"></span>

## 4 Experiments and Discussion

In the following, we compare methods on 4 [MSDA](#page-0-0) benchmarks: (i) [Continuously Stirred Tank Reactor \(CSTR\)](#page-0-0) [Pilario](#page-6-16) [and Cao](#page-6-16) [\(2018\)](#page-6-16); [Fernandes Montesuma et al.](#page-6-17) [\(2022\)](#page-6-17), (ii) [Tennessee Eastmann Process \(TEP\)](#page-0-0) [Reinartz et al.](#page-6-18) [\(2021\)](#page-6-18); [Fernandes Montesuma et al.](#page-6-19) [\(2023b\)](#page-6-19), (iii) [Case Western Reserve University \(CWRU\)](#page-0-0)<sup>[1](#page-3-2)</sup> and (iv) [Caltech-Office 10](#page-0-0) [\(CO\)](#page-0-0) [Saenko et al.](#page-6-20) [\(2010\)](#page-6-20); [Griffin et al.](#page-6-21) [\(2007\)](#page-6-21). While (i-iii) are fault diagnosis benchmarks, (iv) is a standard benchmark in visual [DA.](#page-0-0) An overview is presented in table [1.](#page-4-0) The goal of tested algorithms is producing a small synthetic summary for the target domain.

For classification, we use a [Support Vector Machine \(SVM\)](#page-0-0) over extracted features. For the [CSTR,](#page-0-0) we use the norm of the power spectrum of each sensor data [Fernandes Montesuma et al.](#page-6-17) [\(2022\)](#page-6-17). For the [TEP, CWRU](#page-0-0) and [CO](#page-0-0) we use activations of neural networks, as in [Fernandes Montesuma et al.](#page-6-19) [\(2023b\)](#page-6-19) and [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4) respectively. All features are standardized to zero mean and unit variance.

<span id="page-3-2"></span><sup>1</sup> <https://engineering.case.edu/bearingdatacenter/download-data-file>

|  |  | Table 1: Overview of benchmarks used in our experiments. |
|--|--|----------------------------------------------------------|
|  |  |                                                          |

<span id="page-4-0"></span>

| Benchmark         | # Samples | # Domains | # Classes | # Features |
|-------------------|-----------|-----------|-----------|------------|
| CSTR              | 2860      | 7         | 13        | 7          |
| TEP               | 17289     | 6         | 29        | 128        |
| CWRU              | 24000     | 3         | 10        | 256        |
| Caltech-Office 10 | 2533      | 4         | 10        | 4096       |

In this setting, we compare 5 methods for [MSDA-DD:](#page-0-0) (i) random sampling (source-only), (ii) random sampling (targetonly), (iii) [WBT,](#page-0-0) (iv) [MSDA-DM](#page-0-0) and (v) [DaDiL.](#page-0-0) On one hand, methods (iii-v) constitute our proposed adaptations for [MSDA-](#page-0-0)dd. On the other hand, (i,ii) are standard baselines in [MSDA](#page-0-0) and [DD.](#page-0-0) For (i), no adaptation is done towards the target, thus it is an intrinsically worst-case scenario. For (ii), there is no distribution shift, which characterizes it as a best case scenario. In both (i,ii), we randomly sample  $n = SPC \times n_c$  samples from the overall data.

<span id="page-4-1"></span>Image /page/4/Figure/5 description: This image displays three radar charts, each representing different values of SPC (1, 10, and 50) under the CSTR condition. The axes of each chart are labeled TEP, CO, and CWRU. Five data series are plotted on each chart, indicated by different colored lines and markers: Source-Only (blue), WBT (orange), MSDA-DC (green), DaDiL (red), and Target-Only (purple). The radial grid lines represent values from 0 to 100, with markings at 20, 40, 60, 80, and 100. The charts show the performance of these five methods across the three specified SPC values.

Figure 2: Global comparison of [MSDA](#page-0-0) methods in a distillation setting, for 1, 10 and 50 [SPC.](#page-0-0)

First, we present an overview of our results in Fig. [2,](#page-4-1) as a function of [SPC.](#page-0-0) Globally, [DaDiL](#page-0-0) and [WBT](#page-0-0) are largely superior to the baseline and [MSDA-DM,](#page-0-0) especially in the fault diagnosis benchmarks. Surprisingly, the performance gap is more marked for small values of [SPC,](#page-0-0) indicating that these methods are able to provide better generalization. This remark shows that [MSDA](#page-0-0) is possible, even with as little as 1 sample for each class in the target domain.

Second, unlike [DD](#page-0-0) [Wang et al.](#page-6-0) [\(2018\)](#page-6-0); [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6), for an increasing  $SPC$ , [MSDA-DD](#page-0-0) methods do not converge to random sampling. Indeed, random sampling the source domain do not tackle the distributional shift problem. On the other hand one can expect random sampling the target domain to be optimal for large  $SPC$ (e.g., [CWRU](#page-0-0) and [CO](#page-0-0) in Fig. [2\)](#page-4-1). Nonetheless, as demonstrated in Fig. [2,](#page-4-1) [MSDA-DD](#page-0-0) are *sample efficient*, in the sense that they achieve high performance with as little as  $SPC = 1$ , which represents 0.04%, 0.16%, 0.39%, and 0.45% of the overall number of samples in [CWRU, TEP,](#page-0-0) [CO](#page-0-0) and [CSTR](#page-0-0) benchmarks.

Due to space constraints, we detail results only on the [TEP](#page-0-0) benchmark. We explore how adaptation evolves for various values of [SPC](#page-0-0) in the context of the 5 methods. Contrary to other benchmarks, on [TEP](#page-0-0) performance remains stable over the range  $SPC \in \{1, \dots, 50\}$ . On one hand, [WBT](#page-0-0) and [DaDiL](#page-0-0) have nearly equivalent performance, which agrees with previous research on this benchmark [Fernandes Montesuma et al.](#page-6-19) [\(2023b\)](#page-6-19). On the

<span id="page-4-2"></span>Image /page/4/Figure/10 description: The image is a scatter plot with a legend at the top indicating three categories: DaDiL (represented by black stars), WBT (represented by black triangles), and MSDA-DC (represented by dark red dots). The plot displays clusters of colored blobs, with each blob representing a data point. Within these colored blobs, the markers for DaDiL, WBT, and MSDA-DC are scattered, suggesting a visualization of data points categorized by these three labels. The colored blobs themselves appear to represent different classes or groups within the data, with colors ranging from yellow and orange to green, blue, red, and purple. The overall arrangement suggests a dimensionality reduction technique like t-SNE or UMAP has been applied to visualize the relationships between these data points.

Figure 3: UMAP projections of the [TEP](#page-0-0) data for mode 1, where each color represent a different class.

other hand, we are able to reach [State-of-the-Art \(SOTA\),](#page-0-0) and to improve over the optimistic target-only scenario with only 1% of target domain samples, or 0.16% of the overall number of samples.

Next, we focus on the performance gap between [WBT, DaDiL](#page-0-0) and [MSDA-DM.](#page-0-0) In Fig. [3](#page-4-2) we show the UMAP [McInnes](#page-6-22) [et al.](#page-6-22) [\(2018\)](#page-6-22) of target domain data, where the synthesized samples are highlighted. While [WBT](#page-0-0) and [DaDiL](#page-0-0) yield synthetic samples close to each other, [MSDA-DM](#page-0-0) generates synthetic samples positioned in the wrong class cluster. This phenomenon indicates that the Wasserstein distance is a better candidate for [DD.](#page-0-0) Indeed, while the *linear* [MMD](#page-0-0) is only able to match  $1^{st}$  order moments, the Wasserstein distance handles more complex distribution mismatch.

Image /page/5/Figure/4 description: This figure displays six subplots, each representing a different mode (Mode 1 through Mode 6). Each subplot is a line graph plotting accuracy on the y-axis against samples per class on the x-axis, ranging from 0 to 50. Five different methods are represented by distinct colored lines and markers: Baseline (blue diamonds), WBT (orange squares), MSDA-DC (green circles), DaDiL (purple circles), and Target-Only (red squares). In general, accuracy increases with the number of samples per class for all methods, with WBT and DaDiL consistently achieving the highest accuracy across all modes, often reaching above 80%. MSDA-DC also shows strong performance, typically above 70%. Baseline and Target-Only generally show lower accuracy, with Baseline starting around 40% and Target-Only around 60% and showing less improvement with more samples.

Figure 4: Classification accuracy as a function of [SPC,](#page-0-0) for the 6 domains in the [TEP](#page-0-0) benchmark. Error bars indicate 95% confidence intervals.

<span id="page-5-0"></span>

## 5 Conclusion

In this paper, we bridge two fields of [ML: MSDA](#page-0-0) and [DD.](#page-0-0) We propose a new problem, called [MSDA-DD,](#page-0-0) were concurrently to [MSDA](#page-0-0) one also seeks to summarize the unlabeled target domain with labeled samples, while retaining as much information as possible. To that end, we adapt 3 [SOTA](#page-0-0) methods, [Montesuma and Mboula](#page-6-2) [\(2021b](#page-6-2)[,a\)](#page-6-3)[,Fernandes Monte](#page-6-4)[suma et al.](#page-6-4) [\(2023a\)](#page-6-4), and [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6) to our setting. Data summaries generated by these methods capture knowledge from the multiple labeled source domains and the unlabeled target domain itself. We experiment extensively on 3 fault diagnosis benchmarks [\(CSTR, TEP,](#page-0-0) and [CWRU\)](#page-0-0) and 1 visual [DA](#page-0-0) benchmark (Caltech-Office 10).

Our experiments show a series of intriguing results. First, we achieve [SOTA](#page-0-0) performance through [WBT](#page-0-0) [Montesuma](#page-6-2) [and Mboula](#page-6-2) [\(2021b,](#page-6-2)[a\)](#page-6-3) and [DaDiL](#page-0-0) [Fernandes Montesuma et al.](#page-6-4) [\(2023a\)](#page-6-4) with only 1 sample per class. For instance, in the context of the [TEP](#page-0-0) benchmark, this represents only  $1\%$  of the samples in the target domain and 0.16% of the overall number of samples. Second, unlike previous studies in standard [DD](#page-0-0) [Wang et al.](#page-6-0) [\(2018\)](#page-6-0); [Zhao and Bilen](#page-6-6) [\(2023\)](#page-6-6), [MSDA](#page-0-0) with [DD](#page-0-0) is not equivalent to random sampling when [SPC](#page-0-0) is large. This remark is due to the distributional shift phenomenon involved in [MSDA.](#page-0-0)

Our work opens an interesting line of research, combining [MSDA](#page-0-0) and [DD.](#page-0-0) Future works include domain-incremental learning and considering label shifts between the different domains in [MSDA.](#page-0-0)

## References

- <span id="page-6-5"></span>Agueh, M. and Carlier, G. (2011). Barycenters in the wasserstein space. *SIAM Journal on Mathematical Analysis*, 43(2):904–924.
- <span id="page-6-15"></span>Courty, N., Flamary, R., Tuia, D., and Rakotomamonjy, A. (2017). Optimal transport for domain adaptation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 39(9):1853–1865.
- <span id="page-6-1"></span>Crammer, K., Kearns, M., and Wortman, J. (2008). Learning from multiple sources. *Journal of Machine Learning Research*, 9(8).
- <span id="page-6-13"></span>Cuturi, M. and Doucet, A. (2014). Fast computation of wasserstein barycenters. In *International conference on machine learning*, pages 685–693. PMLR.
- <span id="page-6-4"></span>Fernandes Montesuma, E., Mboula, F. M. N., and Souloumiac, A. (2023a). Multi-source domain adaptation through dataset dictionary learning in wasserstein space. In *26th European Conference on Artificial Intelligence*.
- <span id="page-6-17"></span>Fernandes Montesuma, E., Mulas, M., Corona, F., and Mboula, F.-M. N. (2022). Cross-domain fault diagnosis through optimal transport for a cstr process. *IFAC-PapersOnLine*, 55(7):946–951.
- <span id="page-6-19"></span>Fernandes Montesuma, E., Mulas, M., Ngolè Mboula, F., Corona, F., and Souloumiac, A. (2023b). Multi-source domain adaptation for cross-domain fault diagnosis of chemical processes. *arXiv preprint arXiv:2308.11247*.
- <span id="page-6-10"></span>Gretton, A., Borgwardt, K. M., Rasch, M. J., Schölkopf, B., and Smola, A. (2012). A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773.
- <span id="page-6-21"></span>Griffin, G., Holub, A., and Perona, P. (2007). Caltech-256 object category dataset. Technical report, California Institute of Technology.
- <span id="page-6-8"></span>Lee, H. B., Lee, D. B., and Hwang, S. J. (2022). Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*.
- <span id="page-6-22"></span>McInnes, L., Healy, J., and Melville, J. (2018). Umap: Uniform manifold approximation and projection for dimension reduction. *arXiv preprint arXiv:1802.03426*.
- <span id="page-6-3"></span>Montesuma, E. F. and Mboula, F. M. N. (2021a). Wasserstein barycenter for multi-source domain adaptation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 16785–16793.
- <span id="page-6-2"></span>Montesuma, E. F. and Mboula, F. M. N. (2021b). Wasserstein barycenter transport for acoustic adaptation. In *ICASSP 2021 - 2021 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)*, pages 3405–3409.
- <span id="page-6-11"></span>Peyré, G., Cuturi, M., et al. (2019). Computational optimal transport: With applications to data science. *Foundations and Trends® in Machine Learning*, 11(5-6):355–607.
- <span id="page-6-16"></span>Pilario, K. E. S. and Cao, Y. (2018). Canonical variate dissimilarity analysis for process incipient fault detection. *IEEE Transactions on Industrial Informatics*, 14(12):5308–5315.
- <span id="page-6-18"></span>Reinartz, C., Kulahci, M., and Ravn, O. (2021). An extended tennessee eastman simulation dataset for fault-detection and decision support systems. *Computers & Chemical Engineering*, 149:107281.
- <span id="page-6-14"></span>Sachdeva, N. and McAuley, J. (2023). Data distillation: A survey. *arXiv preprint arXiv:2301.04272*.
- <span id="page-6-20"></span>Saenko, K., Kulis, B., Fritz, M., and Darrell, T. (2010). Adapting visual category models to new domains. In *European conference on computer vision*, pages 213–226. Springer.
- <span id="page-6-12"></span>Sriperumbudur, B. K., Fukumizu, K., Gretton, A., Schölkopf, B., and Lanckriet, G. R. G. (2012). On the empirical estimation of integral probability metrics. *Electronic Journal of Statistics*, 6(none):1550 – 1599.
- <span id="page-6-9"></span>Villani, C. et al. (2009). *Optimal transport: old and new*, volume 338. Springer.
- <span id="page-6-0"></span>Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. (2018). Dataset distillation. *arXiv preprint arXiv:1811.10959*.
- <span id="page-6-7"></span>Zhao, B. and Bilen, H. (2021). Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR.
- <span id="page-6-6"></span>Zhao, B. and Bilen, H. (2023). Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523.