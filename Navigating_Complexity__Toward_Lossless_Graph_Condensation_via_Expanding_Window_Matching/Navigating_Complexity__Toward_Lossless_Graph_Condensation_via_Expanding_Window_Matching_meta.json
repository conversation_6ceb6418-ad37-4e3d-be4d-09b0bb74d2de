{"table_of_contents": [{"title": "Navigating Complexity: Toward Lossless Graph Condensation via Expanding\nWindow Matching", "heading_level": null, "page_id": 0, "polygon": [[58.5, 89.25], [537.29296875, 89.25], [537.29296875, 121.623046875], [58.5, 121.623046875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 206.25], [195.75, 206.25], [195.75, 217.3359375], [148.5, 217.3359375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[306.0, 206.12109375], [384.75, 206.12109375], [384.75, 217.3359375], [306.0, 217.3359375]]}, {"title": "2. Method", "heading_level": null, "page_id": 1, "polygon": [[305.103515625, 503.12109375], [360.0, 503.12109375], [360.0, 514.72265625], [305.103515625, 514.72265625]]}, {"title": "2.1. Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[306.0, 581.25], [383.25, 581.25], [383.25, 591.6796875], [306.0, 591.6796875]]}, {"title": "2.2. Preparing Curriculum-based Expert Trajectories", "heading_level": null, "page_id": 2, "polygon": [[54.0, 640.5], [283.5, 640.5], [283.5, 650.4609375], [54.0, 650.4609375]]}, {"title": "2.3. Expanding Window Matching", "heading_level": null, "page_id": 3, "polygon": [[54.0, 141.75], [201.75, 141.75], [201.75, 152.0771484375], [54.0, 152.0771484375]]}, {"title": "2.4. Knowledge Embedding Extractor", "heading_level": null, "page_id": 4, "polygon": [[306.0, 502.5], [468.75, 502.5], [468.75, 512.40234375], [306.0, 512.40234375]]}, {"title": "Algorithm 1 GEOM for condensing graph.", "heading_level": null, "page_id": 5, "polygon": [[54.0, 336.0], [228.0, 336.0], [228.0, 346.11328125], [54.0, 346.11328125]]}, {"title": "2.5. Final Objective and Algorithm", "heading_level": null, "page_id": 5, "polygon": [[305.701171875, 373.5], [456.75, 373.5], [456.75, 383.818359375], [305.701171875, 383.818359375]]}, {"title": "3. Experiments", "heading_level": null, "page_id": 5, "polygon": [[305.5517578125, 530.25], [385.5, 530.25], [385.5, 540.75], [305.5517578125, 540.75]]}, {"title": "3.1. <PERSON><PERSON>", "heading_level": null, "page_id": 5, "polygon": [[304.9541015625, 550.5], [349.5, 550.5], [349.5, 560.7421875], [304.9541015625, 560.7421875]]}, {"title": "3.2. Results", "heading_level": null, "page_id": 6, "polygon": [[54.0, 617.25], [104.25, 617.25], [104.25, 627.2578125], [54.0, 627.2578125]]}, {"title": "3.3. Ablation", "heading_level": null, "page_id": 6, "polygon": [[305.25, 593.25], [363.0, 593.25], [363.0, 603.66796875], [305.25, 603.66796875]]}, {"title": "3.4. Visualization", "heading_level": null, "page_id": 7, "polygon": [[306.0, 298.5], [380.408203125, 298.5], [380.408203125, 308.21484375], [306.0, 308.21484375]]}, {"title": "4. Related Work", "heading_level": null, "page_id": 7, "polygon": [[306.0, 570.0], [391.5, 570.0], [391.5, 580.8515625], [306.0, 580.8515625]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[54.0, 174.75], [125.25, 174.75], [125.25, 187.0751953125], [54.0, 187.0751953125]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 324.75], [148.5, 324.75], [148.5, 336.4453125], [54.0, 336.4453125]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 498.0], [150.75, 498.0], [150.75, 509.30859375], [54.0, 509.30859375]]}, {"title": "Contribution Statement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 629.25], [177.75, 629.25], [177.75, 641.1796875], [54.0, 641.1796875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[306.0, 296.25], [365.16796875, 296.25], [365.16796875, 306.474609375], [306.0, 306.474609375]]}, {"title": "A. Dataset Details", "heading_level": null, "page_id": 11, "polygon": [[54.0, 67.5], [147.7705078125, 67.5], [147.7705078125, 78.9873046875], [54.0, 78.9873046875]]}, {"title": "A.1. Statistics of Dataset", "heading_level": null, "page_id": 11, "polygon": [[54.0, 87.75], [159.0, 87.75], [159.0, 99.193359375], [54.0, 99.193359375]]}, {"title": "A.2. Performance of Dataset", "heading_level": null, "page_id": 11, "polygon": [[54.0, 306.75], [176.25, 306.75], [176.25, 317.8828125], [54.0, 317.8828125]]}, {"title": "<PERSON><PERSON>ils of the Training Scheduler", "heading_level": null, "page_id": 11, "polygon": [[54.0, 504.66796875], [238.5, 504.66796875], [238.5, 515.49609375], [54.0, 515.49609375]]}, {"title": "C. Theoretical Analysis", "heading_level": null, "page_id": 12, "polygon": [[54.0, 120.75], [174.814453125, 120.75], [174.814453125, 131.7744140625], [54.0, 131.7744140625]]}, {"title": "C.1. <PERSON><PERSON> of Theorem 2.4", "heading_level": null, "page_id": 12, "polygon": [[54.0, 140.25], [165.75, 140.25], [165.75, 150.3369140625], [54.0, 150.3369140625]]}, {"title": "C.2. Detailed Analysis", "heading_level": null, "page_id": 12, "polygon": [[54.0, 456.0], [150.609375, 456.0], [150.609375, 466.3828125], [54.0, 466.3828125]]}, {"title": "D. Training Samples Analysis", "heading_level": null, "page_id": 12, "polygon": [[54.0, 675.0], [206.25, 675.0], [206.25, 685.65234375], [54.0, 685.65234375]]}, {"title": "E. Time Complexity Analysis", "heading_level": null, "page_id": 13, "polygon": [[54.0, 208.634765625], [204.75, 208.634765625], [204.75, 221.396484375], [54.0, 221.396484375]]}, {"title": "F. Analysis of Matching Range", "heading_level": null, "page_id": 13, "polygon": [[54.0, 667.08984375], [212.765625, 667.08984375], [212.765625, 679.46484375], [54.0, 679.46484375]]}, {"title": "G. Implementation Details", "heading_level": null, "page_id": 14, "polygon": [[54.0, 549.0], [191.25, 549.0], [191.25, 559.58203125], [54.0, 559.58203125]]}, {"title": "H. Visualizations", "heading_level": null, "page_id": 15, "polygon": [[54.0, 558.80859375], [142.5, 558.80859375], [142.5, 570.41015625], [54.0, 570.41015625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 84], ["Text", 7], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7582, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 138], ["Text", 6], ["Reference", 5], ["Caption", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 1], ["Footnote", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 887, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 161], ["Text", 7], ["Equation", 5], ["TextInlineMath", 4], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 820], ["Line", 150], ["Text", 13], ["Equation", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 775], ["Line", 204], ["Text", 6], ["Equation", 3], ["TextInlineMath", 3], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1080, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1622], ["Line", 134], ["TableCell", 61], ["ListItem", 14], ["Text", 7], ["SectionHeader", 4], ["Equation", 3], ["Reference", 3], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1419, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["TableCell", 321], ["Line", 98], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 357], ["Line", 169], ["TableCell", 56], ["Reference", 6], ["Text", 4], ["Caption", 3], ["Figure", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7082, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 90], ["ListItem", 15], ["Reference", 10], ["Text", 9], ["SectionHeader", 5], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 91], ["ListItem", 29], ["Reference", 29], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 77], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["TableCell", 168], ["Line", 52], ["Text", 5], ["SectionHeader", 4], ["Reference", 4], ["ListItem", 3], ["Equation", 3], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4349, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 692], ["Line", 120], ["Text", 6], ["SectionHeader", 4], ["Equation", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4414, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 50], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 95], ["Text", 5], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 466], ["Span", 154], ["Line", 55], ["Text", 5], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 13144, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 33], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 714, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Navigating_Complexity__Toward_Lossless_Graph_Condensation_via_Expanding_Window_Matching"}