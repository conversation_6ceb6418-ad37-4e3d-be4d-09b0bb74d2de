{"table_of_contents": [{"title": "Neural Tangent Kernel:\nConvergence and Generalization in Neural Networks", "heading_level": null, "page_id": 0, "polygon": [[111.0, 99.0], [500.8359375, 99.0], [500.8359375, 136.0283203125], [111.0, 136.0283203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 346.5], [329.25, 346.5], [329.25, 357.328125], [282.75, 357.328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 625.7109375], [191.25, 625.7109375], [191.25, 636.5390625], [107.25, 636.5390625]]}, {"title": "1.1 Contribution", "heading_level": null, "page_id": 1, "polygon": [[107.25, 251.25], [186.75, 251.25], [186.75, 261.421875], [107.25, 261.421875]]}, {"title": "2 Neural networks", "heading_level": null, "page_id": 1, "polygon": [[107.25, 532.5], [212.466796875, 532.5], [212.466796875, 543.33984375], [107.25, 543.33984375]]}, {"title": "3 Kernel gradient", "heading_level": null, "page_id": 2, "polygon": [[107.1298828125, 426.0], [207.75, 427.32421875], [207.75, 438.15234375], [107.1298828125, 438.15234375]]}, {"title": "3.1 Random functions approximation", "heading_level": null, "page_id": 3, "polygon": [[106.5, 311.25], [275.25, 311.25], [275.25, 320.783203125], [106.5, 320.783203125]]}, {"title": "4 Neural tangent kernel", "heading_level": null, "page_id": 4, "polygon": [[106.5, 72.0], [238.5, 72.0], [238.5, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "4.1 Initialization", "heading_level": null, "page_id": 4, "polygon": [[106.5, 293.25], [186.0, 293.25], [186.0, 303.380859375], [106.5, 303.380859375]]}, {"title": "4.2 Training", "heading_level": null, "page_id": 5, "polygon": [[106.5, 74.25], [168.0, 74.25], [168.0, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "5 Least-squares regression", "heading_level": null, "page_id": 5, "polygon": [[106.8310546875, 532.5], [252.75, 532.5], [252.75, 543.33984375], [106.8310546875, 543.33984375]]}, {"title": "6 Numerical experiments", "heading_level": null, "page_id": 6, "polygon": [[106.5, 561.75], [245.25, 561.75], [245.25, 572.73046875], [106.5, 572.73046875]]}, {"title": "6.1 Convergence of the NTK", "heading_level": null, "page_id": 7, "polygon": [[106.5, 285.0], [236.25, 285.0], [236.25, 294.6796875], [106.5, 294.6796875]]}, {"title": "6.2 Kernel regression", "heading_level": null, "page_id": 7, "polygon": [[107.25, 443.25], [205.59375, 443.25], [205.59375, 452.84765625], [107.25, 452.84765625]]}, {"title": "6.3 Convergence along a principal component", "heading_level": null, "page_id": 7, "polygon": [[106.5, 630.0], [310.5, 630.0], [310.5, 639.6328125], [106.5, 639.6328125]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 541.5], [183.75, 541.5], [183.75, 553.0078125], [106.5, 553.0078125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.75], [207.75, 72.75], [207.75, 83.6279296875], [106.5, 83.6279296875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 145.5], [164.953125, 145.5], [164.953125, 156.5244140625], [107.25, 156.5244140625]]}, {"title": "A Appendix", "heading_level": null, "page_id": 10, "polygon": [[106.8310546875, 316.5], [179.25, 316.5], [179.25, 327.55078125], [106.8310546875, 327.55078125]]}, {"title": "A.1 Asymptotics at Initialization", "heading_level": null, "page_id": 10, "polygon": [[107.25, 525.0], [253.5, 525.0], [253.5, 534.4453125], [107.25, 534.4453125]]}, {"title": "A.2 Asymptotics during Training", "heading_level": null, "page_id": 12, "polygon": [[107.25, 328.5], [257.25, 327.0], [257.25, 338.958984375], [107.25, 339.0]]}, {"title": "A.3 A Priori Control during Training", "heading_level": null, "page_id": 14, "polygon": [[106.5, 610.5], [274.32421875, 610.5], [274.32421875, 621.0703125], [106.5, 621.0703125]]}, {"title": "A.4 Positive-Definiteness of \\Theta_{\\infty}^{(L)}", "heading_level": null, "page_id": 16, "polygon": [[108.0, 519.6227416992188], [252.38833618164062, 519.6227416992188], [252.38833618164062, 532.8461608886719], [108.0, 532.8461608886719]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 43], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5064, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 453], ["Line", 55], ["Text", 6], ["ListItem", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1031], ["Line", 64], ["TextInlineMath", 9], ["Equation", 3], ["Reference", 2], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 924], ["Line", 144], ["TextInlineMath", 9], ["Equation", 8], ["Text", 6], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 729], ["Line", 85], ["TextInlineMath", 8], ["Text", 7], ["Equation", 6], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 694], ["Line", 81], ["TextInlineMath", 7], ["Equation", 6], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 791], ["Line", 75], ["TextInlineMath", 9], ["Equation", 4], ["Text", 3], ["Reference", 2], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 612], ["Line", 58], ["TextInlineMath", 5], ["SectionHeader", 3], ["Caption", 2], ["Text", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 940, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 76], ["TextInlineMath", 4], ["Text", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1098, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 47], ["ListItem", 16], ["Reference", 16], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 66], ["Reference", 8], ["ListItem", 7], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 919], ["Line", 126], ["TextInlineMath", 8], ["Equation", 8], ["Text", 5], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1138, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 894], ["Line", 150], ["TextInlineMath", 8], ["Equation", 8], ["Text", 6], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1207, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1260], ["Line", 239], ["TextInlineMath", 8], ["Equation", 8], ["Text", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3103, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 851], ["Line", 146], ["TextInlineMath", 9], ["Equation", 6], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 929], ["Line", 214], ["Equation", 9], ["Text", 9], ["TextInlineMath", 2], ["ListItem", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3379, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 1095], ["Line", 102], ["TextInlineMath", 7], ["Equation", 7], ["Text", 4], ["Reference", 2], ["ListItem", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 835], ["Line", 112], ["Text", 9], ["Equation", 7], ["TextInlineMath", 6], ["ListItem", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 47], ["Text", 5], ["Equation", 4], ["TextInlineMath", 3], ["ListItem", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Neural Tangent Kernel- Convergence and Generalization in Neural Networks"}