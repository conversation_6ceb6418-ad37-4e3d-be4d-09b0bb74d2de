{"table_of_contents": [{"title": "Neural Spectral Decomposition for Dataset\nDistillation", "heading_level": null, "page_id": 0, "polygon": [[156.0, 114.75], [456.75, 114.75], [456.75, 145.79296875], [156.0, 145.79296875]]}, {"title": "2 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 1, "polygon": [[133.5, 92.25], [222.75, 92.25], [222.75, 102.48046875], [133.5, 102.48046875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[133.5, 117.0], [229.5, 117.0], [229.5, 128.4873046875], [133.5, 128.4873046875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[133.27734375, 448.98046875], [237.0, 448.98046875], [237.0, 460.58203125], [133.27734375, 460.58203125]]}, {"title": "4 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 3, "polygon": [[133.5, 92.25], [223.0751953125, 92.25], [223.0751953125, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "3 Method", "heading_level": null, "page_id": 3, "polygon": [[133.5, 617.25], [202.0078125, 617.25], [202.0078125, 628.8046875], [133.5, 628.8046875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 6, "polygon": [[133.5, 451.5], [229.5, 451.5], [229.5, 462.90234375], [133.5, 462.90234375]]}, {"title": "4.1 Datasets & Implementation Details", "heading_level": null, "page_id": 6, "polygon": [[133.5, 552.0], [339.169921875, 552.0], [339.169921875, 561.90234375], [133.5, 561.90234375]]}, {"title": "4.2 Evaluation on the Condensed Data", "heading_level": null, "page_id": 7, "polygon": [[133.5, 550.5], [336.75, 550.5], [336.75, 561.12890625], [133.5, 561.12890625]]}, {"title": "4.3 Cross-architecture generalization", "heading_level": null, "page_id": 9, "polygon": [[133.5, 230.25], [326.25, 230.25], [326.25, 240.15234375], [133.5, 240.15234375]]}, {"title": "4.4 Exploring Spectral Decomposition", "heading_level": null, "page_id": 10, "polygon": [[133.35205078125, 117.75], [334.08984375, 117.75], [334.08984375, 128.1005859375], [133.35205078125, 128.1005859375]]}, {"title": "4.5 Ablation Studies", "heading_level": null, "page_id": 11, "polygon": [[133.5, 624.75], [246.3837890625, 624.75], [246.3837890625, 635.37890625], [133.5, 635.37890625]]}, {"title": "4.6 Visualizations", "heading_level": null, "page_id": 12, "polygon": [[133.5, 599.25], [231.0, 599.25], [231.0, 609.08203125], [133.5, 609.08203125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[133.5, 448.5], [219.0, 448.5], [219.0, 460.1953125], [133.5, 460.1953125]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[133.5, 117.0], [198.0, 117.0], [198.0, 128.197265625], [133.5, 128.197265625]]}, {"title": "Neural Spectral Decomposition for Dataset\nDistillation", "heading_level": null, "page_id": 16, "polygon": [[156.0, 114.75], [456.75, 114.75], [456.75, 145.599609375], [156.0, 145.599609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 44], ["Text", 4], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4347, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 45], ["Text", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 43], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 44], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 344], ["Line", 43], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 748, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 534], ["Line", 47], ["TextInlineMath", 4], ["Text", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["TableCell", 285], ["Line", 44], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5496, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 203], ["Line", 43], ["Text", 6], ["TextInlineMath", 2], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["TableCell", 60], ["Line", 38], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["Text", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 5651, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 40], ["TableCell", 37], ["Text", 4], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 43], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["TableCell", 104], ["Line", 44], ["Caption", 3], ["Text", 3], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2825, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["TableCell", 70], ["Line", 40], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7277, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 33], ["Text", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 723, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 49], ["ListItem", 18], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 48], ["ListItem", 18], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 25], ["Text", 4], ["SectionHeader", 1], ["Picture", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 24], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Text", 1], ["Picture", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1228, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 6], ["Line", 2], ["Text", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 736, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 2], ["ListItem", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 650, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 682, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Neural_Spectral_Decomposition_for_Dataset_Distillation"}