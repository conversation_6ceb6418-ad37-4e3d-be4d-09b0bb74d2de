# New Properties of the Data Distillation Method When Working With Tabular Data

<PERSON>, <PERSON> Moscow State University, Moscow, Russia

Abstract. Data distillation is the problem of reducing the volume of training data while keeping only the necessary information. With this paper, we deeper explore the new data distillation algorithm, previously designed for image data. Our experiments with tabular data show that the model trained on distilled samples can outperform the model trained on the original dataset. One of the problems of the considered algorithm is that produced data has poor generalization on models with different hyperparameters. We show that using multiple architectures during distillation can help overcome this problem.

Keywords: Dataset Distillation, Knowledge Distillation, Neural Networks, Synthetic Data, Gradient Descent, Tabular Data.

## 1 Introduction

Data distillation is an aggregation of all possible information from the original training dataset to reduce its volume. The algorithm proposed in [\[1\]](#page-11-0) tries to produce a small synthetic dataset, which can be used to train models reaching the same quality as with training on the original dataset. In addition, the algorithm also reduces the number of optimization steps needed for training on new data, limiting this number in the objective.

Besides pure scientific interest, research in this new area can be very helpful in practice. For example, often the solution to one problem requires many different models to be trained on the same dataset. The creation of a new synthetic dataset that allows to simultaneously reduce training time for a large number of models with different architectures and hyperparameters would be very helpful. However, the mentioned algorithm has some drawbacks. Distilled data is poorly generalized for models not involved in the distillation process. In this paper, we examine the work of the algorithm on tabular data trying to address this problem.

The rest of the work is divided into 7 sections. In Section 2 we do a short overview of related work. A detailed description of the data distillation algorithm with its complexity analysis is located in Section 3. Section 4 consists of descriptions of architectures and the tabular dataset used in the research. In Section 5 we show results of experiments and examine the properties of synthetic tabular data. In Section 6 we examine the possibility of training models with different architectures on one synthetic dataset. Finally, we present our conclusions in Section 7.

## 2 Related Work

The basis of the data distillation algorithm is the optimization of synthetic data and learning rates with hypergradients. The application of backpropagation [\[6\]](#page-11-1) for optimization of hyperparameters was proposed in [\[9\]](#page-11-2) and [\[10\]](#page-11-3). Backpropagation through L-BFGS [\[11\]](#page-11-4) and SGD with momentum [\[12\]](#page-11-5) was presented in [\[7\]](#page-11-6), and more memory-efficient algorithm was proposed in [\[8\]](#page-11-7). In addition, [\[8\]](#page-11-7) conducted experiments with data optimization.

The algorithm examined in our work was developed in [\[1\]](#page-11-0), where successful distillation of the MNIST dataset [\[4\]](#page-11-8) was shown. Leaving only 10 examples (one for each class), and thus reducing the dataset volume by 600 times, they were able to train the LeNet model [\[5\]](#page-11-9) to quality close to the quality of training on the original dataset. Also, they proposed to use fixed distribution for network initialization to increase distilled data generalization, but still couldn't reach quality obtained with fixed initialization. It is important to note that authors of the considering algorithm [\[1\]](#page-11-0) were inspired by network distillation [\[2\]](#page-11-10), that is, the transfer of knowledge from an ensemble of well-trained models into a single compact one.

The way to distill both objects and their labels was shown in [\[3\]](#page-11-11). Authors showed that such distillation increases accuracy for several image classification tasks and allows distilled datasets to consist of fewer samples than the number of classes. Also, they showed the possibility to distill text data.

## 3 Distillation Algorithm

The simplest version of the algorithm developed in [\[1\]](#page-11-0) is the one-step version. Let  $\theta_0$  be the initial model's weights vector sampled from a fixed distribution  $p(\theta_0)$ , x be the original data,  $\tilde{x}$  be the synthetic data (randomly initialized vectors),  $\tilde{\eta}$  be a synthetic learning rate (positive scalar needed in the gradient descent method), and  $l(x, \theta)$  be a loss function. If we do gradient descent step and get updated weights  $\theta_1$  then the distillation algorithm can be written in the form of the following optimization problem:

$$
\tilde{x}^*, \tilde{\eta}^* = \underset{\tilde{x}, \tilde{\eta}}{\operatorname{argmin}} \mathbb{E}_{\theta_0 \sim p(\theta_0)} l(x, \theta_1) = \underset{\tilde{x}, \tilde{\eta}}{\operatorname{argmin}} \mathbb{E}_{\theta_0 \sim p(\theta_0)} l(x, \theta_0 - \tilde{\eta} \nabla_{\theta_0} l(\tilde{x}, \theta_0)).
$$
 (1)

To launch the gradient descent method and find the optimum of this problem we have to calculate second-order derivatives, which is possible for the majority of loss functions and model's architectures. In general, when we want to train a model on distilled data for a few steps or even for a few epochs, the algorithm looks a bit more complicated. To describe it we introduce the concepts of external and internal steps and epochs, and the concept of internal models. So, at each internal step of each internal epoch, several internal models are trained on synthetic data. In [\[1\]](#page-11-0), internal models have the same architecture and different initializations  $\theta_0^{(j)} \sim p(\theta_0)$ . At each external step of each external epoch, the loss function of these trained models is evaluated on the original data. After this, we can calculate the direction to make the descent step and optimize the synthetic data and learning rates. As a result, the general-case algorithm at each external step solves the following optimization problem:

<span id="page-2-0"></span>
$$
\begin{cases}
\theta_0^{(j)} \sim p(\theta_0); \quad j = 1, ..., m; \\
\theta_{k+1}^{(j)} = \theta_k^{(j)} - \tilde{\eta}_k \nabla_{\theta} l(\tilde{x}_{i(k)}, \theta_k^{(j)}); \quad k = 0, ..., n-1; \quad i(k) = k \mod s; \\
\mathcal{L} = \frac{1}{m} \sum_{j=1}^m l(x, \theta_n^{(j)}) \to \min_{\tilde{x}, \tilde{\eta}}.
\end{cases}
$$
(2)

In  $(2)$  s is the number of internal steps of one internal epoch; n is the total number of steps of the internal loop and  $m$  is the number of internal models. Optimization requires an estimation of gradients  $\nabla_{\tilde{x}} \mathcal{L}$  and  $\nabla_{\tilde{\eta}} \mathcal{L}$ :

$$
d\mathcal{L} = \sum_{j=1}^{m} \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} d\theta_n^{(j)} = \sum_{j=1}^{m} \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} d(\theta_{n-1}^{(j)} - \tilde{\eta}_{n-1} \nabla_{\theta} l(\tilde{x}_{i(n-1)}, \theta_{n-1}^{(j)})) =
$$
  
$$
= \{g_{n-1}^{(j)} := \tilde{\eta}_{n-1} \nabla_{\theta} l(\tilde{x}_{i(n-1)}, \theta_{n-1}^{(j)})\} = \sum_{j=1}^{m} \left[ \left( \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} - \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} \frac{\partial g_{n-1}^{(j)}}{\partial \theta_{n-1}^{(j)}} \right) d\theta_{n-1}^{(j)} - \left( \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} \frac{\partial g_{n-1}^{(j)}}{\partial \tilde{\eta}_{n-1}} \right) d\tilde{\eta}_{n-1} - \left( \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} \frac{\partial g_{n-1}^{(j)}}{\partial \tilde{x}_{i(n-1)}} \right) d\tilde{x}_{i(n-1)} \right].
$$

It is clear that if we continue to express  $\theta_{n-1}^{(j)}$  through  $\theta_{n-2}^{(j)}$  and so on we will get an expression with  $\theta_0^{(j)}$ . After summing up all the necessary terms, we obtain the following formulas for gradients:

$$
\nabla_{\tilde{\eta}_k} \mathcal{L} = \frac{\partial \mathcal{L}}{\partial \theta_{k+1}} \cdot \frac{\partial g_k}{\partial \tilde{\eta}_k}; \qquad \nabla_{\tilde{x}_j} \mathcal{L} = \sum_{k=0}^{n-1} I[j = i(k)] \cdot \frac{\partial \mathcal{L}}{\partial \theta_{k+1}} \cdot \frac{\partial g_k}{\partial \tilde{x}_i(k)}.
$$
 (3)

Algorithms 1, 2 and 3 show the implementation of the method in pseudo-code. It's clear from the algorithm description that memory and time complexity is high, since we need to store  $n$  copies of the internal model and to perform backward and forward passes through all these  $n$  copies. This limitation negatively affects the performance, since the increment of  $n$  significantly increases the quality of the model trained using the distilled dataset.

Algorithm 1 Main Cycle

1: **Input:**  $p(\theta_0)$ ; *m*; *n*; *s*; *T* — number of external steps; batch sizes.

2: Initialization  $\tilde{x}, \tilde{\eta}$ 

3: loop for each  $t = 1, ..., T$ :

4:  $\nabla_{\tilde{x}} \mathcal{L} = 0, \nabla_{\tilde{\eta}} \mathcal{L} = 0 \text{ // accumulated values of opt. directions.}$ 

- 5: Get a minibatch of real training data  $x$
- 6: **loop** for each model  $j = 1, ..., m$ :

7: Model initialization  $\theta_0^{(j)} \sim p(\theta_0)$ 

- 8: Res  $\leftarrow$  **Forward** // see Algorithm 2
- 9:  $\nabla_{\tilde{x}} \mathcal{L}, \nabla_{\tilde{\eta}} \mathcal{L} \leftarrow$  **Backward** // see Algorithm 3
- Update  $\tilde{x}, \tilde{\eta}$

#### Algorithm 2 Forward Pass

1: **Input:**  $\theta_0^{(j)}$ ,  $\tilde{x}$ ,  $\tilde{\eta}$ ,  $x$ . 2: Res  $\leftarrow \theta_0^{(j)}$ 3: **loop** for each internal step  $k = 0, ... n - 1$ : 4:  $g_k = \tilde{\eta}_k \nabla_{\theta} l(\tilde{x}_{i(k)}, \theta_k^{(j)})$ 5: θ  $\binom{(j)}{k+1} = \theta_k^{(j)} - g_k$ 6: Res  $\leftarrow$   $g_k$ ,  $\theta_{k+1}^{(j)}$  // remember comp. graph and model 7:  $\frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}} = \frac{1}{m} \frac{\partial l(x, \theta_n^{(j)})}{\partial \theta_n^{(j)}}$ <br>8: Res  $\leftarrow \frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}}$ 9: Output: Res

### Algorithm 3 Backward Pass

1: Input:  $\nabla_{\tilde{x}} \mathcal{L}, \nabla_{\tilde{\eta}} \mathcal{L}, \text{Res}$  // Res — comp. graphs and weights of models. 2:  $\frac{\partial \mathcal{L}}{\partial \theta_n^{(j)}}$  ← Res 3: **loop** for each internal step  $k = n - 1, ...0$ : 4:  $g_k, \theta_k^{(j)} \leftarrow Res$ 5:  $\nabla_{\tilde{x}_{i(k)}} \mathcal{L} \leftarrow \nabla_{\tilde{x}_{i(k)}} \mathcal{L} + \frac{\partial \mathcal{L}}{\partial \theta_{k+1}^{(j)}}$  $\frac{\partial g_k}{\partial \tilde{x}_{i(k)}}$ 6:  $\nabla_{\tilde{\eta}_k} \mathcal{L} \leftarrow \nabla_{\tilde{\eta}_k} \mathcal{L} + \frac{\partial \mathcal{L}}{\partial \theta_{k+1}^{(j)}}$  $\frac{\partial g_k}{\partial \tilde{\eta}_k}$ 7:  $\frac{\partial \mathcal{L}}{\partial \theta_k^{(j)}} = \frac{\partial \mathcal{L}}{\partial \theta_{k+1}^{(j)}} \left( 1 - \frac{\partial g_k}{\partial \theta_k^{(j)}} \right)$ λ 8: Output:  $\nabla_{\tilde{x}} \mathcal{L}, \nabla_{\tilde{\eta}} \mathcal{L}$ 

## 4 Data and Models

We consider a simple two-dimensional binary classification problem, which dataset volume is 1,500 objects (see Fig. [1](#page-4-0) a). The dataset is divided into training and test parts in a 2:1 ratio. We distill the training part and use the test part for quality

estimation. We suppose that using such a small amount of data and producing less extreme distillation can help us to explore new properties of the algorithm. Note that it is not always possible with big visual datasets and large models due to the complexity of the distillation. For experiments we use three fully connected architectures: 1-layer, 2-layer and 4-layer. Figure [1](#page-4-0) (b, c) schematically shows non-linear architectures.

Image /page/4/Figure/2 description: The image displays three plots labeled a), b), and c). Plot a) is a scatter plot showing two crescent-shaped clusters of data points, one in blue labeled '0' and the other in orange labeled '1'. The x-axis is labeled 'x1' and ranges from -1 to 2, while the y-axis is labeled 'x2' and ranges from -0.5 to 1.0. Plots b) and c) are diagrams illustrating neural network architectures. Plot b) shows a sequential network with four fully connected layers (fc1, fc2, fc3, fc4) and ReLU activation functions between them. The layer dimensions are fc1: (2, 10), fc2: (10, 10), fc3: (10, 10), and fc4: (10, 1). Plot c) shows a simpler network with two fully connected layers, fc1: (2, 10) and fc2: (10, 1), with a ReLU activation function between them.

<span id="page-4-0"></span>Fig. 1. Used data and non-linear architectures: a) whole original dataset, b) 2-layer model, c) 4-layer model.

First, we estimate the quality of training on the whole dataset. We train models 25 times with random initialization from Xavier distribution. Each training takes 500 epochs. Note that 500 epochs are more than enough for convergence for all three architectures (see Fig. [2](#page-4-1) d), and it seems that the training procedure could be stopped after 200 epochs. Hereinafter, such figures show the boundaries of the 95% confidence interval estimated with the bootstrap procedure. In addition Figure [2](#page-4-1) (a-c) shows the distribution of achieved accuracy; the largest model reached the highest quality.

Image /page/4/Figure/5 description: This image displays four plots related to neural network performance. Plot (a) is a histogram for a 1-layer network, showing accuracy observations with a mean of 0.766 and a median of 0.788. Plot (b) is a histogram for a 2-layer network, with accuracy observations, a mean of 0.877, and a median of 0.879. Plot (c) is a histogram for a 4-layer network, displaying accuracy observations with a mean of 0.995 and a median of 1.000. Plot (d) shows the convergence of accuracy over epochs for 1-layer, 2-layer, and 4-layer networks, with the 4-layer network achieving the highest and fastest convergence.

<span id="page-4-1"></span>Fig. 2. The quality of training on the whole dataset: a–c) distribution of accuracy for different initializations; d) the convergence of accuracy.

Figure [3](#page-5-0) (a, c) shows the boundaries of the decision rules. For each architecture, it shows the median-quality model. Note that the 2-layer model was not able to significantly outperform the linear solution, despite the significant number of made gradient descent steps and use of the whole training dataset.

Image /page/5/Figure/1 description: The image displays four plots, labeled a), b), c), and d), each illustrating a decision boundary for a neural network on a 2D dataset. Plots a) and b) represent a 2-layer network with accuracies of 0.879 and 0.941, respectively. Plots c) and d) represent a 4-layer network with accuracies of 1.000 and 0.913, respectively. Each plot shows two crescent-shaped clusters of data points: red points labeled 'class: 1' and green points labeled 'class: 0'. The background of each plot is colored in shades of blue and white, indicating the predicted class for different regions of the feature space (x1 and x2). The decision boundaries vary across the plots, with plot c) showing a perfect separation of the classes.

<span id="page-5-0"></span>**Fig. 3.** The decision rule boundary for models with median quality: a),c) trained on the whole dataset; b, d) trained on distilled data. The 2-layer model builds a more complex decision rule using the distilled data.

### 5 Tabular Data Distillation

#### 5.1 Examining Hyperparameters

The distillation algorithm has several hyperparameters: the number of internal epochs, steps and models, as they significantly affect complexity, it is important to choose the most appropriate ones. The number of internal models affects the total time of distillation. The number of steps and epochs influences  $n$  and thus affects the total time of distillation and the size of needed memory. Note that the number of steps needed to train any model on distilled data is fixed. It means that there is a risk that some models may not converge in the preselected number of steps. Another parameter is the number of synthetic objects taking part in each inner step. We choose this parameter to be 4 since it seems to be enough to describe the border of two classes.

To select hyperparameters we performed several experiments similar to ones in [\[1\]](#page-11-0) (see Fig. [4\)](#page-5-1). Note that increase of both the number of epochs and steps improves the final accuracy of the algorithms, while a significant increase in the number of models does not give a noticable change.

Image /page/5/Figure/7 description: This image contains three plots labeled a), b), and c). All three plots show accuracy on the y-axis. Plot a) shows accuracy versus distill epochs, with data points for 1-layer, 2-layer, and 4-layer networks. The x-axis ranges from 0 to 100. Plot b) shows accuracy versus distill steps, with data points for 1-layer, 2-layer, and 4-layer networks. The x-axis ranges from 0 to 100. Plot c) shows accuracy versus nets number, with data points for 1-layer, 2-layer, and 4-layer networks. The x-axis ranges from 1 to 20. In all plots, the 2-layer network generally shows the highest accuracy, followed by the 4-layer network, and then the 1-layer network.

<span id="page-5-1"></span>Fig. 4. The dependence of the test quality on: a) the number of internal epochs (1 internal model and 1 internal step); b) the number of internal steps (1 internal model and 1 internal epoch); c) the number of internal models (10 internal steps and 1 internal epoch).

##### 5.2 The Distillation Algorithm Performance

To check the distillation algorithm performance on tabular data, we launch distillation 10 times for each architecture with different initializations of  $\theta_0$ ,  $\tilde{x}$  and  $\tilde{\eta}$ ). Each launch takes 50 outer epochs which is equal to 800 outer iterations since the batch size of 64. For this experiment, we choose the number of internal steps to be 40, the number of internal epochs to be 5 and the number of internal models to be 3. This set of hyperparameters leads to the total number of synthetic objects equals 320, which is more than 3 times less than the original data volume. Note that during training there are  $3 \times 800 \times 5 \times 40$  forward passes through the model, and for each gradient descent step we need to store  $5 \times 40$ model's copies. As a result, total distillation time for all three architectures and 10 restarts reaches 4 hours, while the usual training procedure of models on the whole original data lasts only a few minutes. To compare the standard training procedure described in Section 4 with training on distilled data we present similar plots.

Image /page/6/Figure/3 description: This image contains three histograms side-by-side, each representing the accuracy of a neural network with a different number of layers: 1-layer, 2-layer, and 4-layer. The y-axis for all histograms is labeled "observations" and ranges from 0.0 to 3.0 (or 4.0 for the 4-layer plot). The x-axis for all histograms is labeled "accuracy". The 1-layer plot shows accuracies clustered around 0.870, with a mean of 0.871 and a median of 0.871. The 2-layer plot shows accuracies clustered around 0.990-1.000, with a mean of 0.941 and a median of 0.935. The 4-layer plot shows accuracies clustered around 0.900-0.910, with a mean of 0.906 and a median of 0.911.

<span id="page-6-0"></span>Fig. 5. Distribution of accuracy for different initializations.

Figures [2](#page-4-1) (a, b, c) and [5](#page-6-0) show the distribution of accuracy, and Table [1](#page-10-0) shows mean and standard deviation. Comparing them we can see that the quality of 1-layer and 2-layer architecture has grown. We suggest that the distilled data has become additional parameters and allowed the network to better solve the problem. Note that at the same time, the quality of 4-layer models has decreased a bit.

Figures [2.](#page-4-1)d and [6](#page-7-0) (a, e) show the convergence of both training procedures. Note that the number of steps is not enough to train the 2-layer and 4-layer models on the distilled data. Therefore, we assume that quality will increase if the number of iterations is greater.

Figure [7](#page-7-1) shows synthetic objects from different internal steps for datasets used to achieve median quality. Note that there is no strong similarity between distilled and original objects, as it was in the experiments with images [\[1\]](#page-11-0). Also, it seems that objects of data distilled for the linear model are often located along the decision boundary. Thus we can assume that the data can overfit for a

Image /page/7/Figure/1 description: This figure displays eight plots, arranged in a 2x4 grid, comparing the accuracy and loss of neural networks with different numbers of layers (1-layer, 2-layer, and 4-layer) across various strategies and training steps/epochs. The top row (a, b, c, d) shows accuracy plots. Plot (a) tracks accuracy against steps for up to 200 steps, while plots (b), (c), and (d) track accuracy against epochs for up to 50 epochs, each corresponding to a different strategy (1 Strategy, 2 Strategy, and 3 Strategy, respectively). The bottom row (e, f, g, h) shows loss plots, mirroring the structure of the top row. Plot (e) tracks loss against steps for up to 200 steps, and plots (f), (g), and (h) track loss against epochs for up to 50 epochs, also corresponding to 1 Strategy, 2 Strategy, and 3 Strategy, respectively. Each plot includes shaded regions representing the variance or confidence interval for each layer configuration.

<span id="page-7-0"></span>Fig. 6. The convergence of the accuracy and logarithmic loss functions on the test part for various strategies of increasing the number of training steps.

Image /page/7/Figure/3 description: The image displays a 3x4 grid of scatter plots, each visualizing data points in a 2D space with axes labeled x1 and x2. Each subplot is titled with a combination of 'layer / step', indicating variations in network layers and training steps. The first row shows plots for '1-layer' at steps 1, 14, 27, and 40. The second row shows plots for '2-layer' at the same steps. The third row shows plots for '4-layer' at steps 1, 14, 27, and 40. Within each plot, there are several star-shaped data points colored teal and orange, and a curved line composed of small gray dots. The arrangement of these elements suggests a visualization of a machine learning process, possibly demonstrating how data points are classified or separated over different training stages and network architectures.

<span id="page-7-1"></span>Fig. 7. Synthetic objects of different internal steps (1st, 14th, 27th and last) that were used to train median-quality models.

specific architecture which can be a barrier to train other architectures on such data.

Figure [3](#page-5-0) shows the boundaries of the decision rules for median-quality models trained on the original (a, c) and distilled (b, d) data. The progress of the 2-layer architecture is clear: using distilled data models can build more complex decision rules, which bring them closer to bigger models.

##### 5.3 The Problem of a Small Number of Epochs

Due to memory and time complexity, it is undesirable to use a large number of internal epochs in the distillation procedure. Fixation of a small number of steps causes the aforementioned problem: it doesn't allow training procedure on distilled data to converge. To overcome this problem, we explore three strategies for artificially increasing the number of epochs when training new models.

Image /page/8/Figure/3 description: The image displays four subplots labeled a), b), c), and d), each illustrating learning rate over steps. Subplots a), b), and c) show learning rate trends for 1-layer, 2-layer, and 4-layer models, respectively, with the x-axis representing steps from 0 to 200 and the y-axis representing learning rate from 0.0 to 1.5 (or 2.5 for b)). These plots show a general decrease in learning rate over steps with some fluctuations. Subplot d) titled '1 Strategy' shows a different learning rate pattern over 2000 steps, with the learning rate increasing from 0 to a peak of approximately 0.8 at 500 steps, and then decreasing to near 0 by 2000 steps. The y-axis for subplot d) ranges from 0.0 to 0.8.

<span id="page-8-0"></span>Fig. 8. Learning rates: a-c) obtained by distillation and averaged for 10 different initializations; d) used in the first strategy.

Image /page/8/Figure/5 description: The image displays a grid of six histograms, each illustrating the distribution of accuracy for different strategies and layer configurations. Each histogram is labeled with a letter from 'a' to 'f'. Histograms 'a', 'b', and 'c' represent 2-layer configurations with 1, 2, and 3 strategies, respectively. Histograms 'd', 'e', and 'f' represent 4-layer configurations with 1, 2, and 3 strategies, respectively. Each histogram shows 'observations' on the y-axis and 'accuracy' on the x-axis. Vertical dashed lines indicate the mean and median accuracy for each configuration. Specifically, for 'a' (1 Strategy / 2-layer), the mean accuracy is 0.956 and the median is 0.981. For 'b' (2 Strategy / 2-layer), the mean is 0.954 and the median is 0.990. For 'c' (3 Strategy / 2-layer), the mean is 0.961 and the median is 0.978. For 'd' (1 Strategy / 4-layer), the mean is 0.916 and the median is 0.941. For 'e' (2 Strategy / 4-layer), the mean is 0.941 and the median is 0.959. For 'f' (3 Strategy / 4-layer), the mean is 0.955 and the median is 0.963.

<span id="page-8-1"></span>Fig. 9. Distribution of accuracy for different initializations, architectures and strategies of increasing the number of training steps.

Figure [8](#page-8-0) (a, b, c) shows synthetic learning rates. Note that there are strong fluctuations from iteration to iteration, so it seems difficult to just replace such a complex scheme with a universal one. Nevertheless, we show that a standard strategy (see Fig. [8](#page-8-0) d) can improve the quality of models trained on distilled data (see Fig. [5,](#page-6-0) [9](#page-8-1) a, d, Table [1\)](#page-10-0). This strategy increases step sizes in 1.1 times at the beginning of each new epoch and then similarly decreases them in 0.95

###### 10 D. Medvedev and A. D'yakonov

times. Note that the new strategy allowed non-linear models to converge (see Fig. [6](#page-7-0) b, f) even without using synthetic learning rate.

Other strategies use synthetic learning rates. The second strategy repeats synthetic epochs multiplied on a coefficient. Thus the synthetic learning rate stays unchanged for the first 5 epochs. For the next 5 epochs, each learning rate is multiplied on 0.98 and repeated again. Then, to get 5 more epochs, we again multiply each learning rate but on  $0.98 \cdot 0.98 = 0.9604$ , and so on we repeat this 10 times. Note that in contrast to the smooth convergence of the previous strategy (see Fig.  $6 \text{ b}$ , f), the new one has strong fluctuations during training (see Fig.  $6c, g$ ), but outperforms the previous one (see Fig.  $9b, e$ ).

The third strategy attempts to correct the inaccurate connection of the epochs of the previous strategy. Instead of cyclically repeating all five epochs, only the last is repeated. Note that the training procedure convergence has indeed become smoother (see Fig. [6](#page-7-0) d, h) and models reach the higher quality (see Fig. [9](#page-8-1) c, f, Table [1\)](#page-10-0).

## 6 Data Generalization to Different Architectures

One of the possible practical applications of the data distillation is the fast training of a large number of different architectures with different initializations to acceptable quality. Therefore, it is important for the synthetic data to be well generalized to all variations of networks. To examine this issue, we tried to train models of different architectures on each distilled dataset.

Table [1](#page-10-0) (first subtable) depicts the results. Note that the synthetic data generally acts acceptable for architectures simpler than used in distillation procedure. The worst result was obtained when training a 4-layer model on the data distilled for a 2-layer. Since the data distilled for 2-layer models doesn't seem much different from the data distilled for 4-layer models, we assume that the problem can be caused by synthetic learning rates. So it makes sense to try each of the three strategies described in the previous section. Next subtables in Table [1](#page-10-0) show the result: it seems that for all three cases there are some improvements in the quality. The most significant changes touched the worst case. Note that using the strategy without distilled learning rates helped to get much better quality, even higher than when training on data distilled specifically for this architecture.

The natural assumption is that data distillation using all three architectures can lead to better results. to do so we select  $m = 3$ , but instead of using three internal models with the same architecture, we use three different ones: 1-layer, 2-layer and 4-layer. Table [2](#page-10-1) shows the results: for all non-linear architectures, we were able to reach higher accuracy using undistilled learning rates. Note that new synthetic data (see Fig. [10\)](#page-10-2) looks similar to the data from previous experiments (see Fig. [7\)](#page-7-1).

###### Tabular Data Distillation 11

| Data Models         | Test Models   |               |               |
|---------------------|---------------|---------------|---------------|
|                     | 1-layer       | 2-layer       | 4-layer       |
| original            | 0.766   0.089 | 0.877   0.005 | 0.995   0.015 |
| 1-layer             | 0.871   0.003 | 0.869   0.004 | 0.864   0.006 |
| 2-layer             | 0.808   0.014 | 0.941   0.043 | 0.691   0.182 |
| 4-layer             | 0.825   0.014 | 0.879   0.013 | 0.906   0.054 |
| Strategy1 + 1-layer | 0.863   0.006 | 0.860   0.008 | 0.860   0.010 |
| Strategy1 + 2-layer | 0.808   0.010 | 0.956   0.047 | 0.985   0.015 |
| Strategy1 + 4-layer | 0.818   0.012 | 0.911   0.059 | 0.916   0.062 |
| Strategy2 + 1-layer | 0.869   0.004 | 0.867   0.006 | 0.865   0.005 |
| Strategy2 + 2-layer | 0.804   0.012 | 0.954   0.065 | 0.672   0.229 |
| Strategy2 + 4-layer | 0.827   0.017 | 0.937   0.035 | 0.941   0.055 |
| Strategy3 + 1-layer | 0.870   0.003 | 0.866   0.006 | 0.863   0.008 |
| Strategy3 + 2-layer | 0.807   0.011 | 0.961   0.041 | 0.834   0.210 |

<span id="page-10-0"></span> $\overline{\text{Strategy3} + 4\text{-layer}}$  0.835  $\pm$  0.016 0.910  $\pm$  0.041 0.955  $\pm$  0.039 Table 1. Mean and standard deviation of accuracy on the test part for different sets of synthetic data and models. Bold font indicates the biggest value in the column.

| Data models | Test Models                           |                                     |                   |  |
|-------------|---------------------------------------|-------------------------------------|-------------------|--|
|             | 1-layer                               | 2-layer                             | 4-layer           |  |
| raw steps   | $0.859 \pm 0.005$                     | $0.881 \pm 0.004$                   | $0.867 \pm 0.122$ |  |
| strategy 1  | $0.851 \pm 0.007$                     | $[0.970 \pm 0.028] 0.986 \pm 0.014$ |                   |  |
| strategy 2  | $\left  {0.862 \pm 0.007} \right $    | $0.941 \pm 0.027$                   | $0.984 \pm 0.017$ |  |
| strategy 3  | $0.858 \pm 0.006$   $0.897 \pm 0.014$ |                                     | $0.965 \pm 0.045$ |  |

<span id="page-10-1"></span>Table 2. Mean and standard deviation of accuracy on the test part for different sets of synthetic data and models. Bold font indicates the biggest value in the column. Data distilled using all three architectures.

Image /page/10/Figure/5 description: The image displays four scatter plots arranged horizontally, each representing a different step in a process. The plots are titled 'step: 1', 'step: 14', 'step: 27', and 'step: 40'. Each plot shows two sets of data points marked with star shapes. One set of stars is colored teal with a black outline, and the other set is colored orange with a black outline. Both sets of points are plotted against axes labeled 'x1' and 'x2'. The plots show a progression where the points appear to be moving or changing their configuration over the steps. In each plot, there are also faint, curved lines that seem to represent a trajectory or a boundary, with the teal points generally following the upper curve and the orange points following the lower curve. The axes in all plots range from -4 to 4 for both x1 and x2, with grid lines at intervals of 2.

<span id="page-10-2"></span>Fig. 10. Synthetic objects of different internal steps (1st, 14th, 27th and last) that were used to train median-quality models. Data distilled using all three architectures.

## 7 Conclusion

In this work, we examined the distillation of tabular data using the algorithm proposed in [\[1\]](#page-11-0). We observed that models trained using distilled data can outperform models trained on the whole original data. We show that synthetic objects have some generalizability and can be successfully used in the training of different architectures. In addition, we found that it is sometimes better not to use synthetic learning rates, and explored some strategies to increase the number of training steps. As future work, we plan to change the memory complexity according to the work [\[8\]](#page-11-7). Also, we want to improve the distilled data generalizing ability using stochastic depth networks [\[13\]](#page-11-12). Finally, we would like to bring the distribution of synthetic objects closer to the original.

### 8 Acknowledgments

We thank Sergey Ivanov for detailed feedback on the initial version of this paper. This research was performed at the Center for Big Data Storage and Analysis of Lomonosov Moscow State University and was supported by the National Technology Initiative Foundation (13/1251/2018 of December 11, 2018).

### References

- <span id="page-11-0"></span>1. Wang, T., Zhu, J., Torralba, A., Efros, A. A.: Dataset Distillation. CoRR; abs/1811.10959 (2018)
- <span id="page-11-10"></span>2. Hinton, G., Vinyals, O., Dean, J.: Distilling the Knowledge in a Neural Network. In: NIPS Deep Learning and Representation Learning Workshop. (2015)
- <span id="page-11-11"></span>3. Sucholutsky, I., Schonlau, M.: Soft-Label Dataset Distillation and Text Dataset Distillation. CoRR; abs/1910.02551 (2019)
- <span id="page-11-8"></span>4. MNIST Handwritten Digit Database, <http://yann.lecun.com/exdb/mnist/>. Last accessed 24 Jun 2020.
- <span id="page-11-9"></span>5. Lecun, Y., Bottou, L., Bengio, Y., Haffner, P.: Gradient-Based Learning Applied to Document Recognition. In: Proceedings of the IEEE, vol. 86, pp. 2278–2324 (1998)
- <span id="page-11-1"></span>6. LeCun, Y., Boser, B., Denker, J. S., Henderson, D., Howard, R. E., Hubbard, W., and Jackel, L. D.: Backpropagation Applied to Handwritten Zip Code Recognition-Neural Computation. Neural Computation 1(4), 541–551 (1989)
- <span id="page-11-6"></span>7. Domke, J.: Generic Methods for Optimization-Based Modeling. In: Proceedings of the Fifteenth International Conference on Artificial Intelligence and Statistics, pp. 318–326. PMLR (2012)
- <span id="page-11-7"></span>8. Maclaurin, D., Duvenaud, D. and Adams, R.: Gradient-Based Hyperparameter Optimization Through Reversible Learning. CoRR; abs/1502.03492 (2015)
- <span id="page-11-2"></span>9. Bengio, Y.: Gradient-Based Optimization of Hyperparameters. Neural Computation 12(8), 1889–1900 (2000)
- <span id="page-11-3"></span>10. Baydin, A., Pearlmutter, B.: Automatic Differentiation of Algorithms for Machine Learning. In: Proceedings of the AutoML Workshop at the International Conference on Machine Learning (ICML). Beijing, China, June 21–26 (2014)
- <span id="page-11-4"></span>11. Liu, D. C., Nocedal, J.: On the Limited Memory BFGS Method for Large Scale Optimization. Mathematical Programming 45, 503–528 (1989)
- <span id="page-11-5"></span>12. Polyak, B.: Some Methods of Speeding Up the Convergence of Iteration Methods. USSR Computational Mathematics and Mathematical Physics, vol. 4, pp. 1–17 (1964)
- <span id="page-11-12"></span>13. Huang, G., Sun, Y., Liu, Z., Sedra, D. and Weinberger, K.: Deep Networks With Stochastic Depth. CoRR; abs/1603.09382 (2016)