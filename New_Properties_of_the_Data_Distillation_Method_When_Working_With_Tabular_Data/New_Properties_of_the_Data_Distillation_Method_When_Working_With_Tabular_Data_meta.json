{"table_of_contents": [{"title": "New Properties of the Data Distillation Method\nWhen Working With Tabular Data", "heading_level": null, "page_id": 0, "polygon": [[135.75, 115.5], [478.125, 115.5], [478.125, 146.373046875], [135.75, 146.373046875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 370.5], [229.5, 370.5], [229.5, 381.3046875], [133.5, 381.3046875]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[133.5, 117.0], [237.568359375, 117.0], [237.568359375, 128.2939453125], [133.5, 128.2939453125]]}, {"title": "3 Distillation Algorithm", "heading_level": null, "page_id": 1, "polygon": [[133.5, 399.0], [286.4267578125, 399.0], [286.4267578125, 410.6953125], [133.5, 410.6953125]]}, {"title": "Algorithm 2 Forward Pass", "heading_level": null, "page_id": 3, "polygon": [[133.5, 288.75], [258.0, 288.75], [258.0, 299.25], [133.5, 299.25]]}, {"title": "Algorithm 3 Backward Pass", "heading_level": null, "page_id": 3, "polygon": [[133.27734375, 450.0], [265.5, 450.0], [265.5, 460.96875], [133.27734375, 460.96875]]}, {"title": "4 Data and Models", "heading_level": null, "page_id": 3, "polygon": [[133.5, 608.25], [256.8427734375, 608.25], [256.8427734375, 619.91015625], [133.5, 619.91015625]]}, {"title": "5 Tabular Data Distillation", "heading_level": null, "page_id": 5, "polygon": [[133.5, 262.388671875], [303.75, 262.388671875], [303.75, 272.830078125], [133.5, 272.830078125]]}, {"title": "5.1 Examining Hyperparameters", "heading_level": null, "page_id": 5, "polygon": [[133.5, 283.5], [306.0, 285.0], [306.0, 294.75], [133.5, 294.486328125]]}, {"title": "5.2 The Distillation Algorithm Performance", "heading_level": null, "page_id": 6, "polygon": [[133.35205078125, 117.75], [364.271484375, 117.75], [364.271484375, 128.00390625], [133.35205078125, 128.00390625]]}, {"title": "5.3 The Problem of a Small Number of Epochs", "heading_level": null, "page_id": 8, "polygon": [[132.75439453125, 117.75], [381.005859375, 117.75], [381.005859375, 127.9072265625], [132.75439453125, 127.9072265625]]}, {"title": "10 <PERSON><PERSON> and <PERSON><PERSON>", "heading_level": null, "page_id": 9, "polygon": [[133.5, 92.25], [296.25, 92.25], [296.25, 101.70703125], [133.5, 101.70703125]]}, {"title": "6 Data Generalization to Different Architectures", "heading_level": null, "page_id": 9, "polygon": [[133.5, 335.25], [430.91015625, 335.25], [430.91015625, 346.88671875], [133.5, 346.88671875]]}, {"title": "Tabular Data Distillation 11", "heading_level": null, "page_id": 10, "polygon": [[342.45703125, 92.25], [480.75, 92.25], [480.75, 101.513671875], [342.45703125, 101.513671875]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 10, "polygon": [[133.5, 605.25], [220.5, 605.25], [220.5, 617.203125], [133.5, 617.203125]]}, {"title": "8 Acknowledgments", "heading_level": null, "page_id": 11, "polygon": [[133.5, 219.0], [261.0, 219.0], [261.0, 230.484375], [133.5, 230.484375]]}, {"title": "References", "heading_level": null, "page_id": 11, "polygon": [[133.5, 309.0], [198.0, 309.0], [198.0, 320.58984375], [133.5, 320.58984375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 41], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4110, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 51], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 609], ["Line", 119], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 628], ["Line", 38], ["TextInlineMath", 7], ["ListItem", 5], ["SectionHeader", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 91], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1813, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 97], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 80], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 979, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["Line", 203], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2390, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 147], ["Text", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2227, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 41], ["Text", 6], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["TableCell", 140], ["Line", 70], ["Caption", 3], ["Reference", 3], ["SectionHeader", 2], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["Text", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 44], ["ListItem", 13], ["Reference", 13], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/New_Properties_of_the_Data_Distillation_Method_When_Working_With_Tabular_Data"}