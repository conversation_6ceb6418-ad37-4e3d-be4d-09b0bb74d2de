# No Free Lunch in "Privacy for Free: How does Dataset Condensation Help Privacy"

<PERSON> <PERSON><PERSON> Apple Milad Nasr Google

#### Abstract

New methods designed to preserve data privacy require careful scrutiny. Failure to preserve privacy is hard to detect, and yet can lead to catastrophic results when a system implementing a "privacypreserving" method is attacked. A recent work selected for an Outstanding Paper Award at ICML 2022 [\[DZL22\]](#page-5-0) claims that dataset condensation (DC) significantly improves data privacy when training machine learning models. This claim is supported by theoretical analysis of a specific dataset condensation technique and an empirical evaluation of resistance to some existing membership inference attacks.

In this note we examine the claims in [\[DZL22\]](#page-5-0) and describe major flaws in the empirical evaluation of the method and its theoretical analysis. These flaws imply that [\[DZL22\]](#page-5-0) does not provide statistically significant evidence that DC improves the privacy of training ML models over a naive baseline. Moreover, previously published results show that DP-SGD, the standard approach to privacy preserving ML, simultaneously gives better accuracy and achieves a (provably) lower membership attack success rate.

### 1 Flaws in Experimental Evidence

In the problem of dataset condensation (DC) the input is a large dataset  $T = \{x_1, \ldots, x_n\}$ . The goal is to output a small dataset  $S = \{s_1, \ldots, s_m\}$ , where  $m = r_{ipc} \cdot n$  for some fraction  $r_{ipc} \ll 1$  (e.g.  $r_{ipc} = 1/100$ ) that is "almost" as good as T when used for training a learning algorithm. More formally, the expected generalization error when training a model on S should be comparable to expected generalization error when training a model on T.

In [\[DZL22\]](#page-5-0) the authors propose to use DC to improve data privacy of training a machine learning model. The primary focus of this work is the distribution matching (DM) technique of dataset condensation [\[ZB21\]](#page-6-0).

#### 1.1 The comparison to the naive baseline is incorrect

[\[DZL22\]](#page-5-0) compare the privacy of DM and several other dataset condensation schemes to the privacy of a much simpler condensation scheme: simply choose a random subset of the training data as the condensed dataset. When running this baseline, however, the paper incorrectly measures the attack advantage and the corresponding ROC curve. As a result, the reported baseline attack advantage rate of 92.8% is wrong: the correct figure is just 1.6%. This implies that DM's  $1.06 \pm 1.20\%$  attack advantage rate does not give a statistically significant advantage over the baseline.

We begin with some notation. Let  $\mathcal{A}_{\text{condense}}: T \to f$  be an algorithm that takes a full training dataset T, then condenses it to a dataset S via a condensation algorithm, and then trains a model  $f$  on this smaller dataset S. The proposed baseline considers a trivial condensation algorithm  $A_{\text{random}} : T \to f$  that takes a full training dataset T, "condenses" it to a dataset S by randomly sampling  $1\%$  $1\%$ <sup>1</sup> of the dataset T, and then trains a model f on S.

Recall what it means to measure the accuracy of an adversary who aims to perform a membership inference attack (MIA) [\[SSSS17;](#page-5-1) [SZHBFB18;](#page-5-2) [NSH19;](#page-5-3) [SDSOJ19;](#page-5-4) [JUO20;](#page-5-5) [NSTPC21;](#page-5-6) [SM21;](#page-5-7) [CCNSTT22\]](#page-5-8). Summarized briefly, we should perform the following steps:

<span id="page-0-0"></span><sup>&</sup>lt;sup>1</sup>The values ( $r_{inc} = 0.02$  and  $r_{inc} = 0.002$ ) are evaluated in [\[DZL22\]](#page-5-0) but we discuss only the results 1% case for brevity.

<span id="page-1-1"></span>Image /page/1/Figure/0 description: This is a Receiver Operating Characteristic (ROC) curve plotted on a log-log scale. The x-axis represents the False Positive Rate (FPR) and ranges from 10^-4 to 10^0. The y-axis represents the True Positive Rate (TPR) and also ranges from 10^-4 to 10^0. A dashed gray line, representing a random classifier, runs diagonally from the bottom left to the top right. The blue curve, representing the classifier's performance, starts at a very low FPR and TPR (around 10^-4), then rises gradually, staying close to the random classifier line for low FPR values. As the FPR increases, the TPR also increases, with the blue curve generally staying above the dashed line, indicating better-than-random performance. The curve shows a series of horizontal steps at very low TPR values before starting a more continuous upward trend.

Figure 1: Membership inference accuracy of a corrected baseline compression technique on CIFAR-10, measured over the full dataset.

- 1. The attacker and defender agree on a "universe" of possible samples  $U$ . For this note,  $U$  is the entire training data of CIFAR-10.
- [2](#page-1-0). The defender randomly samples a new dataset  $T \subset U$  by taking any  $x \in U$  with 50% probability.<sup>2</sup>
- 3. Then, the defender trains a model f on T and sends f to the attacker. (In  $[DZL22]$  the attacker is allowed access to intermediate computations used to produce  $f$  but neither their no our attacks make use of anything besides  $f$ .)
- 4. The attacker runs a membership inference attack on each example  $x \in U$ . The attacker scores 1 point if they correctly guess the membership status (i.e., the attacker guesses "member" and  $x \in T$  or the attacker guesses "nonmember" and  $x \notin T$ ); if the guess is incorrect the attacker scores 0 points.
- 5. The attack success rate is then computed the average of this score over each example in the dataset U. From here we can compute the **attack advantage** as  $2 \times$  (success rate  $-50\%$ ), i.e., how much better the attack performs than random chance.

This is how [\[DZL22\]](#page-5-0) evaluate the privacy of their proposed scheme when evaluating DM. But this is not how [\[DZL22\]](#page-5-0) evaluate the privacy of the baseline.

Specifically, when [\[DZL22\]](#page-5-0) evaluate the privacy of the baseline, the accuracy is measured on a set  $S \cup S'$ where S is the output of the baseline defense (i.e., a randomly selected  $1\%$  subset of T), but where S' is a non-member set of the same size randomly selected from  $T \setminus S$ . This is not correct. It is unfair to compare one scheme over a full universe  $U$  to another scheme that evaluates over a (much smaller and more "vulnerable"!) subset  $S \cup S'$ . Viewed differently, *attack accuracy* is strongly influenced by the overall base rate; when the base rate of members is 1%, the attack is much harder than when the base rate is 50%. And [\[DZL22\]](#page-5-0) here is comparing one attack with a base rate of 1% to another attack with a base rate of 50%.

When we re-evaluate this baseline correctly—by correctly following the protocol—we obtain an attack advantage of just [1](#page-1-1).6%, over fifty times weaker than the claim of  $92.8 \pm 5.31\%$ . Figure 1 shows the

<span id="page-1-0"></span><sup>&</sup>lt;sup>2</sup>While in general the sampling probability can be arbitrary,  $[DZL22]$  use 50%.

| Technique                        | Test Accuracy | DP $\varepsilon$ | Formal Gurantees | Attack Advantage |
|----------------------------------|---------------|------------------|------------------|------------------|
| DM [DZL22]                       | 59%           | N/A              | No               | 1.06 $\pm$ 1.2%  |
| DP-SGD (Same arch as in [DZL22]) | 61%           | 8                | Yes              | 2.8 $\pm$ 0.8%   |
| DP-SGD (Same arch as in [DZL22]) | 77%           | >5000            | No               | 3.0 $\pm$ 1.0%   |
| DP-SGD [TB20]                    | 64%           | 2                | Yes              | <0.8%            |
| DP-SGD [DBHSB22]                 | 66%           | 2                | Yes              | <0.8%            |

<span id="page-2-0"></span>Table 1: The DM scheme [\[DZL22\]](#page-5-0) is strictly dominated by DP-SGD [\[ACGMMTZ16\]](#page-5-9) along every dimension. We used  $\delta = 10^{-5}$  in the privacy analysis.

ROC curve of our proper baseline, and as we can see while the adversary can still distinguish which instances are sub-sampled, the adversary cannot determine which instance were member of the training dataset but not selected to be trained on. We also remark that the highest expected MIA advantage that can be attained when only 1% of the members dataset is used for training is 2%.

For comparison, MIA accuracy of DM on CIFAR-10 as stated  $[DZL22]$  is 1.06  $\pm$  1.20%. Thus DM does not provide a statistically significant improvement over the baseline invalidating the main claim of this work.

We note that  $[DZL22, p.7]$  $[DZL22, p.7]$  acknowledge the difference in the evaluation setups as "we vary a little bit the attack setting". They also evaluate MIA of DM initialized with a random subset S and attack restricted to  $S \cup S'$ . As noted by the authors, MIA accuracy of DM in this setting is comparable to that of the baseline further suggesting that DM does not have an advantage over the baseline.

#### 1.2 DM does not offer better privacy-utility tradeoff than published baselines

It is claimed that DM provides privacy "for free". This is not the case. First, note that DM achieves significantly lower accuracy than models trained on the entire dataset. For example, DM on CIFAR-10 with  $r_{inc} = 0.01$  achieves an accuracy of 59% compared to state-of-the-art models that reach well over 96% accuracy—so any privacy that is offered does cost at least 37% drop in accuracy.

But more importantly, previously published privacy-preserving defenses [\[DBHSB22;](#page-5-10) [TB20\]](#page-6-1) already have trained models that achieve higher accuracy while also providing better MIA protection and provable privacy guarantees. [\[TB20\]](#page-6-1) achieve 64% accuracy (5% higher accuracy) at the very conservative value of  $\varepsilon = 2$ (better provable privacy than the claimed "empirical" bound of  $\hat{\varepsilon} = 2.3$ ), while also maintaining an identical attack success rate (within the margin of error). A more recent work [\[DBHSB22\]](#page-5-10) also showed it is possible to achieve high accuracy with the same privacy budget.

To be even more direct in our comparison, we also train two new models using DP-SGD with the same network architecture as [\[DZL22\]](#page-5-0) (even though it is not optimized for the use of DP-SGD). Compared to DM's 59% accuracy and  $1.06 \pm 1.2\%$  membership inference attack advantage, our model achieves 77% accuracy (18% higher than DM) and  $3.0 \pm 0.8\%$  attack advantage (again within the margin of error). If we want to be even more conservative, we can train the same model used in DM to  $\varepsilon = 8$  differential privacy which still outperforms the accuracy of DM: 61% accuracy (2% better) and 2.8% attack advantage (again within the margin of error). Figure [2](#page-3-0) shows the membership inference attack ROC curve for this settings, also the membership inference attack advantage is 2.8%.

Table [1](#page-2-0) summarizes these results, and as we can see, DM is never superior along any dimension.

#### 1.3 The empirical $\hat{\varepsilon}$ is misleading

To relate the results to differential privacy and to compare against differentially private dataset generation techniques [\[DZL22\]](#page-5-0) claim an empirical value of  $\hat{\varepsilon} = 2.30$  for their algorithm. This approach appears to be based on prior work that establishes empirical *lower* bounds on the differential privacy parameter  $\varepsilon$  [\[JUO20;](#page-5-5) [NSTPC21\]](#page-5-6). Unlike in prior work, [\[DZL22\]](#page-5-0) use the value they calculate as a privacy guarantee, that is an

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This is a log-log plot of a Receiver Operating Characteristic (ROC) curve. The x-axis represents the False Positive Rate (FPR) and ranges from 10^-4 to 10^0. The y-axis represents the True Positive Rate (TPR) and also ranges from 10^-4 to 10^0. The plot shows a blue curve that generally increases from the bottom left to the top right, indicating the performance of a classifier. A dashed gray line, representing a random classifier, is also shown for comparison. The blue curve is mostly above the dashed line, suggesting better than random performance.

Figure 2: Training CIFAR-10 dataset by DP-SGD using the ConvNet architecture (same exact network as in [\[DZL22\]](#page-5-0)) and  $\varepsilon = 8$ .

upper bound on a privacy parameter. However [\[DZL22\]](#page-5-0) do not formally define the value they estimate and the specific  $\hat{\varepsilon}$  is neither an upper bound nor a lower bound on the differential privacy parameter.

The value  $\hat{\varepsilon}$  is computed by running a state-of-the-art membership inference [\[CCNSTT22\]](#page-5-8) attack and reading off one particular true positive to false positive ratio along this curve. Because  $\ln(\frac{TPR}{FPR})$  lower bounds  $\varepsilon$ , by running an attack and choosing the maximum ratio between these two quantities it is possible to lower bound DP parameter  $\varepsilon$ . However, the use of  $\hat{\varepsilon}$  computed in this way as a privacy guarantee has at least two flaws. While it is true that  $\ln(\frac{TPR}{FPR})$  lower bounds DP  $\varepsilon$ , it is invalid to run an attack once and read a single point off the ROC curve and use the true positive and false positive rate at this single point because we have no *statistical confidence* in the results.

Statistical techniques need to be used to ensure we make claims that are correct instead of measuring potential flukes. In particular, when prior work has reported results by studying the TPR-FPR tradeoff, it has used a threshold of  $95\%$  confidence [\[JUO20;](#page-5-5) [NSTPC21;](#page-5-6) [DBHSB22\]](#page-5-10).

The second flaw is that average-case measurements can be misleading when used as a privacy guarantee. A value of  $\hat{\varepsilon} = 0.2$  (corresponding to TPR/FPR ratio of  $\approx 1.22$ ) over the entire universe dataset can correspond to TPR/FPR ratio of  $\infty$  over a specific identifiable subgroup comprising 10% of the dataset (and ratio 1 over the remainder of the dataset). The situation can be even more extreme when the attacker is only interested in the membership of a very specific individual in the dataset. In contrast, differential privacy limits the ability to infer presence of any individual in any dataset. For this reason, when prior work has established empirical lower bounds on the value of DP  $\varepsilon$  they have used specific *auditing* techniques [\[JUO20;](#page-5-5) [NSTPC21\]](#page-5-6) that attempt to reason over worst-case datasets—not just average-case datasets.

To show the difference between an average case and a more adversarial case we designed a simple auditing scenario. We construct two datasets,  $D$  and  $D'$ , each of which is a modified version of the CIFAR-10 dataset where we removed all of the examples from class zero except one instance for dataset D and two instances for dataset  $D'$ . As a result when we do dataset condensation on dataset  $D$  all of the condensed examples of class zero are the only example for class zero, but for dataset  $D'$  condense examples contain information about both examples. Therefore, when train a classifier on these condense examples and we query the second example from the dataset  $D'$  it can reveal if the classifier model was trained on that example or not. Note that these two datasets differ by exactly one example, and so any algorithm that satisfies differential privacy

should produce an indistinguishable output.

We find that DM does not hide the presence of an extra point on this adversarially crafted dataset. To demonstrate this, we run the DM training pipeline<sup>[3](#page-4-0)</sup> to train many models on either D or D' and ask an adversary to distinguish between the two. Using this simple worst-case setting we a achieve 100% detection rate at distinguishing between  $D$  and  $D'$ .

## 2 Issues with the model and theoretical analysis of privacy

[\[DZL22\]](#page-5-0) aim to demonstrate that formal privacy guarantees can be established for the distribution matching (DM) technique of dataset condensation [\[ZB21\]](#page-6-0). For this purpose they consider a special case of the technique restricted to linear feature extractors. However, we believe that the dataset condensation algorithm resulting from this restriction cannot give any nontrivial results. In addition, the privacy analysis is based on an unrealistic assumption that itself implies differential privacy.

#### 2.1 Model

The theoretical analysis in this work is for the distribution matching technique of dataset condensation [\[ZB21\]](#page-6-0). Proposition 4.4 [\[DZL22\]](#page-5-0) characterizes the technique (for linear feature extractors) the rest of the subsequent privacy analysis is applied to this characterization. Specifically, this characterization states the following. If the technique is initialized<sup>[4](#page-4-1)</sup> with a set of examples  $S_{\text{init}} = \{s'_1, \ldots, s'_m\}$  and run on the training dataset  $T = \{x_1, \ldots, x_n\}$  then it will result in a set of examples  $S = \{s_1, \ldots, s_m\}$  such that for all  $i \in [m],$ 

$$
s_i = s'_i - \frac{1}{m} \sum_{j \in [m]} s'_j + \frac{1}{n} \sum_{j \in [n]} x_j.
$$

Note that this transformation amounts to centering the dataset at the mean of the original dataset (instead of the mean of  $S_{\text{init}}$ ). In the first initialization technique that [\[DZL22\]](#page-5-0) consider the dataset  $S_{\text{init}}$  is chosen to be a random subset of  $T$  then. In this case, by the standard high-dimensional concentration results,

$$
\frac{1}{m} \sum_{j \in [m]} s'_j \approx \frac{1}{n} \sum_{j \in [n]} x_j
$$

and the transformation is essentially an identity.

The second initialization scheme this work considers is sampling from the d-dimensional normal distribution subject to the mean of the samples being the origin (in particular,  $S_{\text{init}}$  is centered). DM applied to this dataset will result in a dataset which is centered at the mean of T but is otherwise independent of T. In particular, if the dataset  $T$  already happens to be centered (which is a common preprocessing step) then DM is just an identity transformation.

It is clear that such a transformation cannot possibly lead to a perceptible improvement in the generalization error of a learning algorithm over running the algorithm on  $S_{\text{init}}$  itself. Thus the theoretical analysis in [\[DZL22\]](#page-5-0) is uninformative.

### 2.2 Privacy analysis

The privacy analysis is based on the Assumption 4.8 [\[DZL22\]](#page-5-0) stating that given the training set T, a learning algorithm outputs a model  $\theta$ , from a distribution proportional to  $\exp(-\sum_{i\in[n]}l(\theta,x_i))$ , where l is the loss function. This is the standard exponential mechanism that is well-known<sup>[5](#page-4-2)</sup> to be  $\epsilon$ -DP where  $\epsilon$  is equal to

<span id="page-4-0"></span> $3$ We used Distribution matching (DM) approach with random initialization and color\_crop\_cutout\_flip\_scale\_rotate for augmentations, we used the same number of condensed images for both  $D$  and  $D'$ 

<span id="page-4-1"></span> $^{4}$ In Proposition 4.4 the initial dataset is referred to as S and the resulting condensed dataset  $S^*$ . This is inconsistent with the notation in the rest of the work so we a different notation.

<span id="page-4-2"></span> $5$ [https://en.wikipedia.org/wiki/Exponential\\_mechanism\\_\(differential\\_privacy\)]( https://en.wikipedia.org/wiki/Exponential_mechanism_(differential_privacy))

twice the range of the loss function  $l(\theta, \cdot)$ . Naturally, had the model been output from such a distribution we would have privacy and membership inference would not achieve high-accuracy (with reasonable assumptions on the range of the loss such as the one made in Assumption 4.9 of [\[DZL22\]](#page-5-0)).

The assumption is borrowed from [\[SDSOJ19\]](#page-5-4), where it is used to prove that, under this assumption black-box membership inference is as accurate as white-box and to design practical membership inference attacks. In particular, the use is "harmless" as it is not used to provide privacy guarantees.

Finally, we note that it should not be surprising or useful to know that an algorithm that uses little information about T to modify S (just the mean of T) is private on points in  $T \setminus S$ .

# Acknowledgements

We thank Gautam Kamath, Aleksandar Nikolov, Thomas Steinke, Kunal Talwar and Florian Tramer for their valuable commments and useful suggestions on this note.

# References

<span id="page-5-10"></span><span id="page-5-9"></span><span id="page-5-8"></span><span id="page-5-7"></span><span id="page-5-6"></span><span id="page-5-5"></span><span id="page-5-4"></span><span id="page-5-3"></span><span id="page-5-2"></span><span id="page-5-1"></span><span id="page-5-0"></span>

| [ACGMMTZ16] | M. Abadi, A. Chu, I. Goodfellow, H. B. McMahan, I. Mironov, K. Talwar, and L. Zhang.<br>"Deep learning with differential privacy". In: Proceedings of the 2016 ACM SIGSAC con-<br>ference on computer and communications security. 2016, pp. 308-318.                         |
|-------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [CCNSTT22]  | N. Carlini, S. Chien, M. Nasr, S. Song, A. Terzis, and F. Tramer. "Membership inference<br>attacks from first principles". In: 2022 IEEE Symposium on Security and Privacy (SP).<br>IEEE. 2022, pp. 1897-1914.                                                                |
| [DBHSB22]   | S. De, L. Berrada, J. Hayes, S. L. Smith, and B. Balle. "Unlocking high-accuracy differ-<br>entially private image classification through scale". In: $arXiv$ preprint $arXiv:2204.13650$<br>(2022).                                                                          |
| [DZL22]     | T. Dong, B. Zhao, and L. Lyu. "Privacy for Free: How does Dataset Condensation Help<br>Privacy?" In: $arXiv$ preprint $arXiv:2206.00240$ (2022).                                                                                                                              |
| [JUO20]     | M. Jagielski, J. Ullman, and A. Oprea. "Auditing differentially private machine learning:<br>How private is private sgd?" In: Advances in Neural Information Processing Systems 33<br>(2020), pp. 22205-22216.                                                                |
| [NSH19]     | M. Nasr, R. Shokri, and A. Houmansadr. "Comprehensive privacy analysis of deep learn-<br>ing: Passive and active white-box inference attacks against centralized and federated learn-<br>ing". In: 2019 IEEE symposium on security and privacy (SP). IEEE. 2019, pp. 739–753. |
| [NSTPC21]   | M. Nasr, S. Songi, A. Thakurta, N. Papernot, and N. Carlin. "Adversary instantiation:<br>Lower bounds for differentially private machine learning". In: 2021 IEEE Symposium on<br>Security and Privacy (SP). IEEE. 2021, pp. 866–882.                                         |
| [SDSOJ19]   | A. Sablayrolles, M. Douze, C. Schmid, Y. Ollivier, and H. Jégou. "White-box vs black-<br>box: Bayes optimal strategies for membership inference". In: International Conference on<br><i>Machine Learning.</i> PMLR. 2019, pp. 5558-5567.                                      |
| [SM21]      | L. Song and P. Mittal. "Systematic evaluation of privacy risks of machine learning models".<br>In: 30th USENIX Security Symposium (USENIX Security 21). 2021, pp. 2615–2632.                                                                                                  |
| [SSSS17]    | R. Shokri, M. Stronati, C. Song, and V. Shmatikov. "Membership inference attacks against<br>machine learning models". In: 2017 IEEE symposium on security and privacy (SP). IEEE.<br>2017, pp. 3-18.                                                                          |
| [SZHBFB18]  | A. Salem, Y. Zhang, M. Humbert, P. Berrang, M. Fritz, and M. Backes. "MI-leaks: Model<br>and data independent membership inference attacks and defenses on machine learning<br>models". In: $arXiv$ preprint $arXiv:1806.01246$ (2018).                                       |

<span id="page-6-1"></span>

| [TB20] | F. Tramer and D. Boneh. ".Differentially private learning needs better features (or much more data)". In: $arXiv$ preprint $arXiv:2011.11660$ (2020). |
|--------|-------------------------------------------------------------------------------------------------------------------------------------------------------|
|--------|-------------------------------------------------------------------------------------------------------------------------------------------------------|

<span id="page-6-0"></span>[ZB21] B. Zhao and H. Bilen. "Dataset Condensation with Distribution Matching". In: (2021). url: <https://arxiv.org/abs/2110.04181>.