{"table_of_contents": [{"title": "OD<sup>3</sup>: Optimization-free Dataset Distillation for Object\nDetection", "heading_level": null, "page_id": 0, "polygon": [[108.9228515625, 99.720947265625], [502.03125, 99.720947265625], [502.03125, 138.4453125], [108.9228515625, 138.4453125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 264.0], [328.5, 264.0], [328.5, 274.95703125], [282.75, 274.95703125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 504.75], [191.25, 504.75], [191.25, 515.8828125], [107.25, 515.8828125]]}, {"title": "2 Method", "heading_level": null, "page_id": 2, "polygon": [[106.5, 186.0], [167.25, 186.0], [167.25, 196.9365234375], [106.5, 196.9365234375]]}, {"title": "2.1 OD<sup>3</sup> Framework", "heading_level": null, "page_id": 3, "polygon": [[106.5, 119.25], [198.75, 119.25], [198.75, 130.130859375], [106.5, 130.130859375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 5, "polygon": [[107.25, 549.75], [192.0, 549.75], [192.0, 560.7421875], [107.25, 560.7421875]]}, {"title": "3.1 Experimental Results", "heading_level": null, "page_id": 6, "polygon": [[106.5, 471.75], [223.5234375, 471.75], [223.5234375, 483.01171875], [106.5, 483.01171875]]}, {"title": "3.2 Ablation Studies", "heading_level": null, "page_id": 7, "polygon": [[106.5, 510.08203125], [202.4560546875, 510.08203125], [202.4560546875, 521.68359375], [106.5, 521.68359375]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.25, 548.75390625], [197.3759765625, 548.75390625], [197.3759765625, 559.58203125], [107.25, 559.58203125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.98046875, 72.0], [183.75, 72.0], [183.75, 83.53125], [106.98046875, 83.53125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 181.5], [165.0, 181.5], [165.0, 192.779296875], [107.25, 192.779296875]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 11, "polygon": [[106.5, 72.0], [159.0, 72.0], [159.0, 84.35302734375], [106.5, 84.35302734375]]}, {"title": "A Limitations and Societal Impacts", "heading_level": null, "page_id": 11, "polygon": [[106.5, 98.25], [298.23046875, 98.25], [298.23046875, 109.828125], [106.5, 109.828125]]}, {"title": "B Algorithm", "heading_level": null, "page_id": 11, "polygon": [[106.5, 237.0], [182.25, 237.0], [182.25, 249.046875], [106.5, 249.046875]]}, {"title": "C Distilled Data Distribution Statistics", "heading_level": null, "page_id": 12, "polygon": [[106.75634765625, 72.0], [313.76953125, 72.0], [313.76953125, 83.86962890625], [106.75634765625, 83.86962890625]]}, {"title": "D More Ablations", "heading_level": null, "page_id": 13, "polygon": [[105.75, 72.0], [209.3291015625, 72.0], [209.3291015625, 83.86962890625], [105.75, 83.86962890625]]}, {"title": "E Proof of Theorem 2.2", "heading_level": null, "page_id": 14, "polygon": [[106.5, 332.96484375], [237.0, 332.96484375], [237.0, 343.79296875], [106.5, 343.79296875]]}, {"title": "Overlap handling.", "heading_level": null, "page_id": 15, "polygon": [[106.5, 111.75], [180.75, 111.75], [180.75, 122.2998046875], [106.5, 122.2998046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 67], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 89], ["Text", 5], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 634], ["Line", 86], ["Reference", 5], ["TextInlineMath", 4], ["Equation", 4], ["Text", 4], ["ListItem", 2], ["Footnote", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 84], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 503], ["Line", 99], ["Text", 12], ["Equation", 7], ["Reference", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 773], ["Line", 69], ["Text", 6], ["TextInlineMath", 5], ["Equation", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 627], ["TableCell", 470], ["Line", 67], ["Table", 3], ["Caption", 3], ["Text", 3], ["TableGroup", 3], ["Reference", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 30847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 390], ["Span", 372], ["Line", 56], ["Caption", 3], ["Table", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 15805, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 461], ["Line", 90], ["Text", 5], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1094, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 50], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 42], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 372], ["Line", 50], ["SectionHeader", 3], ["Text", 2], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["TableCell", 253], ["Line", 120], ["Text", 3], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11750, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 362], ["Span", 307], ["Line", 60], ["Text", 4], ["Table", 3], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 13719, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 925], ["TableCell", 208], ["Line", 94], ["TextInlineMath", 6], ["Equation", 3], ["Text", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7121, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 21], ["TextInlineMath", 3], ["<PERSON>Footer", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/OD3__Optimization-free_Dataset_Distillation_for_Object_Detection"}