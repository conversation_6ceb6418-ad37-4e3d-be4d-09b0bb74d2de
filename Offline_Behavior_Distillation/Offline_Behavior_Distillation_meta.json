{"table_of_contents": [{"title": "Offline Behavior Distillation", "heading_level": null, "page_id": 0, "polygon": [[200.25, 97.5], [409.9921875, 97.5], [409.9921875, 116.208984375], [200.25, 116.208984375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 291.0], [328.5, 291.0], [328.5, 301.833984375], [282.75, 301.833984375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 533.25], [191.25, 533.25], [191.25, 544.5], [107.25, 544.5]]}, {"title": "2 Related works", "heading_level": null, "page_id": 1, "polygon": [[106.5, 546.75], [200.25, 546.75], [200.25, 558.03515625], [106.5, 558.03515625]]}, {"title": "3 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[107.20458984375, 435.75], [195.75, 435.75], [195.75, 446.66015625], [107.20458984375, 446.66015625]]}, {"title": "3.1 Problem Setup", "heading_level": null, "page_id": 3, "polygon": [[106.5, 138.0], [194.25, 138.0], [194.25, 147.7265625], [106.5, 147.7265625]]}, {"title": "3.2 Backpropagation through Time", "heading_level": null, "page_id": 3, "polygon": [[106.5, 316.916015625], [265.5, 316.916015625], [265.5, 326.583984375], [106.5, 326.583984375]]}, {"title": "4 Methods", "heading_level": null, "page_id": 3, "polygon": [[107.20458984375, 484.5], [171.228515625, 484.5], [171.228515625, 495.38671875], [107.20458984375, 495.38671875]]}, {"title": "4.1 Data-based and Policy-based BC", "heading_level": null, "page_id": 3, "polygon": [[106.5, 609.75], [269.25, 609.75], [269.25, 619.91015625], [106.5, 619.91015625]]}, {"title": "4.2 Action-value weighted PBC", "heading_level": null, "page_id": 4, "polygon": [[106.5, 270.75], [248.326171875, 270.75], [248.326171875, 280.7578125], [106.5, 280.7578125]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 5, "polygon": [[107.25, 567.0], [192.146484375, 567.0], [192.146484375, 578.14453125], [107.25, 578.14453125]]}, {"title": "5.1 Main Results", "heading_level": null, "page_id": 6, "polygon": [[106.5, 423.75], [187.5, 423.75], [187.5, 433.8984375], [106.5, 433.8984375]]}, {"title": "6 Discussion", "heading_level": null, "page_id": 8, "polygon": [[107.25, 346.11328125], [181.5, 346.11328125], [181.5, 358.48828125], [107.25, 358.48828125]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.2333984375, 72.0], [183.75, 72.0], [183.75, 83.724609375], [106.2333984375, 83.724609375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[106.98046875, 224.25], [165.0, 224.25], [165.0, 234.544921875], [106.98046875, 234.544921875]]}, {"title": "A Proofs", "heading_level": null, "page_id": 12, "polygon": [[105.0, 70.5], [163.5, 70.5], [163.5, 84.0], [105.0, 84.0]]}, {"title": "A.1 Proof of Theorem 2", "heading_level": null, "page_id": 12, "polygon": [[106.5, 102.75], [216.7998046875, 102.75], [216.7998046875, 114.75], [106.5, 114.75]]}, {"title": "B Implementation Details", "heading_level": null, "page_id": 13, "polygon": [[106.5, 243.0], [249.0, 243.0], [249.0, 254.07421875], [106.5, 254.07421875]]}, {"title": "C Training Time Comparison", "heading_level": null, "page_id": 13, "polygon": [[106.98046875, 451.30078125], [269.54296875, 451.30078125], [269.54296875, 462.90234375], [106.98046875, 462.90234375]]}, {"title": "D Examples of Distilled Data", "heading_level": null, "page_id": 14, "polygon": [[106.5, 70.962890625], [265.5, 70.962890625], [265.5, 84.111328125], [106.5, 84.111328125]]}, {"title": "E The Performance of Av-PBC across Different Synthetic Data Sizes", "heading_level": null, "page_id": 14, "polygon": [[106.5, 358.681640625], [466.76953125, 358.681640625], [466.76953125, 371.056640625], [106.5, 371.056640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 50], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 55], ["Text", 3], ["ListItem", 3], ["TextInlineMath", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 746], ["Line", 53], ["Text", 3], ["TextInlineMath", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 736], ["Line", 87], ["TextInlineMath", 7], ["SectionHeader", 4], ["Equation", 3], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1240], ["Line", 81], ["TextInlineMath", 11], ["Equation", 4], ["Reference", 4], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 62], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["Reference", 2], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 544], ["TableCell", 162], ["Line", 51], ["TextInlineMath", 3], ["Text", 3], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6412, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 666], ["TableCell", 219], ["Line", 128], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13239, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 288], ["TableCell", 97], ["Line", 50], ["Text", 6], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1643, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 47], ["ListItem", 13], ["Reference", 13], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 48], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 29], ["ListItem", 11], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1204], ["Line", 213], ["TextInlineMath", 3], ["Equation", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 12518, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["TableCell", 82], ["Line", 65], ["Text", 5], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Table", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 9249, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["TableCell", 51], ["Line", 29], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Picture", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4801, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Offline_Behavior_Distillation"}