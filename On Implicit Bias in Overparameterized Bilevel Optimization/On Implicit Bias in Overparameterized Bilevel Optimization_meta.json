{"table_of_contents": [{"title": "On Implicit Bias in Overparameterized Bilevel Optimization", "heading_level": null, "page_id": 0, "polygon": [[111.0, 89.25], [485.296875, 89.25], [485.296875, 103.9306640625], [111.0, 103.9306640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 186.978515625], [148.5, 186.978515625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 498.48046875], [133.5, 498.48046875], [133.5, 509.30859375], [54.0, 509.30859375]]}, {"title": "2. Background", "heading_level": null, "page_id": 1, "polygon": [[306.0, 474.0], [383.25, 474.0], [383.25, 484.9453125], [306.0, 484.9453125]]}, {"title": "3. Equilibrium Concepts", "heading_level": null, "page_id": 3, "polygon": [[54.0, 579.0], [181.5, 579.0], [181.5, 590.1328125], [54.0, 590.1328125]]}, {"title": "3.1. Cold-Start Equilibrium", "heading_level": null, "page_id": 3, "polygon": [[306.0, 172.5], [426.12890625, 172.5], [426.12890625, 182.6279296875], [306.0, 182.6279296875]]}, {"title": "3.2. Warm-Start Equilibrium", "heading_level": null, "page_id": 3, "polygon": [[306.0, 451.5], [432.75, 451.5], [432.75, 461.35546875], [306.0, 461.35546875]]}, {"title": "3.3. Solution Properties", "heading_level": null, "page_id": 4, "polygon": [[54.0, 69.0], [155.25, 69.0], [155.25, 79.42236328125], [54.0, 79.42236328125]]}, {"title": "3.3.1. IMPLIC<PERSON> BIAS FROM HYPERGRAD APPROX.", "heading_level": null, "page_id": 4, "polygon": [[306.59765625, 612.75], [522.94921875, 612.75], [522.94921875, 622.6171875], [306.59765625, 622.6171875]]}, {"title": "4. Empirical Overparameterization Results", "heading_level": null, "page_id": 5, "polygon": [[306.75, 68.25], [528.75, 68.25], [528.75, 79.80908203125], [306.75, 79.80908203125]]}, {"title": "4.1. Inner Overparameterization: Dataset Distillation", "heading_level": null, "page_id": 5, "polygon": [[306.0, 265.5], [535.5, 265.5], [535.5, 275.537109375], [306.0, 275.537109375]]}, {"title": "4.2. Outer Overparameterization: Anti-Distillation", "heading_level": null, "page_id": 7, "polygon": [[54.0, 502.5], [272.25, 502.5], [272.25, 512.40234375], [54.0, 512.40234375]]}, {"title": "5. Related Work", "heading_level": null, "page_id": 8, "polygon": [[54.0, 398.25], [138.8056640625, 398.25], [138.8056640625, 409.1484375], [54.0, 409.1484375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 503.5078125], [377.25, 503.5078125], [377.25, 514.3359375], [306.0, 514.3359375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.32568359375], [54.0, 79.32568359375]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 12, "polygon": [[54.0, 315.0], [155.25, 315.0], [155.25, 326.197265625], [54.0, 326.197265625]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 13, "polygon": [[54.0, 66.75], [106.5, 66.75], [106.5, 79.90576171875], [54.0, 79.90576171875]]}, {"title": "A. Notation", "heading_level": null, "page_id": 14, "polygon": [[52.5, 65.25], [117.0, 65.25], [117.0, 79.7607421875], [52.5, 79.7607421875]]}, {"title": "<PERSON>. Extended Related Work", "heading_level": null, "page_id": 15, "polygon": [[54.0, 67.5], [192.75, 67.5], [192.75, 79.083984375], [54.0, 79.083984375]]}, {"title": "<PERSON><PERSON> Derivations", "heading_level": null, "page_id": 15, "polygon": [[54.0, 596.70703125], [129.75, 596.70703125], [129.75, 608.30859375], [54.0, 608.30859375]]}, {"title": "C.1. Minimum-Norm Response Jacobian", "heading_level": null, "page_id": 15, "polygon": [[54.0, 616.5], [229.5, 616.5], [229.5, 627.2578125], [54.0, 627.2578125]]}, {"title": "C.2. Iterated Projection.", "heading_level": null, "page_id": 16, "polygon": [[54.0, 191.25], [159.0, 191.25], [159.0, 201.0], [54.0, 201.0]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 17, "polygon": [[54.0, 67.5], [103.5, 67.5], [103.5, 79.03564453125], [54.0, 79.03564453125]]}, {"title": "D.1. Proof of Statement 3.1", "heading_level": null, "page_id": 18, "polygon": [[54.0, 537.75], [170.25, 537.75], [170.25, 547.59375], [54.0, 547.59375]]}, {"title": "D.2. <PERSON><PERSON> of Theorem 3.1", "heading_level": null, "page_id": 19, "polygon": [[54.0, 69.0], [165.0, 69.0], [165.0, 78.890625], [54.0, 78.890625]]}, {"title": "D.3. Proof of Remark 3.2", "heading_level": null, "page_id": 19, "polygon": [[54.0, 512.25], [162.75, 512.25], [162.75, 522.45703125], [54.0, 522.45703125]]}, {"title": "D.4. Proof of Statement 3.2", "heading_level": null, "page_id": 19, "polygon": [[54.0, 664.5], [171.0, 664.5], [171.0, 674.82421875], [54.0, 674.82421875]]}, {"title": "E. Proximal Best-Response", "heading_level": null, "page_id": 20, "polygon": [[54.0, 186.0], [194.25, 186.0], [194.25, 197.033203125], [54.0, 197.033203125]]}, {"title": "F. Equivalence Between <PERSON>rolling and Neumann Hypergradients", "heading_level": null, "page_id": 20, "polygon": [[54.0, 425.25], [387.0, 425.25], [387.0, 436.9921875], [54.0, 436.9921875]]}, {"title": "G. Experimental Details and Extended Results", "heading_level": null, "page_id": 21, "polygon": [[54.0, 397.16015625], [294.0, 397.16015625], [294.0, 408.76171875], [54.0, 408.76171875]]}, {"title": "G.1. Details and Extended Results for Dataset Distillation.", "heading_level": null, "page_id": 21, "polygon": [[54.0, 466.5], [302.25, 466.5], [302.25, 476.82421875], [54.0, 476.82421875]]}, {"title": "G.2. Details and Extended Results for Anti-Distillation.", "heading_level": null, "page_id": 23, "polygon": [[54.0, 253.5], [289.5, 253.5], [289.5, 262.96875], [54.0, 262.96875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 24, "polygon": [[54.0, 557.25], [129.75, 557.25], [129.75, 569.25], [54.0, 569.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 443], ["Line", 97], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 3], ["Equation", 2], ["Footnote", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7049, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 553], ["Line", 114], ["TableCell", 80], ["Text", 7], ["Caption", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4984, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 739], ["Line", 106], ["TextInlineMath", 6], ["Caption", 2], ["Equation", 2], ["Figure", 1], ["Footnote", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 829], ["Line", 120], ["TableCell", 16], ["TextInlineMath", 7], ["Text", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4800, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 677], ["Line", 121], ["TextInlineMath", 11], ["Text", 6], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 716], ["Line", 104], ["Text", 6], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 100], ["Text", 4], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1790, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 113], ["TableCell", 41], ["TextInlineMath", 4], ["Caption", 3], ["Text", 3], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7233, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 132], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 107], ["ListItem", 31], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 105], ["ListItem", 31], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 106], ["ListItem", 30], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 26], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 15], ["ListItem", 8], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 502], ["TableCell", 148], ["Line", 91], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Table", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4389, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 439], ["Line", 62], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["Line", 66], ["Equation", 13], ["Text", 8], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 911], ["Line", 62], ["Equation", 18], ["Text", 5], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1004], ["Line", 104], ["TextInlineMath", 7], ["Equation", 7], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 732], ["Line", 82], ["TextInlineMath", 6], ["Equation", 5], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1555, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 1052], ["Line", 152], ["Equation", 11], ["TextInlineMath", 5], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["Line", 148], ["Text", 8], ["Equation", 6], ["TextInlineMath", 2], ["SectionHeader", 2], ["Caption", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["TableCell", 63], ["Line", 60], ["Caption", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3216, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 65], ["Text", 4], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1419, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 104], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1964, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 607], ["Line", 85], ["TableCell", 46], ["ListItem", 5], ["Text", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 10477, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/On Implicit Bias in Overparameterized Bilevel Optimization"}