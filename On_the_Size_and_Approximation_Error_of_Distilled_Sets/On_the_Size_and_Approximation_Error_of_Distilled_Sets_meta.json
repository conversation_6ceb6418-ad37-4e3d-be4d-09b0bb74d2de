{"table_of_contents": [{"title": "On the Size and Approximation Error of Distilled Sets", "heading_level": null, "page_id": 0, "polygon": [[65.25, 89.25], [493.6640625, 89.25], [493.6640625, 106.927734375], [65.25, 106.927734375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[64.5, 467.9296875], [174.0, 467.9296875], [174.0, 481.078125], [64.5, 481.078125]]}, {"title": "2. Related work", "heading_level": null, "page_id": 1, "polygon": [[64.5, 203.80078125], [180.75, 203.80078125], [180.75, 218.8828125], [64.5, 218.8828125]]}, {"title": "3. Background", "heading_level": null, "page_id": 2, "polygon": [[65.25, 206.25], [171.75, 206.25], [171.75, 220.81640625], [65.25, 220.81640625]]}, {"title": "3.1. Kernel ridge regression", "heading_level": null, "page_id": 2, "polygon": [[65.21923828125, 357.75], [224.25, 357.75], [224.25, 369.896484375], [65.21923828125, 369.896484375]]}, {"title": "4. Main result: on the existence of small distilled sets", "heading_level": null, "page_id": 3, "polygon": [[64.5, 618.0], [424.5, 618.0], [424.5, 632.28515625], [64.5, 632.28515625]]}, {"title": "5. Experimental Study", "heading_level": null, "page_id": 8, "polygon": [[65.25, 206.89453125], [222.75, 206.89453125], [222.75, 221.203125], [65.25, 221.203125]]}, {"title": "5.1. 2d Gaussian Random Fields", "heading_level": null, "page_id": 8, "polygon": [[65.25, 302.02734375], [250.716796875, 302.02734375], [250.716796875, 314.40234375], [65.25, 314.40234375]]}, {"title": "5.2. Two Gaussian Clusters Classification", "heading_level": null, "page_id": 9, "polygon": [[65.25, 210.568359375], [300.0, 208.5], [300.0, 222.169921875], [65.25, 222.169921875]]}, {"title": "5.3. MNIST Binary Classification", "heading_level": null, "page_id": 9, "polygon": [[65.25, 548.75390625], [255.0, 548.75390625], [255.0, 560.35546875], [65.25, 560.35546875]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 10, "polygon": [[65.181884765625, 360.75], [167.25, 360.75], [167.25, 374.537109375], [65.181884765625, 374.537109375]]}, {"title": "acknowledgements", "heading_level": null, "page_id": 10, "polygon": [[65.25, 622.5], [193.5, 622.5], [193.5, 636.92578125], [65.25, 636.92578125]]}, {"title": "References", "heading_level": null, "page_id": 11, "polygon": [[63.75, 89.25], [138.75, 89.25], [138.75, 105.0908203125], [63.75, 105.0908203125]]}, {"title": "A. Experiment Details", "heading_level": null, "page_id": 15, "polygon": [[65.25, 90.685546875], [222.0, 90.685546875], [222.0, 105.380859375], [65.25, 105.380859375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 41], ["Text", 7], ["SectionHeader", 2], ["Picture", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4176, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 43], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 816], ["Line", 55], ["TextInlineMath", 4], ["Text", 4], ["Reference", 4], ["ListItem", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 752], ["Line", 76], ["TextInlineMath", 5], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["Equation", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 827], ["Line", 122], ["Text", 5], ["TextInlineMath", 5], ["Reference", 5], ["Equation", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1267, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 879], ["Line", 113], ["TextInlineMath", 7], ["Equation", 5], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1175, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 773], ["Line", 143], ["Equation", 5], ["TextInlineMath", 5], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1463, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1213], ["Line", 339], ["Text", 4], ["Equation", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4282, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 624], ["Line", 147], ["Reference", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1942, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 718], ["Line", 110], ["Reference", 4], ["TextInlineMath", 2], ["SectionHeader", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2153, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 65], ["TableCell", 42], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6083, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 39], ["ListItem", 11], ["Reference", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 40], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 38], ["ListItem", 14], ["Reference", 14], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 19], ["ListItem", 7], ["Reference", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 23], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/On_the_Size_and_Approximation_Error_of_Distilled_Sets"}