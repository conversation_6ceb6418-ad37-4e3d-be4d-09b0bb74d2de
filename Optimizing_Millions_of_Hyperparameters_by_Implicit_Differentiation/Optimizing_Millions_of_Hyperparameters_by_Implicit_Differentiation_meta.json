{"table_of_contents": [{"title": "Optimizing Millions of Hyperparameters by Implicit Differentiation", "heading_level": null, "page_id": 0, "polygon": [[82.4765625, 63.7119140625], [552.75, 63.7119140625], [552.75, 78.50390625], [82.4765625, 78.50390625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[166.5, 156.0], [218.25, 157.5], [218.25, 168.802734375], [166.5, 168.802734375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[72.652587890625, 425.25], [170.25, 425.25], [170.25, 436.60546875], [72.652587890625, 436.60546875]]}, {"title": "Contributions", "heading_level": null, "page_id": 0, "polygon": [[327.0, 536.25], [398.25, 536.25], [398.25, 545.66015625], [327.0, 545.66015625]]}, {"title": "2 Overview of Proposed Algorithm", "heading_level": null, "page_id": 1, "polygon": [[74.25, 314.25], [291.65625, 314.25], [291.65625, 325.23046875], [74.25, 325.23046875]]}, {"title": "2.1 Proposed Algorithms", "heading_level": null, "page_id": 2, "polygon": [[74.25, 609.75], [209.478515625, 609.75], [209.478515625, 620.296875], [74.25, 620.296875]]}, {"title": "3 Related Work", "heading_level": null, "page_id": 2, "polygon": [[74.25, 675.75], [178.400390625, 675.75], [178.400390625, 687.19921875], [74.25, 687.19921875]]}, {"title": "4 Method", "heading_level": null, "page_id": 3, "polygon": [[74.25, 491.25], [142.5, 491.25], [142.5, 502.34765625], [74.25, 502.34765625]]}, {"title": "4.1 Hyperparameter Opt. is Pure-Response", "heading_level": null, "page_id": 3, "polygon": [[74.25, 560.25], [303.908203125, 560.25], [303.908203125, 571.5703125], [74.25, 571.5703125]]}, {"title": "4.2 Unrolled Optimization and the IFT", "heading_level": null, "page_id": 3, "polygon": [[325.5, 497.25], [532.810546875, 497.25], [532.810546875, 507.76171875], [325.5, 507.76171875]]}, {"title": "4.3 Scope and Lim<PERSON>s", "heading_level": null, "page_id": 4, "polygon": [[74.25, 696.75], [213.75, 696.75], [213.75, 706.1484375], [74.25, 706.1484375]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 4, "polygon": [[326.25, 258.0], [422.25, 258.0], [422.25, 269.15625], [326.25, 269.15625]]}, {"title": "5.1 Approximate Inversion Algorithms", "heading_level": null, "page_id": 4, "polygon": [[326.25, 465.0], [528.75, 465.0], [528.75, 474.890625], [326.25, 474.890625]]}, {"title": "5.2 Overfitting a Small Validation Set", "heading_level": null, "page_id": 5, "polygon": [[74.25, 449.25], [273.0, 449.25], [273.0, 459.03515625], [74.25, 459.03515625]]}, {"title": "5.3 Dataset Distillation", "heading_level": null, "page_id": 5, "polygon": [[74.25, 672.0], [199.5, 672.0], [199.5, 682.55859375], [74.25, 682.55859375]]}, {"title": "5.4 Learned Data Augmentation", "heading_level": null, "page_id": 5, "polygon": [[326.25, 414.75], [498.0, 416.25], [498.0, 426.1640625], [326.25, 426.1640625]]}, {"title": "5.5 RNN Hyperparameter Optimization", "heading_level": null, "page_id": 6, "polygon": [[323.9296875, 371.25], [537.75, 371.25], [537.75, 381.498046875], [323.9296875, 381.498046875]]}, {"title": "5.6 Effects of Many Hyperparameters", "heading_level": null, "page_id": 7, "polygon": [[74.25, 563.25], [272.53125, 563.25], [272.53125, 573.1171875], [74.25, 573.1171875]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[326.25, 559.58203125], [412.98046875, 559.58203125], [412.98046875, 570.41015625], [326.25, 570.41015625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[74.25, 72.75], [187.5, 72.75], [187.5, 84.111328125], [74.25, 84.111328125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[74.25, 209.021484375], [138.43212890625, 209.021484375], [138.43212890625, 220.623046875], [74.25, 220.623046875]]}, {"title": "Optimizing Millions of Hyperparameters by Implicit Differentiation\nAppendix", "heading_level": null, "page_id": 11, "polygon": [[83.25, 92.25], [553.5, 92.25], [553.5, 123.2666015625], [83.25, 123.2666015625]]}, {"title": "A Extended Background", "heading_level": null, "page_id": 11, "polygon": [[74.25, 153.75], [230.25, 153.75], [230.25, 165.6123046875], [74.25, 165.6123046875]]}, {"title": "B Extended Related Work", "heading_level": null, "page_id": 11, "polygon": [[74.25, 690.75], [241.154296875, 690.75], [241.154296875, 701.89453125], [74.25, 701.89453125]]}, {"title": "C Implicit Function Theorem", "heading_level": null, "page_id": 13, "polygon": [[74.25, 71.25], [259.5, 71.25], [259.5, 84.0146484375], [74.25, 84.0146484375]]}, {"title": "D Proofs", "heading_level": null, "page_id": 14, "polygon": [[72.75, 70.5], [138.0, 69.75], [138.0, 83.53125], [72.75, 84.75]]}, {"title": "E Experiments", "heading_level": null, "page_id": 16, "polygon": [[74.25, 72.75], [171.9755859375, 72.75], [171.9755859375, 84.0146484375], [74.25, 84.0146484375]]}, {"title": "E.1 Overfitting a Small Validation Set", "heading_level": null, "page_id": 16, "polygon": [[74.25, 201.75], [275.25, 201.75], [275.25, 211.921875], [74.25, 211.921875]]}, {"title": "E.2 Dataset Distillation", "heading_level": null, "page_id": 16, "polygon": [[74.25, 636.75], [201.75, 636.75], [201.75, 647.75390625], [74.25, 647.75390625]]}, {"title": "E.3 Learned Data Augmentation", "heading_level": null, "page_id": 16, "polygon": [[74.25, 697.5], [248.25, 697.5], [248.25, 706.921875], [74.25, 706.921875]]}, {"title": "E.4 RNN Hyperparameter Optimization", "heading_level": null, "page_id": 16, "polygon": [[325.5, 147.6298828125], [538.5, 147.6298828125], [538.5, 157.2978515625], [325.5, 157.2978515625]]}, {"title": "CIFAR-100 Distillation", "heading_level": null, "page_id": 17, "polygon": [[256.693359375, 151.5], [376.5, 151.5], [376.5, 163.5], [256.693359375, 163.5]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 93], ["Text", 9], ["ListItem", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7133, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 972], ["Line", 182], ["Text", 6], ["TextInlineMath", 6], ["Equation", 5], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 810], ["Line", 156], ["TableCell", 25], ["TextInlineMath", 5], ["Text", 5], ["Reference", 4], ["Caption", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1095], ["Line", 243], ["TableCell", 72], ["Text", 8], ["Reference", 4], ["Table", 3], ["SectionHeader", 3], ["Caption", 1], ["Equation", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4694, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 768], ["Line", 178], ["TextInlineMath", 8], ["Text", 7], ["Equation", 4], ["SectionHeader", 3], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1086, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 479], ["Line", 128], ["Text", 6], ["Figure", 3], ["Caption", 3], ["SectionHeader", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2462, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["Line", 57], ["TableCell", 30], ["Reference", 4], ["Caption", 3], ["Text", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Table", 1], ["SectionHeader", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 5756, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 110], ["TableCell", 64], ["Text", 9], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2261, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 104], ["ListItem", 22], ["Reference", 21], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 301], ["Line", 105], ["ListItem", 23], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 104], ["ListItem", 25], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 86], ["TextInlineMath", 4], ["Text", 4], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 71], ["TableCell", 66], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["Line", 31], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1121], ["Line", 316], ["Equation", 6], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 11768, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 586], ["Line", 178], ["TextInlineMath", 3], ["Equation", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3482, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 100], ["Text", 8], ["SectionHeader", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 867, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 60], ["Span", 7], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Caption", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Optimizing_Millions_of_Hyperparameters_by_Implicit_Differentiation"}