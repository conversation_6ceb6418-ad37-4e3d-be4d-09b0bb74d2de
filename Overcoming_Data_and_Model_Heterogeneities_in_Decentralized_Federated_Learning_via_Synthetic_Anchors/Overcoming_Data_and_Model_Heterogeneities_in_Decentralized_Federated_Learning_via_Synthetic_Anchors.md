# Overcoming Data and Model heterogeneities in Decentralized Federated Learning via Synthetic Anchors

Chun<PERSON><PERSON>  $12$  Kartik Srinivas  $13$  Xin Zhang  $4$  Xiaoxiao Li $12$ 

## Abstract

Conventional Federated Learning (FL) involves collaborative training of a global model while maintaining user data privacy. One of its branches, decentralized FL, is a serverless network that allows clients to own and optimize different local models separately, which results in saving management and communication resources. Despite the promising advancements in decentralized FL, it may reduce model generalizability due to lacking a global model. In this scenario, managing data and model heterogeneity among clients becomes a crucial problem, which poses a unique challenge that must be overcome: *How can every client's local model learn generalizable representation in a decentralized manner?* To address this challenge, we propose a novel Decentralized FL technique by introducing Synthetic Anchors, dubbed as DESA. Based on the theory of domain adaptation and Knowledge Distillation (KD), we theoretically and empirically show that synthesizing global anchors based on raw data distribution facilitates mutual knowledge transfer. We further design two effective regularization terms for local training: *1) REG loss* that regularizes the distribution of the client's latent embedding with the anchors and *2) KD loss* that enables clients to learn from others. Through extensive experiments on diverse client data distributions, we showcase the effectiveness of DESA in enhancing both inter- and intra-domain accuracy of each client. The implementation of DESA can be found at: <https://github.com/ubc-tea/DESA>

## 1. Introduction

Federated learning (FL) has emerged as an important paradigm to perform machine learning from multi-source data in a distributed manner. Conventional FL techniques leverage a large number of clients to process a global model learning, which is coordinated by a central server. However, there arises concerns on increased vulnerability of system failures and trustworthiness concerns for the central server design in the conventional FL. An emerging paradigm, called decentralized FL, is featured by its serverless setting to address the issues. Recent work has shown decentralized FL framework can provide more flexibility and solubility (Beltrán et al., [2023;](#page-8-0) [Yuan et al.,](#page-11-0) [2023b\)](#page-11-0), where they relax the use of central server for model aggregation. However, this could deflect the generalization capability of each client model. Although most of the works in decentralized FL focus on model personalization [\(Huang](#page-9-0) [et al.,](#page-9-0) [2022\)](#page-9-0), we consider it crucial for decentralized FL to be generalizable since local training data may not align with local testing data in practice.

Client heterogeneity is a common phenomenon in FL that can deteriorate model generalizability. On one hand, dataheterogeneity relaxes the assumption that the data across all the client are independent and identically distributed (i.i.d.). To solve the problem, a plethora of methods have been proposed. However, most of them assumes that the model architectures are invariant across clients [\(Li et al.,](#page-9-1) [2020b;](#page-9-1)[a;](#page-9-2) [2021b;](#page-9-3) [Karimireddy et al.,](#page-9-4) [2020;](#page-9-4) [Tang et al.,](#page-10-0) [2022\)](#page-10-0). On the other hand, many practical FL applications (*e.g.*, Internet-of-Things and mobile device system) face modelheterogeneity, where clients have devices with different computation capabilities and memory constraints. In conventional FL, strategies have been proposed to leverage knowledge transferring to address the model heterogeneity issue, *e.g.*, server collects labeled data with the similar distribution as the client data or clients transmit models [\(Lin et al.,](#page-9-5) [2020;](#page-9-5) [Zhu et al.,](#page-11-1) [2021\)](#page-11-1). However, these operations usually require a server to coordinate the knowledge distillation and assume global data is available [\(Li & Wang,](#page-9-6) [2019;](#page-9-6) [Lin et al.,](#page-9-5) [2020;](#page-9-5) [Tan et al.,](#page-10-1) [2022\)](#page-10-1). Thus, they are not applicable to decentralized FL and may not fit into real-world scenarios. Recently, there are works proposing to perform test-time

<sup>&</sup>lt;sup>1</sup>Department of Electrical and Computer Engineering, The University of British Columbia, Canada <sup>2</sup>Vector Institute, Canada <sup>3</sup>Indian Institute of Technology Hyderabad, India (Work was done during internship at UBC)  ${}^{4}$ Meta, U.S.A.. Correspondence to: Xiaoxiao Li <<EMAIL>>.

*Proceedings of the*  $41^{st}$  *International Conference on Machine Learning*, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).

<span id="page-1-2"></span>Image /page/1/Figure/1 description: The image displays two diagrams, labeled (a) and (b), illustrating decision boundaries in a machine learning context. Diagram (a) shows two decision boundaries, labeled 'Decision Boundary 1' and 'Decision Boundary 2', separating data points. A legend indicates that blue triangles represent 'Client 1', orange triangles represent 'Client 2', and green triangles represent 'Synthetic anchor'. Triangles denote 'Class a' and circles denote 'Class b'. Diagram (b) shows a similar set of data points, but with a different decision boundary labeled 'DeSA Decision Boundary'. This diagram also illustrates two loss functions, 'LKD' and 'LREG', with arrows indicating their effects on the decision boundary and data points, respectively. The 'LREG' loss is shown to pull data points within a circular region towards the center.

Figure 1: The decision boundary before (a) and after (b) applying our proposed  $\mathcal{L}_{\text{REG}}$  (Eq. [4\)](#page-3-0) and  $\mathcal{L}_{\text{KD}}$  (Eq. [6\)](#page-4-0) using our synthetic anchor data.  $\mathcal{L}_{\text{REG}}$  aims to group the raw feature towards synthetic anchor feature, and  $\mathcal{L}_{KD}$  twists the local decision boundary towards the generalized decision boundary.

adaptation for out-of-federation clients [\(Jiang et al.,](#page-9-7) [2023\)](#page-9-7), while this paper focuses on the solution during FL training time.

We can see that both heterogeneous FL and decentralized FL leave the gray space of the following practical research question: *How can every client model perform well on other client domains, in a completely decentralized and heterogeneous FL setup?* Such a problem is referred as *decentralized federated mutual learning*, which is further detailed in Section [2.2.](#page-1-0) To the best of our knowledge, we are the first to address both data and model heterogeneity issues under serverless decentralized FL setting (see the comparison with related work in Table [1\)](#page-1-1).

In this work, we tackle the research question by performing local heterogeneity harmonized training and knowledge distillation. In particular, we synthesize a lightweight synthetic data generation process via distribution matching [\(Zhao &](#page-11-2) [Bilen,](#page-11-2) [2023\)](#page-11-2), and the synthetic data are exchangeable across clients to augment local datasets. We theoretically and empirically show that the synthetic data can serve as anchor points to improves FL for two purposes: 1) reducing the domain-gap between the distributions in the latent feature space; and 2) enabling global knowledge distillation for mutual learning. The effect of the two losses are visualized in Figure [1.](#page-1-2) In summary, we tackle a realistic and challenging setting in decentralized FL, where both data and model heterogeneities exist, without acquiring publicly available global real data. Our contributions are listed as follows:

- To circumvent the heterogeneity on data and model, we propose an innovative algorithm named Decentralized Federated Learning with Synthetic Anchors (DESA) that utilizes only a small number of synthetic data.
- We theoretically and empirically show that the strategic design of synthetic anchor data and our novel FL loss function effectively boost local model generalization in diverse data scenarios.

<span id="page-1-1"></span>Table 1: Comparison of the settings with other related heterogeneous FL and decentralized FL methods.

| Methods                      | Data Hetero-<br>geneity | Model Hetero-<br>geneity | Serverless | No Public<br>Data |
|------------------------------|-------------------------|--------------------------|------------|-------------------|
| VHL (Tang et al., 2022)a     | ✓                       | ✗                        | ✗          | ✓                 |
| FedGen (Zhu et al., 2021)    | ✓                       | ✗                        | ✗          | ✓                 |
| FedHe (Chan & Ngai, 2021)    | ✗                       | ✓                        | ✓          | ✓                 |
| FedDF (Lin et al., 2020)     | ✓                       | ✓                        | ✗          | ✗                 |
| FCCL (Huang et al., 2022)    | ✓                       | ✓                        | ✗          | ✗                 |
| FedProto (Tan et al., 2022)  | ✓                       | ✓                        | ✗          | ✓                 |
| FedFTG (Zhang et al., 2022b) | ✓                       | ✓                        | ✗          | ✓                 |
| DENSE (Zhang et al., 2022a)  | ✓                       | ✓                        | ✗          | ✓                 |
| DESA (ours)                  | ✓                       | ✓                        | ✓          | ✓                 |

<sup>a</sup> VHL has a single global model, trained using mutual information from all clients. Therefore we reference it under Mutual Learning.

• We conduct extensive experiments prove DESA's effectiveness, surpassing existing decentralized FL algorithms. It excels in inter- and intra-client performance across diverse tasks.

<span id="page-1-4"></span>

### 2. Preliminaries

#### 2.1. Conventional Federated Learning

Conventional FL aims to learn a single generalized global model that performs optimally on all the clients' data domains. Mathematically, the learning problem can be formulated as

<span id="page-1-3"></span>
$$
M^* = \arg\min_{M \in \mathcal{M}} \sum_{i=1}^N \mathbb{E}_{\mathbf{x}, y \sim P_i} [\mathcal{L}(M(\mathbf{x}), y)] \qquad (1)
$$

where  $M^*$  is the optimized global model from the shared model space  $\mathcal{M}, P_i$  is the local data distribution at the *i*th client,  $\mathcal L$  is the loss function and  $(\mathbf x, y)$  is the feature-label pair. Inspired by the pioneering work of FedAvg [\(McMa](#page-10-2)[han et al.,](#page-10-2) [2017\)](#page-10-2), a plethora of methods have tried to fill in the performance gap of FedAvg on data-heterogeneous scenario, which can be categorized in two main orthogonal directions: *Direction 1* aims to minimize the difference between the local and global model parameters to improve convergence [\(Li et al.,](#page-9-2) [2020a;](#page-9-2) [Karimireddy et al.,](#page-9-4) [2020;](#page-9-4) [Wang et al.,](#page-10-3) [2020\)](#page-10-3). *Direction 2* enforces consistency in local embedded features using anchors and regularization loss [\(Tang et al.,](#page-10-0) [2022;](#page-10-0) [Zhou et al.,](#page-11-5) [2022;](#page-11-5) [Ye et al.,](#page-11-6) [2022\)](#page-11-6). This work follows the second research direction and aim to leverage anchor points to handle data heterogeneity. We also tackle the more challenging problem of domain shift, unlike other methods that only assume a label-shift amongst the client-data distributions.

<span id="page-1-0"></span>

#### 2.2. Decentralized FL and Mutual Learning

Standard decentralized FL aims to solve the similar generalization objective as conventional FL (*i.e.*, Eq. [1\)](#page-1-3), only, without a central server to do so [\(Gao et al.,](#page-9-8) [2022\)](#page-9-8), and the objective applies to each local models  $M_i$ . Here, we focus on learning from each other under heterogeneous models

and data distributions. This brings in an essential line of works, known as *mutual learning*, where clients learn from each other to obtain the essential generalizability for their models. For example, during quarantine for pandemic, hospitals want to collaboratively train a model for classifying the virus. It is desired the models are generalizable to virus variants from different area, so that after quarantine the local models are still effective for incoming tourists.

Although mutual learning with heterogeneous data and models has been studied recently, most of them assume the existence of public real data [\(Lin et al.,](#page-9-5) [2020;](#page-9-5) [Huang et al.,](#page-9-0) [2022;](#page-9-0) [Gong et al.,](#page-9-9) [2022\)](#page-9-9) or a central server to coordinate the generation of synthetic data from the local client data [\(Zhang](#page-11-4) [et al.,](#page-11-4) [2022a;](#page-11-4) [Zhu et al.,](#page-11-1) [2021;](#page-11-1) [Zhang et al.,](#page-11-3) [2022b\)](#page-11-3). Another line of works rely on a server to aggregate locally generated logits or prototypes, and use it as local training guidance [\(Jeong et al.,](#page-9-10) [2018;](#page-9-10) [Chan & Ngai,](#page-8-1) [2021;](#page-8-1) [Tan](#page-10-1) [et al.,](#page-10-1) [2022\)](#page-10-1). In addition, more recent works have suggested that each clients train two models, a larger model for local training and a smaller model for mutual information exchange [\(Wu et al.,](#page-10-4) [2022;](#page-10-4) [Shen et al.,](#page-10-5) [2023\)](#page-10-5). However, none of the above methods simultaneously address both non-iid data and heterogeneous models under serverless and data-free setting. In this work, we explore mutual learning to optimize both local (intra-client) and global (inter-client) dataset accuracy (see the detailed setup in Sec. [3.1\)](#page-2-0). We list the comparison with other methods in Table [1](#page-1-1) and more detailed related works in Appendix [G.](#page-20-0)

<span id="page-2-0"></span>

## 3. Method

### 3.1. Notation and Problem Setup

Suppose there are  $N$  clients with *i*th client denoted as  $C_i$ . Let's represent the **private datasets** on  $C_i$  as  $D_i = \{x, y\}$ , where x is the feature and  $y \in \{1, \dots, K\}$  is the label from  $K$  classes. Let  $\mathcal L$  represent a real-valued loss function for classification (*e.g.*,cross-entropy loss). Denote the communication neighboring nodes of the client  $C_i$  in the system as  $\mathcal{N}(C_i)$  and the local models as  $\{M_i = \rho_i \circ \psi_i\}_{i=1}^{i=N}$ , where  $\psi_i$  represents the feature encoder and  $\rho_i$  represents the classification head for the *i*th client's model  $M_i$ . DESA returns trained client models  $\{M_i\}_{i=1}^{i=N}$ .

Our work aims to connect two key areas, heterogeneous FL and decentralized FL, termed as *decentralized federated mutual learning*, where we train multiple client models in a decentralized way such that they can generalize well across all clients' data domains. Mathematically, our objective is formulated as, for every client  $i$ ,

<span id="page-2-3"></span>
$$
M_i^* = \underset{M_i \in \mathcal{M}_i}{\arg \min} \underbrace{\mathbb{E}_{\mathbf{x}, y \sim P_i}[\mathcal{L}(M_i(\mathbf{x}), y)]}_{\text{Intra-client}}
$$
  
+ 
$$
\sum_{j \in \mathcal{N}(C_i)} \underbrace{\mathbb{E}_{\mathbf{x}, y \sim P_j}[\mathcal{L}(M_i(\mathbf{x}), y)]}_{\text{Inter-client}},
$$
(2)

where  $M_i^*$  is the best possible model for client i with respect to the model space  $\mathcal{M}_i$ .

Overview of DESA. The overall objective of DESA is to improve local models' generalizability in FL training under both model and data heterogeneity in a serverless setting as shown in in Figure [2\(](#page-3-1)a). The pipeline of DESA is depicted in Figure [2\(](#page-3-1)b). Our algorithm contains three important aspects: 1) we generate synthetic anchor data by matching raw data distribution and share them amongst the client's neighbors; 2) we train each client model locally with a synthetic anchor-based feature regularizer; and 3) we allow the models to learn from each other via knowledge distillation based on the synthetic anchors. The effectiveness of steps 2 and 3 can be observed in Figure [1.](#page-1-2) The next three subsections delve deeper into these three designs. The full algorithm is depicted in Algorithm [1.](#page-4-1)

<span id="page-2-2"></span>

### 3.2. Synthetic Anchor Datasets Generation

The recent success of dataset distillation-based data synthesis technique that generates data with similar representation power as the original raw data [\(Zhao et al.,](#page-11-7) [2020;](#page-11-7) [Zhao &](#page-11-2) [Bilen,](#page-11-2) [2023\)](#page-11-2). Thus, we propose to leverage this method to efficiently and effectively generate a synthetic anchor dataset without requiring any additional model pretraining. Inspired by our theoretical analysis in Sec. [4,](#page-4-2) we utilize distribution matching [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2) to distill local synthetic anchor data using the empirical maximum mean discrepancy loss (MMD) [\(Gretton et al.,](#page-9-11) [2012\)](#page-9-11) as follows,

<span id="page-2-1"></span>
$$
D_i^{\text{Syn}} = \underset{D}{\text{arg min}} \left\| \frac{1}{|D_i|} \sum_{(\mathbf{x}, y) \in D_i} \psi^{\text{rand}}(\mathbf{x}|y) - \frac{1}{|D|} \sum_{(\mathbf{x}, y) \in D} \psi^{\text{rand}}(\mathbf{x}|y) \right\|^2, \quad (3)
$$

where  $\psi^{\text{rand}}$  is a randomly sampled feature extractor for each iteration,  $D_i$  is the raw data for client i, and  $D_i^{Syn}$  is its target synthetic data. Following Eq. [3,](#page-2-1) we can manipulate the synthetic anchor dataset generation in a class-balanced manner, which enables the label prior to being unbiased towards a set of classes.

Similar to other FL work sharing distilled synthetic data for efficiency [\(Song et al.,](#page-10-6) [2023\)](#page-10-6), After local data synthesis, we request each client to share it among peers to ensure they possess same global information, and the global synthetic

<span id="page-3-1"></span>Image /page/3/Figure/1 description: The image displays a two-part figure illustrating the DeSA framework. Part (a), titled "Heterogeneous Setup," shows a central "DeSA" box connected to a laptop and a server, each with associated neural network diagrams and data distribution histograms. A smartphone is also depicted with a neural network and histogram. Part (b), titled "DeSA pipeline," details the process in two sections: "Data Synthesis with Distillation" and "Collecting Synthetic Data." The "Data Synthesis" section shows input data (xi, yi) and synthetic data (xSyn, ySyn) feeding into a model psi\_rand, with an output LMMD. The "Collecting Synthetic Data" section illustrates the aggregation of synthetic data from multiple clients (pSyn = union of piSyn for i in N). The lower part of (b) shows "DeSA Training (client i)" with synthetic data (xSyn, ySyn) and client data (xi, yi) feeding into a model Mi, producing logits. This is followed by "DeSA Training (i's neighbor client j)" where synthetic data (pSyn) is used with model Mj to produce logits. The pipeline also shows "Averaged logits from neighbors" used in conjunction with client i's logits to calculate LKL and LCE losses, with a model psi\_i and pi\_i involved in the process. A legend indicates "Forward" (solid black arrow), "Update" (solid red arrow), "Shared" (dashed line), and "Transmit" (dashed arrow).

Figure 2: Heterogeneous setup and DESA pipeline. (a) We assume a realistic FL scenario, where clients have different data distributions and computational powers, which results in different model architectures. (b) DESA pipeline consists of three phases, local data synthesis (top left) , global synthetic data aggregation (top right)(Section [3.2\)](#page-2-2), and decentralized training (bottom) using anchor regularization(Section [3.3\)](#page-3-2) and knowledge distillation (Section [3.4\)](#page-3-3).

anchor data is denoted as  $D^{Syn} = \bigcup_i D_i^{Syn-1}$  $D^{Syn} = \bigcup_i D_i^{Syn-1}$  $D^{Syn} = \bigcup_i D_i^{Syn-1}$ . As shown in Algorithm [1,](#page-4-1) our method is designed to work only to receive neighbor node information, and DeSA is designed for peerto-peer decentralized network. Since the loss requires all nodes' information, we can leverage the FastMix algorithm to aggregate all nodes' information as in [\(Ye et al.,](#page-11-8) [2020;](#page-11-8) [Luo & Ye,](#page-10-7) [2022\)](#page-10-7). This method can aggregate all nodes' information via adjacent nodes' communication at a linear speed. It is very common in fully decentralized optimization. In fact, our method can also work if each node can only receive neighbor nodes' information during training, and we empirically show the feasibility in our CIFAR10C experiments by sampling neighboring clients.

It is worth noting that, different from [\(Song et al.,](#page-10-6) [2023\)](#page-10-6), we further propose novel loss terms and training strategies to help mitigate the distribution discrepancy between the clients, which are detailed in the following sections (see Sec. [3.3](#page-3-2) and Sec. [3.4\)](#page-3-3), enabling improved model performance, as intuitively incorporating  $D^{Syn}$  into training can only achieve sub-optimal results (see Figure [3](#page-7-0) when both  $\lambda_{KD}$  and  $\lambda_{REG}$  equals to 0.).

<span id="page-3-2"></span>

### 3.3. REG Loss for Feature Regularization

The synthetic anchor regularization loss enforces the model to learn a client-domain invariant representation of the data. [\(Tang et al.,](#page-10-0) [2022\)](#page-10-0) and other domain incremental works [\(Rostami,](#page-10-8) [2021\)](#page-10-8) show that, adding a distribution discrepancy based loss in the latent space enables learning of a domain-invariant encoder  $\psi$ . However, most of the domain adaptation works require explicit access to the real data from other domains, or generates random noise as anchors. We propose using the latent space distribution of the synthetic anchor data  $D^{Syn}$  as a synthetic anchor to which the client-model specific encoders  $\psi_i$  can project their local private data onto. The loss function can be therefore defined as,

<span id="page-3-0"></span>
$$
\mathcal{L}_{REG}(\psi_i) = \mathbb{E}[d(\psi_i(D^{Syn})||\psi_i(D_i))],\tag{4}
$$

where  $K$  is the number of classes,  $d$  is distance computed using the supervised contrastive loss,

$$
d(\psi_i; D^{Syn}, D_i) = \sum_{j \in B} -\frac{1}{|B_{\backslash j}^{y_j}|}
$$
$$
\sum_{\mathbf{x}_p \in B_{\backslash j}^{y_j}} \log \frac{\exp(\psi_i(\mathbf{x}_j) \cdot \psi_i(\mathbf{x}_p)/\tau_{temp})}{\sum_{\mathbf{x}_a \in B_{\backslash j}} \exp(\psi_i(\mathbf{x}_j) \cdot \psi_i(\mathbf{x}_a)/\tau_{temp})} (5)
$$

where  $B_{\setminus j}$  represents a batch containing both local raw data  $D_i$  and global synthetic data  $D^{Syn}$  but without data j,  $B_{\backslash i}^{y_j}$  $\setminus j$ is a subset of  $B_{\setminus i}$  only with samples belonging to class  $y_j$ , and  $\tau_{temp}$  is a scalar temperature parameter. Note that we will detach the synthetic anchor data to ensure we are pulling local features to global features.

<span id="page-3-3"></span>

### 3.4. Knowledge Distillation for Information Exchange

This step allows a single client model to learn from all the other models using a common synthetic anchor dataset  $D^{Syn}$ . Under our setting of model heterogeneity among clients, we cannot aggregate the model parameters by simply averaging as in FedAvg [\(McMahan et al.,](#page-10-2) [2017\)](#page-10-2). Instead, we

<span id="page-3-4"></span><sup>1</sup>By default, we perform simple interpolation (averaging) among clients as it is shown that using this mixup strategy can improve model fairness [\(Chuang & Mroueh,](#page-8-2) [2021\)](#page-8-2).

propose to utilize knowledge distillation (KD) [\(Hinton et al.,](#page-9-12) [2015\)](#page-9-12) for decentralized model aggregation. Specifically, the fact that  $D^{Syn}$  is representative of the joint distributions of the clients allows it to be an ideal dataset for knowledge transfer. Thus, to enable the client model to mimic the predictions of the other models, we also incorporate KD loss using  $D^{Syn}$ , formulated as

$$
\mathcal{L}_{KD}(M_i) = \mathcal{L}_{KL}(M_i(\mathbf{x}^{Syn}), \bar{Z}_i),
$$
$$
\bar{Z}_i = \frac{1}{|N(C_i)|} \sum_{j \in N(C_i)} M_j(\mathbf{x}^{Syn}),
$$
(6)

where  $(\mathbf{x}^{Syn}, y^{Syn}) \sim D^{Syn}$ ,  $N(i)$  is the neighbor clients of client  $C_i$ , and  $\mathcal{L}_{KL}$  is the KL-Divergence between the output logits of  $x^{Syn}$  on  $M_i$  and the averaged output logits of  $x^{Syn}$  on  $M_j, \forall j \in N(C_i)$ . Finally, we formulate our objective function as

$$
\mathcal{L} = \mathcal{L}_{CE}(D_i \cup D^{Syn}; M_i) + \lambda_{REG} \mathcal{L}_{REG}(D_i, D^{Syn}; M_i) + \lambda_{KD} \mathcal{L}_{KD}(D^{Syn}; M_i, \bar{Z}_i),
$$
(7)

where  $\mathcal{L}_{CE}(D; M)$  is the K-classes cross entropy loss on data D and model M.  $\lambda_{REG}$  and  $\lambda_{KD}$  are the hyperparameters for regularization and KD losses,  $\mathcal{L}_{REG}$  and  $\mathcal{L}_{KD}$  are as defined in Eq. [4](#page-3-0) and Eq. [6,](#page-4-0) and  $\overline{Z}_i$  is the shared logits from neighboring clients  $N(C_i)$ . We also incorporate our class-conditional-generated global synthetic data  $D^{Syn}$  in the CE loss to enforce models to perform well on general domains and to benefit from the augmented dataset. Overall, we formulate our objective function as

<span id="page-4-1"></span>Algorithm 1 Serverless DESA (Procedures for Client i) 1: **procedure**  $INT(C_i)$ 2: for all  $j \in \mathcal{N}(C_i)$  do 3:  $D^{Syn} = D^{Syn} \cup \text{GET-IMG}(C_j)$ 4: **procedure** LOCALTRAIN( $C_i, t$ ) 5: **if** client  $C_i$  is sampled **then** 6: share  $Z_i = M_i(D^{Syn})$  to  $\mathcal{N}(C_i)$ 7: get  $\bar{Z}_i = 1/|\mathcal{N}(C_i)| \sum_j^{j \in \mathcal{N}(C_i)} Z_j$ 8:  $\mathcal{L}_{CE} = \text{CLASSIFICATION}(D_i \cup D^{Syn}; M_i)$ 9:  $\mathcal{L}_{REG}$  = FEATURE-REG( $D_i, D^{Syn}; M_i$ ) 10:  $\mathcal{L}_{KD} = \text{KD}(D^{Syn}; M_i, \overline{Z})$ 11:  $\mathcal{L} = \mathcal{L}_{CE} + \lambda_{REG} \mathcal{L}_{REG} + \lambda_{KD} \mathcal{L}_{KD}$ 12:  $M_i = M_i - \eta \nabla_{M_i} \mathcal{L}$ 

<span id="page-4-2"></span>

## 4. Theoretical Analysis

In this section, we focus on providing a theoretical justification for our algorithm. The technical challenge is to analyze the effect of minimizing the overall loss function (Eq. [7\)](#page-4-3) on the generalizability on the global data distribution  $P<sup>T</sup>$  of a client model.

Notation: Here, we state the intuitive definitions borrowed from [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3). For the precise definitions please refer to the notation table in Appendix [A.](#page-12-0) The domain pair  $(P, f<sup>P</sup>)$  represents the source distribution and its optimal labeling function. The  $\epsilon$  - error of a hypothesis M on the domain pair  $(P, f^P)$  is defined as the probability of a mismatch between the optimal labeling function  $f<sup>P</sup>$  and the hypothesis M. Additionally, the  $H\Delta H$  divergence ( $d_{H\Delta H}$ ) describes a distance measure between two distributions. Analysis: Our analysis focuses on the generalization on a global data distribution  $P<sup>T</sup>$  that is the average of the client

<span id="page-4-0"></span>distributions [\(Marfoq et al.,](#page-10-9) [2021\)](#page-10-9), with labeling function  $f<sup>T</sup>$  the same as  $f<sup>S</sup>$ . We assume that the minimization of loss in Eq [\(2\)](#page-2-3) matches the optimal labeling function  $f<sup>T</sup>$  of the global distribution  $P<sup>T</sup>$ , and formalizes the intuition of generalizing over closely related client distributions.

We proceed by defining the distribution of our global synthetic data as  $P^{Syn}$  with the corresponding labeling function  $f^{Syn}$ . As  $P^{Syn}$  is also leveraged for the knowledge distillation, inspired by [\(Feng et al.,](#page-8-4) [2021\)](#page-8-4) we describe  $(P_{KD}^{Syn}, f_{KD}^{Syn})$  as follows.

<span id="page-4-7"></span><span id="page-4-3"></span>Definition 4.1. The extended knowledge distillation (KD) domain pair  $(P_{KD}^{Syn}, f_{KD}^{Syn})$  of a client  $C_i$ , originating from the KD dataset  $D_{KD}^{Syn}$  is defined as

$$
D_{KD}^{Syn} = {\mathbf{x}^{Syn}, \frac{1}{|N(C_i)|} \sum_{j \in N(C_i)} M_j(\mathbf{x}^{Syn})\} \sim (P_{KD}^{Syn}, f_{KD}^{Syn})
$$

where  $M_i(\mathbf{x}^{Syn})$  is the predicted logit on global synthetic data  $\mathbf{x}^{Syn} \sim P^{Syn}(x)$ .

Definition 4.2. We define the the overall source distribution of the client  $C_i$  as  $P_i^S$ , which is a convex combination of the local and synthetic distributions

<span id="page-4-6"></span>
$$
P_i^S = \alpha P_i + \alpha^{Syn} P^{Syn} + \alpha_{KD}^{Syn} P_{KD}^{Syn}
$$
 (8)

The positive component weights  $\{\alpha, \alpha^{Syn}, \alpha_{KD}^{Syn}\}$  describe the dependence of the client  $C_i$ , on the local, synthetic and knowledge distillation data, and  $\alpha + \alpha^{Syn} + \alpha_{KD}^{Syn} = 1$ .

<span id="page-4-4"></span>**Theorem 1.** *Denote the client*  $C_i$ *'s model as*  $M_i = \rho_i \circ \psi_i \in$  $\mathcal{P}_i \circ \Psi_i = \mathcal{M}_i$  and its overall source distribution as  $P^S_i$  with *component weights (*α*). Then the generalization error on the global data distribution* P T *can be bounded as follows*

<span id="page-4-5"></span>
$$
\epsilon_{PT}(M_i) \leq \epsilon_{P_i^S}(M_i) + \alpha \mathbf{C}(P_i, P_T) + \alpha^{Syn} \epsilon_{PT}(f^{Syn}) + \alpha_{KD}^{Syn} \epsilon_{PT}(f_{KD}^{Syn}) + \frac{(1-\alpha)}{2} d_{\mathcal{P}_i \Delta \mathcal{P}_i}(\psi \circ P^{Syn}, \psi \circ P^T)
$$
(9)

where  $\mathbf{C}(P_i, P_T)$  are small distance terms depending on *the distributions*  $P_i$  *and*  $P_T$ *.* 

Below we give a short summary of the interpretation of our theorem in [4.3.](#page-5-0) For a more detailed interpretation, please refer to Appendix [A.](#page-12-0)

<span id="page-5-0"></span>*Remark* 4.3*.* The first term is minimized via local crossentropy loss. The second term is a constant given data distributions. The third and fourth terms measure the discrepancy between the labeling function of the target domain  $f_T$  and the labeling function  $f^{Syn}$  or  $f^{Syn}_{KD}$  of the corresponding synthetic distribution  $P^{Syn}$  or  $P_{KD}^{Syn}$ . With proper dataset distillation [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2), we have the model trained on  $D^{Syn}$  similar to that trained by  $D^T$ , *i.e.*,  $\mathbb{E}_{\mathbf{x} \sim P^T}[l(M^T(\mathbf{x}), y)] \simeq \mathbb{E}_{\mathbf{x} \sim P^{Syn}}[l(M^{Syn}(\mathbf{x}), y)]$ , implying  $f^{Syn} \rightarrow f^T$  and then we have a small  $\epsilon_{PT}(f^{Syn})$ . A small  $\epsilon_{PT} (f_{KD}^{Syn})$  can be achieved when every client achieves low  $\mathcal{L}_{CE}$ , indicating the model ability to learn  $f_{KD}^{Syn}$  approximating to  $f^T$ . The last term  $d_{\mathcal{P}_i \Delta \mathcal{P}_i}$  motivates the need for reducing our domain-invariant regularizer in Eq. [4,](#page-3-0) elicited to be bounded.

Furthermore, for the domain pair  $(D, f^D)$ , we denote  $\mathcal{J}(D) = |\epsilon_D(M) - \epsilon_{P^T}(M)| + \epsilon_{P^T}(f^D)$ . The following proposition implies our generalization bound in Theorem [1](#page-4-4) is tighter than the generalization bound of training with local data in Eq. [11](#page-12-1) [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3) under some mild conditions.

<span id="page-5-1"></span>Proposition 2. *Under the conditions in Theorem [1,](#page-4-4) if it further holds that*

$$
\sup_{M \in \mathcal{M}_i} \min \{ \mathcal{J}(P_{KD}^{Syn}), \mathcal{J}(P_{KD}^{Syn}) \} \n\leq \inf_{M \in \mathcal{M}_i} (\epsilon_{P_i}(M) - \epsilon_{PT}(M)) + \mathbf{C}(P_i, P_T) \quad (10)
$$

*then we can get a tighter generalization bound on the* i*th client's model* M<sup>i</sup> *than learning with local data only.*

The key idea of Proposition [2](#page-5-1) is to have the generalization bounds induced by  $P^{Syn}$  and  $P_{KD}^{Syn}$  is smaller than the generalization bound by the local training data distribution  $P_i$ . When the local data heterogeneity is severe,  $\inf_{M \in \mathcal{M}_i} (\epsilon_{P_i}(M) - \epsilon_{P^T}(M))$  and  $\mathbf{C}(P_i, P^T)$  would be large. As the synthetic data and the extended KD data are approaching the the global data distribution, the left side term in [\(10\)](#page-5-2) would be small. Thus, the above proposition points out that, to reach better generalization, the model learning should rely more on the synthetic data and the extended KD data, when the local data are highly heterogeneous and the synthetic and the extended KD datasets are similar to the global ones.

## 5. Experiment

### 5.1. Training Setup

Datasets and Models We extensively evaluate DESA under data heterogeneity in our experiments. Specifically, we

consider three classification tasks on three sets of domainshifted datasets:

1) DIGITS={MNIST [\(LeCun et al.,](#page-9-13) [1998\)](#page-9-13), SVHN [\(Netzer](#page-10-10) [et al.,](#page-10-10) [2011\)](#page-10-10), USPS [\(Hull,](#page-9-14) [1994\)](#page-9-14), SynthDigits [\(Ganin &](#page-9-15) [Lempitsky,](#page-9-15) [2015\)](#page-9-15), MNIST-M [\(Ganin & Lempitsky,](#page-9-15) [2015\)](#page-9-15)} contains digits from different styles, and each dataset represents one client.

2) OFFICE={Amazon [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11), Caltech [\(Grif](#page-9-16)[fin et al.,](#page-9-16) [2007\)](#page-9-16), DSLR [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11), and Web-Cam [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11)} contains images from different cameras and environments, and, similarly, each dataset represents one client.

3) CIFAR10C consists 57 subsets with domain- and labelshifted datasets sampled using Dirichlet distribution with  $\beta = 2$  from Cifar10-C [\(Hendrycks & Dietterich,](#page-9-17) [2019\)](#page-9-17).

More information about datasets and image synthesis can be found in Appendix [E.](#page-18-0) In our model heterogeneity experiments (Sec. [5.2\)](#page-6-0), we randomly assign model architectures from {ConvNet, AlexNet} for each client, while in model homogeneous experiments, we use ConvNet for all clients (see Appendix [F](#page-20-1) for model details).

Comparison Methods We compare DESA with two sets of baseline federated learning methods: one considers heterogeneous models (Sec. [5.2\)](#page-6-0) and the other considers homogeneous models (Sec. [5.3\)](#page-6-1). For *heterogeneous model experiments*, we compare with FedHe [\(Chan & Ngai,](#page-8-1) [2021\)](#page-8-1), FedDF [\(Lin et al.,](#page-9-5) [2020\)](#page-9-5), FCCL [\(Huang et al.,](#page-9-0) [2022\)](#page-9-0), and FedProto [\(Tan et al.,](#page-10-1) [2022\)](#page-10-1), and assume the clients owns personalized models<sup>[2](#page-5-3)</sup>. For *homogeneous model experiments*, we compare with FedAvg [\(McMahan et al.,](#page-10-2) [2017\)](#page-10-2), FedProx [\(Li et al.,](#page-9-1) [2020b\)](#page-9-1), MOON [\(Li et al.,](#page-9-3) [2021b\)](#page-9-3), Scaffold [\(Karimireddy et al.,](#page-9-4) [2020\)](#page-9-4), FedGen [\(Zhu et al.,](#page-11-1) [2021\)](#page-11-1), and VHL [\(Tang et al.,](#page-10-0) [2022\)](#page-10-0), and assume these baseline methods can leverage a server for global model aggregation.

<span id="page-5-2"></span>FL Training Setup If not otherwise specified, we use SGD optimizer with a learning rate of  $10^{-2}$ , and our default setting for local model update epochs is 1, total update rounds is 100, and the batch size for local training is 32. Since we only have a few clients for DIGITS and OFFICE experiments, we will select all the clients for each iteration, while we randomly sample 10% and 20% clients for each round when performing CIFAR10C experiments. By default,  $\lambda_{\text{REG}}$  and  $\lambda_{\text{KD}}$  are set to 1.

<span id="page-5-3"></span><sup>&</sup>lt;sup>2</sup>For the purposes of this comparison, we have excluded FedFTG [\(Zhang et al.,](#page-11-3) [2022b\)](#page-11-3) and DENSE [\(Zhang et al.,](#page-11-4) [2022a\)](#page-11-4), which address heterogeneities in different learning scenarios. FedFTG focuses on fine-tuning a global model, and DENSE belongs to one-shot FL, and both of them requires aggregate local information and train a generator on the server side. Note that none of the data-sharing-based baseline methods employ privacypreserving techniques.

<span id="page-6-2"></span>Table 2: Heterogeneous model experiments. We compare with model heterogeneous FL methods and report the averaged global accuracy over all client models. The best accuracy is marked in **bold**. Observe that DESA can achieve best averaged global accuracy on DIGITS and OFFICE. For CIFAR10C, DESA can outperform most of the baseline methods except for FCCL, which utilizes CIFAR100 as the public dataset. This is because CIFAR100 and CIFAR10C have a genuine semantic overlap.

|                                       |               | <b>DIGITS</b> |              |              |              |              | OFFICE       |              |              |              |              | CIFAR10C     |              |              |
|---------------------------------------|---------------|---------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|                                       |               | $MN(C)^a$     | SV(A)        | US(C)        | Syn(A)       | MM(C)        | Avg          | AM(A)        | CA(C)        | DS(A)        | WE(A)        | Avg          | 0.1          | 0.2          |
| FedHe                                 |               | 59.51         | 66.67        | 49.89        | 75.39        | 71.57        | 64.81        | 33.33        | 47.17        | 36.86        | 52.96        | 42.59        | 47.73        | 51.26        |
| FedDF                                 | Cifar100      | 65.98         | 65.21        | 61.30        | 69.65        | 74.48        | 67.32        | 38.87        | 49.51        | 33.12        | 46.89        | 42.09        | 27.69        | 35.70        |
|                                       | <b>FMNIST</b> | 43.05         | 69.14        | 44.95        | 74.67        | 71.27        | 60.61        | 39.13        | 46.53        | 40.23        | 43.77        | 42.36        | 28.26        | 36.50        |
| <b>FCCL</b>                           | Cifar100      | -             | -            | -            | -            | -            | -            | 38.22        | 49.10        | 44.68        | 52.26        | 46.07        | 51.70        | 50.78        |
|                                       | <b>FMNIST</b> | 46.43         | 61.02        | 42.64        | 63.05        | 66.39        | 55.91        | 27.39        | 46.78        | 38.56        | 48.47        | 40.30        | <b>52.40</b> | 51.83        |
| FedProto                              |               | 62.59         | 71.74        | 58.52        | <b>81.19</b> | 74.44        | 69.70        | 38.08        | 25.06        | 26.49        | 47.22        | 34.21        | 16.82        | 31.39        |
| $DESA(D_{	ext{VHL}}^{Syn})^{	ext{b}}$ |               | 54.40         | 62.03        | 42.34        | 67.75        | 73.03        | 59.91        | 8.82         | 48.98        | 16.90        | 49.13        | 30.96        | 47.49        | 52.04        |
| <b>DESA</b>                           |               | <b>70.12</b>  | <b>76.17</b> | <b>71.17</b> | <b>81.10</b> | <b>73.83</b> | <b>74.47</b> | <b>51.35</b> | <b>52.80</b> | <b>52.17</b> | <b>52.31</b> | <b>54.46</b> | <b>48.19</b> | <b>52.80</b> |

a The letter inside the parenthesis is the model architecture used by the client. A and C represent AlexNet and ConvNet, respectively.

<sup>b</sup> For VHL baseline, we use the synthetic data sampling strategy in VHL only. The purpose is to show DESA can generate better synthetic anchor data for feature regularization and knowledge distillation.

<span id="page-6-0"></span>

### 5.2. Heterogeneous Model Experiments

The objective of the experiments is to show that DESA can effectively leverage and learn generalized information from other clients under data and model heterogeneities. Thus, we report the averaged global accuracy by testing *i*-th local model on every client j's ( $\forall j, j \in [N]$ ) test sets. Note FedDF and FCCL require accessing to public available data. To make a fair comparison, we use FMNIST [\(Xiao](#page-10-12) [et al.,](#page-10-12) [2017\)](#page-10-12) and Cifar100 [\(Krizhevsky et al.,](#page-9-18) [2009\)](#page-9-18) as public datasets for knowledge distillation. DESA( $D_{\mathrm{VHL}}^{Syn}$ ) uses our training pipeline, but the synthetic data is sampled from an untrained StyleGAN [\(Karras et al.,](#page-9-19) [2019\)](#page-9-19) as in VHL [\(Tang](#page-10-0) [et al.,](#page-10-0) [2022\)](#page-10-0).

Based on our experiment results for heterogeneous models in Table. [2,](#page-6-2) it is evident that our method DESA significantly enhances the overall accuracy on DIGITS and OFFICE. In the CIFAR10C experiments, we increase the total training rounds to 200 since the performance of FL is notably hindered by a low client sampling ratio, especially in the case of serverless methods. As shown in the table, DESA can improve the global accuracy and out-performs most of the baseline methods, except for FCCL when the client sampling ratio is 0.1. We believe this is due to the extreme decentralized learning setting, *i.e.,* under model and data heterogeneity, serverless, and low client sampling ratio scenarios. FCCL utilizes pre-trained models, which provides a good starting point. However, we observe that the method tends to overfit easier by the fact that the performance drops when the client sampling ratio increases. Moreover, it is worth mentioning that the accuracies of FedDF and FCCL vary significantly when switching from Cifar100 to FM-NIST (the FedDF-DIGITS and FCCL-OFFICE results). We found that the training with both methods are easy to obtain nan loss, which we mark as '-' in the table. This suggests that these methods strongly rely on the public data, which restricts the utility of the methods. In contrast, DESA

does not depend on public data, and furthermore, it is completely serverless.

<span id="page-6-1"></span>

### 5.3. Homogeneous Model Experiments

<span id="page-6-3"></span>Table 3: Homogeneous model experiments. We compare with model homogeneous FL methods and report the averaged local accuracy. The best accuracy is marked in bold.

|          | DIGITS       | OFFICE       | CIFAR10C     |              |
|----------|--------------|--------------|--------------|--------------|
|          |              |              | 0.1          | 0.2          |
| FedAvg   | 94.20        | 76.45        | 65.26        | 66.40        |
| FedProx  | 94.19        | 76.45        | 65.33        | 66.36        |
| MOON     | 94.37        | 73.64        | 64.74        | 66.70        |
| Scaffold | 94.95        | 77.52        | <b>65.66</b> | 67.15        |
| VHL      | 94.11        | 75.69        | 64.67        | 66.55        |
| FedGen   | 82.62        | 63.60        | 45.77        | 48.10        |
| DESA     | <b>95.53</b> | <b>82.92</b> | 64.47        | <b>68.13</b> |

Among the baseline methods for homogeneous model experiments, they learn a generalizable global model via model aggregation on a server, which is not required in DESA. As these baseline methods are only evaluated on their single global model, for a fair comparison, we report the averaged local accuracy in the experiments. Specifically, the average local accuracy for the baseline methods is the average performance over testing the global model over all the clients; while we report, we report the average local accuracy on testing i-th local model on client i's test set over all the clients.

One can observe from Table [3](#page-6-3) that DESA can effectively leverage local information and outperforms other methods DIGITS and OFFICE. For CIFAR10C, although DESA has highest averaged local accuracy when client ratio is 0.2, it has lower performance when client ratio is 0.1. This is because smaller client sampling ratios have a larger impact on decentralized learning as we do not have a global model, and thus some clients may suffer from low model performance

due to insufficient training and the scarce global information from the sampled neighbor clients. Overall, despite the serverless setting, DESA is compatible with the baseline methods that have central servers.

### 5.4. Ablation studies for DESA

The effectiveness of DESA relies on the novel designs of synthetic anchor data and the losses. To evaluate how these designs influences the performance of DESA, we vary the number of synthetic anchor data (IPC) and the loss coefficients  $(\lambda's)$  in the following paragraphs. If not otherwise specified, we use the default hyperparameters and model heterogeneous setting. We report the averaged global accuracy in this section.

<span id="page-7-0"></span>Image /page/7/Figure/4 description: The image displays two line graphs side-by-side, both plotting "Global Accuracy" on the y-axis against a parameter on the x-axis. Graph (a), labeled "(a) λKD", shows "Global Accuracy" on the y-axis (ranging from 42 to 54) and "λKD" on the x-axis (ranging from 0 to 2). Five lines are plotted, representing different values of "λREG": 0 (blue), 0.1 (red), 0.5 (green), 1 (cyan), and 2 (magenta). Graph (b), labeled "(b) λREG", shows "Global Accuracy" on the y-axis (ranging from 42 to 54) and "λREG" on the x-axis (ranging from 0 to 2). Five lines are plotted, representing different values of "λKD": 0 (blue), 0.1 (red), 0.5 (green), 1 (cyan), and 2 (magenta).

Figure 3: Ablation studies for  $\lambda$ 's using OFFICE. We report the averaged global accuracy when changing the  $\lambda$  values.

<span id="page-7-1"></span>Table 4: Ablation study on the size of synthetic dataset using DIGITS. We use Images-Per-Class (IPC) to show its size.

| <b>IPC</b>                                                 |  | $\begin{array}{ c c c c c c c c c c c c c c c c c c c$ |  |
|------------------------------------------------------------|--|--------------------------------------------------------|--|
| Global Acc   70.12   72.74   72.46   74.32   70.29   70.45 |  |                                                        |  |

**Evaluation of**  $\lambda$  **Selections**  $\lambda_{KD}$  and  $\lambda_{REG}$  play an important role to help the clients to learn generalized information as well as improving the performance on local data. We select OFFICE as the candidate dataset for this set of experiments because it has larger domain-shift. We vary both of  $\lambda_{KD}$  and  $\lambda_{REG}$  between 0 to 2 and report the global accuracy in Figure [3\(](#page-7-0)a). One can observe that when  $\lambda_{KD}$ or  $\lambda_{REG}$  increases, the overall global accuracy increases. However, in Figure [3\(](#page-7-0)b), when we increase  $\lambda_{REG}$  to 2, the performance drops. This happens because the magnitude of REG loss term dominates the total training loss. Overall, we conclude that both  $\lambda_{KD}$  helps the local model learn information from other clients' models, and  $\lambda_{REG}$  improves the global performance by enforcing the local model to learn generalized features.

Evaluation of Size of Synthetic Dataset The size of synthetic data is a critical hyperparameter for DESA as it represents the shared local information. Since DESA synthesizes class-balanced data, we use Images-Per-Class (IPC) to represent the size of the synthetic data. We select DIGITS as the candidate dataset for this set of experiments because it contains larger number of data for each client, which allows

us to increase IPC up to 200. One can observe in Table [4](#page-7-1) that blindly increasing the IPC does not guarantee to obtain optimal global accuracy. It will cause the loss function to be dominated by the last 2 terms of Eq. [9,](#page-4-5) *i.e.,* by synthetic data. However, synthesizing larger number of synthetic data may degrade its quality, and the sampled batch for  $\mathcal{L}_{REG}$ may fail to capture the distribution.

### 5.5. Further Discussion

Communication Overhead Although DESA requires training and transferring local synthetic data to every clients, the process happens before the FL training, and can be preprocessed offline. During DESA training, clients only need to share logits *w.r.t.* global synthetic data, resulting in a lightweight *in-training* communication overhead. We discuss the communication of DESA compared to standard FL in Appendix [D,](#page-17-0) and show that DESA has lower overall communication overhead.

Privacy DESA requires sharing image-level information among clients in FL, which may raise privacy concerns. Therefore, we empirically show that our distilled synthetic data can protect against some privacy attacks in Appendix [B.1.](#page-15-0) We further discuss DESA's potential for higher privacy guarantee using Differential Privacy [\(Abadi et al.,](#page-8-5) [2016\)](#page-8-5) in Appendix [B.2.](#page-16-0) Furthermore, we claim that decentralized FL with both data and model heterogeneities is an extremely challenging setting, where existing solutions either require sharing real public data [\(Lin et al.,](#page-9-5) [2020;](#page-9-5) [Huang](#page-9-0) [et al.,](#page-9-0) [2022\)](#page-9-0) or synthetic data generated from GAN-based generator [\(Zhang et al.,](#page-11-4) [2022a;](#page-11-4)[b\)](#page-11-3).

Theory vs. Practice We note that obtaining the tight bound from our theoretical findings in Theorem [1](#page-4-4) requires proper dataset distillation. In Section [3.2,](#page-2-2) we propose an efficient approximate solution for dataset distillation. Although perfect dataset distillation may not be achievable (as shown in the visualization in Appendix [E\)](#page-18-0), we have found through experimentation that using our synthetic data in combination with the proposed  $\mathcal{L}_{REG}$  (Eq. [4\)](#page-3-0) and  $\mathcal{L}_{KD}$  (Eq. [6\)](#page-4-0) can already lead to improved generalization.

## 6. Conclusion

A novel and effective method, DESA, is presented that utilizes synthetic data to deal with both data and model heterogeneities in serverless decentralized FL. In particular, DESA introduces a pipeline that involves synthetic data generalization, and we propose a new scheme that incorporates the synthetic data as anchor points in decentralized FL model training. To address heterogeneity issues, we utilize the synthetic anchor data and propose two regularization losses: anchor loss and knowledge distillation loss. We provide theoretical analysis on the generalization bound to

justify the effectiveness of DESA using the synthetic anchor data. Empirically, the resulted client models not only achieve compelling local performance but also can generalize well onto other clients' data distributions, boosting cross-domain performance. Through extensive experiments on various classification tasks, we show that DESA robustly improves the efficacy of collaborative learning when compared with state-of-the-art methods, under both model and data heterogeneous settings.

## Acknowledgement

This work is supported in part by the Natural Sciences and Engineering Research Council of Canada (NSERC), Public Safety Canada, CIFAR Catalyst Grant, and Compute Canada Research Platform.

## Impact Statement

This paper represents an effort to progress the field of machine learning and distributed learning. Our work has various potential societal impacts, although we do not believe any specific one must be highlighted here.

## References

- <span id="page-8-5"></span>Abadi, M., Chu, A., Goodfellow, I., McMahan, H. B., Mironov, I., Talwar, K., and Zhang, L. Deep learning with differential privacy. In *Proceedings of the 2016 ACM SIGSAC conference on computer and communications security*, pp. 308–318, 2016.
- <span id="page-8-6"></span>Albuquerque, I., Monteiro, J., Darvishi, M., Falk, T. H., and Mitliagkas, I. Generalizing to unseen domains via distribution matching. *arXiv preprint arXiv:1911.00804*, 2019.
- <span id="page-8-12"></span>Assran, M., Loizou, N., Ballas, N., and Rabbat, M. Stochastic gradient push for distributed deep learning. In *International Conference on Machine Learning*, pp. 344–353. PMLR, 2019.
- <span id="page-8-0"></span>Beltrán, E. T. M., Pérez, M. Q., Sánchez, P. M. S., Bernal, S. L., Bovet, G., Pérez, M. G., Pérez, G. M., and Celdrán, A. H. Decentralized federated learning: Fundamentals, state of the art, frameworks, trends, and challenges. *IEEE Communications Surveys & Tutorials*, 2023.
- <span id="page-8-7"></span>Ben-David, S., Blitzer, J., Crammer, K., and Pereira, F. Analysis of representations for domain adaptation. *Advances in neural information processing systems*, 19, 2006.
- <span id="page-8-3"></span>Ben-David, S., Blitzer, J., Crammer, K., Kulesza, A., Pereira, F., and Vaughan, J. W. A theory of learning from different domains. *Machine learning*, 79:151–175, 2010.

- <span id="page-8-10"></span>Carlini, N., Chien, S., Nasr, M., Song, S., Terzis, A., and Tramer, F. Membership inference attacks from first principles. In *2022 IEEE Symposium on Security and Privacy (SP)*, pp. 1897–1914. IEEE, 2022a.
- <span id="page-8-15"></span>Carlini, N., Feldman, V., and Nasr, M. No free lunch in" privacy for free: How does dataset condensation help privacy". *arXiv preprint arXiv:2209.14987*, 2022b.
- <span id="page-8-16"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750– 4759, 2022.
- <span id="page-8-1"></span>Chan, Y. H. and Ngai, E. C. Fedhe: Heterogeneous models and communication-efficient federated learning. In *2021 17th International Conference on Mobility, Sensing and Networking (MSN)*, pp. 207–214. IEEE, 2021.
- <span id="page-8-11"></span>Chang, K., Balachandar, N., Lam, C., Yi, D., Brown, J., Beers, A., Rosen, B., Rubin, D. L., and Kalpathy-Cramer, J. Distributed deep learning networks among institutions for medical imaging. *Journal of the American Medical Informatics Association*, 25(8):945–954, 2018.
- <span id="page-8-2"></span>Chuang, C.-Y. and Mroueh, Y. Fair mixup: Fairness via interpolation. *arXiv preprint arXiv:2103.06503*, 2021.
- <span id="page-8-8"></span>Crammer, K., Kearns, M., and Wortman, J. Learning from multiple sources. *Journal of Machine Learning Research*, 9(8), 2008.
- <span id="page-8-17"></span>Cui, J., Wang, R., Si, S., and Hsieh, C.-J. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pp. 6565– 6590. PMLR, 2023.
- <span id="page-8-14"></span>Donahue, K. and Kleinberg, J. Model-sharing games: Analyzing federated learning under voluntary participation. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 35, pp. 5303–5311, 2021.
- <span id="page-8-9"></span>Dong, T., Zhao, B., and Lyu, L. Privacy for free: How does dataset condensation help privacy? *arXiv preprint arXiv:2206.00240*, 2022.
- <span id="page-8-13"></span>Fallah, A., Mokhtari, A., and Ozdaglar, A. Personalized federated learning with theoretical guarantees: A modelagnostic meta-learning approach. *Advances in Neural Information Processing Systems*, 33:3557–3568, 2020.
- <span id="page-8-4"></span>Feng, H., You, Z., Chen, M., Zhang, T., Zhu, M., Wu, F., Wu, C., and Chen, W. Kd3a: Unsupervised multisource decentralized domain adaptation via knowledge distillation. In *ICML*, pp. 3274–3283, 2021.

- <span id="page-9-15"></span>Ganin, Y. and Lempitsky, V. Unsupervised domain adaptation by backpropagation. In *International conference on machine learning*, pp. 1180–1189. PMLR, 2015.
- <span id="page-9-8"></span>Gao, D., Yao, X., and Yang, Q. A survey on heterogeneous federated learning. *arXiv preprint arXiv:2210.04505*, 2022.
- <span id="page-9-22"></span>Ghosh, A., Chung, J., Yin, D., and Ramchandran, K. An efficient framework for clustered federated learning. *IEEE Transactions on Information Theory*, 68(12):8076–8091, 2022.
- <span id="page-9-9"></span>Gong, X., Sharma, A., Karanam, S., Wu, Z., Chen, T., Doermann, D., and Innanje, A. Preserving privacy in federated learning with ensemble cross-domain knowledge distillation. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 36, pp. 11891–11899, 2022.
- <span id="page-9-26"></span>Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial nets. *Advances in neural information processing systems*, 27, 2014.
- <span id="page-9-11"></span>Gretton, A., Borgwardt, K. M., Rasch, M. J., Schölkopf, B., and Smola, A. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-9-16"></span>Griffin, G., Holub, A., and Perona, P. Caltech-256 object category dataset. 2007.
- <span id="page-9-17"></span>Hendrycks, D. and Dietterich, T. Benchmarking neural network robustness to common corruptions and perturbations. *arXiv preprint arXiv:1903.12261*, 2019.
- <span id="page-9-12"></span>Hinton, G., Vinyals, O., and Dean, J. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-9-0"></span>Huang, W., Ye, M., and Du, B. Learn from others and be yourself in heterogeneous federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10143–10153, 2022.
- <span id="page-9-21"></span>Huang, Y., Chu, L., Zhou, Z., Wang, L., Liu, J., Pei, J., and Zhang, Y. Personalized cross-silo federated learning on non-iid data. In *Proceedings of the AAAI conference on artificial intelligence*, volume 35, pp. 7865–7873, 2021a.
- <span id="page-9-20"></span>Huang, Y., Gupta, S., Song, Z., Li, K., and Arora, S. Evaluating gradient inversion attacks and defenses in federated learning. *Advances in Neural Information Processing Systems*, 34:7232–7241, 2021b.
- <span id="page-9-14"></span>Hull, J. J. A database for handwritten text recognition research. *IEEE Transactions on pattern analysis and machine intelligence*, 16(5):550–554, 1994.

- <span id="page-9-10"></span>Jeong, E., Oh, S., Kim, H., Park, J., Bennis, M., and Kim, S. Federated distillation and augmentation under non-iid private data. *NIPS Wksp. MLPCD*, 2018.
- <span id="page-9-7"></span>Jiang, M., Yang, H., Cheng, C., and Dou, Q. Iop-fl: Insideoutside personalization for federated medical image segmentation. *IEEE Transactions on Medical Imaging*, 2023.
- <span id="page-9-4"></span>Karimireddy, S. P., Kale, S., Mohri, M., Reddi, S., Stich, S., and Suresh, A. T. Scaffold: Stochastic controlled averaging for federated learning. In *International Conference on Machine Learning*, pp. 5132–5143. PMLR, 2020.
- <span id="page-9-19"></span>Karras, T., Laine, S., and Aila, T. A style-based generator architecture for generative adversarial networks. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 4401–4410, 2019.
- <span id="page-9-18"></span>Krizhevsky, A., Hinton, G., et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-9-13"></span>LeCun, Y., Bottou, L., Bengio, Y., and Haffner, P. Gradientbased learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-9-25"></span>Lee, S., Chun, S., Jung, S., Yun, S., and Yoon, S. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pp. 12352–12364. PMLR, 2022.
- <span id="page-9-23"></span>Li, C., Li, G., and Varshney, P. K. Decentralized federated learning via mutual knowledge transfer. *IEEE Internet of Things Journal*, 9(2):1136–1147, 2021a.
- <span id="page-9-6"></span>Li, D. and Wang, J. Fedmd: Heterogenous federated learning via model distillation. *arXiv preprint arXiv:1910.03581*, 2019.
- <span id="page-9-24"></span>Li, G., Togo, R., Ogawa, T., and Haseyama, M. Dataset distillation for medical dataset sharing. *arXiv preprint arXiv:2209.14603*, 2022.
- <span id="page-9-3"></span>Li, Q., He, B., and Song, D. Model-contrastive federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10713– 10722, 2021b.
- <span id="page-9-2"></span>Li, T., Sahu, A. K., Zaheer, M., Sanjabi, M., Talwalkar, A., and Smith, V. Federated optimization in heterogeneous networks. *Proceedings of Machine learning and systems*, 2:429–450, 2020a.
- <span id="page-9-1"></span>Li, X., Huang, K., Yang, W., Wang, S., and Zhang, Z. On the convergence of fedavg on non-iid data. *International Conference on Learning Representations*, 2020b.
- <span id="page-9-5"></span>Lin, T., Kong, L., Stich, S. U., and Jaggi, M. Ensemble distillation for robust model fusion in federated learning. *Advances in Neural Information Processing Systems*, 33: 2351–2363, 2020.

- <span id="page-10-7"></span>Luo, L. and Ye, H. Decentralized stochastic variance reduced extragradient method. *arXiv preprint arXiv:2202.00509*, 2022.
- <span id="page-10-9"></span>Marfoq, O., Neglia, G., Bellet, A., Kameni, L., and Vidal, R. Federated multi-task learning under a mixture of distributions. *Advances in Neural Information Processing Systems*, 34:15434–15447, 2021.
- <span id="page-10-20"></span>Matsuda, K., Sasaki, Y., Xiao, C., and Onizuka, M. Fedme: Federated learning via model exchange. In *Proceedings of the 2022 SIAM international conference on data mining (SDM)*, pp. 459–467. SIAM, 2022.
- <span id="page-10-2"></span>McMahan, B., Moore, E., Ramage, D., Hampson, S., and y Arcas, B. A. Communication-efficient learning of deep networks from decentralized data. In *Artificial intelligence and statistics*, pp. 1273–1282. PMLR, 2017.
- <span id="page-10-10"></span>Netzer, Y., Wang, T., Coates, A., Bissacco, A., Wu, B., and Ng, A. Y. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-10-19"></span>Pappas, C., Chatzopoulos, D., Lalis, S., and Vavalis, M. Ipls: A framework for decentralized federated learning. In *2021 IFIP Networking Conference (IFIP Networking)*, pp. 1–6. IEEE, 2021.
- <span id="page-10-8"></span>Rostami, M. Lifelong domain adaptation via consolidated internal distribution. *Advances in neural information processing systems*, 34:11172–11183, 2021.
- <span id="page-10-16"></span>Roy, A. G., Siddiqui, S., Pölsterl, S., Navab, N., and Wachinger, C. Braintorrent: A peer-to-peer environment for decentralized federated learning. *arXiv preprint arXiv:1905.06731*, 2019.
- <span id="page-10-11"></span>Saenko, K., Kulis, B., Fritz, M., and Darrell, T. Adapting visual category models to new domains. In *Computer Vision–ECCV 2010: 11th European Conference on Computer Vision, Heraklion, Crete, Greece, September 5-11, 2010, Proceedings, Part IV 11*, pp. 213–226. Springer, 2010.
- <span id="page-10-17"></span>Sheller, M. J., Reina, G. A., Edwards, B., Martin, J., and Bakas, S. Multi-institutional deep learning modeling without sharing patient data: A feasibility study on brain tumor segmentation. In *Brainlesion: Glioma, Multiple Sclerosis, Stroke and Traumatic Brain Injuries: 4th International Workshop, BrainLes 2018, Held in Conjunction with MICCAI 2018, Granada, Spain, September 16, 2018, Revised Selected Papers, Part I 4*, pp. 92–104. Springer, 2019.
- <span id="page-10-18"></span>Sheller, M. J., Edwards, B., Reina, G. A., Martin, J., Pati, S., Kotrotsou, A., Milchenko, M., Xu, W., Marcus, D., Colen, R. R., et al. Federated learning in medicine: facilitating multi-institutional collaborations without sharing patient data. *Scientific reports*, 10(1):12598, 2020.

- <span id="page-10-5"></span>Shen, T., Zhang, J., Jia, X., Zhang, F., Lv, Z., Kuang, K., Wu, C., and Wu, F. Federated mutual learning: a collaborative machine learning method for heterogeneous data, models, and objectives. *Frontiers of Information Technology & Electronic Engineering*, 24(10):1390–1402, 2023.
- <span id="page-10-13"></span>Shokri, R., Stronati, M., Song, C., and Shmatikov, V. Membership inference attacks against machine learning models. In *2017 IEEE symposium on security and privacy (SP)*, pp. 3–18. IEEE, 2017.
- <span id="page-10-6"></span>Song, R., Liu, D., Chen, D. Z., Festag, A., Trinitis, C., Schulz, M., and Knoll, A. Federated learning via decentralized dataset distillation in resource-constrained edge environments. In *2023 International Joint Conference on Neural Networks (IJCNN)*, pp. 1–10. IEEE, 2023.
- <span id="page-10-1"></span>Tan, Y., Long, G., Liu, L., Zhou, T., Lu, Q., Jiang, J., and Zhang, C. Fedproto: Federated prototype learning across heterogeneous clients. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 36, pp. 8432–8440, 2022.
- <span id="page-10-0"></span>Tang, Z., Zhang, Y., Shi, S., He, X., Han, B., and Chu, X. Virtual homogeneity learning: Defending against data heterogeneity in federated learning. *arXiv preprint arXiv:2206.02465*, 2022.
- <span id="page-10-15"></span>Wang, H.-P., Chen, D., Kerkouche, R., and Fritz, M. Fedgloss-dp: Federated, global learning using synthetic sets with record level differential privacy. *arXiv preprint arXiv:2302.01068*, 2023.
- <span id="page-10-3"></span>Wang, J., Liu, Q., Liang, H., Joshi, G., and Poor, H. V. Tackling the objective inconsistency problem in heterogeneous federated optimization. *Advances in neural information processing systems*, 33:7611–7623, 2020.
- <span id="page-10-21"></span>Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., and You, Y. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-10-4"></span>Wu, C., Wu, F., Lyu, L., Huang, Y., and Xie, X. Communication-efficient federated learning via knowledge distillation. *Nature communications*, 13(1):2032, 2022.
- <span id="page-10-12"></span>Xiao, H., Rasul, K., and Vollgraf, R. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-10-14"></span>Xiong, Y., Wang, R., Cheng, M., Yu, F., and Hsieh, C.-J. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 16323–16332, 2023.

- <span id="page-11-10"></span>Yang, R., Tian, J., and Zhang, Y. Regularized mutual learning for personalized federated learning. In *Asian Conference on Machine Learning*, pp. 1521–1536. PMLR, 2021.
- <span id="page-11-8"></span>Ye, H., Zhou, Z., Luo, L., and Zhang, T. Decentralized accelerated proximal gradient descent. *Advances in Neural Information Processing Systems*, 33:18308–18317, 2020.
- <span id="page-11-6"></span>Ye, R., Ni, Z., Xu, C., Wang, J., Chen, S., and Eldar, Y. C. Fedfm: Anchor-based feature matching for data heterogeneity in federated learning. *arXiv preprint arXiv:2210.07615*, 2022.
- <span id="page-11-9"></span>Yuan, L., Ma, Y., Su, L., and Wang, Z. Peer-to-peer federated continual learning for naturalistic driving action recognition. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 5249–5258, 2023a.
- <span id="page-11-0"></span>Yuan, L., Sun, L., Yu, P. S., and Wang, Z. Decentralized federated learning: A survey and perspective. *arXiv preprint arXiv:2306.01603*, 2023b.
- <span id="page-11-4"></span>Zhang, J., Chen, C., Li, B., Lyu, L., Wu, S., Ding, S., Shen, C., and Wu, C. Dense: Data-free one-shot federated learning. *Advances in Neural Information Processing Systems*, 35:21414–21428, 2022a.
- <span id="page-11-3"></span>Zhang, L., Shen, L., Ding, L., Tao, D., and Duan, L.-Y. Finetuning global model via data-free knowledge distillation for non-iid federated learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 10174–10183, 2022b.
- <span id="page-11-11"></span>Zhang, Y., Xiang, T., Hospedales, T. M., and Lu, H. Deep mutual learning. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4320– 4328, 2018.
- <span id="page-11-12"></span>Zhao, B. and Bilen, H. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-11-13"></span>Zhao, B. and Bilen, H. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-11-2"></span>Zhao, B. and Bilen, H. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-11-7"></span>Zhao, B., Mopuri, K. R., and Bilen, H. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2020.

- <span id="page-11-5"></span>Zhou, T., Zhang, J., and Tsang, D. Fedfa: Federated learning with feature anchors to align feature and classifier for heterogeneous data. *arXiv preprint arXiv:2211.09299*, 2022.
- <span id="page-11-1"></span>Zhu, Z., Hong, J., and Zhou, J. Data-free knowledge distillation for heterogeneous federated learning. In *International conference on machine learning*, pp. 12878–12889. PMLR, 2021.

Road Map of Appendix Our appendix is organized into five sections. The theoretical analysis and proof is in Appendix [A.](#page-12-0) Appendix [B.1](#page-15-0) shows the results for Membership Inference Attack (MIA) on DESA trained models using DIGITS datasets. Appendix [B.2](#page-16-0) discusses how we inject DP mechanism in our data synthesis process, and shows that using DP synthetic anchor data for DESA can still yeilds comparable performance. Appendix [E](#page-18-0) introduce the selected datasets and how we synthesize anchor data in detail. Appendix [F](#page-20-1) describes the model architectures (ConvNet and AlexNet) we use in our experiments. Finally, Appendix [G](#page-20-0) provides a detailed literature review about the related works. Our code and model checkpoints are available along with the supplementary materials.

<span id="page-12-0"></span>

## A. Theoretical Analysis and Proofs

### A.1. Notation

Table 5: Notations used

| $D_i = (x_i, y_i)_{i=1}^{i=m}$                                    | $\triangleq$ | Local Dataset of client $C_i$                                                                         |
|-------------------------------------------------------------------|--------------|-------------------------------------------------------------------------------------------------------|
| $P_i(x,y)$                                                        | $\triangleq$ | The local joint distribution of client $C_i$                                                          |
| $L_{CE}(a, b)$                                                    | $\triangleq$ | Cross entropy Loss between distributions $a$ and $b$                                                  |
| $L_{KL}(a, b)$                                                    | $\equiv$     | KL divergence loss between distributions $(a, b)$                                                     |
| $\mathcal{M}_i$                                                   | $\triangleq$ | Model Space of Client $C_i$                                                                           |
| $\mathcal{P}_i$                                                   | $\triangleq$ | Space of Classifier heads for Client $C_i$                                                            |
| $\Psi_i$                                                          | $\triangleq$ | Space of encoder heads for Client $C_i$                                                               |
| $M_i = \rho_i \circ \psi_i$                                       | $\triangleq$ | Model for client $C_i$ with encoder and decoder heads sampled from $P_i$                              |
|                                                                   |              | and $\Psi_i$                                                                                          |
| $\boldsymbol{\alpha} = [\alpha, \alpha^{Syn}, \alpha_{KD}^{Syn}]$ | $\triangleq$ | Component weights for the losses, as defined in Eq. 8                                                 |
|                                                                   |              |                                                                                                       |
| Notations from (Ben-David et al., 2010)                           |              |                                                                                                       |
|                                                                   |              |                                                                                                       |
| $(P, f^P)$                                                        | $\triangleq$ | $P(x)$ is the source data distribution and $f^P$ is the optimal labelling<br>function                 |
|                                                                   |              |                                                                                                       |
| $\epsilon_P(M)$                                                   | $\triangleq$ | $\Pr_{x \sim P(x)}(M(x) \neq f_P(x))$                                                                 |
| $d_{\mathcal{H}\Delta\mathcal{H}}(P_s, P_t)$                      | $\triangleq$ | $2 \sup_{h, h' \in \mathcal{H}}  P_{x\sim P_s}(h(x) \neq h(x')) - P_{x\sim P_t}(h(x) \neq h(x')) $    |
| $\lambda(P)$                                                      | $\triangleq$ | Least error of a jointly trained model = min $_{M \in \mathcal{M}} \epsilon_P(M) + \epsilon_{P_T}(M)$ |
| $C(P_i, P^T)$                                                     | A            | distance term appearing in (Ben-David et al., 2010) =                                                 |

#### A.2. Proof for Theorem [1](#page-4-4)

*Proof.* The training data at *i*th client are from as three distributions: 1) the local source data; 2) the global virtual data; 3) the extended KD data. The data from the first two groups are used for the cross entropy loss and the distribution divergence, while the third is used for Knowledge distillation.

Without loss of generality, at *i*th client, we set the weight for  $P_i$ ,  $P^{Syn}$  and  $P_{KD}^{Syn}$  as  $\alpha$ ,  $\alpha^{Syn}$  and  $\alpha_{KD}^{Syn}$ , respectively. For notation simplicity, we assume  $\alpha + \alpha^{Syn} + \alpha_{KD}^{Syn} = 1$ . Then the training source data at *i*th client is  $P_i^S = \alpha P_i + \alpha P_i$  $\alpha^{Syn}P^{Syn} + \alpha_{KD}^{Syn}P_{KD}^{Syn}.$ 

From Theorem 2 in [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3), it holds that

<span id="page-12-1"></span>
$$
\epsilon_{PT}(M_i) \le \epsilon_{P_i}(M_i) + \mathbf{C}(P_i, P_T) \tag{11}
$$

where  $\mathbf{C}(P_i, P_T) = \frac{1}{2} d_{\mathcal{M}_i \Delta \mathcal{M}_i}(P_i, P^T) + \lambda(P_i)$  and  $\lambda(P_i) = \min_{M \in \mathcal{M}_i} \epsilon_{P_i}(M) + \epsilon_{P}(\mathcal{M})$  is a constant. These terms are small unless the data heterogeneity is severe [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3). Then with [\(11\)](#page-12-1) and Lemma [1,](#page-14-0) we have three inequalities, which we will add after multiplying each one of them with their corresponding component weight  $\alpha$ .

Furthermore, note that the support of  $P^{Syn}$  and  $P_{KD}^{Syn}$  are the same. [\(4.1\)](#page-4-7). Therefore, their distances from the support of the global distribution will also be the same, i.e.

$$
d_{\mathcal{P}_i \Delta \mathcal{P}_i}(\psi \circ P^{Syn}, \psi \circ P^T) = d_{\mathcal{P}_i \Delta \mathcal{P}_i}(\psi \circ P_{KD}^{Syn}, \psi \circ P^T)
$$

Continuing after adding all three inequalities and using Claim [1](#page-15-1) to introduce  $\epsilon_{P_i^S}(M)$ 

$$
\epsilon_{PT}(M_i) \leq \epsilon_{P_i^S}(M_i) + \mathbf{C}(P_i, P^T) + \frac{\alpha^{Syn}}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T) + \epsilon_{PT}(f^{Syn})) + \frac{\alpha_{KD}^{Syn}}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T) + \epsilon_{P_{KD}^T}(f^{Syn}))
$$

$$
\leq \epsilon_{P_i^S}(M_i) + \alpha \mathbf{C}(P_i, P^T) + \alpha^{Syn} \epsilon_{PT}(f^{Syn}) + \alpha_{KD}^{Syn} \epsilon_{P_{KD}^T}(f^{Syn}) + \frac{(1-\alpha)}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T)
$$
 $(12)$ 

With the last condition coming from  $\alpha + \alpha^{Syn} + \alpha_{KD}^{Syn} = 1$ 

$$
\epsilon_{P^T}(M_i) \leq \epsilon_{P_i^S}(M_i) + \alpha \mathbf{C}(P_i, P^T) + \alpha^{Syn} \epsilon_{P^T}(f^{Syn}) + \alpha_{KD}^{Syn} \epsilon_{P^T}(f_{KD}^{Syn}) + \frac{(1-\alpha)}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T)
$$
(13)

## A.3. Interpretation for Theorem [1](#page-4-4)

From Eq. [\(9\)](#page-4-5), it can be seen that the generalization bound for  $M_i$  consists of five terms.

- The first term  $\epsilon_{P_i^S}(M_i)$  is the error bound with respect to the training source data distribution. With Claim [1](#page-15-1) in appendix, minimizing this term is equivalent to optimizing the loss  $\alpha \mathbb{E}_{(\mathbf{x},y)\sim P_i} \mathcal{L}_{CE} + \alpha^{Syn} \mathbb{E}_{(\mathbf{x},y)\sim P^{Syn}} \mathcal{L}_{CE} +$  $\alpha_{KD}^{Syn} \mathbb{E}_{(\mathbf{x},y)\sim P^{Syn}} \mathcal{L}_{KD}$ . Since this is the form of our loss function in Eq. [7,](#page-4-3) we expect this term to be minimized
- The second term is inherited from the original generalization bound in [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3) with the local training data. For our case, it can be controlled by the component weight  $\alpha$ . If we rely less on the local data (i.e. smaller  $\alpha$ ), then these terms will be vanishing. Moreover even if we rely more on local data, it is essentially a distance measure between the local client distribution  $P_i$  and the global data distribution  $P^T$ . Since the global data distribution is an average of the closely related local client distributions, we expect this term to be small [\(Tang et al.,](#page-10-0) [2022\)](#page-10-0), [\(Ben-David](#page-8-3) [et al.,](#page-8-3) [2010\)](#page-8-3), [\(Albuquerque et al.,](#page-8-6) [2019\)](#page-8-6).
- The third term measures the discrepancy between real labeling and the synthetic data labeling mechanisms. This discrepancy will be low because of our synthetic data generation process. Note that the data distillation's objective is to achieve  $\mathbb{E}_{\mathbf{x} \sim P^T}[l(M^T(\mathbf{x}), y)] \simeq \mathbb{E}_{\mathbf{x} \sim P^{Syn}}[l(M^{Syn}(\mathbf{x}), y)]$  (Eq. 1 in [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2)). If we change the M to a well-trained deep NN, then it's easy to see the synthetic data labelling  $f^{syn}$  will be similar to the real labelling  $f_T$ . Here we leverage the distribution matching that uses MMD loss to minimize the embedding differences between the synthetic data and real data in the same class [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2) as a proxy way to achieve that.
- The fourth term originates from the knowledge distillation loss in equation [6.](#page-4-0) Here, we use the consensus knowledge from neighbour models to improve the local model. The labelling function of the extended KD data  $f_{KD}^{syn}$ , changes as training continues and the neighbour models learn to generalize well. Towards the end of training, predictions from the consensus knowledge should match the predictions of the true labeling function, therefore,  $f_{KD}^{syn}$  will be close to  $f_T$ .
- The fifth term is a distribution divergence between the encoded distributions of  $P^{Syn}$  and  $P^T$ . This is minimized by the domain invariant regularizer in Eq. [4,](#page-3-0) which acts as an anchor to pull all the encoded distributions together.

Remark: In order to get a tight generalization guarantee, we only need one of the fourth or fifth terms to be small. Since, if either any one of them is small, we can adjust the component weights  $\alpha$  (practically  $\lambda_{REG}$  and  $\lambda_{KD}$ ) to get a better generalization guarantee.

#### A.4. Proof for Proposition [2](#page-5-1)

*Proof.* Without loss of generality, let's start with

$$
\sup_{M \in \mathcal{M}_i} |\epsilon_{P^{Syn}}(M) - \epsilon_{P^T}(M)| + \epsilon_{P^T}(f^{Syn}) \le \inf_{M \in \mathcal{M}_i} (\epsilon_{P_i}(M) - \epsilon_{P^T}(M)) + \frac{1}{2} d_{\mathcal{M}_i \Delta \mathcal{M}_i}(P_i, P^T) + \lambda(P_i). \quad (14)
$$

Then it holds that for any  $M \in \mathcal{M}_i$ ,

$$
\epsilon_{PSyn}(M) - \epsilon_{PT}(M) + \epsilon_{PT}(f^{Syn}) \leq \epsilon_{P_i}(M) - \epsilon_{PT}(M) + \mathbf{C}(P_i, P^T)
$$
  
\n
$$
\Rightarrow \epsilon_{PSyn}(M) + \epsilon_{PT}(f^{Syn}) \leq \epsilon_{P_i}(M) + \mathbf{C}(P_i, P^T)
$$
\n(15)

Note that the right side of [\(15\)](#page-14-1) is the original bound in Theorem 2 in [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3). Similarly, we can achieve

$$
\epsilon_{P_{KD}^{Syn}}(M) + \epsilon_{PT}(f_{KD}^{Syn}) \le \epsilon_{P_i}(M) + \frac{1}{2} d_{\mathcal{M}_i \Delta \mathcal{M}_i}(P_i, P^T) + \lambda(P_i)
$$
\n(16)

Combining [\(15](#page-14-1)[-16\)](#page-14-2) together with the component weights  $\alpha$  and setting  $\alpha \to 0$ ,

$$
\alpha_{KD}^{Syn} \epsilon_{P_{KD}^{Syn}}(M) + \alpha^{Syn} \epsilon_{P^{Syn}}(M) + \epsilon_{P^T}(f_{KD}^{Syn}) \le \epsilon_{P_i}(M) + \frac{1}{2} d_{\mathcal{M}_i \Delta \mathcal{M}_i}(P_i, P^T) + \lambda(P_i)
$$
(17)

Therefore, we conclude that our global generalization bound in Theorem [1](#page-4-4) (which is the LHS of Eq. [17\)](#page-14-3) is tighter than the original bound in [\(Ben-David et al.,](#page-8-3) [2010\)](#page-8-3) (RHS), when the condition of Proposition [2](#page-5-1) holds.

<span id="page-14-3"></span><span id="page-14-2"></span><span id="page-14-1"></span> $\Box$ 

<span id="page-14-0"></span>

#### A.5. Some useful lemmas and claims

**Lemma 1.** *Denote the model as*  $M = \rho \circ \psi \in \mathcal{M}$ *. The global generalization bound holds as* 

$$
\epsilon_{P^T}(M) \le \epsilon_P(M) + \frac{1}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P, \psi \circ P^T) + \epsilon_{P^T}(f),\tag{18}
$$

where  $(P,f)$  could be either  $(P^{Syn},f^{Syn})$  or  $(P_{KD}^{Syn},f_{KD}^{Syn})$  pair.

*Proof.* For any model  $M = \rho \circ \psi \in \mathcal{M}$ , we have the following bound for the global virtual data distribution:

$$
\epsilon_{PT}(M) - \epsilon_{PSyn}(M) \stackrel{(a)}{=} \epsilon_{PT}(M, f^T) - \epsilon_{PSyn}(M, f^{Syn})
$$
  
\n
$$
\stackrel{(b)}{\leq} |\epsilon_{PT}(M, f^{Syn}) + \epsilon_{PT}(f^{Syn}, f^T) - \epsilon_{PSyn}(M, f^{Syn})|
$$
  
\n
$$
\leq |\epsilon_{PT}(M, f^{Syn}) - \epsilon_{PSyn}(M, f^{Syn})| + \epsilon_{PT}(f^{Syn})
$$
  
\n
$$
= |\epsilon_{PT}(\rho \circ \psi, f^{Syn}) - \epsilon_{PSyn}(\rho \circ \psi, f^{Syn})| + \epsilon_{PT}(f^{Syn})
$$
  
\n
$$
= |\epsilon_{\psi \circ PT}(\rho, f^{Syn} \circ \psi^{-1}) - \epsilon_{\psi \circ PSyn}(\rho, f^{Syn} \circ \psi^{-1})| + \epsilon_{PT}(f^{Syn})
$$
  
\n
$$
\leq \sup_{\rho, \rho'} |\epsilon_{\psi \circ PT}(\rho, \rho') - \epsilon_{\psi \circ PSyn}(\rho, \rho')| + \epsilon_{PT}(f^{Syn})
$$
  
\n
$$
\leq \frac{1}{2} d_{\mathcal{P} \Delta \mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T) + \epsilon_{PT}(f^{Syn})
$$
 (19)

where (a) is by definitions and (b) relies on the triangle inequality for classification error [\(Ben-David et al.,](#page-8-7) [2006;](#page-8-7) [Crammer](#page-8-8) [et al.,](#page-8-8) [2008\)](#page-8-8). Thus, we have that

$$
\epsilon_{P^T}(M) \le \epsilon_{P^{Syn}}(M) + \frac{1}{2} d_{\mathcal{P}\Delta\mathcal{P}}(\psi \circ P^{Syn}, \psi \circ P^T) + \epsilon_{P^T}(f^{Syn}). \tag{20}
$$

<span id="page-14-4"></span>Similarly, as the the extended KD dataset shares the same feature distribution with the global virtual dataset, thus the above bound also holds for  $f_{KD}^{Syn}$ .  $\Box$  **Lemma 2** (Appendix A [\(Feng et al.,](#page-8-4) [2021\)](#page-8-4)). *For the extended source domain*  $(\mathbf{x}^{Syn}, \hat{y}^{Syn}) \sim \hat{P}^{Syn}$ , training the *related model with the knowledge distillation loss*  $L_{KD} = D_{KD}(\hat{y}^{Syn} \| h(x))$  *equals to optimizing the task risk*  $\epsilon_{\hat{P}^{Syn}} =$  $\mathbb{P}_{(\mathbf{x}^{Syn}, \hat{y}^{Syn}) \sim \hat{P}^{Syn}}[h(\mathbf{x}) \neq \arg \max \hat{y}^{Syn}].$ 

<span id="page-15-1"></span>**Claim 1.** With the training source data at ith client as  $P_i^S$  with the component weight  $\alpha = [\alpha, \alpha^{Syn}, \alpha_{KD}^{Syn}]^\top$  on the local data, virtual data and extended KD data,  $\epsilon_{P_i^S}(h)$  is minimized by optimizing the loss:

$$
\min_{M \in \mathcal{M}} \alpha \mathbb{E}_{(\mathbf{x}, y) \sim P_i} L_{CE}(y, M(\mathbf{x})) + \alpha^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P^{Syn}} L_{CE}(y, M(\mathbf{x})) + \alpha_{KD}^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P_{KD}^{Syn}} L_{KL}(y \| M(\mathbf{x})) \tag{21}
$$

*Proof.* Note that

$$
\min_{M \in \mathcal{M}} \mathbb{E}_{(\mathbf{x}, y) \sim P_i^{(S)}} L_{KL}(y \| M(\mathbf{x}))
$$

$$
\propto \min_{M \in \mathcal{M}} \alpha \mathbb{E}_{(\mathbf{x}, y) \sim P_i} L_{KL}(y \| M(\mathbf{x})) + \alpha^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P^{Syn}} L_{KL}(y \| M(\mathbf{x})) + \alpha^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P_{KD}^{Syn}} L_{KL}(y \| M(\mathbf{x}))
$$

$$
\propto \min_{M \in \mathcal{M}} \alpha \mathbb{E}_{(\mathbf{x}, y) \sim P_i} L_{CE}(y, M(\mathbf{x})) + \alpha^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P^{Syn}} L_{CE}(y, M(\mathbf{x})) + \alpha^{Syn} \mathbb{E}_{(\mathbf{x}, y) \sim P_{KD}^{Syn}} L_{KL}(y \| M(\mathbf{x}))
$$

where (a) is because  $L_{KL}(y||h(\mathbf{x})) = L_{CE}(y, h(\mathbf{x})) - H(y)$ , where  $H(y) = -y \log(y)$  is a constant depending on data distribution. With Lemma [2](#page-14-4) and Pinsker's inequality, it is easy to show that  $\epsilon_{P_i^S}(h)$  is minimized by optimizing the above loss.  $\Box$ 

## B. Privacy Discussion for DESA

Sharing image-level information among clients may raise privacy concerns. However, we claim that decentralized FL with both data and model heterogeneities is an extremely challenging setting, where existing solutions either require sharing real public data [\(Lin et al.,](#page-9-5) [2020;](#page-9-5) [Huang et al.,](#page-9-0) [2022\)](#page-9-0) or synthetic data generated from real data with GAN-based generator [\(Zhang](#page-11-4) [et al.,](#page-11-4) [2022a](#page-11-4)[;b\)](#page-11-3). Instead, we propose to use distribution matching to distill data, a simple and less data-greedy strategy, for data synthesis. Research has shown that using distilled data can defend against privacy attacks [\(Dong et al.,](#page-8-9) [2022\)](#page-8-9) such as membership inference attacks (MIA) [\(Shokri et al.,](#page-10-13) [2017\)](#page-10-13) and gradient inversion attacks [\(Huang et al.,](#page-9-20) [2021b\)](#page-9-20). We show the DESA's defense against MIA [\(Carlini et al.,](#page-8-10) [2022a\)](#page-8-10) in Appendix [B.1.](#page-15-0) In addition, recent papers have successfully applied differential privacy (DP) [\(Abadi et al.,](#page-8-5) [2016\)](#page-8-5) mechanism into data distillation [\(Xiong et al.,](#page-10-14) [2023;](#page-10-14) [Wang et al.,](#page-10-15) [2023\)](#page-10-15) to ensure privacy. We also discuss the feasibility of adding DP into data distillation process following [\(Xiong et al.,](#page-10-14) [2023\)](#page-10-14) and show that DESA is still effective using the DP synthetic anchor data in Appendix [B.2.](#page-16-0) We would like to put more emphasis on our proposed methodology and theoretical analysis in the main text, as sharing synthetic data commonly exists in the related work mentioned above and we fairly align with their settings in our comparisons. Thus, we consider the potential privacy risk of FL with DESA is beyond the main scope of our study and leave it in our Appendix.

<span id="page-15-0"></span>

## B.1. Membership Inference Attack

We show what under the basic setting of DESA *(i.e.*, not applying Differential Privacy when generating local synthetic data), we can better protect the membership information of local real data than local training or FedAvg [\(McMahan et al.,](#page-10-2) [2017\)](#page-10-2) on local real data only when facing Membership Inference Attack (MIA) on trained local models. Although we share the logits during communication, it's important to note that these logits are from synthetic anchor data and not real data that needs protection. Therefore, we cannot use MIA methods that rely on logits. Instead, we perform a strong MIA attack recently proposed and evaluate it following the approach in [\(Carlini et al.,](#page-8-10) [2022a\)](#page-8-10).

The goal of the experiment is to investigate whether our local model is vulnerable to MIA, namely leaking information about local real datasets' membership. To compare and demonstrate the effectiveness of the chosen attack, we also present results from local training and FedAvg training. We conduct MIA experiments using DIGITS. The MIA for local training and FedAvg is related to real local training data. Since we use synthetic anchor data generated from other clients with data distillation, we also provide MIA results for inferring real data of other clients. For example, if attacking SVHN's local model, local training and FeAvg report the MIA results on SVHN only, while we also report MIA results on MNIST, USPS, SynthDigits, MNIST-M for DESA.

Using the metric in [\(Carlini et al.,](#page-8-10) [2022a\)](#page-8-10), the results are shown in Figure [4.](#page-16-1) The Ref(diagonal) line indicates MIA cannot tell the differences between training and testing data. If the line bends towards True Positive Rate, it means the membership

<span id="page-16-1"></span>Image /page/16/Figure/1 description: The image displays three Receiver Operating Characteristic (ROC) curves, labeled (a) SVHN, (b) SynthDigits, and (c) MNIST-M. Each plot has the False Positive Rate on the x-axis, ranging from 0.0 to 1.0, and the True Positive Rate on the y-axis, also ranging from 0.0 to 1.0. Several lines representing different methods are plotted in each graph, including DeSA(MNIST), DeSA(SVHN), DeSA(USPS), DeSA(SynthDigits), DeSA(MNIST-M), Local(SVHN), FedAvg(SVHN), Local(SynthDigits), FedAvg(SynthDigits), Local(MNIST-M), FedAvg(MNIST-M), and Ref(diagonal). The Ref(diagonal) line is a dashed black line representing a baseline. The other lines represent the performance of various DeSA (Domain-specific adversarial) methods, local methods, and federated averaging methods on different datasets.

Figure 4: MIA on the models trained by SVHN, SynthDigits, and MNIST-M clients. Observe that the synthetic data sharing of DESA does not reveal other clients' local data identity information.

form the training set can be inferred. It is shown that all the MIA curves of targeted and other cients lie along the Ref line for DESA's model, which indicates that the membership of each training sets is well protected given the applied attack. While the curves for the MIA attacks on FedAvg and local training with SVHN dataset are all offset the Ref (diagonal) line towards True Positive, indicating they are more vulnerable to MIA and leaking training data information.

<span id="page-16-0"></span>

## B.2. Differential Privacy for Data Synthesis

To enhance the data privacy-preservation on shared synthetic anchor data, we apply the Differential Privacy stochastic gradient descent (DP-SGD) [\(Abadi et al.,](#page-8-5) [2016\)](#page-8-5) for the synthetic image generation. DP-SGD protects local data information via noise injection on clipped gradients. In our experiments, we apply Gaussian Mechanism for the inejcted noise. Specifically, we first sample a class-balanced subset from the raw data to train the objective [3.](#page-2-1) We set up the batch size as 256. For each iteration, we clip the gradient so that its  $l_2$ -norm is 2. The injected noises are from  $\mathcal{N}(0, 1.2)$ . This step ensures ( $\epsilon$ ,  $\delta$ )-DP with ( $\epsilon$ ,  $\delta$ ) values in {(7.622, 0.00015), (10.3605, 0.00021), (8.6677, 0.00017), (7.3174, 0.00014), (7.6221, 0.00015)} guarantees for {MNIST, SVHN, USPS, SynthDigits, MNIST-M}, respectively. We visualize the non-DP and DP synthetic images from each clients in DIGITS in Figure [5](#page-16-2) and Figure [6,](#page-17-1) respectively. One can observe that the synthetic data with DP mechanism is noisy and hard to inspect the individual information of the raw data.

<span id="page-16-2"></span>Image /page/16/Figure/6 description: The image displays five grids of images, each labeled with a letter and a dataset name: (a) Mnist, (b) SVHN, (c) USPS, (d) SynthDigits, and (e) Mnist-M. Each grid contains multiple small images of handwritten digits. The Mnist, USPS, and Mnist-M grids show clearer, more distinct digits, while the SVHN and SynthDigits grids display digits that are more abstract and colorful, suggesting they are generated or processed in a different manner. The digits range from 0 to 9, arranged in rows.

Figure 5: Visualization of the global and local synthetic images from the DIGITS dataset. (a) visualized the MNIST client; (b) visualized the SVHN client; (c) visualized the USPS client; (d) visualized the SynthDigits client; (e) visualized the MNIST-M client; (f) visualized the server synthetic data.

We replace the synthetic data by DP synthetic data and perform DIGITS experiments, and the result is shown in Table [6.](#page-17-2) It can be observed that although DESA's performance slightly drops due to the DP mechanism, the averaged inter and intra-accuracy are in the second place, which indicates that DESA is robust as long as we can synthesize images that roughly captures the global data distribution.

<span id="page-17-1"></span>Image /page/17/Figure/1 description: The image displays five grids of images, each labeled with a letter and a dataset name: (a) Mnist, (b) SVHN, (c) USPS, (d) SynthDigits, and (e) Mnist-M. Each grid contains multiple small, colorful, and somewhat noisy images of digits. The Mnist grid appears to show digits 0 through 9 repeated in rows. The other grids also contain digit images, but they are less clearly discernible due to the noise and color variations.

Figure 6: Visualization of the global and local synthetic images from the DIGITS dataset with **DP mechanism**. (a) visualized the MNIST client; (b) visualized the SVHN client; (c) visualized the USPS client; (d) visualized the SynthDigits client; (e) visualized the MNIST-M client; (f) visualized the server synthetic data.

<span id="page-17-2"></span>Table 6: We add the the results for DESA trained with DP synthetic anchor data into our Table [2.](#page-6-2) The best result is marked as bold, and the second best is marked as blue. The table shows that DESA with DP synthetic anchor data can still obtain comparable results as DESA with non-DP synthetic data.

|                              |                      | <b>DIGITS</b> |       |       |        |       |                    |
|------------------------------|----------------------|---------------|-------|-------|--------|-------|--------------------|
|                              |                      | MN(C)         | SV(A) | US(C) | Syn(A) | MM(C) | Avg                |
| FedHe                        |                      | 59.51         | 66.67 | 49.89 | 75.39  | 71.57 | 64.81              |
| FedDF                        | Cifar100             | 65.98         | 65.21 | 61.30 | 69.65  | 74.48 | 67.32              |
|                              | <b>FMNIST</b>        | 43.05         | 69.14 | 44.95 | 74.67  | 71.27 | $\overline{60.61}$ |
| <b>FCCL</b>                  | CIFAR <sub>100</sub> |               | -     |       |        |       |                    |
|                              | <b>FMNIST</b>        | 46.43         | 61.02 | 42.64 | 63.05  | 66.39 | 55.91              |
| FedProto                     |                      | 62.59         | 71.74 | 58.52 | 81.19  | 74.44 | 69.70              |
| $DESA(D_{\text{VHL}}^{Syn})$ |                      | 54.40         | 62.03 | 42.34 | 67.75  | 73.03 | 59.91              |
| <b>DESA</b>                  |                      | 70.12         | 76.17 | 71.17 | 81.10  | 73.83 | 74.47              |
| DESA(DP)                     |                      | 69.06         | 71.54 | 63.92 | 78.93  | 73.16 | 71.12              |

## C. Local Epoch

Here we present the effect of local epochs on DESA. To ensure fair comparison, we fix the total training iterations for the three experiments, *i.e.*,we set FL communication rounds to 50 when local epochs is 2 to match up with local epoch equals to 1 when FL communication rounds is 100. Figure [7](#page-17-3) shows that DESA is robust to various local epoch selections. The experiment is run on DIGITS dataset, and we report the global accuracy.

<span id="page-17-0"></span>

## D. Communication Overhead

As noted in Section [3.1,](#page-2-0) DESA only requires sharing logits w.r.t. Global synthetic data during training. Thus it has a relatively low communication overhead compared to baseline methods which require sharing model parameters. For fair comparison, we analyze the communication cost based on the number of parameters Pre-FL and During-FL in Table [8.](#page-18-1) Note that we show the number of parameters for one communication round for During-FL, and the total communication cost depends on the number of global iterations. One can observe that sharing logits can largely reduce the communication overhead. For example, if we use ConvNet as our model, set IPC=50, and train for 100 global iteration, the total number of parameters for communication for DeSA will be  $30.7 \text{ K} \times 50 \text{ (Pre-FL)} + 10 \text{ (number of classes)} \times 50 \text{ (images/class)}$ 

<span id="page-17-3"></span>Table 7: Ablation study on number of local epochs. The experiment is run on DIGITS dataset.

| Local Epoch | 1     | 2     | 5     |
|-------------|-------|-------|-------|
| Global Acc  | 74.47 | 74.15 | 74.34 |

 $\times$  10 (logits/image)  $\times$  100 (global iteration) = 2.04M. In comparison, baseline methods need to share 0 (Pre-FL) + 320K (parameters/iteration)  $\times$  100 (global iteration) = 32M, which is much larger than DeSA. Under model heterogeneity experimental setting, clients using AlexNet would suffer even higher total communication cost, which is 0 (Pre-FL) + 1.87M (parameters/iteration)  $\times$  100 (global iteration) = 187M.

<span id="page-18-1"></span>Table 8: Comparison of communication overhead. Note that for DESA, we only share virtual global anchor logits during training. The total communication cost counts the total parameter transferred for 100 global iterations. IPC is the synthesized images per class, and C is the number of classes.

|           | ConvNet | AlexNet | Global Anchor Logits           |
|-----------|---------|---------|--------------------------------|
| Pre-FL    | 0       | 0       | 30.7 K $\times$ IPC $\times$ C |
| During-FL | 320 K   | 1.87 M  | $100 \times IPC \times C$      |
| Total     | 32M     | 187M    | $40.7K \times IPC \times C$    |

<span id="page-18-0"></span>

## E. Datasets and Synthetic Images

<span id="page-18-2"></span>Image /page/18/Picture/5 description: The image displays five grids of numbers, labeled (a) through (e). Grid (a) shows clear, white digits on a black background: three '0's, two '1's, and three '2's. Grid (b) presents a 3x3 arrangement of colorful images, each containing a digit. The digits visible are '0', '1', '2', and '10'. Grid (c) is similar to (a) but with slightly blurred, grayscale digits: three '0's, two '1's, and three '2's. Grid (d) features a 3x3 grid of colorful images with digits drawn on them, including '0', '1', and '2'. Grid (e) also shows a 3x3 grid of colorful images with digits, displaying '0', '1', and '2'.

Figure 7: Visualization of the original digits dataset. (a) visualized the MNIST client; (b) visualized the SVHN client; (c) visualized the USPS client; (d) visualized the SynthDigits client; (e) visualized the MNIST-M client.

<span id="page-18-3"></span>Image /page/18/Picture/7 description: The image displays a grid of objects categorized into four groups labeled (a) Amazon, (b) Caltech, (c) DSLR, and (d) Webcam. Each group contains three rows and three columns of images, showcasing different items. The Amazon section features backpacks, a scooter, bicycles, and calculators. The Caltech section includes backpacks, a briefcase, bicycles, calculators, and a color swatch. The DSLR section shows backpacks and bicycles. The Webcam section also displays backpacks and bicycles. The overall arrangement is a comparative visual dataset of objects captured under different conditions or from different sources.

Figure 8: Visualization of the original digits dataset. (a) visualized the Amazon client; (b) visualized the Caltech client; (c) visualized the DSLR client; (d) visualized the Webcam client

Detailed Information of Selected Datasets 1) DIGITS={MNIST [\(LeCun et al.,](#page-9-13) [1998\)](#page-9-13), SVHN [\(Netzer et al.,](#page-10-10) [2011\)](#page-10-10), USPS [\(Hull,](#page-9-14) [1994\)](#page-9-14), SynthDigits [\(Ganin & Lempitsky,](#page-9-15) [2015\)](#page-9-15), MNIST-M [\(Ganin & Lempitsky,](#page-9-15) [2015\)](#page-9-15)} consists of 5 digit datasets with handwritten, real street and synthetic digit images of  $0, 1, \dots, 9$ . Thus, we assume 5 clients for this set of experiments. We use DIGITS as baseline to show DESA can handle FL under large domain shift. Example images can be found in Figure [7.](#page-18-2)

2) OFFICE={Amazon [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11), Caltech [\(Griffin et al.,](#page-9-16) [2007\)](#page-9-16), DSLR [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11), and WebCam [\(Saenko](#page-10-11)

<span id="page-19-0"></span>Image /page/19/Picture/1 description: The image displays a grid of six smaller grids, labeled (a) through (f). Each smaller grid contains nine images arranged in a 3x3 format. The images appear to be generated samples, possibly from a machine learning model, and depict various objects such as airplanes, cars, and birds. The overall presentation is a comparison of different sets of generated images.

Figure 9: Visualization of the original CIFAR10C. Sampled images from the first six clients.

[et al.,](#page-10-11) [2010\)](#page-10-11)} consists of four data sources from Office-31 [\(Saenko et al.,](#page-10-11) [2010\)](#page-10-11) (Amazon, DSLR, and WebCam) and Caltech-256 [\(Griffin et al.,](#page-9-16) [2007\)](#page-9-16) (Caltech), resulting in four clients. Each client possesses images that were taken using various camera devices in different real-world environments, each featuring diverse backgrounds. We show DESA can handle FL under large domain shifted real-world images using OFFICE. Example images can be found in Figure [8.](#page-18-3) 3) CIFAR10C consists subsets extracted from Cifar10-C [\(Hendrycks & Dietterich,](#page-9-17) [2019\)](#page-9-17), a collection of augmented Cifar10 [\(Krizhevsky et al.,](#page-9-18) [2009\)](#page-9-18) that applies 19 different corruptions. We employ a Dirichlet distribution with  $\beta = 2$  for the purpose of generating three partitions within each distorted non-IID dataset. As a result, we have 57 clients with domainand label-shifted datasets. Example images can be found in Figure [9.](#page-19-0)

Synthetic Data Generation We fix ConvNet as the backbone for data synthesis to avoid additional domain shift caused by different model architectures. We set learning rate to 1 and use SGD optimizer with momentum  $= 0.5$ . The batch size for DIGITS and CIFAR10 is set to 256, while we use 32 for OFFICE as it's clients has fewer data points. For the same reason, we use 500 synthetic data points for DIGITS and CIFAR10C, and we set 100 synthetic data points for OFFICE. The training iteration for DIGITS and OFFICE is 1000, and we set 2000 for CIFAR10C since it contains more complex images.

We show the local synthetic images and global anchor images of DIGITS, OFFICE, and CIFAR10C in Figure [10,](#page-19-1) Figure [11,](#page-20-2) and Figure [12,](#page-20-3) respectively.

<span id="page-19-1"></span>Image /page/19/Figure/6 description: The image displays a grid of six subfigures, labeled (a) through (f). Each subfigure contains a 3x3 grid of colorful, noisy images of handwritten digits. Subfigure (a) shows digits 0, 1, and 2. Subfigure (b) shows a grid of mostly gray, noisy images. Subfigure (c) also shows digits 0, 1, and 2, with a different color palette and noise pattern than (a). Subfigures (d), (e), and (f) all show similar 3x3 grids of noisy, colorful digit images, with (d) labeled "SynthDigits", (e) labeled "Mnist-M", and (f) labeled "Average". The overall impression is a comparison of digit generation or representation across different datasets.

Figure 10: Visualization of the sampled global and local synthetic images from the DIGITS dataset. (a) visualized the MNIST client's synthetic data; (b) visualized the SVHN client's synthetic data; (c) visualized the USPS client's synthetic data; (d) visualized the SynthDigits client's synthetic data; (e) visualized the MNIST-M client's synthetic data; (f) visualized the server synthetic data.

<span id="page-20-2"></span>Image /page/20/Picture/1 description: The image displays a grid of generated images, categorized by source and labeled (a) Amazon, (b) Caltech, (c) DSLR, (d) Webcam, and (e) Average. Each category contains three rows of images. The first row in each category shows images of backpacks. The second row shows images of bicycles. The third row shows images of cameras. The images in categories (b), (c), and (d) appear to be more distorted and less clear than those in categories (a) and (e).

Figure 11: Visualization of the sampled global and local synthetic images from the OFFICE dataset. (a) visualized the Amazon client's synthetic data; (b) visualized the Caltech client's synthetic data; (c) visualized the DSLR client's synthetic data; (d) visualized the Webcam client's synthetic data; (e) visualized the averaged synthetic data.

<span id="page-20-3"></span>Image /page/20/Figure/3 description: The image displays a grid of images, organized into six columns labeled (a) Client0, (b) Client1, (c) Client2, (d) Client3, (e) Client4, and (f) Average. Each of the first five columns contains a 3x3 grid of smaller images, which appear to be generated samples, many of them with significant noise and color artifacts. The final column, labeled 'Average', also contains a 3x3 grid of images, but these images are much blurrier and less distinct, suggesting an averaged or representative output. The images within the client columns show a variety of colors and textures, with some appearing to have faint shapes or patterns, while others are predominantly noisy. The 'Average' column shows muted colors and a general lack of detail across all nine images.

Figure 12: Visualization of the sampled global and local synthetic images from the first 5 clients in CIFAR10C dataset. (a) visualized the first 's synthetic data; (b) visualized the second client's synthetic data; (c) visualized the third client's synthetic data; (d) visualized the forth client's synthetic data; (e) visualized the fifth client's synthetic data; (f) visualized the server synthetic data.

<span id="page-20-1"></span>

## F. Model Architectures

We use ConvNet to perform data distillation for the best synthesis quality. For model hetero genenity scenarios, we randomly select classification model architectures from {AlexNet, ConvNet}. The detailed setup for bot models are depicted in Table [9](#page-20-4) and Table [10](#page-21-0)

<span id="page-20-4"></span>Table 9: AlexNet architecture. For the convolutional layer (Conv2D), we list parameters with a sequence of input and output dimensions, kernel size, stride, and padding. For the max pooling layer (MaxPool2D), we list kernel and stride. For a fully connected layer (FC), we list input and output dimensions.

| Layer | Details                                         |
|-------|-------------------------------------------------|
| 1     | Conv2D(3, 128, 5, 1, 4), ReLU, MaxPoo2D(2, 2)   |
| 2     | Conv2D(128, 192, 5, 1, 2), ReLU, MaxPoo2D(2, 2) |
| 3     | Conv2D(192, 256, 3, 1, 1), ReLU                 |
| 4     | Conv2D(256, 192, 3, 1, 1), ReLU                 |
| 5     | Conv2D(192, 192, 3, 1, 1), ReLU, MaxPoo2D(2, 2) |
| 22    | FC(3072, num_class)                             |

<span id="page-20-0"></span>

## G. More Related Work

### G.1. Model Homogeneous Federated Learning

We list down different Model Homogeneous FL approaches in decentralized FL and collaborative methods that are relevant to our setting.

<span id="page-21-0"></span>Table 10: ConvNet architecture. For the convolutional layer (Conv2D), we list parameters with a sequence of input and output dimensions, kernel size, stride, and padding. For the max pooling layer (MaxPool2D), we list kernel and stride. For a fully connected layer (FC), we list the input and output dimensions. For the GroupNormalization layer (GN), we list the channel dimension.

| Layer | Details                                                    |
|-------|------------------------------------------------------------|
| 1     | Conv2D(3, 128, 3, 1, 1), GN(128), ReLU, AvgPool2d(2,2,0)   |
| 2     | Conv2D(128, 118, 3, 1, 1), GN(128), ReLU, AvgPool2d(2,2,0) |
| 3     | Conv2D(128, 128, 3, 1, 1), GN(128), ReLU, AvgPool2d(2,2,0) |
| 4     | FC(1152, num_class)                                        |

#### G.1.1. DECENTRALIZED FEDERATED LEARNING

In order to tackle training a global model without a server, Decentralized FL methods communicate a set of models through diverse decentralized client-network topologies (such as a ring - [\(Chang et al.,](#page-8-11) [2018\)](#page-8-11), Mesh - [\(Roy et al.,](#page-10-16) [2019\)](#page-10-16), or a sequential line [\(Assran et al.,](#page-8-12) [2019\)](#page-8-12)) using different communication protocols such as Single-peer(gossip) or Multiple-Peer(Broadcast). [\(Yuan et al.,](#page-11-9) [2023a;](#page-11-9) [Sheller et al.,](#page-10-17) [2019;](#page-10-17) [2020\)](#page-10-18) pass a single model from client to client similar to an Incremental Learning setup. In this continual setting, only a single model is trained. [\(Pappas et al.,](#page-10-19) [2021;](#page-10-19) [Roy et al.,](#page-10-16) [2019;](#page-10-16) [Assran et al.,](#page-8-12) [2019\)](#page-8-12) pass models and aggregate their weights similar to conventional FL. Since these models use averaged aggregation techniques similar to FedAvg, most of these methods assume client **model homogeneity**. DESA's client network topology is similar to that of a Mesh using the broadcast-gossip protocol, where every client samples certain neighbours in each communication round for sharing logits.

None of the works above aim to train various client model types without a server, which is our goal.

#### G.1.2. COLLABORATIVE METHODS

[\(Fallah et al.,](#page-8-13) [2020\)](#page-8-13) uses an MAML(model agnostic meta learning) framework to explicitly train model homogeneous client models to personalize well. The objective function of MAML evaluates the personalized performance assuming a one-step gradient descent update on the subsequent task. [\(Huang et al.,](#page-9-21) [2021a\)](#page-9-21) modifies the personalized objective by adding an attention inducing term to the objective function which promotes collaboration between pairs of clients that have similar data.

[\(Ghosh et al.,](#page-9-22) [2022\)](#page-9-22) captures settings where different groups of users have their own objectives (learning tasks) but by aggregating their private data with others in the same cluster (same learning task), they can leverage the strength in numbers in order to perform more efficient personalized federated learning [\(Donahue & Kleinberg,](#page-8-14) [2021\)](#page-8-14) uses game theory to analyze whether a client should jointly train with other clients in a conventional FL setup [\[2.1\]](#page-1-4) assuming it's primary objective is to minimize the MSE loss on its own private dataset. They also find techniques where it is more beneficial for the clients to create coalitions and train one global model.

All the above works either slightly change the intra-client objective to enable some collaboration between modelhomogeneous clients or explicitly create client clusters to collaboratively learn from each other. They do not tackle the general objective function that we do- [2](#page-2-3)

### G.2. Model Heterogeneous Federated Learning

Model heterogeneous FL approaches relevant to DESA broadly come under the following two types.

#### G.2.1. KNOWLEDGE DISTILLATION METHODS

[\(Gong et al.,](#page-9-9) [2022\)](#page-9-9) proposes FedKD that is a one-shot centralized Knowledge distillation approach on unlabelled public data after the local training stage in-order to mitigate the accuracy drop due to the label shift amongst clients. DENSE [\(Zhang](#page-11-4) [et al.,](#page-11-4) [2022a\)](#page-11-4) propose one-shot federated learning to generate decision boundary-aware synthetic data and train the global model on the server side. FedFTG [\(Zhang et al.,](#page-11-3) [2022b\)](#page-11-3) finetunes the global model by knowledge distillation with hard sample mining. [\(Yang et al.,](#page-11-10) [2021\)](#page-11-10) introduces a method called Personalized Federated Mutual Learning (PFML), which leverages the non-IID properties to create customized models for individual parties. PFML incorporates mutual learning into the local update process within each party, enhancing both the global model and personalized local models. Furthermore, mutual distillation is employed to expedite convergence. The method assumes homogeneity of models for global server aggregation. However, all the above methods are centralized.

#### G.2.2. MUTUAL LEARNING METHODS

Papers in this area predominantly use ideas from deep-mutual learning [\(Zhang et al.,](#page-11-11) [2018\)](#page-11-11) [\(Matsuda et al.,](#page-10-20) [2022\)](#page-10-20) uses deep mutual learning to train heterogeneous local models for the sole purpose of personalization. The method creates clusters of clients whose local models have similar outputs. Clients within a cluster exchange their local models in-order to tackle label shift amongst the data points. However, the method is centralized and each client maintains two copies of models, one which is personalized and one that is exchanged. [\(Li et al.,](#page-9-23) [2021a\)](#page-9-23) has a similar setting to [\(Chan & Ngai,](#page-8-1) [2021\)](#page-8-1), but instead solves the problem in a peer to peer decentralized manner using soft logit predictions on the local data of a client itself. It makes its own baselines that assume model homogeneity amongst clients, also their technique assumes that there is no covariate shift because it only uses local data for the soft predictions. However, their technique can be modified for model heterogeneity. They report personalization(Intra) accuracies only.

## G.3. Dataset Distillation

Data distillation methods aim to create concise data summaries  $D_{syn}$  that can effectively substitute the original dataset D in tasks such as model training, inference, and architecture search. Moreover, recent studies have justified that data distillation also preserves privacy [\(Dong et al.,](#page-8-9) [2022;](#page-8-9) [Carlini et al.,](#page-8-15) [2022b\)](#page-8-15) which is critical in federated learning. In practice, dataset distillation is used in healthcare for medical data sharing for privacy protection [\(Li et al.,](#page-9-24) [2022\)](#page-9-24). We briefly mention two types of Distillation works below.

### G.3.1. GRADIENT AND TRAJECTORY MATCHING TECHNIQUES

Gradient Matching [\(Zhao et al.,](#page-11-7) [2020\)](#page-11-7) is proposed to make the deep neural network produce similar gradients for both the terse synthetic images and the original large-scale dataset. The objective function involves matching the gradients of the loss w.r.t weights(parameters) evaluated on both D and  $D_{syn}$  at successive parameter values during the optimization on the original dataset  $D$ . Usually the cosine distance is used to measure the difference in gradient direction. Other works in this area modify the objective function slightly, by either adding class contrastive signals for better stability [\(Lee et al.,](#page-9-25) [2022\)](#page-9-25) or by adding same image-augmentations(such as crop, rotate to both D and  $D_{syn}$ )[\(Zhao & Bilen,](#page-11-12) [2021\)](#page-11-12). A similar technique is that of [\(Cazenavette et al.,](#page-8-16) [2022\)](#page-8-16) which tries to match the intermediate parameters in the optimization trajectory of both  $D$ and  $D_{Syn}$ . It is very *computationally expensive* because of a gradient unrolling in the optimization. TESLA [\(Cui et al.,](#page-8-17) [2023\)](#page-8-17) attempts at using linear-algebraic manipulations to give better computational guarantees for Trajectory matching

### G.3.2. DISTRIBUTION MATCHING TECHNIQUES

Distribution matching [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2) solves the distillation task via a single-level optimization, leading to a *vastly improved scalability*. More specifically, instead of matching the quality of models on  $D$  vs.  $D_{syn}$ , distribution-matching techniques directly match the distribution of D vs.  $D_{syn}$  in a latent encoded space. See [3](#page-2-1) for the objective function. CAFE [\(Wang et al.,](#page-10-21) [2022\)](#page-10-21) further refines the distribution-matching idea by solving a bilevel optimization problem for jointly optimizing a single encoder and the data summary, rather than using a pre-determined set of encoders Adversarial techniques using Distribution matching such as IT-GAN [\(Zhao & Bilen,](#page-11-13) [2022\)](#page-11-13) and GAN [\(Goodfellow et al.,](#page-9-26) [2014\)](#page-9-26) aren't suitable for a serverless setting. Since we aim to mitigate drifts in client-distribution across using our synthetic data, Distribution Matching is a more natural option for our work.