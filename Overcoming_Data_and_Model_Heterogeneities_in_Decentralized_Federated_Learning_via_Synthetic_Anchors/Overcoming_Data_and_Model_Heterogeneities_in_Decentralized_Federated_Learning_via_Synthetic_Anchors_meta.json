{"table_of_contents": [{"title": "Overcoming Data and Model heterogeneities in Decentralized Federated\nLearning via Synthetic Anchors", "heading_level": null, "page_id": 0, "polygon": [[76.5, 89.25], [519.9609375, 89.25], [519.9609375, 120.849609375], [76.5, 120.849609375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 193.5], [195.75, 193.5], [195.75, 204.767578125], [148.5, 204.767578125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[305.8505859375, 193.5], [385.5, 193.5], [385.5, 205.927734375], [305.8505859375, 205.927734375]]}, {"title": "2. Preliminaries", "heading_level": null, "page_id": 1, "polygon": [[306.0, 276.75], [388.5, 276.75], [388.5, 287.33203125], [306.0, 287.33203125]]}, {"title": "2.1. Conventional Federated Learning", "heading_level": null, "page_id": 1, "polygon": [[306.0, 296.25], [469.5, 296.25], [469.5, 306.66796875], [306.0, 306.66796875]]}, {"title": "2.2. Decentralized FL and Mutual Learning", "heading_level": null, "page_id": 1, "polygon": [[305.25, 641.25], [493.6640625, 641.25], [493.6640625, 650.84765625], [305.25, 650.84765625]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 425.25], [108.0, 425.25], [108.0, 436.21875], [54.0, 436.21875]]}, {"title": "3.1. Notation and Problem Setup", "heading_level": null, "page_id": 2, "polygon": [[54.0, 445.5], [195.0, 445.5], [195.0, 455.94140625], [54.0, 455.94140625]]}, {"title": "3.2. Synthetic Anchor Datasets Generation", "heading_level": null, "page_id": 2, "polygon": [[306.0, 377.25], [489.0, 377.25], [489.0, 386.525390625], [306.0, 386.525390625]]}, {"title": "3.3. REG Loss for Feature Regularization", "heading_level": null, "page_id": 3, "polygon": [[54.0, 591.75], [232.5, 591.75], [232.5, 602.12109375], [54.0, 602.12109375]]}, {"title": "3.4. Knowledge Distillation for Information Exchange", "heading_level": null, "page_id": 3, "polygon": [[306.0, 640.5], [537.0, 640.5], [537.0, 650.84765625], [306.0, 650.84765625]]}, {"title": "4. Theoretical Analysis", "heading_level": null, "page_id": 4, "polygon": [[54.0, 639.0], [173.25, 639.0], [173.25, 650.84765625], [54.0, 650.84765625]]}, {"title": "5. <PERSON>", "heading_level": null, "page_id": 5, "polygon": [[54.0, 656.25], [129.0, 656.25], [129.0, 667.08984375], [54.0, 667.08984375]]}, {"title": "5.1. Training Setup", "heading_level": null, "page_id": 5, "polygon": [[54.0, 676.5], [136.5, 676.5], [136.5, 686.8125], [54.0, 686.8125]]}, {"title": "5.2. Heterogeneous Model Experiments", "heading_level": null, "page_id": 6, "polygon": [[54.0, 264.75], [222.75, 264.75], [222.75, 275.34375], [54.0, 275.34375]]}, {"title": "5.3. Homogeneous Model Experiments", "heading_level": null, "page_id": 6, "polygon": [[306.0, 302.25], [471.75, 302.25], [471.75, 312.08203125], [306.0, 312.08203125]]}, {"title": "5.4. Ablation studies for DESA", "heading_level": null, "page_id": 7, "polygon": [[54.0, 129.75], [186.0, 129.75], [186.0, 139.9921875], [54.0, 139.9921875]]}, {"title": "5.5. Further Discussion", "heading_level": null, "page_id": 7, "polygon": [[305.25, 165.75], [405.75, 165.75], [405.75, 176.0537109375], [305.25, 176.0537109375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 570.75], [377.25, 570.75], [377.25, 582.01171875], [306.0, 582.01171875]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 192.0], [150.908203125, 192.0], [150.908203125, 203.607421875], [54.0, 203.607421875]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[54.0, 276.0], [147.75, 276.0], [147.75, 287.33203125], [54.0, 287.33203125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 360.0], [111.75, 360.0], [111.75, 371.25], [54.0, 371.25]]}, {"title": "A. Theoretical Analysis and Proofs", "heading_level": null, "page_id": 12, "polygon": [[54.0, 168.0], [234.0, 168.0], [234.0, 179.244140625], [54.0, 179.244140625]]}, {"title": "A.1. Notation", "heading_level": null, "page_id": 12, "polygon": [[54.0, 188.25], [112.5, 188.25], [112.5, 199.16015625], [54.0, 199.16015625]]}, {"title": "A.2. Proof for Theorem 1", "heading_level": null, "page_id": 12, "polygon": [[54.0, 530.25], [162.0, 530.25], [162.0, 540.24609375], [54.0, 540.24609375]]}, {"title": "A.3. Interpretation for Theorem 1", "heading_level": null, "page_id": 13, "polygon": [[54.0, 341.25], [198.75, 340.5], [198.75, 351.9140625], [54.0, 351.9140625]]}, {"title": "A.4. Proof for Proposition 2", "heading_level": null, "page_id": 14, "polygon": [[54.0, 69.0], [173.25, 69.0], [173.25, 78.697265625], [54.0, 78.697265625]]}, {"title": "A.5. Some useful lemmas and claims", "heading_level": null, "page_id": 14, "polygon": [[54.0, 389.25], [210.375, 389.25], [210.375, 399.8671875], [54.0, 399.8671875]]}, {"title": "B. Privacy Discussion for DESA", "heading_level": null, "page_id": 15, "polygon": [[54.0, 319.5], [218.25, 319.5], [218.25, 331.224609375], [54.0, 331.224609375]]}, {"title": "B.1. Membership Inference Attack", "heading_level": null, "page_id": 15, "polygon": [[54.0, 507.75], [203.25, 508.921875], [203.25, 519.0], [54.0, 518.9765625]]}, {"title": "B.2. Differential Privacy for Data Synthesis", "heading_level": null, "page_id": 16, "polygon": [[54.0, 355.974609375], [240.0, 355.5], [240.0, 367.576171875], [54.0, 367.576171875]]}, {"title": "C. Local Epoch", "heading_level": null, "page_id": 17, "polygon": [[54.0, 455.16796875], [136.4150390625, 455.16796875], [136.4150390625, 467.54296875], [54.0, 467.54296875]]}, {"title": "D. Communication Overhead", "heading_level": null, "page_id": 17, "polygon": [[54.0, 537.92578125], [207.0, 537.92578125], [207.0, 551.07421875], [54.0, 551.07421875]]}, {"title": "E. Datasets and Synthetic Images", "heading_level": null, "page_id": 18, "polygon": [[54.0, 250.98046875], [226.5, 250.98046875], [226.5, 264.515625], [54.0, 264.515625]]}, {"title": "F. Model Architectures", "heading_level": null, "page_id": 20, "polygon": [[54.0, 411.46875], [173.3203125, 411.46875], [173.3203125, 424.6171875], [54.0, 424.6171875]]}, {"title": "<PERSON><PERSON> More Related Work", "heading_level": null, "page_id": 20, "polygon": [[54.0, 654.328125], [173.25, 654.328125], [173.25, 666.703125], [54.0, 666.703125]]}, {"title": "G.1. Model Homogeneous Federated Learning", "heading_level": null, "page_id": 20, "polygon": [[54.0, 675.2109375], [253.107421875, 675.2109375], [253.107421875, 686.8125], [54.0, 686.8125]]}, {"title": "G.1.1. DECENTRALIZED FEDERATED LEARNING", "heading_level": null, "page_id": 21, "polygon": [[54.0, 230.291015625], [262.2216796875, 230.291015625], [262.2216796875, 241.892578125], [54.0, 241.892578125]]}, {"title": "G.1.2. COLLABORATIVE METHODS", "heading_level": null, "page_id": 21, "polygon": [[54.0, 385.55859375], [208.880859375, 385.55859375], [208.880859375, 397.546875], [54.0, 397.546875]]}, {"title": "G.2. Model Heterogeneous Federated Learning", "heading_level": null, "page_id": 21, "polygon": [[54.0, 596.70703125], [256.5, 596.70703125], [256.5, 608.30859375], [54.0, 608.30859375]]}, {"title": "G.2.1. KNOWLEDGE DISTILLATION METHODS", "heading_level": null, "page_id": 21, "polygon": [[54.0, 639.6328125], [250.5673828125, 639.6328125], [250.5673828125, 651.234375], [54.0, 651.234375]]}, {"title": "G.2.2. MUTUAL LEARNING METHODS", "heading_level": null, "page_id": 22, "polygon": [[54.0, 127.3271484375], [218.7421875, 127.3271484375], [218.7421875, 138.9287109375], [54.0, 138.9287109375]]}, {"title": "G.3. Dataset Distillation", "heading_level": null, "page_id": 22, "polygon": [[54.0, 266.255859375], [157.5, 266.255859375], [157.5, 278.244140625], [54.0, 278.244140625]]}, {"title": "G.3.1. GRADIENT AND TRAJECTORY MATCHING TECHNIQUES", "heading_level": null, "page_id": 22, "polygon": [[54.0, 357.134765625], [320.25, 357.134765625], [320.25, 369.123046875], [54.0, 369.123046875]]}, {"title": "G.3.2. DISTRIBUTION MATCHING TECHNIQUES", "heading_level": null, "page_id": 22, "polygon": [[54.0, 494.61328125], [257.25, 494.61328125], [257.25, 506.21484375], [54.0, 506.21484375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 89], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9149, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 538], ["Line", 114], ["TableCell", 100], ["Text", 8], ["Reference", 5], ["ListItem", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2374, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 653], ["Line", 120], ["Text", 5], ["TextInlineMath", 5], ["Reference", 4], ["SectionHeader", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1041, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 554], ["Line", 119], ["TextInlineMath", 5], ["Reference", 5], ["SectionHeader", 2], ["Text", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3412, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1333], ["Line", 138], ["TextInlineMath", 13], ["Reference", 8], ["Equation", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1045, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 804], ["Line", 109], ["Text", 9], ["TextInlineMath", 5], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 660], ["TableCell", 372], ["Line", 93], ["Text", 8], ["Reference", 4], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12995, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 596], ["Line", 122], ["TableCell", 8], ["Text", 7], ["SectionHeader", 3], ["Caption", 2], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2522, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 92], ["ListItem", 18], ["Reference", 18], ["Text", 3], ["SectionHeader", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 94], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 99], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 55], ["ListItem", 14], ["Reference", 14], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 688], ["TableCell", 109], ["Line", 64], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9632, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 797], ["Line", 100], ["ListItem", 5], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1403, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1016], ["Line", 115], ["Text", 7], ["Equation", 7], ["Reference", 5], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3119, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 647], ["Line", 76], ["TextInlineMath", 4], ["Text", 4], ["Equation", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 115], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1749, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["TableCell", 101], ["Line", 64], ["Reference", 4], ["Text", 3], ["Caption", 2], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1588, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["TableCell", 32], ["Line", 26], ["Text", 4], ["Reference", 4], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3575, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 25], ["Text", 3], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 29], ["TableCell", 28], ["Reference", 5], ["SectionHeader", 3], ["Text", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4511, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 45], ["TableCell", 20], ["Text", 8], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1301, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 41], ["Text", 4], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Overcoming_Data_and_Model_Heterogeneities_in_Decentralized_Federated_Learning_via_Synthetic_Anchors"}