{"table_of_contents": [{"title": "POINT CLOUD DATASET DISTILLATION", "heading_level": null, "page_id": 0, "polygon": [[95.923828125, 81.75], [396.24609375, 81.75], [396.24609375, 97.453125], [95.923828125, 97.453125]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.1171875, 168.22265625], [334.986328125, 168.22265625], [334.986328125, 179.82421875], [276.1171875, 179.82421875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[96.521484375, 428.484375], [207.38671875, 428.484375], [207.38671875, 440.0859375], [96.521484375, 440.0859375]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[97.19384765625, 453.0], [212.25, 453.0], [212.25, 464.44921875], [97.19384765625, 464.44921875]]}, {"title": "138\n3.2 DATASET DISTILLATION WITH ROTATIONS", "heading_level": null, "page_id": 2, "polygon": [[72.75, 435.75], [312.0, 435.75], [312.0, 446.2734375], [72.75, 446.2734375]]}, {"title": "4 THE PROPOSED METHOD", "heading_level": null, "page_id": 3, "polygon": [[91.51611328125, 81.83935546875], [255.796875, 81.83935546875], [255.796875, 93.44091796875], [91.51611328125, 93.44091796875]]}, {"title": "164\n165\n4.1 PLUG-AND-PLAY POINT CLOUD ROTATOR", "heading_level": null, "page_id": 3, "polygon": [[72.75, 103.5], [312.275390625, 103.5], [312.275390625, 122.25], [72.75, 122.25]]}, {"title": "4.2 POINT-WISE GENERATOR", "heading_level": null, "page_id": 3, "polygon": [[94.5791015625, 574.27734375], [240.1083984375, 574.27734375], [240.1083984375, 584.33203125], [94.5791015625, 584.33203125]]}, {"title": "4.3 DISTILLATION TASKS", "heading_level": null, "page_id": 4, "polygon": [[94.5791015625, 516.0], [224.25, 516.65625], [224.25, 526.5], [94.5791015625, 525.9375]]}, {"title": "4.4 DISCUSSION", "heading_level": null, "page_id": 5, "polygon": [[97.2685546875, 505.44140625], [188.1123046875, 505.44140625], [188.1123046875, 515.49609375], [97.2685546875, 515.49609375]]}, {"title": "5 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[95.69970703125, 675.75], [200.25, 675.75], [200.25, 687.5859375], [95.69970703125, 687.5859375]]}, {"title": "5.2 PART SEGMENTATION", "heading_level": null, "page_id": 6, "polygon": [[94.4296875, 671.25], [226.810546875, 671.25], [226.810546875, 682.55859375], [94.4296875, 682.55859375]]}, {"title": "", "heading_level": null, "page_id": 8, "polygon": [[100.8544921875, 331.611328125], [274.4736328125, 331.611328125], [274.4736328125, 341.279296875], [100.8544921875, 341.279296875]]}, {"title": "5.4 ABLATION STUDIES", "heading_level": null, "page_id": 8, "polygon": [[93.0849609375, 355.5], [216.75, 355.5], [216.75, 365.0625], [93.0849609375, 365.0625]]}, {"title": "5.5 TIME AND SPACE OVERHEAD", "heading_level": null, "page_id": 8, "polygon": [[101.07861328125, 624.9375], [256.5, 624.9375], [256.5, 634.9921875], [101.07861328125, 634.9921875]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[93.30908203125, 648.52734375], [196.330078125, 648.52734375], [196.330078125, 659.35546875], [93.30908203125, 659.35546875]]}, {"title": "540\n541\nREFERENCES", "heading_level": null, "page_id": 10, "polygon": [[69.75, 78.0], [176.25, 78.0], [176.25, 100.5], [69.75, 100.5]]}, {"title": "A PROOF OF THEOREMS", "heading_level": null, "page_id": 13, "polygon": [[87.03369140625, 82.5], [241.751953125, 82.5], [241.751953125, 93.779296875], [87.03369140625, 93.779296875]]}, {"title": "B IMPLEMENTATION DETAILS OF DD3D", "heading_level": null, "page_id": 13, "polygon": [[102.27392578125, 654.0], [323.630859375, 654.0], [323.630859375, 665.54296875], [102.27392578125, 665.54296875]]}, {"title": "756\nAlgorithm 4 PyTorch code of DD3D", "heading_level": null, "page_id": 14, "polygon": [[72.0, 79.5], [258.1875, 79.5], [258.1875, 94.02099609375], [72.0, 94.02099609375]]}, {"title": "C DETAILS OF DATASETS", "heading_level": null, "page_id": 14, "polygon": [[107.25, 276.890625], [257.291015625, 276.890625], [257.291015625, 294.0], [107.25, 294.0]]}, {"title": "D HYPERPARAMETERS", "heading_level": null, "page_id": 14, "polygon": [[102.498046875, 561.0], [233.384765625, 561.0], [233.384765625, 572.34375], [102.498046875, 572.34375]]}, {"title": "Table 9: Hyperparameters used for Validation.", "heading_level": null, "page_id": 15, "polygon": [[211.5, 81.35595703125], [399.234375, 81.35595703125], [399.234375, 91.50732421875], [211.5, 91.50732421875]]}, {"title": "E DETAILS OF BACKBONES", "heading_level": null, "page_id": 15, "polygon": [[99.2109375, 215.982421875], [256.5, 215.982421875], [256.5, 227.583984375], [99.2109375, 227.583984375]]}, {"title": "Table 11: Results on ModelNet40", "heading_level": null, "page_id": 15, "polygon": [[339.0, 626.25], [476.630859375, 626.25], [476.630859375, 634.9921875], [339.0, 636.5390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 103], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5809, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 107], ["Text", 9], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 821], ["Line", 139], ["Text", 11], ["TextInlineMath", 6], ["ListItem", 3], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 689], ["Line", 111], ["Text", 8], ["TextInlineMath", 6], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 669], ["Line", 125], ["Text", 9], ["TextInlineMath", 6], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["ListItem", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 718, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 827], ["Line", 167], ["TableCell", 138], ["Text", 10], ["Reference", 4], ["SectionHeader", 2], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 108], ["Text", 10], ["ListItem", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 519], ["Span", 359], ["Line", 124], ["Text", 8], ["Reference", 3], ["Table", 2], ["<PERSON>Footer", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12605, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 348], ["TableCell", 214], ["Line", 130], ["Text", 6], ["Reference", 4], ["SectionHeader", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Figure", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9787, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 131], ["Text", 8], ["Reference", 4], ["Figure", 3], ["Caption", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2449, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 101], ["TableCell", 96], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TableOfContents", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3803, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 102], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["TableCell", 116], ["Line", 102], ["Reference", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4285, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1156], ["Line", 273], ["Text", 12], ["Equation", 9], ["TextInlineMath", 5], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2394, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 105], ["TableCell", 105], ["Text", 22], ["Reference", 5], ["ListItem", 4], ["SectionHeader", 3], ["Code", 3], ["Table", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5544, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 524], ["TableCell", 184], ["Line", 125], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["SectionHeader", 3], ["TextInlineMath", 3], ["Table", 2], ["Reference", 2], ["ListItem", 1], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7377, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 97], ["Text", 3], ["Figure", 3], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2074, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 69], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 797, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/POINT CLOUD DATASET DISTILLATION"}