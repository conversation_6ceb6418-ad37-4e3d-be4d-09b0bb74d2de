# <span id="page-0-0"></span>PRANC: Pseudo RAndom Networks for Compacting deep models

<PERSON>rsa Nooralineja<PERSON> University of California, <PERSON> University

Kossar <PERSON> \* University of California, Davis

> <PERSON><PERSON><PERSON>

## Abstract

*We demonstrate that a deep model can be reparametrized as a linear combination of several randomly initialized and frozen deep models in the weight space. During training, we seek local minima that reside within the subspace spanned by these random models (i.e., 'basis' networks). Our framework, PRANC, enables significant compaction of a deep model. The model can be reconstructed using a single scalar 'seed,' employed to generate the pseudo-random 'basis' networks, together with the learned linear mixture coefficients. In practical applications, PRANC addresses the challenge of efficiently storing and communicating deep models, a common bottleneck in several scenarios, including multi-agent learning, continual learners, federated systems, and edge devices, among others. In this study, we employ PRANC to condense image classification models and compress images by compacting their associated implicit neural networks. PRANC outperforms baselines with a large margin on image classification when compressing a deep model almost* 100 *times. Moreover, we show that PRANC enables memory-efficient inference by generating layerwise weights on the fly. The source code of PRANC is here:* <https://github.com/UCDvision/PRANC>

## 1. Introduction

The prevailing notion is that larger deep models yield improved accuracy. Yet, it remains unclear if the better generalization of larger models stems from the increased complexity of the architecture or more parameters. Moreover, among numerous good local minima in the loss function, training finds one. In this paper, we introduce a fresh University of California, Davis

Soroush Abbasi Koohpayegani\*

Rana Muhammad Shahroz Khan\* Vanderbilt University

Hamed Pirsiavash University of California, Davis

approach: viewing a deep model as a linear combination within the weight space of several randomly initialized and frozen models. During learning, our goal shifts to finding a minimum that exists within the subspace defined by these initial models. Our findings highlight the potential to significantly compact deep models by retaining only the seed value of the pseudo-random generator and the coefficients for weight combination.

This efficient reparameterization benefits AI and ML applications by reducing deep model size for easier storage or communication. In modern neural networks with millions to billions of parameters, storage, and communication become costly. This issue worsens in low-bitrate environments due to physical constraints or adversarial disruption. For instance, underwater applications might have as low as 100 bits per second bandwidth, then, transferring ResNet18's 11M parameter model takes more than 40 days in such conditions. Moreover, in distributed learning with many agents, high-bandwidth WiFi networks still face congestion issues.

Going beyond communications, loading or storing these large models on edge devices poses another significant challenge. Edge devices often come with small memories unsuitable for storing large neural networks and may want to run the model less frequently (on-demand). Hence, they may benefit from compacting a deep model to fewer parameters to construct the model layer-by-layer or even kernelby-kernel on-demand to run each inference. This will result in significantly less I/O cost.

One may compact the model by distilling it into a smaller model [\[18\]](#page-9-0), pruning the model parameters [\[29\]](#page-9-1), quantizing the parameters  $[26]$ , or sharing the weights as much as possible [\[40,](#page-10-0) [6\]](#page-9-3). More recently, dataset distillation [\[48\]](#page-10-1) is proposed. It can be seen as an alternative to model compression since one can store or communicate the distilled dataset and then train the model again when needed. However, most of

<sup>\*</sup>Equal contribution

<span id="page-1-1"></span>Image /page/1/Figure/0 description: The image displays a diagram illustrating a machine learning model. The diagram is divided into three main sections. The top left section, labeled 'Training Task', shows a collection of images, suggesting a classification task, and an INR (Implicit Neural Representation) model that takes an image as input and outputs RGB values. The middle section shows a process where the INR model's output is combined with scaled basis networks (represented by \$\hat{\theta}\_1, \hat{\theta}\_2, ..., \hat{\theta}\_k\$ scaled by coefficients \$\alpha\_1, \alpha\_2, ..., \alpha\_k\$) to form a final model represented by \$\theta = \sum\_i \alpha\_i \hat{\theta}\_i\$. This combined model is then fed into a 'Task Loss' function, which can be Cross Entropy or Mean Squared Error. Backpropagation is shown to update the coefficients \$\alpha\$. The bottom section is divided into two parts: 'Random Basis Networks' and 'Storing/Comm.'. The 'Random Basis Networks' section shows a 'Seed' leading to a series of basis networks (\$\hat{\theta}\_1, \hat{\theta}\_2, ..., \hat{\theta}\_k\$). The 'Storing/Comm.' section shows a 'Seed' and the coefficients \$\alpha\$, indicating their use for storage or communication. The text below the diagram, starting with 'Figure 1', describes the model's restriction to a linear combination.

<span id="page-1-0"></span>Figure 1. We restrict the deep model to be a linear combination of  $k$  randomly initialized models. Since the number of models is much less than the size of the model, it is much less expensive to communicate or store the coefficients compared to the model or data itself. We tune  $\alpha$  to minimize the loss of the task using standard backpropagation.

these methods are limited to small reduction factors, *e.g.*, less than  $30\times$ . Also, knowledge distillation methods reduce the model architecture to a smaller one with fewer layers, are moder divined<br>as while, in some methods,  $\frac{1}{2}$  while,  $\frac{2}{3}$ <br>in some methods,  $\frac{2}{3}$ which may limit the future application of that model, *e.g.*, for future fine-tuning or lifelong learning, which needs the deeper architecture.

We are interested in compacting a deep model by a considerable factor (*e.g.*,  $100\times$ ) without changing its architecture. The core idea behind our approach is simple. We constrain our model to be a linear combination of a finite set of  $t_{\text{total}}$  our moder to be a milear combination or a mile randomly initialized models, called *basis* models. Hence, the problem boils down to finding the optimal linear mixture coefficients that result in a network that can solve the task effectively. The model can then be succinctly represented by the seed (a single scalar) to generate the pseudo-random basis models and the linear mixture coefficients (See Fig-Final divided and the distribution of the section of the section of the section of the section of the section of the section of the section of the section of the section of the section of the section of the section of the tion of a deep model without changing its architecture. This ing reason: Si need to reason: Si need to the trigger is present on the trigger is present on the trigger in the trigger is present on the trigger in the trigger in the trigger in the trigger in the trigger in the trigger enables us to study the effect of increasing the size of the architecture (both depth and width) without changing the number of optimized parameters (see Figure 3).  $\inf$  images, which is similar ordering of images, which is similar to images, which is similar to images, which is similar to images, which is similar to images, which is similar to images, which is similar to image of  $\$ 

In addition to efficiency, our proposed method provides interest in applications concerning cybersecurity and prisecure communication and storage, which is of significant vacy. Briefly, our 'basis' models are generated with pseudorandom generators with a specific 'seed.' This seed could be privately shared between authenticated entities. Given the minimal self-correlation of pseudo-random sequences, a slight seed change produces a drastically different set of 'basis' models, making the publicly shared linear mixture

coefficients useless to unauthorized parties. This design choice facilitates secure communication and storage, especially in cybersecurity or privacy-sensitive applications.

J works, urging further theoretical investigations. Theoretically, overparametrization is vital in contemporary neural networks, enhancing their representational power and simplifying optimization due to an improved loss landscape [\[33,](#page-10-2) [30\]](#page-9-4). Solutions of these over-parameterized systems often form a positive-dimension manifold [\[8\]](#page-9-5), with larger systems lacking non-global minima [\[34\]](#page-10-3). Considering the abundance of good solutions, we examine if we can confine the solution search to low-dimensional subspaces dom subspaces in the weight space of overparametrized net-

 $S<sup>on</sup>$  Contributions: Below are our specific contributions:

- $\bullet$  Introducing PRANC, a simple but effective network reparameterization framework that is memory-efficient  $t_{\text{eff}}$  model training data, and  $t_{\text{eff}}$  model is  $t_{\text{eff}}$  $\frac{1}{\sqrt{2}}$  during both the learning and reconstruction phases,
- Assessing the effectiveness of PRANC in compacting image recognition models for various benchmark datasets and model architectures, showing higher ac- $\ddot{\text{c}}$  curacy with a much fewer parameters compared to extensive recent baselines,
- $\sum_{n=1}^{\infty}$  Demonstrating the effectiveness of PRANC for image compression by compacting implicit neural representations for both natural and medical images,
- Showcasing the potential of PRANC in applications n-<br>requiring encrypted communication of models (or data  $\frac{1}{2}$  variety  $\frac{1}{2}$  and  $\frac{1}{2}$  are between designed  $\frac{1}{2}$  $\sum_{n=1}^{\infty}$  represented via models).

### $\mathcal{L}$ angles. Related work

diction task to learn unsupervised features.<br>The learn unsupervised features in the learn unsupervised features. **Random networks:** Some prior works  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $[35, 31, 7, 12]$  $sk$  have shown that randomly initialized networks have a subed a network that competes with the original network in accu-m racy. Some recent papers like [\[50\]](#page-10-6) introduced an application g- for using this fact in continual learning. Instead of finding a-<br>subnetworks in a randomly generated network (i.e., mask- $\frac{1}{1}$  the combined with a contrasting  $\frac{1}{1}$  of  $\frac{1}{1}$  of  $\frac{1}{1}$ <sup>11S</sup> ing), we seek a linear combination of a small set of ranne domly generated networks, denoted as *basis* models, that  $n_e$  can solve the task.

Model compression: Model compression is not a new es topic. HashedNet [\[6\]](#page-9-3) uses weight grouping with a hash function to reduce the number of learnable parameters. It can be seen as a specific case of our method where the random models are binary with an equal number of ones and each weight of the original model is one only in one of the random models. [\[6\]](#page-9-3) experiments with MLP on small datasets. We reproduce HashedNet for our setting and show that our method outperforms it. Similar to HashedNet, Weight Fixed Network (WFN) [\[40\]](#page-10-0) compresses the model

<span id="page-2-0"></span>by minimizing the entropy and number of unique parameters in a network. WFN preserves the model's accuracy with a  $10\times$  reduction in storage size. Instead of hard-sharing the weights in HasedNet, [\[45\]](#page-10-7) uses soft sharing. Although all these methods reduce the number of parameters, they all need to keep the index of each element to reconstruct the network. Han *et al*. [\[15\]](#page-9-8) use pruning, quantization, and Huffman coding to achieve compression rates generally less than  $50\times$ . More recent approaches like MIRACLE [\[16\]](#page-9-9) and weightless [\[37\]](#page-10-8) have shown promising results with much higher compression rates (*e.g*., +400×). However, they use large architectures, *e.g*. VGG which has 150M parameters, so even after  $400 \times$  compression, there are still more than 300K parameters (more than a dense ResNet-20). We show that we can reduce the number of required parameters keeping the network architecture intact.

Model pruning and quantization: Compressing a model can be defined as reducing the number of bytes required to store a deep model. Several papers like XNOR-NET [\[36\]](#page-10-9) and EWGS [\[26\]](#page-9-2) use weight/activation (W/A) quantization for reducing the size of a network. Although W/A Quantization has proven to be an effective approach for reducing network size while maintaining accuracy, it is mainly designed for optimizing the computation for network inference. Another approach that is used for compressing a model is pruning the set of less important weights to zero, which reduces the number of floating point operations (FLOPS) and can also reduce the amount of data required to store and communicate a network. These methods include: Neuron Merging [\[20\]](#page-9-10), Dynamic pruning [\[29,](#page-9-1) [38\]](#page-10-10), ChipNet [\[43\]](#page-10-11), Pruning at initializing [\[17\]](#page-9-11), Wang et.al. [\[46\]](#page-10-12), and Collaborative Compression (CC) [\[28\]](#page-9-12). Once again, most of these methods use sparsity factors of  $20\times$  or less, which is lower than our goal in this paper. We compare our method, PRANC, with existing works that provide extreme compression rates (+99% pruning rate), e.g., DPF[\[29\]](#page-9-1), STR[\[23\]](#page-9-13), and SuRP[\[19\]](#page-9-14). Lastly, there are some prior works that decompose model filters as a linear combination of some basis filters  $[14, 2]$  $[14, 2]$  $[14, 2]$ . The goal of such methods is to reduce the computation and not necessarily the number of parameters. We focus on an extremely small number of parameters that cannot be achieved by such methods.

Data compression - core set: Another approach to recreating an accurate network is to store or communicate its training dataset and train a network in the target agent. Since most of the datasets are large, methods are proposed to synthesize metadata in the shape of images or obtain a core set of the dataset. These methods include: Dataset Distillation (DD) [\[48\]](#page-10-1), which regresses images and learning rate, Flexible Dataset Distillation (FDD) [\[4\]](#page-9-17), which regresses pseudo-labels for real images, soft labeling dataset distillation (SLDD) [\[41\]](#page-10-13), that generates pseudo-label and

images. All these methods require the seed that initializes the network. Other methods, including Dataset Condensation with distribution matching (DM) [\[52\]](#page-10-14), with differentiable Siamese augmentation (DSA) [\[51\]](#page-10-15), and Dataset distillation by matching training trajectories (DDMT) [\[5\]](#page-9-18) took a step further and devised seed-independent approaches. These methods often rely on a second-order optimization, which is computationally expensive and limits their application.Moreover, the size of data required for storage in these methods is proportional to the size of input images. We show that PRANC provides better accuracy with a much fewer regressed parameters on the same architectures compared to the mentioned approaches.

Image compression: Some popular codecs like JPEG are based on hand-crafted modules to compress an image. Another line of image compression methods is learningbased approaches. These approaches usually train an autoencoder on a large population of images [\[3,](#page-9-19) [32,](#page-10-16) [25,](#page-9-20) [9,](#page-9-21) [13\]](#page-9-22) and store the code. Our method of using INR is also learning-based but is different from the above techniques since the model is learned on a single image (to overfit) rather than on a population of images. Hence, it may not suffer from the biases of the training data. COIN  $[10]$  is probably the closest to our method, which overfits an INR and stores all the parameters. We are different since we compact the INR by reparametrizing it as a linear combination of random networks and storing the coefficients.

### 3. Method

We are interested in training a deep model with a very small number of parameters so that it is less expensive to transfer the model from one agent to another or store it on an agent with small memory. This is in contrast to the goal of most prior works (*e.g*., model compression, pruning, or quantization) that aim to reduce the inference computation or improve the generalization while preserving the accuracy. Hence, we introduce a compact representation assuming no change in the model size, number of non-zero parameters, or the precision of computation.

We assume that the deep model can be written as a linear combination of a set of randomly initialized models, called basis. Since we can use a pseudo-random generator to generate the random models, one can communicate or store all random models by simply communicating or storing a single scalar that is the seed of the pseudo-random generator. Although basis models are not necessarily orthogonal to each other, their pairwise dot product is close to zero since the number of samples (models) is much smaller than the dimensionality of each model. Then we optimize the weights of each base model so that their linear combination can solve the task (e.g., image classification).

More formally, given a set of training images  $\{x_i\}_{i=1}^N$ and their corresponding labels  $\{y_i\}_{i=1}^N$ , we want to train a

Image /page/3/Figure/0 description: The image is a contour plot titled "Depiction of Loss Landscape." It shows a complex loss landscape with multiple local minima, indicated by blue regions and labeled "Local Minimum." A black arrow originates from the center and points towards the upper right quadrant, labeled "Random Basis Network, \(\hat{\theta}\_1\)." A yellow arrow extends from the end of the black arrow further into the upper right quadrant, labeled "\(\alpha\_1 \hat{\theta}\_1\)." The plot uses a color gradient from blue (low loss) to red (high loss) to represent the loss values across the landscape. Dashed lines indicate axes, and a dotted diagonal line runs through the center.

<span id="page-3-0"></span>Figure 2. A simple illustration of the loss landscape of a model with two parameters and one basis model. None of the two local minima may be in the span of the basis models, so we search for  $\alpha$  to find a local minimum in the span of the basis models.

deep model  $f(.;\theta)$  with parameters  $\theta \in \mathbb{R}^d$  so that  $f(x_i;\theta)$ predicts  $y_i$ . The standard practice is to optimize  $\theta$  by minimizing the empirical risk:

$$
R(\theta) = \frac{1}{N} \sum_{i=1}^{N} L(f(x_i; \theta), y_i)
$$

where  $L(\cdot, \cdot)$  is a discrepancy-measure, e.g., crossentropy. In communicating such a model, we need to send a high-dimensional vector  $\theta$  that contains d scalars.

To reduce the cost of communication, we assume a set of randomly initialized basis models with parameters  $\{\hat{\theta}_j\}_{j=1}^k$ . These k basis models are generated using a known seed and are frozen throughout the learning process. Then we define:  $\theta := \sum_{j=1}^{k} \alpha_j \hat{\theta}_j$ , where  $\alpha_j$  is a scalar weight for the j'th basis model. Assuming that  $k \ll d$ , it will be much less expensive to communicate or store  $\alpha$  instead of  $\theta$ .

To optimize  $\alpha$ , one may first optimize for  $\theta$  to find  $\theta^*$ and then regress it by minimizing:

$$
\arg\min_{\alpha} ||\theta^* - \sum_{j=1}^k \alpha_j \hat{\theta}_j||^2
$$

However, since  $k \ll d$ , the optimum solution  $\theta^*$  may be far from the span of the basis models, resulting in an inferior solution (also shown empirically in our experiments). We argue that there are an infinite number of solutions for  $\theta$  that are as good as  $\theta^*$ , so we may search for one with a smaller residual error when projected to the span of the basis models. Hence, we search for a solution that minimizes the task loss in the basis models' span by optimizing:

$$
\arg\min_{\alpha} \sum_{i} L\Big(f(x_i; \sum_{j=1}^{k} \alpha_j \hat{\theta}_j), y_i\Big) \tag{1}
$$

Note that at the test time, after reconstructing the model by linear combination, the inference for PRANC is exactly the same as the standard dense model.

Optimization efficiency: Note that the optimization is very simple and efficient since  $\frac{dL}{d\alpha} = \frac{dL}{d\theta} \frac{d\theta}{d\alpha}$  and  $\frac{d\theta}{d\alpha_j} = \hat{\theta}_j$ . Hence, we use standard backpropagation to calculate  $\frac{dL}{d\theta}$ and then multiply that with the basis models' matrix to get:

$$
\frac{dL}{d\alpha} = \frac{dL}{d\theta} \times \hat{\theta}
$$

Memory efficiency in training: Note that the matrix of basis models  $\hat{\theta}$  is very large, so keeping that in the memory is not efficient. Hence, we divide this matrix into multiple smaller chunks, and at each iteration, we generate each chunk using a pseudo-random generator at the GPU itself, perform the multiplication, discard the chunk, and go to the next chunk. This method reduces the memory footprint by a large factor at the cost of generating the whole random basis once per iteration, which is very efficient in modern GPUs. Choosing chunks of 100 alpha values for ResNet18 consumes almost 4.4GB (i.e.,  $11M \times 4 \times 100$ ) of GPU memory which is reasonable.

Model reconstruction efficiency: Since basis models are generated using a pseudo-random generator, we can reconstruct the model using a simple running average of the basis models: generate each entry in  $\hat{\theta}_i$ , multiply it with  $\alpha_i$ , add it to the running average, discard the entry and go to the next entry of  $\theta_i$ . This way, the memory footprint of the reconstruction becomes negligible (i.e.,  $d + 1$ ).

On-demand model reconstruction: In some applications, the agent may need to run the inference rarely but does not have enough memory to hold the model. The device can store  $\alpha$ , reconstruct each convolutional filter using the corresponding entries of the basis models, apply it to the input, and then discard the filter and go to the next filter. This process has a very small memory footprint as it needs to store  $\alpha$  and just one filter at a time.

Distributed learning: In order to train the model on multiple GPUs, we use a simple distributed learning algorithm to increase  $m$ , the number of basis models. We divide  $m$  basis models between  $q$  GPUs so that each GPU works on  $m/g$  basis models only. Then, we distribute  $\alpha$  among GPUs. Each GPU calculates the partial weighted average over its basis models and distributes it to all GPUs. Then, all GPUs will have access to the complete weighted average and will use it to do backpropagation in standard distributed learning form and update their own set of  $\alpha$ .

BatchNorm layer: We minimize the loss of the task by tuning the  $\alpha$  instead of the model weights as done in standard learning. However, the parameters of the BatchNorm layer are not tuned by PRANC. For the simplicity of this work, we assume that we can communicate those parameters and include them in the budget. This makes sense since the number of BatchNorm parameters is relatively small compared to the number of weight parameters.

### 4. Application

We test our framework on two different applications.

Image classification networks: In this setting, we parameterize an image recognition model, e.g., ResNet-20 for CIFAR-10, by our PRANC framework and optimize  $\alpha s$  instead of the model weights. This results in a compact model that can be stored and communicated very efficiently.

<span id="page-4-2"></span>Image compression using implicit neural networks: We also test our framework on compressing an implicit neural network (INR) that is over-fitted to a single image  $[42]$ . Such an INR inputs the coordinate of the pixel and returns the color value. Hence, one can store or communicate the INR model instead of the original image. We parameterize a standard INR model [\[42\]](#page-10-17) using the PRANC framework so that we optimize the  $\alpha s$  instead of the weights of the INR model. Our method outperforms JPEG compression on two standard datasets and two evaluation metrics.

## 5. Experiments on image classification

We report extensive results of PRANC on various datasets, architectures, and number of basis models.

#### 5.1. Comparison with model pruning methods:

For communicating a sparse model with a sparsity rate of more than  $2\times$ , it is required to transmit two numbers per parameter: the value of weight and its index in the network. Therefore, even if a model pruning method uses a pruning factor of 99% (100 $\times$  reduction in size) since it should transmit the indices alongside the values, the actual reduction size will be smaller than  $100 \times$ . DPF [\[29\]](#page-9-1), STR[\[23\]](#page-9-13), LAMP $[27]$ , RiGL $[11]$ , and SuRP $[19]$  are the SOTA methods that use a large sparsity  $(+50\times)$  and maintain a reasonable accuracy. We used their code on CIFAR-10 and CIFAR-100 along with ResNet-20 and ResNet-56 architectures and compared them with our method in Table [1.](#page-4-0) PRANC achieves consistently higher accuracy with fewer parameters. Please note that all these methods excluded BatchNorm layers from their pruning process. Therefore we also excluded them from the parameter count. For ResNet-20, the number of BatchNorm parameters is 2,752 and for ResNet-56 it is 8,128.

#### 5.2. Comparison with model distillation methods:

One of the critical baselines for our work is model distillation. However, the number of parameters we use is very small compared to any existing CNN architecture. Even LeNet[\[24\]](#page-9-26) (one of the smallest CNN architectures), has more than 60,000 parameters. To compare PRANC with model distillation, we trained a ResNet18 on CIFAR-10 and distilled its knowledge to a LeNet model. On the other hand, we compressed a ResNet20 model using PRANC with 10,000  $\alpha$ s and a ResNet56 model with merely 5,000  $\alpha$ s and compared their accuracies. As shown in Table [2,](#page-4-1) PRANC-compressed architectures require almost  $5\times$  fewer parameters while achieving higher accuracies with a significant gap (81.48% vs. 74.1%).

#### 5.3. Comparison with dataset distillation methods:

In Table [3,](#page-5-0) we report the accuracy and the number of parameters for PRANC in comparison with various dataset

<span id="page-4-0"></span>Table 1. Comparison of our model with SOTA pruning methods, DPF [\[29\]](#page-9-1), STR[\[23\]](#page-9-13), LAMP[\[27\]](#page-9-24), RiGL[\[11\]](#page-9-25), and SuRP[\[19\]](#page-9-14). "Pr." denotes the pruning rate. Also, when the network is pruned, we have to keep two numbers for each weight: the weight itself and its position in the model. Note that we excluded the number of BatchNorm parameters in this table since that is constant for all the models. This number is 2,752 for Resnet-20 and 8,128 for ResNet-56.

| <b>Method</b>            | Data        | Arch.      | # Params exc.<br><b>BatchNorm</b> | Accuracy     |
|--------------------------|-------------|------------|-----------------------------------|--------------|
| <b>Baseline (Pr. 0%)</b> | C10         | R20        | 269,722                           | <b>88.92</b> |
| <b>DPF(Pr. 98.2%)</b>    | C10         | R20        | $4.920	imes2$                     | <b>41.86</b> |
| <b>RiGL(Pr. 99.62%)</b>  | C10         | R20        | $1026 	imes 2$                    | <b>50.9</b>  |
| <b>LAMP(Pr. 99.62%)</b>  | C10         | R20        | $1026 	imes 2$                    | <b>51.24</b> |
| <b>SuRP (Pr. 99.62%)</b> | C10         | R20        | $1026	imes2$                      | <b>54.22</b> |
| <b>STR (Pr. 95.5%)</b>   | C10         | R20        | $12,238	imes 2$                   | <b>75.99</b> |
| <b>Ours</b>              | <b>C10</b>  | <b>R20</b> | <b>1,000</b>                      | <b>64.59</b> |
| <b>Ours</b>              | <b>C10</b>  | <b>R20</b> | <b>10,000</b>                     | <b>81.48</b> |
| <b>Baseline (Pr. 0%)</b> | C10         | R56        | 853,018                           | <b>91.64</b> |
| <b>DPF (Pr. 98.43%)</b>  | C10         | R56        | $13,414 	imes 2$                  | <b>47.66</b> |
| <b>SuRP (Pr. 98.73%)</b> | C10         | R56        | $10,834 	imes 2$                  | <b>66.65</b> |
| <b>STR (Pr. 98.4%)</b>   | C10         | R56        | $13,312	imes 2$                   | <b>67.77</b> |
| <b>Ours</b>              | <b>C10</b>  | <b>R56</b> | <b>5,000</b>                      | <b>76.87</b> |
| <b>Baseline (Pr. 0%)</b> | C100        | R20        | 275,572                           | <b>60.84</b> |
| <b>DPF (Pr. 96.13%)</b>  | C100        | R20        | $10,770	imes 2$                   | <b>12.25</b> |
| <b>SuRP (Pr. 97.48%)</b> | C100        | R20        | $6,797	imes2$                     | <b>14.46</b> |
| <b>STR (Pr. 96.12%)</b>  | C100        | R20        | $10,673 	imes 2$                  | <b>13.18</b> |
| <b>Ours</b>              | <b>C100</b> | <b>R20</b> | <b>5,000</b>                      | <b>32.33</b> |
| <b>Baseline (Pr. 0%)</b> | C100        | R56        | 858,868                           | <b>64.32</b> |
| <b>DPF (Pr. 97.8%)</b>   | C100        | R56        | $19,264 	imes 2$                  | <b>19.11</b> |
| <b>SuRP (Pr. 98.72%)</b> | C100        | R56        | $10,919 	imes 2$                  | <b>14.59</b> |
| <b>STR (Pr. 97.8%)</b>   | C100        | R56        | $18,881	imes2$                    | <b>25.98</b> |
| <b>Ours</b>              | <b>C100</b> | <b>R56</b> | <b>5,000</b>                      | <b>32.97</b> |

<span id="page-4-1"></span>Table 2. Comparison with model distillation. PRANC outperforms a LeNet distilled from ResNet-18 on CIFAR-10. 2,752 and 8,128 are the number of BatchNorm parameters that we exclude from the coefficients but need to consider them as parameters.

| Method             | Arch.      | # Params           | Acc.          |
|--------------------|------------|--------------------|---------------|
| Distilled from R18 | LeNet      | 62,006             | 74.1%         |
| Ours               | <b>R56</b> | $5,000 + (8,128)$  | <b>76.87%</b> |
| Ours               | <b>R20</b> | $10,000 + (2,752)$ | <b>81.48%</b> |

distillation methods. Most of these methods are based on meta-learning approaches that involve a high computational cost and memory footprint at the training time, so they are limited in the depth of the model. Moreover, they need to do a few gradient descent steps in constructing the model. Nonetheless, the number of required parameters in dataset distillation methods is proportional to the size of the input image. For instance, in distilling CIFAR-10 to 10 images only, we need to store at least  $10 \times 32 \times 32 \times 3$  parameters. In order to be comparable with the SOTA Dataset Distillation methods, we use AlexNet (which is a modified version that is described in [\[48\]](#page-10-1)) on CIFAR-10. For CIFAR-100 and tinyImageNet, we use depth-3 and depth-4 128-width ConvNet architectures described in [\[5\]](#page-9-18), respectively. Note that some dataset distillation methods do not <span id="page-5-2"></span>require a seed, so they solve a more challenging task since the distilled data should be able to tune any randomly initialized model. However, since we are focusing on reducing the cost of communication and storage, using a fixed seed as the central part of our idea is not prohibitive.

<span id="page-5-0"></span>Table 3. Comparison with dataset distillation methods on various datasets and architectures. 3-128-Conv and 4-128-Conv represents 3-depth 128-width ConvNet and 4-depth 128-width ConvNet, respectively. "Trained model" is the upper bound of our method since one can optimize all weights and transmit/store them. Our method outperforms the baselines with a large margin and a much fewer parameters.

| Method           | Task   | Arch       | # Params  | Acc.  |
|------------------|--------|------------|-----------|-------|
| Trained model    | C10    | AlexNet    | 1,756,426 | 84.8  |
| <b>FDD</b> [4]   | C10    | AlexNet    | 397,000   | 43.2  |
| SLDD [41]        | C10    | AlexNet    | 308,200   | 60.0  |
| <b>DD</b> [48]   | C10    | AlexNet    | 307,200   | 54.0  |
| <b>DM</b> [52]   | C10    | AlexNet    | 30,720    | 26.0  |
| <b>DSA</b> [51]  | C10    | AlexNet    | 30,720    | 28.8  |
| DC [53]          | C10    | AlexNet    | 30,720    | 28.3  |
| <b>CAFE</b> [47] | C10    | AlexNet    | 30,720    | 30.3  |
| CAFE+DSA [47]    | C10    | AlexNet    | 30,720    | 31.6  |
| <b>DDMT</b> [5]  | C10    | AlexNet    | 30,720    | 46.3  |
| <b>Ours</b>      | C10    | AlexNet    | 17,000    | 76.69 |
| Trained model    | C100   | 3-128-Conv | 504,420   | 56.2  |
| <b>FDD</b> [4]   | C100   | 3-128-Conv | 317,200   | 11.5  |
| <b>DM</b> [52]   | C100   | 3-128-Conv | 307,200   | 11.4  |
| <b>DSA</b> [51]  | C100   | 3-128-Conv | 307,200   | 13.9  |
| DC [53]          | C100   | 3-128-Conv | 307,200   | 12.8  |
| CAFE+DSA [47]    | C100   | 3-128-Conv | 307,200   | 14.0  |
| <b>DDMT</b> [5]  | C100   | 3-128-Conv | 307,200   | 24.3  |
| <b>Ours</b>      | C100   | 3-128-Conv | 15,000    | 25.57 |
| Trained model    | tinyIN | 4-128-Conv | 857,160   | 37.6  |
| <b>DM</b> [52]   | tinyIN | 4-128-Conv | 2,457,600 | 3.9   |
| <b>DDMT</b> [5]  | tinyIN | 4-128-Conv | 2,457,600 | 8.8   |
| <b>Ours</b>      | tinyIN | 4-128-Conv | 15,000    | 12.02 |

### 5.4. Large-Scale dataset and models:

So far, we have provided evidence that our method can outperform recent works in dataset distillation, model pruning, and model compression in terms of the number of parameters vs. accuracy. Since our method is reasonably efficient in learning, particularly compared to meta-learning approaches that depend on second-order derivatives of the network, we can evaluate it on larger-scale models. We evaluate our method on ImageNet100 with ResNet-18 architecture. Table [4](#page-5-1) shows the results. Our method achieves 61.08% accuracy with less than 1% parameters, while the standard ResNet-18 model achieves 82.1% accuracy with more than 11M parameters. Since ResNet-18 is a huge model, we use one of the unique capabilities of PRANC, which is creating the network on the fly. We only used a single NVIDIA 3090 GPU and trained our model for 200 epochs, using Adam optimizer and step scheduler with  $\gamma = 0.5$  for every 50 epochs and an initial learning rate of 0.001. Also, we distributed our budget of  $\alpha$  vector across the layers, *i.e.,* we used 4,000 coefficients for each layer of the convolutional encoder and 20,000 coefficients for our classifier (giving us a total of 100,000 coefficients).

<span id="page-5-1"></span>Table 4. Result of our method on ImageNet-100 dataset and ResNet-18. 19,200 is the total number of parameters of all Batch-Norm layers in our model

| Method        | # Params             | Acc.     |
|---------------|----------------------|----------|
| trained model | 11,227,812           | $82.1%$  |
| HashedNet [6] | $110,000 + (19,200)$ | 52.96%   |
| <b>Ours</b>   | $100,000 + (19,200)$ | $61.08%$ |
| <b>Ours</b>   | $200,000 + (19,200)$ | $67.28%$ |

#### 5.5. Ablation study:

In PRANC,  $k$ , the number of basis models, is a hyperparameter. One question is: "How will  $k$  affect the performance?" Moreover, it is arguable that "why do we try to find a linear combination that is accurate on the task? why not try to regress an entire already trained network?" Also, "Can k be more important than the architecture? *i.e.,* can we use large  $k$  with a small network and still get high accuracy?" In this section, we answer these questions.

Sensitivity to seed: Since one of the applications of PRANC is in federated learning, it is worth discussing its pseudo-encryption ability. We experimented with changing the seed at the reconstruction time. On CIFAR-10 with AlexNet, with a minor change in seed, the accuracy of the reconstructed model dropped from 74.0% to 9.4%, which is close to chance. This is expected as the new basis models are not correlated with the ones that are used in training. This fact means that the seed can act as a mutual key between the agents and even if  $\alpha$  is intercepted, reconstructing the model is nearly impossible. Therefore, in federated learning applications which deal with safe communication of deep learning models, PRANC can be used as both compression and encryption method.

Regressing  $\theta^*$  directly: We can first train a model to get  $\theta^*$  and then optimize for  $\alpha$  by regressing that solution using MSE loss in the parameter space. As shown in Fig. [2,](#page-3-0) this may not succeed since the optimal model may not be in the span of the basis models, and also the MSE loss in the parameter space is not necessarily correlated with the task loss. Table [5](#page-6-1) shows that the accuracy of this baseline using 10,000 parameters is close to chance.

Effect of varying  $k$  vs. architecture: We perform an ablation study to understand the effect of the number of basis models, k vs. the architecture of the model. One can argue that sometimes it is better to design a better architecture rather than increasing the number of  $\alpha$ s. Here, we change k

| tinyIN denotes tiny ImageNet datasets |              |                  |               |
|---------------------------------------|--------------|------------------|---------------|
| <b>Dataset</b>                        | Architecture | <b>Full Acc.</b> | Regress. Acc. |
| C10                                   | AlexNet      | 84.8 %           | 10.0%         |
| C10                                   | LeNet [24]   | 73.5%            | 12.74%        |
| C100                                  | 3-128-Conv   | 56.2 %           | 1.14%         |
| C100                                  | AlexNet      | 50.7%            | 1.0%          |
| tinyIN                                | 4-128-Conv   | 37.6 %           | 0.5%          |

<span id="page-6-2"></span><span id="page-6-1"></span>Table 5. Results of regressing a pretrained model using 10,000 basis models. C10 and C100 denote CIFAR-10 and CIFAR-100 and tinyIN denotes tiny ImageNet datasets

from 1,000 to 20,000 for LeNet, AlexNet, ResNet-20, and ResNe-56 on CIFAR-10 and plot the accuracy in Figure [3.](#page-6-0) All experiments have been done in the same setup with 400 epochs with Adam optimizer. As expected, the accuracy increases as we increase the number of basis functions. However, as we can see, the architecture has more effect on the accuracy compared to k.

Image /page/6/Figure/3 description: The image is a line graph showing the accuracy of four different neural network models (ResNet-20, ResNet-56, AlexNet, and LeNet) as a function of a parameter 'k'. The x-axis represents 'k' with values 1000, 2000, 5000, 10000, and 20000. The y-axis represents 'Accuracy' ranging from 50 to 85. ResNet-56 consistently shows the highest accuracy, starting at approximately 66% for k=1000 and reaching about 84% for k=20000. ResNet-20 follows, with accuracy starting at around 62% and ending at approximately 83%. AlexNet's accuracy begins at about 50% and increases to roughly 78%. LeNet has the lowest accuracy, starting at approximately 52% and reaching about 69%.

<span id="page-6-0"></span>Figure 3. Illustration of impact of  $k$  in accuracy of different architectures on CIFAR-10. The accuracy improves by increasing the number of basis models. However, the architecture plays more critical role in the accuracy compared to the number of basis.

### 6. Experiments on image compression

As another application of PRANC, here we show that we can compress an image by compacting the implicit neural network (INR) that is overfitted to the image. The INR model inputs the coordinates of a pixel and outputs the color of that pixel. We will store or communicate the seed of the pseudo-random generator and  $\alpha$  values only. We use [\[42\]](#page-10-17) as our INR model. The INR is a 4-layer MLP (3 hidden layers) model with 512 neurons at each layer and a Layer-Norm [\[1\]](#page-9-27) after each layer. We encode the pixel coordinate in the input to 512 dimensions using Fourier mapping and use three neurons in the output for RGB images (the color values) and one neuron for X-ray images.

We use a half-precision floating point for  $\alpha s$  to further reduce the storage cost. We use a different set of  $\alpha$ s for each layer of the network.

To reduce the memory footprint, we divide the base network matrix  $\hat{\theta}$  into smaller chunks and generate and then discard each chunk at the GPU for each iteration of the optimization. Similar to stochastic gradient descent, we randomly sample a subset of pixels to be optimized at each iteration, leading to faster convergence due to more frequent parameter updates.

We evaluate our model on three different datasets: Kodak dataset  $\lceil 22 \rceil$  that contains 24 non-compressed images of size  $512 \times 768$ , **CLIC-mobile** test set [\[44\]](#page-10-20) that contains 178 high-resolution images (e.g.,  $1512 \times 2016$ ), and 64 ran-domly sampled images from NIH Chest X-ray dataset [\[49\]](#page-10-21) that consists of 100, 000 de-identified chest X-rays images of size  $1024 \times 1024$ .

Baselines. We compare our method with the following hand-crafted image codecs: JEPG, JPEG2000, and WebP. Our goal is to show that PRANC is a general framework that performs well when simply applied to INR compression out of the box. Hence, we do not compare it with more advanced codecs like BPG and VTM since they are highly engineered and include components like entropy coding. Those results are presented in the supplementary material. We also compare with learning-based image compression methods in the supplementary (BMS, MBT, and CST). Note that, unlike PRANC, these methods require a large dataset of images to pre-train their auto-encoder. This limits the applications and also may degrade the results for out-ofdistribution data points. For instance, unlike PRANC, models that are trained on a training set may not be suitable for medical data since they may not be truthful to abnormalities specific to patients not represented in the training data.

COIN  $[10]$  is probably the closest to our method which trains an INR using SIREN [\[39\]](#page-10-22) and stores all the parameters. Since COIN does not use Fourier mapping, for a fair comparison, we produce a similar baseline, called 'trained INR,' using our MLP network described above without the PRANC framework. For the trained INR, we reduce the width of the network to match the compression rate of our method and use half-precision floating points.

Results. We evaluate our model with two metrics: PSNR and MS-SSIM. Note that we fix the network architecture and vary the number of  $\alpha$  values to get different bit-perpixel (bpp) values for each image. We report the results for the Kodak dataset in Figure [13](#page-14-0) and CLIC-mobile dataset on Table [11.](#page-14-1) Our method outperforms JPEG and INR. We report the results of the chest X-ray in Table [7.](#page-7-0) An example image is shown in Figure [6](#page-8-0) for the Kodac dataset and in Figure [23](#page-23-0) for the X-ray dataset.

Ablation. Since we reconstruct the model weights with  $\alpha$  values, one can vary the size of the architecture without changing the number of parameters ( $\alpha$  values), hence, in PRANC, we can increase both width and depth without changing the bit-rate. We keep the number of parameters  $k = 102K$  and vary both the network width and depth of the MLP model. Results on the Kodak dataset are shown in Table [8.](#page-7-1) Interestingly, we can improve the performance by increasing the model depth while keeping the number of

<span id="page-7-2"></span>Image /page/7/Figure/0 description: The image contains two plots. The top plot is titled "Kodak - bit-rate vs. PSNR" and shows the relationship between bit-rate (in bpp) on the x-axis and PSNR (in dB) on the y-axis. The bottom plot is titled "Kodak - bit-rate vs. MS-SSIM" and shows the relationship between bit-rate (in bpp) on the x-axis and MS-SSIM (in dB) on the y-axis. Both plots display data for several compression methods: Trained INR (green dots), COIN (blue dots), JPEG (red dots), JPEG2000 (teal dots), WebP (yellow dots), and PRANC (ours) (pink stars). In the top plot, the PSNR values range from approximately 22 dB to 36 dB, and the bit-rate values range from 0.0 to 1.0 bpp. In the bottom plot, the MS-SSIM values range from approximately 6 dB to 18 dB, and the bit-rate values range from 0.0 to 1.0 bpp. The bottom of the image has the text "Figure 4. Kodak Dataset Image Compression: Our method ext" which is cut off.

Figure 4. Kodak Dataset Image Compression: Our method outperforms JPEG and 'trained INR' on both PSNR and MS-SSIM evaluations at various bitrates. Note that, unlike the other baselines, our method is learned on a single image and is not handcrafted, except for the architecture of the INR model, which is a simple MLP.

learnable parameters constant.

Sorting  $\alpha$  values. In Figure [14,](#page-15-0) we show reconstructed images with a subset of  $\alpha$  values with the largest absolute values. Since we have a different set of  $\alpha$ s for each layer of MLP in image compression, we sort absolute values and select the top  $p\%$  of each layer with higher values. We vary  $p$  and visualize the reconstructed images for each  $p$  value. In the supplementary material, for the image classification setting, we show that in reconstructing the ResNet model using a partial set of  $\alpha$  values, choosing larger  $|\alpha|$  values leads to much better accuracy compared to choosing a random subset.

**Implementation Details.** For each image, we train  $\alpha$ values with 10k iterations on Kodak and 5k iterations on CLIC-mobile dataset. Each iteration processes 25% of the pixels of the image sampled randomly. Note that increasing the number of iterations cannot damage the model since the goal is to overfit to the image and generalization is not an issue. We use PyTorch Adam [\[21\]](#page-9-29) optimizer with an initial learning rate of 1e−3 and a Cosine scheduler. More details about the number of  $\alpha$  values per layer for each bpp are in the supplementary material.

## 7. Future directions

PRANC can enable multiple future directions: Generative models for memory-replay: Our method can

Table 6. CLIC-mobile Dataset Image Compression: PRANC outperforms JPEG and JPEG2000 with smaller bpp on this dataset.

| Model        | bpp   | PSNR  | MS-SSIM |
|--------------|-------|-------|---------|
| WebP         | 0.185 | 30.07 | 0.940   |
| JPEG2000     | 0.126 | 29.40 | 0.918   |
| JPEG         | 0.195 | 24.82 | 0.836   |
| Trained INR  | 0.125 | 26.93 | 0.864   |
| PRANC (ours) | 0.119 | 29.71 | 0.920   |

<span id="page-7-0"></span>Table 7. Chest X-ray Dataset Image compression: We compare PRANC with JPEG for X-ray images. Our method is better than JPEG with a lower bpp. Since unlike auto-encoders, PRANC does not use any population-based training, it may be better suited for medical images since it may preserve out-of-distribution artifacts which are important for diagnosis purposes. However, we leave studying this for future work.

| Model          | <b>PRANC</b> | <b>JPEG</b> |
|----------------|--------------|-------------|
| bpp            | 0.152        | 0.168       |
| <b>PSNR</b>    | 36.28        | 34.25       |
| <b>MS-SSIM</b> | 0.972        | 0.921       |

<span id="page-7-1"></span>Table 8. Effect of increasing width/depth of the model: We can increase both the depth and width of the MLP model without changing the number of parameters. When changing the depth, we redistribute the number of  $\alpha$  values uniformly among layers to keep the total number constant. More details are in the Supp.

| Width   | 128   | 256   | 512   | 1024  |
|---------|-------|-------|-------|-------|
| PSNR    | 30.00 | 32.05 | 31.5  | 31.32 |
| MS-SSIM | 0.937 | 0.963 | 0.959 | 0.961 |
| Depth   | 3     | 4     | 5     |       |
| PSNR    | 30.78 | 31.5  | 32.38 |       |
| MS-SSIM | 0.978 | 0.959 | 0.965 |       |

be used to compact a generative model (e.g., GANs or diffusion models), where the  $\alpha$  parameters may be stored in the agent or sent to another agent. Then, any agent can reconstruct the model in the future and draw samples from it that are similar to the samples that were used earlier to train the model. This enables memory replay in lifelong learning in a single agent with limited memory or in multiple agents with limited communication.

Progressive compactness: In this method, we assumed a set of basis models with no specific ordering. However, one can optimize  $\alpha$  so that the earlier indices of  $\alpha$  can reconstruct an acceptable model. Then, depending on the communication or storage budget, the target agent can decide on how many  $\alpha$  parameters it needs by trading off between accuracy and compactness. We showed that sorting  $\alpha$  values is a first step in this direction, but one may learn them in decreasing importance order as a future work.

 $p$ =100%, PSNR:24.75  $p$ =90%, PSNR:24.47  $p$ =80%, PSNR:23.25  $p$ =70%, PSNR:21.51  $p$ =50%, PSNR:17.91  $p$ =30%, PSNR:14.40 Original Image

Image /page/8/Picture/1 description: A series of images show a stone colonnade with arches and a wet, reflective floor. The images progress from a clear, detailed view to increasingly pixelated and abstract representations of the same scene. The final image is a highly pixelated close-up of the colonnade's arches.

Figure 5. Effect of keeping  $p\%$  of basis models with the highest absolute  $\alpha$  values. We get reasonable images with a smaller subset of basis models. One can reconstruct an approximate image upon receiving a partial set of  $\alpha$  values, similar to progressive JPEG.

Image /page/8/Picture/3 description: The image displays an original picture of a young child with face paint. Two close-up inset images are shown to the left of the main picture. The top inset shows a close-up of the child's eye, which has yellow face paint around it. The bottom inset shows a close-up of the child's shoulder and neck area, revealing a colorful, patterned garment and some dark hair. The main image shows the child's face with yellow and blue face paint, resembling sun rays around the eye and on the forehead. The child is wearing a red and green garment with a patterned design. Yellow boxes highlight the child's eye and a portion of their shoulder in the main image, corresponding to the inset images.

Image /page/8/Picture/4 description: The image displays a comparison of image compression techniques applied to a portrait of a young child. The child has face paint, including a yellow circle around one eye and colorful markings on their cheeks. The image is divided into four quadrants, each showing a different result of the compression. The top left quadrant shows the original image with a close-up of the child's eye and a section of their clothing. It is labeled "Ours (bpp=0.31, PSNR=30.36)". The top right quadrant shows a similar view but with a lower PSNR value, indicating more compression artifacts. It is labeled "Ours (bpp=0.17, PSNR=27.53)". The bottom left quadrant appears to be a higher quality reconstruction, similar to the top left. The bottom right quadrant shows a significantly degraded image with blocky artifacts and reduced color depth, suggesting a much higher compression ratio.

Image /page/8/Picture/5 description: This image displays a comparison of image compression techniques applied to a portrait of a young child with face paint. The image is divided into four quadrants, each showing a different compression result. The top left quadrant is labeled "Ours (bpp=0.31, PSNR=30.36)" and shows a high-quality result. The top right quadrant is labeled "Ours (bpp=0.17, PSNR=27.53)" and shows a slightly lower quality result. The bottom left quadrant is labeled "JPEG (bpp=0.31, PSNR=28.85)" and shows a result with noticeable compression artifacts. The bottom right quadrant is labeled "JPEG (bpp=0.17, PSNR=21.86)" and shows a significantly degraded image with blocky artifacts and color banding. Each quadrant also includes zoomed-in inset views of the child's eye and clothing to highlight the differences in detail and quality.

Figure 6. Kodak visualization. We compare PRANC with JPEG on image 15 of the Kodak dataset. See Supp. for more examples.

<span id="page-8-0"></span>

### 8. Conclusion

We introduced a simple yet effective method that can learn a model as a linear combination of a set of frozen randomly initialized models. The final model can be compactly stored or communicated using the seed of the pseudorandom generator and the coefficients. Moreover, our method has a small memory footprint at the learning or reconstruction stages. We perform extensive experiments on multiple image classification datasets with multiple architectures and also on image compression settings and show that our method achieves better accuracy with fewer parameters compared to SOTA baselines. We believe many applications including lifelong learning and distributed learning can benefit from our ideas. Hence, we hope this paper opens the door to studying more advanced compacting methods based on frozen random networks.

Limitations: As discussed, some model parameters, e.g., BatchNorm layers, cannot be easily reparameterized

Image /page/8/Picture/10 description: This is a medical image showing a chest X-ray with several highlighted areas. The main image displays a frontal view of a chest X-ray, with two yellow boxes drawn around specific regions. The left side of the main image has a yellow box highlighting the upper chest and shoulder area, showing what appears to be surgical hardware or implants near the clavicle and scapula, along with several small, linear objects clustered together. Another yellow box is placed on the right side of the main image, encompassing the lung field. To the left of the main image, there are two smaller, unhighlighted X-ray views. The top-left view is a close-up of the shoulder and upper chest area, similar to the region highlighted in the main image, showing the same surgical hardware and clustered objects. The bottom-left view is a lateral or oblique view of the chest, focusing on the lung and rib cage, with some visible lung markings.

Image /page/8/Picture/11 description: The image displays a comparison between an original chest X-ray and a processed version, likely a JPEG compression. Both images are presented side-by-side, with the original on the left and the processed version on the right. Each main X-ray image has two smaller, zoomed-in sections below it, highlighting specific areas of the chest. The top left zoomed-in section of the original image shows the shoulder and upper chest area with some metallic implants visible. The top right zoomed-in section of the original image focuses on the lung area. The processed image on the right exhibits a loss of detail and contrast compared to the original, particularly in the zoomed-in sections. The bottom text indicates that the original image has a PSNR of 35.70, and the processed image is labeled as JPEG with the same PSNR value, suggesting a comparison of compression artifacts.

Ours (bpp=0.15, PSNR=35.52)

JPEG (bpp=0.15, PSNR=33.03)

Figure 7. Chest X-ray visualization. We compare PRANC and JPEG on a Chest X-ray image. See Supp. for more examples.

using our method since they are calculated directly from data rather than minimizing the task loss. In this paper, we assumed we communicate them with no change and included them in our budget. Lastly, PRANC is computationally expensive for a large number of basis, so is currently not suitable for training very large models.

Acknowledgement: This work was partially supported by the Defense Advanced Research Projects Agency (DARPA) under Contract No. HR00112190135 and HR00112090023, the United States Air Force under Contract No. FA8750-19-C-0098, and funding from NSF grants 1845216 and 1920079. Any opinions, findings, conclusions, or recommendations expressed in this paper are those of the authors and do not necessarily reflect the views of the United States Air Force, DARPA, or NSF.

## References

- <span id="page-9-27"></span>[1] Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. *arXiv preprint arXiv:1607.06450*, 2016. [7](#page-6-2)
- <span id="page-9-16"></span>[2] Hessam Bagherinezhad, Mohammad Rastegari, and Ali Farhadi. Lcnn: Lookup-based convolutional neural network. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 7120–7129, 2017. [3](#page-2-0)
- <span id="page-9-19"></span>[3] Johannes Ballé, David Minnen, Saurabh Singh, Sung Jin Hwang, and Nick Johnston. Variational image compression with a scale hyperprior. In *International Conference on Learning Representations*, 2018. [3](#page-2-0)
- <span id="page-9-17"></span>[4] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [3,](#page-2-0) [6](#page-5-2)
- <span id="page-9-18"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. *arXiv preprint arXiv:2203.11932*, 2022. [3,](#page-2-0) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-9-3"></span>[6] Wenlin Chen, James Wilson, Stephen Tyree, Kilian Weinberger, and Yixin Chen. Compressing neural networks with the hashing trick. In *International conference on machine learning*, pages 2285–2294. PMLR, 2015. [1,](#page-0-0) [2,](#page-1-1) [6](#page-5-2)
- <span id="page-9-6"></span>[7] Xiaohan Chen, Jason Zhang, and Zhangyang Wang. Peek-aboo: What (more) is disguised in a randomly weighted neural network, and how to find it efficiently. In *International Conference on Learning Representations*, 2021. [2](#page-1-1)
- <span id="page-9-5"></span>[8] Yaim Cooper. Global minima of overparameterized neural networks. *SIAM Journal on Mathematics of Data Science*, 3(2):676–691, 2021. [2](#page-1-1)
- <span id="page-9-21"></span>[9] JCMSA Djelouah and Christopher Schroers. Content adaptive optimization for neural image compression. In *Proceedings of the CVPR*, 2019. [3](#page-2-0)
- <span id="page-9-23"></span>[10] Emilien Dupont, Adam Goliński, Milad Alizadeh, Yee Whye Teh, and Arnaud Doucet. Coin: Compression with implicit neural representations. *arXiv preprint arXiv:2103.03123*, 2021. [3,](#page-2-0) [7](#page-6-2)
- <span id="page-9-25"></span>[11] Utku Evci, Trevor Gale, Jacob Menick, Pablo Samuel Castro, and Erich Elsen. Rigging the lottery: Making all tickets winners. In *International Conference on Machine Learning*, pages 2943–2952. PMLR, 2020. [5](#page-4-2)
- <span id="page-9-7"></span>[12] Claudio Gallicchio and Simone Scardapane. Deep randomized neural networks. *Recent Trends in Learning From Data*, pages 43–68, 2020. [2](#page-1-1)
- <span id="page-9-22"></span>[13] Tiansheng Guo, Jing Wang, Ze Cui, Yihui Feng, Yunying Ge, and Bo Bai. Variable rate image compression with content adaptive optimization. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, pages 122–123, 2020. [3](#page-2-0)
- <span id="page-9-15"></span>[14] Kai Han, Yunhe Wang, Qi Tian, Jianyuan Guo, Chunjing Xu, and Chang Xu. Ghostnet: More features from cheap operations. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 1580– 1589, 2020. [3](#page-2-0)
- <span id="page-9-8"></span>[15] Song Han, Huizi Mao, and William J Dally. Deep compression: Compressing deep neural networks with pruning,

trained quantization and huffman coding. *arXiv preprint arXiv:1510.00149*, 2015. [3](#page-2-0)

- <span id="page-9-9"></span>[16] Marton Havasi, Robert Peharz, and José Miguel Hernández-Lobato. Minimal random code learning: Getting bits back from compressed model parameters. *arXiv preprint arXiv:1810.00440*, 2018. [3](#page-2-0)
- <span id="page-9-11"></span>[17] Soufiane Hayou, Jean-Francois Ton, Arnaud Doucet, and Yee Whye Teh. Robust pruning at initialization. *arXiv preprint arXiv:2002.08797*, 2020. [3](#page-2-0)
- <span id="page-9-0"></span>[18] Geoffrey Hinton, Oriol Vinyals, Jeff Dean, et al. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2(7), 2015. [1](#page-0-0)
- <span id="page-9-14"></span>[19] Berivan Isik, Tsachy Weissman, and Albert No. An information-theoretic justification for model pruning. In *International Conference on Artificial Intelligence and Statistics*, pages 3821–3846. PMLR, 2022. [3,](#page-2-0) [5](#page-4-2)
- <span id="page-9-10"></span>[20] Woojeong Kim, Suhyun Kim, Mincheol Park, and Geunseok Jeon. Neuron merging: Compensating for pruned neurons. *Advances in Neural Information Processing Systems*, 33:585–595, 2020. [3](#page-2-0)
- <span id="page-9-29"></span>[21] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014. [8](#page-7-2)
- <span id="page-9-28"></span>[22] Kodak. Kodak Dataset. http://r0k.us/graphics/kodak/, 1991. [7](#page-6-2)
- <span id="page-9-13"></span>[23] Aditya Kusupati, Vivek Ramanujan, Raghav Somani, Mitchell Wortsman, Prateek Jain, Sham Kakade, and Ali Farhadi. Soft threshold weight reparameterization for learnable sparsity. In *Proceedings of the International Conference on Machine Learning*, July 2020. [3,](#page-2-0) [5](#page-4-2)
- <span id="page-9-26"></span>[24] Yann LeCun et al. Lenet-5, convolutional neural networks. *URL: http://yann. lecun. com/exdb/lenet*, 20(5):14, 2015. [5,](#page-4-2) [7](#page-6-2)
- <span id="page-9-20"></span>[25] Jooyoung Lee, Seunghyun Cho, and Seung-Kwon Beack. Context-adaptive entropy model for end-to-end optimized image compression. *arXiv preprint arXiv:1809.10452*, 2018. [3](#page-2-0)
- <span id="page-9-2"></span>[26] Junghyup Lee, Dohyung Kim, and Bumsub Ham. Network quantization with element-wise gradient scaling. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 6448–6457, 2021. [1,](#page-0-0) [3](#page-2-0)
- <span id="page-9-24"></span>[27] Jaeho Lee, Sejun Park, Sangwoo Mo, Sungsoo Ahn, and Jinwoo Shin. Layer-adaptive sparsity for the magnitude-based pruning. *arXiv preprint arXiv:2010.07611*, 2020. [5](#page-4-2)
- <span id="page-9-12"></span>[28] Yuchao Li, Shaohui Lin, Jianzhuang Liu, Qixiang Ye, Mengdi Wang, Fei Chao, Fan Yang, Jincheng Ma, Qi Tian, and Rongrong Ji. Towards compact cnns via collaborative compression. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 6438– 6447, 2021. [3](#page-2-0)
- <span id="page-9-1"></span>[29] Tao Lin, Sebastian U Stich, Luis Barba, Daniil Dmitriev, and Martin Jaggi. Dynamic model pruning with feedback. *arXiv preprint arXiv:2006.07253*, 2020. [1,](#page-0-0) [3,](#page-2-0) [5](#page-4-2)
- <span id="page-9-4"></span>[30] Chaoyue Liu, Libin Zhu, and Mikhail Belkin. Loss landscapes and optimization in over-parameterized non-linear systems and neural networks. *Applied and Computational Harmonic Analysis*, 59:85–116, 2022. [2](#page-1-1)

- <span id="page-10-5"></span>[31] Eran Malach, Gilad Yehudai, Shai Shalev-Schwartz, and Ohad Shamir. Proving the lottery ticket hypothesis: Pruning is all you need. In *International Conference on Machine Learning*, pages 6682–6691. PMLR, 2020. [2](#page-1-1)
- <span id="page-10-16"></span>[32] David Minnen, Johannes Ballé, and George D Toderici. Joint autoregressive and hierarchical priors for learned image compression. *Advances in neural information processing systems*, 31, 2018. [3](#page-2-0)
- <span id="page-10-2"></span>[33] Behnam Neyshabur, Zhiyuan Li, Srinadh Bhojanapalli, Yann LeCun, and Nathan Srebro. The role of over-parametrization in generalization of neural networks. In *International Conference on Learning Representations*, 2019. [2](#page-1-1)
- <span id="page-10-3"></span>[34] Ouynh Nguyen, Mahesh Chandra Mukkamala, and Matthias Hein. On the loss landscape of a class of deep neural networks with no bad local valleys. In *International Conference on Learning Representations*, 2019. [2](#page-1-1)
- <span id="page-10-4"></span>[35] Vivek Ramanujan, Mitchell Wortsman, Aniruddha Kembhavi, Ali Farhadi, and Mohammad Rastegari. What's hidden in a randomly weighted neural network? In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11893–11902, 2020. [2](#page-1-1)
- <span id="page-10-9"></span>[36] Mohammad Rastegari, Vicente Ordonez, Joseph Redmon, and Ali Farhadi. Xnor-net: Imagenet classification using binary convolutional neural networks. In *European conference on computer vision*, pages 525–542. Springer, 2016. [3](#page-2-0)
- <span id="page-10-8"></span>[37] Brandon Reagan, Udit Gupta, Bob Adolf, Michael Mitzenmacher, Alexander Rush, Gu-Yeon Wei, and David Brooks. Weightless: Lossy weight encoding for deep neural network compression. In *International Conference on Machine Learning*, pages 4324–4333. PMLR, 2018. [3](#page-2-0)
- <span id="page-10-10"></span>[38] Julien Niklas Siems, Aaron Klein, Cedric Archambeau, and Maren Mahsereci. Dynamic pruning of a neural network via gradient signal-to-noise ratio. In *8th ICML Workshop on Automated Machine Learning (AutoML)*, 2021. [3](#page-2-0)
- <span id="page-10-22"></span>[39] Vincent Sitzmann, Julien Martel, Alexander Bergman, David Lindell, and Gordon Wetzstein. Implicit neural representations with periodic activation functions. *Advances in Neural Information Processing Systems*, 33:7462–7473, 2020. [7](#page-6-2)
- <span id="page-10-0"></span>[40] Christopher Subia-Waud and Srinandan Dasmahapatra. Weight fixing networks. In *European Conference on Computer Vision*, pages 415–431. Springer, 2022. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-10-13"></span>[41] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021. [3,](#page-2-0) [6](#page-5-2)
- <span id="page-10-17"></span>[42] Matthew Tancik, Pratul Srinivasan, Ben Mildenhall, Sara Fridovich-Keil, Nithin Raghavan, Utkarsh Singhal, Ravi Ramamoorthi, Jonathan Barron, and Ren Ng. Fourier features let networks learn high frequency functions in low dimensional domains. *Advances in Neural Information Processing Systems*, 33:7537–7547, 2020. [5,](#page-4-2) [7](#page-6-2)
- <span id="page-10-11"></span>[43] Rishabh Tiwari, Udbhav Bamba, Arnav Chavan, and Deepak K Gupta. Chipnet: Budget-aware pruning with heaviside continuous approximations. *arXiv preprint arXiv:2102.07156*, 2021. [3](#page-2-0)
- <span id="page-10-20"></span>[44] George Toderici, Wenzhe Shi, Radu Timofte, Lucas Theis, Johannes Balle, Eirikur Agustsson, Nick Johnston, and

Fabian Mentzer. Workshop and challenge on learned image compression (clic2020), 2020. [7](#page-6-2)

- <span id="page-10-7"></span>[45] Karen Ullrich, Edward Meeds, and Max Welling. Soft weight-sharing for neural network compression. *arXiv preprint arXiv:1702.04008*, 2017. [3](#page-2-0)
- <span id="page-10-12"></span>[46] Huan Wang, Can Qin, Yulun Zhang, and Yun Fu. Neural pruning via growing regularization. *arXiv preprint arXiv:2012.09243*, 2020. [3](#page-2-0)
- <span id="page-10-19"></span>[47] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *arXiv preprint arXiv:2203.01531*, 2022. [6](#page-5-2)
- <span id="page-10-1"></span>[48] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [3,](#page-2-0) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-10-21"></span>[49] Xiaosong Wang, Yifan Peng, Le Lu, Zhiyong Lu, Mohammadhadi Bagheri, and Ronald M Summers. Chestxray8: Hospital-scale chest x-ray database and benchmarks on weakly-supervised classification and localization of common thorax diseases. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2097–2106, 2017. [7](#page-6-2)
- <span id="page-10-6"></span>[50] Mitchell Wortsman, Vivek Ramanujan, Rosanne Liu, Aniruddha Kembhavi, Mohammad Rastegari, Jason Yosinski, and Ali Farhadi. Supermasks in superposition. *Advances in Neural Information Processing Systems*, 33:15173–15184, 2020. [2](#page-1-1)
- <span id="page-10-15"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [3,](#page-2-0) [6](#page-5-2)
- <span id="page-10-14"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [3,](#page-2-0) [6](#page-5-2)
- <span id="page-10-18"></span>[53] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [6](#page-5-2)

### Appendix

### Orthogonality and norm of basis networks:

In the main submission, we mentioned that random basis networks are almost perpendicular to each other in high dimensional spaces. To show this, we generate 1000 random vectors at the  $d$  dimensional space (varying  $d$  from 10 to 1000), and plot the histogram of their  $\ell_2$  norm and pairwise cosine similarities in Figures [8](#page-11-0) and [9](#page-11-1) respectively. We also run this experiment 100 times, calculate the maximum cosine similarity for each run, and plot the histogram of maximum values in Figure [10.](#page-12-0) As expected, at higher number of dimensions, the cosine similarity gets closer to 0 and the norm gets closer to 1. This empirically suggests that at higher dimensions, the random basis are close to orthonormal basis. Please note that our method does not require orthonormal basis to work.

Image /page/11/Figure/0 description: This figure displays six histograms, each representing data for a different dimension (10-D, 50-D, 100-D, 200-D, 500-D, and 1000-D). The histograms are arranged in two rows and three columns. All histograms show a distribution of values centered around 1.0, with the spread of the distribution decreasing as the dimension increases. The y-axis represents frequency, and the x-axis represents the values, ranging from 0.00 to 2.00 in all plots. The 10-D histogram has the widest spread, while the 1000-D histogram is the narrowest, indicating a more concentrated distribution.

<span id="page-11-0"></span>Figure 8. Histogram of  $\ell_2$  norm of 1000 randomly generated d dimensional vectors. When increasing d the norm approaches 1.

Image /page/11/Figure/2 description: The image displays a grid of six histograms, each representing the distribution of pairwise cosine similarity for randomly generated d-dimensional vectors. The histograms are arranged in two rows and three columns, with the dimensions indicated above each plot: 10-D, 50-D, 100-D in the top row, and 200-D, 500-D, 1000-D in the bottom row. The x-axis for all histograms ranges from -1.00 to 1.00, representing the cosine similarity values. The y-axis represents the frequency or count of occurrences. As the dimensionality (d) increases from 10 to 1000, the histograms become narrower and taller, concentrating around a cosine similarity of 0.00. The 10-D histogram is the widest and shortest, resembling a bell curve. The subsequent histograms progressively narrow, with the 1000-D histogram being the narrowest and tallest, indicating a tighter distribution of cosine similarities around zero as the dimensionality increases.

<span id="page-11-1"></span>Figure 9. Histogram of pairwise cosine similarity of 1000 randomly generated  $d$  dimensional vectors. When increasing  $d$  the cosine similarity approaches 0.

### Reconstruction using a subset of basis:

Figure [12](#page-13-0) shows the distribution of alpha values for a few images from Kodak dataset. As expected, the alpha values vary across the basis models. This motivated us to study "what if we use only a subset of basis models instead of all of them?".

For image classification, we do this experiment by selecting random  $p\%$  of the basis with varying p. We repeat this 4 times. As another selection method, we sort alpha

values based on their absolute values and use the top  $p\%$  of them. As shown in Figure  $11$ , in the random selection, we need most of the basis to be able to retrieve a reasonable accuracy while for the sorted case, a small percentage of the basis models is sufficient to get a reasonable accuracy. This is an interesting observation that somehow loosely suggests that the loss landscape of the optimization for alpha values is reasonably smooth. This is intentionally a vague statement since it needs further investigation as future work.

For image compression, we perform the same experi-

Image /page/12/Figure/0 description: The image displays a grid of six histograms, each representing the distribution of cosine similarity for randomly generated d-dimensional vectors over 100 trials. The histograms are arranged in two rows and three columns, with the dimensions indicated above each plot: 10-D, 50-D, 100-D in the top row, and 200-D, 500-D, 1000-D in the bottom row. The x-axis for all histograms ranges from 0.0 to 1.0, representing cosine similarity values. The y-axis represents the frequency or count. The 10-D histogram shows a sharp peak near 1.0, indicating high cosine similarity. The 50-D and 100-D histograms show peaks centered around 0.6 and 0.45 respectively, with a spread of values. The 200-D, 500-D, and 1000-D histograms show peaks centered at progressively lower values, around 0.35, 0.2, and 0.15 respectively, with a narrower spread as the dimension increases. The caption below the histograms reads: "Figure 10. Histograms of maximum of pairwise cosine similarity of 1000 randomly generated d-dimensional vectors over 100 trials."

<span id="page-12-0"></span>Figure 10. Histogram of maximum of pairwise cosine similarity of 1000 randomly generated  $d$  dimensional vectors over 100 trials. When increasing d, the maximum of cosine similarity approaches 0.

ment and show reasonable reconstructed images with a subset of alpha values with the largest absolute values. Since we have a different set of alphas for each layer of MLP in image compression, we sort absolute values and select top  $p\%$  of each layer with higher values. We vary p and visualize the reconstructed images for each  $p$  value in Figure [14,](#page-15-0) [15](#page-15-1) and [16.](#page-16-0) Additionally, we report the effect of  $p$  in PSNR and MS-SSIM for Kodak dataset in Table [9.](#page-13-2) Similar to our observation in mage classification, we observe that images with  $p = 70\%$  has acceptable quality.

Aside from better understanding the method, this observation can enable communicating a deep model or an image progressively where the sender sends a subset of alpha values (most important ones) first and then gradually sends the rest to improve the model accuracy or the image quality. For image compression, this method is somehow similar to the application of Progressive JPEG where it is hand-crafted to send the low frequency components first. Please note that to use this in practice, this progressive version of our method has some extra-overhead since we also need to communicate which alpha values are sent at each step (e.g., using a bit for each basis). Studying this in more detail is left for future work.

### Details of Image Compression:

As described in the main submission, in image compression experiments, in order to change the bit-per-pixel (bpp), we fix the network architecture and vary the number of  $\alpha$ values per layer. We report the number of  $\alpha$  values per layer for each bpp in Table [10.](#page-13-3)

### Compression to Advanced Image Compression methods:

We compare our method with more advanced codecs (e.g., BPG, VTM) and learned-based image compression methods. Results for CLIC-Mobile are in Table [11](#page-14-1) and the results for Kodak are in Figure [13.](#page-14-0) Note that advanced codecs are heavily hand-crafted by a large community. And, learned-based methods utilize a training dataset to learn a good code (similar to auto-encoder), hence, they may not be able to compress a single image without having access to a corpus of images. In contrast, our method can compress a single image without using a population of images. Moreover, for the same reason, our method cannot be biased towards the popular cases (head of distribution). Obviously, our method has other biases (e.g., what can be represented with INR) that needs to be studied as the future work.

### Visual Comparison to JPEG:

Similar to Figure 5 of main submission, we include more visual comparison to JPEG in Figures [17](#page-17-0) through [20](#page-20-0) (for Kodak dataset with  $512 \times 768$  resolution) and Figures [21](#page-21-0) and [22](#page-22-0) (for CLIC-Mobile dataset with  $1512 \times 2016$  or  $2016 \times 1512$  resolution). Moreover, in Figures [23](#page-23-0) through [25,](#page-25-0) we include visual comparison in chest x-ray dataset with  $1024 \times 1024$  resolution.

Image /page/13/Figure/0 description: The image contains two line graphs side-by-side, both plotting Accuracy (%) against Percentage of alpha (%). The left graph is titled "ResNet 20" and the right graph is titled "ResNet 56". Both graphs display five lines representing different subsets: "Sorted subsets" (blue), "Rand. Subset v1" (orange), "Rand. Subset v2" (green), "Rand. Subset v3" (red), and "Rand. Subset v4" (purple). In both graphs, the "Sorted subsets" line starts at approximately 10% accuracy at 0% alpha and steadily increases to over 80% accuracy at 100% alpha. The other four "Rand. Subset" lines remain relatively flat at around 10% accuracy until approximately 80% alpha, after which they sharply increase, reaching around 80-85% accuracy at 100% alpha. The "Rand. Subset v1" and "Rand. Subset v3" lines are very close to each other in the latter half of the graph, as are "Rand. Subset v2" and "Rand. Subset v4".

<span id="page-13-1"></span>Figure 11. Effect of using only  $p\%$  of basis models selected randomly (4 times) or selected based on highest absolute values of alphas.

Image /page/13/Figure/2 description: The image displays four photographs in the top row and four corresponding histograms in the bottom row. The top row features images of colorful hats on a wall, a sailboat on the water, a river flowing through a mountainous landscape, and a vintage airplane. Each photograph is accompanied by text indicating its mean, standard deviation, and PSNR value. The bottom row consists of histograms that visually represent the distribution of alpha values for each of the images above. The histograms show a bell-shaped distribution, with the majority of values clustered around zero. The x-axis of the histograms ranges from -0.100 to 0.100, and the y-axis represents frequency counts, reaching up to 6000 or 7000 in some cases. The caption below the histograms reads "Figure 12. Distribution of alphas: We plot the distribution of alpha values for a few Kodak images."

Figure 12. Distribution of alphas: We plot the distribution of alpha values for a few Kodak images.

| Table 9. Effect of keeping $p%$ of $\alpha$ with highest absolute value: |       |      |      |       |       |       |       |       |       |       |
|--------------------------------------------------------------------------|-------|------|------|-------|-------|-------|-------|-------|-------|-------|
| percentile $(p%)$                                                        | 10    | 20   | 30   | 40    | 50    | 60    | 70    | 80    | 90    | 100   |
| bpp                                                                      | 0.07  | 0.14 | 0.22 | 0.29  | 0.36  | 0.43  | 0.50  | 0.58  | 0.65  | 0.72  |
| <b>PSNR</b>                                                              | 11.94 | 12.3 | 13.4 | 14.98 | 17.01 | 19.51 | 22.71 | 26.92 | 31.85 | 33.64 |
| MS-SSIM                                                                  | 0.10  | 0.12 | 0.18 | 0.28  | 0.41  | 0.55  | 0.71  | 0.86  | 0.96  | 0.97  |

<span id="page-13-2"></span><span id="page-13-0"></span>

<span id="page-13-3"></span>Table 10. Details of image compression models: We report the number of  $\alpha$  values per layer for each bpp. We use MLP with hidden dimension of 256 for Kodak dataset at bpp of 0.18 (first row), and hidden dimension of 512 for all other experiments. All settings use Fourier mapping of size 512. We use fewer number of alpha values for the last layer since the last layer has fewer number of weights as it goes from hidden layer to only 3 dimensions (RGB values). Also, for the first row, we use more number of alpha values for first layer since it has more number of weights ( $512 \times 256$ ).

| Dataset     | bpp  | Layer-1 | Layer-2 | Layer-3 | Layer-4 | Layer-5 | Total $(\alpha s)$ |
|-------------|------|---------|---------|---------|---------|---------|--------------------|
| Kodak       | 0.18 | 10k     | 7.5k    | 7.5k    | 7.5k    | 2k      | 34.5k              |
|             | 0.31 | 15k     | 15k     | 15k     | 15k     | 2k      | 57k                |
|             | 0.52 | 10k     | 30k     | 30k     | 30k     | 2k      | 102k               |
|             | 0.72 | 20k     | 40k     | 40k     | 40k     | 2k      | 142k               |
| CLIC-Mobile | 0.12 | 45k     | 45k     | 45k     | 45k     | 2k      | 182k               |
| Chest x-ray | 0.15 | 20k     | 20k     | 20k     | 20k     | 2k      | 82k                |

<span id="page-14-1"></span>Table 11. CLIC-mobile Dataset Image Compression: Similar to Table 6 of the main submission, we include comparison to advanced codecs like BPG and VTM. We also compare with some learning-based image compression methods (e.g., MBT, CST, BMS).

| Model        | bpp   | PSNR  | MS-SSIM |
|--------------|-------|-------|---------|
| VTM          | 0.183 | 33.07 | 0.964   |
| CST          | 0.146 | 31.85 | 0.957   |
| MBT          | 0.146 | 31.62 | 0.955   |
| BPG          | 0.128 | 30.65 | 0.942   |
| WebP         | 0.185 | 30.07 | 0.940   |
| BMS          | 0.113 | 29.38 | 0.936   |
| JPEG2000     | 0.126 | 29.40 | 0.918   |
| JPEG         | 0.195 | 24.82 | 0.836   |
| Trained INR  | 0.125 | 26.93 | 0.864   |
| PRANC (ours) | 0.119 | 29.71 | 0.920   |

Image /page/14/Figure/2 description: The image contains two plots comparing different image compression methods on the Kodak dataset. The top plot shows bit-rate versus PSNR (Peak Signal-to-Noise Ratio) in dB, with the x-axis representing bit-rate in bpp (bits per pixel) from 0.0 to 1.0 and the y-axis representing PSNR in dB from 22 to 36. The bottom plot shows bit-rate versus MS-SSIM (Multi-Scale Structural Similarity) in dB, with the x-axis representing bit-rate in bpp from 0.0 to 1.0 and the y-axis representing MS-SSIM in dB from 6 to 18. Both plots include multiple lines representing different compression methods: Trained INR, BPG, COIN, BMS, CST, MBT, JPEG, JPEG2000, VTM, WebP, and PRANC (ours). The PRANC (ours) method is highlighted with pink stars on both plots. The plots indicate that PRANC (ours) generally achieves better performance (higher PSNR and MS-SSIM) at lower bit-rates compared to other methods.

<span id="page-14-0"></span>Figure 13. Kodak Dataset Image Compression: Similar to Figure 4 of the main submission, we include comparison to advanced codecs like BPG and VTM. We also compared with learned-based image compression (e.g., MBT, CST, BMS).

Image /page/15/Picture/0 description: The image displays a grid of eight images, each featuring a European-style church with a tall spire and surrounding buildings under a cloudy sky. The top row shows the original image labeled "Original Image," followed by three processed versions with labels "P:1.0, PSNR:29.67," "P:0.9, PSNR:29.34," and "P:0.8, PSNR:27.83." The bottom row presents four more processed images labeled "P:0.7, PSNR:25.43," "P:0.5, PSNR:20.49," "P:0.3, PSNR:15.77," and "P:0.1, PSNR:12.66." As the 'P' value decreases, the images become progressively more pixelated and distorted, with the lowest 'P' value resulting in a highly noisy and abstract representation.

Figure 14. Effect of keeping  $p\%$  of basis models with the highest absolute alpha values. We get reasonable images with smaller subset of basis models.

<span id="page-15-1"></span><span id="page-15-0"></span>Image /page/15/Picture/2 description: The image displays a grid of eight images, each showing a sign with the text "CVPR" and "Workshop and Challenge on Learned Image Compression". The top row shows the original image and three compressed versions with PSNR values of 35.01, 33.91, and 30.03, corresponding to compression parameters P=1.0, P=0.9, and P=0.8 respectively. The bottom row shows compressed versions with PSNR values of 26.06, 19.90, 14.68, and 11.39, corresponding to compression parameters P=0.7, P=0.5, P=0.3, and P=0.1 respectively. As the compression parameter decreases, the image quality degrades, with the lowest parameter (P=0.1) resulting in a highly noisy and unrecognizable image.

Figure 15. See Figure [14.](#page-15-0)

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image displays a grid of eight photographs, each featuring two glasses of colorful drinks on a wooden table with a cityscape and sky in the background. The top row shows the original image and three progressively degraded versions labeled with 'P' values and PSNR scores: P:1.0, PSNR:29.97; P:0.9, PSNR:29.60; and P:0.8, PSNR:27.91. The bottom row presents further degraded versions: P:0.7, PSNR:25.28; P:0.5, PSNR:19.69; P:0.3, PSNR:14.12; and P:0.1, PSNR:10.91. The degradation is evident as increasing noise and loss of detail in the images from left to right, particularly in the bottom row.

Figure 16. See Figure [14.](#page-15-0)

Image /page/17/Picture/0 description: A woman wearing a red hat and scarf is shown. The image is composed of three parts: a close-up of the woman's eye on the top left, a close-up of the red scarf on the bottom left, and a full portrait of the woman on the right. The woman has blonde hair and is looking towards the viewer. The portrait on the right has two yellow squares highlighting her eye and the side of her hat.

Image /page/17/Picture/2 description: A woman wearing a red hat and scarf smiles at the viewer. The image is a close-up of her face, with two yellow squares highlighting her eye and a section of her hat. The background is a pink, draped fabric. The image appears to be a processed or enhanced version of a photograph, possibly for quality assessment, as indicated by the text below the image which is partially visible and includes numbers like "0.91" and "30.92".

Ours (bpp=0.31, PSNR=30.62)

Image /page/17/Picture/4 description: The image displays a woman wearing a red hat and scarf. The main image is accompanied by two smaller, zoomed-in images on the left. The top left image shows a close-up of the woman's eye, revealing pixelation. The bottom left image shows a close-up of the red scarf, also showing significant pixelation and artifacting. The main image has two yellow squares highlighting areas of interest: one over the woman's eye and another over her scarf. The background of the main image appears to be a pink, textured fabric. Text below the images indicates "JPEG (1/, 0.01, PSNR: 26.62)".

JPEG (bpp=0.31, PSNR=28.68)

Image /page/17/Picture/6 description: The image displays a comparison between two versions of a portrait of a woman wearing a red hat and scarf. The left side shows a higher-resolution image with more detail, including a close-up of the woman's eye and a section of her scarf. The right side shows a lower-resolution, pixelated version of the same portrait, also with close-ups of the eye and scarf. Both sides have yellow squares highlighting specific areas of the face and clothing. The overall impression is a demonstration of image quality degradation or compression.

<span id="page-17-0"></span>Ours (bpp=0.17, PSNR=28.18) JPEG (bpp=0.17, PSNR=22.47) Figure 17. Kodak visualization. We compare PRANC and JPEG on image 4 of Kodak dataset at bpp=0.31 and 0.17

Image /page/18/Picture/0 description: A statue of a person wearing a golden laurel wreath and holding a golden orb is shown. The statue is made of white marble and is draped in flowing robes. The statue's face and hands are detailed, with visible texture and lines. The image is composed of three parts: a close-up of the statue's face, a close-up of its hands, and a full view of the statue with yellow boxes highlighting the face and hands.

Image /page/18/Picture/2 description: This image displays a comparison between two image compression methods, labeled "Ours" and "JPEG". The "Ours" method, with a bitrate of 0.31 bpp and a PSNR of 31.96, is shown on the left. The "JPEG" method, with a bitrate of 0.32 bpp and a PSNR of 28.58, is shown on the right. Both images depict a statue with a golden wreath on its head, holding a golden sphere. Close-up views of the statue's face and hands are provided for both methods, highlighting the differences in detail and clarity. The "Ours" method appears to preserve more detail and exhibit fewer compression artifacts compared to the "JPEG" method.

JPEG (bpp=0.32, PSNR=28.58)

Image /page/18/Picture/5 description: The image displays a comparison of image processing results, likely for denoising or super-resolution. On the left, two close-up crops of a statue are shown, with the full statue in the center, marked with yellow squares highlighting specific areas. The statue appears to be made of stone, with a golden laurel wreath on its head and holding a golden orb. The crops on the left show a textured surface and a hand. On the right, the same statue and crops are presented, but with a pixelated or blocky appearance, and the highlighted areas are overlaid with heatmaps or color gradients, suggesting areas of interest or analysis. The bottom of the image contains text that is partially visible, reading "Ours (bpp: 0.17, PSNR: 30.03)".

Ours (bpp=0.17, PSNR=29.23) JPEG (bpp=0.17, PSNR=23.21) Figure 18. Kodak visualization. We compare PRANC and JPEG on image 17 of Kodak dataset.

Image /page/19/Picture/0 description: A group of motocross racers are lined up at the starting gate, ready to begin a race. They are all wearing colorful helmets and racing suits, and their motorcycles are lined up in a row. The image is taken from a low angle, looking up at the racers. The background is a dirt track with some trees in the distance.

Original image

Image /page/19/Picture/2 description: The image displays two side-by-side photographs of a motocross race start. The left photograph is grainy and appears to be a lower quality capture, while the right photograph is clearer and more vibrant. Both images show a line of motocross riders on their bikes, poised at the starting line. The riders are wearing colorful helmets and racing gear. The bikes are also brightly colored, with prominent front forks and wheels. The background in both images consists of dirt and possibly some greenery, suggesting an outdoor racing environment. The right image, being clearer, allows for better visibility of the details on the bikes and riders, including numbers on some helmets and logos on the bikes.

Ours (bpp=0.31, PSNR=25.57)

JPEG (bpp=0.31, PSNR=21.34)

Image /page/19/Figure/5 description: The image shows a side-by-side comparison of two motocross races. The left image is a full-color, slightly grainy photograph of a group of motocross racers lined up at the start of a race. They are wearing colorful racing gear and helmets, and their bikes are positioned ready to go. The right image is a pixelated, grayscale version of the same scene, with the colors and details significantly reduced. Both images capture the intensity and anticipation of the start of a motocross race.

Ours (bpp=0.17, PSNR=23.3) JPEG (bpp=0.21, PSNR=19.42) Figure 19. Kodak visualization. We compare PRANC and JPEG on image 5 of Kodak dataset.

Image /page/20/Picture/0 description: A close-up shot shows two macaws, one red and one blue and yellow, perched side-by-side against a blurred green background. The red macaw on the right is facing slightly away from the camera, while the blue and yellow macaw on the left is looking to the left with its beak slightly open.

Image /page/20/Picture/2 description: Two parrots are shown side-by-side. The parrot on the left is a blue and yellow macaw, and the parrot on the right is a red macaw. Both parrots are facing to the right, and their heads are turned slightly towards the viewer. The background is blurred and consists of green foliage and white bokeh lights.

Ours (bpp=0.31, PSNR=31.41)

Image /page/20/Picture/4 description: The image contains the text "JPEG (bpp=0.32, PSNR=31.16)".

<span id="page-20-0"></span>Image /page/20/Picture/5 description: Two parrots are shown side-by-side. The parrot on the left is a blue and yellow macaw, and the parrot on the right is a red macaw. The parrots are facing to the right, and their heads are turned slightly towards the viewer. The background is blurred and green, suggesting foliage. The image on the right is a pixelated version of the image on the left.

Ours (bpp=0.17, PSNR=27.88) JPEG (bpp=0.17, PSNR=23.64) Figure 20. Kodak visualization. We compare PRANC and JPEG on image 23 of Kodak dataset.

Image /page/21/Picture/0 description: A close-up shot of a latte with intricate foam art sits on a dark wooden table next to two croissants. The latte is in a white mug, and the foam art resembles a leaf. In the background, a potted plant with lush green leaves adds a touch of nature to the scene. The overall image conveys a cozy and inviting atmosphere, perfect for a coffee break.

Image /page/21/Picture/2 description: A close-up shot of a coffee with latte art and two croissants on a dark wooden table. The coffee is in a white mug with a saucer and a spoon. The croissants are golden brown and flaky. The background is blurred, showing a cafe setting with plants and tables.

Image /page/21/Picture/3 description: A close-up shot of a coffee cup with latte art and two croissants on a white napkin, placed on a dark wooden table. The background shows a blurred cafe interior with shelves of products and a potted plant. The image is presented with two smaller, pixelated crops below, highlighting details of the table surface and the coffee cup.

<span id="page-21-0"></span>Ours (bpp=0.119, PSNR=30.26) JPEG (bpp=0.18, PSNR=25.43) Figure 21. CLIC visualization. We compare PRANC at bpp=0.119 with JPEG at bpp=0.18 on a CLIC image.

Image /page/22/Picture/0 description: A collection of espresso cups and saucers are arranged on a granite countertop. There are three red saucers with white espresso cups, two black espresso cups with white saucers, and one red espresso cup with a white saucer. The cups are of various sizes and colors, and they are arranged in a scattered pattern. The countertop is made of granite and has a speckled pattern. The image also includes a stack of white plates and a sink in the background.

Image /page/22/Picture/2 description: A collection of espresso cups and saucers are arranged on a granite countertop. There are four cups visible: two black cups on red saucers and two white cups on red saucers. One of the white cups is highlighted with a yellow square. Another yellow square highlights a section of the countertop with reflections. A stack of white plates is also visible in the background.

Ours (bpp=0.119, PSNR=28.99)

Image /page/22/Picture/4 description: A collection of espresso cups and saucers are arranged on a granite countertop. There are three red saucers with white cups, and two black saucers with white cups. One of the red saucers with a white cup is highlighted with a yellow square. Another yellow square highlights a section of the granite countertop with a reflection on it.

JPEG (bpp=0.226, PSNR=24.59)

<span id="page-22-0"></span>Figure 22. CLIC visualization. We compare PRANC at bpp=0.119 with JPEG at bpp=0.226 on a CLIC image.

Image /page/23/Picture/0 description: This is an X-ray image of a patient's chest, showing a pacemaker implanted in the upper chest area. The image is presented with three views: a close-up of the left shoulder and upper chest, a close-up of the pacemaker and surrounding ribs, and a wider view of the entire chest with the pacemaker and leads clearly visible. The wider view has two yellow boxes highlighting the area around the left clavicle and the area where the pacemaker is located.

Original image

Image /page/23/Picture/2 description: This is a chest X-ray with two yellow boxes highlighting specific areas. The top left box focuses on the clavicle and shoulder area, showing what appears to be medical tubing or wires. The top right box highlights the right side of the chest, showing a pacemaker device implanted under the skin. The bottom row contains two zoomed-in views of these highlighted areas, with the left image showing the shoulder and clavicle region and the right image showing the pacemaker.

<span id="page-23-0"></span>Image /page/23/Picture/3 description: This is a chest X-ray with two yellow boxes highlighting areas of interest. The top left box focuses on the clavicle and shoulder area, showing what appears to be a medical device implanted near the collarbone. The top right box highlights the left side of the chest, showing a pacemaker or defibrillator device with leads extending into the chest cavity. The bottom portion of the image contains two smaller, magnified views of these highlighted areas, allowing for a closer examination of the medical devices and their placement.

Ours (bpp=0.15, PSNR=36.06) JPEG (bpp=0.15, PSNR=32.11) Figure 23. Chest X-ray visualization. We compare PRANC and JPEG on a Chest X-ray image at bpp=0.15

Image /page/24/Picture/0 description: This is a medical image showing a chest X-ray with three views. The main view is an anterior-posterior (AP) chest X-ray, with two smaller inset views. The top left inset shows a close-up of the shoulder joint, including the clavicle, scapula, and humerus. The bottom left inset shows a magnified view of the lung field, highlighting the lung parenchyma and bronchi. The main AP chest X-ray is annotated with two yellow boxes. One box highlights the right shoulder and clavicle area, similar to the top left inset. The other box highlights a region in the lower left lung field, showing lung tissue and possibly some vascular markings.

Image /page/24/Picture/2 description: This image displays a comparison between two methods of processing chest X-rays, labeled "Ours" and "JPEG". The top row shows two full chest X-ray images, each with two yellow boxes highlighting specific areas. The left X-ray, labeled "Ours (bpp=0.15, PSNR=36.24)", appears sharper and more detailed. The right X-ray, labeled "JPEG (bpp=0.15, PSNR=32.55)", shows a noticeable degradation in quality, with blocky artifacts and reduced detail, particularly in the highlighted areas. The bottom row provides close-up views of the areas marked by the yellow boxes in the top row. The bottom-left images correspond to the "Ours" method, showing clear bone structures and lung tissue. The bottom-right images correspond to the "JPEG" method, exhibiting pixelation and loss of fine details in the same regions.

Figure 24. See Figure [23.](#page-23-0)

Image /page/25/Picture/0 description: This is a medical X-ray image displaying a chest and shoulder area. The main image shows a frontal view of the chest with a pacemaker implanted. The pacemaker generator is visible in the upper right chest, with leads extending down towards the heart. Two smaller inset images provide close-ups of the shoulder joint and the leads within the chest cavity. The shoulder X-ray shows the clavicle, scapula, and humerus, with some detail of the glenohumeral joint. The chest X-ray highlights the placement of the pacemaker leads, with one lead appearing to be positioned in the right ventricle. Yellow boxes highlight the shoulder joint in the top left inset and the area around the pacemaker leads in the main chest X-ray.

Original image

Image /page/25/Picture/2 description: This image displays a comparison between two chest X-rays, likely for evaluating image compression quality. The top row shows two full chest X-rays, each with yellow bounding boxes highlighting specific areas. The left X-ray appears to be a higher quality original, while the right X-ray shows artifacts and reduced detail, suggesting compression. The bottom row provides close-up views of the areas highlighted in the yellow boxes from both X-rays. The left bottom images are clear close-ups of the shoulder and the heart region with medical devices. The right bottom images are pixelated and blurry close-ups of the same regions, indicating significant loss of detail due to compression. Text below the bottom images indicates 'Original (PSNR: 35.54)' and 'JPEG (Q=15, PSNR: 30.00)', quantifying the quality difference.

Ours (bpp=0.15, PSNR=35.54) JPEG (bpp=0.15, PSNR=32.20)

<span id="page-25-0"></span>Figure 25. See Figure [23.](#page-23-0)