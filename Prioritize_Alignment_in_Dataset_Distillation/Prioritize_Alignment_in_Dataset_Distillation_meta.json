{"table_of_contents": [{"title": "Prioritize Alignment in Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[145.5, 99.75], [464.9765625, 99.75], [464.9765625, 116.25], [145.5, 116.25]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 251.25], [328.7109375, 251.25], [328.7109375, 262.388671875], [282.75, 262.388671875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.1298828125, 471.0], [191.6982421875, 471.0], [191.6982421875, 482.625], [107.1298828125, 482.625]]}, {"title": "2 Misaligned Information in Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[107.05517578125, 632.25], [363.0, 632.25], [363.0, 644.2734375], [107.05517578125, 644.2734375]]}, {"title": "2.1 Misaligned Information Extracted by Agent Models", "heading_level": null, "page_id": 2, "polygon": [[106.5, 496.5], [352.318359375, 496.5], [352.318359375, 506.98828125], [106.5, 506.98828125]]}, {"title": "2.2 Misaligned Information Embedded by Metric Matching", "heading_level": null, "page_id": 2, "polygon": [[106.5, 647.25], [369.0, 647.25], [369.0, 657.421875], [106.5, 657.421875]]}, {"title": "3 Method", "heading_level": null, "page_id": 3, "polygon": [[106.5, 132.75], [167.25, 132.75], [167.25, 144.052734375], [106.5, 144.052734375]]}, {"title": "3.1 Preliminary of Trajectory Matching", "heading_level": null, "page_id": 3, "polygon": [[106.5, 201.75], [285.0, 201.75], [285.0, 212.30859375], [106.5, 212.30859375]]}, {"title": "3.2 Filtering Information Extraction", "heading_level": null, "page_id": 3, "polygon": [[106.5, 400.5], [270.0, 400.5], [270.0, 410.6953125], [106.5, 410.6953125]]}, {"title": "3.3 Filtering Information Embedding", "heading_level": null, "page_id": 4, "polygon": [[106.5, 267.0], [275.25, 267.0], [275.25, 276.697265625], [106.5, 276.697265625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 4, "polygon": [[107.25, 448.5], [192.0, 448.5], [192.0, 459.421875], [107.25, 459.421875]]}, {"title": "4.1 Settings", "heading_level": null, "page_id": 4, "polygon": [[107.20458984375, 472.5], [165.75, 472.5], [165.75, 482.625], [107.20458984375, 482.625]]}, {"title": "4.2 Main Results", "heading_level": null, "page_id": 4, "polygon": [[107.25, 626.25], [187.5, 626.25], [187.5, 636.15234375], [107.25, 636.15234375]]}, {"title": "4.3 Ablation Study", "heading_level": null, "page_id": 5, "polygon": [[106.5, 570.41015625], [195.75, 570.41015625], [195.75, 580.46484375], [106.5, 580.46484375]]}, {"title": "4.3.1 Modules", "heading_level": null, "page_id": 5, "polygon": [[106.5, 634.5], [175.5, 634.5], [175.5, 645.43359375], [106.5, 645.43359375]]}, {"title": "4.3.2 Hyper-parameters of Filtering Information Extraction", "heading_level": null, "page_id": 6, "polygon": [[107.05517578125, 368.25], [370.5, 368.25], [370.5, 378.404296875], [107.05517578125, 378.404296875]]}, {"title": "4.3.3 Ratios of Parameter Selection", "heading_level": null, "page_id": 6, "polygon": [[106.98046875, 638.25], [265.5, 638.25], [265.5, 648.140625], [106.98046875, 648.140625]]}, {"title": "5 Discussion", "heading_level": null, "page_id": 7, "polygon": [[107.25, 449.75390625], [181.5380859375, 449.75390625], [181.5380859375, 462.12890625], [107.25, 462.12890625]]}, {"title": "5.1 Distilled Images with Filtering Information Embedding", "heading_level": null, "page_id": 7, "polygon": [[106.5, 475.27734375], [366.9609375, 475.27734375], [366.9609375, 486.10546875], [106.5, 486.10546875]]}, {"title": "5.2 Rationale for Parameter Selection", "heading_level": null, "page_id": 7, "polygon": [[106.5, 646.59375], [276.416015625, 646.59375], [276.416015625, 657.421875], [106.5, 657.421875]]}, {"title": "5.3 Parameter Selection Strategy", "heading_level": null, "page_id": 8, "polygon": [[106.5, 363.708984375], [255.75, 363.708984375], [255.75, 374.923828125], [106.5, 374.923828125]]}, {"title": "5.4 Other Methods with Data Selection", "heading_level": null, "page_id": 8, "polygon": [[106.5, 499.5], [281.25, 499.5], [281.25, 510.46875], [106.5, 510.46875]]}, {"title": "5.5 Generalization to Other Methods", "heading_level": null, "page_id": 8, "polygon": [[106.5, 613.3359375], [273.2783203125, 613.3359375], [273.2783203125, 624.9375], [106.5, 624.9375]]}, {"title": "6 Related Work", "heading_level": null, "page_id": 9, "polygon": [[107.25, 118.5], [198.0, 118.5], [198.0, 129.6474609375], [107.25, 129.6474609375]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.5, 545.25], [183.75, 545.25], [183.75, 557.26171875], [106.5, 557.26171875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.20458984375, 72.75], [164.35546875, 72.75], [164.35546875, 83.48291015625], [107.20458984375, 83.48291015625]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 13, "polygon": [[107.25, 273.0234375], [159.275390625, 273.0234375], [159.275390625, 284.625], [107.25, 284.625]]}, {"title": "A Additional Experimental Results and Findings", "heading_level": null, "page_id": 13, "polygon": [[107.20458984375, 298.5], [367.5, 298.5], [367.5, 310.1484375], [107.20458984375, 310.1484375]]}, {"title": "A.1 Filtering Misaligned Information in DC and DM", "heading_level": null, "page_id": 13, "polygon": [[107.25, 322.5234375], [340.6640625, 322.5234375], [340.6640625, 333.3515625], [107.25, 333.3515625]]}, {"title": "A.2 Data Scheduler", "heading_level": null, "page_id": 13, "polygon": [[106.5, 617.9765625], [200.8125, 617.9765625], [200.8125, 628.8046875], [106.5, 628.8046875]]}, {"title": "A.3 Comparison with Knowledge Distillation Methods", "heading_level": null, "page_id": 14, "polygon": [[106.5, 370.86328125], [347.25, 370.86328125], [347.25, 381.3046875], [106.5, 381.3046875]]}, {"title": "B Experimental Settings", "heading_level": null, "page_id": 14, "polygon": [[106.5, 538.5], [243.0, 538.5], [243.0, 550.30078125], [106.5, 550.30078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 45], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 59], ["Text", 7], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 795, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 108], ["Text", 4], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2296, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 59], ["TextInlineMath", 5], ["Text", 4], ["SectionHeader", 3], ["Equation", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 410], ["TableCell", 234], ["Line", 67], ["SectionHeader", 4], ["TextInlineMath", 2], ["Text", 2], ["Table", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 524], ["Span", 433], ["Line", 69], ["Text", 8], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 13671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 335], ["TableCell", 91], ["Line", 81], ["Text", 12], ["Caption", 5], ["Reference", 5], ["Table", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 8828, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 77], ["Text", 6], ["SectionHeader", 3], ["Figure", 2], ["Reference", 2], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 45], ["Text", 6], ["SectionHeader", 3], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 49], ["Text", 6], ["TextInlineMath", 3], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 46], ["ListItem", 20], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 46], ["TableCell", 40], ["Text", 8], ["SectionHeader", 4], ["Reference", 3], ["Table", 2], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 5959, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["TableCell", 81], ["Line", 57], ["Text", 10], ["Table", 4], ["Caption", 3], ["Reference", 3], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 5019, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 258], ["Span", 209], ["Line", 47], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["Text", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1469, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 648, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 622, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 620, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Prioritize_Alignment_in_Dataset_Distillation"}