{"table_of_contents": [{"title": "Privacy for Free: How does Dataset Condensation Help Privacy?", "heading_level": null, "page_id": 0, "polygon": [[98.25, 89.25], [497.25, 89.25], [497.25, 103.640625], [98.25, 103.640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 188.138671875], [148.5, 188.138671875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 549.75], [132.75439453125, 549.75], [132.75439453125, 561.515625], [54.0, 561.515625]]}, {"title": "2. Background and Related Work", "heading_level": null, "page_id": 1, "polygon": [[306.0, 68.25], [479.3203125, 68.25], [479.3203125, 79.61572265625], [306.0, 79.61572265625]]}, {"title": "2.1. Dataset Condensation", "heading_level": null, "page_id": 1, "polygon": [[305.25, 125.25], [419.255859375, 125.25], [419.255859375, 135.8349609375], [305.25, 135.8349609375]]}, {"title": "2.2. Membership Privacy", "heading_level": null, "page_id": 2, "polygon": [[306.75, 68.25], [414.17578125, 69.0], [414.17578125, 79.32568359375], [306.75, 79.32568359375]]}, {"title": "3. Problem Statement", "heading_level": null, "page_id": 3, "polygon": [[54.0, 108.0], [167.25, 108.75], [167.25, 120.1728515625], [54.0, 120.1728515625]]}, {"title": "4. Theoretical Analysis", "heading_level": null, "page_id": 3, "polygon": [[54.0, 504.0], [172.5, 504.0], [172.5, 514.3359375], [54.0, 514.3359375]]}, {"title": "4.1. Assumptions & Notations", "heading_level": null, "page_id": 3, "polygon": [[305.25, 190.5], [435.0, 190.5], [435.0, 199.740234375], [305.25, 199.740234375]]}, {"title": "4.2. Analysis of Synthetic Data", "heading_level": null, "page_id": 3, "polygon": [[305.25, 549.0], [437.484375, 549.0], [437.484375, 559.1953125], [305.25, 559.1953125]]}, {"title": "4.3. <PERSON><PERSON><PERSON><PERSON> of Models Trained on Synthetic\nData", "heading_level": null, "page_id": 4, "polygon": [[306.0, 473.25], [524.443359375, 473.25], [524.443359375, 495.38671875], [306.0, 495.38671875]]}, {"title": "5. Evaluation", "heading_level": null, "page_id": 5, "polygon": [[306.0, 281.53125], [375.92578125, 281.53125], [375.92578125, 293.1328125], [306.0, 293.1328125]]}, {"title": "5.1. Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[305.25, 375.0], [410.25, 375.0], [410.25, 385.55859375], [305.25, 385.55859375]]}, {"title": "5.2. Membership Privacy of f_{\\mathcal{S}}", "heading_level": null, "page_id": 6, "polygon": [[54.0, 506.25], [186.0, 506.25], [186.0, 516.65625], [54.0, 516.65625]]}, {"title": "5.3. <PERSON><PERSON><PERSON>on with Different Generators", "heading_level": null, "page_id": 7, "polygon": [[54.0, 378.0], [238.5, 378.0], [238.5, 387.685546875], [54.0, 387.685546875]]}, {"title": "5.4. Visual Privacy", "heading_level": null, "page_id": 7, "polygon": [[306.0, 530.96484375], [387.0, 530.96484375], [387.0, 540.24609375], [306.0, 540.24609375]]}, {"title": "6. Discussion and Conclusion", "heading_level": null, "page_id": 8, "polygon": [[54.0, 549.0], [204.0, 549.0], [204.0, 559.96875], [54.0, 559.96875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[306.0, 663.0], [407.25, 663.0], [407.25, 674.05078125], [306.0, 674.05078125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.0, 68.25], [111.0, 79.61572265625], [54.0, 79.61572265625]]}, {"title": "<PERSON><PERSON> of Proposition 4.3", "heading_level": null, "page_id": 11, "polygon": [[54.0, 67.5], [191.25, 67.5], [191.25, 78.890625], [54.0, 78.890625]]}, {"title": "<PERSON><PERSON> Proof of Proposition 4.4", "heading_level": null, "page_id": 11, "polygon": [[54.0, 408.75], [191.25, 408.75], [191.25, 419.203125], [54.0, 419.203125]]}, {"title": "B.1. Empirical verification", "heading_level": null, "page_id": 12, "polygon": [[54.0, 544.5], [168.5390625, 544.5], [168.5390625, 554.5546875], [54.0, 554.5546875]]}, {"title": "Privacy for Free: How does Dataset Condensation Help Privacy?", "heading_level": null, "page_id": 13, "polygon": [[173.25, 46.5], [422.25, 46.5], [422.25, 55.5], [173.25, 55.5]]}, {"title": "<PERSON><PERSON> of Proposition 4.10", "heading_level": null, "page_id": 13, "polygon": [[54.0, 378.0], [197.9736328125, 378.0], [197.9736328125, 388.65234375], [54.0, 388.65234375]]}, {"title": "D. Generalization to Non-Linear Extractor", "heading_level": null, "page_id": 14, "polygon": [[53.7890625, 579.75], [275.25, 579.75], [275.25, 591.29296875], [53.7890625, 591.29296875]]}, {"title": "D.1. Analysis for 2-layer Network as Extractor", "heading_level": null, "page_id": 14, "polygon": [[54.0, 649.5], [252.80859375, 649.5], [252.80859375, 659.35546875], [54.0, 659.35546875]]}, {"title": "D.2. Empirical verification of Proposition 4.3 for non-linear extractor", "heading_level": null, "page_id": 16, "polygon": [[54.0, 360.421875], [349.62890625, 360.421875], [349.62890625, 371.63671875], [54.0, 371.63671875]]}, {"title": "<PERSON><PERSON> Additional Experimental Details and Results", "heading_level": null, "page_id": 16, "polygon": [[54.0, 495.38671875], [297.75, 495.38671875], [297.75, 507.76171875], [54.0, 507.76171875]]}, {"title": "E.1. Details of Hyperparameters and Settings.", "heading_level": null, "page_id": 16, "polygon": [[54.0, 541.01953125], [250.716796875, 541.01953125], [250.716796875, 551.84765625], [54.0, 551.84765625]]}, {"title": "E.2. Loss distribution of data used for DC initialization and test data on f_{\\mathcal{S}}", "heading_level": null, "page_id": 16, "polygon": [[54.0, 633.0], [372.0, 633.0], [372.0, 643.88671875], [54.0, 643.88671875]]}, {"title": "E.3. Visualization of DC-synthesized data distribution", "heading_level": null, "page_id": 17, "polygon": [[54.0, 408.75], [284.25, 408.75], [284.25, 418.81640625], [54.0, 418.81640625]]}, {"title": "E.4. MIA against cGANs", "heading_level": null, "page_id": 17, "polygon": [[54.0, 488.25], [161.5166015625, 488.25], [161.5166015625, 498.48046875], [54.0, 498.48046875]]}, {"title": "E.5. Comparison of accuracy for models trained on synthetic dataset for r_{ipc} = 0.002", "heading_level": null, "page_id": 18, "polygon": [[53.9384765625, 441.75], [415.5, 443.1796875], [415.5, 453.75], [53.9384765625, 452.4609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 98], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8356, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 560], ["Line", 107], ["TextInlineMath", 5], ["Text", 4], ["ListItem", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 907], ["Line", 135], ["TextInlineMath", 8], ["Equation", 6], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1155, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 785], ["Line", 125], ["Text", 9], ["TextInlineMath", 7], ["Reference", 6], ["SectionHeader", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 919], ["Line", 128], ["TextInlineMath", 12], ["Reference", 7], ["Equation", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 839], ["Line", 101], ["TextInlineMath", 8], ["Text", 7], ["Equation", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 112], ["TableCell", 63], ["Text", 7], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Caption", 1], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1081], ["Line", 113], ["TableCell", 108], ["Text", 4], ["TextInlineMath", 4], ["Reference", 4], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 738], ["Line", 229], ["Caption", 5], ["Text", 5], ["Figure", 4], ["FigureGroup", 4], ["Reference", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 4714, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 95], ["ListItem", 26], ["Reference", 26], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 68], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 906], ["Line", 133], ["TextInlineMath", 6], ["Equation", 6], ["Reference", 4], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1305, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 690], ["Line", 130], ["Equation", 7], ["Text", 5], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 485], ["Line", 77], ["Text", 4], ["Reference", 4], ["Equation", 3], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1993, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 689], ["Line", 171], ["Equation", 7], ["Text", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2469, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 1283], ["Line", 198], ["Equation", 8], ["TextInlineMath", 7], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3160, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 471], ["Line", 54], ["Text", 5], ["Reference", 5], ["SectionHeader", 4], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 840, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 114], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1863, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 47], ["TableCell", 6], ["Caption", 3], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Equation", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1115, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Privacy_for_Free__How_does_Dataset_Condensation_Help_Privacy"}