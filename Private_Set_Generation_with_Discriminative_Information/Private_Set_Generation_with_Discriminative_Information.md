# <span id="page-0-0"></span>Private Set Generation with Discriminative Information

<PERSON><PERSON><PERSON>ouche <PERSON>PA Helmholtz Center for Information Security {dingfan.chen, raouf.kerkouche, fritz}@cispa.de

## Abstract

Differentially private data generation techniques have become a promising solution to the data privacy challenge –– it enables sharing of data while complying with rigorous privacy guarantees, which is essential for scientific progress in sensitive domains. Unfortunately, restricted by the inherent complexity of modeling highdimensional distributions, existing private generative models are struggling with the utility of synthetic samples. In contrast to existing works that aim at fitting the complete data distribution, we directly optimize for a small set of samples that are representative of the distribution under the supervision of discriminative information from downstream tasks, which is generally an easier task and more suitable for private training. Our work provides an alternative view for differentially private generation of high-dimensional data and introduces a simple yet effective method that greatly improves the sample utility of state-of-the-art approaches. The source code is available at <https://github.com/DingfanChen/Private-Set>.

# 1 Introduction

Data sharing is vital for the growth of machine learning applications in numerous domains. However, in many application scenarios, data sharing is prohibited due to the private nature of data (e.g., individual data stored on mobile devices, medical treatments, and banking records) and the corresponding stringent regulations, which greatly hinders technological progress. Differentially private (DP) data publishing [\[8,](#page-9-0) [9,](#page-9-1) [11\]](#page-9-2) provides a compelling solution to such challenge, where only a sanitized form of the data is publicly released. Such sanitized synthetic data can be leveraged as if it were the real data, analyzed with established toolchains, and can be shared openly to the public, facilitating technological advance and reproducible research in sensitive domains.

Yet, generation of high-dimensional data with DP guarantees is highly challenging and traditional DP algorithms designed for capturing low-dimensional statistical characteristics are not applicable to this task  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$  $[31, 13, 4, 38]$ ). Instead, inspired by the great successes of deep generative models in learning high-dimensional representations, recent works  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  $[5, 6, 44, 45, 2, 12]$  adopt deep generative neural networks as the underlying generation backbone and incorporate the privacy constraints into the training procedure, such that any privacy leakage upon disclosing the data generator is bounded.

However, these methods have common shortcomings: *(i)* deep generative models are known to be datademanding  $[16]$ , which becomes even harder to train when considering the privacy constraints  $[6, 5]$  $[6, 5]$  $[6, 5]$ ; *(ii)* they do not guarantee any optimal solution for the downstream task (e.g. classification). In fact, existing models are still struggling to generate sanitized data that is useful for downstream data analysis tasks. For example, when training a convolutional neural network (ConvNet) classifier on the private generated data, the highest test accuracy reported in literature is  $< 85\%$  for MNIST dataset with  $(\epsilon, \delta) = (10, 10^{-5})$  [\[5\]](#page-9-4), which lags far behind the discriminative baseline (> 98% with  $(\epsilon, \delta) = (1.2, 10^{-5})$  [\[35\]](#page-11-3)) and makes private generative models less appealing for many practical scenarios with data analysis as the end goal.

36th Conference on Neural Information Processing Systems (NeurIPS 2022).

In this work, we learn to synthesize informative samples that are privacy-preserving and are optimized to train neural networks for downstream tasks. In contrast to existing approaches, we directly optimize a small set of samples instead of the deep generative models that is notoriously difficult to train in a private manner. Moreover, we exploit discriminative information from downstream tasks to guide the samples towards containing more useful information for downstream analysis. Compared to existing works, we improve the task utility by a large extent (up to  $10\%$  downstream test accuracy improvement over state-of-the-art approach), while still preserving the flexibility and generality across varying configurations for downstream analysis. As an added benefit, our formulation naturally distilled the knowledge of original data into a much smaller set, which largely saves the memory and computational consumption for downstream analysis.

We summarize our main contributions as follows.

- We present a new perspective of private high-dimensional data generation, with which we aim to bridge the utility and generality gap between the private generative and discriminative models. We believe this alternative view opens up new possibilities in different research directions ranging from private analysis to generation.
- We introduce a simple yet effective method for generating informative samples that are optimized for training downstream neural networks, while maintaining generality as well as reducing the memory and computation consumption as added benefits.
- Experimental results demonstrate that, in comparison to existing works, our work improves the sample utility by a large margin and offers superior practicability for real-world application scenarios.

# 2 Related Work

Differentially Private Generative Models Training deep generative models in a private manner has become the default choice for private high-dimensional data generation. Existing methods typically adopt differentially private stochastic gradient descent (DP-SGD)  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  $[1, 34, 6, 5]$  or Private Aggregation of Teacher Ensembles (PATE)  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  $[26, 27, 21, 39]$  to equip the deep generative models with rigorous privacy guarantees. Despite significant progress in mitigating training instabilities and improving generation (visual) quality, existing works are still far from being optimal in terms of the sample utility. This is mainly because existing works are attempting to solve a problem that is inherently hard and almost impossible to be solved accurately under the current private training framework. In contrast, we directly optimize the samples (rather than the deep generative models that are much harder to train in a private setting) and exploit the knowledge from a general class of downstream tasks that can be employed on the samples to further guide the training.

Coreset Selection and Generation Our work is largely motivated by recent success in distilling a large dataset into a much smaller set of representative samples, i.e., the coreset. For example, samples from a dataset are selected to be representative based on their ability to mimic the gradient signal  $[25]$ , hardness to fit  $[33]$ , distance to the cluster centers  $[42, 30]$  $[42, 30]$  $[42, 30]$ , etc. Instead of selecting samples from the dataset, our work focus on synthesizing informative samples from scratch [\[40,](#page-11-8) [50,](#page-11-9) [48,](#page-11-10) [20\]](#page-10-8) under DP constraints, and optimizing the sample utility for training downstream neural networks. While recent work [\[7\]](#page-9-9) has shown promising results in dataset distillation under privacy concerns, obtaining strict privacy guarantees has remained challenging. Our set generation formulation is also similar in spirit to works in the field of private queries release  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  $[31, 13, 4, 14]$  which synthesize a set of pseudo-data (under DP guarantees) that is representative of the original data in answering linear queries. However, as neural networks exhibit highly nonlinear properties, methods targeted at linear queries are generally not applicable to our case and are algorithmically distinct from approaches designed for neural nets.

# 3 Background

We consider the standard central model of DP in this paper. We below present several definitions and theorems that will be used in this work.

**Definition 3.1.** (Differential Privacy (DP) [\[8\]](#page-9-0)) A randomized mechanism M with range  $\mathcal{R}$  is  $(\varepsilon, \delta)$ -DP, if

$$
Pr[\mathcal{M}(\mathcal{D}) \in \mathcal{O}] \le e^{\varepsilon} \cdot Pr[\mathcal{M}(\mathcal{D}') \in \mathcal{O}] + \delta
$$
 (1)

holds for any subset of outputs  $\mathcal{O} \subseteq \mathcal{R}$  and for any adjacent datasets  $\mathcal{D}$  and  $\mathcal{D}'$ , where  $\mathcal{D}$  and  $\mathcal{D}'$ differ from each other with only one training example, i.e.,  $\mathcal{D}' = \mathcal{D} \cup \{x\}$  for some x (or vice versa). M corresponds to the set generation algorithm in our case,  $\varepsilon$  is the upper bound of privacy loss, and  $\delta$  is the probability of breaching DP constraints. DP guarantees the difficulty of inferring the presence of an individual in the private dataset by observing the generated set of samples  $\mathcal{M}(D)$ .

Our approach is built on top of the Gaussian mechanism defined as follows.

**Definition 3.2.** (Gaussian Mechanism [\[10\]](#page-9-10)) Let  $f : X \to \mathbb{R}^d$  be an arbitrary d-dimensional function with sensitivity being

$$
\Delta_2 f = \max_{\mathcal{D}, \mathcal{D}'} ||f(\mathcal{D}) - f(\mathcal{D}')||_2
$$
\n(2)

over all adjacent datasets D and D'. The Gaussian Mechanism  $\mathcal{M}_{\sigma}$ , parameterized by  $\sigma$ , adds noise into the output, i.e.,

<span id="page-2-2"></span>
$$
\mathcal{M}_{\sigma}(x) = f(x) + \mathcal{N}(0, \sigma^2 I). \tag{3}
$$

 $\mathcal{M}_{\sigma}$  is  $(\varepsilon, \delta)$ -DP for  $\sigma \geq \sqrt{2 \ln{(1.25/\delta)}} \Delta_2 f / \varepsilon$ .

Any privacy cost is bounded upon releasing the private set of generated data due to the closedness of DP under post-processing.

<span id="page-2-1"></span>**Theorem 3.1.** (Post-processing [\[10\]](#page-9-10)) If M satisfies  $(\varepsilon, \delta)$ -DP,  $F \circ M$  will satisfy  $(\varepsilon, \delta)$ -DP for any data-independent function  $F$  with  $\circ$  denoting the composition operator.

# 4 Method

We consider a standard classification task where we are given a private dataset  $\mathcal{D} = \{(\boldsymbol{x}_i, y_i)\}_{i=1}^N$ with  $x_i \in \mathbb{R}^d$  the feature,  $y_i \in \{1, ..., L\}$  the class label, N the number of samples, L the number of label classes. Our objective is to synthesize a set of samples  $S = \{(\mathbf{x}_i^S, y_i^S)\}_{i=1}^M$  such that *(i)* samples in  $S$  have the same form as data in  $D$ ; *(ii)* a neural network trained on  $S$  should maximally match generalization performance of a deep neural network that is trained on D; *(iii)* the privacy leakage of D when releasing S is upper bounded by a pre-defined privacy level  $(\varepsilon, \delta)$ .

Let  $F(\cdot; \theta^D)$  and  $F(\cdot; \theta^S)$  be the deep neural networks parameterized by  $\theta^D$  and  $\theta^S$  that are trained on  $D$  and  $S$  respectively. The objective can be formulated as:

<span id="page-2-0"></span>
$$
\mathbb{E}_{(\boldsymbol{x},y)\sim P_{\mathcal{D}}}[\ell(F(\boldsymbol{x};\boldsymbol{\theta}^{\mathcal{D}}),y)] \simeq \mathbb{E}_{(\boldsymbol{x},y)\sim P_{\mathcal{D}}}[\ell(F(\boldsymbol{x};\boldsymbol{\theta}^{\mathcal{S}}),y)] \tag{4}
$$

where  $\ell$  denotes the loss function (e.g., cross-entropy for the classification task) and the expectation is taken over the real data distribution  $P_{\mathcal{D}}$ .

Equation [4](#page-2-0) can be naturally achieved once  $\theta^S \approx \theta^D$ . In particular, when given the same initialization  $\theta_0^D = \theta_0^S$ , solving for  $\theta_t^S \approx \theta_t^D$  at each training iteration t leads to  $\theta^S \approx \theta^D$  as desired. This can be achieved by optimizing the synthetic set  $S$  such that it yields a similar gradient as if the network is trained on the real dataset at each iteration t:

<span id="page-2-3"></span>
$$
\min_{\mathcal{S}} \mathcal{L}_{\text{dis}}(\nabla_{\boldsymbol{\theta}} \mathcal{L}(\mathcal{S}, \boldsymbol{\theta}_t), \nabla_{\boldsymbol{\theta}} \mathcal{L}(\mathcal{D}, \boldsymbol{\theta}_t))
$$
(5)

where  $\nabla_{\theta} \mathcal{L}(\mathcal{S}, \theta_t)$  corresponds to the gradient of the classification loss on the synthetic set S,  $\nabla_{\theta} \mathcal{L}(\mathcal{D}, \theta_t)$  denotes the stochastic gradient on the real data, and  $\mathcal{L}_{dis}$  is a sum of cosine distances between the gradients at each layer [\[50,](#page-11-9) [48\]](#page-11-10) (See supplementary material for more details).

To mimic the training procedure,  $S$  and the network  $F(\cdot; \theta)$  are updated jointly in an iterative manner, where in each outer iteration the  $S$  is trained to minimize the gradient matching loss  $\mathcal{L}_{dis}$  and in each inner iterations the network parameters  $\theta_t$  are optimized towards minimizing the classification loss on the synthetic set S. Moreover, S is optimized over multiple initializations of network parameters  $\theta_0$  to ensure the generalization ability of  $S$  over different random initialization when training a downstream

Image /page/2/Figure/18 description: This is a diagram illustrating a machine learning process with two main loops. The top loop involves a component labeled 'S' feeding into a block labeled 'F', which then outputs to a block labeled 'L'. From 'L', an output 'gS\_theta\_t' goes to 'L\_dis', and a gradient 'nabla S L\_dis' also feeds into 'L\_dis'. The parameter 'theta\_t' is shown to be updated by both loops. The bottom loop starts with data 'x, y ~ PD' feeding into another block labeled 'F', which outputs to a block labeled 'L'. From 'L', an output 'gD\_theta\_t' goes to 'M\_sigma,C', which then outputs to 'gD\_theta\_t'. The diagram includes a legend indicating different types of arrows: yellow dotted arrows represent the 'inner loop', green dotted arrows represent the 'outer loop', red arrows represent 'sensitive' data flow, and blue arrows represent '(epsilon, delta)-private' data flow. The parameter 'theta\_t' is shown to be updated by the inner loop and influences the outer loop.

Figure 1: Illustration for the training pipeline.

model. The objective can be summarize as follows [\[40,](#page-11-8) [50,](#page-11-9) [48\]](#page-11-10):

$$
S = \underset{S}{\arg\min} \mathbb{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \sum_{t=0}^{T-1} [\mathcal{L}_{\text{dis}}(\nabla_{\boldsymbol{\theta}} \mathcal{L}(S, \boldsymbol{\theta}_t), \nabla_{\boldsymbol{\theta}} \mathcal{L}(\mathcal{D}, \boldsymbol{\theta}_t))]
$$
(6)

where  $P_{\theta_0}$  stands for the distribution over the initialization of network parameters.

We incorporate DP constraints by sanitizing the stochastic gradient on real data  $\nabla_{\theta} \mathcal{L}(\mathcal{D}, \theta_t)$  at each outer iteration, while leaving the inner iterations unchanged as their privacy is guaranteed by the post-processing theorem [3.1.](#page-2-1) The final objective can be formulated as follows:

<span id="page-3-1"></span>
$$
S = \underset{S}{\arg\min} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \sum_{t=0}^{T-1} [\mathcal{L}_{\text{dis}}(g_{\theta_t}^S, \widetilde{g_{\theta_t}^D})] \tag{7}
$$

where we use  $g_{\theta_t}^{\mathcal{D}}$  to denote the parameter gradient on  $\mathcal{D}$  that is sanitized via Gaussian mechanism [3.2,](#page-2-2) and  $g_{\theta_t}^S$  to denote the parameter gradient on S. The whole pipeline is summarized in Algorithm [1.](#page-3-0) We use the subsampled Renyi-DP accountant [\[1,](#page-9-8) [23\]](#page-10-10) to compute the overall privacy cost accumulated for iteratively updating  $S$ . Note that the training procedure and the privacy computation are approximately as simple as training a classification network with DP-SGD, which in general has lower difficulty than training a DP deep generative models as done in existing works (witnessed by a significant performance gap in terms of the classification accuracy). Moreover, in contrast to previous works, our synthetic set  $S$  is directly optimized for downstream tasks, which naturally leads to superior downstream utility to existing approaches.

### Algorithm 1: Private Set Generation (PSG)

<span id="page-3-0"></span>**Input:** Dataset  $\mathcal{D} = \{(x_i, y_i)\}_{i=1}^N$ , learning rate for update network parameters  $\tau_{\theta}$  and  $\tau_{\mathcal{S}}$ , batch size B, gradient clipping bound C, number of runs R, outer iterations T, inner iterations J, batches K, desired privacy cost  $\varepsilon$  given a pre-defined  $\delta$ 

### Output: Synthetic set $S$

Compute the required DP noise scale  $\sigma$  numerically [\[1,](#page-9-8) [23\]](#page-10-10) so that the privacy cost equals  $\varepsilon$  after the training; Initialize synthetic set S (features  $x^S$  are from Gaussian noise; labels are balanced set depending on the pre-defined number of samples per class) ;

for *run* in  $\{1, ..., R\}$  do Initialize model parameter  $\theta_0 \sim P_{\theta_0}$ ; for *outer\_iter* in {1, ..., T} do  $\boldsymbol{\theta}_{t+1} = \boldsymbol{\theta}_t$ for *batch\_index* in  $\{1, ..., K\}$  do Uniformly sample random batch  $\{(x_i, y_i)\}_{i=1}^B$  from  $\mathcal{D}$ ; for  $\mathit{each}\left(\boldsymbol{x}_{i},y_{i}\right)$  do // Compute per-example gradients on real data  $g_{{\boldsymbol{\theta}}_t}^{\mathcal{D}}({\boldsymbol{x}}_i) = \ell(F({\boldsymbol{x}}_i;{\boldsymbol{\theta}}_t), y_i)$ // Clip gradients  $g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i) = g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i) \cdot \min(1,C/\|g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i)\|_2)$ end // Add noise to average gradient with Gaussian mechanism  $g_{\theta_t}^{\mathcal{D}} = \frac{1}{B}\sum_{i=1}^B (g_{\theta_t}^{\mathcal{D}}(\boldsymbol{x}_i) + \mathcal{N}(0, \sigma^2 C^2 I))$ // Compute parameter gradients on synthetic data and update  $\mathcal S$  $g_{{\bm{\theta}}_t}^\mathcal{S} = \nabla_{\bm{\theta}} \mathcal{L}(\mathcal{S}, {\bm{\theta}}_t)) = \frac{1}{M} \sum_{i=1}^M \ell(F({\bm{x}}_i^\mathcal{S}; {\bm{\theta}}_t), y_i^\mathcal{S})$  $\mathcal{S} = \mathcal{S} - \tau_{\mathcal{S}} \cdot \nabla_{\mathcal{S}} \mathcal{L}_{\mathrm{dis}}(g_{{\bm{\theta}}_t}^{\mathcal{S}}, g_{{\bm{\theta}}_t}^{\mathcal{D}})$ end end for *inner\_iter* in  $\{1, ..., J\}$  do // Update network parameter using  $S$  $\theta_t = \theta_t - \tau_{\theta} \cdot \dot{\nabla}_{\theta} \mathcal{L}(\mathcal{S}, \theta_t)$ end end return Synthetic set  $S$ 

| ٦<br>v<br>٠<br>٦<br>٦<br>I |  |  |
|----------------------------|--|--|
|----------------------------|--|--|

<span id="page-4-0"></span>

|               |  | MNIST             |                    | FashionMNIST      |                    | MNIST   |          |          | FashionMNIST |          |          |      |
|---------------|--|-------------------|--------------------|-------------------|--------------------|---------|----------|----------|--------------|----------|----------|------|
|               |  | $\varepsilon = 1$ | $\varepsilon = 10$ | $\varepsilon = 1$ | $\varepsilon = 10$ |         | $spc=10$ | $spc=20$ | full         | $spc=10$ | $spc=20$ | full |
| DP-CGAN       |  | -                 | 52.5               | -                 | 50.2               | Real    | 93.6     | 95.9     | 99.6         | 74.4     | 77.4     | 93.5 |
| G-PATE        |  | 58.8              | 80.9               | 58.1              | 69.3               | DPSGD   | -        | -        | 96.5         | -        | -        | 82.9 |
| DataLens      |  | 71.2              | 80.7               | 64.8              | 70.6               | DP-CGAN | 57.4     | 57.1     | 52.5         | 51.4     | 53.0     | 50.2 |
| GS-WGAN       |  | -                 | 84.9               | -                 | 63.1               | GS-WGAN | 83.3     | 85.5     | 84.9         | 58.7     | 59.5     | 63.1 |
| DP-Merf       |  | 72.7              | 85.7               | 61.2              | 72.4               | DP-Merf | 80.2     | 83.2     | 85.7         | 66.6     | 67.9     | 72.4 |
| DP-Sinkhorn   |  | -                 | 83.2               | -                 | 71.1               | Ours    | 94.9     | 95.6     | -            | 75.6     | 77.7     | -    |
| Ours (spc=20) |  | 80.9              | 95.6               | 70.2              | 77.7               |         |          |          |              |          |          |      |

Table 1: Test accuracy (%) on real data of downstream ConvNet classifiers when training on the synthetic set with  $\delta = 10^{-5}$ . (a) Comparison under different privacy cost  $\varepsilon \in \{1, 10\}$ . (b) Comparison when varying the number of samples per class (spc) for training the downstream ConvNet with  $\varepsilon = 10$ , while "full" corresponds to 6000 samples per class. We show the results when training on real data non-privately and with DPSGD [\[1\]](#page-9-8) as reference.

<span id="page-4-2"></span>

# 5 Experiment

<span id="page-4-1"></span>

## 5.1 Classification

We first compare private set generation (PSG) with existing DP generative models on standard classification benchmarks including MNIST [\[18\]](#page-10-11) and FashionMNIST [\[43\]](#page-11-11).

Setup. We use by default a ConvNet with 3 blocks where each block contains one Conv layer with 128 filters, followed by Instance Normalization [\[36\]](#page-11-12), ReLU activation and AvgPooling modules, and a fully connected (FC) layer as the final output layer. We initialize the network parameters using Kaiming initialization [\[15\]](#page-10-12) and the synthetic samples using standard Gaussian. We report the averaged results over 3 runs of experiments for all the comparisons. We list below the default hyperparameters used for the main experiments and refer to the supplementary material for more details: Clipping bound  $C = 0.1$ ,  $R = 1000$  for  $\varepsilon = 10$  (and 200 for  $\varepsilon = 1$ ), number of samples per class (spc)  $\in \{10, 20\}, K = 10, T = 10$  for spc=10 (and =20 for spc=20).

Comparison to state of the art. We show in Table [1a](#page-4-0) the results of, to the best of our knowledge, all existing DP high-dimensional data generation methods (whose validity has been justified via peer review at top-tier conferences) that report results on the benchmark datasets we consider. These include DP-CGAN [\[34\]](#page-11-4), G-PATE [\[21\]](#page-10-5), DataLens [\[39\]](#page-11-5), GS-WGAN [\[6\]](#page-9-5), DP-Merf [\[12\]](#page-9-7), DP-Sinkhorn [\[5\]](#page-9-4). For methods that are not open-sourced, we report the original results from the published paper. As shown in Table [1a,](#page-4-0) our formulation results in significant improvement in the sample utility (measured by test accuracy on real data) for training downstream classification models. Specifically, the improvement is consistent and significant (around 5-10% increase over different configurations) for both the low privacy budget regime  $(\varepsilon=1)$  (around 8-9% improvement over SOTA in this case) and a relatively high privacy regime ( $\varepsilon$ =10) where all the investigated methods achieve convergence (around 10% and 5% increase in test accuracy for MNIST and FashionMNIST, respectively). Note that in contrast to most existing methods that show superiority only for a certain range of privacy levels, our improvement covers a wide range, if not all, of practical scenarios spanning across different privacy levels.

We then focus on the open-sourced methods that are strictly comparable (e.g., G-PATE and DataLens provide data-dependent  $\varepsilon$ , i.e., publishing  $\varepsilon$  value will introduce privacy cost and are thus not directly comparable) to ours and conduct a comprehensive investigation through different angles.

**Memory and computation cost.** We additionally show that our method is the only one that simultaneously shows advantages in reducing the memory and computation consumption of downstream analysis. As shown in Table [1b,](#page-4-0) training the classifier with full (6000 samples per class) size of samples in most cases yields an upper bound for the test accuracy, while training on randomly subsampled smaller sets will decrease the performance, unless the generated samples are not informative such that they can be harmful to the downstream tasks (e.g., for DP-CGAN). In contrast, we directly optimize

<span id="page-5-0"></span>

|                                   | <b>MNIST</b> |       |         |       |          |      |         | FashionMNIST |         |       |          |      |  |
|-----------------------------------|--------------|-------|---------|-------|----------|------|---------|--------------|---------|-------|----------|------|--|
|                                   | ConvNet      | LeNet | AlexNet | VGG11 | ResNet18 | MLP  | ConvNet | LeNet        | AlexNet | VGG11 | ResNet18 | MLP  |  |
| Real                              | 99.6         | 99.2  | 99.5    | 99.6  | 99.7     | 98.3 | 93.5    | 88.9         | 91.5    | 93.8  | 94.5     | 86.9 |  |
| DP-CGAN                           | 50.2         | 52.6  | 52.1    | 54.7  | 51.8     | 54.3 | 50.2    | 52.6         | 52.1    | 54.7  | 51.8     | 54.3 |  |
| <b>GS-WGAN</b>                    | 84.9         | 83.2  | 80.5    | 87.9  | 89.3     | 74.7 | 54.7    | 62.7         | 55.1    | 57.3  | 58.9     | 65.4 |  |
| DP-Merf                           | 85.7         | 87.2  | 84.4    | 81.7  | 81.3     | 85.0 | 72.4    | 67.9         | 64.9    | 70.1  | 66.7     | 73.1 |  |
| <b>Ours <math>(spc=10)</math></b> | 94.9         | 91.3  | 90.3    | 93.6  | 94.3     | 86.1 | 75.6    | 68.0         | 66.2    | 74.7  | 72.1     | 62.8 |  |
| <b>Ours <math>(spc=20)</math></b> | 95.6         | 93.0  | 92.3    | 94.5  | 94.1     | 87.1 | 77.7    | 68.0         | 59.1    | 76.8  | 70.8     | 62.2 |  |

**Table 2:** Comparison of generalization ability across different network architecture with  $(\varepsilon, \delta)$  = (10, 10−<sup>5</sup> ). Our generated set is optimized with *ConvNet*, while the downstream classifiers are of different architecture. The classifiers are trained on the full synthetic set for baseline methods.

<span id="page-5-1"></span>Image /page/5/Figure/2 description: Two line graphs show test accuracy (%) on the y-axis versus Epsilon on the x-axis, ranging from 0 to 10. Both graphs display four lines representing different methods: 'Ours (spc=10)' (blue), 'Ours (spc=20)' (orange), 'GS-WGAN' (green), and 'DP-CGAN' (red). The left graph shows test accuracy generally increasing with Epsilon, with 'Ours (spc=10)' and 'Ours (spc=20)' reaching approximately 95% accuracy by Epsilon=4 and staying high. 'GS-WGAN' shows a fluctuating increase, reaching around 78% by Epsilon=10. 'DP-CGAN' shows a slower, more erratic increase, reaching about 60% by Epsilon=10. The right graph shows a similar trend but with lower overall accuracy. 'Ours (spc=10)' and 'Ours (spc=20)' reach around 70% accuracy by Epsilon=2 and plateau around 75% thereafter. 'GS-WGAN' shows a steady increase, reaching about 65% by Epsilon=10. 'DP-CGAN' shows a very slow and fluctuating increase, reaching only about 50% by Epsilon=10.

(a) MNIST

(b) FashionMNIST

Figure 2: Comparison of the convergence rate to existing private generative models with iteratively accumulated privacy cost. X-axis: privacy cost  $\varepsilon$ , Y-axis: utility (i.e., test accuracy  $(\%)$ ) for training downstream ConvNet classifiers.

to compress the useful information into a small set of samples and naturally save the memory and computation consumption for downstream analysis tasks.

Generalization ability across different architectures. One natural concern of our formulation could be the generalization ability to unseen situations. While we exploit discriminative information to guide the training, we (in principle) inevitably trade the generality off against task-specific utility, leaving no performance guarantees for new models. Interestingly, as shown in Table [2,](#page-5-0) we find that our generated set still provides better utility than all baseline methods in most cases, even though the models for evaluation have a completely different architecture from the one we used for training. The only case where our generated set does not work well is for training MLPs. We conjecture that it is due to the difference in the network properties that result in distinct gradient signals: for example, layers in MLPs are densely connected while being sparsely connected in ConvNets, and Convolutional layers are translation equivalent while FC layers in MLPs are not. Moreover, we argue that this may not be a bug, but a feature. Note that the reference results on real data also indicate that the MLP is inferior to other architectures while models with ConvNet, VGG, or ResNet architecture perform well in most cases. In this regard, results on our generated set generally align well with the result on real data, which suggests the possibility of conducting model selection with our private generated set.

Convergence rate. For most private (gradient-based) iterative methods, the privacy cost accumulates in each training iteration, and thus faster convergence is highly preferable. We show in Figure [2](#page-5-1) the training curves where the y-axis denotes the utility and the x-axis corresponds to the privacy. We observe that our method generally has a much faster convergence rate than existing methods that need to accumulate the privacy cost for each iteration. In particular, our method already achieves a decent level of utility with  $\varepsilon \leq 2$  which is much lower than the privacy budget used in most previous works (normally  $\varepsilon = 10$ ).

## 5.2 Application: Private Continual Learning

The utility guarantee of our formulation requires that the network architecture is known to the data provider/generator. Fortunately, it is not a rare case in practice. In particular, our method is naturally

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image contains two line graphs side-by-side, both plotting "Averaged Test Accuracy (%)" on the y-axis against "Training Stage" on the x-axis, ranging from 0 to 4. The left graph shows DPSGD (ε = 1) starting at 100% and dropping to around 30%, DPSGD (ε = 10) starting at 100% and dropping to around 35%, DP-Merf (ε = 1) starting at 95% and dropping to around 72%, DP-Merf (ε = 10) starting at 98% and dropping to around 75%, Ours (ε = 1) starting at 98% and dropping to around 78%, and Ours (ε = 10) starting at 100% and dropping to around 80%. The right graph shows DPSGD (ε = 1) starting at 100% and dropping to around 28%, DPSGD (ε = 10) starting at 100% and dropping to around 30%, DP-Merf (ε = 1) starting at 95% and dropping to around 65%, DP-Merf (ε = 10) starting at 98% and dropping to around 70%, Ours (ε = 1) starting at 98% and dropping to around 75%, and Ours (ε = 10) starting at 100% and dropping to around 78%.

(a) SplitMNIST

(b) SplitFashionMNIST

**Figure 3:** Comparison for private training in the continual learning setting with  $\delta = 10^{-5}$  and different  $\varepsilon$ . X-axis: training stage, Y-axis: averaged test accuracy (over all the stages till the current one). We use a ConvNet classifier in this case and set  $spc = 10$  for our method and  $spc = 6000$  for DP-Merf as default.

applicable to cases where *(i)* there are multiple parties involved in training a model and they agree on one common training protocol (i.e., the network architecture is known to all participants); *(ii)* each party has its own data whose privacy need to be protected (i.e., the training need to be DP); *(iii)* data on each party exhibit distinct property and is all informative for the final task (i.e., a synthetic set of representative samples that capture such properties would greatly aid the final task).

One example is continual learning  $[19, 32]$  $[19, 32]$  $[19, 32]$  where the training of the classification network is split into stages. Here we consider a setting adjusted to the DP training: to protect the privacy of its data, each party is responsible for a different training stage where it performs DP training of the model on its data, and subsequently delivers the DP model to the party responsible for the next training stage. Note that the raw data would not be transferred as otherwise the privacy would be leaked.

We conduct DP training on the SplitMNIST and SplitFashionMNIST datasets where the data is partitioned into 5 parts (based on the class labels, which corresponds to the class-incremental [\[30\]](#page-10-7) setup) and we assume each part is held by one party and can not be accessed by others for privacy sake (See supplementary material for more details). We show in Figure  $\overline{3}$  $\overline{3}$  $\overline{3}$  (green curves) the baseline results of DP training of model under the private class-incremental setting (i.e., each party finetune the model is obtained from the previous stage on its own data using DP-SGD). Apparently, this naive training scheme leads to catastrophic forgetting of information learned in the early stages. Even worse is that the common strategy to cope with this issue requires transferring a small set of real data to other parties such that it can be replayed in the later training stage  $[30, 3, 28]$  $[30, 3, 28]$  $[30, 3, 28]$  $[30, 3, 28]$  $[30, 3, 28]$ , which is not directly applicable to the private setting as transferring the data breaks privacy. In contrast, private generation methods can be seamlessly applied to this case, where a set of DP synthetic samples is transferred to enable the final model to learn the characteristics of each partition of data. In particular, our formulation is better suitable for this setting than other generation methods as the network architecture is known to all participants and samples can be tailored to the specific network via our formulation. This is verified in Figure [3,](#page-6-0) where our synthetic samples are generally more informative for training the classifier when compared to DP-Merf – the overall best existing works in terms of the downstream utility. Moreover, as our formulation condenses the information into a small set of samples by construction, we also enjoy the advantages when considering the computation, storage, and communication cost.

<span id="page-6-1"></span>

# 6 Discussion

In this section, we present several key factors that distinguish our approach from existing ones and discuss possible concerns regarding our private set generation formulation.

Trade-off between Visual Quality and Task Utility. Our formulation is designed for optimizing the utility of downstream analysis tasks instead of the visual appearance as done in previous works, thereby leaving no performance guarantee for the visual quality of the synthetic samples. Moreover, the optimization of the private synthetic set is unconstrained and unregulated over the whole data space, with the gradient signal as the only guidance. As the data to gradient mapping is generally

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays two sets of grids, labeled (a) MNIST (w/o prior) and (b) FashionMNIST (w/o prior). Each grid contains multiple rows and columns of images. The MNIST grid shows handwritten digits from 0 to 9, with each row dedicated to a specific digit. The FashionMNIST grid shows images of clothing items, with each row seemingly representing a category of clothing. The images within the grids appear to be generated samples, with varying degrees of clarity and fidelity.

(c) MNIST (with prior) (d) FashionMNIST (with prior)

<span id="page-7-1"></span>**Figure 4:** Our synthetic samples under  $(\varepsilon, \delta) = (10, 10^{-5})$  for MNIST and FashionMNIST datasets with or without (w/o) incorporating a DCGAN generator network as image prior.

Image /page/7/Figure/4 description: The image contains two line graphs side-by-side, both plotting Test Accuracy (%) against Epsilon. The x-axis for both graphs ranges from 0 to 10, labeled as 'Epsilon'. The y-axis for both graphs ranges from 0 to 100, labeled as 'Test Accuracy (%)'. Each graph displays six lines, representing different experimental conditions: 'w/o prior (spc=1)', 'w/o prior (spc=10)', 'w/o prior (spc=20)', 'with prior (spc=1)', 'with prior (spc=10)', and 'with prior (spc=20)'. The left graph shows a general upward trend for most lines, with 'w/o prior (spc=20)' and 'w/o prior (spc=10)' reaching the highest accuracies around 90-95%. The 'w/o prior (spc=1)' line starts low and gradually increases to about 80%. The lines representing 'with prior' conditions generally start lower and show more fluctuations, with 'with prior (spc=20)' reaching around 70% and the other two 'with prior' lines reaching around 60-70%. The right graph shows a similar pattern but with generally lower overall accuracies. The 'w/o prior (spc=20)' and 'w/o prior (spc=10)' lines reach accuracies around 70-75%. The 'w/o prior (spc=1)' line increases from around 10% to about 55%. The 'with prior' lines in the right graph are mostly below 70%, with 'with prior (spc=20)' reaching around 65%, and the other two 'with prior' lines fluctuating between 50% and 65%.

(a) MNIST

(b) FashionMNIST

Figure 5: Comparison of the convergence rate when training with or without (w/o) the image prior from DCGAN. X-axis: privacy cost  $\varepsilon$ , Y-axis: test accuracy (%) for training downstream ConvNet classifiers.

non-injective (i.e., different data can results in the same gradient), searching for the correct data given the gradient is an indefinite problem, which inevitably leads to outcomes that are out of the data manifold in practice. This can be seen in the first row of Figure [4](#page-7-0) where we plot our private synthetic samples trained under the default setting.

Recall that one key difference between our formulation and existing works is that: we directly optimize for a set of samples instead of the deep generative models. We then take a further step and investigate whether this difference is the key factor that determines the samples' visual quality. To do this, we employ a DCGAN [\[29\]](#page-10-15) model from [\[6\]](#page-9-5) as the generator backbone (denoted as  $G$ ), let  $\mathbf{x}^S$  to be outputs of  $\tilde{G}$ , and then optimize over the network parameter of  $G$  using the gradient matching loss as in Equation [5.](#page-2-3) Mathematically, this transforms Equation [7](#page-3-1) into:

$$
\min_{\boldsymbol{\varphi}} \mathbb{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \sum_{t=0}^{T-1} [\mathcal{L}_{\text{dis}}(g_{\boldsymbol{\theta}_t}^S, \widetilde{g_{\boldsymbol{\theta}_t}^D})] \quad \text{with} \quad \mathcal{S} = \{ G(\boldsymbol{z}_i; \boldsymbol{\varphi}), y_i^S \}_{i=1}^M
$$
\n(8)

where  $\varphi$  is the parameter of G,  $z_i$  is random Gaussian noise (fixed during training). Basically, this formulation restricts the synthetic images to be within the output space of  $G$ , and the inductive bias introduced by the convolutional structure serves as deep image prior [\[37\]](#page-11-14) to regularize the visual appearance of the synthetic images.

We show the synthetic samples in the second row of Figure [4,](#page-7-0) and compare the utilities with our original formulation in Figure [5.](#page-7-1) We observe that the prior from the deep generative model is indeed important for the visual quality. However, interestingly, better visual quality does not mean better utility. Specifically, optimizing over the parameter of generator  $G$  exhibits a slower convergence than directly optimizing the samples, while the final performance is also inferior (See quantitative results in Table [3\)](#page-8-0). This gives several important indications that help inform future research in this field: *(i)* the goal of achieving better downstream utility may be incompatible with the goal of achieving

better sample visual quality, while dedicated efforts towards different goals are necessary; *(ii)* deep generative models may not be the best option for the task of private data generation as they result in suboptimal utility (mainly due to its slow convergence), which questions the current default way of thinking in this field.

Scalability & Transparency. We discuss the possible issues when scaling to more complicated datasets which: *(i)* contains a large number of label classes; *(ii)* are diverse and require a large number of samples to capture the statistical characteristics of the data distribution. For *(i)*, the complexity of our (and all the other) approaches will definitely increase as the number of label classes increases. When considering the number of variables that need to be optimized, the complexity increases linearly for our case, while for all methods (that optimize over the network parameters) the increase is no less than ours. While the application of DP deep learning (of discriminative models) to datasets with >10 label classes is rare, we anticipate that dealing with a much larger number of label classes is too ambitious for DP generative modeling for now. For *(ii)*, we conduct the experiment when varying the number of samples per class and present the results in [4,](#page-8-0) where we indeed observe the training difficulty when the number of samples increases. We conjecture that it is mainly because the gradient signals for updating the synthetic samples get sparser when the number increases, which results in a lower convergence rate and thus worse results especially when the allowed privacy budget is low. However, it is arguable whether this is a shortage as smaller amounts of samples allow more savings in the storage and computation consumption while providing greater transparency of downstream analysis.

<span id="page-8-0"></span>

|                                                                                                                            |  | <b>MNIST</b> |    |  | FashionMNIST |    | <b>MNIST</b> |                                                      |                                                 | FashionMNIST |  |    |                                         |    |
|----------------------------------------------------------------------------------------------------------------------------|--|--------------|----|--|--------------|----|--------------|------------------------------------------------------|-------------------------------------------------|--------------|--|----|-----------------------------------------|----|
|                                                                                                                            |  | 10           | 20 |  | 10           | 20 |              | 10                                                   | 20                                              | 50           |  | 10 | 20                                      | 50 |
| w/o prior 81.4 94.9 95.6 66.7 75.6 77.7<br>with prior 88.2 92.2 90.6 63.0 70.2 70.7                                        |  |              |    |  |              |    |              |                                                      |                                                 |              |  |    | 81.4 94.9 95.6 94.0 66.7 75.6 77.7 71.3 |    |
| <b>Table 3:</b> Test accuracy $(\%)$ on real data of downstream                                                            |  |              |    |  |              |    |              | <b>Table 4:</b> Test accuracy $(\%)$ on real data of |                                                 |              |  |    |                                         |    |
| ConvNet classifier with or without $(w/o)$ adopting image<br>$min_{x} f_{\text{max}}$ $DCAM$ $\rightarrow$ $(10, 10^{-5})$ |  |              |    |  |              |    |              |                                                      | downstream ConvNet classifier when varying<br>. |              |  |    |                                         |    |

prior from DCGAN under  $(\varepsilon, \delta) = (10, 10^{-5})$ .

the numbers of samples per class (spc) under  $(\varepsilon, \delta) = (10, 10^{-5}).$ 

Generality and Expressiveness. Our formulation focus on the task of training downstream neural networks, and thus have no guarantees for other (and more general) purpose. In contrast, deep generative models are designed for capturing the complete data distribution and, once perfectly trained, can be applied to more general cases. While our formulation seems to be inferior in this regard, we argue that this should not be a major shortcoming that outweighs the advantages: First of all, while deep generative models in principle have much greater expressiveness than a small set of samples, such upper bound is hard, if not impossible, to be achieved in the privacy learning setting. Instead, compromising the upper bound for a more achievable target is worthy and shows great improvement over existing works as demonstrated in section [5.1.](#page-4-1) Moreover, our formulation generalizes seamlessly to any gradient-based learning methods that a downstream analyst may adopt. While such methods already cover the most part of the possible analysis algorithms that could be adopted for high-dimensional data, we believe that our approach does exhibit a good level of practical applicability.

# 7 Conclusion

We introduce a novel view of private high-dimensional data generation: instead of attempting to train deep generative models in a DP manner, we directly optimize a set of samples under the supervision of discriminative information for downstream utility. We present a simple yet effective method that allows synthesizing a small set of samples that are representative of the original data distribution and informative for training downstream neural networks. We demonstrate via extensive experiments that our formulation leads to great improvement over state-of-the-art approaches in terms of the task utility, without losing the generality for performing analysis tasks in practice. Moreover, our results question the current default way of thinking and provide insights for further pushing the frontier in the field of private data generation.

# Broader Impact

The widespread availability of rich data has fueled the growth of machine learning applications in numerous domains. However, in real-world application scenarios, data sharing is always prohibited due to the private nature of data and the corresponding stringent regulations, which greatly hinders technological progress. Our work contributes to making the latest advances in privacy-preserving data generation. In particular, our method improves the data utility compared to the state-of-the-art privacy-preserving data generation methods. In particular, we show the success on high dimensional data, which will be key to bringing those methods to a broader range of applications. Consequently, we expect broad adaptations of our technique and hence positive societal impacts. We are not aware of any extra negative societal impacts beyond generic risks of ML technology in general.

# Acknowledgments

This work is partially funded by the Helmholtz Association within the projects "Trustworthy Federated Data Analytics (TFDA)" (ZT-I-OO1 4) and "Protecting Genetic Data with Synthetic Cohorts from Deep Generative Models (PRO-GENE-GEN)" (ZT-I-PF-5-23). We acknowledge Max Planck Institute for Informatics and "Helmholtz AI computing resources" (HAICORE) for providing computing resources.

# References

- <span id="page-9-8"></span>[1] M. Abadi, A. Chu, I. Goodfellow, H. B. McMahan, I. Mironov, K. Talwar, and L. Zhang. Deep learning with differential privacy. In *Proceedings of the 2016 ACM SIGSAC Conference on Computer and Communications Security (CCS)*, 2016.
- <span id="page-9-6"></span>[2] B. K. Beaulieu-Jones, Z. S. Wu, C. Williams, and C. S. Greene. Privacy-preserving generative deep neural networks support clinical data sharing. biorxiv. *DOI*, 2017.
- <span id="page-9-11"></span>[3] E. Belouadah and A. Popescu. Il2m: Class incremental learning with dual memory. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, 2019.
- <span id="page-9-3"></span>[4] A. Blum, K. Ligett, and A. Roth. A learning theory approach to noninteractive database privacy. *Journal of the ACM (JACM)*, 2013.
- <span id="page-9-4"></span>[5] T. Cao, A. Bie, A. Vahdat, S. Fidler, and K. Kreis. Don't generate me: Training differentially private generative models with sinkhorn divergence. *Advances in Neural Information Processing Systems (NeurIPS)*, 2021.
- <span id="page-9-5"></span>[6] D. Chen, T. Orekondy, and M. Fritz. Gs-wgan: A gradient-sanitized approach for learning differentially private generators. In *Neural Information Processing Systems (NeurIPS)*, 2020.
- <span id="page-9-9"></span>[7] T. Dong, B. Zhao, and L. Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning (ICML)*, 2022.
- <span id="page-9-0"></span>[8] C. Dwork. Differential privacy: A survey of results. In *International Conference on Theory and Applications of Models of Computation (TAMC)*. Springer, 2008.
- <span id="page-9-1"></span>[9] C. Dwork, M. Naor, O. Reingold, G. N. Rothblum, and S. Vadhan. On the complexity of differentially private data release: efficient algorithms and hardness results. In *Proceedings of the forty-first annual ACM symposium on Theory of computing (STOC)*, 2009.
- <span id="page-9-10"></span>[10] C. Dwork, A. Roth, et al. The algorithmic foundations of differential privacy. *Foundations and Trends® in Theoretical Computer Science*, 9(3–4), 2014.
- <span id="page-9-2"></span>[11] B. C. Fung, K. Wang, R. Chen, and P. S. Yu. Privacy-preserving data publishing: A survey of recent developments. *ACM Computing Surveys (Csur)*, 2010.
- <span id="page-9-7"></span>[12] F. Harder, K. Adamczewski, and M. Park. Dp-merf: Differentially private mean embeddings with randomfeatures for practical privacy-preserving data generation. In *International conference on artificial intelligence and statistics (AISTAT)*. PMLR, 2021.

- <span id="page-10-1"></span>[13] M. Hardt and G. N. Rothblum. A multiplicative weights mechanism for privacy-preserving data analysis. In *IEEE 51st Annual Symposium on Foundations of Computer Science (FOCS)*, 2010.
- <span id="page-10-9"></span>[14] M. Hardt, K. Ligett, and F. McSherry. A simple and practical algorithm for differentially private data release. *Advances in neural information processing systems (NIPS)*, 2012.
- <span id="page-10-12"></span>[15] K. He, X. Zhang, S. Ren, and J. Sun. Delving deep into rectifiers: Surpassing human-level performance on imagenet classification. In *Proceedings of the IEEE international conference on computer vision*, pages 1026–1034, 2015.
- <span id="page-10-2"></span>[16] T. Karras, M. Aittala, J. Hellsten, S. Laine, J. Lehtinen, and T. Aila. Training generative adversarial networks with limited data. *Advances in Neural Information Processing Systems (NeurIPS)*, 33, 2020.
- <span id="page-10-18"></span>[17] A. Krizhevsky, G. Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-10-11"></span>[18] Y. LeCun, L. Bottou, Y. Bengio, and P. Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11), 1998.
- <span id="page-10-13"></span>[19] Z. Li and D. Hoiem. Learning without forgetting. *IEEE transactions on pattern analysis and machine intelligence (TPAMI)*, 40(12), 2017.
- <span id="page-10-8"></span>[20] Y. Liu, Y. Su, A.-A. Liu, B. Schiele, and Q. Sun. Mnemonics training: Multi-class incremental learning without forgetting. In *Proceedings of the IEEE/CVF conference on Computer Vision and Pattern Recognition (CVPR)*, 2020.
- <span id="page-10-5"></span>[21] Y. Long, B. Wang, Z. Yang, B. Kailkhura, A. Zhang, C. A. Gunter, and B. Li. G-PATE: Scalable differentially private data generator via private aggregation of teacher discriminators. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. W. Vaughan, editors, *Advances in Neural Information Processing Systems (NeurIPS)*, 2021.
- <span id="page-10-16"></span>[22] I. Mironov. Rényi differential privacy. In *IEEE 30th Computer Security Foundations Symposium (CSF)*, 2017.
- <span id="page-10-10"></span>[23] I. Mironov, K. Talwar, and L. Zhang. Rényi differential privacy of the sampled gaussian mechanism. *CoRR*, abs/1908.10530, 2019.
- <span id="page-10-17"></span>[24] I. Mironov, K. Talwar, and L. Zhang. Rényi differential privacy of the sampled gaussian mechanism. *arXiv preprint arXiv:1908.10530*, 2019.
- <span id="page-10-6"></span>[25] B. Mirzasoleiman, J. Bilmes, and J. Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning (ICML)*. PMLR, 2020.
- <span id="page-10-3"></span>[26] N. Papernot, M. Abadi, Ú. Erlingsson, I. Goodfellow, and K. Talwar. Semi-supervised knowledge transfer for deep learning from private training data. In *International Conference on Learning Representations (ICLR)*, 2017.
- <span id="page-10-4"></span>[27] N. Papernot, S. Song, I. Mironov, A. Raghunathan, K. Talwar, and U. Erlingsson. Scalable private learning with pate. In *International Conference on Learning Representations (ICLR)*, 2018.
- <span id="page-10-14"></span>[28] A. Prabhu, P. H. Torr, and P. K. Dokania. Gdumb: A simple approach that questions our progress in continual learning. In *European conference on computer vision (ECCV)*. Springer, 2020.
- <span id="page-10-15"></span>[29] A. Radford, L. Metz, and S. Chintala. Unsupervised representation learning with deep convolutional generative adversarial networks. In Y. Bengio and Y. LeCun, editors, *International Conference on Learning Representations (ICLR)*, 2016.
- <span id="page-10-7"></span>[30] S.-A. Rebuffi, A. Kolesnikov, G. Sperl, and C. H. Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition (CVPR)*, 2017.
- <span id="page-10-0"></span>[31] A. Roth and T. Roughgarden. Interactive privacy via the median mechanism. In *Proceedings of the forty-second ACM symposium on Theory of computing (STOC)*, 2010.

- <span id="page-11-13"></span>[32] J. Schwarz, W. Czarnecki, J. Luketina, A. Grabska-Barwinska, Y. W. Teh, R. Pascanu, and R. Hadsell. Progress & compress: A scalable framework for continual learning. In *International Conference on Machine Learning (ICML)*. PMLR, 2018.
- <span id="page-11-6"></span>[33] M. Toneva, A. Sordoni, R. T. d. Combes, A. Trischler, Y. Bengio, and G. J. Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-11-4"></span>[34] R. Torkzadehmahani, P. Kairouz, and B. Paten. Dp-cgan: Differentially private synthetic data and label generation. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR) Workshops*, 2019.
- <span id="page-11-3"></span>[35] F. Tramer and D. Boneh. Differentially private learning needs better features (or much more data). *arXiv preprint arXiv:2011.11660*, 2020.
- <span id="page-11-12"></span>[36] D. Ulyanov, A. Vedaldi, and V. Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016.
- <span id="page-11-14"></span>[37] D. Ulyanov, A. Vedaldi, and V. Lempitsky. Deep image prior. In *Proceedings of the IEEE conference on computer vision and pattern recognition (CVPR)*, 2018.
- <span id="page-11-0"></span>[38] G. Vietri, G. Tian, M. Bun, T. Steinke, and S. Wu. New oracle-efficient algorithms for private synthetic data release. In *International Conference on Machine Learning (ICML)*. PMLR, 2020.
- <span id="page-11-5"></span>[39] B. Wang, F. Wu, Y. Long, L. Rimanic, C. Zhang, and B. Li. Datalens: Scalable privacy preserving training via gradient compression and aggregation. In *Proceedings of the 2021 ACM SIGSAC Conference on Computer and Communications Security (CCS)*, 2021.
- <span id="page-11-8"></span>[40] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-15"></span>[41] Y.-X. Wang, B. Balle, and S. P. Kasiviswanathan. Subsampled rényi differential privacy and analytical moments accountant. In *The 22nd International Conference on Artificial Intelligence and Statistics (AISTAT)*. PMLR, 2019.
- <span id="page-11-7"></span>[42] M. Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning (ICML)*, 2009.
- <span id="page-11-11"></span>[43] H. Xiao, K. Rasul, and R. Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms, 2017.
- <span id="page-11-1"></span>[44] L. Xie, K. Lin, S. Wang, F. Wang, and J. Zhou. Differentially private generative adversarial network. *arXiv preprint arXiv:1802.06739*, 2018.
- <span id="page-11-2"></span>[45] J. Yoon, J. Jordon, and M. van der Schaar. PATE-GAN: Generating synthetic data with differential privacy guarantees. In *International Conference on Learning Representations (ICLR)*, 2019.
- <span id="page-11-16"></span>[46] A. Yousefpour, I. Shilov, A. Sablayrolles, D. Testuggine, K. Prasad, M. Malek, J. Nguyen, S. Ghosh, A. Bharadwaj, J. Zhao, G. Cormode, and I. Mironov. Opacus: User-friendly differential privacy library in PyTorch. *arXiv preprint arXiv:2109.12298*, 2021.
- <span id="page-11-17"></span>[47] F. Zenke, B. Poole, and S. Ganguli. Continual learning through synaptic intelligence. In *International Conference on Machine Learning (ICML)*. PMLR, 2017.
- <span id="page-11-10"></span>[48] B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning (ICML)*. PMLR, 2021.
- <span id="page-11-18"></span>[49] B. Zhao and H. Bilen. Dataset condensation with distribution matching, 2021.
- <span id="page-11-9"></span>[50] B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.

# Checklist

The checklist follows the references. Please read the checklist guidelines carefully for information on how to answer these questions. For each question, change the default **[TODO]** to [Yes], [No], or [N/A]. You are strongly encouraged to include a **justification to your answer**, either by referencing the appropriate section of your paper or providing a brief inline description. For example:

- Did you include the license to the code and datasets? [Yes] See Section ??.
- Did you include the license to the code and datasets? [No] The code and the data are proprietary.
- Did you include the license to the code and datasets? [N/A]

Please do not modify the questions and only use the provided macros for your answers. Note that the Checklist section does not count towards the page limit. In your paper, please delete this instructions block and only keep the Checklist section heading above along with the questions/answers below.

- 1. For all authors...
  - (a) Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? [Yes]
  - (b) Did you describe the limitations of your work? [Yes] See Section [6](#page-6-1)
  - (c) Did you discuss any potential negative societal impacts of your work? [Yes]
  - (d) Have you read the ethics review guidelines and ensured that your paper conforms to them? [Yes]
- 2. If you are including theoretical results...
  - (a) Did you state the full set of assumptions of all theoretical results? [N/A]
  - (b) Did you include complete proofs of all theoretical results? [N/A]
- 3. If you ran experiments...
  - (a) Did you include the code, data, and instructions needed to reproduce the main experimental results (either in the supplemental material or as a URL)? [No] Code will be released upon publication.
  - (b) Did you specify all the training details (e.g., data splits, hyperparameters, how they were chosen)? [Yes] See Sectio[n5](#page-4-2)
  - (c) Did you report error bars (e.g., with respect to the random seed after running experiments multiple times)? [No]
  - (d) Did you include the total amount of compute and the type of resources used (e.g., type of GPUs, internal cluster, or cloud provider)? [Yes] In supplementary
- 4. If you are using existing assets (e.g., code, data, models) or curating/releasing new assets...
  - (a) If your work uses existing assets, did you cite the creators? [N/A]
  - (b) Did you mention the license of the assets? [N/A]
  - (c) Did you include any new assets either in the supplemental material or as a URL? [N/A]
  - (d) Did you discuss whether and how consent was obtained from people whose data you're using/curating? [N/A]
  - (e) Did you discuss whether the data you are using/curating contains personally identifiable information or offensive content? [N/A]
- 5. If you used crowdsourcing or conducted research with human subjects...
  - (a) Did you include the full text of instructions given to participants and screenshots, if applicable? [N/A]
  - (b) Did you describe any potential participant risks, with links to Institutional Review Board (IRB) approvals, if applicable? [N/A]
  - (c) Did you include the estimated hourly wage paid to participants and the total amount spent on participant compensation? [N/A]

# Supplementary Materials for "Private Set Generation with Discriminative Information"

These supplementary materials include the privacy analysis  $(\& A)$ , the details of the adopted algorithms  $(\S$ B), and the details of experiment setup ( $\S$ C), and additional results and discussions ( $\S$ D). The source code is available at <https://github.com/DingfanChen/Private-Set>.

<span id="page-13-0"></span>

# A Privacy Analysis

Our privacy computation is based on the notion of Rényi-DP, which we recall as follows.

<span id="page-13-2"></span>**Definition A.1.** (Rényi Differential Privacy (RDP) [\[22\]](#page-10-16)). A randomized mechanism M is  $(\alpha, \varepsilon)$ -RDP with order  $\alpha$ , if

$$
D_{\alpha}(\mathcal{M}(\mathcal{D})||\mathcal{M}(\mathcal{D}')) = \frac{1}{\alpha - 1} \log \mathbb{E}_{x \sim \mathcal{M}(\mathcal{D})} \left[ \left( \frac{Pr[\mathcal{M}(\mathcal{D}) = x]}{Pr[\mathcal{M}(\mathcal{D}') = x]} \right)^{\alpha - 1} \right] \le \varepsilon
$$
 (9)

holds for any adjacent datasets D and D', where  $D_{\alpha}(P||Q) = \frac{1}{\alpha-1} \log \mathbb{E}_{x \sim Q}[(P(x)/Q(x))^{\alpha}]$  is the Rényi divergence of order  $\alpha > 1$  between the distributions P and Q.

To compute the privacy cost of our approach, we numerically compute  $D_{\alpha}(\mathcal{M}(\mathcal{D})||\mathcal{M}(\mathcal{D}'))$  in Definition [A.1](#page-13-2) for a range of orders  $\alpha$  [\[24,](#page-10-17) [41\]](#page-11-15) in each training step that requires access to the real gradient  $g_{\theta}^{\mathcal{D}}$ . To obtain the overall accumulated privacy cost over multiple training iterations, we use the composition properties of RDP summarized by the following theorem.

**Theorem A.1.** (Adaptive Composition of RDP [\[24\]](#page-10-17)). Let  $f : \mathcal{D} \to \mathcal{R}_1$  be  $(\alpha, \varepsilon_1)$ -RDP and  $g: \mathcal{R}_1 \times \mathcal{D} \to \mathcal{R}_2$  be  $(\alpha, \varepsilon_2)$ -RDP, then the mechanism defined as  $(X, Y)$ , where  $X \sim f(\mathcal{D})$  and  $Y \sim g(X, \mathcal{D})$ , satisfies  $(\alpha, \varepsilon_1 + \varepsilon_2)$ -RDP

In total, our private set generation (PSG) approach (shown in Algorithm 1 of the main paper) and the generator prior variant (shown in Algorithm [2\)](#page-14-0) can be regarded as a composition over  $RTK$  (i.e., the number of iterations where the real gradient is used) homogenous subsampled Gaussian mechanisms (with the subsampling ratio =  $B/N$ ) in terms of the privacy cost.

Lastly, we use the following theorem to convert  $(\alpha, \varepsilon)$ -RDP to  $(\varepsilon, \delta)$ -DP.

**Theorem A.2.** (From RDP to  $(\varepsilon, \delta)$ -DP [\[22\]](#page-10-16)). If M is a  $(\alpha, \varepsilon)$ -RDP mechanism, then M is also  $\left(\varepsilon+\frac{\log 1/\delta}{\alpha-1}\right)$  $\frac{\log 1/\delta}{\alpha-1}$ ,  $\delta$ )-DP for any  $0 < \delta < 1$ .

<span id="page-13-1"></span>

# B Algorithms

**Objective** . The distance  $\mathcal{L}_{dis}$  (in Equation 5 of the main paper) between the real and synthetic gradients is defined to be the sum of cosine distance at each layer [\[50,](#page-11-9) [48\]](#page-11-10). Let  $\theta^l$  denote the weight at the l-th layer, the distance can be formularized as follows,

$$
\mathcal{L}_{dis}(\nabla_{\theta}\mathcal{L}(\mathcal{S},\theta_t), \nabla_{\theta}\mathcal{L}(\mathcal{D},\theta_t)) = \sum_{l=1}^{L} d(\nabla_{\theta^l}\mathcal{L}(\mathcal{S},\theta_t), \nabla_{\theta^l}\mathcal{L}(\mathcal{D},\theta_t))
$$

where d denotes the cosine distance between the gradients at each layer:

$$
d(\boldsymbol{A},\boldsymbol{B})=\sum_{i=1}^{out}\left(1-\frac{\boldsymbol{A}_{i\cdot}\cdot\boldsymbol{B}_{i\cdot}}{\|\boldsymbol{A}_{i\cdot}\|\|\boldsymbol{B}_{i\cdot}\|}\right)
$$

 $A_i$  and  $B_i$  are the flattened gradient vectors to each output node i. For FC layers,  $\theta^l$  is a 2D tensor with dimension out  $\times$  in and the flattened gradient vector has dimension in, while for Conv layer,  $\theta^l$  is a 4D tensor with dimensionality out  $\times in \times h \times w$  and the flattened vector has dimension  $in \times h \times w$ . *out,in, h, w* corresponds to the number of output and input channels, kernel height, and width, respectively.

Generator Prior . We present the pseudocode of the generator prior experiments (Section 6 of the main paper) in Algorithm [2,](#page-14-0) which is supplementary to Figure 4,5 and Equation 8 of the main paper.

<span id="page-14-0"></span>**Input:** Dataset  $\mathcal{D} = \{ (x_i, y_i) \}_{i=1}^N$ , learning rate for update network parameters  $\tau_{\theta}$  and  $\tau_{\varphi}$ , batch size B, DP noise scale  $\sigma$ , gradient clipping bound C, number of runs R, outer iterations T, inner iterations J, batches K, number of classes L, number of samples per class (spc), desired privacy cost  $\varepsilon$  given a pre-defined  $\delta$ **Output:** Synthetic set  $S$ Compute the DP noise scale  $\sigma$  numerically so that the privacy cost equals to  $\varepsilon$  after the training; Initialize model parameter  $\varphi$  of the conditional generator  $G$ ; for *c* in  $\{1, ..., L\}$  do for *sample\_index* in spc do  $y_i^{\mathcal{S}}=c$ ; Sample  $z_i \sim \mathcal{N}(0, I)$  ( $z_i$  is fixed for each corresponding synthetic sample during the training) ;  $\boldsymbol{x}^{\mathcal{S}} = G(\boldsymbol{z}_i, y^{\mathcal{S}}_i; \, \boldsymbol{\varphi});$ Insert  $(x_i^{\mathcal{S}}, y_i^{\mathcal{S}})$  into  $\mathcal{S}$ ; end end for *run* in  $\{1, ..., R\}$  do Initialize model parameter  $\theta_0 \sim P_{\theta_0}$ ; for *outer\_iter* in  $\{1, ..., T\}$  do  $\boldsymbol{\theta}_{t+1} = \boldsymbol{\theta}_t$ for *batch\_index* in  $\{1, ..., K\}$  do Uniformly sample random batch  $\{(x_i, y_i)\}_{i=1}^B$  from  $\mathcal{D}$ ; for  $\mathit{each}\left(\boldsymbol{x}_{i},y_{i}\right)$  do // Compute per-example gradients on real data  $g_{{\boldsymbol{\theta}}_t}^{\mathcal{D}}({\boldsymbol{x}}_i) = \ell(F({\boldsymbol{x}}_i;{\boldsymbol{\theta}}_t), y_i)$ // Clip gradients  $g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i) = g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i) \cdot \min(1,C/\|g_{{\bm{\theta}}_t}^{\mathcal{D}}({\bm{x}}_i)\|_2)$ end // Add noise to average gradient with Gaussian mechanism  $g_{\theta_t}^{\mathcal{D}} = \frac{1}{B}\sum_{i=1}^B (g_{\theta_t}^{\mathcal{D}}(\boldsymbol{x}_i) + \mathcal{N}(0, \sigma^2 C^2 I))$ // Compute parameter gradients on synthetic data and update  $G$  $g_{{\bm{\theta}}_t}^{\mathcal{S}} = \nabla_{\bm{\theta}} \mathcal{L}({\mathcal{S}}, {\bm{\theta}}_t)) = \frac{1}{M} \sum_{i=1}^M \ell(F({\bm{x}}_i^{\mathcal{S}}; {\bm{\theta}}_t), y_i^{\mathcal{S}})$  where  ${\bm{x}}_i^{\mathcal{S}} = G({\bm{z}}_i, y_i^{\mathcal{S}}; {\bm{\varphi}})$  $\boldsymbol{\varphi} = \boldsymbol{\varphi} - \tau_{\boldsymbol{\varphi}} \cdot \nabla_{\boldsymbol{\varphi}} \mathcal{L}_{\mathrm{dis}}(g_{{\boldsymbol{\theta}}_t}^{\mathcal{S}}, g_{{\boldsymbol{\theta}}_t}^{\mathcal{D}})$ end end for *inner\_iter* in  $\{1, ..., J\}$  do // Update network parameter using  $S$  $\begin{array}{l} \mathcal{S} = \{G(\boldsymbol{z}_i, y^{\mathcal{S}}_i; \boldsymbol{\varphi}), y^{\mathcal{S}}_i\}_{i=1}^M\ \boldsymbol{\theta}_t = \boldsymbol{\theta}_t - \tau_{\boldsymbol{\theta}}\cdot\nabla_{\boldsymbol{\theta}}\mathcal{L}(\mathcal{S}, \boldsymbol{\theta}_t) \end{array}$ end end return Synthetic set S

The only difference to the original PSG formulation is that the samples are restricted to be the output of a generator network and the updates are conducted on the generator network parameters (See Figure [6](#page-15-1) for the illustration and see Figure 1 in the main paper for a comparison). Note that we fix the random latent code  $z<sub>i</sub>$  during the whole training process to guarantee that there is no other randomness/degree of freedom except that introduced by the generator network itself. While it is possible to allow random sampling of the latent code and generate change-

<span id="page-15-1"></span>Image /page/15/Figure/1 description: This is a diagram illustrating a machine learning model. The diagram shows two main processing paths. The top path involves a generator network 'G' that takes input 'z' and outputs 'S'. 'S' is then processed by a network 'F' which outputs a loss 'L' and a value 'g\_theta\_t^S'. This 'g\_theta\_t^S' is used in a loss function 'L\_dis'. The bottom path takes data 'x, y ~ P\_D' and processes it through a network 'F' which outputs a loss 'L' and a value 'g\_theta\_t^D'. This 'g\_theta\_t^D' is then processed by 'M\_sigma,C' to produce '~g\_theta\_t^D'. The diagram also shows feedback loops and parameter updates indicated by different colored dotted arrows: yellow for the inner loop, green for the outer loop, red for sensitive data, and blue for (epsilon, delta)-private information. There are also inputs 'phi' to the generator 'G' and 'theta\_t' to the network 'F'.

Figure 6: Training pipeline (with prior).

able  $S$  to mimic the training of generative models (i.e., train a generative network using the gradient matching loss), we observe that the training easily fails in the early stage. We argue that this also indicates that training a generative network is a harder task than training a set of samples directly, which explains the better convergence behavior and superior final performance of our formulation in comparison to existing works (which build on top of deep generative networks).

<span id="page-15-0"></span>

# C Experiment Setup

## C.1 Datasets

**MNIST** [\[18\]](#page-10-11) dataset contains  $28 \times 28$  grayscale images of digit numbers. The dataset comprises 60K training images and 10K testing images in total. The task is to classify the image into one of the 10 classes based on the digit number it contains.

**Fashion-MNIST** [\[43\]](#page-11-11) dataset consists of  $28 \times 28$  grayscale images fashion products of 10 categories. The total dataset size is 60K for the training set and 10K for the testing set, respectively. The task is to classify the fashion product given in the images.

<span id="page-15-3"></span>

## C.2 Required Resources and Computational Complexity

All our models and methods are implemented in PyTorch. Our experiments are conducted with Nvidia Tesla V100 and Quadro RTX8000 GPUs and a common configuration with 16GB GPU memory is sufficient for conducting all our experiments.

In comparison to normal non-private training, the major part of the additional memory and computation cost is introduced by the DP-SGD [\[1\]](#page-9-8) step (for the per-sample gradient computation) that sanitizes the parameter gradient on real data, while the other steps (including the update on  $S$ , and the updates of  $F(\cdot; \theta)$  on S are equivalent to multiple calls of the normal non-private forward and backward passes (whose costs have lower magnitude than the DP-SGD step). Moreover, our formulation requires much less computational and memory consumption than previous works that require training multiple instances of the generative modules [\[6,](#page-9-5) [21,](#page-10-5) [39\]](#page-11-5).

<span id="page-15-2"></span>

## C.3 Hyperparameters

**Training.** We set the default value of hyperparameters as follows: batch size  $= 256$  for both computing the parameter gradients in the outer iterations and for update the classifier  $F$  in the inner iterations, gradient clipping bound  $C = 0.1$ ,  $R = 1000$  for  $\varepsilon = 10$  (and  $R = 200$  for  $\varepsilon = 1$ ),  $K = 10$ . The number of inner J and outer T iterations are dependent on the number of samples per class (spc), as more samples generally requires more iterations till convergence:  $(T, J)$  is set to be  $(1, 1)$ ,  $(10, 50)$ ,  $(20, 25)$  and  $(50, 10)$  for  $spec = 1, 10, 20, 50$ , respectively. The DP noise scale  $\sigma$ is calculated numerically<sup>[1](#page-0-0)</sup> [\[1,](#page-9-8) [24\]](#page-10-17) so that the privacy cost equals to  $\varepsilon$  after the training (with  $RTK$ steps in total that consume privacy budget), given that  $\delta = 10^{-5}$ . The learning rate is set to be  $\tau_{\theta} = 0.01$  (and  $\tau_{\varphi} = 0.01$  for training with generator prior) and  $\tau_{\varsigma} = 0.1$  for updating the network parameters and samples, respectively. We use SGD optimizer for the classifier  $F$ , and samples  $S$ 

<sup>&</sup>lt;sup>1</sup> Based on Google's TensorFlow privacy under version  $\leq$  0.8.0: [https://github.com/tensorflow/](https://github.com/tensorflow/privacy/blob/master/tensorflow_privacy/privacy/analysis/rdp_accountant.py) [privacy/blob/master/tensorflow\\_privacy/privacy/analysis/rdp\\_accountant.py](https://github.com/tensorflow/privacy/blob/master/tensorflow_privacy/privacy/analysis/rdp_accountant.py)

(with momentum=  $(0.5)$ , while we use Adam optimizer for the generator G if trained with prior. For the training process, no data augmentation is adopted. Our implementation of the DP-SGD step and the uniform data sampling operation is based on the Opacus  $\left[46\right]$ <sup>[2](#page-0-0)</sup> package.

Evaluation. We set the epoch to be 40 and 300 when training the downstream classification models on the synthetic data with "full" size (spc = 6000) and small size (spc  $\in \{1, 10, 20, 50\}$ ), respectively, to guarantee the convergence of downstream model training and maintain the evaluation efficiency. We set the learning rate to be 0.01 at the beginning and decrease it (by multiplying with  $0.1$ ) when half of the total epoch is achieved. We use SGD optimizer with momentum=  $0.9$ , weight decay=  $5 \cdot 10^{-4}$  and set batch size = 256 for training the classifier. Random cropping and re-scaling are adopted as data augmentation when training the classification model.

<span id="page-16-0"></span>

## C.4 Baseline Methods

We present more details about the implementation of the baseline methods. In particular, we provide the default value of the privacy hyperparameters below.

**DP-Merf** [\[12\]](#page-9-7) <sup>[3](#page-0-0)</sup> For  $\varepsilon = 1$  we use as the default hyperparameters setting provided in the official implementation: DP noise scale  $\sigma = 5.0$ , training epoch = 5, while for  $\varepsilon = 10$ , the DP noise scale is  $\sigma = 0.568$ .

**DP-CGAN** [\[34\]](#page-11-4) <sup>[4](#page-0-0)</sup> We set the default hyper-parameters as follows: gradient clipping bound  $C = 1.1$ , noise scale  $\sigma = 2.1$ , batch size= 600 and total number of training iterations= 30K. We exclude this model from evaluation at  $\varepsilon = 1$  as the required noise scale is too large for the training to make progress, which is consistent with the results in literature [\[12,](#page-9-7) [6\]](#page-9-5).

**GS-WGAN**  $\begin{bmatrix} 6 \end{bmatrix}$  <sup>[5](#page-0-0)</sup> We adopt the default configuration provided by the official implementation  $(\varepsilon = 10)$ : the subsampling rate = 1/1000, DP noise scale  $\sigma = 1.07$ , batch size = 32. Following [\[6\]](#page-9-5), we pretrain (warm-start) the model for  $2K$  iterations, and subsequently train for  $20K$  iterations. Similar to the case for DP-CGAN, we exclude this model from evaluation at  $\varepsilon = 1$  as the required noise scale is too large for the training to be stable.

For G-PATE [\[21\]](#page-10-5), DataLens [\[39\]](#page-11-5) and DP-Sinkhorn [\[5\]](#page-9-4), we present the same results as reported in the original papers (in Table 1 of the main paper) as reference, as they are either not directly comparable to ours or not open-sourced.

## C.5 Private Continual Learning

Setting. The experiments presented in Section 5.2 of the main paper correspond to the classincremental learning setting  $[30]$  where the data partition at each stage contains data from disjoint subsets of label classes. And the task protocol is sequentially learning to classify a given sample into all the classes seen so far. For our experiments on SplitMNIST and SplitFashionMNIST benchmarks [\[47\]](#page-11-17), the datasets are split into 5 partitions each containing samples of 2 label classes. The evaluation task is thus binary classification for the first stage, while two more classes are included after each following stage.

While a clear definition of the private continual learning setting is, to the best of our knowledge, missing in the literature, we introduce a basic case where privacy can be strictly protected during the whole training process. In brief, we need to guarantee that all the information that is delivered to another party/stage should be privacy-preserving.

Hence, for the **DP-SGD** [\[1\]](#page-9-8) baseline, the classification model is initialized to be a 10-class classifier, and is updated (fine-tuned) via DP-SGD at each training stage on each data partition. During the whole process, the model is transferred between different parties while privacy is guaranteed by DP-SGD training.

 $\overline{2}$ <https://opacus.ai/>

<sup>3</sup> <https://github.com/frhrdr/dp-merf>

<sup>4</sup> <https://github.com/reihaneh-torkzadehmahani/DP-CGAN>

<sup>5</sup> <https://github.com/DingfanChen/GS-WGAN>

And for the private generation methods, i.e., **DP-Merf** and **Ours**, we use a fixed privacy budget to train a private generative model or a private synthetic set for each partition/stage. Subsequently, such a generative model or synthetic set is transferred between parties for conducting different training stages. For evaluation, a  $n$ -class classifier is initialized and then trained on the transferred private synthetic samples for each stage, where  $n$  is the total number of label classes seen so far. In our experiments, both methods only exploit information from the local partition for the generation, i.e., our private set is optimized on a freshly initialized classification network at each stage and for DP-Merf the mean embedding is taken over the local partition. While our formulation can be adjusted to (and may be further improved by) more advanced training strategies designed for continual learning to eliminate forgetting, many of such strategies are not directly compatible with private training as they require access to old data. We believe that our introduced private continual learning setting is of independent interest and leave an in-depth investigation of this topic as future work.

Hyperparameters. We use the default values for the hyperparameters as shown in Section  $C<sub>13</sub>$  and [C.4,](#page-16-0) except that the training epoch is set to be 10 for **DP-SGD** and the runs  $R = 200$  for **Ours**, to balance the convergence, forgetting effect, and evaluation efficiency. Moreover, the DP noise scale is calibrated to each *partition* of the data.

<span id="page-17-0"></span>

# D Additional Results and Discussions

## D.1 Dataset Distillation Basis

In this paper, we propose to use the gradient matching technique  $[50, 48]$  $[50, 48]$  $[50, 48]$  (among existing dataset distillation approaches) as a basis for private set generation. In the following, we briefly discuss other popular dataset condensation approaches that achieve competitive performance for non-private tasks but appear less suitable for private learning. For example, [\[40\]](#page-11-8) requires solving a nested optimization problem, which makes it hard to quantify the individual's effect (i.e., the sensitivity) and thus difficult to impose DP into the training. In addition, [\[49\]](#page-11-18) relies on "per-class" feature aggregation as the only source of supervision to guide the synthetic data towards representing its target label class. However, this "per-class" operation contradicts label privacy and the requirement of uniform sampling for the privacy cost computation. In contrast, our formulation adopts uniform sampling (which is compatible with DP) and exploits the (inherently class-dependent) gradient signals to generate representative samples.

## D.2 Computation Time

Under the default setting (See Section [C.2](#page-15-3) and [C.3\)](#page-15-2), it takes around 4.5 hours and 11 hours to train the synthetic data for the case of  $spc = 10$  and  $spc = 20$ , respectively. To the best of our knowledge, our method is more efficient than existing works that require pre-training of (multiple) models [\[6,](#page-9-5) [21\]](#page-10-5), but requires more running time than methods that use static pre-computed features [\[12\]](#page-9-7). Moreover, we see a tendency that the distilled dataset requires less time on downstream tasks compared to samples from generative models due to the smaller (distilled) sample size.

## D.3 Evaluation on Colored Images

In this section, we provide additional evaluation results on colored image benchmark dataset. On CIFAR-10 [\[17\]](#page-10-18) dataset, We use the same default setting as described in Section [C.3](#page-15-2) and adjust the network architectures to the input dimension  $(32 \times 32 \times 3)$ . We summarize in Figure [5](#page-17-1) the quantitative results of downstream utility when varying the number of samples per class ( $spc \in \{1, 10, 20\}$ )

<span id="page-17-1"></span>

|                    | 1    | 10   | 20   |
|--------------------|------|------|------|
| non-private        | 30.0 | 48.6 | 52.6 |
| $\varepsilon = 10$ | 28.9 | 40.3 | 42.6 |

Table 5: Test accuracy (%) on real data of downstream ConvNet classifier on CIFAR-10.

and show as reference the results when training non-privately (We show here the results when applying uniform sampling of the data instead of the original per-class sampling approach [\[50,](#page-11-9) [48\]](#page-11-10) also for the non-private baseline for controlled comparison). Additionally, we show in Figure [7](#page-18-0) the synthetic images when training under DP ( $\varepsilon = 10$ ), and in [8](#page-18-1) the results when training non-privately. We observe that while the synthetic samples look noisy and non-informative, they do provide useful features for downstream classifiers, leading to a decent level of performance. Note that colored

images are generally challenging for private learning. In fact, this makes our work the first one that is able to report non-trivial performance on this dataset.

<span id="page-18-0"></span>Image /page/18/Figure/1 description: Figure 7 shows CIFAR-10 data with epsilon equal to 10. The figure is divided into two sections. The top section displays a grid of small, noisy images, likely representing features or filters learned by a model. The bottom section displays a grid of clearer, more recognizable images, also likely from the CIFAR-10 dataset, arranged in rows and columns.

at aft Figure 8: CIFAR-10 (non-private)

**La** 

<span id="page-18-1"></span>ā