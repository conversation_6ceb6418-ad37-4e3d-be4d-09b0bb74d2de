{"table_of_contents": [{"title": "Private Set Generation with Discriminative\nInformation", "heading_level": null, "page_id": 0, "polygon": [[145.5, 99.0], [465.0, 97.5], [465.0, 136.2216796875], [145.5, 136.2216796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 239.25], [328.5, 239.25], [328.5, 250.013671875], [282.75, 250.013671875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.5, 428.25], [191.6982421875, 428.25], [191.6982421875, 439.3125], [106.5, 439.3125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[107.25, 338.25], [198.720703125, 338.25], [198.720703125, 349.20703125], [107.25, 349.20703125]]}, {"title": "3 Background", "heading_level": null, "page_id": 1, "polygon": [[106.5, 642.75], [189.75, 642.75], [189.75, 653.5546875], [106.5, 653.5546875]]}, {"title": "4 Method", "heading_level": null, "page_id": 2, "polygon": [[107.25, 327.0], [166.5, 327.0], [166.5, 337.9921875], [107.25, 337.9921875]]}, {"title": "Algorithm 1: Private Set Generation (PSG)", "heading_level": null, "page_id": 3, "polygon": [[107.05517578125, 327.0], [282.990234375, 327.0], [282.990234375, 337.60546875], [107.05517578125, 337.60546875]]}, {"title": "Output: Synthetic set S", "heading_level": null, "page_id": 3, "polygon": [[106.8310546875, 375.0], [207.2373046875, 375.0], [207.2373046875, 384.3984375], [106.8310546875, 384.3984375]]}, {"title": "5 Experiment", "heading_level": null, "page_id": 4, "polygon": [[106.5, 285.75], [187.5, 285.75], [187.5, 297.580078125], [106.5, 297.580078125]]}, {"title": "5.1 Classification", "heading_level": null, "page_id": 4, "polygon": [[106.5, 311.25], [188.1123046875, 311.25], [188.1123046875, 321.75], [106.5, 321.75]]}, {"title": "5.2 Application: Private Continual Learning", "heading_level": null, "page_id": 5, "polygon": [[106.30810546875, 680.25], [306.0, 680.25], [306.0, 691.06640625], [106.30810546875, 691.06640625]]}, {"title": "6 Discussion", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 612.75], [181.08984375, 612.75], [181.08984375, 624.55078125], [107.1298828125, 624.55078125]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 573.890625], [184.078125, 573.890625], [184.078125, 586.265625], [106.5, 586.265625]]}, {"title": "Broader Impact", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.75], [191.25, 72.75], [191.25, 84.0146484375], [106.5, 84.0146484375]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 9, "polygon": [[107.25, 212.25], [203.3525390625, 212.25], [203.3525390625, 223.13671875], [107.25, 223.13671875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 300.8671875], [165.55078125, 300.8671875], [165.55078125, 311.6953125], [107.25, 311.6953125]]}, {"title": "Checklist", "heading_level": null, "page_id": 12, "polygon": [[107.25, 72.0], [157.5, 72.0], [157.5, 83.6279296875], [107.25, 83.6279296875]]}, {"title": "Supplementary Materials for \"Private Set Generation\nwith Discriminative Information\"", "heading_level": null, "page_id": 13, "polygon": [[106.5, 99.0], [505.01953125, 99.0], [505.01953125, 136.705078125], [106.5, 136.705078125]]}, {"title": "A Privacy Analysis", "heading_level": null, "page_id": 13, "polygon": [[107.25, 255.0], [214.7080078125, 255.0], [214.7080078125, 266.44921875], [107.25, 266.44921875]]}, {"title": "B Algorithms", "heading_level": null, "page_id": 13, "polygon": [[106.5, 579.75], [186.75, 579.75], [186.75, 590.51953125], [106.5, 590.51953125]]}, {"title": "C Experiment Setup", "heading_level": null, "page_id": 15, "polygon": [[107.1298828125, 285.205078125], [223.6728515625, 285.205078125], [223.6728515625, 297.966796875], [107.1298828125, 297.966796875]]}, {"title": "C.1 Datasets", "heading_level": null, "page_id": 15, "polygon": [[107.25, 310.1484375], [169.5, 310.1484375], [169.5, 320.9765625], [107.25, 320.9765625]]}, {"title": "C.2 Required Resources and Computational Complexity", "heading_level": null, "page_id": 15, "polygon": [[107.25, 414.5625], [356.203125, 414.5625], [356.203125, 425.390625], [107.25, 425.390625]]}, {"title": "C.3 Hyperparameters", "heading_level": null, "page_id": 15, "polygon": [[107.25, 561.90234375], [210.076171875, 561.90234375], [210.076171875, 572.73046875], [107.25, 572.73046875]]}, {"title": "C.4 Baseline Methods", "heading_level": null, "page_id": 16, "polygon": [[106.5, 204.0], [209.77734375, 204.0], [209.77734375, 215.208984375], [106.5, 215.208984375]]}, {"title": "C.5 Private Continual Learning", "heading_level": null, "page_id": 16, "polygon": [[107.25, 456.75], [252.3603515625, 456.75], [252.3603515625, 467.15625], [107.25, 467.15625]]}, {"title": "D Additional Results and Discussions", "heading_level": null, "page_id": 17, "polygon": [[106.5, 272.25], [307.5, 272.25], [307.5, 284.044921875], [106.5, 284.044921875]]}, {"title": "D.1 Dataset Distillation Basis", "heading_level": null, "page_id": 17, "polygon": [[106.5, 296.25], [239.25, 296.25], [239.25, 307.0546875], [106.5, 307.0546875]]}, {"title": "D.2 Computation Time", "heading_level": null, "page_id": 17, "polygon": [[106.5, 450.75], [214.5, 450.75], [214.5, 460.58203125], [106.5, 460.58203125]]}, {"title": "D.3 Evaluation on Colored Images", "heading_level": null, "page_id": 17, "polygon": [[106.5, 549.75], [262.5, 549.75], [262.5, 559.58203125], [106.5, 559.58203125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 46], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6213, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 57], ["Text", 3], ["ListItem", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 879], ["Line", 72], ["TextInlineMath", 10], ["Equation", 4], ["Reference", 4], ["Text", 3], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 811, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 764], ["Line", 66], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 298], ["TableCell", 221], ["Line", 60], ["Text", 4], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3098, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["TableCell", 162], ["Line", 73], ["Caption", 4], ["Text", 4], ["Reference", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6923, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 86], ["Text", 6], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1085, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 370], ["Line", 87], ["Caption", 6], ["Figure", 2], ["Text", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1785, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["TableCell", 73], ["Line", 58], ["Text", 6], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 8551, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 46], ["ListItem", 12], ["Reference", 12], ["SectionHeader", 3], ["Text", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 47], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 45], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 48], ["ListItem", 26], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 605], ["Line", 55], ["TextInlineMath", 9], ["SectionHeader", 3], ["Equation", 3], ["Reference", 3], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 826], ["Line", 50], ["TextInlineMath", 2], ["Text", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 470], ["Line", 68], ["Text", 5], ["SectionHeader", 4], ["Reference", 4], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 53], ["Text", 8], ["Footnote", 4], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 57], ["TableCell", 24], ["Text", 4], ["SectionHeader", 4], ["TextInlineMath", 3], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1024, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 20], ["Line", 10], ["Text", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 617, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Private_Set_Generation_with_Discriminative_Information"}