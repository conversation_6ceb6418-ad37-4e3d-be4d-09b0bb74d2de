{"table_of_contents": [{"title": "Progressive trajectory matching for medical dataset distillation", "heading_level": null, "page_id": 0, "polygon": [[111.75, 89.25], [499.5, 89.25], [499.5, 101.**********], [111.75, 101.**********]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[152.25, 216.75], [198.75, 216.75], [198.75, 227.00390625], [152.25, 227.00390625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[52.5, 612.75], [137.**********, 612.75], [137.**********, 624.1640625], [52.5, 624.1640625]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 1, "polygon": [[52.5, 642.33984375], [148.74169921875, 642.33984375], [148.74169921875, 653.94140625], [52.5, 653.94140625]]}, {"title": "3 Methods", "heading_level": null, "page_id": 2, "polygon": [[52.5, 79.5], [117.75, 79.5], [117.75, 90.685546875], [52.5, 90.685546875]]}, {"title": "3.1 Overall Framework", "heading_level": null, "page_id": 2, "polygon": [[52.5, 95.25], [171.75, 95.25], [171.75, 106.**********], [52.5, 106.**********]]}, {"title": "3.2 Progressive Trajectory Matching", "heading_level": null, "page_id": 2, "polygon": [[52.5, 624.0], [232.5, 624.0], [232.5, 635.37890625], [52.5, 635.37890625]]}, {"title": "3.3 Dynamic Retraining and Overlap Mitigation", "heading_level": null, "page_id": 2, "polygon": [[313.5, 522.0], [548.25, 522.0], [548.25, 531.75], [313.5, 531.75]]}, {"title": "3.4 Overall Optimization", "heading_level": null, "page_id": 3, "polygon": [[52.5, 664.5], [179.**********, 664.5], [179.**********, 675.59765625], [52.5, 675.59765625]]}, {"title": "Experiments", "heading_level": null, "page_id": 3, "polygon": [[317.35546875, 112.5], [400.4296875, 112.5], [400.4296875, 123.943359375], [317.35546875, 123.943359375]]}, {"title": "4.1 Benchmark and Datasets", "heading_level": null, "page_id": 3, "polygon": [[313.5, 132.0], [456.75, 132.0], [456.75, 142.3125], [313.5, 142.3125]]}, {"title": "High-Resolution Medical Benchmark", "heading_level": null, "page_id": 3, "polygon": [[313.5, 353.25], [474.75, 353.25], [474.75, 362.935546875], [313.5, 362.935546875]]}, {"title": "Mnist 2D/3D Medical Benchmark", "heading_level": null, "page_id": 3, "polygon": [[313.5, 537.75], [459.0, 537.75], [459.0, 547.98046875], [313.5, 547.98046875]]}, {"title": "4.2 Baselines and Models", "heading_level": null, "page_id": 4, "polygon": [[52.5, 367.3828125], [179.25, 367.3828125], [179.25, 377.82421875], [52.5, 377.82421875]]}, {"title": "4.3 Implementation Details", "heading_level": null, "page_id": 4, "polygon": [[52.5, 612.75], [189.0, 612.75], [189.0, 623.390625], [52.5, 623.390625]]}, {"title": "4.4 Results", "heading_level": null, "page_id": 4, "polygon": [[313.5, 433.5], [375.0, 433.5], [375.0, 444.33984375], [313.5, 444.33984375]]}, {"title": "5 Analysis", "heading_level": null, "page_id": 5, "polygon": [[52.5, 615.75], [116.25, 615.75], [116.25, 627.2578125], [52.5, 627.2578125]]}, {"title": "5.1 Quantitative Analysis", "heading_level": null, "page_id": 5, "polygon": [[52.5, 632.25], [181.6875, 632.25], [181.6875, 643.88671875], [52.5, 643.88671875]]}, {"title": "Cross-Architecture Generalization.", "heading_level": null, "page_id": 5, "polygon": [[52.5, 647.75390625], [204.0, 647.75390625], [204.0, 658.58203125], [52.5, 658.58203125]]}, {"title": "Time and Memory Complexity Evaluation", "heading_level": null, "page_id": 5, "polygon": [[313.5, 615.75], [496.5, 615.75], [496.5, 626.09765625], [313.5, 626.09765625]]}, {"title": "5.2 Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[51.75, 269.25], [149.25, 268.5], [149.25, 279.59765625], [52.5, 279.59765625]]}, {"title": "5.3 Qualitative Analysis", "heading_level": null, "page_id": 6, "polygon": [[313.5, 467.25], [435.0, 465.75], [435.0, 477.59765625], [313.5, 477.75]]}, {"title": "6 Conclusions", "heading_level": null, "page_id": 6, "polygon": [[313.470703125, 634.5], [396.0, 634.5], [396.0, 645.8203125], [313.470703125, 645.8203125]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[52.5, 54.38232421875], [110.25, 54.38232421875], [110.25, 66.17724609375], [52.5, 66.17724609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 260], ["Line", 107], ["Text", 6], ["SectionHeader", 3], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6220, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 114], ["Text", 9], ["ListItem", 4], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 886], ["Line", 197], ["Text", 10], ["SectionHeader", 4], ["TextInlineMath", 4], ["Equation", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["Line", 127], ["SectionHeader", 5], ["Text", 4], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 697, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1168], ["TableCell", 273], ["Line", 81], ["Text", 6], ["SectionHeader", 3], ["Caption", 2], ["Table", 2], ["Reference", 2], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6405, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1133], ["TableCell", 166], ["Line", 106], ["Text", 6], ["SectionHeader", 4], ["Reference", 4], ["Caption", 3], ["Table", 2], ["Figure", 2], ["FigureGroup", 2], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 10906, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 780], ["Line", 256], ["TableCell", 47], ["Text", 7], ["Caption", 4], ["Reference", 4], ["SectionHeader", 3], ["Figure", 3], ["FigureGroup", 3], ["Table", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 13076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 388], ["Line", 107], ["ListItem", 23], ["Reference", 23], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 42], ["ListItem", 11], ["Reference", 11], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Progressive_Trajectory_Matching_for_Medical_Dataset_Distillation"}