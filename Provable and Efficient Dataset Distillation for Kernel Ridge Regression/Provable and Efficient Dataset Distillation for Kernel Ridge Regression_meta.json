{"table_of_contents": [{"title": "Provable and Efficient Dataset Distillation\nfor Kernel Ridge Regression", "heading_level": null, "page_id": 0, "polygon": [[149.25, 99.75], [462.0, 99.75], [462.0, 136.125], [149.25, 136.125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 243.0], [329.30859375, 243.0], [329.30859375, 254.07421875], [282.75, 254.07421875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 497.70703125], [192.0, 497.70703125], [192.0, 509.30859375], [107.25, 509.30859375]]}, {"title": "2 Related works", "heading_level": null, "page_id": 1, "polygon": [[106.5, 632.28515625], [201.2607421875, 632.28515625], [201.2607421875, 644.66015625], [106.5, 644.66015625]]}, {"title": "3 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[106.5, 248.25], [195.75, 248.25], [195.75, 259.1015625], [106.5, 259.1015625]]}, {"title": "3.1 Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[106.5, 270.75], [212.25, 270.75], [212.25, 280.37109375], [106.5, 280.37109375]]}, {"title": "3.2 Dataset Distillation for Kernel Ridge Regression (KRR)", "heading_level": null, "page_id": 2, "polygon": [[106.5, 453.75], [367.5, 453.75], [367.5, 464.0625], [106.5, 464.0625]]}, {"title": "4 Dataset Distillation for Linear Ridge Regression (LRR)", "heading_level": null, "page_id": 3, "polygon": [[106.98046875, 111.0], [408.0, 111.0], [408.0, 122.58984375], [106.98046875, 122.58984375]]}, {"title": "4.1 Analytical Computation for Linear Ridge Regression", "heading_level": null, "page_id": 3, "polygon": [[106.681640625, 214.5], [355.5, 214.5], [355.5, 225.0703125], [106.681640625, 225.0703125]]}, {"title": "4.2 Finding Realistic Distilled Data", "heading_level": null, "page_id": 4, "polygon": [[107.25, 161.25], [264.0, 161.25], [264.0, 171.0263671875], [107.25, 171.0263671875]]}, {"title": "4.3 Label Distillation", "heading_level": null, "page_id": 4, "polygon": [[106.5, 666.75], [205.5, 666.75], [205.5, 676.7578125], [106.5, 676.7578125]]}, {"title": "5 Dataset Distillation for Kernel Ridge Regression (KRR)", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 146.25], [409.5, 146.25], [409.5, 157.587890625], [106.98046875, 157.587890625]]}, {"title": "5.1 Surjective Feature Mapping", "heading_level": null, "page_id": 5, "polygon": [[106.5, 474.75], [251.25, 474.75], [251.25, 484.9453125], [106.5, 484.9453125]]}, {"title": "5.2 Non-surjective Feature Mapping", "heading_level": null, "page_id": 6, "polygon": [[106.5, 441.75], [270.0, 441.75], [270.0, 452.07421875], [106.5, 452.07421875]]}, {"title": "6 Applications", "heading_level": null, "page_id": 7, "polygon": [[106.90576171875, 273.75], [190.951171875, 273.75], [190.951171875, 285.3984375], [106.90576171875, 285.3984375]]}, {"title": "6.1 An Implication for KIP-type Algorithms", "heading_level": null, "page_id": 7, "polygon": [[106.5, 354.75], [303.75, 354.75], [303.75, 364.869140625], [106.5, 364.869140625]]}, {"title": "6.2 Privacy Preservation of Dataset Distillation", "heading_level": null, "page_id": 7, "polygon": [[106.5, 558.0], [315.0, 558.0], [315.0, 568.08984375], [106.5, 568.08984375]]}, {"title": "7 Experiments", "heading_level": null, "page_id": 8, "polygon": [[106.5, 220.5], [192.0, 220.5], [192.0, 232.41796875], [106.5, 232.41796875]]}, {"title": "8 Conclusion and Future Works", "heading_level": null, "page_id": 9, "polygon": [[106.5, 528.64453125], [281.9443359375, 528.64453125], [281.9443359375, 540.24609375], [106.5, 540.24609375]]}, {"title": "Acknowledgement", "heading_level": null, "page_id": 10, "polygon": [[106.5, 72.75], [204.0, 72.75], [204.0, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 145.5], [164.25, 145.5], [164.25, 156.5244140625], [107.25, 156.5244140625]]}, {"title": "Appendices", "heading_level": null, "page_id": 13, "polygon": [[105.0, 69.75], [210.0, 69.75], [210.0, 91.3623046875], [105.0, 91.3623046875]]}, {"title": "A Broader Impact", "heading_level": null, "page_id": 13, "polygon": [[106.5, 470.25], [211.5, 470.25], [211.5, 483.3984375], [106.5, 483.3984375]]}, {"title": "B Additional Experiment Details", "heading_level": null, "page_id": 13, "polygon": [[106.5, 533.28515625], [285.0, 533.28515625], [285.0, 545.66015625], [106.5, 545.66015625]]}, {"title": "C Proofs for Linear Ridge Regularization", "heading_level": null, "page_id": 14, "polygon": [[107.25, 72.75], [328.7109375, 72.75], [328.7109375, 83.337890625], [107.25, 83.337890625]]}, {"title": "C.1 Analytical Computation for Linear Ridge Regression", "heading_level": null, "page_id": 14, "polygon": [[107.25, 96.75], [358.5, 96.75], [358.5, 106.734375], [107.25, 106.734375]]}, {"title": "C.3 Finding Realistic Distilled Data", "heading_level": null, "page_id": 17, "polygon": [[106.5, 630.0], [266.255859375, 630.0], [266.255859375, 640.01953125], [106.5, 640.01953125]]}, {"title": "C.4 Label Distillation", "heading_level": null, "page_id": 18, "polygon": [[107.25, 474.75], [207.087890625, 474.75], [207.087890625, 484.171875], [107.25, 484.171875]]}, {"title": "D Proofs for Kernel Ridge Regression", "heading_level": null, "page_id": 20, "polygon": [[105.75, 72.75], [309.75, 72.75], [309.75, 83.53125], [105.75, 83.53125]]}, {"title": "D.1 Deep Nonlinear Neural Networks", "heading_level": null, "page_id": 20, "polygon": [[106.5, 265.5], [276.1171875, 265.5], [276.1171875, 276.1171875], [106.5, 276.1171875]]}, {"title": "D.2 Deep Linear Neural Networks", "heading_level": null, "page_id": 20, "polygon": [[106.5, 632.25], [261.0, 632.25], [261.0, 642.0], [106.5, 642.0]]}, {"title": "E Applications", "heading_level": null, "page_id": 25, "polygon": [[106.5, 72.75], [193.341796875, 72.75], [193.341796875, 83.28955078125], [106.5, 83.28955078125]]}, {"title": "E.1 An Implication for KIP-type Algorithms", "heading_level": null, "page_id": 25, "polygon": [[106.**********, 96.75], [305.25, 96.75], [305.25, 106.8310546875], [106.**********, 106.8310546875]]}, {"title": "\\Box", "heading_level": null, "page_id": 25, "polygon": [[493.06640625, 392.25], [505.5, 392.25], [505.5, 404.89453125], [493.06640625, 404.89453125]]}, {"title": "E.2 Privacy Preservation of Dataset Distillation", "heading_level": null, "page_id": 25, "polygon": [[107.25, 417.0], [316.5, 417.0], [316.5, 426.55078125], [107.25, 426.55078125]]}, {"title": "NeurIPS Paper Checklist", "heading_level": null, "page_id": 27, "polygon": [[106.5, 71.30126953125], [238.6142578125, 71.30126953125], [238.6142578125, 84.35302734375], [106.5, 84.35302734375]]}, {"title": "1. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 27, "polygon": [[107.25, 93.0], [152.103515625, 93.0], [152.103515625, 104.5107421875], [107.25, 104.5107421875]]}, {"title": "2. Limitations", "heading_level": null, "page_id": 27, "polygon": [[107.25, 285.3984375], [171.826171875, 285.3984375], [171.826171875, 295.83984375], [107.25, 295.83984375]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 27, "polygon": [[119.25, 316.529296875], [181.08984375, 316.529296875], [181.08984375, 326.970703125], [119.25, 326.970703125]]}, {"title": "3. Theory Assumptions and Proofs", "heading_level": null, "page_id": 27, "polygon": [[107.25, 640.01953125], [259.5322265625, 640.01953125], [259.5322265625, 650.84765625], [107.25, 650.84765625]]}, {"title": "4. Experimental Result Reproducibility", "heading_level": null, "page_id": 28, "polygon": [[107.25, 186.0], [278.25, 186.0], [278.25, 197.1298828125], [107.25, 197.1298828125]]}, {"title": "5. Open access to data and code", "heading_level": null, "page_id": 28, "polygon": [[107.25, 615.0], [246.0, 615.0], [246.0, 625.7109375], [107.25, 625.7109375]]}, {"title": "Answer: [No]", "heading_level": null, "page_id": 28, "polygon": [[119.25, 657.75], [176.25, 657.75], [176.25, 668.63671875], [119.25, 668.63671875]]}, {"title": "Guidelines:", "heading_level": null, "page_id": 28, "polygon": [[119.0830078125, 699.0], [167.25, 699.0], [167.25, 708.85546875], [119.0830078125, 708.85546875]]}, {"title": "6. Experimental Setting/Details", "heading_level": null, "page_id": 29, "polygon": [[106.5, 275.537109375], [244.5, 275.537109375], [244.5, 285.978515625], [106.5, 285.978515625]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 29, "polygon": [[119.25, 317.689453125], [180.6416015625, 317.689453125], [180.6416015625, 327.744140625], [119.25, 327.744140625]]}, {"title": "7. Experiment Statistical Significance", "heading_level": null, "page_id": 29, "polygon": [[107.25, 413.015625], [270.0, 411.0], [270.0, 422.296875], [107.25, 423.0]]}, {"title": "Answer: [Yes]", "heading_level": null, "page_id": 29, "polygon": [[119.25, 453.0], [181.08984375, 453.0], [181.08984375, 463.67578125], [119.25, 463.67578125]]}, {"title": "8. Experiments Compute Resources", "heading_level": null, "page_id": 30, "polygon": [[106.75634765625, 73.3798828125], [264.462890625, 73.3798828125], [264.462890625, 83.6279296875], [106.75634765625, 83.6279296875]]}, {"title": "9. Code Of Ethics", "heading_level": null, "page_id": 30, "polygon": [[106.**********, 273.75], [186.767578125, 273.75], [186.767578125, 283.8515625], [106.**********, 283.8515625]]}, {"title": "10. <PERSON><PERSON>s", "heading_level": null, "page_id": 30, "polygon": [[103.5, 427.7109375], [193.640625, 427.7109375], [193.640625, 437.765625], [103.5, 437.765625]]}, {"title": "11. Safeguards", "heading_level": null, "page_id": 31, "polygon": [[102.**********, 120.65625], [169.**********, 120.65625], [169.**********, 131.677734375], [102.**********, 131.677734375]]}, {"title": "12. Licenses for existing assets", "heading_level": null, "page_id": 31, "polygon": [[103.5, 314.208984375], [236.**********, 314.208984375], [236.**********, 324.650390625], [103.5, 324.650390625]]}, {"title": "13. <PERSON>sets", "heading_level": null, "page_id": 31, "polygon": [[102.75, 568.5], [169.**********, 568.5], [169.**********, 579.3046875], [102.75, 579.3046875]]}, {"title": "14. Crowdsourcing and Research with Human Subjects", "heading_level": null, "page_id": 32, "polygon": [[102.12451171875, 99.0], [342.45703125, 99.0], [342.45703125, 110.408203125], [102.12451171875, 110.408203125]]}, {"title": "15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Sub-\njects", "heading_level": null, "page_id": 32, "polygon": [[102.75, 277.857421875], [508.0078125, 277.857421875], [508.0078125, 299.900390625], [102.75, 299.900390625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 50], ["Text", 7], ["SectionHeader", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 11203, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 54], ["TableCell", 40], ["ListItem", 4], ["Text", 3], ["Table", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1455, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 931], ["Line", 92], ["TableCell", 40], ["TextInlineMath", 7], ["SectionHeader", 3], ["Equation", 3], ["Text", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1333, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1027], ["Line", 57], ["TextInlineMath", 7], ["Reference", 3], ["SectionHeader", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1834, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 40], ["Reference", 6], ["Text", 5], ["TextInlineMath", 5], ["SectionHeader", 2], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 636, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 943], ["Line", 73], ["TextInlineMath", 7], ["Text", 4], ["Equation", 3], ["ListItem", 2], ["SectionHeader", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5458, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1169], ["Line", 93], ["TableCell", 32], ["TextInlineMath", 9], ["Reference", 3], ["Text", 2], ["Equation", 2], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 730], ["Line", 59], ["Reference", 6], ["TextInlineMath", 5], ["ListItem", 4], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 392], ["TableCell", 130], ["Line", 52], ["Text", 3], ["TextInlineMath", 3], ["Reference", 3], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3128, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["TableCell", 149], ["Line", 48], ["Text", 4], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 12757, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 171], ["Line", 48], ["ListItem", 15], ["Reference", 15], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 49], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 17], ["ListItem", 7], ["Reference", 7], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 34], ["ListItem", 22], ["Text", 4], ["SectionHeader", 3], ["ListGroup", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1192], ["Line", 120], ["TextInlineMath", 10], ["Equation", 5], ["SectionHeader", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1715, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 995], ["Line", 136], ["TextInlineMath", 9], ["Equation", 7], ["Text", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 1327], ["Line", 68], ["TextInlineMath", 10], ["Equation", 7], ["Text", 3], ["ListItem", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 1098], ["Line", 122], ["TextInlineMath", 13], ["Equation", 7], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 1055], ["Line", 146], ["Equation", 10], ["TableCell", 10], ["TextInlineMath", 7], ["Text", 5], ["ListItem", 2], ["Form", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5669, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 10], ["Equation", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 1016], ["Line", 88], ["TextInlineMath", 10], ["Equation", 4], ["ListItem", 4], ["SectionHeader", 3], ["Text", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3734, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 914], ["Line", 118], ["TextInlineMath", 8], ["Equation", 6], ["ListItem", 4], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2523, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 1038], ["Line", 157], ["Equation", 8], ["TextInlineMath", 6], ["Text", 4], ["ListItem", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1145, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 1018], ["Line", 109], ["TextInlineMath", 10], ["Equation", 8], ["TableCell", 6], ["Form", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1536, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 25], ["TextInlineMath", 3], ["Equation", 2], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 992], ["Line", 51], ["TextInlineMath", 13], ["Equation", 5], ["SectionHeader", 4], ["Text", 3], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 838], ["Line", 174], ["TableCell", 15], ["TextInlineMath", 4], ["Equation", 4], ["Form", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3619, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 52], ["ListItem", 12], ["Text", 10], ["SectionHeader", 5], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 54], ["ListItem", 16], ["Text", 6], ["SectionHeader", 4], ["ListGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 53], ["ListItem", 19], ["Text", 6], ["SectionHeader", 4], ["ListGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 51], ["Text", 12], ["ListItem", 12], ["SectionHeader", 3], ["ListGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 52], ["ListItem", 16], ["Text", 13], ["SectionHeader", 3], ["ListGroup", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 36], ["Text", 9], ["ListItem", 8], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Provable and Efficient Dataset Distillation for Kernel Ridge Regression"}