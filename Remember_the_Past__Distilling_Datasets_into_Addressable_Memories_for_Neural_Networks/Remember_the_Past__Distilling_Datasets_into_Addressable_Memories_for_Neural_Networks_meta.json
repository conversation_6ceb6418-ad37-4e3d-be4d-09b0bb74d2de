{"table_of_contents": [{"title": "Remember the Past: Distilling Datasets\ninto Addressable Memories for Neural Networks", "heading_level": null, "page_id": 0, "polygon": [[124.611328125, 99.75], [485.89453125, 99.75], [485.89453125, 136.318359375], [124.611328125, 136.318359375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.5419921875, 251.25], [328.5, 251.25], [328.5, 261.228515625], [282.5419921875, 261.228515625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 483.0], [191.84765625, 483.0], [191.84765625, 493.83984375], [107.25, 493.83984375]]}, {"title": "2 Related works", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 382.271484375], [200.25, 382.271484375], [200.25, 394.259765625], [106.98046875, 394.259765625]]}, {"title": "3 Background: dataset distillation", "heading_level": null, "page_id": 1, "polygon": [[106.5, 663.22265625], [292.25390625, 663.22265625], [292.25390625, 674.82421875], [106.5, 674.82421875]]}, {"title": "4 Model", "heading_level": null, "page_id": 2, "polygon": [[107.25, 317.109375], [159.7236328125, 317.109375], [159.7236328125, 327.9375], [107.25, 327.9375]]}, {"title": "4.1 Dataset Distillation as memory addressing", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 511.5], [310.5, 511.5], [310.5, 521.68359375], [106.98046875, 521.68359375]]}, {"title": "Algorithm 1", "heading_level": null, "page_id": 4, "polygon": [[107.25, 73.5], [159.0, 73.5], [159.0, 83.38623046875], [107.25, 83.38623046875]]}, {"title": "4.2 Learning framework: back-propagation through time", "heading_level": null, "page_id": 4, "polygon": [[107.25, 357.75], [360.75, 357.75], [360.75, 367.76953125], [107.25, 367.76953125]]}, {"title": "5 Experiments", "heading_level": null, "page_id": 5, "polygon": [[106.5, 307.634765625], [193.341796875, 307.634765625], [193.341796875, 320.396484375], [106.5, 320.396484375]]}, {"title": "5.1 Dataset Distillation", "heading_level": null, "page_id": 5, "polygon": [[106.5, 445.11328125], [213.9609375, 445.11328125], [213.9609375, 456.71484375], [106.5, 456.71484375]]}, {"title": "5.2 Continual learning", "heading_level": null, "page_id": 7, "polygon": [[106.5, 549.75], [211.5703125, 549.75], [211.5703125, 560.35546875], [106.5, 560.35546875]]}, {"title": "5.3 Synthesizing new classifiers after learning", "heading_level": null, "page_id": 8, "polygon": [[106.5, 561.515625], [309.75, 561.515625], [309.75, 572.34375], [106.5, 572.34375]]}, {"title": "5.3.1 Extrapolating between tasks", "heading_level": null, "page_id": 8, "polygon": [[106.5, 649.30078125], [259.8310546875, 649.30078125], [259.8310546875, 660.12890625], [106.5, 660.12890625]]}, {"title": "5.3.2 Dataset Distillation extension – recall the past with images", "heading_level": null, "page_id": 9, "polygon": [[106.5, 196.5], [386.0859375, 196.5], [386.0859375, 206.89453125], [106.5, 206.89453125]]}, {"title": "6 Conclusion and limitations", "heading_level": null, "page_id": 9, "polygon": [[107.25, 468.75], [263.4169921875, 468.75], [263.4169921875, 479.14453125], [107.25, 479.14453125]]}, {"title": "7 Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[106.5, 643.5], [225.7646484375, 643.5], [225.7646484375, 654.71484375], [106.5, 654.71484375]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.25, 72.75], [164.25, 72.75], [164.25, 83.48291015625], [107.25, 83.48291015625]]}, {"title": "A Experiment setups", "heading_level": null, "page_id": 13, "polygon": [[106.2333984375, 71.30126953125], [225.7646484375, 71.30126953125], [225.7646484375, 84.06298828125], [106.2333984375, 84.06298828125]]}, {"title": "A.1 Dataset Distillation", "heading_level": null, "page_id": 13, "polygon": [[107.1298828125, 130.0341796875], [215.6044921875, 130.0341796875], [215.6044921875, 141.0556640625], [107.1298828125, 141.0556640625]]}, {"title": "A.2 Continual learning", "heading_level": null, "page_id": 13, "polygon": [[107.25, 608.6953125], [215.75390625, 608.6953125], [215.75390625, 620.296875], [107.25, 620.296875]]}, {"title": "A.3 New classifier synthesis", "heading_level": null, "page_id": 14, "polygon": [[107.1298828125, 270.703125], [232.9365234375, 270.703125], [232.9365234375, 281.53125], [107.1298828125, 281.53125]]}, {"title": "B Additional results and discussion", "heading_level": null, "page_id": 14, "polygon": [[106.5, 503.12109375], [296.8857421875, 503.12109375], [296.8857421875, 514.72265625], [106.5, 514.72265625]]}, {"title": "B.1 Dataset Distillation", "heading_level": null, "page_id": 14, "polygon": [[106.5, 529.8046875], [214.55859375, 529.8046875], [214.55859375, 539.859375], [106.5, 539.859375]]}, {"title": "B.2 Continual learning", "heading_level": null, "page_id": 16, "polygon": [[106.5, 140.572265625], [214.857421875, 140.572265625], [214.857421875, 151.787109375], [106.5, 151.787109375]]}, {"title": "B.3 New classifier synthesis", "heading_level": null, "page_id": 16, "polygon": [[106.8310546875, 250.20703125], [235.3271484375, 250.20703125], [235.3271484375, 261.421875], [106.8310546875, 261.421875]]}, {"title": "C Visualization and analysis", "heading_level": null, "page_id": 16, "polygon": [[107.25, 477.2109375], [262.5, 477.2109375], [262.5, 489.5859375], [107.25, 489.5859375]]}, {"title": "D More visualization comparisons", "heading_level": null, "page_id": 16, "polygon": [[106.5, 631.51171875], [293.2998046875, 631.51171875], [293.2998046875, 643.88671875], [106.5, 643.88671875]]}, {"title": "D.1 Same amount of generated images", "heading_level": null, "page_id": 16, "polygon": [[107.05517578125, 680.23828125], [280.599609375, 680.23828125], [280.599609375, 691.83984375], [107.05517578125, 691.83984375]]}, {"title": "D.2 Various image per class budgets", "heading_level": null, "page_id": 18, "polygon": [[106.5, 521.25], [269.25, 521.25], [269.25, 533.25], [106.5, 533.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 45], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6203, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 52], ["Text", 7], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 581], ["Line", 64], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 618], ["Line", 138], ["TextInlineMath", 6], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 841, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 805], ["Line", 60], ["ListItem", 17], ["TextInlineMath", 4], ["SectionHeader", 2], ["Reference", 2], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1051, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 53], ["Text", 8], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 510], ["TableCell", 144], ["Line", 69], ["Text", 5], ["TextInlineMath", 3], ["Reference", 3], ["Table", 1], ["Equation", 1], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 804, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 364], ["Line", 113], ["TableCell", 42], ["Text", 6], ["Caption", 3], ["Reference", 3], ["Figure", 2], ["FigureGroup", 2], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1876, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["TableCell", 156], ["Line", 54], ["Text", 6], ["Reference", 3], ["SectionHeader", 2], ["Table", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 57], ["TableCell", 24], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1029, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 48], ["ListItem", 19], ["Reference", 16], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 48], ["ListItem", 18], ["Reference", 12], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 25], ["ListItem", 11], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 51], ["Text", 6], ["ListItem", 6], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 161], ["Span", 159], ["Line", 46], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7962, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["TableCell", 129], ["Line", 95], ["Caption", 3], ["Text", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 11287, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 48], ["Text", 8], ["SectionHeader", 5], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 23], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 956, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 160], ["Span", 44], ["Line", 9], ["Caption", 4], ["Table", 2], ["TableGroup", 2], ["Picture", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 7341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 70], ["Span", 26], ["Caption", 6], ["Line", 5], ["Picture", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 3231, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 52], ["Span", 14], ["Caption", 3], ["Line", 3], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1262, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 112], ["Span", 5], ["Line", 3], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1283, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Line", 23], ["Span", 5], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 450], ["Span", 5], ["Line", 3], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3309, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Remember_the_Past__Distilling_Datasets_into_Addressable_Memories_for_Neural_Networks"}