{"table_of_contents": [{"title": "Rethinking Data Distillation: Do Not Overlook Calibration", "heading_level": null, "page_id": 0, "polygon": [[115.5, 106.5], [478.5, 106.5], [478.5, 119.109375], [115.5, 119.109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 243.0], [191.25, 243.0], [191.25, 253.6875], [144.75, 253.6875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 488.25], [126.75, 488.25], [126.75, 499.25390625], [48.75, 499.25390625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.494140625, 288.75], [393.0, 288.75], [393.0, 300.287109375], [307.494140625, 300.287109375]]}, {"title": "3. Limitation Analysis of DDNNs' Calibration", "heading_level": null, "page_id": 2, "polygon": [[48.0, 539.25], [285.0, 539.25], [285.0, 550.6875], [48.0, 550.6875]]}, {"title": "3.1. DDNNs are Less Calibratable", "heading_level": null, "page_id": 2, "polygon": [[307.5, 282.75], [467.25, 282.75], [467.25, 293.326171875], [307.5, 293.326171875]]}, {"title": "3.2. DD Contains Limited Semantic Information", "heading_level": null, "page_id": 3, "polygon": [[48.0, 72.75], [276.0, 72.75], [276.0, 83.2412109375], [48.0, 83.2412109375]]}, {"title": "3.3. Limited Semantic Information Weakens En-\ncoding Capacity", "heading_level": null, "page_id": 3, "polygon": [[307.5, 276.0], [544.5, 276.0], [544.5, 298.16015625], [307.5, 298.16015625]]}, {"title": "4. Our Proposed Techniques", "heading_level": null, "page_id": 3, "polygon": [[307.5, 645.75], [453.75, 645.75], [453.75, 656.6484375], [307.5, 656.6484375]]}, {"title": "4.1. <PERSON><PERSON>", "heading_level": null, "page_id": 4, "polygon": [[48.0, 273.0], [208.283203125, 273.0], [208.283203125, 283.271484375], [48.0, 283.271484375]]}, {"title": "4.2. Masked Distillation Training", "heading_level": null, "page_id": 4, "polygon": [[48.75, 636.75], [204.75, 636.75], [204.75, 646.59375], [48.75, 646.59375]]}, {"title": "Algorithm 1: Masked Distillation Training", "heading_level": null, "page_id": 4, "polygon": [[313.5, 273.216796875], [489.181640625, 273.216796875], [489.181640625, 285.75], [313.5, 285.75]]}, {"title": "4.3. Connection to Dropouts", "heading_level": null, "page_id": 5, "polygon": [[48.75, 660.0], [181.5, 660.0], [181.5, 670.95703125], [48.75, 670.95703125]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 5, "polygon": [[307.1953125, 461.25], [386.25, 461.25], [386.25, 472.18359375], [307.1953125, 472.18359375]]}, {"title": "5.1. Experiment Setup", "heading_level": null, "page_id": 5, "polygon": [[307.5, 481.5], [414.0, 481.5], [414.0, 491.51953125], [307.5, 491.51953125]]}, {"title": "5.2. Empirical Analysis of MTS", "heading_level": null, "page_id": 6, "polygon": [[48.75, 393.0], [198.0, 393.0], [198.0, 403.34765625], [48.75, 403.34765625]]}, {"title": "5.3. Empirical Analysis of MDT", "heading_level": null, "page_id": 6, "polygon": [[48.0, 624.75], [199.5, 624.75], [199.5, 635.37890625], [48.0, 635.37890625]]}, {"title": "5.4. Enabling Calibratable DDNNs", "heading_level": null, "page_id": 6, "polygon": [[307.5, 538.5], [471.75, 538.5], [471.75, 549.52734375], [307.5, 549.52734375]]}, {"title": "5.5. Enhancing Se<PERSON><PERSON> Information of DDNNs", "heading_level": null, "page_id": 7, "polygon": [[48.75, 180.75], [276.75, 180.75], [276.75, 191.42578125], [48.75, 191.42578125]]}, {"title": "5.6. Improving Encoding Capacity of DDNNs", "heading_level": null, "page_id": 7, "polygon": [[48.75, 339.75], [262.5, 339.75], [262.5, 350.560546875], [48.75, 350.560546875]]}, {"title": "6. Ablation Studies", "heading_level": null, "page_id": 7, "polygon": [[48.75, 526.5], [147.0, 526.5], [147.0, 537.92578125], [48.75, 537.92578125]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[48.75, 562.5], [120.0, 562.5], [120.0, 574.27734375], [48.75, 574.27734375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[48.75, 72.75], [106.5, 72.75], [106.5, 83.8212890625], [48.75, 83.8212890625]]}, {"title": "<PERSON><PERSON> Distillation Backbones", "heading_level": null, "page_id": 11, "polygon": [[48.75, 224.25], [180.0, 224.25], [180.0, 236.478515625], [48.75, 236.478515625]]}, {"title": "A.1. Datasets and Networks", "heading_level": null, "page_id": 11, "polygon": [[48.75, 249.0], [180.0, 249.0], [180.0, 259.48828125], [48.75, 259.48828125]]}, {"title": "<PERSON>. Additional Experiments", "heading_level": null, "page_id": 11, "polygon": [[48.0, 440.25], [187.5, 440.25], [187.5, 451.6875], [48.0, 451.6875]]}, {"title": "B.1. Details in Masked Temperature Scaling", "heading_level": null, "page_id": 11, "polygon": [[48.75, 465.0], [256.5, 465.0], [256.5, 474.890625], [48.75, 474.890625]]}, {"title": "B.2. More Results on SVD of Distilled Data and Full\nData", "heading_level": null, "page_id": 11, "polygon": [[307.5, 225.75], [546.0, 225.75], [546.0, 248.66015625], [307.5, 248.66015625]]}, {"title": "B.3. Performance Analysis of FDNNs", "heading_level": null, "page_id": 12, "polygon": [[47.25, 72.0], [224.25, 72.0], [224.25, 84.0], [47.25, 84.0]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 104], ["Text", 9], ["SectionHeader", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7386, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 289], ["Line", 103], ["Text", 11], ["ListItem", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 379], ["Line", 133], ["Reference", 6], ["Text", 5], ["Caption", 3], ["Figure", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1926, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 95], ["Text", 7], ["Reference", 6], ["SectionHeader", 3], ["Equation", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 742], ["TableCell", 180], ["Line", 86], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 3], ["Table", 2], ["Equation", 2], ["Text", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5144, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 591], ["Line", 145], ["Text", 7], ["Reference", 5], ["Equation", 4], ["Figure", 3], ["Caption", 3], ["SectionHeader", 3], ["FigureGroup", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2519, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 487], ["Line", 116], ["TableCell", 59], ["Text", 9], ["Reference", 5], ["Caption", 3], ["SectionHeader", 3], ["Table", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 6848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["Line", 110], ["TableCell", 96], ["Text", 8], ["Reference", 6], ["Caption", 3], ["SectionHeader", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9067, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 107], ["Text", 8], ["Figure", 3], ["Reference", 3], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2432, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 456], ["Line", 113], ["ListItem", 30], ["Reference", 30], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 418], ["Line", 113], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 417], ["Line", 99], ["TableCell", 80], ["SectionHeader", 5], ["Reference", 4], ["Text", 3], ["Table", 2], ["Caption", 2], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2099, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Line", 11], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Rethinking_Data_Distillation__Do_Not_Overlook_Calibration"}