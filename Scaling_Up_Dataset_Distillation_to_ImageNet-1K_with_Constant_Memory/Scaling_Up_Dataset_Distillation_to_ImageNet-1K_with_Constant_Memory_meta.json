{"table_of_contents": [{"title": "Scaling Up Dataset Distillation to ImageNet-1K with Constant Memory", "heading_level": null, "page_id": 0, "polygon": [[78.75, 89.25], [516.97265625, 89.25], [516.97265625, 104.1240234375], [78.75, 104.1240234375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 176.25], [195.75, 176.25], [195.75, 187.0751953125], [148.5, 187.0751953125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 584.25], [132.75, 584.25], [132.75, 595.16015625], [54.0, 595.16015625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[54.0, 610.5], [138.75, 610.5], [138.75, 621.84375], [54.0, 621.84375]]}, {"title": "3. Background", "heading_level": null, "page_id": 1, "polygon": [[305.25, 644.25], [383.25, 644.25], [383.25, 655.48828125], [305.25, 655.48828125]]}, {"title": "3.1. Matching Training Trajectories", "heading_level": null, "page_id": 1, "polygon": [[306.0, 664.5], [459.75, 664.5], [459.75, 675.2109375], [306.0, 675.2109375]]}, {"title": "3.2. Scability of the current MTT method", "heading_level": null, "page_id": 2, "polygon": [[54.0, 323.25], [231.0, 323.25], [231.0, 334.318359375], [54.0, 334.318359375]]}, {"title": "4. Our proposed method", "heading_level": null, "page_id": 2, "polygon": [[306.0, 144.75], [435.09375, 144.75], [435.09375, 155.9443359375], [306.0, 155.9443359375]]}, {"title": "4.1. MTT with constant memory", "heading_level": null, "page_id": 2, "polygon": [[306.0, 213.75], [444.75, 213.75], [444.75, 223.91015625], [306.0, 223.91015625]]}, {"title": "4.2. Memory complexity v.s. other methods", "heading_level": null, "page_id": 3, "polygon": [[305.25, 270.75], [491.25, 270.75], [491.25, 280.951171875], [305.25, 280.951171875]]}, {"title": "4.3. Soft labels", "heading_level": null, "page_id": 4, "polygon": [[54.0, 261.421875], [117.75, 261.421875], [117.75, 271.4765625], [54.0, 271.4765625]]}, {"title": "5. Experimental Results", "heading_level": null, "page_id": 4, "polygon": [[306.0, 551.25], [429.75, 551.25], [429.75, 562.67578125], [306.0, 562.67578125]]}, {"title": "5.1. Experiment setup", "heading_level": null, "page_id": 4, "polygon": [[306.0, 570.75], [401.25, 570.75], [401.25, 581.625], [306.0, 581.625]]}, {"title": "5.2. Empirical results", "heading_level": null, "page_id": 5, "polygon": [[306.0, 148.5], [398.25, 148.5], [398.25, 158.748046875], [306.0, 158.748046875]]}, {"title": "5.3. Training cost analysis", "heading_level": null, "page_id": 5, "polygon": [[306.0, 326.25], [418.5, 326.25], [418.5, 336.638671875], [306.0, 336.638671875]]}, {"title": "5.4. Ablation study on soft labels", "heading_level": null, "page_id": 6, "polygon": [[54.0, 631.5], [194.25, 631.5], [194.25, 641.25], [54.0, 641.25]]}, {"title": "5.5. Cross-Architecture generalization", "heading_level": null, "page_id": 7, "polygon": [[54.0, 564.75], [216.75, 564.75], [216.75, 574.27734375], [54.0, 574.27734375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 489.0], [377.25, 489.0], [377.25, 501.1875], [306.0, 501.1875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[54.0, 108.75], [155.390625, 108.75], [155.390625, 119.689453125], [54.0, 119.689453125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 169.189453125], [111.75, 169.189453125], [111.75, 179.82421875], [54.0, 179.82421875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 10, "polygon": [[54.0, 66.37060546875], [120.5771484375, 66.37060546875], [120.5771484375, 79.13232421875], [54.0, 79.13232421875]]}, {"title": "A.1. Bi-level optimization", "heading_level": null, "page_id": 10, "polygon": [[54.0, 87.35009765625], [163.5, 87.35009765625], [163.5, 99.193359375], [54.0, 99.193359375]]}, {"title": "A.2. Complexity of other distillation methods", "heading_level": null, "page_id": 10, "polygon": [[54.0, 230.09765625], [246.83203125, 230.09765625], [246.83203125, 241.3125], [54.0, 241.3125]]}, {"title": "A.3. Datasets", "heading_level": null, "page_id": 10, "polygon": [[54.0, 321.36328125], [112.5087890625, 321.36328125], [112.5087890625, 332.96484375], [54.0, 332.96484375]]}, {"title": "A.4. Data preprocessing", "heading_level": null, "page_id": 10, "polygon": [[54.0, 425.00390625], [158.080078125, 425.00390625], [158.080078125, 436.60546875], [54.0, 436.60546875]]}, {"title": "A.5. Models", "heading_level": null, "page_id": 10, "polygon": [[54.0, 504.66796875], [107.7275390625, 504.66796875], [107.7275390625, 515.49609375], [54.0, 515.49609375]]}, {"title": "A.6. Hardwares", "heading_level": null, "page_id": 10, "polygon": [[54.0, 619.91015625], [124.1630859375, 619.91015625], [124.1630859375, 631.51171875], [54.0, 631.51171875]]}, {"title": "A.7. Label learning", "heading_level": null, "page_id": 11, "polygon": [[54.0, 388.072265625], [138.05859375, 388.072265625], [138.05859375, 398.70703125], [54.0, 398.70703125]]}, {"title": "A.8. Training cost analysis", "heading_level": null, "page_id": 11, "polygon": [[54.0, 587.25], [168.5390625, 587.25], [168.5390625, 597.48046875], [54.0, 597.48046875]]}, {"title": "A.9. Augmentation", "heading_level": null, "page_id": 12, "polygon": [[54.0, 421.5], [137.3115234375, 421.5], [137.3115234375, 431.96484375], [54.0, 431.96484375]]}, {"title": "A.10. Competitors", "heading_level": null, "page_id": 12, "polygon": [[54.0, 513.0], [134.47265625, 513.0], [134.47265625, 523.23046875], [54.0, 523.23046875]]}, {"title": "A.11. Learning learning rate", "heading_level": null, "page_id": 12, "polygon": [[54.0, 641.25], [177.75, 641.25], [177.75, 650.84765625], [54.0, 650.84765625]]}, {"title": "A.12. Hyperparameters", "heading_level": null, "page_id": 13, "polygon": [[54.0, 220.4296875], [156.75, 220.4296875], [156.75, 231.2578125], [54.0, 231.2578125]]}, {"title": "A.13. Example distilled image", "heading_level": null, "page_id": 13, "polygon": [[54.0, 501.9609375], [183.0, 501.9609375], [183.0, 512.7890625], [54.0, 512.7890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 91], ["Text", 5], ["SectionHeader", 3], ["Footnote", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6373, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 102], ["Text", 8], ["ListItem", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 1026], ["Line", 186], ["Text", 8], ["TextInlineMath", 7], ["Equation", 6], ["Reference", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3257, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 677], ["Line", 127], ["TextInlineMath", 6], ["Reference", 5], ["Text", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 937, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 102], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 762], ["Line", 103], ["Text", 8], ["Reference", 5], ["Footnote", 4], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 491], ["TableCell", 118], ["Line", 86], ["Text", 7], ["Caption", 5], ["Reference", 5], ["Table", 2], ["Figure", 2], ["TableGroup", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 11688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 110], ["TableCell", 25], ["Text", 6], ["Reference", 4], ["Caption", 3], ["SectionHeader", 2], ["Figure", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["TextInlineMath", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4346, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 91], ["ListItem", 22], ["Reference", 22], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 32], ["ListItem", 10], ["Reference", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 43], ["SectionHeader", 7], ["Text", 5], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 42], ["Text", 4], ["Reference", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 802, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["TableCell", 71], ["Line", 45], ["Text", 5], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7457, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 192], ["Span", 138], ["Line", 34], ["Text", 4], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9971, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 679, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1029, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Line", 5], ["Span", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 594, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 597, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Line", 7], ["Span", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 654, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Line", 9], ["Span", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 629, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 628, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Scaling_Up_Dataset_Distillation_to_ImageNet-1K_with_Constant_Memory"}