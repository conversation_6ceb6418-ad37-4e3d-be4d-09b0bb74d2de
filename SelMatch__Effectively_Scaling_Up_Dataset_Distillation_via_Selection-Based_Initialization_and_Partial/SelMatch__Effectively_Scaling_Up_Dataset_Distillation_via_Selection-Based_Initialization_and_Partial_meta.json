{"table_of_contents": [{"title": "SelMatch: Effectively Scaling Up Dataset Distillation via Selection-Based\nInitialization and Partial Updates by Trajectory Matching", "heading_level": null, "page_id": 0, "polygon": [[74.25, 89.25], [522.3515625, 89.25], [522.3515625, 120.462890625], [74.25, 120.462890625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[149.18994140625, 193.5], [195.75, 193.5], [195.75, 204.57421875], [149.18994140625, 204.57421875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 526.5], [132.0, 526.5], [132.0, 537.92578125], [54.0, 537.92578125]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[306.0, 155.9443359375], [396.24609375, 155.9443359375], [396.24609375, 166.9658203125], [306.0, 166.9658203125]]}, {"title": "3. Moti<PERSON>", "heading_level": null, "page_id": 2, "polygon": [[306.0, 503.25], [375.75, 503.25], [375.75, 514.3359375], [306.0, 514.3359375]]}, {"title": "3.1. Preliminary", "heading_level": null, "page_id": 2, "polygon": [[306.0, 523.5], [376.822265625, 523.5], [376.822265625, 534.05859375], [306.0, 534.05859375]]}, {"title": "3.2. Limitations of Traditional Methods in Larger IPC", "heading_level": null, "page_id": 3, "polygon": [[54.0, 336.0], [286.5, 336.0], [286.5, 346.306640625], [54.0, 346.306640625]]}, {"title": "4. Main Method: SelMatch", "heading_level": null, "page_id": 3, "polygon": [[306.0, 552.75], [446.25, 552.75], [446.25, 563.8359375], [306.0, 563.8359375]]}, {"title": "5. Experimental Results", "heading_level": null, "page_id": 5, "polygon": [[54.0, 359.25], [177.75, 359.25], [177.75, 370.08984375], [54.0, 370.08984375]]}, {"title": "5.1. Experiment Setup", "heading_level": null, "page_id": 5, "polygon": [[54.0, 379.5], [150.75, 379.5], [150.75, 390.005859375], [54.0, 390.005859375]]}, {"title": "5.2. Main Results", "heading_level": null, "page_id": 5, "polygon": [[306.0, 201.75], [382.5, 201.75], [382.5, 211.53515625], [306.0, 211.53515625]]}, {"title": "5.3. Ablation Study and Further Analysis", "heading_level": null, "page_id": 6, "polygon": [[54.0, 581.23828125], [231.75, 581.23828125], [231.75, 590.51953125], [54.0, 590.51953125]]}, {"title": "6. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 8, "polygon": [[54.0, 327.75], [126.75, 327.75], [126.75, 338.958984375], [54.0, 338.958984375]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[306.0, 204.0], [399.75, 204.0], [399.75, 214.822265625], [306.0, 214.822265625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[305.25, 431.25], [407.25, 431.25], [407.25, 442.79296875], [305.25, 442.79296875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[306.0, 491.25], [363.0, 491.25], [363.0, 502.34765625], [306.0, 502.34765625]]}, {"title": "<PERSON>. Full Algorithm: SelMatch", "heading_level": null, "page_id": 11, "polygon": [[54.0, 68.25], [203.9501953125, 68.25], [203.9501953125, 79.5673828125], [54.0, 79.5673828125]]}, {"title": "Freeze \\mathcal{D}_{\\text{select}}.\nrepeat", "heading_level": null, "page_id": 11, "polygon": [[63.75, 273.75], [121.5, 273.75], [121.5, 294.0], [63.75, 294.0]]}, {"title": "B. Implementation Details", "heading_level": null, "page_id": 11, "polygon": [[54.0, 459.0], [189.0, 459.0], [189.0, 470.63671875], [54.0, 470.63671875]]}, {"title": "B.1. SelMatch and Reproduction of MTT", "heading_level": null, "page_id": 11, "polygon": [[54.0, 479.25], [232.189453125, 479.25], [232.189453125, 489.5859375], [54.0, 489.5859375]]}, {"title": "B.2. Reproduction of Other Baselines", "heading_level": null, "page_id": 12, "polygon": [[54.0, 269.736328125], [214.5, 269.736328125], [214.5, 280.177734375], [54.0, 280.177734375]]}, {"title": "B.3. Evaluation of Distilled Dataset", "heading_level": null, "page_id": 12, "polygon": [[54.0, 469.86328125], [204.75, 469.86328125], [204.75, 479.91796875], [54.0, 479.91796875]]}, {"title": "<PERSON><PERSON> Experimental Results", "heading_level": null, "page_id": 12, "polygon": [[54.0, 564.75], [211.5, 564.75], [211.5, 575.82421875], [54.0, 575.82421875]]}, {"title": "C.1. Sliding Window Experiment", "heading_level": null, "page_id": 12, "polygon": [[54.0, 584.25], [197.9736328125, 584.25], [197.9736328125, 594.38671875], [54.0, 594.38671875]]}, {"title": "C.2. Coverage Analysis", "heading_level": null, "page_id": 12, "polygon": [[54.0, 676.5], [155.091796875, 676.5], [155.091796875, 686.8125], [54.0, 686.8125]]}, {"title": "<PERSON>. Further Ablation Studies", "heading_level": null, "page_id": 14, "polygon": [[54.0, 67.91748046875], [198.8701171875, 67.91748046875], [198.8701171875, 78.93896484375], [54.0, 78.93896484375]]}, {"title": "D.1. Other Difficulty Scores", "heading_level": null, "page_id": 14, "polygon": [[54.0, 87.75], [173.25, 87.75], [173.25, 98.27490234375], [54.0, 98.27490234375]]}, {"title": "D.2. Other Distillation Methods", "heading_level": null, "page_id": 14, "polygon": [[54.0, 568.5], [189.75, 568.5], [189.75, 578.53125], [54.0, 578.53125]]}, {"title": "E. <PERSON> Guidance", "heading_level": null, "page_id": 15, "polygon": [[54.0, 411.46875], [159.0, 411.46875], [159.0, 423.0703125], [54.0, 423.0703125]]}, {"title": "F. Visualization", "heading_level": null, "page_id": 15, "polygon": [[54.0, 650.07421875], [135.0, 650.07421875], [135.0, 661.67578125], [54.0, 661.67578125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 88], ["Text", 7], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5204, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["Line", 114], ["Text", 8], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 430], ["Line", 116], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 953, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 352], ["Line", 104], ["Text", 6], ["TextInlineMath", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 761, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 662], ["Line", 103], ["TextInlineMath", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 891, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 101], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 649], ["TableCell", 398], ["Line", 91], ["Text", 11], ["Reference", 5], ["Table", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 11439, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 508], ["Line", 103], ["Reference", 6], ["TextInlineMath", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1947, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 98], ["Text", 7], ["ListItem", 5], ["Reference", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 94], ["ListItem", 27], ["Reference", 27], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 19], ["ListItem", 5], ["Reference", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 697], ["Line", 48], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1185, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["TableCell", 72], ["Line", 53], ["SectionHeader", 5], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["Line", 168], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3144, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 61], ["TableCell", 24], ["Reference", 4], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2065, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 366], ["Line", 76], ["TableCell", 24], ["Text", 4], ["Reference", 4], ["Caption", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2303, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 33], ["Line", 8], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 934, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 13], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 638, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 5], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Line", 5], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/SelMatch__Effectively_Scaling_Up_Dataset_Distillation_via_Selection-Based_Initialization_and_Partial"}