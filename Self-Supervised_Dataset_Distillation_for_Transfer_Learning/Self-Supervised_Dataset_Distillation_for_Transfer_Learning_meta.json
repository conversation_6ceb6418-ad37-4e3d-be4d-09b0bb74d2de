{"table_of_contents": [{"title": "SELF-S<PERSON>ERVISED DATASET DISTILLATION\nFOR TRANSFER LEARNING", "heading_level": null, "page_id": 0, "polygon": [[104.58984375, 81.0], [428.220703125, 81.0], [428.220703125, 115.822265625], [104.58984375, 115.822265625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 210.0], [334.5, 210.0], [334.5, 221.58984375], [276.75, 221.58984375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.25, 457.5], [207.0, 457.5], [207.0, 468.31640625], [107.25, 468.31640625]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 2, "polygon": [[107.1298828125, 122.25], [212.25, 122.25], [212.25, 133.7080078125], [107.1298828125, 133.7080078125]]}, {"title": "3 METHOD", "heading_level": null, "page_id": 2, "polygon": [[107.25, 558.80859375], [173.25, 558.80859375], [173.25, 569.63671875], [107.25, 569.63671875]]}, {"title": "3.1 PRELIMINARIES", "heading_level": null, "page_id": 2, "polygon": [[107.25, 575.82421875], [200.8125, 575.82421875], [200.8125, 585.87890625], [107.25, 585.87890625]]}, {"title": "3.2 KERNEL RIDGE REGRESSION ON SELF-SUPERVISED TARGET", "heading_level": null, "page_id": 3, "polygon": [[106.5, 231.0], [389.373046875, 231.0], [389.373046875, 241.119140625], [106.5, 241.119140625]]}, {"title": "4 EXPERIMENTS", "heading_level": null, "page_id": 5, "polygon": [[107.1298828125, 157.587890625], [200.9619140625, 157.587890625], [200.9619140625, 168.416015625], [107.1298828125, 168.416015625]]}, {"title": "4.1 EXPERIMENTAL SETUPS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 211.341796875], [235.3271484375, 211.341796875], [235.3271484375, 221.009765625], [106.5, 221.009765625]]}, {"title": "4.2 EXPERIMENTAL RESULTS AND ANALYSIS", "heading_level": null, "page_id": 6, "polygon": [[106.5, 591.75], [307.5, 591.75], [307.5, 601.34765625], [106.5, 601.34765625]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[107.25, 566.25], [195.8818359375, 566.25], [195.8818359375, 577.37109375], [107.25, 577.37109375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 9, "polygon": [[107.25, 314.25], [176.1591796875, 314.25], [176.1591796875, 325.037109375], [107.25, 325.037109375]]}, {"title": "A PROOF OF THEOREM 1", "heading_level": null, "page_id": 13, "polygon": [[106.5, 81.75], [244.5, 81.75], [244.5, 93.63427734375], [106.5, 93.63427734375]]}, {"title": "B VISUALIZATION OF DISTILLED IMAGES", "heading_level": null, "page_id": 14, "polygon": [[106.5, 80.630859375], [331.1015625, 80.630859375], [331.1015625, 93.779296875], [106.5, 93.779296875]]}, {"title": "C EXPERIMENTAL RESULTS OF ARCHITECTURE GENERALIZATION", "heading_level": null, "page_id": 15, "polygon": [[106.5, 81.75], [454.81640625, 81.75], [454.81640625, 93.6826171875], [106.5, 93.6826171875]]}, {"title": "D IMAGENETTE EXPERIMENTS", "heading_level": null, "page_id": 16, "polygon": [[106.15869140625, 81.75], [276.416015625, 81.75], [276.416015625, 93.97265625], [106.15869140625, 93.97265625]]}, {"title": "E ABLATION STUDY ON THE INITIALIZATION OF Y_s", "heading_level": null, "page_id": 16, "polygon": [[106.5, 417.0], [377.25, 417.0], [377.25, 429.0], [106.5, 429.0]]}, {"title": "F FID SCORE OF CONDENSED DATASET", "heading_level": null, "page_id": 16, "polygon": [[106.5, 588.75], [321.0, 588.75], [321.0, 600.9609375], [106.5, 600.9609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 53], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3444, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 95], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 842, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 55], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 1186], ["Line", 113], ["TextInlineMath", 6], ["Equation", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8545, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1521], ["Line", 81], ["ListItem", 17], ["TextInlineMath", 5], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 51], ["ListItem", 6], ["Text", 4], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1375], ["TableCell", 330], ["Line", 54], ["Table", 3], ["Caption", 3], ["Reference", 3], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 24943, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 58], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1374, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 679], ["Line", 65], ["TableCell", 60], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Equation", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 49], ["ListItem", 10], ["Reference", 10], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 49], ["ListItem", 18], ["Reference", 18], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 46], ["Line", 16], ["ListItem", 6], ["Reference", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 860], ["Line", 157], ["Equation", 4], ["TextInlineMath", 3], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3227, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 24], ["Line", 6], ["Picture", 3], ["Caption", 3], ["PictureGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 1403], ["TableCell", 378], ["Line", 47], ["Table", 4], ["Caption", 4], ["TableGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 19847, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 837], ["TableCell", 144], ["Line", 42], ["Caption", 4], ["Table", 4], ["SectionHeader", 3], ["TableGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 4779, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Self-Supervised_Dataset_Distillation_for_Transfer_Learning"}