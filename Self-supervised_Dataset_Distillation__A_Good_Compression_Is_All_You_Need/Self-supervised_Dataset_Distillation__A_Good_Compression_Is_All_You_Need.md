# <span id="page-0-1"></span>Self-supervised Dataset Distillation: A Good Compression Is All You Need

<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>\*

VILA Lab, <PERSON> University of AI {muxin.zhou,zeyuan.yin,zhiqiang.shen}@mbzuai.ac.ae, <EMAIL> <https://github.com/VILA-Lab/SRe2L/tree/main/SCDD/>

Image /page/0/Figure/3 description: The image displays a grid of 24 images, arranged in four rows and six columns. Each row is labeled with a different category: "SRe2L T-IN", "Ours T-IN", "SRe2L IN", and "Ours IN". The columns are also labeled with specific objects or scenes, including "Golden Fish", "Wooden Spoon", "Plate", "Lemon", "Spider Web", "Water Tower", "Pizza", "Lion", "Brown Bear", and "JellyFish". The images themselves appear to be generated or processed, with varying degrees of clarity and artistic style, depicting the items listed in the column headers.

<span id="page-0-0"></span>Fig. 1: Example distilled images from  $SRe^2L$  [\[57\]](#page-17-0) and our  $64 \times 64$  Tiny-ImageNet (top two rows), 224×224 ImageNet-1K (bottom two rows). All our synthetic data is generally More visualization results are available at [link](https://drive.google.com/file/d/1uQgGPx36WkBH-qh6iTpx2H2Dn86qXRAR/view?usp=sharing). the original validation data markedly superior to previous dataset distillation methods. )<br>de<br>∙'' ated from the self-supervised pretrained models, while the more realistic images with two rows), 224 ×224 ImageNet-1K (bottom two rows). All our synthetic data is generbetter semantic alignment and details are obtained. Moreover, training a conventional deep model with our distilled images results in a model that achieves test accuracy on

Abstract. Dataset distillation aims to compress information from a large-scale original dataset to a new compact dataset while striving to preserve the utmost degree of the original data informational essence. Previous studies have predominantly concentrated on aligning the intermediate statistics between the original and distilled data, such as weight trajectory, features, gradient, BatchNorm, etc. In this work, we consider addressing this task through the new lens of model informativeness in the compression stage on the original dataset pretraining. We observe that with the prior state-of-the-art  $\text{SRe}^2$ L, as model sizes increase, it becomes increasingly challenging for supervised pretrained models to recover learned information during data synthesis, as the channel-wise mean and variance inside the model are flatting and less informative. We further notice that larger variances in BN statistics from self-supervised

<sup>⋆</sup> Corresponding author.

models enable larger loss signals to update the recovered data by gradients, enjoying more informativeness during synthesis. Building on this observation, we introduce SC-DD, a simple yet effective Self-supervised Compression framework for Dataset Distillation that facilitates diverse information compression and recovery compared to traditional supervised learning schemes, further reaps the potential of large pretrained models with enhanced capabilities. Extensive experiments are conducted on CIFAR-100, Tiny-ImageNet and ImageNet-1K datasets to demonstrate the superiority of our proposed approach. The proposed SC-DD outperforms all previous state-of-the-art supervised dataset distillation methods when employing larger models, such as  $SRe^{2}L$ , MTT, TESLA, DC, CAFE, etc., by large margins under the same recovery and posttraining budgets.

Keywords: Dataset Distillation and Condensation · Self-supervsied Pretraining · BatchNorm Variance

## 1 Introduction

Large-scale datasets and models are two major thrusts for the current remarkable achievements in the domains of computer vision [\[14,](#page-15-0)[19,](#page-15-1)[28\]](#page-16-0), natural language processing  $[3, 25, 38]$  $[3, 25, 38]$  $[3, 25, 38]$  $[3, 25, 38]$  $[3, 25, 38]$  and speech  $[17, 21]$  $[17, 21]$  $[17, 21]$ . In the field of dataset distillation, sev-eral studies, including MTT [\[7\]](#page-14-1),  $\text{SRe}^2L$  [\[57\]](#page-17-0), TESLA [\[12\]](#page-15-5), and CDA [\[56\]](#page-17-1), have emphasized the crucial role of large-scale datasets. Yet, the significance of largescale models within dataset distillation task remains underacknowledged. The recent SRe2L approach [\[57\]](#page-17-0) suggests using a more expansive squeezing model for recovery. However, as these model sizes expand, supervised pretrained models in this approach face growing difficulties in retrieving learned knowledge during data synthesis with inferior performance. As shown in Fig.  $2$ ,  $\text{SRe}^2L$  experiences significant declines in performance as the size of the recovery models increases.

<span id="page-1-0"></span>Image /page/1/Figure/5 description: This is a line graph showing the accuracy of different models on three recovery models: ResNet-18, ResNet-50, and ResNet-101. The y-axis represents accuracy, ranging from 0 to 60. The x-axis represents the recovery models. The graph displays several lines representing different models and their performance. The legend indicates the following models: TESLA ResNet-18 (black circle), SRe2L ResNet-18 (red square), SRe2L ResNet-50 (red plus sign), SRe2L ResNet-101 (red inverted triangle), Ours ResNet-18 (green square), Ours ResNet-101 (green triangle), and Ours RegNet-x-8gf (green star). The graph also includes labels for 'SC-DD (Ours)' and 'SRe2L (NeurIPS'23)' and 'TESLA (ICML'23)' indicating specific methods or papers. The lines show varying trends in accuracy across the different recovery models, with some models showing increasing accuracy and others showing decreasing accuracy.

**Fig. 2:** Top-1 accuracy of  $SRe<sup>2</sup>L$  [\[57\]](#page-17-0) and our approach on full ImageNet-1K with recovery model scales from small to large. The recovery budget is  $1k$  iterations. Each curve presents the post validation on ResNet-{18, 50, 101} and RegNet-x-8gf.

In this work, we tackle the challenges associated with the complexities of compression during the pertaining stage. We investigate the reasons behind the supervised learned model's inefficacy in dataset distillation, especially as the model size increases. Our observations reveal that when a model undergoes supervised pretraining, the BN statistic distributions of both intermediate mean and variance across channels in a layer trend towards uniformity, as illustrated in Fig. [3](#page-2-0) (the top-right two sub-figures<sup>[1](#page-1-1)</sup>). Moreover, the larger the

<span id="page-1-2"></span>2

<span id="page-1-1"></span><sup>1</sup> x-axis represents the channel index and y-axis represents the value.

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Figure/1 description: The image displays a diagram illustrating a learning paradigm with three distinct branches. The top branch, labeled 'backbone-classifier end-to-end pretraining,' shows an input image of a dog feeding into a neural network, followed by output labels 'dog, cat, fish, ...' and two graphs: one for 'channel-wise mean' with values around 0, and another for 'channel-wise variance' with values around 0. The middle branch, labeled 'label-agnostic pretraining,' also shows an input image of a dog feeding into a neural network, but with a vertical line separating the network into two parts. The output is similar labels and two graphs: the 'channel-wise mean' graph shows fluctuating values, and the 'channel-wise variance' graph also shows fluctuating values. The bottom branch, labeled 'data-classifier alignment,' shows an input image of a dog feeding into a neural network with similar output labels. This branch then leads to a 'mean/variance matching' step, represented by a dashed square with noise, which is then fed into another neural network. This final network's output is labeled 'synthesis' and is accompanied by a grid of generated images, starting with noisy images at the top and progressing to more recognizable images of dogs and cats at the bottom. The overall figure is titled 'Figure 3: Overview of our learning paradigm.'

Fig. 3: Overview of our learning paradigm. The top-left subfigure is the paradigm of supervised pertaining with an end-to-end training scheme for both the backbone network and final alignment classifier. The bottom-left subfigure is the paradigm of our proposed procedure for dataset distillation: a backbone model is first pretrained using a self-supervised objective, then a linear probing layer is adjusted to align the distribution of pertaining and target dataset distribution. We do not fine-tune the backbone during the alignment phase to preserve the better intermediate distributions of mean and variance in batch normalization layers (illustrated in the middle yellow line chart of the figure). The bottom-middle subfigure is the data synthesis procedure and the left subfigure is the visualization of distilled images.

model, the more even and flatting this statistic distribution appears. Given that SRe2L [\[57\]](#page-17-0) employs these distributions as layer-wise and channel-wise labels to retrieve pretrained dataset details, the flattened distributions make it challenging for the model to discern and retain the most important and fine-grained information from the intricate knowledge.

To mitigate the limitations of inadequate supervisory signals in data synthesis from the current pretraining scheme, we initiate our investigation by examining how different pretraining objectives affect the distribution of intermediate features. This analysis leads to a basic understanding of why existing advanced dataset distillation approaches experience a decline in performance as the size of model increases<sup>[2](#page-2-1)</sup>. We then present a distributional pertaining paradigm that focuses on amplifying the magnitude of intermediate mean and variance of the backbone, in line with a model-label semantic alignment learning process. This resembles the traditional self-supervised pretraining combined with the linear probing method. However, it is important to note that our pretraining does not always align with the same datasets. For instance, we might pretrain our backbone using the larger-scale ImageNet-21K and subsequently align it with ImageNet-1K dataset, leveraging the richer information in the larger dataset.

We present a concrete definition of compression in our framework, and explore its significance in the context of dataset distillation. The ability to compress a

<span id="page-2-1"></span><sup>&</sup>lt;sup>2</sup> Here, we indicate the model size in recovery stage instead of post-validation stage.

dataset by a model is partially measured by the accuracy of the model derived. In the scenario of supervised training, this pertains directly to the accuracy achieved on validation data. When dealing with a self-supervised learning approach, we employ linear probing, solely tuning the final classifier to align with the semantic labels. However, a good compression method in dataset distillation means the learned model can recover more informative data which is beneficial in subsequent training on synthetic data. We observe that a self-supervised learned model with linear probing (to preserve distribution on the backbone learned during compression) will have lower accuracy in the pretraining stage, but is easier for data synthesis and post-training in dataset distillation task.

Our framework enjoys at least the following three advantages: (1) Amplified supervision signals in data synthesis from the pertaining. (2) A positive correlation between model size in recovery stage and performance in post-training stage, which provides more potential for scaling-up this problem. (3) The simplicity of whole learning process while offering state-of-the-art performance. We highlight that large models matter for data synthesis in dataset distillation, but previous approaches failed to employ large-scale models in both data synthesis and post-training phases on this task. This work represents the inaugural and pioneering effort to integrate self-supervised pretraining into the dataset distillation, forming a simple approach that currently yields competitive accuracy. The proposed procedure can be regarded as a decoupling process of intermediate feature distribution learning and high-level semantic alignment, which entirely differs from previous dataset distillation solutions. Our approach consistently improves the performance when the size of recovering model becomes larger.

Despite its conceptual simplicity, we show empirically that with the proposed solution, namely Self-supervised Compression for Dataset Distillation (SC-DD), our approach achieves significantly better accuracy than the prior state-of-theart MTT, SRe2L, TESAL, DM with the better scalability to larger recovery model architectures. The performance gains are evident across various datasets: SC-DD obtains  $53.4\%$  on CIFAR-100,  $45.9\%$  on Tiny-ImageNet, and  $53.1\%$  on ImageNet-1K under IPC 50, surpassing previous the best by remarkable margins. More importantly, our approach demonstrates a positive correlation between model size and performance, that is, as the recovery model scales, we observe a corresponding uptick in efficacy, thus amplifying its prospective advantages. This characteristic gains heightened significance in the era dominated by largescale models and datasets, providing indispensable value for scaling up dataset distillation task.

Our contributions:

- We identify the drawback of previous state-of-the-art approach in dataset distillation that larger recovering models are consistently inferior under the supervised learning scheme. We uncover that the reason stems from the less informative mean and variance distributions for data synthesis.
- To our best knowledge, this is the pioneering work to reveal that selfsupervised intermediate distributions are more informative for dataset distillation. Our work highlights the importance of pertaining scheme, and we provide detailed intuitions and analyses both empirically and theoretically.

<span id="page-4-0"></span>– Extensive experiments are conducted on various CIFAR-100, Tiny-ImageNet, ImageNet-1K datasets and diverse model architectures. The proposed selfsupervised learning framework outperforms all prior supervised dataset distillation counterparts by significant margins.

## 2 Approach

Preliminaries: Dataset Distillation. Given an original large-scale dataset, Dataset Distillation (DD) seeks to generate a significantly condensed dataset comprised of synthetic samples with corresponding one-hot or soft labels. Despite its smaller size, models trained on this distilled dataset can achieve performance levels akin to those trained on the original dataset. Let  $\mathcal{D}_l = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}_{i=1}^{|\mathcal{D}_l|}$  be a large labeled dataset, our goal is to synthesize a more concise distilled dataset, which we will denote as  $\mathcal{D}_d = \{(\mathbf{x}'_i, \mathbf{y}'_i)\}_{i=1}^{|\mathcal{D}_d|}$ . In this distilled dataset,  $\mathbf{y}'$  represents either a hard or soft label corresponding to the synthetic data  $x'$ . It is also worth noting that the size of the distilled dataset,  $|\mathcal{D}_d|$ , is significantly smaller than the original dataset  $|\mathcal{D}_l|$ . Nonetheless,  $|\mathcal{D}_d|$  retains the crucial information from  $|\mathcal{D}_l|$ . Our learning task is then defined on this distilled synthetic dataset:

$$
\boldsymbol{\theta}_{\mathcal{D}_d} = \underset{\boldsymbol{\theta}}{\arg\min} \mathcal{L}_{\mathcal{D}_d}(\boldsymbol{\theta})
$$
\n(1)

where  $\theta$  is the model weights in post-training on synthetic data.  $\mathcal L$  is the objective function, e.g., cross-entropy loss.

$$
\mathcal{L}_{\mathcal{D}_d}(\boldsymbol{\theta}) = \mathbb{E}_{(\boldsymbol{x}', \boldsymbol{y}') \in \mathcal{D}_d} \left[ \ell \left( \mathcal{M}_{\boldsymbol{\theta}_{\mathcal{D}_d}}(\boldsymbol{x}') , \boldsymbol{y}' \right) \right]
$$
(2)

where  $\mathcal{M}_{\theta_{\mathcal{D}_d}}$  is the target model to train. The concrete  $\ell$  formulation can be the soft cross-entropy loss if  $y'$  is a soft label. The goal of dataset distillation is to generate synthetic data that aspires to achieve either a specific or minimal deviation in performance on the original dataset. This comparison is drawn between models trained on the synthetic data and that trained on the original dataset. Consequently, our goal is to optimize the synthetic dataset  $\mathcal{D}_d$  accordingly:

$$
\underset{\mathcal{D}_d, |\mathcal{D}_d|}{\arg \min} (\sup \{|\ell(\mathcal{M}_{\boldsymbol{\theta}_{\mathcal{D}_l}}(\boldsymbol{x}_{val}), \boldsymbol{y}_{val}) - \ell(\mathcal{M}_{\boldsymbol{\theta}_{\mathcal{D}_d}}(\boldsymbol{x}_{val}), \boldsymbol{y}_{val})|\}_{(\boldsymbol{x}_{val}, \boldsymbol{y}_{val}) \sim \mathcal{D}_l})
$$
(3)

where  $x_{val}$  and  $y_{val}$  are the real validation data and corresponding label in the original dataset  $\mathcal{D}_l$ . In the procedure, we learn both <data, label> $\in \mathcal{D}_d$  with the corresponding number of distilled data in each class.

**Previous Solutions on Large-scale Datasets.**  $SRe^{2}L$  [\[57\]](#page-17-0) represents the first approach to effectively compress the large-scale ImageNet-1K dataset while preserving its vital information and performance attributes. This method unfolds across three distinct phases: initially, a model undergoes training from scratch in a supervised scheme, ensuring it captures the majority of the significant information from the original dataset. Subsequently, in the second phase, a recovery procedure is employed to generate the intended data from Gaussian noise. Finally, in the third phase, the generated synthetic data undergoes a crop-level relabeling process to accurately represent the synthetic data's actual soft labels.

## <span id="page-5-0"></span>2.1 Understanding Dataset Compression

Typically, we can employ supervised learning (SL) or self-supervised learning (SSL) paradigms to compress knowledge from original large-scale datasets like ImageNet-1K and store information into a trained model with dense parameters for the subsequent synthesizing of distilled data. Fig. [3](#page-2-0) illustrates an overview of our learning paradigm. Recently, self-supervised representation learning approaches have demonstrated superior performance on representation capability over supervised models, especially in downstream tasks such as image recognition and dense prediction. In this section, we first discuss the expressivity and generalization of self-supervised pertaining for dataset distillation task, and then analyze the essence that the self-supervised representations of the mean and variance statistics are more informative than the supervised models.

Expressivity. This refers to the ability of a model to capture a wide variety of underlying patterns and structures in original data. We aim to address "Can the representation learned from SSL accurately express the inherent distributions for categories?" Prior study [\[31\]](#page-16-2) has proven that training a linear layer upon SSL yields a small approximation error for complex ground truth function class and can substantially reduce labeled sample complexity, which indicates SSL models are more expressive than supervised ones. Our visualization in Fig. [1](#page-0-0) also supports this statement.

Generalization. We aim to ensure that the data generated from various pretraining approaches achieves enhanced generalization across diverse architectures and tasks, akin to the performance observed on the real datasets. The prior study [\[22\]](#page-15-6) in SSL presented a measurement to offer an upper bound for the generalization ability in downstream classification tasks. This approach with the measure uncovers that the effectiveness of contrastive self-supervised learning hinges on three critical factors of the alignment of positive samples, the divergence of class centers, and the density of augmented data. The alignment pertains to the characteristics of the better learned representations by SSL.

Our Observations. Distinct objective functions such as SL and SSL invariably result in varying feature distributions. It has been noted that a supervised loss stabilizes the intermediate distribution, whereas a self-supervised loss emphasizes making the features more distinct. Furthermore, models trained with SSL demonstrate greater ease in synthesizing data during the process of dataset distillation. In this work, we focus on data synthesis that leverages pretrained representation distributions. We parameterize the final predictor as follows: given features  $f(x) \in \mathbb{R}^k$  for some feature extractor parameters  $w \in \mathcal{W}$ , and a linear "head"  $v \in \mathcal{V}$ , we have  $f_{v,w}(x) = v^\top f(x)$ . In our experiments, f is a deep network and  $v$  is a linear projection.

**Theorem 1.** Batch Normalization statistical parameters  $\Theta$  (mean  $\mu$  and variance  $\sigma^2$ ) derived from self-supervised contrastive learning are more fluctuant than those from supervised learning, which is more informative for dataset distillation recovery of image synthesis with higher entropy, i.e.,  $H(\Theta_{ssl}) > H(\Theta_{sl})$ .

**Problem statement:** Let  $\Theta_{ssl}$  denote the batch norm parameters (mean  $\mu_{ssl}$ and variance  $\sigma_{ssl}^2$ ) obtained from a model trained using self-supervised con-

<span id="page-6-1"></span><span id="page-6-0"></span>Image /page/6/Figure/1 description: The image contains two plots side-by-side. Both plots have 'value' on the y-axis and 'channel index' on the x-axis, ranging from 0 to 60. The left plot is titled 'Channel-mean-var' and shows four lines: MoCo-v3-ResNet50 (yellow), ResNet18 (red), ResNet50 (blue), and ResNet101 (green). A table within this plot shows 'MoCo v3' with a value of 233.19, 'ResNet18-sup' with 0.79, 'ResNet50-sup' with 0.12, and 'ResNet101-sup' with 0.05. The yellow line shows significant peaks around channel indices 25 and 45, reaching values of approximately 40 and 70 respectively. The other lines stay close to zero. The right plot is titled 'Channel-var-var' and displays the same four lines. A table within this plot shows 'MoCo v3' with a value of 28016.62, 'ResNet18-sup' with 0.14798, 'ResNet50-sup' with 0.00265, and 'ResNet101-sup' with 0.00253. The yellow line in this plot exhibits a very sharp peak around channel index 45, reaching a value of approximately 1300. Other lines remain close to zero.

Fig. 4: Illustration of mean (left) and variance (right) of the first BN layer in the residual block from self-supervised MoCo-v3-ResNet-50, supervised ResNet-{18, 50, 101}. In each subfigure, the x-axis represents the channel index, y-axis represents the corresponding value. The table inside each subfigure represents the variance across all channels, which reflects the fluctuation of statistics in the BN layer.

trastive learning, and let  $\Theta_{sl}$  denote the batch norm parameters (mean  $\mu_{sl}$  and variance  $\sigma_{sl}^2$ ) obtained from a model trained using traditional supervised learning. We claim that  $\Theta_{ssl}$  encapsulates a richer representation of the underlying data distribution compared to  $\Theta_{sl}$ . For a given dataset  $\mathcal{D}_l$  with data samples  $x_i \in \mathcal{D}_l$ , the batch norm parameters for self-supervised contrastive learning and supervised learning are as follows: 1) For self-supervised contrastive learning:

$$
\mu_{ssl} = \frac{1}{|\mathcal{D}_l|} \sum_{i=1}^{|\mathcal{D}_l|} f_{\theta}(x_i), \sigma_{ssl}^2 = \frac{1}{|\mathcal{D}_l|} \sum_{i=1}^{|\mathcal{D}_l|} (f_{\theta}(x_i) - \mu_{ssl})^2
$$
(4)

2) For supervised learning:

$$
\mu_{sl} = \frac{1}{|\mathcal{D}_l|} \sum_{i=1}^{|\mathcal{D}_l|} f_{\phi}(x_i), \sigma_{sl}^2 = \frac{1}{|\mathcal{D}_l|} \sum_{i=1}^{|\mathcal{D}_l|} (f_{\phi}(x_i) - \mu_{sl})^2
$$
(5)

where  $f_{\theta}$  and  $f_{\phi}$  are the feature-extracting functions of the models trained with self-supervised contrastive learning and supervised learning, respectively.

**Proof:** To prove that  $\Theta_{ssl}$  is more informative than  $\Theta_{sl}$ , we can analyze the entropy of the resulting feature distributions. The entropy of a distribution is a measure of its information content, with higher entropy indicating a more informative distribution. The entropy  $H$  of the batch-normalized features can be defined as:

$$
H(\Theta) = -\int p_{\Theta}(x) \log p_{\Theta}(x) dx \tag{6}
$$

where  $p_{\Theta}(x)$  is the probability density function of the batch-normalized features.

Assuming that the amount of pretraining data is large and diverse enough, according to the central limit theorem [\[42\]](#page-16-3), the features follow a Gaussian distribution post-normalization, we can simplify the entropy of both distributions to (detailed proof is provided in Appendix.):

$$
H(\Theta_{ssl}) = \frac{1}{2}\log(2\pi e \sigma_{ssl}^2), H(\Theta_{sl}) = \frac{1}{2}\log(2\pi e \sigma_{sl}^2)
$$
\n(7)

This expression gives us the entropy of a Gaussian distribution in terms of its variance  $\sigma^2$ . The entropy is maximized when the variance is large, indicating that a broader distribution (more uncertainty) leads to higher entropy.

Given that self-supervised contrastive objective encourages a model to learn an embedding space where similar samples are close to each other and dissimilar ones are further apart, this process preserves more intrinsic data variability compared to supervised learning which may collapse representations to discriminative features relevant only to class labels.

Consequently, we hypothesize that  $\sigma_{ssl}^2 > \sigma_{sl}^2$ , leading to  $H(\Theta_{ssl}) > H(\Theta_{sl})$ . To empirically validate this hypothesis, one would conduct extensive experiments to compare the variances of features obtained through both learning methods across various datasets, as shown in Fig. [4.](#page-6-0)

Hence, under the assumption that self-supervised contrastive learning leads to a greater preservation of data variability, the batch normalization parameters derived from this approach can be considered more informative than those derived from supervised learning, as evidenced by the greater entropy in the batch-normalized feature distribution.

How to Choose Optimal Pretrained Models. One of our key contributions is the proposed selection criterion for the BN-matching-based dataset distillation frameworks. Through theoretical proof and empirical experimental phenomenon analysis, we clarify that the degree of variation in these BN statistics is the key for enjoying the large capability of the BN-matching data synthesis.

<span id="page-7-0"></span>Image /page/7/Figure/6 description: The image contains two plots showing loss values over iteration index. The left plot shows 'MoCo-v2-BN-Loss' in yellow and 'Sup-ResNet18-BN-Loss' in red. The y-axis is labeled 'Loss Value' and ranges from 0 to 120. The x-axis is labeled 'Iteration Index' and ranges from 0 to 1000. Both lines start at a high loss value and decrease over iterations, with the yellow line generally showing higher and more fluctuating values than the red line. The right plot shows 'MoCo-v2-CE-Loss' in yellow and 'Sup-ResNet18-CE-Loss' in red. The y-axis is labeled 'Loss Value' and ranges from 0 to 6. The x-axis is labeled 'Iteration Index' and ranges from 0 to 1000. This plot includes a magnified inset showing the behavior of the lines between approximately iteration 0 and 200. In this plot, both lines start at a high loss value and decrease. The red line exhibits periodic spikes, reaching values around 3.5, while the yellow line stays closer to a value of 2.5.

Fig. 5: Loss trajectories during data synthesis. Left subfigure illustrates the BN loss term and right subfigure illustrates the CE loss term. The backbone is ResNet-18 for both self-supervised and supervised training schemes.

Optimization Effects. We explore how the pertaining scheme and BN statistic distributions affect the optimization during data recovery. As shown in Eq. [9](#page-9-0) of the following Sec. [2.4,](#page-8-0) our objective for recovery is  $\mathcal{L}_{CE} + \mathcal{L}_{BN}$  matching. To understand the optimization effects using this loss combination, we visualize the individual loss trajectories for both  $\mathcal{L}_{CE}$  and  $\mathcal{L}_{BN}$ . As shown in Fig. [5,](#page-7-0) we have two interesting observations: Firstly, from left subfigure we can see that when employing the self-supervised pretrained model, larger  $\mathcal{L}_{\rm BN}$  loss at the early stage occurs which is naturally expected as the variance of BN statistics is larger upon the discussion above, while the final loss magnitude is similar to <span id="page-8-2"></span>the ones that are from the supervised model, this phenomenon aligns with our conjecture that the variance of BN supervision helps optimize the synthetic data in early and middle stages more thoroughly, leading to higher generation quality. More interestingly, the second observation from the right subfigure shows that the varied BN supervisions can even stabilize the loss curve of the main  $\mathcal{L}_{CE}$ term. We emphasize that these two findings have not been found or discussed by any prior literature in the dataset distillation task.

### 2.2 Model-Data Alignment

Finetuning [\[45,](#page-17-2) [58\]](#page-17-3) is a common practice for adapting a pretrained model to specific target data. However, this method alters the pretrained model's parameters  $\Theta_{ssl}$ , potentially disrupting the original BN distributions. Linear probing is an alternative way for aligning pretrained model to the target dataset. We found it is more effective than fine-tuning for data synthesis, which has also demonstrated superiority in other tasks like domain adaptation [\[26,](#page-16-4) [29\]](#page-16-5). Given that  $f_{v,w}(x) = v^\top f(x)$ , our approach involves keeping f frozen while focusing on learning  $v$ . This strategy effectively separates the learning of intermediate feature distributions from the alignment of higher-level semantic information.

## 2.3 Imbalanced BN Statistic Distribution Matching

In the data synthesis phase, we use current batch's mean and variance statistics to match the self-supervised pretrained global statistics. Unlike  $S\text{Re}^2L$ , we apply new imbalanced coefficients according to the property of pretrained models as the objective, which can be formulated as an imbalanced batch-match-global manner:

<span id="page-8-1"></span>
$$
Rreg(x′)=∑kβk‰μk(x′)-E(μk|Dk)‰22+∑kγk‰σk2(x′)-E(σk2|Dk)‰22≈∑kβk‰μk(x′)-BNkRM‰22+∑kγk‰σk2(x′)-BNkRV‰22
$$
 $(8)$ 

where k is the index of BN layer,  $\mu_k(\boldsymbol{x}')$  and  $\sigma_k^2(\boldsymbol{x}')$  are the channel-wise mean and variance in current batch data.  $\mathbf{BN}_k^{\mathrm{RM}}$  and  $\mathbf{BN}_k^{\mathrm{RV}}$  are mean and variance in the pretrained model at k-th BN layer, which are globally counted.  $\beta_k$  and  $\gamma_k$  are coefficients to control the contributions of different layers. In our experiments, we also tried to make these hyperparameters to be learnable and the accuracy is similar to our grid search experiments in Table [5.](#page-11-0)

## <span id="page-8-0"></span>2.4 A Simple DD Framework via Self-supervised Pretraining

As illustrated in Fig. [3,](#page-2-0) here we summarize how to develop a strong data distillation baseline by integrating insights and methods from above analyses alongside established optimization strategies. We show that our simple framework already achieves state-of-the-art accuracy using the same evaluation protocol. This can be a crucial contribution towards understanding the true impact of the method for dataset distillation and towards minimizing the true gap between the distilled datasets and full datasets. We leverage self-supervised learned models and apply the data synthesis objective as follows:

<span id="page-9-0"></span>
$$
\underset{\mathcal{D}_{d},|\mathcal{D}_{d}|}{\arg \min} \ell\left(\mathcal{M}_{\boldsymbol{\theta}_{\mathcal{D}_{l}}}\left(\boldsymbol{x}'_{\mathrm{syn}}\right), \boldsymbol{y}\right) + \alpha \mathcal{R}_{\mathrm{reg}} \tag{9}
$$

<span id="page-9-1"></span>where  $\mathcal{M}_{\theta_{\mathcal{D}_l}}$  is the SSL pretrained model after alignment. The first term is the cross-entropy loss with ground-truth, and the second is the BN matching loss.  $\alpha$ is the coefficient to balance the contributions of these two losses.

#### 2.5 Post-training for Validation

Similar to [\[57\]](#page-17-0), a pre-generated soft label [\[46\]](#page-17-4) scheme is employed to eliminate the teacher models trained on the original dataset in post-validation. The posttraining objective is:

### 2.5 Post-training for Validation

where  $x'_{\mathbf{R}_i}$  is the *i*-th crop in the synthetic image and  $y'_i$  is the corresponding soft label.  $y_i'$  is from the SSL pretrained model after alignment. Finally, we can train the model  $\phi_{\theta_{\mathcal{D}_d}}$  on the synthetic data using the objective.

### 3 Experiments

## 3.1 Datasets and Implementation Details

We verify the effectiveness of our approach on various datasets, including CIFAR-100 [\[27\]](#page-16-6), Tiny-ImageNet [\[30\]](#page-16-7), and ImageNet-1K [\[13\]](#page-15-7). CIFAR-100 contains 50K images with  $32\times32$  pixels categorized into 100 classes. Tiny ImageNet comprises 200 classes with 500 images of  $64\times64$  resolution per class. ImageNet-1K consists of approximately 1.2M training images with  $224\times224$  resolution into 1000 classes. We squeeze training datasets based on the self-supervised pretraining and evaluate the performance of synthetic data using original validation datasets. Except the self-supervised pertaining, recovery and post-training configurations follow the protocol of  $S\text{Re}^{2}L$  [\[57\]](#page-17-0). Specifically, we use fewer 3K iter-ations in recovery according to our ablations in Table [4](#page-11-1) instead of  $4K$  as  $SRe<sup>2</sup>L$ used. The post-training budgets on various datasets are the same as  $\text{SRe}^2L$  [\[57\]](#page-17-0) and we examine more network structures for the cross-architecture evaluation, such as RegNet-X-8gf [\[41\]](#page-16-8), SqueezeNetV1.0 [\[23\]](#page-15-8), MobileNet-V3-L [\[20\]](#page-15-9), and Shuffl $eNetV2-0.5x$  [\[35\]](#page-16-9). The computational resources employed in these experiments include the NVIDIA A100 (40G) and 4090 GPUs. More details (e.g.,  $\alpha$ ) regarding our experimental settings can be referred to our Appendix.

### 3.2 Comparison with State-of-the-art Approaches

In this section, we compare the performance of our approach with current stateof-the-art methods in Table [1.](#page-10-0) These results demonstrate the effectiveness of our method in various large-scale dataset distillation scenario.

CIFAR-100. The results are presented in the first group of Table [1.](#page-10-0) MoCo-v2- 200ep ResNet-18 is employed as the compression model, with the same architecture ResNet-18 used in SRe<sup>2</sup>L serving as the validation model. Our approach achieves a significant 4.0% enhancement over SRe<sup>2</sup>L when the IPC is set to 50.

<span id="page-10-2"></span><span id="page-10-0"></span>Table 1: Comparison of dataset distillation and coreset selection methods. We trained 3 times each to get  $\bar{m} \pm n$ . \* indicates the result is not provided. SRe<sup>2</sup>L [\[57\]](#page-17-0), ours and whole data results are based on the ResNet-18 as the backbone.

|               |                        | IPC Ratio %               |               | Coreset Selection                            |                                                                                                                                                         |                                                                                  |                                                                                                                | Training Set Synthesis                                        |                                                    |                                          |                                                                                                                                   | Whole |
|---------------|------------------------|---------------------------|---------------|----------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------|----------------------------------------------------|------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|-------|
|               |                        |                           | Random        |                                              | Herding Forgetting                                                                                                                                      | DM [61]                                                                          | CAFE+DSA [51]                                                                                                  | $MTT$ $[7]$                                                   | TESLA $[12]$ SRe <sup>2</sup> L $[57]$             |                                          | Ours                                                                                                                              | Data  |
| $CIFAR-100$   | 10<br>50               | 0.2<br>$^{2}$<br>10       |               |                                              | $4.2 \pm 0.3$ $8.4 \pm 0.3$ $4.5 \pm 0.2111.4 \pm 0.3$<br>$14.6 \pm 0.5$ 17.3 $\pm$ 0.3 15.1 $\pm$ 0.3 29.7 $\pm$ 0.3                                   |                                                                                  | $14.0 \pm 0.3$<br>$31.5 \pm 0.2$<br>$30.0 \pm 0.4$ 33.7 $\pm$ 0.5 30.5 $\pm$ 0.3 43.6 $\pm$ 0.4 42.9 $\pm$ 0.2 | $24.3 \pm 0.3$ 24.8 ± 0.4<br>$40.1 \pm 0.4$<br>$47.7 \pm 0.2$ | $41.7 \pm 0.3$<br>$47.9 \pm 0.3$                   | $\overline{\phantom{0}}$<br>$49.4 \pm$ * | $\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$53.4 \pm 1.1$                                                            | 79.1  |
| Tiny ImageNet | 10<br>50               | 0.2<br>2<br>10            | $1.4 \pm 0.1$ | $2.8 \pm 0.2$                                | $1.6 \pm 0.11$ 3.9 $\pm$ 0.2<br>$5.0 \pm 0.2$ 6.3 $\pm$ 0.2 5.1 $\pm$ 0.2 12.9 $\pm$ 0.4<br>$15.0 \pm 0.4$ 16.7 $\pm$ 0.3 15.0 $\pm$ 0.3 24.1 $\pm$ 0.3 |                                                                                  | $\overline{\phantom{a}}$                                                                                       | $8.8 \pm 0.3$<br>$23.2 \pm 0.2$<br>$28.0 \pm 0.3$             | $\overline{a}$<br>$\overline{a}$<br>$\overline{a}$ |                                          | $\overline{\phantom{a}}$<br>$31.6 \pm 0.1$<br>$41.1 \pm 0.4$ $45.9 \pm 0.2$                                                       | 61.2  |
| ImageNet-1K   | 10<br>50<br>100<br>200 | 0.8<br>3.9<br>7.8<br>11.7 |               | $\overline{\phantom{a}}$<br>$\sim$<br>$\sim$ | $\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$                                                                        | $\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$ | $\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$                                                           | ۰<br>۰<br>۰                                                   | $17.8 \pm 1.3$<br>$27.9 \pm 1.2$                   |                                          | $21.3 \pm 0.6$ 32.1 $\pm$ 0.21<br>$46.8 \pm 0.2$ 53.1 $\pm$ 0.1<br>$52.8 \pm 0.3$ 57.9 $\pm$ 0.1<br>$57.0 \pm 0.4$ 63.5 $\pm$ 0.1 | 73.2  |

Tiny-ImageNet. The results of Tiny-ImageNet in the second group of Table [1](#page-10-0) show that our approach consistently outperforms SRe<sup>2</sup>L, achieving a higher validation accuracy with an improvement of 4.8% under the IPC setting of 50. ImageNet-1K. As shown in the third group of Table [1,](#page-10-0) our method demonstrates notable improvements compared to  $\text{SRe}^2L$ , with increases of 10.8%, 6.3%, 5.1%, and 6.5% under IPC 10, 50, 100, and 200, respectively. A more comprehensive comparison on various post-training architectures and IPCs is shown in Table [6,](#page-11-0) which demonstrates significant improvements over prior art  $\text{SRe}^2L$ .

### 3.3 Ablation

Our framework encompasses three core phases: compression, recovery, and validation. We carry out extensive ablation experiments in each phase to assess the various factors influencing the overall results.

<span id="page-10-1"></span>Image /page/10/Figure/6 description: This is a line graph showing the validation accuracy of two models, IPC10 and IPC50, over validation epochs. The x-axis represents the validation epoch, ranging from 200 to 1000. The y-axis represents the validation accuracy, ranging from 10 to 60. The IPC10 model, represented by a blue line with circular markers, shows an increasing accuracy from approximately 14% at epoch 100 to about 54% at epoch 1000. The IPC50 model, represented by an orange line with circular markers, shows a higher accuracy, starting at approximately 50% at epoch 100 and reaching about 66% at epoch 1000. The graph indicates that the IPC50 model consistently outperforms the IPC10 model in terms of validation accuracy.

Fig. 6: Top-1 accuracy on ImageNet-1K distilled images from MoCo v3 ResNet-50 under IPC 10 and 50 across various validation epochs. Post-validation models of ResNet-18 are saturated after 800 epochs in validation.

Compression Phase. (1) Selfsupervised Pretraining Budget: We examine the impact of the number of pretraining epochs in the compression phase, showcasing the validation accuracy across three models: ResNet-{18, 50, 101}. As shown in each group of Table [2,](#page-11-1) longer pretraining does not necessarily lead to better results. For instance, the optimal performance for MoCo v2 is observed at 200 epochs, aligning with our earlier assessments and discussions for compression that the variation in BN statistics is the key for the BN-matching-based data syn-

thesis, instead of the higher pretraining performance. We also show the channelwise variances of different self-supervised models' first layer BN mean in the last column, which aligns with the final performance well. (2) Architecture: We examine the effects of various pretrained model sizes and differing model widths on compression. (i) Model Size: Our analysis, as shown in the first group of Table [3,](#page-11-1) reveals that larger pretrained models generally can lead to higher validation accuracy. This finding supports the strategy of scaling up the pretrained

ImageNet-1K datasets recovered from dif-tilled datasets from MoCo v2 and SwAV. ferent pretrained models and epochs. Recovery models are ResNet-50 MoCo v2 [\[10\]](#page-15-10), MoCo v3 [\[11\]](#page-15-11), SwAV [\[5\]](#page-14-2), and DINO [\[6\]](#page-14-3).

|                | pretrain |       |       | validation accuracy $(\%)$ |                                   |
|----------------|----------|-------|-------|----------------------------|-----------------------------------|
|                | epoch    |       |       |                            | Res-18 Res-50 Res-101 BN-mean Var |
|                | 200      | 46.62 | 56.72 | 56.60                      | $5.98 \times 10^{-3}$             |
| MoCo $v2$ [10] | 400      | 45.51 | 55.12 | 55.93                      | $4.94 \times 10^{-3}$             |
|                | 800      | 39.35 | 49.93 | 50.28                      | $3.00 \times 10^{-3}$             |
|                | 100      | 49.75 | 57.95 | 57.24                      | $2.16\times10^{2}$                |
| MoCo $v3$ [11] | 300      | 53.13 | 60.92 | 60.98                      | $2.33\times10^{2}$                |
|                | 1000     | 50.62 | 59.21 | 59.23                      |                                   |
|                | 100      | 34.72 | 44.96 | 44.24                      | $2.03\times10^{2}$                |
|                | 200      | 47.55 | 57.32 | 56.26                      | $2.91\times10^{2}$                |
| $SwAV$ [5]     | 400      | 48.02 | 58.25 | 57.90                      | $3.82\times10^{2}$                |
|                | 800      | 49.70 | 58.70 | 56.39                      | $3.40\times10^{2}$                |
| DINO [6]       | 100      | 46.08 | 55.32 | 55.41                      |                                   |
|                | 800      | 47.27 | 51.73 | 54.40                      |                                   |

<span id="page-11-2"></span><span id="page-11-1"></span>Table 2: Validation results on distilled Table 3: ImageNet-1K val results on dis-

|        | pretrain model | validation accuracy (%) |              |              |
|--------|----------------|-------------------------|--------------|--------------|
|        |                | ResNet-18               | ResNet-50    | ResNet-101   |
| MoCov2 | ResNet-18      | 43.15                   | 53.41        | 53.72        |
|        | ResNet-50      | 46.62                   | 56.72        | 56.60        |
|        | ResNet-101     | <b>47.71</b>            | <b>57.12</b> | <b>56.88</b> |
| SwAV   | ResNet-50      | 48.02                   | 58.25        | 57.90        |
|        | ResNet-50-w2   | 49.80                   | <b>58.93</b> | 59.51        |
|        | ResNet-50-w4   | <b>49.83</b>            | 58.72        | 58.46        |
|        | ResNet-50-w5   | 49.59                   | 58.80        | <b>59.70</b> |

Table 4: Validation results on distilled ImageNet-1K datasets recovered from MoCo v3 with various recovery iterations.

| MoCo v3<br>recovery iteration | validation accuracy (%) |              |              |
|-------------------------------|-------------------------|--------------|--------------|
|                               | ResNet-18               | ResNet-50    | ResNet-101   |
| 1K                            | 51.36                   | 59.78        | 60.06        |
| 2K                            | 52.97                   | 60.63        | 60.09        |
| <b>3K</b>                     | <b>53.13</b>            | <b>60.92</b> | <b>60.98</b> |
| 4K                            | 51.90                   | 59.98        | 60.35        |

Table 5: Ablation on the coefficient of the first BN loss term in Eq. [8](#page-8-1) when  $k = 0$ , other layers' coefficients are set to 1 following [\[57\]](#page-17-0).

distilled ImageNet-1K data with IPC 50 from with  $SRe^{2}L$  [\[57\]](#page-17-0) on four different IPCs MoCo-v3-300ep ResNet-50 in recovery, and and three model architectures. ResNet-{18, 50, 101} in post-validation.

<span id="page-11-0"></span>The validation results are derived from the Table 6: Validation result comparison

| Frist BN multiplier<br>recovery parameter | validation accuracy (%) |              |              | Network    | Method     | validation accuracy (%) |             |             |             |
|-------------------------------------------|-------------------------|--------------|--------------|------------|------------|-------------------------|-------------|-------------|-------------|
|                                           | ResNet-18               | ResNet-50    | ResNet-101   |            |            | IPC=10                  | 50          | 100         | 200         |
| 1x                                        | 52.49                   | 60.10        | 58.98        | ResNet-18  | SRe²L [57] | 21.3                    | 46.8        | 52.8        | 57.0        |
| 5x                                        | 53.09                   | 60.43        | <b>61.11</b> | ResNet-18  | Ours       | <b>32.1</b>             | <b>53.1</b> | <b>57.9</b> | <b>63.5</b> |
| 10x                                       | <b>53.13</b>            | <b>60.92</b> | 60.98        | ResNet-50  | SRe²L [57] | 28.4                    | 55.6        | 61.0        | 64.6        |
| 15x                                       | 50.96                   | 58.15        | 56.29        | ResNet-50  | Ours       | <b>38.9</b>             | <b>60.9</b> | <b>65.8</b> | <b>67.8</b> |
| 20x                                       | 50.82                   | 57.69        | 57.30        | ResNet-101 | SRe²L [57] | 30.9                    | 60.8        | 62.8        | 65.9        |
|                                           |                         |              |              | ResNet-101 | Ours       | <b>39.6</b>             | <b>61.0</b> | <b>65.6</b> | <b>68.2</b> |

model size, a trend that is increasingly popular in current practices. Such scaling proves beneficial, significantly enhancing our model's performance. (ii) Model Width: The second group of Table [3](#page-11-1) indicates that while expanding the width of pretrained ResNet-50 model can lead to improved accuracy, widening it to w2 appears to be sufficient. This width demonstrates a performance nearly equivalent to that of w4 and w5 models.

Recovery Phase. (1) BN coeffcient: The term BN coeffcient refers to the first batch-norm multiplier parameter. As shown in Table [5,](#page-11-0) two among three of our highest accuracy are achieved with a BN setting of 10. Consequently, we adopt 10 for all subsequent experiments. It is important to note that the marked change in accuracy as BN values are adjusted. This transition, from rising to declining accuracy with increasing BN values, underscores the importance of carefully fine-tuning the BN coefficient to optimize model performance. (2) Iteration Budget: As in Table [4,](#page-11-1) we observe the optimal performance at 3K it-

<span id="page-12-1"></span><span id="page-12-0"></span>Image /page/12/Figure/1 description: The image displays three 3D scatter plots, each representing data points for three categories: Abacus (red), African Elephant (pink), and Albatross (orange). The plots are labeled (1) MTT, (2) SRe2L, and (3) Ours. Each plot shows a different distribution of these categories in a three-dimensional space, with the 'Ours' plot showing the clearest separation between the categories.

Fig. 7: Synthetic data clustering of MTT, SRe<sup>2</sup>L and ours on Tiny-ImageNet with three classes: Abacus, African Elephant and Albatross.

| <b>Table 7: Validation results using</b> |  |  |  |
|------------------------------------------|--|--|--|
| different datasets with distilled data   |  |  |  |
| from MoCo v2 pretrained models.          |  |  |  |

| MoCo v2 pretraining | validation accuracy ( $\%$ ) |           |            |
|---------------------|------------------------------|-----------|------------|
|                     | ResNet-18                    | ResNet-50 | ResNet-101 |
| ImageNet-1K         | 46.62                        | 56.72     | 56.60      |
| ImageNet-21K        | 40.55                        | 50.34     | 48.71      |
| CIFAR-100           | 56.63                        | 58.67     | 58.00      |
| Tiny-ImageNet       | 46.43                        | 47.85     | 49.14      |

erations with a noticeable turning point in accuracy occurring at this mark. This recovery budget is smaller than  $SRe^{2}L$  while achieving higher accuracy, indicating that simply increasing iterations does not linearly correlate with enhanced performance without employing a better synthesis approach. It also emphasizes the importance of a strategic approach in calibrating the iteration count.

Post-evaluation Phase. In this phase, we assess the effectiveness of various validation models, specifically ResNet-18, 50, and 101, in evaluating the quality of distilled images. As presented in different groups of Table [2,](#page-11-1) a general trend is observed where accuracy improves as the size of the model increases. However, it is particularly notable that ResNet-50 and ResNet-101 demonstrate similar performance levels. This trend is observed to be consistently maintained across various experimental setups, despite any changes in the experimental conditions. Moreover, as shown in Fig. [6,](#page-10-1) during the post-validation stage, we observe that longer training on the distilled images leads to higher accuracy, with significant enhancements across various IPC settings. However, it is important to note that models tend to reach a saturation point after approximately 800 epochs.

### 3.4 Analysis

Synthetic Image Clustering. We perform kmeans [\[36\]](#page-16-10) on the synthetic images with PCA [\[24\]](#page-15-12) (to reduce the input pixels to three dimensions) for obtaining the clustering distributions, as illustrated in Fig. [7.](#page-12-0) It reflects that our synthetic images have the best semantic capability than MTT and SRe<sup>2</sup>L.

Cross-Architecture. We conduct experiments to evaluate the validation accuracy of various models, using the MoCo v3 pretrained model for recovery. The models tested include ResNet-{18, 50, 101}, RegNet, SqueezeNet, MobileNet-v3, ShuffleNet, and Vision Transformer. The results are detailed in Table [8.](#page-13-0) Among these, RegNet-X-8gf achieves the highest accuracy.

When examining the ResNet model family, we observe that increasing model size did not consistently lead to better accuracy, particularly with ResNet-50 and 101. In contrast, the impressive result of RegNet-X-8gf shows the benefits of more fundamental architectural changes. Notably, our result on ViT-Tiny is significantly better than baseline  $SRe^2L$ , which demonstrates the effectiveness of our proposed framework. Overall, our results are consistently better than SRe<sup>2</sup>L.

Generally, models with larger parameter scales tend to deliver better performance across different architectures, as shown in Fig. [8.](#page-13-0) For smaller-scale

<span id="page-13-2"></span>models like ShuffleNet and SqueezeNet, the accuracy is limited to ∼20-30%. For larger models such as ResNet, they show noticeable improvements in performance. Interestingly,  $\text{SRe}^2$ L synthetic data on ViT-Tiny architecture does not perform well since ViT requires more training data to make it saturated, while for our framework, it dramatically improves the accuracy of  $\text{SRe}^2L$  by 38.72%, demonstrating the stronger capability and potential of our proposed approach.

<span id="page-13-0"></span>Image /page/13/Figure/2 description: This is a scatter plot showing the relationship between the number of parameters (in millions) on the x-axis and validation accuracy on the y-axis. The plot displays various neural network architectures, categorized by 'Ours' and 'SRe2L' prefixes, with different markers and colors representing different models. For example, 'Ours-ResNet-18' is marked with a pink triangle, 'Ours-ResNet-50' with a pink square, and 'SRe2L-ResNet-18' with a green triangle. Several data points are clustered around 0-10 parameters with accuracies between 10-30, while others are around 30-45 parameters with accuracies between 40-60. Specifically, 'ResNet-50' appears twice, once with around 25 parameters and 50 accuracy, and again with around 30 parameters and 60 accuracy. 'ResNet-101' is shown with approximately 40 parameters and 60 accuracy, and 'RegNet-X-8gf' is also around 40 parameters and 60 accuracy. 'ViT-Tiny' is plotted with around 5 parameters and 15 accuracy, and also with around 5 parameters and 55 accuracy.

Fig. 8: Top-1 accuracy of our framework on ImageNet-1K val set using various posttraining models at wide parameter scales.

**Table 8:** Top-1 val accuracy  $(IPC=50)$ with various validation models. Recovery model is pretrained by MoCo v3.

| Various Architectures Accuracy (%) |         |            |       |
|------------------------------------|---------|------------|-------|
| Model                              | #Params | SRe2L [57] | Ours  |
| ResNet-18                          | 11.7M   | 46.80      | 53.13 |
| ResNet-50                          | 25.6M   | 55.60      | 60.92 |
| ResNet-101                         | 44.5M   | 57.60      | 60.98 |
| RegNet-X-8gf                       | 39.6M   | 59.89      | 61.94 |
| SqueezeNet V1.0                    | 1.2M    | 14.66      | 22.25 |
| MobileNet-V3-L                     | 5.4M    | 45.19      | 51.65 |
| ShuffleNet V2-0.5x                 | 1.4M    | 16.71      | 27.48 |
| ViT-Tiny                           | 5.7M    | 15.41      | 54.13 |

Cross-Dataset. In the first group of Table [7,](#page-12-0) we employ a pretrained MoCo v2 model, initially pretrained on ImageNet-21K, for data compression to learn the intermediate distribution of ImageNet-21K dataset, and then apply ImageNet-1K's data for classifier alignment and subsequent validation. It is observed that the accuracy achieved with this cross-dataset approach is decent, but falls short of the standard method. For a more comprehensive comparison with conventional dataset methods, we incorporate CIFAR-100 and Tiny-ImageNet into the second group of this table.

### 3.5 Application: Data-free Pruning

The condensed dataset can be effectively utilized in many aspects, serving as a valuable resource for enhancing the model's adaptability to new information and tasks over time.

<span id="page-13-1"></span>

| Method | IPC 10     |              | IPC 50     |              |
|--------|------------|--------------|------------|--------------|
|        | SRe2L [57] | Ours         | SRe2L [57] | Ours         |
| Top-1  | 17.48      | <b>23.89</b> | 28.19      | <b>32.19</b> |

Table 9: Data-free pruning results. We apply our method to data-free pruning [\[48\]](#page-17-7) similar to the protocol demonstrated in [\[55\]](#page-17-8), using network slimming [\[34\]](#page-16-11) as the base pruning method and VGG-11 [\[47\]](#page-17-9) as the backbone. The resulting accuracy is shown in Table [9.](#page-13-1) It is evident that our approach outperforms the baseline approach significantly.

# 4 Related Work

Dataset Distillation. Dataset Distillation can be grouped into two categories: (1) Meta-learning based frameworks, including backpropagation-through-time methods, such as DD [\[52\]](#page-17-10), LD [\[2\]](#page-14-4), GTN [\[49\]](#page-17-11), and kernel ridge regression methods, such as KIP [\[37\]](#page-16-12), FRePo [\[64\]](#page-17-12). (2) Matching based frameworks, such as Gradient Match [\[62\]](#page-17-13), Batch-Norm Match [\[57\]](#page-17-0), Trajectory Match [\[7\]](#page-14-1) and Distribution Match [\[61\]](#page-17-5). Recently, distilling on large-scale datasets has received <span id="page-14-9"></span>significant attention in the community, and many works have been proposed, including [\[1,](#page-14-5)[8,](#page-14-6)[16,](#page-15-13)[33,](#page-16-13)[40,](#page-16-14)[44,](#page-16-15)[50,](#page-17-14)[53,](#page-17-15)[54,](#page-17-16)[56,](#page-17-1)[57,](#page-17-0)[63\]](#page-17-17). For the broader overview of related approaches for dataset distillation, please refer to [\[32,](#page-16-16) [43,](#page-16-17) [59\]](#page-17-18).

Self-supervised Learning. Self-supervised learning (SSL) has emerged as a significant area of research in unsupervised representation learning. The key idea behind SSL is to use the input data itself to generate supervisory signals, often through designing pretext tasks, thus alleviating the need for large amounts of labeled data. The approaches can be divided into the following categories: (1) Contrastive based methods, such as SimCLR [\[9\]](#page-14-7), MoCo [\[18\]](#page-15-14), Barlow Twins [\[60\]](#page-17-19). (2) Clustering based methods, such has SwAV [\[5\]](#page-14-2), DeepCluster [\[4\]](#page-14-8). (3) Distillation based methods, such as BYOL  $[15]$ , DINO  $[6]$ , DINO v2  $[39]$ .

## 5 Conclusion

In this study, we have delved into the challenges of data recovery for synthesizing by examining fluctuations in amplitude through mean and variance across channels. We pinpointed the bottlenecks of previous state-of-the-art methods that their inability to effectively scale up recovery model sizes is largely due to a flattened guidance distribution. To address this, we introduce SC-DD, a simple yet effective approach designed to amplify these fluctuations, allowing for a more nuanced capture of information during data synthesis. We hope that our contributions in this work can inspire further advancements in supervised or self-supervised compression methods tailored for large-scale dataset distillation.

## References

- <span id="page-14-5"></span>1. Abbasi, A., Shahbazi, A., Pirsiavash, H., Kolouri, S.: One category one prompt: Dataset distillation using diffusion models. arXiv preprint arXiv:2403.07142 (2024) [15](#page-14-9)
- <span id="page-14-4"></span>2. Bohdal, O., Yang, Y., Hospedales, T.: Flexible dataset distillation: Learn labels instead of images (2020) [14](#page-13-2)
- <span id="page-14-0"></span>3. Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J.D., Dhariwal, P., Neelakantan, A., Shyam, P., Sastry, G., Askell, A., et al.: Language models are few-shot learners. Advances in neural information processing systems 33, 1877–1901 (2020) [2](#page-1-2)
- <span id="page-14-8"></span>4. Caron, M., Bojanowski, P., Joulin, A., Douze, M.: Deep clustering for unsupervised learning of visual features (2019) [15](#page-14-9)
- <span id="page-14-2"></span>5. Caron, M., Misra, I., Mairal, J., Goyal, P., Bojanowski, P., Joulin, A.: Unsupervised learning of visual features by contrasting cluster assignments (2021) [12,](#page-11-2) [15,](#page-14-9) [23](#page-22-0)
- <span id="page-14-3"></span>6. Caron, M., Touvron, H., Misra, I., Jégou, H., Mairal, J., Bojanowski, P., Joulin, A.: Emerging properties in self-supervised vision transformers (2021) [12,](#page-11-2) [15,](#page-14-9) [23](#page-22-0)
- <span id="page-14-1"></span>7. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories (2022) [2,](#page-1-2) [11,](#page-10-2) [14](#page-13-2)
- <span id="page-14-6"></span>8. Chen, M., Huang, B., Lu, J., Li, B., Wang, Y., Cheng, M., Wang, W.: Dataset distillation via adversarial prediction matching. arXiv preprint arXiv:2312.08912 (2023) [15](#page-14-9)
- <span id="page-14-7"></span>9. Chen, T., Kornblith, S., Norouzi, M., Hinton, G.: A simple framework for contrastive learning of visual representations (2020) [15](#page-14-9)

- 16 M. Zhou et al.
- <span id="page-15-10"></span>10. Chen, X., Fan, H., Girshick, R., He, K.: Improved baselines with momentum contrastive learning (2020) [12,](#page-11-2) [23](#page-22-0)
- <span id="page-15-11"></span>11. Chen, X., Xie, S., He, K.: An empirical study of training self-supervised vision transformers (2021) [12,](#page-11-2) [23](#page-22-0)
- <span id="page-15-5"></span>12. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. In: International Conference on Machine Learning. pp. 6565–6590. PMLR (2023) [2,](#page-1-2) [11](#page-10-2)
- <span id="page-15-7"></span>13. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A largescale hierarchical image database. In: 2009 IEEE conference on computer vision and pattern recognition. pp. 248–255. Ieee (2009) [10](#page-9-1)
- <span id="page-15-0"></span>14. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. In: International Conference on Learning Representations (2021) [2](#page-1-2)
- <span id="page-15-15"></span>15. Grill, J.B., Strub, F., Altché, F., Tallec, C., Richemond, P.H., Buchatskaya, E., Doersch, C., Pires, B.A., Guo, Z.D., Azar, M.G., Piot, B., Kavukcuoglu, K., Munos, R., Valko, M.: Bootstrap your own latent: A new approach to self-supervised learning (2020) [15](#page-14-9)
- <span id="page-15-13"></span>16. Gu, J., Vahidian, S., Kungurtsev, V., Wang, H., Jiang, W., You, Y., Chen, Y.: Efficient dataset distillation via minimax diffusion. arXiv preprint arXiv:2311.15529 (2023) [15](#page-14-9)
- <span id="page-15-3"></span>17. Gulati, A., Qin, J., Chiu, C.C., Parmar, N., Zhang, Y., Yu, J., Han, W., Wang, S., Zhang, Z., Wu, Y., et al.: Conformer: Convolution-augmented transformer for speech recognition. Interspeech 2020 (2020) [2](#page-1-2)
- <span id="page-15-14"></span>18. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning. In: CVPR (2020) [15,](#page-14-9) [23](#page-22-0)
- <span id="page-15-1"></span>19. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016) [2](#page-1-2)
- <span id="page-15-9"></span>20. Howard, A., Sandler, M., Chu, G., Chen, L.C., Chen, B., Tan, M., Wang, W., Zhu, Y., Pang, R., Vasudevan, V., et al.: Searching for mobilenetv3. In: Proceedings of the IEEE/CVF international conference on computer vision. pp. 1314–1324 (2019) [10](#page-9-1)
- <span id="page-15-4"></span>21. Hsu, W.N., Bolte, B., Tsai, Y.H.H., Lakhotia, K., Salakhutdinov, R., Mohamed, A.: Hubert: Self-supervised speech representation learning by masked prediction of hidden units. IEEE/ACM Transactions on Audio, Speech, and Language Processing 29, 3451–3460 (2021) [2](#page-1-2)
- <span id="page-15-6"></span>22. Huang, W., Yi, M., Zhao, X., Jiang, Z.: Towards the generalization of contrastive self-supervised learning. In: The Eleventh International Conference on Learning Representations (2023) [6](#page-5-0)
- <span id="page-15-8"></span>23. Iandola, F.N., Han, S., Moskewicz, M.W., Ashraf, K., Dally, W.J., Keutzer, K.: Squeezenet: Alexnet-level accuracy with 50x fewer parameters and< 0.5 mb model size. arXiv preprint arXiv:1602.07360 (2016) [10](#page-9-1)
- <span id="page-15-12"></span>24. Jolliffe, I.T., Cadima, J.: Principal component analysis: a review and recent developments. Philosophical transactions of the royal society A: Mathematical, Physical and Engineering Sciences 374(2065), 20150202 (2016) [13](#page-12-1)
- <span id="page-15-2"></span>25. Kenton, J.D.M.W.C., Toutanova, L.K.: Bert: Pre-training of deep bidirectional transformers for language understanding. In: Proceedings of NAACL-HLT. pp. 4171–4186 (2019) [2](#page-1-2)

- <span id="page-16-4"></span>26. Kirichenko, P., Izmailov, P., Wilson, A.G.: Last layer re-training is sufficient for robustness to spurious correlations. In: The Eleventh International Conference on Learning Representations (2022) [9](#page-8-2)
- <span id="page-16-6"></span>27. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images (2009) [10](#page-9-1)
- <span id="page-16-0"></span>28. Krizhevsky, A., Sutskever, I., Hinton, G.E.: Imagenet classification with deep convolutional neural networks. Advances in neural information processing systems 25 (2012) [2](#page-1-2)
- <span id="page-16-5"></span>29. Kumar, A., Raghunathan, A., Jones, R., Ma, T., Liang, P.: Fine-tuning can distort pretrained features and underperform out-of-distribution. In: International Conference on Learning Representations (2022) [9](#page-8-2)
- <span id="page-16-7"></span>30. Le, Y., Yang, X.: Tiny imagenet visual recognition challenge. CS 231N 7(7), 3 (2015) [10](#page-9-1)
- <span id="page-16-2"></span>31. Lee, J.D., Lei, Q., Saunshi, N., Zhuo, J.: Predicting what you already know helps: Provable self-supervised learning. Advances in Neural Information Processing Systems 34, 309–323 (2021) [6](#page-5-0)
- <span id="page-16-16"></span>32. Lei, S., Tao, D.: A comprehensive survey to dataset distillation. arXiv preprint arXiv:2301.05603 (2023) [15](#page-14-9)
- <span id="page-16-13"></span>33. Liu, H., Xing, T., Li, L., Dalal, V., He, J., Wang, H.: Dataset distillation via the wasserstein metric. arXiv preprint arXiv:2311.18531 (2023) [15](#page-14-9)
- <span id="page-16-11"></span>34. Liu, Z., Li, J., Shen, Z., Huang, G., Yan, S., Zhang, C.: Learning efficient convolutional networks through network slimming. In: Proceedings of the IEEE international conference on computer vision. pp. 2736–2744 (2017) [14](#page-13-2)
- <span id="page-16-9"></span>35. Ma, N., Zhang, X., Zheng, H.T., Sun, J.: Shufflenet v2: Practical guidelines for efficient cnn architecture design. In: Proceedings of the European conference on computer vision (ECCV). pp. 116–131 (2018) [10](#page-9-1)
- <span id="page-16-10"></span>36. MacQueen, J., et al.: Some methods for classification and analysis of multivariate observations. In: Proceedings of the fifth Berkeley symposium on mathematical statistics and probability. vol. 1, pp. 281–297. Oakland, CA, USA (1967) [13](#page-12-1)
- <span id="page-16-12"></span>37. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression (2021) [14](#page-13-2)
- <span id="page-16-1"></span>38. OpenAI: Gpt-4 technical report (2023) [2](#page-1-2)
- <span id="page-16-18"></span>39. Oquab, M., Darcet, T., Moutakanni, T., Vo, H., Szafraniec, M., Khalidov, V., Fernandez, P., Haziza, D., Massa, F., El-Nouby, A., Assran, M., Ballas, N., Galuba, W., Howes, R., Huang, P.Y., Li, S.W., Misra, I., Rabbat, M., Sharma, V., Synnaeve, G., Xu, H., Jegou, H., Mairal, J., Labatut, P., Joulin, A., Bojanowski, P.: Dinov2: Learning robust visual features without supervision (2023) [15](#page-14-9)
- <span id="page-16-14"></span>40. Qin, T., Deng, Z., Alvarez-Melis, D.: Distributional dataset distillation with subtask decomposition. arXiv preprint arXiv:2403.00999 (2024) [15](#page-14-9)
- <span id="page-16-8"></span>41. Radosavovic, I., Kosaraju, R.P., Girshick, R., He, K., Dollár, P.: Designing network design spaces. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 10428–10436 (2020) [10](#page-9-1)
- <span id="page-16-3"></span>42. Rosenblatt, M.: A central limit theorem and a strong mixing condition. Proceedings of the national Academy of Sciences  $42(1)$ ,  $43-47$  (1956) [7,](#page-6-1) [19](#page-18-0)
- <span id="page-16-17"></span>43. Sachdeva, N., McAuley, J.: Data distillation: A survey. arXiv preprint arXiv:2301.04272 (2023) [15](#page-14-9)
- <span id="page-16-15"></span>44. Shao, S., Yin, Z., Zhou, M., Zhang, X., Shen, Z.: Generalized large-scale data condensation via various backbone and statistical matching. arXiv preprint arXiv:2311.17950 (2023) [15](#page-14-9)

- 18 M. Zhou et al.
- <span id="page-17-2"></span>45. Shen, Z., Liu, Z., Qin, J., Savvides, M., Cheng, K.T.: Partial is better than all: revisiting fine-tuning strategy for few-shot learning. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 35, pp. 9594–9602 (2021) [9](#page-8-2)
- <span id="page-17-4"></span>46. Shen, Z., Xing, E.: A fast knowledge distillation framework for visual recognition. In: European Conference on Computer Vision. pp. 673–690. Springer (2022) [10](#page-9-1)
- <span id="page-17-9"></span>47. Simonyan, K., Zisserman, A.: Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556 (2014) [14](#page-13-2)
- <span id="page-17-7"></span>48. Srinivas, S., Babu, R.V.: Data-free parameter pruning for deep neural networks. arXiv preprint arXiv:1507.06149 (2015) [14](#page-13-2)
- <span id="page-17-11"></span>49. Such, F.P., Rawal, A., Lehman, J., Stanley, K.O., Clune, J.: Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data (2019) [14](#page-13-2)
- <span id="page-17-14"></span>50. Sun, P., Shi, B., Yu, D., Lin, T.: On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. arXiv preprint arXiv:2312.03526 (2023) [15,](#page-14-9) [24](#page-23-0)
- <span id="page-17-6"></span>51. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: Cafe: Learning to condense dataset by aligning features (2022) [11](#page-10-2)
- <span id="page-17-10"></span>52. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation (2020) [14](#page-13-2)
- <span id="page-17-15"></span>53. Wu, Y., Du, J., Liu, P., Lin, Y., Cheng, W., Xu, W.: Dd-robustbench: An adversarial robustness benchmark for dataset distillation. arXiv preprint arXiv:2403.13322 (2024) [15](#page-14-9)
- <span id="page-17-16"></span>54. Xue, E., Li, Y., Liu, H., Shen, Y., Wang, H.: Towards adversarially robust dataset distillation by curvature regularization. arXiv preprint arXiv:2403.10045 (2024) [15](#page-14-9)
- <span id="page-17-8"></span>55. Yin, H., Molchanov, P., Alvarez, J.M., Li, Z., Mallya, A., Hoiem, D., Jha, N.K., Kautz, J.: Dreaming to distill: Data-free knowledge transfer via deepinversion. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 8715–8724 (2020) [14](#page-13-2)
- <span id="page-17-1"></span>56. Yin, Z., Shen, Z.: Dataset distillation in large data era. arXiv preprint arXiv:2311.18838 (2023) [2,](#page-1-2) [15](#page-14-9)
- <span id="page-17-0"></span>57. Yin, Z., Xing, E., Shen, Z.: Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In: NeurIPS (2023) [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-2) [5,](#page-4-0) [10,](#page-9-1) [11,](#page-10-2) [12,](#page-11-2) [14,](#page-13-2) [15](#page-14-9)
- <span id="page-17-3"></span>58. Yosinski, J., Clune, J., Bengio, Y., Lipson, H.: How transferable are features in deep neural networks? Advances in neural information processing systems 27 (2014) [9](#page-8-2)
- <span id="page-17-18"></span>59. Yu, R., Liu, S., Wang, X.: Dataset distillation: A comprehensive review. arXiv preprint arXiv:2301.07014 (2023) [15](#page-14-9)
- <span id="page-17-19"></span>60. Zbontar, J., Jing, L., Misra, I., LeCun, Y., Deny, S.: Barlow twins: Self-supervised learning via redundancy reduction (2021) [15](#page-14-9)
- <span id="page-17-5"></span>61. Zhao, B., Bilen, H.: Dataset condensation with distribution matching (2022) [11,](#page-10-2) [14](#page-13-2)
- <span id="page-17-13"></span>62. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching (2021) [14](#page-13-2)
- <span id="page-17-17"></span>63. Zhou, B., Zhong, L., Chen, W.: Improve cross-architecture generalization on dataset distillation. arXiv preprint arXiv:2402.13007 (2024) [15](#page-14-9)
- <span id="page-17-12"></span>64. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression (2022) [14](#page-13-2)

<span id="page-18-0"></span>

# Appendix

In the appendix, we provide more details for supplementing the main paper, including:

- Section [A:](#page-18-1) Proofs for Section 2 in the main paper.
- Section [B:](#page-19-0) Training time/computational cost analysis.
- Section [C:](#page-21-0) More implementation details.

• Section [D:](#page-22-1) Accuracy of self-supervised pretrained models for image synthesis/recovery.

- Section [E:](#page-22-2) Additional ablation studies.
- Section [F:](#page-23-1) Additional visualization.

<span id="page-18-1"></span>

# A Proofs for Section 2

Restatement of Equation 7 in the main paper. Assuming that the amount of pre-training data is large, and the data is diverse enough, according to the central limit theorem [\[42\]](#page-16-3), the features follow a Gaussian distribution postnormalization, we can simplify the entropy of both distributions to:

$$
H(\Theta_{ssl}) = \frac{1}{2}\log(2\pi e \sigma_{ssl}^2), H(\Theta_{sl}) = \frac{1}{2}\log(2\pi e \sigma_{sl}^2)
$$
(11)

**Proof:** The entropy of a continuous random variable (feature representation) is a measure of the uncertainty associated with its possible outcomes. For a Gaussian distribution, which is a continuous distribution, the entropy can be calculated using the differential entropy formula for a normal distribution. The probability density function (PDF) for a Gaussian distribution is given by:

$$
p(z) = \frac{1}{\sqrt{2\pi\sigma^2}} \exp\left(-\frac{(z-\mu)^2}{2\sigma^2}\right)
$$
 (12)

where z is the input feature representation,  $\mu$  is the mean of the feature representation distribution,  $\sigma$  is the standard deviation of the distribution,  $\sigma^2$  is the variance.

The differential entropy  $H$  for a continuous random representation with probability density function  $p(z)$  is given by:

$$
H(\Theta) = -\int_{-\infty}^{\infty} p(z) \log(p(z)) dz
$$
 (13)

Substituting the PDF of the Gaussian distribution into the entropy formula, we get:

$$
H(\Theta) = -\int_{-\infty}^{\infty} \frac{1}{\sqrt{2\pi\sigma^2}} \exp\left(-\frac{(z-\mu)^2}{2\sigma^2}\right)
$$

$$
\log\left(\frac{1}{\sqrt{2\pi\sigma^2}} \exp\left(-\frac{(z-\mu)^2}{2\sigma^2}\right)\right) dz
$$
 (14)

Then, we split the logarithm into two parts using the logarithm property:

$$
H(\Theta) = -\int_{-\infty}^{\infty} p(z) [\log \left( \frac{1}{\sqrt{2\pi\sigma^2}} \right) + \log \left( \exp \left( -\frac{(z-\mu)^2}{2\sigma^2} \right) \right)] dz
$$
 $(15)$ 

We further simplify the second term inside the integral by using the fact that  $\log(\exp(a)) = a.$ 

$$
H(\Theta) = -\int_{-\infty}^{\infty} p(z) \left[ -\log(\sqrt{2\pi\sigma^2}) - \frac{(z-\mu)^2}{2\sigma^2} \right] dz \tag{16}
$$

The integral of the Gaussian PDF  $p(z)$  over its entire range is 1, and the integral of  $p(z)$  times a quadratic function centered on its mean is simply the variance  $\sigma^2$ . So, the entropy simplifies to:

$$
H(\Theta) = -\int_{-\infty}^{\infty} p(z) \log p(z) dz
$$
  
= 
$$
-\mathbb{E}\left[\log \left[\left(2\pi\sigma^2\right)^{-1/2} \exp\left(-\frac{1}{2\sigma^2}(z-\mu)^2\right)\right]\right]
$$
  
= 
$$
\frac{1}{2}\log\left(2\pi\sigma^2\right) + \frac{1}{2\sigma^2}\mathbb{E}\left[(z-\mu)^2\right]
$$
  
= 
$$
\frac{1}{2}\log\left(2\pi\sigma^2\right) + \frac{1}{2}.
$$
 (17)

where  $\mathbb{E}[(z-\mu)^2] = \sigma^2$ . Finally, the entropy of z is a function of its variance  $\sigma^2$ .

$$
H(\Theta) = \frac{1}{2}\log(2\pi\sigma^2) + \frac{1}{2}
$$
 (18)

Since  $e$  is the base of the natural logarithm, the  $\frac{1}{2}$  outside the log can be taken inside to give e:

$$
H(\Theta) = \frac{1}{2}\log(2\pi e\sigma^2)
$$
 (19)

This final expression gives us the entropy of a Gaussian distribution in terms of its variance  $\sigma^2$ . The entropy is maximized when the variance is large, indicating that a broader distribution (more uncertainty) leads to higher entropy.

<span id="page-19-0"></span>

## B Computational Cost Analysis

We present the consumption of GPU hours in the processes of self-supervised squeezing, image synthesis/recovery, and validation/post-training.

Squeezing: The time costs associated with self-supervised pre-training and linear probing are listed under various model architectures, as outlined in Table [10.](#page-20-0)

<span id="page-20-1"></span>Image /page/20/Figure/1 description: This is a bar chart comparing the accuracy of three different ResNet models (ResNet-18, ResNet-50, and ResNet-101) under two different conditions: (1) SRe\$^2\$L and (2) Ours. The y-axis represents accuracy in percentage, ranging from 40% to 60%. For SRe\$^2\$L, ResNet-18 achieves an accuracy of approximately 44%, ResNet-50 around 46%, and ResNet-101 around 52%. For Ours, ResNet-18 achieves an accuracy of approximately 43%, ResNet-50 around 56%, and ResNet-101 around 59%.

 $(1)$  SRe<sup>2</sup>L (1) SRe<sup>2</sup>L (2) Ours (2) Ours (2) Ours (2) Ours (2) Ours **Fig. 9:** Top-1 accuracy on distilled images from different recovery model architectures with various validation models. In each subfigure, x-axis represents the validation model trained on the images synthesized from ResNet-{18, 50, 101} in each group of the subfigures, i.e., each column in the histogram.

Recovery: The time costs incurred during the recovery process, considering different model architectures (ResNet-18, ResNet-50, and ResNet-101), are detailed in Table [11.](#page-21-1)

Validation: The time costs in the validation phase, subject to varying IPC, are presented in Table [12.](#page-21-2) An increase in IPC is associated with an augmentation in time costs, concomitant with an increase in validation accuracy.

<span id="page-20-0"></span>

| Method  | Model        | Time (hours/per epoch) |                |
|---------|--------------|------------------------|----------------|
|         |              | pre-training           | linear probing |
| MoCo v2 | ResNet-18    | 0.14                   | 0.04           |
|         | ResNet-50    | 0.22                   | 0.06           |
|         | ResNet-101   | 0.26                   | 0.13           |
| MoCo v3 | ResNet-50    | -                      | 0.06           |
| SwAV    | ResNet-50    | -                      | 0.06           |
|         | ResNet-50-w2 | -                      | 0.09           |
|         | ResNet-50-w4 | -                      | 0.23           |
|         | ResNet-50-w5 | -                      | 0.33           |
| DINO    | ResNet-50    | -                      | 0.06           |

Table 10: Time consumption per epoch of self-supervised pre-training on ImageNet-1K with  $4 \times$  NVIDIA A100 (40G) GPUs. Pretraining time estimations for MoCo v3, SwAV and DINO are omitted as they are entirely the same as the default pretraning overhead. We use the off-the-shelf official pretrained models for them, and train a linear probing layer for each by ourselves.

| Recover Architecture | GPU hours |
|----------------------|-----------|
| ResNet18             | 7.78      |
| ResNet50             | 25.04     |
| ResNet101            | 35.44     |

<span id="page-21-2"></span><span id="page-21-1"></span>Table 11: Time consumption to generate distilled images on ImageNet-1K with IPC 50 using one 4090 GPU.

| IPC | GPU hours |
|-----|-----------|
| 10  | 0.75      |
| 50  | 2.53      |
| 100 | 4.58      |

Table 12: Post training GPU hours for every 100 epoch with MoCo v3 ResNet50 under various recovery IPCs using one 4090 GPU.

<span id="page-21-0"></span>

# C Implementation Details

We outline the parameter configurations for the processes of squeezing, recovering, and validation across CIFAR-100, Tiny-ImageNet, and ImageNet, as detailed in Table [14,](#page-22-3) Table [15,](#page-23-2) and Table [16,](#page-23-3) respectively.

CIFAR-100. The MoCo<sup>[3](#page-21-3)</sup> framework is used for pretraining ResNet-18, 50 backbones, achieving the linear accuracy presented in the first group of Table [18.](#page-24-0) Hyper-parameters from the second column of Table [14](#page-22-3) are utilized for the pretraining. Subsequently, the parameters from the third and fourth columns in the same table are employed, leading to a validation accuracy of 58.7% under IPC 50.

Tiny-ImageNet. The same MoCo framework as used on CIFAR-100 is adapted to pretrain ResNet-18, 50 backbones on Tiny-ImageNet dataset, their linear accuracy is provided in the second group of Table [18.](#page-24-0) The parameter settings outlined in Table [15](#page-23-2) contribute to achieving the validation accuracy reported in the main paper.

**ImageNet-1K.** The linear probing of MoCo v3 is employed using parameters specified in the second column of Table [16.](#page-23-3) Subsequently, parameters from the third and fourth columns in the same table are employed for the recovery and validation phases. Notably, a 600-ep training budget in validation phase is performed for achieving the final performance in the main paper, while a 300-ep training budget is applied in all other experiments for ablation studies.

Additionally, in Table [13,](#page-22-4) we provide the details of self-supervised objectives for different models, including MoCo v2, MoCo v3, SwAV, and DINO. The table also encompasses the corresponding hyper-parameters of learning rate and Batch Normalization coefficient in recovery phase.

<span id="page-21-3"></span> $^3$ https:// $\cosh$ .research.google.com/github/facebookresearch/moco/blob/ [colab-notebook/colab/moco\\_cifar10\\_demo.ipynb](https://colab.research.google.com/github/facebookresearch/moco/blob/colab-notebook/colab/moco_cifar10_demo.ipynb).

<span id="page-22-4"></span><span id="page-22-0"></span>

| pretraining type | method  | loss objective                                                                                          | lr   | BN coefficient |
|------------------|---------|---------------------------------------------------------------------------------------------------------|------|----------------|
| contrastive      | MoCo v2 | $\mathcal{L} = -\log \frac{\exp(q \cdot k_{+}/\tau)}{\sum_{i=0}^{K} \exp(q \cdot k_{i}/\tau)}$          | 0.35 | 0.25           |
|                  | MoCo v3 |                                                                                                         | 0.25 | 0.0005         |
| clustering       | SwAV    | $\sum_k \mathbf{q}_s^{(k)} \log \mathbf{p}_t^{(k)} - \sum_k \mathbf{q}_t^{(k)} \log \mathbf{p}_s^{(k)}$ | 0.3  | 0.001          |
| distillation     | DINO    | $\min_{\theta_s} H(P_t(x), P_s(x))$                                                                     | 0.25 | 0.01           |

<span id="page-22-3"></span>Table 13: Loss objectives and their corresponding hyperparameters for different pertaining methods.

| Config                 | Pretrain | Recover      | Validation     |
|------------------------|----------|--------------|----------------|
| $\alpha_{BN}$          | -        | 0.005        | -              |
| optimizer              | SGD      | Adam         | AdamW          |
| base learning rate     | 0.06     | 0.4          | 0.005          |
| weight decay           | $5e-4$   | $1e-4$       | 0.01           |
| optimizer momentum     | 0.9      | $(0.5, 0.9)$ | $(0.9, 0.999)$ |
| batch size             | 512      | 100          | 64             |
| learning rate schedule | cosine   | cosine       | cosine         |
| recovering iteration   | -        | 1,000        | -              |
| training epoch         | 200      | -            | 200            |

**Table 14:** Hyper-parameter setting on CIFAR-100. Optimizer parameters  $(\beta_1, \beta_2)$ represent the exponential decay rate for the first and second moment estimates.

<span id="page-22-1"></span>

## D Accuracy of Self-supervised Models for Recovery

We provide all our linear probing models that we used for image synthesis/recovery. On CIFAR-100 and Tiny-ImageNet datasets, we utilize ResNet-18 and ResNet50 models that are pretrained using MoCo [\[18\]](#page-15-14). The results are provided in Table [18.](#page-24-0) On ImageNet-1K, we employ various model architectures pretrained on MoCo V2 [\[10\]](#page-15-10), v3 [\[11\]](#page-15-11), SwAV [\[5\]](#page-14-2) and DINO [\[6\]](#page-14-3) with different pertaining budgets. The detailed results are shown in Table [17.](#page-24-1)

<span id="page-22-2"></span>

# E Additional Ablation Studies

We examine two factors that could influence the final accuracy in validation, including training budget and batch size.

Post-training budget. To investigate the impact of epochs on validation accuracy, we conduct experiments by evaluating at 10 distinct budgets of epochs (from 100 to 1,000) using distilled images with IPC 10 and 50, as illustrated in Fig. [6.](#page-10-1) The observed gradual improvement in accuracy with increasing epochs supports the assertion that augmenting the training epoch contributes to enhanced accuracy. However, this correlation holds true within a specific epoch range. As epochs continue to increase, the rate of validation accuracy improvement diminishes. Notably, an augmentation of epochs from 900 to 1,000 for IPC of 10 resulted in a decline in validation accuracy from 54.59% to 54.45%.

<span id="page-23-2"></span><span id="page-23-0"></span>

| Config                 | Pretrain | Recover    | Validation  |
|------------------------|----------|------------|-------------|
| $\alpha_{BN}$          | -        | 0.1        | -           |
| optimizer              | SGD      | Adam       | SGD         |
| base learning rate     | 0.06     | 0.6        | 0.2         |
| weight decay           | $5e-4$   | $1e-4$     | $1e-4$      |
| optimizer momentum     | 0.9      | (0.5, 0.9) | 0.9         |
| batch size             | 512      | 100        | 64          |
| learning rate schedule | cosine   | cosine     | cosine      |
| recovering iteration   | -        | 1,000      | -           |
| augmentation           | -        | $RRC^*$    | RandAugment |
| training epoch         | 200      | -          | 500         |

<span id="page-23-3"></span>Table 15: Hyperparameter setting on Tiny-ImageNet. \* represents RandomResized-Crop. Optimizer parameters  $(\beta_1, \beta_2)$  represent the exponential decay rate for the first and second moment estimates. We choose a slightly larger post-training budget, which aligns with RDED [\[50\]](#page-17-14).

| Config                 | Linear probing | Recover      | Validation     |
|------------------------|----------------|--------------|----------------|
| $\alpha_{BN}$          | -              | 0.0005       | -              |
| optimizer              | SGD            | Adam         | AdamW          |
| base learning rate     | 0.03           | 0.25         | 0.001          |
| weight decay           | $1e-4$         | $1e-4$       | 0.01           |
| optimizer momentum     | 0.9            | $(0.5, 0.9)$ | $(0.9, 0.999)$ |
| batch size             | 256            | 50           | 64             |
| learning rate schedule | cosine         | cosine       | cosine         |
| recovering iteration   | -              | 1,000        | -              |
| augmentation           | -              | $RRC^*$      | $RRC^*$        |
| training epoch         | 200            | -            | 300            |

Table 16: Hyper-parameter setting on ImageNet-1K. \* represents RandomResized-Crop. Optimizer parameters  $(\beta_1, \beta_2)$  represent the exponential decay rate for the first and second moment estimates.

Batch size. In Table [19,](#page-25-0) we provide the Top-1 accuracy of various batch sizes in the validation phase on the ImageNet-1K dataset. The accuracy demonstrates an upward trend as the batch size decreases, reaching its peak at a batch size of 64. Subsequently, a reduction in accuracy is observed with further decreases in batch size. Notably, the optimal accuracy is attained when employing a batch size of 64, prompting its consistent utilization in all our experiments conducted on ImageNet-1K.

Moreover, for a more comprehensive understanding of our method's capability to the state-of-the-art SRe<sup>2</sup>L, we illustrate Fig. [9,](#page-20-1) which shows that, contrary to the observed trend in SRe<sup>2</sup>L method (left subfigure) where larger models used for recovery lead to decreased post-validation performance, our method exhibits an inverse relationship. As the size of the recovery models increases, performance improves, indicating a higher potential in our approach.

<span id="page-23-1"></span>

# F Additional Visualization

We provide additional clustering visualization of semantically similar classes by MTT,  $SRe^{2}L$ , and our proposed SC-DD with three classes: Fly, Bee, and La-

<span id="page-24-1"></span>

| method  | model        | pretrain epochs | linear acc. |
|---------|--------------|-----------------|-------------|
| MoCo v2 | ResNet-50    | 100             | 64.7        |
|         |              | 200             | 67.6        |
|         |              | 400             | 69.6        |
|         |              | 800             | 71.1        |
|         | ResNet-18    | 200             | 53.3        |
|         | ResNet-18    | 800             | 53.5        |
|         | ResNet-101   | 200             | 69.9        |
|         | ResNet-101   | 800             | 73.0        |
| MoCo v3 | ResNet-50    | 100             | 68.9        |
|         |              | 300             | 72.8        |
|         |              | 1,000           | 74.6        |
| SwAV    | ResNet-50    | 100             | 72.0        |
|         |              | 200             | 73.8        |
|         |              | 400             | 74.5        |
|         |              | 800             | 75.3        |
|         | ResNet-50-w2 | 400             | 77.2        |
|         | ResNet-50-w4 | 400             | 77.4        |
|         | ResNet-50-w5 | 400             | 77.9        |
| DINO    | ResNet-50    | 100             | 66.8        |
|         |              | 800             | 75.3        |

<span id="page-24-0"></span>Table 17: Linear classification results on self-supervised models.

| dataset                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | $_{\rm model}$                                                     | linear acc   |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|--------------|
| $CIFAR-100$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ResNet-18<br>$ResNet-50$                                           | 60.8<br>60.8 |
| $\label{thm:1} \begin{minipage}{.4\linewidth} \begin{tabular}{l} \multicolumn{3}{c}{\textbf{Tiny-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Tiny-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf{Dly-ImageNet}}\\ \multicolumn{3}{c}{\textbf$ | $\begin{array}{c} \text{ResNet-18}\\ \text{ResNet-50} \end{array}$ | 45.4<br>51.0 |

Table 18: Linear classification results on small datasets, using MoCo to pretrain the backbone for 200 epochs.

dybug. As shown in Fig. [10,](#page-25-1) it can be observed that the data generated by our approach is more distinguishable in the low dimension space, indicating that thay have learned more semantic information in the synthetic data.

Furthermore, we visualize more synthetic images derived from Tiny-ImageNet and ImageNet-1K datasets in Fig. [11,](#page-26-0) [12](#page-27-0) and [13.](#page-28-0) We first provide the comparison of distilled images from MTT, SRe<sup>2</sup>L, and our proposed approach on Tiny-ImageNet, as in Fig. [11.](#page-26-0) It can be observed that the images generated by MTT have richer textures and color information, but are less realistic for the object of the class. Then, we conduct a comparative visualization of the distilled images from our proposed method and  $\text{SRe}^2L$  on ImageNet-1K, as illustrated in Fig. [12.](#page-27-0)

By comparing images generated by various approaches, we illustrate that the images refined through our approach possess greater realism and encapsulate a broader spectrum of information than those produced by competing methods. This evidence demonstrates the enhanced effectiveness of the proposed approach.

26 M. Zhou et al.

<span id="page-25-1"></span>Image /page/25/Figure/1 description: This image displays three 3D scatter plots, each representing data points categorized into three classes: Fly (red dots), Bee (pink dots), and Ladybug (orange dots). The plots are labeled (1) MTT, (2) SRe²L, and (3) Ours, suggesting a comparison of different methods or models. Each plot shows a different distribution and separation of the data points for the three classes, with the 'Ours' plot appearing to show the best separation between the classes.

<span id="page-25-0"></span>Fig. 10: Clustering visualization of semantically-similar synthetic data by MTT,  $\mathrm{SRe}^2\mathrm{L}$ and ours with three similar classes: Fly, Bee, and Ladybug.

| Batch Size | Accuracy     |
|------------|--------------|
| 128        | 58.68        |
| 96         | 59.34        |
| 48         | 59.98        |
| 64         | <b>60.92</b> |
| 32         | 58.31        |

Table 19: Validation results on synthetic dataset recovered from MoCo v3 300-ep pretrained ResNet-50 with various batch sizes.

|                                                                   |             |       |                            | recovery model $\frac{\text{validation accuracy } (\%)}{\text{ResNet-18 ResNet-101 RegNet-X-8gf}}$ |  |  |
|-------------------------------------------------------------------|-------------|-------|----------------------------|----------------------------------------------------------------------------------------------------|--|--|
| Ours                                                              | ResNet-18   | 43.15 | 53.72                      | 56.39                                                                                              |  |  |
|                                                                   | $ResNet-50$ | 46.62 | 56.60                      | 58.00                                                                                              |  |  |
|                                                                   | RegNet-101  | 47.71 | 56.88                      | 59.42                                                                                              |  |  |
| recovery model $\overline{\text{ResNet-18 ResNet-50 ResNet-101}}$ |             |       | validation accuracy $(\%)$ |                                                                                                    |  |  |
|                                                                   |             |       |                            |                                                                                                    |  |  |
| SRe <sup>2</sup> L                                                | ResNet-18   | 43.69 | 48.36                      | 51.57                                                                                              |  |  |
|                                                                   | $ResNet-50$ | 40.66 | 46.02                      | 49.06                                                                                              |  |  |
|                                                                   | ResNet-101  | 39.95 | 45.56                      | 48.92                                                                                              |  |  |

Table 20: Top-1 accuracy of ours (MoCo v2 based) and SRe<sup>2</sup>L on ImageNet-1K dataset.

<span id="page-26-0"></span>Image /page/26/Figure/1 description: This is a grid of images displaying synthetic data from Tiny ImageNet. The grid is organized into three rows labeled MTT, SRee²L, and SC-DD, and six columns. The first row shows images of a goldfish, a black widow spider, a goose, a koala, a snail, and a dragonfly. The second row shows similar subjects but with different visual representations. The third row also displays these subjects with further variations. Below the first set of columns, the labels Goldfish, Black Widow, Goose, Koala, Snail, and Dragonfly are present. Below the second set of columns, the labels Sea Slug, American Lobster, Spiny Lobster, Black Stork, Golden Retriever, and Guinea Pig are present, corresponding to the images in the rows above them. The overall image is a comparative display of synthetic data generation methods.

 ${\bf Fig. 11:}$  Comparative synthetic data on Tiny-ImageNet from MTT,  ${\rm SRe^2L}$  and our SC-DD.

<span id="page-27-0"></span>Image /page/27/Picture/1 description: The image displays a grid of synthetic images, organized into rows and columns, with labels indicating different categories and methods. The rows are labeled 'SRe²L' and 'SC-DD', suggesting two different approaches or datasets. The columns are labeled with names of objects or scenes: Goldfish, Snow Bird, Bulbul, Hair Slide, Reaper, House Finch, Indigo Bird, Mailbox, Megalith, Mobile Home, Mousetrap, Water Ouzel, Nail, Oscilloscope, Packet, Pirate Ship, Plate Rack, Partridge, Flowerpot, Quill, Reflex Camera, Worm Fence, Agaric, and Whiskey Jug. Each cell in the grid contains a synthetic image corresponding to the row and column labels. The overall presentation is a comparative display of synthetic data, likely for evaluation purposes.

Fig. 12: Comparative synthetic data on ImageNet-1K from  $\text{SRe}^2$ L and our *SC-DD*.

<span id="page-28-0"></span>Image /page/28/Picture/1 description: This is a grid of 40 images, arranged in 5 rows and 8 columns. The images appear to be synthetic data, possibly generated by a machine learning model, as suggested by the caption below the grid which reads "Fig. 13: Synthetic data on ImageNet 1K from our SG-DD". The images themselves are diverse, featuring a variety of subjects including animals (dogs, birds, cats, a kangaroo, a deer), objects (cars, trash cans, a coffee maker, a ship, a joystick, a bottle), landscapes, and abstract patterns. The overall quality and style of the images vary, with some appearing more realistic than others, and many exhibiting a somewhat dreamlike or distorted aesthetic, likely due to the synthetic generation process.

Fig. 13: Synthetic data on ImageNet-1K from our SC-DD.