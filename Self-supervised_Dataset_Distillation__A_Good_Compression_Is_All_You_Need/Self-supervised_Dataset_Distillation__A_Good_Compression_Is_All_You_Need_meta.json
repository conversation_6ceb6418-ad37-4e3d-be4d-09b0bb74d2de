{"table_of_contents": [{"title": "Self-supervised Dataset Distillation: A Good\nCompression Is All You Need", "heading_level": null, "page_id": 0, "polygon": [[151.505859375, 114.75], [462.0, 114.75], [462.0, 145.6962890625], [151.505859375, 146.25]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 1, "polygon": [[133.5, 313.5], [228.75, 313.5], [228.75, 325.23046875], [133.5, 325.23046875]]}, {"title": "4 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 3, "polygon": [[133.5, 92.25], [227.408203125, 92.25], [227.408203125, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "2 Approach", "heading_level": null, "page_id": 4, "polygon": [[133.5, 183.0], [212.25, 183.0], [212.25, 194.4228515625], [133.5, 194.4228515625]]}, {"title": "2.1 Understanding Dataset Compression", "heading_level": null, "page_id": 5, "polygon": [[133.5, 117.75], [345.75, 117.75], [345.75, 128.1005859375], [133.5, 128.1005859375]]}, {"title": "2.2 Model-Data Alignment", "heading_level": null, "page_id": 8, "polygon": [[133.4267578125, 212.25], [277.5, 212.25], [277.5, 222.169921875], [133.4267578125, 222.169921875]]}, {"title": "2.3 Imbalanced BN Statistic Distribution Matching", "heading_level": null, "page_id": 8, "polygon": [[133.5, 345.75], [401.25, 345.75], [401.25, 356.94140625], [133.5, 356.94140625]]}, {"title": "2.4 A Simple DD Framework via Self-supervised Pretraining", "heading_level": null, "page_id": 8, "polygon": [[132.8291015625, 555.75], [447.75, 555.75], [447.75, 566.54296875], [132.8291015625, 566.54296875]]}, {"title": "2.5 Post-training for Validation", "heading_level": null, "page_id": 9, "polygon": [[133.5, 189.75], [300.0, 189.75], [300.0, 199.16015625], [133.5, 199.16015625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 9, "polygon": [[133.5, 327.0], [229.6494140625, 327.0], [229.6494140625, 337.21875], [133.5, 337.21875]]}, {"title": "3.1 Datasets and Implementation Details", "heading_level": null, "page_id": 9, "polygon": [[133.5, 345.75], [349.03125, 345.75], [349.03125, 356.16796875], [133.5, 356.16796875]]}, {"title": "3.2 Comparison with State-of-the-art Approaches", "heading_level": null, "page_id": 9, "polygon": [[133.5, 567.75], [390.0, 567.75], [390.0, 577.37109375], [133.5, 577.37109375]]}, {"title": "3.3 Ablation", "heading_level": null, "page_id": 10, "polygon": [[133.5, 340.5], [204.0, 340.5], [204.0, 350.947265625], [133.5, 350.947265625]]}, {"title": "3.4 Analysis", "heading_level": null, "page_id": 12, "polygon": [[133.5, 444.75], [204.099609375, 444.75], [204.099609375, 455.16796875], [133.5, 455.16796875]]}, {"title": "14 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 13, "polygon": [[133.5, 92.25], [225.9140625, 92.25], [225.9140625, 101.900390625], [133.5, 101.900390625]]}, {"title": "3.5 Application: Data-free Pruning", "heading_level": null, "page_id": 13, "polygon": [[133.5, 447.0], [318.0, 447.0], [318.0, 457.48828125], [133.5, 457.48828125]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 13, "polygon": [[133.5, 573.75], [237.0, 573.75], [237.0, 585.87890625], [133.5, 585.87890625]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 14, "polygon": [[133.27734375, 261.0], [219.75, 261.0], [219.75, 272.25], [133.27734375, 272.25]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[133.5, 406.5], [198.0, 406.5], [198.0, 418.04296875], [133.5, 418.04296875]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 18, "polygon": [[133.5, 114.75], [202.5, 114.75], [202.5, 129.55078125], [133.5, 129.55078125]]}, {"title": "A Proofs for Section 2", "heading_level": null, "page_id": 18, "polygon": [[133.5, 274.5], [275.51953125, 274.5], [275.51953125, 286.55859375], [133.5, 286.55859375]]}, {"title": "20 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 19, "polygon": [[133.5, 92.25], [226.810546875, 92.25], [226.810546875, 102.287109375], [133.5, 102.287109375]]}, {"title": "B Computational Cost Analysis", "heading_level": null, "page_id": 19, "polygon": [[133.5, 581.23828125], [330.75, 581.23828125], [330.75, 592.83984375], [133.5, 592.83984375]]}, {"title": "C Implementation Details", "heading_level": null, "page_id": 21, "polygon": [[133.5, 303.0], [296.25, 303.0], [296.25, 314.7890625], [133.5, 314.7890625]]}, {"title": "D Accuracy of Self-supervised Models for Recovery", "heading_level": null, "page_id": 22, "polygon": [[133.5, 384.0], [447.046875, 384.0], [447.046875, 395.033203125], [133.5, 395.033203125]]}, {"title": "E Additional Ablation Studies", "heading_level": null, "page_id": 22, "polygon": [[133.5, 506.25], [322.13671875, 506.25], [322.13671875, 518.203125], [133.5, 518.203125]]}, {"title": "24 <PERSON><PERSON> et al.", "heading_level": null, "page_id": 23, "polygon": [[133.5, 92.25], [226.5, 92.25], [226.5, 101.70703125], [133.5, 101.70703125]]}, {"title": "F Additional Visualization", "heading_level": null, "page_id": 23, "polygon": [[133.5, 615.75], [300.0, 615.75], [300.0, 628.41796875], [133.5, 628.41796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 46], ["Text", 3], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6508, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 62], ["Text", 5], ["Reference", 3], ["TextInlineMath", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1362, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 49], ["Text", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Footnote", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 879, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 47], ["Text", 4], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 61], ["TextInlineMath", 4], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListItem", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 46], ["Text", 5], ["TextInlineMath", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 113], ["Equation", 4], ["TextInlineMath", 3], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 984, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 57], ["Text", 5], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 854, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 98], ["Text", 3], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1911, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 63], ["Text", 4], ["SectionHeader", 4], ["TextInlineMath", 3], ["Equation", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 67], ["TableCell", 65], ["Text", 4], ["Reference", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 753, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["TableCell", 366], ["Line", 84], ["Caption", 4], ["Table", 4], ["Text", 4], ["TableGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 21324, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["TableCell", 58], ["Line", 51], ["Text", 6], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 8072, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["TableCell", 92], ["Line", 88], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["Text", 2], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9882, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 45], ["Reference", 10], ["ListItem", 9], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 50], ["ListItem", 17], ["Reference", 16], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 192], ["Line", 50], ["ListItem", 19], ["Reference", 19], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 48], ["ListItem", 21], ["Reference", 20], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 316], ["Line", 75], ["Text", 7], ["ListItem", 5], ["Equation", 4], ["SectionHeader", 2], ["ListGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1058, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 104], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4770, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 92], ["TableCell", 76], ["Line", 34], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6682, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["Line", 40], ["TableCell", 32], ["Text", 7], ["Reference", 4], ["Table", 2], ["Caption", 1], ["SectionHeader", 1], ["Footnote", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2530, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["TableCell", 123], ["Line", 55], ["Reference", 5], ["Text", 3], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5793, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["TableCell", 176], ["Line", 46], ["Reference", 4], ["SectionHeader", 2], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5663, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 137], ["Span", 131], ["Line", 49], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 12042, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["TableCell", 56], ["Line", 27], ["Caption", 4], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1904, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 62], ["Line", 17], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 763, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 780, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 678, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Self-supervised_Dataset_Distillation__A_Good_Compression_Is_All_You_Need"}