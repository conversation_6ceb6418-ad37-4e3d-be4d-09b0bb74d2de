{"table_of_contents": [{"title": "Soft-Label Dataset Distillation and Text Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[90.75, 101.8037109375], [521.15625, 101.8037109375], [521.15625, 114.5654296875], [90.75, 114.5654296875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[279.0, 300.75], [332.296875, 300.75], [332.296875, 311.501953125], [279.0, 311.501953125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[88.9013671875, 588.0], [180.193359375, 588.0], [180.193359375, 599.02734375], [88.9013671875, 599.02734375]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 3, "polygon": [[89.25, 92.18408203125], [188.25, 92.18408203125], [188.25, 103.9306640625], [89.25, 103.9306640625]]}, {"title": "2.1 Knowledge Distillation", "heading_level": null, "page_id": 3, "polygon": [[89.25, 114.275390625], [239.2119140625, 114.275390625], [239.2119140625, 125.68359375], [89.25, 125.68359375]]}, {"title": "2.2 Learning from 'small' data", "heading_level": null, "page_id": 3, "polygon": [[89.25, 326.583984375], [259.083984375, 326.583984375], [259.083984375, 337.798828125], [89.25, 337.798828125]]}, {"title": "2.3 Dataset Reduction, Prototype Generation, and Summarization", "heading_level": null, "page_id": 3, "polygon": [[89.25, 496.546875], [459.298828125, 496.546875], [459.298828125, 507.375], [89.25, 507.375]]}, {"title": "2.4 Generative Adversarial Networks", "heading_level": null, "page_id": 4, "polygon": [[89.25, 201.48046875], [295.83984375, 201.48046875], [295.83984375, 213.08203125], [89.25, 213.08203125]]}, {"title": "2.5 Measuring Problem Dimensionality", "heading_level": null, "page_id": 4, "polygon": [[89.25, 384.78515625], [307.5, 384.78515625], [307.5, 396.0], [89.25, 396.0]]}, {"title": "3. Extending Dataset Distillation", "heading_level": null, "page_id": 5, "polygon": [[89.12548828125, 93.0], [287.173828125, 93.0], [287.173828125, 104.3173828125], [89.12548828125, 104.3173828125]]}, {"title": "3.1 Motivation", "heading_level": null, "page_id": 5, "polygon": [[89.2001953125, 114.0], [172.5732421875, 114.0], [172.5732421875, 124.8134765625], [89.2001953125, 124.8134765625]]}, {"title": "3.2 Basic Approach", "heading_level": null, "page_id": 7, "polygon": [[89.25, 310.5], [199.318359375, 310.5], [199.318359375, 321.169921875], [89.25, 321.169921875]]}, {"title": "3.3 Learnable Labels", "heading_level": null, "page_id": 8, "polygon": [[88.5, 93.5859375], [205.5, 93.5859375], [205.5, 103.5439453125], [88.5, 103.5439453125]]}, {"title": "3.4 Text and Other Sequences", "heading_level": null, "page_id": 8, "polygon": [[88.5, 444.7265625], [257.4404296875, 444.7265625], [257.4404296875, 454.78125], [88.5, 454.78125]]}, {"title": "Algorithm 1a Soft-Label Dataset Distillation (SLDD)", "heading_level": null, "page_id": 9, "polygon": [[89.25, 201.75], [354.75, 201.75], [354.75, 213.46875], [89.25, 213.46875]]}, {"title": "9: end for", "heading_level": null, "page_id": 9, "polygon": [[93.30908203125, 492.6796875], [159.4248046875, 492.6796875], [159.4248046875, 505.0546875], [93.30908203125, 505.0546875]]}, {"title": "3.5 Random initializations and multiple steps", "heading_level": null, "page_id": 10, "polygon": [[89.25, 176.25], [341.859375, 176.25], [341.859375, 186.78515625], [89.25, 186.78515625]]}, {"title": "4. Experiments", "heading_level": null, "page_id": 10, "polygon": [[89.25, 491.1328125], [180.0, 491.1328125], [180.0, 501.9609375], [89.25, 501.9609375]]}, {"title": "4.1 Metrics", "heading_level": null, "page_id": 10, "polygon": [[88.97607421875, 512.25], [154.79296875, 512.25], [154.79296875, 522.84375], [88.97607421875, 522.84375]]}, {"title": "4.2 Image Data", "heading_level": null, "page_id": 12, "polygon": [[89.25, 93.5859375], [177.205078125, 93.5859375], [177.205078125, 104.607421875], [89.25, 104.607421875]]}, {"title": "(b) Step 5", "heading_level": null, "page_id": 13, "polygon": [[282.0, 280.5], [330.50390625, 280.5], [330.50390625, 291.97265625], [282.0, 291.97265625]]}, {"title": "4.3 Text Data", "heading_level": null, "page_id": 14, "polygon": [[88.5, 364.869140625], [168.75, 364.869140625], [168.75, 376.083984375], [88.5, 376.083984375]]}, {"title": "Soft-Label Dataset Distillation and Text Dataset Distillation", "heading_level": null, "page_id": 18, "polygon": [[138.75, 39.0], [468.75, 39.0], [468.75, 49.35498046875], [138.75, 49.35498046875]]}, {"title": "5. Conclusion", "heading_level": null, "page_id": 22, "polygon": [[89.2001953125, 642.75], [171.0, 642.75], [171.0, 653.94140625], [89.2001953125, 653.94140625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 24, "polygon": [[89.25, 453.0], [195.75, 453.0], [195.75, 464.0625], [89.25, 464.0625]]}, {"title": "References", "heading_level": null, "page_id": 25, "polygon": [[88.5, 92.18408203125], [153.0, 92.18408203125], [153.0, 104.5107421875], [88.5, 104.5107421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 42], ["Text", 13], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6524, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 143], ["Span", 105], ["Line", 41], ["Caption", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3561, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 297], ["Line", 163], ["TableCell", 100], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6967, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 43], ["SectionHeader", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 88], ["Line", 36], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 37], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Line", 94], ["Span", 48], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 686, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["Line", 70], ["Equation", 4], ["Text", 3], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 191], ["Line", 53], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 36], ["ListItem", 7], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 602, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 48], ["SectionHeader", 3], ["Text", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["Line", 42], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 42], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Line", 50], ["Span", 29], ["Figure", 3], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2278, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 36], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Line", 25], ["Span", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 670, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 39], ["Figure", 4], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2950, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 43], ["Figure", 4], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3110, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 842], ["TableCell", 172], ["Line", 119], ["Text", 7], ["Table", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2262, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 141], ["Span", 87], ["Line", 22], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 43], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 273], ["Span", 165], ["Line", 60], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3375, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 608, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["TableCell", 84], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableOfContents", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9860, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 30], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 39], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 38], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 35], ["ListItem", 12], ["Reference", 12], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Soft-Label_Dataset_Distillation_and_Text_Dataset_Distillation"}