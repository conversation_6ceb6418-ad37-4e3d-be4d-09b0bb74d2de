{"table_of_contents": [{"title": "Sparse Parameterization for Epitomic\nDataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[164.25, 98.25], [447.0, 98.25], [447.0, 135.931640625], [164.25, 135.931640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 262.5], [329.25, 262.5], [329.25, 273.41015625], [282.75, 273.41015625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.1298828125, 515.109375], [192.0, 515.109375], [192.0, 525.9375], [107.1298828125, 525.9375]]}, {"title": "2 Method", "heading_level": null, "page_id": 2, "polygon": [[107.25, 111.0], [167.25, 111.0], [167.25, 122.58984375], [107.25, 122.58984375]]}, {"title": "2.1 Spatial-Agnostic Recurrent Parameterization", "heading_level": null, "page_id": 2, "polygon": [[106.5, 354.0], [324.0, 354.0], [324.0, 363.322265625], [106.5, 363.322265625]]}, {"title": "2.2 Training Objective and Feature Sparsification", "heading_level": null, "page_id": 3, "polygon": [[107.25, 310.5], [326.25, 310.5], [326.25, 320.396484375], [107.25, 320.396484375]]}, {"title": "Algorithm 1 Sparse Parameterization for Epitomic Dataset Distillation (SPEED).", "heading_level": null, "page_id": 4, "polygon": [[107.25, 73.5], [432.10546875, 73.5], [432.10546875, 83.67626953125], [107.25, 83.67626953125]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 4, "polygon": [[106.5, 480.75], [192.0, 480.75], [192.0, 491.90625], [106.5, 491.90625]]}, {"title": "3.1 Comparisons", "heading_level": null, "page_id": 4, "polygon": [[106.5, 582.0], [188.1123046875, 582.0], [188.1123046875, 592.06640625], [106.5, 592.06640625]]}, {"title": "3.2 Generalization", "heading_level": null, "page_id": 5, "polygon": [[106.5, 648.0], [194.25, 648.0], [194.25, 658.1953125], [106.5, 658.1953125]]}, {"title": "3.3 Ablation Study", "heading_level": null, "page_id": 6, "polygon": [[106.5, 681.0], [195.75, 681.0], [195.75, 691.06640625], [106.5, 691.06640625]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.25, 306.75], [198.0, 306.75], [198.0, 319.04296875], [107.25, 319.04296875]]}, {"title": "5 Conclusion and Limitations", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 189.0], [269.25, 189.0], [269.25, 200.900390625], [107.1298828125, 200.900390625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[107.25, 395.25], [207.75, 395.25], [207.75, 406.0546875], [107.25, 406.0546875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 478.5], [165.0, 478.5], [165.0, 489.5859375], [107.25, 489.5859375]]}, {"title": "I Experiment Details", "heading_level": null, "page_id": 14, "polygon": [[106.083984375, 72.0], [224.25, 72.0], [224.25, 84.15966796875], [106.083984375, 84.15966796875]]}, {"title": "II Additional Results and Analyses", "heading_level": null, "page_id": 15, "polygon": [[106.5, 438.15234375], [295.5, 438.15234375], [295.5, 449.75390625], [106.5, 449.75390625]]}, {"title": "III Additional Visualizations", "heading_level": null, "page_id": 18, "polygon": [[106.5, 633.0], [263.4169921875, 633.0], [263.4169921875, 644.2734375], [106.5, 644.2734375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 44], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3819, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 88], ["Text", 4], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 891, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 649], ["Line", 81], ["TextInlineMath", 6], ["Equation", 3], ["SectionHeader", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 747], ["Line", 113], ["TextInlineMath", 7], ["Equation", 3], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 687], ["Line", 58], ["ListItem", 12], ["TextInlineMath", 6], ["SectionHeader", 3], ["Text", 2], ["Reference", 2], ["Code", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1122, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 776], ["TableCell", 404], ["Line", 60], ["Text", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 11486, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 724], ["TableCell", 120], ["Line", 119], ["Reference", 5], ["Caption", 4], ["Text", 3], ["Figure", 2], ["Table", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 3336, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 424], ["TableCell", 222], ["Line", 60], ["Reference", 5], ["Caption", 4], ["Table", 2], ["Text", 2], ["TextInlineMath", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 17340, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 329], ["Line", 59], ["TableCell", 32], ["Text", 3], ["TextInlineMath", 3], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2327, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 48], ["ListItem", 7], ["Reference", 5], ["Text", 4], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 49], ["ListItem", 23], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["Line", 47], ["ListItem", 24], ["Reference", 20], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 49], ["ListItem", 22], ["Reference", 16], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 22], ["ListItem", 12], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 51], ["Text", 10], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["TableCell", 48], ["Line", 38], ["Text", 4], ["Caption", 2], ["Picture", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4701, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 59], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 41], ["TableCell", 18], ["Text", 7], ["Picture", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 347], ["TableCell", 107], ["Line", 56], ["Text", 4], ["Table", 3], ["Caption", 3], ["TableGroup", 3], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 614, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 646, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Line", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 630, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Line", 4], ["Span", 3], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 607, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Line", 5], ["Span", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 627, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1202, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1270, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Line", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1238, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Sparse Parameterization for Epitomic Dataset Distillation"}