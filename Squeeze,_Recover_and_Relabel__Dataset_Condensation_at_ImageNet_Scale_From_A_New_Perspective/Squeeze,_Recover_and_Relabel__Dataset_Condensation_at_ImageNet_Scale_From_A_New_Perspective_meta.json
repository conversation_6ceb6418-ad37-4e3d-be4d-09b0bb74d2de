{"table_of_contents": [{"title": "Squeeze, Recover and Relabel: Dataset Condensation at\nImageNet Scale From A New Perspective", "heading_level": null, "page_id": 0, "polygon": [[105.75, 100.5], [504.75, 99.75], [504.75, 136.0283203125], [105.75, 136.0283203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 243.0], [328.7109375, 243.0], [328.7109375, 253.880859375], [282.75, 253.880859375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 456.75], [191.25, 456.75], [191.25, 468.31640625], [107.25, 468.31640625]]}, {"title": "Contributions.", "heading_level": null, "page_id": 2, "polygon": [[107.25, 210.0], [171.0, 210.0], [171.0, 220.4296875], [107.25, 220.4296875]]}, {"title": "2 Approach", "heading_level": null, "page_id": 2, "polygon": [[107.25, 381.75], [177.0, 381.75], [177.0, 392.90625], [107.25, 392.90625]]}, {"title": "2.1 Decoupling Outer-loop and Inner-loop Training", "heading_level": null, "page_id": 3, "polygon": [[106.5, 159.0], [334.5, 159.0], [334.5, 170.15625], [106.5, 170.15625]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 4, "polygon": [[106.5, 597.0], [192.0, 597.0], [192.0, 608.30859375], [106.5, 608.30859375]]}, {"title": "3.1 Squeezing Analysis", "heading_level": null, "page_id": 5, "polygon": [[106.5, 269.25], [213.0, 269.25], [213.0, 279.404296875], [106.5, 279.404296875]]}, {"title": "3.2 Recovering Analysis", "heading_level": null, "page_id": 5, "polygon": [[106.5, 476.25], [218.25, 476.25], [218.25, 486.10546875], [106.5, 486.10546875]]}, {"title": "3.3 Relabeling Analysis", "heading_level": null, "page_id": 6, "polygon": [[106.5, 679.5], [216.75, 679.5], [216.75, 689.90625], [106.5, 689.90625]]}, {"title": "3.4 Condensed Dataset Evaluation", "heading_level": null, "page_id": 7, "polygon": [[106.5, 608.25], [261.7734375, 608.25], [261.7734375, 618.36328125], [106.5, 618.36328125]]}, {"title": "3.5 Cross-Architecture Generalization", "heading_level": null, "page_id": 8, "polygon": [[107.25, 393.75], [278.25, 393.75], [278.25, 404.12109375], [107.25, 404.12109375]]}, {"title": "3.6 Synthetic Image Visualization", "heading_level": null, "page_id": 8, "polygon": [[106.5, 582.0], [258.75, 582.0], [258.75, 592.06640625], [106.5, 592.06640625]]}, {"title": "3.7 Application: Continual Learning", "heading_level": null, "page_id": 9, "polygon": [[106.45751953125, 73.5], [271.5, 73.5], [271.5, 83.86962890625], [106.45751953125, 83.86962890625]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 9, "polygon": [[107.25, 314.25], [197.3759765625, 314.25], [197.3759765625, 325.23046875], [107.25, 325.23046875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.5, 510.46875], [183.75, 510.46875], [183.75, 521.296875], [106.5, 521.296875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[107.20458984375, 72.0], [164.25, 72.0], [164.25, 83.6279296875], [107.20458984375, 83.6279296875]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 13, "polygon": [[106.3828125, 70.91455078125], [169.2861328125, 70.91455078125], [169.2861328125, 85.02978515625], [106.3828125, 85.02978515625]]}, {"title": "A Implementation Details", "heading_level": null, "page_id": 13, "polygon": [[106.5, 206.12109375], [249.0, 206.12109375], [249.0, 216.94921875], [106.5, 216.94921875]]}, {"title": "A.1 Dataset Statistics", "heading_level": null, "page_id": 13, "polygon": [[107.25, 230.25], [206.490234375, 230.25], [206.490234375, 240.5390625], [107.25, 240.5390625]]}, {"title": "A.2 Squeezing Details", "heading_level": null, "page_id": 13, "polygon": [[106.5, 478.5], [208.7314453125, 478.5], [208.7314453125, 488.8125], [106.5, 488.8125]]}, {"title": "A.3 Recovering Details", "heading_level": null, "page_id": 15, "polygon": [[106.45751953125, 215.25], [213.75, 215.25], [213.75, 225.84375], [106.45751953125, 225.84375]]}, {"title": "A.4 Relabeling & Validation Details", "heading_level": null, "page_id": 15, "polygon": [[107.25, 465.0], [267.75, 465.0], [267.75, 475.27734375], [107.25, 475.27734375]]}, {"title": "B Low-Resolution Data", "heading_level": null, "page_id": 15, "polygon": [[106.5, 605.25], [237.0, 605.25], [237.0, 617.203125], [106.5, 617.203125]]}, {"title": "C Feature Embedding Distribution", "heading_level": null, "page_id": 16, "polygon": [[106.5, 493.06640625], [296.25, 493.06640625], [296.25, 504.66796875], [106.5, 504.66796875]]}, {"title": "D Theoretical Analysis", "heading_level": null, "page_id": 16, "polygon": [[106.5, 654.71484375], [234.1318359375, 654.71484375], [234.1318359375, 666.31640625], [106.5, 666.31640625]]}, {"title": "E More Visualization of Synthetic Data", "heading_level": null, "page_id": 18, "polygon": [[105.0, 70.5], [318.75, 70.5], [318.75, 84.0], [105.0, 84.0]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 53], ["SectionHeader", 3], ["Text", 3], ["Footnote", 3], ["Reference", 3], ["TextInlineMath", 1], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6381, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 51], ["Text", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 671, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 77], ["TableCell", 48], ["Text", 6], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3423, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 60], ["TextInlineMath", 4], ["Text", 3], ["Equation", 3], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 532], ["Line", 94], ["Text", 7], ["TextInlineMath", 3], ["Equation", 3], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1919, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 48], ["TableCell", 41], ["Text", 7], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3567, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["TableCell", 71], ["Line", 26], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2070, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 83], ["Text", 5], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1066, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["TableCell", 82], ["Line", 58], ["Text", 4], ["Reference", 4], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["TextInlineMath", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 14949, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 73], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 945, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 52], ["ListItem", 19], ["Reference", 19], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 55], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 66], ["Line", 16], ["ListItem", 6], ["Reference", 6], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["TableCell", 60], ["Line", 47], ["Text", 7], ["ListItem", 5], ["SectionHeader", 4], ["Reference", 3], ["Table", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4018, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 444], ["TableCell", 144], ["Line", 87], ["Text", 8], ["TextInlineMath", 3], ["Equation", 3], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["TableCell", 64], ["Line", 49], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3312, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["TableCell", 132], ["Line", 55], ["Reference", 6], ["Text", 5], ["Table", 3], ["Caption", 3], ["SectionHeader", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 8858, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["Line", 81], ["Text", 6], ["Equation", 5], ["TextInlineMath", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 7], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 32], ["Line", 20], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 712, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 7], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 8], ["Line", 5], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 601, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Squeeze,_Recover_and_Relabel__Dataset_Condensation_at_ImageNet_Scale_From_A_New_Perspective"}