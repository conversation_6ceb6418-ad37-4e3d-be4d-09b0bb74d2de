{"table_of_contents": [{"title": "Structure-free Graph Condensation: From\nLarge-scale Graphs to Condensed Graph-free Data", "heading_level": null, "page_id": 0, "polygon": [[116.25, 99.75], [494.26171875, 99.75], [494.26171875, 136.51171875], [116.25, 136.51171875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 265.5], [329.25, 265.5], [329.25, 276.310546875], [282.75, 276.310546875]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.05517578125, 526.5], [191.25, 526.5], [191.25, 537.5390625], [107.05517578125, 537.5390625]]}, {"title": "2 Structure-Free Graph Condensation", "heading_level": null, "page_id": 2, "polygon": [[107.25, 400.5], [312.75, 400.5], [312.75, 411.46875], [107.25, 411.46875]]}, {"title": "2.1 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[107.25, 423.0], [189.0087890625, 423.0], [189.0087890625, 433.8984375], [107.25, 433.8984375]]}, {"title": "2.2 Overview of SFGC Framework", "heading_level": null, "page_id": 3, "polygon": [[107.25, 635.25], [264.1640625, 635.25], [264.1640625, 645.046875], [107.25, 645.046875]]}, {"title": "2.3 Training Trajectory Meta-matching", "heading_level": null, "page_id": 4, "polygon": [[107.25, 392.25], [284.25, 392.25], [284.25, 402.57421875], [107.25, 402.57421875]]}, {"title": "Algorithm 1 Structure-Free Graph Condensation (SFGC)", "heading_level": null, "page_id": 5, "polygon": [[107.25, 73.5], [333.75, 73.5], [333.75, 83.2412109375], [107.25, 83.2412109375]]}, {"title": "2.4 Graph Neural Feature Score", "heading_level": null, "page_id": 5, "polygon": [[106.90576171875, 625.5], [252.75, 626.25], [252.75, 636.15234375], [106.90576171875, 636.15234375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 433.5], [192.146484375, 433.5], [192.146484375, 445.5], [107.1298828125, 445.5]]}, {"title": "3.1 Experimental Settings", "heading_level": null, "page_id": 6, "polygon": [[106.5, 465.0], [225.9140625, 465.0], [225.9140625, 474.50390625], [106.5, 474.50390625]]}, {"title": "3.2 Experimental Results", "heading_level": null, "page_id": 7, "polygon": [[107.20458984375, 401.02734375], [222.75, 401.02734375], [222.75, 411.08203125], [107.20458984375, 411.08203125]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.0], [183.75, 72.0], [183.75, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "Acknowledgment", "heading_level": null, "page_id": 9, "polygon": [[107.25, 256.78125], [198.2724609375, 256.78125], [198.2724609375, 267.99609375], [107.25, 267.99609375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 342.6328125], [164.953125, 342.6328125], [164.953125, 353.84765625], [107.25, 353.84765625]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 15, "polygon": [[107.25, 71.68798828125], [159.0, 71.68798828125], [159.0, 84.25634765625], [107.25, 84.25634765625]]}, {"title": "A Related Works", "heading_level": null, "page_id": 15, "polygon": [[107.25, 154.9775390625], [205.5, 154.9775390625], [205.5, 168.1259765625], [107.25, 168.1259765625]]}, {"title": "B Potential Application Scenarios", "heading_level": null, "page_id": 15, "polygon": [[106.5, 539.47265625], [290.162109375, 539.47265625], [290.162109375, 551.84765625], [106.5, 551.84765625]]}, {"title": "C Dataset Details", "heading_level": null, "page_id": 16, "polygon": [[106.5, 458.25], [207.0, 458.25], [207.0, 469.86328125], [106.5, 469.86328125]]}, {"title": "D More Analysis of Structure-free Paradigm", "heading_level": null, "page_id": 16, "polygon": [[106.30810546875, 675.0], [345.75, 675.0], [345.75, 686.0390625], [106.30810546875, 686.0390625]]}, {"title": "E More Experimental Settings and Results", "heading_level": null, "page_id": 18, "polygon": [[106.5, 278.244140625], [335.28515625, 278.244140625], [335.28515625, 290.232421875], [106.5, 290.232421875]]}, {"title": "E.1 Time Complexity Analysis & Dynamic Memory Cost Comparison", "heading_level": null, "page_id": 18, "polygon": [[105.75, 304.5], [412.5, 304.5], [412.5, 314.982421875], [105.75, 314.982421875]]}, {"title": "E.2 Effectiveness of Graph Neural Feature Score in SFGC", "heading_level": null, "page_id": 18, "polygon": [[105.75, 597.48046875], [363.0, 597.48046875], [363.0, 608.30859375], [105.75, 608.30859375]]}, {"title": "E.3 Analysis of Different Meta-matching Ranges", "heading_level": null, "page_id": 19, "polygon": [[106.5, 573.0], [322.5, 573.0], [322.5, 583.171875], [106.5, 583.171875]]}, {"title": "E.4 Performance on Graph Node Clustering Task", "heading_level": null, "page_id": 20, "polygon": [[105.6357421875, 428.25], [327.0, 428.25], [327.0, 439.69921875], [105.6357421875, 439.69921875]]}, {"title": "E.5 Visualization of Our Condensed Graph-free Data", "heading_level": null, "page_id": 21, "polygon": [[106.5, 507.0], [343.5, 507.0], [343.5, 517.81640625], [106.5, 517.81640625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["Line", 46], ["Text", 7], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5598, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 69], ["Text", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 696, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 585], ["Line", 61], ["TextInlineMath", 4], ["Text", 3], ["ListItem", 3], ["SectionHeader", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 813], ["Line", 131], ["Text", 2], ["TextInlineMath", 2], ["Equation", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 945, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 937], ["Line", 88], ["TextInlineMath", 5], ["Text", 2], ["Equation", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1007, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 590], ["Line", 74], ["ListItem", 9], ["TextInlineMath", 5], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 338], ["Line", 60], ["Text", 4], ["TextInlineMath", 3], ["SectionHeader", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1001], ["TableCell", 180], ["Line", 65], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1275, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 472], ["TableCell", 405], ["Line", 71], ["Text", 3], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 47], ["ListItem", 9], ["Reference", 9], ["SectionHeader", 3], ["Text", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 48], ["ListItem", 15], ["Reference", 15], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 49], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 48], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 48], ["ListItem", 18], ["Reference", 18], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 83], ["Line", 22], ["ListItem", 8], ["Reference", 8], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 195], ["Line", 53], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["TableCell", 54], ["Line", 50], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1482, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 735], ["TableCell", 204], ["Line", 91], ["TextInlineMath", 5], ["Equation", 3], ["Text", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3610, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 50], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Reference", 2], ["Figure", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 65], ["TableCell", 41], ["Text", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 776, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 304], ["Span", 227], ["Line", 53], ["Text", 3], ["Table", 3], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 11799, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 129], ["TableCell", 115], ["Line", 34], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8510, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Structure-free_Graph_Condensation__From_Large-scale_Graphs_to_Condensed_Graph-free_Data"}