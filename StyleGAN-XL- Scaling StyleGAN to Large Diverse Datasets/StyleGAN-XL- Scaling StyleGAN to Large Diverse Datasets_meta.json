{"table_of_contents": [{"title": "StyleGAN-XL: Scaling StyleGAN to Large Diverse Datasets", "heading_level": null, "page_id": 0, "polygon": [[50.25, 77.25], [468.75, 75.75], [468.75, 94.11767578125], [50.25, 94.11767578125]]}, {"title": "AXEL SAUER, KATJA SCHWARZ, and ANDREAS GEIGER", "heading_level": null, "page_id": 0, "polygon": [[49.306640625, 103.5], [336.75, 103.5], [336.75, 116.4990234375], [49.306640625, 116.4990234375]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[51.0, 554.25], [138.75, 554.25], [138.75, 563.0625], [51.0, 563.0625]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[317.25, 334.5], [405.75, 334.5], [405.75, 344.1796875], [317.25, 344.1796875]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 1, "polygon": [[50.25, 647.25], [132.75, 647.25], [132.75, 657.03515625], [50.25, 657.03515625]]}, {"title": "3 SCALING STYLEGAN TO IMAGENET", "heading_level": null, "page_id": 2, "polygon": [[50.25, 313.5], [222.75, 313.5], [222.75, 323.68359375], [50.25, 323.68359375]]}, {"title": "3.1 Adapting Regularization and Architectures", "heading_level": null, "page_id": 2, "polygon": [[50.25, 648.0], [249.22265625, 648.0], [249.22265625, 657.421875], [50.25, 657.421875]]}, {"title": "3.2 Reintroducing Progressive Growing", "heading_level": null, "page_id": 3, "polygon": [[50.25, 636.75], [219.75, 636.75], [219.75, 646.98046875], [50.25, 646.98046875]]}, {"title": "3.3 Exploiting Multiple Feature Networks", "heading_level": null, "page_id": 4, "polygon": [[50.25, 475.5], [228.75, 475.5], [228.75, 485.33203125], [50.25, 485.33203125]]}, {"title": "3.4 Classifier Guidance for GANs", "heading_level": null, "page_id": 4, "polygon": [[317.25, 147.75], [461.25, 147.75], [461.25, 157.4912109375], [317.25, 157.4912109375]]}, {"title": "4 RESULTS", "heading_level": null, "page_id": 4, "polygon": [[316.5, 414.75], [372.0, 414.75], [372.0, 424.6171875], [316.5, 424.6171875]]}, {"title": "4.1 Image Synthesis", "heading_level": null, "page_id": 4, "polygon": [[316.5, 516.0], [408.0, 516.0], [408.0, 525.9375], [316.5, 525.9375]]}, {"title": "4.2 Inversion and Manipulation", "heading_level": null, "page_id": 5, "polygon": [[50.25, 336.0], [187.5, 336.0], [187.5, 345.33984375], [50.25, 345.33984375]]}, {"title": "5 LIMITATIONS AND FUTURE WORK", "heading_level": null, "page_id": 5, "polygon": [[316.5, 369.0], [486.0, 369.0], [486.0, 379.37109375], [316.5, 379.37109375]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 8, "polygon": [[50.25, 80.25], [153.0, 80.25], [153.0, 90.298828125], [50.25, 90.298828125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[51.0, 194.25], [109.5, 194.25], [109.5, 203.994140625], [51.0, 203.994140625]]}, {"title": "A PREPROCESSING IMAGENET", "heading_level": null, "page_id": 9, "polygon": [[316.5, 647.25], [460.5, 647.25], [460.5, 657.80859375], [316.5, 657.80859375]]}, {"title": "B CLASSES OF UNALIGNED HUMANS", "heading_level": null, "page_id": 10, "polygon": [[51.0, 437.25], [223.5, 437.25], [223.5, 447.046875], [51.0, 447.046875]]}, {"title": "C INFERENCE SPEED", "heading_level": null, "page_id": 10, "polygon": [[50.25, 537.0], [150.98291015625, 537.0], [150.98291015625, 547.59375], [50.25, 547.59375]]}, {"title": "D RESULTS ON UNIMODAL DATASETS", "heading_level": null, "page_id": 10, "polygon": [[50.25, 636.75], [225.75, 636.75], [225.75, 646.98046875], [50.25, 646.98046875]]}, {"title": "E ADDITIONAL QUALITATIVE RESULTS", "heading_level": null, "page_id": 10, "polygon": [[317.25, 333.0], [494.25, 333.0], [494.25, 342.052734375], [317.25, 342.052734375]]}, {"title": "F IMPLEMENTATION DETAILS", "heading_level": null, "page_id": 10, "polygon": [[317.25, 444.75], [455.25, 444.75], [455.25, 455.16796875], [317.25, 455.16796875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 231], ["Line", 84], ["Text", 7], ["SectionHeader", 4], ["Picture", 1], ["Caption", 1], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5742, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["Line", 119], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 109], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 99], ["TableCell", 80], ["Text", 5], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 782, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 641], ["Line", 108], ["Text", 7], ["SectionHeader", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 493], ["Line", 108], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 182], ["Span", 157], ["Line", 29], ["Caption", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Figure", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 1252, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 13], ["Text", 2], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1872, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 449], ["Line", 146], ["ListItem", 38], ["Reference", 38], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 586, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 454], ["Line", 147], ["ListItem", 35], ["Reference", 35], ["Text", 4], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 412], ["Line", 89], ["TableCell", 84], ["Text", 10], ["SectionHeader", 5], ["Reference", 4], ["Caption", 3], ["Table", 3], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 6257, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 40], ["TableCell", 12], ["Text", 3], ["Reference", 3], ["Caption", 1], ["Table", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5237, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 23], ["Line", 11], ["Caption", 5], ["Picture", 3], ["Text", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2393, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 18], ["Line", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 616, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 15], ["Line", 9], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 705, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 606, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 4], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1242, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 752, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 52], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 995, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/StyleGAN-XL- Scaling StyleGAN to Large Diverse Datasets"}