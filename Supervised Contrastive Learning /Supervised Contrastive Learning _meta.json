{"table_of_contents": [{"title": "Supervised Contrastive Learning", "heading_level": null, "page_id": 0, "polygon": [[182.25, 99.0], [428.25, 98.25], [428.25, 116.982421875], [182.25, 116.982421875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 280.5], [328.5, 280.5], [328.5, 291.97265625], [282.75, 291.97265625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 516.0], [191.6982421875, 516.0], [191.6982421875, 526.7109375], [107.25, 526.7109375]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 2, "polygon": [[106.5, 154.5], [198.0, 154.5], [198.0, 166.2890625], [106.5, 166.2890625]]}, {"title": "3 Method", "heading_level": null, "page_id": 3, "polygon": [[106.5, 123.0], [167.25, 123.0], [167.25, 134.384765625], [106.5, 134.384765625]]}, {"title": "3.1 Representation Learning Framework", "heading_level": null, "page_id": 3, "polygon": [[106.30810546875, 247.5], [289.5, 247.5], [289.5, 257.5546875], [106.30810546875, 257.5546875]]}, {"title": "3.2 Contrastive Loss Functions", "heading_level": null, "page_id": 3, "polygon": [[106.3828125, 483.0], [246.75, 483.0], [246.75, 492.29296875], [106.3828125, 492.29296875]]}, {"title": "3.2.1 Self-Supervised Contrastive Loss", "heading_level": null, "page_id": 3, "polygon": [[106.5, 591.6796875], [279.0, 591.6796875], [279.0, 600.9609375], [106.5, 600.9609375]]}, {"title": "3.2.2 Supervised Contrastive Losses", "heading_level": null, "page_id": 4, "polygon": [[106.5, 108.75], [267.75, 108.75], [267.75, 118.0458984375], [106.5, 118.0458984375]]}, {"title": "3.2.3 Connection to Triplet Loss and N-pairs Loss", "heading_level": null, "page_id": 5, "polygon": [[107.20458984375, 401.25], [326.25, 401.25], [326.25, 411.46875], [107.20458984375, 411.46875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[107.25, 480.0], [191.6982421875, 480.0], [191.6982421875, 490.74609375], [107.25, 490.74609375]]}, {"title": "4.1 Classification Accuracy", "heading_level": null, "page_id": 5, "polygon": [[107.1298828125, 659.25], [230.5458984375, 659.25], [230.5458984375, 669.41015625], [107.1298828125, 669.41015625]]}, {"title": "4.2 Rob<PERSON>ness to Image Corruptions and Reduced Training Data", "heading_level": null, "page_id": 6, "polygon": [[106.5, 614.25], [396.0, 614.25], [396.0, 624.9375], [106.5, 624.9375]]}, {"title": "4.3 Hyperparameter Stability", "heading_level": null, "page_id": 7, "polygon": [[106.5, 504.75], [240.75, 504.75], [240.75, 514.72265625], [106.5, 514.72265625]]}, {"title": "4.4 Transfer Learning", "heading_level": null, "page_id": 7, "polygon": [[106.5, 636.75], [210.0, 636.75], [210.0, 646.98046875], [106.5, 646.98046875]]}, {"title": "4.5 Training Details", "heading_level": null, "page_id": 8, "polygon": [[106.5, 73.5], [200.25, 73.5], [200.25, 83.53125], [106.5, 83.53125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[107.25, 459.75], [165.0, 459.75], [165.0, 470.25], [107.25, 470.25]]}, {"title": "Supplementary", "heading_level": null, "page_id": 12, "polygon": [[257.25, 70.5], [353.25, 70.5], [353.25, 84.44970703125], [257.25, 84.44970703125]]}, {"title": "5 Training Setup", "heading_level": null, "page_id": 12, "polygon": [[106.5, 110.25], [203.80078125, 110.25], [203.80078125, 122.203125], [106.5, 122.203125]]}, {"title": "6 Gradient Derivation", "heading_level": null, "page_id": 12, "polygon": [[106.98046875, 519.0], [229.5, 519.0], [229.5, 530.96484375], [106.98046875, 530.96484375]]}, {"title": "7 Intrinsic Hard Positive and Negative Mining Properties", "heading_level": null, "page_id": 14, "polygon": [[106.5, 602.25], [409.5, 602.25], [409.5, 614.25], [106.5, 614.25]]}, {"title": "8 Triplet Loss Derivation from Contrastive Loss", "heading_level": null, "page_id": 16, "polygon": [[106.5, 660.0], [361.5, 660.0], [361.5, 670.95703125], [106.5, 670.95703125]]}, {"title": "9 Supervised Contrastive Loss Hierarchy", "heading_level": null, "page_id": 17, "polygon": [[106.5, 297.0], [327.0, 297.0], [327.0, 308.6015625], [106.5, 308.6015625]]}, {"title": "10 Effect of Temperature in Loss Function", "heading_level": null, "page_id": 18, "polygon": [[107.25, 163.001953125], [333.75, 163.001953125], [333.75, 173.63671875], [107.25, 173.63671875]]}, {"title": "11 Effect of Number of Positives", "heading_level": null, "page_id": 18, "polygon": [[107.25, 448.5], [281.25, 448.5], [281.25, 459.03515625], [107.25, 459.03515625]]}, {"title": "12 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 18, "polygon": [[107.25, 643.5], [189.75, 643.5], [189.75, 654.71484375], [107.25, 654.71484375]]}, {"title": "13 Two stage training on Cross Entropy", "heading_level": null, "page_id": 19, "polygon": [[107.25, 533.25], [320.25, 533.25], [320.25, 545.66015625], [107.25, 545.66015625]]}, {"title": "14 Training Details", "heading_level": null, "page_id": 19, "polygon": [[106.5, 672.0], [215.6044921875, 672.0], [215.6044921875, 684.10546875], [106.5, 684.10546875]]}, {"title": "14.1 Optimizer", "heading_level": null, "page_id": 20, "polygon": [[107.25, 186.0], [180.3427734375, 186.0], [180.3427734375, 196.453125], [107.25, 196.453125]]}, {"title": "14.2 Data Augmentation", "heading_level": null, "page_id": 20, "polygon": [[106.98046875, 470.25], [219.75, 470.25], [219.75, 479.91796875], [106.98046875, 479.91796875]]}, {"title": "15 Change Log", "heading_level": null, "page_id": 22, "polygon": [[106.5, 71.59130859375], [195.8818359375, 71.59130859375], [195.8818359375, 83.77294921875], [106.5, 83.77294921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 77], ["Text", 11], ["Footnote", 4], ["SectionHeader", 3], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7140, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 48], ["Text", 4], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 691, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 56], ["ListItem", 3], ["TextInlineMath", 3], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["Line", 64], ["SectionHeader", 4], ["Text", 3], ["ListItem", 3], ["TextInlineMath", 3], ["Reference", 2], ["Equation", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 659], ["Line", 108], ["TableCell", 10], ["Reference", 4], ["TextInlineMath", 3], ["ListItem", 3], ["Text", 2], ["Equation", 2], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1839, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 657], ["Line", 101], ["TextInlineMath", 6], ["Equation", 3], ["SectionHeader", 3], ["Reference", 3], ["Text", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["TableCell", 140], ["Line", 52], ["Text", 5], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 216], ["TableCell", 174], ["Line", 53], ["Reference", 4], ["Caption", 3], ["Table", 2], ["Text", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Figure", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 277], ["Line", 54], ["Reference", 9], ["ListItem", 8], ["Text", 4], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 309], ["Line", 59], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 57], ["ListItem", 23], ["Reference", 23], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 31], ["ListItem", 11], ["Reference", 11], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 294], ["Line", 82], ["Reference", 4], ["SectionHeader", 3], ["Equation", 2], ["Text", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 956, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 641], ["Line", 119], ["Equation", 3], ["TextInlineMath", 2], ["Reference", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1513, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 896], ["Line", 218], ["Text", 6], ["Equation", 4], ["Reference", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2366, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 740], ["Line", 173], ["Equation", 7], ["Text", 5], ["Reference", 5], ["TextInlineMath", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2644, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 769], ["Line", 126], ["TextInlineMath", 5], ["Equation", 2], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5261, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["Line", 103], ["Equation", 4], ["TextInlineMath", 4], ["Text", 3], ["Reference", 3], ["ListItem", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1888, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 57], ["TableCell", 22], ["Text", 8], ["SectionHeader", 3], ["ListItem", 2], ["Reference", 2], ["Table", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1868, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["TableCell", 77], ["Line", 46], ["Caption", 3], ["Reference", 3], ["SectionHeader", 2], ["Text", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 700, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["TableCell", 92], ["Line", 47], ["ListItem", 4], ["Reference", 3], ["Table", 2], ["Caption", 2], ["SectionHeader", 2], ["Text", 2], ["TableGroup", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 102], ["Span", 85], ["Line", 54], ["Caption", 2], ["Reference", 2], ["TextInlineMath", 1], ["Table", 1], ["Text", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2658, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 41], ["Line", 15], ["Text", 6], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Supervised Contrastive Learning "}