# Synthesizing Informative Training Samples with <PERSON><PERSON>, <PERSON><PERSON> School of Informatics, The University of Edinburgh {bo.zhao, h<PERSON>en}@ed.ac.uk

## Abstract

Remarkable progress has been achieved in synthesizing photo-realistic images with generative adversarial networks (GANs). Recently, GANs are utilized as the training sample generator when obtaining or storing real training data is expensive even infeasible. However, traditional GANs generated images are not as informative as the real training samples when being used to train deep neural networks. In this paper, we propose a novel method to synthesize Informative Training samples with GAN (IT-GAN). Specifically, we freeze a pre-trained GAN model and learn the informative latent vectors that correspond to informative training samples. The synthesized images are required to preserve information for training deep neural networks rather than visual reality or fidelity. Experiments verify that the deep neural networks can learn faster and achieve better performance when being trained with our IT-GAN generated images<sup>[1](#page-0-0)</sup>. We also show that our method is a promising solution to dataset condensation problem.

# 1 Introduction

In the last decade, generative adversarial networks (GANs) [\[9;](#page-6-0) [28;](#page-7-0) [35;](#page-7-1) [67;](#page-8-0) [5;](#page-6-1) [17\]](#page-6-2) have been successfully applied to synthesize photo-realistic images in various tasks, including for generating novel realistic images [\[16;](#page-6-3) [5\]](#page-6-1), image manipulation [\[50;](#page-7-2) [12\]](#page-6-4), image-to-image translation [\[14;](#page-6-5) [67\]](#page-8-0), text-to-image translation [\[37;](#page-7-3) [60\]](#page-8-1), super-resolution [\[22;](#page-6-6) [56\]](#page-8-2) and photo inpainting [\[33;](#page-7-4) [25\]](#page-6-7). The main focus of these works has been on improving the reality and fidelity of GAN generated images. More recently, the interest of the community has shifted into turning GANs into infinite training data generators. To this end, GANs have been used to synthesize labelled training samples for part segmentation [\[61;](#page-8-3) [57;](#page-8-4) [24;](#page-6-8) [23\]](#page-6-9), forming memory for previously seen tasks in continual learning [\[41;](#page-7-5) [53;](#page-7-6) [7\]](#page-6-10), distilling/transferring knowledge [\[8;](#page-6-11) [26;](#page-6-12) [51\]](#page-7-7), augmenting existing real data [\[2;](#page-6-13) [4;](#page-6-14) [42;](#page-7-8) [40\]](#page-7-9) and reducing privacy leakage [\[55;](#page-8-5) [34\]](#page-7-10). Nevertheless, the common assumption in these works, on which little attention has been paid before [\[36\]](#page-7-11), is that GAN synthesized images are inherently informative for training models.

In this work, we question this assumption and ask whether the objective of generating images that are expected to be real-looking (*i.e*. by using a discriminator loss) is sufficient to train deep networks from scratch. We hypothesize that generating realistic images does not automatically guarantee good training samples, and we propose a GAN based method, *IT-GAN* that can generate more *I*nformative *T*raining samples such that a model trained on them yields better generalization performance (illustrated in Fig. [1\)](#page-1-0). In particular, we first show that training a standard convolutional network (*i.e*. ResNet18 [\[11\]](#page-6-15)) from scratch on a state-of-the-art GAN (*i.e*. BigGAN [\[5\]](#page-6-1)) synthesized images performs significantly worse than training them on the original real training images (*i.e*. 77.8% v.s. 93.4% testing accuracy on CIFAR10 [\[20\]](#page-6-16)). We also study an alternative strategy and show that learning latent vectors to reconstruct the original real images by GAN inversion [\[1;](#page-6-17) [66;](#page-8-6) [54\]](#page-8-7) improves the informativeness of synthetic images for training deep models, while still performing significantly

<span id="page-0-0"></span><sup>&</sup>lt;sup>1</sup>The implementation is available at  $https://github.com/VICO-VoE/IT-GAN$ .

NeurIPS 2022 Workshop on Synthetic Data for Empowering ML Research.

Image /page/1/Figure/0 description: This figure illustrates the comparison of three generative adversarial network (GAN) models: GAN, GAN Inversion, and IT-GAN. The left side shows a 'Latent Space' with various colored dots, representing different points in the latent space. Arrows connect these points to corresponding images in the 'Synthetic' column, which are generated images of horses. The 'Synthetic' images are then compared to images in the 'Real' column, which are actual images of horses. The comparisons are labeled as 'Real-looking?', 'Look same?', and 'Informative for training?'. The right side of the figure presents a 'Performance' table, showing accuracy scores for CIFAR10 and CIFAR100 datasets for each model. GAN achieves 77.8 on CIFAR10 and 45.2 on CIFAR100. GAN Inversion achieves 82.9 on CIFAR10 and 55.0 on CIFAR100. IT-GAN achieves the highest performance with 85.7 on CIFAR10 and 60.1 on CIFAR100.

<span id="page-1-0"></span>Figure 1: Different objectives and performances of traditional GAN, GAN inversion [\[1\]](#page-6-17) and our IT-GAN. BigGAN [\[5\]](#page-6-1) architecture is used in all three methods. We learn latent vectors in the latent space of a pre-trained GAN that correspond to informative training samples. We show that our IT-GAN achieves better performance than traditional GAN and GAN inversion on CIFAR10/100 when being used as the training data generator.

worse than the training on real images. Motivated by this observation, we propose to learn a set of latent vectors, which are fed into a pre-trained GAN to generate informative training images, such that their representations are statistically similar to those of real images with the same embedding model. In contrast to generating realistic images, our method ensures that the synthesized images include similar discriminative patterns to the original training images which in return enables more effective training of downstream task models.

Our method is most related to the recently emerging problem of dataset distillation/condensation [\[49;](#page-7-12) [64\]](#page-8-8) that aims to synthesize a small number of informative training samples so that deep neural networks trained on synthetic samples can obtain comparable generalization performance to those trained on real samples. Most of dataset condensation methods [\[49;](#page-7-12) [46;](#page-7-13) [3;](#page-6-18) [64;](#page-8-8) [62;](#page-8-9) [31;](#page-7-14) [32;](#page-7-15) [48;](#page-7-16) [6\]](#page-6-19) involves the expensive bilevel optimization, in which the outer-loop and inner-loop optimize the synthetic data and neural network parameters respectively. Recently, [\[63\]](#page-8-10) propose to match the distribution of real and synthetic data in many randomly sampled embedding spaces, thus it involves neither bi-level optimization nor second-order derivative. This method significantly reduces the synthesis cost while achieving comparable performance.

Inspired by [\[63\]](#page-8-10), we learn to generate informative training samples with our IT-GAN by minimizing the distribution matching loss of real and generated synthetic samples. Different from [\[63\]](#page-8-10) that optimizes image pixels directly, our method optimizes latent vectors of a pre-trained GAN, so that our method can convert any pre-trained GAN into an informative training sample generator. In experiments, we verify that our method can generate more informative training samples than the traditional GAN and GAN inversion, so that deep neural networks can learn faster on our synthetic images and achieve better testing performance on CIFAR10 and CIFAR100 datasets. We also compare IT-GAN to dataset condensation method [\[63\]](#page-8-10) and show that our IT-GAN achieves better performance with same storage budget. In the remainder of the paper, we present our method in Sec. [2,](#page-1-1) evaluate our method in multiple image classification benchmarks in Sec. [3](#page-3-0) and conclude the paper in Sec. [4.](#page-5-0) The appendix presents the preliminary of GANs and dataset condensation, and also the ablation study.

## <span id="page-1-1"></span>2 Method

#### 2.1 Initializing with GAN Inversion

Given a pre-trained generator G, we initialize the whole latent set  $\mathcal{Z} \in \mathbb{R}^{|\mathcal{T}| \times d_z}$  by GAN inversion, so that the synthetic image  $G(z)$  of each latent vector  $z \in \mathbb{R}^{d_z}$  corresponds to the real image  $x \in \mathbb{R}^{d_z}$ in the training set  $\mathcal{T} = \{x_i, y_i\}^{|\mathcal{T}|}_{i=1}$ , where  $d_z$  and  $d_I$  are the dimensions of latent vector and image respectively. We use the GAN inversion method proposed in [\[1\]](#page-6-17) which learns the latent vectors by minimizing both feature and pixel distances between the synthetic image and real image:

<span id="page-1-2"></span>
$$
\arg\min_{\boldsymbol{z}} \frac{1}{d_f} \|\psi_{\boldsymbol{\vartheta}}(G(\boldsymbol{z})) - \psi_{\boldsymbol{\vartheta}}(\boldsymbol{x})\|^2 + \frac{\lambda_{pixel}}{d_I} \|G(\boldsymbol{z}) - \boldsymbol{x}\|^2,\tag{1}
$$

where  $\psi_{\theta}$  is a pre-trained feature extractor,  $d_f$  is the feature dimension and  $\lambda_{pixel} = 1$  by default.

## 2.2 Condensing Training Information

Motivated by dataset condensation methods, we condense the training knowledge from real images into synthetic images that are generated by G. Furthermore, we learn the optimal latent vector set by

Image /page/2/Figure/0 description: This is a diagram illustrating a process with forward and backpropagation steps. The process starts with an input 'Z' going into a component labeled 'G'. The output of 'G' is 'S'. 'S' and 'T' are then fed into two components labeled 'Aω'. The output of these 'Aω' components goes into a component labeled 'ψϑ'. Finally, the output of 'ψϑ' leads to a component representing a calculation: '(1-λ)⋅Lcon + λ⋅R'. Red dashed arrows indicate backpropagation paths from 'S' to 'G', from the output of 'Aω' to 'Aω', and from the output of 'ψϑ' to 'ψϑ'. The diagram also includes labels 'Forward' with a right-pointing arrow and 'Backpropagation' with a left-pointing dashed arrow.

<span id="page-2-0"></span>Figure 2: Illustration of IT-GAN. We input the latent vector set  $Z$  into a pre-trained generator  $G$  and generate the synthetic set S. Then, the synthetic set S and real training set  $\mathcal T$  are input into the differentiable Siamese augmentation  $\mathcal{A}_{\omega}(\cdot)$  and then randomly sampled embedding function  $\psi_{\vartheta}(\cdot)$  to obtain feature embeddings, where  $ω$  and  $\theta$  are the parameters. The condensation loss  $\mathcal{L}_{con}$  and regularization R with coefficient  $\lambda$  are computed for optimizing  $Z$ .

minimizing the condensation loss (illustrated in Fig. [2\)](#page-2-0). As the large latent vector set  $\mathcal Z$  has the same or comparable scale (instance number) as the whole training set  $T$ , the condensation optimization has to be simple and fast. Thus, we leverage the condensation method proposed in [\[63\]](#page-8-10) which has neither bi-level optimization nor second-order derivation. Specifically, the synthetic samples are expected to have similar distribution to that of real training samples in randomly sampled embedding space  $\psi_{\theta}$ :

$$
\mathcal{L}_{con} = \mathbb{E}_{\substack{\boldsymbol{\vartheta} \sim P_{\boldsymbol{\vartheta}} \\ \omega \sim \Omega}} ||\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(\boldsymbol{x}_i, \omega)) - \frac{1}{|\mathcal{Z}|} \sum_{j=1}^{|\mathcal{Z}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(G(\boldsymbol{z}_j), \omega))||^2,
$$
(2)

where the differentiable augmentation  $\mathcal{A}_{\omega}$  parameterized with  $\omega \sim \Omega$  is applied to increase the data-efficiency [\[62\]](#page-8-9). Similarly, we can also match the mean gradients  $\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} g_{\theta}(\mathcal{A}(\mathbf{x}_i, \omega), y_i)$ and  $\frac{1}{|Z|} \sum_{j=1}^{|Z|} g_{\theta}(\mathcal{A}(G(z_j), \omega), y_j)$  of real and synthetic samples [\[64\]](#page-8-8), where  $y_i$  is the label and g is gradient function. We empirically verify that distribution matching and gradient matching have close performances.

#### 2.3 Regularization

As the whole latent vector set size  $|\mathcal{Z}|$  is large, we need to split  $\mathcal Z$  into many batches  $B_i^{\mathcal Z}$  and train each batch independently. Training multiple batches (or subsets) with the same condensation loss  $\mathcal{L}_{con}$  will enforce the different batches to be homogeneous and thus decrease the informativeness when combining them for training. To avoid this problem, we further add the regularization:

$$
R = \mathbb{E}_{\substack{\boldsymbol{\vartheta} \sim P_{\boldsymbol{\vartheta}} \\ \omega \sim \Omega}} |||\psi_{\boldsymbol{\vartheta}}(\mathcal{A}(\boldsymbol{x}_i,\omega)) - \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(G(\boldsymbol{z}_i),\omega))||^2, \tag{3}
$$

where  $x_i$  and  $z_i$  are a pair. Different from the feature alignment in GAN inversion methods, our regularization can better preserve the training information as it is calculated over the randomly sampled embedding spaces and Siamese augmentation strategies which can mimic the training dynamics. The total training loss is

<span id="page-2-1"></span>
$$
\mathcal{L} = (1 - \lambda) \cdot \mathcal{L}_{con} + \lambda \cdot R,\tag{4}
$$

where  $\lambda$  is the coefficient of regularization.

#### 2.4 Training Algorithm

The training algorithm is illustrated in Alg. [1.](#page-3-1) Given the pre-trained generator  $G$ , we freeze its parameters. We initialize a set of learnable latent vectors  $\mathcal Z$  by implementing GAN inversion using Eq. [1.](#page-1-2) Then, we sample a batch of latent vectors  $B_c^{\mathcal{Z}} \sim \mathcal{Z}$  and corresponding real image batch  $B_c^T \sim T$  for each class c. We also sample an independent large-batch  $\tilde{B}_c^T \sim T$  for condensing training information. The augmentation parameter  $\omega_c \sim \Omega$  is sampled for all three batches. Then, we compute the condensation loss  $\mathcal{L}_{con}$  and regularization R respectively. The latent vector set  $\mathcal{Z}$  is optimized by minimizing the total loss Eq. [4.](#page-2-1) Note that the synthetic samples  $G(z)$  are generated instantaneously before computing the loss.

**Latent Vector Ensemble** Training the whole latent vector set  $Z$  in one device (*e.g.* GPU) can be infeasible or slow, as the sample number is the same or comparable to that of the original dataset. Thus, we split the whole latent vector set into many batches and train independently and then combine them to use. Latent vector ensemble is also important strategy for real-world learning scenarios such as continual learning, curriculum learning and distributed learning.

## Algorithm 1: IT-GAN.

**Input:** Training set  $T$ 1 **Required:** Pre-trained generator G, latent vector set  $\mathcal Z$  for C classes, deep neural network  $\psi_{\vartheta}$ parameterized with  $\mathbf{\vartheta} \sim P_{\mathbf{\vartheta}}$ , differentiable augmentation  $\mathcal{A}_{\omega}$  parameterized with  $\omega \sim \Omega$ , coefficient  $\lambda$ , training iterations  $K$ , learning rate  $\eta$ . 2 Initialize  $\mathcal Z$  by GAN inversion using Eq. [1](#page-1-2) and correspond to every sample in  $\mathcal T$ . 3 for  $k = 0, \dots, K - 1$  do 4 Sample  $\theta$  ∼  $P_{\theta}$ 5 Sample batch  $B_c^Z \sim \mathcal{Z}$  and corresponding  $B_c^T \sim \mathcal{T}$ , large-batch  $\tilde{B}_c^T \sim \mathcal{T}$  and  $\omega_c \sim \Omega$  for every class c 6 Compute  $\mathcal{L}_{con} = \sum_{c=0}^{C-1} \left\| \frac{1}{|B_c^{\mathcal{T}}|} \sum_{\boldsymbol{x} \in B_c^{\mathcal{T}}} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}_{\omega_c}(\boldsymbol{x})) - \frac{1}{|\tilde{B}_c^{\mathcal{Z}}|} \sum_{(\boldsymbol{z},y) \in \tilde{B}_c^{\mathcal{Z}}} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}_{\omega_c}(G(\boldsymbol{z}))) \right\|^2$ 7 Compute  $R = \frac{1}{|B_c^T|} \sum_{\boldsymbol{x} \in B_c^T, \boldsymbol{z} \in B_c^Z} ||\psi_{\boldsymbol{\vartheta}}(\mathcal{A}_{\omega_c}(\boldsymbol{x})) - \psi_{\boldsymbol{\vartheta}}(\mathcal{A}_{\omega_c}(G(\boldsymbol{z})))||^2$   $\triangleright$  each  $\boldsymbol{z}$  corresponds to  $\boldsymbol{x}$  $\mathbf{s}$   $\mathcal{L} = (1 - \lambda) \cdot \mathcal{L}_{con} + \lambda \cdot R$ 

$$
\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\begin{array}{c}\n\bullet \\
\bullet \\
\end{array}\n\quad\n\end{array}
$$

<span id="page-3-1"></span>Output: Z

|          | GAN            | GAN Inversion  | IT-GAN                         | Upper-bound    |
|----------|----------------|----------------|--------------------------------|----------------|
| CIFAR10  | 77.8 $\pm$ 0.7 | 82.9 $\pm$ 0.6 | <b>85.7<math>\pm</math>0.4</b> | 93.4 $\pm$ 0.2 |
| CIFAR100 | 45.2 $\pm$ 1.0 | 55.0 $\pm$ 0.8 | <b>60.1<math>\pm</math>0.2</b> | 74.1 $\pm$ 0.2 |

<span id="page-3-2"></span>Table 1: Performance (%) comparison among traditional GAN, GAN Inversion and our IT-GAN. The synthetic images produced by the three methods are used to train ResNet18 from scratch and then test on real testing data. The upper-bound performance is achieved by training ResNet18 on the original real training set.

# <span id="page-3-0"></span>3 Experiments

#### 3.1 Experimental Settings

Experimental Settings. We do experiments on CIFAR10 and CIFAR100 [\[20\]](#page-6-16) datasets. The experiments have two phases. In the first phase, we learn the informative latent vectors which correspond to those informative training samples on one architecture. In the second phase, we train randomly initialized deep neural networks on synthesized images and then test on the real testing set. Following [\[64;](#page-8-8) [62;](#page-8-9) [63\]](#page-8-10), we use ConvNet and ResNet18 [\[11\]](#page-6-15) in experiments. ConvNet is lightweight model with 3 convolutional blocks, and each block consists of a 128-kernel convolutional layer, instance normalization [\[47\]](#page-7-17), ReLU activation [\[30\]](#page-7-18) and average pooling. ResNet18 is equipped with batch normalization [\[13\]](#page-6-20). For simplicity, we train latent vectors on ConvNet and then test on ResNet18 in most experiments. We find that the learned latent vectors and their corresponding synthetic images generalize well to unseen architectures.

Competitors. We compare our method to traditional *GAN*: the images are generated with the randomly sampled latent vectors and *GAN Inversion* [\[1\]](#page-6-17): the images are generated with the optimized latent vectors which reconstruct the real images in original training set. We pre-train BigGAN models [\[5\]](#page-6-1) using the state-of-the-art training strategy [\[65\]](#page-8-11). Besides, we also compare to dataset condensation method [\[63\]](#page-8-10) with a similar storage budget and verify that our method achieves better performance.

**Hyper-parameters.** We use Adam optimizer [\[18\]](#page-6-21) with learning rate  $\eta = 0.001$  for all experiments, which is validated in ablation study. We train latent vectors for 5000 iterations. We use batch size 1250 and 500 for splitting latent vectors of CIFAR10 and CIFAR100 into subsets respectively, and then learn these subsets independently in main experiments. The regularization coefficient  $\lambda$  can be searched from  $10^{-4,-3,-2,-1}$  roughly. For simplicity, we set it to be 0 when the training batch size is large enough. Please refer to the ablation study in appendix for more details. We pre-train hundreds of ConvNets on CIFAR10 and CIFAR100 and then used in experiments. The pre-training is not expensive as ConvNet architecture is simple and small. For training neural networks, we use SGD optimizer and train for 200 epochs. The learning rate is 0.01 in the first half epochs and then decreases to 0.001 in the second half epochs. We believe the performance of our IT-GAN can be further improved by using larger batch size, carefully tuning  $\eta$  and  $\lambda$ , and using better performing embedding functions.

Image /page/4/Figure/0 description: Two line graphs show the accuracy (%) over training epochs. Both graphs have the x-axis labeled "Training Epoch" ranging from 0 to 200, and the y-axis labeled "Accuracy (%)" ranging from 40 to 100. The left graph displays three sets of lines: a dashed blue line labeled "GAN Train", a solid blue line labeled "GAN Test", a dashed green line labeled "GAN Inversion Train", a solid green line labeled "GAN Inversion Test", a dashed red line labeled "IT-GAN Train", and a solid red line labeled "IT-GAN Test". The "GAN Train" and "IT-GAN Train" lines reach nearly 100% accuracy. The "GAN Inversion Train" and "IT-GAN Test" lines reach around 85% accuracy, and the "GAN Test" line reaches around 77% accuracy. The right graph shows similar lines but with different accuracy levels. The "GAN Train" and "IT-GAN Train" lines reach nearly 100% accuracy. The "GAN Inversion Train" and "IT-GAN Test" lines reach around 57% accuracy, and the "GAN Test" line reaches around 43% accuracy. Both graphs include shaded areas around the lines, indicating variability.

<span id="page-4-1"></span><span id="page-4-0"></span>Figure 3: Train ResNet18 on synthetic CIFAR10 images produced by GAN, GAN Inversion and IT-GAN. images produced by GAN, GAN Inversion and IT-GAN. Figure 4: Train ResNet18 on synthetic CIFAR100

#### 3.2 Comparison to GAN Methods

Whole-set Learning. In this setting, we sample latent vectors from the normal distribution for *GAN*. For GAN Inversion and our IT-GAN, we sample latent vectors from the whole learned latent vector set that has the same size as the real training set. The performances are presented in Tab. [1.](#page-3-2) Training ResNet18 on samples generated by traditional *GAN* achieves 77.8% and 45.2% testing accuracies on CIFAR10 and CIFAR100 respectively, while the upper-bound performances that are obtained by training on original real training set are 93.4% and 74.1% on two datasets. The significant performance gap indicates that, although the synthetic images are real-looking, they have quite different distribution from the real images which has not been revealed by the discriminator.

GAN Inversion can improve the performances by producing synthetic samples that are visually close to original ones. However, there is still big performance gap between GAN Inversion and real data training. The possible reason is that GAN Inversion tries to minimize the pixel-level difference between synthetic and real images, however some training information is lost. Our IT-GAN (85.7% and 60.1%) further improves the GAN Inversion performances (82.9% and 55.0%) by 2.8% and 5.1% on CIFAR10 and CIFAR100 respectively. It means that our method can produce more informative training samples with pre-trained GANs.

Fig. [3](#page-4-0) and Fig. [4](#page-4-1) plot the training and testing curves on CIFAR10 and CIFAR100 datasets. The curves show that the training accuracies of our method generated samples converge slower than the others, while the testing accuracies increase remarkably faster than the others. This training dynamics also proves that the training samples generated by our method are more informative for training models.

Subset Learning. To have a closer look to the informativeness of synthesized training samples and the training efficiency, we do experiments with small subsets of latent vectors. Specifically, for traditional GAN, we randomly sample a small subset of latent vectors. For GAN Inversion and our IT-GAN, we randomly learn a small subset of latent vectors. Then, the synthetic images that correspond to the sampled/learned latent vectors are used to train neural networks from scratch. Fig. [5](#page-5-1) shows the performance curves of the three methods with varying subset size from 50 latent vectors per class to 1250 latent vectors per class. The curves verify that our IT-GAN always produces more informative training samples which have remarkable improvements over traditional GAN and GAN Inversion.

Visualization We visualize the synthetic images in Fig. [6.](#page-5-2) The synthetic images are recognizable, although there may exist some artificial patterns. We think those artificial patterns can improve the informativeness of training samples. Note that our goal is to generate informative training samples instead of real-looking ones.

#### 3.3 Comparison to Dataset Condensation

We compare our method to dataset condensation methods under the close memory budget. Our method requires 40.8 MB storage for CIFAR10 and CIFAR100 respectively, which consists of 16.4 MB of BigGAN model and 24.4 MB of 128 dim latent vectors. This storage size is around 25% of the original dataset (162 MB). Thus, we compare to the dataset condensation methods that synthesize 25% samples for CIFAR10 and CIFAR100. However, few dataset condensation methods report the

Image /page/5/Figure/0 description: This line graph displays the accuracy of three different generative adversarial networks (GANs) as the number of latent vectors per class increases. The x-axis represents the number of latent vectors per class, with values ranging from 50 to 1250. The y-axis represents the accuracy in percentage, ranging from 40% to 80%. The graph shows three lines: GAN (blue), GAN Inversion (green), and IT-GAN (red). All three models show an increasing trend in accuracy as the number of latent vectors increases. The IT-GAN model consistently achieves the highest accuracy across all tested values, followed by GAN Inversion, and then GAN. For example, at 50 latent vectors, IT-GAN has an accuracy of approximately 47%, GAN Inversion around 43%, and GAN around 42%. At 1250 latent vectors, IT-GAN reaches about 82% accuracy, GAN Inversion reaches about 76%, and GAN reaches about 75%.

Image /page/5/Picture/1 description: The image displays two grids of images, labeled "CIFAR10" on the left and "CIFAR100" on the right. Each grid contains 100 smaller images arranged in a 10x10 format. The CIFAR10 grid features a variety of objects and animals, including cars, birds, cats, dogs, deer, horses, ships, and airplanes. The CIFAR100 grid also shows a diverse range of subjects, such as fruits (apples, pears, radishes), fish, bears, insects (bees, beetles), flowers, and bicycles.

Figure 5: Small set of latent vectors of CIFAR10 are sampled/learned and then evaluated on ResNet18.

<span id="page-5-2"></span>Figure 6: Visualization of synthetic images for CIFAR10 and CIFAR100. Note that our goal is to generate informative training samples instead of real-looking ones.

<span id="page-5-1"></span>

|          |          | DM             | <b>IT-GAN</b>                    | Upper-bound    |
|----------|----------|----------------|----------------------------------|----------------|
| CIFAR10  | ConvNet  | $80.8 pm 0.3$ | <b><math>82.8 pm 0.3</math></b> | $84.8 pm 0.1$ |
|          | ResNet18 | $85.1 pm 0.3$ | <b><math>85.7 pm 0.4</math></b> | $93.4 pm 0.2$ |
| CIFAR100 | ConvNet  | $50.5 pm 0.3$ | <b><math>55.7 pm 0.4</math></b> | $56.2 pm 0.3$ |
|          | ResNet18 | $56.7 pm 0.5$ | <b><math>60.1 pm 0.2</math></b> | $74.1 pm 0.2$ |

<span id="page-5-3"></span>Table 2: Compare to dataset condensation method DM [\[63\]](#page-8-10) with the same storage (25% of the whole dataset).

results with such large synthetic set sizes due to the expensive optimization. As shown in Tab. [2,](#page-5-3) we compare to DM [\[63\]](#page-8-10) which is simple and effective without involving bi-level optimization and second-order derivation. The results indicate that our method outperforms DM on both CIFAR10 and CIFAR100 no matter whether ConvNet or ResNet18 models are trained. Note that the synthetic data are all learned with ConvNet. Especially, on the challenging CIFAR100 dataset, our IT-GAN achieves  $55.7\pm0.4\%$  and  $60.1\pm0.2\%$  for training the two models, which exceed DM (50.5 $\pm0.3\%$  and  $56.7\pm0.5\%$  by  $5.2\%$  and 3.4% respectively. Our method can easily further reduce the storage size by reducing the latent vector dimension. Furthermore, our method is more scalable as increasing latent vectors is cheaper than increasing synthetic images especially for high-resolution images. Hence, IT-GAN is a promising solution to dataset condensation problem.

## 3.4 Cross-architecture Generalization

The learned latent vectors and their corresponding synthetic images are generic to unseen architectures. We test them on popular deep neural networks including ConvNet, VGG19 [\[43\]](#page-7-19), ResNet18 [\[11\]](#page-6-15), WRN-16-8 [\[58\]](#page-8-12) and MobileNetV2 [\[39\]](#page-7-20). The results in Tab. [3](#page-5-4) verify that the synthetic training images work well in training all kinds of networks in downstream tasks.

|          | ConvNet  | VGG19    | ResNet18 | WRN-16-8 | MobileNetV2 |
|----------|----------|----------|----------|----------|-------------|
| CIFAR10  | 82.8 0.3 | 86.0 0.3 | 85.7 0.4 | 84.6 0.6 | 84.6 0.6    |
| CIFAR100 | 55.7 0.4 | 60.4 0.6 | 60.1 0.2 | 57.6 0.5 | 59.5 0.6    |

<span id="page-5-4"></span>Table 3: Cross-architecture generalization performance (%). We learn the latent vectors with ConvNet as the feature embedding function and then evaluate the generated training images on various unseen architectures.

# <span id="page-5-0"></span>4 Conclusion

In this paper, we investigate the informativeness of GANs synthesized images for training deep neural networks from scratch. We propose IT-GAN that converts a pre-trained GAN into an informative training sample generator. Condensation loss and diversity regularization are designed to learn the informative latent vectors. Experiments on popular image datasets verify that the deep neural networks can learn faster and achieve better performance when being trained with IT-GAN generated images. We also show that our method is a promising solution to dataset condensation problem.

Acknowledgment. This work is funded by China Scholarship Council 201806010331 and the EPSRC programme grant Visual AI EP/T028572/1.

## References

- <span id="page-6-17"></span>[1] Rameen Abdal, Yipeng Qin, and Peter Wonka. Image2stylegan: How to embed images into the stylegan latent space? In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 4432–4441, 2019.
- <span id="page-6-13"></span>[2] Antreas Antoniou, Amos Storkey, and Harrison Edwards. Data augmentation generative adversarial networks. *arXiv preprint arXiv:1711.04340*, 2017.
- <span id="page-6-18"></span>[3] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *Neural Information Processing Systems Workshop*, 2020.
- <span id="page-6-14"></span>[4] Christopher Bowles, Liang Chen, Ricardo Guerrero, Paul Bentley, Roger Gunn, Alexander Hammers, David Alexander Dickie, Maria Valdés Hernández, Joanna Wardlaw, and Daniel Rueckert. Gan augmentation: Augmenting training data using generative adversarial networks. *arXiv preprint arXiv:1810.10863*, 2018.
- <span id="page-6-1"></span>[5] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *ICLR*, 2019.
- <span id="page-6-19"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-6-10"></span>[7] Yulai Cong, Miaoyun Zhao, Jianqiao Li, Sijia Wang, and Lawrence Carin. Gan memory with no forgetting. *Advances in Neural Information Processing Systems*, 33:16481–16494, 2020.
- <span id="page-6-11"></span>[8] Gongfan Fang, Jie Song, Chengchao Shen, Xinchao Wang, Da Chen, and Mingli Song. Data-free adversarial distillation. *arXiv preprint arXiv:1912.11006*, 2019.
- <span id="page-6-0"></span>[9] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *Advances in neural information processing systems*, pages 2672–2680, 2014.
- <span id="page-6-22"></span>[10] Ishaan Gulrajani, Faruk Ahmed, Martin Arjovsky, Vincent Dumoulin, and Aaron C Courville. Improved training of wasserstein gans. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-6-15"></span>[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016.
- <span id="page-6-4"></span>[12] Zhenliang He, Wangmeng Zuo, Meina Kan, Shiguang Shan, and Xilin Chen. Attgan: Facial attribute editing by only changing what you want. *IEEE transactions on image processing*, 28(11):5464–5478, 2019.
- <span id="page-6-20"></span>[13] Sergey Ioffe and Christian Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. *ArXiv*, abs/1502.03167, 2015.
- <span id="page-6-5"></span>[14] Phillip Isola, Jun-Yan Zhu, Tinghui Zhou, and Alexei A Efros. Image-to-image translation with conditional adversarial networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 1125–1134, 2017.
- <span id="page-6-25"></span>[15] Woo-Young Kang and BT Zhang. Continual learning with generative replay via discriminative variational autoencoder, 2018.
- <span id="page-6-3"></span>[16] Tero Karras, Timo Aila, Samuli Laine, and Jaakko Lehtinen. Progressive growing of gans for improved quality, stability, and variation. *arXiv preprint arXiv:1710.10196*, 2017.
- <span id="page-6-2"></span>[17] Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 4401–4410, 2019.
- <span id="page-6-21"></span>[18] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014.
- <span id="page-6-23"></span>[19] Diederik P Kingma and Max Welling. Auto-encoding variational bayes. *arXiv preprint arXiv:1312.6114*, 2013.
- <span id="page-6-16"></span>[20] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009.
- <span id="page-6-24"></span>[21] Anders Boesen Lindbo Larsen, Søren Kaae Sønderby, Hugo Larochelle, and Ole Winther. Autoencoding beyond pixels using a learned similarity metric. In *International conference on machine learning*, pages 1558–1566. PMLR, 2016.
- <span id="page-6-6"></span>[22] Christian Ledig, Lucas Theis, Ferenc Huszár, Jose Caballero, Andrew Cunningham, Alejandro Acosta, Andrew Aitken, Alykhan Tejani, Johannes Totz, Zehan Wang, et al. Photo-realistic single image superresolution using a generative adversarial network. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4681–4690, 2017.
- <span id="page-6-9"></span>[23] Daiqing Li, Huan Ling, Seung Wook Kim, Karsten Kreis, Adela Barriuso, Sanja Fidler, and Antonio Torralba. Bigdatasetgan: Synthesizing imagenet with pixel-wise annotations. *arXiv preprint arXiv:2201.04684*, 2022.
- <span id="page-6-8"></span>[24] Daiqing Li, Junlin Yang, Karsten Kreis, Antonio Torralba, and Sanja Fidler. Semantic segmentation with generative models: Semi-supervised learning and strong out-of-domain generalization. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8300–8311, 2021.
- <span id="page-6-7"></span>[25] Yijun Li, Sifei Liu, Jimei Yang, and Ming-Hsuan Yang. Generative face completion. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 3911–3919, 2017.
- <span id="page-6-12"></span>[26] Liangchen Luo, Mark Sandler, Zi Lin, Andrey Zhmoginov, and Andrew Howard. Large-scale generative data-free distillation. *arXiv preprint arXiv:2012.05578*, 2020.

- <span id="page-7-23"></span>[27] Lars Mescheder, Sebastian Nowozin, and Andreas Geiger. Adversarial variational bayes: Unifying variational autoencoders and generative adversarial networks. In *International Conference on Machine Learning*, pages 2391–2400. PMLR, 2017.
- <span id="page-7-0"></span>[28] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014.
- <span id="page-7-22"></span>[29] Takeru Miyato, Toshiki Kataoka, Masanori Koyama, and Yuichi Yoshida. Spectral normalization for generative adversarial networks. *arXiv preprint arXiv:1802.05957*, 2018.
- <span id="page-7-18"></span>[30] Vinod Nair and Geoffrey E Hinton. Rectified linear units improve restricted boltzmann machines. In *Proceedings of the 27th international conference on machine learning (ICML-10)*, pages 807–814, 2010.
- <span id="page-7-14"></span>[31] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel-ridge regression. In *International Conference on Learning Representations*, 2021.
- <span id="page-7-15"></span>[32] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *arXiv preprint arXiv:2107.13034*, 2021.
- <span id="page-7-4"></span>[33] Deepak Pathak, Philipp Krahenbuhl, Jeff Donahue, Trevor Darrell, and Alexei A Efros. Context encoders: Feature learning by inpainting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2536–2544, 2016.
- <span id="page-7-10"></span>[34] Haibo Qiu, Baosheng Yu, Dihong Gong, Zhifeng Li, Wei Liu, and Dacheng Tao. Synface: Face recognition with synthetic data. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 10880–10890, 2021.
- <span id="page-7-1"></span>[35] Alec Radford, Luke Metz, and Soumith Chintala. Unsupervised representation learning with deep convolutional generative adversarial networks. *arXiv preprint arXiv:1511.06434*, 2015.
- <span id="page-7-11"></span>[36] Suman Ravuri and Oriol Vinyals. Seeing is not necessarily believing: Limitations of biggans for data augmentation. *International Conference on Learning Representations Workshops*, 2019.
- <span id="page-7-3"></span>[37] Scott Reed, Zeynep Akata, Xinchen Yan, Lajanugen Logeswaran, Bernt Schiele, and Honglak Lee. Generative adversarial text to image synthesis. In *International conference on machine learning*, pages 1060–1069. PMLR, 2016.
- <span id="page-7-21"></span>[38] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. *Advances in neural information processing systems*, 29, 2016.
- <span id="page-7-20"></span>[39] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018.
- <span id="page-7-9"></span>[40] Siyu Shao, Pu Wang, and Ruqiang Yan. Generative adversarial networks for data augmentation in machine fault diagnosis. *Computers in Industry*, 106:85–93, 2019.
- <span id="page-7-5"></span>[41] Hanul Shin, Jung Kwon Lee, Jaehong Kim, and Jiwon Kim. Continual learning with deep generative replay. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-7-8"></span>[42] Hoo-Chang Shin, Neil A Tenenholtz, Jameson K Rogers, Christopher G Schwarz, Matthew L Senjem, Jeffrey L Gunter, Katherine P Andriole, and Mark Michalski. Medical image synthesis for data augmentation and anonymization using generative adversarial networks. In *International workshop on simulation and synthesis in medical imaging*, pages 1–11. Springer, 2018.
- <span id="page-7-19"></span>[43] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-7-24"></span>[44] Jingkuan Song, Hanwang Zhang, Xiangpeng Li, Lianli Gao, Meng Wang, and Richang Hong. Selfsupervised video hashing with hierarchical binary auto-encoder. *IEEE Transactions on Image Processing*, 27(7):3210–3221, 2018.
- <span id="page-7-25"></span>[45] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O Stanley, and Jeff Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. *International Conference on Machine Learning (ICML)*, 2020.
- <span id="page-7-13"></span>[46] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-7-17"></span>[47] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016.
- <span id="page-7-16"></span>[48] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *CVPR*, 2022.
- <span id="page-7-12"></span>[49] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-7-2"></span>[50] Ting-Chun Wang, Ming-Yu Liu, Jun-Yan Zhu, Andrew Tao, Jan Kautz, and Bryan Catanzaro. Highresolution image synthesis and semantic manipulation with conditional gans. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 8798–8807, 2018.
- <span id="page-7-7"></span>[51] Yaxing Wang, Abel Gonzalez-Garcia, David Berga, Luis Herranz, Fahad Shahbaz Khan, and Joost van de Weijer. Minegan: effective knowledge transfer from gans to target domains with few images. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9332–9341, 2020.
- <span id="page-7-26"></span>[52] Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021.
- <span id="page-7-6"></span>[53] Chenshen Wu, Luis Herranz, Xialei Liu, Joost van de Weijer, Bogdan Raducanu, et al. Memory replay gans: Learning to generate new categories without forgetting. *Advances in Neural Information Processing Systems*, 31, 2018.

- <span id="page-8-7"></span>[54] Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. Gan inversion: A survey. *arXiv preprint arXiv:2101.05278*, 2021.
- <span id="page-8-5"></span>[55] Liyang Xie, Kaixiang Lin, Shu Wang, Fei Wang, and Jiayu Zhou. Differentially private generative adversarial network. *arXiv preprint arXiv:1802.06739*, 2018.
- <span id="page-8-2"></span>[56] You Xie, Erik Franz, Mengyu Chu, and Nils Thuerey. tempogan: A temporally coherent, volumetric gan for super-resolution fluid flow. *ACM Transactions on Graphics (TOG)*, 37(4):1–15, 2018.
- <span id="page-8-4"></span>[57] Yu Yang, Xiaotian Cheng, Hakan Bilen, and Xiangyang Ji. Learning to annotate part segmentation with gradient matching. In *International Conference on Learning Representations*, 2022.
- <span id="page-8-12"></span>[58] Sergey Zagoruyko and Nikos Komodakis. Wide residual networks. *arXiv preprint arXiv:1605.07146*, 2016.
- <span id="page-8-13"></span>[59] Han Zhang, Ian Goodfellow, Dimitris Metaxas, and Augustus Odena. Self-attention generative adversarial networks. In *International conference on machine learning*, pages 7354–7363. PMLR, 2019.
- <span id="page-8-1"></span>[60] Han Zhang, Tao Xu, Hongsheng Li, Shaoting Zhang, Xiaogang Wang, Xiaolei Huang, and Dimitris N Metaxas. Stackgan: Text to photo-realistic image synthesis with stacked generative adversarial networks. In *Proceedings of the IEEE international conference on computer vision*, pages 5907–5915, 2017.
- <span id="page-8-3"></span>[61] Yuxuan Zhang, Huan Ling, Jun Gao, Kangxue Yin, Jean-Francois Lafleche, Adela Barriuso, Antonio Torralba, and Sanja Fidler. Datasetgan: Efficient labeled data factory with minimal human effort. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 10145– 10155, 2021.
- <span id="page-8-9"></span>[62] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021.
- <span id="page-8-10"></span>[63] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021.
- <span id="page-8-8"></span>[64] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-8-11"></span>[65] Shengyu Zhao, Zhijian Liu, Ji Lin, Jun-Yan Zhu, and Song Han. Differentiable augmentation for dataefficient gan training. *Neural Information Processing Systems*, 2020.
- <span id="page-8-6"></span>[66] Jiapeng Zhu, Yujun Shen, Deli Zhao, and Bolei Zhou. In-domain gan inversion for real image editing. In *European conference on computer vision*, pages 592–608. Springer, 2020.
- <span id="page-8-0"></span>[67] Jun-Yan Zhu, Taesung Park, Phillip Isola, and Alexei A Efros. Unpaired image-to-image translation using cycle-consistent adversarial networks. In *Proceedings of the IEEE international conference on computer vision*, pages 2223–2232, 2017.

## A Preliminary

#### A.1 Generative Adversarial Networks

GANs  $[9]$  aim to synthesize photo-realistic images, which typically consist of a generator G and discriminator D. During training, the generator and discriminator are optimized for the minimax loss function:

$$
\min_{G} \max_{D} \mathbb{E}_{\boldsymbol{x} \sim P(\boldsymbol{x})}[\log D(\boldsymbol{x})] + \mathbb{E}_{\boldsymbol{z} \sim P(\boldsymbol{z})}[\log(1 - D(G(\boldsymbol{z})))],\tag{5}
$$

where  $P(z)$  is the distribution of latent vector z and  $P(x)$  is the real data distribution. A good generator is the one that can generate images to fool the discriminator. [\[28\]](#page-7-0) proposes a conditional GAN model that conditions each generated image on a semantic category  $y$ :

$$
\min_{G} \max_{D} \mathbb{E}_{\mathbf{x} \sim P(\mathbf{x})}[\log D(\mathbf{x}|y)] + \mathbb{E}_{\mathbf{z} \sim P(\mathbf{z})}[\log(1 - D(G(\mathbf{z}|y)))]. \tag{6}
$$

In this paper, we focus on conditional image generation task. In addition to its various applications [\[50;](#page-7-2) [12;](#page-6-4) [67;](#page-8-0) [60;](#page-8-1) [56;](#page-8-2) [25\]](#page-6-7), the recent advances in GANs focus on increasing the training stability [\[38;](#page-7-21) [10;](#page-6-22) [29\]](#page-7-22) and generating more diverse and real-looking images [\[5;](#page-6-1) [17;](#page-6-2) [59\]](#page-8-13).

While one can naively employ a state-of-the-art GAN model to synthesize images for specific classes and then build a synthetic image dataset, we argue and demonstrate that the synthesized sample, despite its real-looking appearance, is not informative to train accurate deep neural networks. In other words, models trained on such synthetic data obtain significantly lower performance when being applied to real images at test time (illustrated in Fig. [1\)](#page-1-0). This may be due to at least two reasons. First there can be a domain gap between synthesized and real training images, though a low discrimination loss has been achieved. Hence, the model trained on synthetic images has inferior performance on real testing images. Second the synthesized images, though optimized to look realistic, may be not as informative as real images for training purposes due to the loss of information about training. A potential way to address both issues is to find the latent vector for each synthesized image to obtain similar visual appearance to a real corresponding train image, which is investigated in GAN inversion [\[54\]](#page-8-7).

GAN Inversion. GAN Inversion [\[1;](#page-6-17) [66;](#page-8-6) [54\]](#page-8-7) aims to find the latent vector in the the latent space of the pre-trained GAN model, which can faithfully recover a given image. Many GAN inversion methods have been proposed, and they can be roughly categorized into 3 families: optimization based inversion, learning based inversion and hybrid methods. Usually, better performance is achieved by optimization based inversion methods, as they learn latent vector for each image independently. With a pre-trained generator  $G$ , the latent vector for every real image is optimized by:

$$
z^* = \underset{z}{\arg\min} \ell(G(z) - x),\tag{7}
$$

and  $\ell(\cdot)$  is the feature or pixel distance measurement. Please refer to [\[54\]](#page-8-7) for more details. GAN inversion methods focus on the manipulation of visual effects of a specific image. The learned synthetic images are not guaranteed to have more information for training deep neural networks.

Auto-encoder. Auto-encoders [\[19;](#page-6-23) [21;](#page-6-24) [27;](#page-7-23) [15;](#page-6-25) [44\]](#page-7-24) can also generate real-looking images by learning to reconstruct real images. We believe that our method can also work well with auto-encoders, while integration with GANs may have better performances due to the better latent space of GANs. Thus, we leave the integration with auto-encoder for the future work.

## A.2 Dataset Condensation

Given a large training set  $\mathcal{T} = \{x_i, y_i\}|_{i=1}^{|\mathcal{T}|}$ , dataset condensation aims to learn a small synthetic set  $S = \{s_i, y_i\}\big|_{i=1}^{|S|}$  so that the model  $\theta^S$  trained on S has close generalization performance to the model  $\theta^{\mathcal{T}}$  trained on  $\mathcal{T}$ . Following the notations in [\[63\]](#page-8-10), the objective is formulated as

$$
\mathbb{E}_{\boldsymbol{x} \sim P_{\mathcal{D}}}[\ell(\phi_{\boldsymbol{\theta}}\tau(\boldsymbol{x}), y)] \simeq \mathbb{E}_{\boldsymbol{x} \sim P_{\mathcal{D}}}[\ell(\phi_{\boldsymbol{\theta}}\zeta(\boldsymbol{x}), y)],\tag{8}
$$

where the loss  $\ell$  is computed on the samples from the real data distribution  $P_{\mathcal{D}}$ , and  $\phi$  is a deep neural network parameterized with  $\theta^{\mathcal{S}}$  or  $\theta^{\mathcal{T}}$ .

| Learning rate | 0.001          | 0.01           | 0.1            | 1              |
|---------------|----------------|----------------|----------------|----------------|
| Distribution  | 55.8 $\pm$ 1.2 | 56.8 $\pm$ 1.1 | 52.3 $\pm$ 0.9 | 46.5 $\pm$ 1.2 |
| Gradient      | 56.3 $\pm$ 1.4 | 54.9 $\pm$ 1.2 | 50.9 $\pm$ 0.9 | 43.4 $\pm$ 0.8 |

<span id="page-10-0"></span>Table T4: Comparison of distribution and gradient matching.

Meta-loss Based Methods. The existing solutions to dataset condensation can be categorized based on the objective functions. [\[49\]](#page-7-12) propose a meta-learning based method that formulates the trained model as a function of the learnable synthetic set:  $\theta^S(\mathcal{S})$  and then minimizes the meta-loss on the real training set:

$$
S^* = \underset{S}{\arg\min} \mathcal{L}^{\mathcal{T}}(\theta^S(S)) \quad \text{subject to} \quad \theta^S(S) = \underset{\theta}{\arg\min} \mathcal{L}^S(\theta). \tag{9}
$$

This meta-learning objective has to compute the bilevel optimization and unroll the recursive computation graph. Thus, it is both time-consuming and difficult to optimize. Several methods have been proposed to improve it by introducing learnable labels [\[46;](#page-7-13) [3\]](#page-6-18), ridge regression [\[3\]](#page-6-18) and kernel ridge regression [\[31;](#page-7-14) [32\]](#page-7-15).

[\[45\]](#page-7-25) propose the generative teaching network (GTN). Specifically, they train a generator that produces informative synthetic samples and minimize the meta-loss on real training set. In experiments, they find that training generator with random or shuffled latent vectors is worse than training with deterministic sequence of latent vectors.

Matching-loss Based Methods. [\[64\]](#page-8-8) propose a new framework that learns condensed synthetic data by matching the network gradients w.r.t. the real and synthetic data throughout the network optimization:

$$
S^* = \underset{S}{\arg\min} \mathbf{E}_{{\theta_0} \sim P_{{\theta_0}}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\theta} \mathcal{L}^S(\theta_t), \nabla_{\theta} \mathcal{L}^T(\theta_t)) \right] \quad (10)
$$

$$
\text{subject to } \theta_{t+1} \leftarrow \text{opt-alg}_{{\theta}}(\mathcal{L}^S(\theta_t), \varsigma_{\theta}, \eta_{\theta}),
$$

where  $D(\cdot, \cdot)$  computes the matching loss,  $P_{\theta_0}$  is the distribution of parameter initialization, and T,  $\varsigma_{\theta}$ ,  $\eta_{\theta}$  are the hyper-parameters. The new framework avoids to unroll the recursive computation graph, although it also involves bilevel optimization and second-order derivative. In addition, the synthetic data can learn from more supervision throughout the training dynamics of deep neural networks. [\[62\]](#page-8-9) further improve the data efficiency by introducing the differentiable Siamese augmentation that enables the learned synthetic data to train deep neural networks efficiently with data augmentation. [\[52\]](#page-7-26) learn some basic samples and combine them to form more new training samples which also improves the data efficiency. [\[48\]](#page-7-16) design an efficient bi-level optimization algorithm with dynamic outer and inner loops. [\[6\]](#page-6-19) propose to match training trajectories and thus mimic long-range behavior of real-data training.

Although training deep models on small synthetic sets is extremely fast, the above-mentioned bilevel optimization based condensation methods still require much computational resources to learn large synthetic sets. [\[63\]](#page-8-10) propose a simple yet effective method without bilevel optimization and secondorder derivative. Specifically, they match the distribution of real and synthetic data in many sampled embedding spaces:

$$
S^* = \underset{S}{\arg\min} \mathbb{E}_{\substack{\boldsymbol{\vartheta} \sim P_{\boldsymbol{\vartheta}} \\ \omega \sim \Omega}} \left\| \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(\boldsymbol{x}_i, \omega)) - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(\boldsymbol{s}_j, \omega)) \right\|^2, \tag{11}
$$

where  $\psi_{\theta}$  is the embedding function parameterized with  $\theta$  sampled from  $P_{\theta}$ .  $\mathcal{A}(\cdot,\omega)$  is the differentiable Siamese augmentation [\[62\]](#page-8-9) and  $\omega \sim \Omega$  is the augmentation parameter. [\[63\]](#page-8-10) also analytically connect the distribution matching with gradient matching [\[64\]](#page-8-8). The results show that with randomly initialized neural networks as the embedding functions, the method can achieve comparable or better performance than the state-of-the-art while significantly speeding up the synthesis process.

## B Ablation Study

We do ablation study experiments on CIFAR10 dataset. Unless otherwise stated, we train latent vectors with randomly initialized ConvNets for simplicity.

|                                                                                                                                                 | Split    | $1 \times 500$ | $2 \times 250$ |                                              | $4 \times 125$                                                          | $5 \times 100$ |     |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------|----------|----------------|----------------|----------------------------------------------|-------------------------------------------------------------------------|----------------|-----|--|
|                                                                                                                                                 | Accuracy |                |                | $71.8 \pm 0.8$ $71.6 \pm 0.5$ $71.1 \pm 1.0$ |                                                                         | $70.6 \pm 0.8$ |     |  |
|                                                                                                                                                 |          |                |                |                                              | Table T5: Performance $(\%)$ w.r.t. batch size to split latent vectors. |                |     |  |
| $\theta$                                                                                                                                        | 0.001    | 0.01           | 0.02           | 0.05                                         | 0.1                                                                     | 0.2            | 0.5 |  |
| $70.6 \pm 0.8$ $71.1 \pm 0.5$ $71.3 \pm 0.8$ $71.3 \pm 0.7$ $70.9 \pm 0.7$ $70.4 \pm 0.7$ $70.5 \pm 0.8$ $70.0 \pm 1.0$ $69.9 \pm 0.8$<br>Fixed |          |                |                |                                              |                                                                         |                |     |  |
| Random 69.7±1.0 70.4±0.7 71.1±0.8 70.8±0.8 70.5±0.8 70.6±0.8 70.0±0.7 70.1±1.0 69.7±1.1                                                         |          |                |                |                                              |                                                                         |                |     |  |

<span id="page-11-1"></span><span id="page-11-0"></span>Table T6: The comparison of fixed and random splitting strategies.

Distribution v.s. Gradient. DC [\[64\]](#page-8-8) and DM [\[63\]](#page-8-10) use feature distribution and gradient to implement dataset condensation respectively. We compare the effects of using distribution and gradient as the matching objective in our method. We set regularization coefficient  $\lambda = 0$  and learn 100 latent vectors per class. According to Tab. [T4,](#page-10-0) the performances of distribution and gradient matching with optimal learning rate are comparable. Thus, we use distribution matching for less computational cost.

Batch Size for Splitting Latent Vector Set. Due to the limitation of GPU memory, we cannot load all latent vectors and corresponding synthetic images into GPU for implementing back-propagation jointly. Thus, we have to split the latent vector set and optimize the subsets. We study the relation between performance and batch size for splitting latent vectors. Given total 500 latent vectors per class, they are split into  $1 \times 500$ ,  $2 \times 250$ ,  $4 \times 125$ ,  $5 \times 100$  groups in four experiments. In each experiment, the different groups of latent vectors are learned independently and then combined for training neural networks. For example, in  $2 \times 250$  experiment, we learn 2 independent 250 latent vectors per class sets and then combine them.  $\lambda$  is set to be 0. Tab. [T5](#page-11-0) presents the results, and the results indicate that larger batch size will have better performance. The reason is that when the latent vectors are randomly split into more subsets and learned separately, they will be homogeneous in terms of the training knowledge as they are trained with the same objective.

Fixed v.s. Mixed Latent Vector Set Splitting. In the above ablation study, the latent vector set is split into several fixed sets and learned independently. Another possible splitting strategy is mixed splitting. It means that the latent vectors will be mixed and randomly re-split in each training iteration. In this experiment, we validate two types of splitting with fixed and random grouping respectively. We learn 500 latent vectors per class and split them into 5 subsets. Tab. [T6](#page-11-1) depicts the results of two kinds of splitting with varying  $\lambda$ . The results show that when the regularization coefficient  $\lambda$  is small, random splitting is worse than fixed splitting. The two splitting strategies are comparable when  $\lambda$  is large. This phenomenon can also be explained by the aforementioned homogenization problem, and appropriate  $\lambda$  can relieve this problem by regularizing individual sample to preserve the diversity. Note that the magnitude of  $\mathcal{L}_{con}$  will vary significantly for different training batch sizes, thus  $\lambda$  needs to be tuned for specific training batch size.

Regularization Coefficient. Tab. [T6](#page-11-1) verifies that the regularization is important for learning better latent vectors. Especially, when the latent vectors are randomly grouped and optimized in each iteration, *i.e.* random splitting, each latent vector is forced to cooperate with every other one to achieve the same objective (minimizing condensation loss), which causes the homogenization problem of learned latent vectors. The regularization can largely relieve this problem.

Network Parameter Distribution. We can train latent vectors with feature embedding functions  $\psi_{\theta}(\cdot)$  from different distributions  $P_{\theta}$ . We study the influence of the network parameter distribution on the learned latent vectors in Tab. [T7.](#page-11-2) Following [\[63\]](#page-8-10), we train hundreds of ConvNets and group them (including intermediate snapshots) based on their validation performances. For example, we group networks with validation performance between 50% and 60%. Then, we learn 100 latent

<span id="page-11-2"></span>

| Random | $10-20$ $20-30$ $30-40$ $40-50$ $50-60$ $60-70$ $>70$                                                                |  |  |  |  |
|--------|----------------------------------------------------------------------------------------------------------------------|--|--|--|--|
|        | $55.8 + 1.2$ $55.8 + 1.4$ $56.3 + 0.6$ $56.4 + 0.5$ $57.2 + 0.7$ $57.0 + 1.0$ $57.3 + 0.8$ $57.3 + 1.1$ $57.3 + 1.2$ |  |  |  |  |

Table T7: Learning with different network parameter distributions. Networks are grouped based on validation performances (%).

vectors per class on these network groups separately. Tab. [T7](#page-11-2) shows that our method works well on all groups of networks including randomly initialized ones. Generally speaking, networks with higher performances lead to better learned latent vectors. Specifically, training latent vectors with ConvNets that have  $> 70\%$  validation accuracies achieves 57.3% testing accuracy which outperforms the result (55.8%) achieved by training with randomly initialized ConvNets by 1.5%.