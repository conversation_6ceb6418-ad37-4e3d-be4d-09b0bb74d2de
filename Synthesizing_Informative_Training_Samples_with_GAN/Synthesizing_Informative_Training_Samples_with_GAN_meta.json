{"table_of_contents": [{"title": "Synthesizing Informative Training Samples with GAN", "heading_level": null, "page_id": 0, "polygon": [[106.5, 97.5], [503.25, 99.0], [503.25, 117.0], [106.5, 116.982421875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.69140625, 222.75], [329.25, 222.75], [329.25, 233.96484375], [282.69140625, 233.96484375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 411.75], [191.9970703125, 411.75], [191.9970703125, 423.84375], [107.25, 423.84375]]}, {"title": "2 Method", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 525.0], [166.5, 525.0], [166.5, 535.9921875], [106.98046875, 535.9921875]]}, {"title": "2.1 Initializing with GAN Inversion", "heading_level": null, "page_id": 1, "polygon": [[106.5322265625, 548.75390625], [265.5, 548.75390625], [265.5, 558.03515625], [106.5322265625, 558.03515625]]}, {"title": "2.2 Condensing Training Information", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 681.0], [275.25, 681.0], [275.25, 691.453125], [106.98046875, 691.453125]]}, {"title": "2.3 Regularization", "heading_level": null, "page_id": 2, "polygon": [[107.25, 373.5], [194.25, 373.5], [194.25, 383.25], [107.25, 383.25]]}, {"title": "2.4 Training Algorithm", "heading_level": null, "page_id": 2, "polygon": [[107.25, 552.75], [214.5, 552.75], [214.5, 562.67578125], [107.25, 562.67578125]]}, {"title": "Algorithm 1: IT-GAN.", "heading_level": null, "page_id": 3, "polygon": [[106.15869140625, 76.5], [201.111328125, 76.5], [201.111328125, 86.818359375], [106.15869140625, 86.818359375]]}, {"title": "3 Experiments", "heading_level": null, "page_id": 3, "polygon": [[106.98046875, 361.1953125], [193.1923828125, 361.1953125], [193.1923828125, 372.796875], [106.98046875, 372.796875]]}, {"title": "3.1 Experimental Settings", "heading_level": null, "page_id": 3, "polygon": [[106.5, 387.0], [226.810546875, 387.0], [226.810546875, 397.16015625], [106.5, 397.16015625]]}, {"title": "3.2 Comp<PERSON>on to GAN Methods", "heading_level": null, "page_id": 4, "polygon": [[106.5, 239.958984375], [260.4287109375, 239.958984375], [260.4287109375, 250.013671875], [106.5, 250.013671875]]}, {"title": "3.3 Comparison to Dataset Condensation", "heading_level": null, "page_id": 4, "polygon": [[106.5, 647.75390625], [291.05859375, 647.75390625], [291.05859375, 657.80859375], [106.5, 657.80859375]]}, {"title": "3.4 Cross-architecture Generalization", "heading_level": null, "page_id": 5, "polygon": [[106.5, 442.5], [275.25, 442.5], [275.25, 452.84765625], [106.5, 452.84765625]]}, {"title": "4 Conclusion", "heading_level": null, "page_id": 5, "polygon": [[107.25, 604.5], [184.2275390625, 604.5], [184.2275390625, 616.81640625], [107.25, 616.81640625]]}, {"title": "References", "heading_level": null, "page_id": 6, "polygon": [[106.5, 72.75], [164.35546875, 72.75], [164.35546875, 84.0146484375], [106.5, 84.0146484375]]}, {"title": "A Preliminary", "heading_level": null, "page_id": 9, "polygon": [[106.5, 72.75], [191.25, 72.75], [191.25, 83.77294921875], [106.5, 83.77294921875]]}, {"title": "A.1 Generative Adversarial Networks", "heading_level": null, "page_id": 9, "polygon": [[106.98046875, 98.25], [276.75, 98.25], [276.75, 107.5078125], [106.98046875, 107.5078125]]}, {"title": "A.2 Dataset Condensation", "heading_level": null, "page_id": 9, "polygon": [[107.25, 615.0], [227.25, 615.0], [227.25, 625.7109375], [107.25, 625.7109375]]}, {"title": "B Ablation Study", "heading_level": null, "page_id": 10, "polygon": [[106.5, 677.25], [206.25, 677.25], [206.25, 688.359375], [106.5, 688.359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 46], ["Text", 4], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3801, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 325], ["Line", 69], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 827, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 626], ["Line", 72], ["Text", 4], ["Equation", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 732, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 606], ["Line", 51], ["TableCell", 21], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 1], ["Equation", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4455, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 73], ["Text", 6], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 957, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["TableCell", 76], ["Line", 51], ["Reference", 5], ["Caption", 4], ["Text", 4], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Figure", 1], ["Picture", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 8337, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 67], ["ListItem", 26], ["Reference", 26], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 67], ["ListItem", 27], ["Reference", 27], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 33], ["ListItem", 14], ["Reference", 14], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 59], ["Text", 7], ["Equation", 4], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["Line", 95], ["TableCell", 23], ["Text", 6], ["Equation", 3], ["TextInlineMath", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3916, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["TableCell", 66], ["Line", 50], ["Text", 5], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 2, "llm_tokens_used": 2396, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 6], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Synthesizing_Informative_Training_Samples_with_GAN"}