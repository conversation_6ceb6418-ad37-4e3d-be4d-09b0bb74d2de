{"table_of_contents": [{"title": "Synthetic Text Generation for Training Large Language Models\nvia Gradient Matching", "heading_level": null, "page_id": 0, "polygon": [[100.5, 89.25], [495.0, 89.25], [495.0, 120.65625], [100.5, 120.65625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 203.25], [195.75, 203.25], [195.75, 214.822265625], [148.5, 214.822265625]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 549.0], [132.8291015625, 549.0], [132.8291015625, 561.515625], [54.0, 561.515625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[54.0, 634.5], [139.5, 634.5], [139.5, 645.8203125], [54.0, 645.8203125]]}, {"title": "2.1. Dataset Distillation (DD)", "heading_level": null, "page_id": 1, "polygon": [[54.0, 654.75], [178.5, 654.75], [178.5, 665.15625], [54.0, 665.15625]]}, {"title": "2.2. Synthetic Text Generation using Generative Models", "heading_level": null, "page_id": 1, "polygon": [[305.25, 637.5], [542.25, 637.5], [542.25, 648.140625], [305.25, 648.140625]]}, {"title": "3. Problem Formulation", "heading_level": null, "page_id": 2, "polygon": [[54.0, 545.25], [178.5, 545.25], [178.5, 556.1015625], [54.0, 556.1015625]]}, {"title": "4. Method", "heading_level": null, "page_id": 2, "polygon": [[306.0, 479.25], [360.0, 479.25], [360.0, 490.74609375], [306.0, 490.74609375]]}, {"title": "4.1. Text Generation via Gradient Matching", "heading_level": null, "page_id": 2, "polygon": [[305.25, 536.25], [494.25, 536.25], [494.25, 546.046875], [305.25, 546.046875]]}, {"title": "4.2. Alternating Between Text and Embedding Spaces", "heading_level": null, "page_id": 3, "polygon": [[54.0, 387.0], [284.0361328125, 387.0], [284.0361328125, 396.7734375], [54.0, 396.7734375]]}, {"title": "Constrained Gradient Matching in the Embedding Space.", "heading_level": null, "page_id": 3, "polygon": [[305.25, 188.25], [543.0, 188.25], [543.0, 198.193359375], [305.25, 198.193359375]]}, {"title": "Algorithm 1 GRADient matching w. ADMM (GRADMM)", "heading_level": null, "page_id": 4, "polygon": [[54.0, 68.25], [291.05859375, 68.25], [291.05859375, 78.7939453125], [54.0, 78.7939453125]]}, {"title": "4.3. Dealing with High-dimension Gradients", "heading_level": null, "page_id": 4, "polygon": [[306.75, 69.0], [495.0, 69.0], [495.0, 79.3740234375], [306.75, 79.3740234375]]}, {"title": "4.4. Filtering the Generated Examples", "heading_level": null, "page_id": 4, "polygon": [[306.0, 395.25], [469.5, 395.25], [469.5, 405.66796875], [306.0, 405.66796875]]}, {"title": "4.5. Making GRADMM Differentially Private", "heading_level": null, "page_id": 5, "polygon": [[54.0, 220.5], [248.25, 220.5], [248.25, 230.25], [54.0, 230.25]]}, {"title": "4.6. Convergence Analysis", "heading_level": null, "page_id": 5, "polygon": [[54.0, 575.25], [167.25, 575.25], [167.25, 584.71875], [54.0, 584.71875]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 5, "polygon": [[305.40234375, 656.25], [385.5, 656.25], [385.5, 666.703125], [305.40234375, 666.703125]]}, {"title": "5.1. Experimental settings", "heading_level": null, "page_id": 5, "polygon": [[306.0, 676.5], [418.5, 676.5], [418.5, 686.8125], [306.0, 686.8125]]}, {"title": "5.2. Main results", "heading_level": null, "page_id": 6, "polygon": [[54.0, 623.25], [126.75, 623.25], [126.75, 633.83203125], [54.0, 633.83203125]]}, {"title": "5.2.1. GENERATING LARGER SYNTHETIC FINE-TUNING\nDATA IN DATA-SCARCE REGIME", "heading_level": null, "page_id": 6, "polygon": [[305.701171875, 334.5], [542.25, 334.5], [542.25, 356.16796875], [305.701171875, 356.16796875]]}, {"title": "5.2.2. GENERATING SMALL SYNTHETIC DATA BASED\nON LARGER FINE-TUNING DATA", "heading_level": null, "page_id": 6, "polygon": [[306.0, 544.5], [537.0, 544.5], [537.0, 565.76953125], [306.0, 565.76953125]]}, {"title": "5.3. Analysis", "heading_level": null, "page_id": 7, "polygon": [[54.0, 663.99609375], [109.5, 663.99609375], [109.5, 674.05078125], [54.0, 674.05078125]]}, {"title": "5.4. Ablation study", "heading_level": null, "page_id": 8, "polygon": [[54.0, 604.5], [136.5, 604.5], [136.5, 614.8828125], [54.0, 614.8828125]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 533.25], [377.25, 533.25], [377.25, 544.11328125], [306.0, 544.11328125]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [147.75, 68.25], [147.75, 79.7607421875], [54.0, 79.7607421875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 9, "polygon": [[54.0, 187.5], [155.390625, 187.5], [155.390625, 198.7734375], [54.0, 198.7734375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 283.5], [111.75, 283.5], [111.75, 294.486328125], [54.0, 294.486328125]]}, {"title": "<PERSON><PERSON>ver<PERSON>", "heading_level": null, "page_id": 12, "polygon": [[54.0, 68.25], [136.5, 68.25], [136.5, 79.51904296875], [54.0, 79.51904296875]]}, {"title": "A.1. <PERSON><PERSON> of Lemma 4.1", "heading_level": null, "page_id": 12, "polygon": [[54.0, 113.25], [159.75, 113.25], [159.75, 123.6533203125], [54.0, 123.6533203125]]}, {"title": "A.2. <PERSON><PERSON> of Theorem 4.2", "heading_level": null, "page_id": 12, "polygon": [[54.0, 432.0], [165.75, 432.0], [165.75, 441.6328125], [54.0, 441.6328125]]}, {"title": "A.3. <PERSON><PERSON> of Corollary 4.3", "heading_level": null, "page_id": 13, "polygon": [[54.0, 243.0], [168.75, 243.0], [168.75, 254.07421875], [54.0, 254.07421875]]}, {"title": "<PERSON><PERSON> Prompts", "heading_level": null, "page_id": 13, "polygon": [[54.0, 356.25], [113.25, 356.25], [113.25, 367.189453125], [54.0, 367.189453125]]}, {"title": "B.1. Zero/Few-shot prompts", "heading_level": null, "page_id": 13, "polygon": [[54.0, 377.25], [174.75, 377.25], [174.75, 386.912109375], [54.0, 386.912109375]]}, {"title": "B.2. Few-shots Evaluation prompts", "heading_level": null, "page_id": 13, "polygon": [[54.0, 688.5], [204.75, 688.5], [204.75, 698.80078125], [54.0, 698.80078125]]}, {"title": "{Few-shot examples}", "heading_level": null, "page_id": 14, "polygon": [[69.7763671875, 134.0947265625], [159.275390625, 134.0947265625], [159.275390625, 145.3095703125], [69.7763671875, 145.3095703125]]}, {"title": "C. Generation Samples", "heading_level": null, "page_id": 14, "polygon": [[54.0, 335.865234375], [174.216796875, 335.865234375], [174.216796875, 348.240234375], [54.0, 348.240234375]]}, {"title": "C.1. Synthetic SST2 Samples by GRADMM", "heading_level": null, "page_id": 14, "polygon": [[53.9384765625, 356.16796875], [240.0, 356.16796875], [240.0, 367.76953125], [53.9384765625, 367.76953125]]}, {"title": "C.1.2. NEGATIVE LABEL", "heading_level": null, "page_id": 14, "polygon": [[54.0, 551.4609375], [163.6083984375, 551.4609375], [163.6083984375, 562.2890625], [54.0, 562.2890625]]}, {"title": "C.2. Synthetic Rotten Tomatoes Samples by GRADMM", "heading_level": null, "page_id": 14, "polygon": [[54.0, 669.796875], [290.4609375, 669.796875], [290.4609375, 680.625], [54.0, 680.625]]}, {"title": "C.2.2. NEGATIVE LABEL", "heading_level": null, "page_id": 15, "polygon": [[54.0, 356.25], [163.5, 356.25], [163.5, 367.3828125], [54.0, 367.3828125]]}, {"title": "C.3. Synthetic Tweet Emotions Samples by GRADMM", "heading_level": null, "page_id": 15, "polygon": [[54.0, 471.75], [285.75, 471.75], [285.75, 483.01171875], [54.0, 483.01171875]]}, {"title": "C.3.2. NEGATIVE LABEL", "heading_level": null, "page_id": 15, "polygon": [[54.0, 606.0], [163.5, 606.75], [163.5, 616.81640625], [54.0, 616.81640625]]}, {"title": "<PERSON><PERSON> Additional experiments", "heading_level": null, "page_id": 17, "polygon": [[54.0, 66.6123046875], [192.4453125, 66.6123046875], [192.4453125, 79.470703125], [54.0, 79.470703125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 314], ["Line", 87], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON>Footer", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8117, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 337], ["Line", 103], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 670], ["Line", 109], ["Text", 7], ["TextInlineMath", 5], ["SectionHeader", 3], ["Equation", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 742], ["Line", 116], ["Text", 10], ["Equation", 6], ["TextInlineMath", 5], ["Reference", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 911], ["Line", 117], ["ListItem", 18], ["Text", 6], ["Equation", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 801], ["Line", 128], ["TextInlineMath", 10], ["Text", 6], ["SectionHeader", 4], ["Equation", 4], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2172, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1494], ["TableCell", 542], ["Line", 80], ["Text", 7], ["SectionHeader", 3], ["TextInlineMath", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 14895, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 636], ["Line", 109], ["TableCell", 40], ["Text", 6], ["Reference", 4], ["Caption", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 100], ["TableCell", 72], ["Text", 9], ["TextInlineMath", 4], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3513, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 93], ["ListItem", 20], ["Reference", 20], ["SectionHeader", 3], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 95], ["ListItem", 24], ["Reference", 23], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 77], ["ListItem", 19], ["Reference", 19], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 953], ["Line", 108], ["Equation", 13], ["Text", 8], ["TextInlineMath", 6], ["Reference", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 58], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 4], ["Equation", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 44], ["ListItem", 11], ["Text", 8], ["SectionHeader", 5], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 36], ["ListItem", 19], ["Text", 7], ["ListGroup", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 117], ["Reference", 5], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 3969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 20], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Synthetic_Text_Generation_for_Training_Large_Language_Models_via_Gradient_Matching"}