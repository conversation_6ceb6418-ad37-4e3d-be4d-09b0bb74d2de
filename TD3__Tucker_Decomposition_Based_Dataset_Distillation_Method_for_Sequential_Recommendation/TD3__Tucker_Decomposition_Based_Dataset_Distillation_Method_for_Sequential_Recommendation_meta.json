{"table_of_contents": [{"title": "TD3: Tucker Decomposition Based Dataset Distillation Method\nfor Sequential Recommendation", "heading_level": null, "page_id": 0, "polygon": [[58.5, 83.25], [551.25, 83.25], [551.25, 118.529296875], [58.5, 118.529296875]]}, {"title": "<PERSON><PERSON>\njun<PERSON><EMAIL>\nBeijing University of Posts and\nTelecommunications,\nBeijing, China", "heading_level": null, "page_id": 0, "polygon": [[159.75, 254.4609375], [295.541015625, 254.4609375], [295.541015625, 317.25], [159.75, 317.25]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[441.75, 130.904296875], [497.25, 130.904296875], [497.25, 142.505859375], [441.75, 142.505859375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[52.5, 327.0], [97.04443359375, 327.0], [97.04443359375, 337.60546875], [52.5, 337.60546875]]}, {"title": "CCS Concepts", "heading_level": null, "page_id": 0, "polygon": [[316.5, 411.0], [387.75, 411.0], [387.75, 421.91015625], [316.5, 421.91015625]]}, {"title": "• Information systems \\rightarrow Data mining.", "heading_level": null, "page_id": 0, "polygon": [[316.5, 425.25], [474.240234375, 425.25], [474.240234375, 435.4453125], [316.5, 435.4453125]]}, {"title": "Keywords", "heading_level": null, "page_id": 0, "polygon": [[316.5, 447.75], [367.5, 447.75], [367.5, 457.875], [316.5, 457.875]]}, {"title": "ACM Reference Format:", "heading_level": null, "page_id": 0, "polygon": [[316.5, 478.5], [405.75, 478.5], [405.75, 488.0390625], [316.5, 488.0390625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[317.25, 564.22265625], [398.25, 564.22265625], [398.25, 574.27734375], [317.25, 574.27734375]]}, {"title": "Figure 1: Comparison of the data-centric recommender sys-\ntem through the lens of dataset distillation approach with\nthe traditional model-centric recommender system. The key\ndifference lies in their distinct optimization objectives.", "heading_level": null, "page_id": 1, "polygon": [[52.5, 243.75], [295.5, 243.75], [295.5, 286.9453125], [52.5, 286.9453125]]}, {"title": "2 Related Work", "heading_level": null, "page_id": 1, "polygon": [[316.5, 488.25], [403.5, 488.25], [403.5, 498.48046875], [316.5, 498.48046875]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 2, "polygon": [[316.5, 85.5], [399.75, 85.5], [399.75, 95.***********], [316.5, 95.***********]]}, {"title": "3.1 Overview", "heading_level": null, "page_id": 2, "polygon": [[316.5, 146.25], [390.0, 146.25], [390.0, 157.1044921875], [316.5, 157.1044921875]]}, {"title": "WWW '25, April 28-May 2, 2025, Sydney, NSW, Australia <PERSON><PERSON><PERSON> et al.", "heading_level": null, "page_id": 3, "polygon": [[52.5, 60.75], [558.0, 60.75], [558.0, 69.***********], [52.5, 69.***********]]}, {"title": "3.2 Synthetic Summary Decomposition", "heading_level": null, "page_id": 3, "polygon": [[52.5, 403.5], [254.25, 403.5], [254.25, 413.7890625], [52.5, 413.7890625]]}, {"title": "3.3 Enhanced Bi-Level Optimization", "heading_level": null, "page_id": 3, "polygon": [[316.16015625, 365.25], [504.0, 365.25], [504.0, 375.1171875], [316.16015625, 375.1171875]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[52.5, 203.25], [134.25, 203.25], [134.25, 215.015625], [52.5, 215.015625]]}, {"title": "4.1 Settings", "heading_level": null, "page_id": 5, "polygon": [[52.5, 251.25], [118.***********, 251.25], [118.***********, 261.03515625], [52.5, 261.03515625]]}, {"title": "4.2 Overall Performance", "heading_level": null, "page_id": 5, "polygon": [[317.25, 408.75], [447.0, 408.75], [447.0, 418.81640625], [317.25, 418.81640625]]}, {"title": "4.3 Time and Memory Analysis", "heading_level": null, "page_id": 6, "polygon": [[52.5, 488.25], [216.0, 488.25], [216.0, 498.8671875], [52.5, 498.8671875]]}, {"title": "4.4 Cross-Architecture Generalization", "heading_level": null, "page_id": 7, "polygon": [[52.5, 405.75], [248.25, 405.75], [248.25, 416.109375], [52.5, 416.109375]]}, {"title": "4.5 Ablation Studies", "heading_level": null, "page_id": 7, "polygon": [[52.5, 597.75], [160.5, 597.75], [160.5, 608.30859375], [52.5, 608.30859375]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 7, "polygon": [[317.056640625, 411.75], [391.5, 411.75], [391.5, 421.91015625], [317.056640625, 421.91015625]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[52.5, 86.25], [108.0, 86.25], [108.0, 96.099609375], [52.5, 96.099609375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 283], ["Line", 105], ["Text", 16], ["SectionHeader", 9], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 104], ["Text", 6], ["ListItem", 4], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 892], ["Line", 166], ["TextInlineMath", 7], ["Equation", 5], ["Text", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 694], ["Line", 127], ["Text", 5], ["TextInlineMath", 4], ["SectionHeader", 3], ["Equation", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2004, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 753], ["Line", 168], ["TableCell", 80], ["Text", 5], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4229, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 950], ["TableCell", 184], ["Line", 118], ["Text", 9], ["SectionHeader", 3], ["ListItem", 3], ["Caption", 2], ["Table", 2], ["Footnote", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 6306, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 1480], ["TableCell", 277], ["Line", 94], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["Reference", 2], ["SectionHeader", 1], ["Equation", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1490, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 109], ["TableCell", 102], ["Text", 5], ["Caption", 3], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2497, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["Line", 155], ["Reference", 50], ["ListItem", 49], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 386], ["Line", 117], ["Reference", 34], ["ListItem", 33], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/TD3__Tucker_Decomposition_Based_Dataset_Distillation_Method_for_Sequential_Recommendation"}