{"table_of_contents": [{"title": "Taming Diffusion for Dataset Distillation with High Representativeness", "heading_level": null, "page_id": 0, "polygon": [[80.25, 89.25], [516.375, 89.25], [516.375, 104.02734375], [80.25, 104.02734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 186.8818359375], [148.5, 186.8818359375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 560.25], [132.75, 560.25], [132.75, 571.5703125], [54.0, 571.5703125]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[306.0, 108.75], [390.8671875, 108.75], [390.8671875, 119.9794921875], [306.0, 119.9794921875]]}, {"title": "3. Background, Formulation, and Motivation", "heading_level": null, "page_id": 2, "polygon": [[54.0, 179.25], [285.978515625, 180.0], [285.978515625, 191.0390625], [54.0, 191.0390625]]}, {"title": "3.1. Preliminaries on Diffusion Models", "heading_level": null, "page_id": 2, "polygon": [[54.0, 200.25], [219.75, 200.25], [219.75, 210.76171875], [54.0, 210.76171875]]}, {"title": "3.2. Problem Formulation for Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[54.0, 592.5], [264.75, 592.5], [264.75, 602.5078125], [54.0, 602.5078125]]}, {"title": "3.3. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 2, "polygon": [[306.0, 69.5126953125], [373.236328125, 69.5126953125], [373.236328125, 78.6005859375], [306.0, 78.6005859375]]}, {"title": "4. Methodology for Dataset Distillation", "heading_level": null, "page_id": 3, "polygon": [[54.0, 492.0], [253.5, 492.0], [253.5, 502.734375], [54.0, 502.734375]]}, {"title": "4.1. Framework Overview", "heading_level": null, "page_id": 3, "polygon": [[54.0, 549.0], [166.5, 549.0], [166.5, 559.1953125], [54.0, 559.1953125]]}, {"title": "4.2. Domain Mapping", "heading_level": null, "page_id": 3, "polygon": [[306.0, 267.0], [400.5, 267.0], [400.5, 277.470703125], [306.0, 277.470703125]]}, {"title": "4.3. Distribution Matching", "heading_level": null, "page_id": 4, "polygon": [[54.0, 471.0], [169.5, 471.0], [169.5, 481.46484375], [54.0, 481.46484375]]}, {"title": "4.4. Group Sampling", "heading_level": null, "page_id": 4, "polygon": [[306.0, 166.5], [396.84375, 166.5], [396.84375, 177.0205078125], [306.0, 177.0205078125]]}, {"title": "4.5. Advantages of D^3HR", "heading_level": null, "page_id": 5, "polygon": [[54.0, 592.5], [163.5, 592.5], [163.5, 603.28125], [54.0, 603.28125]]}, {"title": "5. Main Results", "heading_level": null, "page_id": 5, "polygon": [[306.0, 417.0], [387.28125, 417.0], [387.28125, 428.09765625], [306.0, 428.09765625]]}, {"title": "5.1. Experimental Details", "heading_level": null, "page_id": 5, "polygon": [[306.0, 437.765625], [415.5, 437.765625], [415.5, 447.046875], [306.0, 447.046875]]}, {"title": "5.2. Comp<PERSON>on with State-of-the-art Methods", "heading_level": null, "page_id": 6, "polygon": [[54.0, 443.1796875], [255.796875, 443.1796875], [255.796875, 453.234375], [54.0, 453.234375]]}, {"title": "1, \\mathbf{D}^3\\mathbf{HR} demonstrates superior per-\nformance across all IPCs compared with baselines.", "heading_level": null, "page_id": 6, "polygon": [[54.0, 460.9137268066406], [291.0890808105469, 460.9137268066406], [291.0890808105469, 484.171875], [54.0, 484.171875]]}, {"title": "5.3. Cross-architecture Generalization", "heading_level": null, "page_id": 6, "polygon": [[305.25, 251.25], [469.5, 251.25], [469.5, 261.615234375], [305.25, 261.615234375]]}, {"title": "6. Analysis", "heading_level": null, "page_id": 6, "polygon": [[306.0, 429.0], [363.0, 429.0], [363.0, 439.69921875], [306.0, 439.69921875]]}, {"title": "6.1. Ablation Studies", "heading_level": null, "page_id": 6, "polygon": [[306.0, 450.0], [396.0, 450.0], [396.0, 459.421875], [306.0, 459.421875]]}, {"title": "6.2. Analysis of Different Inversion Steps", "heading_level": null, "page_id": 7, "polygon": [[54.0, 467.25], [228.75, 467.25], [228.75, 477.59765625], [54.0, 477.59765625]]}, {"title": "6.3. Image Visualization", "heading_level": null, "page_id": 7, "polygon": [[306.0, 489.0], [410.25, 489.0], [410.25, 499.25390625], [306.0, 499.25390625]]}, {"title": "6.4. D<sup>3</sup>HR Outperforms Under Hard Labels", "heading_level": null, "page_id": 7, "polygon": [[305.25, 628.5], [495.75, 628.5], [495.75, 638.859375], [305.25, 638.859375]]}, {"title": "6.5. Improving Results with More Soft Labels", "heading_level": null, "page_id": 8, "polygon": [[54.0, 322.5], [249.75, 322.5], [249.75, 331.8046875], [54.0, 331.8046875]]}, {"title": "6.6. <PERSON><PERSON><PERSON> of D^3HR", "heading_level": null, "page_id": 8, "polygon": [[54.0, 562.5], [161.25, 562.5], [161.25, 572.34375], [54.0, 572.34375]]}, {"title": "6.7. Storage Requirements Smaller than D", "heading_level": null, "page_id": 8, "polygon": [[305.103515625, 281.25], [488.25, 281.25], [488.25, 291.19921875], [305.103515625, 291.19921875]]}, {"title": "7. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 423.0], [377.25, 423.0], [377.25, 435.4453125], [306.0, 435.4453125]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[306.0, 555.0], [399.75, 555.0], [399.75, 566.15625], [306.0, 566.15625]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 8, "polygon": [[306.0, 639.0], [402.0, 639.0], [402.0, 650.4609375], [306.0, 650.4609375]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 68.25], [111.0, 68.25], [111.0, 79.03564453125], [54.0, 79.03564453125]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 11, "polygon": [[253.5, 64.92041015625], [343.353515625, 64.92041015625], [343.353515625, 86.67333984375], [253.5, 86.67333984375]]}, {"title": "A. Theoretical Analysis", "heading_level": null, "page_id": 11, "polygon": [[54.0, 208.5], [175.5, 208.5], [175.5, 219.462890625], [54.0, 219.462890625]]}, {"title": "B. More Implementation Details", "heading_level": null, "page_id": 12, "polygon": [[54.0, 454.5], [220.5, 454.5], [220.5, 465.609375], [54.0, 465.609375]]}, {"title": "<PERSON><PERSON> Additional Experiments", "heading_level": null, "page_id": 13, "polygon": [[54.0, 67.5], [194.3876953125, 67.5], [194.3876953125, 78.9873046875], [54.0, 78.9873046875]]}, {"title": "C.1. Validate the Limitations of D^4M", "heading_level": null, "page_id": 13, "polygon": [[54.0, 87.75], [212.25, 87.75], [212.25, 98.61328125], [54.0, 98.61328125]]}, {"title": "C.2. More Analysis about Group Sampling", "heading_level": null, "page_id": 13, "polygon": [[54.0, 640.5], [238.5, 640.5], [238.5, 650.84765625], [54.0, 650.84765625]]}, {"title": "C.3. Visualization of Storage requirements", "heading_level": null, "page_id": 14, "polygon": [[54.0, 604.5], [237.0, 604.5], [237.0, 615.26953125], [54.0, 615.26953125]]}, {"title": "C.4. Evolution of the Latent Space Across Timesteps", "heading_level": null, "page_id": 15, "polygon": [[53.71435546875, 663.75], [279.75, 663.75], [279.75, 675.2109375], [53.71435546875, 675.2109375]]}, {"title": "C.5. More Comparison of Other SOTA Methods under Different Setting", "heading_level": null, "page_id": 16, "polygon": [[53.6396484375, 651.75], [363.375, 651.75], [363.375, 662.44921875], [53.6396484375, 662.44921875]]}, {"title": "C.6. Cross-architecture Generalization", "heading_level": null, "page_id": 17, "polygon": [[54.0, 391.5], [221.25, 391.5], [221.25, 402.1875], [54.0, 402.1875]]}, {"title": "C.7. More Ablation Studies on the Hyper-parameters", "heading_level": null, "page_id": 18, "polygon": [[54.0, 294.0], [282.0, 294.0], [282.0, 304.34765625], [54.0, 304.34765625]]}, {"title": "D. Image Visualization", "heading_level": null, "page_id": 18, "polygon": [[54.0, 668.25], [171.75, 668.25], [171.75, 680.23828125], [54.0, 680.23828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 318], ["Line", 89], ["Text", 6], ["SectionHeader", 3], ["Footnote", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8751, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 96], ["Text", 9], ["ListItem", 3], ["Caption", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1204, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 838], ["Line", 111], ["Text", 8], ["TextInlineMath", 7], ["SectionHeader", 4], ["Reference", 3], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1037, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 103], ["Text", 4], ["SectionHeader", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1267], ["Line", 151], ["TextInlineMath", 12], ["Equation", 5], ["Reference", 4], ["Text", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1124], ["TableCell", 137], ["Line", 83], ["TextInlineMath", 4], ["Text", 4], ["Caption", 3], ["SectionHeader", 3], ["Reference", 3], ["Table", 1], ["Figure", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 714], ["TableCell", 107], ["Line", 103], ["Text", 8], ["SectionHeader", 5], ["Caption", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 16369, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 390], ["Line", 105], ["Text", 5], ["Reference", 5], ["Figure", 3], ["Caption", 3], ["TextInlineMath", 3], ["SectionHeader", 3], ["FigureGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2128, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 521], ["Line", 101], ["TableCell", 94], ["Text", 7], ["SectionHeader", 6], ["Caption", 4], ["Reference", 4], ["Table", 2], ["TextInlineMath", 2], ["TableGroup", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8569, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 92], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 93], ["ListItem", 23], ["Reference", 23], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 801], ["Line", 73], ["TextInlineMath", 7], ["ListItem", 4], ["Equation", 4], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 567], ["TableCell", 85], ["Line", 82], ["TextInlineMath", 5], ["Equation", 5], ["Text", 4], ["Reference", 4], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 4289, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 24], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 672, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 200], ["TableCell", 82], ["Line", 30], ["Caption", 5], ["Table", 4], ["TableGroup", 4], ["Reference", 3], ["Text", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 9391, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 52], ["Line", 27], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 814, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 93], ["Caption", 2], ["Figure", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 896, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["TableCell", 221], ["Line", 30], ["Caption", 2], ["Table", 2], ["Text", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8349, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 828], ["TableCell", 162], ["Line", 38], ["Reference", 5], ["Caption", 4], ["Table", 4], ["TableGroup", 4], ["Text", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 4523, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 15], ["Figure", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 733, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 5], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 724, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Taming_Diffusion_for_Dataset_Distillation_with_High_Representativeness"}