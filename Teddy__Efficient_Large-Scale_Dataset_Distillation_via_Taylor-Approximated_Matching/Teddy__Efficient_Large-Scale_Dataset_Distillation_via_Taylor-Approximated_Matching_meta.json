{"table_of_contents": [{"title": "Teddy: Efficient Large-Scale Dataset Distillation\nvia Taylor-Approximated Matching", "heading_level": null, "page_id": 0, "polygon": [[138.2080078125, 114.75], [474.75, 114.75], [474.75, 145.40625], [138.2080078125, 146.25]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[133.5, 549.75], [229.5, 549.75], [229.5, 561.90234375], [133.5, 561.90234375]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 2, "polygon": [[133.5, 534.75], [242.4990234375, 534.75], [242.4990234375, 546.43359375], [133.5, 546.43359375]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 3, "polygon": [[133.5, 408.0], [232.5, 408.0], [232.5, 419.203125], [133.5, 419.203125]]}, {"title": "3.1 Preliminary", "heading_level": null, "page_id": 3, "polygon": [[133.5, 480.69140625], [220.5, 480.69140625], [220.5, 489.97265625], [133.5, 489.97265625]]}, {"title": "3.2 Taylor-Approximated Matching", "heading_level": null, "page_id": 4, "polygon": [[133.5, 396.75], [320.642578125, 396.75], [320.642578125, 407.21484375], [133.5, 407.21484375]]}, {"title": "3.3 Model Pool Generation", "heading_level": null, "page_id": 6, "polygon": [[133.5, 230.25], [278.25, 230.25], [278.25, 240.15234375], [133.5, 240.15234375]]}, {"title": "8 <PERSON> et al.", "heading_level": null, "page_id": 7, "polygon": [[133.5, 92.25], [204.099609375, 92.25], [204.099609375, 101.9970703125], [133.5, 101.9970703125]]}, {"title": "3.4 Algorithm Summary", "heading_level": null, "page_id": 7, "polygon": [[133.5, 527.25], [264.0, 527.25], [264.0, 536.765625], [133.5, 536.765625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 8, "polygon": [[133.5, 330.75], [228.90234375, 330.75], [228.90234375, 342.6328125], [133.5, 342.6328125]]}, {"title": "4.1 Experiment Setting", "heading_level": null, "page_id": 8, "polygon": [[133.5, 483.75], [259.5, 483.75], [259.5, 494.61328125], [133.5, 494.61328125]]}, {"title": "4.2 Results on Baselines", "heading_level": null, "page_id": 10, "polygon": [[133.5, 156.0], [264.3134765625, 156.0], [264.3134765625, 166.095703125], [133.5, 166.095703125]]}, {"title": "4.3 Results on Cross-Architecture Generalization", "heading_level": null, "page_id": 10, "polygon": [[133.5, 390.75], [390.26953125, 390.75], [390.26953125, 401.02734375], [133.5, 401.02734375]]}, {"title": "4.4 Ablation Study", "heading_level": null, "page_id": 10, "polygon": [[133.5, 626.25], [238.5, 626.25], [238.5, 636.15234375], [133.5, 636.15234375]]}, {"title": "4.5 Efficiency Evaluation", "heading_level": null, "page_id": 12, "polygon": [[133.5, 328.5], [267.0, 328.5], [267.0, 338.958984375], [133.5, 338.958984375]]}, {"title": "4.6 Continual Learning", "heading_level": null, "page_id": 12, "polygon": [[133.5, 481.5], [258.0, 481.5], [258.0, 491.90625], [133.5, 491.90625]]}, {"title": "4.7 Visualization", "heading_level": null, "page_id": 13, "polygon": [[133.5, 259.5], [225.75, 259.5], [225.75, 269.54296875], [133.5, 269.54296875]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 13, "polygon": [[133.5, 375.0], [220.53515625, 375.0], [220.53515625, 386.138671875], [133.5, 386.138671875]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 13, "polygon": [[133.5, 591.75], [246.75, 591.75], [246.75, 602.89453125], [133.5, 602.89453125]]}, {"title": "References", "heading_level": null, "page_id": 14, "polygon": [[133.5, 117.0], [198.0, 117.0], [198.0, 128.197265625], [133.5, 128.197265625]]}, {"title": "A Comprehensive Theoretical Analysis", "heading_level": null, "page_id": 17, "polygon": [[133.5, 116.25], [373.5, 116.25], [373.5, 128.390625], [133.5, 128.390625]]}, {"title": "B Error Analysis for Taylor Approximation", "heading_level": null, "page_id": 21, "polygon": [[133.5, 290.25], [399.75, 290.25], [399.75, 302.4140625], [133.5, 302.4140625]]}, {"title": "C More Experimental Results", "heading_level": null, "page_id": 22, "polygon": [[133.5, 426.0], [320.34375, 424.5], [320.34375, 437.37890625], [133.5, 437.37890625]]}, {"title": "C.1 Experimental Results on Imbalanced Dataset", "heading_level": null, "page_id": 22, "polygon": [[133.5, 451.5], [391.5, 451.5], [391.5, 462.12890625], [133.5, 462.12890625]]}, {"title": "C.2 Experimental Results on Larger Network", "heading_level": null, "page_id": 22, "polygon": [[133.5, 610.5], [371.25, 610.5], [371.25, 620.68359375], [133.5, 620.68359375]]}, {"title": "C.3 Experimental Results on Other Task", "heading_level": null, "page_id": 23, "polygon": [[133.27734375, 158.25], [347.25, 158.25], [347.25, 168.5126953125], [133.27734375, 168.5126953125]]}, {"title": "C.4 Experimental Results on Small Dataset", "heading_level": null, "page_id": 23, "polygon": [[133.5, 277.5], [361.5, 277.5], [361.5, 287.71875], [133.5, 287.71875]]}, {"title": "D More Implementation Details", "heading_level": null, "page_id": 23, "polygon": [[133.5, 433.125], [333.0, 433.125], [333.0, 444.7265625], [133.5, 444.7265625]]}, {"title": "D.1 Model Pool Generation", "heading_level": null, "page_id": 23, "polygon": [[133.5, 456.71484375], [282.09375, 456.71484375], [282.09375, 466.76953125], [133.5, 466.76953125]]}, {"title": "D.2 Data Generation", "heading_level": null, "page_id": 24, "polygon": [[133.5, 264.75], [249.0, 264.75], [249.0, 275.537109375], [133.5, 275.537109375]]}, {"title": "D.3 Validation", "heading_level": null, "page_id": 24, "polygon": [[133.4267578125, 624.75], [216.0, 624.75], [216.0, 635.765625], [133.4267578125, 635.765625]]}, {"title": "26 <PERSON> et al.", "heading_level": null, "page_id": 25, "polygon": [[133.5, 92.25], [204.99609375, 92.25], [204.99609375, 101.900390625], [133.5, 101.900390625]]}, {"title": "E More Visualization Results", "heading_level": null, "page_id": 25, "polygon": [[133.5, 538.5], [317.056640625, 538.5], [317.056640625, 550.6875], [133.5, 550.6875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 40], ["Text", 6], ["SectionHeader", 2], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 57], ["Text", 4], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 835, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 44], ["ListItem", 4], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 285], ["Line", 41], ["Text", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 299], ["Line", 82], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["TextInlineMath", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2259, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 33], ["Text", 5], ["Equation", 3], ["TextInlineMath", 3], ["Reference", 3]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 6565, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 45], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 41], ["TextInlineMath", 3], ["SectionHeader", 2], ["Text", 2], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 40], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 447], ["TableCell", 110], ["Line", 46], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 42], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 98], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1466, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 42], ["Text", 5], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 36], ["SectionHeader", 3], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 649, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 50], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 51], ["ListItem", 19], ["Reference", 18], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 25], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 290], ["Line", 29], ["Equation", 4], ["Text", 3], ["TextInlineMath", 3], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8537, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 210], ["Line", 31], ["Equation", 4], ["TextInlineMath", 3], ["Reference", 3], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1047, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 397], ["Line", 20], ["TextInlineMath", 2], ["Equation", 2], ["Reference", 2], ["Text", 1], ["Caption", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 10177, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 26], ["Text", 4], ["Equation", 3], ["Reference", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 10527, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 28], ["Text", 8], ["Equation", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9445, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 52], ["TableCell", 41], ["Text", 3], ["SectionHeader", 3], ["Reference", 3], ["Caption", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 9937, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 40], ["Text", 6], ["SectionHeader", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["TableCell", 197], ["Line", 46], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["Caption", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 6017, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 159], ["Span", 112], ["Line", 41], ["SectionHeader", 2], ["Caption", 2], ["Text", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2984, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 80], ["Line", 14], ["Picture", 8], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 10, "llm_error_count": 0, "llm_tokens_used": 6016, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Teddy__Efficient_Large-Scale_Dataset_Distillation_via_Taylor-Approximated_Matching"}