{"table_of_contents": [{"title": "Textual Dataset Distillation via Language Model Embedding", "heading_level": null, "page_id": 0, "polygon": [[111.09319899244332, 79.47640249332146], [484.15617128463475, 78.72662511130899], [484.15617128463475, 92.607666015625], [111.09319899244332, 92.607666015625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[156.857421875, 213.68655387355298], [203.7109375, 213.68655387355298], [203.7109375, 225.50634765625], [156.857421875, 225.50634765625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[69.80856423173803, 509.84861976847725], [155.38035264483625, 509.84861976847725], [155.38035264483625, 522.138671875], [69.80856423173803, 522.138671875]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 2, "polygon": [[69.05793450881612, 72.72840605520926], [165.13853904282115, 72.72840605520926], [165.13853904282115, 83.8197021484375], [69.05793450881612, 83.8197021484375]]}, {"title": "2.1 Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[69.05793450881612, 93.72217275155832], [185.376953125, 93.72217275155832], [185.376953125, 105.147216796875], [69.05793450881612, 105.147216796875]]}, {"title": "2.2 Core-Set selection", "heading_level": null, "page_id": 2, "polygon": [[69.05793450881612, 542.0890471950133], [179.556640625, 542.0890471950133], [179.556640625, 552.9736328125], [69.05793450881612, 552.9736328125]]}, {"title": "2.3 Vec2Text", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 95.97150489759572], [373.81360201511336, 95.97150489759572], [373.81360201511336, 107.10009765625], [304.7556675062972, 107.10009765625]]}, {"title": "3 Methodology", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 532.3419412288513], [391.8287153652393, 532.3419412288513], [391.8287153652393, 543.1064453125], [304.7556675062972, 543.1064453125]]}, {"title": "3.1 Problem Statement", "heading_level": null, "page_id": 2, "polygon": [[304.7556675062972, 554.0854853072128], [422.60453400503775, 554.0854853072128], [422.60453400503775, 565.71875], [304.7556675062972, 565.71875]]}, {"title": "3.2 Proposed Method", "heading_level": null, "page_id": 3, "polygon": [[68.3073047858942, 470.86019590382904], [178.6498740554156, 470.86019590382904], [178.6498740554156, 483.0810546875], [68.3073047858942, 483.0810546875]]}, {"title": "4 Experiments Configuration", "heading_level": null, "page_id": 4, "polygon": [[69.05793450881612, 380.2978515625], [230.44332493702768, 380.2978515625], [230.44332493702768, 392.220703125], [69.05793450881612, 392.220703125]]}, {"title": "5 Results and Discussion", "heading_level": null, "page_id": 5, "polygon": [[69.05793450881612, 644.8085485307213], [206.42317380352642, 644.8085485307213], [206.42317380352642, 656.990234375], [69.05793450881612, 656.990234375]]}, {"title": "5.1 Comparison between data augmentation\nand data distillation", "heading_level": null, "page_id": 5, "polygon": [[304.7556675062972, 416.126447016919], [520.9370277078085, 416.126447016919], [520.9370277078085, 440.734375], [304.7556675062972, 440.734375]]}, {"title": "5.2 Effect of Distillation Ratio on recovered\naccuracy", "heading_level": null, "page_id": 6, "polygon": [[69.05793450881612, 73.181640625], [280.7355163727959, 73.181640625], [280.7355163727959, 97.02734375], [69.05793450881612, 97.02734375]]}, {"title": "5.3 Effect of embedding model", "heading_level": null, "page_id": 6, "polygon": [[69.05793450881612, 301.3603515625], [221.4357682619647, 301.3603515625], [221.4357682619647, 313.283203125], [69.05793450881612, 313.283203125]]}, {"title": "5.4 Is task-specific prompt necessary?", "heading_level": null, "page_id": 6, "polygon": [[69.05793450881612, 703.037109375], [255.6572265625, 703.037109375], [255.6572265625, 714.548828125], [69.05793450881612, 714.548828125]]}, {"title": "5.5 Is vec2text necessary?", "heading_level": null, "page_id": 6, "polygon": [[304.7556675062972, 323.150390625], [433.1133501259446, 323.150390625], [433.1133501259446, 334.662109375], [304.7556675062972, 334.662109375]]}, {"title": "5.6 Effect of the vec2text model's quality", "heading_level": null, "page_id": 6, "polygon": [[304.7556675062972, 527.093499554764], [504.4231738035264, 527.093499554764], [504.4231738035264, 538.9951171875], [304.7556675062972, 538.9951171875]]}, {"title": "5.7 <PERSON><PERSON><PERSON><PERSON> with LoRA", "heading_level": null, "page_id": 7, "polygon": [[69.05793450881612, 393.63312555654494], [207.92443324937028, 393.63312555654494], [207.92443324937028, 405.17138671875], [69.05793450881612, 405.17138671875]]}, {"title": "5.8 Cost of Generating Embedding Vectors", "heading_level": null, "page_id": 7, "polygon": [[304.7556675062972, 73.47818343722173], [514.9319899244332, 73.47818343722173], [514.9319899244332, 84.2822265625], [304.7556675062972, 84.2822265625]]}, {"title": "6 Limitation", "heading_level": null, "page_id": 7, "polygon": [[304.7556675062972, 185.19501335707923], [379.775390625, 185.19501335707923], [379.775390625, 197.857666015625], [304.7556675062972, 197.857666015625]]}, {"title": "7 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[68.3073047858942, 71.9786286731968], [147.1234256926952, 71.9786286731968], [147.1234256926952, 84.5391845703125], [68.3073047858942, 84.5391845703125]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[69.05793450881612, 251.81884765625], [127.60705289672543, 251.81884765625], [127.60705289672543, 263.33056640625], [69.05793450881612, 263.33056640625]]}, {"title": "A Architecture of Downstream Models", "heading_level": null, "page_id": 10, "polygon": [[69.05793450881612, 71.9786286731968], [279.2342569269521, 71.9786286731968], [279.2342569269521, 84.48779296875], [69.05793450881612, 84.48779296875]]}, {"title": "A.1 LR, NB and SVM", "heading_level": null, "page_id": 10, "polygon": [[69.05793450881612, 98.22083704363313], [183.1536523929471, 98.22083704363313], [183.1536523929471, 110.5947265625], [69.05793450881612, 110.5947265625]]}, {"title": "A.2 TextCNN", "heading_level": null, "page_id": 10, "polygon": [[69.05793450881612, 252.67497773820125], [142.81591796875, 252.67497773820125], [142.81591796875, 264.97509765625], [69.05793450881612, 264.97509765625]]}, {"title": "A.3 TextRNN", "heading_level": null, "page_id": 10, "polygon": [[304.7556675062972, 72.7191162109375], [377.5667506297229, 72.7191162109375], [377.5667506297229, 84.4364013671875], [304.7556675062972, 84.4364013671875]]}, {"title": "A.4 distilBERT", "heading_level": null, "page_id": 10, "polygon": [[304.7556675062972, 596.96484375], [387.341796875, 596.96484375], [387.341796875, 608.4765625], [304.7556675062972, 608.4765625]]}, {"title": "A.5 T5-Base", "heading_level": null, "page_id": 11, "polygon": [[69.05793450881612, 71.4857177734375], [137.3652392947103, 71.4857177734375], [137.3652392947103, 84.6419677734375], [69.05793450881612, 84.6419677734375]]}, {"title": "B Implementation Detail", "heading_level": null, "page_id": 11, "polygon": [[68.3073047858942, 180.384521484375], [208.2216796875, 180.384521484375], [208.2216796875, 193.746337890625], [68.3073047858942, 193.746337890625]]}, {"title": "B.1 Training Hyper-parameters", "heading_level": null, "page_id": 11, "polygon": [[68.3073047858942, 201.968994140625], [228.3017578125, 201.968994140625], [228.3017578125, 214.81689453125], [68.3073047858942, 214.81689453125]]}, {"title": "B.2 K-centroid Sampling", "heading_level": null, "page_id": 11, "polygon": [[68.3073047858942, 404.1435546875], [196.8720703125, 404.1435546875], [196.8720703125, 417.2998046875], [68.3073047858942, 417.2998046875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 222], ["Line", 100], ["Text", 6], ["SectionHeader", 3], ["<PERSON>Footer", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7870, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 102], ["Text", 7], ["ListItem", 3], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 101], ["SectionHeader", 6], ["Text", 6], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 78], ["Text", 7], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 688, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 102], ["Text", 11], ["Footnote", 5], ["Reference", 5], ["SectionHeader", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 255], ["Span", 242], ["Line", 90], ["Text", 4], ["ListItem", 3], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 12657, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 292], ["Line", 104], ["TableCell", 59], ["Text", 7], ["SectionHeader", 5], ["Table", 2], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5183, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 122], ["TableCell", 66], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["Caption", 2], ["Figure", 1], ["Table", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2894, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 105], ["ListItem", 24], ["Reference", 24], ["SectionHeader", 2], ["ListGroup", 2], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 71], ["ListItem", 18], ["Reference", 18], ["ListGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 527], ["Line", 120], ["SectionHeader", 5], ["Code", 5], ["Text", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 37], ["SectionHeader", 4], ["Text", 3], ["Reference", 3], ["Code", 2], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Line", 134], ["Span", 5], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1514, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Textual Dataset Distillation via Language Model Embedding"}