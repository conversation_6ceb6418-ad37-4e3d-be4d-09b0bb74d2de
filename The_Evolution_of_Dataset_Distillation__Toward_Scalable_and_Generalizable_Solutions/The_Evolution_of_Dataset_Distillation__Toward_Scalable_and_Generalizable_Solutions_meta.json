{"table_of_contents": [{"title": "The Evolution of Dataset Distillation: Toward\nScalable and Generalizable Solutions", "heading_level": null, "page_id": 0, "polygon": [[70.5, 56.1708984375], [540.87890625, 54.75], [540.87890625, 106.154296875], [70.5, 106.154296875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 330.0], [140.748046875, 330.0], [140.748046875, 341.47265625], [48.0, 341.47265625]]}, {"title": "2 FUNDAMENTAL DATASET DISTILLATION METH-\nODS", "heading_level": null, "page_id": 1, "polygon": [[46.5, 587.25], [300.0, 587.25], [300.0, 611.015625], [46.5, 611.015625]]}, {"title": "2.1 Matching Based Approaches", "heading_level": null, "page_id": 1, "polygon": [[310.78125, 662.0625], [465.0, 662.0625], [465.0, 671.34375], [310.78125, 671.34375]]}, {"title": "2.1.1 Gradient Matching", "heading_level": null, "page_id": 1, "polygon": [[311.25, 675.75], [421.646484375, 675.75], [421.646484375, 686.0390625], [311.25, 686.0390625]]}, {"title": "2.1.2 Trajectory Matching", "heading_level": null, "page_id": 2, "polygon": [[46.5, 583.5], [162.0, 583.5], [162.0, 594.0], [46.5, 594.0]]}, {"title": "2.1.3 Distribution Matching", "heading_level": null, "page_id": 3, "polygon": [[47.25, 519.0], [168.75, 519.0], [168.75, 528.64453125], [47.25, 528.64453125]]}, {"title": "2.1.4 Latent and Frequency Space Methods", "heading_level": null, "page_id": 4, "polygon": [[46.5, 267.75], [240.75, 267.75], [240.75, 277.857421875], [46.5, 277.857421875]]}, {"title": "2.1.5 Plug-and-Play Approaches", "heading_level": null, "page_id": 4, "polygon": [[311.080078125, 159.0], [456.75, 159.0], [456.75, 169.5], [311.080078125, 169.5]]}, {"title": "2.2 Scalable Dataset Distillation Methods", "heading_level": null, "page_id": 5, "polygon": [[46.5, 425.25], [239.25, 425.25], [239.25, 434.671875], [46.5, 434.671875]]}, {"title": "2.2.1 Dataset Distillation via Generative Models", "heading_level": null, "page_id": 5, "polygon": [[47.25, 586.5], [255.0, 586.5], [255.0, 596.70703125], [47.25, 596.70703125]]}, {"title": "2.2.1.1 GAN-based Methods", "heading_level": null, "page_id": 5, "polygon": [[46.5, 684.0], [177.75, 684.0], [177.75, 693.7734375], [46.5, 693.7734375]]}, {"title": "2.2.1.2 Diffusion-based Methods", "heading_level": null, "page_id": 5, "polygon": [[311.25, 359.25], [460.5, 359.25], [460.5, 369.509765625], [311.25, 369.509765625]]}, {"title": "2.2.2 Decoupling Optimization: SRe2L Series", "heading_level": null, "page_id": 6, "polygon": [[46.5, 630.0], [246.75, 630.0], [246.75, 639.6328125], [46.5, 639.6328125]]}, {"title": "2.2.3 Soft Labels", "heading_level": null, "page_id": 7, "polygon": [[46.5, 285.0], [127.5, 285.0], [127.5, 294.486328125], [46.5, 294.486328125]]}, {"title": "2.3 Efficiency and Effectiveness in Dataset Distillation", "heading_level": null, "page_id": 7, "polygon": [[310.5, 333.544921875], [563.25, 333.544921875], [563.25, 343.599609375], [310.5, 343.599609375]]}, {"title": "2.3.1 Selective Dataset Distillation", "heading_level": null, "page_id": 7, "polygon": [[311.25, 426.0], [463.5, 426.0], [463.5, 436.21875], [311.25, 436.21875]]}, {"title": "2.3.2 Lossless Distillation", "heading_level": null, "page_id": 8, "polygon": [[310.482421875, 388.072265625], [427.025390625, 388.072265625], [427.025390625, 398.3203125], [310.482421875, 398.3203125]]}, {"title": "2.3.3 Diversity of Distilled Data", "heading_level": null, "page_id": 9, "polygon": [[46.5, 138.0], [186.0, 138.0], [186.0, 148.5966796875], [46.5, 148.5966796875]]}, {"title": "2.3.4 Augmentation Strategies", "heading_level": null, "page_id": 9, "polygon": [[46.5, 525.0], [183.0, 525.0], [183.0, 534.4453125], [46.5, 534.4453125]]}, {"title": "2.3.5 Extreme Compression Techniques", "heading_level": null, "page_id": 9, "polygon": [[311.080078125, 421.91015625], [487.5, 421.91015625], [487.5, 431.96484375], [311.080078125, 431.96484375]]}, {"title": "2.4 Distillation in Non-IID and Non-centralized Settings", "heading_level": null, "page_id": 9, "polygon": [[310.5, 602.25], [564.75, 602.25], [564.75, 612.17578125], [310.5, 612.17578125]]}, {"title": "2.4.1 Addressing Non-IID Challenges", "heading_level": null, "page_id": 10, "polygon": [[46.5, 43.5], [213.0, 43.5], [213.0, 53.75390625], [46.5, 53.75390625]]}, {"title": "2.4.2 Addressing Non-centralized Challenges", "heading_level": null, "page_id": 10, "polygon": [[311.25, 43.5], [510.75, 43.5], [510.75, 53.75390625], [311.25, 53.75390625]]}, {"title": "2.5 Robustness in Dataset Distillation", "heading_level": null, "page_id": 11, "polygon": [[45.75, 416.8828125], [224.71875, 416.8828125], [224.71875, 426.1640625], [45.75, 426.1640625]]}, {"title": "2.5.1 Adversarial Attack", "heading_level": null, "page_id": 11, "polygon": [[46.5, 583.5], [156.0, 583.5], [156.0, 592.83984375], [46.5, 592.83984375]]}, {"title": "2.5.2 Backdoor Attack", "heading_level": null, "page_id": 11, "polygon": [[311.25, 306.75], [412.5, 306.75], [412.5, 317.49609375], [311.25, 317.49609375]]}, {"title": "2.5.3 Beyond Adversarial and Backdoor Attacks", "heading_level": null, "page_id": 11, "polygon": [[311.25, 630.73828125], [519.75, 630.73828125], [519.75, 640.01953125], [311.25, 640.01953125]]}, {"title": "2.6 Model-agnostic Solutions", "heading_level": null, "page_id": 12, "polygon": [[45.75, 95.90625], [187.5, 95.90625], [187.5, 105.9609375], [45.75, 105.9609375]]}, {"title": "3 EMERGING APPLICATIONS AND DOMAINS", "heading_level": null, "page_id": 12, "polygon": [[311.25, 94.**********], [534.0, 94.**********], [534.0, 105.**********], [311.25, 105.**********]]}, {"title": "3.1 Temporal Domain", "heading_level": null, "page_id": 12, "polygon": [[310.5, 230.25], [415.5, 230.25], [415.5, 240.15234375], [310.5, 240.15234375]]}, {"title": "3.2 Multi-modal Dataset Distillation", "heading_level": null, "page_id": 12, "polygon": [[311.25, 617.25], [477.0, 617.25], [477.0, 627.2578125], [311.25, 627.2578125]]}, {"title": "3.3 Medical Domain", "heading_level": null, "page_id": 13, "polygon": [[45.75, 223.5], [143.25, 223.5], [143.25, 233.578125], [45.75, 233.578125]]}, {"title": "3.4 Other Applications", "heading_level": null, "page_id": 13, "polygon": [[45.75, 477.75], [156.0, 477.75], [156.0, 487.65234375], [45.75, 487.65234375]]}, {"title": "4 PERFORMANCE COMPARISON", "heading_level": null, "page_id": 13, "polygon": [[311.25, 372.75], [476.25, 372.75], [476.25, 384.205078125], [311.25, 384.205078125]]}, {"title": "4.1 Impact of IPC Settings and Practical Considera-\ntions", "heading_level": null, "page_id": 14, "polygon": [[46.5, 43.5], [300.75, 43.5], [300.75, 63.**********], [46.5, 63.**********]]}, {"title": "4.2 Performance Analysis Across Dataset Scales and\nComplexities", "heading_level": null, "page_id": 14, "polygon": [[46.5, 522.0], [300.0, 522.75], [300.0, 543.7265625], [46.5, 543.7265625]]}, {"title": "4.3 Performance and Scalability Across Techniques", "heading_level": null, "page_id": 14, "polygon": [[311.25, 431.25], [552.0, 431.25], [552.0, 441.24609375], [311.25, 441.24609375]]}, {"title": "5 CHALLENGES AND FUTURE DIRECTIONS", "heading_level": null, "page_id": 15, "polygon": [[311.25, 691.5], [529.5, 691.5], [529.5, 701.89453125], [311.25, 701.89453125]]}, {"title": "5.1 Challenges", "heading_level": null, "page_id": 16, "polygon": [[45.75, 43.5], [122.25, 43.5], [122.25, 54.09228515625], [45.75, 54.09228515625]]}, {"title": "5.2 Future Directions", "heading_level": null, "page_id": 16, "polygon": [[46.5, 641.25], [149.4140625, 641.25], [149.4140625, 650.84765625], [46.5, 650.84765625]]}, {"title": "6 CONCLUSION", "heading_level": null, "page_id": 16, "polygon": [[311.25, 534.4453125], [396.84375, 534.4453125], [396.84375, 544.5], [311.25, 544.5]]}, {"title": "ACKNOWLEDGMENT", "heading_level": null, "page_id": 17, "polygon": [[47.25, 143.25], [147.0, 143.25], [147.0, 153.4306640625], [47.25, 153.4306640625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 17, "polygon": [[47.25, 189.0], [116.25, 189.0], [116.25, 198.580078125], [47.25, 198.580078125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 90], ["Text", 9], ["Footnote", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9476, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 468], ["Line", 160], ["Text", 10], ["SectionHeader", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 384], ["Line", 118], ["TableCell", 35], ["Text", 8], ["Caption", 2], ["Equation", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["Figure", 1], ["TextInlineMath", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 728, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 112], ["Text", 10], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 687, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 339], ["Line", 131], ["Text", 9], ["SectionHeader", 2], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 241], ["Line", 117], ["Text", 12], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 128], ["Text", 6], ["Equation", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 668, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 351], ["Line", 125], ["Text", 10], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 264], ["Line", 122], ["Text", 11], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 115], ["Text", 10], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 284], ["Line", 120], ["Text", 9], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 108], ["Text", 10], ["SectionHeader", 4], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 798, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 255], ["Line", 116], ["Text", 11], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 270], ["Line", 114], ["Text", 7], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 118], ["Text", 11], ["SectionHeader", 3], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 2738], ["TableCell", 1456], ["Line", 109], ["Text", 5], ["Table", 2], ["Reference", 2], ["Caption", 1], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3633, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 121], ["Text", 13], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 569], ["Line", 151], ["Reference", 52], ["ListItem", 51], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 555], ["Line", 152], ["ListItem", 53], ["Reference", 53], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 572], ["Line", 151], ["ListItem", 53], ["Reference", 53], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 72], ["ListItem", 22], ["Reference", 22], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/The_Evolution_of_Dataset_Distillation__Toward_Scalable_and_Generalizable_Solutions"}