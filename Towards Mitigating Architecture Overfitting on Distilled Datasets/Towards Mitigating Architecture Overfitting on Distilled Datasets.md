# Towards Mitigating Architecture Overfitting on Distilled Datasets

<PERSON><PERSON>, <PERSON><sup>∗</sup>

Department of Computer Science, City University of Hong Kong, Hong Kong SAR, China <EMAIL>, <EMAIL>

*Abstract*—Dataset distillation methods have demonstrated remarkable performance for neural networks trained with very limited training data. However, a significant challenge arises in the form of *architecture overfitting*: the distilled training dataset synthesized by a specific network architecture (i.e., training network) generates poor performance when trained by other network architectures (i.e., test networks), especially when the test networks have a larger capacity than the training network. This paper introduces a series of approaches to mitigate this issue. Among them, DropPath renders the large model to be an implicit ensemble of its sub-networks, and knowledge distillation ensures each sub-network acts similarly to the small but well-performing teacher network. These methods, characterized by their smoothing effects, significantly mitigate architecture overfitting. We conduct extensive experiments to demonstrate the effectiveness and generality of our methods. Particularly, across various scenarios involving different tasks and different sizes of distilled data, our approaches significantly mitigate architecture overfitting. Furthermore, our approaches achieve comparable or even superior performance when the test network is larger than the training network. Codes are available at [CityU-](https://github.com/CityU-MLO/mitigate_architecture_overfitting)[MLO/mitigate](https://github.com/CityU-MLO/mitigate_architecture_overfitting) architecture overfitting.

*Index Terms*—Dataset distillation. Overfitting. Efficient learning. Neural network architecture.

## I. INTRODUCTION

Deep learning has achieved tremendous success in various applications [\[1,](#page-8-0) [2\]](#page-9-0), but training a powerful deep neural network requires massive training data [\[3,](#page-9-1) [4\]](#page-9-2). To accelerate training, one possible way is to construct a new but smaller training set that preserves most of the information of the original larger set. In this regard, we can use *coreset* [\[5,](#page-9-3) [6\]](#page-9-4) to sample a subset of the original training set or *dataset distillation* [\[7,](#page-9-5) [8\]](#page-9-6) to synthesize a small training set. Compared with coreset, dataset distillation is demonstrated to achieve much better performance when the amount of data is extremely small [\[6,](#page-9-4) [9\]](#page-9-7). Furthermore, dataset distillation is shown to benefit various applications, such as continual learning [\[8,](#page-9-6) [9,](#page-9-7) [10,](#page-9-8) [11\]](#page-9-9), neural architecture search [\[8,](#page-9-6) [11\]](#page-9-9), and privacy preservation [\[12,](#page-9-10) [13\]](#page-9-11). Therefore, in this work, we focus on dataset distillation to compress the training set.

In the dataset distillation framework, the small training set, which is also called the *distilled dataset*, is learned by using a neural network, which we call *training network*, to extract the most important information from the original training set. Existing data distillation methods are based on various techniques, including meta-learning [\[7,](#page-9-5) [14,](#page-9-12) [15,](#page-9-13) [16,](#page-9-14) [17,](#page-9-15) [18\]](#page-9-16)

and data matching [\[8,](#page-9-6) [9,](#page-9-7) [11,](#page-9-9) [19,](#page-9-17) [20,](#page-9-18) [21,](#page-9-19) [22\]](#page-9-20). These methods are then evaluated by the test accuracy of another neural network, which we call *test network*, trained on the distilled dataset. In summary, in the context of dataset distillation, the training network serves as the model utilized for constructing the distilled dataset, while the test network is employed to showcase the performance achievable through the distilled dataset.

Despite efficiency, dataset distillation methods generally suffer from *architecture overfitting* [\[9,](#page-9-7) [11,](#page-9-9) [17,](#page-9-15) [18,](#page-9-16) [20\]](#page-9-18). That is, the performance of the test network trained on the distilled dataset degrades significantly when it has a different network architecture from the training network. Moreover, the performance deteriorates further when there is a larger difference between the training and test networks in terms of depth and topological structure. Specifically, due to high computational complexity and optimization challenges in dataset distillation, the training networks are usually shallow networks, such as 3-layer convolutional neural networks (CNN) as used in Zhou et al. [\[18\]](#page-9-16), Cazenavette et al. [\[20\]](#page-9-18). However, such shallow networks are rarely employed in practical applications due to their limited representation power. Consequently, we posit that the architecture overfitting seriously undermines the practicality of distilled datasets in real-world scenarios.

Our analysis in this work indicates that the performance gap between different network architectures is larger in the case of training on the distilled dataset than in the case of training on the subset of the original training set. In addition, compared with methods compressing the training set by subset selection, dataset distillation achieves better performance when using the same amount of training instances and is thus more popular in downstream applications [\[9,](#page-9-7) [11,](#page-9-9) [13\]](#page-9-11). Therefore, we mainly focus on dataset distillation, in which the effectiveness of the proposed method can be better revealed.

In this work, we demonstrate that the architecture overfitting issue on distilled datasets can be mitigated by a better architecture design and training scheme of test networks on the distilled dataset. Firstly, we combine DropPath with knowledge distillation from a small teacher network. Specifically, DropPath renders the large model to be an implicit ensemble of its sub-networks, and knowledge distillation ensures each subnetwork acts similarly to the small but well-performing teacher network. As a result, the large models could outperform small teacher models on distilled datasets. Additionally, we propose a series of approaches, including three-phase DropPath keep rate, improved shortcut connection, periodical learning rates,

<sup>∗</sup> denotes the correspondence author.

70 70 60 Test Accuracy (%) Test Accuracy (%) Test Accuracy (%) 60 50 3-layer CNN 3-layer CNN 50 ResNet18 (baseline) ResNet18 (baseline) ResNet18 (ours) ResNet18 (ours) 40 AlexNet (baseline) AlexNet (baseline) ٠A 40 AlexNet (ours) AlexNet (ours) VGG (baseline) VGG (baseline) 30 VGG (ours) VGG (ours) 30 ResNet50 (baseline) ResNet50 (baseline) ResNet50 (ours) ResNet50 (ours) 20 1 10 50 1 10 50 Images per Class Images per Class

<span id="page-1-0"></span>(a) When we use FRePo [\[18\]](#page-9-16) to construct the distilled dataset.

(b) When we use MTT [\[20\]](#page-9-18) to construct the distilled dataset.

Fig. 1. Effectiveness of our method on different architectures, different dataset distillation methods, and different images per class (IPCs) on CIFAR10. We use a 3-layer CNN as the training network, so it performs the best among various architectures in baselines (dashed lines). Our methods (solid lines) can significantly narrow down the performance gap between the 3-layer CNN and other architectures.

a better optimizer and a stronger augmentation scheme, to further boost the performance. These methods share a common characteristic of smoothing the optimization problem from different aspects. Notably, our proposed methods are also generic: we conduct comprehensive experiments on different network architectures, different numbers of instances per class (IPC), different dataset distillation methods and different datasets to demonstrate the effectiveness of our methods. Figure [1](#page-1-0) above demonstrates the performance of our proposed methods in various scenarios. It is clear that our methods greatly mitigate architecture overfitting and make large networks trained on distilled datasets achieve better performance in most cases. As a result, the utility and transferability of distilled datasets in practice is markedly enhanced even without modifying the dataset distillation algorithm. In addition to dataset distillation, our methods can also improve the performance of training on a small real dataset, including those constructed by coresets. Although some tasks, like synthetic-to-real generalization [\[23\]](#page-9-21) and few-shot learning [\[24\]](#page-9-22), are also classical problems, customizing our method for them is out of the scope of this work, because we focus on training large networks on small datasets from scratch. We leave these tasks as future works.

We summarize the contributions of this paper as follows:

- 1) We propose a series of approaches to mitigate architecture overfitting on distilled datasets. Among them, Drop-Path renders the large model to be an implicit ensemble of its sub-networks, and knowledge distillation ensures each sub-network acts similarly to the small teacher network. These methods share a common characteristic of smoothing the optimization problem. Our proposed methods are plug-and-play and applicable to different model architectures and training schemes.
- 2) We conduct extensive experiments to demonstrate that our method significantly mitigates architecture overfitting across different network architectures, different dataset distillation approaches, different numbers of instances per class (IPC), and different datasets.
- 3) Moreover, our method generally improves the perfor-

mance of deep networks trained on limited real data. As a result, large networks outperform small networks on various amounts of training data, even when there are only 100 training samples.

## II. RELATED WORKS

Dataset Distillation: The goal of dataset distillation is to learn a smaller set of training samples called *distilled dataset* that preserves essential information of the original large dataset so that models trained on this small dataset have similar performance to those trained on the original large dataset.

Existing dataset distillation approaches are based on either meta-learning or data matching [\[25\]](#page-9-23). The former category includes backpropagation through time (BPTT) approach [\[7,](#page-9-5) [14,](#page-9-12) [15\]](#page-9-13) and kernel ridge regression (KRR) approach [\[16,](#page-9-14) [17,](#page-9-15) [18,](#page-9-16) [26,](#page-9-24) [27\]](#page-9-25); the latter category includes gradient matching [\[11,](#page-9-9) [19\]](#page-9-17), trajectory matching [\[20,](#page-9-18) [21,](#page-9-19) [28,](#page-9-26) [29,](#page-9-27) [30,](#page-9-28) [31,](#page-9-29) [32,](#page-10-0) [33\]](#page-10-1), and distribution matching [\[9,](#page-9-7) [22,](#page-9-20) [34,](#page-10-2) [35,](#page-10-3) [36,](#page-10-4) [37,](#page-10-5) [38,](#page-10-6) [39\]](#page-10-7). In addition, some works [\[40,](#page-10-8) [41,](#page-10-9) [42,](#page-10-10) [43,](#page-10-11) [44,](#page-10-12) [45,](#page-10-13) [46,](#page-10-14) [47,](#page-10-15) [48\]](#page-10-16) leverage better optimization schemes to improve the performance of dataset distillation. However, these methods are shown to suffer from severe *architecture overfitting*: the significant performance degradation when the architecture of the training network and the test network are different. Recently, some factorization methods [\[49,](#page-10-17) [50,](#page-10-18) [51,](#page-10-19) [52,](#page-10-20) [53,](#page-10-21) [54,](#page-10-22) [55\]](#page-10-23), which learn synthetic datasets by optimizing their factorized features and corresponding decoders, greatly improve the crossarchitecture transferability. However, the instance per class (IPC), which indicates the size of the distilled dataset, used in these methods is much larger than that of meta-learning and data matching approaches, which greatly cancels out the advantages of dataset distillation. To better fit the motivation of dataset distillation, we only consider small IPCs (1, 10 and 50) in this work, so the factorization methods are not included for comparison.

Model Ensemble: Model ensemble aims to integrate multiple models to improve the generalization performance. Popular ensemble methods for classification models include bagging [\[56\]](#page-10-24), AdaBoost [\[57\]](#page-10-25), random forest [\[58\]](#page-10-26), random subspace [\[59\]](#page-10-27), and gradient boosting [\[60\]](#page-10-28). However, these methods require training several models and thus are computationally expensive. By contrast, DropOut [\[61\]](#page-11-0) trains the model only once but stochastically masks its intermediate feature maps during training. At each training iteration with DropOut, only part of the model parameters are updated, which forms a sub-network of the model. In this regard, DropOut enables implicit model ensembles of different sub-networks to improve the generalization performance. Similar to DropOut, DropPath [\[62\]](#page-11-1) also implicitly ensembles sub-networks but it blocks a whole layer rather than masking some feature maps. Therefore, it is applicable to network architectures with multiple branches, such as ResNet [\[63\]](#page-11-2), otherwise, the model output will be zero if a layer of a single branch network is dropped. By contrast, we propose a DropPath variant in this work which is generic, applicable to single-branch networks and effective in mitigating architecture overfitting.

Knowledge Distillation: Knowledge distillation [\[64\]](#page-11-3) aims to compress a well-trained large model (i.e., teacher model) into a smaller and more efficient model (i.e., student model) with comparable performance. The standard knowledge distillation [\[64\]](#page-11-3) is also known as offline distillation since the teacher model is fixed when training the student model. Online distillation [\[65,](#page-11-4) [66\]](#page-11-5) is proposed to further improve the performance of the student model, especially when a largecapacity high-performance teacher model is not available. In online distillation, both the teacher model and the student model are updated simultaneously. In most cases, knowledge distillation methods use large models as the teachers and small models as the students, which is based on the fact that larger models typically have better performance. However, in the context of dataset distillation, a smaller test network with the same architecture as the training network can achieve a better performance than a larger one on the distilled dataset, so we use the small model as the teacher and the large model as the student in this work. In this way, the performance of large models trained on distilled datasets can be boosted significantly.

We show in the following sections that combining DropPath and knowledge distillation, architecture overfitting on distilled datasets can be almost overcome.

## III. METHODS

In this section, we introduce the approaches that are effective in mitigating architecture overfitting on distilled datasets. Our methods are motivated by the intuition that the large model can act as an implicit ensemble of small models [\[61,](#page-11-0) [62\]](#page-11-1). First, we propose a DropPath variant, which implicitly ensemble sub-networks of models and is different from vanilla DropPath [\[62\]](#page-11-1) in that the proposed DropPath variant is also applicable to single-branch architectures. Correspondingly, we optimize the shortcut connections of ResNet-like architecture to accommodate DropPath better. Second, we use knowledge distillation [\[64\]](#page-11-3) as a form of regularization to ensure each sub-network induced by DropPath acts similarly to the teacher network. In contrast to traditional knowledge distillation approaches [\[64,](#page-11-3) [65\]](#page-11-4), the teacher model is smaller than the student model in our cases. Finally, we adopt a periodical learning rate scheduler, a gradient symbol-based optimizer [\[67\]](#page-11-6), and a stronger data augmentation scheme to improve the performance further.

## <span id="page-2-0"></span>*A. DropPath with Three-Phase Keep Rate*

Similar to DropOut [\[61\]](#page-11-0), DropPath [\[62\]](#page-11-1), a.k.a., stochastic depth, was proposed to improve generalization. While DropOut masks some entries of feature maps, DropPath randomly prunes the entire branch in a multi-branch architecture. To obtain a deterministic model for evaluation, DropPath is deactivated during inference. To ensure the expectation of the feature maps to be consistent for training and inference, we scale the output of feature maps after DropPath during training. Mathematically, DropPath works as follows:

<span id="page-2-1"></span>
$$
\text{DropPath}(x) = \frac{m}{p} \cdot x, \quad m = \text{Bernoulli}(p). \tag{1}
$$

where  $p \in [0, 1]$  denotes the keep rate,  $m = \text{Bernoulli}(p) \in$  $\{0, 1\}$  outputs 1 with probability p and 0 with probability 1−p. The scaling factor  $1/p$  is used to ensure the expectation of the feature maps remains unchanged after DropPath. The detailed derivation is in Appendix [C.](#page-12-0) Figure [2](#page-3-0) (a) illustrates how DropPath is integrated into networks. It effectively decreases the model complexity during training and can force the model to learn more generalizable representations using fewer layers. Same as DropOut, any network trained with DropPath can be regarded as an ensemble of its subnetworks [\[68\]](#page-11-7), which has been proven to improve generalization [\[56,](#page-10-24) [57,](#page-10-25) [58,](#page-10-26) [59,](#page-10-27) [60\]](#page-10-28). Note that, DropOut masks part of the feature maps and effectively decreases the network width; by contrast, DropPath removes a branch and thus decreases the effective network depth. In the context of dataset distillation, the test network is deeper than the training network, so we can decrease the effective depth of the test network by DropPath. This approach implicitly bridges the architecture disparity between the training and test networks. Consequently, we anticipate that DropPath will mitigate the problem of architecture overfitting on distilled datasets.

**Three-Phase Keep Rate:** The keep rate  $p$  is the key parameter that controls the effective depth of model architecture when using DropPath. Since the mask  $m = \text{Bernoulli}(p)$ , the effective depth gets smaller as  $p$  decreases. In the early phase of training, the model is underfitting, stochastic architecture brings optimization challenges for training the model, so we turn off DropPath by setting the keep rate  $p = 1$  in the first few epochs to ensure that the network learns meaningful representations. We then gradually decrease  $p$  to decrease the effective depth and thus to decrease the architecture disparity between the effective test network and the training network until the value of  $p$  reaches the predefined minimum value after several epochs. In the final phase of training, we decrease the architecture stochasticity by increasing the value of  $p$  to a higher value to ensure good training convergence. In the experiments, we shrink the keep rate every few epochs.

The pseudo-code is demonstrated in Algorithm [1.](#page-3-1) Unless specified, we set  $\gamma = 0.1$ ,  $p_{\min} = 0.5$ ,  $p_{\text{final}} = 0.8$ ,  $T = 500$ ,

Image /page/3/Figure/1 description: This figure illustrates four different neural network architectures: (a) Multi-branch, (b) Single-branch, (c) Original shortcut, and (d) Improved shortcut. The multi-branch architecture consists of two ConvBlocks, a DropPath, a shortcut connection, and a ReLU activation, with the outputs summed. The single-branch architecture features two ConvBlocks (one without ReLU), multiplication by 'm' and '(1-m)' respectively, a virtual shortcut, and a ReLU activation, with the outputs summed. The original shortcut shows a sequence of operations including a 1x1 convolution with 2 channels and a normalization layer, connected via a shortcut. The improved shortcut includes a 2x2 MaxPool, a 1x1 convolution with 1 channel, and a normalization layer, also connected via a shortcut. All architectures utilize summation operations indicated by a '+' symbol.

<span id="page-3-0"></span>Fig. 2. (a) The DropPath used for multi-branch residual blocks during training, it does not block the shortcut path. (b) The DropPath used for single-branch networks during training. Here,  $m = \text{Bernoulli}(p) \in \{0, 1\}$ ,  $p \in [0, 1]$  denotes the keep rate. Only when the main path is pruned  $(m = 0)$ , the virtual shortcut is activated, and vice versa. DropPath is always deactivated, i.e.,  $p = 1$ , during inference. (c) The original architecture of a shortcut connection to downsample feature maps, which consists of a  $1 \times 1$  convolution layer with the stride of 2 and a normalization layer. (d) The improved architecture of a shortcut connection to downsample feature maps, which is a sequence of a  $2 \times 2$  max pooling layer, a  $1 \times 1$  convolution layer with the stride of 1 and a normalization layer.

## <span id="page-3-1"></span>Algorithm 1 DropPath with Three-Phase Keep Rate

- 1: Input: the data:  $x$ ; current epoch index:  $i$ ; decaying factor:  $0 < \gamma < 1$ ; minimum keep rate:  $p_{\text{min}}$ ; final keep rate:  $p_{final}$ ; period of decay: T; warmup period:  $W$ ; stabilization epoch: S.
- 2: if  $i < W$  then
- 3:  $p \leftarrow 1$
- 4: else if  $i < S$  then
- 5:  $p \leftarrow max(p_{\min}, 1 \gamma \cdot \text{ceil}((i-W)/T))$  {ceil function returns the smallest integer bigger than the input}

```
6: else
```

```
7: p \leftarrow p_{\text{final}}8: end if
```

```
9: if is training then
```

```
10: m ← Bernoulli(p) {Bernoulli distribution}
11: \mathbf{y} \leftarrow \frac{m}{p} \cdot \mathbf{x}12: else
13: y \leftarrow x14: end if
```

15: Output: y

 $W = 500$ ,  $S = 3000$  in the experiments. The corresponding curve of the dynamic keep rate is shown in Figure ?? of Appendix [B.](#page-12-1)

Generalize to Single-Branch Networks: DropPath prunes the entire branch, so it is not applicable to single-branch networks, such as VGG [\[69\]](#page-11-8). This is because we need to ensure the input and the output of the network are always connected, otherwise, we will obtain a trivial constant model. By contrast, in the case of multi-branch networks such as ResNet, we prune the main path of a residual block stochastically, while the shortcut connections are always kept.

To improve the performance of single-branch networks, we propose a variant of DropPath. As illustrated in Figure [2\(](#page-3-0)b), we add a virtual shortcut connection between two layers, such

as two consecutive convolutional layers in VGG, to form a "pseudo-residual" block. This structure is similar to a real residual block, however, since we are training a single-branch architecture instead of a real ResNet, the virtual shortcut connection is only used when the main path is pruned by DropPath during training. That is to say when the main path is not pruned, the virtual shortcut connection is removed so that we are still training a single-branch network. Correspondingly, the virtual shortcut connection is discarded during inference. It should be noted that the feature is not scaled in virtual shortcut connection. The detailed derivation is also deferred to Appendix [C.](#page-12-0)

Improved Shortcut Connection: In the original ResNet [\[63\]](#page-11-2), if one residual block's input shape is the same as its output shape, the shortcut connection is just an identity function, otherwise a  $1 \times 1$  convolution layer of a stride larger than one, which may be followed by a normalization layer as shown in Figure [2\(](#page-3-0)c), is adopted in the shortcut connection to transform the input's shape to match the output's. In the latter case, the resolution of the feature maps is divided by the stride. For example, if the stride is 2, the top left entry in each  $2 \times 2$  area of the input feature map is sampled, whereas the rest 3 entities of the same area are directly dropped.

This naive subsampling strategy will cause dramatic information loss when we use DropPath. Specifically, if DropPath prunes the main path as in Figure [2](#page-3-0) (a), the shortcut connection will dominate the output of the residual block. In this regard, the naive subsampling strategy may corrupt or degrade the quality of the features, since it always picks a fixed entry of a grid. To tackle this issue, we replace the original shortcut connect with a  $2\times 2$  max pooling followed by a  $1\times 1$  convolutional layer with the stride of 1. This improved structure will preserve the most important information after pooling instead of the one from a fixed entry. Figure [2](#page-3-0) (c) and (d) show the comparison between the original and improved shortcut connections when the shapes of input and output are different.

## *B. Knowledge Distillation from Small Teacher Model*

Given sufficient training data, large models usually perform better than small models due to their larger representation capability. Knowledge distillation aims to compress a welltrained large model (i.e., teacher model) into a smaller model (i.e., student model) without compromising too much performance. The basic idea behind knowledge distillation is to distill the knowledge from a teacher model into a student model by forcing the student's predictions (or internal activations) to match those of the teacher [\[70\]](#page-11-9). Specifically, we can use Kullback-Leibler (KL) divergence  $\mathcal{L}_{KL}$  with temperature [\[64\]](#page-11-3) to match the predictions of student and teacher models. Then, we can combine the KL divergence as the regularization term in addition to the classification loss. Mathematically, the overall loss with knowledge distillation is:

<span id="page-4-2"></span>
$$
\mathcal{L}(\mathbf{y}_s, \mathbf{y}_t, y) = \alpha \cdot \tau^2 \cdot \mathcal{L}_{KL}(\mathbf{y}_s, \mathbf{y}_t) + (1 - \alpha) \cdot \mathcal{L}_{CE}(\mathbf{y}_s, y)
$$
 (2)

where  $\tau$  denotes the temperature factor, and  $\alpha \in (0,1)$  denotes the weight factor to balance the KL divergence  $\mathcal{L}_{KL}$  and the cross-entropy loss  $\mathcal{L}_{CE}$ . The output logits of the student model and teacher model are denoted by  $y_s$  and  $y_t$ , respectively.  $y$ denotes the target.

When training on distalled dataset, small models perform better than large ones, since small models are employed as the training network to construct distilled dataset. As a result, we adopt the small training network as the teacher model  $y_t$  and the large test network as the student model  $y_s$ . The computational overhead in knowledge distillation mainly arises from calculating  $y_t$ , i.e., the output of the teacher model. In this case, the computational overhead is negligible because evaluating on the small teacher model is much more efficient than on the large student model.

<span id="page-4-0"></span>

## *C. Training and Data Augmentation*

Besides aforementioned methods, we use the following methods to further improve the performance.

Periodical Learning Rate: Because of the three-phase stepwise scheduler for the keep rate  $p$ , we expect the network to jump out of the current local minima, and tries to search for a better one when  $p$  changes. Inspired by [\[71\]](#page-11-10), we use a cosine annealing curve with warmup to adjust the learning rate, and we periodically reset it when  $p$  changes. Formally, the learning rate  $\ln i$  in the *i*-th epoch is calculated as follows:

$$
\mathrm{lr}_{i} = \begin{cases} \lambda_{i} \cdot \frac{\mathrm{mod}(i,t)}{T_{\mathrm{warm}}} \cdot \mathrm{lr}_{\mathrm{max}}, & \text{if } \mathrm{mod}(i,t) \le T_{\mathrm{warm}},\\ 0.5\lambda_{i}(1+\cos(\pi\frac{\mathrm{mod}(i,t)-T_{\mathrm{warm}}}{T_{\mathrm{max}}-T_{\mathrm{warm}}})) \cdot \mathrm{lr}_{\mathrm{max}}, & \text{otherwise.} \end{cases} \tag{3}
$$

where  $T$  is the decay period of the keep rate  $p$  of DropPath, S is the stabilization epoch.  $t = T$  when  $i < S$ , otherwise  $t = S$ .  $\lambda_i = \lambda^{\lfloor \min(i, S)/T \rfloor}$  where  $\lambda$  is a base decaying factor, and  $|\cdot|$  denotes the floor function.  $\ln_{\text{max}}$  denotes the maximum learning rate,  $mod(x, y)$  denotes the remainder of  $x/y$ . The maximum iterations of the cosine annealing function and the number of warmup epochs are denoted by  $T_{\text{max}}$  and  $T_{\text{warm}}$ , respectively. Figure ?? of Appendix [B](#page-12-1) shows an example of how the learning rate changes.

Better Optimizer: Lion [\[67\]](#page-11-6) is a gradient symbol-based optimizer. It has faster convergence speed and is capable of finding better local minima for ResNets. Thus, we use Lion as the default optimizer in our experiments.

Stronger Augmentation: The data augmentation strategy used in MTT [\[20\]](#page-9-18) samples a single augmentation operation from a pool to augment the input image. However, we observe that sampling more operations will better diversify the model's inputs and thus improve the performance, especially when IPC is small. For convenience, when sampling  $k$  operations, we call this strategy  $k$ -fold augmentation. Empirically, we use 2-fold augmentation when IPC is 10 or 50 and 4-fold augmentation when IPC is 1.

In summary, our proposed methods share a common characteristic of smoothing the optimization problem that can improve generalization: (a) in terms of architecture, DropPath smooths the predictions by forming an implicit ensemble of sub-networks; (b) knowledge distillation smooths the objective function by introducing the predictions of teacher models as soft labels; (c) better optimizer is capable of finding flatter local minima; (d) stronger data augmentation smooth the loss landscape in the sample space [\[72,](#page-11-11) [73\]](#page-11-12).

## IV. EXPERIMENTS

In this section, we evaluate our method on different dataset distillation algorithms, different numbers of instances per class (IPC), different datasets and different network architectures. Our methods are shown effective in mitigating architecture overfitting in these settings and generic to improve the performance on limited real data. In addition, we plot the Hessian eigenvalues and visualize the landscape of different models to corroborate the smoothing effect of the proposed methods. Ultimately, we conduct extensive ablation studies for analysis. Implementation details are deferred to Appendix [A.](#page-11-13)

## *A. Mitigate Architecture Overfitting in Dateset Distillation*

<span id="page-4-1"></span>TABLE I EXPERIMENTAL SETTINGS. *DP* DENOTES DROPPATH WITH THREE-PHASE KEEP RATE, *KD* DENOTES KNOWLEDGE DISTILLATION. BESIDES, THE MISCELLANEOUS (MISC.) INCLUDES THE METHODS IN SECTION [III-C.](#page-4-0)

| Method          | DP | KD | Misc. |
|-----------------|----|----|-------|
| <b>Baseline</b> | x  | x  | x     |
| w/o DP&KD       | x  | x  | ✓     |
| w/o DP          | x  | ✓  | ✓     |
| w/o KD          | ✓  | x  | ✓     |
| Full            | ✓  | ✓  | ✓     |

We first evaluate our method on three representative dataset distillation (DD) algorithms, i.e., neural Feature Regression with Pooling (FRePo) [\[18\]](#page-9-16), Matching Training Trajectories (MTT) [\[20\]](#page-9-18) and Difficulty-Aligned Trajectory Matching (DATM) [\[29\]](#page-9-27). Furthermore, we test several ablations of our methods, the names and the settings of each ablation are elaborated in Table [I.](#page-4-1)

We comprehensively evaluate the performance of these methods under various settings, including different numbers

#### <span id="page-5-1"></span>TABLE II

TEST ACCURACIES OF MODELS TRAINED ON THE DISTILLED DATA OF CIFAR10 AND CIFAR100 [\[74\]](#page-11-14) WITH DIFFERENT IPCS. 3-LAYER CNN IS THE ARCHITECTURE USED FOR DATA DISTILLATION AND IS THE TEACHER MODEL OF KNOWLEDGE DISTILLATION. THE RESULTS IN THE BRACKET INDICATE THE GAPS FROM THE BASELINE PERFORMANCE OF 3-LAYER CNN. NOTE THAT FOR IPC=100/500, THE TEACHER MODEL OF RESNET50 IS RESNET18 W/O DP&KD. THE RESULTS IN BOLD ARE THE BEST RESULTS AMONG DIFFERENT SETTINGS. NOTE THAT DP AND KD ARE NOT APPLICABLE FOR 3-LAYER CNN, SO WE DO NOT HAVE THE TEST ACCURACY OF 3-LAYER CNN IN THESE SETTINGS.

<span id="page-5-0"></span>

| DD         | <b>IPC</b>   | Methods                                                           | 3-layer<br><b>CNN</b>                  | ResNet18                                                                                   | AlexNet                                                                                 | VGG11                                                                                    | ResNet50                                                                                    | DD               | <b>IPC</b>   | Methods                                                           | 3-layer<br><b>CNN</b>                            | ResNet18                                                                                    | AlexNet                                                                                     | VGG11                                                                                     | ResNet50                                                                                   |
|------------|--------------|-------------------------------------------------------------------|----------------------------------------|--------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|------------------|--------------|-------------------------------------------------------------------|--------------------------------------------------|---------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------|
|            | $\mathbf{1}$ | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 44.3<br>44.8 $(+0.5)$<br>÷.            | $34.4( -9.9)$<br>35.6(.8.7)<br>47.2 $(+2.9)$<br>$37.0(-7.3)$<br>49.3 $(+5.0)$              | $41.8$ $(-2.5)$<br>$47.4(+3.1)$<br>49.7 $(+5.4)$<br>46.0 $(+1.7)$<br>50.7 $(+6.4)$      | 44.0 $(-0.3)$<br>$41.5$ $(-2.8)$<br>$48.7 (+4.4)$<br>$41.1(-3.2)$<br>48.8 $(+4.5)$       | $25.9$ $(-18.4)$<br>$30.3$ $(-14.0)$<br>39.3(.5.0)<br>$32.5$ $(-11.8)$<br>41.5 $(-2.8)$     |                  | $\mathbf{1}$ | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 26.2<br>$26.1(-0.1)$                             | $18.7$ $(-7.5)$<br>$16.0$ $(-10.2)$<br>21.3(4.9)<br>$17.1$ $(-9.1)$<br>$24.4$ (-1.8)        | $22.9$ $(-3.3)$<br>$22.3$ $(-3.9)$<br>$23.9$ $(-2.3)$<br>$22.1$ $(-4.1)$<br>$25.3$ $(-0.9)$ | 22.6(.3.6)<br>$18.4$ $(-7.8)$<br>$21.8$ $(-4.4)$<br>$17.9$ $(-8.3)$<br>$24.0$ (-2.2)      | $13.5$ $(-12.7)$<br>$14.5( -11.7)$<br>$18.2$ $(-8.0)$<br>$14.3( -11.9)$<br>$23.7$ (-2.5)   |
| FRePo [18] | 10           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 63.0<br>$64.7$ (+1.7)<br>$\sim$        | 55.6(.7.4)<br>$61.0(-2.0)$<br>$64.0(+1.0)$<br>$63.9(+0.9)$<br>$66.6 (+3.6)$                | 59.3 $(-3.6)$<br>$62.3$ $(-0.7)$<br>$63.3 (+0.3)$<br>$63.8 (+0.8)$<br>64.8 $(+1.8)$     | $61.3$ $(-1.7)$<br>62.4(.0.6)<br>$63.6 (+0.6)$<br>$62.2$ $(-0.8)$<br>$65.4(+2.4)$        | 44.4 (-18.6)<br>$54.7$ $(-8.3)$<br>$57.7(-5.3)$<br>$54.0(-9.0)$<br>62.4(.0.6)               | FRePo [18]       | 10           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 34.4<br>40.2 $(+5.8)$<br>$\sim$<br>$\sim$        | $32.1(-2.3)$<br>$35.3 (+0.9)$<br>$39.4 (+5.0)$<br>$34.8 (+0.4)$<br>40.6 $(+6.2)$            | $33.1(-1.3)$<br>$37.9(+3.5)$<br>$39.2$ $(-4.8)$<br>$38.5 (+4.1)$<br>$39.9(+5.5)$            | $34.1(-0.3)$<br>$37.2 (+2.8)$<br>$38.9(+4.5)$<br>$36.6 (+2.2)$<br>39.4 $(+5.0)$           | $28.1(-6.3)$<br>$33.7(-0.7)$<br>$38.5 (+4.1)$<br>$35.0(+0.6)$<br>40.1 $(+5.7)$             |
|            | 50           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 70.5<br>$72.4(+1.9)$<br>$\blacksquare$ | $66.7$ $(-3.8)$<br>$73.0 (+2.5)$<br>$73.9 (+3.4)$<br>$74.5(+4.0)$<br>$74.5$ (+4.0)         | $66.8$ $(-3.7)$<br>$71.0 (+0.5)$<br>$72.1 (+1.6)$<br>$71.5(+1.0)$<br>$73.2 (+2.7)$      | $68.3$ $(-2.2)$<br>$70.9(+0.4)$<br>$72.0(+1.5)$<br>$70.1(-0.4)$<br>$72.8 (+2.3)$         | $60.5$ $(-10.0)$<br>$71.2 (+0.7)$<br>$72.9(+2.4)$<br>$70.6 (+0.1)$<br>$73.2$ $(+2.7)$       |                  | 50           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br><b>Full</b> | 42.1<br>46.2 $(+4.1)$<br>$\sim$<br>$\frac{1}{2}$ | $46.7 (+4.6)$<br>$46.8 (+4.7)$<br>$48.3 (+6.2)$<br>$47.2 (+5.1)$<br>48.5 $(+6.4)$           | $45.5(+3.4)$<br>$46.1 (+4.0)$<br>44.6 $(+2.5)$<br>47.0 $(+4.9)$<br>$46.6 (+4.5)$            | $45.5 (+3.4)$<br>$45.5(+3.4)$<br>$45.8 (+3.7)$<br>$45.0(+2.9)$<br>46.7 $(+4.6)$           | $45.8 (+3.7)$<br>$46.9(+4.8)$<br>48.7 $(+6.6)$<br>$46.1$ $(+4.0)$<br>49.1 $(+7.0)$         |
|            | 1            | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 48.3<br>$46.8$ $(-1.5)$                | $37.2$ $(-11.1)$<br>$36.9$ (-11.4)<br>41.6 $(-6.7)$<br>$35.5$ $(-12.8)$<br>47.2 $(-1.1)$   | $40.5$ $(-7.8)$<br>$43.2( -5.1)$<br>$46.7$ $(-1.6)$<br>$41.1$ $(-7.2)$<br>$47.3$ (-1.0) | 39.3(9.0)<br>$36.7$ (-11.6)<br>38.6(.9.7)<br>$34.4$ $(-13.9)$<br>44.1 $(-4.2)$           | $22.4$ $(-25.9)$<br>$24.7$ $(-23.6)$<br>$32.4$ $(-15.9)$<br>$28.5$ (-19.8)<br>43.0 $(-5.3)$ |                  | 1            | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br><b>Full</b> | 24.4<br>$25.0$ (+0.6)<br>÷.                      | $14.3( -10.1)$<br>$12.5$ $(-11.9)$<br>$13.3$ $(-11.1)$<br>$13.6$ $(-10.8)$<br>$24.9$ (+0.5) | $17.0$ $(-7.4)$<br>$20.6$ $(-3.8)$<br>$24.4(+0.0)$<br>$19.7$ $(-4.7)$<br>$25.8$ (+1.4)      | 15.6(.8.8)<br>$8.2$ $(-16.2)$<br>$10.2$ $(-14.2)$<br>$12.4$ $(-12.0)$<br>$22.1$ $(-2.3)$  | $4.6$ $(-19.8)$<br>$6.0$ $(-18.4)$<br>$8.5(-15.9)$<br>$9.3$ $(-15.1)$<br>$24.6$ (+0.2)     |
| MTT [20]   | 10           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 63.6<br>$65.0$ (+1.4)<br>$\sim$        | 48.9 (-14.7)<br>$51.3( -12.3)$<br>$61.4$ $(-2.2)$<br>$60.7$ $(-2.9)$<br>$67.4$ (+3.8)      | $56.9$ $(-6.7)$<br>$60.7$ $(-2.9)$<br>$52.7$ $(-10.9)$<br>59.2(4.4)<br>$68.3 (+4.7)$    | $52.6$ $(-11.0)$<br>$56.0$ $(-7.6)$<br>$48.8$ $(-14.8)$<br>57.6(.6.0)<br>$67.1$ $(+3.5)$ | $28.1$ $(-35.5)$<br>$39.8$ $(-23.8)$<br>49.9 (-13.7)<br>$47.5$ $(-16.1)$<br>63.8 $(+0.2)$   | MTT [20]         | 10           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 38.4<br>$38.5(+0.1)$<br>$\sim$<br>÷,             | $32.9$ $(-5.5)$<br>$32.7(-5.7)$<br>$35.0(-3.4)$<br>$34.6(-3.8)$<br>38.4 $(+0.0)$            | $33.7( -4.7)$<br>$36.0(-2.4)$<br>$38.2$ $(-0.2)$<br>$34.9$ $(-3.5)$<br>39.9 $(+1.5)$        | $28.8$ $(-9.6)$<br>33.9(4.5)<br>$35.5(-2.9)$<br>$33.2$ $(-5.2)$<br>$36.4$ (-2.0)          | $22.5$ $(-15.9)$<br>$30.6$ $(-7.8)$<br>$34.2$ $(-4.2)$<br>$32.9$ $(-5.5)$<br>38.5 $(+0.1)$ |
|            | 50           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 70.2<br>$70.5$ $(+0.3)$                | $62.3( -7.9)$<br>$68.1$ $(-2.1)$<br>$66.9( -3.3)$<br>69.8 $(-0.4)$<br>$71.0$ (+0.8)        | 67.5(.2.7)<br>69.5(.0.7)<br>$63.8$ $(-6.4)$<br>$67.2$ $(-3.0)$<br>$72.0$ (+1.8)         | $63.0$ $(-7.2)$<br>67.6(.2.6)<br>$61.2$ $(-9.0)$<br>69.0 $(-1.2)$<br>69.5 $(-1.2)$       | $53.1$ $(-17.1)$<br>66.5(.3.7)<br>$66.8$ $(-3.4)$<br>65.0(.5.2)<br>$70.0(-0.2)$             |                  | 50           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 44.5<br>46.0 $(+1.5)$                            | $43.1$ $(-1.4)$<br>$46.2$ $(+1.7)$<br>$47.2 (+2.7)$<br>$46.9(+2.4)$<br>48.9 $(+4.4)$        | $41.4( -3.1)$<br>$46.1$ $(+1.6)$<br>$47.1$ $(+2.6)$<br>$45.7$ $(+1.2)$<br>47.6 $(+3.1)$     | 39.3(.5.2)<br>44.5 $(+0.0)$<br>$45.1 (+0.6)$<br>43.4 $(-1.1)$<br>45.1 $(+0.6)$            | $38.7$ $(-5.8)$<br>$45.5(+1.0)$<br>$47.2$ $(+2.7)$<br>$46.8$ $(+2.3)$<br>49.4 $(+4.9)$     |
|            | 10           | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br>Full        | 58.2<br>$66.5 (+8.3)$                  | $50.4$ $(-7.8)$<br>$51.0$ $(-7.2)$<br>$54.9( -3.3)$<br>59.6 $(+1.4)$<br>$64.3 (+6.1)$      | $58.4(+0.2)$<br>$60.3$ $(+5.1)$<br>$65.8(+7.6)$<br>$63.5 (+5.3)$<br>$67.5 (+9.3)$       | $53.1$ $(-5.1)$<br>$57.4$ $(-0.8)$<br>$61.9(+3.7)$<br>$60.5 (+2.3)$<br>$63.4 (+5.2)$     | $28.8$ $(-29.4)$<br>$39.6(-18.6)$<br>$43.2$ $(-15.0)$<br>53.5(4.7)<br>59.8 $(+1.6)$         |                  | 10           | <b>Baseline</b><br>w/o DP&KD<br>w/o DP<br>w/o KD<br>Full          | 29.6<br>$32.4 (+2.8)$                            | $21.9( -7.7)$<br>25.5(4.1)<br>$29.7(+0.1)$<br>$29.5$ $(-0.1)$<br>33.9 $(+4.3)$              | $26.8$ $(-2.8)$<br>$32.4(+2.8)$<br>$34.6 (+5.0)$<br>$32.3 (+2.7)$<br>$35.3 (+5.7)$          | $21.2$ $(-8.4)$<br>$26.4$ $(-3.2)$<br>$31.1 (+1.5)$<br>$30.6 (+1.0)$<br>$33.1$ $(+3.5)$   | $8.7$ (-20.9)<br>$17.9( -11.7)$<br>$24.2$ $(-5.4)$<br>$28.2$ $(-1.4)$<br>$35.2$ (+5.6)     |
| DATM [29]  | 50           | <b>Baseline</b><br>w/o DP&KD<br>w/o DP<br>$w/o$ KD<br>Full        | 70.0<br>$74.5$ $(+4.5)$<br>$\sim$      | $69.2$ $(-0.8)$<br>$72.1$ $(+2.1)$<br>$73.0 (+3.0)$<br>$75.4 (+5.4)$<br>$75.7$ $(+5.7)$    | $71.5(+1.5)$<br>$73.7 (+3.7)$<br>$75.3 (+5.3)$<br>$73.8 (+3.8)$<br>$77.2$ $(+7.2)$      | $66.9( -3.1)$<br>$71.8(+1.8)$<br>$71.9(+1.9)$<br>$73.1 (+3.1)$<br>$74.7$ $(+4.7)$        | $54.4( -15.6)$<br>$70.0 (+0.0)$<br>$72.5 (+2.5)$<br>$73.3 (+3.3)$<br>$75.7$ $(+5.7)$        | <b>DATM</b> [29] | 50           | <b>Baseline</b><br>w/o DP&KD<br>w/o DP<br>w/o KD<br>Full          | 46.8<br>47.4 $(+0.6)$<br>$\sim$                  | 44.0 $(-2.8)$<br>47.6 $(+0.8)$<br>$51.3 (+4.5)$<br>49.9 $(+3.4)$<br>52.0 $(+5.2)$           | 44.7 $(-2.1)$<br>48.6 $(+1.8)$<br>$50.6 (+3.8)$<br>48.0 $(+1.2)$<br>$50.6 (+3.8)$           | $41.7$ $(-5.1)$<br>$46.5$ $(-0.3)$<br>48.8 $(+2.0)$<br>48.2 $(+1.4)$<br>$50.3$ (+3.5)     | $39.1$ $(-7.7)$<br>46.6 $(-0.2)$<br>$50.5 (+3.7)$<br>$50.7$ $(+3.9)$<br>$54.0$ (+7.2)      |
|            | 500          | <b>Baseline</b><br>w/o DP&KD<br>$w/o$ DP<br>w/o KD<br><b>Full</b> | 76.5<br>$83.3 (+6.8)$<br>$\sim$        | $82.5(+6.0)$<br>$86.0$ (+9.5)<br>$85.9(+9.4)$<br>$86.7$ $(+10.2)$<br><b>86.8</b> $(+10.3)$ | $80.1$ $(+3.6)$<br>$83.5 (+7.0)$<br>$84.4(+7.9)$<br>$84.3 (+7.8)$<br>$85.1$ (+8.6)      | $77.0 (+0.5)$<br>$82.3 (+5.8)$<br>$83.6 (+7.1)$<br>$84.2$ $(+7.7)$<br>$85.3$ (+8.8)      | $79.8(+3.3)$<br>$85.8 (+9.3)$<br>$86.5(+10.0)$<br>$87.2(+10.7)$<br>87.9 $(+11.4)$           |                  | 100          | <b>Baseline</b><br>w/o DP&KD<br>w/o DP<br>w/o KD<br>Full          | 52.5<br>53.6 $(+1.1)$                            | $55.0 (+2.5)$<br>$58.4 (+5.9)$<br>$59.3(+6.8)$<br>$60.5 (+8.0)$<br>$60.5 (+8.0)$            | $53.1 (+0.6)$<br>$55.6 (+3.1)$<br>$56.7 (+4.2)$<br>$55.8 (+3.3)$<br>56.5 $(+4.0)$           | $51.0$ $(-1.5)$<br>$55.1$ $(+2.6)$<br>$56.3$ $(+3.8)$<br>$57.2$ $(+4.7)$<br>58.0 $(+5.5)$ | $52.1$ $(-0.4)$<br>$58.9(+6.4)$<br>$59.7$ $(+7.2)$<br>$60.6 (+8.1)$<br>$60.9(+8.4)$        |

of instances per class (IPC), different datasets and different architectures of the test networks. Table [II\(a\)](#page-5-0) demonstrate the results on CIFAR10, and the results on CIFAR100 and Tiny-ImageNet are reported in Table [II\(b\)](#page-5-1) and Table [VIII,](#page-13-0) respectively. Note that, DropPath and knowledge distillation are not applicable when we use the same architecture for training and test networks, i.e., 3-layer CNN, because 1) it is too shallow for DropPath; 2) we will converge to the teacher model if we use the same model architecture for the teacher and the student models. We can observe from these results that architecture overfitting is more severe in the case of small IPCs and large architecture discrepancy between the training networks and the test networks, but both DropPath and knowledge distillation is capable of mitigating it. In addition, combining them can

(a) CIFAR10

further improve the performance and overcome architecture overfitting in many cases. For instance, when evaluating our method on distilled images of MTT (CIFAR10, IPC=10), it contributes performance gains of 18.5% and 35.7% for ResNet18 and ResNet50, respectively. We are also interested in how much performance gap between training and test networks we can close. Surprisingly, when IPC=10 and 50, the test accuracies of most network architectures surpass that of the architecture identical to the training network. Along with it, the gaps between different test networks, such as ResNet18 and ResNet50, are also narrowed down in most cases. Additionally, when the IPC reaches 500, our method can still contribute to performance gain.

(b) CIFAR100

DropPath enables an implicit ensemble of the shallow

subnetworks and thus mitigates architecture overfitting. However, each of these sub-networks may have sub-optimal performance. Knowledge distillation can address this issue by encouraging similar outputs between the teacher model and the sub-networks and thus further improves the performance. By contrast, the contribution of knowledge distillation could be marginal without DropPath due to the big difference in architecture [\[75\]](#page-11-15). Empirically, combining DropPath with knowledge distillation not only achieves the best performance, but also greatly decreases the performance difference among different test network architectures.

To better validate the effectiveness of our method, we report the standard deviations of test accuracies of CIFAR10 (FRePo) in Table [III.](#page-6-0) We calculate these standard deviations by running the experiments three times with different random seeds. It can be observed that the standard deviation generally increases as IPC decreases. The reason could be that when IPC gets smaller, there are more solutions that make the training error zero, so the performance of training becomes more sensitive to initialization. Despite this, we can still see significant improvement introduced by our methods.

<span id="page-6-0"></span>TABLE III THE AVERAGE TEST ACCURACIES OF MODELS TRAINED ON THE DISTILLED DATA OF CIFAR10 [\[74\]](#page-11-14) WITH DIFFERENT IPCS. THE NUMBER AFTER  $\pm$  DENOTES THE STANDARD DEVIATION. THESE RESULTS ARE OBTAINED THROUGH THREE REPETITIVE EXPERIMENTS WITH DIFFERENT RANDOM SEEDS. 3-LAYER CNN IS THE ARCHITECTURE USED IN DISTILLATION AND IS THE TEACHER MODEL OF KNOWLEDGE DISTILLATION.

|                 | DD   IPC   Methods   ResNet18 AlexNet VGG11 ResNet50                                                                                                                                                                                                                                                                                     |                                                                                             |  |  |
|-----------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|--|--|
|                 | w/o DP&KD   35.6 $\pm 2.5$ 47.4 $\pm 0.9$ 41.5 $\pm 1.1$ 30.3 $\pm 1.9$<br>$\begin{array}{ c c c c c c c } \hline \text{c}} & 1 & \text{w/o KD} & 37.0 & \pm1.0 & 46.0 & \pm0.6 & 41.1 & \pm1.3 & 32.5 & \pm1.4 \\ \hline \text{c}} & \text{Full} & 49.3 & \pm0.6 & 50.7 & \pm0.1 & 48.8 & \pm0.4 & 41.5 & \pm1.0 \\ \hline \end{array}$ | w/o DP $\left  47.2 \pm 0.5 \right  49.7 \pm 0.7 \right  48.7 \pm 0.6 \right  39.3 \pm 1.4$ |  |  |
| -10             | Full                                                                                                                                                                                                                                                                                                                                     | $66.2 \pm 0.5$ 64.8 $\pm 0.9$ 65.4 $\pm 0.2$ 62.4 $\pm 0.9$                                 |  |  |
| 50 <sup>1</sup> | Full                                                                                                                                                                                                                                                                                                                                     | $74.5 \pm 0.1$ 73.2 $\pm 0.3$ 72.8 $\pm 0.0$ 73.2 $\pm 0.2$                                 |  |  |

## *B. Comparison with Other Baselines*

<span id="page-6-1"></span>TABLE IV COMPARISON WITH BASELINES. P IN DROPPATH AND DROPOUT DENOTES THE KEEP RATE AND *alpha* IN MIXUP AND CUTMIX IS THE PARAMETERS  $\alpha$  AND  $\beta$  IN BETA DISTRIBUTION, WHERE  $\alpha$  AND  $\beta$  ARE THE SAME.

|                | Method Baseline |      |      |      | DropPath DropOut MixUp CutMix<br>$(p=0.5)$ $(p=0.5)$ (alpha=0.5) (alpha=0.5) Ours |      |
|----------------|-----------------|------|------|------|-----------------------------------------------------------------------------------|------|
| Test Acc. 55.6 |                 | 46.2 | 44.3 | 57.6 | 56.9                                                                              | 66.2 |

We further compare our method with some regularization methods on architectures, including DropOut [\[61\]](#page-11-0) and Drop-Path [\[62\]](#page-11-1), and data augmentation methods, including MixUp [\[76\]](#page-11-16) and CutMix [\[77\]](#page-11-17). We use 3-layer CNN as the training network and ResNet18 as the test network. We use FRePo to generate distilled dataset and set  $IPC$  to be 10. Our results are reported in Table [IV.](#page-6-1) We observe that DropPath and DropOut with a constant keep rate deteriorate the performance, compared with the DropPath variant proposed by us. In addition, MixUp and CutMix only contribute marginal performance improvement, compared with 2-fold augmentation in this work. These results further demonstrate the effectiveness of our methods to train distilled datasets.

## C. Improve the Performance of Training on Limited Real Data

In this section, we discuss the performance of our methods when training on a limited amount of real data and compare it with the case of the distilled dataset. Our methods have shown effective on the distilled dataset, we expect them to improve the performance on limited real training data as well. In this case, smaller models also tend to perform better than larger models because both can fit the training set perfectly but the latter suffers more from overfitting.

As illustrated in Figure [3,](#page-7-0) we train models on different fractions of CIFAR10 training set which are randomly sampled. The 3-layer CNN still serves as the teacher model when we use knowledge distillation. Since ResNet18 and ResNet50 exhibit the largest performance differences from the 3-layer CNN in the previous experiments, we only show the results of ResNet18 and ResNet50 here. ResNet18 and ResNet50 significantly outperform 3-layer CNN with enough training data, but they show worse generalization performance than CNN when the fraction is lower than 0.02, i.e., 1000 training instances. Under our methods, the performances of both ResNet18 and ResNet50 surpass that of 3-layer CNN even when the fraction is as small as 0.002, i.e., 100 training instances. However, the performance gain saturates or even declines when the fraction of training data exceeds 0.05. This can be attributed to the suboptimal performance of the teacher model (blue line). Nevertheless, Figure [3](#page-7-0) (d) shows that when the current teacher does not contribute to performance gain anymore, a stronger teacher can further improve the performance.

Furthermore, we observe that the performance gap of training on limited real data is much smaller than that of training on distilled images. For instance, when the fraction of training data is 0.002, which is equivalent to IPC=10, the performance gap between 3-layer CNN and ResNet50 is 4.9% when they are trained on real images. However, when we train them on distilled images of FRePo, the performance gap increases to 18.6%. As for the distilled images generated by MTT, the gap is even larger, which reaches 35.5%. Meanwhile, training on a distilled dataset results in much better performance than training on real data of the same size, which makes it popular in downstream applications. Therefore, we focus on applying our method in the context of dataset distillation, in which the effectiveness of our method can be better revealed.

## D. Smoothing Effect Induced by Proposed Methods

To corroborate the smoothing effect induced by our proposed methods, we analyze the Hessian spectrum of models trained with different ablations. It is known that the curvature in the neighborhood of model parameters is dominated by the top eigenvalues of the Hessian matrix  $\nabla^2 \mathcal{L}_{CE}(\theta)$ , where  $\mathcal{L}_{CE}(\theta)$  denotes the cross-entropy loss w.r.t model parameters  $\theta$ . In the implementation, we use the power iteration as in [\[78,](#page-11-18) [79\]](#page-11-19) to iteratively estimate the top 20 eigenvalues and the corresponding eigenvectors of the Hessian matrix.

As shown in Figure [4](#page-7-1) (a), the eigenvalues of the Hessian matrix for the ResNet18 trained with full setting and Lion optimizer are the lowest among the evaluated settings, which quantitatively indicates that the neighborhood of the minima

Image /page/7/Figure/1 description: This figure displays four plots comparing the test accuracy of different neural network architectures against the fraction of training data used. Each plot has a logarithmic x-axis representing the fraction of training data and a linear y-axis representing test accuracy in percentage. The plots are labeled (a) ResNet18 v.s. 3-layer CNN, (b) ResNet50 v.s. 3-layer CNN, (c) VGG11 v.s. 3-layer CNN, and (d) ResNet50 v.s. ResNet18. Each plot includes an inset showing a zoomed-in view of the lower-left portion of the main plot. In plot (a), the lines represent CNN, RN18, and RN18 DP+KD (CNN). In plot (b), the lines represent CNN, RN50, and RN50 DP+KD (CNN). In plot (c), the lines represent CNN, VGG, and VGG DP+KD (CNN). In plot (d), the lines represent RN18, RN50, RN50 DP+KD (CNN), and RN50 DP+KD (RN18). All plots show that test accuracy generally increases with the fraction of training data, with different models exhibiting varying performance levels.

<span id="page-7-0"></span>Fig. 3. Test accuracies obtained from training on different fractions of CIFAR10, the shadow indicates the standard deviation. We compare the test accuracies (a) between ResNet18 (RN18) and 3-layer CNN (CNN), (b) between ResNet50 (RN50) and CNN, (c) between VGG11 and 3-layer CNN (CNN), and (d) between ResNet50 (RN50) and ResNet18, respectively. The x-axis denotes the fraction of training data, *DP+KD* denotes that the network is trained with DropPath and knowledge distillation. The model enclosed in the brackets after KD represents the teacher model used. Note that we run the experiments three times with different random seeds.

Image /page/7/Figure/3 description: The image displays a collection of plots related to Hessian eigenvalues and loss landscapes. Plot (a) is a line graph titled "Hessian Eigenvalues" showing the "Value" on the y-axis (on a logarithmic scale) against "Index" on the x-axis, from 0 to 20. Five lines represent different conditions: "Full w/ L" (blue), "Full w/ A" (orange), "w/o DP&KD" (green), "w/o DP" (red), and "w/o KD" (purple). Plots (b) through (f) are 3D surface plots illustrating the "loss" as a function of two parameters, \"a1\" and \"a2\". Plot (b) is titled "Full w/ Lion", plot (c) is "Full w/ AdamW", plot (d) is "w/o DP", plot (e) is "w/o KD", and plot (f) is "w/o DP&KD". The axes for \"a1\" and \"a2\" range from -0.4 to 0.4, and the color bar indicates the loss values, generally ranging from 0 to values around 12 or 15.

<span id="page-7-1"></span>Fig. 4. Visualization of the smoothing effect induced by proposed methods. (a) Top 20 eigenvalues of Hessian matrix for ResNet18 trained with different settings, including full setting with Lion optimizer (Full w/ L), full setting with AdamW (Full w/ A), w/o DP, w/o KD and w/o DP&KD. For w/o DP, w/o KD and w/o DP&KD, Lion is adopted by default. (b)-(f) Loss landscape  $\mathcal{L}_{CE}(\theta + \alpha_1 \mathbf{v}_1 + \alpha_2 \mathbf{v}_2)$  of ResNet18 around the minima found by models with different settings, where  $v_1$  and  $v_2$  are the eigenvectors corresponding to the top two eigenvalues of Hessian matrices, respectively. Note that the training data is 100 (IPC=10) distilled images of CIFAR10 by FRePo. ResNet18 is trained with DropPath and knowledge distillation. 3-layer CNN serves as the teacher model where knowledge distillation is adopted.

found by our method has smaller curvature. Furthermore, Figure [4](#page-7-1) (b)-(f) qualitatively shows that our method induces a smoother loss landscape. Notably, DropPath, forming an implicit ensemble of sub-networks, contributes the most to loss landscape smoothing. By contrast, knowledge distillation only has a marginal effect on smoothing.

## *E. Ablation Studies*

We conduct extensive ablation studies here to validate the effectiveness of each component in our methods. In this subsection, we focus on the case of using 3-layer CNN as the training network, ResNet18 as the test network, setting IPC to 10 and generating the distilled dataset by FRePo. Note that the baseline performance of 3-layer CNN trained on the distilled data is 63.0%, its performance improves to 64.7% with better optimization and data augmentation.

DropPath: We first try different minimum keep rates in the three-phase scheduler introduced in Section [III-A.](#page-2-0) As illustrated in Figure [5](#page-8-1) (a) and (c), a lower minimum keep rate and a longer period of decay induce better performance, but both of them make the training longer. To balance performance and efficiency, we set the minimum keep rate and period of decay to 0.5 and 500, respectively. Figure [5](#page-8-1) (b) shows that different final keep rates do not significantly affect the

Image /page/8/Figure/0 description: The image displays five line graphs, each illustrating the relationship between a specific hyperparameter and test accuracy (%). The graphs are labeled (a) Minimum keep rate, (b) Final keep rate, (c) Period of decay, (d) KD weight, and (e) KD temperature. Each graph plots the test accuracy for 'ResNet (full)' against the respective hyperparameter, with 'CNN (baseline)' and 'CNN (improved)' represented by dashed horizontal lines. In graph (a), test accuracy for ResNet (full) ranges from approximately 66.8% at a minimum keep rate of 0.2 to 65.9% at 0.8, with CNN (baseline) at 63.1% and CNN (improved) at 64.7%. Graph (b) shows ResNet (full) accuracy between 66.2% and 66.5% for final keep rates from 0.6 to 1.0, while CNN (baseline) is at 63.0% and CNN (improved) at 64.7%. Graph (c) depicts ResNet (full) accuracy increasing from 63.4% at a period of decay of 100 to a peak of 66.8% at 800, then dropping to 66.0% at 1000, with CNN (baseline) at 63.0% and CNN (improved) at 64.7%. Graph (d) shows ResNet (full) accuracy rising from 63.2% at a KD weight of 0.1 to 66.5% at 0.8, with CNN (baseline) at 63.0% and CNN (improved) at 64.7%. Finally, graph (e) illustrates ResNet (full) accuracy peaking at 66.8% for KD temperatures of 1.5, 2, and 3, with values around 66.0% at 0.5 and 10, while CNN (baseline) is at 63.0% and CNN (improved) is at 64.7%.

<span id="page-8-1"></span>Fig. 5. Ablation studies on minimum keep rate, final keep rate, period of decay, weight and temperature of knowledge distillation (KD). (a) Test accuracies of different minimum keep rates. (b) Test accuracies of different keep rates at the final phase. (c) Test accuracies of different periods of decay. (d) Test accuracies of different KD weights. (e) Test accuracies of different KD temperatures. Regardless of the variation of hyperparameters, ResNet18 trained with our approach generally outperforms 3-layer CNN trained with baseline (orange dashed line) and that trained with better optimization and data augmentation (green dashed line).

<span id="page-8-2"></span>TABLE V ABLATION STUDIES ON THE HIGH KEEP RATE IN THE FINAL PHASE OF TRAINING AND IMPROVED SHORTCUT CONNECTION (SC).

| Final phase | Improved SC | Test Acc. |
|-------------|-------------|-----------|
| x           | x           | 65.2      |
|             | x           | 65.6      |
| x           |             | 65.9      |
|             |             | 66.6      |

<span id="page-8-3"></span>TABLE VI ABLATION STUDIES ABOUT OPTIMIZATION AND DATA AUGMENTATION. IF PERIODICAL LEARNING RATE (LR), LION OPTIMIZER AND STRONGER AUGMENTATION (AUG.) ARE NOT ADOPTED, WE REPLACE THEM WITH COSINE ANNEALING LEARNING RATE, ADAMW AND 1-FOLD AUGMENTATION, RESPECTIVELY.

| Periodical LR | Lion | stronger Aug. | Test Acc.   |
|---------------|------|---------------|-------------|
| ✕             | ✕    | ✕             | 61.6        |
| ✓             | ✕    | ✕             | 61.9        |
| ✓             | ✓    | ✕             | 64.8        |
| ✓             | ✓    | ✓             | <b>66.6</b> |

performance. Moreover, we verify the effectiveness of the high keep rate in the final phase of training, and the improved shortcut connection (SC) introduced in Section [III-A.](#page-2-0) The results shown in Table [V](#page-8-2) indicate that both of them contribute to the performance.

Knowledge Distillation: We also test different hyperparameters of knowledge distillation (KD). As illustrated in Figure [5](#page-8-1) (d) and (e), when weight  $\alpha$  and temperature  $\tau$ are in the range of [0.5, 0.8] and [1, 10], respectively, the performance does not vary significantly. It indicates that our method is quite robust to different hyperparameter choices.

Optimization and Data Augmentation: In Table [VI,](#page-8-3) we replace each of the optimization and data augmentation approaches with a baseline. The results indicate that each of these approaches improves performance. Among them, Lion optimizer contributes a performance improvement of 2.9%.

Impact of Augmentation when IPC=1: It should be noted that the results of IPC=1 in Table [II\(a\)](#page-5-0) are obtained with 4fold augmentation. For comparison, we also get the results with 2-fold augmentation (see in Table [VII\)](#page-8-4). Compared with Table [II\(a\),](#page-5-0) the test accuracies of *w/o DP&KD* and *w/o & KD* in Table [VII](#page-8-4) are higher, but those of *w/o DP* and *Full* are lower. Especially for ResNet50, the performance of *Full* increases by 7.7% with 4-fold augmentation. This indicates that stronger augmentation is necessary when using knowledge distillation when there are extremely limited data, and when the architecture difference between the training and test networks is big. Moreover, we observe that the contribution of 4-fold augmentation is marginal under a larger IPC, so we adopt 4-fold augmentation only when IPC=1.

<span id="page-8-4"></span>TABLE VII TEST ACCURACIES OF MODELS TRAINED ON THE DISTILLED DATA OF CIFAR10 (FREPO, IPC=1). HOWEVER, 2-FOLD AUGMENTATION IS ADOPTED HERE. EXCEPT THAT, THE OTHER SETTINGS ARE THE SAME AS TABLE [II\(](#page-5-0)A).

| <b>IPC</b> | Methods          | 3-layer<br><b>CNN</b>    | ResNet18        | AlexNet         | VGG11           | ResNet <sub>50</sub> |
|------------|------------------|--------------------------|-----------------|-----------------|-----------------|----------------------|
|            | Baseline         | 44.3                     | $34.4( -9.9)$   | 41.8 $(-2.5)$   | 44.0 $(-0.3)$   | $25.9$ $(-18.4)$     |
|            | w/o DP&KD        | 44.8 $(+0.5)$            | $41.2$ $(-3.1)$ | $45.4(+1.1)$    | $45.9(+1.6)$    | $32.8$ $(-11.5)$     |
|            | $w$ / $\circ$ DP | ٠                        | $41.0$ $(-3.3)$ | $44.5 (+0.2)$   | 47.0 $(+2.7)$   | $30.0$ $(-14.3)$     |
|            | $w$ / $\circ$ KD | ٠                        | 39.4(4.9)       | $47.1$ $(+2.8)$ | $38.9( -5.4)$   | $31.0$ $(-13.3)$     |
|            | Full             | $\overline{\phantom{a}}$ | $45.5$ $(+1.2)$ | 47.8 $(+3.5)$   | $46.7$ $(+2.4)$ | 33.8 $(-10.5)$       |

## V. CONCLUSION

This paper studies architecture overfitting when we train models on distilled datasets. To mitigate this issue, we propose a series of approaches based on the intuition that the large model can act as an implicit ensemble of small models. These methods also exhibit a smoothing effect from different aspects. Our methods are efficient and generic, and can improve the performance when training on a small real dataset directly. We believe that our work can help extend the utility of distilled datasets in more real-world scenarios. Recognizing that this work only mitigates architecture overfitting in the evaluation stage, our future work will focus on developing a more generalizable dataset distillation algorithm to address this issue in essence.

#### ACKNOWLEDGMENTS

This work is supported by the internal funds of City University of Hong Kong (No. 9610614 and No. 9229130). It is also supported by the NSFC project (No. 62306250). We also thank Shuqi Liu for her support in experiments.

## REFERENCES

<span id="page-8-0"></span>[1] R. Rombach, A. Blattmann, D. Lorenz, P. Esser, and B. Ommer, "High-resolution image synthesis with latent diffusion models," 2022.

- <span id="page-9-0"></span>[2] J. Jumper, R. Evans, A. Pritzel *et al.*, "Highly accurate protein structure prediction with alphafold," *Nature*, vol. 596, pp. 583–589, 2021. [Online]. Available: <https://doi.org/10.1038/s41586-021-03819-2>
- <span id="page-9-1"></span>[3] A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly *et al.*, "An image is worth 16x16 words: Transformers for image recognition at scale," *arXiv preprint:2010.11929*, 2020.
- <span id="page-9-2"></span>[4] T. Brown, B. Mann, N. Ryder, M. Subbiah, J. D. Kaplan, P. Dhariwal, A. Neelakantan, P. Shyam, G. Sastry, A. Askell *et al.*, "Language models are few-shot learners," *Advances in neural information processing systems*, vol. 33, pp. 1877–1901, 2020.
- <span id="page-9-3"></span>[5] C. Coleman, C. Yeh, S. Mussmann, B. Mirzasoleiman, P. Bailis, P. Liang, J. Leskovec, and M. Zaharia, "Selection via proxy: Efficient data selection for deep learning," *arXiv preprint:1906.11829*, 2019.
- <span id="page-9-4"></span>[6] M. Hwang, Y. Jeong, and W. Sung, "Data distribution search to select core-set for machine learning," in *The 9th International Conference on Smart Media and Applications*, 2020, pp. 172–176.
- <span id="page-9-5"></span>[7] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint:1811.10959*, 2018.
- <span id="page-9-6"></span>[8] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint:2006.05929*, 2020.
- <span id="page-9-7"></span>[9] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, 2023, pp. 6514–6523.
- <span id="page-9-8"></span>[10] A. Rosasco, A. Carta, A. Cossu, V. Lomonaco, and D. Bacciu, "Distilled replay: Overcoming forgetting through synthetic samples," in *Continual Semi-Supervised Learning: First International Workshop, CSSL 2021, Virtual Event, August 19–20, 2021, Revised Selected Papers*. Springer, 2022, pp. 104–117.
- <span id="page-9-9"></span>[11] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*. PMLR, 2021, pp. 12 674– 12 685.
- <span id="page-9-10"></span>[12] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Soft-label" anonymous gastric x-ray image distillation," in *2020 IEEE International Conference on Image Processing (ICIP)*. IEEE, 2020, pp. 305–309.
- <span id="page-9-11"></span>[13] J. Goetz and A. Tewari, "Federated learning via synthetic data," *arXiv preprint:2008.04489*, 2020.
- <span id="page-9-12"></span>[14] O. Bohdal, Y. Yang, and T. Hospedales, "Flexible dataset distillation: Learn labels instead of images," *arXiv preprint:2006.08572*, 2020.
- <span id="page-9-13"></span>[15] I. Sucholutsky and M. Schonlau, "Soft-label dataset distillation and text dataset distillation," in *2021 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2021, pp. 1–8.
- <span id="page-9-14"></span>[16] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2021.

- <span id="page-9-15"></span>[17] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," *Advances in Neural Information Processing Systems*, vol. 34, pp. 5186–5198, 2021.
- <span id="page-9-16"></span>[18] Y. Zhou, E. Nezhadarya, and J. Ba, "Dataset distillation using neural feature regression," *arXiv preprint:2206.00719*, 2022.
- <span id="page-9-17"></span>[19] S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon, "Dataset condensation with contrastive signals," in *International Conference on Machine Learning*. PMLR, 2022, pp. 12 352–12 364.
- <span id="page-9-18"></span>[20] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759.
- <span id="page-9-19"></span>[21] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Scaling up dataset distillation to imagenet-1k with constant memory," *arXiv preprint:2211.10586*, 2022.
- <span id="page-9-20"></span>[22] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 12 196–12 205.
- <span id="page-9-21"></span>[23] W. Chen, Z. Yu, S. De Mello, S. Liu, J. M. Alvarez, Z. Wang, and A. Anandkumar, "Contrastive syn-to-real generalization," *arXiv preprint:2104.02290*, 2021.
- <span id="page-9-22"></span>[24] A. Parnami and M. Lee, "Learning from few examples: A summary of approaches to few-shot learning," *arXiv preprint:2203.04291*, 2022.
- <span id="page-9-23"></span>[25] S. Lei and D. Tao, "A comprehensive survey to dataset distillation," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, vol. 46, no. 1, pp. 17–32, 2023.
- <span id="page-9-24"></span>[26] N. Loo, R. Hasani, A. Amini, and D. Rus, "Efficient dataset distillation using random feature approximation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-9-25"></span>[27] N. Loo, R. Hasani, M. Lechner, and D. Rus, "Dataset distillation with convexified implicit gradients," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2023, pp. 22 649–22 674.
- <span id="page-9-26"></span>[28] J. Du, Y. Jiang, V. T. F. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2023, pp. 3749–3758.
- <span id="page-9-27"></span>[29] Z. Guo, K. Wang, G. Cazenavette, H. Li, K. Zhang, and Y. You, "Towards lossless dataset distillation via difficulty-aligned trajectory matching," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024.
- <span id="page-9-28"></span>[30] J. Du, Q. Shi, and J. T. Zhou, "Sequential subset matching for dataset distillation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-9-29"></span>[31] Y. Lee and H. W. Chung, "SelMatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching," in *Pro-*

*ceedings of the International Conference on Machine Learning (ICML)*, 2024.

- <span id="page-10-0"></span>[32] D. Liu, J. Gu, H. Cao, C. Trinitis, and M. Schulz, "Dataset distillation by automatic training trajectories," in *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-10-1"></span>[33] S. Yang, S. Cheng, M. Hong, H. Fan, X. Wei, and S. Liu, "Neural spectral decomposition for dataset distillation," in *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-10-2"></span>[34] A. Sajedi, S. Khaki, E. Amjadian, L. Z. Liu, Y. A. Lawryshyn, and K. N. Plataniotis, "Datadam: Efficient dataset distillation with attention matching," in *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, October 2023, pp. 17 097–17 107.
- <span id="page-10-3"></span>[35] G. Zhao, G. Li, Y. Qin, and Y. Yu, "Improved distribution matching for dataset condensation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2023, pp. 7856–7865.
- <span id="page-10-4"></span>[36] H. Zhang, S. Li, P. Wang, and S. Zeng, Dan Ge, "M3D: Dataset condensation by minimizing maximum mean discrepancy," in *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*, 2024.
- <span id="page-10-5"></span>[37] W. Deng, W. Li, T. Ding, L. Wang, H. Zhang, K. Huang, J. Huo, and Y. Gao, "Exploiting inter-sample and interfeature relations in dataset distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024, pp. 17 057–17 066.
- <span id="page-10-6"></span>[38] H. Zhang, S. Li, F. Lin, W. Wang, Z. Qian, and S. Ge, "DANCE: Dual-view distribution alignment for dataset condensation," in *Proceedings of the International Joint Conference on Artificial Intelligence (IJCAI)*, 2024.
- <span id="page-10-7"></span>[39] H. Li, Y. Zhou, X. Gu, B. Li, and W. Wang, "Diversified" semantic distribution matching for dataset distillation," in *Proceedings of the ACM International Conference on Multimedia (MM)*, 2024.
- <span id="page-10-8"></span>[40] L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and X. Dongkuan, "Accelerating dataset distillation via model augmentation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2023, pp. 11 950– 11 959.
- <span id="page-10-9"></span>[41] Y. Liu, J. Gu, K. Wang, Z. Zhu, W. Jiang, and Y. You, "DREAM: Efficient dataset distillation by representative matching," in *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, 2023, pp. 17 314–17 324.
- <span id="page-10-10"></span>[42] Y. He, L. Xiao, and T. J. Zhou, "You Only Condense Once: Two rules for pruning condensed datasets," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-11"></span>[43] Y. Shang, Z. Yuan, and Y. Yan, "MIM4DD: Mutual information maximization for dataset distillation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-12"></span>[44] X. Chen, Y. Yang, Z. Wang, and B. Mirzasoleiman, "Data distillation can be like vodka: Distilling more times for better quality," in *Proceedings of the International*

*Conference on Learning Representations (ICLR)*, 2024.

- <span id="page-10-13"></span>[45] F. Yunzhen, V. Ramakrishna, and K. Julia, "Embarassingly simple dataset distillation," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024.
- <span id="page-10-14"></span>[46] Y. He, L. Xiao, J. Tianyi Zhou, and I. Tsang, "Multisize dataset condensation," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024.
- <span id="page-10-15"></span>[47] N. Loo, A. Maalouf, R. Hasani, M. Lechner, A. Amini, and D. Rus, "Large scale dataset distillation with domain shift," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2024.
- <span id="page-10-16"></span>[48] Y. Xu, Y.-L. Li, K. Cui, Z. Wang, C. Lu, Y.-W. Tai, and C.-K. Tang, "Distill gold from massive ores: Bilevel data pruning towards efficient dataset distillation," in *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-10-17"></span>[49] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient synthetic-data parameterization," in *International Conference on Machine Learning*. PMLR, 2022, pp. 11 102–11 118.
- <span id="page-10-18"></span>[50] Z. Deng and O. Russakovsky, "Remember the past: Distilling datasets into addressable memories for neural networks," *arXiv preprint:2206.02916*, 2022.
- <span id="page-10-19"></span>[51] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang, "Dataset distillation via factorization," *arXiv preprint:2210.16774*, 2022.
- <span id="page-10-20"></span>[52] H. B. Lee, D. B. Lee, and S. J. Hwang, "Dataset condensation with latent space knowledge factorization and sharing," *arXiv preprint:2208.10494*, 2022.
- <span id="page-10-21"></span>[53] X. Wei, A. Cao, F. Yang, and Z. Ma, "Sparse parameterization for epitomic dataset distillation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-22"></span>[54] D. Shin, S. Shin, and I.-c. Moon, "Frequency domainbased dataset distillation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-23"></span>[55] H. Zheng, J. Sun, S. Wu, B. Kailkhura, Z. Mao, C. Xiao, and A. Prakash, "Leveraging hierarchical feature sharing for efficient dataset condensation," in *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-10-24"></span>[56] L. Breiman, "Bagging predictors," *Machine learning*, vol. 24, pp. 123–140, 1996.
- <span id="page-10-25"></span>[57] T. Hastie, S. Rosset, J. Zhu, and H. Zou, "Multi-class adaboost," *Statistics and its Interface*, vol. 2, no. 3, pp. 349–360, 2009.
- <span id="page-10-26"></span>[58] L. Breiman, "Random forests," *Machine learning*, vol. 45, pp. 5–32, 2001.
- <span id="page-10-27"></span>[59] T. K. Ho, "Random decision forests," in *Proceedings of 3rd international conference on document analysis and recognition*, vol. 1. IEEE, 1995, pp. 278–282.
- <span id="page-10-28"></span>[60] J. H. Friedman, "Stochastic gradient boosting," *Computational statistics & data analysis*, vol. 38, no. 4, pp. 367–378, 2002.

- <span id="page-11-0"></span>[61] N. Srivastava, G. Hinton, A. Krizhevsky, I. Sutskever, and R. Salakhutdinov, "Dropout: a simple way to prevent neural networks from overfitting," *The journal of machine learning research*, vol. 15, no. 1, pp. 1929–1958, 2014.
- <span id="page-11-1"></span>[62] G. Larsson, M. Maire, and G. Shakhnarovich, "Fractalnet: Ultra-deep neural networks without residuals," *arXiv preprint:1605.07648*, 2016.
- <span id="page-11-2"></span>[63] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2016, pp. 770–778.
- <span id="page-11-3"></span>[64] G. Hinton, O. Vinyals, and J. Dean, "Distilling the knowledge in a neural network," *arXiv preprint:1503.02531*, 2015.
- <span id="page-11-4"></span>[65] Y. Zhang, T. Xiang, T. M. Hospedales, and H. Lu, "Deep mutual learning," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2018, pp. 4320–4328.
- <span id="page-11-5"></span>[66] D. Chen, J.-P. Mei, C. Wang, Y. Feng, and C. Chen, "Online knowledge distillation with diverse peers," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 34, no. 04, 2020, pp. 3430–3437.
- <span id="page-11-6"></span>[67] X. Chen, C. Liang, D. Huang, E. Real, K. Wang, Y. Liu, H. Pham, X. Dong, T. Luong, C.-J. Hsieh *et al.*, "Symbolic discovery of optimization algorithms," *arXiv preprint:2302.06675*, 2023.
- <span id="page-11-7"></span>[68] A. Krizhevsky, I. Sutskever, and G. E. Hinton, "Imagenet classification with deep convolutional neural networks," in *Advances in Neural Information Processing Systems*, F. Pereira, C. Burges, L. Bottou, and K. Weinberger, Eds., vol. 25. Curran Associates, Inc., 2012. [Online]. Available: [https://proceedings.neurips.cc/paper](https://proceedings.neurips.cc/paper_files/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf)\_files/paper/2012/ [file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf](https://proceedings.neurips.cc/paper_files/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf)
- <span id="page-11-8"></span>[69] K. Simonyan and A. Zisserman, "Very deep convolutional networks for large-scale image recognition," *arXiv preprint:1409.1556*, 2014.
- <span id="page-11-9"></span>[70] L. Beyer, X. Zhai, A. Royer, L. Markeeva, R. Anil, and A. Kolesnikov, "Knowledge distillation: A good teacher is patient and consistent," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 10 925–10 934.
- <span id="page-11-10"></span>[71] G. Huang, Y. Li, G. Pleiss, Z. Liu, J. E. Hopcroft, and K. Q. Weinberger, "Snapshot ensembles: Train 1, get m for free," *arXiv preprint:1704.00109*, 2017.
- <span id="page-11-11"></span>[72] C. Shorten and T. M. Khoshgoftaar, "A survey on image data augmentation for deep learning," *Journal of big data*, vol. 6, no. 1, pp. 1–48, 2019.
- <span id="page-11-12"></span>[73] S.-A. Rebuffi, S. Gowal, D. A. Calian, F. Stimberg, O. Wiles, and T. A. Mann, "Data augmentation can improve robustness," *Advances in Neural Information Processing Systems*, vol. 34, pp. 29 935–29 948, 2021.
- <span id="page-11-14"></span>[74] A. Krizhevsky, G. Hinton *et al.*, "Learning multiple layers of features from tiny images," 2009.
- <span id="page-11-15"></span>[75] S. I. Mirzadeh, M. Farajtabar, A. Li, N. Levine, A. Matsukawa, and H. Ghasemzadeh, "Improved knowledge distillation via teacher assistant," in *Proceedings of the*

*AAAI conference on artificial intelligence*, vol. 34, no. 04, 2020, pp. 5191–5198.

- <span id="page-11-16"></span>[76] H. Zhang, M. Cisse, Y. N. Dauphin, and D. Lopez-Paz, "mixup: Beyond empirical risk minimization," *arXiv preprint:1710.09412*, 2017.
- <span id="page-11-17"></span>[77] S. Yun, D. Han, S. J. Oh, S. Chun, J. Choe, and Y. Yoo, "Cutmix: Regularization strategy to train strong classifiers with localizable features," in *Proceedings of the IEEE/CVF international conference on computer vision*, 2019, pp. 6023–6032.
- <span id="page-11-18"></span>[78] Z. Yao, A. Gholami, Q. Lei, K. Keutzer, and M. W. Mahoney, "Hessian-based analysis of large batch training and robustness to adversaries," *Advances in Neural Information Processing Systems*, vol. 31, 2018.
- <span id="page-11-19"></span>[79] C. Liu, M. Salzmann, T. Lin, R. Tomioka, and S. Süsstrunk, "On the loss landscape of adversarial training: Identifying challenges and how to overcome them," *Advances in Neural Information Processing Systems*, vol. 33, pp. 21 476–21 487, 2020.
- <span id="page-11-20"></span>[80] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei, "Imagenet: A large-scale hierarchical image database," in *2009 IEEE conference on computer vision and pattern recognition*. Ieee, 2009, pp. 248–255.

## APPENDIX

<span id="page-11-13"></span>

## *A. Implementation Details*

Datasets: The training sets in the experiments are the distilled datasets of CIFAR10, CIFAR100 [\[74\]](#page-11-14) and Tiny-ImageNet [\[80\]](#page-11-20), but the test sets are their respective original test sets. To better validate the effectiveness of our method, we use the distilled images synthesized by different dataset distillation algorithms, e.g., neural Feature Regression with Pooling (FRePo) [\[18\]](#page-9-16) and Matching Training Trajectories (MTT) [\[20\]](#page-9-18). In addition, we evaluate the performance of our method in different numbers of instances per class (IPC), e.g., 1, 10 and 50. Note that MTT does not provide the final trainable learning rates in the released checkpoints, we adopt the reported initial learning rates in our baselines.

Models: The networks used to synthesize the distilled images (training networks) in FRePo and MTT are 3-layer CNN. Consistent with the hyperparameters reported in the paper, the output channels of hidden layers of the network used in FRePo are 128, 256 and 512, respectively. However, in MTT, all the output channels of hidden layers are set to 128. ResNet18, ResNet50 [\[63\]](#page-11-2), AlexNet [\[68\]](#page-11-7) and VGG11 [\[69\]](#page-11-8) are adopted in the evaluation, they are thereby called test networks. The hyperparameters of networks are the same as those set in [\[20\]](#page-9-18). Note that when training networks on distilled images of FRePo and MTT, batch and instance normalization layers are adopted in networks following the settings of [\[18,](#page-9-16) [20\]](#page-9-18), respectively.

DropPath: As shown in Algorithm [1,](#page-3-1) the decaying factor of keep rate  $\gamma = 0.1$ , minimum keep rate  $p_{min} = 0.5$ , final keep rate  $p_{final} = 0.8$ , period of decay  $T = 500$ , warmup period  $W = 50$ , stabilization epoch  $S = (1 + p_{min}/\gamma) \times T =$ 3000. The total epochs N is set to  $S + 2 \times T = 4000$ . In the improved shortcut, the pooling area depends on the stride of  $1 \times 1$  convolutional layer in the original one. e.g., if the stride of  $1 \times 1$  convolutional layer in the original shortcut is 2, we use a  $2 \times 2$  max pooling.

Knowledge distillation: As shown in Eq. [2,](#page-4-2) the temperature factor  $\tau = 1.5$ , and the weight factor  $\alpha = 0.5$ . If not specifically indicated, the default teacher model is the 3-layer CNN. Note that the teacher model is trained on the same data set as the student model.

Periodical learning rate: In Eq. [5,](#page-12-2) the maximum learning rate  $lr_{max} = 5 \times 10^{-5}$ , the base decaying factor for learning rate  $\lambda = 0.8$ . The period of the cosine function  $T_{max}$  and the number of warmup epochs  $T_{warm}$  are 1000 and 50, respectively.

**Optimizer:** Lion [\[67\]](#page-11-6) is adopted in our method, where weight decay  $\lambda_{wd} = 5 \times 10^{-3}$ , coefficient  $\beta_1 = 0.95$ , and  $\beta_2 = 0.98$ .

Augmentation: There are color jittering, cropping, cutout, flipping, scaling, and rotating in the augmentation pool, we sample more operations instead of just one as in [\[20\]](#page-9-18).

Training: For CIFAR10, the batch sizes for different IPCs are 10 (IPC=1), 100 (IPC=10) and 128 (IPC=50), respectively. For CIFAR100, the batch sizes are 100 (IPC=1), 256 (IPC=10) and 256 (IPC=50), respectively. Cross-entropy is adopted as the loss function in our experiments. Since the labels of images are learnable in FRePo, we divide them with a temperature factor  $t = 0.3$  for CIFAR10, 0.04 for CIFAR100, and 0.02 for Tiny-ImageNet, respectively.

## <span id="page-12-1"></span>*B. Supplementary Figures of Methods*

The corresponding curve of the dynamic keep rate is shown in Figure [6](#page-12-3) (a). Mathematically, the dynamic keep rate  $p$  is formulated as

$$
p = \begin{cases} \max(p_{\min}, 1 - \gamma \cdot \text{ceil}((i - W)/T)), & \text{if } i < S, \\ p_{\text{final}}, & \text{otherwise.} \end{cases}
$$
\n
$$
\tag{4}
$$

where  $\gamma \in [0, 1]$  is a decaying factor. *i*, *W*, *T* and *S* denote the current epoch, warmup period, decay period and stabilization epoch, respectively. Unless specified, we set  $\gamma = 0.1$ ,  $p_{\min} =$ 0.5,  $p_{\text{final}} = 0.8$ ,  $T = 500$ ,  $W = 500$ ,  $S = 3000$  in the experiments.

Figure [6](#page-12-3) (b) shows how the periodical learning rate changes. The learning rate  $lr$  at epoch  $i$  is defined as

<span id="page-12-2"></span>
$$
\mathrm{lr}_{i} = \begin{cases} \lambda_{i} \cdot \frac{\mathrm{mod}(i,t)}{T_{\mathrm{warm}}} \cdot \mathrm{lr}_{\mathrm{max}}, & \text{if } \mathrm{mod}(i,t) \le T_{\mathrm{warm}},\\ 0.5\lambda_{i}(1+\cos(\pi\frac{\mathrm{mod}(i,t)-T_{\mathrm{warm}}}{T_{\mathrm{max}}-T_{\mathrm{warm}}})) \cdot \mathrm{lr}_{\mathrm{max}}, & \text{otherwise.} \end{cases} \tag{5}
$$

where  $T$  is the decay period of the keep rate  $p$  of DropPath, S is the stabilization epoch.  $t = T$  when  $i < S$ , otherwise  $t = S$ .  $\lambda_i = \lambda^{\lfloor \min(i, S)/T \rfloor}$  where  $\lambda$  is a base decaying factor, and  $\lfloor \cdot \rfloor$  denotes the floor function.  $\ln_{\text{max}}$  denotes the maximum learning rate,  $mod(x, y)$  denotes the remainder of  $x/y$ . The maximum iterations of the cosine annealing function and the number of warmup epochs are denoted by  $T_{\text{max}}$  and  $T_{\text{warm}}$ , respectively. In implementation, the maximum learning rate  $lr_{max}$  = 5 × 10<sup>-5</sup>, the base decaying factor for learning rate  $\lambda = 0.8$ . The period of the cosine function  $T_{max}$  and the number of warmup epochs  $T_{warm}$  are 1000 and 50, respectively.

Image /page/12/Figure/13 description: The image displays two plots, labeled (a) and (b). Plot (a) is a line graph showing 'keep rate' on the y-axis and 'epoch' on the x-axis, ranging from 0 to 4000. The keep rate starts at 1.0 and decreases in steps to 0.5 at epoch 2500, then increases to 0.8 and remains constant until epoch 4000. Plot (b) is a line graph showing 'learning rate' on the y-axis and 'epoch' on the x-axis, also ranging from 0 to 4000. The learning rate starts at approximately 5e-5, drops sharply, then exhibits a cyclical pattern of sharp increases followed by gradual decreases, with the peaks becoming lower over time.

<span id="page-12-3"></span>Fig. 6. Supplementary figures. (a) Scheduler of three-phase keep rate. (b) Curve of periodical learning rate.

<span id="page-12-0"></span>

## *C. Effect of scaling factor* 1/p *in DropPath*

For multi-branch networks, Eq. [1](#page-2-1) shows that DropPath $(\mathbf{x}) = \frac{m}{p} \cdot \mathbf{x}$ ,  $m = \text{Bernoulli}(p)$ , where  $p \in [0, 1]$  denotes the keep rate,  $m = \text{Bernoulli}(p) \in \{0, 1\}$ outputs 1 with probability p and 0 with probability  $1 - p$ . We consider the module output  $y =$  DropPath $(x)$  in the training phase, then the expectation of  $y$  given  $x$  is  $\mathbb{E}(\mathbf{y}) = p \cdot \frac{1}{p} \cdot \mathbf{x} + (1-p) \cdot \frac{0}{p} \cdot \mathbf{x} = \mathbf{x}$ . In the test phase, DropPath is disabled, so the module output is simply  $x$  and consistent with the expectation in the training phase. If there is no scaling factor  $1/p$  in Eq. [1](#page-2-1) and  $p < 1$ , the expectation of the module outputs in the training and test phases will be different, which leads to performance degradation.

However, virtual shortcut connection is adopted in singlebranch networks, so the implementation of DropPath is different from that in multi-branch networks. In single-branch networks, the DropPath affects both main and shortcut paths. Therefore, given an input  $x$  and the output of the main path  $x'$ , if scaling factor is not considered here, the formulation of DropPath can be rewritten as DropPath $(\mathbf{x}) = m \cdot \mathbf{x}' + (1 - m) \cdot$ x,  $m = \text{Bernoulli}(p)$ . Assume that the expectations of x' and  $x$  are the same, the expectation of module output  $y$  given  $\boldsymbol{x}$  is  $\mathbb{E}(\boldsymbol{y}) = m \cdot \mathbb{E}(\boldsymbol{x}') + (1-m) \cdot \boldsymbol{x} = m \cdot \boldsymbol{x} + (1-m) \cdot \boldsymbol{x} = \boldsymbol{x},$ which is not changed by  $m$ . As a result, the scaling factor is not necessary here.

## *D. Results on Tiny-ImageNet*

The results on Tiny-ImageNet are reported in [VIII,](#page-13-0) respectively. The observations on CIFAR10 and CIFAR100 are analogous to those on and Tiny-ImageNet, which indicates that our method is effective on different datasets.

| DD              | <b>IPC</b> | Methods         | <b>CNN</b>               | ResNet18            | AlexNet         | VGG11          | ResNet50        |
|-----------------|------------|-----------------|--------------------------|---------------------|-----------------|----------------|-----------------|
|                 |            | <b>Baseline</b> | 16.6                     | $15.6(-1.0)$        | $16.5$ (-0.1)   | $16.6 (+0.0)$  | $13.4(-3.2)$    |
|                 |            | w/o DP&KD       | 17.7 $(+1.1)$            | $12.3$ $(-4.3)$     | $13.7(-2.9)$    | $14.1(-2.5)$   | $12.8$ $(-3.8)$ |
| FRePo [18]      | 1          | $w/o$ DP        |                          | $15.8$ (-0.8)       | $16.6 (+0.0)$   | $16.4$ (-0.2)  | 16.6 $(+0.0)$   |
|                 |            | w/o KD          |                          | $12.5$ $(-4.1)$     | $14.9( -1.7)$   | $13.6(-3.0)$   | $11.9( -4.7)$   |
|                 |            | <b>Full</b>     | $\overline{\phantom{a}}$ | 18.9 $(+2.3)$       | 18.5 $(+1.9)$   | 18.3 $(1.7)$   | 19.1 $(+2.5)$   |
|                 |            | <b>Baseline</b> | 24.9                     | $24.2$ (-0.7)       | $24.8$ $(-0.1)$ | $25.2 (+0.3)$  | $24.9(+0.0)$    |
|                 |            | w/o DP&KD       | $23.0$ (-1.9)            | $21.7$ (-3.2)       | $23.8$ (-1.1)   | $24.2(-0.7)$   | $23.1(-1.8)$    |
|                 | 10         | $w/o$ DP        |                          | $25.4(+0.5)$        | $25.2(+0.3)$    | $26.4(+1.5)$   | $26.9(+2.0)$    |
|                 |            | w/o KD          |                          | $21.5$ (-3.4)       | $22.4$ $(-2.5)$ | $24.0(-0.9)$   | $21.6(-3.3)$    |
|                 |            | Full            | $\frac{1}{2}$            | $26.8$ (+1.9)       | $24.9(+0.0)$    | $26.6$ (+1.7)  | $27.3 (+2.4)$   |
|                 |            | <b>Baseline</b> | 8.8                      | $6.2$ (-2.6)        | $6.7$ $(-2.1)$  | $7.3$ $(-1.5)$ | $2.7(-6.1)$     |
|                 |            | w/o DP&KD       | 9.6 $(+0.8)$             | $6.1$ $(-2.7)$      | $8.4(-0.4)$     | $7.2$ $(-1.6)$ | $3.2 (-5.6)$    |
|                 | 1          | $w/o$ DP        |                          | $6.5$ (-2.3)        | $9.1 (+0.3)$    | $7.9(-0.9)$    | $3.6(-5.2)$     |
|                 |            | w/o KD          |                          | $6.7$ (-2.1)        | $8.1 \ (-0.7)$  | $6.8$ $(-2.0)$ | $4.0( -4.8)$    |
| <b>MTT</b> [20] |            | Full            | $\overline{\phantom{0}}$ | <b>8.1</b> $(-0.7)$ | $9.2 (+0.4)$    | $8.2 \ (-0.6)$ | $8.2 \ (-0.6)$  |
|                 | 10         | <b>Baseline</b> | 19.3                     | $17.2$ (-2.1)       | 14.3 $(-5.0)$   | $15.1(-4.2)$   | 11.2(.8.1)      |
|                 |            | w/o DP&KD       | $20.1(+0.8)$             | $16.6$ $(-2.7)$     | $18.7(-0.6)$    | $16.2$ (-3.1)  | $15.2(-4.1)$    |
|                 |            | $w/o$ DP        |                          | $17.3$ $(-2.0)$     | $21.2(+1.9)$    | $19.9(+0.6)$   | $18.7(-0.6)$    |
|                 |            | w/o KD          |                          | 19.0 $(-0.3)$       | $17.7(-1.6)$    | $15.2(-4.1)$   | $17.7(-1.6)$    |
|                 |            | Full            | $\overline{\phantom{a}}$ | $22.6 (+3.3)$       | $21.6 (+2.3)$   | $20.5$ (+1.2)  | $21.8$ (+2.5)   |
|                 |            | <b>Baseline</b> | 16.0                     | $13.8(-2.2)$        | $16.0 (+0.0)$   | $14.7(-1.3)$   | $10.6$ (-5.4)   |
|                 |            | w/o DP&KD       | $18.4 (+2.4)$            | $13.7(-2.3)$        | $17.2 (+1.2)$   | $16.4 (+0.4)$  | $13.7(-2.3)$    |
|                 | 10         | $w/o$ DP        |                          | $13.8$ $(-2.2)$     | $17.8(+1.8)$    | $18.2 (+2.2)$  | $15.3(-0.7)$    |
| DATM [29]       |            | w/o KD          | $\overline{\phantom{0}}$ | $16.3 (+0.3)$       | $15.9(-0.1)$    | $17.6 (+1.6)$  | $12.7(-3.3)$    |
|                 |            | Full            | $\blacksquare$           | 17.3 $(+1.3)$       | 17.6 $(+1.6)$   | 18.2 $(+2.2)$  | 15.9 $(-0.1)$   |
|                 |            | <b>Baseline</b> | 23.5                     | $27.7 (+4.2)$       | $28.4(+4.9)$    | $24.6(+1.1)$   | $21.6(-1.9)$    |
|                 |            | w/o DP&KD       | $24.8 (+1.3)$            | $29.2 (+5.7)$       | $29.6 (+6.1)$   | $25.3(+1.8)$   | $22.8$ $(-0.7)$ |
|                 | 50         | $w/o$ DP        |                          | $28.2 (+4.7)$       | $29.8 (+6.3)$   | $27.7 (+4.2)$  | $26.1 (+2.6)$   |
|                 |            | w/o KD          |                          | $28.5 (+5.0)$       | $28.2 (+4.7)$   | $26.5 (+3.0)$  | $23.2$ $(-0.2)$ |
|                 |            | <b>Full</b>     |                          | $29.9(+6.4)$        | $30.1 (+6.6)$   | $28.3$ (+4.8)  | $26.9 (+3.4)$   |

<span id="page-13-0"></span>TABLE VIII TEST ACCURACIES OF MODELS TRAINED ON THE DISTILLED DATA OF TINY-IMAGENET [\[80\]](#page-11-20) WITH DIFFERENT IPCS. 3-LAYER CNN IS THE ARCHITECTURE USED FOR DATA DISTILLATION AND IS THE TEACHER MODEL OF KD. NOTE THAT FOR DATM (IPC=50), THE TEACHER MODEL OF