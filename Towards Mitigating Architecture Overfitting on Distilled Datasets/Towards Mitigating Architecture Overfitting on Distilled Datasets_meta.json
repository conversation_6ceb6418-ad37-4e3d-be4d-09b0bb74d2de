{"table_of_contents": [{"title": "Towards Mitigating Architecture Overfitting on\nDistilled Datasets", "heading_level": null, "page_id": 0, "polygon": [[72.75, 59.25], [540.28125, 59.25], [540.28125, 107.314453125], [72.75, 107.314453125]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.5, 464.25], [213.75, 464.25], [213.75, 473.34375], [133.5, 473.34375]]}, {"title": "II. RELATED WORKS", "heading_level": null, "page_id": 1, "polygon": [[390.75, 350.25], [483.75, 350.25], [483.75, 360.228515625], [390.75, 360.228515625]]}, {"title": "III. METHODS", "heading_level": null, "page_id": 2, "polygon": [[141.046875, 576.75], [206.25, 576.75], [206.25, 585.87890625], [141.046875, 585.87890625]]}, {"title": "<PERSON><PERSON> with Three-Phase Keep Rate", "heading_level": null, "page_id": 2, "polygon": [[309.75, 131.25], [484.5, 131.25], [484.5, 141.0556640625], [309.75, 141.0556640625]]}, {"title": "Algorithm 1 DropPath with Three-Phase Keep Rate", "heading_level": null, "page_id": 3, "polygon": [[48.0, 316.5], [264.0, 316.5], [264.0, 326.390625], [48.0, 326.390625]]}, {"title": "B. Knowledge Distillation from Small Teacher Model", "heading_level": null, "page_id": 4, "polygon": [[47.513671875, 56.25], [269.25, 56.25], [269.25, 66.4189453125], [47.513671875, 66.4189453125]]}, {"title": "C. Training and Data Augmentation", "heading_level": null, "page_id": 4, "polygon": [[48.0, 470.25], [198.75, 470.25], [198.75, 479.53125], [48.0, 479.53125]]}, {"title": "IV. EXPERIMENTS", "heading_level": null, "page_id": 4, "polygon": [[396.24609375, 333.0], [478.125, 333.0], [478.125, 342.826171875], [396.24609375, 342.826171875]]}, {"title": "<PERSON><PERSON> Architecture Overfitting in Dateset Distillation", "heading_level": null, "page_id": 4, "polygon": [[309.287109375, 483.0], [552.75, 483.0], [552.75, 493.06640625], [309.287109375, 493.06640625]]}, {"title": "TABLE II", "heading_level": null, "page_id": 5, "polygon": [[287.173828125, 58.34619140625], [323.25, 58.34619140625], [323.25, 66.17724609375], [287.173828125, 66.17724609375]]}, {"title": "<PERSON><PERSON> with Other Baselines", "heading_level": null, "page_id": 6, "polygon": [[48.0, 483.0], [201.2607421875, 483.0], [201.2607421875, 493.06640625], [48.0, 493.06640625]]}, {"title": "C. Improve the Performance of Training on Limited Real Data", "heading_level": null, "page_id": 6, "polygon": [[310.5, 56.25], [564.0, 56.25], [564.0, 66.75732421875], [310.5, 66.75732421875]]}, {"title": "<PERSON><PERSON> Effect Induced by Proposed Methods", "heading_level": null, "page_id": 6, "polygon": [[310.5, 578.25], [524.25, 578.25], [524.25, 588.5859375], [310.5, 588.5859375]]}, {"title": "E. Ablation Studies", "heading_level": null, "page_id": 7, "polygon": [[47.849853515625, 686.25], [129.75, 686.25], [129.75, 696.09375], [47.849853515625, 696.09375]]}, {"title": "V. CONCLUSION", "heading_level": null, "page_id": 8, "polygon": [[401.25, 445.5], [474.0, 445.5], [474.0, 455.16796875], [401.25, 455.16796875]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 8, "polygon": [[390.75, 626.25], [484.1015625, 626.25], [484.1015625, 634.9921875], [390.75, 634.9921875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 8, "polygon": [[408.75, 699.75], [466.5, 699.75], [466.5, 709.2421875], [408.75, 709.2421875]]}, {"title": "APPENDIX", "heading_level": null, "page_id": 11, "polygon": [[414.0, 348.0], [460.5, 348.0], [460.5, 357.521484375], [414.0, 357.521484375]]}, {"title": "A. Implementation Details", "heading_level": null, "page_id": 11, "polygon": [[309.75, 363.75], [420.75, 363.75], [420.75, 373.18359375], [309.75, 373.18359375]]}, {"title": "B. Supplementary Figures of Methods", "heading_level": null, "page_id": 12, "polygon": [[47.25, 375.0], [205.5, 375.0], [205.5, 384.978515625], [47.25, 384.978515625]]}, {"title": "C. Effect of scaling factor 1/p in DropPath", "heading_level": null, "page_id": 12, "polygon": [[310.5, 180.75], [493.06640625, 180.75], [493.06640625, 191.0390625], [310.5, 191.0390625]]}, {"title": "<PERSON><PERSON> Results on Tiny-ImageNet", "heading_level": null, "page_id": 12, "polygon": [[309.884765625, 499.5], [432.0, 499.5], [432.0, 508.921875], [309.884765625, 508.921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 101], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Footnote", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4612, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 118], ["Text", 6], ["Caption", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 114], ["Text", 7], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2], ["Equation", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 105], ["Text", 7], ["ListItem", 6], ["Code", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 810, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 671], ["Line", 118], ["TableCell", 48], ["Text", 10], ["SectionHeader", 4], ["Reference", 3], ["Equation", 2], ["TextInlineMath", 2], ["Caption", 1], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3164, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 2089], ["TableCell", 160], ["Line", 155], ["Text", 6], ["Reference", 2], ["SectionHeader", 1], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 583], ["Line", 123], ["TableCell", 34], ["Text", 7], ["SectionHeader", 3], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 754], ["Line", 240], ["Text", 4], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2554, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 656], ["Line", 142], ["TableCell", 107], ["Text", 8], ["Reference", 5], ["Caption", 4], ["Table", 3], ["SectionHeader", 3], ["TableGroup", 2], ["Figure", 1], ["ListItem", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 14677, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 117], ["ListItem", 30], ["Reference", 30], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 369], ["Line", 117], ["ListItem", 29], ["Reference", 29], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 115], ["Reference", 21], ["ListItem", 20], ["Text", 3], ["SectionHeader", 2], ["ListGroup", 2], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1076], ["Line", 118], ["TextInlineMath", 7], ["Text", 6], ["Reference", 4], ["SectionHeader", 3], ["Equation", 2], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 836, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["TableCell", 239], ["Line", 45], ["Table", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Towards Mitigating Architecture Overfitting on Distilled Datasets"}