# Towards Adversarially Robust Dataset Distillation by Curvature Regularization

<PERSON><sup>1</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><sup>3</sup>

<sup>1</sup>Department of Computer Science, University of Toronto

<sup>2</sup>Electrical and Computer Engineering, University of California San Diego

<sup>3</sup>School of Information Sciences, University of Illinois Urbana-Champaign

<sup>4</sup>Siebel School of Computing and Data Science, University of Illinois Urbana-Champaign

<EMAIL>, yiji<PERSON><PERSON>@ucsd.edu, <EMAIL>, {hl57, yifan26, haohanw}@illinois.edu,

## Abstract

Dataset distillation (DD) allows datasets to be distilled to fractions of their original size while preserving the rich distributional information, so that models trained on the distilled datasets can achieve a comparable accuracy while saving significant computational loads. Recent research in this area has been focusing on improving the accuracy of models trained on distilled datasets. In this paper, we aim to explore a new perspective of DD. We study how to embed adversarial robustness in distilled datasets, so that models trained on these datasets maintain the high accuracy and meanwhile acquire better adversarial robustness. We propose a new method that achieves this goal by incorporating curvature regularization into the distillation process with much less computational overhead than standard adversarial training. Extensive empirical experiments suggest that our method not only outperforms standard adversarial training on both accuracy and robustness with less computation overhead but is also capable of generating robust distilled datasets that can withstand various adversarial attacks. Our implementation is available at: [https://github.com/yumozi/GUARD.](https://github.com/yumozi/GUARD)

## Introduction

In the era of big data, the computational demands for training deep learning models are continuously growing due to the increasing volume of data. This presents substantial challenges, particularly for entities with limited computational resources. To mitigate such issues, concepts like dataset distillation [\(Wang et al. 2018\)](#page-8-0) and dataset condensation [\(Zhao,](#page-8-1) [Mopuri, and Bilen 2021;](#page-8-1) [Zhao and Bilen 2021,](#page-8-2) [2023\)](#page-8-3) have emerged, offering a means to reduce the size of the data while maintaining its utility. A successful implementation of dataset distillation can bring many benefits, such as enabling more cost-effective research on large datasets and models.

Dataset distillation (DD) refers to the task of synthesizing a smaller dataset such that models trained on this smaller set yield high performance when tested against the original, larger dataset. Dataset distillation algorithms take a large dataset as input and generate a compact, synthetic dataset. The efficacy of the distilled dataset is assessed by evaluating models trained on it against the original dataset.

Conventionally, distilled datasets are evaluated based on their standard test accuracy. Therefore, recent research has expanded rapidly in the direction of improving the test accuracy following the evaluation procedure [\(Sachdeva and](#page-8-4) [McAuley 2023\)](#page-8-4). Additionally, many studies focus on improving the efficiency of the distillation process [\(Sachdeva](#page-8-4) [and McAuley 2023\)](#page-8-4).

Less attention, however, has been given to an equally important aspect of this area of research: the adversarial robustness of models trained on distilled datasets. Adversarial robustness is a key indicator of a model's resilience against malicious inputs, making it a crucial aspect of trustworthy machine learning. Given the potential of dataset distillation to safeguard the privacy of the original dataset [\(Geng](#page-7-0) [et al. 2023;](#page-7-0) [Chen et al. 2023\)](#page-7-1), exploring its capability to also enhance model robustness opens a promising avenue for advancing research in trustworthy machine learning [\(Liu,](#page-7-2) [Chaudhary, and Wang 2023\)](#page-7-2). Thus, our work seeks to bridge this gap and focuses on the following question: How can we embed adversarial robustness into the dataset distillation process, thereby generating datasets that lead to more robust models?

Motivated by this question, we explore potential methods to accomplish this goal. As it turns out, it is not as simple as adding adversarial training to the distillation process. To find a more consistent method, we study the theoretical connection between adversarial robustness and dataset distillation. Our theory suggests that we can directly improve the robustness of the distilled dataset by minimizing the curvature of the loss function with respect to the real data. Based on our findings, we propose a novel method, GUARD (Geometric Regularization for Adversarially Robust Dataset), which incorporates curvature regularization into the distillation process. We then evaluate GUARD against existing distillation methods on ImageNette, Tiny ImageNet, and ImageNet datasets. In summary, the contributions of this paper are as follows

- Empirical and theoretical exploration of adversarial robustness in distilled datasets
- A theory-motivated method, GUARD, that offers robust dataset distillation with minimal computational overhead
- Detailed evaluation of GUARD to demonstrate its effectiveness across multiple aspects

Copyright © 2025, Association for the Advancement of Artificial Intelligence (www.aaai.org). All rights reserved.

# Related Works

## Dataset Distillation

Aiming to address the issue of the increasing amount of data required to train deep learning models, the goal of dataset distillation is to efficiently train neural networks using a small set of synthesized training examples from a larger dataset. Dataset distillation (DD) [\(Wang et al. 2018\)](#page-8-0) was one of the first such methods developed, and it showed that training on a few synthetic images can achieve similar performance on MNIST and CIFAR10 as training on the original dataset. Later, [Cazenavette et al.](#page-7-3) [\(2022\)](#page-7-3); [Zhao and Bilen](#page-8-2) [\(2021\)](#page-8-2); [Zhao, Mopuri, and Bilen](#page-8-1) [\(2021\)](#page-8-1); [Lee et al.](#page-7-4) [\(2022\)](#page-7-4) explored different methods of distillation, including gradient and trajectory matching w.r.t. the real and synthetic data, with stronger supervision for the training process. Instead of matching the weights of the neural network, another thread of works [\(Wang et al. 2022;](#page-8-5) [Zhao and Bilen 2023;](#page-8-3) [Zhang](#page-8-6) [et al. 2024;](#page-8-6) [Liu et al. 2023\)](#page-7-5) focuses on matching feature distributions of the real and synthetic data in the embedding space to better align features or preserve real-feature distribution. Considering the lack of efficiency of the bi-level optimization in previous methods, [Nguyen et al.](#page-8-7) [\(2021\)](#page-8-7); [Zhou, Nezhadarya, and Ba](#page-8-8) [\(2022\)](#page-8-8) aim to address the significant amount of meta gradient computation challenges. [Nguyen, Chen, and Lee](#page-8-9) [\(2020\)](#page-8-9) proposed a kernel-inducing points meta-learning algorithm and they further leverage the connection between the infinitely wide ConvNet and kernel ridge regression for better performance. Furthermore, [Su](#page-8-10)[cholutsky and Schonlau](#page-8-10) [\(2021\)](#page-8-10) addresses the simultaneous distillation of images and their corresponding soft labels. Later, some works focused on further improving efficiency of the process, such as [Yin, Xing, and Shen](#page-8-11) [\(2023\)](#page-8-11) that introduced SRe<sup>2</sup>L, which optimizes the distillation process by dividing it into three distinct steps for greater efficiency, and [Xu et al.](#page-8-12) [\(2024\)](#page-8-12), which proposed an approach to enhance both the efficiency and performance by first pruning the original dataset. Finally, [Li et al.](#page-7-6) [\(2024\)](#page-7-6) further advanced the process by dynamically pruning the original dataset based on the desired compression ratio and extracting information from deeper layers of the network.

Dataset distillation approaches can be broadly classified into four families based on their underlying principles: metamodel matching, gradient matching, distribution matching, and trajectory matching [\(Sachdeva and McAuley 2023\)](#page-8-4). Regardless of the particular approach, most of the existing methods rely on optimizing the distilled dataset w.r.t. a network trained with real data, such methods include DD [\(Wang et al. 2018\)](#page-8-0), DC [\(Zhao, Mopuri, and Bilen 2021\)](#page-8-1), DSA [\(Zhao and Bilen 2021\)](#page-8-2), MTT [\(Cazenavette et al. 2022\)](#page-7-3), DCC [\(Lee et al. 2022\)](#page-7-4), SRe<sup>2</sup>L [\(Yin, Xing, and Shen 2023\)](#page-8-11), ATT [\(Liu et al. 2024\)](#page-7-7) and many more.

In a related direction, some works also address the robustness of dataset distillation, but specifically focusing on out-of-distribution (OOD) robustness. For instance, [Vahid](#page-8-13)[ian et al.](#page-8-13) [\(2024\)](#page-8-13) employs risk minimization techniques to ensure robustness, while TrustDD [\(Ma et al. 2024\)](#page-7-8) incorporates outliers during the distillation process to facilitate OOD detection.

## Adversarial Attacks

Adversarial attacks are a significant concern in the field of machine learning, as they can cause models to make incorrect predictions even when presented with seemingly similar input. [Kurakin, Goodfellow, and Bengio](#page-7-9) [\(2017\)](#page-7-9) demonstrates the real-world implications of these attacks. Many different types of adversarial attacks have been proposed in the literature [\(Goodfellow, Shlens, and Szegedy 2015;](#page-7-10) [Madry et al. 2018\)](#page-7-11). In particular, Projected Gradient Descent (PGD) is a widely used adversarial attack that has been shown to be highly effective against a variety of machine learning models [\(Madry et al. 2018\)](#page-7-11). The limitations of defensive distillation, a technique initially proposed for increasing the robustness of machine learning models, were explored by [Papernot et al.](#page-8-14) [\(2017\)](#page-8-14). [Moosavi-Dezfooli,](#page-8-15) [Fawzi, and Frossard](#page-8-15) [\(2016\)](#page-8-15) introduced DeepFool, an efficient method to compute adversarial perturbations. Other notable works include the study of the transferability of adversarial attacks by [Papernot, McDaniel, and Goodfellow](#page-8-16) [\(2016\)](#page-8-16), the simple and effective black-box attack by [Nar](#page-8-17)[odytska and Kasiviswanathan](#page-8-17) [\(2016\)](#page-8-17), and the zeroth-order optimization-based attack by [Chen et al.](#page-7-12) [\(2017\)](#page-7-12). More recently, [Athalye, Carlini, and Wagner](#page-7-13) [\(2018\)](#page-7-13) investigated the robustness of obfuscated gradients, and [Wong, Schmidt, and](#page-8-18) [Kolter](#page-8-18) [\(2019\)](#page-8-18) introduced the Wasserstein smoothing as a novel defense against adversarial attacks. [Croce and Hein](#page-7-14) [\(2020\)](#page-7-14) introduced AutoAttack, which is a suite of adversarial attacks consisting of four diverse and parameter-free attacks that are designed to provide a comprehensive evaluation of a model's robustness to adversarial attacks.

## Adversarial Defense

Numerous defenses against adversarial attacks have been proposed. Among these, adversarial training stands out as a widely adopted defense mechanism that entails training machine learning models on both clean and adversarial examples [\(Goodfellow, Shlens, and Szegedy 2015\)](#page-7-10). Several derivatives of the adversarial training approach have been proposed, such as ensemble adversarial training [\(Tramer`](#page-8-19) [et al. 2018\)](#page-8-19), and randomized smoothing [\(Cohen, Rosenfield,](#page-7-15) [and Kolter 2019\)](#page-7-15). However, while adversarial training can be effective, it bears the drawback of being computationally expensive and time-consuming.

Some defense mechanisms adopt a geometrical approach to robustness. One such defense mechanism is CURE [\(Moosavi-Dezfooli et al. 2019\)](#page-8-20), a method that seeks to improve model robustness by reducing the curvature of the loss landscape during training. Similarly, [Miyato et al.](#page-8-21) [\(2015\)](#page-8-21) improved the smoothness of the output distribution, [Cisse](#page-7-16) [et al.](#page-7-16) [\(2017b\)](#page-7-16) enforced Lipschitz constants, [Ross and Doshi-](#page-8-22)[Velez](#page-8-22) [\(2018\)](#page-8-22) employed input gradient regularization, to improve the models' adversarial robustness.

Several other types of defense techniques have also been proposed, such as corrupting with additional noise and preprocessing with denoising autoencoders by [Gu and Rigazio](#page-7-17) [\(2014\)](#page-7-17), the defensive distillation approach by [Papernot et al.](#page-8-23) [\(2016\)](#page-8-23), the Houdini adversarial examples by [Cisse et al.](#page-7-18) [\(2017a\)](#page-7-18), and the approximate null space augmentation by [Liu et al.](#page-7-19) [\(2025\)](#page-7-19).

## Preliminary

## Dataset Distillation

Before we delve into the theory of robustness in dataset distillation methods, we will formally introduce the formulation of dataset distillation in this section.

Notations Let  $T$  represent the real dataset, drawn from the distribution  $\mathcal{D}_{\mathcal{T}}$ . The dataset  $\mathcal T$  comprises n image-label pairs, defined as  $\mathcal{T} = \{(\mathbf{x}_i, y_i)\}_{i=1}^n$ . Similarly, let S denote the distilled dataset, drawn from the distribution  $\mathcal{D}_{\mathcal{S}}$ , and consisting of m image-label pairs, defined as  $S =$  $\{(\tilde{\mathbf{x}}_j, \tilde{y}_j)\}_{j=1}^m$ , where  $m \ll n$ . Conventionally, instead of directly expressing the size of the distilled dataset as  $|S|$ , it is more common to describe it in terms of "images per class" (IPC). Let  $\ell(\mathbf{x}, y; \boldsymbol{\theta})$  denote the loss function of a model parameterized by  $\theta$  on a sample  $(x, y)$ , and  $\mathcal{L}(\mathcal{T}; \theta)$  denotes the empirical loss on T,  $\mathcal{L}(\mathcal{T}; \boldsymbol{\theta}) = \frac{1}{n} \sum_{i=1}^{n} \ell(\mathbf{x}_i, y_i; \boldsymbol{\theta}).$ 

Given the real training set  $T$ , dataset distillation aims to find the optimal synthetic dataset  $S^*$  by solving the following bi-level optimization problem:

$$
S^* = \underset{S}{\arg\min} \mathop{\mathbb{E}}_{(\mathbf{x}, y) \sim \mathcal{D}_{\mathcal{T}}} \ell(\mathbf{x}, y; \theta(S))
$$
  
subject to  $\theta(S) = \underset{\theta}{\arg\min} \mathcal{L}(S; \theta).$  (1)

Directly solving this problem requires searching for the optimal parameters in the inner problem and unrolling the gradient descent steps in the computation graph to find the hypergradient with respect to  $S$ , which is computationally expensive. One common alternative approach is to align a model trained on the distilled set with one trained on the real dataset. Conceptually, it can be summarized in the below equation:

subject to

$$
\min_{S} D(\theta(S), \theta(T))
$$
 $\theta(S) = \arg\min_{\theta} \mathcal{L}(S; \theta)$ and

 $\theta(T) = \arg\min_{\theta} \mathcal{L}(T; \theta)$ ,  $(2)$ 

where  $D$  is a manually chosen distance function. Recent works have proliferated along this direction, with methods such as gradient matching [\(Zhao, Mopuri, and Bilen 2021\)](#page-8-1) and trajectory matching [\(Cazenavette et al. 2022\)](#page-7-3), each focusing on aligning different aspects of the model's optimization dynamics. Some works have also tried to align the distribution of the distilled data with that of the real data [\(Zhao](#page-8-3) [and Bilen 2023;](#page-8-3) [Zhang et al. 2024;](#page-8-6) [Liu et al. 2023\)](#page-7-5), or recover a distilled version of the training data from a trained model [\(Yin, Xing, and Shen 2023;](#page-8-11) [Buzaglo et al. 2023\)](#page-7-20). These methods do not rely on the computation of secondorder gradients, leading to improved efficiency and performance on large-scale datasets.

Despite the wide spectrum of methods for dataset distillation, they were primarily designed for improving the standard test accuracy, and significantly less attention has been paid to the adversarial robustness. In the following, we conduct a preliminary study to show that adversarial robustness cannot be easily incorporated into the distilled data by the common approach of adversarial training, necessitating more refined analysis.

## The Limitation of Adversarial Training in Dataset **Distillation**

<span id="page-2-0"></span>Table 1: Accuracy of ResNet18 on ImageNette trained on distilled datasets from GUARD,  $SRe^{2}L$ , and  $SRe^{2}L$  with adversarial training

| IPC | Attack        | GUARD        | SRe2L | SRe2L +Adv |
|-----|---------------|--------------|-------|------------|
| 1   | None (Clean)  | <b>37.49</b> | 27.97 | 11.61      |
|     | <b>PGD100</b> | <b>16.22</b> | 12.05 | 10.03      |
|     | Square        | <b>26.74</b> | 18.62 | 11.18      |
|     | AutoAttack    | <b>15.81</b> | 12.12 | 10.03      |
|     | CW            | <b>29.14</b> | 20.38 | 10.31      |
|     | MIM           | <b>16.32</b> | 12.05 | 10.03      |
| 10  | None (Clean)  | <b>57.93</b> | 42.42 | 12.81      |
|     | <b>PGD100</b> | <b>23.87</b> | 4.76  | 9.93       |
|     | Square        | <b>44.07</b> | 22.77 | 11.46      |
|     | AutoAttack    | <b>19.69</b> | 4.99  | 9.96       |
|     | CW            | <b>58.67</b> | 22.11 | 10.90      |
|     | MIM           | <b>21.80</b> | 4.76  | 9.96       |

In the supervised learning setting, one of the most commonly used methods to enhance model robustness is adversarial training, which involves training the model on adversarial examples that are algorithmically searched for or crafted [\(Goodfellow, Shlens, and Szegedy 2015\)](#page-7-10). This can be formulated as

$$
\min_{\boldsymbol{\theta}} \mathop{\mathbb{E}}_{(\mathbf{x}, y) \sim \mathcal{D}} \left( \max_{\|\mathbf{v}\| \le \rho} \ell(\mathbf{x} + \mathbf{v}, y; \boldsymbol{\theta}) \right), \tag{3}
$$

where v is some perturbation within the  $\ell_p$  ball with radius  $\rho$ , and  $\mathcal D$  is the data distribution.

Analogously, in the dataset distillation setting, one intuitive way to distill robust datasets would be to synthesize a distilled dataset using a robust model trained with adversarial training. As mentioned in the related works section, many dataset distillation methods utilize a model trained on the original dataset as a comparison target, therefore this technique can be easily integrated to those methods.

While embedding adversarial training directly within the dataset distillation process may seem like an intuitive and straightforward approach, our comprehensive analysis reveals its limitations across various distillation methods. As an example, we show the evaluation of one such implementation based on  $SRe^{2}L$  [\(Yin, Xing, and Shen 2023\)](#page-8-11) in Table [1.](#page-2-0) The results indicate a significant decline in clean accuracy for models trained on datasets distilled using this technique, in contrast to those synthesized by the original method. Moreover, the improvements in robustness achieved are very inconsistent. In our experiment, we only employed a weak PGD attack with  $\epsilon = 1/255$  to generate adversarial examples for adversarial training, leading to the conclusion that even minimal adversarial training can detrimentally impact model performance when integrated into the dataset distillation process.

Such outcomes are not entirely unexpected. Previous studies, such as those by [Zhang et al.](#page-8-24) [\(2020\)](#page-8-24), have indicated that adversarial training can significantly alter the semantics of images through perturbations, even when adhering to set norm constraints. This can lead to the cross-over mixture problem, severely degrading the clean accuracy. We hypothesize that these adverse effects might be magnified during the distillation process, where the distilled dataset's constrained size results in a distribution that is vastly different from that of the original dataset.

## Methods

## Formulation of the Robust Distillation Problem

Extending the distillation problem to the adversarial robustness setting, robust dataset distillation can be formulated as a tri-level optimization problem as below:

<span id="page-3-3"></span>
$$
S^* = \underset{S}{\arg\min} \mathop{\mathbb{E}}_{(\mathbf{x},y) \sim \mathcal{D}_{\mathcal{T}}} \left( \underset{\|\mathbf{v}\| \leq \rho}{\max} \ell(\mathbf{x} + \mathbf{v}, y; \theta(S)) \right)
$$
  
subject to  $\theta(S) = \underset{\theta}{\arg\min} \mathcal{L}(S; \theta).$  (4)

If we choose to directly optimize for the robust dataset distillation objective, the tri-level optimization problem will result in a hugely inefficient process. Instead, we will uncover a theoretical relationship between dataset distillation and adversarial robustness to come up with a more efficient method that avoids the tri-level optimization process.

## Theoretical Bound of Robustness

Our aim is to create a method that allows us to efficiently and reliably introduce robustness into distilled datasets, thus we will start by exploring the theoretical connections between dataset distillation and adversarial robustness. Conveniently, previous works [\(Jetley, Lord, and Torr 2018;](#page-7-21) [Fawzi](#page-7-22) [et al. 2018\)](#page-7-22) have studied the adversarial robustness of neural networks via the geometry of the loss landscape. Inspired by [Moosavi-Dezfooli, Fawzi, and Frossard](#page-8-15) [\(2016\)](#page-8-15), here we find connections between standard training procedures and dataset distillation to provide a theoretical bound for the adversarial loss of models trained with distilled datasets.

Let  $\ell(\mathbf{x}, y; \theta)$  denote the loss function of the neural network, or  $\ell(\mathbf{x})$  for simplicity, and v denote a perturbation vector. By Taylor's Theorem,

$$
\ell(\mathbf{x} + \mathbf{v}) = \ell(\mathbf{x}) + \nabla \ell(\mathbf{x})^{\top} \mathbf{v} + \frac{1}{2} \mathbf{v}^{\top} \mathbf{H} \mathbf{v} + o(||\mathbf{v}||^{2}).
$$
 (5)

We are interested in the property of  $\ell(\cdot)$  in the locality of x, so we focus on the quadratic approximation  $\tilde{\ell}(\mathbf{x} + \mathbf{v}) =$  $\ell(\mathbf{x}) + \nabla \ell(\mathbf{x})^{\top} \mathbf{y} + \frac{1}{2} \mathbf{v}^{\top} \mathbf{H} \mathbf{v}$ . We define the adversarial loss on real data as  $\tilde{\ell}_{\rho}^{adv}(\mathbf{x}) = \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\mathbf{x} + \mathbf{v})$ . We can expand this and take the expectation over the distribution with class label c, denoted as  $D_c$ , to get the following:

$$
\mathbb{E}_{\mathbf{x}\sim D_c} \tilde{\ell}_{\rho}^{adv}(\mathbf{x}) \leq \mathbb{E}_{\mathbf{x}\sim D_c} \ell(\mathbf{x}) + \rho \mathbb{E}_{\mathbf{x}\sim D_c} \|\nabla \ell(\mathbf{x})\| +
$$
\n
$$
\frac{1}{2} \rho^2 \mathbb{E}_{\mathbf{x}\sim D_c} \lambda_1(\mathbf{x}),
$$
\n(6)

where  $\lambda_1$  is the largest eigenvalue of the Hessian matrix  $H(\ell(\mathbf{x}))$ . Then, we have the proposition:

Proposition 1. *Let* x ′ *be a distilled datum with the label* c and satisfies  $||h(\mathbf{x}') - \mathbb{E}_{\mathbf{x} \sim D_c}[h(\mathbf{x})]|| \leq \sigma$ , where  $h(\cdot)$  is a *feature extractor. Assume*  $\ell(\cdot)$  *is convex in*  $\mathbf{x}$  *and*  $\tilde{\ell}_{\rho}^{adv}(\cdot)$  *is* L*-Lipschitz in the feature space, then the below inequality holds:*

<span id="page-3-0"></span>
$$
\tilde{\ell}_{\rho}^{adv}(\mathbf{x}') \leq \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \ell(\mathbf{x}) + \rho \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \|\nabla \ell(\mathbf{x})\| + \frac{1}{2} \rho^2 \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \lambda_1(\mathbf{x}) + L\sigma.
$$
\n(7)

Given the assumption of convexity in the loss function, we can further observe that in a convex landscape the gradient magnitude tends to be lower near the optimal points. Therefore, in the context of a convex loss function and a well-distilled dataset, the gradient term  $\rho \mathbb{E}_{\mathbf{x} \sim D_c} \|\nabla \ell(\mathbf{x})\|$ contribute insignificantly to RHS of Eq. [7.](#page-3-0) This insignificance is amplified by the presence of the curvature term,  $\frac{1}{2}\rho^2 \mathbb{E}_{\mathbf{x} \sim D_c} \lambda_1(\mathbf{x})$ , which provides a sufficient descriptor of the loss landscape under our assumptions. Hence, it is reasonable to simplify the expression by omitting the gradient term, resulting in a focus on the curvature term, which is more representative of the convexity assumption and the characteristics of a well-distilled dataset. The revised expression would then be:

<span id="page-3-1"></span>
$$
\tilde{\ell}_{\rho}^{adv}(\mathbf{x}') \leq \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \ell(\mathbf{x}) + \frac{1}{2} \rho^2 \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \lambda_1(\mathbf{x}) + L\sigma.
$$
 (8)

Dataset distillation methods usually already optimizes for  $\ell(\mathbf{x})$ , and we can also assume that the  $\sigma$  for a well-distilled dataset is small. Hence, we can conclude that the upper bound of adversarial loss of distilled datasets is largely affected by the curvature of the loss function in the locality of real data samples.

In the appendix, we give a more thorough proof of the proposition and discuss the validity of some of the assumptions made. In the Experiments section, we also show results from an ablation study to demonstrate the empirical effects of some of these assumptions.

## Geometric Regularization for Adversarially Robust Dataset

Based on our theoretical discussion, we propose a method, GUARD (Geometric Regularization for Adversarially Robust Dataset). Since the theorem suggests that the upper bound of the adversarial loss is mainly determined by the curvature of the loss function, we modify the distillation process so that the trained model has a loss function with a low curvature with respect to real data.

Reducing  $\lambda_1$  in Eq. [8](#page-3-1) requires computing the Hessian matrix to get the largest eigenvalue  $\lambda_1$ , which is quite computationally expensive. Here we find an efficient approximation of it. Let  $v_1$  be the unit eigenvector corresponding to  $\lambda_1$ , then the Hessian-vector product is

$$
\mathbf{H}\mathbf{v}_1 = \lambda_1 \mathbf{v}_1 = \lim_{h \to 0} \frac{\nabla \ell(\mathbf{x} + h\mathbf{v}_1) - \nabla \ell(\mathbf{x})}{h}.
$$
 (9)

<span id="page-3-2"></span>We take the differential approximation of the Hessian-vector product, because we are interested in the curvature in a local area of  $x$  rather than its asymptotic property. Therefore, for a small  $h$ ,

$$
\lambda_1 = \|\lambda_1 \mathbf{v}_1\| \approx \|\frac{\nabla \ell(\mathbf{x} + h\mathbf{v}_1) - \nabla \ell(\mathbf{x})}{h}\|.
$$
 (10)

Previous works [\(Fawzi et al. 2018;](#page-7-22) [Jetley, Lord, and](#page-7-21) [Torr 2018;](#page-7-21) [Moosavi-Dezfooli et al. 2019\)](#page-8-20) have empirically shown that the direction of the gradient has a large cosine similarity with the direction of  $v_1$  in the input space of neural networks. Instead of calculating  $v_1$  directly, it is more efficient to take the gradient direction as a surrogate of  $v_1$ to perturb the input x. So we replace the  $v_1$  above with the normalized gradient  $\mathbf{z} = \frac{\nabla \ell(\mathbf{x})}{\|\nabla \ell(\mathbf{x})\|}$  $\frac{\sqrt{\ell(\mathbf{x})}}{\|\nabla \ell(\mathbf{x})\|}$ , and define the regularized loss  $\ell_R$  to encourage linearity in the input space:

$$
\ell_R(\mathbf{x}) = \ell(\mathbf{x}) + \lambda \|\nabla \ell(\mathbf{x} + h\mathbf{z}) - \nabla \ell(\mathbf{x})\|^2, \qquad (11)
$$

where  $\ell$  is the original loss function, h is the discretization step, and the denominator  $h$  is merged with the regularization coefficient  $\lambda$ .

## Engineering Specification

In order to evaluate the effectiveness of our method, we implemented GUARD using the  $SRe<sup>2</sup>L$  method as a baseline. We incorporated our regularizer into the squeeze step of SRe<sup>2</sup>L by substituting the standard training loss with the modified loss outlined in Eq. [11.](#page-4-0) In the case of  $SRe<sup>2</sup>L$ , this helps to synthesize a robust distilled dataset by allowing images to be recovered from a robust model in the subsequent recover step.

## Experiments

## Experiment Settings

For a systematic evaluation of our method, we investigate the top-1 classification accuracy of models trained on data distilled from three commonly-used datasets in this area: ImageNette [\(Howard 2018\)](#page-7-23), Tiny ImageNet [\(Le and](#page-7-24) [Yang 2015\)](#page-7-24), and ImageNet-1K [\(Deng et al. 2009\)](#page-7-25). ImageNette is a subset of ImageNet-1K containing 10 easy-toclassify classes. Tiny ImageNet is a scaled-down subset of ImageNet-1K, containing 200 classes and 100,000 downsized 64x64 images. We trained networks using the distilled datasets and subsequently evaluated the network's performance on the validation split of the original datasets (because none of these datasets have a test split with publicly available labels). For consistency in our experiments across all datasets, we used the standard ResNet18 architecture [\(He](#page-7-26) [et al. 2016\)](#page-7-26) to synthesize the distilled datasets and evaluate their performance.

During the squeeze step of the distillation process, we trained the model on the original dataset over 50 epochs using a learning rate of 0.025. Based on preliminary experiments, we determined that the settings  $h = 3$  and  $\lambda = 100$ provide an optimal configuration for our regularizer. In the recover step, we performed 2000 iterations to synthesize the images and run 300 epochs to generate the soft labels to obtain the full distilled dataset. In the evaluation phase, we trained a ResNet18 model on the distilled dataset for 300 epochs, before assessing it on the validation split of the original dataset.

## Comparison with Other Methods

As of now, there is only a small number of dataset distillation methods that can achieve good performance on ImageNet-level datasets, therefore our choices for comparison is small. Here, we first compare our method to the original SRe2L [\(Yin, Xing, and Shen 2023\)](#page-8-11) to observe the direct effect of our regularizer on the adversarial robustness of the trained model. We also compare with MTT [\(Cazenavette](#page-7-3) [et al. 2022\)](#page-7-3) and TESLA [\(Cui et al. 2023\)](#page-7-27) on the same datasets to gain a further understanding on the differences in robustness between our method and other dataset distillation methods. We utilized the exact ConvNet architecture described in the papers of MTT and TESLA for their distillation and evaluation, as their performance on ResNet seems to be significantly lower.

<span id="page-4-0"></span>We evaluate all the methods on three distillation scales: 10 IPC, 50 IPC, and 100 IPC. We also employed a range of attacks to evaluate the robustness of the model, including PGD100 [\(Madry et al. 2017\)](#page-7-28), Square [\(Andriushchenko](#page-7-29) [et al. 2020\)](#page-7-29), AutoAttack [\(Croce and Hein 2020\)](#page-7-14), CW [\(Car](#page-7-30)[lini and Wagner 2017\)](#page-7-30), and MIM [\(Dong et al. 2017\)](#page-7-31). This assortment includes both white-box and black-box attacks, providing a comprehensive evaluation of GUARD. For all adversarial attacks, with the exception of CW attack, we use the setting  $\epsilon = 1/255$ . For CW specifically, we set the box constraint  $c$  to  $10^{-5}$ . Due to computational limits, we were not able to obtain results for MTT and TESLA with the 100 IPC setting on ImageNet, as well as the 100 IPC setting on ImageNet for all methods.

## Results

<span id="page-4-1"></span>Image /page/4/Figure/15 description: The image displays a grid of nine generated images, each labeled with the name of an animal. The animals depicted are Gold Fish, White Shark, Cock, Tree Frog, Peacock, Green Snake, Jelly Fish, Goose, Flamingo, and Sea Lion. The images are arranged in two rows, with the top row containing five images and the bottom row containing four. The generated images are somewhat abstract and stylized, with varying degrees of clarity in representing the animals.

Figure 1: Visualization of distilled images generated using GUARD with 1 IPC setting from ImageNet.

The results are detailed in Table [2.](#page-5-0) It can be observed that GUARD consistently outperforms both SRe<sup>2</sup>L and MTT in terms of robustness across various attacks. Interestingly, we observe an increase in clean accuracy upon incorporating GUARD across various settings. While enhancing clean accuracy was not the primary goal of GUARD, this outcome aligns with its function as a regularizer, potentially aiding in model generalization. In the context of dataset distillation, where the goal is to distill essential features of the original dataset into a smaller subset, improving the generalization is expected to have positive effects on the performance. We also provide a visualization of the distilled images generated by GUARD in Figure [1,](#page-4-1) utilizing a distillation scale of 1 image per class among selected ImageNet classes. It can

be seen that the images exhibit characteristics that resemble a blend of multiple objects within their assigned class, highlighting the method's capacity to capture essential features.

## Ablation Study on Gradient Regularization

Eq. [7](#page-3-0) showed that the adversarial loss is upper-bounded by the normal loss, the gradient magnitude, and the curvature term. GUARD regularizes the curvature term while disregarding the gradient magnitude, which could theoretically reduce the upper bound of the loss as well. Here, we investigate the effect of regularizing gradient instead of curvature and present the results in Table [3.](#page-6-0) The results indicate that GUARD outperforms the gradient regularization alternatives, regardless of the regularization parameter.

## Discussion

### Robustness Guarantee

Due to the nature of dataset distillation, it is impossible to optimize the robustness of the final model with respect to the real dataset. Therefore, most approaches in this direction, including ours, have to optimize the adversarial loss of the model with respect to the distilled dataset. Unfortunately, there is always a distribution shift between the real and distilled datasets, which raises uncertainties about whether robustness on the distilled dataset will be effectively transferred when evaluated against the real dataset. Nevertheless, our theoretical framework offers assurances regarding this concern. A comparison between Eq. [6](#page-3-2) and Eq. [7](#page-3-0) reveals that the bounds of adversarial loss for real data and distilled data differ only by  $L\sigma$ . For a well-distilled dataset,  $\sigma$  should be relatively small. We have thus demonstrated that the disparity between minimizing adversarial loss on the distilled dataset and on the real dataset is confined to this constant. This conclusion of our theory allows future robust dataset distillation methods to exclusively enhance robustness with respect to the distilled dataset, without worrying if the robustness can transfer well to the real dataset.

## Computational Overhead

The structure of robust dataset distillation, as outlined in Eq. [4,](#page-3-3) inherently presents a tri-level optimization challenge. Typically, addressing such a problem could entail employing complex tri-level optimization algorithms, resulting in significant computational demands. One example of this is the integration of adversarial training within the distillation framework, which necessitates an additional optimization loop for generating adversarial examples within each iteration. However, GUARD's approach, as detailed in Eq. [11,](#page-4-0) introduces an efficient alternative. GUARD's regularization loss only requires an extra forward pass to compute the loss  $\ell(\mathbf{x} + h\mathbf{z})$  within each iteration. Therefore, integrating GUARD's regularizer into an existing method does not significantly increase the overall computational complexity, ensuring that the computational overhead remains minimal. This efficiency is particularly notable given the computationally intensive nature of tri-level optimization in robust dataset distillation. In Table [4,](#page-6-1) we present a comparison of the time per iteration required for a tri-level optimization <span id="page-5-0"></span>Table 2: Evaluation of different dataset distillation methods under adversarial attacks on ImageNette, TinyImageNet, and ImageNet. The best results among all methods are highlighted in bold, second best are underlined.

| Dataset      | IPC                                                                                                                                                                                                                                                                                          | Attack                                                             | Methods                                                                               |                                                               |                                                |                                                    |
|--------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|---------------------------------------------------------------------------------------|---------------------------------------------------------------|------------------------------------------------|----------------------------------------------------|
|              |                                                                                                                                                                                                                                                                                              |                                                                    | GUARD                                                                                 | SRe2L                                                         | MTT                                            | TESLA                                              |
|              | 10                                                                                                                                                                                                                                                                                           | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack              | 57.93<br>23.87<br><b>44.07</b><br>19.69                                               | 42.42<br>4.76<br>22.77<br>4.99                                | <b>58.43</b><br><b>39.85</b><br>34.79<br>33.72 | 36.84<br>28.10<br>24.61<br>24.48                   |
|              |                                                                                                                                                                                                                                                                                              | CW<br>MIM                                                          | <b>41.47</b><br>21.80                                                                 | 22.11<br>4.76                                                 | <b>34.57</b><br><b>39.20</b>                   | 24.61<br>28.15                                     |
|              |                                                                                                                                                                                                                                                                                              | None (Clean)<br><b>PGD100</b>                                      | <b>80.86</b><br><b>41.42</b>                                                          | <b>80.15</b><br>12.30                                         | 59.69<br><b>41.13</b>                          | 36.21<br>28.72                                     |
| ImageNette   | 50                                                                                                                                                                                                                                                                                           | Square<br>AutoAttack<br>CW<br>MIM                                  | <b>72.81</b><br>42.47<br><b>58.67</b><br><b>43.23</b>                                 | 61.50<br>12.91<br><b>53.42</b><br>12.43                       | 36.72<br>35.46<br>36.54<br><b>41.69</b>        | 25.34<br>27.21<br>29.01<br>30.12                   |
|              | 100                                                                                                                                                                                                                                                                                          | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack              | <b>83.39</b><br><b>57.50</b><br><b>77.68</b><br>64.84                                 | <b>85.83</b><br>31.65<br>19.18<br>17.93                       | 64.33<br>44.89<br>40.41<br>39.46               | 45.04<br>33.98<br>29.27<br>28.99                   |
|              |                                                                                                                                                                                                                                                                                              | CW<br>MIM                                                          | <b>69.35</b><br><b>65.07</b>                                                          | <b>68.20</b><br>18.98                                         | 40.66<br>44.89                                 | 29.32<br>33.98                                     |
|              | 1None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack<br>CW<br>MIM <b>37.00</b><br>6.39<br><b>19.53</b><br>4.91<br><b>8.40</b><br>6.5133.18<br>1.08<br><b>15.85</b><br>0.79<br>3.24<br>1.108.14<br>4.08<br>2.48<br>2.44<br>2.50<br>4.0814.06<br><b>8.40</b><br>6.31<br>6.16<br>6.26<br>8.40 |                                                                    |                                                                                       |                                                               |                                                |                                                    |
| TinyImageNet | 50                                                                                                                                                                                                                                                                                           | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack<br>CW<br>MIM | <b>55.61</b><br><b>15.63</b><br><b>36.93</b><br>13.84<br><b>20.46</b><br><b>16.09</b> | <b>56.42</b><br>0.27<br><b>15.50</b><br>0.16<br>12.12<br>0.29 | 17.84<br>5.62<br>3.84<br>3.52<br>3.66<br>5.64  | 28.24<br>12.12<br>10.39<br>10.01<br>10.13<br>12.12 |
|              | 100                                                                                                                                                                                                                                                                                          | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack<br>CW<br>MIM | <b>60.13</b><br>13.79<br><b>37.06</b><br>12.76<br><b>20.05</b><br>14.35               | 59.30<br>0.25<br>17.74<br>0.19<br>14.02<br>0.24               | 29.16<br>8.63<br>7.29<br>6.75<br>6.93<br>8.63  | 30.48<br>14.45<br>12.02<br>11.57<br>11.57<br>14.45 |
| ImageNet-1K  | 10                                                                                                                                                                                                                                                                                           | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack<br>CW<br>MIM | 27.25<br>5.25<br>17.88<br>3.33<br>7.68<br>5.23                                        | 21.30<br>0.55<br>18.02<br>0.34<br>3.21<br>0.51                | -                                              | -                                                  |
|              | 50                                                                                                                                                                                                                                                                                           | None (Clean)<br><b>PGD100</b><br>Square<br>AutoAttack<br>CW<br>MIM | 39.89<br>9.77<br>28.39<br>7.03<br>14.14<br>9.84                                       | 46.80<br>0.59<br>32.40<br>0.47<br>6.31<br>0.64                | -                                              | -                                                  |

algorithm, such as the one used for embedded adversarial

<span id="page-6-0"></span>Table 3: Accuracy on ImageNette of original SRe<sup>2</sup>L, GUARD, and gradient regularization on  $SRe<sup>2</sup>L$  with regularization parameters ( $\lambda_g = 10^{-4}, 10^{-3}, 10^{-2}, 0.1, 1$ , where  $\lambda_g$  is omitted in table columns for brevity). AA stands for AutoAttack. The best results among all methods are highlighted in bold.

| IPC | Attack        | Methods |       |       |       |       |       |       |
|-----|---------------|---------|-------|-------|-------|-------|-------|-------|
|     |               | SRe²L   | GUARD | 10-4  | 10-3  | 10-2  | 0.1   | 1     |
| 1   | (Clean)       | 27.97   | 37.49 | 13.72 | 16.15 | 16.41 | 17.58 | 18.75 |
|     | <b>PGD100</b> | 12.05   | 16.22 | 6.39  | 9.32  | 10.27 | 10.39 | 13.27 |
|     | Square        | 18.62   | 26.74 | 9.71  | 11.69 | 13.68 | 12.94 | 15.97 |
|     | AA            | 12.12   | 15.81 | 6.32  | 9.35  | 10.17 | 10.42 | 13.25 |
|     | CW            | 20.38   | 29.14 | 7.62  | 11.06 | 11.64 | 11.31 | 14.47 |
|     | MIM           | 12.05   | 16.32 | 6.39  | 9.25  | 10.39 | 10.39 | 13.27 |
| 10  | (Clean)       | 42.42   | 57.93 | 44.82 | 41.81 | 42.80 | 43.34 | 40.31 |
|     | <b>PGD100</b> | 4.76    | 23.87 | 15.39 | 14.47 | 15.41 | 13.68 | 16.92 |
|     | Square        | 22.77   | 44.07 | 34.06 | 31.80 | 34.73 | 31.85 | 31.03 |
|     | AA            | 4.99    | 19.69 | 15.62 | 14.52 | 15.64 | 13.78 | 16.87 |
|     | CW            | 22.11   | 41.47 | 21.58 | 20.64 | 21.17 | 18.85 | 21.53 |
|     | MIM           | 4.76    | 21.80 | 15.41 | 14.60 | 15.34 | 13.66 | 17.41 |
| 50  | (Clean)       | 80.15   | 80.86 | 76.46 | 74.06 | 73.12 | 74.52 | -     |
|     | <b>PGD100</b> | 12.30   | 41.42 | 35.54 | 35.36 | 33.48 | 29.17 | -     |
|     | Square        | 61.50   | 72.81 | 67.08 | 63.24 | 63.97 | 65.53 | -     |
|     | AA            | 12.91   | 42.47 | 35.59 | 35.54 | 33.52 | 29.32 | -     |
|     | CW            | 53.42   | 58.67 | 46.06 | 45.43 | 44.05 | 41.02 | -     |
|     | MIM           | 12.43   | 43.23 | 35.61 | 35.39 | 33.66 | 29.07 | -     |

training, against the time required for GUARD. The findings show that GUARD is much more computationally efficient and has a lower memory usage as well.

<span id="page-6-1"></span>Table 4: Computation overhead of GUARD compared with embedded adversarial training. Experiments are performed on one NVIDIA A100 80GB PCIe GPU with batch size 32. We measure 5 times per iteration training time and report the average and standard deviation.

| Method       | Time (s) / Iter                                        | Peak Mem |
|--------------|--------------------------------------------------------|----------|
| GUARD        | $0.007 colorbox{red}{white}{	ext{	extpm}}2.661e^{-4}$ | 3851MiB  |
| Adv Training | $2.198 colorbox{red}{white}{	ext{	extpm}}6.735e^{-4}$ | 4211MiB  |

## Transferability

Our investigation focuses on studying the effectiveness of the curvature regularizer within the  $SRe<sup>2</sup>L$  framework. Theoretically, this method can be extended to a broad spectrum of dataset distillation methods. GUARD's application is feasible for any distillation approach that utilizes a model trained on the original dataset as a comparison target during the distillation phase — a strategy commonly seen across many dataset distillation techniques as noted in the Related Works section. This criterion is met by the majority of dataset distillation approaches, with the exception of those following the distribution matching approach, which may not consistently employ a comparison model [\(Sachdeva and](#page-8-4)

[McAuley 2023\)](#page-8-4). This observation suggests GUARD's potential compatibility with a wide array of dataset distillation strategies. To demonstrate this, we explored two additional implementation of GUARD using DC [\(Zhao, Mopuri, and](#page-8-1) [Bilen 2021\)](#page-8-1) and CDA [\(Yin and Shen 2023\)](#page-8-25) as baseline distillation methods. DC represents an earlier, simpler approach that leverages gradient matching for distillation purposes, whereas CDA is a more recent distillation technique, specifically designed for very large datasets. As shown in Table [5,](#page-6-2) GUARD consistently improves both clean accuracy and robustness across various dataset distillation methods.

<span id="page-6-2"></span>Table 5: Direct comparison of the original DC, SRe<sup>2</sup>L, and CDA methods with the addition of GUARD regularizer (marked by † ) on CIFAR10. The best results among each pair of compared methods are highlighted in bold.

| IPC | Attack            | Methods                  |                |       |                             |       |                       |  |
|-----|-------------------|--------------------------|----------------|-------|-----------------------------|-------|-----------------------|--|
|     |                   | DC                       | $DC^{\dagger}$ |       | $SRe^2L$ $SRe^2L^{\dagger}$ |       | $CDA$ $CDA^{\dagger}$ |  |
|     | None (Clean)      | 29.96                    | 30.95          | 17.13 | 22.88                       | 14.98 | 23.18                 |  |
|     | <b>PGD100</b>     | 24.59                    | 46.88          | 13.56 | 19.21                       | 12.69 | 18.70                 |  |
| 1   | Square            | 24.72                    | 48.56          | 13.75 | 19.55                       | 12.84 | 19.17                 |  |
|     | <b>AutoAttack</b> | 24.33                    | 14.99          | 13.43 | 18.91                       | 12.63 | 18.42                 |  |
|     | CW                | 24.58                    | 15.19          | 13.52 | 18.95                       | 12.62 | 18.52                 |  |
|     | MIM               | 24.62                    | 15.27          | 13.57 | 19.22                       | 12.69 | 18.71                 |  |
|     | None (Clean)      | 45.38                    | 46.83          | 26.58 | 30.76                       | 20.55 | 30.65                 |  |
|     | <b>PGD100</b>     | 31.84                    | 32.36          | 18.24 | 22.31                       | 14.60 | 24.33                 |  |
| 10  | Square            | 33.71                    | 33.54          | 19.99 | 24.16                       | 15.93 | 25.66                 |  |
|     | <b>AutoAttack</b> | 31.05                    | 31.84          | 18.11 | 21.58                       | 14.47 | 24.04                 |  |
|     | CW                | 31.95                    | 32.35          | 18.73 | 21.98                       | 14.83 | 24.51                 |  |
|     | <b>MIM</b>        | 31.89                    | 32.37          | 18.25 | 22.35                       | 14.62 | 24.34                 |  |
|     | None (Clean)      |                          |                | 43.96 | 44.05                       | 36.32 | 43.05                 |  |
|     | <b>PGD100</b>     |                          |                | 24.74 | 33.12                       | 21.58 | 33.02                 |  |
| 50  | Square            | $\overline{\phantom{0}}$ |                | 29.68 | 35.22                       | 25.76 | 35.19                 |  |
|     | <b>AutoAttack</b> |                          |                | 24.45 | 32.24                       | 21.46 | 31.96                 |  |
|     | CW                |                          |                | 26.09 | 32.67                       | 22.54 | 32.56                 |  |
|     | <b>MIM</b>        |                          |                | 24.81 | 33.12                       | 21.61 | 33.03                 |  |

## Conclusions

Our work focuses on a novel perspective on dataset distillation by emphasizing its adversarial robustness characteristics. Upon reaching the theoretical conclusion that the adversarial loss of distilled datasets is bounded by the curvature, we proposed GUARD, a method that can be integrated into many dataset distillation methods to provide robustness against diverse types of attacks and potentially improve clean accuracy. Our theory also provided the insight that the optimization of robustness with respect to distilled and real datasets is differentiated only by a constant term, which may open up potentials for subsequent research in the field. Future work could explore the integration of robustness into more dataset distillation approaches as well as outof-distribution settings. We hope our work contributes to the development of DD methods that are not only efficient but also robust, and will inspire further research in this area.

## References

<span id="page-7-29"></span>Andriushchenko, M.; Croce, F.; Flammarion1, N.; and Hein, M. 2020. Square attack: a query-efficient black-box adversarial attack via random search. In *Proceedings of the European Conference on Computer Vision*.

<span id="page-7-13"></span>Athalye, A.; Carlini, N.; and Wagner, D. 2018. Obfuscated gradients give a false sense of security: Circumventing defenses to adversarial examples. *arXiv preprint arXiv:1802.00420*.

<span id="page-7-20"></span>Buzaglo, G.; Haim, N.; Yehudai, G.; Vardi, G.; Oz, Y.; Nikankin, Y.; and Irani, M. 2023. Deconstructing Data Reconstruction: Multiclass, Weight Decay and General Losses. *arXiv preprint arXiv:2307.01827*.

<span id="page-7-30"></span>Carlini, N.; and Wagner, D. 2017. Towards evaluating the robustness of neural networks. In *IEEE Symposium on Security and Privacy*, 39–57.

<span id="page-7-3"></span>Cazenavette, G.; He, K.; Torralba, A.; Efros, A. A.; and Zhu, J.-Y. 2022. Dataset distillation by matching training trajectories. In *IEEE Conference on Computer Vision and Pattern Recognition*.

<span id="page-7-12"></span>Chen, P.-Y.; Zhang, H.; Sharma, Y.; Yi, J.; and Hsieh, C.- J. 2017. Zoo: Zeroth order optimization based black-box attacks to deep neural networks without training substitute models. In *Proceedings of the 10th ACM Workshop on Artificial Intelligence and Security*, 15–26. ACM.

<span id="page-7-1"></span>Chen, Z.; Geng, J.; Zhu, D.; Woisetschlaeger, H.; Li, Q.; Schimmler, S.; Mayer, R.; and Rong, C. 2023. A Comprehensive Study on Dataset Distillation: Performance, Privacy, Robustness and Fairness. *arXiv preprint arXiv:2305.03355*.

<span id="page-7-18"></span>Cisse, M.; Adi, Y.; Neverova, N.; and Keshet, J. 2017a. Houdini: Fooling deep structured prediction models. *arXiv preprint arXiv:1707.05373*.

<span id="page-7-16"></span>Cisse, M.; Bojanowski, P.; Grave, E.; Dauphin, Y.; and Usunier, N. 2017b. Parseval networks: Improving robustness to adversarial examples. In *Proceedings of the 34th International Conference on Machine Learning*, 854–863.

<span id="page-7-15"></span>Cohen, J.; Rosenfield, E.; and Kolter, Z. 2019. Certified adversarial robustness via randomized smoothing. In *International Conference on Machine Learning*.

<span id="page-7-14"></span>Croce, F.; and Hein, M. 2020. Reliable evaluation of adversarial robustness with an ensemble of diverse parameter-free attacks. In *International Conference on Machine Learning*.

<span id="page-7-27"></span>Cui, J.; Wang, R.; Si, S.; and Hsieh, C.-J. 2023. Scaling Up Dataset Distillation to ImageNet-1K with Constant Memory. In *International Conference on Machine Learning*.

<span id="page-7-25"></span>Deng, J.; Dong, W.; Socher, R.; Li, L.-J.; Li, K.; and Fei-Fei, L. 2009. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, 248–255. Ieee.

<span id="page-7-31"></span>Dong, Y.; Pang, T.; Su, H.; Zhu, J.; Hu, X.; and Li, J. 2017. Boosting Adversarial Attacks with Momentum. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*.

<span id="page-7-22"></span>Fawzi, A.; Moosavi-Dezfooli, S.-M.; Frossard, P.; and Soatto, S. 2018. Empirical Study of the Topology and Geometry of Deep Networks. In *IEEE Conference on Computer Vision and Pattern Recognition*.

<span id="page-7-0"></span>Geng, J.; Chen, Z.; Wang, Y.; Woisetschlaeger, H.; Li, Q.; Schimmler, S.; Mayer, R.; Zhao, Z.; and Rong, C. 2023. A Survey on Dataset Distillation: Approaches, Applications, and Future Directions. *arXiv preprint arXiv:2305.01975*.

<span id="page-7-10"></span>Goodfellow, I. J.; Shlens, J.; and Szegedy, C. 2015. Explaining and harnessing adversarial examples. In *International Conference on Learning Representations*.

<span id="page-7-17"></span>Gu, S.; and Rigazio, L. 2014. Towards Deep Neural Network Architectures Robust to Adversarial Examples. arXiv:1412.5068.

<span id="page-7-26"></span>He, K.; Zhang, X.; Ren, S.; and Sun, J. 2016. Deep Residual Learning for Image Recognition. In *IEEE Conference on Computer Vision and Pattern Recognition*, 770–778.

<span id="page-7-23"></span>Howard, J. 2018. Imagenette.

<span id="page-7-21"></span>Jetley, S.; Lord, N.; and Torr, P. 2018. With Friends Like These, Who Needs Adversaries? In *Advances in neural information processing systems*.

<span id="page-7-32"></span>Khromov, G.; and Singh, S. P. 2023. Some Intriguing Aspects about Lipschitz Continuity of Neural Networks. *arXiv preprint arXiv:2302.10886*.

<span id="page-7-9"></span>Kurakin, A.; Goodfellow, I.; and Bengio, S. 2017. Adversarial examples in the physical world. *International Conference on Learning Representations Workshops*.

<span id="page-7-24"></span>Le, Y.; and Yang, X. 2015. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7): 3.

<span id="page-7-4"></span>Lee, S.; Chun, S.; Jung, S.; Yun, S.; and Yoon, S. 2022. Dataset Condensation with Contrastive Signals. In *International Conference on Machine Learning*.

<span id="page-7-6"></span>Li, Z.; Guo, Z.; Zhao, W.; Zhang, T.; Cheng, Z.-Q.; Khaki, S.; Zhang, K.; Sajedi, A.; Plataniotis, K. N.; Wang, K.; and You, Y. 2024. Prioritize Alignment in Dataset Distillation. arXiv:2408.03360.

<span id="page-7-7"></span>Liu, D.; Gu, J.; Cao, H.; Trinitis, C.; and Schulz, M. 2024. Dataset Distillation by Automatic Training Trajectories. arXiv:2407.14245.

<span id="page-7-2"></span>Liu, H.; Chaudhary, M.; and Wang, H. 2023. Towards Trustworthy and Aligned Machine Learning: A Data-centric Survey with Causality Perspectives. arXiv:2307.16851.

<span id="page-7-19"></span>Liu, H.; Singh, A.; Li, Y.; and Wang, H. 2025. Approximate Nullspace Augmented Finetuning for Robust Vision Transformers. In *The Second Conference on Parsimony and Learning (Proceedings Track)*.

<span id="page-7-5"></span>Liu, H.; Xing, T.; Li, L.; Dalal, V.; He, J.; and Wang, H. 2023. Dataset Distillation via the Wasserstein Metric. *arXiv preprint arXiv:2311.18531*.

<span id="page-7-8"></span>Ma, S.; Zhu, F.; Cheng, Z.; and Zhang, X.-Y. 2024. Towards trustworthy dataset distillation. *Pattern Recognition*, 110875.

<span id="page-7-28"></span>Madry, A.; Makelov, A.; Schmidt, L.; Tsipras, D.; and Vladu, A. 2017. Towards deep learning models resistant to adversarial attacks. *arXiv preprint arXiv:1706.06083*.

<span id="page-7-11"></span>Madry, A.; Makelov, A.; Schmidt, L.; Tsipras, D.; and Vladu, A. 2018. Towards Deep Learning Models Resistant to Adversarial Attacks. In *Proceedings of the International Conference on Learning Representations*.

<span id="page-8-21"></span>Miyato, T.; ichi Maeda, S.; Koyama, M.; Nakae, K.; and Ishii, S. 2015. Distributional Smoothing with Virtual Adversarial Training. arXiv:1507.00677.

<span id="page-8-15"></span>Moosavi-Dezfooli, S.-M.; Fawzi, A.; and Frossard, P. 2016. Deepfool: a simple and accurate method to fool deep neural networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2574–2582.

<span id="page-8-20"></span>Moosavi-Dezfooli, S.-M.; Fawzi, A.; Uesato, J.; and Frossard, P. 2019. Robustness via curvature regularization, and vice versa. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, 9078–9086.

<span id="page-8-17"></span>Narodytska, N.; and Kasiviswanathan, S. P. 2016. Simple Black-Box Adversarial Perturbations for Deep Networks. arXiv:1612.06299.

<span id="page-8-9"></span>Nguyen, T.; Chen, Z.; and Lee, J. 2020. Dataset metalearning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*.

<span id="page-8-7"></span>Nguyen, T.; Novak, R.; Xiao, L.; and Lee, J. 2021. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34: 5186–5198.

<span id="page-8-16"></span>Papernot, N.; McDaniel, P.; and Goodfellow, I. 2016. Transferability in Machine Learning: from Phenomena to Black-Box Attacks using Adversarial Samples. arXiv:1605.07277.

<span id="page-8-14"></span>Papernot, N.; McDaniel, P.; Jha, S.; Fredrikson, M.; Celik, Z. B.; and Swami, A. 2017. The limitations of deep learning in adversarial settings. In *2016 IEEE European Symposium on Security and Privacy*.

<span id="page-8-23"></span>Papernot, N.; McDaniel, P.; Wu, X.; Jha, S.; and Swami, A. 2016. Distillation as a defense to adversarial perturbations against deep neural networks. In *2016 IEEE Symposium on Security and Privacy (SP)*, 582–597. IEEE.

<span id="page-8-22"></span>Ross, A. S.; and Doshi-Velez, F. 2018. Improving the adversarial robustness and interpretability of deep neural networks by regularizing their input gradients. In *AAAI Conference on Artificial Intelligence*.

<span id="page-8-4"></span>Sachdeva, N.; and McAuley, J. 2023. Data distillation: a survey. *arXiv preprint arXiv:2301.04272*.

<span id="page-8-10"></span>Sucholutsky, I.; and Schonlau, M. 2021. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, 1–8. IEEE.

<span id="page-8-19"></span>Tramèr, F.; Kurakin, A.; Papernot, N.; Goodfellow, I.; Boneh, D.; and McDaniel, P. 2018. Ensemble adversarial training: attacks and defenses. In *International Conference on Learning Representations*.

<span id="page-8-13"></span>Vahidian, S.; Wang, M.; Gu, J.; Kungurtsev, V.; Jiang, W.; and Chen, Y. 2024. Group Distributionally Robust Dataset Distillation with Risk Minimization. arXiv:2402.04676.

<span id="page-8-5"></span>Wang, K.; Zhao, B.; Peng, X.; Zhu, Z.; Yang, S.; Wang, S.; Huang, G.; Bilen, H.; Wang, X.; and You, Y. 2022. CAFE: Learning to Condense Dataset by Aligning Features. In *IEEE Conference on Computer Vision and Pattern Recognition*.

<span id="page-8-0"></span>Wang, T.; Zhu, J.-Y.; Torralba, A.; and Efros, A. A. 2018. Dataset distillation. *arXiv preprint arXiv:1811.10959*.

<span id="page-8-18"></span>Wong, E.; Schmidt, F. R.; and Kolter, J. Z. 2019. Wasserstein Adversarial Examples via Projected Sinkhorn Iterations. *arXiv preprint arXiv:1902.07906*.

<span id="page-8-12"></span>Xu, Y.; Li, Y.-L.; Cui, K.; Wang, Z.; Lu, C.; Tai, Y.-W.; and Tang, C.-K. 2024. Distill Gold from Massive Ores: Bi-level Data Pruning towards Efficient Dataset Distillation. In *Proceedings of the European Conference on Computer Vision (ECCV)*.

<span id="page-8-25"></span>Yin, Z.; and Shen, Z. 2023. Dataset Distillation in Large Data Era. *arXiv preprint arXiv:2311.18838*.

<span id="page-8-11"></span>Yin, Z.; Xing, E.; and Shen, Z. 2023. Squeeze, Recover and Relabel: Dataset Condensation at ImageNet Scale From A New Perspective. In *Advances in Neural Information Processing Systems*.

<span id="page-8-6"></span>Zhang, H.; Li, S.; Wang, P.; and Zeng, S., Dan Ge. 2024. M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*.

<span id="page-8-24"></span>Zhang, J.; Xu, X.; Han, B.; Niu, G.; Cui, L.; Sugiyama, M.; and Kankanhalli, M. 2020. Attacks which do not kill training make adversarial learning stronger. In *International Conference on Machine Learning*, 11278–11287. PMLR.

<span id="page-8-2"></span>Zhao, B.; and Bilen, H. 2021. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*.

<span id="page-8-3"></span>Zhao, B.; and Bilen, H. 2023. Dataset condensation with distribution matching. In *IEEE Winter Conference on Applications of Computer Vision*.

<span id="page-8-1"></span>Zhao, B.; Mopuri, K. R.; and Bilen, H. 2021. Dataset condensation with gradient matching. In *International Conference on Learning Representations*.

<span id="page-8-8"></span>Zhou, Y.; Nezhadarya, E.; and Ba, J. 2022. Dataset distillation using neural feature regression. In *Advances in Neural Information Processing Systems*.

# Towards Adversarially Robust Dataset Distillation by Curvature Regularization

Supplementary Material

## Proof of Proposition 1

<span id="page-9-3"></span>The adversarial loss of an arbitrary input sample x can be upper-bounded as below:

$$
\tilde{\ell}_{\rho}^{adv}(\mathbf{x}) = \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\mathbf{x} + \mathbf{v})
$$
$$
= \max_{\|\mathbf{v}\| \leq \rho} \ell(\mathbf{x}) + \nabla \ell(\mathbf{x})^{\top} \mathbf{v} + \frac{1}{2} \mathbf{v}^{\top} \mathbf{H} \mathbf{v}
$$
$$
\leq \max_{\|\mathbf{v}\| \leq \rho} \ell(\mathbf{x}) + \|\nabla \ell(\mathbf{x})\| \|\mathbf{v}\| + \frac{1}{2} \lambda_1(\mathbf{x}) \|\mathbf{v}\|^2
$$
$$
= \ell(\mathbf{x}) + \|\nabla \ell(\mathbf{x})\| \rho + \frac{1}{2} \lambda_1(\mathbf{x}) \rho^2, \quad (12)
$$

where  $\lambda$  is the largest eigenvalue of the Hessian  $\mathbf{H}(\ell(\mathbf{x}))$ . Taking expectation over the distribution of real data with class label c, denoted as  $D_c$ ,

<span id="page-9-0"></span>
$$
\mathbb{E}_{\mathbf{x} \sim D_c} \tilde{\ell}_{\rho}^{adv}(\mathbf{x}) \leq \mathbb{E}_{\mathbf{x} \sim D_c} \ell(\mathbf{x}) + \rho \mathbb{E}_{\mathbf{x} \sim D_c} \|\nabla \ell(\mathbf{x})\| + \frac{1}{2} \rho^2 \mathbb{E}_{\mathbf{x} \sim D_c} \lambda_1(\mathbf{x}).
$$
\n(13)

With the assumption that  $\tilde{\ell}(\mathbf{x})$  is convex, we know that  $\tilde{\ell}_{\rho}^{adv}(\mathbf{x})$  is also convex, because  $\forall \lambda \in [0, 1],$ 

$$
\tilde{\ell}_{\rho}^{adv}(\lambda \mathbf{x}_{1} + (1 - \lambda)\mathbf{x}_{2})
$$

$$
= \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\lambda \mathbf{x}_{1} + (1 - \lambda)\mathbf{x}_{2} + \mathbf{v})
$$

$$
= \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\lambda(\mathbf{x}_{1} + \mathbf{v}) + (1 - \lambda)(\mathbf{x}_{2} + \mathbf{v}))
$$

$$
\leq \max_{\|\mathbf{v}\| \leq \rho} \lambda \tilde{\ell}(\mathbf{x}_{1} + \mathbf{v}) + (1 - \lambda)\tilde{\ell}(\mathbf{x}_{2} + \mathbf{v})
$$

$$
\leq \lambda \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\mathbf{x}_{1} + \mathbf{v}) + (1 - \lambda) \max_{\|\mathbf{v}\| \leq \rho} \tilde{\ell}(\mathbf{x}_{2} + \mathbf{v})
$$

$$
= \lambda \tilde{\ell}_{\rho}^{adv}(\mathbf{x}_{1}) + (1 - \lambda)\tilde{\ell}_{\rho}^{adv}(\mathbf{x}_{2}).
$$
 $(14)$ 

Therefore, by Jensen's Inequality,

<span id="page-9-1"></span>
$$
\tilde{\ell}_{\rho}^{adv}(\mathop{\mathbb{E}}_{\mathbf{x}\sim D_c} \mathbf{x}) \leq \mathop{\mathbb{E}}_{\mathbf{x}\sim D_c} \tilde{\ell}_{\rho}^{adv}(\mathbf{x}).
$$
\n(15)

Let  $x'$  be a datum distilled from the training data with class label  $c$ . It should be close in distribution to that of the real data. Hence, we can assume the maximum mean discrepancy (MMD) between the distilled data and real data is bounded as  $||h(\mathbf{x}') - \mathbb{E}_{\mathbf{x} \sim D_c} h(\mathbf{x})|| \le \sigma$ , where  $h(\cdot)$  is a feature extractor. If  $h(\cdot)$  is invertible, then  $\mathcal{L}^{adv}_{\rho}(\cdot) = \tilde{\ell}^{adv}_{\rho}(h^{-1}(\cdot))$  is a function defined on the feature space. We assume that  $\mathcal{L}^{adv}_{\rho}(\cdot)$  is *L*-Lipschitz, it follows that

$$
\mathcal{L}_{\rho}^{adv}(h(\mathbf{x}')) \leq \mathcal{L}_{\rho}^{adv}(\underset{\mathbf{x} \sim D_c}{\mathbb{E}} h(\mathbf{x})) + L\sigma.
$$
 (16)

If we add the assumption that  $h(\cdot)$  is linear,  $\mathbb{E}_{\mathbf{x} \sim D_c} h(\mathbf{x}) = h(\mathbb{E}_{\mathbf{x} \sim D_c} \mathbf{x})$ , then

<span id="page-9-4"></span><span id="page-9-2"></span>
$$
\tilde{\ell}_{\rho}^{adv}(\mathbf{x}') \le \tilde{\ell}_{\rho}^{adv}(\mathbf{E}_{\mathbf{x} \sim D_c} \mathbf{x}) + L\sigma.
$$
\n(17)

Combining Eq. [13,](#page-9-0) Eq. [15,](#page-9-1) Eq. [17,](#page-9-2) we get

$$
\tilde{\ell}_{\rho}^{adv}(\mathbf{x}') \leq \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \ell(\mathbf{x}) + \rho \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \|\nabla \ell(\mathbf{x})\| + \frac{1}{2} \rho^2 \mathop{\mathbb{E}}_{\mathbf{x} \sim D_c} \lambda_1(\mathbf{x}) + L\sigma.
$$
\n(18)

**Discussion** The inequality in Eq. [12](#page-9-3) is an equality if and only if the direction of the gradient is the same as the direction of  $\lambda_1$ . Previous work has empirically shown that the two directions have a large cosine similarity in the input space of neural networks. Our assumption about the Lipschitz continuity of  $\mathcal{L}_{\rho}^{adv}(\cdot)$  is reasonable, as recent work has shown improved estimation of the Lipschitz constant of neural networks in a wide range of settings [\(Khromov and Singh 2023\)](#page-7-32). Although our assumptions about the convexity of  $\ell(\mathbf{x})$  and the linearity of  $h(\cdot)$  is relatively strong, it still reflects important aspects of reality, as our experiment in Table [2](#page-5-0) has shown that reducing the curvature term in r.h.s of Eq. [18](#page-9-4) effectively improves the robustness of models trained on distilled data. Moreover, in Fig. [2](#page-10-0) we plot the distribution of eigenvalues of the real data samples on the loss landscape of a model trained on standard distilled data and a model trained on robust distilled data from our GUARD method, respectively. GUARD corresponds to a flatter curve of eigenvalue distribution, indicating that the loss landscape becomes more linear after our regularization.

<span id="page-10-0"></span>Image /page/10/Figure/1 description: Two identical line graphs are displayed side-by-side, both titled "Curvature Averages". The x-axis for both graphs is labeled "Index" and ranges from 0 to 3000. The y-axis is not explicitly labeled but shows a range of values. Both graphs depict a curve that starts near the origin, rises sharply, then flattens out, and finally rises very steeply towards the end of the x-axis. The curve in the left graph appears to be slightly lower than the curve in the right graph for most of the x-axis range, but both end at the same maximum y-value.

Figure 2: A comparison between the curvature profiles of a baseline dataset distillation method (left) and GUARD (right) in the form of sorted eigenvalues of the hessian

## Additional Results

In this section of the appendix, we provide supplementary results from our experiments. Table [6](#page-10-1) presents a detailed comparison of the effects of various adversarial attacks on GUARD, SRe<sup>2</sup>L, MTT, and TESLA, which were excluded from the main paper due to space constraints. The results further highlight GUARD's improved adversarial robustness, mai=ntaining a positive trend of being much better than other methods.

Furthermore, Table [7](#page-11-0) showcases additional comparisons illustrating the computational efficiency of GUARD. The results demonstrate that GUARD consistently achieves significantly faster runtimes per iteration compared to adversarial training.

<span id="page-10-1"></span>Table 6: Evaluation of different dataset distillation methods under adversarial attacks on ImageNette and TinyImageNet, under the 1 IPC setting. The best results among all methods are highlighted in bold, second best are underlined.

| Dataset      | IPC | Attack     | Methods      |       |              |       |
|--------------|-----|------------|--------------|-------|--------------|-------|
|              |     |            | GUARD        | SRe2L | MTT          | TESLA |
| Imagenette   | 1   | PGD100     | 16.22        | 12.05 | <b>18.60</b> | 24.68 |
|              |     | Square     | <b>26.74</b> | 18.62 | 1.40         | 22.21 |
|              |     | AutoAttack | <b>15.81</b> | 12.12 | 1.40         | 22.16 |
|              |     | CW         | <b>29.14</b> | 20.38 | 1.40         | 22.26 |
|              |     | MIM        | 16.32        | 12.05 | <b>18.60</b> | 24.68 |
| TinyImagenet | 1   | PGD100     | 1.48         | 0.83  | <b>3.26</b>  | 3.64  |
|              |     | Square     | <b>3.26</b>  | 2.44  | 1.76         | 2.83  |
|              |     | AutoAttack | <b>0.74</b>  | 0.66  | 1.76         | 2.78  |
|              |     | CW         | <b>1.54</b>  | 1.18  | 1.76         | 2.82  |
|              |     | MIM        | 1.49         | 0.88  | <b>3.28</b>  | 3.64  |

<span id="page-11-0"></span>Table 7: Relative slowdown introduced by adding GUARD versus adversarial training to  $SRe<sup>2</sup>L$  in terms of average runtime per iteration, tested on three variations of the ImageNette dataset with image sizes of 160x160px, 320x320px, and the original size, using two different graphics cards.

| Method        | A100   |        |          | RTX4090 |        |          |
|---------------|--------|--------|----------|---------|--------|----------|
|               | 160px  | 320px  | Original | 160px   | 320px  | Original |
| GUARD         | 4.63x  | 3.96x  | 4.54x    | 2.37x   | 3.26x  | 2.81x    |
| Adv. Training | 49.83x | 49.96x | 53.40x   | 35.14x  | 53.22x | 41.23x   |

## Effect of GUARD on Images

In this section, we provide a detailed comparison between synthetic images produced by GUARD and those generated by other methods. In Fig. [3,](#page-12-0) we showcase distilled images from GUARD (utilizing SRe<sup>2</sup>L as a baseline) alongside images from SRe<sup>2</sup>L.

Our comparative analysis reveals that the images generated by the GUARD method appear to have more distinct object outlines when compared with those from the baseline SRe2L method. This improved definition of objects may facilitate better generalization in subsequent model training, which could offer an explanation for the observed increases in clean accuracy.

Additionally, our synthetic images exhibit a level of high-frequency noise, which bears similarity to the disruptions introduced by adversarial attacks. While visually subtle, this attribute might play a role in enhancing the resilience of models against adversarial inputs, as training on these images could prepare the models to handle unexpected perturbations more effectively. This suggests that the GUARD method could represent a significant advancement in the creation of synthetic datasets that promote not only visual fidelity but also improved robustness in practical machine learning applications.

## Algorithm of GUARD with Optimization-based Distillation Methods

In our main paper, we explained how GUARD can be easily intergrated into  $SRe<sup>2</sup>L$  by incorporating the regularizer into the model's training loss during the squeeze (pre-training) step. We later demonstrated that GUARD could also be integrated into other distillation methods, such as DC [\(Zhao, Mopuri, and Bilen 2021\)](#page-8-1). However, unlike  $SRe<sup>2</sup>L$ , DC lacks a pre-training phase; instead, the model's training and distillation occur simultaneously, making the integration less straightforward.

Therefore, we present the GUARD algorithm using DC as the baseline method in Alg. [1.](#page-13-0) For each outer iteration  $k$ , we sample a new initial weight from some random distribution of weights to ensure the synthetic dataset can generalize well to a range of weight initializations. After, we iteratively sample a minibatch pair from the real dataset and the synthetic dataset and compute the loss over them on a neural network with the weights  $\theta_t$ . We compute the regularized loss on real data through Eq. [11.](#page-4-0) Finally, we compute the gradient of the losses w.r.t.  $\theta$ , and update the synthetic dataset through stochastic gradient descent on the distance between the gradients. At the end of each inner iteration t, we update the weights  $\theta_{t+1}$  using the updated synthetic dataset.

<span id="page-12-0"></span>Image /page/12/Picture/0 description: A dark, abstract image with glowing, ethereal shapes that resemble jellyfish. The colors are predominantly deep blues and purples, with hints of pink and white. The background is textured and appears to be a dark, possibly underwater, environment. The overall impression is one of mystery and luminescence.

Image /page/12/Picture/1 description: A surreal, abstract image with a Siamese cat-like face in the center, surrounded by a chaotic blend of colors and textures. The cat's face is rendered in shades of white, brown, and black, with blue eyes. The background is a jumble of greens, blues, reds, and purples, with a textured, almost crystalline appearance. The overall impression is dreamlike and distorted.

Image /page/12/Picture/2 description: A distorted, abstract image with a horse-like figure in the center, seemingly playing with a basketball. The colors are a mix of oranges, browns, and blues, with a textured, almost mosaic-like appearance. The background is a blend of dark and light colors, suggesting a natural or outdoor setting. The overall impression is dreamlike and surreal.

**GUARD** 

Image /page/12/Picture/4 description: A glitchy, abstract image shows three colorful bowls. The bowl in the front is blue, and the bowl behind it is red. A third, white bowl is visible to the left. The bowls are arranged in a still life composition against a textured, green background.

Image /page/12/Picture/5 description: This is a highly abstract and glitchy image that appears to depict two orange butterflies with black markings on their wings. They are positioned in the center of the frame, facing away from each other. The background is a chaotic mix of dark greens, purples, and reds, with blurry white shapes that could be flowers or leaves. The overall impression is one of digital distortion and vibrant, yet unsettling, colors.

Image /page/12/Picture/6 description: A dark, abstract image with several translucent, jellyfish-like forms floating in the center. The forms are illuminated from within, with colors ranging from pale blue and white to yellow and orange. The background is a deep, textured dark blue or black, suggesting an underwater or deep space environment. The shapes are organic and flowing, with some appearing to pulse or trail tendrils.

Image /page/12/Picture/8 description: The image displays five distinct panels, each featuring a different subject. The first panel shows several jellyfish in dark water. The second panel is a close-up of a Siamese cat's face, with a prominent eye. The third panel depicts a person playing basketball. The fourth panel shows a cat peeking out of a bucket. The fifth panel features a cello and other musical instruments, with a red, bulbous object in the foreground.

ese cat

Image /page/12/Picture/10 description: This is an abstract, impressionistic painting that appears to depict a person in a military uniform. The figure is rendered in shades of green, brown, and gray, with a hint of orange on the chest area. The style is blurry and dreamlike, with visible brushstrokes that give the image a textured feel. The background is indistinct, suggesting an outdoor or natural setting.

 ${\bf Backetball} \label{eq:8} {\bf SRe^2L}$ 

Image /page/12/Picture/12 description: A surreal image shows a cat sleeping in a stack of three buckets. The cat is mostly brown and white with some black markings. The buckets are silver and appear to be made of metal. The background is blurred and appears to be an outdoor setting with trees and foliage.

Image /page/12/Picture/14 description: The image is an abstract painting with a surreal and dreamlike quality. It features a chaotic arrangement of distorted figures and objects, rendered in a palette of warm reds, oranges, and browns, with accents of green and black. In the foreground, large, amorphous red shapes dominate, possibly representing organic forms or abstract masses. Behind these, several figures are depicted in dynamic, contorted poses. One figure appears to be playing a cello, its body elongated and twisted. Another figure, in orange and blue, seems to be in mid-air, perhaps leaping or falling. The background is a textured mix of abstract patterns and suggestions of architectural elements, creating a sense of depth and unease. The overall impression is one of movement, transformation, and emotional intensity.

Cello

Image /page/12/Picture/16 description: This is a highly distorted and glitchy image that appears to show a dog sitting in front of a laptop. The dog has brown and white fur and is looking towards the laptop screen. The laptop is open, and the keyboard is visible. The overall image has a chaotic and abstract quality due to the digital artifacts and color distortions.

Image /page/12/Picture/17 description: A close-up, abstract image shows a pair of brown strappy high-heeled shoes with a textured, almost mosaic-like background. The shoes are positioned in the center, with their straps creating an intricate, interwoven pattern. The background is a blend of greens, whites, and pinks, with a shimmering, slightly distorted effect, suggesting a floral or natural setting viewed through a textured lens.

Image /page/12/Picture/18 description: The image is a highly abstract and distorted representation of what appears to be a Christmas tree with ornaments. The colors are a chaotic mix of greens, oranges, and various bright, glitchy hues. Two orange-like shapes, possibly ornaments, are visible, one slightly above and to the right of the other. The background is a blur of green and white, with a textured, almost quilted pattern overlaying parts of the image. The overall impression is one of digital noise and fragmentation applied to a festive scene.

**GUARD** 

Image /page/12/Picture/20 description: The image is an abstract, colorful, and textured digital artwork. It features a central, diamond-patterned shape in shades of orange and yellow, resembling a tufted cushion or a stylized fruit. Surrounding this central element are swirling patterns of green, white, and dark colors, creating a sense of depth and movement. The overall impression is one of organic forms and vibrant, almost psychedelic, visual effects.

Image /page/12/Picture/21 description: This is an abstract, colorful image with a swirling, distorted effect. The dominant colors are white, black, orange, and yellow, with hints of red and green. The shapes are organic and flowing, suggesting a close-up view of something textured or layered. There are areas that resemble eyes or faces, but they are highly stylized and fragmented. The overall impression is one of intense visual activity and perhaps a dreamlike or surreal quality.

Image /page/12/Picture/22 description: The image shows a close-up of a ginger cat lying down. Overlayed on the cat's back are abstract, colorful shapes with glowing effects. These shapes appear to be digital or artistic renderings, with some resembling stylized ears or heads, and others having a more fluid, abstract form. There are hints of text or symbols within some of these shapes, such as what looks like a "2" and other unreadable characters, rendered in red and white. The overall impression is a blend of a real animal with a surreal, digital art overlay.

Image /page/12/Picture/24 description: A close-up, abstract image shows a distorted view of a person's foot wearing a clear, strappy sandal. The sandal has a pinkish-purple hue and appears to be made of plastic or PVC. The foot is positioned in a way that the toes are bent and the sole is visible. The background is blurred and appears to be outdoors with hints of green foliage and dark, earthy tones.

Image /page/12/Picture/26 description: A close-up, abstract image shows a vibrant red and orange petal-like structure with a glossy, almost liquid texture. The petal is curled and folded, revealing darker purple and black tones within its depths. To the right, a portion of a fluffy, brown and black animal's face is visible, with its eye appearing dark and beady.

Orange<br>SRe<sup>2</sup>L

Image /page/12/Picture/28 description: This is an abstract, textured image with swirling patterns of brown, orange, and green. The colors blend together in a way that suggests organic forms, possibly leaves or petals, but the overall impression is one of abstract art rather than a clear depiction of a specific object.

Laptop Sandal Orange Pineapple Corn

Image /page/12/Picture/30 description: This is a close-up, abstract image with a textured, almost woven appearance. The dominant colors are warm tones of orange, yellow, and red, with some darker, almost black elements and hints of pink. The texture appears to be made up of many vertical, ribbed lines. In the center of the image, there are two distinct shapes. One is a dark, coiled object, possibly a spring or a stylized snake. Above and slightly behind it, there is a lighter-colored, quadrupedal animal, perhaps a small dog or a fox, with its head turned to the left. The overall impression is one of vibrant, organic texture with a surreal or dreamlike quality.

Figure 3: Comparative visualization of distilled images from GUARD and SRe<sup>2</sup>L with 1 ipc setting on ImageNet.

Algorithm 1: Algorithm of GUARD (based on DC)

<span id="page-13-0"></span>**Input:** T: Training set; S: initial synthetic dataset with C classes;  $p(\theta_0)$ : initial weights distribution;  $\phi_\theta$ : neural network; K: number of outer-loop steps; T: number of inner-loop steps;  $\varsigma_{\theta}$ : number of steps for updating weights;  $\varsigma_{S}$ : number of steps for updating synthetic samples;  $\eta_{\theta}$ : learning rate for updating weights;  $\eta_{S}$ : learning rate for updating synthetic samples; D: gradient distance function; h: discretization step;  $\lambda$ : strength of regularization

for each outer training step  $k = 1$  to K do Sample initial weight  $\theta_0 \sim p(\theta_0)$ ; for each inner training step  $t = 1$  to T do for each class  $c = 1$  to  $C$  do Sample  $\omega \sim \Omega$  and a minibatch pair  $B_c^{\mathcal{T}} \sim \mathcal{T}$  and  $B_c^{\mathcal{S}} \sim \mathcal{S}$ ; Compute loss on synthetic data  $\mathcal{L}_{c}^{\mathcal{S}} = \frac{1}{|B_{c}^{\mathcal{S}}|} \sum_{(\mathbf{s}, \mathbf{y}) \in B_{c}^{\mathcal{S}}} \ell(\phi_{\theta_{t}}(\mathbf{s}), \mathbf{y})$ ; Compute loss on real data  $\mathcal{L}_{c}^{\mathcal{T}} = \frac{1}{|B_{c}^{\mathcal{T}}|} \sum_{(\mathbf{x}, \mathbf{y}) \in B_{c}^{\mathcal{T}}} \ell(\phi_{\theta_{t}}(\mathbf{x}), \mathbf{y});$ Compute  $z = \frac{\nabla \ell(\phi_{\theta_t}(\mathbf{s}), \mathbf{y})}{\|\nabla \ell(\phi_{\theta_t}(\mathbf{s}, \mathbf{y}))\|}$  $\frac{\mathbf{v}\epsilon(\varphi_{\theta_t}(\mathbf{s}), \mathbf{y})}{\|\nabla \ell(\phi_{\theta_t}(\mathbf{s}, \mathbf{y}))\|}$ Compute loss on perturbed real data  $\mathcal{L}_{c}^{\mathcal{T}_{z}} = \frac{1}{|B_{c}^{\mathcal{T}}|} \sum_{(\mathbf{x}, \mathbf{y}) \in B_{c}^{\mathcal{T}}} \ell(\phi_{\theta_{t}}(\mathbf{x} + hz), \mathbf{y});$ Compute regularizer  $\mathcal{R} = \nabla_{\theta} \mathcal{L}_{c}^{\mathcal{T}_{z}}(\theta_{t}) - \nabla_{\theta} \mathcal{L}_{c}^{\mathcal{T}}(\theta_{t});$ Compute regularized loss on real data  $\mathcal{L}_{c}^{\mathcal{T}_{\mathcal{R}}} = \mathcal{L}_{c}^{\mathcal{T}} + \lambda \mathcal{R}$ ; Update  $\mathcal{S}_c \leftarrow \text{sgd}_{\mathcal{S}}(D(\nabla_{\theta} \mathcal{L}_c^{\mathcal{S}}(\theta_t), \nabla_{\theta} \mathcal{L}_c^{\mathcal{T}_{\mathcal{R}}}(\theta_t)), \varsigma_{\mathcal{S}}, \eta_{\mathcal{S}});$ end Update  $\theta_{t+1} \leftarrow \text{sgd}_{\theta}(\mathcal{L}(\theta_t, \mathcal{S}), \varsigma_{\theta}, \eta_{\theta})$ ; end end Output: robust condensed dataset S