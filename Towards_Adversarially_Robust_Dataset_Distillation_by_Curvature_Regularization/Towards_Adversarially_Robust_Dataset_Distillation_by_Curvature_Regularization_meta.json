{"table_of_contents": [{"title": "Towards Adversarially Robust Dataset Distillation by Curvature Regularization", "heading_level": null, "page_id": 0, "polygon": [[60.0, 98.25], [551.25, 98.25], [551.25, 111.375], [60.0, 111.375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[153.298828125, 217.5], [192.75, 217.5], [192.75, 227.390625], [153.298828125, 227.390625]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[139.4033203125, 446.25], [207.0, 446.25], [207.0, 456.71484375], [139.4033203125, 456.71484375]]}, {"title": "Related Works", "heading_level": null, "page_id": 1, "polygon": [[133.5, 54.0], [213.0, 54.0], [213.0, 66.37060546875], [133.5, 66.37060546875]]}, {"title": "Dataset Distillation", "heading_level": null, "page_id": 1, "polygon": [[52.5, 72.0], [146.8740234375, 72.0], [146.8740234375, 83.4345703125], [52.5, 83.4345703125]]}, {"title": "Adversarial Attacks", "heading_level": null, "page_id": 1, "polygon": [[318.75, 54.75], [414.0, 54.75], [414.0, 65.935546875], [318.75, 65.935546875]]}, {"title": "Adversarial Defense", "heading_level": null, "page_id": 1, "polygon": [[318.0, 396.0], [414.75, 396.0], [414.75, 406.44140625], [318.0, 406.44140625]]}, {"title": "Preliminary", "heading_level": null, "page_id": 2, "polygon": [[141.0, 54.75], [204.75, 54.75], [204.75, 66.17724609375], [141.0, 66.17724609375]]}, {"title": "Dataset Distillation", "heading_level": null, "page_id": 2, "polygon": [[52.5, 69.0], [147.09814453125, 69.0], [147.09814453125, 80.244140625], [52.5, 80.244140625]]}, {"title": "The Limitation of Adversarial Training in Dataset\nDistillation", "heading_level": null, "page_id": 2, "polygon": [[318.0, 54.75], [554.25, 55.5], [554.25, 77.923828125], [318.0, 77.923828125]]}, {"title": "Methods", "heading_level": null, "page_id": 3, "polygon": [[148.59228515625, 135.0], [197.525390625, 135.0], [197.525390625, 145.1162109375], [148.59228515625, 145.1162109375]]}, {"title": "Formulation of the Robust Distillation Problem", "heading_level": null, "page_id": 3, "polygon": [[52.5, 152.25], [274.921875, 152.25], [274.921875, 161.6484375], [52.5, 161.6484375]]}, {"title": "Theoretical Bound of Robustness", "heading_level": null, "page_id": 3, "polygon": [[52.5, 340.5], [208.58203125, 340.5], [208.58203125, 351.52734375], [52.5, 351.52734375]]}, {"title": "Geometric Regularization for Adversarially\nRobust Dataset", "heading_level": null, "page_id": 3, "polygon": [[318.0, 489.0], [524.25, 489.0], [524.25, 511.2421875], [318.0, 511.2421875]]}, {"title": "Engineering Specification", "heading_level": null, "page_id": 4, "polygon": [[52.5, 269.25], [174.0, 269.25], [174.0, 279.59765625], [52.5, 279.59765625]]}, {"title": "Experiments", "heading_level": null, "page_id": 4, "polygon": [[139.5, 380.25], [207.0, 380.25], [207.0, 390.5859375], [139.5, 390.5859375]]}, {"title": "Experiment Settings", "heading_level": null, "page_id": 4, "polygon": [[52.5, 395.25], [150.0, 395.25], [150.0, 405.66796875], [52.5, 405.66796875]]}, {"title": "Comparison with Other Methods", "heading_level": null, "page_id": 4, "polygon": [[318.0, 55.5], [475.5, 55.5], [475.5, 65.548828125], [318.0, 65.548828125]]}, {"title": "Results", "heading_level": null, "page_id": 4, "polygon": [[317.953125, 388.5], [354.0, 388.5], [354.0, 398.70703125], [317.953125, 398.70703125]]}, {"title": "Ablation Study on Gradient Regularization", "heading_level": null, "page_id": 5, "polygon": [[52.5, 98.25], [256.5, 98.25], [256.5, 109.3447265625], [52.5, 109.3447265625]]}, {"title": "Discussion", "heading_level": null, "page_id": 5, "polygon": [[144.0, 222.75], [201.75, 222.75], [201.75, 233.771484375], [144.0, 233.771484375]]}, {"title": "Robustness Guarantee", "heading_level": null, "page_id": 5, "polygon": [[52.5, 237.75], [159.75, 237.75], [159.75, 248.66015625], [52.5, 248.66015625]]}, {"title": "Computational Overhead", "heading_level": null, "page_id": 5, "polygon": [[52.5, 482.25], [174.0, 482.25], [174.0, 492.6796875], [52.5, 492.6796875]]}, {"title": "Transferability", "heading_level": null, "page_id": 6, "polygon": [[52.5, 557.25], [124.5, 557.25], [124.5, 568.4765625], [52.5, 568.4765625]]}, {"title": "Conclusions", "heading_level": null, "page_id": 6, "polygon": [[406.5, 513.0], [471.0, 513.0], [471.0, 523.6171875], [406.5, 523.6171875]]}, {"title": "References", "heading_level": null, "page_id": 7, "polygon": [[144.0, 54.75], [202.5, 54.75], [202.5, 65.7421875], [144.0, 65.7421875]]}, {"title": "Towards Adversarially Robust Dataset Distillation by Curvature Regularization", "heading_level": null, "page_id": 9, "polygon": [[101.25, 54.0], [510.0, 54.0], [510.0, 66.08056640625], [101.25, 66.08056640625]]}, {"title": "Proof of Proposition 1", "heading_level": null, "page_id": 9, "polygon": [[248.25, 108.75], [361.5, 108.75], [361.5, 120.076171875], [248.25, 120.076171875]]}, {"title": "Additional Results", "heading_level": null, "page_id": 10, "polygon": [[256.693359375, 417.0], [353.513671875, 417.0], [353.513671875, 427.7109375], [256.693359375, 427.7109375]]}, {"title": "Effect of GUARD on Images", "heading_level": null, "page_id": 11, "polygon": [[231.75, 172.5], [378.75, 172.5], [378.75, 184.078125], [231.75, 184.078125]]}, {"title": "Algorithm of GUARD with Optimization-based Distillation Methods", "heading_level": null, "page_id": 11, "polygon": [[129.75, 309.0], [481.5, 309.0], [481.5, 320.009765625], [129.75, 320.009765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 94], ["Text", 13], ["SectionHeader", 3], ["ListItem", 3], ["Footnote", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6080, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 116], ["Text", 7], ["SectionHeader", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 661], ["Line", 132], ["TableCell", 118], ["Text", 9], ["SectionHeader", 3], ["TextInlineMath", 3], ["Equation", 3], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8151, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 817], ["Line", 163], ["Text", 10], ["Equation", 6], ["SectionHeader", 4], ["TextInlineMath", 4], ["Reference", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 437], ["Line", 106], ["Text", 7], ["SectionHeader", 5], ["Equation", 2], ["TextInlineMath", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["TableCell", 176], ["Line", 121], ["Text", 6], ["SectionHeader", 4], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4451, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["TableCell", 497], ["Line", 117], ["Text", 5], ["Table", 3], ["Reference", 3], ["Caption", 2], ["SectionHeader", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 19740, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 112], ["Text", 33], ["Reference", 33], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 90], ["Text", 26], ["Reference", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 840], ["Line", 141], ["Equation", 7], ["Text", 5], ["Reference", 5], ["TextInlineMath", 3], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7164, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["TableCell", 142], ["Line", 52], ["Text", 3], ["Reference", 2], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Table", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6793, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["TableCell", 45], ["Line", 30], ["Text", 6], ["SectionHeader", 2], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1307, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 40], ["Picture", 20], ["Text", 7], ["Line", 7], ["Caption", 6], ["PictureGroup", 6], ["Reference", 1]], "block_metadata": {"llm_request_count": 22, "llm_error_count": 0, "llm_tokens_used": 13264, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 23], ["Text", 2], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Towards_Adversarially_Robust_Dataset_Distillation_by_Curvature_Regularization"}