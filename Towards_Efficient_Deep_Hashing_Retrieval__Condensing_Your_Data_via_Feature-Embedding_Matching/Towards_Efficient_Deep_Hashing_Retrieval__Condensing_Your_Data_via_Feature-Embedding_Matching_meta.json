{"table_of_contents": [{"title": "Towards Efficient Deep Hashing Retrieval:\nCondensing Your Data via Feature-Embedding\nMatching", "heading_level": null, "page_id": 0, "polygon": [[75.75, 54.0], [536.25, 54.0], [536.25, 131.677734375], [75.75, 131.677734375]]}, {"title": "I. INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[133.4267578125, 388.5], [214.5, 388.5], [214.5, 398.3203125], [133.4267578125, 398.3203125]]}, {"title": "II. <PERSON><PERSON><PERSON><PERSON> CONDENSATION IN DEEP HASHING\nRETRIEVAL", "heading_level": null, "page_id": 0, "polygon": [[333.0, 646.5], [542.373046875, 646.5], [542.373046875, 668.25], [333.0, 668.25]]}, {"title": "<PERSON><PERSON> Problem Setup", "heading_level": null, "page_id": 0, "polygon": [[309.75, 670.5], [390.26953125, 670.5], [390.26953125, 681.78515625], [309.75, 681.78515625]]}, {"title": "B. Distribution Matching is Better to Condense for DHR", "heading_level": null, "page_id": 1, "polygon": [[47.43896484375, 420.75], [285.0, 420.75], [285.0, 430.41796875], [47.43896484375, 430.41796875]]}, {"title": "III. METHOD", "heading_level": null, "page_id": 1, "polygon": [[407.25, 192.75], [468.263671875, 192.75], [468.263671875, 201.8671875], [407.25, 201.8671875]]}, {"title": "Algorithm 1: IEM", "heading_level": null, "page_id": 2, "polygon": [[312.57421875, 50.25], [397.44140625, 50.25], [397.44140625, 61.43994140625], [312.57421875, 61.43994140625]]}, {"title": "2 repeat", "heading_level": null, "page_id": 2, "polygon": [[311.25, 125.25], [352.318359375, 125.25], [352.318359375, 133.8046875], [311.25, 133.8046875]]}, {"title": "IV. EXPERIMENTS", "heading_level": null, "page_id": 2, "polygon": [[392.0625, 296.25], [477.0, 296.25], [477.0, 305.89453125], [392.0625, 305.89453125]]}, {"title": "A. Implementation Details", "heading_level": null, "page_id": 2, "polygon": [[309.75, 313.435546875], [420.75, 313.435546875], [420.75, 322.5], [309.75, 322.5]]}, {"title": "B. Synthetic Set Evaluation.", "heading_level": null, "page_id": 2, "polygon": [[310.5, 514.5], [426.75, 514.5], [426.75, 524.00390625], [310.5, 524.00390625]]}, {"title": "C. Efficiency Comparison.", "heading_level": null, "page_id": 3, "polygon": [[48.0, 470.25], [157.5, 470.25], [157.5, 479.91796875], [48.0, 479.91796875]]}, {"title": "D. Generalization Comparison.", "heading_level": null, "page_id": 3, "polygon": [[47.25, 646.5], [179.25, 646.5], [179.25, 655.875], [47.25, 655.875]]}, {"title": "E. Ablation Study.", "heading_level": null, "page_id": 3, "polygon": [[309.884765625, 496.5], [387.75, 496.5], [387.75, 506.98828125], [309.884765625, 506.98828125]]}, {"title": "V. CONCLUSION", "heading_level": null, "page_id": 3, "polygon": [[398.935546875, 619.5], [474.0, 619.5], [474.0, 629.96484375], [398.935546875, 629.96484375]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 4, "polygon": [[145.5, 51.0], [203.80078125, 51.0], [203.80078125, 61.34326171875], [145.5, 61.34326171875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 92], ["Text", 12], ["SectionHeader", 4], ["ListItem", 3], ["Footnote", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3786, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 647], ["Line", 138], ["Text", 6], ["TextInlineMath", 5], ["Equation", 4], ["SectionHeader", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 707], ["Line", 138], ["Text", 8], ["TextInlineMath", 5], ["SectionHeader", 5], ["Equation", 3], ["Reference", 3], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1720, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 330], ["Span", 294], ["Line", 130], ["Text", 6], ["Reference", 5], ["Caption", 4], ["SectionHeader", 4], ["Table", 2], ["Figure", 2], ["TableGroup", 2], ["FigureGroup", 2]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 11436, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 103], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Towards_Efficient_Deep_Hashing_Retrieval__Condensing_Your_Data_via_Feature-Embedding_Matching"}