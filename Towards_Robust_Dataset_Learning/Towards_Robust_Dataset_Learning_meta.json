{"table_of_contents": [{"title": "Towards Robust Dataset Learning", "heading_level": null, "page_id": 0, "polygon": [[191.25, 106.5], [403.119140625, 106.5], [403.119140625, 119.302734375], [191.25, 119.302734375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 262.5], [191.25, 262.5], [191.25, 273.0234375], [144.75, 273.0234375]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 530.25], [127.52490234375, 530.25], [127.52490234375, 541.40625], [48.75, 541.40625]]}, {"title": "2. Related Works", "heading_level": null, "page_id": 1, "polygon": [[48.75, 516.0], [138.75, 516.0], [138.75, 526.32421875], [48.75, 526.32421875]]}, {"title": "3. Learning Robust Dataset: A Principled Ap-\nproach", "heading_level": null, "page_id": 1, "polygon": [[307.5, 216.75], [544.5, 216.75], [544.5, 241.892578125], [307.5, 241.892578125]]}, {"title": "4. Theoretical Analysis", "heading_level": null, "page_id": 3, "polygon": [[48.75, 516.75], [167.25, 516.75], [167.25, 527.25], [48.75, 527.25]]}, {"title": "4.1. Problem settings", "heading_level": null, "page_id": 3, "polygon": [[48.0, 666.0], [147.75, 664.5], [147.75, 676.7578125], [48.0, 676.7578125]]}, {"title": "4.2. Natural training on the clean dataset is non-\nrobust", "heading_level": null, "page_id": 4, "polygon": [[48.75, 192.75], [285.978515625, 192.75], [285.978515625, 215.40234375], [48.75, 215.40234375]]}, {"title": "4.3. Natural training on the robust dataset of our\nframework is robust", "heading_level": null, "page_id": 4, "polygon": [[48.75, 624.0], [286.875, 624.0], [286.875, 645.8203125], [48.75, 645.8203125]]}, {"title": "4.4. Extension to general data distributions.", "heading_level": null, "page_id": 5, "polygon": [[48.75, 288.75], [253.5, 288.75], [253.5, 298.16015625], [48.75, 298.16015625]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 5, "polygon": [[48.75, 476.25], [128.25, 476.25], [128.25, 487.265625], [48.75, 487.265625]]}, {"title": "5.1. <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 5, "polygon": [[48.0, 540.0], [122.25, 540.0], [122.25, 549.9140625], [48.0, 549.9140625]]}, {"title": "5.2. Ablation study", "heading_level": null, "page_id": 6, "polygon": [[307.5, 615.75], [398.25, 615.75], [398.25, 626.87109375], [307.5, 626.87109375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[48.75, 72.0], [120.75, 72.0], [120.75, 83.77294921875], [48.75, 83.77294921875]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[48.75, 216.17578125], [106.5, 216.17578125], [106.5, 227.390625], [48.75, 227.390625]]}, {"title": "<PERSON>. Additional proofs", "heading_level": null, "page_id": 10, "polygon": [[48.0, 72.75], [155.25, 72.75], [155.25, 83.724609375], [48.0, 83.724609375]]}, {"title": "A.1. Proof of Lemma 1", "heading_level": null, "page_id": 10, "polygon": [[48.0, 669.0], [156.0, 669.0], [156.0, 679.8515625], [48.0, 679.8515625]]}, {"title": "A.2. <PERSON><PERSON> of Theorem 1", "heading_level": null, "page_id": 11, "polygon": [[48.0, 525.75], [162.75, 525.75], [162.75, 536.37890625], [48.0, 536.37890625]]}, {"title": "A.3. <PERSON><PERSON> of Theorem 2", "heading_level": null, "page_id": 12, "polygon": [[48.0, 645.75], [163.5, 646.5], [163.5, 657.421875], [48.0, 657.421875]]}, {"title": "A.4. Advanced Theoretical Analysis on a General Dataset", "heading_level": null, "page_id": 13, "polygon": [[48.0, 73.5], [318.849609375, 73.5], [318.849609375, 83.724609375], [48.0, 83.724609375]]}, {"title": "Lemma 5. The sum of independent symmetric distributions is also symmetric.", "heading_level": null, "page_id": 13, "polygon": [[48.0, 168.75], [363.0, 168.75], [363.0, 178.083984375], [48.0, 178.083984375]]}, {"title": "<PERSON>. Additional Experiment Results", "heading_level": null, "page_id": 15, "polygon": [[47.25, 69.0], [223.5, 69.0], [223.5, 85.5], [47.25, 85.5]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 84], ["Text", 9], ["SectionHeader", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4969, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 557], ["Line", 119], ["Text", 6], ["ListItem", 3], ["TextInlineMath", 3], ["SectionHeader", 2], ["Reference", 2], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1162, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 773], ["Line", 155], ["TextInlineMath", 5], ["Text", 4], ["Equation", 4], ["Reference", 2], ["Picture", 1], ["Caption", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6341, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 971], ["Line", 114], ["Reference", 8], ["Text", 6], ["TextInlineMath", 6], ["Equation", 3], ["SectionHeader", 2], ["ListItem", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2175, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1003], ["Line", 119], ["TextInlineMath", 10], ["Text", 8], ["Reference", 8], ["Equation", 6], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 566], ["Line", 101], ["Text", 9], ["Reference", 5], ["TextInlineMath", 3], ["SectionHeader", 3], ["Footnote", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 359], ["TableCell", 146], ["Line", 109], ["Text", 5], ["Reference", 3], ["Caption", 2], ["Table", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 445], ["Line", 132], ["TableCell", 97], ["Text", 6], ["Reference", 3], ["Table", 2], ["Caption", 2], ["Figure", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 3744, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 387], ["Line", 112], ["ListItem", 25], ["Reference", 25], ["SectionHeader", 2], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 321], ["Line", 83], ["ListItem", 22], ["Reference", 20], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 1310], ["Line", 72], ["TextInlineMath", 7], ["Reference", 4], ["Equation", 3], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 1235], ["Line", 97], ["TextInlineMath", 6], ["Equation", 6], ["Text", 5], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2925, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 1436], ["Line", 94], ["Equation", 8], ["TextInlineMath", 7], ["Text", 4], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 2, "llm_tokens_used": 4021, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1279], ["Line", 79], ["Equation", 9], ["TextInlineMath", 8], ["Text", 5], ["Reference", 5], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3177, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 851], ["Line", 59], ["TextInlineMath", 4], ["Equation", 4], ["Text", 4], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8484, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 29], ["TableCell", 24], ["Line", 9], ["SectionHeader", 1], ["Table", 1], ["Caption", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3266, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Towards_Robust_Dataset_Learning"}