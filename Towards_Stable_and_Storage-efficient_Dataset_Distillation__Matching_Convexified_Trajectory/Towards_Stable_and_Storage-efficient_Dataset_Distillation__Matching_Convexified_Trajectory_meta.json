{"table_of_contents": [{"title": "Towards Stable and Storage-efficient Dataset\nDistillation: Matching Convexified Trajectory", "heading_level": null, "page_id": 0, "polygon": [[136.5, 99.75], [475.13671875, 99.75], [475.13671875, 136.705078125], [136.5, 136.705078125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.75, 364.5], [328.5, 364.5], [328.5, 374.923828125], [282.75, 374.923828125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[107.25, 618.0], [192.4453125, 618.0], [192.4453125, 629.19140625], [107.25, 629.19140625]]}, {"title": "2 Preliminaries and Related Work", "heading_level": null, "page_id": 2, "polygon": [[107.25, 133.5], [291.75, 133.5], [291.75, 144.9228515625], [107.25, 144.9228515625]]}, {"title": "2.1 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[107.25, 158.25], [189.0, 158.25], [189.0, 168.416015625], [107.25, 168.416015625]]}, {"title": "2.2 Dataset Distillation Methods.", "heading_level": null, "page_id": 2, "polygon": [[106.5, 325.5], [255.0, 325.5], [255.0, 335.865234375], [106.5, 335.865234375]]}, {"title": "3 Motivation", "heading_level": null, "page_id": 2, "polygon": [[106.90576171875, 657.0], [183.0, 657.0], [183.0, 668.63671875], [106.90576171875, 668.63671875]]}, {"title": "3.1 Review of Multi-step Trajectory Matching", "heading_level": null, "page_id": 2, "polygon": [[106.083984375, 681.0], [311.080078125, 681.0], [311.080078125, 691.453125], [106.083984375, 691.453125]]}, {"title": "3.2 Motivation: A New Perspective to Optimize the Trajectory", "heading_level": null, "page_id": 3, "polygon": [[106.5, 341.25], [379.5, 341.25], [379.5, 352.30078125], [106.5, 352.30078125]]}, {"title": "4 Our proposed MCT Method", "heading_level": null, "page_id": 5, "polygon": [[106.5322265625, 72.0], [271.5, 72.0], [271.5, 83.2412109375], [106.5322265625, 83.2412109375]]}, {"title": "4.1 Matching Convexified Trajectory", "heading_level": null, "page_id": 5, "polygon": [[106.8310546875, 96.75], [272.25, 96.75], [272.25, 106.927734375], [106.8310546875, 106.927734375]]}, {"title": "4.2 Continuous Sampling", "heading_level": null, "page_id": 5, "polygon": [[107.20458984375, 480.75], [223.5, 480.75], [223.5, 490.74609375], [107.20458984375, 490.74609375]]}, {"title": "4.3 Memory-Efficient Storage", "heading_level": null, "page_id": 5, "polygon": [[106.5, 681.0], [242.25, 681.0], [242.25, 691.453125], [106.5, 691.453125]]}, {"title": "5 Experiment", "heading_level": null, "page_id": 6, "polygon": [[107.25, 395.2265625], [187.5, 395.2265625], [187.5, 406.0546875], [107.25, 406.0546875]]}, {"title": "5.1 Experiment Setup", "heading_level": null, "page_id": 6, "polygon": [[107.1298828125, 419.25], [208.7314453125, 419.25], [208.7314453125, 429.64453125], [107.1298828125, 429.64453125]]}, {"title": "5.2 Experiment Result", "heading_level": null, "page_id": 7, "polygon": [[106.5, 401.25], [211.5, 401.25], [211.5, 412.2421875], [106.5, 412.2421875]]}, {"title": "5.3 Ablation Studies", "heading_level": null, "page_id": 8, "polygon": [[106.5, 219.26953125], [202.60546875, 219.26953125], [202.60546875, 230.484375], [106.5, 230.484375]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 8, "polygon": [[106.5, 404.25], [184.974609375, 404.25], [184.974609375, 416.8828125], [106.5, 416.8828125]]}, {"title": "7 Limitations", "heading_level": null, "page_id": 9, "polygon": [[106.5, 144.0], [186.0, 144.0], [186.0, 155.84765625], [106.5, 155.84765625]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.25, 305.25], [165.0, 305.25], [165.0, 317.109375], [107.25, 317.109375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 50], ["Text", 8], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4476, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 45], ["Text", 8], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 844, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 389], ["Line", 56], ["Text", 6], ["SectionHeader", 5], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 960], ["Line", 143], ["TextInlineMath", 5], ["Equation", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1282, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 54], ["TextInlineMath", 3], ["Figure", 1], ["Caption", 1], ["Text", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 743, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 858], ["Line", 94], ["TextInlineMath", 5], ["SectionHeader", 4], ["Equation", 4], ["Text", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1081, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 303], ["Line", 43], ["TextInlineMath", 3], ["Text", 3], ["Equation", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 731, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["TableCell", 105], ["Line", 77], ["Text", 4], ["Caption", 2], ["Reference", 2], ["Table", 1], ["Figure", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 774, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["TableCell", 74], ["Line", 33], ["Caption", 4], ["Table", 3], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 2, "llm_tokens_used": 7034, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 48], ["Reference", 12], ["ListItem", 11], ["Text", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 45], ["Reference", 18], ["ListItem", 17], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Towards_Stable_and_Storage-efficient_Dataset_Distillation__Matching_Convexified_Trajectory"}