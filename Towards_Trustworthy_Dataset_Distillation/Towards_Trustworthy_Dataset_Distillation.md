# arXiv:2307.09165v2 [cs.LG] 11 Aug 2024 arXiv:2307.09165v2 [cs.LG] 11 Aug 2024

# Towards Trustworthy Dataset Distillation

Shijie Ma<sup>1,2</sup>, <PERSON><PERSON><sup>1,2</sup>, <PERSON><PERSON><sup>1,2</sup>, <PERSON><PERSON><PERSON><sup>1,2\*</sup>

<sup>1</sup>MAIS, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China <sup>2</sup>School of Artificial Intelligence, University of Chinese Academy of Sciences, Beijing, 100049, China {mashijie2021, zhufei2018, chengzhen2019}@ia.ac.cn, <EMAIL>

## Abstract

*Efficiency and trustworthiness are two eternal pursuits when applying deep learning in real-world applications. With regard to efficiency, dataset distillation (DD) endeavors to reduce training costs by distilling the large dataset into a tiny synthetic dataset. However, existing methods merely concentrate on in-distribution (InD) classification in a closed-world setting, disregarding out-of-distribution (OOD) samples. On the other hand, OOD detection aims to enhance models' trustworthiness, which is always inefficiently achieved in full-data settings. For the first time, we simultaneously consider both issues and propose a novel paradigm called Trustworthy Dataset Distillation (TrustDD). By distilling both InD samples and outliers, the condensed datasets are capable of training models competent in both InD classification and OOD detection. To alleviate the requirement of real outlier data, we further propose to corrupt InD samples to generate pseudo-outliers, namely Pseudo-Outlier Exposure (POE). Comprehensive experiments on various settings demonstrate the effectiveness of TrustDD, and POE surpasses the state-of-the-art method Outlier Exposure (OE). Compared with the preceding DD, TrustDD is more trustworthy and applicable to open-world scenarios. Our code is available at* [https:](https://github.com/mashijie1028/TrustDD) [//github.com/mashijie1028/TrustDD](https://github.com/mashijie1028/TrustDD)

# 1. Introduction

When applying algorithms and deploying models in practical scenarios, efficiency and trustworthiness are two crucial factors that require careful consideration. On the one hand, the large amounts of training data  $[8, 34]$  $[8, 34]$  $[8, 34]$  and computational resources are impractical and not affordable in downstream applications. On the other hand, trustworthiness and reliability also matter. One could not expect the test data to always be drawn from the same distribution as the training data, so deep networks are supposed to detect

<span id="page-0-0"></span>Image /page/0/Figure/9 description: The image illustrates a comparison between 'Ordinary DD' and 'TrustDD' methods for handling out-of-distribution (OOD) data. Both methods involve training a neural network on an in-distribution (InD) dataset of horses and an OOD dataset of bubbly images. In the 'Ordinary DD' approach, the model is shown to incorrectly classify the OOD data as 'CAT' with high confidence, indicated by a red cross. In contrast, the 'TrustDD' method correctly identifies the InD data as 'HORSE' with high confidence and the OOD data as 'REJECT' with low confidence, indicated by green checkmarks. This highlights TrustDD's ability to reliably reject OOD samples.

Figure 1: Advantages of the proposed TrustDD over preceding dataset distillation (Ordinary DD). For test OOD samples, models trained by Ordinary DD assign high confidence and misclassify bubbly samples in Texture [\[5\]](#page-8-1) as cats, while TrustDD is capable to train reliable models to reject OOD samples with low confidence.

*out-of-distribution* (OOD) samples [\[13,](#page-8-2) [14,](#page-8-3) [27\]](#page-9-1) from unknown classes, rather than irresponsibly classify them into known categories, which may lead to catastrophic damage in safety-critical scenarios. For instance, in autonomous driving [\[2\]](#page-8-4), it is desirable to transfer control to the driver once the system detects anomalous situations rather than making arbitrary decisions. In medical imaging [\[45\]](#page-9-2), detecting abnormalities areas and unknown diseases are important for diagnosis.

In terms of efficiency, efficient deep learning [\[30\]](#page-9-3) has emerged to reduce computational and training data requirements while minimizing performance degradation in various perspectives. Knowledge distillation [\[17\]](#page-8-5), model quantization  $[20]$ , and lightweight neural networks  $[18]$  offer efficient model options. From an orthogonal perspective, making data itself more efficient is also promising. *Dataset Distillation* (DD) [\[44\]](#page-9-4) aims to learn a small synthetic dataset, upon which model trained could gain similar performance on test dataset compared to the model trained on the original training dataset. Zhao *et al*. [\[51,](#page-10-0) [53\]](#page-10-1) reformulated DD as a gradient matching problem to bypass the

<sup>\*</sup>Corresponding author.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: Two density plots are shown side-by-side. Both plots have "MSP score" on the x-axis, ranging from 0.0 to 1.0, and "frequency" on the y-axis, ranging from 0 to 4. The legend in both plots indicates two categories: "InD: CIFAR10" represented by a blue shaded area, and "OOD: Texture" represented by an orange shaded area. The left plot shows that the "InD: CIFAR10" distribution peaks sharply near 1.0, while the "OOD: Texture" distribution has a broader peak between 0.4 and 0.6. The right plot shows a different distribution where the "OOD: Texture" distribution peaks around 0.4, and the "InD: CIFAR10" distribution has a bimodal shape with peaks around 0.4 and a higher peak near 1.0.

(a) DD (FPR: 91.62) (b) TrustDD (FPR: 75.49) Figure 2: Maximum Softmax Probability score distribution of InD and OOD samples. TrustDD could train better OOD detectors than ordinary DD (Figure  $2a \rightarrow 2b$  $2a \rightarrow 2b$ ). Models are trained on CIFAR10 with 50 Image Per Class (IPC).

complex optimization procedure. Cazenavette *et al*. [\[4\]](#page-8-8) proposed to match training trajectories of the target network with a synthetically trained network to distill datasets, their method surpassed the former state-of-the-art (SOTA) by a large margin. Another line of research [\[29,](#page-9-5) [32,](#page-9-6) [33,](#page-9-7) [55\]](#page-10-2) resorts to kernel-based methods to obtain the closed-form solution in the inner loop. However, trustworthiness and reliability haven't been taken into account in dataset distillation studies, as a result, models tend to suffer from overconfidence problems.

As for the trustworthiness, OOD detection works [\[13,](#page-8-2) [25,](#page-9-8) [27,](#page-9-1) [28\]](#page-9-9) aim to improve models' ability to detect and reject OOD samples. Existing works are always conducted in fulldata settings, which is inefficient and time-consuming.

In short, although both efficiency and trustworthiness are important when deploying models in real-world applications, previous works solely focus on one aspect, which is less applicable. Motivated by this, in this paper, we consider both efficiency and trustworthiness simultaneously from the data perspective, and take the first step towards trustworthy dataset distillation. To achieve this goal, we take the spirit of Outlier Exposure (OE) [\[14\]](#page-8-3) and establish a learning paradigm called *Trustworthy Dataset Distillation* (TrustDD). To relax the requirement to auxiliary outlier dataset, we further propose to generate *pseudo-outliers* from in-distribution samples and expose them to deep networks to boost OOD detection, which is named *Pseudo-Outlier Exposure* (POE). We distill both InD and the generated outliers into a tiny dataset, such distilled dataset is capable of training not only competent classifiers but also reliable OOD detectors (See Figure [2.](#page-1-0)).

To the best of our knowledge, this is the first time that trustworthiness is taken into consideration in dataset distillation. Models trained on the distilled dataset perform well on in-distribution (InD) classification and out-ofdistribution (OOD) detection simultaneously. Figure [1](#page-0-0) illustrates the superiority of TrustDD for handling both tasks.

Our main contributions are summarized as follows:

• We propose a novel paradigm, named *Trustworthy Dataset Distillation* (TrustDD), considering both InD

classification and OOD detection, which ensures both efficiency and trustworthiness and improves the OOD detection performance of DD.

- The proposed *Pseudo-Outlier Exposure* (POE) in TrustDD performs comparably or surpasses the commonly-used SOTA OE even though POE does not need to collect real outlier data.
- Extensive experiments show TrustDD improves OOD detection performance by a large margin without the loss of InD classification accuracy, making DD trustworthy and applicable to the open-world.

## 2. Related Work

### 2.1. Dataset Distillation

Coreset selection  $[1, 36]$  $[1, 36]$  $[1, 36]$  is an early-stage research in data-efficient learning. Most methods rely heavily on heuristics to select representatives, which are short-sighted and sub-optimal. *Dataset Distillation* (DD) [\[35,](#page-9-11) [50\]](#page-10-3), also known as *Dataset Condensation* (DC) [\[51,](#page-10-0) [53\]](#page-10-1), aims to synthesize a tiny dataset to train models which could perform comparably to the complete dataset setting. A pioneer work by Wang *et al*. [\[44\]](#page-9-4) formulates DD in a bi-level meta-learning framework. One line of research focuses on simplifying the inner loop by solving the closed-form solution towards kernel ridge regression [\[29,](#page-9-5) [32,](#page-9-6) [33,](#page-9-7) [55\]](#page-10-2). Alternatively, Zhao *et al*. [\[53\]](#page-10-1) argued that making parameters trained on condensed data approximate the target parameters and derived gradient matching objective, which simplified DD from the parameter perspective. They further applied Differentiable Siamese Augmentation (DSA) [\[51\]](#page-10-0) to distill more informative images by enabling effective data augmentation on synthetic data. By combining metalearning and parameter matching, Cazenavette *et al*. [\[4\]](#page-8-8) proposed directly Matching Training Trajectories (MTT) and achieved SOTA results. A recent work called TESLA [\[7\]](#page-8-10) reduced GPU memory consumption and could be viewed as a memory-efficient version of MTT [\[4\]](#page-8-8). Instead of matching gradients or parameters, recent works proposed to condense datasets by matching features [\[43,](#page-9-12) [52\]](#page-10-4).

DD has been applied in many down-stream tasks, including continual learning [\[52\]](#page-10-4), privacy-preserving learning [\[9\]](#page-8-11) and neural architecture search [\[38,](#page-9-13) [51\]](#page-10-0). Generally speaking, existing DD algorithms focus solely on improving the accuracy of models trained on the distilled data, regardless of trustworthiness and reliability.

### 2.2. Out-of-distribution Detection

OOD detection [\[13\]](#page-8-2) seeks to detect samples from novel classes by assigning a confidence score, *i.e*., an indicator of *normality*, for each sample. If the score is higher than a predefined threshold then such input is noted as in-distribution.

A closely-related area to OOD detection is Open Set Recognition [\[19,](#page-8-12) [41\]](#page-9-14), which focuses on known-class classification and unknown-class rejection. In this paper, we adopt the procedure of OOD detection for comprehensive evaluation.

Preceding works [\[13,](#page-8-2) [25,](#page-9-8) [27,](#page-9-1) [28\]](#page-9-9) of OOD detection concentrated on design suitable detection score function, *e.g*., Maximum Softmax Probability (MSP), Energy Score (Energy), Maximum Logit Score (MLS), to make InD and OOD samples more separable without interfering the training process. Following studies explored to learn better representations [\[15,](#page-8-13) [39\]](#page-9-15). Besides, Hendrycks *et al*. [\[14\]](#page-8-3) introduced Outlier Exposure (OE) to explicitly utilize an auxiliary outlier dataset. OE is a simple yet effective method to achieve SOTA OOD detection performance.

However, OOD detection has largely overlooked scenarios with limited data, and DD is mainly conducted in closed-world. In this paper, we improve the OOD detection performance of DD to make DD both efficient and trustworthy, which is more applicable in real open-world.

## 3. Background

### 3.1. Matching-based Dataset Distillation

Basic Notations. Let  $\mathcal{T} = \{ (x^i, y^i) \}$  $\left| \begin{array}{c} \n\sqrt{7} \\ \n\sqrt{1} \\ \n\end{array} \right|$  denote the whole large dataset, DD aims to distill the original dataset  $\mathcal{T}$  into a tiny yet informative dataset  $\mathcal{S} = \{ (s^i, y^i) \}$ |S| <sup>i</sup>=1 *i.e*.,  $|\mathcal{S}| \ll |\mathcal{T}|$  so that model trained on S could exhibit comparable performance with the one trained on  $\mathcal{T}$ . f denotes the neural network parameterized with  $\theta$  and  $f_{\theta}(\boldsymbol{x})$  is the output softmax probabilities on sample  $x$ .

Formulation. From the perspective of information theory, as in  $[26]$ , the general objective of DD is as follows:

<span id="page-2-0"></span>
$$
S^* = \arg\max_{S} I(\mathcal{T}; S | \tau)
$$
 (1)

where  $I(\mathcal{T}; \mathcal{S}|\tau)$  denotes the mutual information conditioned on the *task indicator random variable* τ. Maximizing the mutual information ensures the condensed dataset  $S^*$  contains as much information in  $T$  as possible to train models to implement task  $\tau$ . For existing DD works [\[4,](#page-8-8) [7,](#page-8-10) [26,](#page-9-16) [51,](#page-10-0) [52,](#page-10-4) [53\]](#page-10-1),  $\tau$  denotes InD classification task.

However, Eq. [\(1\)](#page-2-0) only provides a general and implicit criterion. To be more task-specific and make DD computational tractable, prior works  $[44, 53]$  $[44, 53]$  $[44, 53]$  derived Eq. [\(1\)](#page-2-0) into the loss function form as follows:

$$
S^* = \arg\min_{S} \mathcal{L}_{ce}^{\mathcal{T}}(\theta^S(S))
$$
  
s.t.  $\theta^S(S) = \arg\min_{\theta} \mathcal{L}_{ce}^S(\theta)$  (2)

<span id="page-2-1"></span>Eq. [\(2\)](#page-2-1) is a meta-learning framework requiring bi-level optimization. In the inner loop, one could optimize the network parameters  $\theta^{\mathcal{S}}$  on the distilled dataset  $\mathcal{S}$ , hence,  $\theta^{\mathcal{S}}$  is a function of  $S$ , while the outer loop learns to update  $S$  through minimizing the loss  $\mathcal{L}_{ce}^{\mathcal{T}}$  on the real dataset.  $\mathcal{L}_{ce}^{\mathcal{T}}$  and  $\mathcal{L}_{ce}^{\mathcal{S}}$  are cross-entropy function on  $T$  and  $S$  respectively.

Methods Review. We briefly review two parameters matching-based DD methods: DSA [\[51\]](#page-10-0) and MTT [\[4\]](#page-8-8). Zhao *et al.* [\[51,](#page-10-0) [53\]](#page-10-1) proposed to circumvent the bi-level optimization problem in Eq. [\(2\)](#page-2-1) by gradient matching. They contended that similar performance could be obtained by similar parameters, *i.e.*, making  $\theta^S \approx \theta^T$ . With the same initialization  $\theta_0^S$  and  $\theta_0^T$ , they further assumed that  $\theta_t^S$  =  $\theta_t^{\mathcal{T}} = \hat{\theta}_t$  could be achieved at each iteration t, and simplified Eq. [\(2\)](#page-2-1) into a gradient matching problem:

<span id="page-2-2"></span>
$$
\mathcal{L}_{distill}(\mathcal{S}) = D(\nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{S}), \hat{\theta}_t), \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{T}), \hat{\theta}_t))
$$
(3)

where  $A(\cdot)$  is the differentiable siamese augmentation. Cazenavette *et al*. [\[4\]](#page-8-8) introduced MTT to directly align model parameters through trajectories matching:

<span id="page-2-4"></span>
$$
\mathcal{L}_{\text{distill}}(\mathcal{S}) = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^{\star}\|_{2}^{2}}{\|\theta_{t}^{\star} - \theta_{t+M}^{\star}\|_{2}^{2}} \tag{4}
$$

where  $\theta_t^*$  and  $\hat{\theta}_t$  denote parameters trained on  $\mathcal{T}(M)$  updates in total) and  $S(N)$  updates in total) at time step t respectively, and  $N \ll M$ .

Here, we put both DSA  $[51]$  and MTT  $[4]$  into a unified framework. The parameter update process is as follows:

<span id="page-2-3"></span>
$$
\hat{\theta}_{i+1} = \hat{\theta}_i - \alpha \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{S}); \hat{\theta}_i)
$$
 (5)

In their cases, loss functions  $\mathcal L$  in Eq. [\(3\)](#page-2-2) and Eq. [\(5\)](#page-2-3) are typically cross-entropy classification loss  $\mathcal{L}_{ce}$ . From Eq. [\(5\)](#page-2-3) we can observe that both  $\hat{\theta}_t$  and  $\hat{\theta}_{t+N}$  in Eq. [\(3\)](#page-2-2) and Eq. [\(4\)](#page-2-4) are functions of S, as a result,  $\mathcal{L}_{distill}$  is also a function of  $S$  in both equations. One could optimize distillation loss  $\mathcal{L}_{distill}$  by gradient descent to learn distilled dataset  $\mathcal{S}$ :

$$
S^* = \arg\min_{S} \mathcal{L}_{distill}(\mathcal{S})
$$
 (6)

### 3.2. Outlier Exposure

Outlier Exposure (OE) [\[14\]](#page-8-3) utilizes an auxiliary outlier dataset to improve OOD detection as follows:

<span id="page-2-5"></span>
$$
\mathcal{L} = \mathbb{E}_{(\boldsymbol{x}, y) \sim \mathcal{D}_{\text{in}}} \mathcal{L}_{\text{ce}}(f_{\theta}(\boldsymbol{x}), y) + \lambda \mathbb{E}_{\boldsymbol{x}' \sim \mathcal{D}_{\text{out}}} H(\mathcal{U}; f_{\theta}(\boldsymbol{x}')) \tag{7}
$$

where  $\mathcal{L}_{ce}(f_{\theta}(x), y)$  is the cross-entropy loss for classification. For OOD detection, it is appropriate to minimize  $H(\mathcal{U}; f_{\theta}(\boldsymbol{x}'))$ , *i.e.*, the cross-entropy between a uniform distribution and output probabilities, namely maximize the output uncertainty of outliers, which could in turn implicitly make the model generalize to detect unseen OOD samples. Note that the auxiliary dataset  $\mathcal{D}_{out}$  should not overlap with test OOD datasets  $\mathcal{D}_{out}^{test}$  for fair evaluation.

Similar to DD, OE  $[14]$  is also from the perspective of data independent of model structure and training tricks. Besides, OE serves as the SOTA method in OOD detection.

## 4. Methodology

# 4.1. TrustDD Paradigm

To make DD trustworthy, we set *task indicator variable*  $\tau = (\tau_{\text{InD}}; \tau_{\text{OOD}})$  in Eq. [\(1\)](#page-2-0), taking both InD classification and OOD detection into consideration. We expect such trustworthy DD to generalize well across various network architectures and OOD detection scores. Hence, similar to DD, we derive *Trustworthy Dataset Distillation* (TrustDD) from the perspective of data and resort to OE [\[14\]](#page-8-3), *i.e*., utilizing outliers to boost OOD detection.

TrustDD Notations. We additionally consider auxiliary outliers like OE [\[14\]](#page-8-3) in TrustDD. Let  $\mathcal{T} = \mathcal{T}_{\text{in}} \bigcup \mathcal{T}_{\text{out}}$  denote the full dataset, where  $\mathcal{T}_{in} = \{(\mathbf{x}_{in}^i, y_{in}^i)\}\$  $\vert \frac{\vert \mathcal{T}_{\text{in}}\vert }{\vert \mathcal{T}_{\text{out}}\vert },\ \mathcal{T}_{\text{out}}\ =$  $\{\boldsymbol{x}^i_\mathsf{out}\} \big|$  $\frac{|\mathcal{T}_{out}|}{i=1}$  are in-distribution (InD) and out-of-distribution (OOD) dataset (with no labels), respectively. Here,  $x_{\text{in}}^i \in$  $\mathcal{X}_{\text{in}}, y_{\text{in}}^i \in \{0, 1, \cdots, C-1\}, x_{\text{out}}^i \in \mathcal{X}_{\text{out}}$ . We propose to distill both InD and outliers into  $S = S_{\text{in}} \bigcup S_{\text{out}}$  where  $\mathcal{S}_{\text{in}} = \{ (s_{\text{in}}^i, y_{\text{in}}^i) \}$  $\begin{array}{rcl} |{\mathcal{S}_{\text{in}}}| & \mathcal{S}_{\text{out}} = & \{s_{\text{out}}^i\} \end{array}$  $\left|\begin{array}{cc} |\mathcal{S}_{\text{out}}| \\ i=1 \end{array}\right|$  and  $|\mathcal{S}|$  =  $|\mathcal{S}_\text{in}| + |\mathcal{S}_\text{out}| \ll |\mathcal{T}| = |\mathcal{T}_\text{in}| + |\mathcal{T}_\text{out}|.$ 

TrustDD Formulation. Assume the true distribution of InD and OOD are  $P_{\text{in}}$  and  $P_{\text{out}}$  respectively.  $\mathcal{R} = \mathcal{R}_{\text{in}} +$  $\lambda \mathcal{R}_{out}$  denotes the integrated risk of InD classification risk and OOD detection risk, which could be rewritten as:

$$
\mathcal{R} = \mathbb{E}_{(\boldsymbol{x}, y) \sim P_{\text{in}}} [\mathbb{1}(\arg \max f_{\theta}(\boldsymbol{x}) \neq y)] \n+ \lambda \mathbb{E}_{(\boldsymbol{x}, y) \sim P_{\text{in}}} [\mathbb{1}(S(\boldsymbol{x}) < \delta)] \n+ \lambda \mathbb{E}_{\boldsymbol{x}' \sim P_{\text{out}}} [\mathbb{1}(S(\boldsymbol{x}') > \delta)]
$$
\n(8)

Here,  $\mathbb{1}(\cdot)$  is the indicator function and  $S(x)$  denotes the detection score function, when  $S(x)$  is larger than a pre-defined threshold  $\delta$ , x is referred to as positive (indistribution).  $\lambda$  is a trade-off between two tasks.

In order to learn and update  $S$ , similar to Eq. [\(2\)](#page-2-1), one can rewrite the risk in terms of the loss function. As a consequence, TrustDD paradigm could be written as follows:

$$
S^* = \arg\min_{S} \mathcal{L}^{\mathcal{T}}(\theta^S(S))
$$
  
s.t.  $\theta^S(S) = \arg\min_{\theta} \mathcal{L}^S(\theta)$  (9)

<span id="page-3-1"></span><span id="page-3-0"></span>where:

$$
\mathcal{L}^{\mathcal{T}}(\theta) = \frac{1}{|\mathcal{T}_{\text{in}}|} \sum_{(\boldsymbol{x}, y) \in \mathcal{T}_{\text{in}}} \mathcal{L}_{ce}(f_{\theta}(\boldsymbol{x}), y) \\ + \lambda \frac{1}{|\mathcal{T}_{\text{out}}|} \sum_{\boldsymbol{x}' \in \mathcal{T}_{\text{out}}} H(\mathcal{U}; f_{\theta}(\boldsymbol{x}')) \hfill (10)
$$

<span id="page-3-2"></span>
$$
\mathcal{L}^{S}(\theta) = \frac{1}{|\mathcal{S}_{\text{in}}|} \sum_{(\mathbf{s}, y) \in \mathcal{S}_{\text{in}}} \mathcal{L}_{ce}(f_{\theta}(\mathbf{s}), y) + \lambda \frac{1}{|\mathcal{S}_{\text{out}}|} \sum_{\mathbf{s}' \in \mathcal{S}_{\text{out}}} H(\mathcal{U}; f_{\theta}(\mathbf{s}')) \tag{11}
$$

 $\mathcal{L}^{\mathcal{T}}$  and  $\mathcal{L}^{\mathcal{S}}$  are loss functions on  $\mathcal{T}$  and  $\mathcal{S}$  respectively. Intu-itively, Eq. [\(9\)](#page-3-0) ensures model  $\theta^{\mathcal{S}}$  trained on  $\mathcal{S}$  could perform well on  $\mathcal T$  considering both InD and OOD tasks, as a result, we distill  $T$  into a tiny yet informative dataset  $S^*$ .

In fact, the formulation of TrustDD in Eq.  $(9)$  is the same as Eq. [\(2\)](#page-2-1) when  $\lambda = 0$ . In the case of TrustDD,  $\lambda > 0$ , which means that we consider both InD classification and OOD detection, while prior DD works [\[44,](#page-9-4) [51,](#page-10-0) [53\]](#page-10-1) only consider the first term in Eq.  $(10)$  and Eq.  $(11)$ .

<span id="page-3-4"></span>

### 4.2. Pseudo-Outlier Exposure

**Motivation.** OE  $[14]$  has a clear limitation as it relies on curated auxiliary data. Nevertheless, it is not always practical to collect such outliers. Besides, OE [\[14\]](#page-8-3) is highly sensitive to the outlier data, as seen in Table [1.](#page-3-3) When choosing random noises or a specific OOD dataset as auxiliary outliers, the performance gain brought by OE is very subtle. As a result, employing a curated outlier dataset (*e.g*., 300K Random Images [\[14\]](#page-8-3)) is essential for OE. To avoid the dependence on real outlier data and make OOD detection more applicable, we propose to synthesize *pseudo-outliers* from in-distribution data, *i.e*., the original dataset. We argue that in-distribution corruption is effective to generate a large number of *pseudo-outliers*, which can be used to maximize the output uncertainty in Eq.  $(7)$ . We refer to this method as *Pseudo-Outlier Exposure* (POE). POE is more practical and applicable than OE without the requirement of real outliers.

<span id="page-3-3"></span>Table 1: OOD performance of models trained with IPC=10 when applying different auxiliary outliers in OE [\[14\]](#page-8-3). By default, OE [\[14\]](#page-8-3) adopts 300K Random Images (300K).

| Outlier Data | None  | <b>Gauss</b> | Uniform | <b>SVHN</b> | 300K  |
|--------------|-------|--------------|---------|-------------|-------|
| AUROC.       | 63.36 | 66.81        | 66.71   | 66.33       | 70.12 |
| AUPR-IN      | 67.45 | 70.59        | 70.13   | 69.90       | 73.51 |

Corruption Transformations. OOD samples refer to the ones exhibiting semantic shifts, namely belonging to categories outside of the training data. To synthesize such outliers, one should ensure that corruption leads to a noticeable semantic shift. If the corruption is semantic-preserving, then *pseudo-outliers* are also in-distribution samples, minimizing  $H(\mathcal{U}; f_{\theta}(\boldsymbol{x}))$  in Eq. [\(7\)](#page-2-5) could degrade the InD classification performance, which leads to bad OOD detection  $[41]$ . For natural scene images like CIFAR  $[21]$  and ImageNet [\[8\]](#page-8-0), we mainly perform four corruption transformations: jigsaw, invert, mosaic and speckle. An example of *pseudo-outliers* is shown in Figure [3.](#page-4-0)

Regarding the specific operations, jigsaw divides InD images  $x_{\text{in}} \in \mathbb{R}^{C \times H \times W}$  into 6 ~ 8 patches and shuffle them to generate  $x_{\text{out}}$ . invert means channel-wise inversion on certain channels, *i.e.*,  $x_{\text{out}}[C, :, :] = 1 - x_{\text{in}}[C, :, :]$ . mosaic blurs  $x_{\text{in}}$  to make it unrecognizable. speckle corrupts the input in a pixel-wise manner, *i.e.*,  $x_{\text{out}}$  =  $x_{\text{in}} + x_{\text{in}} \times \text{rand\_like}(x_{\text{in}})$  and clip it to range [0, 1].

<span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays a grid of 20 smaller images, arranged in 4 rows and 5 columns. The first column shows two airplanes and two cars. The second column shows two airplanes and two cars, with some images appearing to be partially reconstructed or fragmented. The third column presents stylized, colorful versions of airplanes and cars. The fourth column contains pixelated representations of airplanes and cars. The fifth column is filled with highly noisy, colorful images that are difficult to discern.

original jigsaw invert mosaic speckle Figure 3: Visualization of InD corruption to synthesize *pseudo-outliers* on CIFAR10 [\[21\]](#page-9-17). The corruption transformations are: jigsaw, invert, mosaic and speckle.

<span id="page-4-1"></span>

#### Algorithm 1 Trustworthy Dataset Distillation (TrustDD)

**Input:** Original dataset  $\mathcal{T}_{in}$ .

- **Input:** Corruption function  $C(\cdot, \cdot)$  with transformation set  $\Omega = \{$  jigsaw, invert, mosaic, speckle,  $\cdots$ , differentiable augmentation function  $\mathcal{A}(\cdot)$ .
- **Input:** Network learning rate  $\alpha_1$ , distilled image learning rate  $\alpha_2$ .
- **Input:** Number of network updates N and image updates  $N<sub>S</sub>$  in each iteration.
- **Input:** Integrated loss  $\mathcal{L}^{\mathcal{T}}(\theta)$  in Eq. [\(10\)](#page-3-1) and  $\mathcal{L}^{\mathcal{S}}(\theta)$  in Eq. [\(11\)](#page-3-2), distillation loss  $\mathcal{L}_{distill}(\mathcal{S})$ . (like Eq. [\(3\)](#page-2-2) and Eq. [\(4\)](#page-2-4))
- 1:  $\triangleright$  Corrupt  $\mathcal{T}_{in}$  to generate pseudo-outlier dataset:
- 2:  $\mathcal{T}_{\text{out}} = \mathcal{C}(\mathcal{T}_{\text{in}}, \Omega)$
- 3: ▷ Initialize distilled dataset:
- 4:  $S_{\text{in}} \sim \mathcal{T}_{\text{in}}$ ,  $S_{\text{out}} \sim \mathcal{T}_{\text{out}}$  and  $S = S_{\text{in}} \bigcup \mathcal{S}_{\text{out}}$
- 5: while not converged do
- 6:  $\triangleright$  Initialize network parameters:  $\hat{\theta}_t$  (obtained from the previous iteration or randomly sampled)
- 7:  $\triangleright$  Update network parameters N times:
- 8:  $\hat{\theta}_{i+1} = \hat{\theta}_i \alpha_1 \nabla_{\theta} \mathcal{L}^{\mathcal{S}}(\hat{\theta}_i)$
- 9:  $\triangleright$  Compute distillation loss  $\mathcal{L}_{distill}(\mathcal{S})$  on  $\hat{\theta}_{t+N}$
- 10:  $\triangleright$  Update distilled images  $N_{\mathcal{S}}$  times:
- 11:  $S_{\text{in}} \leftarrow S_{\text{in}} \alpha_2 \nabla_{S_{\text{in}}} \mathcal{L}_{\text{distill}}(\mathcal{S})$
- 12:  $S_{\text{out}} \leftarrow S_{\text{out}} \alpha_2 \nabla_{S_{\text{out}}} \mathcal{L}_{\text{distill}}(\mathcal{S})$

```
13: end while
```

**Output:** Distilled dataset  $S^* = S_{\text{in}}^* \bigcup S_{\text{out}}^*$ 

# 4.3. Overall Learning Framework

Based on the TrustDD paradigm and POE, we can derive the overall learning framework of TrustDD in Algorithm [1.](#page-4-1)

Firstly, we corrupt InD data to generate pseudo outliers  $\mathcal{T}_{out} = \mathcal{C}(\mathcal{T}_{in}, \Omega)$ . By simply substituting the single cross-entropy loss  $\mathcal{L}_{ce}$  with *integrated loss* and distilling both InD and generated outliers, we could upgrade ordinary DD to TrustDD. For instance, one can simply add a loss term  $H(U; f_{\theta}(S_{out}))$  to  $\mathcal{L}_{ce}$  in Eq. [\(3\)](#page-2-2) and Eq. [\(5\)](#page-2-3) so as to make DSA  $[51]$  and MTT  $[4]$  trustworthy and reliable. For DSA [\[51\]](#page-10-0), Line [6](#page-4-1) initializes  $\hat{\theta}_t$  from the previous itera-tion while for MTT [\[4\]](#page-8-8) sampled from expert parameters  $\theta_t^*$ . Note that we have implemented *integrated loss* twice,  $\mathcal{L}^{\mathcal{T}}$ (Eq.  $(10)$ ) is to compute  $\mathcal{L}_{distill}$  in Line [9](#page-4-1) and further update distilled images in Line [11](#page-4-1) and Line [12,](#page-4-1)  $\mathcal{L}^{\mathcal{S}}$  (Eq. [\(11\)](#page-3-2)) is to update the network in Line [8.](#page-4-1)

In general, TrustDD could be seamlessly built upon most matching-based DD methods [\[4,](#page-8-8) [7,](#page-8-10) [26,](#page-9-16) [51,](#page-10-0) [53\]](#page-10-1) by applying corresponding  $\mathcal{L}_{distill}(\mathcal{S})$  in Algorithm [1.](#page-4-1) Besides, TrustDD

<span id="page-4-2"></span>

| Table 2: In-distribution datasets $\mathcal{D}_{in}$ for InD classification      |                                                                                                  |  | InD datasets $\mathcal{D}_{\text{in}}$ | OOD datasets $\mathcal{D}_{\text{out}}^{\text{test}}$                   |
|----------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|--|----------------------------------------|-------------------------------------------------------------------------|
| and corresponding test out-of-distribution datasets $\mathcal{D}_{out}^{test}$ . |                                                                                                  |  | CIFAR [21]                             | Texture [5], Places365 [54],<br>Tiny ImageNet [6], LSUN [49], iSUN [47] |
| ImageNet Subsets [4, 10]                                                         | Texture [5], Species [12], iNaturalist [40],<br>ImageNet-O [16], OpenImage-O [42]                |  |                                        |                                                                         |
| MNIST [23]                                                                       | Texture [5], Places365 [54], Tiny ImageNet [6],<br>notMNIST [3], FashionMNIST [46], CIFAR10 [21] |  |                                        |                                                                         |
| SVHN [31]                                                                        | Texture [5], Places365 [54], Tiny ImageNet [6],<br>LSUN [49], iSUN [47], CIFAR10 [21]            |  |                                        |                                                                         |

could be equipped with OE or POE, by choosing real outliers or *pseudo-outliers* as  $\mathcal{T}_{\text{out}}$ .

## 5. Experiments

<span id="page-4-3"></span>

### 5.1. Experimental Setup

Datasets. Following [\[4,](#page-8-8) [51\]](#page-10-0), we evaluate TrustDD on various datasets, including natural scene datasets (CI-FAR [\[21\]](#page-9-17) and ImageNet Subsets [\[4,](#page-8-8) [10\]](#page-8-15)) and digit datasets (MNIST  $[23]$  and SVHN  $[31]$ ). The corresponding test datasets for OOD detection [\[48\]](#page-10-7) are shown in Table [2.](#page-4-2)  $\mathcal{D}_{in}$ and  $\mathcal{D}_{out}^{\text{test}}$  do not have any category intersections. By default, the size ratio of test InD and OOD dataset is kept as 1 : 1.

**Metrics.** For OOD detection  $[13]$ , we use the common metrics: (1) *FPR95*: False Positive Rate when True Positive Rate is at 95%. (2) *AUROC*: Area Under the Receiver Operating Characteristic curve. (3) *AUPR-IN* and *AUPR-OUT*: Area under the Precision-Recall curve where InD and OOD samples are regarded as positives respectively.

By default, we adopt Maximum Softmax Probabilities (MSP) [\[13\]](#page-8-2) as the OOD detection score in our experiments. Other confidence scores can be easily applied in our method which will be shown in Section [5.2.](#page-5-0)

Training and Evaluation Procedure. The training and evaluation include three steps. (1) Distill the whole dataset into a small informative dataset, *i.e*., dataset distillation. By default, we fix the distilled InD size  $|S_{in}| = \text{\#class} \times$ IPC, for baseline, we set  $|S_{out}| = 0$ , *i.e.* without outlier distillation, while for TrustDD on OE and POE, we keep  $|\mathcal{S}_{out}| = |\mathcal{S}_{in}|$ . We also compare the baseline with OE and POE with the same total distilled size  $|S|$  in Section [5.3.](#page-7-0) (2) Train models on the distilled dataset. (3) Evaluate the trained models on real InD datasets and real test OOD datasets. OOD detection results on gaussian and uniform noises are only reported in Table [3](#page-6-0) and Table [4,](#page-6-1) for other OOD-related results, we report the average over  $\mathcal{D}_{out}^{\text{test}}$ datasets in Table [2.](#page-4-2) In all experiments, we distill the dataset 3 times, 5 models are trained on each distilled dataset, and the average of all 15 evaluations is reported.

DD Frameworks. In this paper, we mainly implement TrustDD on two well-known algorithms: DSA [\[51\]](#page-10-0) and MTT [\[4\]](#page-8-8). When the number of categories or Image Per Class (IPC) is high, we resort to TESLA [\[7\]](#page-8-10), a memoryefficient version of MTT. To be more specific, for CIFAR,

we build TrustDD upon DSA and TESLA with IPC=10, we also explore IPC=1 and IPC=50 in Section [5.4.](#page-7-1) For digit datasets (MNIST and SVHN), we implement DSA with IPC=10. For ImageNet subsets, we employ MTT with IPC=1. Besides, TrustDD could be implemented with both OE and POE. For OE, we instantiate  $\mathcal{T}_{out}$  with 300K Ran-dom Images following [\[14\]](#page-8-3), for POE, we generate  $\mathcal{T}_{out}$  via ensemble of InD corruption as Line [2](#page-4-1) in Algorithm [1.](#page-4-1)

Network Architectures and Hyper-parameters. We mainly implement TrustDD on ConvNet, which is default and canonical in the literature of DD [\[4,](#page-8-8) [7,](#page-8-10) [51\]](#page-10-0). For ImageNet subsets, we employ ConvNets with 5 convolutional layers, while 3 for the remaining datasets. For the trade-off weight, we simply set  $\lambda = 0.5$  in all experiments as in [\[14\]](#page-8-3). Other DD algorithm-related hyper-parameters are directly adopted from the corresponding papers [\[4,](#page-8-8) [7,](#page-8-10) [51\]](#page-10-0). For fair comparison among DD methods, we do not employ ZCA whitening for pre-processing in MTT [\[4\]](#page-8-8) and TESLA [\[7\]](#page-8-10), so the InD classification might degrade slightly compared with their official results, however, this does not hinder demonstrating the effectiveness of TrustDD through experiments.

<span id="page-5-0"></span>

### 5.2. Main Results

Ordinary DD performs poorly on OOD detection. Previous DD works [\[4,](#page-8-8) [7,](#page-8-10) [51\]](#page-10-0) are confined to a closed world setting, serving as the baseline with very weak OOD detection performance. For instance, as shown in Figure [2,](#page-1-0) the model trained on dataset condensed from ordinary DD obtains 91.62% FPR95, *i.e*., misclassifies 91.62% negative (OOD) samples as InD when the true positive rate is 95%, while TrustDD reduces such error by  $\sim 16.13\%$ .

Is OOD knowledge distillable? Results in Table [3](#page-6-0) and Table [4](#page-6-1) validate the effectiveness of OE [\[14\]](#page-8-3) in limited-data scenarios, *i.e*. DD settings. For instance, TrustDD (with OE) increases the AUROC metric by  $\sim 35.80\%$  when InD and OOD datasets are CIFAR100 and gaussian noise, re-spectively. Table [3](#page-6-0) and Table [4](#page-6-1) illustrate consistent improvements of TrustDD (with OE) over DD. So the answer is yes. TrustDD could not only distill knowledge from InD samples, but also from OOD data. Figure [5b](#page-7-2) further demonstrates that the distilled outliers could be informative.

POE even surpasses the SOTA OE. The main advantage of POE is that it does not require real outliers. With fewer efforts to carefully collect outlier data compared with OE, POE even surpasses OE for OOD detection as shown in Table [3](#page-6-0) and Table [4.](#page-6-1) POE significantly improves models' ability to detect OOD samples, especially for random noises. A consistent improvement could be witnessed from OE to POE across various test datasets. Considering models trained on CIFAR100 with TESLA to detect uniform noises, compared to OE, POE shows ∼ 44.00% increase in AUROC and  $\sim 21.36\%$  gains averaged on all 7 test OOD

<span id="page-5-1"></span>Image /page/5/Figure/6 description: This image contains two bar charts side-by-side, both plotting accuracy on the y-axis against datasets on the x-axis. The left chart is labeled "DSA" and shows results for "CIFAR 10" and "CIFAR 100". For "CIFAR 10", the "baseline" accuracy is approximately 52, "OE" is approximately 50, and "POE" is approximately 52. For "CIFAR 100", the "baseline" accuracy is approximately 32, "OE" is approximately 32, and "POE" is approximately 32. The right chart is labeled "TESLA" and also shows results for "CIFAR 10" and "CIFAR 100". For "CIFAR 10", the "baseline" accuracy is approximately 55, "OE" is approximately 58, and "POE" is approximately 58. For "CIFAR 100", the "baseline" accuracy is approximately 29, "OE" is approximately 30, and "POE" is approximately 31. Both charts include a legend indicating "baseline" (red), "OE" (teal), and "POE" (beige).

Figure 4: In-distribution accuracy  $(\%)$  of model trained on distilled data on CIFAR10 and CIFAR100.

datasets. Thus, POE could serve as a strong baseline for future research.

Why does POE work? Despite not requiring explicit and curated outlier datasets, POE still outperforms OE in many cases as in Table [3](#page-6-0) and Table [4.](#page-6-1) We argue that *pseudooutliers* are near-ood samples, with appropriate semantic distance to efficiently enhance OOD performance. Once learned to reject such difficult outliers, models are expected to generalize well to detect various OOD samples across the open space [\[24\]](#page-9-24). Furthermore, POE not only works in the DD setting, but also performs admirably in full dataset scenarios, *i.e*., traditional OOD settings. Please refer to the Appendix for more details.

Besides, we also evaluate InD performance on CIFAR as in Figure [4.](#page-5-1) TrustDD with both OE and POE boosts OOD performance without the loss of InD classification.

Digit Datasets and ImageNet Subsets. For digit datasets: MNIST [\[23\]](#page-9-21) and SVHN [\[31\]](#page-9-23), we implement TrustDD on DSA with IPC=10. Results are shown in Table [5.](#page-6-2) In this setting, the *pseudo-outliers* contain very narrow semantic information compared with natural image scenarios (*e.g*., POE in CIFAR) due to the limited semantic range of InD dataset, while OE employs a wide range of outlier semantics, leading to better OOD performance.

For higher resolution datasets, we carry out experiments on ImageNet [\[8\]](#page-8-0), due to the huge memoryconsumption issue of DD, similar to  $[4]$ , we mainly implement TrustDD with IPC=1 on ImageNet Subsets including ImageNette [\[10\]](#page-8-15) and ImageFruit [\[4\]](#page-8-8), we also randomly sample another subset, and name it ImageMisc, results are shown in Table [6.](#page-6-3) Both OE and POE improve OOD detection by a large margin. POE outperforms OE in ImageNette and ImageFruit but underperforms OE in ImageMisc, although in all 3 subsets, the performance gap is negligible. When  $|S_{\text{out}}|$  is small, the advantage of POE over OE is relatively subtle, more details are discussed in Section. [5.4.](#page-7-1)

TrustDD and other OOD scores are complementary. To test the generalization of TrustDD across different OOD scores, we also evaluate two well-known detection scores: Maximum Logit Score (MLS) [\[41\]](#page-9-14) and Energy Score (Energy) [\[28\]](#page-9-9) in addition to MSP [\[13\]](#page-8-2). Here we use DSA with IPC=10, results in Table [7](#page-6-4) show consistent improvements in TrustDD. Furthermore, switching the score from MSP to

<span id="page-6-0"></span>

| $\mathcal{D}_{in}$ | $\mathcal{D}_\text{out}^\text{test}$ | FPR95 $\downarrow$ |       |            | AUROC ↑  |       |            | AUPR-IN $\uparrow$ |       |            | AUPR-OUT $\uparrow$ |       |            |
|--------------------|--------------------------------------|--------------------|-------|------------|----------|-------|------------|--------------------|-------|------------|---------------------|-------|------------|
|                    |                                      | baseline           | OE    | POE (ours) | baseline | OE    | POE (ours) | baseline           | OE    | POE (ours) | baseline            | OE    | POE (ours) |
| CIFAR10            | Gaussian                             | 68.62              | 69.59 | 28.70      | 83.82    | 85.37 | 95.30      | 86.40              | 88.07 | 96.03      | 80.75               | 81.45 | 94.52      |
|                    | Uniform                              | 68.70              | 70.24 | 29.53      | 83.66    | 85.33 | 95.17      | 86.23              | 88.05 | 95.93      | 86.60               | 81.28 | 94.38      |
|                    | Texture                              | 91.74              | 81.91 | 81.10      | 61.73    | 73.73 | 72.85      | 75.39              | 83.92 | 83.00      | 44.08               | 58.51 | 58.13      |
|                    | Places365                            | 90.44              | 86.93 | 86.79      | 62.98    | 68.82 | 68.62      | 64.58              | 69.86 | 70.44      | 59.88               | 65.60 | 65.46      |
|                    | Tiny ImageNet                        | 90.17              | 87.44 | 82.46      | 63.44    | 68.98 | 71.82      | 64.83              | 70.79 | 72.74      | 60.35               | 65.36 | 69.49      |
|                    | <b>LSUN</b>                          | 87.65              | 84.13 | 80.21      | 66.85    | 73.56 | 75.12      | 69.17              | 75.61 | 76.73      | 63.65               | 69.74 | 72.24      |
|                    | iSUN                                 | 88.61              | 85.66 | 80.67      | 65.28    | 71.42 | 74.10      | 69.62              | 75.05 | 77.23      | 59.51               | 65.28 | 69.20      |
|                    | <b>mean</b>                          | 83.70              | 80.84 | 67.07      | 69.68    | 75.31 | 79.00      | 73.75              | 78.76 | 81.73      | 64.97               | 69.60 | 74.77      |
| CIFAR100           | Gaussian                             | 98.84              | 88.90 | 1.82       | 36.77    | 72.57 | 99.56      | 49.41              | 77.81 | 99.60      | 41.36               | 65.64 | 99.53      |
|                    | Uniform                              | 99.01              | 89.66 | 1.99       | 36.22    | 71.51 | 99.52      | 49.11              | 77.04 | 99.56      | 41.07               | 64.42 | 99.49      |
|                    | Texture                              | 96.55              | 83.05 | 78.63      | 47.30    | 63.89 | 70.34      | 63.68              | 75.41 | 80.42      | 33.64               | 52.53 | 58.77      |
|                    | Places365                            | 91.91              | 87.28 | 89.02      | 59.33    | 63.94 | 63.18      | 60.08              | 63.72 | 63.62      | 57.19               | 62.31 | 61.05      |
|                    | Tiny ImageNet                        | 91.79              | 87.87 | 71.45      | 61.67    | 66.95 | 75.93      | 64.23              | 68.93 | 75.95      | 58.51               | 63.42 | 76.18      |
|                    | <b>LSUN</b>                          | 91.16              | 83.19 | 69.46      | 62.87    | 71.47 | 77.83      | 65.71              | 72.66 | 77.66      | 59.61               | 68.49 | 77.60      |
|                    | iSUN                                 | 91.66              | 85.70 | 68.93      | 61.50    | 68.01 | 76.01      | 66.02              | 71.13 | 76.77      | 55.85               | 62.77 | 75.09      |
|                    | <b>mean</b>                          | 94.42              | 86.52 | 54.47      | 52.24    | 68.33 | 80.34      | 59.75              | 72.39 | 81.94      | 49.60               | 62.80 | 78.24      |

Table 3: OOD detection performance of models trained on DSA distilled dataset on CIFAR, with IPC=10.

Table 4: OOD detection performance of models trained on TESLA distilled dataset on CIFAR, with IPC=10.

<span id="page-6-1"></span>

| $\mathcal{D}_{in}$ | $\mathcal{D}_\text{out}^\text{test}$ |          | FPR95 $\downarrow$ |            |          | AUROC ↑ |            |          | AUPR-IN $\uparrow$ |            |          | AUPR-OUT 1 |            |
|--------------------|--------------------------------------|----------|--------------------|------------|----------|---------|------------|----------|--------------------|------------|----------|------------|------------|
|                    |                                      | baseline | <b>OE</b>          | POE (ours) | baseline | OE      | POE (ours) | baseline | OE                 | POE (ours) | baseline | <b>OE</b>  | POE (ours) |
|                    | Gaussian                             | 99.58    | 97.79              | 22.33      | 38.40    | 54.10   | 96.20      | 51.63    | 64.23              | 96.79      | 40.72    | 49.39      | 95.52      |
|                    | Uniform                              | 99.61    | 98.08              | 22.93      | 37.48    | 53.03   | 96.09      | 51.03    | 63.48              | 96.68      | 40.33    | 48.61      | 95.43      |
|                    | Texture                              | 91.23    | 84.64              | 83.99      | 61.97    | 67.42   | 70.10      | 75.14    | 78.12              | 80.99      | 44.96    | 52.74      | 54.45      |
| CIFAR10            | Places 365                           | 89.32    | 86.32              | 87.04      | 63.66    | 69.68   | 67.51      | 64.62    | 71.28              | 68.75      | 61.13    | 66.12      | 64.41      |
|                    | Tiny ImageNet                        | 89.04    | 87.84              | 82.52      | 64.88    | 68.35   | 73.01      | 66.81    | 70.69              | 74.56      | 61.85    | 64.49      | 69.76      |
|                    | LSUN                                 | 87.18    | 85.28              | 81.95      | 67.17    | 71.55   | 74.44      | 69.01    | 74.01              | 76.47      | 64.38    | 67.57      | 70.72      |
|                    | iSUN                                 | 88.12    | 87.01              | 82.19      | 65.79    | 69.50   | 73.10      | 69.71    | 73.92              | 76.54      | 60.35    | 62.97      | 67.52      |
|                    | mean                                 | 92.01    | 89.57              | 66.14      | 57.05    | 64.80   | 78.64      | 63.99    | 70.82              | 81.54      | 53.39    | 58.84      | 73.97      |
|                    | Gaussian                             | 99.93    | 99.59              | 0.00       | 35.87    | 56.92   | 100.0      | 48.06    | 67.29              | 100.0      | 39.28    | 49.30      | 100.0      |
|                    | Uniform                              | 99.93    | 99.74              | 0.00       | 35.55    | 56.00   | 100.0      | 48.04    | 66.67              | 100.0      | 39.14    | 48.68      | 100.0      |
|                    | Texture                              | 96.10    | 88.98              | 74.67      | 46.42    | 61.59   | 74.00      | 61.42    | 75.04              | 82.90      | 33.64    | 46.68      | 63.21      |
| CIFAR100           | Places 365                           | 92.67    | 88.82              | 87.78      | 58.92    | 64.76   | 66.30      | 60.39    | 66.22              | 67.25      | 56.26    | 62.03      | 63.54      |
|                    | Tiny ImageNet                        | 93.70    | 93.10              | 68.71      | 57.88    | 62.22   | 78.43      | 60.53    | 66.09              | 78.21      | 54.86    | 57.66      | 78.34      |
|                    | <b>LSUN</b>                          | 94.17    | 93.79              | 71.29      | 57.91    | 62.59   | 78.64      | 60.96    | 67.28              | 79.44      | 54.47    | 57.31      | 77.40      |
|                    | iSUN                                 | 94.32    | 94.30              | 70.00      | 57.02    | 60.90   | 77.12      | 62.43    | 67.73              | 78.27      | 51.11    | 53.21      | 75.38      |
|                    | mean                                 | 95.83    | 94.05              | 53.21      | 49.94    | 60.71   | 82.07      | 57.40    | 68.05              | 83.72      | 46.97    | 53.55      | 79.70      |

<span id="page-6-2"></span>Table 5: InD classification and OOD detection performance on digit datasets: MNIST and SVHN, with IPC=10.

| $Din$ | AUROC    |              |            | AUPR-IN  |              |            | Accuracy |              |            |
|-------|----------|--------------|------------|----------|--------------|------------|----------|--------------|------------|
|       | baseline | OE           | POE (ours) | baseline | OE           | POE (ours) | baseline | OE           | POE (ours) |
| MNIST | 95.70    | <b>99.71</b> | 99.25      | 96.77    | <b>99.75</b> | 99.45      | 97.91    | <b>98.11</b> | 98.08      |
| SVHN  | 84.13    | <b>96.75</b> | 95.52      | 93.19    | <b>98.57</b> | 97.97      | 79.52    | <b>78.04</b> | 79.24      |

<span id="page-6-3"></span>Table 6: InD classification and OOD detection performance on ImageNet Subsets, with IPC=1.

| $\mathcal{D_{\text{in}}}$ | AUROC    |              |              | AUPR-IN  |       |              | Accuracy |              |              |
|---------------------------|----------|--------------|--------------|----------|-------|--------------|----------|--------------|--------------|
|                           | baseline | OE           | POE (ours)   | baseline | OE    | POE (ours)   | baseline | OE           | POE (ours)   |
| ImageNette                | 59.52    | 65.35        | <b>67.23</b> | 73.68    | 79.49 | <b>80.09</b> | 47.88    | <b>57.00</b> | 55.72        |
| ImageFruit                | 51.18    | 56.90        | <b>57.14</b> | 67.37    | 72.05 | <b>72.21</b> | 28.16    | 30.96        | <b>32.16</b> |
| ImageMisc                 | 58.88    | <b>64.44</b> | 64.01        | 75.52    | 79.36 | 78.66        | 43.88    | <b>48.68</b> | 47.12        |

Energy results in a greater improvement under the POE condition ( $\sim 8.95\%$ ) compared to baseline ( $\sim 1.10\%$ ) and OE  $({\sim 1.70\%})$ . TrustDD (with POE) and score functions are from different perspectives, combining TrustDD with suitable scores could enhance OOD performance by a larger margin than ordinary DD and OE.

Cross-Architecture Generalization. TrustDD is from the data perspective, here we validate its generalization abilities across different backbones. We implement DSA on

<span id="page-6-4"></span>Table 7: OOD detection performance on various scores. Mean AUROC is reported.

| dataset  | MSP      |       |              | MLS      |       |              | Energy   |       |              |
|----------|----------|-------|--------------|----------|-------|--------------|----------|-------|--------------|
|          | baseline | OE    | POE(ours)    | baseline | OE    | POE(ours)    | baseline | OE    | POE(ours)    |
| CIFAR10  | 64.06    | 71.30 | <b>72.50</b> | 65.39    | 73.00 | <b>78.32</b> | 65.16    | 73.00 | <b>81.45</b> |
| CIFAR100 | 58.53    | 66.85 | <b>72.66</b> | 60.58    | 70.30 | <b>76.44</b> | 60.65    | 71.52 | <b>77.74</b> |

CIFAR10 with IPC=50. Images are distilled on ConvNet, and used to train ConvNet, AlexNet [\[22\]](#page-9-25), VGG-11 [\[37\]](#page-9-26) and ResNet-18 [\[11\]](#page-8-19) to test InD and OOD performance. Re-sults in Table [8](#page-6-5) show TrustDD could generalize well to unseen model architectures, and POE outperforms OE consistently. Interestingly, when transferring to unseen backbones, InD classification degrades all the time, while OOD performance for OE and POE even increases.

<span id="page-6-5"></span>Table 8: Test performance on various network architectures. Results are shown in the form of baseline/OE/POE.

| Metric   | ConvNet                   | AlexNet                   | VGG                        | ResNet                    |
|----------|---------------------------|---------------------------|----------------------------|---------------------------|
| AUROC    | 66.08/73.74/ <b>76.75</b> | 61.05/79.82/ <b>89.54</b> | 58.37/79.30/ <b>88.65</b>  | 61.30/77.08/ <b>81.13</b> |
| AUPR-IN  | 71.24/77.51/ <b>80.16</b> | 64.99/81.18/ <b>90.05</b> | 62.61/80.37/ <b>89.08</b>  | 65.98/77.85/ <b>80.52</b> |
| Accuracy | <b>60.55</b> /59.01/60.22 | 56.23/56.88/ <b>58.36</b> | 55.02/ <b>58.08</b> /56.29 | 50.00/50.44/ <b>51.25</b> |

<span id="page-7-2"></span>Image /page/7/Figure/0 description: This image contains two bar charts. The left chart, labeled (a), shows the AUROC performance for different distilled sizes (20, 100, and 200) using three methods: all InD, InD + OE, and InD + POE. The right chart, labeled (b), compares the AUROC performance of four methods (OE-R, OE-D, POE-R, POE-D) on two datasets: DSA and TESLA. The y-axis for both charts represents AUROC, ranging from 50 to 80.

(a) The necessity of distilling  $S_{\text{out}}$ . (b) Distilled  $S_{\text{out}}$  is informative. Figure 5: The rationale of TrustDD. (a). OOD detection performance of distilling only InD and distilling both InD and OOD to same size  $|\mathcal{S}|$ . All InD:  $|\mathcal{S}_{in}| = |\mathcal{S}|, |\mathcal{S}_{out}| = 0$ . InD+OE and InD+POE:  $|\mathcal{S}_{in}| = |\mathcal{S}_{out}| = \frac{1}{2} |\mathcal{S}|$ . (b). OOD detection performance of OE-R, OE-D, POE-R and POE-D, where '-R' denotes 'randomly selected' outliers while '-D' denotes 'distilled' outliers via TrustDD.

<span id="page-7-0"></span>

### 5.3. Further Analysis

Why not simply distill more InD data? Vaze *et al*. [\[41\]](#page-9-14) found a positive correlation between ID and OOD performance. It is natural to consider simply increasing the size of  $S_{\text{in}}$  (*i.e.*, without explicitly distilling outliers like TrustDD) to train better classifiers, which are also better OOD detectors, however, Figure [5a](#page-7-2) shows that it is ineffective to improve OOD detection by simply distill more InD images. When raising  $|\mathcal{S}|$  from 100 to 200, AUROC increases only  $\sim 1.23\%$ , but  $\sim 3.54\%$  for TrustDD on our proposed POE.

Distilled outliers and random outliers. TrustDD not only distills informative InD samples, but also informative outliers. By replacing the distilled outliers with randomly selected outliers, the performance degrades significantly. Figure [5b](#page-7-2) validates the effectiveness of outlier distillation.

Why not distill both InD and outliers into InD? There is a more efficient way by distilling both  $\mathcal{T}_{in}$  and  $\mathcal{T}_{out}$  together into a single set  $S_{single}$  and make models trained on Ssingle competent in both InD and OOD tasks, *i.e*., leave out the second term in Eq. [\(11\)](#page-3-2) and make  $S_{single} = S$ . Nevertheless, distilling both  $\mathcal{T}_{in}$  and  $\mathcal{T}_{out}$  together into  $\mathcal{S}_{single}$  will decrease the information content of  $\mathcal{T}_{in}$  in S to save room for  $\mathcal{T}_{\text{out}}$ , leading to degraded classification performance, which could in turn result in poor OOD performance [\[41\]](#page-9-14), even worse than baseline. More details are shown in Appendix.

<span id="page-7-1"></span>

### 5.4. Ablation Studies

Different IPCs (Image Per Class). To validate the effectiveness of TrustDD on different distillation size  $|S|$ , we further conduct TrustDD experiments on CIFAR10 with IPC=1/10/50. Results in Table [9](#page-7-3) show TrustDD consistently boosts OOD performance without the degradation of InD classification. In Table [9,](#page-7-3) as IPC grows, the advantage of POE over OE becomes increasingly obvious. An intuitive explanation is that POE relies on *pseudo-outliers* (*i.e*., nearood samples) to model boundaries between InD and OOD, as  $|S_{\text{out}}|$  grows, such boundaries are increasingly accurate,

<span id="page-7-4"></span>Image /page/7/Figure/8 description: A line graph displays four metrics: FPR95, AUROC, AUPR-IN, and Accuracy, plotted against the x-axis labeled "Sout". The y-axis ranges from 40 to 100. The FPR95 line, marked with yellow diamonds, starts at approximately 92 and decreases to around 83. The AUROC line, indicated by orange circles, begins near 60 and rises to about 72, then fluctuates slightly. The AUPR-IN line, represented by teal triangles, starts at approximately 65 and increases to about 75, then gradually declines. The Accuracy line, shown with dark squares, remains relatively flat between 50 and 52 across the x-axis.

Figure 6: Influence of distilled outlier size. Here, we keep  $|\mathcal{S}_{\text{in}}|$  equals to 100, and changes  $|\mathcal{S}_{\text{out}}|$ .

<span id="page-7-3"></span>leading to superior OOD performance of POE.

Table 9: InD and OOD performance on different IPCs.

| IPC | AUROC    |              |              | Accuracy |       |              |
|-----|----------|--------------|--------------|----------|-------|--------------|
|     | baseline | OE           | POE (ours)   | baseline | OE    | POE (ours)   |
| 1   | 54.91    | <b>63.89</b> | 61.73        | 29.07    | 31.96 | <b>32.15</b> |
| 10  | 64.06    | 71.30        | <b>72.50</b> | 51.74    | 50.24 | <b>51.91</b> |
| 50  | 66.08    | 73.74        | <b>76.75</b> | 60.55    | 59.01 | 60.22        |

The Influence of  $|S_{\text{out}}|$ . As described in Section [5.1,](#page-4-3) TrustDD additionally condenses outlier data  $S_{out}$  to train better OOD detectors at the cost of slightly larger distilled size  $|S|$ , to balance efficiency and trustworthiness, we keep InD IPC=10, *i.e.*  $|\mathcal{S}_{in}| = 100$ , and distill various numbers of outliers, the results are shown in Figure [6.](#page-7-4) When  $|\mathcal{S}_{\text{in}}| = 100$ , it is appropriate to set  $|\mathcal{S}_{\text{out}}| \in [50, 150]$  according to the practical issues.

<span id="page-7-6"></span>

#### 5.5. Visualizations

Distilled images are visualized in Figure [7](#page-7-5) and Figure [8.](#page-8-20) When distilling  $\mathcal{S}_{\text{out}}$ , we explicitly make each  $s^i_{\text{out}}$  align with one corruption. In Figure [7,](#page-7-5) the first four rows in  $S_{\text{out}}$  correspond to jigsaw, invert, mosaic and speckle re-spectively, similar results could be observed in Figure [8.](#page-8-20) Please refer to the Appendix for more visualizations.

<span id="page-7-5"></span>Image /page/7/Figure/16 description: The image displays two grids of smaller images, side-by-side. The left grid is labeled "Distilled InD Sin" and contains 100 smaller images arranged in a 10x10 grid. These images appear to be generated samples, with many depicting recognizable objects or scenes, though some are abstract or blurry. The right grid is labeled "Distilled OOD Sout" and also contains 100 smaller images in a 10x10 grid. These images are more varied and abstract than those on the left. Some rows show noisy, pixelated patterns, while others display blocks of solid colors like red and cyan, interspersed with blurry or abstract imagery. The overall impression is a comparison between two sets of generated images, likely from different models or training conditions.

Figure 7: Visualization of TrustDD distill images on CI-FAR10  $[21]$  with IPC=10.

<span id="page-8-20"></span>Image /page/8/Picture/0 description: The image displays two rows of abstract, colorful images. The top row is labeled "Distilled InD S\_in" and contains 12 images. The bottom row is labeled "Distilled OOD S\_out" and also contains 12 images. The images in both rows are highly stylized and appear to be generated or processed, with vibrant colors and abstract patterns, some resembling distorted natural scenes or objects.

Figure 8: Visualization of TrustDD distill images on ImageNette  $[10]$  with IPC=1.

# 6. Conclusion

In this paper, we propose a novel learning paradigm called *Trustworthy Dataset Distillation* (TrustDD) from the data perspective, which is the first attempt to take both efficiency and trustworthiness into consideration for dataset distillation. TrustDD explicitly condenses in-distribution samples and outliers into separate tiny yet informative sets, upon which models trained perform competently on InD classification and OOD detection simultaneously. We further introduce to generate *pseudo-outliers* via InD corruption, and the proposed POE achieves superior performance compared with SOTA method OE even without the accessibility to real outlier data. Without degradation in InD classification performance, TrustDD with POE makes DD more trustworthy and more applicable to the open-world, and serves as a strong baseline for future research in the under-explored open-world setting in DD.

# References

- <span id="page-8-9"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-8-4"></span>[2] Daniel Bogdoll, Maximilian Nitsche, and J Marius Zöllner. Anomaly detection in autonomous driving: A survey. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 4488–4499, 2022.
- <span id="page-8-18"></span>[3] Yaroslav Bulatov. Notmnist dataset. *Google (Books/OCR), Tech. Rep.[Online]. Available: http://yaroslavvb. blogspot. it/2011/09/notmnist-dataset. html*, 2, 2011.
- <span id="page-8-8"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-8-1"></span>[5] Mircea Cimpoi, Subhransu Maji, Iasonas Kokkinos, Sammy Mohamed, and Andrea Vedaldi. Describing textures in the wild. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 3606–3613, 2014.
- <span id="page-8-14"></span>[6] cs231n.stanford.edu. Cs231n: Convolutional neural networks for visual recognition.
- <span id="page-8-10"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.

- <span id="page-8-0"></span>[8] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE Conference on Computer Vision and Pattern Recognition*, pages 248–255, 2009.
- <span id="page-8-11"></span>[9] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang Niu, and Sivan Sabato, editors, *Proceedings of the 39th International Conference on Machine Learning*, volume 162 of *Proceedings of Machine Learning Research*, pages 5378– 5396. PMLR, 17–23 Jul 2022.
- <span id="page-8-15"></span>[10] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french.
- <span id="page-8-19"></span>[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, June 2016.
- <span id="page-8-16"></span>[12] Dan Hendrycks, Steven Basart, Mantas Mazeika, Andy Zou, Joseph Kwon, Mohammadreza Mostajabi, Jacob Steinhardt, and Dawn Song. Scaling out-of-distribution detection for real-world settings. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang Niu, and Sivan Sabato, editors, *Proceedings of the 39th International Conference on Machine Learning*, volume 162 of *Proceedings of Machine Learning Research*, pages 8759–8773. PMLR, 17–23 Jul 2022.
- <span id="page-8-2"></span>[13] Dan Hendrycks and Kevin Gimpel. A baseline for detecting misclassified and out-of-distribution examples in neural networks. In *International Conference on Learning Representations*, 2017.
- <span id="page-8-3"></span>[14] Dan Hendrycks, Mantas Mazeika, and Thomas Dietterich. Deep anomaly detection with outlier exposure. In *International Conference on Learning Representations*, 2019.
- <span id="page-8-13"></span>[15] Dan Hendrycks, Mantas Mazeika, Saurav Kadavath, and Dawn Song. Using self-supervised learning can improve model robustness and uncertainty. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-8-17"></span>[16] Dan Hendrycks, Kevin Zhao, Steven Basart, Jacob Steinhardt, and Dawn Song. Natural adversarial examples. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 15262–15271, 2021.
- <span id="page-8-5"></span>[17] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-8-7"></span>[18] Andrew G Howard, Menglong Zhu, Bo Chen, Dmitry Kalenichenko, Weijun Wang, Tobias Weyand, Marco Andreetto, and Hartwig Adam. Mobilenets: Efficient convolutional neural networks for mobile vision applications. *arXiv preprint arXiv:1704.04861*, 2017.
- <span id="page-8-12"></span>[19] Hongzhi Huang, Yu Wang, Qinghua Hu, and Ming-Ming Cheng. Class-specific semantic reconstruction for open set recognition. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, pages 1–14, 2022.
- <span id="page-8-6"></span>[20] Benoit Jacob, Skirmantas Kligys, Bo Chen, Menglong Zhu, Matthew Tang, Andrew Howard, Hartwig Adam, and Dmitry Kalenichenko. Quantization and training of neural networks for efficient integer-arithmetic-only inference. In *Proceed-*

*ings of the IEEE conference on computer vision and pattern recognition*, pages 2704–2713, 2018.

- <span id="page-9-17"></span>[21] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-9-25"></span>[22] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60(6):84–90, 2017.
- <span id="page-9-21"></span>[23] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-9-24"></span>[24] Kimin Lee, Honglak Lee, Kibok Lee, and Jinwoo Shin. Training confidence-calibrated classifiers for detecting outof-distribution samples. In *International Conference on Learning Representations*, 2018.
- <span id="page-9-8"></span>[25] Kimin Lee, Kibok Lee, Honglak Lee, and Jinwoo Shin. A simple unified framework for detecting out-of-distribution samples and adversarial attacks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-9-16"></span>[26] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang Niu, and Sivan Sabato, editors, *Proceedings of the 39th International Conference on Machine Learning*, volume 162 of *Proceedings of Machine Learning Research*, pages 12352–12364. PMLR, 17–23 Jul 2022.
- <span id="page-9-1"></span>[27] Shiyu Liang, Yixuan Li, and R. Srikant. Enhancing the reliability of out-of-distribution image detection in neural networks. In *International Conference on Learning Representations*, 2018.
- <span id="page-9-9"></span>[28] Weitang Liu, Xiaoyun Wang, John Owens, and Yixuan Li. Energy-based out-of-distribution detection. *Advances in neural information processing systems*, 33:21464–21475, 2020.
- <span id="page-9-5"></span>[29] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho, editors, *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-9-3"></span>[30] Gaurav Menghani. Efficient deep learning: A survey on making deep learning models smaller, faster, and better. *ACM Computing Surveys*, 2021.
- <span id="page-9-23"></span>[31] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y. Ng. Reading digits in natural images with unsupervised feature learning. In *NIPS Workshop on Deep Learning and Unsupervised Feature Learning 2011*, 2011.
- <span id="page-9-6"></span>[32] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021.
- <span id="page-9-7"></span>[33] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan, editors, *Advances in Neural Information Processing Systems*, volume 34, pages 5186– 5198. Curran Associates, Inc., 2021.
- <span id="page-9-0"></span>[34] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry,

Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *International conference on machine learning*, pages 8748–8763. PMLR, 2021.

- <span id="page-9-11"></span>[35] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023.
- <span id="page-9-10"></span>[36] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018.
- <span id="page-9-26"></span>[37] K. Simonyan and A. Zisserman. Very deep convolutional networks for large-scale image recognition. In *International Conference on Learning Representations*, May 2015.
- <span id="page-9-13"></span>[38] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In Hal Daumé III and Aarti Singh, editors, *Proceedings of the 37th International Conference on Machine Learning*, volume 119 of *Proceedings of Machine Learning Research*, pages 9206–9216. PMLR, 13–18 Jul 2020.
- <span id="page-9-15"></span>[39] Jihoon Tack, Sangwoo Mo, Jongheon Jeong, and Jinwoo Shin. Csi: Novelty detection via contrastive learning on distributionally shifted instances. *Advances in neural information processing systems*, 33:11839–11852, 2020.
- <span id="page-9-19"></span>[40] Grant Van Horn, Oisin Mac Aodha, Yang Song, Yin Cui, Chen Sun, Alex Shepard, Hartwig Adam, Pietro Perona, and Serge Belongie. The inaturalist species classification and detection dataset. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 8769–8778, 2018.
- <span id="page-9-14"></span>[41] Sagar Vaze, Kai Han, Andrea Vedaldi, and Andrew Zisserman. Open-set recognition: a good closed-set classifier is all you need? In *International Conference on Learning Representations*, 2022.
- <span id="page-9-20"></span>[42] Haoqi Wang, Zhizhong Li, Litong Feng, and Wayne Zhang. Vim: Out-of-distribution with virtual-logit matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4921–4930, 2022.
- <span id="page-9-12"></span>[43] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022.
- <span id="page-9-4"></span>[44] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-9-2"></span>[45] Julia Wolleb, Florentin Bieder, Robin Sandkühler, and Philippe C. Cattin. Diffusion models for medical anomaly detection. In Linwei Wang, Qi Dou, P. Thomas Fletcher, Stefanie Speidel, and Shuo Li, editors, *Medical Image Computing and Computer Assisted Intervention – MICCAI 2022*, pages 35–45, Cham, 2022. Springer Nature Switzerland.
- <span id="page-9-22"></span>[46] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms, 2017.
- <span id="page-9-18"></span>[47] Pingmei Xu, Krista A Ehinger, Yinda Zhang, Adam Finkelstein, Sanjeev R Kulkarni, and Jianxiong Xiao. Turkergaze:

Crowdsourcing saliency with webcam based eye tracking. *arXiv preprint arXiv:1504.06755*, 2015.

- <span id="page-10-7"></span>[48] Jingkang Yang, Pengyun Wang, Dejian Zou, Zitang Zhou, Kunyuan Ding, WENXUAN PENG, Haoqi Wang, Guangyao Chen, Bo Li, Yiyou Sun, Xuefeng Du, Kaiyang Zhou, Wayne Zhang, Dan Hendrycks, Yixuan Li, and Ziwei Liu. OpenOOD: Benchmarking generalized out-ofdistribution detection. In *Thirty-sixth Conference on Neural Information Processing Systems Datasets and Benchmarks Track*, 2022.
- <span id="page-10-6"></span>[49] Fisher Yu, Ari Seff, Yinda Zhang, Shuran Song, Thomas Funkhouser, and Jianxiong Xiao. Lsun: Construction of a large-scale image dataset using deep learning with humans in the loop. *arXiv preprint arXiv:1506.03365*, 2015.
- <span id="page-10-3"></span>[50] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-10-0"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021.
- <span id="page-10-4"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. 2023.
- <span id="page-10-1"></span>[53] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-5"></span>[54] Bolei Zhou, Agata Lapedriza, Aditya Khosla, Aude Oliva, and Antonio Torralba. Places: A 10 million image database for scene recognition. *IEEE transactions on pattern analysis and machine intelligence*, 40(6):1452–1464, 2017.
- <span id="page-10-2"></span>[55] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.

# A. Appendix

## A.1. Datasets

### A.1.1 InD Datasets

Digit datasets. MNIST [\[23\]](#page-9-21) and SVHN [\[31\]](#page-9-23) are two wellknown digit datasets. MNIST is a handwritten digits dataset with 60,000 training samples and 10,000 test samples with size  $28\times28$ . SVHN is about street view numbers containing 73, 257 and 26, 032 training and test samples with size  $32 \times$ 32, respectively. Both MNIST and SVHN have 10 classes, and MNIST images are grayscale images.

CIFAR [\[21\]](#page-9-17). CIFAR10 and CIFAR100 are natural color image datasets containing 50, 000 training images and 10, 000 test images, with size  $32 \times 32$ . CIFAR10 has 10 classes while CIFAR100 has 100 classes.

ImageNet Subsets. Following [\[4\]](#page-8-8), each subset is with 10 classes, and images are down-sampled to  $128 \times 128$ . Here, we mainly use three subsets: ImageNette [\[10\]](#page-8-15), ImageFruit [\[4\]](#page-8-8) and ImageMisc. ImageNette and ImageFruit are two pre-existing datasets, while ImageMisc is created in this paper by randomly sampling 10 classes in ImageNet [\[8\]](#page-8-0) full dataset. Here, we enumerate the specific categories of each subset in Table [10.](#page-11-0)

<span id="page-11-0"></span>Table 10: The specific categories of three ImageNet Subsets, which are shown in the form of class-idclass-name.

| Subset     | Categories                                                                                                                                               |
|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
| ImageNette | 0-tench, 217-english springer, 482-cassette player, 491-chainsaw, 497-church,<br>566-horn, 569-dustcart, 571-gas pump, 574-golf ball, 701-chute          |
| ImageFruit | 953-pineapple, 954-banana, 949-strawberry, 950-orange, 951-lemon,<br>957-pomegranate, 952-fig, 945-bell pepper, 943-cucumber, 948-green apple            |
| ImageMisc  | 2-shark, 99-goose, 207-golden retriever, 404-airliner, 430-basketball,<br>565-freight car, 691-oxygen mask, 851-television, 949-strawberry, 966-red wine |

### A.1.2 OOD datasets

The corresponding test OOD datasets are listed in Table [2.](#page-4-2) When the image resolution is not the same, we resize OOD images to the InD image size, *e.g*., the resized version of Tiny ImageNet [\[6\]](#page-8-14) and LSUN [\[49\]](#page-10-6).

## A.2. Experimental Details

Metrics. For InD classification, we measure the classification accuracy, For OOD detection, we measure the commonly-used metrics: (1) FPR95: False Positive Rate when True Positive Rate is at 95%, which could be understood as the ratio of OOD samples that models misclassify as InD samples when 95% of the in-distribution samples could be detected. (2) AUROC: Area Under the Receiver Operating Characteristic curve. AUROC could be interpreted as the probability that each positive (InD) sample to be assigned a higher score than OOD samples. (3) AUPR-IN and AUPR-OUT: Area under the Precision-Recall curve where in-distribution and out-of-distribution samples are regarded as positives respectively. Both AUROC and AUPR(- IN or -OUT) are threshold-independent metrics.

Hyper-parameters. The main hyper-parameter is the trade-off weight  $\lambda$  of two tasks, we find that the results are not sensitive when  $\lambda$  is in [0.3, 0.7] through validation experiments (CIFAR serves as the InD dataset and SVHN serves as the validation OOD test dataset), so we simply set  $\lambda = 0.5$  in all experiments as in [\[14\]](#page-8-3). For networks, we mainly use ConvNet, which is with several convolutional layers, each has 128 channels and is equipped with RELU, InstanceNorm and average pooling. The final fullyconnected layer outputs the logits.

OE data. OE [\[14\]](#page-8-3) explicitly utilizes curated outliers, in all our experiments, we use 300K Random Images [\[14\]](#page-8-3), the example images are shown in Table [9.](#page-11-1)

<span id="page-11-1"></span>Figure 9: Examples of 300K Random Images in OE.

POE and corruptions. Corruptions should noticeably change the semantics of in-distribution samples. For natural scene images, we mainly implement jigsaw, invert, mosaic and speckle as in Section [4.2.](#page-3-4) It is worth noting that flip is a semantic-preserving transformation for natural images, however, this is not the case for digits and characters. Hence, for digits datasets like MNIST [\[23\]](#page-9-21) and SVHN  $[31]$ , one could also apply  $f$ lip transformation which slightly shifts the semantics. For digits, flip refers to horizontal (without '0', '1', '8') or vertical (without '0', '1', '3', '8') flips, flipping '0', '1' and '8' does not shift the semantics of digits, so when generating *pseudo-outliers* we simply leave out them. With the ensemble of corruption transformations, one could generate an outlier dataset  $\mathcal{T}_{\text{out}}$ from  $\mathcal{T}_{\text{in}}$ . Ensemble means that we employ various corruptions to different images and collect them together to obtain  $\mathcal{T}_{\text{out}}$ , here, for each image, we implement one certain corruption.

The *pseudo-outliers* of CIFAR, MNIST, SVHN, and ImageNette are visualized in Figure [10](#page-12-0) — Figure [13.](#page-12-1)

<span id="page-12-0"></span>Image /page/12/Picture/0 description: The image displays four rows of figures, each showcasing different types of pseudo-outliers on various datasets. The first row, labeled Figure 10, visualizes pseudo-outliers on CIFAR10, presenting 'original', 'jigsaw', 'invert', 'mosaic', and 'speckle' categories. The second row, Figure 11, visualizes pseudo-outliers on MNIST with the same categories: 'original', 'jigsaw', 'flip', 'mosaic', and 'speckle'. The third row, Figure 12, visualizes pseudo-outliers on SVHN, also with 'original', 'jigsaw', 'flip', 'mosaic', and 'speckle' categories. The final row, Figure 13, visualizes pseudo-outliers on ImageNet, displaying 'original', 'jigsaw', 'invert', 'mosaic', and 'speckle' categories. Each category within the figures consists of a grid of images, demonstrating the effect of different outlier visualization techniques.

Figure 13: Visualization of *pseudo-outliers* on ImageNette [\[10\]](#page-8-15).

<span id="page-12-1"></span>

# A.3. Performance on Original Full Dataset

on the test datasets in Table [2.](#page-4-2)

We evaluate InD classification and OOD detection on models trained on the original full dataset for reference as theoretical upper bounds, as shown in Table [11.](#page-13-0) POE still performs competently in the full dataset setting, *i.e*. ordinary OOD detection scenario, which demonstrates the superiority of the proposed POE in enhancing OOD detection, *i.e.*, POE also serves as a competitive method for improving OOD detection performance without the requirement of real outlier dataset. we report the mean OOD performance

## A.4. Additional Results

### A.4.1 Detailed Results of Digit Datasets

Here, we show the detailed results of digit datasets in Ta-ble [12.](#page-13-1) ConvNets are trained on DSA [\[51\]](#page-10-0) distilled images with IPC=10. The digits contain limited semantics for nearood *pseudo-outliers* in POE, while OE leverages a real outlier dataset that is rich in semantic information, as a result, OE obtains better OOD performance.

Table 11: InD classification and OOD detection performance of models trained on the original full dataset.

<span id="page-13-0"></span>

| Dataset           | FPR95 $↓$ |       |            | AUROC $↑$ |       |            | AUPR-IN $↑$ |       |            | AUPR-OUT $↑$ |       |            | Accuracy $↑$ |       |            |
|-------------------|-----------|-------|------------|-----------|-------|------------|-------------|-------|------------|--------------|-------|------------|--------------|-------|------------|
|                   | baseline  | OE    | POE (ours) | baseline  | OE    | POE (ours) | baseline    | OE    | POE (ours) | baseline     | OE    | POE (ours) | baseline     | OE    | POE (ours) |
| <b>MNIST</b>      | 1.84      | 0.01  | 0.06       | 99.32     | 99.99 | 99.92      | 99.47       | 99.99 | 99.93      | 99.07        | 99.99 | 99.91      | 99.69        | 99.65 | 99.74      |
| <b>SVHN</b>       | 22.50     | 0.06  | 2.26       | 96.61     | 99.98 | 99.56      | 98.67       | 99.99 | 99.79      | 89.22        | 99.95 | 98.93      | 95.88        | 95.84 | 95.90      |
| <b>CIFAR10</b>    | 77.24     | 35.48 | 35.84      | 79.95     | 93.21 | 92.25      | 84.08       | 94.31 | 93.30      | 72.92        | 91.40 | 90.99      | 85.72        | 86.90 | 87.10      |
| <b>CIFAR100</b>   | 90.53     | 78.63 | 60.52      | 66.71     | 77.77 | 82.09      | 72.72       | 81.87 | 84.14      | 58.61        | 71.86 | 79.93      | 57.49        | 58.21 | 58.64      |
| <b>ImageNette</b> | 77.42     | 73.67 | 59.06      | 82.91     | 84.52 | 88.01      | 91.48       | 92.27 | 93.80      | 65.02        | 67.86 | 75.57      | 85.80        | 88.00 | 89.20      |
| <b>ImageFruit</b> | 89.69     | 83.52 | 79.69      | 67.74     | 69.01 | 74.87      | 82.03       | 82.32 | 85.99      | 47.46        | 52.08 | 59.52      | 64.20        | 65.60 | 66.40      |
| <b>ImageMisc</b>  | 76.88     | 68.59 | 67.97      | 83.19     | 83.94 | 84.66      | 91.34       | 91.81 | 92.36      | 67.09        | 70.11 | 70.31      | 87.60        | 88.60 | 87.20      |

Table 12: OOD detection performance on digit datasets: MNIST [\[23\]](#page-9-21) and SVHN [\[31\]](#page-9-23), with IPC=10.

<span id="page-13-1"></span>

| $\mathcal{D}_{in}$ | $\mathcal{D}_{out}^{test}$ | FPR95 $\downarrow$ |       |            | AUROC $\uparrow$ |       |            | AUPR-IN $\uparrow$ |       |            | AUPR-OUT $\uparrow$ |       |            |
|--------------------|----------------------------|--------------------|-------|------------|------------------|-------|------------|--------------------|-------|------------|---------------------|-------|------------|
|                    |                            | baseline           | OE    | POE (ours) | baseline         | OE    | POE (ours) | baseline           | OE    | POE (ours) | baseline            | OE    | POE (ours) |
|                    | Texture                    | 11.94              | 0.35  | 1.27       | 97.46            | 99.94 | 99.01      | 98.81              | 99.97 | 99.54      | 93.37               | 99.91 | 97.51      |
|                    | Places365                  | 20.37              | 0.06  | 0.28       | 96.80            | 99.97 | 99.61      | 97.66              | 99.97 | 99.69      | 95.39               | 99.97 | 99.50      |
|                    | Tiny ImageNet              | 21.58              | 0.08  | 0.36       | 96.65            | 99.97 | 99.52      | 97.54              | 99.97 | 99.63      | 95.17               | 99.97 | 99.38      |
| <b>MNIST</b>       | <b>Fashion MNIST</b>       | 41.32              | 4.10  | 1.89       | 92.96            | 99.05 | 99.22      | 94.42              | 99.19 | 99.39      | 90.50               | 98.97 | 99.01      |
|                    | notMNIST                   | 36.36              | 2.67  | 6.59       | 93.75            | 99.33 | 98.58      | 94.72              | 99.40 | 98.81      | 92.00               | 99.31 | 98.30      |
|                    | CIFAR10                    | 22.69              | 0.00  | 0.31       | 96.56            | 99.99 | 99.56      | 97.49              | 99.99 | 99.66      | 94.98               | 99.99 | 99.42      |
|                    | mean                       | 25.71              | 1.21  | 1.78       | 95.70            | 99.71 | 99.25      | 96.77              | 99.75 | 99.45      | 93.57               | 99.69 | 98.85      |
|                    | Texture                    | 69.73              | 23.07 | 29.77      | 83.66            | 95.95 | 94.81      | 96.09              | 99.08 | 98.83      | 49.87               | 85.20 | 80.81      |
|                    | Places365                  | 71.93              | 23.73 | 36.73      | 83.63            | 96.01 | 93.72      | 86.69              | 96.57 | 94.71      | 79.92               | 95.48 | 92.63      |
|                    | Tiny ImageNet              | 70.29              | 16.24 | 18.90      | 84.39            | 97.27 | 96.89      | 93.97              | 98.98 | 98.84      | 62.98               | 92.88 | 91.96      |
| <b>SVHN</b>        | <b>LSUN</b>                | 68.88              | 11.09 | 13.95      | 84.93            | 98.04 | 97.62      | 94.18              | 99.27 | 99.11      | 64.08               | 94.79 | 93.80      |
|                    | iSUN                       | 69.32              | 11.02 | 14.03      | 84.83            | 98.04 | 97.61      | 94.70              | 99.35 | 99.20      | 61.25               | 94.26 | 93.14      |
|                    | CIFAR10                    | 72.82              | 27.80 | 42.00      | 83.34            | 95.19 | 92.48      | 93.53              | 98.16 | 97.12      | 60.78               | 88.18 | 81.34      |
|                    | mean                       | 70.50              | 18.83 | 25.90      | 84.13            | 96.75 | 95.52      | 93.19              | 98.57 | 97.97      | 63.15               | 91.80 | 88.95      |

Table 13: OOD detection performance on ImageNet Subsets [\[4,](#page-8-8) [10\]](#page-8-15), with IPC=1.

<span id="page-13-2"></span>

| $\mathcal{D}_{in}$ | $\mathcal{D}_{out}^{test}$ | FPR95 $\downarrow$ |       |           | AUROC $\uparrow$ |       |           | AUPR-IN $\uparrow$ |       |           | AUPR-OUT $\uparrow$ |       |           |
|--------------------|----------------------------|--------------------|-------|-----------|------------------|-------|-----------|--------------------|-------|-----------|---------------------|-------|-----------|
|                    |                            | baseline           | OE    | POE(ours) | baseline         | OE    | POE(ours) | baseline           | OE    | POE(ours) | baseline            | OE    | POE(ours) |
| ImageNette         | Texture                    | 90.31              | 84.53 | 80.47     | 63.80            | 69.65 | 72.98     | 76.98              | 82.42 | 83.95     | 45.56               | 51.32 | 56.35     |
|                    | <b>Species</b>             | 93.59              | 91.48 | 89.53     | 56.62            | 64.22 | 65.21     | 71.06              | 79.05 | 79.02     | 39.24               | 43.86 | 45.84     |
|                    | iNaturalist                | 92.34              | 88.05 | 87.73     | 59.96            | 64.87 | 68.19     | 73.59              | 78.67 | 81.03     | 41.59               | 45.98 | 48.81     |
|                    | ImageNet-O                 | 93.20              | 91.17 | 89.06     | 59.13            | 63.69 | 62.68     | 74.07              | 78.67 | 76.56     | 40.14               | 43.83 | 44.58     |
|                    | OpenImage-O                | 93.83              | 89.92 | 86.33     | 58.09            | 64.34 | 67.11     | 72.71              | 78.65 | 79.90     | 39.68               | 44.86 | 49.53     |
|                    | mean                       | 92.66              | 89.03 | 86.62     | 59.52            | 65.35 | 67.23     | 73.68              | 79.49 | 80.09     | 41.24               | 45.97 | 49.02     |
| ImageFruit         | Texture                    | 92.73              | 90.70 | 90.16     | 54.48            | 58.97 | 61.71     | 69.99              | 73.53 | 75.77     | 38.10               | 41.93 | 44.61     |
|                    | <b>Species</b>             | 95.70              | 91.48 | 92.58     | 51.39            | 58.91 | 56.12     | 67.99              | 73.80 | 71.45     | 34.93               | 41.02 | 39.56     |
|                    | iNaturalist                | 94.53              | 91.33 | 91.17     | 52.53            | 55.90 | 60.30     | 68.37              | 71.57 | 75.10     | 36.21               | 38.99 | 42.38     |
|                    | ImageNet-O                 | 95.31              | 92.66 | 92.27     | 49.13            | 54.74 | 53.07     | 65.57              | 69.91 | 68.37     | 34.06               | 37.72 | 37.93     |
|                    | OpenImage-O                | 94.38              | 92.73 | 93.91     | 48.37            | 55.99 | 54.50     | 64.94              | 71.45 | 70.35     | 33.93               | 38.88 | 37.18     |
|                    | mean                       | 94.53              | 91.78 | 92.02     | 51.18            | 56.90 | 57.14     | 67.37              | 72.05 | 72.21     | 35.45               | 39.71 | 40.33     |
| ImageMisc          | Texture                    | 90.86              | 86.09 | 85.55     | 62.33            | 68.04 | 69.36     | 77.68              | 81.90 | 82.62     | 43.30               | 49.73 | 51.86     |
|                    | <b>Species</b>             | 93.59              | 92.97 | 90.47     | 57.99            | 62.85 | 62.67     | 74.98              | 78.89 | 77.77     | 38.99               | 42.45 | 43.58     |
|                    | iNaturalist                | 93.36              | 89.61 | 88.75     | 59.17            | 66.64 | 68.54     | 76.31              | 80.73 | 81.76     | 39.56               | 47.02 | 49.25     |
|                    | ImageNet-O                 | 94.14              | 93.20 | 93.36     | 56.66            | 61.50 | 58.81     | 73.69              | 77.07 | 75.34     | 38.13               | 41.46 | 40.12     |
|                    | OpenImage-O                | 93.28              | 90.39 | 91.56     | 58.28            | 63.19 | 60.65     | 74.93              | 78.18 | 75.77     | 39.43               | 44.55 | 41.87     |
|                    | mean                       | 93.05              | 90.45 | 89.94     | 58.88            | 64.44 | 64.01     | 75.52              | 79.36 | 78.66     | 39.88               | 45.04 | 45.33     |

### A.4.2 Detailed Results of ImageNet Subsets

Results on three ImageNet Subsets are shown in Table [13.](#page-13-2) ConvNets are trained on DSA [\[51\]](#page-10-0) distilled images with IPC=1. POE consistently outperforms OE in most circumstances, which illustrates the effectiveness of POE.

#### A.5. Additional Analysis

##### A.5.1 Corruptions Transformations

In this paper, for POE, we mainly implement several corruptions and utilize the ensemble of *pseudo-outliers* from various corruption operations. We also test the performance of POE with only one certain type of corruption, as shown in Table [14.](#page-14-0) The experimental results demonstrate the benefits of the ensemble of these corruption transformations. The reason is that the ensemble offers more near-ood samples for better modelling the boundaries between InD and OOD samples, leading to better OOD performance.

<span id="page-14-0"></span>Table 14: OOD detection performance of POE with different corruption transformations.

| corruption | no    | jigsaw | invert | mosaic | speckle | ensemble |
|------------|-------|--------|--------|--------|---------|----------|
| AUROC      | 64.06 | 69.33  | 66.48  | 64.74  | 70.67   | 72.50    |

##### A.5.2 Why not distill both InD and outliers into InD?

Here, let's continue the third issue in Section [5.3.](#page-7-0) One seemingly more efficient way is to distill  $\mathcal{T}_{in}$  and  $\mathcal{T}_{out}$  altogether into a single set  $S_{single}$  rather than into  $S_{in}$  and  $\mathcal{S}_{\text{out}}$  separately, to be more specific, one could simply leave out the second term in Eq. [\(11\)](#page-3-2), *i.e*., make model trained on  $S_{single}$  could approximate the performance when trained on both  $\mathcal{T}_{in}$  and  $\mathcal{T}_{out}$ . For example, when implementing on DSA [\[51\]](#page-10-0), one could rewrite the distillation loss as:

<span id="page-14-1"></span>
$$
\mathcal{L}_{distill}(\mathcal{S}) = D(\nabla_{\theta} \mathcal{L}_{ce}(\mathcal{A}(\mathcal{S}_{single}), \hat{\theta}_{t}), \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{T}), \hat{\theta}_{t}))
$$
\n(12)

where  $\mathcal L$  is the *integrated loss* considering both tasks:  $\mathcal L =$  $\mathbb{E}_{(\bm{x},y)\sim\mathcal{D}_{\text{in}}}\mathcal{L}_{\text{ce}}(f_\theta(\bm{x}), y) + \lambda \mathbb{E}_{\bm{x}'\sim\mathcal{D}_{\text{out}}}H(\mathcal{U}; f_\theta(\bm{x}')),$  and  $\mathcal{L}_{\text{ce}}$ is the cross-entropy loss. Eq.  $(12)$  aims to make the gradient trained on  $S_{single}$  approaches the gradient trained on both  $\mathcal{T}_{in}$ and  $\mathcal{T}_{out}$ , which is more efficient.

However, this does not work as expected. From Fig-ure [14](#page-14-2) one could tell that when distilling  $\mathcal{T}_{in}$  and  $\mathcal{T}_{out}$  jointly to a single set  $S_{single}$ , the classification loss  $\mathcal{L}_{ce}$  increases compared with baseline (*i.e.*  $\lambda = 0$ ), although the outlier loss  $H(\cdot)$  decreases, in this case, the InD classification drops from  $\sim 52\%$  to  $\sim 40\%$ , leading to drastically weak OOD performance (AUROC dropped from  $\sim 64\%$  to 60%). An intuitive explanation is that, as in Section [5.3,](#page-7-0) the outlier loss  $H(\cdot)$  decreased compared with the baseline at the cost of the increase in classification loss  $\mathcal{L}_{ce}$ , *i.e.*, at the cost of InD classification performance, which could further deteriorate OOD performance due to the approximately positive correlation [\[41\]](#page-9-14) between InD and OOD performance. When keeping the total distilled size  $|\mathcal{S}|$  fixed, additionally incorporating outlier information into  $S_{single}$  will lead to a decrease in the information content of in-distribution portion in  $\mathcal{S}_{single}$ .

<span id="page-14-2"></span>Image /page/14/Figure/10 description: The image contains two plots. The left plot, labeled (a) InD loss Lce(f\_theta(x), y), shows the cross-entropy loss as a function of iteration. The y-axis ranges from 75 to 175, and the x-axis ranges from 0 to 1000 iterations. Two lines are plotted: one in black with square markers representing lambda=0, and another in red with circle markers representing lambda=1. The black line starts at approximately 165 and decreases to around 80 by 1000 iterations. The red line starts at approximately 165 and fluctuates between 125 and 140 throughout the iterations. The right plot, labeled (b) OOD loss H(U; f\_theta(x')), shows the out-of-distribution loss as a function of iteration. The y-axis ranges from 225 to 325, and the x-axis ranges from 0 to 1000 iterations. Two lines are plotted: one in yellow with diamond markers representing lambda=0, and another in teal with triangle markers representing lambda=1. Both lines start at approximately 320 and generally decrease over the iterations, with the teal line generally showing lower values than the yellow line after the initial iterations.

(a) InD loss  $\mathcal{L}_{ce}(f_{\theta}(\boldsymbol{x}), y)$ (b) OOD loss  $H(\mathcal{U}; f_{\theta}(\boldsymbol{x}'))$ Figure 14: InD and OOD loss function with respect to different iterations during training.  $\lambda = 0$  denotes baseline, and  $\lambda = 1$  means that distill both InD and OOD into a single set  $\mathcal{S}_{single}$ .

#### A.6. Additional Visualizations

In this section, we visualize additional results to complement the results of Section [5.5.](#page-7-6) Visualizations of CI-FAR10, MNIST, SVHN, ImageNette, ImageFruit and ImageMisc are shown in Figure [15](#page-15-0) — Figure [20.](#page-19-0)

<span id="page-15-0"></span>Image /page/15/Picture/0 description: The image displays two grids of small images, side-by-side. The left grid is labeled "OE: Sin" and the right grid is labeled "OE: Sout". Both grids appear to contain generated images, possibly from a machine learning model, with a variety of abstract and somewhat recognizable shapes and colors. The images in the left grid seem to have a slightly different distribution of colors and patterns compared to the right grid.

OE: \$S\_{in}\$

Image /page/15/Picture/2 description: A grid of 80 small, abstract, colorful images, each with a black border. The images appear to be generated by a machine learning model, possibly a GAN, as they are varied and somewhat blurry, with no clear discernible objects or patterns. The colors range from dark blues and browns to lighter whites and yellows, with some images featuring red and green hues. The overall impression is a mosaic of abstract art pieces.

Image /page/15/Picture/4 description: The image displays two grids of small images, labeled "POE: Sin" on the left and "POE: Sout" on the right. The left grid, "POE: Sin", contains 64 small images arranged in an 8x8 matrix. These images appear to be generated samples, with many showing abstract or somewhat recognizable shapes and colors, possibly representing objects or scenes. The right grid, "POE: Sout", also contains 64 small images in an 8x8 matrix. These images are more varied and some appear to be noisy or abstract, with distinct blocks of color like red and blue interspersed with more complex patterns. The overall impression is a comparison of two sets of generated images, likely from a machine learning model.

Figure 15: Visualization of  $S_{in}$  and  $S_{out}$  distilled by TrustDD with OE and POE on CIFAR10 [\[21\]](#page-9-17), with IPC=10.

OE: \$S\_i\$

Image /page/15/Picture/8 description: A grid of 100 small images, arranged in 10 rows and 10 columns. Each small image appears to be a generated image, possibly from a machine learning model, with varying degrees of clarity and detail. The images depict a range of subjects, including what appear to be animals, vehicles, and abstract patterns. Some images are blurry, while others have more discernible features. The overall impression is a collection of diverse, synthesized visual content.

Image /page/16/Figure/0 description: The image displays four grids of handwritten digits. The top left grid, labeled "OE: Sin", shows clear, distinct digits from 0 to 9, arranged in rows. The top right grid, labeled "OE: Sout", contains a collection of noisy and distorted images that are vaguely reminiscent of digits. The bottom left grid, labeled "POE: Sin", is similar to the top left grid, presenting clear digits from 0 to 9. The bottom right grid, labeled "POE: Sout", also shows distorted and noisy images, with some resembling digits more than others.

Figure 16: Visualization of  $S_{in}$  and  $S_{out}$  distilled by TrustDD with OE and POE on MNIST [\[23\]](#page-9-21), with IPC=10.

Image /page/17/Picture/0 description: The image displays four grids of images, each labeled with "OE: S\_in" or "OE: S\_out". The top left grid and bottom left grid both show clear images of digits from 0 to 9, arranged in rows and columns. The top right grid and bottom right grid show more abstract, noisy, and less discernible images, with some colorful elements and a green square in the top right grid.

POE:  $S_{\text{in}}$  POE:  $S_{\text{out}}$ Figure 17: Visualization of  $S_{\text{in}}$  and  $S_{\text{out}}$  distilled by TrustDD with OE and POE on SVHN [\[31\]](#page-9-23), with IPC=10.

Image /page/18/Picture/0 description: The image is a grid of ten abstract, colorful, and somewhat distorted images. The top row contains five images: the first appears to be a person in a grassy area, the second a black and white spotted animal, the third a swirling vortex of dark colors, the fourth a bright explosion of red and orange, and the fifth a cityscape or forest with tall structures. The bottom row also contains five images: the first is a complex, dark, and textured pattern, the second a blurry scene with trees and water, the third a glass or jar with red and blue contents, the fourth a soccer ball on grass, and the fifth a sky with colorful floating objects resembling flowers or balloons.

Image /page/18/Picture/2 description: The image displays two grids of abstract, colorful images. The left grid, labeled "OE: Sin", contains six images arranged in two rows of three. The top row shows a swirling vortex-like image, a colorful explosion, and a building with a blue sky. The bottom row shows a glass with red liquid, a green grassy field with a white sphere, and a blue sky with three flowers. The right grid, labeled "OE: Sout", also contains six images arranged in two rows of three. These images are more abstract and glitchy, with a mix of colors and distorted shapes, some appearing to be faces or figures.

Image /page/18/Picture/4 description: The image displays two grids of abstract, colorful images. The left grid, labeled "POE: Sin", contains eight images arranged in two rows of four. The right grid, labeled "POE: Sout", also contains eight images arranged in two rows of four. The images in both grids are highly abstract and appear to be generated by a neural network, with swirling patterns, vibrant colors, and distorted shapes. Some images suggest faint outlines of objects or scenes, such as figures, buildings, or natural elements, but they are heavily abstracted and difficult to discern clearly.

Image /page/18/Picture/6 description: The image is a grid of 10 abstract, colorful images. The images are arranged in two rows of five. The colors are vibrant and include blues, greens, reds, yellows, and purples. The images appear to be generated by a neural network or artificial intelligence, as they are abstract and do not clearly depict recognizable objects or scenes. Some of the images have a swirling or textured appearance, while others are more fragmented or pixelated. The overall impression is one of digital art or experimental visual output.

Figure 18: Visualization of  $S_{in}$  and  $S_{out}$  distilled by TrustDD with OE and POE on ImageNette [\[10\]](#page-8-15), with IPC=1.

Image /page/18/Figure/9 description: The image displays four grids of abstract, colorful images, arranged in a 2x2 layout. Each grid contains six smaller images arranged in two rows of three. The top-left grid is labeled "OE: Sin", the top-right grid is labeled "OE: Sout", the bottom-left grid is labeled "POE: Sin", and the bottom-right grid is labeled "POE: Sout". The images within the "OE: Sin" and "POE: Sin" grids appear to be more recognizable, with some resembling fruits like pineapples, strawberries, and possibly other round fruits, rendered in vibrant, somewhat abstract styles. The images in the "OE: Sout" and "POE: Sout" grids are more abstract and chaotic, with a greater variety of colors and less discernible shapes, appearing more like digital noise or complex patterns.

Figure 19: Visualization of  $S_{in}$  and  $S_{out}$  distilled by TrustDD with OE and POE on ImageFruit [\[4\]](#page-8-8), with IPC=1.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: A grid of ten abstract images, arranged in two rows of five. The images are highly stylized and colorful, with a dreamlike or impressionistic quality. Some images appear to depict natural scenes, such as landscapes or flora, while others are more abstract and geometric. The overall impression is one of vibrant, chaotic energy.

Image /page/19/Picture/2 description: The image displays two grids of abstract, colorful images. The left grid, labeled "OE: Sin", contains six images arranged in two rows of three. The right grid, labeled "OE: Sout", also contains six images arranged in two rows of three. The images in both grids are highly abstract and appear to be generated by a neural network, with swirling colors and distorted shapes that vaguely resemble objects or scenes but are not clearly identifiable.

Image /page/19/Picture/4 description: The image is a grid of ten abstract, colorful images. The top row contains four images: the first is a blue and white abstract pattern, the second is a blurry image of a dog sitting on grass, the third is a yellow and brown fuzzy object, and the fourth is a blue and white abstract landscape. The bottom row contains six images: the first is a colorful abstract scene with many figures, the second is a blurry circular object, the third is a dark, abstract hallway, the fourth is a close-up of red flowers, and the fifth is a glass of red wine.

Image /page/19/Picture/6 description: The image displays a grid of ten abstract, colorful, and somewhat distorted images. The images are arranged in two rows of five. Each individual image appears to be a complex pattern of colors and shapes, with no clear discernible objects or scenes. The overall impression is one of digital art or a visualization of complex data, possibly from a neural network or a generative process.

POE:  $S_{\text{in}}$  POE:  $S_{\text{out}}$ 

Figure 20: Visualization of  $S_{in}$  and  $S_{out}$  distilled by TrustDD with OE and POE on ImageMisc, with IPC=1.