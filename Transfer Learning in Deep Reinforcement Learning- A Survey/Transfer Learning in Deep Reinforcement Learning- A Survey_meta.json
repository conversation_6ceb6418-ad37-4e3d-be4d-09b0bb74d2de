{"table_of_contents": [{"title": "HHS Public Access", "heading_level": null, "page_id": 0, "polygon": [[129.75, 19.5], [334.6875, 19.5], [334.6875, 43.7958984375], [129.75, 43.7958984375]]}, {"title": "Transfer Learning in Deep Reinforcement Learning: A Survey", "heading_level": null, "page_id": 0, "polygon": [[88.751953125, 127.810546875], [496.5, 127.810546875], [496.5, 145.212890625], [88.751953125, 145.212890625]]}, {"title": "<PERSON><PERSON><PERSON>,", "heading_level": null, "page_id": 0, "polygon": [[88.6025390625, 159.134765625], [160.3212890625, 159.134765625], [160.3212890625, 171.703125], [88.6025390625, 171.703125]]}, {"title": "<PERSON><PERSON><PERSON>,", "heading_level": null, "page_id": 0, "polygon": [[89.25, 206.89453125], [153.0, 206.89453125], [153.0, 219.26953125], [89.25, 219.26953125]]}, {"title": "<PERSON><PERSON>,", "heading_level": null, "page_id": 0, "polygon": [[89.25, 241.505859375], [147.322265625, 241.505859375], [147.322265625, 253.880859375], [89.25, 253.880859375]]}, {"title": "<PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[89.25, 289.072265625], [143.25, 289.072265625], [143.25, 301.447265625], [89.25, 301.447265625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[89.25, 343.212890625], [141.345703125, 343.212890625], [141.345703125, 357.521484375], [89.25, 357.521484375]]}, {"title": "Keywords", "heading_level": null, "page_id": 0, "polygon": [[89.05078125, 554.94140625], [138.357421875, 554.94140625], [138.357421875, 568.08984375], [89.05078125, 568.08984375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[88.9013671875, 607.921875], [179.25, 607.921875], [179.25, 622.6171875], [88.9013671875, 622.6171875]]}, {"title": "2 Deep Reinforcement Learning and Transfer Learning", "heading_level": null, "page_id": 2, "polygon": [[89.25, 281.724609375], [408.796875, 281.724609375], [408.796875, 295.259765625], [89.25, 295.259765625]]}, {"title": "2.1 Reinforcement Learning Basics", "heading_level": null, "page_id": 2, "polygon": [[85.763671875, 303.57421875], [264.1640625, 303.57421875], [264.1640625, 315.5625], [85.763671875, 315.5625]]}, {"title": "2.2 Reinforcement Learning Algorithms", "heading_level": null, "page_id": 3, "polygon": [[89.25, 560.25], [285.0, 560.25], [285.0, 571.18359375], [89.25, 571.18359375]]}, {"title": "2.3 Transfer Learning in the Context of Reinforcement Learning", "heading_level": null, "page_id": 4, "polygon": [[89.25, 633.75], [398.25, 633.75], [398.25, 644.66015625], [89.25, 644.66015625]]}, {"title": "2.4 Related Topics", "heading_level": null, "page_id": 5, "polygon": [[89.25, 314.25], [184.5, 314.25], [184.5, 325.6171875], [89.25, 325.6171875]]}, {"title": "3 Analyzing Transfer Learning", "heading_level": null, "page_id": 6, "polygon": [[89.25, 324.0], [269.3935546875, 324.0], [269.3935546875, 337.21875], [89.25, 337.21875]]}, {"title": "3.1 Categorization of Transfer Learning Approaches", "heading_level": null, "page_id": 6, "polygon": [[89.25, 399.8671875], [342.75, 399.8671875], [342.75, 410.6953125], [89.25, 410.6953125]]}, {"title": "3.2 Case Analysis of Transfer Learning in the context of Reinforcement Learning", "heading_level": null, "page_id": 7, "polygon": [[89.25, 383.818359375], [479.25, 383.818359375], [479.25, 394.259765625], [89.25, 394.259765625]]}, {"title": "3.3 Evaluation metrics", "heading_level": null, "page_id": 8, "polygon": [[88.5, 370.08984375], [203.9501953125, 370.08984375], [203.9501953125, 381.69140625], [88.5, 381.69140625]]}, {"title": "4 Related Work", "heading_level": null, "page_id": 9, "polygon": [[89.25, 363.75], [186.0, 363.75], [186.0, 376.6640625], [89.25, 376.6640625]]}, {"title": "5 Transfer Learning Approaches Deep Dive", "heading_level": null, "page_id": 10, "polygon": [[88.67724609375, 64.5], [343.5, 64.5], [343.5, 76.763671875], [88.67724609375, 76.763671875]]}, {"title": "5.1 <PERSON><PERSON>", "heading_level": null, "page_id": 10, "polygon": [[88.9013671875, 168.75], [192.0, 168.75], [192.0, 179.7275390625], [88.9013671875, 179.7275390625]]}, {"title": "5.2 Learning from Demonstrations", "heading_level": null, "page_id": 12, "polygon": [[88.5, 350.25], [261.0, 350.25], [261.0, 360.421875], [88.5, 360.421875]]}, {"title": "5.3 Policy Transfer", "heading_level": null, "page_id": 16, "polygon": [[89.25, 61.5], [185.25, 61.5], [185.25, 72.6064453125], [89.25, 72.6064453125]]}, {"title": "5.4 Inter-Task Mapping", "heading_level": null, "page_id": 18, "polygon": [[89.25, 588.75], [204.75, 588.75], [204.75, 599.4140625], [89.25, 599.4140625]]}, {"title": "5.5 Representation Transfer", "heading_level": null, "page_id": 20, "polygon": [[88.5, 432.75], [228.75, 432.75], [228.75, 443.1796875], [88.5, 443.1796875]]}, {"title": "6 Applications", "heading_level": null, "page_id": 25, "polygon": [[89.25, 132.75], [181.5, 132.0], [181.5, 145.01953125], [89.25, 145.01953125]]}, {"title": "Large Model Training:", "heading_level": null, "page_id": 26, "polygon": [[89.25, 61.5], [194.25, 61.5], [194.25, 72.**********], [89.25, 72.**********]]}, {"title": "Health Informatics:", "heading_level": null, "page_id": 26, "polygon": [[89.25, 204.0], [181.5, 204.0], [181.5, 215.015625], [89.25, 215.015625]]}, {"title": "Others:", "heading_level": null, "page_id": 26, "polygon": [[89.25, 389.25], [126.0, 389.25], [126.0, 399.48046875], [89.25, 399.48046875]]}, {"title": "7 Future Perspectives", "heading_level": null, "page_id": 26, "polygon": [[89.25, 548.25], [224.419921875, 548.25], [224.419921875, 559.96875], [89.25, 559.96875]]}, {"title": "Transfer Learning from Black-Box:", "heading_level": null, "page_id": 26, "polygon": [[89.25, 623.25], [255.0, 623.25], [255.0, 634.21875], [89.25, 634.21875]]}, {"title": "Framework-Agnostic Knowledge Transfer:", "heading_level": null, "page_id": 27, "polygon": [[89.25, 448.5], [291.0, 448.5], [291.0, 458.6484375], [89.25, 458.6484375]]}, {"title": "Evaluation and Benchmarking:", "heading_level": null, "page_id": 27, "polygon": [[89.25, 548.25], [237.0, 548.25], [237.0, 558.80859375], [89.25, 558.80859375]]}, {"title": "Knowledge Transfer to and from Pre-Trained Large Models:", "heading_level": null, "page_id": 28, "polygon": [[89.25, 61.5], [371.7421875, 61.5], [371.7421875, 72.84814453125], [89.25, 72.84814453125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 28, "polygon": [[89.25, 402.0], [203.80078125, 402.0], [203.80078125, 413.40234375], [89.25, 413.40234375]]}, {"title": "Biographies", "heading_level": null, "page_id": 28, "polygon": [[89.25, 457.5], [160.0224609375, 457.5], [160.0224609375, 469.4765625], [89.25, 469.4765625]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 29, "polygon": [[87.93017578125, 639.6328125], [173.25, 639.6328125], [173.25, 652.0078125], [87.93017578125, 652.0078125]]}, {"title": "TABLE 2:", "heading_level": null, "page_id": 40, "polygon": [[53.25, 344.56640625], [68.095458984375, 344.56640625], [68.095458984375, 389.25], [53.25, 389.25]]}, {"title": "TABLE 3:", "heading_level": null, "page_id": 41, "polygon": [[53.25, 345.33984375], [67.236328125, 345.33984375], [67.236328125, 390.0], [53.25, 390.0]]}, {"title": "TABLE 4:", "heading_level": null, "page_id": 42, "polygon": [[53.25, 341.859375], [68.25, 341.859375], [68.25, 391.5], [53.25, 391.5]]}, {"title": "TABLE 5:", "heading_level": null, "page_id": 43, "polygon": [[53.25, 342.052734375], [67.348388671875, 342.052734375], [67.348388671875, 389.25], [53.25, 389.25]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 47], ["Text", 10], ["SectionHeader", 9], ["<PERSON><PERSON><PERSON><PERSON>", 4], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8999, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 5], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 394], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["ListItem", 6], ["Text", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 434], ["Line", 54], ["TextInlineMath", 6], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 4], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 50], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 320], ["Line", 53], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 4], ["Equation", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 4], ["SectionHeader", 2], ["ListItem", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 198], ["Line", 50], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 2], ["Footnote", 2], ["ListGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 46], ["ListItem", 12], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 4], ["ListGroup", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 4], ["ListItem", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 239], ["Line", 46], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 5], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 45], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["TextInlineMath", 4], ["Text", 3], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 5], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 52], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 4], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 400], ["Line", 60], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 4], ["Text", 4], ["Equation", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 57], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 5], ["TextInlineMath", 2], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 442], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["TextInlineMath", 4], ["Text", 3], ["SectionHeader", 1], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 519], ["Line", 74], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Equation", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 399], ["Line", 51], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 326], ["Line", 52], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 50], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 6], ["TextInlineMath", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 274], ["Line", 48], ["TextInlineMath", 5], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 5]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 4], ["Text", 3], ["Equation", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 54], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 5], ["TextInlineMath", 3], ["Equation", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 5], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 51], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 6], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 50], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 6], ["SectionHeader", 5]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 49], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 4], ["ListItem", 3], ["SectionHeader", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 42], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["SectionHeader", 3], ["Text", 3], ["ListItem", 2], ["Picture", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 568, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 37], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Text", 4], ["Picture", 3], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1676, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 62], ["ListItem", 24], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 62], ["ListItem", 24], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 63], ["ListItem", 23], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 62], ["ListItem", 26], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 63], ["ListItem", 26], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 62], ["ListItem", 25], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 63], ["ListItem", 22], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["Text", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 37], ["ListItem", 12], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Line", 39], ["Span", 23], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 3], ["Figure", 3]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 1866, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 38], ["<PERSON><PERSON><PERSON><PERSON>", 7], ["Text", 3], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["TableCell", 115], ["Line", 48], ["<PERSON><PERSON><PERSON><PERSON>", 7], ["SectionHeader", 1], ["Text", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6276, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 29], ["TableCell", 22], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["Table", 2], ["SectionHeader", 1], ["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5505, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 64], ["TableCell", 48], ["<PERSON><PERSON><PERSON><PERSON>", 6], ["SectionHeader", 1], ["Text", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 47], ["<PERSON><PERSON><PERSON><PERSON>", 5], ["Text", 2], ["SectionHeader", 1], ["Table", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Transfer Learning in Deep Reinforcement Learning- A Survey"}