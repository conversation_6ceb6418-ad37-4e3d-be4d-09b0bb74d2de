{"table_of_contents": [{"title": "Trust-Aware Diversion for Data-Effective Distillation", "heading_level": null, "page_id": 0, "polygon": [[135.75, 89.25], [460.5, 89.25], [460.5, 103.833984375], [135.75, 103.833984375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.44287109375, 185.25], [195.75, 185.25], [195.75, 195.873046875], [148.44287109375, 195.873046875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 573.0], [132.75, 573.0], [132.75, 584.33203125], [54.0, 584.33203125]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[305.25, 294.0], [391.5, 294.0], [391.5, 304.927734375], [305.25, 304.927734375]]}, {"title": "3. Preliminary and Problem Setup", "heading_level": null, "page_id": 2, "polygon": [[54.0, 580.5], [231.0, 580.5], [231.0, 592.06640625], [54.0, 592.06640625]]}, {"title": "4. Method", "heading_level": null, "page_id": 2, "polygon": [[305.25, 651.0], [359.25, 651.0], [359.25, 662.0625], [305.25, 662.0625]]}, {"title": "4.1. Outer Loop: Diverting Distillation for Trustworthy\nLearning", "heading_level": null, "page_id": 3, "polygon": [[54.0, 468.75], [289.86328125, 468.75], [289.86328125, 491.1328125], [54.0, 491.1328125]]}, {"title": "4.2. Inner Loop: Trust-Aware Recalibration", "heading_level": null, "page_id": 4, "polygon": [[54.0, 509.25], [243.0, 509.25], [243.0, 518.58984375], [54.0, 518.58984375]]}, {"title": "5. Experiments", "heading_level": null, "page_id": 4, "polygon": [[306.0, 644.25], [385.5, 644.25], [385.5, 655.48828125], [306.0, 655.48828125]]}, {"title": "5.1. Datasets", "heading_level": null, "page_id": 4, "polygon": [[305.25, 664.5], [362.478515625, 664.5], [362.478515625, 675.2109375], [305.25, 675.2109375]]}, {"title": "5.2. Experimental Setup", "heading_level": null, "page_id": 6, "polygon": [[54.0, 118.5], [159.0, 118.5], [159.0, 128.1005859375], [54.0, 128.1005859375]]}, {"title": "5.3. Results", "heading_level": null, "page_id": 6, "polygon": [[54.0, 329.25], [104.25, 329.25], [104.25, 339.732421875], [54.0, 339.732421875]]}, {"title": "5.4. Ablation Study", "heading_level": null, "page_id": 7, "polygon": [[54.0, 369.75], [138.0, 369.75], [138.0, 380.53125], [54.0, 380.53125]]}, {"title": "5.5. Comp<PERSON>on with Learning from Noisy Labels", "heading_level": null, "page_id": 7, "polygon": [[54.0, 628.5], [269.25, 628.5], [269.25, 638.859375], [54.0, 638.859375]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 7, "polygon": [[306.0, 353.25], [377.25, 353.25], [377.25, 364.2890625], [306.0, 364.2890625]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 7, "polygon": [[305.8505859375, 641.25], [399.75, 641.25], [399.75, 651.62109375], [305.8505859375, 651.62109375]]}, {"title": "References", "heading_level": null, "page_id": 8, "polygon": [[54.0, 68.25], [111.75, 68.25], [111.75, 79.32568359375], [54.0, 79.32568359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 256], ["Line", 95], ["Text", 7], ["SectionHeader", 3], ["Figure", 1], ["Caption", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4188, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 313], ["Line", 123], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 737, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 746], ["Line", 124], ["Text", 7], ["TextInlineMath", 6], ["Equation", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 391], ["Line", 106], ["Text", 5], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 835, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 831], ["Line", 134], ["TextInlineMath", 6], ["Text", 4], ["Equation", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1036, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 1479], ["TableCell", 493], ["Line", 192], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 509], ["Line", 108], ["TableCell", 93], ["Text", 7], ["SectionHeader", 2], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 2, "llm_tokens_used": 11487, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 422], ["TableCell", 137], ["Line", 98], ["Text", 7], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8304, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 97], ["ListItem", 23], ["Reference", 23], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 94], ["ListItem", 23], ["Reference", 23], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 60], ["ListItem", 14], ["Reference", 14], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Trust-Aware Diversion for Data-Effective Distillation "}