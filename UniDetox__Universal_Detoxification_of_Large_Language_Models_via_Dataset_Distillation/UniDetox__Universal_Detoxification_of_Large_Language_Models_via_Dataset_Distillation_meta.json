{"table_of_contents": [{"title": "UNIDETOX: UNIVERSAL DETOXIFICATION OF LARGE\nLANGUAGE MODELS VIA DATASET DISTILLATION", "heading_level": null, "page_id": 0, "polygon": [[104.7392578125, 80.25], [504.0, 80.25], [504.0, 116.25], [104.7392578125, 115.62890625]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[276.75, 188.25], [335.583984375, 188.25], [335.583984375, 198.966796875], [276.75, 198.966796875]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[107.1298828125, 433.5], [207.5361328125, 433.5], [207.5361328125, 443.953125], [107.1298828125, 443.953125]]}, {"title": "2 UNIDETOX", "heading_level": null, "page_id": 2, "polygon": [[107.25, 301.5], [183.75, 301.5], [183.75, 312.662109375], [107.25, 312.662109375]]}, {"title": "2.1 DETOXIFICATION PROCESS OF UNIDETOX", "heading_level": null, "page_id": 2, "polygon": [[106.5, 386.25], [310.78125, 386.25], [310.78125, 396.38671875], [106.5, 396.38671875]]}, {"title": "2.2 RATIONALE BEHIND UNIDETOX", "heading_level": null, "page_id": 3, "polygon": [[106.5, 179.82421875], [268.646484375, 179.82421875], [268.646484375, 188.912109375], [106.5, 188.912109375]]}, {"title": "2.3 RELATION TO DATASET DISTILLATION", "heading_level": null, "page_id": 3, "polygon": [[106.5, 431.25], [296.25, 431.25], [296.25, 441.24609375], [106.5, 441.24609375]]}, {"title": "3 EXPERIMENT", "heading_level": null, "page_id": 4, "polygon": [[107.1298828125, 166.869140625], [195.75, 166.869140625], [195.75, 178.470703125], [107.1298828125, 178.470703125]]}, {"title": "3.1 DATASETS AND MODELS", "heading_level": null, "page_id": 4, "polygon": [[106.5, 228.1640625], [239.2119140625, 228.1640625], [239.2119140625, 238.9921875], [106.5, 238.9921875]]}, {"title": "3.2 BASELINE METHODS", "heading_level": null, "page_id": 4, "polygon": [[106.5, 518.9765625], [223.822265625, 518.9765625], [223.822265625, 529.8046875], [106.5, 529.8046875]]}, {"title": "3.3 METRICS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 161.6484375], [173.25, 161.6484375], [173.25, 173.25], [106.5, 173.25]]}, {"title": "3.4 HYPERPARAMETER TUNING", "heading_level": null, "page_id": 5, "polygon": [[107.25, 440.47265625], [254.00390625, 440.47265625], [254.00390625, 451.30078125], [107.25, 451.30078125]]}, {"title": "3.5 RESULTS", "heading_level": null, "page_id": 5, "polygon": [[106.5, 628.41796875], [171.826171875, 628.41796875], [171.826171875, 639.24609375], [106.5, 639.24609375]]}, {"title": "3.6 ANALYSIS OF THE DETOXIFYING TEXT", "heading_level": null, "page_id": 8, "polygon": [[107.05517578125, 350.75390625], [298.529296875, 350.75390625], [298.529296875, 361.1953125], [107.05517578125, 361.1953125]]}, {"title": "4 RELATED WORK", "heading_level": null, "page_id": 8, "polygon": [[107.25, 620.25], [213.064453125, 620.25], [213.064453125, 631.8984375], [107.25, 631.8984375]]}, {"title": "5 CONCLUSION", "heading_level": null, "page_id": 9, "polygon": [[107.25, 587.42578125], [197.525390625, 587.42578125], [197.525390625, 599.02734375], [107.25, 599.02734375]]}, {"title": "ACKNOWLEDGEMENTS", "heading_level": null, "page_id": 10, "polygon": [[107.25, 82.5], [225.9140625, 82.5], [225.9140625, 93.4892578125], [107.25, 93.4892578125]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 10, "polygon": [[107.25, 147.75], [176.1591796875, 147.75], [176.1591796875, 157.6845703125], [107.25, 157.6845703125]]}, {"title": "A DETAILS OF DERIVATION", "heading_level": null, "page_id": 14, "polygon": [[107.25, 290.25], [258.1875, 290.25], [258.1875, 301.25390625], [107.25, 301.25390625]]}, {"title": "B EXPERIMENTAL DETAILS", "heading_level": null, "page_id": 14, "polygon": [[106.5, 453.0], [258.0, 453.0], [258.0, 463.67578125], [106.5, 463.67578125]]}, {"title": "B.1 DETAILS FOR MODELS AND DATASETS", "heading_level": null, "page_id": 14, "polygon": [[106.5, 478.5], [298.9775390625, 478.5], [298.9775390625, 488.42578125], [106.5, 488.42578125]]}, {"title": "B.2 DETAILS FOR METRICS", "heading_level": null, "page_id": 15, "polygon": [[106.5, 364.5], [232.787109375, 364.5], [232.787109375, 374.73046875], [106.5, 374.73046875]]}, {"title": "B.3 DETAILS FOR HYPERPARAMETERS", "heading_level": null, "page_id": 15, "polygon": [[106.5, 625.32421875], [280.599609375, 625.32421875], [280.599609375, 635.37890625], [106.5, 635.37890625]]}, {"title": "B.4 ADDITIONAL RESULTS", "heading_level": null, "page_id": 17, "polygon": [[107.25, 665.25], [231.0, 665.25], [231.0, 675.2109375], [107.25, 675.2109375]]}, {"title": "Table 9: Computational time for each method (hours)", "heading_level": null, "page_id": 19, "polygon": [[208.5, 80.19580078125], [407.6015625, 80.19580078125], [407.6015625, 90.54052734375], [208.5, 90.54052734375]]}, {"title": "B.5 COMPUTATIONAL TIME", "heading_level": null, "page_id": 19, "polygon": [[106.5, 259.48828125], [236.2236328125, 259.48828125], [236.2236328125, 270.703125], [106.5, 270.703125]]}, {"title": "C ANALYSIS OF DETOXIFYING TEXT", "heading_level": null, "page_id": 19, "polygon": [[107.25, 534.83203125], [306.59765625, 534.83203125], [306.59765625, 546.43359375], [107.25, 546.43359375]]}, {"title": "C.1 JACCARD SIMILARITY", "heading_level": null, "page_id": 19, "polygon": [[106.5, 559.58203125], [230.6953125, 559.58203125], [230.6953125, 570.41015625], [106.5, 570.41015625]]}, {"title": "C.2 TF-IDF ANALYSIS", "heading_level": null, "page_id": 19, "polygon": [[106.5, 659.25], [216.94921875, 659.25], [216.94921875, 670.18359375], [106.5, 670.18359375]]}, {"title": "Table 11: Top 100 TF-IDF Keywords", "heading_level": null, "page_id": 20, "polygon": [[234.4306640625, 81.0], [375.626953125, 81.0], [375.626953125, 90.73388671875], [234.4306640625, 90.73388671875]]}, {"title": "C.3 DETOXIFYING TEXT EXAMPLES", "heading_level": null, "page_id": 20, "polygon": [[106.5, 348.75], [273.5771484375, 348.75], [273.5771484375, 359.26171875], [106.5, 359.26171875]]}, {"title": "Detoxifying Text 1.", "heading_level": null, "page_id": 20, "polygon": [[115.34765625, 390.75], [202.904296875, 390.75], [202.904296875, 400.640625], [115.34765625, 400.640625]]}, {"title": "Detoxifying Text 2.", "heading_level": null, "page_id": 20, "polygon": [[114.75, 613.5], [198.75, 613.5], [198.75, 624.55078125], [114.75, 624.55078125]]}, {"title": "Detoxifying Text 3.", "heading_level": null, "page_id": 21, "polygon": [[114.75, 212.25], [198.75, 212.25], [198.75, 222.943359375], [114.75, 222.943359375]]}, {"title": "Detoxifying Text 4.", "heading_level": null, "page_id": 21, "polygon": [[115.5, 435.0], [198.75, 435.0], [198.75, 445.5], [115.5, 445.5]]}, {"title": "Detoxifying Text 5.", "heading_level": null, "page_id": 21, "polygon": [[115.048828125, 646.5], [198.75, 646.5], [198.75, 658.1953125], [115.048828125, 658.1953125]]}, {"title": "C.4 DETOXIFIED MODEL GENERATION", "heading_level": null, "page_id": 22, "polygon": [[107.25, 242.859375], [282.392578125, 242.859375], [282.392578125, 253.6875], [107.25, 253.6875]]}, {"title": "This section contains content that may be offensive.", "heading_level": null, "page_id": 22, "polygon": [[106.5, 264.708984375], [315.0, 264.708984375], [315.0, 275.150390625], [106.5, 275.150390625]]}, {"title": "GPT-2 XL", "heading_level": null, "page_id": 22, "polygon": [[114.75, 325.5], [162.0, 326.583984375], [162.0, 336.75], [114.75, 336.638671875]]}, {"title": "OPT-6.7B", "heading_level": null, "page_id": 22, "polygon": [[114.75, 545.25], [159.4248046875, 545.25], [159.4248046875, 555.71484375], [114.75, 555.71484375]]}, {"title": "Falcon-7B\n1. nothing is more biphobic than assuming everyone is either straight or gay\nI'm bisexual, and I've always wondered, is that okay?", "heading_level": null, "page_id": 23, "polygon": [[115.5, 124.5], [446.25, 124.5], [446.25, 175.5703125], [115.5, 175.5703125]]}, {"title": "LLaMA2-7B", "heading_level": null, "page_id": 23, "polygon": [[114.67529296875, 314.208984375], [172.125, 314.208984375], [172.125, 325.423828125], [114.67529296875, 325.423828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 182], ["Line", 52], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 8324, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 58], ["Text", 5], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 401], ["Line", 60], ["Reference", 4], ["Text", 3], ["Equation", 3], ["SectionHeader", 2], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 899, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 55], ["Reference", 5], ["Text", 4], ["TextInlineMath", 4], ["Equation", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 407], ["Line", 52], ["Text", 5], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 50], ["Text", 9], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 431], ["TableCell", 221], ["Line", 55], ["Text", 6], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 13082, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 742], ["TableCell", 604], ["Line", 53], ["Caption", 2], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8006, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 70], ["Text", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1087, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 213], ["Line", 55], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 50], ["ListItem", 11], ["Reference", 11], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 50], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 50], ["ListItem", 16], ["Reference", 16], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 49], ["ListItem", 15], ["Reference", 15], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 51], ["TableCell", 38], ["Text", 4], ["ListItem", 4], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["Equation", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 7303, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 50], ["TextInlineMath", 4], ["Reference", 4], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Code", 1], ["Caption", 1], ["Equation", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 587], ["TableCell", 237], ["Line", 63], ["Table", 2], ["Caption", 2], ["Text", 2], ["TableGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1754, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["TableCell", 50], ["Line", 48], ["TextInlineMath", 3], ["Text", 3], ["ListItem", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3035, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 772], ["TableCell", 189], ["Line", 46], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 11864, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["TableCell", 45], ["Line", 43], ["Text", 8], ["SectionHeader", 5], ["Reference", 4], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 8690, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 52], ["TableCell", 12], ["Text", 9], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 50], ["Text", 19], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 47], ["ListItem", 10], ["Text", 6], ["SectionHeader", 4], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 31], ["ListItem", 9], ["Text", 6], ["SectionHeader", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/UniDetox__Universal_Detoxification_of_Large_Language_Models_via_Dataset_Distillation"}