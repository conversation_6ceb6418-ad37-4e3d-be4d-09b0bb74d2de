{"table_of_contents": [{"title": "Unlocking the Potential of Federated Learning: The Symphony of Dataset\nDistillation via Deep Generative Latents", "heading_level": null, "page_id": 0, "polygon": [[69.4775390625, 106.5], [524.25, 106.5], [524.25, 137.478515625], [69.4775390625, 137.478515625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[144.75, 259.5], [191.25, 259.5], [191.25, 270.703125], [144.75, 270.703125]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[48.75, 645.75], [127.5, 645.75], [127.5, 657.03515625], [48.75, 657.03515625]]}, {"title": "2. Related Work", "heading_level": null, "page_id": 1, "polygon": [[307.494140625, 420.0], [392.25, 420.0], [392.25, 431.19140625], [307.494140625, 431.19140625]]}, {"title": "3. Method", "heading_level": null, "page_id": 2, "polygon": [[48.0, 422.25], [102.0, 422.25], [102.0, 433.8984375], [48.0, 433.8984375]]}, {"title": "3.1. Overview", "heading_level": null, "page_id": 2, "polygon": [[48.75, 444.0], [114.75, 444.0], [114.75, 454.39453125], [48.75, 454.39453125]]}, {"title": "Algorithm 1 The FedDGM Framework", "heading_level": null, "page_id": 2, "polygon": [[307.5, 73.5], [468.0, 73.5], [468.0, 83.53125], [307.5, 83.53125]]}, {"title": "3.2. Problem Formulation", "heading_level": null, "page_id": 2, "polygon": [[307.5, 635.25], [430.5, 635.25], [430.5, 645.43359375], [307.5, 645.43359375]]}, {"title": "3.3. Global Data Distillation", "heading_level": null, "page_id": 3, "polygon": [[48.0, 659.25], [181.5, 659.25], [181.5, 670.18359375], [48.0, 670.18359375]]}, {"title": "3.3.1 Distillation via Matching Training Trajectories", "heading_level": null, "page_id": 3, "polygon": [[307.5, 73.5], [538.5, 73.5], [538.5, 83.6279296875], [307.5, 83.6279296875]]}, {"title": "3.3.2 Optimization via Deep Generative Latents", "heading_level": null, "page_id": 4, "polygon": [[48.0, 74.25], [261.0, 74.25], [261.0, 83.6279296875], [48.0, 83.6279296875]]}, {"title": "4. Theoretical Analysis", "heading_level": null, "page_id": 4, "polygon": [[307.5, 111.0], [426.0, 111.0], [426.0, 122.8798828125], [307.5, 122.8798828125]]}, {"title": "5. <PERSON>", "heading_level": null, "page_id": 5, "polygon": [[48.75, 653.25], [122.44482421875, 653.25], [122.44482421875, 664.3828125], [48.75, 664.3828125]]}, {"title": "5.1. Experimental Setup", "heading_level": null, "page_id": 5, "polygon": [[48.75, 672.75], [162.75, 672.75], [162.75, 683.33203125], [48.75, 683.33203125]]}, {"title": "5.2. Performance and Convergence Rate", "heading_level": null, "page_id": 6, "polygon": [[48.75, 540.6328125], [238.5, 540.6328125], [238.5, 551.4609375], [48.75, 551.4609375]]}, {"title": "5.3. Impact of Hyperparameters", "heading_level": null, "page_id": 8, "polygon": [[48.0, 600.0], [201.75, 600.0], [201.75, 611.015625], [48.0, 611.015625]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 9, "polygon": [[307.494140625, 633.75], [378.75, 633.75], [378.75, 644.66015625], [307.494140625, 646.20703125]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[48.75, 369.0], [106.5, 369.0], [106.5, 380.337890625], [48.75, 380.337890625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 199], ["Line", 82], ["Text", 9], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4087, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 114], ["ListItem", 7], ["Text", 6], ["ListGroup", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 811, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 844], ["Line", 100], ["TextInlineMath", 5], ["SectionHeader", 4], ["Text", 3], ["Reference", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 602], ["Line", 123], ["TextInlineMath", 4], ["Equation", 2], ["SectionHeader", 2], ["Text", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 716, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 664], ["Line", 154], ["Text", 10], ["Equation", 5], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 889], ["Line", 140], ["TextInlineMath", 10], ["Equation", 6], ["Text", 4], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 938], ["TableCell", 294], ["Line", 95], ["Reference", 5], ["Text", 4], ["Caption", 3], ["Table", 1], ["SectionHeader", 1], ["Picture", 1], ["Figure", 1], ["TableGroup", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 13311, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 1324], ["TableCell", 408], ["Line", 104], ["Caption", 3], ["Text", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 10104, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Line", 132], ["Span", 130], ["Caption", 4], ["Text", 4], ["Reference", 4], ["Figure", 3], ["FigureGroup", 3], ["SectionHeader", 1], ["Picture", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 2966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 204], ["Line", 100], ["Text", 7], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2412, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 327], ["TableCell", 132], ["Line", 114], ["ListItem", 17], ["Reference", 17], ["ListGroup", 2], ["Caption", 1], ["Table", 1], ["Text", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 88], ["ListItem", 23], ["Reference", 22], ["ListGroup", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Unlocking_the_Potential_of_Federated_Learning__The_Symphony_of_Dataset_Distillation_via_Deep_Generat"}