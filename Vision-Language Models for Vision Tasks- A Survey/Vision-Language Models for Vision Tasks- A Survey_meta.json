{"table_of_contents": [{"title": "Vision-Language Models for Vision Tasks:\nA Survey", "heading_level": null, "page_id": 0, "polygon": [[82.5, 56.25], [532.212890625, 56.25], [532.212890625, 107.9912109375], [82.5, 107.9912109375]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[48.0, 334.5], [140.44921875, 334.5], [140.44921875, 344.953125], [48.0, 344.953125]]}, {"title": "2 BACKGROUND", "heading_level": null, "page_id": 2, "polygon": [[47.25, 141.75], [137.08740234375, 141.75], [137.08740234375, 152.3671875], [47.25, 152.3671875]]}, {"title": "2.1 Training Paradigms for Visual Recognition", "heading_level": null, "page_id": 2, "polygon": [[46.5, 253.5], [262.5, 253.5], [262.5, 263.548828125], [46.5, 263.548828125]]}, {"title": "2.1.1 Traditional Machine Learning and Prediction", "heading_level": null, "page_id": 2, "polygon": [[46.5, 372.75], [264.75, 372.75], [264.75, 382.271484375], [46.5, 382.271484375]]}, {"title": "2.1.2 Deep Learning from Scratch and Prediction", "heading_level": null, "page_id": 2, "polygon": [[46.84130859375, 489.0], [262.5, 489.0], [262.5, 498.8671875], [46.84130859375, 498.8671875]]}, {"title": "2.1.3 Supervised Pre-training, Fine-tuning and Prediction", "heading_level": null, "page_id": 2, "polygon": [[46.5, 675.75], [296.25, 675.75], [296.25, 686.42578125], [46.5, 686.42578125]]}, {"title": "2.1.5 VLM Pre-training and Zero-shot Prediction", "heading_level": null, "page_id": 2, "polygon": [[311.25, 549.0], [522.0, 549.0], [522.0, 559.1953125], [311.25, 559.1953125]]}, {"title": "2.2 Development of VLMs for Visual Recognition", "heading_level": null, "page_id": 3, "polygon": [[46.5, 444.75], [273.75, 444.75], [273.75, 454.39453125], [46.5, 454.39453125]]}, {"title": "2.3 Relevant Surveys", "heading_level": null, "page_id": 3, "polygon": [[45.75, 711.0], [149.5634765625, 711.0], [149.5634765625, 720.84375], [45.75, 720.84375]]}, {"title": "3 VLM FOUNDATIONS", "heading_level": null, "page_id": 3, "polygon": [[310.18359375, 507.0], [429.71484375, 507.0], [429.71484375, 518.203125], [310.18359375, 518.203125]]}, {"title": "3.1 Network Architectures", "heading_level": null, "page_id": 3, "polygon": [[310.5, 710.25], [436.5, 710.25], [436.5, 719.68359375], [310.5, 719.68359375]]}, {"title": "3.1.1 Architectures for Learning Image Features", "heading_level": null, "page_id": 4, "polygon": [[46.6171875, 145.5], [257.25, 145.5], [257.25, 154.7841796875], [46.6171875, 154.7841796875]]}, {"title": "3.1.2 Architectures for Learning Language Features", "heading_level": null, "page_id": 4, "polygon": [[46.5, 477.75], [273.75, 477.75], [273.75, 487.65234375], [46.5, 487.65234375]]}, {"title": "3.2 VLM Pre-training Objectives", "heading_level": null, "page_id": 4, "polygon": [[45.75, 618.75], [198.75, 618.75], [198.75, 629.19140625], [45.75, 629.19140625]]}, {"title": "3.2.1 Contrastive Objectives", "heading_level": null, "page_id": 4, "polygon": [[47.25, 699.0], [174.0, 699.0], [174.0, 709.2421875], [47.25, 709.2421875]]}, {"title": "3.2.2 Generative Objectives", "heading_level": null, "page_id": 4, "polygon": [[310.78125, 588.75], [436.5, 588.75], [436.5, 599.02734375], [310.78125, 599.02734375]]}, {"title": "3.2.3 Alignment Objectives", "heading_level": null, "page_id": 5, "polygon": [[47.102783203125, 412.5], [168.75, 412.5], [168.75, 422.68359375], [47.102783203125, 422.68359375]]}, {"title": "3.3 VLM Pre-training Frameworks", "heading_level": null, "page_id": 5, "polygon": [[45.75, 675.75], [205.5, 675.75], [205.5, 685.265625], [45.75, 685.265625]]}, {"title": "3.4 Evaluation Setups and Downstream Tasks", "heading_level": null, "page_id": 5, "polygon": [[311.25, 298.5], [525.75, 298.5], [525.75, 308.794921875], [311.25, 308.794921875]]}, {"title": "3.4.1 Zero-shot Prediction", "heading_level": null, "page_id": 5, "polygon": [[311.25, 384.0], [429.0, 384.0], [429.0, 393.486328125], [311.25, 393.486328125]]}, {"title": "3.4.2 Linear Probing", "heading_level": null, "page_id": 5, "polygon": [[311.25, 687.0], [405.75, 687.0], [405.75, 696.8671875], [311.25, 696.8671875]]}, {"title": "4 DATASETS", "heading_level": null, "page_id": 6, "polygon": [[47.25, 301.5], [117.0, 301.5], [117.0, 312.275390625], [47.25, 312.275390625]]}, {"title": "4.1 Datasets for Pre-training VLMs", "heading_level": null, "page_id": 6, "polygon": [[46.5, 364.5], [210.0, 364.5], [210.0, 374.150390625], [46.5, 374.150390625]]}, {"title": "4.2 Datasets for VLM Evaluation", "heading_level": null, "page_id": 6, "polygon": [[46.5, 539.25], [199.5, 539.25], [199.5, 549.52734375], [46.5, 549.52734375]]}, {"title": "5 VISION-LANGUAGE MODEL PRE-TRAINING", "heading_level": null, "page_id": 6, "polygon": [[47.21484375, 682.5], [275.25, 682.5], [275.25, 694.16015625], [47.21484375, 694.16015625]]}, {"title": "5.1 VLM Pre-Training with Contrastive Objectives", "heading_level": null, "page_id": 6, "polygon": [[310.5, 243.75], [540.75, 243.75], [540.75, 253.880859375], [310.5, 253.880859375]]}, {"title": "5.1.1 Image Contrastive Learning", "heading_level": null, "page_id": 6, "polygon": [[311.25, 304.5], [460.5, 304.5], [460.5, 314.208984375], [311.25, 314.208984375]]}, {"title": "5.1.2 Image-Text Contrastive Learning", "heading_level": null, "page_id": 6, "polygon": [[311.25, 385.5], [480.75, 385.5], [480.75, 395.61328125], [311.25, 395.61328125]]}, {"title": "JOURNAL OF L<sup>o</sup>TEX CLASS FILES,MARCH 2023 8 A 2009 1999 1999 1999 1999 1999 1999 1999", "heading_level": null, "page_id": 7, "polygon": [[47.25, 26.25], [564.75, 26.25], [564.75, 36.279052734375], [47.25, 36.279052734375]]}, {"title": "5.1.3 Image-Text-Label Contrastive Learning", "heading_level": null, "page_id": 7, "polygon": [[311.25, 464.8359375], [506.25, 464.8359375], [506.25, 474.1171875], [311.25, 474.1171875]]}, {"title": "5.1.4 Discussion", "heading_level": null, "page_id": 7, "polygon": [[311.25, 618.75], [390.0, 618.75], [390.0, 628.03125], [311.25, 628.03125]]}, {"title": "JOURNAL OF LATEX CLASS FILES, MARCH 2023 AND A SERVICE STOLEN ASSESSED FOR A SERVICE STOLEN ASSESSED.", "heading_level": null, "page_id": 8, "polygon": [[46.5, 26.25], [564.0, 26.25], [564.0, 35.384765625], [46.5, 35.384765625]]}, {"title": "5.2 VLM Pre-training with Generative Objectives", "heading_level": null, "page_id": 8, "polygon": [[46.5, 589.5], [271.5, 589.5], [271.5, 599.80078125], [46.5, 599.80078125]]}, {"title": "5.2.1 Masked Image Modelling", "heading_level": null, "page_id": 8, "polygon": [[47.25, 664.5], [184.5, 664.5], [184.5, 674.05078125], [47.25, 674.05078125]]}, {"title": "5.2.2 Masked Language Modelling", "heading_level": null, "page_id": 8, "polygon": [[311.25, 614.8828125], [465.0, 614.8828125], [465.0, 624.9375], [311.25, 624.9375]]}, {"title": "5.2.3 Masked Cross-Modal Modelling", "heading_level": null, "page_id": 9, "polygon": [[47.25, 177.0], [212.25, 178.5], [212.25, 188.25], [47.25, 187.4619140625]]}, {"title": "5.2.4 Image-to-Text Generation", "heading_level": null, "page_id": 9, "polygon": [[47.25, 334.5], [187.5, 334.5], [187.5, 343.79296875], [47.25, 343.79296875]]}, {"title": "5.2.5 Discussion", "heading_level": null, "page_id": 9, "polygon": [[47.25, 651.0], [125.25, 651.0], [125.25, 661.2890625], [47.25, 661.2890625]]}, {"title": "5.3 VLM Pre-training with Alignment Objectives", "heading_level": null, "page_id": 9, "polygon": [[311.25, 43.5], [533.109375, 43.5], [533.109375, 53.25], [311.25, 53.25]]}, {"title": "5.3.1 Image-Text Matching", "heading_level": null, "page_id": 9, "polygon": [[311.25, 128.25], [431.5078125, 128.25], [431.5078125, 138.05859375], [311.25, 138.05859375]]}, {"title": "5.3.2 Region-Word Matching", "heading_level": null, "page_id": 9, "polygon": [[310.18359375, 389.25], [440.47265625, 389.25], [440.47265625, 398.70703125], [310.18359375, 398.70703125]]}, {"title": "5.3.3 Discussion", "heading_level": null, "page_id": 9, "polygon": [[311.25, 507.0], [389.671875, 507.0], [389.671875, 516.65625], [311.25, 516.65625]]}, {"title": "5.4 Summary and Discussion", "heading_level": null, "page_id": 9, "polygon": [[311.25, 652.5], [451.5, 652.5], [451.5, 662.0625], [311.25, 662.0625]]}, {"title": "6 VLM TRANSFER LEARNING", "heading_level": null, "page_id": 10, "polygon": [[46.99072265625, 518.9765625], [202.5, 518.9765625], [202.5, 529.8046875], [46.99072265625, 529.8046875]]}, {"title": "6.1 Motivation of Transfer learning", "heading_level": null, "page_id": 10, "polygon": [[45.75, 652.39453125], [208.5, 652.39453125], [208.5, 662.44921875], [45.75, 662.44921875]]}, {"title": "6.2 Common Setup of Transfer Learning", "heading_level": null, "page_id": 10, "polygon": [[311.080078125, 460.1953125], [500.25, 460.1953125], [500.25, 470.25], [311.080078125, 470.25]]}, {"title": "6.3 Common Transfer Learning Methods", "heading_level": null, "page_id": 10, "polygon": [[310.5, 604.5], [501.0, 604.5], [501.0, 614.49609375], [310.5, 614.49609375]]}, {"title": "6.3.1 Transfer via Prompt Tuning", "heading_level": null, "page_id": 10, "polygon": [[311.25, 665.15625], [455.25, 665.15625], [455.25, 674.4375], [311.25, 674.4375]]}, {"title": "6.3.2 Transfer via Feature Adaptation", "heading_level": null, "page_id": 11, "polygon": [[311.080078125, 538.5], [476.25, 538.5], [476.25, 548.75390625], [311.080078125, 548.75390625]]}, {"title": "6.3.3 Other Transfer Methods", "heading_level": null, "page_id": 12, "polygon": [[46.5, 132.75], [179.25, 132.75], [179.25, 142.9892578125], [46.5, 142.9892578125]]}, {"title": "6.4 Summary and Discussion", "heading_level": null, "page_id": 12, "polygon": [[45.75, 354.75], [187.5, 354.75], [187.5, 364.869140625], [45.75, 364.869140625]]}, {"title": "7 VLM KNOWLEDGE DISTILLATION", "heading_level": null, "page_id": 12, "polygon": [[46.84130859375, 498.75], [229.5, 498.75], [229.5, 509.6953125], [46.84130859375, 509.6953125]]}, {"title": "7.1 Motivation of Distilling Knowledge from VLMs", "heading_level": null, "page_id": 12, "polygon": [[45.75, 618.0], [278.25, 618.0], [278.25, 628.41796875], [45.75, 628.41796875]]}, {"title": "7.2 Common Knowledge Distillation Methods", "heading_level": null, "page_id": 12, "polygon": [[310.5, 43.5], [523.5, 43.5], [523.5, 53.560546875], [310.5, 53.560546875]]}, {"title": "7.2.1 Knowledge Distillation for Object Detection", "heading_level": null, "page_id": 12, "polygon": [[311.25, 140.25], [522.75, 140.25], [522.75, 150.43359375], [311.25, 150.43359375]]}, {"title": "7.2.2 Knowledge Distillation for Semantic Segmentation", "heading_level": null, "page_id": 12, "polygon": [[311.25, 560.25], [554.25, 560.25], [554.25, 570.41015625], [311.25, 570.41015625]]}, {"title": "TABLE 5: Summary of VLM knowledge distillation methods. [code] directs to code websites.", "heading_level": null, "page_id": 13, "polygon": [[105.0380859375, 40.5], [501.75, 40.5], [501.75, 49.7900390625], [105.0380859375, 49.7900390625]]}, {"title": "7.3 Summary and Discussion", "heading_level": null, "page_id": 13, "polygon": [[46.5, 554.25], [187.5, 554.25], [187.5, 564.609375], [46.5, 564.609375]]}, {"title": "8 PERFORMANCE COMPARISON", "heading_level": null, "page_id": 13, "polygon": [[311.25, 536.25], [476.25, 536.25], [476.25, 546.8203125], [311.25, 546.8203125]]}, {"title": "8.1 Performance of VLM Pre-training", "heading_level": null, "page_id": 13, "polygon": [[310.18359375, 604.5], [483.0, 604.5], [483.0, 614.109375], [310.18359375, 614.109375]]}, {"title": "TABLE 6: Performance of VLM pre-training methods over zero-shot prediction setup on image classification tasks.", "heading_level": null, "page_id": 14, "polygon": [[62.6044921875, 40.5], [547.453125, 40.5], [547.453125, 50.5634765625], [62.6044921875, 50.5634765625]]}, {"title": "8.2 Performance of VLM Transfer Learning", "heading_level": null, "page_id": 14, "polygon": [[311.25, 687.0], [509.25, 687.0], [509.25, 697.5], [311.25, 697.5]]}, {"title": "8.3 Performance of VLM Knowledge Distillation", "heading_level": null, "page_id": 15, "polygon": [[45.75, 697.5], [268.5, 697.5], [268.5, 706.921875], [45.75, 706.921875]]}, {"title": "8.4 Summary", "heading_level": null, "page_id": 15, "polygon": [[310.5, 664.3828125], [378.75, 664.3828125], [378.75, 673.6640625], [310.5, 673.6640625]]}, {"title": "9 FUTURE DIRECTIONS", "heading_level": null, "page_id": 16, "polygon": [[47.25, 466.5], [190.5, 466.5], [190.5, 477.59765625], [47.25, 477.59765625]]}, {"title": "10 CONCLUSION", "heading_level": null, "page_id": 17, "polygon": [[47.25, 108.75], [139.5, 108.75], [139.5, 119.9794921875], [47.25, 119.9794921875]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 17, "polygon": [[46.5, 295.259765625], [116.25, 295.259765625], [116.25, 305.701171875], [46.5, 305.701171875]]}, {"title": "APPENDIX", "heading_level": null, "page_id": 20, "polygon": [[47.25, 367.5], [100.33154296875, 367.5], [100.33154296875, 377.82421875], [47.25, 377.82421875]]}, {"title": "<PERSON><PERSON> STATISTICS ON VISUAL RECOGNITION VLM\nPUBLICATIONS", "heading_level": null, "page_id": 20, "polygon": [[46.84130859375, 383.818359375], [300.0, 382.5], [300.0, 407.98828125], [46.84130859375, 407.98828125]]}, {"title": "B. DATASETS FOR PRE-TRAINING VLM", "heading_level": null, "page_id": 20, "polygon": [[46.84130859375, 542.953125], [242.349609375, 542.953125], [242.349609375, 553.0078125], [46.84130859375, 553.0078125]]}, {"title": "B.1. Image-Text Datasets", "heading_level": null, "page_id": 20, "polygon": [[46.5, 687.0], [160.5, 687.0], [160.5, 697.25390625], [46.5, 697.25390625]]}, {"title": "B.2. Auxiliary Datasets", "heading_level": null, "page_id": 20, "polygon": [[311.25, 576.75], [415.5, 576.75], [415.5, 586.265625], [311.25, 586.265625]]}, {"title": "C. DATASETS FOR EVALUATION", "heading_level": null, "page_id": 20, "polygon": [[311.25, 709.5], [468.0, 709.5], [468.0, 720.0703125], [311.25, 720.0703125]]}, {"title": "C.1. Datasets for Image Classification", "heading_level": null, "page_id": 21, "polygon": [[46.5, 117.75], [218.25, 117.75], [218.25, 127.810546875], [46.5, 127.810546875]]}, {"title": "C.2. Datasets for Action Recognition", "heading_level": null, "page_id": 22, "polygon": [[46.5, 543.33984375], [213.9609375, 543.33984375], [213.9609375, 554.94140625], [46.5, 554.94140625]]}, {"title": "C.3. Datasets for Semantic Segmentation", "heading_level": null, "page_id": 22, "polygon": [[311.25, 87.6884765625], [498.0, 87.6884765625], [498.0, 98.9033203125], [311.25, 98.9033203125]]}, {"title": "C.4. Datasets for Object Detection", "heading_level": null, "page_id": 22, "polygon": [[310.5, 345.919921875], [465.75, 345.919921875], [465.75, 356.748046875], [310.5, 356.748046875]]}, {"title": "C.5. Datasets for Image and Text Retrieval", "heading_level": null, "page_id": 22, "polygon": [[311.25, 614.8828125], [501.75, 614.8828125], [501.75, 625.7109375], [311.25, 625.7109375]]}, {"title": "JOURNAL OF LATEX CLASS FILES, MARCH 2023 24", "heading_level": null, "page_id": 23, "polygon": [[46.5, 26.25], [564.75, 26.25], [564.75, 35.384765625], [46.5, 35.384765625]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 305], ["Line", 90], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 2], ["Footnote", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 15031, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 469], ["Line", 156], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1238, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 114], ["Text", 10], ["SectionHeader", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 822, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 411], ["Line", 100], ["Text", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1168, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 1190], ["Line", 232], ["TextInlineMath", 13], ["Reference", 7], ["Equation", 6], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 881], ["Line", 190], ["Text", 11], ["TextInlineMath", 10], ["Reference", 6], ["Equation", 5], ["SectionHeader", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 829, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 616], ["TableCell", 170], ["Line", 126], ["Text", 8], ["SectionHeader", 7], ["Reference", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3076, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 789], ["TableCell", 334], ["Line", 90], ["Text", 5], ["SectionHeader", 3], ["Table", 1], ["TextInlineMath", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4848, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 693], ["TableCell", 304], ["Line", 115], ["SectionHeader", 4], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10843, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 489], ["Line", 168], ["SectionHeader", 8], ["Text", 8], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2262, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 545], ["TableCell", 304], ["Line", 93], ["Text", 8], ["SectionHeader", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9815, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 137], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["TextInlineMath", 2], ["FigureGroup", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1440, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 115], ["Text", 9], ["SectionHeader", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 96], ["TableCell", 18], ["Text", 6], ["SectionHeader", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5445, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 1226], ["TableCell", 951], ["Line", 107], ["Text", 6], ["Table", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Caption", 2], ["TableGroup", 2]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 10666, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 1528], ["TableCell", 270], ["Line", 113], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Table", 2], ["SectionHeader", 2], ["TableGroup", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["TableCell", 144], ["Line", 123], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9177, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 558], ["Line", 145], ["ListItem", 58], ["Reference", 58], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 625], ["Line", 153], ["ListItem", 65], ["Reference", 65], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 660], ["Line", 154], ["ListItem", 68], ["Reference", 68], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 514], ["Line", 122], ["ListItem", 34], ["Reference", 14], ["SectionHeader", 6], ["Text", 4], ["ListGroup", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 120], ["ListItem", 19], ["Text", 2], ["ListGroup", 2], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 328], ["Line", 113], ["ListItem", 19], ["ListGroup", 5], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 26], ["Text", 4], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Vision-Language Models for Vision Tasks- A Survey"}