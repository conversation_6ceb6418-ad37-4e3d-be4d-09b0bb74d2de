{"table_of_contents": [{"title": "Vision-Language Dataset Distillation", "heading_level": null, "page_id": 0, "polygon": [[69.75, 81.404296875], [359.7890625, 81.404296875], [359.7890625, 96.9697265625], [69.75, 96.9697265625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[281.3466796875, 221.783203125], [330.0, 221.783203125], [330.0, 232.611328125], [281.3466796875, 232.611328125]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[70.5, 457.5], [159.57421875, 457.5], [159.57421875, 468.703125], [70.5, 468.703125]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 2, "polygon": [[70.5, 81.75], [171.75, 81.75], [171.75, 94.21435546875], [70.5, 94.21435546875]]}, {"title": "3 Method", "heading_level": null, "page_id": 2, "polygon": [[70.5, 504.28125], [135.0, 504.28125], [135.0, 515.109375], [70.5, 515.109375]]}, {"title": "3.1 Problem Formulation", "heading_level": null, "page_id": 2, "polygon": [[70.5, 578.25], [193.5, 578.25], [193.5, 588.97265625], [70.5, 588.97265625]]}, {"title": "3.2 Baselines: Coreset Selection", "heading_level": null, "page_id": 3, "polygon": [[70.5, 652.0078125], [226.51171875, 652.0078125], [226.51171875, 662.0625], [70.5, 662.0625]]}, {"title": "3.3 Bi-trajectory Guided Vision-Language Co-Distillation", "heading_level": null, "page_id": 4, "polygon": [[70.5, 316.142578125], [337.078125, 316.142578125], [337.078125, 326.970703125], [70.5, 326.970703125]]}, {"title": "Algorithm 1 Bi-Trajectory Co-Distillation", "heading_level": null, "page_id": 5, "polygon": [[70.5, 83.25], [262.5, 83.25], [262.5, 93.392578125], [70.5, 93.392578125]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 6, "polygon": [[70.5, 208.634765625], [159.7236328125, 208.634765625], [159.7236328125, 220.623046875], [70.5, 220.623046875]]}, {"title": "4.1 Vision-Language Distillation Setup", "heading_level": null, "page_id": 6, "polygon": [[70.5, 298.740234375], [255.19921875, 298.740234375], [255.19921875, 309.568359375], [70.5, 309.568359375]]}, {"title": "4.2 Key Results", "heading_level": null, "page_id": 7, "polygon": [[70.5, 381.111328125], [151.505859375, 381.111328125], [151.505859375, 391.939453125], [70.5, 391.939453125]]}, {"title": "4.3 Ablation Studies", "heading_level": null, "page_id": 9, "polygon": [[70.5, 431.25], [172.5, 431.25], [172.5, 441.6328125], [70.5, 441.6328125]]}, {"title": "5 Conclusion", "heading_level": null, "page_id": 10, "polygon": [[70.5, 81.93603515625], [151.0576171875, 81.93603515625], [151.0576171875, 94.31103515625], [70.5, 94.31103515625]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 10, "polygon": [[70.5, 399.48046875], [176.25, 399.48046875], [176.25, 411.08203125], [70.5, 411.08203125]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[70.5, 512.40234375], [131.40966796875, 512.40234375], [131.40966796875, 524.00390625], [70.5, 524.00390625]]}, {"title": "A<PERSON>ndix", "heading_level": null, "page_id": 14, "polygon": [[70.5, 82.6611328125], [120.75, 82.6611328125], [120.75, 94.166015625], [70.5, 94.166015625]]}, {"title": "A Full Details for Distilled Performance", "heading_level": null, "page_id": 14, "polygon": [[70.5, 199.93359375], [298.529296875, 199.93359375], [298.529296875, 212.6953125], [70.5, 212.6953125]]}, {"title": "B CIFAR10 Classification vs Retrieval Distillation", "heading_level": null, "page_id": 15, "polygon": [[70.5, 81.75], [350.525390625, 81.75], [350.525390625, 94.0693359375], [70.5, 94.0693359375]]}, {"title": "C Upper Bound Performance", "heading_level": null, "page_id": 15, "polygon": [[70.5, 485.25], [240.2578125, 485.25], [240.2578125, 496.93359375], [70.5, 496.93359375]]}, {"title": "D Analysis on Distilled Images", "heading_level": null, "page_id": 16, "polygon": [[70.5, 81.75], [249.22265625, 81.75], [249.22265625, 94.359375], [70.5, 94.359375]]}, {"title": "E Additional Ablation Studies", "heading_level": null, "page_id": 17, "polygon": [[70.5, 80.630859375], [245.0390625, 80.630859375], [245.0390625, 94.0693359375], [70.5, 94.0693359375]]}, {"title": "E.1 Distilled Dataset Initialization", "heading_level": null, "page_id": 17, "polygon": [[70.5, 156.1376953125], [235.775390625, 156.1376953125], [235.775390625, 168.1259765625], [70.5, 168.1259765625]]}, {"title": "E.2 Encoder Backbone Selection", "heading_level": null, "page_id": 18, "polygon": [[70.5, 548.75390625], [228.90234375, 548.75390625], [228.90234375, 558.80859375], [70.5, 558.80859375]]}, {"title": "E.2.1 Language Backbones", "heading_level": null, "page_id": 18, "polygon": [[70.5, 595.5], [203.25, 595.5], [203.25, 605.6015625], [70.5, 605.6015625]]}, {"title": "E.2.2 Vision Backbones", "heading_level": null, "page_id": 18, "polygon": [[70.5, 689.25], [187.8134765625, 689.25], [187.8134765625, 699.57421875], [70.5, 699.57421875]]}, {"title": "E.3 Pretrained vs. Non-pretrained", "heading_level": null, "page_id": 20, "polygon": [[70.5, 83.25], [235.4765625, 83.25], [235.4765625, 93.73095703125], [70.5, 93.73095703125]]}, {"title": "E.4 Synthetic Steps", "heading_level": null, "page_id": 20, "polygon": [[70.5, 282.75], [169.5849609375, 282.75], [169.5849609375, 293.1328125], [70.5, 293.1328125]]}, {"title": "F Beyond Trajectory Matching", "heading_level": null, "page_id": 20, "polygon": [[70.5, 623.25], [249.22265625, 623.25], [249.22265625, 635.765625], [70.5, 635.765625]]}, {"title": "G Additional Visualizations", "heading_level": null, "page_id": 22, "polygon": [[70.5, 80.630859375], [228.75, 80.630859375], [228.75, 94.359375], [70.5, 94.359375]]}, {"title": "COCO Initialized Texts, iteration = 0.", "heading_level": null, "page_id": 26, "polygon": [[229.5, 198.0], [383.25, 198.0], [383.25, 206.89453125], [229.5, 206.89453125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["Line", 47], ["Text", 8], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 6004, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 59], ["Text", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 871, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 423], ["Line", 57], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 350], ["Line", 69], ["TextInlineMath", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Equation", 1], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 775, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 393], ["Line", 49], ["Text", 5], ["ListItem", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 724], ["Line", 90], ["ListItem", 14], ["TextInlineMath", 3], ["Reference", 3], ["Equation", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 333], ["Line", 50], ["TextInlineMath", 3], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 362], ["TableCell", 139], ["Line", 50], ["Text", 4], ["Table", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3160, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["TableCell", 309], ["Line", 87], ["Text", 13], ["Picture", 7], ["Caption", 4], ["Table", 3], ["Reference", 3], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 9, "llm_error_count": 1, "llm_tokens_used": 13069, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 436], ["Span", 316], ["Line", 53], ["Text", 5], ["Table", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 15332, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 46], ["ListItem", 6], ["Reference", 6], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 44], ["ListItem", 21], ["Reference", 21], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 44], ["ListItem", 20], ["Reference", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 18], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 682], ["TableCell", 349], ["Line", 52], ["Text", 3], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["TableCell", 74], ["Line", 43], ["Text", 4], ["Reference", 4], ["SectionHeader", 2], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 53], ["Line", 19], ["Reference", 3], ["Text", 2], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1300, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["TableCell", 56], ["Line", 34], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 571, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 50], ["Text", 7], ["SectionHeader", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 645, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 352], ["Span", 159], ["Line", 58], ["Table", 3], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 16747, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 360], ["Span", 209], ["Line", 52], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 22966, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["TableCell", 56], ["Line", 20], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5912, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 12], ["Reference", 3], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1206, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 55], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 112], ["Line", 56], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 10], ["Line", 4], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1250, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 53], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 54], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 16], ["Line", 4], ["Picture", 2], ["Caption", 2], ["PictureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1194, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 54], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListItem", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 53], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Vision-Language_Dataset_Distillation"}