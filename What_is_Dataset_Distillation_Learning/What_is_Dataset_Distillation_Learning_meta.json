{"table_of_contents": [{"title": "What is Dataset Distillation Learning?", "heading_level": null, "page_id": 0, "polygon": [[179.1474609375, 89.25], [416.25, 89.25], [416.25, 103.7373046875], [179.1474609375, 103.7373046875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[148.5, 175.5], [195.75, 175.5], [195.75, 187.171875], [148.5, 187.171875]]}, {"title": "1. Introduction", "heading_level": null, "page_id": 0, "polygon": [[54.0, 477.75], [132.75, 477.75], [132.75, 490.359375], [54.0, 490.359375]]}, {"title": "What kind of information is captured in distilled data?", "heading_level": null, "page_id": 1, "polygon": [[54.0, 433.5], [289.5, 433.5], [289.5, 443.953125], [54.0, 443.953125]]}, {"title": "2. Preliminary", "heading_level": null, "page_id": 1, "polygon": [[305.25, 203.607421875], [381.0, 203.607421875], [381.0, 215.595703125], [305.25, 215.595703125]]}, {"title": "3. Distilled vs. Real Data", "heading_level": null, "page_id": 2, "polygon": [[54.0, 543.0], [181.5, 543.0], [181.5, 554.16796875], [54.0, 554.16796875]]}, {"title": "4. Information Captured in Distilled Data", "heading_level": null, "page_id": 3, "polygon": [[54.0, 240.0], [267.75, 240.0], [267.75, 251.173828125], [54.0, 251.173828125]]}, {"title": "4.1. Predictions of models trained on distilled data is\nsimilar to models trained with early-stopping", "heading_level": null, "page_id": 3, "polygon": [[305.25, 455.25], [530.25, 455.25], [530.25, 477.2109375], [305.25, 477.2109375]]}, {"title": "4.2. Recognition on the distilled data is learned early in\nthe training process", "heading_level": null, "page_id": 4, "polygon": [[54.0, 628.5], [289.564453125, 628.5], [289.564453125, 650.84765625], [54.0, 650.84765625]]}, {"title": "4.3. Distilled data stores little information beyond what\nwould be learned early in training", "heading_level": null, "page_id": 4, "polygon": [[306.0, 363.0], [542.25, 363.0], [542.25, 385.751953125], [306.0, 385.751953125]]}, {"title": "5. <PERSON><PERSON><PERSON> of Captured Information", "heading_level": null, "page_id": 5, "polygon": [[54.0, 656.25], [250.5, 656.25], [250.5, 668.63671875], [54.0, 668.63671875]]}, {"title": "5.1. Influence functions for understanding distilled data", "heading_level": null, "page_id": 5, "polygon": [[306.0, 606.75], [542.25, 606.75], [542.25, 616.81640625], [306.0, 616.81640625]]}, {"title": "5.2. Distilled data contains semantic information beyond\nthe class label", "heading_level": null, "page_id": 7, "polygon": [[54.0, 440.25], [289.5, 441.0], [289.5, 462.12890625], [54.0, 462.12890625]]}, {"title": "6. Conclusion", "heading_level": null, "page_id": 8, "polygon": [[306.0, 384.978515625], [377.25, 384.978515625], [377.25, 395.806640625], [306.0, 395.806640625]]}, {"title": "Impact Statement", "heading_level": null, "page_id": 8, "polygon": [[306.0, 541.5], [399.75, 541.5], [399.75, 553.0078125], [306.0, 553.0078125]]}, {"title": "Acknowledgements", "heading_level": null, "page_id": 8, "polygon": [[306.0, 672.75], [406.5, 672.75], [406.5, 684.10546875], [306.0, 684.10546875]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[54.0, 168.22265625], [111.75, 168.22265625], [111.75, 179.244140625], [54.0, 179.244140625]]}, {"title": "A. Loss Landscape Analysis", "heading_level": null, "page_id": 11, "polygon": [[54.0, 371.25], [198.421875, 371.25], [198.421875, 382.271484375], [54.0, 382.271484375]]}, {"title": "<PERSON>. Additional Mixing and Prediction Analysis", "heading_level": null, "page_id": 15, "polygon": [[54.0, 67.33740234375], [288.0703125, 67.33740234375], [288.0703125, 79.22900390625], [54.0, 79.22900390625]]}, {"title": "C. Variability of agreements", "heading_level": null, "page_id": 17, "polygon": [[54.0, 331.8046875], [199.6171875, 331.8046875], [199.6171875, 343.79296875], [54.0, 343.79296875]]}, {"title": "D. Does distilled data points contain information outside of the labeled class?", "heading_level": null, "page_id": 19, "polygon": [[54.0, 361.5], [447.0, 361.5], [447.0, 373.5703125], [54.0, 373.5703125]]}, {"title": "E. Connecting Curvature with Information Content", "heading_level": null, "page_id": 20, "polygon": [[54.0, 441.6328125], [320.25, 441.6328125], [320.25, 454.0078125], [54.0, 454.0078125]]}, {"title": "F. Initializing BPTT with Real Images", "heading_level": null, "page_id": 21, "polygon": [[54.0, 526.32421875], [249.75, 526.32421875], [249.75, 538.69921875], [54.0, 538.69921875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 79], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5729, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 561], ["Line", 101], ["Text", 7], ["TextInlineMath", 4], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 121], ["Text", 3], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 249], ["Line", 99], ["Text", 7], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 945, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 103], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 784, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 90], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 781, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 408], ["Line", 109], ["TextInlineMath", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Equation", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1661, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 101], ["TableCell", 60], ["Text", 6], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3964, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 58], ["Text", 5], ["Caption", 3], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 665, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 235], ["Line", 95], ["ListItem", 24], ["Reference", 24], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 73], ["ListItem", 22], ["Reference", 22], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 238], ["Line", 44], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["TextInlineMath", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 766, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 55], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 837, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Line", 129], ["Span", 36], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1631, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 242], ["Line", 72], ["Text", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 2056, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 286], ["Line", 71], ["Text", 5], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1166, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 368], ["Line", 98], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 3019, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 37], ["ListItem", 6], ["Text", 3], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 773, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 68], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1515, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 340], ["Line", 91], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1172, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 35], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 803, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["TableCell", 36], ["Line", 31], ["Reference", 3], ["Caption", 2], ["Picture", 2], ["Text", 2], ["PictureGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2428, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 17], ["Line", 6], ["Caption", 3], ["Picture", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Text", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 1286, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/What_is_Dataset_Distillation_Learning"}