# <span id="page-0-0"></span>You Only Condense Once: Two Rules for Pruning Condensed Datasets

<PERSON>, <PERSON><PERSON>, <PERSON><sup>⊠</sup>

CFAR, Agency for Science, Technology and Research, Singapore IHPC, Agency for Science, Technology and Research, Singapore {<PERSON>\_<PERSON>, <PERSON>\_<PERSON>}@cfar.a-star.edu.sg

## Abstract

Dataset condensation is a crucial tool for enhancing training efficiency by reducing the size of the training dataset, particularly in on-device scenarios. However, these scenarios have two significant challenges: 1) the varying computational resources available on the devices require a dataset size different from the pre-defined condensed dataset, and 2) the limited computational resources often preclude the possibility of conducting additional condensation processes. We introduce You Only Condense Once (YOCO) to overcome these limitations. On top of one condensed dataset, YOCO produces smaller condensed datasets with two embarrassingly simple dataset pruning rules: Low LBPE Score and Balanced Construction. YOCO offers two key advantages: 1) it can flexibly resize the dataset to fit varying computational constraints, and 2) it eliminates the need for extra condensation processes, which can be computationally prohibitive. Experiments validate our findings on networks including ConvNet, ResNet and DenseNet, and datasets including CIFAR-10, CIFAR-100 and ImageNet. For example, our YOCO surpassed various dataset condensation and dataset pruning methods on CIFAR-10 with ten Images Per Class (IPC), achieving 6.98-8.89% and 6.31-23.92% accuracy gains, respectively. The code is available at: <https://github.com/he-y/you-only-condense-once>.

# 1 Introduction

Deep learning models often require vast amounts of data to achieve optimal performance. This data-hungry nature of deep learning algorithms, coupled with the growing size and complexity of datasets, has led to the need for more efficient dataset handling techniques. Dataset condensation is a promising approach that enables models to learn from a smaller and more representative subset of the entire dataset. Condensed datasets are especially utilized in on-device scenarios, where limited computational resources and storage constraints necessitate the use of a compact training set.

However, these on-device scenarios have two significant constraints. First, the diverse and fluctuating computational resources inherent in these scenarios necessitate a level of flexibility in the size of the dataset. But the requirement of flexibility is not accommodated by the fixed sizes of previous condensed datasets. Second, the limited computational capacity in these devices also makes extra condensation processes impractical, if not impossible. Therefore, the need for adaptability in the size of the condensed dataset becomes increasingly crucial. Furthermore, this adaptability needs to be realized without introducing another computationally intensive condensation process.

We introduce You Only Condense Once (YOCO) to enable the flexible resizing (pruning) of condensed datasets to fit varying on-device scenarios without extra condensation process (See Fig. [1\)](#page-1-0). The first rule of our proposed method involves a metric to evaluate the importance of training samples in the context of dataset condensation.

 $\overline{\mathbb{C}}$  Corresponding Author

<sup>37</sup>th Conference on Neural Information Processing Systems (NeurIPS 2023).

From the gradient of the loss function, we develop the Logit-Based Prediction Error (LBPE) score to rank training samples. This metric quantifies the neural network's difficulty in recognizing each sample. Specifically, training samples with low LBPE scores are considered easy as they indicate that the model's prediction is close to the true label. These samples exhibit simpler patterns, easily captured by the model. Given the condensed datasets' small size, prioritizing easier samples with low LBPE scores is crucial to avoid overfitting.

A further concern is that relying solely on a metricbased ranking could result in an imbalanced distribution of classes within the dataset. Imbalanced

<span id="page-1-0"></span>Image /page/1/Figure/2 description: The image is a diagram illustrating a machine learning model condensation process. The top section shows the initial step: condensing a full dataset to create a condensed dataset with IPC\_N (Image Per Class). The right side outlines the goal: achieving IPC\_1, IPC\_2, ..., IPC\_(N-1) across various scenarios and sizes. The bottom left section details the previous method, requiring 200K multiplied by (N-1) epochs to condense each IPC individually. The bottom right section presents the proposed method, requiring only 1K epochs to score all IPCs simultaneously, showing IPC\_1, IPC\_2, ..., IPC\_(N-1) with example images for each.

Figure 1: Previous methods (left) require extra condensation processes, but ours (right) do not.

datasets can lead to biased predictions, as models tend to focus on the majority class, ignoring the underrepresented minority classes. This issue has not been given adequate attention in prior research about dataset pruning [\[3,](#page-14-0) [41,](#page-16-0) [34\]](#page-15-0), but it is particularly important when dealing with condensed datasets. In order to delve deeper into the effects of class imbalance, we explore Rademacher Complexity [\[31\]](#page-15-1), a widely recognized metric for model complexity that is intimately connected to generalization error and expected loss. Based on the analysis, we propose **Balanced Construction** to ensure that the condensed dataset is both informative and balanced.

The key contributions of our work are: 1) To the best of our knowledge, it's the first work to provide a solution to adaptively adjust the size of a condensed dataset to fit varying computational constraints. 2) After analyzing the gradient of the loss function, we propose the LBPE score to evaluate the sample importance and find out easy samples with low LBPE scores are suitable for condensed datasets. 3) Our analysis of the Rademacher Complexity highlights the challenges of class imbalance in condensed datasets, leading us to construct balanced datasets.

# 2 Related Works

#### 2.1 Dataset Condensation/Distillation

Dataset condensation, or distillation, synthesizes a compact image set to maintain the original dataset's information. Wang *et al.* [\[45\]](#page-16-1) pioneer an approach that leverages gradient-based hyperparameter optimization to model network parameters as a function of this synthetic data. Building on this, Zhao *et al.* [\[53\]](#page-16-2) introduce gradient matching between real and synthetic image-trained models. Kim *et al.* [\[19\]](#page-14-1) further extend this, splitting synthetic data into *n* factor segments, each decoding into  $n^2$  training images. Zhao & Bilen [\[50\]](#page-16-3) apply consistent differentiable augmentation to real and synthetic data, thus enhancing information distillation. Cazenavette *et al.* [\[1\]](#page-13-0) propose to emulate the long-range training dynamics of real data by aligning learning trajectories. Liu *et al.* [\[24\]](#page-15-2) advocate matching only representative real dataset images, selected based on latent space cluster centroid distances. Additional research avenues include integrating a contrastive signal [\[21\]](#page-14-2), matching distribution or features [\[52,](#page-16-4) [44\]](#page-16-5), matching multi-level gradients [\[16\]](#page-14-3), minimizing accumulated trajectory error [\[7\]](#page-14-4), aligning loss curvature [\[39\]](#page-15-3), parameterizing datasets [\[6,](#page-14-5) [23,](#page-15-4) [51,](#page-16-6) [43\]](#page-16-7), and optimizing dataset condensation [\[27,](#page-15-5) [32,](#page-15-6) [33,](#page-15-7) [42,](#page-16-8) [55,](#page-16-9) [25,](#page-15-8) [49,](#page-16-10) [4,](#page-14-6) [26\]](#page-15-9). Nevertheless, the fixed size of condensed datasets remains an unaddressed constraint in prior work.

#### 2.2 Dataset Pruning

Unlike dataset condensation that alters image pixels, dataset pruning preserves the original data by selecting a representative subset. Entropy [\[3\]](#page-14-0) keeps hard samples with maximum entropy (uncertainty [\[22,](#page-15-10) [38\]](#page-15-11)), using a smaller proxy network. Forgetting [\[41\]](#page-16-0) defines forgetting events as an accuracy drop at consecutive epochs, and hard samples with the most forgetting events are important. AUM [\[35\]](#page-15-12) identifies data by computing the Area Under the Margin, the difference between the true label logits and the largest other logits. Memorization [\[11\]](#page-14-7) prioritizes a sample if it significantly improves the probability of correctly predicting the true label. GraNd [\[34\]](#page-15-0) and EL2N [\[34\]](#page-15-0) classify samples as hard based on the presence of large gradient norm and large norm of error vectors, respectively. CCS [\[54\]](#page-16-11) extends previous methods by pruning hard samples and using stratified sampling to

achieve good coverage of data distributions at a large pruning ratio. Moderate [\[47\]](#page-16-12) selects moderate samples (neither hard nor easy) that are close to the score median in the feature space. Optimizationbased [\[48\]](#page-16-13) chooses samples yielding a strictly constrained generalization gap. In addition, other dataset pruning (or coreset selection) methods [\[8,](#page-14-8) [28,](#page-15-13) [36,](#page-15-14) [17,](#page-14-9) [30,](#page-15-15) [18,](#page-14-10) [2,](#page-13-1) [29,](#page-15-16) [9,](#page-14-11) [10,](#page-14-12) [46,](#page-16-14) [15\]](#page-14-13) are widely used for Active Learning [\[38,](#page-15-11) [37\]](#page-15-17). However, the previous methods consider full datasets and often neglect condensed datasets.

## 3 Method

#### 3.1 Preliminaries

We denote a dataset by  $S = (x_i, y_i)_{i=1}^N$ , where  $x_i$  represents the  $i^{th}$  input and  $y_i$  represents the corresponding true label. Let  $\mathcal{L}(p(\mathbf{w}, x), y)$  be a loss function that measures the discrepancy between the predicted output  $p(\mathbf{w}, x)$  and the true label y. The loss function is parameterized by a weight vector w, which we optimize during the training process.

We consider a time-indexed sequence of weight vectors,  $w_t$ , where  $t = 1, \ldots, T$ . This sequence represents the evolution of the weights during the training process. The gradient of the loss function with respect to the weights at time t is given by  $g_t(x, y) = \nabla_{\mathbf{w}_t} \mathcal{L}(p(\mathbf{w}_t, x), y)$ .

#### 3.2 Identifying Important Training Samples

Our goal is to propose a measure that quantifies the importance of a training sample. We begin by analyzing the gradient of the loss function with respect to the weights  $w_t$ :

$$
\nabla_{\mathbf{w}_t} \mathcal{L}(p(\mathbf{w}_t, x), y) = \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t}.
$$
(1)

We aim to determine the impact of training samples on the gradient of the loss function, as the gradient plays a critical role in the training process of gradient-based optimization methods. Note that our ranking method is inspired by EL2N [\[34\]](#page-15-0), but we interpret it in a different way by explicitly considering the dataset size  $|S|$ .

Definition 1 (Logit-Based Prediction Error - LBPE): The Logit-Based Prediction Error (LBPE) of a training sample  $(x, y)$  at time t is given by:

<span id="page-2-0"></span>
$$
LBPEt(x, y) = \mathbb{E}|p(\mathbf{w}_{t}, x) - y|_{2},
$$
\n(2)

where  $w_t$  is the weights at time t, and  $p(w_t, x)$  represents the prediction logits.

Lemma 1 (Gradient and Importance of Training Samples): The gradient of the loss function  $\nabla_{\mathbf{w}_t}\mathcal{L}(p(\mathbf{w}_t, x), y)$  for a dataset S is influenced by the samples with prediction errors.

**Proof of Lemma 1:** Consider two datasets S and  $S_{-i}$ , where  $S_{-i}$  is obtained by removing the sample  $(x_j, y_j)$  from S. Let the gradients of the loss function for these two datasets be  $\nabla_{\mathbf{w}_t}^S \mathcal{L}$  and  $\nabla_{\mathbf{w}_t}^{S \to j} \mathcal{L}$ , respectively. The difference between the gradients is given by (see Appendix [A.1](#page-10-0) for proof):

$$
\Delta \nabla_{\mathbf{w}_t} \mathcal{L} = \frac{-1}{|S|(|S|-1)} \sum_{(x,y)\in S_{\neg j}} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t} + \frac{1}{|S|} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x_j), y_j)}{\partial p(\mathbf{w}_t, x_j)} \cdot \frac{\partial p(\mathbf{w}_t, x_j)}{\partial \mathbf{w}_t}
$$
(3)

Let us denote the error term as:  $e_j = p(\mathbf{w}_t, x_j) - y_j$ , the LBPE score for sample  $(x_j, y_j)$  is given by LBPE<sub>t</sub> $(x_j, y_j) = \mathbb{E}|e_j|_2$ , and the difference in gradients related to the sample  $(x_j, y_j)$  can be rewritten as:

$$
\frac{1}{|S|} \frac{\partial \mathcal{L}(e_j)}{\partial e_j} \cdot \frac{\partial p(\mathbf{w}_t, x_j)}{\partial \mathbf{w}_t}.
$$
\n(4)

If the sample  $(x_i, y_i)$  has a lower LBPE score, it implies that the error term  $e_i$  is smaller. Let's consider the mean squared error (MSE) loss function, which is convex. The MSE loss function is

<span id="page-3-0"></span>Algorithm 1 Compute LBPE score for samples over epochs

**Require:** Training dataset S and its size  $|S|$ , weights  $w_t$ , true labels y, model's predicted probabilities  $p(\mathbf{w}_t, x)$ , number of epochs E, Epochs with Top-K accuracy<br>Initialize matrix: LBPE= torch.zeros((E, |S|))  $\rightarrow$  LBPE scores over samples and epochs 1: Initialize matrix: LBPE= torch.zeros $((E, |S|))$ 2: Initialize accuracy: ACC= torch.zeros(E)  $\triangleright$  Track the accuracy over epochs<br>3: **for** each epoch t in range E **do**  $\triangleright$  Loop through each epoch 3: for each epoch t in range  $E$  do 4: **for** each sample index i in S **do**  $\triangleright$  Loop through each sample in the dataset 5: Compute error term for sample i at epoch t:  $e_{i} = p(\mathbf{w}_t, x_i) - y_i$ Compute error term for sample *i* at epoch t:  $e_{i,t} = p(\mathbf{w}_t, x_i) - y_i$ 6: Compute LBPE score for sample *i* at epoch *t* with MSE loss: LBPE<sub>i,t</sub> =  $\mathbb{E}_{MSE} |e_{i,t}|_2$ 7: end for 8: Compute accuracy at epoch  $t$ : ACC<sub>t</sub> 9: end for 10:  $Top_K \leftarrow \text{argsort}(ACC)[-k!]$   $\triangleright$  Find the epochs with the Top-K accuracy 11: AVG\_LBPE ← mean(LBPE[Top\_k, :]) ▷ Average LBPE score over Top-K epochs 12: return AVG\_LBPE

defined as  $\mathcal{L}(e_j) = \frac{1}{2}(e_j)^2$ . Consequently, the derivative of the loss function  $\frac{\partial \mathcal{L}(e_j)}{\partial e_j} = e_j$  would be smaller for samples with smaller LBPE scores, leading to a smaller change in the gradient  $\Delta \nabla_{\mathbf{w}_t}\mathcal{L}$ .

Rule 1: For a small dataset, a sample with a lower LBPE score will be more important. Let  $S$ be a dataset of size |S|, partitioned into subsets  $S_{easy}$  (lower LBPE scores) and  $S_{hard}$  (higher LBPE scores).

*Case 1: Small Dataset* - When the dataset size  $|S|$  is small, the model's capacity to learn complex representations is limited. Samples in  $S_{easy}$  represent prevalent patterns in the data, and focusing on learning from them leads to a lower average expected loss. This enables the model to effectively capture the dominant patterns within the limited dataset size. Moreover, the gradients of the loss function for samples in  $S_{easy}$  are smaller, leading to faster convergence and improved model performance within a limited number of training iterations.

*Case 2: Large Dataset* - When the dataset size |S| is large, the model has the capacity to learn complex representations, allowing it to generalize well to both easy and hard samples. As the model learns from samples in both  $S_{easy}$  and  $S_{hard}$ , its overall performance improves, and it achieves higher accuracy on hard samples. Training on samples in  $S_{hard}$  helps the model learn more discriminative features, as they often lie close to the decision boundary.

Therefore, in the case of a small dataset, samples with lower LBPE scores are more important.

The use of the LBPE importance metric is outlined in Algorithm [1.](#page-3-0) LBPE scores over the epochs with the Top-K training accuracy are averaged. The output of this algorithm is the average LBPE score.

#### 3.3 Balanced Construction

In this section, we prove that a more balanced class distribution yields a lower expected loss.

**Definition 2.1 (Dataset Selection**  $S_A$  and  $S_B$ ):  $S_A$  is to select images from each class based on their LBPE scores such that the selection is balanced across classes, and  $S_B$  is to select images purely based on their LBPE scores without considering the class balance. Formally, we have:

<span id="page-3-1"></span> $S_A = (x_i, y_i) : x_i \in \mathcal{X}_k$ , and  $\text{LBPE}_t(x_i)$  $(s, y_i) \leq \tau_k,$   $S_B = (x_i, y_i) : \text{LBPE}_t(x_i, y_i) \leq \tau$  (5) where  $X_k$  denotes the set of images from class k, and  $\tau_k$  is a threshold for class k,  $\tau$  is a global threshold. Then  $S_A$  is a more balanced dataset compared to  $S_B$ .

**Definition 2.2 (Generalization Error):** The generalization error of a model is the difference between the expected loss on the training dataset and the expected loss on an unseen test dataset:

GenErr(
$$
\mathbf{w}
$$
) =  $\mathbb{E}[L_{\text{test}}(\mathbf{w})] - \mathbb{E}[L_{\text{train}}(\mathbf{w})].$  (6)

**Definition 2.3 (Rademacher Complexity):** The Rademacher complexity [\[31\]](#page-15-1) of a hypothesis class  $H$  for a dataset S of size N is defined as:

$$
\mathcal{R}_N(\mathcal{H}) = \mathbb{E}\left[\sup_{h \in \mathcal{H}} \frac{1}{N} \sum_{i=1}^N \sigma_i h(\mathbf{x}_i)\right],\tag{7}
$$

<span id="page-4-0"></span>Algorithm 2 Balanced Dataset Construction

**Require:** Condensed dataset  $S = (x_i, y_i)_{i=1}^m$  with classes **K**, LBPE scores LBPE, class-specific thresholds  $\tau = \tau_{k_{k=1}}^{K}$  to ensure an equal number of samples for each class

1: Initialize  $S_A = \emptyset$  > Initialize the balanced subset as an empty set 2: for each class  $k \in \mathbf{K}$  do 3:  $I_{sel} \leftarrow \{i : y_i = k \text{ and } \mathbf{LBPE}_t(x_i, y_i) \leq \tau_k\}$   $\triangleright$  Find indices of samples for class k 4:  $S_A \leftarrow S_A \cup (x_i, y_i) : i \in I_{sel}$   $\triangleright$  Add the selected samples to the balanced subset 5: end for 6: **return**  $S_A$ 

where  $\sigma_i$  are independent Rademacher random variables taking values in  $-1$ , 1 with equal probability.

**Lemma 2.1 (Generalization Error Bound):** With a high probability, the generalization error is upper-bounded by the Rademacher complexity of the hypothesis class:

GenErr(**w**) 
$$
\leq 2\mathcal{R}_N(\mathcal{H}) + \mathcal{O}\left(\frac{1}{\sqrt{N}}\right)
$$
, (8)

where  $O$  represents the order of the term.

**Lemma 2.2 (Rademacher Complexity Comparison):** The Rademacher complexity of dataset  $S_A$  is less than that of dataset  $S_B$ :

$$
\mathcal{R}_{N_A}(\mathcal{H}) \leq \mathcal{R}_{N_B}(\mathcal{H}).\tag{9}
$$

**Theorem 2.1:** The expected loss for the dataset  $S_A$  is less than or equal to  $S_B$  when both models achieve similar performance on their respective training sets.

**Proof of Theorem 2.1:** Using Lemma 2.1 and Lemma 2.2, we have:

$$
GenErr(\mathbf{w}_A) \le GenErr(\mathbf{w}_B). \tag{10}
$$

Assuming that both models achieve similar performance on their respective training sets, the training losses are approximately equal:

$$
\mathbb{E}[L_{\text{train}}(\mathbf{w}_A)] \approx \mathbb{E}[L_{\text{train}}(\mathbf{w}_B)]. \tag{11}
$$

Given this assumption, we can rewrite the generalization error inequality as:

$$
\mathbb{E}[L_{\text{test}}(\mathbf{w}_A)] - \mathbb{E}[L_{\text{train}}(\mathbf{w}_A)] \leq \mathbb{E}[L_{\text{test}}(\mathbf{w}_B)] - \mathbb{E}[L_{\text{train}}(\mathbf{w}_B)]. \tag{12}
$$

Adding  $\mathbb{E}[L_{\text{train}}(\mathbf{w}_A)]$  to both sides, we get:

$$
\mathbb{E}[L_{\text{test}}(\mathbf{w}_A)] \le \mathbb{E}[L_{\text{test}}(\mathbf{w}_B)]. \tag{13}
$$

This result indicates that the balanced dataset  $S_A$  is better than  $S_B$ .

**Theorem 2.2:** Let  $S_F$  and  $S_C$  be the full and condensed datasets, respectively, and let both  $S_F$  and  $S_C$  have an imbalanced class distribution with the same degree of imbalance. Then, the influence of the imbalanced class distribution on the expected loss is larger for the condensed dataset  $S_C$  than for the full dataset  $S_F$ .

**Proof of Theorem 2.2:** We compare the expected loss for the full and condensed datasets, taking into account their class imbalances. Let  $L(h)$  denote the loss function for the hypothesis h. Let  $\mathbb{E}[L(h)|S]$  denote the expected loss for the hypothesis h on the dataset S. Let  $n_{kF}$  and  $n_{kC}$  denote the number of samples in class k for datasets  $S_F$  and  $S_C$ , respectively. Let  $m_F$  and  $m_C$  denote the total number of samples in datasets  $S_F$  and  $S_C$ , respectively. Let  $r_k = \frac{n_k}{m_F} = \frac{n_k}{m_C}$  be the class ratio for each class k in both datasets. The expected loss for  $S_F$  and  $S_C$  can be written as:

$$
\mathbb{E}[L(h)|S_F] = \sum_{k=1}^{K} r_k \mathbb{E}[l(h(x), y)|\mathcal{X}_k], \quad \mathbb{E}[L(h)|S_C] = \sum_{k=1}^{K} r_k \mathbb{E}[l(h(x), y)|\mathcal{X}_k], \tag{14}
$$

To show this, let's compare the expected loss per sample in each dataset:

$$
\frac{\mathbb{E}[L(h)|S_C]}{m_C} > \frac{\mathbb{E}[L(h)|S_F]}{m_F}.\tag{15}
$$

This implies that the influence of the imbalanced class distribution is larger for  $S_C$  than for  $S_F$ .

Rule 2: Balanced class distribution should be utilized for the condensed dataset. The construction of a balanced class distribution based on LBPE scores is outlined in Algorithm [2.](#page-4-0) Its objective is to create an equal number of samples for each class to ensure a balanced dataset.

## 4 Experiments

## 4.1 Experiment Settings

IPC stands for "Images Per Class". IPC<sub>F→T</sub> means flexibly resize the condensed dataset from size F to size T. More detailed settings can be found in Appendix B.1.

Dataset Condensation Settings. The CIFAR-10 and CIFAR-100 datasets [\[20\]](#page-14-14) are condensed via ConvNet-D3 [\[12\]](#page-14-15), and ImageNet-10 [\[5\]](#page-14-16) via ResNet10-AP [\[13\]](#page-14-17), both following IDC [\[19\]](#page-14-1). IPC includes 10, 20, or 50, depending on the experiment. For both networks, the learning rate is 0.01 with 0.9 momentum and 0.0005 weight decay. The SGD optimizer and a multi-step learning rate scheduler are used. The training batch size is 64, and the network is trained for  $2000 \times 100$  epochs for CIFAR-10/CIFAR-100 and  $500 \times 100$  epochs for ImageNet-10.

YOCO Settings. 1) LBPE score selection. To reduce computational costs, we derive the LBPE score from training dynamics of early  $E$  epochs. To reduce variance, we use the LBPE score from the top-K training epochs with the highest accuracy. For CIFAR-10, we set  $E = 100$  and  $K = 10$ for all the IPC<sub>F</sub> and IPC<sub>T</sub>. For CIFAR-100 and ImageNet-10, we set  $E = 200$  and  $K = 10$  for all the IPC<sub>F</sub> and IPC<sub>T</sub>. 2) **Balanced construction.** We use  $S_A$  in Eq. [5](#page-3-1) to achieve a balanced construction. Following IDC [\[19\]](#page-14-1), we leverage a multi-formation framework to increase the synthetic data quantity while preserving the storage budget. Specifically, an IDC-condensed image is composed of  $n^2$  patches. Each patch is derived from one original image with the resolution scaled down by a factor of  $1/n^2$ . Here, n is referred to as the "factor" in the multi-formation process. For CIFAR-10 and CIFAR-100 datasets,  $n = 2$ ; for ImageNet-10 dataset,  $n = 3$ . We create balanced classes according to these patches. As a result, all the classes have the same number of samples. 3) Flexible resizing. For datasets with IPC<sub>F</sub> = 10 and IPC<sub>F</sub> = 20, we select IPC<sub>T</sub> of 1, 2, and 5. For IPC<sub>F</sub> = 50, we select IPC<sub>T</sub> of 1, 2, 5, and 10. For a condensed dataset with IPC<sub>F</sub>, the performance of its flexible resizing is indicated by the average accuracy across different IPC $_T$  values.

Comparison Baselines. We have two sets of baselines for comparison: 1) dataset condensation methods including IDC[\[19\]](#page-14-1), DREAM[\[24\]](#page-15-2), MTT [\[1\]](#page-13-0), DSA [\[50\]](#page-16-3) and KIP [\[32\]](#page-15-6) and 2) dataset pruning methods including SSP [\[40\]](#page-15-18), Entropy [\[3\]](#page-14-0), AUM [\[35\]](#page-15-12), Forgetting [\[41\]](#page-16-0), EL2N [\[34\]](#page-15-0), and CCS [\[54\]](#page-16-11). For dataset condensation methods, we use a random subset as the baseline. For dataset pruning methods, their specific metrics are used to rank and prune datasets to the required size.

#### 4.2 Primary Results

Tab. [1](#page-6-0) provides a comprehensive comparison of different methods for flexibly resizing datasets from an initial IPC<sub>F</sub> to a target IPC<sub>T</sub>. In this table, we have not included ImageNet results on DREAM [\[24\]](#page-15-2) since it only reports on Tiny ImageNet with a resolution of  $64\times64$ , in contrast to ImageNet's 224×224. The third column of the table shows the accuracy of the condensed dataset at the parameter IPC<sub>F</sub>. We then flexibly resize the dataset from IPC<sub>F</sub> to IPC<sub>T</sub>. The blue area represents the average accuracy across different  $IPC<sub>T</sub>$  values. For instance, consider the CIFAR-10 dataset with IPC<sub>F</sub> = 10. Resizing it to IPC<sub>F</sub> = 1, 2, and 5 using our method yields accuracies of 42.28%, 46.67%, and 55.96%, respectively. The average accuracy of these three values is 48.30%. This value surpasses the 37.08% accuracy of SSP [\[40\]](#page-15-18) by a considerable margin of 11.22%.

Ablation Study. Tab. [2](#page-5-0) shows the ablation study of the LBPE score and the balanced construction across dataset condensation methods. In the first row, the baseline results are shown where neither the LBPE score nor the balanced construction is applied. "Balanced only" (second row) indicates the selection method is random selection and the class distribution is balanced. "LBPE only" (third row) means the

<span id="page-5-0"></span>

| Table 2: Ablation study on two rules. (CIFAR- |  |
|-----------------------------------------------|--|
| 10: IPC <sub>10→1</sub> )                     |  |

| LBPE | Balanced | IDC [19]     | DREAM [24]   | MTT [1]      | KIP [32]     |
|------|----------|--------------|--------------|--------------|--------------|
| -    | -        | 28.23        | 30.87        | 19.75        | 14.06        |
| -    | ✓        | 30.19        | 32.83        | 19.09        | 16.27        |
| ✓    | -        | 39.38        | 37.30        | 20.37        | 15.78        |
| ✓    | ✓        | <b>42.28</b> | <b>42.29</b> | <b>22.02</b> | <b>22.24</b> |

| Dataset     | $IPC_F$ | Acc.  | IPC            |         | Condensation<br>$IDC[19]$ $DREAM[24]$ |          | SSP[40] Entropy[3] AUM[35] Forg.[41] EL2N[34] CCS[54] |          | Pruning Method |          |         | Ours                        |
|-------------|---------|-------|----------------|---------|---------------------------------------|----------|-------------------------------------------------------|----------|----------------|----------|---------|-----------------------------|
|             |         |       |                |         |                                       |          |                                                       |          |                |          |         |                             |
| CIFAR-10    |         |       | $\mathbf{1}$   | 28.23   | 30.87                                 | 27.83    | 30.30                                                 | 13.30    | 16.68          | 16.95    | 33.54   | 42.28                       |
|             |         |       | $\overline{c}$ | 37.10   | 38.88                                 | 34.95    | 38.88                                                 | 18.44    | 22.13          | 23.26    | 39.20   | 46.67                       |
|             | 10      | 67.50 | 5              | 52.92   | 54.23                                 | 48.47    | 52.85                                                 | 41.40    | 45.49          | 46.58    | 53.23   | 55.96                       |
|             |         |       | Avg.           | 39.42   | 41.33                                 | 37.08    | 40.68                                                 | 24.38    | 28.10          | 28.93    | 41.99   | 48.30                       |
|             |         |       | Diff.          | $-8.89$ | $-6.98$                               | $-11.22$ | $-7.63$                                               | $-23.92$ | $-20.20$       | $-19.37$ | $-6.31$ | $\overline{\phantom{a}}$    |
|             |         |       | 1              | 29.45   | 27.61                                 | 28.99    | 17.95                                                 | 7.21     | 12.23          | 7.95     | 31.28   | 38.77                       |
|             |         |       | $\overline{c}$ | 34.27   | 36.11                                 | 34.51    | 24.46                                                 | 8.67     | 12.17          | 9.47     | 38.71   | 44.54                       |
|             |         |       | 5              | 45.85   | 48.28                                 | 46.38    | 34.12                                                 | 12.85    | 15.55          | 16.03    | 48.19   | 53.04                       |
|             | 50      | 74.50 | 10             | 57.71   | 59.11                                 | 56.81    | 47.61                                                 | 22.92    | 27.01          | 31.33    | 56.80   | 61.10                       |
|             |         |       | Avg.           | 41.82   | 42.78                                 | 41.67    | 31.04                                                 | 12.91    | 16.74          | 16.20    | 43.75   | 49.36                       |
|             |         |       | Diff.          | $-7.54$ | $-6.58$                               | $-7.69$  | $-18.33$                                              | $-36.45$ | $-32.62$       | $-33.17$ | $-5.62$ | $\mathcal{L}_{\mathcal{A}}$ |
|             |         |       | 1              | 14.78   | 15.05                                 | 14.94    | 11.28                                                 | 3.64     | 6.45           | 5.12     | 18.97   | 22.57                       |
|             |         | 45.40 | $\overline{c}$ | 22.49   | 21.78                                 | 20.65    | 16.78                                                 | 5.93     | 10.03          | 8.15     | 25.27   | 29.09                       |
|             | 10      |       | 5              | 34.90   | 35.54                                 | 30.48    | 29.96                                                 | 17.32    | 21.45          | 22.40    | 36.01   | 38.51                       |
|             |         |       | Avg.           | 24.06   | 24.12                                 | 22.02    | 19.34                                                 | 8.96     | 12.64          | 11.89    | 26.75   | 30.06                       |
|             |         |       | Diff.          | $-6.00$ | $-5.93$                               | $-8.03$  | $-10.72$                                              | $-21.09$ | $-17.41$       | $-18.17$ | $-3.31$ | $\overline{\phantom{a}}$    |
|             |         | 49.50 | $\mathbf{1}$   | 13.92   | 13.26                                 | 14.65    | 5.75                                                  | 2.96     | 7.59           | 4.59     | 18.72   | 23.74                       |
|             |         |       | $\overline{c}$ | 20.62   | 20.41                                 | 20.27    | 8.63                                                  | 3.96     | 10.64          | 6.18     | 24.08   | 29.93                       |
| CIFAR-100   | 20      |       | 5              | 31.21   | 31.81                                 | 30.34    | 17.51                                                 | 8.25     | 17.63          | 11.76    | 32.81   | 38.02                       |
|             |         |       | Avg.           | 21.92   | 21.83                                 | 21.75    | 10.63                                                 | 5.06     | 11.95          | 7.51     | 25.20   | 30.56                       |
|             |         |       | Diff.          | $-8.65$ | $-8.74$                               | $-8.81$  | $-19.93$                                              | $-25.51$ | $-18.61$       | $-23.05$ | $-5.36$ | $\sim$                      |
|             |         |       | 1              | 13.41   | 13.36                                 | 15.90    | 1.86                                                  | 2.79     | 9.03           | 4.21     | 19.05   | 23.47                       |
|             |         |       | $\overline{c}$ | 20.38   | 19.97                                 | 21.26    | 2.86                                                  | 3.04     | 12.66          | 5.01     | 24.32   | 29.59                       |
|             |         |       | 5              | 29.92   | 29.88                                 | 29.63    | 6.04                                                  | 4.56     | 20.23          | 7.24     | 31.93   | 37.52                       |
|             | 50      | 52.60 | 10             | 37.79   | 37.85                                 | 36.97    | 13.31                                                 | 8.56     | 29.11          | 11.72    | 38.05   | 42.79                       |
|             |         |       | Avg.           | 25.38   | 25.27                                 | 25.94    | 6.02                                                  | 4.74     | 17.76          | 7.05     | 28.34   | 33.34                       |
|             |         |       | Diff.          | $-7.97$ | $-8.08$                               | $-7.40$  | $-27.33$                                              | $-28.61$ | $-15.59$       | $-26.30$ | $-5.01$ | $\overline{\phantom{a}}$    |
|             |         |       | $\mathbf{1}$   | 44.93   | $\blacksquare$                        | 45.69    | 40.98                                                 | 17.84    | 32.07          | 41.00    | 44.27   | 53.91                       |
|             |         |       | $\sqrt{2}$     | 57.84   | ÷,                                    | 58.47    | 52.04                                                 | 29.13    | 44.89          | 54.47    | 56.53   | 59.69                       |
|             | 10      | 72.80 | 5              | 67.20   | $\blacksquare$                        | 63.11    | 64.60                                                 | 44.56    | 55.13          | 65.87    | 67.36   | 64.47                       |
|             |         |       | Avg.           | 56.66   | ÷,                                    | 55.76    | 52.54                                                 | 30.51    | 44.03          | 53.78    | 56.05   | 59.36                       |
|             |         |       | Diff.          | 2.70    | $\overline{\phantom{a}}$              | 3.60     | 6.82                                                  | 28.85    | 15.33          | 5.58     | 3.31    | $\overline{\phantom{a}}$    |
| ImageNet-10 |         |       | $\mathbf{1}$   | 42.00   | $\blacksquare$                        | 43.13    | 36.13                                                 | 14.51    | 24.98          | 24.09    | 34.64   | 53.07                       |
|             |         |       | $\overline{c}$ | 53.93   | ä,                                    | 54.82    | 46.91                                                 | 19.09    | 31.27          | 33.16    | 42.22   | 58.96                       |
|             | 20      | 76.60 | 5              | 59.56   | $\overline{\phantom{a}}$              | 61.27    | 56.44                                                 | 27.78    | 36.44          | 46.02    | 57.11   | 64.38                       |
|             |         |       | Avg.           | 51.83   | ÷,                                    | 53.07    | 46.49                                                 | 20.46    | 30.90          | 34.42    | 44.66   | 58.80                       |
|             |         |       | Diff.          | 6.97    | $\blacksquare$                        | 5.73     | 12.31                                                 | 38.34    | 27.90          | 24.38    | 14.14   | $\blacksquare$              |

<span id="page-6-0"></span>Table 1: IPC means "images per class". Flexibly resize dataset from IPC<sub>F</sub> to IPC<sub>T</sub> (IPC<sub>F→T</sub>). The blue areas represent the average accuracy of listed  $IPC<sub>T</sub>$  datasets for different values of T. The gray areas indicate the accuracy difference between the corresponding methods and ours.

LBPE score is used for ranking samples, and the selection results are purely based on the LBPE score without considering the class balance. "LBPE + Balanced" indicates that both elements are included for sample selection. The empirical findings conclusively affirm the effectiveness of the two rules, which constitute the principal contributions of our YOCO method.

Standard Deviation of Experiments. Different training dynamics and network initializations impact the final results. Therefore, the reported results are averaged over three different training dynamics, and each training dynamic is evaluated based on three different network initializations. See Appendix B.2 for the primary results table with standard deviation.

## 4.3 Analysis of Two Rules

## 4.3.1 Analysis of LBPE Score for Sample Ranking

Tab. [3](#page-7-0) illustrates the robust performance of our YOCO method across diverse network structures, including ConvNet, ResNet, and DenseNet, demonstrating its strong generalization ability. Additionally, we present different sample ranking metrics from dataset pruning methods on these networks, demonstrating that our method outperforms both random selection and other data pruning methods.

In Tab. [4,](#page-7-0) we experiment with prioritizing easy samples over hard ones. We achieve this by reversing the importance metrics introduced by AUM [\[35\]](#page-15-12), Forg. [\[41\]](#page-16-0), and EL2N [\[34\]](#page-15-0) that originally prioritize

<span id="page-7-0"></span>Table 3: Accuracies on different network structures and different sample ranking metrics. (IDC [\[19\]](#page-14-1) condensed CIFAR-10:  $IPC_{10\to 1}$ )

Table 4: Prioritizing easy samples is better for different dataset pruning and dataset condensation methods. " $\mathcal{R}$ ?" represents whether to reverse the metrics which prioritize hard samples. (CIFAR-10:  $IPC_{10\rightarrow1}$ )

|            | ConvNet [12] | ResNet [13] | DenseNet [14] | Method     | $\mathcal{R}$ ? | IDC[19] | DREAM[24] | MTT[1] | DSA[50] |
|------------|--------------|-------------|---------------|------------|-----------------|---------|-----------|--------|---------|
| Random     | 28.23        | 24.14       | 24.63         | AUM [35]   | -               | 13.30   | 14.43     | 15.33  | 14.25   |
| SSP[40]    | 27.83        | 24.64       | 24.75         | AUM [35]   | $\checkmark$    | 37.97   | 38.18     | 16.63  | 18.23   |
| Entropy[3] | 30.30        | 30.53       | 29.93         | Forg. [41] | -               | 16.68   | 16.26     | 18.82  | 16.55   |
| AUM[35]    | 13.30        | 15.04       | 14.56         | Forg. [41] | $\checkmark$    | 36.69   | 36.15     | 16.65  | 17.03   |
| Forg.[41]  | 16.68        | 16.75       | 17.43         | EL2N [34]  | -               | 16.95   | 18.13     | 16.98  | 13.14   |
| EL2N[34]   | 16.95        | 19.98       | 21.43         | EL2N [34]  | $\checkmark$    | 33.11   | 34.36     | 19.01  | 21.29   |
| Ours       | 42.28        | 34.53       | 34.29         | Ours       | -               | 42.28   | 42.29     | 22.02  | 22.40   |

<span id="page-7-1"></span>Table 5: Balanced construction works on different dataset pruning methods. "B?" represents whether to use balanced construction. The subscript  $_{\text{t-value}}$  indicates the accuracy gain from balanced construction. (IDC [\[19\]](#page-14-1) condensed CIFAR-10:  $IPC_{10\rightarrow T}$ )

| IPC  | B?  | Random         | SSP [40]       | Entropy [3]    | AUM [35]       | Forg. [41]     | EL2N [34]      | Ours           |
|------|-----|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| IPC1 | -   | 28.23          | 27.83          | 30.30          | 13.30          | 16.68          | 16.95          | 37.63          |
| IPC1 | $✓$ | $30.05 + 1.82$ | $33.21 + 5.38$ | $33.67 + 3.37$ | $15.64 + 2.34$ | $19.09 + 2.41$ | $18.43 + 1.48$ | $42.28 + 4.65$ |
| IPC2 | -   | 37.10          | 34.95          | 38.88          | 18.44          | 22.13          | 23.26          | 42.99          |
| IPC2 | $✓$ | $39.44 + 2.34$ | $40.57 + 5.62$ | $42.17 + 3.29$ | $23.84 + 5.40$ | $28.06 + 5.93$ | $26.54 + 3.28$ | $46.67 + 3.68$ |
| IPC5 | -   | 52.92          | 48.47          | 52.85          | 41.40          | 45.49          | 46.58          | 53.86          |
| IPC5 | $✓$ | $52.64 - 0.28$ | $49.44 + 0.97$ | $54.73 + 1.88$ | $47.23 + 5.83$ | $48.02 + 2.53$ | $48.86 + 2.28$ | $55.96 + 2.10$ |

hard samples. Our results indicate that across various condensed datasets, including IDC [\[19\]](#page-14-1), DREAM [\[24\]](#page-15-2), MTT [\[1\]](#page-13-0), and DSA [\[50\]](#page-16-3), there is a distinct advantage in prioritizing easier samples over harder ones. These findings lend support to our Rule 1.

## 4.3.2 Analysis of Balanced Construction

Fig. [2](#page-8-0) presents the class distributions with and without a balanced construction for different datasets and different IPC $_{\mathbf{F}\rightarrow\mathbf{T}}$ . As explained in YOCO settings, our balanced construction is based on the multi-formation framework from IDC [\[19\]](#page-14-1). Therefore, the x-axis represents the count of images after multi-formation instead of the condensed images. It is evident that a ranking strategy relying solely on the LBPE score can result in a significant class imbalance, particularly severe in the ImageNet dataset. As depicted in Fig. [2\(](#page-8-0)f), three classes have no image patches left. Our balanced construction method effectively mitigates this issue. Notably, in the case of ImageNet- $10_{10\rightarrow 1}$ , the balanced construction boosts the accuracy by an impressive 19.37%.

To better understand the impact of balanced class distribution on various dataset pruning methods, we conducted a comparative analysis, as presented in Tab. [5.](#page-7-1) Clearly, achieving a balanced class distribution significantly enhances the performance of all examined methods. Remarkably, our proposed method consistently outperforms others under both imbalanced and balanced class scenarios, further substantiating the efficacy of our approach.

## 4.4 Other Analysis

Sample Importance Rules Differ between Condensed Dataset and Full Dataset. In Fig. [3,](#page-8-0) we compare Sample Importance Rules for Condensed Datasets (IPC<sub>10</sub>, IPC<sub>50</sub>) and the Full Dataset (IPC<sub>5000</sub>), by adjusting the pruning ratio from 10% to 90%. Unmarked solid lines mean prioritizing easy samples, dashed lines suggest prioritizing hard samples, while marked solid lines depict the accuracy disparity between the preceding accuracies. Therefore, the grey region above zero indicates "Prefer easy samples" (Rule $_{easy}$ ), while the blue region below zero represents "Prefer hard samples"(Rule $_{hard}$ ). We have two observations. First, as the pruning ratio increases, there is a gradual transition from Rule $_{\text{hard}}$  to Rule $_{\text{easy}}$ . Second, the turning point of this transition depends on the dataset size. Specifically, the turning points for  $IPC_{10}$ ,  $IPC_{50}$ , and  $IPC_{5000}$  occur at pruning

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image displays six horizontal bar charts, arranged in two rows of three. Each chart represents class-wise counts for balanced and imbalanced datasets, with a dashed line indicating the balanced count and a solid blue bar representing the imbalanced count. The charts are labeled (a) CIFAR-10\_{10}\rightarrow1, (b) CIFAR-10\_{10}\rightarrow2, and (c) CIFAR-10\_{10}\rightarrow5 in the top row, and their corresponding bottom row charts are not fully visible but appear to be related to CIFAR-100 and ImageNet. For chart (a), the balanced count is 42.28% and the imbalanced count is 37.63%. For chart (b), the balanced count is 46.67% and the imbalanced count is 42.29%. For chart (c), the balanced count is 55.96% and the imbalanced count is 53.86%. The bottom row charts show different percentage values: (a) 38.77% balanced and 30.70% imbalanced, (b) 24.21% balanced and 22.22% imbalanced, and (c) 53.91% balanced and 34.90% imbalanced. The y-axis in all charts is labeled 'Classes' and ranges from 0 to 9 (or higher for the middle bottom chart), while the x-axis is labeled 'Count'.

(d) CIFAR- $10_{50\rightarrow 1}$ (e) CIFAR-100<sub>10→1</sub> (f) ImageNet-10<sub>10→1</sub> Figure 2: Balanced and imbalanced selection by ranking

samples with LBPE score. Dataset $_{\mathbf{F}\rightarrow\mathbf{T}}$  denotes resizing the dataset from  $IPC<sub>F</sub>$  to  $IPC<sub>T</sub>$ . Accuracies for each setting are also listed in the legend. (IDC [\[19\]](#page-14-1) condensed datasets)

Image /page/8/Figure/2 description: A line graph displays the accuracy (%) on the y-axis against the dataset pruning ratio (%) on the x-axis, ranging from 10 to 90. The graph includes three sets of lines: IPC10 (blue), IPC50 (red), and IPC5000 (Full) (black). Each set has three lines representing 'Easy' (solid), 'Hard' (dashed), and 'Diff' (solid with markers). The 'Diff' lines indicate the difference between 'Easy' and 'Hard' accuracy. A legend at the top clarifies the line types and colors. Additionally, a shaded region at the bottom, from -10% to 0% accuracy, is labeled with text explaining that IPC\_Diff > 0 means preferring easy samples, IPC\_Diff < 0 means preferring hard samples, and a dividing line represents IPC\_Diff = IPC\_Easy - IPC\_Hard.

Figure 3: Different sample importance rules between condensed datasets and full datasets.

ratios of 24%, 38%, and 72%, respectively. These experimental outcomes substantiate our Rule 1 that condensed datasets should adhere to  $Rule_{easy}$ .

Performance Gap from Multi-formation. We would like to explain the huge performance gap between multi-formation-based methods (IDC [\[19\]](#page-14-1) and DREAM [\[24\]](#page-15-2)) and other methods (MTT [\[1\]](#page-13-0), KIP [\[33\]](#page-15-7), and DSA [\[50\]](#page-16-3)) in Tab. [2](#page-5-0) and Tab. [4.](#page-7-0) The potential reason is that a single image can be decoded to  $2^2 = 4$ low-resolution images via multi-formation. As a result, methods employing multi-formation generate four times as many images compared to those that do not use multi-formation. The illustration is shown in Fig. [4.](#page-8-1)

<span id="page-8-1"></span>Image /page/8/Figure/6 description: The image shows a transformation from condensed data to synthetic data. The condensed data is represented by a 2x2 grid of colored squares, with each square divided into two triangles. The squares are layered, with colors ranging from light yellow to orange and red. An arrow labeled "Uniform formation" points from the condensed data to the synthetic data. The synthetic data is shown as multiple layers of uniformly colored squares, with colors including green, yellow, and orange.

Figure 4: Illustration of the multi-formation with a factor of 2. (Taken from IDC [\[19\]](#page-14-1))

Why Use LBPE Score from the Top- $K$  Training Epochs with the Highest Accuracy? As shown in Eq. [2,](#page-2-0) different training epoch t leads to a different LBPE score. Fig. [5](#page-9-0) illustrates the accuracy of the dataset selected via the LBPE score across specific training epochs. We select LBPE scores from the initial 100 epochs out of 1000 original epochs to reduce computational costs. We have two observations. First, the model's accuracy during the first few epochs is substantially low. LBPE scores derived from these early-stage epochs might not accurately represent the samples' true importance since the model is insufficiently trained. Second, there's significant variance in accuracy even after 40 epochs, leading to potential instability in the LBPE score selection. To address this, we average LBPE scores from epochs with top- $K$  accuracy, thereby reducing variability and ensuring a more reliable sample importance representation.

Speculating on Why LBPE Score Performs Better at Certain Epochs? In Fig. [7,](#page-9-1) we present the distribution of LBPE scores at various training epochs, with scores arranged in ascending order for each class to facilitate comparison across epochs. Our experiment finds the LBPE scores decrease as the epoch number increases. The superior accuracy of  $LBE_{90}$  is due to two reasons. First, the model at the  $90<sub>th</sub>$  epoch is more thoroughly trained than the model at the first epoch, leading to more accurate LBPE scores. Second, the LBPE $_{90}$  score offers a more uniform distribution and a wider range  $[0, 1]$ , enhancing sample distinction. In contrast, the LBPE<sub>1000</sub> score is mostly concentrated within a narrow range [0, 0.1] for the majority of classes, limiting differentiation among samples. More possible reasons will be explored in future studies.

Visualization. Fig. [6](#page-9-0) visualizes the easy and hard samples identified by our YOCO method. We notice that most easy samples have a distinct demarcation between the object and its background. This is particularly evident in the classes of "vacuum cleaner" and "cocktail shaker". The easy samples in

Image /page/9/Figure/0 description: A line graph shows the accuracy percentage on the y-axis and the epoch number on the x-axis. The graph starts at approximately 28% accuracy at epoch 0 and increases to about 34% at epoch 5. It then fluctuates between 34% and 42% accuracy, with several peaks and valleys, until epoch 100, where the accuracy is around 40%. The title of the x-axis is "Which Epoch for LBPE Score", and the title of the y-axis is "Accuracy (%)". The graph has a grid for better readability.

<span id="page-9-0"></span>Image /page/9/Picture/1 description: This image is a grid of images, organized into columns labeled with text at the top and rows labeled with text on the right. The columns are labeled 'poke bonnet', 'green mamba', 'langur', 'Doberman pinscher', 'gyromitra', 'gazelle hound', 'vacuum cleaner', 'window screen', 'cocktail shaker', and 'garden spider'. The rows are labeled 'Easy Original', 'Easy Condensed', 'Hard Original', and 'Hard Condensed'. Each cell in the grid contains a smaller image corresponding to the label of its column and row. The 'poke bonnet' column shows images of pink bonnets. The 'green mamba' column shows images of green snakes. The 'langur' column shows images of monkeys. The 'Doberman pinscher' column shows images of Doberman pinscher dogs. The 'gyromitra' column shows images of fungi. The 'gazelle hound' column shows images of gazelle hounds. The 'vacuum cleaner' column shows images of vacuum cleaners. The 'window screen' column shows images of window screens. The 'cocktail shaker' column shows images of cocktail shakers. The 'garden spider' column shows images of garden spiders.

Figure 5: Accuracy of the dataset selected with LBPE score at specific epochs.

Figure 6: Visualization of hard and easy samples of ImageNet dataset selected by our method. Both the original and condensed images are shown for comparison.

<span id="page-9-1"></span>Image /page/9/Figure/4 description: The image displays a grid of 10 plots, each representing a different class (0-9) with labels like 'airplane', 'automobile', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', and 'truck'. Each plot has 'Score' on the y-axis and 'Sorted sample index according to scores' on the x-axis, ranging from 0 to 40. Within each plot, there are three lines representing scores at different epochs: Epoch 1 (light teal), Epoch 90 (teal), and Epoch 200 (dark teal), along with a dashed black line representing Epoch 1000 with its corresponding percentage accuracy. The percentages for Epochs 1, 90, 200, and 1000 are 28.32%, 42.11%, 39.09%, and 32.25% respectively. The plots show how the scores of samples within each class change as the model is trained over epochs, with the samples sorted by their scores.

Figure 7: LBPE scores at different epochs (LBPE $_{epoch}$ ) for ten classes of the CIFAR-10 dataset.

these two classes have clean backgrounds, while the hard samples have complex backgrounds. The visualization provides evidence of our method's ability to identify easy and hard samples.

## 5 Conclusion, Limitation and Future Work

We introduce You Only Condense Once (YOCO), a novel approach that resizes condensed datasets flexibly without an extra condensation process, enabling them to adjust to varying computational constraints. YOCO comprises two key rules. First, YOCO employs the Logit-Based Prediction Error (LBPE) score to rank the importance of training samples and emphasizes the benefit of prioritizing easy samples with low LBPE scores. Second, YOCO underscores the need to address the class imbalance in condensed datasets and utilizes Balanced Construction to solve the problem. Our experiments validated YOCO's effectiveness across different networks and datasets. These insights offer valuable directions for future dataset condensation and dataset pruning research.

We acknowledge several limitations and potential areas for further investigation. First, although our method uses early training epochs to reduce computational costs, determining the sample importance in the first few training epochs or even before training is interesting for future work. Second, we only utilize the LBPE score to establish the importance of samples within the dataset. However, relying on a single metric might not be the optimal approach. There are other importance metrics, such as SSP [\[40\]](#page-15-18) and AUM [\[35\]](#page-15-12), that could be beneficial to integrate into our methodology. Third, as our current work only covers clean datasets like CIFAR-10, the performance of our method on noisy datasets requires further investigation.

## 6 Acknowledgement

This work is partially supported by Joey Tianyi Zhou's A\*STAR SERC Central Research Fund (Useinspired Basic Research), the Singapore Government's Research, Innovation and enterprise 2020 Plan (Advanced Manufacturing and Engineering domain) under Grant A18A1b0045, and A\*STAR CFAR Internship Award for Research Excellence (CIARE). The computational work for this article was partially performed on resources of the National Supercomputing Centre (NSCC), Singapore (<https://www.nscc.sg>).

## A Proof

<span id="page-10-0"></span>

### A.1 Proof of Lemma 1

Lemma 1 (Gradient and Importance of Training Samples): The gradient of the loss function  $\nabla_{\mathbf{w}_t}\mathcal{L}(p(\mathbf{w}_t, x), y)$  for a dataset S is influenced by the samples with prediction errors.

**Proof of Lemma 1**: Let the gradients of the loss function for these two datasets be  $\nabla_{\mathbf{w}_t}^S \mathcal{L}$  and  $\nabla_{\mathbf{w}_t}^{S \to j} \mathcal{L}$ , respectively. Then, we have:

$$
\nabla_{\mathbf{w}_t}^S \mathcal{L} = \frac{1}{|S|} \sum_{(x,y) \in S} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t},
$$
(16)

and

$$
\nabla_{\mathbf{w}_t}^{S \to j} \mathcal{L} = \frac{1}{|S_{\to j}|} \sum_{(x,y) \in S_{\to j}} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t}.
$$
(17)

The difference between the gradients for the two datasets is given by:

$$
\Delta \nabla \mathbf{w}_t \mathcal{L} = \frac{1}{|S|} \sum_{(x,y) \in S} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t} - \frac{1}{|S \neg j|} \sum_{(x,y) \in S \neg j} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t}.
$$
\n(18)

Split the sum in the first term into two sums, one with the  $j$ -th sample and one without:

$$
\frac{1}{|S|}\left(\sum_{(x,y)\in S_{\neg j}}\frac{\partial \mathcal{L}(p(\mathbf{w}_t,x),y)}{\partial p(\mathbf{w}_t,x)}\cdot\frac{\partial p(\mathbf{w}_t,x)}{\partial \mathbf{w}_t}+\frac{\partial \mathcal{L}(p(\mathbf{w}_t,x_j),y_j)}{\partial p(\mathbf{w}_t,x_j)}\cdot\frac{\partial p(\mathbf{w}_t,x_j)}{\partial \mathbf{w}_t}\right).
$$

Notice that the only difference between the sums is the absence of the j-th sample in  $S_{\neg j}$ . Simplify the expression by using  $|S_{\neg j}| = |S| - 1$ :

$$
\Delta \nabla_{\mathbf{w}_t} \mathcal{L} = \left(\frac{1}{|S|} - \frac{1}{|S|-1}\right) \sum_{(x,y) \in S_{\neg j}} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t} + \frac{1}{|S|} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x_j), y_j)}{\partial p(\mathbf{w}_t, x_j)} \cdot \frac{\partial p(\mathbf{w}_t, x_j)}{\partial \mathbf{w}_t}
$$
\n(19)

Then we have:

$$
\Delta \nabla_{\mathbf{w}_t} \mathcal{L} = \frac{-1}{|S|(|S|-1)} \sum_{(x,y)\in S_{\neg j}} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x), y)}{\partial p(\mathbf{w}_t, x)} \cdot \frac{\partial p(\mathbf{w}_t, x)}{\partial \mathbf{w}_t} + \frac{1}{|S|} \frac{\partial \mathcal{L}(p(\mathbf{w}_t, x_j), y_j)}{\partial p(\mathbf{w}_t, x_j)} \cdot \frac{\partial p(\mathbf{w}_t, x_j)}{\partial \mathbf{w}_t}
$$
(20)

## B Experiment

### B.1 Detailed Settings

Our work is mainly based on IDC [\[19\]](#page-14-1), and we use the open-source code assets for our paper  $2<sup>3</sup>$  $2<sup>3</sup>$ . In section [B.1.1](#page-11-2) and [B.1.2,](#page-11-3) we will explain the condensation process of IDC. In section [B.1.3,](#page-12-0) we will explain all the methods we compared including dataset condensation and dataset pruning.

<span id="page-11-2"></span>

### B.1.1 Dataset and Data Augmentation

CIFAR-10. The training set of the original CIFAR-10 dataset [\[20\]](#page-14-14) contains 10 classes, and each class has 5,000 images with the shape of  $32 \times 32$  pixels.

CIFAR-100. The training set of the original CIFAR-100 dataset [\[20\]](#page-14-14) contains 100 classes, and each class contains 500 images with the shape of  $32 \times 32$  pixels.

ImageNet-10. ImageNet-10 is the subset of ImageNet-1K [\[5\]](#page-14-16) containing only 10 classes, where each class has on average 1, 200 images of resolution  $224 \times 224$ .

The augmentations include:

- Color: adjusts the brightness, saturation, and contrast of images.
- Crop: pads the image and then randomly crops back to the original size.
- Flip: flips the images horizontally with a probability of 0.5.
- Scale: randomly scale the images by a factor according to a ratio.
- Rotate: rotates the image by a random angle according to a ratio.
- Cutout: randomly removes square parts of the image, replacing the removed parts with black squares.
- Mixup: randomly selects a square region within the image and replaces this region with the corresponding section from another randomly chosen image. It happens at a probability of 0.5.

Following IDC [\[19\]](#page-14-1), we perform augmentation during training networks in condensation and evaluation, and we use coloring, cropping, flipping, scaling, rotating, and mixup. When updating network parameters, image augmentations are different for each image in a batch; when updating synthetic images, the same augmentations are utilized for the synthetic images and corresponding real images in a batch.

<span id="page-11-3"></span>

### B.1.2 Condensation Training and Evaluation

The condensation training process contains three initial components: real images, synthetic images (initially set as a subset of real images), and a randomly initialized network.

Condensation Training 1: inner loop for 1) image update and 2) network update. The inner loop of the condensation process involves two parts. The first part is the **update of the synthetic images.** The synthetic images are modified by matching the gradients from the network as it processes both real and synthetic images, with consistent augmentations applied throughout a mini-batch. The second part is the **update of the network.** The network that provides the gradient for images is updated after the image update. During network updates, only real images are used for training. In addition, the network training is limited to an early stage with just 100 epochs for both ConvNet-D3 and ResNet10-AP.

Condensation Training 2: outer loop for network re-initialization. The outer loop randomly re-initializes the network but does not re-initialize the synthetic images. The outer loop has 2000 and 500 iterations for ConvNet-D3 and ResNet10-AP, respectively.

Condensation Evaluation. For condensation evaluation, we need a network trained on the condensed datasets. Unlike condensation training, we fully train the network for 1000 epochs for ConvNet-D3 and ResNet10-AP.

<span id="page-11-0"></span><sup>&</sup>lt;sup>2</sup>[https://github.com/snu-mllab/Efficient-Dataset-Condensation](#page-0-0)

<span id="page-11-1"></span><sup>&</sup>lt;sup>3</sup>[https://github.com/haizhongzheng/Coverage-centric-coreset-selection](#page-0-0)

Image /page/12/Figure/1 description: The image displays a diagram illustrating a data condensation and multi-formation process for image datasets. It is divided into two sections, labeled (a) CIFAR-10: factor = 2. and (b) CIFAR-100: factor = 2. Each section shows a flow from 'Real Data' to 'Condensed Data' via a 'Condense' step, and then to 'Synthetic Data' via a 'Multi-Formation' step. Section (a) shows four images of airplanes and jets in the 'Real Data' and 'Synthetic Data' sections, with four smaller, more abstract images in the 'Condensed Data' section. Section (b) shows nine images of dogs and people in the 'Real Data' and 'Synthetic Data' sections, with nine smaller, more abstract images in the 'Condensed Data' section. The 'Synthetic Data' in both sections appears to be a lower-resolution or more abstract representation of the 'Real Data'.

<span id="page-12-1"></span><span id="page-12-0"></span>**Dataset Condensation.** IDC [\[19\]](#page-14-1) and DREAM [\[24\]](#page-15-2) use multi-formation of factor  $= n$ , while KIP [\[32,](#page-15-6) [33\]](#page-15-7), DSA [\[50\]](#page-16-3), and MTT [\[1\]](#page-13-0) contain only one image in a condensed image.

(c) ImageNet-10: factor  $= 3$ .

Figure 8: Illustration of multi-formation process.

Fig. [8](#page-12-1) illustrates the process of multi-formation (i.e., uniform formation) using a factor  $n$ . First, real data are down-scaled to  $\frac{1}{n^2}$  their size, and  $n^2$  number of real data are put together to form condensed data. Condensed data are what we store on disk. During training and evaluation, condensed data undergo a multi-formation process that splits the condensed data into  $n^2$  data and restores them to the size of real data.

<span id="page-12-2"></span>Dataset Pruning. Tab. [6](#page-12-2) shows the components required for each dataset pruning method. Based on implementations, dataset pruning baselines can be roughly put into two categories, i.e., 1) modelbased and 2) training dynamic-based methods.

Table 6: Components required for each dataset pruning method.

| Method      | Model | Training<br>Dynamics | Training<br>Time | Label |
|-------------|-------|----------------------|------------------|-------|
| SSP [40]    | ✓     |                      | Full             |       |
| Entropy [3] | ✓     |                      | Full             | ✓     |
| AUM [35]    |       | ✓                    | Full             | ✓     |
| Forg. [41]  |       | ✓                    | Full             | ✓     |
| EL2N [34]   |       | ✓                    | Early            | ✓     |
| Ours        |       | ✓                    | Early            | ✓     |

1) Model-based methods require a pretrained method for image importance ranking. Self-Supervised Prototype (SSP) [\[40\]](#page-15-18) utilizes the k-means algorithm to cluster feature spaces extracted from pretrained models. The number of clusters is exactly the number of selected samples, and we select images with the closest distance to the centroid in the feature space. Entropy [\[3\]](#page-14-0) keeps samples with the largest entropy indicating the maximum uncertainty, and we prune samples with the least entropy. Both methods use ConvNet-D3 models pre-trained with condensed datasets.

2) Training dynamic-based methods keep track of the model training dynamics. AUM [\[35\]](#page-15-12) keeps hard samples by considering a small area between the correct prediction and the largest logits of other labels. For Forgetting [\[41\]](#page-16-0), a forgetting event of a sample occurs if the training accuracy of a minibatch containing the sample decreases at the next epoch, and samples with the most forgetting events are considered hard samples. We prune samples with the least forgetting events. If samples contain the same forgetting counts, we prune samples in the order of the index. For EL2N [\[34\]](#page-15-0), samples with the large norm of error vector are deemed as hard samples, and we prune samples with the least error. The first 10 epochs' training information is averaged to compute the EL2N score. The above methods all choose to prune easy samples when the pruning ratio is small or moderate. For CCS, we use EL2N [\[34\]](#page-15-0) as the pruning metric, and we prune hard samples with optimal hard cutoff ratios suggested in the paper [\[54\]](#page-16-11).

<span id="page-13-2"></span>

#### B.2 Main Result with Standard Deviation

| $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ $\frac{1}{2}$ |    |          |                      |                                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|----------|----------------------|-----------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|--|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Dataset                                                                                                                                                                                                                                                                                                             |    |          | $IPC_F$ Acc. $IPC_T$ |                                                           | Condensation<br>$\text{IDC}[19]$ $\text{DREAD}([24] \mid \text{SSP}[40]$ $\text{Entropy}[3]$ $\text{AUM}[35]$ $\text{Forg}[41]$ $\text{EL2N}[34]$ $\text{CCS}[54]$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                                                       | Pruning Method |  | Ours                                                                                                                                                                                                                                                                        |
| CIFAR-10                                                                                                                                                                                                                                                                                                            |    | 10 67.50 | 2<br>$\mathcal{F}$   |                                                           | $28.23_{+0.08}$ $30.87_{+0.36}$ $27.83_{+0.10}$ $30.3_{+0.27}$ $13.3_{+0.22}$ $16.68_{+0.19}$ $16.95_{+0.12}$ $33.54_{+0.05}$ $42.28_{+0.15}$<br>$37.1_{\pm 0.34}$ $38.88_{\pm 0.13}$ $34.95_{\pm 0.37}$ $38.88_{\pm 0.03}$ $18.44_{\pm 0.02}$ $22.13_{\pm 0.07}$ $23.26_{\pm 0.25}$ $39.2_{\pm 0.10}$ $46.67_{\pm 0.12}$<br>$[52.92_{\pm 0.06}$ 54.23 <sub>±0.21</sub> $[48.47_{\pm 0.16}$ 52.85 <sub>±0.11</sub> 41.4 <sub>±0.24</sub> 45.49 <sub>±0.13</sub> 46.58 <sub>±0.34</sub> 53.23 <sub>±0.25</sub> 55.96 <sub>±0.07</sub>                                                                                                                                                                                |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
|                                                                                                                                                                                                                                                                                                                     | 50 | 74.50    | 10                   |                                                           | $29.45_{\pm 0.29}$ $27.61_{\pm 0.15}$ $28.99_{\pm 0.08}$ $17.95_{\pm 0.06}$ $7.21_{\pm 0.08}$ $12.23_{\pm 0.17}$ $7.95_{\pm 0.06}$ $31.28_{\pm 0.21}$ $38.77_{\pm 0.09}$<br>$34.27_{\pm 0.16}$ 36.11 $_{\pm 0.27}$ 34.51 $_{\pm 0.23}$ 24.46 $_{\pm 0.08}$ 8.67 $_{\pm 0.17}$ 12.17 $_{\pm 0.07}$ 9.47 $_{\pm 0.04}$ 38.71 $_{\pm 0.25}$ 44.54 $_{\pm 0.08}$<br>$45.85_{+0.11}$ $48.28_{+0.14}$ $46.38_{+0.18}$ $34.12_{+0.26}$ $12.85_{+0.08}$ $15.55_{+0.07}$ $16.03_{+0.08}$ $48.19_{+0.16}$ $53.04_{+0.18}$<br>$[57.71_{\pm 0.17}$ 59.11 $_{\pm 0.06}$ $[56.81_{\pm 0.03}$ 47.61 $_{\pm 0.14}$ 22.92 $_{\pm 0.11}$ 27.01 $_{\pm 0.08}$ 31.33 $_{\pm 0.18}$ 56.8 $_{\pm 0.11}$ 61.1 $_{\pm 0.16}$                |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
| $CIFAR-100$                                                                                                                                                                                                                                                                                                         |    | 10 45.40 | 2<br>5               |                                                           | $14.78_{\pm 0.10}$ $15.05_{\pm 0.04}$ $14.94_{\pm 0.09}$ $11.28_{\pm 0.07}$ $3.64_{\pm 0.04}$ $6.45_{\pm 0.07}$ $5.12_{\pm 0.06}$ $18.97_{\pm 0.04}$ $22.57_{\pm 0.04}$<br>$[22.49_{\pm 0.12} 21.78_{\pm 0.07} 20.65_{\pm 0.07} 16.78_{\pm 0.03} 5.93_{\pm 0.05} 10.03_{\pm 0.01} 8.15_{\pm 0.04} 25.27_{\pm 0.02} 29.09_{\pm 0.05}$<br>$34.9_{\pm 0.02}$ $35.54_{\pm 0.04}$ $30.48_{\pm 0.04}$ $29.96_{\pm 0.12}$ $17.32_{\pm 0.10}$ $21.45_{\pm 0.14}$ $22.4_{\pm 0.09}$ $36.01_{\pm 0.05}$ $38.51_{\pm 0.05}$                                                                                                                                                                                                    |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
|                                                                                                                                                                                                                                                                                                                     |    | 20 49.50 | 2<br>5               |                                                           | $13.92_{\pm 0.03}$ $13.26_{\pm 0.04}$ $14.65_{\pm 0.05}$ $5.75_{\pm 0.02}$ $2.96_{\pm 0.01}$ $7.59_{\pm 0.06}$ $4.59_{\pm 0.05}$ $18.72_{\pm 0.11}$ $23.74_{\pm 0.19}$<br>$20.62_{\pm0.03}$ $20.41_{\pm0.08}$ $20.27_{\pm0.09}$ $8.63_{\pm0.02}$ $3.96_{\pm0.04}$ $10.64_{\pm0.07}$ $6.18_{\pm0.04}$ $24.08_{\pm0.01}$ $29.93_{\pm0.13}$<br>$31.21_{\pm 0.09}$ $31.81_{\pm 0.03}$ $30.34_{\pm 0.10}$ $17.51_{\pm 0.08}$ $8.25_{\pm 0.07}$ $17.63_{\pm 0.04}$ $11.76_{\pm 0.13}$ $32.81_{\pm 0.11}$ $38.02_{\pm 0.08}$                                                                                                                                                                                               |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
|                                                                                                                                                                                                                                                                                                                     |    | 50 52.60 | 2<br>10              |                                                           | $13.41_{\pm 0.02}$ $13.36_{\pm 0.01}$ $15.9_{\pm 0.10}$ $1.86_{\pm 0.04}$ $2.79_{\pm 0.03}$ $9.03_{\pm 0.08}$ $4.21_{\pm 0.02}$ $19.05_{\pm 0.08}$ $23.47_{\pm 0.11}$<br>$[20.38_{\pm 0.11}$ 19.97 $_{\pm 0.21}$ $[21.26_{\pm 0.15}$ 2.86 $_{\pm 0.05}$ 3.04 $_{\pm 0.02}$ 12.66 $_{\pm 0.11}$ 5.01 $_{\pm 0.05}$ 24.32 $_{\pm 0.07}$ 29.59 $_{\pm 0.11}$<br>$29.92_{\pm 0.07}$ $29.88_{\pm 0.09}$ $29.63_{\pm 0.22}$ $6.04_{\pm 0.05}$ $4.56_{\pm 0.10}$ $20.23_{\pm 0.07}$ $7.24_{\pm 0.11}$ $31.93_{\pm 0.06}$ $37.52_{\pm 0.00}$<br>$[37.79_{\pm 0.01}$ $37.85_{\pm 0.12}$ $[36.97_{\pm 0.07}$ $13.31_{\pm 0.10}$ $8.56_{\pm 0.08}$ $29.11_{\pm 0.08}$ $11.72_{\pm 0.06}$ $38.05_{\pm 0.09}$ $42.79_{\pm 0.06}$ |                                                                                                                                                                                                                                                       |                |  |                                                                                                                                                                                                                                                                             |
| ImageNet<br>Subset-10                                                                                                                                                                                                                                                                                               |    | 10 72.80 | 2<br>5               | $44.93_{\pm 0.37}$<br>$57.84 + 0.10$<br>$67.2_{\pm 0.52}$ |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | $58.47_{\pm0.42}$ 52.04 $_{\pm0.29}$ 29.13 $_{\pm0.25}$ 44.89 $_{\pm0.41}$ 54.47 $_{\pm0.40}$ 56.53 $_{\pm0.13}$ 59.69 $_{\pm0.19}$<br>$63.11_{+0.63}$ $64.6_{+0.09}$ $44.56_{+0.66}$ $55.13_{+0.19}$ $65.87_{+0.04}$ $67.36_{+0.35}$ $64.47_{+0.36}$ |                |  | $45.69_{+0.74}$ $40.98_{+0.39}$ $17.84_{+0.45}$ $32.07_{+0.17}$ $41.0_{+0.71}$ $44.27_{+0.80}$ $53.91_{+0.49}$                                                                                                                                                              |
|                                                                                                                                                                                                                                                                                                                     |    | 20 76.60 | 5                    | $42.0 \pm 0.28$<br>$53.93_{\pm 0.31}$<br>$59.56_{+0.29}$  |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | $61.27_{+0.67}$ 56.44 <sub>+0.95</sub> 27.78 <sub>±0.13</sub> 36.44 <sub>±0.53</sub> 46.02 <sub>±0.26</sub> 57.11 <sub>±0.2</sub> 64.38 <sub>±0.38</sub>                                                                                              |                |  | $43.13_{\pm 0.5}$ $36.13_{\pm 0.17}$ $14.51_{\pm 0.12}$ $24.98_{\pm 0.13}$ $24.09_{\pm 0.67}$ $34.64_{\pm 0.16}$ $53.07_{\pm 0.31}$<br>$54.82_{\pm 0.27}$ $46.91_{\pm 0.51}$ $19.09_{\pm 0.45}$ $31.27_{\pm 0.23}$ $33.16_{\pm 0.41}$ $42.22_{\pm 0.17}$ $58.96_{\pm 0.36}$ |

Table 7: IPC means "images per class". Flexibly resize dataset from IPC<sub>F</sub> to IPC<sub>T</sub> (IPC<sub>F→T</sub>). The subscript  $+$ std. denotes the standard deviation.

Tab. [7](#page-13-2) provides the standard deviations for our main result. We note that standard deviations for random selection are averaged over three random score selections and for each set of randomly selected images, the accuracy is averaged over three randomly initialized networks. For dataset pruning methods, the reported results are averaged over three different training dynamics, and each training dynamic is evaluated based on three different network initializations.

# References

- <span id="page-13-0"></span>[1] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Dataset distillation by matching training trajectories. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2022.
- <span id="page-13-1"></span>[2] K. Chitta, J. M. Álvarez, E. Haussmann, and C. Farabet. Training data subset search with ensemble active learning. *IEEE Transactions on Intelligent Transportation Systems*, 23(9):14741–

14752, 2021.

- <span id="page-14-0"></span>[3] C. Coleman, C. Yeh, S. Mussmann, B. Mirzasoleiman, P. Bailis, P. Liang, J. Leskovec, and M. Zaharia. Selection via proxy: Efficient data selection for deep learning. In *Proc. Int. Conf. Learn. Represent.*, 2020.
- <span id="page-14-6"></span>[4] J. Cui, R. Wang, S. Si, and C.-J. Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-14-16"></span>[5] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei. Imagenet: A large-scale hierarchical image database. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 248–255, 2009.
- <span id="page-14-5"></span>[6] Z. Deng and O. Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.
- <span id="page-14-4"></span>[7] J. Du, Y. Jiang, V. T. Tan, J. T. Zhou, and H. Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2023.
- <span id="page-14-8"></span>[8] M. Ducoffe and F. Precioso. Adversarial active learning for deep networks: a margin based approach. *arXiv preprint arXiv:1802.09841*, 2018.
- <span id="page-14-11"></span>[9] D. Feldman and M. Langberg. A unified framework for approximating and clustering data. In *Proceedings of the Forty-Third Annual ACM Symposium on Theory of Computing*, page 569–578, 2011.
- <span id="page-14-12"></span>[10] D. Feldman, M. Schmidt, and C. Sohler. Turning big data into tiny data: Constant-size coresets for k-means, pca, and projective clustering. *SIAM Journal on Computing*, 49(3):601–657, 2020.
- <span id="page-14-7"></span>[11] V. Feldman and C. Zhang. What neural networks memorize and why: Discovering the long tail via influence estimation. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 2881–2891, 2020.
- <span id="page-14-15"></span>[12] S. Gidaris and N. Komodakis. Dynamic few-shot visual learning without forgetting. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 4367–4375, 2018.
- <span id="page-14-17"></span>[13] K. He, X. Zhang, S. Ren, and J. Sun. Deep residual learning for image recognition. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 770–778, 2016.
- <span id="page-14-18"></span>[14] G. Huang, Z. Liu, L. Van Der Maaten, and K. Q. Weinberger. Densely connected convolutional networks. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 4700–4708, 2017.
- <span id="page-14-13"></span>[15] L. Huang and N. K. Vishnoi. Coresets for clustering in euclidean spaces: Importance sampling is nearly optimal. In *Proceedings of the 52nd Annual ACM SIGACT Symposium on Theory of Computing*, page 1416–1429, 2020.
- <span id="page-14-3"></span>[16] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan. Delving into effective gradient matching for dataset condensation. *arXiv preprint arXiv:2208.00311*, 2022.
- <span id="page-14-9"></span>[17] K. Killamsetty, S. Durga, G. Ramakrishnan, A. De, and R. Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *Proc. Int. Conf. Mach. Learn.*, pages 5464–5474, 2021.
- <span id="page-14-10"></span>[18] K. Killamsetty, D. Sivasubramanian, G. Ramakrishnan, and R. Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Proc. AAAI Conf. Artif. Intell.*, pages 8110–8118, 2021.
- <span id="page-14-1"></span>[19] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song. Dataset condensation via efficient synthetic-data parameterization. In *Proc. Int. Conf. Mach. Learn.*, 2022.
- <span id="page-14-14"></span>[20] A. Krizhevsky, G. Hinton, et al. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009.
- <span id="page-14-2"></span>[21] S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon. Dataset condensation with contrastive signals. In *Proc. Int. Conf. Mach. Learn.*, pages 12352–12364, 2022.

- <span id="page-15-10"></span>[22] D. D. Lewis. A sequential algorithm for training text classifiers: Corrigendum and additional data. In *Acm Sigir Forum*, pages 13–19, 1995.
- <span id="page-15-4"></span>[23] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang. Dataset distillation via factorization. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.
- <span id="page-15-2"></span>[24] Y. Liu, J. Gu, K. Wang, Z. Zhu, W. Jiang, and Y. You. DREAM: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-15-8"></span>[25] N. Loo, R. Hasani, A. Amini, and D. Rus. Efficient dataset distillation using random feature approximation. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.
- <span id="page-15-9"></span>[26] N. Loo, R. Hasani, M. Lechner, and D. Rus. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023.
- <span id="page-15-5"></span>[27] J. Lorraine, P. Vicol, and D. Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics*, pages 1540–1552, 2020.
- <span id="page-15-13"></span>[28] K. Margatina, G. Vernikos, L. Barrault, and N. Aletras. Active learning by acquiring contrastive examples. In *Proceedings of the 2021 Conference on Empirical Methods in Natural Language Processing*, pages 650–663, 2021.
- <span id="page-15-16"></span>[29] K. Meding, L. M. S. Buschoff, R. Geirhos, and F. A. Wichmann. Trivial or impossible dichotomous data difficulty masks model differences (on imagenet and beyond). In *Proc. Int. Conf. Learn. Represent.*, 2022.
- <span id="page-15-15"></span>[30] B. Mirzasoleiman, J. Bilmes, and J. Leskovec. Coresets for data-efficient training of machine learning models. In *Proc. Int. Conf. Mach. Learn.*, pages 6950–6960, 2020.
- <span id="page-15-1"></span>[31] M. Mohri and A. Rostamizadeh. Rademacher complexity bounds for non-iid processes. In *Proc. Adv. Neural Inform. Process. Syst.*, 2008.
- <span id="page-15-6"></span>[32] T. Nguyen, Z. Chen, and J. Lee. Dataset meta-learning from kernel ridge-regression. In *Proc. Int. Conf. Learn. Represent.*, 2021.
- <span id="page-15-7"></span>[33] T. Nguyen, R. Novak, L. Xiao, and J. Lee. Dataset distillation with infinitely wide convolutional networks. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 5186–5198, 2021.
- <span id="page-15-0"></span>[34] M. Paul, S. Ganguli, and G. K. Dziugaite. Deep learning on a data diet: Finding important examples early in training. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 20596–20607, 2021.
- <span id="page-15-12"></span>[35] G. Pleiss, T. Zhang, E. Elenberg, and K. Q. Weinberger. Identifying mislabeled data using the area under the margin ranking. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 17044–17056, 2020.
- <span id="page-15-14"></span>[36] O. Pooladzandi, D. Davini, and B. Mirzasoleiman. Adaptive second order coresets for dataefficient machine learning. In *Proc. Int. Conf. Mach. Learn.*, pages 17848–17869, 2022.
- <span id="page-15-17"></span>[37] P. Ren, Y. Xiao, X. Chang, P.-Y. Huang, Z. Li, B. B. Gupta, X. Chen, and X. Wang. A survey of deep active learning. *ACM Comput. Surv.*, 54(9), oct 2021.
- <span id="page-15-11"></span>[38] B. Settles. *Active Learning*. Springer International Publishing, 2012.
- <span id="page-15-3"></span>[39] S. Shin, H. Bae, D. Shin, W. Joo, and I.-C. Moon. Loss-curvature matching for dataset selection and condensation. In *International Conference on Artificial Intelligence and Statistics*, pages 8606–8628, 2023.
- <span id="page-15-18"></span>[40] B. Sorscher, R. Geirhos, S. Shekhar, S. Ganguli, and A. Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 19523–19536, 2022.

- <span id="page-16-0"></span>[41] M. Toneva, A. Sordoni, R. T. des Combes, A. Trischler, Y. Bengio, and G. J. Gordon. An empirical study of example forgetting during deep neural network learning. In *Proc. Int. Conf. Learn. Represent.*, 2019.
- <span id="page-16-8"></span>[42] P. Vicol, J. P. Lorraine, F. Pedregosa, D. Duvenaud, and R. B. Grosse. On implicit bias in overparameterized bilevel optimization. In *Proc. Int. Conf. Mach. Learn.*, pages 22234–22259, 2022.
- <span id="page-16-7"></span>[43] K. Wang, J. Gu, D. Zhou, Z. Zhu, W. Jiang, and Y. You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-16-5"></span>[44] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You. Cafe: Learning to condense dataset by aligning features. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 12196–12205, 2022.
- <span id="page-16-1"></span>[45] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-16-14"></span>[46] M. Welling. Herding dynamical weights to learn. In *Proc. Int. Conf. Mach. Learn.*, pages 1121–1128, 2009.
- <span id="page-16-12"></span>[47] X. Xia, J. Liu, J. Yu, X. Shen, B. Han, and T. Liu. Moderate coreset: A universal method of data selection for real-world data-efficient deep learning. In *Proc. Int. Conf. Learn. Represent.*, 2023.
- <span id="page-16-13"></span>[48] S. Yang, Z. Xie, H. Peng, M. Xu, M. Sun, and P. Li. Dataset pruning: Reducing training data by examining generalization influence. In *Proc. Int. Conf. Learn. Represent.*, 2023.
- <span id="page-16-10"></span>[49] L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and D. Xu. Accelerating dataset distillation via model augmentation. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2023.
- <span id="page-16-3"></span>[50] B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. In *Proc. Int. Conf. Mach. Learn.*, pages 12674–12685, 2021.
- <span id="page-16-6"></span>[51] B. Zhao and H. Bilen. Synthesizing informative training samples with GAN. In *NeurIPS 2022 Workshop on Synthetic Data for Empowering ML Research*, 2022.
- <span id="page-16-4"></span>[52] B. Zhao and H. Bilen. Dataset condensation with distribution matching. In *Proc. IEEE Winter Conf. Appl. Comput. Vis.*, pages 6514–6523, 2023.
- <span id="page-16-2"></span>[53] B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. In *Proc. Int. Conf. Learn. Represent.*, 2021.
- <span id="page-16-11"></span>[54] H. Zheng, R. Liu, F. Lai, and A. Prakash. Coverage-centric coreset selection for high pruning rates. In *Proc. Int. Conf. Learn. Represent.*, 2023.
- <span id="page-16-9"></span>[55] Y. Zhou, E. Nezhadarya, and J. Ba. Dataset distillation using neural feature regression. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.