{"table_of_contents": [{"title": "You Only Condense Once: Two Rules for Pruning\nCondensed Datasets", "heading_level": null, "page_id": 0, "polygon": [[120.75, 99.75], [489.181640625, 99.75], [489.181640625, 136.2216796875], [120.75, 136.2216796875]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.69140625, 251.25], [328.5, 251.25], [328.5, 261.615234375], [282.69140625, 261.615234375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[106.15869140625, 479.25], [191.25, 479.25], [191.25, 490.74609375], [106.15869140625, 490.74609375]]}, {"title": "2 Related Works", "heading_level": null, "page_id": 1, "polygon": [[106.5, 394.83984375], [203.501953125, 394.83984375], [203.501953125, 406.44140625], [106.5, 406.44140625]]}, {"title": "2.1 Dataset Condensation/Distillation", "heading_level": null, "page_id": 1, "polygon": [[106.98046875, 418.4296875], [273.75, 418.4296875], [273.75, 428.484375], [106.98046875, 428.484375]]}, {"title": "2.2 Dataset Pruning", "heading_level": null, "page_id": 1, "polygon": [[106.5, 604.44140625], [201.111328125, 604.44140625], [201.111328125, 614.49609375], [106.5, 614.49609375]]}, {"title": "3 Method", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 156.75], [165.75, 156.75], [165.75, 167.5458984375], [106.98046875, 167.5458984375]]}, {"title": "3.1 Preliminaries", "heading_level": null, "page_id": 2, "polygon": [[106.30810546875, 181.5], [188.25, 181.5], [188.25, 191.5224609375], [106.30810546875, 191.5224609375]]}, {"title": "3.2 Identifying Important Training Samples", "heading_level": null, "page_id": 2, "polygon": [[106.5, 298.5], [302.25, 298.5], [302.25, 308.408203125], [106.5, 308.408203125]]}, {"title": "3.3 Balanced Construction", "heading_level": null, "page_id": 3, "polygon": [[106.5, 514.5], [229.0517578125, 514.5], [229.0517578125, 524.00390625], [106.5, 524.00390625]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 5, "polygon": [[107.25, 141.0], [193.04296875, 141.0], [193.04296875, 152.173828125], [107.25, 152.173828125]]}, {"title": "4.1 Experiment Settings", "heading_level": null, "page_id": 5, "polygon": [[106.5, 165.0], [218.25, 165.0], [218.25, 175.376953125], [106.5, 175.376953125]]}, {"title": "4.2 Primary Results", "heading_level": null, "page_id": 5, "polygon": [[106.98046875, 511.5], [200.8125, 511.5], [200.8125, 521.68359375], [106.98046875, 521.68359375]]}, {"title": "4.3 Analysis of Two Rules", "heading_level": null, "page_id": 6, "polygon": [[107.25, 609.75], [224.8681640625, 609.75], [224.8681640625, 620.68359375], [107.25, 620.68359375]]}, {"title": "4.3.1 Analysis of LBPE Score for Sample Ranking", "heading_level": null, "page_id": 6, "polygon": [[107.25, 631.5], [329.607421875, 631.5], [329.607421875, 641.953125], [107.25, 641.953125]]}, {"title": "4.3.2 Analysis of Balanced Construction", "heading_level": null, "page_id": 7, "polygon": [[106.5, 418.5], [285.75, 418.5], [285.75, 428.484375], [106.5, 428.484375]]}, {"title": "4.4 Other Analysis", "heading_level": null, "page_id": 7, "polygon": [[107.25, 603.66796875], [195.75, 603.66796875], [195.75, 612.94921875], [107.25, 612.94921875]]}, {"title": "5 Conclusion, Limitation and Future Work", "heading_level": null, "page_id": 9, "polygon": [[106.90576171875, 508.921875], [336.75, 508.921875], [336.75, 519.75], [106.90576171875, 519.75]]}, {"title": "6 Acknowledgement", "heading_level": null, "page_id": 10, "polygon": [[106.3828125, 72.0], [221.25, 72.0], [221.25, 83.57958984375], [106.3828125, 83.57958984375]]}, {"title": "A Proof", "heading_level": null, "page_id": 10, "polygon": [[106.5, 186.0], [159.0, 186.0], [159.0, 197.9033203125], [106.5, 197.9033203125]]}, {"title": "A.1 Proof of Lemma 1", "heading_level": null, "page_id": 10, "polygon": [[106.5, 213.0], [209.9267578125, 213.0], [209.9267578125, 223.330078125], [106.5, 223.330078125]]}, {"title": "B Experiment", "heading_level": null, "page_id": 11, "polygon": [[106.5, 71.10791015625], [190.2041015625, 71.10791015625], [190.2041015625, 83.96630859375], [106.5, 83.96630859375]]}, {"title": "B.1 Detailed Settings", "heading_level": null, "page_id": 11, "polygon": [[106.5, 95.95458984375], [206.0419921875, 95.95458984375], [206.0419921875, 107.314453125], [106.5, 107.314453125]]}, {"title": "B.1.1 Dataset and Data Augmentation", "heading_level": null, "page_id": 11, "polygon": [[107.25, 160.3916015625], [277.5, 160.3916015625], [277.5, 171.6064453125], [107.25, 171.6064453125]]}, {"title": "B.1.2 Condensation Training and Evaluation", "heading_level": null, "page_id": 11, "polygon": [[106.8310546875, 485.33203125], [307.1953125, 485.33203125], [307.1953125, 496.16015625], [106.8310546875, 496.16015625]]}, {"title": "B.2 Main Result with Standard Deviation", "heading_level": null, "page_id": 13, "polygon": [[106.5, 278.25], [292.25390625, 278.25], [292.25390625, 288.298828125], [106.5, 288.298828125]]}, {"title": "References", "heading_level": null, "page_id": 13, "polygon": [[107.20458984375, 651.75], [164.5048828125, 651.75], [164.5048828125, 662.8359375], [107.20458984375, 662.8359375]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 46], ["Text", 6], ["SectionHeader", 3], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 5349, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 92], ["Text", 6], ["SectionHeader", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 837, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 676], ["Line", 72], ["TextInlineMath", 8], ["Equation", 4], ["Text", 3], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 684], ["Line", 62], ["TextInlineMath", 7], ["Text", 6], ["Equation", 2], ["Reference", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 793], ["Line", 60], ["Text", 8], ["TextInlineMath", 8], ["Equation", 8], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["TableCell", 64], ["Line", 59], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["Table", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 2331, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 597], ["TableCell", 493], ["Line", 70], ["Text", 4], ["SectionHeader", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 465], ["TableCell", 255], ["Line", 62], ["Caption", 3], ["Text", 3], ["Table", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 22172, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 631], ["Line", 155], ["Text", 7], ["Figure", 3], ["Caption", 2], ["FigureGroup", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2771, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 109], ["Caption", 3], ["Text", 3], ["Figure", 2], ["Reference", 2], ["Picture", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 2601, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 696], ["Line", 129], ["Equation", 6], ["Text", 5], ["SectionHeader", 3], ["TextInlineMath", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 48], ["Text", 10], ["ListItem", 7], ["SectionHeader", 4], ["Reference", 4], ["Footnote", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["TableCell", 75], ["Line", 47], ["Text", 3], ["Reference", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["TextInlineMath", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3680, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 1426], ["TableCell", 86], ["Line", 70], ["Text", 3], ["Reference", 3], ["SectionHeader", 2], ["ListItem", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 46], ["ListItem", 19], ["Reference", 19], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 45], ["ListItem", 19], ["Reference", 19], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 36], ["ListItem", 15], ["Reference", 15], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/You Only Condense Once- Two Rules for Pruning Condensed Datasets"}